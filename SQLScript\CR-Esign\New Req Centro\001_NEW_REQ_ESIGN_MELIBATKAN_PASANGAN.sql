BEGIN TRAN

-- UPDATE MS_MAPPINGJSON SUMIT_POLO
UPDATE A
SET QUERY = '{"join":[{"table":"TR_TASK_D ttd","on":"ttd.LOV_ID = '+RIGHT(TABLE_NAME, CHARINDEX(' ', REVERSE(TABLE_NAME)) - 1)+'.UUID_LOV"},{"table":"MS_QUESTION msq","on":"msq.UUID_QUESTION = ttd.UUID_QUESTION AND msq.REF_ID = ''PRE_INC_PSGN''"},{"table":"TR_TASK_H","on":"TR_TASK_H.UUID_TASK_H = ttd.UUID_TASK_H"},{"table":"TR_TASK_D ttd2","on":"TR_TASK_H.UUID_TASK_H = ttd2.UUID_TASK_H"},{"table":"MS_QUESTION msq2","on":"msq2.UUID_QUESTION = ttd2.UUID_QUESTION and (msq2.ref_id = ''SVY_PSGN_TND_TGN_BAK'' OR msq2.ref_id = ''SVY_PSGN_TND_TGN'')"}]}'
, KEY_SOURCE = 'CASE WHEN '+RIGHT(TABLE_NAME, CHARINDEX(' ', REVERSE(TABLE_NAME)) - 1)+'.CODE = ''1'' THEN (CASE WHEN ttd2.OPTION_TEXT = ''YA'' THEN ''YES'' ELSE ''NO'' END) ELSE ''NO'' END'
FROM MS_MAPPINGJSON A WHERE TYPE = 'SUBMIT_POLO' AND KEY_DESTINATION = 'IsSpouseDigitalSign'
AND FORM_NAME IN  (
'Form Task Completed Barang',
'Form Task Completed Jasa',
'SVY NDS MOTORKU FOTO',
'SVY REGULER NB FOTO',
'SVY REGULER UB FOTO',
'SVY NDS MOBILKU FOTO',
'SVY SLB MOBIL FOTO',
'SVY FASILITAS DANA MOBIL FOTO',
'SVY FASILITAS DANA MOTOR FOTO',
'SVY FASILITAS MODAL USAHA MOBIL FOTO',
'SVY FASILITAS MODAL USAHA MOTOR FOTO',
'SVY SLB MOTOR FOTO',
'SVY IMBT MOBIL SYARIAH FOTO',
'SVY IMBT MOTOR SYARIAH FOTO',
'SVY HAJI MOTOR FOTO'
)

-- UPDATE QUESTIONSET TASK SURVEY DAN COMPLETE
UPDATE MS_QUESTION
SET IS_ACTIVE = 1
WHERE REF_ID = 'SVY_PSGN_TND_TGN_BAK'

UPDATE F
SET F.QUESTION_VALUE = 'copy({SVY_STTS_PERNIKAHAN}==''MAR'',{PRE_INC_PSGN},)',
F.RELEVANT = '{PRE_INC_PSGN}==1'
FROM MS_FORM A with(nolock)
JOIN MS_QUESTIONGROUPOFFORM B with(nolock) ON A.UUID_FORM = B.UUID_FORM
JOIN MS_QUESTIONGROUP C with(nolock) ON B.UUID_QUESTION_GROUP = C.UUID_QUESTION_GROUP
JOIN MS_QUESTIONOFGROUP D with(nolock) ON C.UUID_QUESTION_GROUP = D.UUID_QUESTION_GROUP
JOIN MS_QUESTION E with(nolock) ON D.UUID_QUESTION = E.UUID_QUESTION
LEFT JOIN MS_QUESTIONRELEVANT F with(nolock) ON A.UUID_FORM = F.UUID_FORM AND C.UUID_QUESTION_GROUP = F.UUID_QUESTION_GROUP AND E.UUID_QUESTION = F.UUID_QUESTION
LEFT JOIN MS_ASSETTAG G ON G.UUID_ASSET_TAG=E.UUID_ASSET_TAG
LEFT JOIN MS_ANSWERTYPE H ON E.UUID_ANSWER_TYPE = H.UUID_ANSWER_TYPE 
WHERE  REF_ID = 'SVY_PSGN_TND_TGN_BAK' AND A.FORM_NAME IN (
'Form Task Completed Barang',
'Form Task Completed Jasa',
'SVY NDS MOTORKU TEXT',
'SVY REGULER NB TEXT',
'SVY REGULER UB TEXT',
'SVY NDS MOBILKU TEXT',
'SVY SLB MOBIL TEXT',
'SVY FASILITAS DANA MOBIL TEXT',
'SVY FASILITAS DANA MOTOR TEXT',
'SVY FASILITAS MODAL USAHA MOBIL TEXT',
'SVY FASILITAS MODAL USAHA MOTOR TEXT',
'SVY SLB MOTOR TEXT',
'SVY IMBT MOBIL SYARIAH TEXT',
'SVY IMBT MOTOR SYARIAH TEXT',
'SVY HAJI MOTOR TEXT'
)

UPDATE F
SET F.RELEVANT = RELEVANT + '&& {PRE_INC_PSGN}==0'
FROM MS_FORM A with(nolock)
JOIN MS_QUESTIONGROUPOFFORM B with(nolock) ON A.UUID_FORM = B.UUID_FORM
JOIN MS_QUESTIONGROUP C with(nolock) ON B.UUID_QUESTION_GROUP = C.UUID_QUESTION_GROUP
JOIN MS_QUESTIONOFGROUP D with(nolock) ON C.UUID_QUESTION_GROUP = D.UUID_QUESTION_GROUP
JOIN MS_QUESTION E with(nolock) ON D.UUID_QUESTION = E.UUID_QUESTION
LEFT JOIN MS_QUESTIONRELEVANT F with(nolock) ON A.UUID_FORM = F.UUID_FORM AND C.UUID_QUESTION_GROUP = F.UUID_QUESTION_GROUP AND E.UUID_QUESTION = F.UUID_QUESTION
LEFT JOIN MS_ASSETTAG G ON G.UUID_ASSET_TAG=E.UUID_ASSET_TAG
LEFT JOIN MS_ANSWERTYPE H ON E.UUID_ANSWER_TYPE = H.UUID_ANSWER_TYPE 
WHERE  REF_ID = 'SVY_PSGN_TND_TGN' AND A.FORM_NAME IN (
'Form Task Completed Barang',
'Form Task Completed Jasa',
'SVY NDS MOTORKU TEXT',
'SVY REGULER NB TEXT',
'SVY REGULER UB TEXT',
'SVY NDS MOBILKU TEXT',
'SVY SLB MOBIL TEXT',
'SVY FASILITAS DANA MOBIL TEXT',
'SVY FASILITAS DANA MOTOR TEXT',
'SVY FASILITAS MODAL USAHA MOBIL TEXT',
'SVY FASILITAS MODAL USAHA MOTOR TEXT',
'SVY SLB MOTOR TEXT',
'SVY IMBT MOBIL SYARIAH TEXT',
'SVY IMBT MOTOR SYARIAH TEXT',
'SVY HAJI MOTOR TEXT'
)

UPDATE C
SET QUESTION_IS_ACTIVE = 1, RELEVANT = RELEVANT + '&& {PRE_INC_PSGN}==1', QUESTION_VALUE = 'copy({SVY_STTS_PERNIKAHAN}==''MAR'',{PRE_INC_PSGN},)'
FROM MS_FORM A with(nolock)
JOIN MS_FORMHISTORY B with(nolock) ON A.UUID_FORM = B.UUID_FORM AND B.FORM_VERSION = (SELECT MAX(FORM_VERSION) FROM MS_FORMHISTORY with(nolock) WHERE UUID_FORM = B.UUID_FORM)
JOIN MS_FORMQUESTIONSET C with(nolock) ON B.UUID_FORM = C.UUID_FORM AND B.UUID_FORM_HISTORY = C.UUID_FORM_HISTORY
JOIN MS_ANSWERTYPE D with(nolock) ON C.UUID_ANSWER_TYPE = D.UUID_ANSWER_TYPE
WHERE REF_ID = 'SVY_PSGN_TND_TGN_BAK' AND A.FORM_NAME IN(
'Form Task Completed Barang',
'Form Task Completed Jasa',
'SVY NDS MOTORKU TEXT',
'SVY REGULER NB TEXT',
'SVY REGULER UB TEXT',
'SVY NDS MOBILKU TEXT',
'SVY SLB MOBIL TEXT',
'SVY FASILITAS DANA MOBIL TEXT',
'SVY FASILITAS DANA MOTOR TEXT',
'SVY FASILITAS MODAL USAHA MOBIL TEXT',
'SVY FASILITAS MODAL USAHA MOTOR TEXT',
'SVY SLB MOTOR TEXT',
'SVY IMBT MOBIL SYARIAH TEXT',
'SVY IMBT MOTOR SYARIAH TEXT',
'SVY HAJI MOTOR TEXT'
)

UPDATE C
SET RELEVANT = RELEVANT + '&& {PRE_INC_PSGN}==0'
FROM MS_FORM A with(nolock)
JOIN MS_FORMHISTORY B with(nolock) ON A.UUID_FORM = B.UUID_FORM AND B.FORM_VERSION = (SELECT MAX(FORM_VERSION) FROM MS_FORMHISTORY with(nolock) WHERE UUID_FORM = B.UUID_FORM)
JOIN MS_FORMQUESTIONSET C with(nolock) ON B.UUID_FORM = C.UUID_FORM AND B.UUID_FORM_HISTORY = C.UUID_FORM_HISTORY
JOIN MS_ANSWERTYPE D with(nolock) ON C.UUID_ANSWER_TYPE = D.UUID_ANSWER_TYPE
WHERE REF_ID = 'SVY_PSGN_TND_TGN' AND A.FORM_NAME IN(
'Form Task Completed Barang',
'Form Task Completed Jasa',
'SVY NDS MOTORKU TEXT',
'SVY REGULER NB TEXT',
'SVY REGULER UB TEXT',
'SVY NDS MOBILKU TEXT',
'SVY SLB MOBIL TEXT',
'SVY FASILITAS DANA MOBIL TEXT',
'SVY FASILITAS DANA MOTOR TEXT',
'SVY FASILITAS MODAL USAHA MOBIL TEXT',
'SVY FASILITAS MODAL USAHA MOTOR TEXT',
'SVY SLB MOTOR TEXT',
'SVY IMBT MOBIL SYARIAH TEXT',
'SVY IMBT MOTOR SYARIAH TEXT',
'SVY HAJI MOTOR TEXT'
)


--UPDATE CHOICE_FILTER UNTUK CHECK ESIGN AND SEND ESIGN
UPDATE F
SET CHOICE_FILTER = CHOICE_FILTER + ',{PRE_INC_PSGN},{SVY_PSGN_TND_TGN_BAK}'
FROM MS_FORM A with(nolock)
JOIN MS_QUESTIONGROUPOFFORM B with(nolock) ON A.UUID_FORM = B.UUID_FORM
JOIN MS_QUESTIONGROUP C with(nolock) ON B.UUID_QUESTION_GROUP = C.UUID_QUESTION_GROUP
JOIN MS_QUESTIONOFGROUP D with(nolock) ON C.UUID_QUESTION_GROUP = D.UUID_QUESTION_GROUP
JOIN MS_QUESTION E with(nolock) ON D.UUID_QUESTION = E.UUID_QUESTION
LEFT JOIN MS_QUESTIONRELEVANT F with(nolock) ON A.UUID_FORM = F.UUID_FORM AND C.UUID_QUESTION_GROUP = F.UUID_QUESTION_GROUP AND E.UUID_QUESTION = F.UUID_QUESTION
LEFT JOIN MS_ASSETTAG G ON G.UUID_ASSET_TAG=E.UUID_ASSET_TAG
LEFT JOIN MS_ANSWERTYPE H ON E.UUID_ANSWER_TYPE = H.UUID_ANSWER_TYPE 
WHERE REF_ID IN ('SVY_ES_INVITATIONAL', 'SVY_ES_REGCHECK') AND A.FORM_NAME IN (
'Form Task Completed Barang',
'Form Task Completed Jasa',
'SVY NDS MOTORKU TEXT',
'SVY REGULER NB TEXT',
'SVY REGULER UB TEXT',
'SVY NDS MOBILKU TEXT',
'SVY SLB MOBIL TEXT',
'SVY FASILITAS DANA MOBIL TEXT',
'SVY FASILITAS DANA MOTOR TEXT',
'SVY FASILITAS MODAL USAHA MOBIL TEXT',
'SVY FASILITAS MODAL USAHA MOTOR TEXT',
'SVY SLB MOTOR TEXT',
'SVY IMBT MOBIL SYARIAH TEXT',
'SVY IMBT MOTOR SYARIAH TEXT',
'SVY HAJI MOTOR TEXT'
)

UPDATE C
SET CHOICE_FILTER = CHOICE_FILTER + ',{PRE_INC_PSGN},{SVY_PSGN_TND_TGN_BAK}'
FROM MS_FORM A with(nolock)
JOIN MS_FORMHISTORY B with(nolock) ON A.UUID_FORM = B.UUID_FORM AND B.FORM_VERSION = (SELECT MAX(FORM_VERSION) FROM MS_FORMHISTORY with(nolock) WHERE UUID_FORM = B.UUID_FORM)
JOIN MS_FORMQUESTIONSET C with(nolock) ON B.UUID_FORM = C.UUID_FORM AND B.UUID_FORM_HISTORY = C.UUID_FORM_HISTORY
JOIN MS_ANSWERTYPE D with(nolock) ON C.UUID_ANSWER_TYPE = D.UUID_ANSWER_TYPE
WHERE REF_ID IN ('SVY_ES_INVITATIONAL', 'SVY_ES_REGCHECK') AND A.FORM_NAME IN(
'Form Task Completed Barang',
'Form Task Completed Jasa',
'SVY NDS MOTORKU TEXT',
'SVY REGULER NB TEXT',
'SVY REGULER UB TEXT',
'SVY NDS MOBILKU TEXT',
'SVY SLB MOBIL TEXT',
'SVY FASILITAS DANA MOBIL TEXT',
'SVY FASILITAS DANA MOTOR TEXT',
'SVY FASILITAS MODAL USAHA MOBIL TEXT',
'SVY FASILITAS MODAL USAHA MOTOR TEXT',
'SVY SLB MOTOR TEXT',
'SVY IMBT MOBIL SYARIAH TEXT',
'SVY IMBT MOTOR SYARIAH TEXT',
'SVY HAJI MOTOR TEXT'
)


--UPDATE FORM
UPDATE MS_FORM
SET VERSION=VERSION+1, FORM_LAST_UPDATE = CURRENT_TIMESTAMP, DTM_UPD = GETDATE()
WHERE FORM_NAME IN(
'Form Task Completed Barang',
'Form Task Completed Jasa',
'SVY NDS MOTORKU TEXT', 
'SVY REGULER NB TEXT',
'SVY REGULER UB TEXT',
'SVY NDS MOBILKU TEXT',
'SVY SLB MOBIL TEXT',
'SVY FASILITAS DANA MOBIL TEXT',
'SVY FASILITAS DANA MOTOR TEXT',
'SVY FASILITAS MODAL USAHA MOBIL TEXT',
'SVY FASILITAS MODAL USAHA MOTOR TEXT',
'SVY SLB MOTOR TEXT',
'SVY IMBT MOBIL SYARIAH TEXT',
'SVY IMBT MOTOR SYARIAH TEXT',
'SVY HAJI MOTOR TEXT'
)
--select * from ms_mappingjson 
--WHERE TYPE = 'SUBMIT_POLO' AND KEY_DESTINATION = 'IsSpouseDigitalSign'

COMMIT TRAN