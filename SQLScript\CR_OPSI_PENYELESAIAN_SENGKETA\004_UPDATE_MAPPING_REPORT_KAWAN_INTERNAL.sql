BEGIN TRAN
		INSERT INTO MAPPING_REPORT_KAWAN_INTERNAL(IS_ACTIVE, HEADER_COLUMN, ATTR_JSON_CAE, ATTR_JSON_POLO, SEQ, USR_CRT, DTM_CRT, LOV_GROUP)
		SELECT
			1, 
			'Lembaga Penyelesaian Sengketa', 
			'custProtectCode', 
			'custProtectCode', 
			(select seq+5 from MAPPING_REPORT_KAWAN_INTERNAL 
				where header_column = 'Persetujuan penggunaan data pribadi pemohon dalam pemasaran produk WOMF Finance dan produk mitra WOM Finance sesuai peraturan dan hukum yang berlaku'
			),
			'CR_OPSI_SENGKETA', 
			GETDATE(), 
			'CUSTPROTECT'
		WHERE NOT EXISTS(
			SELECT HEADER_COLUMN FROM MAPPING_REPORT_KAWAN_INTERNAL 
				WHERE HEADER_COLUMN = 'Lembaga Penyelesaian Sengketa'
			)

COMMIT TRAN