BEGIN TRAN

DECLARE @USR_CRT VARCHAR(50) = 'CR_OPSI_SENGKETA'

DECLARE @MAPPING_TABLE TABLE(
	COL_NAME VARCHAR(100),
	REF_ID VARCHAR(100),
	UUID_FORM BIGINT, 
	PRODUCT_CATEGORY_CODE VARCHAR(100),
	IS_PILOTING VARCHAR(1),
	SOURCE_DATA VARCHAR(100),
	TABLE_NAME VARCHAR(100)
)

INSERT INTO @MAPPING_TABLE 
SELECT 'custProtectCode', 'LMBG_PNYLSN_SNGKT', A.UUID_FORM, A.PRODUCT_CATEGORY_CODE, IS_PILOTING, SOURCE_DATA, 'TBL_MAP_TASK_D'
FROM TBL_MAP_TASK_D A JOIN MS_FORM B ON A.UUID_FORM=B.UUID_FORM
WHERE FORM_NAME LIKE '%PRE SURVEY%' OR FORM_NAME LIKE '%TEXT%' OR FORM_NAME LIKE '%FOTO%'  OR FORM_NAME LIKE '%COMPLETED%'
GROUP BY A.UUID_FORM, PRODUCT_CATEGORY_CODE, IS_PILOTING, SOURCE_DATA

INSERT INTO @MAPPING_TABLE 
SELECT 'custProtectCode', 'LMBG_PNYLSN_SNGKT', A.UUID_FORM, NULL, NULL, SOURCE_DATA, 'REF_MAP_POLO_MSS'
FROM REF_MAP_POLO_MSS A JOIN MS_FORM B ON A.UUID_FORM=B.UUID_FORM
WHERE FORM_NAME LIKE '%PRE SURVEY%' OR FORM_NAME LIKE '%TEXT%' OR FORM_NAME LIKE '%FOTO%'  OR FORM_NAME LIKE '%COMPLETED%'
GROUP BY A.UUID_FORM, SOURCE_DATA



INSERT INTO TBL_MAP_TASK_D(USR_CRT, DTM_CRT, COL_NAME, REF_ID, UUID_FORM,PRODUCT_CATEGORY_CODE,IS_PILOTING,JSON_ATTR_REF,IS_READONLY,SOURCE_DATA,ANSWER_VALUE,CONDITION_ORDER,IS_MANDATORY)
SELECT
	@USR_CRT, GETDATE(), 'custProtectCode', 'LMBG_PNYLSN_SNGKT', A.UUID_FORM, A.PRODUCT_CATEGORY_CODE, A.IS_PILOTING, NULL, 0, A.SOURCE_DATA, NULL, 10, 1
	FROM @MAPPING_TABLE A
	WHERE A.TABLE_NAME = 'TBL_MAP_TASK_D' AND NOT EXISTS (
		SELECT REF_id FROM TBL_MAP_TASK_D TEMP 
			WHERE TEMP.COL_NAME = 'custProtectCode' AND TEMP.REF_ID = 'LMBG_PNYLSN_SNGKT' AND TEMP.UUID_FORM = A.UUID_FORM 
			AND TEMP.PRODUCT_CATEGORY_CODE = A.PRODUCT_CATEGORY_CODE AND TEMP.SOURCE_DATA=A.SOURCE_DATA
	) 

INSERT INTO REF_MAP_POLO_MSS(UUID_FORM, JSON_PARAM, REF_ID, IS_READONLY, SOURCE_DATA, IS_MANDATORY)
SELECT A.UUID_FORM, 'custProtectCode', 'LMBG_PNYLSN_SNGKT', 0, SOURCE_DATA, 1
	FROM @MAPPING_TABLE A
	WHERE A.TABLE_NAME = 'REF_MAP_POLO_MSS' AND NOT EXISTS (
		SELECT REF_ID FROM REF_MAP_POLO_MSS TEMP WITH(NOLOCK) 
			WHERE TEMP.JSON_PARAM = 'custProtectCode' AND TEMP.REF_ID = 'LMBG_PNYLSN_SNGKT' AND TEMP.UUID_FORM = A.UUID_FORM 
			AND TEMP.SOURCE_DATA=A.SOURCE_DATA
	) 


DECLARE @MAPPING_TABLE_TASK_D TABLE(
	REF_ID_SOURCE VARCHAR(100),
	REF_ID_TARGET VARCHAR(100),
	FORM_NAME_SOURCE VARCHAR(100), 
	FORM_NAME_TARGET VARCHAR(100),
	PRODUCT_CATEGORY_CODE VARCHAR(100),
	IS_PILOTING VARCHAR(1)
)

INSERT INTO @MAPPING_TABLE_TASK_D 
SELECT 'LMBG_PNYLSN_SNGKT', 'LMBG_PNYLSN_SNGKT', A.FORM_NAME_SOURCE, A.FORM_NAME_TARGET, PRODUCT_CATEGORY_CODE, IS_PILOTING
FROM MS_MAPPING_TASK_D A WITH(NOLOCK)
WHERE (FORM_NAME_SOURCE LIKE '%PRE SURVEY%' OR FORM_NAME_SOURCE LIKE '%TEXT%' OR FORM_NAME_SOURCE LIKE '%FOTO%' OR FORM_NAME_SOURCE LIKE '%COMPLETED%')
AND (FORM_NAME_TARGET LIKE '%PRE SURVEY%' OR FORM_NAME_TARGET LIKE '%TEXT%' OR FORM_NAME_TARGET LIKE '%FOTO%' OR FORM_NAME_TARGET LIKE '%COMPLETED%')
GROUP BY FORM_NAME_SOURCE, FORM_NAME_TARGET, PRODUCT_CATEGORY_CODE, IS_PILOTING



INSERT INTO MS_MAPPING_TASK_D(USR_CRT, DTM_CRT, FORM_NAME_SOURCE, FORM_NAME_TARGET, REF_ID_SOURCE, REF_ID_TARGET, IS_READONLY, IS_MANDATORY, PRODUCT_CATEGORY_CODE, SOURCE_DATA, ANSWER_VALUE, CONDITION_ORDER, IS_PILOTING)
SELECT
	@USR_CRT, GETDATE(), FORM_NAME_SOURCE, FORM_NAME_TARGET, 'LMBG_PNYLSN_SNGKT', 'LMBG_PNYLSN_SNGKT', 0, 1, PRODUCT_CATEGORY_CODE, NULL, NULL, 10, IS_PILOTING
	FROM @MAPPING_TABLE_TASK_D A 
	WHERE NOT EXISTS (
		SELECT FORM_NAME_SOURCE FROM MS_MAPPING_TASK_D TEMP WITH(NOLOCK) 
			WHERE TEMP.REF_ID_SOURCE = 'LMBG_PNYLSN_SNGKT' AND TEMP.REF_ID_TARGET = 'LMBG_PNYLSN_SNGKT' AND TEMP.FORM_NAME_SOURCE = A.FORM_NAME_SOURCE 
			AND TEMP.FORM_NAME_TARGET=A.FORM_NAME_TARGET AND TEMP.PRODUCT_CATEGORY_CODE=A.PRODUCT_CATEGORY_CODE AND TEMP.IS_PILOTING=A.IS_PILOTING
	) 

COMMIT TRAN