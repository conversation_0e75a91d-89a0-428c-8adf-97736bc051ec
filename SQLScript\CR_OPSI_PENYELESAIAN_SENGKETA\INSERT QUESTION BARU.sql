USE [WOMFMSS]

BEGIN TRAN
GO

DECLARE @table_temp TABLE (
		FORM_NAME VARCHAR(MAX),
		SEQ_QUESTION_GROUP_OF_FORM VARCHAR(MAX),
		SEQ_QUESTION_OF_GROUP VARCHAR(MAX),
		QUESTION_GROUP_LABEL VARCHAR(MAX),
		REF_ID VARCHAR(MAX),
		QUESTION_LABEL VARCHAR(MAX),
		ANSWER_TYPE_NAME VARCHAR(MAX),
		LOV_GROUP VARCHAR(MAX),
		IS_VISIBLE VARCHAR(MAX),
		IS_MANDATORY VARCHAR(MAX),
		IS_READONLY VARCHAR(MAX),
		IS_HOLIDAY_ALLOWED VARCHAR(MAX),
		MAX_LENGTH INT,
		REGEX_PATTERN VARCHAR(MAX),
		UUID_ASSET_TAG BIGINT,
		UUID_ORDER_TAG BIGINT,
		IMG_QLT VARCHAR(MAX),
		RELEVANT VARCHAR(MAX),
		CHOICE_FILTER VARCHAR(MAX),
		CALCULATE VARCHAR(MAX),
		QUESTION_VALUE VARCHAR(MAX),
		QUESTION_VALIDATION VARCHAR(MAX)
);

INSERT INTO @table_temp
VALUES
('Form Pre Survey','32','109','Pre Survey Layer 3','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'20','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('Form Task Completed Barang','37','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('Form Task Completed Jasa','37','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY NDS MOTORKU TEXT','192','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY REGULER NB TEXT','193','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY REGULER UB TEXT','194','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY NDS MOBILKU TEXT','197','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY SLB MOBIL TEXT','197','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY FASILITAS DANA MOBIL TEXT','197','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY FASILITAS DANA MOTOR TEXT','187','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY FASILITAS MODAL USAHA MOBIL TEXT','187','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY FASILITAS MODAL USAHA MOTOR TEXT','197','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY SLB MOTOR TEXT','167','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY HAJI MOTOR TEXT','192','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY IMBT MOBIL SYARIAH TEXT','197','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)',''),
('SVY IMBT MOTOR SYARIAH TEXT','167','10','E-Sign','LMBG_PNYLSN_SNGKT','Lembaga Penyelesaian Sengketa','Dropdown','CUSTPROTECT','1','1','0',NULL,'200','',NULL,NULL,'','({SVY_IS_CNCL_APP}==''0'')','{$BRANCH_ID}','','copy(''{LMBG_PNYLSN_SNGKT}''=='''', lov(CUSTPROTECT;1),)','')

PRINT 'insert table_temp';

INSERT INTO MS_QUESTION
SELECT
	'1' IS_ACTIVE,
	'CR_OPSI_PENYELESAIAN_SENGKETA' USR_CRT,
	GETDATE() DTM_CRT,
	NULL USR_UPD,
	NULL DTM_UPD,
	tt.REF_ID REF_ID,
	tt.QUESTION_LABEL QUESTION_LABEL,
	(SELECT ma.UUID_ANSWER_TYPE FROM MS_ANSWERTYPE ma WITH(NOLOCK) WHERE ma.ANSWER_TYPE_NAME = tt.ANSWER_TYPE_NAME) UUID_ANSWER_TYPE,
	ISNULL(tt.LOV_GROUP, '') LOV_GROUP,
	tt.IS_VISIBLE IS_VISIBLE,
	tt.IS_MANDATORY IS_MANDATORY,
	tt.IS_READONLY IS_READONLY,
	NULL IS_HOLIDAY_ALLOWED,
	tt.MAX_LENGTH MAX_LENGTH,
	ISNULL(tt.REGEX_PATTERN, '') REGEX_PATTERN,
	UUID_ASSET_TAG UUID_ASSET_TAG,
	NULL UUID_COLLECTION_TAG,
	(SELECT am.UUID_MS_SUBSYSTEM FROM AM_MSSUBSYSTEM am WITH(NOLOCK) WHERE am.SUBSYSTEM_NAME = 'MS') UUID_MS_SUBSYSTEM,
	NULL UUID_ORDER_TAG,
	'' IMG_QLT,
	NULL ASSET_DOC_CODE
FROM @table_temp tt
WHERE NOT EXISTS (
	SELECT TOP 1 mq.REF_ID
	FROM MS_QUESTION mq WITH(NOLOCK)
	WHERE mq.REF_ID = tt.REF_ID
);
PRINT 'insert ms_question';

INSERT INTO MS_FORMHISTORY
SELECT 
		UUID_FORM
		,(SELECT MAX(FORM_VERSION + 1) FROM MS_FORMHISTORY WHERE UUID_FORM = mf.UUID_FORM) FORM_VERSION
		,'Add new question sengketa' CHANGE_LOG
		,'CR_OPSI_PENYELESAIAN_SENGKETAT' USR_CRT
		,GETDATE() DTM_CRT
		,NULL USR_UPD
		,NULL DTM_UPD
FROM MS_FORM mf WHERE mf.FORM_NAME IN 
(
'Form Pre Survey',
'Form Task Completed Barang',
'Form Task Completed Jasa',
'SVY FASILITAS DANA MOBIL TEXT',
'SVY FASILITAS DANA MOTOR TEXT',
'SVY FASILITAS MODAL USAHA MOBIL TEXT',
'SVY FASILITAS MODAL USAHA MOTOR TEXT',
'SVY HAJI MOTOR TEXT',
'SVY IMBT MOBIL SYARIAH TEXT',
'SVY IMBT MOTOR SYARIAH TEXT',
'SVY NDS MOBILKU TEXT',
'SVY NDS MOTORKU TEXT',
'SVY REGULER NB TEXT',
'SVY REGULER UB TEXT',
'SVY SLB MOBIL TEXT',
'SVY SLB MOTOR TEXT'
);

PRINT 'insert ms_formhistory';

INSERT INTO MS_QUESTIONOFGROUP
SELECT
  'CR_OPSI_PENYELESAIAN_SENGKETA' USR_CRT,
  GETDATE() DTM_CRT,
  mqg.UUID_QUESTION_GROUP,
  mq.UUID_QUESTION,
  MIN(tt.SEQ_QUESTION_OF_GROUP) SEQ_ORDER
FROM 
  @table_temp tt
  INNER JOIN MS_QUESTIONGROUP mqg WITH(NOLOCK) ON mqg.QUESTION_GROUP_LABEL = tt.QUESTION_GROUP_LABEL
  INNER JOIN MS_QUESTION mq WITH(NOLOCK) ON mq.REF_ID = tt.REF_ID
GROUP BY
  mqg.UUID_QUESTION_GROUP,
  mq.UUID_QUESTION;

PRINT 'insert MS_QUESTIONOFGROUP';

INSERT INTO MS_QUESTIONRELEVANT
SELECT
	(SELECT top(1) mf.UUID_FORM FROM MS_FORM mf WITH(NOLOCK) WHERE mf.FORM_NAME = tt.FORM_NAME) UUID_FORM,
	(SELECT top(1) mq.UUID_QUESTION FROM MS_QUESTION mq WITH(NOLOCK) WHERE mq.REF_ID = tt.REF_ID) UUID_QUESTION,
	(SELECT top(1) mq.UUID_QUESTION_GROUP FROM MS_QUESTIONGROUP mq WITH(NOLOCK) WHERE mq.QUESTION_GROUP_LABEL = tt.QUESTION_GROUP_LABEL) UUID_QUESTION_GROUP,
	ISNULL(tt.RELEVANT, '') RELEVANT,
	ISNULL(tt.CALCULATE, '') CALCULATE,
	ISNULL(tt.CHOICE_FILTER, '') CHOICE_FILTER,
	ISNULL(tt.QUESTION_VALIDATION, '') QUESTION_VALIDATION,
	'' QUESTION_ERROR_MESSAGE,
	ISNULL(tt.QUESTION_VALUE, '') QUESTION_VALUE,
	'' RELEVANT_MANDATORY
FROM @table_temp tt;

PRINT 'insert MS_QUESTIONRELEVANT';

DECLARE @USR_UPD VARCHAR(100);
DECLARE @HIS_LOG VARCHAR(2000);
SET @USR_UPD = 'CR_OPSI_PENYELESAIAN_SENGKETA'
SET @HIS_LOG = 'Add New Question for CR_OPSI_PENYELESAIAN_SENGKETA';

DECLARE @LIST_FORM TABLE (
	FORM_NAME VARCHAR(100)
	,NEW_UUID_FORM_HISTORY BIGINT
);

INSERT INTO @LIST_FORM (FORM_NAME) VALUES
('Form Pre Survey'),
('Form Task Completed Barang'),
('Form Task Completed Jasa'),
('SVY NDS MOTORKU TEXT'),
('SVY REGULER NB TEXT'),
('SVY REGULER UB TEXT'),
('SVY NDS MOBILKU TEXT'),
('SVY SLB MOBIL TEXT'),
('SVY FASILITAS DANA MOBIL TEXT'),
('SVY FASILITAS DANA MOTOR TEXT'),
('SVY FASILITAS MODAL USAHA MOBIL TEXT'),
('SVY FASILITAS MODAL USAHA MOTOR TEXT'),
('SVY SLB MOTOR TEXT'),
('SVY HAJI MOTOR TEXT'),
('SVY SLB MOBIL TEXT'),
('SVY SLB MOTOR TEXT')
;

PRINT '@LIST_FORM';

INSERT INTO MS_FORMQUESTIONSET
SELECT 
	(SELECT mf.UUID_FORM_HISTORY FROM MS_FORMHISTORY mf WITH(NOLOCK) WHERE mf.UUID_FORM = A.UUID_FORM AND mf.FORM_VERSION = (SELECT MAX(mf1.FORM_VERSION) FROM MS_FORMHISTORY mf1 WITH(NOLOCK) WHERE mf1.UUID_FORM = A.UUID_FORM)) UUID_FORM_HISTORY 
	,E.UUID_QUESTION
	,E.IS_ACTIVE QUESTION_IS_ACTIVE
	,E.REF_ID
	,E.QUESTION_LABEL
	,E.UUID_ANSWER_TYPE
	,E.LOV_GROUP
	,E.IS_VISIBLE
	,E.IS_MANDATORY
	,E.IS_READONLY
	,E.IS_HOLIDAY_ALLOWED
	,E.MAX_LENGTH
	,E.REGEX_PATTERN
	,E.UUID_ASSET_TAG
	,E.UUID_COLLECTION_TAG
	,C.UUID_MS_SUBSYSTEM
	,E.UUID_ORDER_TAG
	,E.IMG_QLT
	,C.UUID_QUESTION_GROUP
	,C.IS_ACTIVE QUESTION_GROUP_IS_ACTIVE
	,C.QUESTION_GROUP_LABEL
	,D.SEQ_ORDER QUESTION_OF_GROUP_SEQ
	,A.UUID_FORM
	,A.IS_ACTIVE FORM_IS_ACTIVE
	,A.FORM_NAME
	,A.UUID_FORM_CATEGORY
	,A.IS_PRINTABLE
	,B.LINE_SEQ_ORDER QUESTION_GROUP_OF_FORM_SEQ
	,ISNULL(F.RELEVANT, '')
	,ISNULL(F.CALCULATE, '')
	,ISNULL(F.CHOICE_FILTER, '')
	,ISNULL(F.QUESTION_VALIDATION, '')
	,ISNULL(F.QUESTION_ERROR_MESSAGE, '')
	,ISNULL(F.QUESTION_VALUE, '')
	,@USR_UPD USR_CRT
	,GETDATE() DTM_CRT
	,ISNULL(F.RELEVANT_MANDATORY, '') RELEVANT_MANDATORY
	FROM MS_FORM A with(nolock)
	JOIN @LIST_FORM G ON A.FORM_NAME = G.FORM_NAME
	JOIN MS_QUESTIONGROUPOFFORM B with(nolock) ON A.UUID_FORM = B.UUID_FORM
	JOIN MS_QUESTIONGROUP C with(nolock) ON B.UUID_QUESTION_GROUP = C.UUID_QUESTION_GROUP
	JOIN MS_QUESTIONOFGROUP D with(nolock) ON C.UUID_QUESTION_GROUP = D.UUID_QUESTION_GROUP
	JOIN MS_QUESTION E with(nolock) ON D.UUID_QUESTION = E.UUID_QUESTION
	JOIN MS_QUESTIONRELEVANT F with(nolock) ON A.UUID_FORM = F.UUID_FORM AND C.UUID_QUESTION_GROUP = F.UUID_QUESTION_GROUP AND E.UUID_QUESTION = F.UUID_QUESTION
	WHERE A.FORM_NAME = G.FORM_NAME;
	
PRINT 'insert MS_FORMQUESTIONSET';

 COMMIT TRAN
GO