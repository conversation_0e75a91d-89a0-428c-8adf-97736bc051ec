GO
/****** Object:  UserDefinedFunction [dbo].[[getDefaultProdOffByKAT]]    Script Date: 2/18/2025 2:38:05 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE FUNCTION [dbo].[getDefaultProdOffByKAT]
(
    @kat varchar (10),
    @productCat varchar(20),
	@officeCode varchar(20)
)
RETURNS TABLE
AS 
RETURN (
	with cte as (
 		select distinct spo.PRODUCT_OFFERING_CODE, BRANCH_CODE AS OFFICE_CODE, '1' as rank
		from MS_MAPPING_USER_KAT kat (nolock)
		join MS_BRANCH mb (nolock) on mb.UUID_BRANCH = kat.UUID_BRANCH and mb.IS_ACTIVE = '1' and mb.IS_PILOTING = '1' and kat.IS_ACTIVE = '1'
		join TBL_PRODUCT_CATEGORY tpc (nolock) on kat.TBL_PRODUCT_CATEGORY_ID = tpc.TBL_PRODUCT_CATEGORY_ID and tpc.KONVEN_SYARIAH = mb.KONVEN_SYARIAH and tpc.IS_DELETED = '0'
		join STAGING_PRODUCT_OFFERING spo (nolock) on spo.PRODUCT_CATEGORY_CODE = tpc.PRODUCT_CATEGORY_CODE and spo.IS_DELETED = '0'
		where KAT_CODE = @kat AND ((spo.PRODUCT_CATEGORY_CODE = @productCat AND @productCat <> '') OR (@productCat = ''))
		UNION ALL
		select distinct spo.PRODUCT_OFFERING_CODE, O.OFFICE_CODE, '2' as rank
		from [CONFINS].[CONFINS].[dbo].OFFICE_ZIPCODE_MEMBER ozm (nolock) 
		join [CONFINS].[CONFINS].[dbo].REF_ZIPCODE rz (nolock) on ozm.REF_ZIPCODE_ID = rz.REF_ZIPCODE_ID and rz.IS_ACTIVE = '1'
		join [CONFINS].[CONFINS].[dbo].REF_OFFICE o (nolock) on ozm.REF_OFFICE_ID = o.REF_OFFICE_ID and o.IS_ACTIVE = '1'
		join MS_BRANCH mb (nolock) on mb.BRANCH_CODE = o.OFFICE_CODE and mb.IS_ACTIVE = '1' and mb.IS_PILOTING = '1'
		join TBL_PRODUCT_CATEGORY tpc (nolock) on tpc.KONVEN_SYARIAH = mb.KONVEN_SYARIAH and tpc.IS_DELETED = '0'
		join STAGING_PRODUCT_OFFERING spo (nolock) on spo.PRODUCT_CATEGORY_CODE = tpc.PRODUCT_CATEGORY_CODE and spo.IS_DELETED = '0'
		where rz.SUB_ZIPCODE = @kat  and ((spo.PRODUCT_CATEGORY_CODE = @productCat AND @productCat <> '') OR (@productCat = ''))
	) SELECT 
			CASE WHEN (SELECT COUNT(1) FROM cte WHERE OFFICE_CODE = @officeCode) > 0
				THEN (SELECT TOP 1 PRODUCT_OFFERING_CODE from cte WHERE OFFICE_CODE = @officeCode order by rank)
			ELSE (SELECT TOP 1 PRODUCT_OFFERING_CODE from cte order by rank) 
			END AS PRODUCT_OFFERING_CODE
) 