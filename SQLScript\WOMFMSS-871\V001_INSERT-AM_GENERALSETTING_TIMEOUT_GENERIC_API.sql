IF NOT EXISTS (SELECT 1 FROM AM_GENERALSETTING WITH (NOLOCK) WHERE GS_CODE = 'TIMEOUT_GENERIC_API')
INSERT INTO [dbo].[AM_GENERALSETTING]
           ([IS_ACTIVE]
           ,[USR_CRT]
           ,[DTM_CRT]
           ,[USR_UPD]
           ,[DTM_UPD]
           ,[GS_CODE]
           ,[GS_PROMPT]
           ,[GS_TYPE]
           ,[GS_VALUE]
           ,[EDITABLE])
     VALUES
           (1
           ,'SCRIPT'
           ,GETDATE()
           ,NULL
           ,NULL
           ,'TIMEOUT_GENERIC_API'
           ,'Timeout for generic API (in milisecond)(if the value is 0 then the timeout will be set to 1200000 ms'
           ,'java.lang.String'
           ,'120000'
           ,'1')
GO

