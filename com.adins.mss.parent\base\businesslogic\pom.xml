<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.adins.mss</groupId>
    <artifactId>com.adins.mss.base</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>com.adins.mss.base.businesslogic</artifactId>
  <dependencies>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.persistence.dao-api</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.persistence.dao-model</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.model</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
	<dependency>
		<groupId>com.adins.framework</groupId>
		<artifactId>com.adins.framework.tool.properties</artifactId>
		<version>${adins-framework.version}</version>
	</dependency>
	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.core</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.foundation</groupId>
  		<artifactId>com.adins.foundation.workflow.bussinesslogic-api.engine</artifactId>
  		<version>${adins-foundation.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>org.apache.commons</groupId>
  		<artifactId>commons-lang3</artifactId>
  		<version>${commons-lang3.version}</version>
  	</dependency>
  	<dependency>
    	<groupId>org.springframework.security</groupId>
	    <artifactId>spring-security-core</artifactId>
	    <version>${spring-security.version}</version>
	</dependency>
  	<dependency>
       <groupId>org.springframework</groupId>
       <artifactId>spring-jms</artifactId>
       <version>${spring-framework.version}</version>
   </dependency>
   <dependency>
	    <groupId>javax.jms</groupId>
	    <artifactId>javax.jms-api</artifactId>
	    <version>${jms.version}</version>
	</dependency>
  </dependencies>
</project>