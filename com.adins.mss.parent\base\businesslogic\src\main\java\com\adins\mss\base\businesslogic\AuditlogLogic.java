package com.adins.mss.base.businesslogic;

import java.util.Date;

import com.adins.framework.persistence.dao.api.IAuditable;
import com.adins.framework.persistence.dao.api.ManagerDAO;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmAuditlog;

public class AuditlogLogic implements IAuditable {

	private ManagerDAO managerDAO;
	private String applicationname;
	private String subsystemname;
	private long amMsuser;
	private String screenid;
	private String terminaladdress;
	private String terminalid;
	private String terminaluserid;

	public ManagerDAO getManagerDAO() {
		return managerDAO;
	}

	public void setManagerDAO(ManagerDAO managerDAO) {
		this.managerDAO = managerDAO;
	}
	
	public String getApplicationname() {
		return applicationname;
	}

	public void setApplicationname(String applicationname) {
		this.applicationname = applicationname;
	}

	public String getSubsystemname() {
		return subsystemname;
	}

	public void setSubsystemname(String subsystemname) {
		this.subsystemname = subsystemname;
	}

	public long getAmMsuser() {
		return amMsuser;
	}

	public void setAmMsuser(long amMsuser) {
		this.amMsuser = amMsuser;
	}

	public String getScreenid() {
		return screenid;
	}

	public void setScreenid(String screenid) {
		this.screenid = screenid;
	}

	public String getTerminaladdress() {
		return terminaladdress;
	}

	public void setTerminaladdress(String terminaladdress) {
		this.terminaladdress = terminaladdress;
	}

	public String getTerminalid() {
		return terminalid;
	}

	public void setTerminalid(String terminalid) {
		this.terminalid = terminalid;
	}

	public String getTerminaluserid() {
		return terminaluserid;
	}

	public void setTerminaluserid(String terminaluserid) {
		this.terminaluserid = terminaluserid;
	}

	public void setAuditContextParams(AuditContext auditContext) {
		this.setAmMsuser(Integer.valueOf(auditContext.getCallerId()));
		this.setApplicationname((String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_APPLICATIONNAME));
		this.setSubsystemname((String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME));
		this.setScreenid((String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_SCREENID));
		this.setTerminaladdress((String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_TERMINALADDRESS));
		this.setTerminalid((String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_TERMINALID));
		this.setTerminaluserid((String) auditContext.getParameters().get(AuditContext.KEY_PARAMS_TERMINALUSERID));
	}
	
	@Override
	public void insertAuditLog(String activity, String tableName, String fieldName, String oldValue, String newValue, String keyValue) {
		AmAuditlog amAuditlog = new AmAuditlog();
		amAuditlog.setActivityDate(new Date());
		amAuditlog.setActivity(activity);
		amAuditlog.setTableName(tableName);
		amAuditlog.setFieldName(fieldName);
		amAuditlog.setOldValue(oldValue);
		amAuditlog.setNewValue(newValue);
		amAuditlog.setKeyValue(keyValue);

		amAuditlog.setApplicationName(applicationname);
		amAuditlog.setSubsystemName(subsystemname);
		amAuditlog.setUuidMsUser(amMsuser);
		amAuditlog.setScreenId(screenid);
		amAuditlog.setTerminalAddress(terminaladdress);
		amAuditlog.setTerminalId(terminalid);
		amAuditlog.setTerminalUserId(terminaluserid);

		this.getManagerDAO().insert(amAuditlog);	
	}

}
