package com.adins.mss.base.businesslogic;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Queue;
import javax.jms.Session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;
import org.springframework.scheduling.annotation.Async;

public class BaseJmsLogic {
	private Logger logger = LoggerFactory.getLogger(BaseLogic.class);
	private JmsTemplate jmsTemplate;
	private Queue queue;
	
	public void setJmsTemplate(JmsTemplate jmsTemplate) {
		this.jmsTemplate = jmsTemplate;
	}

	public void setQueue(Queue queue) {
		this.queue = queue;
	}
	
	@Async
	public void sendMessage(final String message){
		this.jmsTemplate.send(this.queue, new MessageCreator() {
			@Override
			public Message createMessage(Session session) throws JMSException {
				try{
					return session.createTextMessage(message);
				} catch (JMSException e){
					logger.error(e.toString());
					return null;
				}
			}

		});
		
	}
}
