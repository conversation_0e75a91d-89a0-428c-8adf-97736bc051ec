package com.adins.mss.base.businesslogic;

import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.Queue;
import javax.jms.Session;

import org.apache.commons.lang3.LocaleUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessageCreator;
import org.springframework.scheduling.annotation.Async;

import com.adins.foundation.workflow.bussinesslogic.api.engine.WfEngineLogic;
import com.adins.framework.persistence.dao.api.AuditLog;
import com.adins.framework.persistence.dao.api.ManagerDAO;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.TrTasklink;
import com.adins.mss.model.TrTaskrejectedhistory;
import com.adins.mss.model.WfEvent;
import com.adins.mss.model.WfEventroute;

@SuppressWarnings({ "unchecked", "rawtypes" })
public class BaseLogic {
	private Logger logger = LoggerFactory.getLogger(BaseLogic.class);
	
	private ManagerDAO managerDAO;
	protected AuditLog auditManager;
	private JmsTemplate jmsTemplate;
	private Queue queue;
	private Queue queueTelecheck;
	
	@Autowired
	protected WfEngineLogic wfEngineLogic;
	
	public void setJmsTemplate(JmsTemplate jmsTemplate) {
		this.jmsTemplate = jmsTemplate;
	}

	public void setQueue(Queue queue) {
		this.queue = queue;
	}
	
	public ManagerDAO getManagerDAO() {
		return managerDAO;
	}

	public void setManagerDAO(ManagerDAO managerDAO) {
		this.managerDAO = managerDAO;
	}
	
	public void setAuditManager(AuditLog auditManager) {
		this.auditManager = auditManager;
	}

	public void setWfEngineLogic(WfEngineLogic wfEngineLogic) {
		this.wfEngineLogic = wfEngineLogic;
	}
	
	public Queue getQueueTelecheck() {
		return queueTelecheck;
	}

	public void setQueueTelecheck(Queue queueTelecheck) {
		this.queueTelecheck = queueTelecheck;
	}
	
	//-----------------------------------
	


	//commit status task order
	public void commitOrder(AmMsuser usr, String notes, TrTaskH trTaskH, AmMssubsystem subsystem, int flag, String codeProcess, AuditContext auditContext) {
		if (!GlobalVal.SUBSYSTEM_MS.equals(subsystem.getSubsystemName())) {
			return;
		}
		
		//ambil order id berdasarkan task survey
		Object[][] params = {{Restrictions.eq("uuidTaskHSurvey", trTaskH.getUuidTaskH())}};
	
		TrTasklink trTasklink = this.getManagerDAO().selectOne(TrTasklink.class, params);
		TrTaskH trTaskHOrder = this.getManagerDAO().selectOne(
			"from TrTaskH tth left join fetch tth.msStatustask where tth.uuidTaskH = :uuidTaskH", 
			new Object[][] {{"uuidTaskH", trTasklink.getUuidTaskHOrder()}});
		
		//ambil semua survey id berdasarkan order id (kecuali yg status PTS dan deleted)
		String[] taskExceptPTSDel = this.getUuidTaskExceptPTSDel(trTaskHOrder.getUuidTaskH());
		Long[] taskExceptPTSDelConvert = new Long[taskExceptPTSDel.length];
		for (int i = 0; i < taskExceptPTSDel.length; i++) {
			taskExceptPTSDelConvert[i] = Long.valueOf(taskExceptPTSDel[i]);
		}
		Object[][] paramsTask = {{Restrictions.eq("uuidTaskHOrder", trTaskHOrder.getUuidTaskH())},
									{Restrictions.in("uuidTaskHSurvey", taskExceptPTSDelConvert)}};
		Map<String, Object> mapTaskLink = this.getManagerDAO().list(TrTasklink.class, paramsTask, null);
		List<TrTasklink> listTaskLink = (List<TrTasklink>) mapTaskLink.get(GlobalKey.MAP_RESULT_LIST);
		
		//ambil sequence terakhir dari masing2 status survey
		String[][] taskArr = new String[2][listTaskLink.size()];
		logger.info("Size looping...{}", listTaskLink.size());
		int i = 0;
		for (TrTasklink taskLink : listTaskLink) {
			logger.info("Start looping...{}", taskLink.getUuidTaskHSurvey());
			TrTaskH trTaskHSurvey = this.getManagerDAO().selectOne(
					"from TrTaskH tth join fetch tth.msStatustask where tth.uuidTaskH = :uuidTaskH", 
					new Object[][] {{"uuidTaskH", taskLink.getUuidTaskHSurvey()}});
			long uuidProcess = this.getUuidProcess(trTaskHSurvey, trTaskHSurvey.getMsStatustask().getAmMssubsystem());
			Object[][] paramsEvent = {
				{Restrictions.eq("trTaskH.uuidTaskH", taskLink.getUuidTaskHSurvey())},
				{Restrictions.eq("wfProcess.uuidProcess", uuidProcess)}
			};
			WfEvent wfEvent = this.getManagerDAO().selectOne(WfEvent.class, paramsEvent);
			Object[][] paramsEventRoute = {{Restrictions.eq("wfEvent.uuidEvent", wfEvent.getUuidEvent())}};
			Object[][] ordersEventRoute = {{"wfEvent.eventRouteSeq", "DESC"}};
			Map<String, Object> wfEventrouteMap = this.getManagerDAO().list(WfEventroute.class, paramsEventRoute, ordersEventRoute);
			List<WfEventroute> wfEventroutes = (List<WfEventroute>) wfEventrouteMap.get(GlobalKey.MAP_RESULT_LIST);
			Integer eventRouteSeq = wfEventroutes.get(0).getEventRouteSeq();
			
			taskArr[0][i] = taskLink.getUuidTaskHSurvey().toString();
			taskArr[1][i] = eventRouteSeq.toString();
			i++;
		}
	
		//ambil index dari survey dengan status terkecil
		List coll = Arrays.asList(taskArr[1]);
		int minIdx = coll.indexOf(Collections.min(coll));
		
		//jika task survey yang disubmit adalah task dengan status terkecil maka update status order
		if (String.valueOf(trTaskH.getUuidTaskH()).equals(taskArr[0][minIdx])) {
			if (GlobalVal.CODE_PROCESS_ASSIGNMENT.equals(codeProcess) || GlobalVal.CODE_PROCESS_REASSIGNMENT.equals(codeProcess)) {
				trTaskHOrder.setAssignDate(new Date());
			} 
			else if (GlobalVal.CODE_PROCESS_SUBMITTED.equals(codeProcess)) {
				trTaskHOrder.setSubmitDate(new Date());
			} 
			else if (GlobalVal.CODE_PROCESS_VERIFIED.equals(codeProcess) 
					|| GlobalVal.CODE_PROCESS_REJECTED_VERIFICATION.equals(codeProcess)) {
				trTaskHOrder.setVerificationDate(new Date());
			} 
			else if (GlobalVal.CODE_PROCESS_APPROVED.equals(codeProcess) 
					|| GlobalVal.CODE_PROCESS_RELEASED.equals(codeProcess)
					|| GlobalVal.CODE_PROCESS_REJECTED_APPROVAL.equals(codeProcess)) {
				trTaskHOrder.setApprovalDate(new Date());
			}
			
			//penambahan logic terkait update workflow order
			//updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
			TrTaskH tth = this.getManagerDAO().selectOne(
				"from TrTaskH tth left join fetch tth.trTaskH join fetch tth.msStatustask where tth.uuidTaskH = :uuidTaskH", 
				new Object[][] {{"uuidTaskH", trTaskH.getUuidTaskH()}});
			if (tth.getTrTaskH() != null) {
				//cek apakah task sebelumnya PTS atau tidak
				TrTaskH taskBefore = this.getManagerDAO().selectOne(
					"from TrTaskH tth join fetch tth.msStatustask where tth.uuidTaskH = :uuidTaskH", 
					new Object[][] {{"uuidTaskH", tth.getTrTaskH().getUuidTaskH()}});
				if (GlobalVal.SURVEY_STATUS_PROMISE_TO_SURVEY.equals(taskBefore.getMsStatustask().getStatusCode())) {
					logger.info("Order status = " + trTaskHOrder.getMsStatustask().getStatusCode() + ", Status survey = " + tth.getMsStatustask().getStatusCode());
					if (GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
							&& GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
								updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					} 
					else if (GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
							&& GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
								updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					} 
					else if (GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
							&& GlobalVal.SURVEY_STATUS_TASK_REJECTED.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
								updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					} 
					else if (GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
							&& GlobalVal.SURVEY_STATUS_TASK_RELEASED.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
								updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					} 
					else if (GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
							&& GlobalVal.SURVEY_STATUS_TASK_RELEASED.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
								updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					} 
					else if (GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
							&& GlobalVal.SURVEY_STATUS_TASK_REJECTED.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
								updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					}
				} 
				else {
					if (GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
						&& GlobalVal.SURVEY_STATUS_TASK_RELEASED.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
							updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					} 
					else if (GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
						&& GlobalVal.SURVEY_STATUS_TASK_REJECTED.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
							updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					} 
					else if (GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
						&& GlobalVal.SURVEY_STATUS_TASK_RELEASED.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
							updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					}
					else if (GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
						&& GlobalVal.SURVEY_STATUS_TASK_REJECTED.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
							updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					} 
					else if (GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equalsIgnoreCase(trTaskHOrder.getMsStatustask().getStatusCode())
						&& GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equalsIgnoreCase(tth.getMsStatustask().getStatusCode())){
							updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
					}
				}
			} 
			else {
				updateStatusOrder(trTaskH, usr, notes, trTaskHOrder, flag, codeProcess, auditContext);
			}
			//end penambahan logic
		}
	}

	//get uuidProcess by flag source and subsystem
	public long getUuidProcess(TrTaskH trTaskH, AmMssubsystem subsystem) {
		if (GlobalVal.SUBSYSTEM_MS.equals(subsystem.getSubsystemName()) && "1".equals(subsystem.getIsActive())) {
			if (GlobalVal.SUBSYSTEM_MS.equals(trTaskH.getFlagSource())) {
			    String[][] param = {{"param", GlobalVal.PROCESS_CODE_MSADHOC}};
			    BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
			    return uuidProcess.longValue();
			} 
			else if (GlobalVal.SPVADHOC.equals(trTaskH.getFlagSource())) {
                String[][] param = {{"param", GlobalVal.PROCESS_CODE_MSADHOCSPV}};
                BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
                return uuidProcess.longValue();
			}
			else if (GlobalVal.MSCOREF.equals(trTaskH.getFlagSource())) {
                String[][] param = {{"param", GlobalVal.PROCESS_CODE_MSCOREF}};
                BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
                return uuidProcess.longValue();
			}
			else if (GlobalVal.MSCOREP.equals(trTaskH.getFlagSource())) {
                String[][] param = {{"param", GlobalVal.PROCESS_CODE_MSCOREP}};
                BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
                return uuidProcess.longValue();
			}
			else {
                String[][] param = {{"param", GlobalVal.PROCESS_CODE_MSCORE}};
                BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
                return uuidProcess.longValue();
			}
		}
		else if (GlobalVal.SUBSYSTEM_MC.equals(subsystem.getSubsystemName())) {
            String[][] param = {{"param", GlobalVal.PROCESS_CODE_MC}};
            BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
					selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
            return uuidProcess.longValue();
		} 
		else if (GlobalVal.SUBSYSTEM_MO.equals(subsystem.getSubsystemName())) {
			if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
                String[][] param = {{"param", GlobalVal.PROCESS_CODE_MO}};
                BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
                return uuidProcess.longValue();
			} 
			else {
                String[][] param = {{"param", GlobalVal.PROCESS_CODE_SMO}};
                BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
                return uuidProcess.longValue();
			}
		}
		else if (GlobalVal.SUBSYSTEM_MT.equals(subsystem.getSubsystemName())) {
			if (GlobalVal.PROCESS_CODE_MTCORE.equals(trTaskH.getFlagSource())) {
			    String[][] param = {{"param", GlobalVal.PROCESS_CODE_MTCORE}};
			    BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
			    return uuidProcess.longValue();
			} 
			else if (GlobalVal.PROCESS_CODE_MTADHOC.equals(trTaskH.getFlagSource())) {
                String[][] param = {{"param", GlobalVal.PROCESS_CODE_MTADHOC}};
                BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
                return uuidProcess.longValue();
			} 
			else {
                String[][] param = {{"param", GlobalVal.PROCESS_CODE_MTADHOCSPV}};
                BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
						selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
                return uuidProcess.longValue();
			}
		}
		
		return 0L;
	}

	public void updateStatusOrder(TrTaskH trTaskH, AmMsuser usr, String notes, TrTaskH trTaskHOrder, int flag, String codeProcess, AuditContext auditContext) {
		if (!GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
			return;
		}
				
		Object[][] paramsSubsystem = {{Restrictions.eq("subsystemName", GlobalVal.SUBSYSTEM_MO)}};
		AmMssubsystem amMssubsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, paramsSubsystem);
		long uuidProcess = this.getUuidProcess(trTaskHOrder, amMssubsystem);
		MsStatustask msStatustaskOrder = this.commitWf(trTaskHOrder, uuidProcess, amMssubsystem, flag);
		trTaskHOrder.setMsStatustask(msStatustaskOrder);
		trTaskHOrder.setDtmUpd(new Date());
		
		String fullname = StringUtils.EMPTY;
		if (null != usr) {
			fullname = usr.getFullName();
		}
		
		this.getManagerDAO().update(trTaskHOrder);
		//INSERT INTO CHECK HISTORY ORDER
		insertTaskHistory(auditContext, msStatustaskOrder, trTaskHOrder, notes + " Updated by Mobile Survey", 
				codeProcess, fullname);
	}

	public MsStatustask commitWfAndUpdateTaskH(TrTaskH trTaskH, long uuidProcess, AmMssubsystem subsystem, int flag) {
		MsStatustask msStatustask = this.commitWf(trTaskH, uuidProcess, subsystem, flag);
		trTaskH.setMsStatustask(msStatustask);
		trTaskH.setDtmUpd(new Date());
		this.getManagerDAO().update(trTaskH);
		return msStatustask;
	}

	public MsStatustask commitWf(TrTaskH trTaskH, long uuidProcess, AmMssubsystem subsystem, int flag) {
		if (null == trTaskH) {
			throw new IllegalArgumentException("trTaskH is required");
		}
						
		List<Long> nextTask = wfEngineLogic.getNextTaskUuid(uuidProcess, trTaskH.getUuidTaskH());
		String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess, trTaskH.getUuidTaskH(), nextTask.get(flag));
				
		Object[][] params2 = { { Restrictions.eq("statusCode", statusCode) }, { Restrictions.eq("amMssubsystem.uuidMsSubsystem", subsystem.getUuidMsSubsystem())} };
		MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);

		return msStatustask;
	}

	public void insertTaskHistory(AuditContext auditContext, MsStatustask msStatusTask,
	        TrTaskH trTaskH, String notes, String codeProcess, String fieldPerson, AmMsuser actor, TrTaskrejectedhistory trTaskRejectedHistory) {
		String fullName = auditContext.getCallerId();
		if(actor != null){
			fullName = actor.getFullName();
		}
        TrTaskhistory trTaskHistory = new TrTaskhistory(msStatusTask, trTaskH, auditContext.getCallerId(), new Date(),
    			StringUtils.left(notes, 2048), fieldPerson,  fullName, codeProcess);
        trTaskHistory.setTrTaskrejectedhistory(trTaskRejectedHistory);
        
        this.getManagerDAO().insert(trTaskHistory);
    }

	public void insertTaskHistory(AuditContext auditContext, MsStatustask msStatusTask, TrTaskH trTaskH, String notes, String codeProcess, String fieldPerson) {
		String actor = StringUtils.EMPTY;
		if(Pattern.matches("[0-9]+", auditContext.getCallerId())){
			AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
					Long.valueOf(auditContext.getCallerId()));
			actor = (null == user) ? auditContext.getCallerId() : user.getFullName();
		}
		else{
			actor = auditContext.getCallerId();
		}	
		
		TrTaskhistory trTaskHistory = new TrTaskhistory(msStatusTask, trTaskH, 
				auditContext.getCallerId(), new Date(),
				StringUtils.left(notes, 2048), fieldPerson,  actor, codeProcess);
		
		this.getManagerDAO().insert(trTaskHistory);
	}
	
	protected boolean isWfHasVerification(long uuidTaskH) {		
		final String sql = "SELECT COUNT(1) FROM TR_TASKHISTORY WHERE UUID_TASK_H=:uuidTaskH and CODE_PROCESS=:codeProcess";
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("uuidTaskH", uuidTaskH);
		paramMap.put("codeProcess", GlobalVal.CODE_PROCESS_VERIFIED);
		
		Integer count = (Integer) this.managerDAO.selectOneNativeString(sql, paramMap);		
		return (count.intValue() > 0);
	}
	
	public Locale retrieveLocaleAudit(AuditContext auditContext) {
		if (auditContext == null || auditContext.getParameters() == null 
				|| auditContext.getParameters().isEmpty()) {
			Locale locale = this.retrieveDefaultLocale();
			this.logger.trace("AuditContext is null, using default locale {}", locale);
			return locale;
		}
		
		Map<String, Object> auditParams = auditContext.getParameters();
		String auditLang = (String) auditParams.get(GlobalKey.AUDIT_KEY_LOCALE);
		if (StringUtils.isBlank(auditLang)) {
			Locale locale = this.retrieveDefaultLocale();
			this.logger.trace("AuditContext's locale not found, using default locale {}", locale);
			return locale;
		}
		
		return LocaleUtils.toLocale(auditLang);
	}
	
	public Locale retrieveDefaultLocale() {
		Object[][] param = {{Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DEFAULT_LOCALE)}};
		AmGeneralsetting gs = (AmGeneralsetting) this.getManagerDAO().selectOne(AmGeneralsetting.class, 
				param);
		if (gs == null) {
			return LocaleUtils.toLocale("id");
		}
		
		return LocaleUtils.toLocale(StringUtils.lowerCase(gs.getGsValue()));
	}
	
	@Async
	public void sendMessage(final String message){
		this.jmsTemplate.send(this.queue, new MessageCreator() {
			@Override
			public Message createMessage(Session session) throws JMSException {
				try{
					return session.createTextMessage(message);
				} catch (JMSException e){
					logger.error(e.toString());
					return null;
				}
			}

		});
	}
	
	@Async
	public void sendResultTelecheck(final String message){
		this.jmsTemplate.send(this.queueTelecheck, new MessageCreator() {
			@Override
			public Message createMessage(Session session) throws JMSException {
				try{
					return session.createTextMessage(message);
				} catch (JMSException e){
					logger.error(e.toString());
					return null;
				}
			}

		});
	}
	
	private String[] getUuidTaskExceptPTSDel(long uuid) {
		logger.info("Uuid Task order = {}", uuid);
		String[] status = {GlobalVal.SURVEY_STATUS_TASK_DELETED, GlobalVal.SURVEY_STATUS_PROMISE_TO_SURVEY};
		Object[][] params = { { "uuid", uuid },
								{ "status", status }};
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNativeString("select uuid_task_h from TR_TASK_H tth inner join MS_STATUSTASK mst on mst.UUID_STATUS_TASK = tth.UUID_STATUS_TASK where UUID_TASK_H in (select UUID_TASK_H_SURVEY from tr_tasklink where UUID_TASK_H_ORDER = :uuid) and status_code not in (:status)", params);
		if (!list.isEmpty()) {
			String[] stringResult = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = map.get("d0").toString();
			}
			return stringResult;
		} else {
			return null;
		}
	}
	
	public boolean isTaskWorkflowValid(long uuidTaskH, long uuidMsSubsystem, String flagApproval) {
		boolean isTaskWorkflowValid = false;
		Object [][] paramStatus = { {"uuidTaskH", uuidTaskH}, {"uuidMsSubsystem", uuidMsSubsystem} };
		String statusTaskCode = String.valueOf(this.getManagerDAO().selectOneNativeString(
				"SELECT TOP(1) MSA.STATUS_CODE " +
				"FROM   WF_EVENT WFE WITH (NOLOCK) " +
				"JOIN   WF_EVENTROUTE WFER WITH (NOLOCK) ON WFE.UUID_EVENT = WFER.UUID_EVENT " +
				"JOIN   WF_PROCESSROUTE WFPR WITH (NOLOCK) ON WFPR.UUID_PROCESS_ROUTE = WFER.UUID_PROCESS_ROUTE " +
				"JOIN   WF_ACTIVITY WFA WITH (NOLOCK) ON WFA.UUID_ACTIVITY = WFPR.UUID_ACTIVITY " +
				"JOIN   MS_STATUSTASK MSA WITH (NOLOCK) ON MSA.STATUS_CODE = WFA.ACTIVITY_CODE " +
				"WHERE  WFE.UUID_TASK_H = :uuidTaskH " +
				"       AND MSA.UUID_MS_SUBSYSTEM = :uuidMsSubsystem " +
				"ORDER  BY WFER.EVENT_ROUTE_SEQ DESC", paramStatus));
		
		String defaultStatusCode = null;
		if ("ApprovalDataEntry".equalsIgnoreCase(flagApproval)) {
			defaultStatusCode = "PD";
		} else {
			defaultStatusCode = "PW";
		}
		
		if (defaultStatusCode.equalsIgnoreCase(statusTaskCode)) {
			isTaskWorkflowValid = true;
		}
		return isTaskWorkflowValid;
	}
	
	public static StringBuffer removeUTFCharacters(String data){
		Pattern p = Pattern.compile("\\\\u(\\p{XDigit}{4})");
		Matcher m = p.matcher(data);
		StringBuffer buf = new StringBuffer(data.length());
		while (m.find()) {
			String ch = String.valueOf((char) Integer.parseInt(m.group(1), 16));
			m.appendReplacement(buf, Matcher.quoteReplacement(ch));
		}
		m.appendTail(buf);
		return buf;
	}
	
	public String getNikByTask(long uuidTaskH) {
		String result = null;
		
		Object [][] paramNik = { {"uuidTaskH", uuidTaskH} };
		Object nik = this.getManagerDAO().selectOneNativeString(
				"SELECT  ISNULL(TEXT_ANSWER, INT_TEXT_ANSWER) " + 
				"FROM    TR_TASK_D TTD WITH (NOLOCK) " + 
				"JOIN    TR_TASK_H TTH WITH (NOLOCK) ON TTD.UUID_TASK_H = TTH.UUID_TASK_H " + 
				"JOIN    MS_QUESTION MSQ WITH (NOLOCK) ON MSQ.UUID_QUESTION = TTD.UUID_QUESTION " + 
				"JOIN    MS_ASSETTAG MSA WITH (NOLOCK) ON MSA.UUID_ASSET_TAG = MSQ.UUID_ASSET_TAG AND MSA.ASSET_TAG_NAME = 'NIK' " + 
				"WHERE   TTD.UUID_TASK_H = :uuidTaskH", paramNik);
		if (null != nik) {
			result = String.valueOf(nik);
		}
		return result;
	}
	
	public String getOtsFlagSource(String uuidTaskH, boolean flagResurvey) {
		String result = null;
		
		if (flagResurvey) {
			Object[][] paramUuidTaskH = { {"uuidTaskH", uuidTaskH} };
			Object groupTaskIdOtsTemp = this.getManagerDAO().selectOneNativeString(
					"WITH N (UUID_TASK_H, RESURVEY_ID) AS ( " +
					"       SELECT TTH.UUID_TASK_H, TTH.RESURVEY_ID " +
					"       FROM   TR_TASK_H TTH WITH (NOLOCK) " +
					"       WHERE  TTH.UUID_TASK_H = :uuidTaskH " +
					"       UNION  ALL " +
					"       SELECT TTH1.UUID_TASK_H, TTH1.RESURVEY_ID " +
					"       FROM   N " +
					"       JOIN   TR_TASK_H TTH1 WITH (NOLOCK) ON N.RESURVEY_ID = TTH1.UUID_TASK_H " +
					") " +
					"SELECT UUID_TASK_H " +
					"from   N " +
					"WHERE  RESURVEY_ID IS NULL", paramUuidTaskH);
			if (null != groupTaskIdOtsTemp) {
				uuidTaskH = String.valueOf(groupTaskIdOtsTemp);
			}
		}
		
		if (StringUtils.isNotBlank(uuidTaskH)) {
			result = String.valueOf(this.getManagerDAO().selectOneNativeString(
					"SELECT FLAG_SOURCE_OTS FROM TBL_OTS_DATA WITH (NOLOCK) WHERE GROUP_TASK_ID = :groupTaskId",
					new Object[][] { {"groupTaskId", uuidTaskH} }));
		}
		
		return result;
	}
	
	public int getStatusBySettingApprovalOts(long uuidForm, String colName, long uuidJobAssign, String flagSourceOTS) {
		Object[][] prm = { {"uuidForm", uuidForm}, {"uuidJobAssign", uuidJobAssign} };
		StringBuilder query = new StringBuilder();
		query.append("SELECT COUNT(1) FROM MS_SETTINGAPPROVALOTS WITH (NOLOCK) ");
		query.append("WHERE UUID_FORM = :uuidForm ");
		query.append("AND UUID_JOB_ASSIGN = :uuidJobAssign ");
		if (StringUtils.isNotBlank(flagSourceOTS)) {
			query.append("AND FLAG_SOURCE = '"+flagSourceOTS+"' ");
		}
		if (StringUtils.isNotBlank(colName)) {
			query.append("AND "+ colName + " = '1'  ");
		} 
		
		return (int) this.getManagerDAO().selectOneNativeString(query.toString(), prm);
	}
	
	public Long getGrouptTaskId(String uuidTaskH) {
		StringBuilder queryGetGroupTaskId = new StringBuilder();
		queryGetGroupTaskId.append("SELECT GROUP_TASK_ID ");
		queryGetGroupTaskId.append("FROM   MS_GROUPTASK WITH (NOLOCK) ");
		queryGetGroupTaskId.append("WHERE  UUID_TASK_H = :uuidTaskH");
		Object [][] prm = { {"uuidTaskH", uuidTaskH} };
		
		return ((BigInteger) this.getManagerDAO().selectOneNativeString(queryGetGroupTaskId.toString(), prm)).longValue();
	}
	
	public Map<String, String> getSurveyorDetailForUpdatePolo(AmMsuser userTask) {
		String svyName = null;
		String svyCode = null;
		String svyEmpPosition = null;
		String svySpv = null;
		String svySpvCode = null;
		String svySpvEmpPosition = null;
		if (null != userTask) {
			this.getManagerDAO().fetch(userTask);
			this.getManagerDAO().fetch(userTask.getMsJob());
			
			svyName = userTask.getFullName();
			svyCode = userTask.getUniqueId();
			svyEmpPosition = userTask.getMsJob().getJobCode();
			
			if (null != userTask.getAmMsuser()) {
				this.getManagerDAO().fetch(userTask.getAmMsuser());
				this.getManagerDAO().fetch(userTask.getAmMsuser().getMsJob());
				
				svySpv = userTask.getAmMsuser().getFullName();
				svySpvCode = userTask.getAmMsuser().getUniqueId();
				svySpvEmpPosition = userTask.getAmMsuser().getMsJob().getJobCode();
			}
		}
		
		Map<String, String> result = new HashMap<>();
		result.put("svyName", svyName);
		result.put("svyCode", svyCode);
		result.put("svyEmpPosition", svyEmpPosition);
		result.put("svySpv", svySpv);
		result.put("svySpvCode", svySpvCode);
		result.put("svySpvEmpPosition", svySpvEmpPosition);
		
		return result;
	}
	
	public String retrieveResultTypeGlobal(String uuidTaskH, String formName, String answerProspect, String answerInterest, String flagFinal) {
		logger.info("retrieveResultTypeGlobal.params(uuidTaskH={},formName={},answerProspect={},answerInterest={},flagFinal={})", uuidTaskH, formName, answerProspect, answerInterest, flagFinal);
		String result = "-";
		Object[][] paramTaskDPros = { {"uuidTaskH", Long.valueOf(uuidTaskH)}, {"assetTagName", GlobalVal.ASSET_TAG_PROSPECT} };
		String answPros = answerProspect;
		if (StringUtils.isBlank(answPros)) {
			if ("1".equals(flagFinal)) {
				answPros = (String) this.getManagerDAO().selectOneNative("services.common.task.getAnswerCodeByAssetTagNameForFinal", paramTaskDPros);
			} else {
				answPros = (String) this.getManagerDAO().selectOneNative("services.common.task.getAnswerCodeByAssetTagName", paramTaskDPros);
			}
		}

		if (StringUtils.isNotBlank(answPros) && (GlobalVal.LOV_CODE_YA.equalsIgnoreCase(answPros) || "PROSPEK".equalsIgnoreCase(answPros))) {
			result = "Prospek";
		} else if (StringUtils.isNotBlank(answPros) && (GlobalVal.LOV_CODE_TIDAK.equalsIgnoreCase(answPros) || "INTEREST".equalsIgnoreCase(answPros))) {
			if (GlobalVal.FORM_VISIT_POLO.equals(formName)) {
				String answInter = answerInterest;
				
				if (StringUtils.isBlank(answInter)) {
					Object[][] paramTaskDInterest = { {"uuidTaskH", Long.valueOf(uuidTaskH)}, {"assetTagName", GlobalVal.ASSET_TAG_INTEREST} };
					if ("1".equals(flagFinal)) {
						answInter = (String) this.getManagerDAO().selectOneNative("services.common.task.getAnswerCodeByAssetTagNameForFinal", paramTaskDInterest);
					} else {
						answInter = (String) this.getManagerDAO().selectOneNative("services.common.task.getAnswerCodeByAssetTagName", paramTaskDInterest);
					}
				}
				if (StringUtils.isNotBlank(answInter) && GlobalVal.LOV_CODE_YA.equalsIgnoreCase(answInter)) {
					result = "Interest";
				} else if(GlobalVal.LOV_CODE_TIDAK.equalsIgnoreCase(answInter)) {
					result = "Not Interest";
				}
			} else {
				result = "Interest";
			}
		}
		
		return result;
	}
	
	public String dynamicDateFormatterByGenset(String strDate, String dateFormat) {
		if (StringUtils.isBlank(strDate)) {
			return strDate;
		}
		
		String result = strDate;
		SimpleDateFormat sdf2 = new SimpleDateFormat(dateFormat);
		if (strDate.contains("/") || strDate.contains("-")) {
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_LIST_DATE_FORMAT)} };
			AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			String gsVal = gs.getGsValue();
			String [] arrListDateFormat = gsVal.split(";");
			String message = null;
			for (int i = 0; i < arrListDateFormat.length; i++) {
				try {
					SimpleDateFormat sdf1 =  new SimpleDateFormat(arrListDateFormat[i]);
					Date dateFromString = sdf1.parse(strDate);
					result = sdf2.format(dateFromString);
					logger.info("SUCCESS PARSING DATE {} FROM FORMAT {} TO FORMAT {}", strDate, arrListDateFormat[i], dateFormat);
					break;
				} catch (Exception e) {
					message = "Error parsing date " + strDate + " with format " + arrListDateFormat[i];
				}
			}
			if (StringUtils.isNotBlank(message)) {
				logger.warn(message);
			}
		} else {
			SimpleDateFormat sdf1 = new SimpleDateFormat("ddMMyyyyHHmmss");
			try {
				Date date = sdf1.parse(strDate);
				result = sdf2.format(date);
			} catch (ParseException e) {
				logger.warn("Error parsing date {} with format {}", strDate, "ddMMyyyyHHmmss");
			}
		}
		
		return result;
	}
	
	public String retrieveSpvCro(String uuidMsUser) {
		String result = "";
		StringBuilder queryBuilder = new StringBuilder();
		queryBuilder.append("SELECT SPVCRO.FULL_NAME ");
		queryBuilder.append("FROM   AM_MSUSER SPVCRO WITH (NOLOCK) ");
		queryBuilder.append("WHERE  SPVCRO.UUID_MS_USER = :uuidMsUser");
		
		Object spvCro = this.getManagerDAO().selectOneNativeString(queryBuilder.toString(),
				new Object[][] { {"uuidMsUser", uuidMsUser} });
		if (null != spvCro) {
			result = String.valueOf(spvCro);
		}
		
		return result;
	}
	
}
