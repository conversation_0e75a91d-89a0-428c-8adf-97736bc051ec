<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.adins.mss</groupId>
    <artifactId>com.adins.mss.base</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>com.adins.mss.base.controller</artifactId>
  <dependencies>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.mvc.struts2</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.tool.servlet</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
	<dependency>
		<groupId>com.adins.framework</groupId>
		<artifactId>com.adins.framework.tool.properties</artifactId>
		<version>${adins-framework.version}</version>
	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.persistence.dao-model</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>javax.servlet</groupId>
  		<artifactId>javax.servlet-api</artifactId>
  		<version>${servlet-api.version}</version>
  		<scope>provided</scope>
  	</dependency>
  	<dependency>
		<groupId>org.apache.struts</groupId>
		<artifactId>struts2-convention-plugin</artifactId>
		<version>${struts2.version}</version>
	</dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.model</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.core</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
	    <groupId>org.directwebremoting</groupId>
	    <artifactId>dwr</artifactId>
	    <version>${dwr.version}</version>
	</dependency>
	<dependency>
	    <groupId>javax.jms</groupId>
	    <artifactId>javax.jms-api</artifactId>
	    <version>${jms.version}</version>
	</dependency>
	<dependency>
  		<groupId>javax.servlet</groupId>
  		<artifactId>javax.servlet-api</artifactId>
  		<version>${servlet-api.version}</version>
  		<scope>provided</scope>
  	</dependency>
  </dependencies>
</project>