package com.adins.mss.base.controller;

import java.lang.reflect.Method;

import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.time.StopWatch;
import org.directwebremoting.AjaxFilter;
import org.directwebremoting.AjaxFilterChain;
import org.directwebremoting.WebContextFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import com.adins.framework.mvc.model.interfaces.LoginIdAware;
import com.adins.framework.mvc.struts2.Struts2Constants;
import com.adins.mss.constants.GlobalKey;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class AdInsAjaxFilter implements AjaxFilter {
    private static final Logger LOG = LoggerFactory.getLogger(AdInsAjaxFilter.class);

    @Override
    public Object doFilter(Object obj, Method method, Object[] params, AjaxFilterChain chain)
            throws Exception {
        StopWatch sw = new StopWatch();
        sw.start();
        HttpSession session = WebContextFactory.get().getSession(false);
        if (session == null ||
                session.getAttribute(GlobalKey.SESSION_LOGIN) == null) {
            LOG.warn("Ajax called from unauthenticated session!");
            throw new SecurityException("Not authenticated!");
        }
        
        LoginIdAware loginBean = (LoginIdAware) session.getAttribute(GlobalKey.SESSION_LOGIN);
        MDC.put(Struts2Constants.MDC_KEY_LOGIN_ID, loginBean.getLoginId());
        
        if (LOG.isTraceEnabled()) {
            Gson gson = new GsonBuilder().serializeNulls().create();            
            LOG.trace("Exec {}.{}({})", obj.getClass().getSimpleName(), method.getName(),
                    gson.toJson(params));
        }
        
        try {
            return chain.doFilter(obj, method, params);
        }
        finally {
            sw.stop();
            LOG.info("Exec {}.{}=<{}>ms", obj.getClass().getSimpleName(), method.getName(), sw.getTime());
            MDC.remove(Struts2Constants.MDC_KEY_LOGIN_ID);
        }
    }

}
