package com.adins.mss.base.controller;

import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.convention.annotation.InterceptorRef;

import com.adins.framework.mvc.struts2.Struts2ActionBase;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.servlet.InetAddressUtils;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings("serial")
@InterceptorRef(value="AdInsStack")
public class AdinsActionBase extends Struts2ActionBase{

	public AdinsActionBase() {
		 this.exceptionMethodMap = new HashMap<>();
		 this.exceptionMethodMap.put(GlobalVal.ACTION_EXCEPTION_METHOD_NAME_DEFAULT_LIST,GlobalVal.ACTION_EXCEPTION_METHOD_NAME_DEFAULT_LIST);
		 this.exceptionMethodMap.put(GlobalVal.ACTION_EXCEPTION_METHOD_NAME_DEFAULT_EDIT,GlobalVal.ACTION_EXCEPTION_METHOD_NAME_DEFAULT_LIST);
		 this.exceptionMethodMap.put(GlobalVal.ACTION_EXCEPTION_METHOD_NAME_DEFAULT_SAVE,GlobalVal.ACTION_EXCEPTION_METHOD_NAME_DEFAULT_EDIT);
		 this.exceptionMethodMap.put(GlobalVal.ACTION_EXCEPTION_METHOD_NAME_DEFAULT_DELETE,GlobalVal.ACTION_EXCEPTION_METHOD_NAME_DEFAULT_LIST);
	}	
	
	protected AuditContext getAuditContext(String screenId) {
		AuditContext result = new AuditContext();
		
		String callerId = null;
		String appName = null;
		String subsysName = null;
		String tenantCode = null;
		
		Map<String, Object> parameters = new HashMap<String, Object>();
		
		if (null != getLoginBean()) {
		    AmMsuser user = getLoginBean().getBean();
			callerId = String.valueOf(getLoginBean().getBean().getUuidMsUser());
			tenantCode = getLoginBean().getTenantCode();
			appName = getLoginBean().getBean().getAmMssubsystem().getAmMsapplication().getApplicationName();
			subsysName = getLoginBean().getBean().getAmMssubsystem().getSubsystemName();
			
			parameters.put(AuditContext.KEY_PARAMS_LOGINID, user.getLoginId());
		}
		
		if (StringUtils.isNotBlank(tenantCode)) {
		    callerId = StringUtils.join(callerId, String.valueOf(GlobalVal.TENANT_TOKEN), tenantCode);
		}
		result.setCallerId(callerId);
//		String[] inetAddr = InetAddressUtils.getInetAddressInfo(getServletRequest()); //COMMENTED - HostName lookup cost much time
		String localLoginName = System.getProperty("user.name").toString();
	
		parameters.put(AuditContext.KEY_PARAMS_APPLICATIONNAME, appName);
		parameters.put(AuditContext.KEY_PARAMS_SUBSYSTEMNAME, subsysName);
		parameters.put(AuditContext.KEY_PARAMS_SCREENID, screenId);
		parameters.put(AuditContext.KEY_PARAMS_TERMINALADDRESS, getServletRequest().getRemoteAddr());
		parameters.put(AuditContext.KEY_PARAMS_TERMINALID, getServletRequest().getRemoteAddr());
		parameters.put(AuditContext.KEY_PARAMS_TERMINALUSERID, localLoginName);
		
		result.setParameters(parameters);
		
		return result;
	}
    
    protected LoginBean getLoginBean() {
    	return (LoginBean) ServletActionContext.getRequest().getSession(false).getAttribute(GlobalKey.SESSION_LOGIN);
    }

    protected void setLoginBean(LoginBean loginBean) {
    	ServletActionContext.getRequest().getSession(true).setAttribute(GlobalKey.SESSION_LOGIN, loginBean);
    }
    
    protected void setSystemTimeout(int systemTimeout) {
    	ServletActionContext.getRequest().getSession().setMaxInactiveInterval(systemTimeout);
    }
    
    protected Map<String, String> sortingMap (List list) {
		Map<String, String> mapCombo = new HashMap<String, String>();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				Map temp = (Map) list.get(i);
				mapCombo.put(temp.get("d0").toString(), (String) temp.get("d1"));
			}
		}
		Map<String, String> map = sortByValues(mapCombo);
		return map;
	}
    
    protected Map sortByValues(Map<String, String> maps) {
		List list = new LinkedList(maps.entrySet());

		Collections.sort(list, new Comparator() {
			public int compare(Object o1, Object o2) {
				return ((Comparable) ((Map.Entry) (o1)).getValue())
						.compareTo(((Map.Entry) (o2)).getValue());
			}
		});

		Map sortedMap = new LinkedHashMap();
		for (Iterator it = list.iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			sortedMap.put(entry.getKey(), entry.getValue());
		}
		return sortedMap;
	}
}
