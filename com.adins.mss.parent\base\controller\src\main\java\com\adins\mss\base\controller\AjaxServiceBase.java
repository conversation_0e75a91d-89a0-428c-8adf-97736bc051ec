package com.adins.mss.base.controller;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.servlet.InetAddressUtils;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;

public class AjaxServiceBase {

    protected String[][] resolveFilters(String[] columns, String[] values) {
        if (ArrayUtils.isEmpty(columns))
            return null;
        
        String[][] params = new String[columns.length][2];
        for (int x = 0; x < columns.length; x++) {
            params[x][0] = columns[x];
            if (!values[x].isEmpty()) {
                params[x][1] = values[x];
            } else {
                params[x][1] = null;
            }
        }
        return params;
    }
    
    protected String[][] resolveFilters(String[][] columns, String[] types, String[] values) {
        if (ArrayUtils.isEmpty(columns))
            return null;
        
        String[][] params = new String[columns.length][2];
        for (int i = 0; i < columns.length; i++) {
            int type = NumberUtils.toInt(types[i]);
            if (type > (columns[i].length - 1) || type < 0) {
                continue;
            }           
            params[i][0] = columns[i][type];
            if (StringUtils.isBlank(values[i]))
                params[i][1] = null;
            else
                params[i][1] = values[i];
        }
        
        return params;
    }

    protected AuditContext getAuditContext(String screenId, HttpServletRequest servletRequest) {
        AuditContext result = new AuditContext();

        String callerId = null;
        String appName = null;
        String subsysName = null;
        String tenantCode = null;

        Map<String, Object> parameters = new HashMap<String, Object>();

        LoginBean loginBean = this.getLoginBean(servletRequest);
        if (loginBean != null) {
            AmMsuser user = loginBean.getBean();
            callerId = String.valueOf(loginBean.getBean().getUuidMsUser());
            tenantCode = loginBean.getTenantCode();
            appName = loginBean.getBean().getAmMssubsystem().getAmMsapplication().getApplicationName();
            subsysName = loginBean.getBean().getAmMssubsystem().getSubsystemName();

            parameters.put(AuditContext.KEY_PARAMS_LOGINID, user.getLoginId());
        }

        if (StringUtils.isNotBlank(tenantCode)) {
            callerId = StringUtils.join(callerId, String.valueOf(GlobalVal.TENANT_TOKEN), tenantCode);
        }
        result.setCallerId(callerId);
//        String[] inetAddr = InetAddressUtils.getInetAddressInfo(servletRequest); //COMMENTED - HostName lookup cost much time
        String localLoginName = System.getProperty("user.name").toString();

        parameters.put(AuditContext.KEY_PARAMS_APPLICATIONNAME, appName);
        parameters.put(AuditContext.KEY_PARAMS_SUBSYSTEMNAME, subsysName);
        parameters.put(AuditContext.KEY_PARAMS_SCREENID, screenId);
        parameters.put(AuditContext.KEY_PARAMS_TERMINALADDRESS, servletRequest.getRemoteAddr());
        parameters.put(AuditContext.KEY_PARAMS_TERMINALID, servletRequest.getRemoteAddr());
        parameters.put(AuditContext.KEY_PARAMS_TERMINALUSERID, localLoginName);

        result.setParameters(parameters);

        return result;
    }
    
    protected LoginBean getLoginBean(HttpServletRequest servletRequest) {
        return (LoginBean) servletRequest.getSession(false).getAttribute(GlobalKey.SESSION_LOGIN);
    }
}
