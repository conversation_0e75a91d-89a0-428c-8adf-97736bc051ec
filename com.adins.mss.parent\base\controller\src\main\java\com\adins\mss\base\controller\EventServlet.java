package com.adins.mss.base.controller;

import java.io.IOException;

import javax.servlet.AsyncContext;
import javax.servlet.AsyncEvent;
import javax.servlet.AsyncListener;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EventServlet  extends HttpServlet {
	private static final long serialVersionUID = 1L;
	private static final Logger LOG = LoggerFactory.getLogger(EventServlet.class);
	private JmsListener listener;
	/**
	 * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
	 */
	protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException {
		//content type must be set to text/event-stream
		if(listener == null)
			listener = new JmsListener();
		response.setContentType("text/event-stream");
		response.setCharacterEncoding("UTF-8");
		final AsyncContext asyncContext = request.startAsync(request, response);
		asyncContext.setTimeout(0); //to avoid ERR_INCOMPLETE_CHUNKED_ENCODING
		
		asyncContext.addListener(new AsyncListener() {
			@Override
			public void onComplete(AsyncEvent event) throws IOException {
				LOG.trace("--ASYNC EVENT COMPLETE-- ");
				AsyncContext asyncContext = event.getAsyncContext();
				listener.removeQueue(asyncContext);
			}

			@Override
			public void onError(AsyncEvent event) throws IOException {
				LOG.trace("--ASYNC EVENT ERROR--");
				AsyncContext asyncContext = event.getAsyncContext();
				listener.removeQueue(asyncContext);
			}

			@Override
			public void onStartAsync(AsyncEvent event) throws IOException {
				LOG.trace("--ASYNC EVENT START--");
			}

			@Override
			public void onTimeout(AsyncEvent event) throws IOException {
				LOG.trace("--ASYNC EVENT TIMEOUT--");
				AsyncContext asyncContext = event.getAsyncContext();
				listener.removeQueue(asyncContext);
			}			
		});
		
		listener.addQueue(asyncContext);
	}
	
//	protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException {
//			Gson gson = new Gson();
//			StringBuilder sb = new StringBuilder();
//			String s;
//			try {
//				while ((s = request.getReader().readLine()) != null) {
//				     sb.append(s);
//				 }
//				MessageEventBean bean = gson.fromJson(sb.toString(), MessageEventBean.class);
//			} catch (IOException e1) {
//				e1.printStackTrace();
//			}
//			
//	}
}
