package com.adins.mss.base.controller;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;

import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.TextMessage;
import javax.servlet.AsyncContext;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.adins.mss.model.custom.MessageEventBean;
import com.adins.mss.model.custom.SseModel;
import com.google.gson.Gson;

public class JmsListener implements MessageListener{
	private  final Logger LOG = LoggerFactory.getLogger(JmsListener.class);
	private  static LinkedBlockingDeque<AsyncContext> queues = new LinkedBlockingDeque<>();

	public  void addQueue(AsyncContext asyncContext) {
		queues.add(asyncContext);
	}
	
	public void removeQueue(AsyncContext asyncContext) {
		queues.remove(asyncContext);
	}

	@Override
	public void onMessage(Message message) {
		try {	
			Gson gson = new Gson();
			String messageText = ((TextMessage) message).getText();
			MessageEventBean bean = gson.fromJson(messageText, MessageEventBean.class);
			for (Iterator<AsyncContext> iterator = queues.iterator(); iterator.hasNext();) {
				AsyncContext asyncContext = iterator.next();
				HttpServletResponse response = (HttpServletResponse) asyncContext.getResponse();
				PrintWriter writer = response.getWriter();
				SseModel sseResponse = new SseModel(bean.getEvent(), messageText);
				writer.print(sseResponse);
				boolean error = writer.checkError();
				if (error) { //Changed page on browser will result error, but not on Single Page Application 
					LOG.debug("Error writer (probably on different page now), removing from context...");
					asyncContext.complete(); //end response
					iterator.remove(); //remove from queues
				}
			}
		} catch (Exception e) {
			LOG.error(e.getMessage(), e);
		}
	}

}
