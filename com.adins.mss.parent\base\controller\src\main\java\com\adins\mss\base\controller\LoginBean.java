package com.adins.mss.base.controller;

import java.util.HashMap;
import java.util.Map;

import com.adins.framework.mvc.model.interfaces.LoginIdAware;
import com.adins.framework.mvc.model.interfaces.TenantCodeAware;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings("serial")
public class LoginBean implements java.io.Serializable, LoginIdAware, TenantCodeAware {
    private AmMsuser bean = new AmMsuser();
	private Map map = new HashMap();
	private String tenantCode;
	
	public AmMsuser getBean() {
		return bean;
	}
	public void setBean(AmMsuser bean) {
		this.bean = bean;
	}
	public Map getMap() {
		return map;
	}
	public void setMap(Map map) {
		this.map = map;
	}
    public String getTenantCode() {
        return tenantCode;
    }
    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
    @Override
    public String getLoginId() {
        return (bean == null) ? null : bean.getLoginId();
    }

}
