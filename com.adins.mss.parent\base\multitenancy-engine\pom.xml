<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.adins.mss</groupId>
    <artifactId>com.adins.mss.base</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>com.adins.mss.base.multitenancy-engine</artifactId>
  <dependencies>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jdbc</artifactId>
      <version>${spring-framework.version}</version>
    </dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.core</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
  		<groupId>org.slf4j</groupId>
  		<artifactId>slf4j-api</artifactId>
  		<version>${slf4j.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>org.aspectj</groupId>
  		<artifactId>aspectjrt</artifactId>
  		<version>${aspectj.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>org.aspectj</groupId>
  		<artifactId>aspectjweaver</artifactId>
  		<version>${aspectj.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>org.springframework</groupId>
  		<artifactId>spring-aop</artifactId>
  		<version>${spring-framework.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>org.apache.commons</groupId>
  		<artifactId>commons-lang3</artifactId>
  		<version>${commons-lang3.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.businesslogic-api.multitenancy</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
	<dependency>
		<groupId>org.apache.cxf</groupId>
		<artifactId>cxf-core</artifactId>
		<version>${cxf.version}</version>
		<optional>true</optional>
	</dependency>
	<dependency>
		<groupId>com.fasterxml.jackson.datatype</groupId>
		<artifactId>jackson-datatype-hibernate4</artifactId>
		<version>${jackson-fasterxml.version}</version>
	</dependency>
	<dependency>
		<groupId>org.springframework.security.oauth</groupId>
		<artifactId>spring-security-oauth2</artifactId>
		<version>${spring-framework-oauth.version}</version>
		<optional>true</optional>
	</dependency>
  	<dependency>
  		<groupId>javax.servlet</groupId>
  		<artifactId>javax.servlet-api</artifactId>
  		<version>${servlet-api.version}</version>
  		<scope>provided</scope>
  		<optional>true</optional>
  	</dependency>
  </dependencies>
</project>