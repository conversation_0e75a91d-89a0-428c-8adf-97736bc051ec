package com.adins.mss.multitenancy;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

public class DefaultRoutingDataSource extends AbstractRoutingDataSource {
    private static final Logger LOG = LoggerFactory.getLogger(DefaultRoutingDataSource.class);

	@Override
	protected Object determineCurrentLookupKey() {
	    LOG.trace("[Schema routing={}]", TenantContextHolder.getSchema());
		return TenantContextHolder.getSchema();
	}

}
