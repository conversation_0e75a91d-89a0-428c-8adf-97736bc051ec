package com.adins.mss.multitenancy;

import com.adins.framework.exception.AdInsException;
import com.adins.mss.exceptions.StatusCodes;


@SuppressWarnings("serial")
public class MultitenantException extends AdInsException {

    public static enum Reason {
        INVALID_TENANT_CODE,
        MISSING_TENANT
    }
    
    private Reason reason;
    
    public Reason getReason() {
        return reason;
    }        
        
    public MultitenantException(Reason reason) {
        super();
        this.reason = reason;
    }

    public MultitenantException(String message, Reason reason) {
        super(message);
        this.reason = reason;
    }

    public MultitenantException(Throwable cause, Reason reason) {
        super(cause);
        this.reason = reason;
    }

    public MultitenantException(String message, Throwable cause, Reason reason) {
        super(message, cause);
        this.reason = reason;
    }

    @Override
    public int getErrorCode() {
        switch (reason) {
            case INVALID_TENANT_CODE:
                return StatusCodes.MULTITENANT_INVALID_CODE;

            default:
                return StatusCodes.UNKNOWN;
        }
    }

}
