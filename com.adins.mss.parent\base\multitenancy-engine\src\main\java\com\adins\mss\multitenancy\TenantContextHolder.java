package com.adins.mss.multitenancy;

import org.slf4j.MDC;

public class TenantContextHolder {
	private static final ThreadLocal<String> schemaHolder = new ThreadLocal<String>();
    private static final String KEY_MDC_TENANTID = "tenantId";

	public static void setSchema(String schema) {
	    MDC.put(KEY_MDC_TENANTID, schema);
		schemaHolder.set(schema);
	}

	public static String getSchema() {
		return schemaHolder.get();
	}

	public static void clearSchema() {
        MDC.remove(KEY_MDC_TENANTID);
		schemaHolder.remove();
	}
}