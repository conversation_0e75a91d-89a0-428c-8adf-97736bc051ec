package com.adins.mss.multitenancy.aspect;

import java.util.List;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.businesslogic.api.multitenancy.MultitenantLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.multitenancy.MultitenantException;
import com.adins.mss.multitenancy.TenantContextHolder;
import com.adins.mss.multitenancy.model.MsTenant;
import com.adins.mss.multitenancy.util.MultitenancyUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.hibernate4.Hibernate4Module;

public class TenantBusinessLogicAspect {
    private static final Logger LOG = LoggerFactory.getLogger(TenantBusinessLogicAspect.class);
    
    private MultitenantLogic multitenantLogic;
    
    //--spring injection
    public void setMultitenantLogic(MultitenantLogic multitenantLogic) {
        this.multitenantLogic = multitenantLogic;
    }
        
    public Object aroundBusinesslogic(ProceedingJoinPoint joinpoint) throws Throwable {
        Object[] args = joinpoint.getArgs();
        String targetClass = joinpoint.getTarget().getClass().getSimpleName();
        String targetMethod = joinpoint.getSignature().getName();
        
        if (ArrayUtils.isEmpty(args)) {
            LOG.error("Missing AuditContext type argument\n\tTarget class={}\n\tMethod signature={}",
                    joinpoint.getTarget().getClass(),
                    joinpoint.getSignature());
            throw new MultitenantException("Method's missing AuditContext type argument",
                    MultitenantException.Reason.MISSING_TENANT);
        }
        
        if (StringUtils.isNotBlank(TenantContextHolder.getSchema())){
            this.stripTenant(args);
            return joinpoint.proceed(args);
        }
        
        if (!this.isSingleTenantImpl()) {
            for (Object arg : args) {
                if (arg instanceof AuditContext) {
                    AuditContext auditContext = (AuditContext) arg;                
                    String schema = MultitenancyUtils.extractTenantId(auditContext.getCallerId(),
                            GlobalVal.TENANT_TOKEN);
                    
                    if (StringUtils.isBlank(schema))
                        continue;
                    
                    TenantContextHolder.setSchema(schema);
                    break;
                }
            }
        }
        
        this.stripTenant(args);
        
        ObjectMapper mapper = new ObjectMapper();
        
        if (LOG.isTraceEnabled()) {
            mapper.registerModule(new Hibernate4Module());
            mapper.configure(SerializationFeature.INDENT_OUTPUT, false);
            mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            
            LOG.trace("{}.{} ARGUMENTS={}", targetClass, targetMethod,
                    mapper.writeValueAsString(args));
        }
        
        LOG.debug("Start invoke {}.{}", targetClass, targetMethod);
        StopWatch sw = new StopWatch();
        sw.start();
        Object invokeResult = joinpoint.proceed(args);
        sw.stop();
        LOG.debug("Finish invoke {}.{}={}ms", targetClass, targetMethod, sw.getTime());
        
        if (LOG.isTraceEnabled()) {
            LOG.trace("{}.{} RESULTS={}", targetClass, targetMethod,
                    mapper.writeValueAsString(invokeResult));        
        }
        
        return invokeResult;
    }
    
    private boolean isSingleTenantImpl() {
        //--check if single tenant metadata configured
        AuditContext auditContext = new AuditContext("SYSTEM");
        int noOfTenants = this.multitenantLogic.countTenants(auditContext);
        if (noOfTenants == 0)
            throw new RuntimeException("Tenants metadata must be configured!");
            
        if (noOfTenants == 1) {
        	List<MsTenant> tenants = this.multitenantLogic.listTenants(null, auditContext);
            String schema = tenants.get(0).getTenantCode();
            TenantContextHolder.setSchema(schema);
            return true;
        }
        
        return false;
    }
    
    private void stripTenant(Object[] objArgs) {
        for (Object arg : objArgs) {
            if (arg instanceof AuditContext) {
                AuditContext auditContext = (AuditContext) arg;
                this.stripTenant(auditContext);
            }
        }
    }
    
    private void stripTenant(AuditContext auditContext) {
        String strippedCallerId = MultitenancyUtils.extractLoginId(auditContext.getCallerId(),
                GlobalVal.TENANT_TOKEN);
        LOG.trace("CallerId={}, Stripped={}", auditContext.getCallerId(), strippedCallerId);
        auditContext.setCallerId(strippedCallerId);
    }
}