package com.adins.mss.multitenancy.aspect;

import java.util.List;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ClassUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.businesslogic.api.multitenancy.MultitenantLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.multitenancy.MultitenantException;
import com.adins.mss.multitenancy.TenantContextHolder;
import com.adins.mss.multitenancy.model.MsTenant;
import com.adins.mss.multitenancy.util.MultitenancyUtils;

public class TenantLoginAspect {
    private static final Logger LOG = LoggerFactory.getLogger(TenantLoginAspect.class);
    public static final char TENANT_TOKEN = GlobalVal.TENANT_TOKEN;    
    
    public static final int LOGIN_ID_ARG_POSITION = 0;
    
    private MultitenantLogic multitenantLogic;
    
    //--spring injection
    public void setMultitenantLogic(MultitenantLogic multitenantLogic) {
        this.multitenantLogic = multitenantLogic;
    }
    
    public Object aroundMobileLogin(ProceedingJoinPoint joinpoint) throws Throwable {       
        TenantContextHolder.clearSchema();
        
        return this.doRouting(joinpoint, true);
    }

    public Object aroundLogin(ProceedingJoinPoint joinpoint) throws Throwable {
        TenantContextHolder.clearSchema();
        
        return this.doRouting(joinpoint, false);
    }
    
    public Object aroundAuthentication(ProceedingJoinPoint joinPoint) throws Throwable {
        TenantContextHolder.clearSchema();
        
        return this.doRoutingOnAuthentication(joinPoint);
    }
    
    private Object doRoutingOnAuthentication(ProceedingJoinPoint joinpoint) throws Throwable {
        Object[] loginArgs = joinpoint.getArgs();
        
        if (ArrayUtils.isEmpty(loginArgs) || loginArgs.length != 1 || !(loginArgs[0] instanceof Authentication)) {
            LOG.warn("ASPECT not set on AuthenticationProvider.authenticate method!! please check your pointcut expression");
            return joinpoint.proceed(loginArgs);
        }

        AuditContext auditContext = new AuditContext("SYSTEM");
        List<MsTenant> tenants = multitenantLogic.listTenants(null, auditContext);
        if (tenants == null || tenants.isEmpty())
            throw new RuntimeException("Tenants metadata must be configured!");
        
        Authentication authentication = (Authentication) loginArgs[0];
        String loginIdTenant = (String) authentication.getPrincipal();        
        
        this.trySetRouting(tenants, loginIdTenant);
        
        return joinpoint.proceed(loginArgs);
    }
    
    private Object doRouting(ProceedingJoinPoint joinpoint, boolean isMobile) throws Throwable {
        Object[] loginArgs = joinpoint.getArgs();
        String targetClass = joinpoint.getTarget().getClass().getSimpleName();
        String targetMethod = joinpoint.getSignature().getName();
        
        AuditContext auditContext = new AuditContext("SYSTEM");
        List<MsTenant> tenants = multitenantLogic.listTenants(null, auditContext);
        if (tenants == null || tenants.isEmpty())
            throw new RuntimeException("Tenants metadata must be configured!");
        
        String loginIdArg = null;
        if (isMobile) {
            loginIdArg = MultitenancyUtils.findLoginIdInAuditContext(loginArgs);
        }
        else {
            loginIdArg = MultitenancyUtils.findLoginId(loginArgs);
        }

        this.trySetRouting(tenants, loginIdArg);
        
        Class<?> loginIdType = loginArgs[LOGIN_ID_ARG_POSITION].getClass();
        if (ClassUtils.isAssignable(loginIdType, String.class)) {
            if (StringUtils.countMatches((String) loginArgs[LOGIN_ID_ARG_POSITION],
                    String.valueOf(TENANT_TOKEN)) == 1) {
                loginArgs[LOGIN_ID_ARG_POSITION] = MultitenancyUtils.extractLoginId(
                        loginIdArg, TENANT_TOKEN);
            }
        }
        
        String schema = TenantContextHolder.getSchema();
        this.setAuditContextTenant(loginArgs, loginIdArg, schema);
        
        LOG.debug("Start invoke {}.{}", targetClass, targetMethod);
        StopWatch sw = new StopWatch();
        sw.start();        
        Object invokeResult = joinpoint.proceed(loginArgs);
        sw.stop();
        LOG.debug("Finish invoke {}.{}={}ms", targetClass, targetMethod, sw.getTime());
        
        return invokeResult;
    }
    
    private void trySetRouting(List<MsTenant> tenants, String loginIdTenant) {
        if (tenants.size() == 1) {
            String schema = tenants.get(0).getTenantCode();
            TenantContextHolder.setSchema(schema);
        }
        else {
            String loginTenantCode = StringUtils.upperCase(
                    MultitenancyUtils.extractTenantId(loginIdTenant, TENANT_TOKEN));                        
            
            for (MsTenant tenant : tenants) {
                String dbTenantCode = tenant.getTenantCode();
                if (StringUtils.equalsIgnoreCase(dbTenantCode, loginTenantCode)) {
                    TenantContextHolder.setSchema(dbTenantCode);
                    
                    return;
                }
            }
            
            if (StringUtils.isBlank(TenantContextHolder.getSchema())) {
                LOG.error("Tenant code not found. Id={}", loginTenantCode);
                throw new MultitenantException("Invalid tenant code",
                        MultitenantException.Reason.INVALID_TENANT_CODE);
            }
        }
    }
    
    private void setAuditContextTenant(Object[] args, String loginId, String tenantCode) {
        //set tenantCode to auditContext
        for (Object arg : args) {
            if (arg instanceof AuditContext) {
                AuditContext auditContextLogin = (AuditContext) arg;
                
                String callerId = null;
                if (StringUtils.containsNone(loginId, TENANT_TOKEN)) {
                    callerId = StringUtils.join(auditContextLogin.getCallerId(),
                            String.valueOf(TENANT_TOKEN), tenantCode);
                }
                else {
                    callerId = loginId;
                }
                auditContextLogin.setCallerId(callerId);
                break;
            }
        }
    }
}
