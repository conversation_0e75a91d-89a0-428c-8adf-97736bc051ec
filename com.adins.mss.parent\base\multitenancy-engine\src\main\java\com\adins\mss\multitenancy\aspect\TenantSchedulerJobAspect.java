package com.adins.mss.multitenancy.aspect;

import java.util.Locale;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;

import com.adins.mss.multitenancy.TenantContextHolder;

public class TenantSchedulerJobAspect implements ApplicationContextAware, MessageSourceAware {
    private static final Logger LOG = LoggerFactory.getLogger(TenantSchedulerJobAspect.class);    
    public static final String SPRING_MULTITENANT_DS_ID = "Mss.MultitenantDataSourceMap"; 
    private ApplicationContext applicationContext;
    @Autowired
   	private MessageSource messageSource;
       
    @Override
   	public void setMessageSource(MessageSource messageSource) {
   		this.messageSource = messageSource;
   	}
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    
    public void aroundRunJob(ProceedingJoinPoint joinPoint) throws Throwable {
        Set<String> tenants = this.listTenants();
        if (tenants == null || tenants.isEmpty())
            throw new RuntimeException(this.messageSource.getMessage("multitenant.error.metadata",
            		null,Locale.ENGLISH));
        

        String targetClass = joinPoint.getTarget().getClass().getSimpleName();
        String targetMethod = joinPoint.getSignature().getName();

        StopWatch sw = new StopWatch();
        for (String tenantCode : tenants) {
            TenantContextHolder.setSchema(tenantCode);
            LOG.info("Start invoke {}.{}", targetClass, targetMethod);
            
            sw.reset();
            sw.start();
            joinPoint.proceed(joinPoint.getArgs());                            
            sw.stop();
            
            LOG.info("Finish invoke {}.{}={}ms", targetClass, targetMethod, sw.getTime());
            TenantContextHolder.clearSchema();
        }   
    }
    
    @SuppressWarnings("unchecked")
	private Set<String> listTenants() {
        Map<String, String> tenantMap = this.applicationContext.getBean(SPRING_MULTITENANT_DS_ID, Map.class);
        return tenantMap.keySet();
    }
}
