package com.adins.mss.multitenancy.cxf;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.adins.mss.multitenancy.TenantContextHolder;

public class MultitenantInterceptor extends AbstractPhaseInterceptor<Message> {
    private static final Logger LOG = LoggerFactory.getLogger(MultitenantInterceptor.class);
    
    public MultitenantInterceptor() {
        super(Phase.PRE_INVOKE);
    }

    @Override
    public void handleMessage(Message message) throws Fault {
        Object isRequest = message.get(Message.REQUESTOR_ROLE);        
        if (BooleanUtils.isTrue((Boolean) isRequest)) {
            // bypass intercept for request message (e.g: request made by WebClient to external service)
            return;
        }
            
        LOG.trace("Clear tenant routing");
        TenantContextHolder.clearSchema();        
    }
}
