package com.adins.mss.multitenancy.oauth2;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.authentication.BearerTokenExtractor;

import com.adins.mss.constants.GlobalKey;
import com.adins.mss.multitenancy.TenantContextHolder;

public class TenantTokenExtractor extends BearerTokenExtractor {
    
    @Override
    public Authentication extract(HttpServletRequest request) {
       Authentication authentication = super.extract(request);
       
       String tenant = this.extractHeaderTenant(request);
       if (StringUtils.isNotBlank(tenant)) {
           TenantContextHolder.setSchema(tenant);
       }
       
       return authentication;
    }
    
    /**
     * Extract the Tenant from a header.
     * 
     * @param request The request.
     * @return The tenant code, or null if no tenant header was supplied.
     */
    protected String extractHeaderTenant(HttpServletRequest request) {
        Enumeration<String> headers = request.getHeaders(GlobalKey.HTTP_HEADER_TENANT);
        while (headers.hasMoreElements()) { // typically there is only one (most servers enforce that)
            String value = headers.nextElement();
            return StringUtils.upperCase(value);
        }

        return null;
    }
}
