package com.adins.mss.multitenancy.util;

import java.util.Locale;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.multitenancy.MultitenantException;
import com.adins.mss.multitenancy.aspect.TenantLoginAspect;

public class MultitenancyUtils {
    private static final Logger LOG = LoggerFactory.getLogger(MultitenancyUtils.class);
    
   	private static MessageSource messageSource;
           
   	public static void setMessageSource(MessageSource messageSource) {
   		MultitenancyUtils.messageSource = messageSource;
   	}
    
    public static String findLoginId(Object[] loginArgs) {
    	if (ArrayUtils.isEmpty(loginArgs)){
        	String message = messageSource.getMessage("multitenant.error.cannotempty", null, Locale.ENGLISH);
            throw new IllegalArgumentException(message);
        }
        
        Object loginId = loginArgs[TenantLoginAspect.LOGIN_ID_ARG_POSITION]; //depends on LoginLogic.doLogin
        return (loginId == null) ? null : (String) loginId;
    }
    
    public static String findLoginIdInAuditContext(Object[] loginArgs) {
        if (ArrayUtils.isEmpty(loginArgs)){
        	String message = messageSource.getMessage("multitenant.error.cannotempty", null, Locale.ENGLISH);
            throw new IllegalArgumentException(message);
        }
        
        AuditContext ac = null; 
        for (Object obj : loginArgs) {
            if (obj instanceof AuditContext) {
                ac = (AuditContext) obj;
                break;
            }
        }
        
        if (ac == null) {
            return null;
        }
        
        return ac.getCallerId();
    }
    
    public static int indexOfAuditContext(Object[] args) {
        final int notFound = -1;
        
        if (args == null)
            return notFound;
        
        for (int i = 0; i < args.length; i++) {
            Object object = args[i];
            if (object instanceof AuditContext) {
                return i;
            }
        }
        
        return notFound;
    }
    
    public static String extractTenantId(String loginId, char tenantSeparator) {
        if (StringUtils.containsNone(loginId, tenantSeparator)) {
            LOG.error("Extracting tenantId, invalid loginId={}", loginId);
        	String message = messageSource.getMessage("multitenant.error.notcontain", null, Locale.ENGLISH);
            throw new MultitenantException(message,
                    MultitenantException.Reason.INVALID_TENANT_CODE);
        }
        
        if (StringUtils.countMatches(loginId, String.valueOf(tenantSeparator)) > 1) {
            LOG.error("Extracting tenantId, invalid loginId={}", loginId);
        	String message = messageSource.getMessage("multitenant.error.contain", null, Locale.ENGLISH);
            throw new MultitenantException(message,
                    MultitenantException.Reason.INVALID_TENANT_CODE);
        }
        
        String[] arr = StringUtils.split(loginId, tenantSeparator);
        if (arr.length <= 1) {
        	String message = messageSource.getMessage("multitenant.error.notenant", null, Locale.ENGLISH);
        	throw new MultitenantException(message, MultitenantException.Reason.INVALID_TENANT_CODE);
        }
        return StringUtils.upperCase(arr[1]);
    }
    
    public static String extractLoginId(String loginId, char tenantSeparator) {        
        if (StringUtils.countMatches(loginId, String.valueOf(tenantSeparator)) > 1) {
            LOG.error("Extracting loginId, invalid loginId={}", loginId);
        	String message = messageSource.getMessage("multitenant.error.contain", null, Locale.ENGLISH);
            throw new MultitenantException(message,
                    MultitenantException.Reason.INVALID_TENANT_CODE);
        }
        else if (StringUtils.countMatches(loginId, String.valueOf(tenantSeparator)) == 0) {
            return loginId;
        }
        
        String[] arr = StringUtils.split(loginId, tenantSeparator);
        return StringUtils.upperCase(arr[0]);
    }
}
