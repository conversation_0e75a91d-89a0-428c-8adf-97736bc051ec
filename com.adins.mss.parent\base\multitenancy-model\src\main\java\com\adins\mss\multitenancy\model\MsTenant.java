package com.adins.mss.multitenancy.model;

// Generated Mar 9, 2015 9:49:40 AM by Hibernate Tools 3.4.0.CR1


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * MsTenant generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "MS_TENANT")
public class MsTenant implements java.io.Serializable {


    private String uuidMsTenant;
    private String tenantCode;
    private String tenantDs;
    private String tenantName;
    private String productKey;
    private String signatureKey;
    private String activationKey;
    private String isActive;
    private String apiKey;

    public MsTenant() {}


    public MsTenant(String uuidMsTenant, String tenantCode, String isActive) {
        this.uuidMsTenant = uuidMsTenant;
        this.tenantCode = tenantCode;
        this.isActive = isActive;
    }

    public MsTenant(String uuidMsTenant, String tenantCode, String tenantDs, String tenantName,
            String productKey, String signatureKey, String activationKey, String isActive, String apiKey) {
        this.uuidMsTenant = uuidMsTenant;
        this.tenantCode = tenantCode;
        this.tenantDs = tenantDs;
        this.tenantName = tenantName;
        this.productKey = productKey;
        this.signatureKey = signatureKey;
        this.activationKey = activationKey;
        this.isActive = isActive;
        this.apiKey = apiKey;
    }

    @Id
    @Column(name = "UUID_MS_TENANT", unique = true, nullable = false, length = 36)
    public String getUuidMsTenant() {
        return this.uuidMsTenant;
    }

    public void setUuidMsTenant(String uuidMsTenant) {
        this.uuidMsTenant = uuidMsTenant;
    }


    @Column(name = "TENANT_CODE", nullable = false, length = 6)
    public String getTenantCode() {
        return this.tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }


    @Column(name = "TENANT_DS", length = 80)
    public String getTenantDs() {
        return this.tenantDs;
    }

    public void setTenantDs(String tenantDs) {
        this.tenantDs = tenantDs;
    }


    @Column(name = "TENANT_NAME", length = 80)
    public String getTenantName() {
        return this.tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }


    @Column(name = "PRODUCT_KEY", length = 80)
    public String getProductKey() {
        return this.productKey;
    }

    public void setProductKey(String productKey) {
        this.productKey = productKey;
    }


    @Column(name = "SIGNATURE_KEY", length = 80)
    public String getSignatureKey() {
        return this.signatureKey;
    }

    public void setSignatureKey(String signatureKey) {
        this.signatureKey = signatureKey;
    }


    @Column(name = "ACTIVATION_KEY", length = 80)
    public String getActivationKey() {
        return this.activationKey;
    }

    public void setActivationKey(String activationKey) {
        this.activationKey = activationKey;
    }


    @Column(name = "IS_ACTIVE", nullable = false, length = 1)
    public String getIsActive() {
        return this.isActive;
    }

    public void setIsActive(String isActive) {
        this.isActive = isActive;
    }

    @Column(name = "API_KEY", length = 80)
    public String getApiKey() {
        return this.apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }


}
