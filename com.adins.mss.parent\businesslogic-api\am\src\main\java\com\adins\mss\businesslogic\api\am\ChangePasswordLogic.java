package com.adins.mss.businesslogic.api.am;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;

public interface ChangePasswordLogic {
	@PreAuthorize("@mssSecurity.isValidUserChangePassword(#uuidMsUser, #callerId.callerId)")
	AmMsuser changePassword(long uuidMsUser, String oldPassword, String newPassword, AuditContext callerId);
	void doCancel(long uuid, AuditContext callerId);
	boolean validatePassword(String password);
}
