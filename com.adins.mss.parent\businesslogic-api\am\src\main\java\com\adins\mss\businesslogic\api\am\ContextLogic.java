package com.adins.mss.businesslogic.api.am;

import java.util.List;

import org.springframework.cache.annotation.Cacheable;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface ContextLogic {
	void logoutAll(AuditContext callerId);
	
    @Cacheable(value="loginContextData")
	List<String> retrieveMenuRightsByLoginId(String loginId, AuditContext callerId);
    
    @Cacheable(value="loginContextData")
	List<String> retrieveMenuRightsByUuid(long uuidUser, AuditContext callerId);
    
    boolean isUserAccountActive(long uuidUser, AuditContext callerId);

	String isUserAccountCheckActive(long uuidUser, AuditContext callerId);
	String actionUserCheck(long uuid, String uri, AuditContext callerID);
}
