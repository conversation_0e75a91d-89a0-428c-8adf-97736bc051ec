package com.adins.mss.businesslogic.api.am;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;

public interface EventLogLogic  {
	void logUserEvent(String applicationName, String applicationVersion, String subsystemName, 
			String subsystemVersion, AmMsuser amMsuser, String activity, String consequence, 
			AuditContext auditContext);
	void logUserEvent(AmMsuser amMsuser, String activity, String consequence, AuditContext auditContext);
}



