package com.adins.mss.businesslogic.api.am;

import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmGeneralsetting;

public interface GeneralSettingLogic {
	Map<String, Object> listGeneralSetting(Object params, Object orders, int pageNumber, 
			int pageSize, AuditContext callerId);
	AmGeneralsetting getGeneralSetting(long uuid, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_GENERAL')")
	String updateGeneralSetting(AmGeneralsetting obj, AuditContext callerId, long uuid);
}
