package com.adins.mss.businesslogic.api.am;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsJob;

public interface GlobalLogic {
	public String getGsValue(String gsCode, AuditContext callerId);
	
	public AmMsuser getAmMsuser(String loginId, AuditContext callerId);

	public AmMsuser getAmMsuserByUuid(String uuidMsUser, AuditContext callerId);

	public MsBranch getMsBranch(String branchCode, AuditContext callerId);
	
	public MsJob getMsJob(String jobCode, AuditContext callerId);
	
}
