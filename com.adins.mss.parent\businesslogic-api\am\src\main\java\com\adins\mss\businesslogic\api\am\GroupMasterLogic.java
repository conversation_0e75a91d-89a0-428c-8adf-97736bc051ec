package com.adins.mss.businesslogic.api.am;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsgroup;

@SuppressWarnings("rawtypes")
public interface GroupMasterLogic {
	Map<String, Object> listGroup(String groupName, String groupDescription, long uuidMsSubsystem, String isActive,
			int pageNumber, int pageSize, AuditContext callerId);
	
	Map<String, Object> listGroupMobileMenu(Object paramsGroup, Object paramsResultOfGroup, AuditContext callerId);
	Map<String, Object> listGroupMenu(Object paramsGroup, Object paramsResultOfGroup, AuditContext callerId);
	Map<String, Object> listGroupToDoList(Object paramsGroup, Object paramsResultOfGroup, int pageNumber, int pageSize, AuditContext callerId);
	
	Map<String, Object> listMobileMenuOfGroup(Object params, Object orders, AuditContext callerId);
	Map<String, Object> listMenuOfGroup(Object params, Object orders, AuditContext callerId);
	Map<String, Object> listToDoList(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> listToDoListOfGroup(Object params, Object orders, AuditContext callerId);
	Map<String, Object> listMemberOfGroup(Object params, long uuidMsGroup, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> listUser(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> listUserByJob(Object params, long uuidMsUser, AuditContext callerId);
	List listUserAddMember(Object params, AuditContext callerId); 
	List listUserAddByJob(Object params, AuditContext callerId);
	
	AmMsgroup getGroup(long uuid, AuditContext callerId);
	List getAppCombo(Object params, Object orders, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_DEL_GROUP')")
	void delete(long uuidMsGroup, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_GROUP')")
	void updateGroup(AmMsgroup amMsgroup, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_GROUP')")
	long insertGroup(AmMsgroup amMsgroup, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_INS_GROUP_MOBILE')")
	void saveGroupMobile(long uuidMsGroup, String[] selectedGroupMobileArr, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_GROUP_MENU')")
	void saveGroupMenu(long uuidMsGroup, String[] selectedMenuArr, AuditContext callerId);
	void saveGroupMenuFeature(long uuidMsGroup, String[] selectedFeatureArr, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_GROUP_TODOLIST')")
	void saveGroupToDoList(long uuidMsGroup, String[] selectedToDoListArr, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_GROUP_MEMBER')")
	void saveGroupMember(long uuidMsGroup, String[] selectedMember, String type, AuditContext callerId);
	
	void insertMobileMenuofGroup(long uuidMsGroup, long[] selectedMember, AuditContext callerId);
	void deleteMobileMenuofGroup(long uuidMsGroup, AuditContext callerId);
	
	void insertMemberOfGroup(long uuidMsGroup, long[] selectedMember, AuditContext callerId);
	void deleteMemberOfGroup(long uuidMsGroup, long uuidMsUser, AuditContext callerId);
	
	void insertToDoListofGroup(long uuidMsGroup, long[] selectedToDoList, AuditContext callerId);
	void deleteToDoListofGroup(long uuidMsGroup, AuditContext callerId);
	
	void insertMenuofGroup(long uuidMsGroup, long[] selectedMenu, AuditContext callerId);
	void deleteMenuofGroup(long uuidMsGroup, AuditContext callerId);
	public Long countListAllUserMember(Object params, AuditContext callerId);
	public Long countListUserMemberByJob(Object params, AuditContext callerId);
	
	Map<String, Object> listGroupMenu2(Object paramsGroup, Object paramsResultOfGroup, AuditContext callerId);
	
	String getListJobAdmin(AuditContext callerId);
}
