package com.adins.mss.businesslogic.api.am;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsgroup;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.MsJob;

@SuppressWarnings("rawtypes")
public interface JobVsGroupLogic {
	List getListJob(Object params, AuditContext callerId);
	Map<String, Object> getGroupListByJob(long uuidJob, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> getGroupListByJob(long uuidJob, AuditContext callerId);
	Ms<PERSON>ob selectOneJob(long uuidJob, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_JOB')")
	void deleteJob (long uuidJob, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_JOB')")
	void insertJob (MsJob job, String task, AmMssubsystem subsystem, String selectedGroups, String mapJobCode, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_JOB')")
	MsJob updateJob (long uuidJob, MsJob job, String selectedGroups, String mapJobCode, AuditContext callerId);
	List<AmMsgroup> getListComboGroup(Object params, Object orders, AuditContext callerId);
	Integer countListJob(Object params, AuditContext callerId);
	List getJobMappingConfins(String valueOf, AuditContext ac);
}
