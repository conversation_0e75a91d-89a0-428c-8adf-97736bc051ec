package com.adins.mss.businesslogic.api.am;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings("rawtypes")
public interface LoginLogic {
	AmMsuser doLogin(String loginId, String password, AuditContext auditContext);
	
	/**
	 * process logout via clicking Log Out Button
	 */
	void doLogout(long uuid, AuditContext callerId);
	
	/**
	 * process logout by user's session timeout
	 */
	void doLogoutSessionTimeout(long uuidUser, AuditContext callerId);
	Map retrieveAttributeOfMember(long uuidMsuser, AuditContext auditContext);
	int retrieveSessionTimeout(AuditContext auditContext);
	void doResetPassword(String loginId, String dob, String email, String ktpNo, AuditContext auditContext);
	
	List doMobileMultiLogin(String uniqueId, String password, AuditContext auditContext);
	AmMsuser doValidateUserEmbed(String loginId, String password, AuditContext auditContext);
}
