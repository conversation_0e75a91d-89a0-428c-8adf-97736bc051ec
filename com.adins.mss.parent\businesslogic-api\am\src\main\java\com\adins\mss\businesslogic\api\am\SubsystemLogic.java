package com.adins.mss.businesslogic.api.am;

import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMssubsystem;

public interface SubsystemLogic {
	Map<String, Object> listSubsystem(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	String insertSubsystem(AmMssubsystem obj, AuditContext callerId);
	String updateSubsystem(AmMssubsystem obj, String uuid, AuditContext callerId);
	String deleteSubsystem(AmMssubsystem obj, String uuid, AuditContext callerId);
}
