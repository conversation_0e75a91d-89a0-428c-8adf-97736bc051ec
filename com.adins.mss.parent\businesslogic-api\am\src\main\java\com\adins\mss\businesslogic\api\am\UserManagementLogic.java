package com.adins.mss.businesslogic.api.am;

import java.io.File;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.custom.UserManagementBean;

@SuppressWarnings("rawtypes")
public interface UserManagementLogic {
	
	Map<String, Object> listUserManagement(Object params, int pageNumber, int pageSize, AuditContext callerId);
	UserManagementBean getUserManagementDetail(long uuid, String task, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_USER') OR hasRole('ROLE_STAGING')")
	void insertUserManagement(UserManagementBean obj, AuditContext callerId, long uuidMsSubsystem, boolean isNC);
	AmMsuser getUserManagement(long uuid, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_USER') OR hasRole('ROLE_STAGING')")
	void updateUserManagement(UserManagementBean obj, AuditContext callerId, boolean isNC);
	@PreAuthorize("hasRole('ROLE_DEL_USER') OR hasRole('ROLE_STAGING')")
	void deleteUserManagement(long uuid, AuditContext callerId);
	String getBranchZipCode(long uuidBranch, AuditContext callerId);
	String isBranchAreaEmpty(long uuidBranch,AuditContext callerId);
	String getLimitCohEnabled(String gsCode, AuditContext callerId);
	String getUpDownEnabled(String gsCode, AuditContext callerId);
	byte[] exportExcel(String subsystemName, String flagMc, AuditContext callerId);
	
	List getBranch(AuditContext callerId);
	List getJob(String subsystemName, AuditContext callerId);
	List getDealer(AuditContext callerId);
	
	byte[] processSpreadSheetFile(File uploadedFile, String subsystem, String flagCoh, AuditContext callerId);
	void insertUserManagement(AmMsuser obj, AuditContext callerId, String interfaceType);
	
	boolean validatePassword(String password);
}
