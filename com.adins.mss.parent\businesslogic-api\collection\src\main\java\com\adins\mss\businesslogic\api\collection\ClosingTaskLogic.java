package com.adins.mss.businesslogic.api.collection;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
@SuppressWarnings("rawtypes")
public interface ClosingTaskLogic {
	@PreAuthorize("hasRole('ROLE_MC') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	String closingTask(AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_MC') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List getClosingTaskList(AuditContext callerId);
}
