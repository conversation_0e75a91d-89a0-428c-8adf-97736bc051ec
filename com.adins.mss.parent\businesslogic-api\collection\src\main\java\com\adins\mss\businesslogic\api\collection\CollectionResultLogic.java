package com.adins.mss.businesslogic.api.collection;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsCollectionresult;

@SuppressWarnings("rawtypes")
public interface CollectionResultLogic {
	List getCollResultList(Object params, AuditContext callerId);
	Integer countListCollectionResult(Object params, AuditContext callerId);
	Map<String, String> getResultListCombo(String uuidCollResultCategory, AuditContext callerId);
	
	List getCollResultCategory( AuditContext callerId ); 
	MsCollectionresult getOneCollectionResult (long uuidCollectionResult, AuditContext callerId);
	void insertCollectionResult (MsCollectionresult collectionResult, AuditContext callerId);
	void updateCollectionResult (MsCollectionresult collectionResult, AuditContext callerId);
}
