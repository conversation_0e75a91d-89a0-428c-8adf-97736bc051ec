package com.adins.mss.businesslogic.api.collection;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface DailyCollectionSummaryLogic {
	void dailyCollSummary(AuditContext callerId);
	
	/**
	 * @param od target date for task deletion. target date = sysdate + od
	 * <p>example od = <b>-1</b>, target date = yesterday
	 * @param callerId
	 */
	void dailyDeleteTask(int od, AuditContext callerId);
	public void resetCohCollector(AuditContext callerId);
	String isDepositActive (AuditContext callerId);
}
