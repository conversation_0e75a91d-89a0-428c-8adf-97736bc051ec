package com.adins.mss.businesslogic.api.collection;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings({"rawtypes"})
public interface DashboardAnalyticsCollectionLogic {
	List getPerformanceMTD(long uuidBranch, AuditContext callerId);
	List getPerformanceToday(long uuidBranch, AuditContext callerId);
	List getTopMTD(long uuidBranch, AuditContext callerId);
	List getTopToday(long uuidBranch, AuditContext callerId);
	List getCollectorStatusToday(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	List userList(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	String getAutoupdateInterval(AuditContext callerId);
	List getDataAnalyticMobile(String task, String diagram,	AuditContext callerId);
}
