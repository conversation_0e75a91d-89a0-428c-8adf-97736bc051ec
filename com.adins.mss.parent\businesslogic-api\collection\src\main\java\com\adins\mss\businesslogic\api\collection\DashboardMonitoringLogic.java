package com.adins.mss.businesslogic.api.collection;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.custom.DashboardMonitoringBean;

@SuppressWarnings("rawtypes")
public interface DashboardMonitoringLogic {	
	List<DashboardMonitoringBean> getUser(long uuidUser, long uuidSubSystem, String modeMap, String startDate, String endDate, AuditContext callerId) throws ParseException;
	List<DashboardMonitoringBean> getTagColl(String uuidUser, String uuidSubSystem, AuditContext callerId);
	List getOtherColl(Object params, AuditContext callerId);
	Map getTaskLists(String modeInOut, long idColl, String idCollSelected, AmMsuser spv, List otherColl, int pageNumber, int pageSize, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_SAVE_DMC_ASSIGNIN')")
	void saveAssignIn(String idTasks, long idColl, AmMsuser actor, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_SAVE_DMC_ASSIGNOUT')")
	void saveAssignOut(String idTasks, String idColl, long idCollSelected, AmMsuser actor, AuditContext callerId);
	List getTaskMonitoring(String uuidColl, String uuidSubsystem, AuditContext callerId);
	AmMsuser getUser(String uuidColl, AuditContext callerId);
	List getSubmittedTaskList(long uuidColl, long uuidSubsystem, AuditContext callerId);
	List getOutstandingTaskList(long uuidColl, long uuidSubsystem, AuditContext callerId);
	DashboardMonitoringBean collectionTracking(String uuidColl, String startDate, String endDate, AuditContext callerId);
	String overLimits(long uuidColl, AuditContext callerId);
	
	List<DashboardMonitoringBean> retrieveCollectionTagging(String uuidSpv, String uuidSubsystem,
            AuditContext callerId);
    
    DashboardMonitoringBean retrieveInfoWindowData(long uuidCollector, long uuidSubsystem, AuditContext callerId);
    
    AmMsuser getAmSpv(long uuidSpv, AuditContext auditContext);
	boolean checkIsSpv(AmMsuser user, AuditContext auditContext);
	Map<String, Object> getHierarkiBranchLogin(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	Map getSpvByBranch(String uuidBranch, int i, int rowPerPageLookup, AuditContext auditContext);
	List userList(long uuidSpv, AuditContext callerId);
	
	List<?> getAllBranch(AuditContext callerId);
	int getCountAllBranch(AuditContext callerId);
	List<?> getCollectorBranchWithColour(String uuidbranch, String idxBranch,AuditContext callerId);
	DashboardMonitoringBean getPercentageBattery(long uuidColl, long idSubsystem, String atb_code, DashboardMonitoringBean bean);
	String getAutoupdateInterval(String GsCode, AuditContext callerId);
}
