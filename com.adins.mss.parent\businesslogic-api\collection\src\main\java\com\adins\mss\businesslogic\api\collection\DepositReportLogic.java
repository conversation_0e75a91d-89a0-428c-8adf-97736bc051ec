package com.adins.mss.businesslogic.api.collection;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.collection.DepositReportBean;
import com.adins.mss.services.model.collection.ListReportDetail;
@SuppressWarnings("rawtypes")
public interface DepositReportLogic {
	@PreAuthorize("hasRole('ROLE_MC') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public void report(AuditContext callerId, DepositReportBean reportHeader, ListReportDetail[] listReportDetail);
	public List getRecapitulation(AuditContext callerId, String[] taskId);
}
