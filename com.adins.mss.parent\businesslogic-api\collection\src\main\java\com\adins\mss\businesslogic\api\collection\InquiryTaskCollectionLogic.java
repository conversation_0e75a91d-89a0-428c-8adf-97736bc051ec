package com.adins.mss.businesslogic.api.collection;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings("rawtypes")
public interface InquiryTaskCollectionLogic {
	List listInquiryTaskCollection(Object params, AuditContext callerId);
	Integer countListInquiryTaskCollection(Object params, AuditContext callerId);
	Map<String, String> getBranchListCombo(long branchId, AuditContext callerId);
	Map<String, String> getStatusListCombo(long subsystemId, AuditContext callerId);
	List getHeader(long uuid, AuditContext callerId) throws UnsupportedEncodingException;
	List getHistory(long uuid, AuditContext callerId);
	List getAnswer(long uuid, AuditContext callerId) throws UnsupportedEncodingException;
	List getInquiryTaskDetailAnswerHistoryRejected2(Object params,Object order, AuditContext callerId) throws UnsupportedEncodingException;
	List listInquiryTaskCollectionByHierarkiUser(Object params,AuditContext callerId);
	Integer countListInquiryTaskCollectionByHierarkiUser(Object params, AuditContext callerId);
	List getHeaderFinal(long uuid, AuditContext callerId) throws UnsupportedEncodingException;
	List getHistoryFinal(long uuid, AuditContext callerId);
	List getAnswerFinal(long uuid, AuditContext callerId) throws UnsupportedEncodingException;
}
