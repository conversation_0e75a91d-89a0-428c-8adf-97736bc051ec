package com.adins.mss.businesslogic.api.collection;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.custom.RvNumberBean;
import com.adins.mss.services.model.newconfins.AddRVNumberListUserBean;

public interface RVNumberLogic {
	void clearRVNumber(AuditContext callerId);
	void getRVfromCore(AuditContext callerId);
	List<RvNumberBean> syncRvNumber(AuditContext callerId, String dtmCrt);
	void syncRV(AuditContext auditContext, String loginId, List<String> rvNumber);
	void clearRVNumberByCollector(long uuidUser);
	String addRVNumber(List<AddRVNumberListUserBean> rvNumberUserList, AuditContext callerId);
}
