package com.adins.mss.businesslogic.api.collection;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportLkpLogic {
	List getComboBranch(String branchId, AuditContext callerId);
	List getSummary(String branchId, String startDate, String endDate, AuditContext callerId, AmMsuser user);
	List getDetail(String branchId, String userId, String startDate, String endDate,  
			AuditContext callerId, AmMsuser user);
	byte[] exportExcel(String branchId, String userId, String startDate,
			String endDate, String type, AuditContext callerId, AmMsuser user);
	String saveExportScheduler(String branchId, String userId, String startDate, String endDate, 
			String type, AuditContext callerId);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
}
