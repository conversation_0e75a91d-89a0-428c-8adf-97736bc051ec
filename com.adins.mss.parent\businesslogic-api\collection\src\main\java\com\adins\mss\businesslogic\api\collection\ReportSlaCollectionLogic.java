package com.adins.mss.businesslogic.api.collection;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrReportresultlog;
@SuppressWarnings("rawtypes")
public interface ReportSlaCollectionLogic {
	List getComboBranch(String branchId, AuditContext callerId);
	List getReportSla(String subsystemId, String branchId, String startDate, 
			String endDate, AuditContext callerId);
	Map getReportSlaDetail(AmMsuser user, String type, String branchId, String userId, String startDate, 
			String endDate, AuditContext callerId);
	byte[] exportExcel(AmMsuser user, String branchId, String userId, String startDate, 
			String endDate, String type, AuditContext callerId);
	String saveExportScheduler(AmMsuser user, String branchId, String userId, String startDate, 
			String endDate, String type, AuditContext callerId);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
}
