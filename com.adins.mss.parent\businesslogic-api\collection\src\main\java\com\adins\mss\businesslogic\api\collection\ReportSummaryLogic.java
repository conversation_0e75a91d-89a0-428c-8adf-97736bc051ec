package com.adins.mss.businesslogic.api.collection;

import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface ReportSummaryLogic {
	@PreAuthorize("hasRole('ROLE_MC') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public Map<String, Object> reportSummary(AuditContext callerId);
}
