package com.adins.mss.businesslogic.api.collection;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.collection.SubmitPrintCountBean;
import com.adins.mss.services.model.common.TaskListBean;
@SuppressWarnings("rawtypes")
public interface TaskCollectionLogic {
	int updatePrintCount(List<SubmitPrintCountBean> print, AuditContext auditContext);	
	@PreAuthorize("hasRole('ROLE_MC') and @mssSecurity.isValidCallerId(#auditContext.callerId, authentication) and @mssSecurity.hasTaskUuid(#uuidTaskH, authentication)")
	String updateRVNumber(long uuidTaskH, String rvNumber, String dtm_use, AuditContext auditContext);
	@PreAuthorize("hasRole('ROLE_MC') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public List<TaskListBean> getTaskLog(AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_MC') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication) and @mssSecurity.hasTaskUuid(#uuidH, authentication)")
	public List<TaskListBean> getTaskLogDetail(long uuidH, String flag, AuditContext callerId);
	public Map getTaskListBean(AuditContext callerId, long uuidTaskH);
}
