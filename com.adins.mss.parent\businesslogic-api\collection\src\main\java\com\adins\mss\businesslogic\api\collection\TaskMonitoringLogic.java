package com.adins.mss.businesslogic.api.collection;

import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
@SuppressWarnings("rawtypes")
public interface TaskMonitoringLogic {

	Map getTaskMonitoring(String id, String uuidSubsystem, AuditContext callerId);

	Map<String, String> getSpvCombo(String uuidBranch, AuditContext callerId);
	Map<String, String> getSpvComboByHierarkiUser(String uuidUser, AuditContext callerId);
	boolean checkIsSpv(AmMsuser amMsUser, AuditContext auditContext);
	String getIntervalDuration(AuditContext callerId);
}