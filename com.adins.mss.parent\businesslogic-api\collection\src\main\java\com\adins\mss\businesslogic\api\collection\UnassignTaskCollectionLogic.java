package com.adins.mss.businesslogic.api.collection;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
@SuppressWarnings("rawtypes")
public interface UnassignTaskCollectionLogic {
	Map<String, Object> listTaskH(AmMsuser amMsuser, Object params, Object paramsCnt, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> listTaskToAssign(String[] selectedTask, AuditContext callerId);
	Map<String, Object> listUser(String mode, AmMsuser amMsuser, AuditContext callerId);
	AmMsuser getUser(long uuidMsUser, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_ASSIGNTASKCOLL')")
	void assignTask(String[] selectedTask, long uuidMsUser, AmMsuser loginBean, long uuidPriority, AuditContext callerId);
	List getPriorityList(Object params, AuditContext callerId);
	List getSpvList(Object params, AuditContext callerId);
	Integer[][] getAssignment(List<AmMsuser> listResult);	
}
