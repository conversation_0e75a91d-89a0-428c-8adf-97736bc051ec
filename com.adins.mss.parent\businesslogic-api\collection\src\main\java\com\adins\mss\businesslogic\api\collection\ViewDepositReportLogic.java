package com.adins.mss.businesslogic.api.collection;

import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrDepositreportH;
import com.adins.mss.model.FinalTrDepositreportH;

@SuppressWarnings("rawtypes")
public interface ViewDepositReportLogic {
	public List getBatchList(AuditContext callerId, Object params) throws SQLException;
	public Integer getBatchListCount(AuditContext callerId, Object params);
	public List getBatchDetail(AuditContext callerId, Object params);
	public List getBatchDetailFinal (AuditContext callerId, Object params);
	public Integer getBatchDetailCount(AuditContext callerId, Object params);
	public Integer getBatchDetailCountFinal(AuditContext callerId, Object params);
	public Map<String, String> getBranchListCombo(long branchId, AuditContext callerId);
	public Map<String, Object> getComboUser(String uuidBranch, AuditContext callerId);
	public String getImageBase64(long uuidDepositH, AuditContext callerId) throws UnsupportedEncodingException;
	public TrDepositreportH getDetailH(long uuidDepositH, AuditContext callerId);
	public FinalTrDepositreportH getDetailHFinal(long uuidDepositH, AuditContext callerId);
	public List getBatchListByHierarkiUser(AuditContext callerId, Object params) throws SQLException;
	public Integer getBatchListCountByHierarkiUser(AuditContext callerId, Object params);
	public Map<String, Object> getComboUserHierarki(String uuidBranch, AuditContext callerId);
	public Map<String, String> getComboUserHierarkiLoad(String uuidBranch, AuditContext callerId);
	public Map<String, String> getComboUserLoad(String uuidBranch, AuditContext callerId);
	public Map<String, Object> getCollByBranch(long uuidBranch, int i, int rowPerPageLookup, AuditContext auditContext);
}
