package com.adins.mss.businesslogic.api.collection;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrViewcollactivity;
import com.adins.mss.model.TrViewinstallmentcard;
import com.adins.mss.model.custom.TrViewpaymenthistoryWrapper;
import com.adins.mss.services.model.collection.CollectionHistoryBean;
import com.adins.mss.services.model.collection.InstallmentScheduleBean;
import com.adins.mss.services.model.collection.PaymentHistoryDBean;
import com.adins.mss.services.model.collection.PaymentHistoryHBean;
import com.adins.mss.services.model.collection.PaymentHistoryHList;

@SuppressWarnings("rawtypes")
public interface ViewHistoryLogic {
	public String getAgreementNo(AuditContext auditContext, String taskID);
	public List<CollectionHistoryBean> collectionHistory(AuditContext auditContext, String taskID);
	public List<PaymentHistoryHList> paymentHistoryH(AuditContext auditContext, String taskID) throws ParseException;
	public List<InstallmentScheduleBean> installmentSchedule(AuditContext auditContext, String taskID);
	
	//add
	/**
     * @deprecated use {@link #insertInstallmentCard(TrViewinstallmentcard[], AuditContext)} instead
     */    
	@Deprecated
	public Map addInstallmetSchedule(AuditContext auditContext, InstallmentScheduleBean bean) throws ParseException;
	
	/**
     * @deprecated use {@link #insertCollectionActivity(TrViewcollactivity[], AuditContext)} instead
     */        
	@Deprecated
	public Map addCollectionHistory(AuditContext auditContext, CollectionHistoryBean bean) throws ParseException;
	
	/**
	 * @deprecated use {@link #insertPaymentHistory(TrViewpaymenthistoryWrapper[], AuditContext)} instead
	 */
	@Deprecated
	public Map addPaymentHistoryH(AuditContext auditContext, PaymentHistoryHBean hBean, PaymentHistoryDBean dBean) throws ParseException;
	
	public void insertInstallmentCard(TrViewinstallmentcard[] instCards, AuditContext auditContext);
	
	public void insertCollectionActivity(TrViewcollactivity[] collActivities, AuditContext auditContext);
	
	public void insertPaymentHistory(TrViewpaymenthistoryWrapper[] paymentHistories, AuditContext auditContext);
}
