package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.AbsensiBean;

public interface AbsensiLogic {
	public static final String KEY_DOABSENSI_MESSAGEKEY = "message.key";
	public static final String KEY_DOABSENSI_MESSAGERESULT = "message.value";
	public static final String KEY_DOABSENSI_SUCCESS = "success";
	
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	Map<String, String> doAbsensi(List<AbsensiBean> locationInfo,String attdAddress, AuditContext callerId, String flagAttd);
	public void sendMessageToJms(List<AbsensiBean> locationInfo, String messageKey, AuditContext callerId);
}
