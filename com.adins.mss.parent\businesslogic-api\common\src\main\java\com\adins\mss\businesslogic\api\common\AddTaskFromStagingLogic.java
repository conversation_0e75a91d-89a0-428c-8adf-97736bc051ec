package com.adins.mss.businesslogic.api.common;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface AddTaskFromStagingLogic {
	@SuppressWarnings("rawtypes")
	List getTaskFromStaging(AuditContext auditContext);
	void doAddTaskFromStaging(String stagingTaskId, AuditContext auditContext);
	void updateTaskFail(String message, String stagingTaskId, AuditContext auditContext);
}
