package com.adins.mss.businesslogic.api.common;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.AddTaskMainDealerRequest;
import com.adins.mss.services.model.common.AddTaskMainDealerResponse;

public interface AddTaskMainDealerLogic {
	public AddTaskMainDealerResponse doAddTaskMainDealer(AddTaskMainDealerRequest request, AuditContext callerId);
}
