package com.adins.mss.businesslogic.api.common;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TblApiDashboard;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.services.model.common.ApprovalHeadBean;

@SuppressWarnings("rawtypes")
public interface ApprovalLogic {	
	List listApproval(String mode, Object params, AuditContext callerId);
	Integer countlistApproval(String mode, Object params, AuditContext callerId);
	Map<String, String> getBranchListCombo(long branchId, AuditContext callerId);
	TrTaskH getApproval(long uuid, AuditContext callerId);
	Map<String, Object> detailApproval(long uuidTaskH, AuditContext callerId);
	List viewMapPhoto(long uuid, AuditContext callerId) throws UnsupportedEncodingException;
	List getWfEngineNextStep(TrTaskH trTaskH, AmMssubsystem msSubsystem, AmMsuser loginUser, AuditContext callerId);
//	@PreAuthorize("hasRole('ROLE_APPROVAL') OR hasRole('ROLE_MS')")
	String updateTaskH(TrTaskhistory objTaskHist, long uuidTaskH, String flagStatusTask, AmMsuser loginUser, String flagApproval,
			String approvalReason, AuditContext callerId);

	/* ========== Mobile Servlet Approval ==========*/
	List<ApprovalHeadBean> approvalHeader(String mode, AuditContext callerId);
	List<ApprovalHeadBean> approvalDetail(long uuidTaskH, AuditContext callerId);
	String submit(long uuidTaskH, String flag, AuditContext auditContext);
	
	String getNotes(long uuid, AuditContext callerId);
	List getFormListCombo(AuditContext callerId);
	
	/* ========== Logic for Approval Data Entry ==========*/
	List listApprovalDataEntry(String mode, Object params, AuditContext callerId);
	Integer countlistApprovalDataEntry(String mode, Object params, AuditContext callerId);
	
	/* ========== Logic for Approval On WOM ==========*/
	List listApprovalOnWomf(String mode, Object params, AuditContext callerId);
	Integer countlistApprovalOnWomf(String mode, Object params, AuditContext callerId);
	
	void rejectWithResurvey(long uuidTaskH, long uuidMsUser, String notes, String mode, AmMsuser loginBean,
			String flagApproval, String approvalReason, AuditContext callerId);
	void updateImageResurvey(String uuidTaskH, String listResurvey, AuditContext callerId);
	List getListLovReason(long uuidTaskH, String params, AuditContext callerId);
	boolean countResurveyTask(long uuidTaskH);
	String claimTask(AmMsuser loginBean, String uuidTaskH, AuditContext callerId);
	String validateAllowApprovalOfTask(AmMsuser loginBean, String uuidApproval, boolean saveApproval,
			AuditContext callerId);
	void insertStartApprovalDate(String uuidApproval, AuditContext callerId);
	List listTaskApprovalOnWomUnclaim(AuditContext callerId);
	void doUnclaim(Map taskMap, AmGeneralsetting gsDuration, AuditContext callerId);
	List<Map<String, Object>> getRegionListCombo(AuditContext callerId);
	List listFailedUpdateStatusIDE(AuditContext callerId);
	void retryHitUpdateStatusIDE(TblApiDashboard bean, AuditContext callerId);
}
