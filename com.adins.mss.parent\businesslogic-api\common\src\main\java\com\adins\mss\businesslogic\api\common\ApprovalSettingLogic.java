package com.adins.mss.businesslogic.api.common;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsSettingapprovalots;

@SuppressWarnings("rawtypes")
public interface ApprovalSettingLogic {
	List listApprovalSettingOts(Object params, AuditContext callerId);
	Integer countListApprovalSettingOts(Object params, AuditContext callerId);
	MsSettingapprovalots getOneOts(long uuid, AuditContext callerId);
	void updateOts(MsSettingapprovalots obj, AuditContext callerId);
	List<MsJob> getListJob(Object params, Object orders, AuditContext callerId);
	void insertOts(MsSettingapprovalots obj, AuditContext callerId);
	void deleteOts(long uuid, AuditContext callerId);
	List<MsForm> getFormList(Object params, Object order, AuditContext callerId);
}