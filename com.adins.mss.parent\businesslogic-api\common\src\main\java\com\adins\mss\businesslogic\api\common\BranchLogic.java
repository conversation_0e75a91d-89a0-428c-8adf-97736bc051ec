package com.adins.mss.businesslogic.api.common;

import java.io.File;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsArea;
import com.adins.mss.model.MsBranch;
import com.adins.mss.services.model.newconfins.ZipOfBranchSyncBean;
@SuppressWarnings("rawtypes")
public interface BranchLogic {
	List listBranch(Object params, AuditContext callerId);
	Integer countListBranch(Object params, AuditContext callerId);
	MsBranch getBranch(long uuid, AuditContext callerId);
	MsBranch getBranch(String branchCode, AuditContext callerId);
	List getBranchArea(long uuid, AuditContext callerId);
	List getSubBranchArea(long uuid, AuditContext callerId);
	List getUserBranchArea(long uuid, AuditContext callerId);
	List getParentBranchArea(long uuid, AuditContext callerId);
	String getUuidArea(List listOfBA, AuditContext callerId);
	List getPathArea(String listUuidArea, AuditContext callerId);
	String getZipCode(long uuid, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_BRANCH')")
	void insertBranch(MsBranch objBranch, MsArea objArea, String polyPath, long uuidParent, String zipCode, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_BRANCH')")
	void updateBranch(MsBranch obj, MsArea objArea, String polyPath, String zipCode, AuditContext callerId);
	void updateBranch(MsBranch obj, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_BRANCH')")
	void deleteBranch(long uuid, AuditContext callerId);
	void updateZipCode(MsBranch obj, String zipCode, AuditContext callerId);
	void updatePilotingCAE(String branchCode, String isPiloting, AuditContext callerId);
	Map<String, Object> listMemberOfBranch(Object params, int pageNumber, int pageSize, AuditContext callerId);
	boolean isExistsAreaName(long uuidBranch, String areaName, AuditContext auditContext);
	
	//method for insert or delete zipcodeOfBranch
	void insertZipOfBranch(String branchId, ZipOfBranchSyncBean[] zipcode, AuditContext callerId);
	void deleteZipOfBranch(String branchId, ZipOfBranchSyncBean[] zipcode, AuditContext callerId);
	
	String getLimitCohEnabled(String gsCode, AuditContext callerId);
	String getUpDownEnabled(String gsCode, AuditContext callerId);
	
	byte[] exportExcel(String subsystem, String coh, AuditContext callerId);
	byte[] processSpreadSheetFile(String subsystem, String coh, File uploadedFile,AuditContext callerId);
}
