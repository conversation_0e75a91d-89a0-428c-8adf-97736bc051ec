package com.adins.mss.businesslogic.api.common;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsStkpromo;
import com.adins.mss.model.custom.CmsBean;
import com.adins.mss.model.custom.CmsBranchBean;
import com.adins.mss.model.custom.CmsDealerBean;

@SuppressWarnings("rawtypes")
public interface CmsLogic {	
	List<CmsBean> listStock(int targetPageNo, int pageSize, AuditContext callerId) throws ParseException, UnsupportedEncodingException;
	Integer countListStok(Object params, AuditContext callerId);
	List<CmsBean> listStockByDealer(int targetPageNo, int pageSize, AuditContext callerId) throws ParseException, UnsupportedEncodingException;
	Integer countListStokByDealer(Object params, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_PROMO')")
	void insertStkpromo(MsStkpromo stkPromo, String uuidParent, String[] uuidBranches, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_PROMO_DLR')")
	void insertStkpromoByDealer(MsStkpromo stkPromo, String uuidParent, String[] uuidDealers, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_PROMO')")
	void updateStkpromo(MsStkpromo stkPromo, String[] uuidBranches, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_PROMO_DLR')")
	void updateStkpromoByDealer(MsStkpromo stkPromo, String[] uuidDealers, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_PROMO')")
	void deleteStkpromo(String uuid, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_PROMO_DLR')")
	void deleteStkpromoByDealer(String uuid, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_CONTENT')")
	void deleteContent(String uuidContent, AuditContext callerId);
	MsStkpromo getStkpromo(String uuid, AuditContext callerId);
	List listPromoOfBranch(String uuid, String uuidParent, String[] uuidBranches,
			String branchName, String branchCode, int targetPageNo, int pageSize, AuditContext callerId);
	List listPromoOfDealer(String uuidBranch, String uuid, String uuidParent, String[] uuidDealers,
			String dealerName, String dealerCode, int targetPageNo, int pageSize, AuditContext callerId);
	Integer countListPromoOfBranch(String uuid, String uuidParent, String[] uuidBranches,
			String branchName, String branchCode, AuditContext callerId);
	Integer countListPromoOfDealer(String uuidBranch, String uuid, String uuidParent, String[] uuidDealers,
			String dealerName, String dealerCode, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_CONTENT')")
	void insertPromoContent(String uuidStkpromo, String[][] contentTypeAndBase64, Date startDate, Date endDate, AuditContext callerId) throws IOException;
	List<CmsBranchBean> listStkPromoOfBranch(String uuid, String uuidParent, String[] uuidBranches, 
			int targetPageNo, int pageSize, AuditContext callerId);
	List<CmsDealerBean> listStkPromoOfDealer(String uuid, String uuidParent, String[] uuidDealers, 
			int targetPageNo, int pageSize, AuditContext callerId);
	Integer countListStkPromoOfBranch(String uuid, String uuidParent, String[] uuidBranches, AuditContext callerId);
	Integer countListStkPromoOfDealer(String uuid, String uuidParent, String[] uuidDealers, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_BRANCH_PROMO')")
	String[] deletePromoOfBranch(String[] uuidBranches, String uuidBranch, String uuidStkPromo, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_DLR_PROMO')")
	String[] deletePromoOfDealer(String[] uuidDealers, String uuidDealer, String uuidStkPromo, AuditContext callerId);
}
