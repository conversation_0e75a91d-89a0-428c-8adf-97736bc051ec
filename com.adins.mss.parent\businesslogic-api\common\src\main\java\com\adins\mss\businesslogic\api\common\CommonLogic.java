package com.adins.mss.businesslogic.api.common;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsAssettag;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsCollectiontag;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsLocation;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsQuestionrelevant;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.MsTskdistributionofmodule;
import com.adins.mss.model.TrLocationhistory;
import com.adins.mss.model.custom.DashboardMonitoringBean;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;

public interface CommonLogic {
	
	public String retrieveNewTaskId(String seqCode);
	
	public String retrieveNewTaskIdTemp(String seqCode);
	
	@PreAuthorize("@mssSecurity.isValidSpvDashboard(#uuidSpv, #callerId.callerId)")
    public List<DashboardMonitoringBean> retrieveMobileLocation(long uuidSpv,
    		long uuidSubsystem, AuditContext callerId);
    
    public List<DashboardMonitoringBean> retrieveMobileArea(long[] uuidMobileUsers,
            AuditContext callerId);
    
    public List<DashboardMonitoringBean> retrieveLocationHistory(long[] uuidMobileUsers,
            Date startDate, Date endDate, AuditContext callerId);
    
    public List<TrLocationhistory> retrieveLocationHistory(long uuidMobileUser,
            Date startDate, Date endDate, AuditContext callerId);
    
    public String getLastLocation(long uuidMsUser, long uuidMsSubsystem, AuditContext callerId);
    
    public void insertLastLocation(long uuidMsUser, long uuidMsSubsystem,
            BigDecimal latitude, BigDecimal longitude, AuditContext callerId);
        
    public String getLastLocationTimestamp(long uuidMsUser, long uuidMsSubsystem, AuditContext callerId);
    
    public void insertLastLocationTimestamp(long uuidMsUser, long uuidMsSubsystem,
            Date timestamp, AuditContext callerId);
    
    public String getTrackingInterval(AuditContext callerId);
    
    public AmMsuser retrieveUserByLoginId(String loginId, boolean isActive, AuditContext callerId);
	
	public AmMssubsystem retrieveSubsystemByName(String subsystemName, AuditContext callerId);
	
	public AmMssubsystem retrieveSubsystemByName(String subsystemName, boolean isActive, AuditContext callerId);
	
	public MsForm retrieveMsFormByUuid(long uuidForm, AuditContext callerId);
	
	public MsForm retrieveMsFormByName(String formName, boolean isActive, long uuidMsSubsystem, AuditContext callerId);
	
	public MsPriority retrieveMsPriority(String priorityDesc, boolean isActive, AuditContext callerId);
	
	public MsBranch retrieveBranchByCode(String branchCode, boolean isActive, AuditContext callerId);
	
	public MsLocation retrieveLocationByName(String locationName, boolean isActive, AuditContext callerId);
    
    public MsQuestion retrieveQuestionByRefId(String refId, AuditContext callerId);
    
	public MsQuestion retrieveQuestionByRefId(String refId, long uuidMsSubsystem, AuditContext callerId);
    
	public MsQuestion retrieveQuestionByuuidQuestion(long uuidQuestion, long uuidMsSubsystem, AuditContext callerId);
        
	public MsQuestion retrieveQuestionByRefIdQset(String refId, long uuidMsSubsystem, long uuidFormHistory, AuditContext callerId);
	
	public MsQuestion retrieveQuestionByuuidQuestionQset(long uuidQuestion, long uuidMsSubsystem, long uuidFormHistory, AuditContext callerId);
	
	
	
	public MsQuestionrelevant retrieveQuestionRelevantQset(long uuidQuestion, long uuidMsSubsystem, long uuidFormHistory, AuditContext callerId);
	
	public MsQuestion retrieveQuestionByUuid(long uuidQuestion, AuditContext callerId);
	
	public MsStatustask retrieveStatusTask(String statusCode, long uuidSubsystem, AuditContext callerId);
	
	public MsStatusmobile retrieveStatusMobile(String mobileStatusCode, AuditContext callerId);
	
	public MsQuestion getQuestionFromQset(MsFormquestionset qSet, AuditContext callerId);
	
	public MsQuestionrelevant getQuestionRelevantFromQset(MsQuestionrelevant bean, MsFormquestionset qSet, AuditContext callerId);
	
	public MsFormhistory retrieveMsFormhistory(long uuidForm, int formVersion, AuditContext callerId);
	
	public List<MsFormquestionset> retrieveMsFormquestionset(long uuidFormHistory, AuditContext callerId);
	
	public List<MsFormquestionset> retrieveMsFormquestionset(long uuidForm, int formVersion, AuditContext callerId);
	
	public AmGeneralsetting retrieveGs(String gsCode, AuditContext callerId);
	
	public MsTskdistributionofmodule retrieveTaskDistributionMode(String uuidMsSubsystem, AuditContext callerId);
	
	public MsCollectiontag retrieveCollectionTag(String tagName, AuditContext callerId);
		
	public MsQuestionrelevant retrieveQuestionRelevantQset(MsQuestion msQuestion, MsForm msForm, int formVersion, AuditContext callerId);
	
	public void insertPercentageBattery(long uuidMsUser, long uuidMsSubsystem,String precentageBattery, AuditContext callerId);
	
	DashboardMonitoringBean retrieveLocationHistoryMT(long uuidMobileUser, Date startDate, Date endDate, AuditContext callerId);
	
	/**
	 * Retrieve next status code available in workflow process.
	 * For example: Status Codes in MO process workflow is: N | U | P | R | A | R | S 
	 * retrieveMONextStatusCodes("U") --> returns ["P", "R", "A", "R", "S"]
	 * 
	 * 
	 * @param startCode
	 * @return
	 */
	public List<String> retrieveWfNextStatusCodes(long uuidProcess, String startCode);
	
	public MsAssettag retrieveAssetTag(String assetName);
	
	public TaskDocumentBean retrieveJsonDocument(long uuidTaskH, boolean isFinal, AuditContext callerId);
	
	public List<String> listAnswersFromJsonForDownloadResult(long uuidTaskH, String baseUrlImage, boolean isLoadIntVal, AuditContext callerId);
	
	public List<TrTaskBean> listAnswersFromJson(long uuidTaskH, boolean isVerified, boolean isFinal, boolean isProcessAssetSurvey, AuditContext callerId);
	
	/**
	 * Digunakan untuk listing jawaban pada 1 kolom yang sama baik untuk Text, Choice, maupun Gambar
	 * jawaban ada pada property textAnswer
	 * 
	 * @param uuidTaskH
	 * @param lazyloadImage	true=image base64 not set to textAnswer
	 * @param isVerified	true=answer taken from FIN_* column
	 * @param isFinal		true=load from FINAL_* table
	 * @param callerId
	 * @return
	 */
	public List<TrTaskBean> listAnswersInTextFromJson(long uuidTaskH, boolean lazyloadImage, boolean isVerified, boolean isFinal, AuditContext callerId);
	public String dynamicChangeDateFormatterByGenset(String date, String format, AuditContext callerId);
}
