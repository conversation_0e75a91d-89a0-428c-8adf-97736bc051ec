package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsDealer;

@SuppressWarnings("rawtypes")
public interface DealerLogic {	
	List listDealer(Object params, AuditContext callerId);
	Integer countListDealer(Object params, AuditContext callerId);
	MsDealer getDealer(long uuid, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_DEALER')")
	void updateDealer(<PERSON><PERSON><PERSON><PERSON> obj, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_DEALER')")
	void insertDealer(MsDealer obj, String uuidParent, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_DEALER')")
	void deleteDealer(long uuid, AuditContext callerId);
	Map<String, Object> listMemberOfDealer(String uuidDealer, int pageNumber, int pageSize,  AuditContext callerId);
	void deleteMemberOfDealer(long uuid, AuditContext callerId);
	List listBranch(String[][] params, AuditContext callerId);
	Integer countListBranch(Object params, AuditContext callerId);
	void insertDealerOfBranch(long uuidDealer, String[] uuidBranches, AuditContext callerId);
}
