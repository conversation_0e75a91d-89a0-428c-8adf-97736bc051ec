package com.adins.mss.businesslogic.api.common;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface FileLogic {
	void GenerateRuleFileExcel(AuditContext auditContext);
	void generateAssetSchemeFileCsv(AuditContext auditContext);
	void generateBlacklistFileCsv(AuditContext auditContext);
	void generateIndustryFileCsv(AuditContext auditContext);
	void generateMarketPriceFileCsv(AuditContext auditContext);
	void generatePOFileCsv(AuditContext auditContext);
	void generatePOAssetFileCsv(AuditContext auditContext);
	void generatePODealerFileCsv(AuditContext auditContext);
}
