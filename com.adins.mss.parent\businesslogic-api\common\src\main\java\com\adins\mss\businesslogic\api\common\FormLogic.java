package com.adins.mss.businesslogic.api.common;

import java.io.File;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsgroup;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsQuestiongroup;
import com.adins.mss.model.MsQuestionrelevant;
import com.adins.mss.model.custom.FormQuestionBean;
@SuppressWarnings("rawtypes")
public interface FormLogic {
	Map<String, Object> listForm(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> listQuestionGroupOfForm(String uuidMsGroup, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> listQuestionOfGroup(long uuidQuestionGroup, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> listFormOfGroup(String uuidForm, AuditContext callerId);
	Map<String, Object> getListQuestionGroupOfForm(String uuidForm, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> questionGroup(Object params, AuditContext callerId);
	
	List questionGroupList(Object params, AuditContext callerId);
	Integer cntQuestionGroup(Object params, AuditContext callerId);
	
	Map<String, Object> changeAuthorize(Object params, String uuidForm,String groupDesc, int pageNumber, int pageSize, AuditContext callerId);
	
	int countReturnOfChangeAuthorize(Object params, String uuidForm,String groupDesc, AuditContext callerId);
	
	List getComboFormCtg(String uuidSubsystem, AuditContext callerId);
	List<AmMsgroup> getComboGroup(Object params, Object orders, AuditContext callerId);
	List<AmMsgroup> getListComboGroup(Object params, Object orders, AuditContext callerId);
	List<MsQuestiongroup> getListSelectedQG(String[] selectedQGroup, AuditContext callerId);
	
	MsForm getForm(String uuid, AuditContext callerId);
	MsQuestiongroup getQuestiongroup(String uuid, AuditContext callerId);
	MsQuestion getQuestion(String uuid, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_UPD_FORM')")
	String updateForm(MsForm msForm, String selectedGroup, String selectedQGroupArr, String order, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_FORM')")
	String insertForm(MsForm msForm, String selectedGroup, String selectedQGroupArr, String order, AuditContext callerId);
	
	void insertQGroupOfForm(String selectedQGroup, String order, String uuidForm, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_DEL_FORMOFGROUP')")
	void deleteFormOfGroup(String uuidFormOfGroup, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_FORMOFGROUP')")
	void saveFormOfGroup(String uuidForm, String[] selectedGroupArr, AuditContext callerId);
	void insertFormOfGroup(String uuidForm, String[] selectedGroup, AuditContext callerId);
	
	MsQuestionrelevant getQRelevant(String uuidQuestion, String uuidQuestionGroup, String uuidForm, AuditContext callerId);
	MsQuestionrelevant getQRelevantPublish(String uuidQuestion, String uuidQuestionGroup, String uuidForm, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_QUESTION_REL')")
	String insertQRelevant(MsQuestionrelevant msQuestionrelevant, String uuidForm, String uuidQuestion, String uuidQuestionGroup, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_QUESTION_REL')")
	String updateQRelevant(MsQuestionrelevant msQuestionrelevant, String uuidForm, String uuidQuestion, String uuidQuestionGroup, AuditContext callerId);
	
	Map<String, Object> exportExcel(String uuidForm, AmMsuser loginBean, AuditContext callerId);
	
	//tambahan 3+1
	byte[] exportTemplateUploadExcel(AuditContext callerId);
	byte[] exportEditExcel( String uuidForm, AuditContext callerId);
	//byte[] exportExcelXlsx(String uuidForm, AmMsuser loginBean, AuditContext callerId);	
	
	List<FormQuestionBean> listQuestionDetail(String uuidForm, int pageNumber, int pageSize, AuditContext callerId);
	List<FormQuestionBean> listQuestionDetailPublish(String uuidForm, int pageNumber, int pageSize, AuditContext callerId);
	
	List<FormQuestionBean> listQuestionDetailforView(Object params,String uuidGroup,  AuditContext callerId);
	Integer countQuestionGroup(Object params, AuditContext callerId);
	byte[] processSpreadSheetFile(File uploadedFile, AuditContext callerId, String uuidForm);
	//end tambahan 3+1
	String getIsUploadFormOn(String gsCode, AuditContext callerId);
	List getPriority(AuditContext callerId);
	List getBranchDescription(AuditContext callerId);
	List getBranchDriver(AuditContext callerId);
	List getZoneOfLocation(AuditContext callerId);
	void insertFormVersioning(String uuidForm, String changeLog, AuditContext callerId);
}
