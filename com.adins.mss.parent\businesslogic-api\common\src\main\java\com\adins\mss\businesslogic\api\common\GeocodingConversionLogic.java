package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface GeocodingConversionLogic {
	List startGeocodingConversion(AuditContext callerId);
	public void geocodeConversion(String uuidTrTaskH, String custAddress, int geoFail, AuditContext context);
	public Map getLatLongGeocodeConversion(String custAddress, AuditContext context);
}
