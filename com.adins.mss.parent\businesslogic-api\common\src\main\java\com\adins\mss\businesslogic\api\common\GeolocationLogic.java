package com.adins.mss.businesslogic.api.common;

import java.util.List;

import com.adins.framework.exception.AdInsException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;

public interface GeolocationLogic {
    
    /**
     * <p>Update coordinate, accuracy for locations passed in argument.
     * Location which already has coordinate, will not be processed.
     * Location with LAC or CellID null, also won't be processed.
     * 
     * <p>Converted cellId to coordinate will be checked for its accuracy.
     * If its accuracy > tolerance, coordinate will not be set.
     * Tolerance configuration is stored in Am_GeneralSetting table with Gs_Code <b>ACCURACY</b>
     * 
     * @param locations
     * @param auditContext
     */
    public void geocodeCellId(List<LocationBean> locations, AuditContext auditContext)
            throws AdInsException;
    
    /**
     * <p>Update coordinate, accuracy for locations passed in argument.
     * Location which already has coordinate, will not be processed.
     * Location with LAC or CellID null, also won't be processed.
     * 
     * <p>Converted cellId to coordinate will be checked for its accuracy.
     * If its accuracy > tolerance, coordinate will not be set.
     * Tolerance configuration is stored in Am_GeneralSetting table with Gs_Code <b>ACCURACY</b>
     * 
     * @param locations
     * @param updateTable
     *      <p><code>true</code>    converted cellId will be saved to these tables:
     *          <ul>
     *              <li>TR_LOCATIONHISTORY</li>
     *              <li>TR_TASK_D</li>
     *              <li>TR_TASKDETAILLOB</li>
     *              <li>TR_TASKREJECTEDDETAIL</li>
     *          </ul>
     *      <p><code>false</code>   converted cellId not saved
     * @param auditContext
     */
    public void geocodeCellId(List<LocationBean> locations, boolean updateTable, AuditContext auditContext)
            throws AdInsException;
    
    public void geocodeCellIdTaskLob(List<LocationBean> locations,
			boolean updateTable, AuditContext auditContext)
			throws AdInsException;
}
