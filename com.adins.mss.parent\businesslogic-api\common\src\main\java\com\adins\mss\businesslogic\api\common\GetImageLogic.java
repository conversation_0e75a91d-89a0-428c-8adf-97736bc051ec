package com.adins.mss.businesslogic.api.common;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.GetImageBean;

public interface GetImageLogic {
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication) and @mssSecurity.hasTaskUuid(#uuidTaskH, authentication)")
	public List<GetImageBean> getImg(long uuidTaskH, long uuidQuestion, AuditContext callerId);
}