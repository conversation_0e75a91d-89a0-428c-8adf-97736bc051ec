package com.adins.mss.businesslogic.api.common;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.services.model.common.GetImageBean;
import com.adins.mss.services.model.common.GetVerificationDBean;
import com.adins.mss.services.model.common.GetVerificationHBean;
import com.adins.mss.services.model.common.GetVerificationHeadBean;

public interface GetVerificationLogic {
	public List<GetVerificationHeadBean> verificationHeader(AuditContext callerId);
	public List verificationDetail(String uuidTaskH, AuditContext callerId);
	public void verify(AuditContext auditContext, String application, GetVerificationHBean taskHBean,
			GetVerificationDBean taskDBean[], String imei, String flagAssignDate);
	
	
	public void updateTaskD(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String optionAnswerId,
			String questionText, String textAnswer, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, String idTaskH);
	public void insertTaskHistory(AuditContext auditContext, MsStatustask msStatusTask, TrTaskH trTaskH, String notes, 
			String codeProcess, String fieldPerson);
}