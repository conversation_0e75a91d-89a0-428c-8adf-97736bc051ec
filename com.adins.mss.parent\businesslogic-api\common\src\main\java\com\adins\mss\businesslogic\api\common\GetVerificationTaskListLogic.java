package com.adins.mss.businesslogic.api.common;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.ActionBean;
import com.adins.mss.services.model.common.GetVerificationHeadBean;

public interface GetVerificationTaskListLogic {
	@PreAuthorize("hasRole('ROLE_MS') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public List<GetVerificationHeadBean> verificationHeader(String mode, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_MS') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public List<GetVerificationHeadBean> verificationDetail(long uuidTaskH, AuditContext callerId);
	public List<ActionBean> getListAction(long uuidTaskH, AuditContext callerId);
}