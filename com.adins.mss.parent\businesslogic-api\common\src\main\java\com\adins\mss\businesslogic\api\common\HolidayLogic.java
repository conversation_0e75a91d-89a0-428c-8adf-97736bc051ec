package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsHolidayH;

@SuppressWarnings("rawtypes")
public interface HolidayLogic {
	
	Map<String, Object> listHolidayScheme(Object params, Object orders, 
		int pageNumber, int pageSize, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_DEL_HOLIDAY_SCHEME')")
	void deleteHolidayScheme(String uuid, AuditContext callerId);
	
	MsHolidayH getHolidayScheme(String uuid, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_UPD_HOLIDAY_SCHEME')")
	void updateHolidayScheme(MsHolidayH obj, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_INS_HOLIDAY_SCHEME')")
	void insertHolidayScheme(MsHolidayH obj, AuditContext callerId);
	
	Map<String, Object> listHolidayBranch(Object params, Object orders,
		int pageNumber, int pageSize, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_DEL_HOLIDAY_BRANCH')")
	void deleteHolidayBranch(String uuidBranch, String uuidHoliday, 
		AuditContext callerId);
	
	List listHolidayBranchNull(Object params, Object orders, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_SAVE_BRANCH_HOLIDAY')")
	void saveBranchHoliday(String uuidHolidayScheme, String branchCodeSelect, 
		AuditContext callerId);
	
	List getTahunDb(Object params, Object orders, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_SAVE_UPD_HOLIDAY')")
	void saveOrUpdateHoliday(String uuidHolidayScheme, String dateAdd, 
		String descAdd, AuditContext callerId);
	
	void generateCalendar(String uuidHolidayScheme, String startYear, 
		String endYear, AuditContext callerId);
	
	String getHolidayByYear(Object params, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_UPD_WEEKDAYS')")
	void updateSetWeekdays(Object params, String daySelect, AuditContext callerId);
	
	Map<String, String> getListTahunCombo(Object params, Object orders, 
		AuditContext callerId);
	
	List listHolidayDetail(String flagTahun, Object params, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_DEL_HOLIDAY_DETAIL')")
	void deleteHolidayDetail(String uuidHolidayDetail, AuditContext callerId);
	
	Integer countListBranchNull(Object params, AuditContext callerId);
	
	Integer countListAddCalendar(String flagTahun, Object params, 
		AuditContext callerId);
}
