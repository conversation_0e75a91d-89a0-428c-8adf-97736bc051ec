package com.adins.mss.businesslogic.api.common;

import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.InvitationalESignResponse;

public interface InvitationalESignLogic {	
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public InvitationalESignResponse sentInvitationalESign(String formName, Map<String, String> request, AuditContext callerId);
	public InvitationalESignResponse sentInvitationalESignAutoPreSurvey(String formName, String taskId, AuditContext callerId);
	public InvitationalESignResponse checkRegistrationStatus(String formName, Map<String, String> request,
			AuditContext callerId);
}
