package com.adins.mss.businesslogic.api.common;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface LazyLoadLogic {
	byte[] getImageLob(String uuidTaskLob, AuditContext callerId);

	byte[] getImageLobFinal(String uuidTaskLob, AuditContext callerId);
	
	byte[] getImageLobReject(String uuidTaskLob, AuditContext callerId);

	byte[] getImageLobRejectFinal(String uuidTaskLob, AuditContext callerId);
}
