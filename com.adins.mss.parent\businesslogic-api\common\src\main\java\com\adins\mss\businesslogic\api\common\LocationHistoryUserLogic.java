package com.adins.mss.businesslogic.api.common;

import java.text.ParseException;
import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.services.model.common.LocationHistoryUserBean;


public interface LocationHistoryUserLogic {
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public String saveLocationHistory(List<LocationHistoryUserBean> listLocationInfo,String precentageBattery, String dataUsage, AuditContext callerId) throws ParseException;
	
	public List<LocationBean> listEmptyCoordinate(AuditContext callerId);
	public void sendMessageToJms(List<LocationHistoryUserBean> listLocationInfo, AuditContext callerId);
}