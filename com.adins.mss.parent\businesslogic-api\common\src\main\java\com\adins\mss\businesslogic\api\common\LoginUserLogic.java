package com.adins.mss.businesslogic.api.common;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.services.model.common.LoginUserBean;
import com.adins.mss.services.model.common.MenuUserBean;

public interface LoginUserLogic {
	List<LoginUserBean> listGeneralSetting(String application, AuditContext callerId);
	List<MenuUserBean> listMenu(AmMsuser loginBean, AuditContext callerId);
	Map<String, String> listLoginUser(String loginId, AuditContext callerId);
    Map<String, String> listLoginUserByUuid(long uuid, AuditContext callerId);
    void sendMessageToJms(<PERSON><PERSON><PERSON> user, AuditContext auditContext);
    AmMsuser doMobileLogin(String loginId, String password, String imei, String androidId, String flagFreshInstall, AuditDataType deviceInformation, AuditContext auditContext);
    Status doResetPassword(String loginId, Date dob, String email, String ktpNo, AuditContext auditContext);
    List doMobileMultiLogin(String uniqueId, String password, AuditContext auditContext);
}