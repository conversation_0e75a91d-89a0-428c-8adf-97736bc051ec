package com.adins.mss.businesslogic.api.common;

import java.io.File;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsLov;

public interface LovLogic {
	MsLov getLov(long params, AuditContext callerId);
	Map<String, Object> listLov(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	
	List listLovByNative(String[][] params, AuditContext callerId);
	Integer countLovByNative(String[][] params, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_UPD_LOV')")
	void updateLov(MsLov msl,  AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_LOV')")
	void insertLov (<PERSON><PERSON><PERSON> msl, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_LOV')")
	void deleteLov(long uuid, AuditContext callerId);
	List<Map<String, Object>> listGroup(AuditContext callerId);
	List<Map<String, Object>> listGroupSync(AuditContext callerId);
	Map<String, Object> listMemberOfLov(long uuidLov, String[][] orders, int pageNumber, int pageSize,  AuditContext callerId);
	void deleteMemberOfLov(long uuidLov, AuditContext callerId);
	List listBranch(String[][] params, AuditContext callerId);
	Integer countListBranch(Object params, AuditContext callerId);
	void insertBranchOfLov(long uuidLov, String[] uuidBranches, AuditContext callerId);
	byte[] exportExcel(AuditContext callerId);
	byte[] processSpreadSheetFile(File uploadedFile,AuditContext callerId);
	
	String getIsSystem(String lovGroup, AuditContext callerId);
}
