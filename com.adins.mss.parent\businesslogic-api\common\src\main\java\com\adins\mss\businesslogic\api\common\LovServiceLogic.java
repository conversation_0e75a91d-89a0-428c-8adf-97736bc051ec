package com.adins.mss.businesslogic.api.common;

import com.adins.framework.persistence.dao.model.AuditContext;

@Deprecated
public interface LovServiceLogic {
	//Sync lov untuk public service
	public String syncLov(AuditContext callerId, String lovGroup, String code, String description, String sequence, String isActive, String constraint1, String constraint2, String constraint3, String constraint4, String constraint5);
}
