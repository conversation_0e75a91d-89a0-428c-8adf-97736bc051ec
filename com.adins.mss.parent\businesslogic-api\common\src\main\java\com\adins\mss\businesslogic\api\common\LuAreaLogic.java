package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsArea;
import com.adins.mss.model.custom.LuAreaBean;

@SuppressWarnings("rawtypes")
public interface LuAreaLogic {
	Map<String, Object> listAreasByBranchArea(Object params, 
			int pageNumber, int pageSize, AuditContext callerId);
	LuAreaBean getAreaBranch(long uuidBranch, AuditContext callerId);
	Ms<PERSON>rea insertArea(Ms<PERSON><PERSON> bean, String areapath, AuditContext callerId);
	LuAreaBean getAreaUser(long uuidUser, AuditContext callerId);
	List getOtherBranch(long id, AuditContext auditContext);
	String getAreaBranchUser(long uuidBranch);
	MsArea validateAreaName(String areaName, AuditContext auditContext);
}
