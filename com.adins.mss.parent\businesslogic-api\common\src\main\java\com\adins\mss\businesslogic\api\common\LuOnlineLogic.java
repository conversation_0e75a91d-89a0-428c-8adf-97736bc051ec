package com.adins.mss.businesslogic.api.common;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.OnlineLuResponse;

public interface LuOnlineLogic {
	public static final int DEFAULT_MAX_LIST_DATA_LUONLINE = 50;
	
	public OnlineLuResponse luOnline(String refId, String lovGroup,
			String searchVal, String choiceFilterVal, AuditContext callerId);
	
	public OnlineLuResponse generalLuOnline(String refId, String lovGroup,
			String searchVal, String choiceFilterVal, AuditContext callerId);
	
    public boolean validateLuOnlineCode(String id, String lovGroup, AuditContext callerId);
}
