package com.adins.mss.businesslogic.api.common;

import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;

public interface LuReportLogic {
	public Map<String, Object> luReportBranch(Object[][] paramList, Object[][] paramCount, boolean isWithRegion, AuditContext callerId);
	public Map<String, Object> luReportUserSpv(Object[][] paramList, Object[][] paramCount, AuditContext callerId);
	public Map<String, Object> luReportUser(String uuidBranch, Object[][] paramList, Object[][] paramCount, AuditContext callerId);
	public String getJobSpvList(AmMsuser loginBean, AuditContext callerId);
}
