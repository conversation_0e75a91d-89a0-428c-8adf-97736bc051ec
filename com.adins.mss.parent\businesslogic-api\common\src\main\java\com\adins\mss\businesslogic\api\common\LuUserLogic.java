package com.adins.mss.businesslogic.api.common;

import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface LuUserLogic {
	Map<String, Object> listUserBrh(String loginId, String fullName, 
			String uuidJob, String uuidBranch, String uuidDealer, 
			long uuidSubsystem, int pageNumber, int pageSize, AuditContext callerId);
	Map<String, Object> listUserDlr(String loginId, String fullName, 
			String uuidJob, String uuidBranch, String uuidDealer, 
			long uuidSubsystem, int pageNumber, int pageSize, AuditContext callerId);
	String getParentJobByLoginJob(long uuid);
}
