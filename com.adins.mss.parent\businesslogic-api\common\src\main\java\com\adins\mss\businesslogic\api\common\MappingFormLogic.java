package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsMappingformH;
@SuppressWarnings("rawtypes")
public interface MappingFormLogic {	
	Map<String, Object> listMappingForm(int pageNumber, int pageSize, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_MAPPINGFORM')")
	void deleteMappingForm(long uuid, AuditContext callerId) ;
	MsMappingformH getMappingForm(long uuid, AuditContext callerId);
	List getFormOrderList(Object params, Object orders, AuditContext callerId, String flag);
	@PreAuthorize("hasRole('ROLE_INS_MAPPINGFORM')")
	void saveMappingFormH(String task, String uuidQuestionSurvey, String uuidQuestionOrder, 
			MsMappingformH formH, String paramUuidFormSurvey, String paramFormSurveyVersion, 
			AuditContext callerId);
	List getQuestionOrder(Object params, Object orders, AuditContext callerId);
	String getQuestionMapping(Object params, AuditContext callerId);
	String getFormSurveyName(Object params, AuditContext callerId);
	List getQuestionLabel(Object params, AuditContext callerId);
	List getQuestionSurvey(Object paramsSurvey, Object paramsOrder, AuditContext callerId);
	void checkMappingName(String mappingName, AuditContext callerId);
}
