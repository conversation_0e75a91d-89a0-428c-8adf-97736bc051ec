package com.adins.mss.businesslogic.api.common;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.context.MessageSource;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsMappingUserDealer;
import com.adins.mss.model.TrUploadtasklog;

@SuppressWarnings("rawtypes")
public interface MappingUserDealerLogic {
	List listMappingUserDealer(Object[][] params, String type, AuditContext callerId);	
	Integer countListMappingUserDealer(Object[][] params, String type, AuditContext callerId);
	byte[] exportExcel(AuditContext callerId);
	void insertMappingUserDealer(MsMappingUserDealer mud, AuditContext callerId);
	void updateMappingUserDealer(MsMappingUserDealer mud, int uuidExist, AuditContext callerId);
	void setMessageSource(MessageSource messageSource);
	Map<String, Object> processSpreadSheetSaveToFile(File uploadedFile, String fileName, AmMsuser loginBean,
			String mode, AuditContext callerId) throws IOException;
	void doUploadMappingProcess(TrUploadtasklog trUploadTaskLog, String type, AuditContext callerId);
}
