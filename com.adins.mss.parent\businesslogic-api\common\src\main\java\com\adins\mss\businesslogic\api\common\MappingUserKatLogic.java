package com.adins.mss.businesslogic.api.common;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.context.MessageSource;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsMappingUserKat;
import com.adins.mss.model.TrUploadtasklog;

public interface MappingUserKatLogic {
	List listMappingUserKat(Object[][] params, AuditContext callerId);
	Integer countListMappingUserKat(Object[][] params, AuditContext callerId);
	byte[] exportExcel(AuditContext callerId);
//	byte[] processSpreadSheetFile(File uploadedFile, AuditContext callerId);
	void insertMappingUserKat(MsMappingUserKat muk, AuditContext callerId);
	void updateMappingUserKat(MsMappingUserKat muk, int uuidExist, AuditContext callerId);
	void setMessageSource(MessageSource messageSource);
	Map<String, Object> processSpreadSheetSaveToFile(File uploadedFile, String fileName, AmMsuser loginBean,
			AuditContext callerId) throws IOException;
	void doUploadTaskProcess(TrUploadtasklog trUploadTaskLog, AuditContext callerId);
	void doUploadMappingUserKATBySP(TrUploadtasklog trUploadTaskLog, AuditContext callerId);
	Map loopDataInToUserKAT(Map result, AuditContext callerId);
	List getProductCategoryList(AuditContext auditContext);	
}
