package com.adins.mss.businesslogic.api.common;

import com.adins.framework.persistence.dao.model.AuditContext;

@Deprecated
public interface NewConfinsUserManagementLogic {
	/**
	 * Returns UUID of new user
	 */
	public String insertUser(String loginId, String name, String activeStatus,
			String branchCode, String jobCode, AuditContext auditContext);
	
	public void updateUser(String loginId, String name, String activeStatus,
			String branchCode, String jobCode, AuditContext auditContext);
	
	public void deleteUser(String loginId, AuditContext auditContext);
}
