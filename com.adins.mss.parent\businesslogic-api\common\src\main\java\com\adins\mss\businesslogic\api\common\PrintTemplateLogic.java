package com.adins.mss.businesslogic.api.common;

import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsPrintitemofform;


public interface PrintTemplateLogic {
	Map<String, Object> listPrintTemplateForm(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	MsForm getForm(long uuid, AuditContext callerId);
	Map<String, Object> listPrintConfiguration(long uuidForm, int pageNumber, int pageSize, AuditContext callerId);
	MsPrintitemofform getPrintItemOfForm(long uuidPrintItemOfForm, AuditContext callerId);
	Map<String, String> getListPrintItemType(Object params, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_PRINTTEMPLATE')")
	void insertPrintConfig (MsPrintitemofform obj, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_PRINTTEMPLATE')")
	void updatePrintConfig(MsPrintitemofform obj, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_PRINTTEMPLATE')")
	void deletePrintConfig(long uuid, AuditContext callerId);
}
