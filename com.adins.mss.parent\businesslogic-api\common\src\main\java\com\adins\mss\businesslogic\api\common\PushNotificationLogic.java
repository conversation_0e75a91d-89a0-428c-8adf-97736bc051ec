package com.adins.mss.businesslogic.api.common;


import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrNotification;
import com.adins.mss.model.custom.Content;
@SuppressWarnings("rawtypes")
public interface PushNotificationLogic {
	void saveTokenId(long uuidMsUser, String tokenId, AuditContext auditContext);
	void sendNotification (String apiKey, Content message);
	void sendNotification (AuditContext auditContext);
	void sendNotificationByTopic(AuditContext auditContext);
	Map<String, Object> getBranchList(Object param, Object order, 
			int pageNumber, int pageSize, AuditContext auditContext);
	List getGroupListBySubsystem(Object param, int pageNumber, 
			int pageSize, AuditContext auditContext);
	Integer getCountGroupListBySubsystem(Object param, int pageNumber, 
			int pageSize, AuditContext auditContext);
	List getIndividu(Object param, int pageNumber, 
			int pageSize, AuditContext auditContext);
	List getSales(Object param, int pageNumber, 
			int pageSize, AuditContext auditContext);
	Integer getCountIndividu(Object params, AuditContext callerId);
	Integer getCountSales(Object params, AuditContext callerId);
	Map<String, Object> getDealerList(Object params, Object order, int pageNumber, 
			int pageSize, AuditContext auditContex);
	void saveNotification (TrNotification notif, AuditContext auditContext);
}
