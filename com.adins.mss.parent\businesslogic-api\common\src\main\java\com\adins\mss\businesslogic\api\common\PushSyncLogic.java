package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsLov;

@SuppressWarnings("rawtypes")
public interface PushSyncLogic {
	@PreAuthorize("hasRole('ROLE_READ_PUSHSYNC')")
	List listPushSync(Object params, int startPage, int endPage, AuditContext callerId);
	Integer countListPushSync(Object params, AuditContext callerId);
	List getJobListCombo(AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_INS_PUSHSYNC')")
	Map lovGroupList(Object params, AuditContext callerId);
	Integer countLovGroupList(Object params, AuditContext callerId);
	List<MsLov> getListLov(String[] selectedLovArr, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_INS_PUSHSYNC')")
	List tableList(AuditContext callerId);
	List generatedSyncList(String paramsTable, String paramsOrder, String[] selectedId, int start, int end ,AuditContext callerId);
	Integer countGeneratedSyncList(String paramsTable, String[] selectedId, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_INS_PUSHSYNC')")
	Map luPushByUserList(Object params, AuditContext callerId);
	Integer countLuPushByUserList(Object params, AuditContext callerId);
	List<AmMsuser> getListUser(String[] selectedUserArr, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_INS_PUSHSYNC')")
	Map luPushByJobList(Object params, AuditContext callerId);
	Integer countLuPushByJobList(Object params, AuditContext callerId);
	List<MsJob> getListJob(String[] selectedJobArr, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_INS_PUSHSYNC')")
	Map luPushByBranchList(Object params, AuditContext callerId);
	Integer countLuPushByBranchList(Object params, AuditContext callerId);
	List<MsBranch> getListBranch(String[] selectedBranchArr, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_INS_PUSHSYNC')")
	Map insertPushSync(String pushSyncBy, String[] leftDataTable, String[] rightDataTable, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_PUSHSYNC')")
	Map deletePushSync(Long seqNo, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_SET_PUSHSYNC_TIME')")
	List getListBranchForSettingTime(Object params[][], AuditContext callerId);
	Integer getListCountBranchForSettingTime(Object params[][], AuditContext callerId);
	void saveTimeForBranch (long uuidBranch, String time, AuditContext callerId);
}