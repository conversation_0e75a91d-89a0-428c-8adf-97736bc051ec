package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsQuestiongroup;
import com.adins.mss.model.MsQuestionofgroup;

@SuppressWarnings("rawtypes")
public interface QuestionGroupLogic {
	Map getQuestionGroupList(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	Map getQuestionList(long uuidQuestiongroup, AuditContext callerId);
	Map questionList(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	MsQuestiongroup selectOneQuestionGroup(long uuid, AuditContext callerId);
	MsQuestionofgroup getQuestionDetail(long uuidQuestion, AuditContext callerId);
	List<MsQuestion> getListQuestion(long[] selectedQuestion, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_QUESTIONGROUP')")
	void insertQuestionGroup (MsQuestiongroup msQuestionGroup, String question, String order, long uuidSubsystem, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_QUESTIONGROUP')")
	void updateQuestionGroup (long uuidQuestionGroup, MsQuestiongroup msQuestionGroup, String question, String order, long uuidSubsystem, AuditContext callerId);
	void insertQuestionOfGroup(String question, String order, long uuidQustionGroup, AuditContext callerId);
	void updateForm (long uuidQuestionGroup, AuditContext callerId);
	List getAnswerTypeList( AuditContext callerId );
}