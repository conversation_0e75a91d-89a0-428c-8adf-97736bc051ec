package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.MsQuestion;

@SuppressWarnings("rawtypes")
public interface QuestionListLogic {
	Map getQuestionList(long uuidMsSubsystem, String questionLabel, int searchBy, String searchValue, String isActive, int pageNumber,
			int pageSize, AuditContext callerId);
	MsQuestion selectOneQuestion( long uuidQuestion, AuditContext callerId );
	List getLovList( AuditContext callerId );
	List getAssetList( AuditContext callerId );
	List getCollectionList( Object[][] params, AuditContext callerId );
	List getOrderList( AuditContext callerId );
	List getAnswerTypeList( AuditContext callerId );
	
	@PreAuthorize("hasRole('ROLE_INS_QUESTION')")
	void insertQuestion( MsQuestion obj, AmMssubsystem subsystem, AuditContext callerId );
	@PreAuthorize("hasRole('ROLE_UPD_QUESTION')")
	void updateQuestion( long uuidQuestion, AmMssubsystem subsystem, MsQuestion obj, AuditContext callerId );
	void updateForm( long uuidQuestion, AuditContext callerId );
	List getLuOnlineList( AuditContext callerId );
}
