package com.adins.mss.businesslogic.api.common;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.QuestionSetBean;

public interface QuestionSetLogic {
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication) and @mssSecurity.hasFormUuidByCallerID(#formId, #callerId.callerId)")
	List<QuestionSetBean> getQuestionSetByFormId(long formId, int version, AuditContext callerId);
}