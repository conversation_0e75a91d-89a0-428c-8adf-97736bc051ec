package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsRegion;

@SuppressWarnings("rawtypes")
public interface RegionLogic {
	List listBranch(Object params, AuditContext callerId);
	Map getRegionList(Object params, Object orders, 
			int pageNumber, int pageSize, AuditContext callerId);
	Map getBranchListOfRegion(long uuidRegion, int pageNumber, 
			int pageSize, AuditContext callerId);
	MsRegion selectOneRegion(String uuidRegion, AuditContext callerId);
	Integer countListBranch(Object params, AuditContext callerId);
	void deleteRegion (String uuidRegion, AuditContext callerId);
	void deleteBranch (String uuidRegionOfBranch, AuditContext callerId);
	void insertRegionOfBranches (String uuidRegion, String[] uuidBranches, 
			AuditContext callerId);
	void insertRegion (MsRegion region, AuditContext callerId);
	void updateRegion (String uuidRegion, MsRegion region, AuditContext callerId);
}
