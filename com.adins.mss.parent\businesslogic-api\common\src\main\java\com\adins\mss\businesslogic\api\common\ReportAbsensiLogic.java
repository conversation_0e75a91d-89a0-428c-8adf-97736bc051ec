package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportAbsensiLogic {
	List getComboBranch(String branchId, AuditContext callerId); 
	List getReportAttendance(String subsystemId, String branchId, String startDate, String endDate, 
			AuditContext callerId);
	List getUser(String uuidUser, int pageNo, int pageSize, AuditContext callerId);
	Integer countUser(String uuidUser, AuditContext callerId);
	List getUserByBranch(AmMsuser user, String uuidBranch, int pageNo, int pageSize, AuditContext callerId);
	List getUserByBranch(String uuidBranch, int pageNo, int pageSize, AmMs<PERSON> user, String job, 
			AuditContext callerId);
	Integer countUserByBranch(AmMsuser user, String uuidBranch, AuditContext callerId);
	Integer countUserByBranch(String uuidBranch,AmMsuser user, String job, AuditContext callerId);
	Map getReportAttendanceDetail(AmMsuser user, String type, String branchId, String userId, String startDate, 
			String endDate, AuditContext callerId);
	byte[] exportExcel(AmMsuser user, String branchId, String userId, String startDate, String endDate, 
			String type, AuditContext callerId);
	String saveExportScheduler(AmMsuser user, String branchId, String userId, String startDate, String endDate, 
			String type, AuditContext callerId);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
	
	List getUserSurveyorByBranch(AmMsuser user, String uuidBranch, int pageNo, int pageSize, AuditContext callerId);
	Integer countUserSurveyorByBranch(AmMsuser user, String uuidBranch, AuditContext callerId);
}
