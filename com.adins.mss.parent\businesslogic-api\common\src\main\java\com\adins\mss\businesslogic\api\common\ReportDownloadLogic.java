package com.adins.mss.businesslogic.api.common;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportDownloadLogic {
	public static final String DATE_FORMAT_DT_DB = "dd MMM yyyy HH:mm";
	public static final String DATE_FORMAT_DTM = "yyyy-MM-dd HH:mm";
	public static final String DATE_FORMAT_DT = "yyyy-MM-dd";
	public static final String DATE_FORMAT_TM = "HH:mm";
	
	public static final String QUERY_PARAM_FINAL = "?isFinal=1";
	public static final String FLAG_FINAL_TABLE = "2";
	
	public Map<String, Object> getCombo(AmMsuser amMsuser, AuditContext callerId);
	public Map<String, Object> getComboUser(String uuidBranch, AuditContext callerId);
	
	/**
	 * @deprecated due to slow performance on writing excel file.
	 * <p>use {@link #exportTaskReport(AmMsuser, AuditContext, String, String, String, String, String, Date, Date, String, String)} 
	 */
	byte[] exportTaskReport(AuditContext callerId, String uuidUser, String uuidSpv, 
			String uuidBranch, String uuidForm, String startDate, String endDate);
	Map<String, Object> exportTaskReport(AmMsuser user, AuditContext callerId, String uuidUser, 
			String uuidSpv, String uuidBranch, String uuidForm, String uuidVersion, Date startDate, 
			Date endDate, String subsystem, String filterBy);
	public List<?> getUserByBranch(String uuidBranch, int i, int rowPerPageLookup, AmMsuser loginBean, 
			AuditContext auditContext);
	public Integer countUserByBranch(String uuidBranch, AmMsuser loginBean, AuditContext auditContext);
	public List<?> getUserBySpv(String uuidSpv, String uuidBranch, int i, int rowPerPageLookup, 
			AmMsuser loginBean, AuditContext auditContext);
	public Integer countUserBySpv(String uuidSpv, String uuidBranch, AmMsuser loginBean, AuditContext auditContext);
	public String getExcelPath(AuditContext callerId);
	public List getListForm(AuditContext callerId);
	public Map<String, Object> getListFormTask(Date minDate, Date maxDate, AuditContext callerId);
	public void generateCollectionReportSchedulerDaily(AuditContext callerId, MsForm msForm, 
			Date startDate, Date endDate);
	public byte[] generateCollectionReportScheduler(AuditContext callerId, MsForm msForm, String path, 
			Date startDate, Date endDate);
	public TrReportresultlog getReportResult(String rptName, AuditContext callerId);
	public List<TrReportresultlog> getReportResultList(AuditContext callerId);
	public void updateStartTime(TrReportresultlog trReportResultLog, AuditContext callerId);
	public void processRequestReport(AuditContext callerId, TrReportresultlog trReprtResultLog);
	
	//method for generate report csv
	public void processRequestReportCSV(AuditContext callerId, TrReportresultlog trReprtResultLog);
	public byte[] generateReportSchedulerCSV(AuditContext callerId, MsForm msForm, String path, 
			Date startDate, Date endDate, int formVersion);
	public void exportTaskReportCSV(AmMsuser user, AuditContext callerId, String uuidUser, String uuidSpv, 
			String uuidBranch, String uuidForm, String uuidFormVersion, Date startDate, Date endDate, String filterBy);
	public Map<String, Object> getVersion(String uuidForm, AuditContext auditContext);
	public Map<String, Object> getComboPerForm(AmMsuser amMsuser, String formName, AuditContext callerId);
	public Map<String, Object> exportTaskPreSurvey(Map<String, String> searchParams);
}
