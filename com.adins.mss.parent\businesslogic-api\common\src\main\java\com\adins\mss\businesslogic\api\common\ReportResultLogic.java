package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings("rawtypes")
public interface ReportResultLogic {
	public List getListReportResult(AuditContext auditContext, Object params, Object orders);
	public Integer getListReportResultCount(AuditContext auditContext, Object params);
	public void cancelRequest(AuditContext auditContext, String seqNo);
	@PreAuthorize("@mssSecurity.isValidReportDownload(#seqNo, #auditContext.callerId)")
	public Map<String, Object> downloadReportResult(AuditContext auditContext, String seqNo);
	@PreAuthorize("@mssSecurity.isValidReportDownload(#seqNo, #auditContext.callerId)")
	public void deleteRequest(AuditContext auditContext, String seqNo);
}
