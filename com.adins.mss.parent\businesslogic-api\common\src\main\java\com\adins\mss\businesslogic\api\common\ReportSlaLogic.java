package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportSlaLogic {
	List getComboBranch(String branchId, AuditContext callerId);
	List getReportSla(String subsystemId, String branchId, String startDate, String endDate, 
			AuditContext callerId, String uuidForm);
	Map getReportSlaDetail(AmMsuser user, String type, String branchId, String userId, String startDate, 
			String endDate, AuditContext callerId, String uuidForm);
	List getUser(String uuidUser, int pageNo, int pageSize, AuditContext callerId);
	Integer countUser(String uuidUser, AuditContext callerId);
	byte[] exportExcel(<PERSON><PERSON><PERSON> user, String branchId, String userId, String startDate, String endDate, 
			String type, AuditContext callerId, String uuidForm);
	List getFormListCombo(AuditContext callerId);
	String saveExportScheduler(AmMsuser user, String branchId, String userId, String startDate, 
			String endDate, String type, AuditContext callerId, String uuidForm);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
}
