package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrReportresultlog;

public interface ReportTaskOtsLogic {
	List<Map<String,Object>> getTaskOtsList(String [][] params,AuditContext callerId);
	String saveExportScheduler(String [][] params,AuditContext callerId);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
	List<Map<String, Object>> retrieveHeaderByQuestionSet(String uuidVersion, AuditContext callerId);
	List<Map<String, Object>> retrieveComboVersion(AuditContext callerId);
	String retrieveEncryptedImage(String uuidLob, AuditContext callerId);
	
}