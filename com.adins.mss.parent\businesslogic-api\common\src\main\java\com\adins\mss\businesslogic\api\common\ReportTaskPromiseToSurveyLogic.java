package com.adins.mss.businesslogic.api.common;

import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportTaskPromiseToSurveyLogic {
	
	public void exportExcelTask(TrReportresultlog trReportResultLog, AuditContext callerId);
	public Map<String, Object> getCombo(AmMsuser amMsuser, String formName, AuditContext callerId);
	public Map<String, Object> export(Map<String, String> searchParams);
	
}
