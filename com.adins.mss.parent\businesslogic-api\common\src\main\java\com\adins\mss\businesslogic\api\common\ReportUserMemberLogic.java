package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsgroup;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportUserMemberLogic {
	List<Map<String,Object>> getBranchListCombo(String userId,AuditContext callerId);
	List getReportUserMember(String[][] params,AuditContext callerId);
	int getReportUserMemberDetailCount(String[][] params,AuditContext callerId);
	List<AmMsgroup> getGroupListCombo(String uuidMsSub,AuditContext callerId);
	byte[] exportExcel(String[][] params,String type,AuditContext callerId);
	String saveExportScheduler(String[][] params,String type,AuditContext callerId);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
	Map<String, Object> getReportUserMemberDetail(String[][] params,AuditContext callerId);
	List getReportUserMemberDetailReport(String[][] params,AuditContext callerId);
}
