package com.adins.mss.businesslogic.api.common;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
@SuppressWarnings("rawtypes")
public interface SchemeLogic {
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public List listScheme(AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication) and @mssSecurity.hasFormUuid(#uuidForm, authentication)")
	public List schemeOne(long uuidForm, AuditContext callerId);
	public List listPrintItems(AuditContext callerId);
	public List listPrintItems(List listScheme, AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication) and @mssSecurity.hasFormUuid(#uuidForm, authentication)")
	public List listPrint(long uuidForm, AuditContext callerId);
}
