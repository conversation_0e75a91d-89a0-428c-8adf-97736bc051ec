package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface SlaTaskPoloLogic {	
	void processSlaTaskVisitIn(Map<String, Object> mapBean, AuditContext callerId);
	void processSlaTaskVisitOut(Map<String, Object> mapBean, AuditContext callerId);
	List<Map<String, Object>> getSlaTaskVisitIn(AuditContext callerId);
	List<Map<String, Object>> getslaTaskVisitOut(AuditContext callerId);
}
