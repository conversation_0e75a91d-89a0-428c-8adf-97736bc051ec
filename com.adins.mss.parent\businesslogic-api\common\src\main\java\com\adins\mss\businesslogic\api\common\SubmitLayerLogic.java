package com.adins.mss.businesslogic.api.common;

import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.BiometricRequest;
import com.adins.mss.services.model.common.BiometricResponse;
import com.adins.mss.services.model.common.CheckDataEkycRequest;
import com.adins.mss.services.model.common.GetDsrRequest;
import com.adins.mss.services.model.common.GetDsrResponse;
import com.adins.mss.services.model.common.SubmitLayerRequest;

public interface SubmitLayerLogic {
	
	public Map<String, Object> doSubmitLayer(SubmitLayerRequest request, boolean isFromSubmitTask, String sendTaskPreSurvey, AuditContext auditContext);
	int doValidateDukcapil(Map<String, String> paramApi, Integer isPreApproval, AuditContext auditContext);
	int doTeleStatus(Map<String, String> paramApi, Integer isPreApproval, AuditContext auditContext);
	int doCheckNegativeCust(Map<String, String> paramApi, Integer isPreApproval, AuditContext auditContext);
	public BiometricResponse doBiometric(BiometricRequest request, String formName, AuditContext auditContext);
	public GetDsrResponse getDsr(GetDsrRequest request, String formName, AuditContext auditContext);
	
	public Map<String, Object> submitLayerGetKBIJ(SubmitLayerRequest request, Map<String, String> mapApi);
	public Map<String, Object> checkDataEKYC(CheckDataEkycRequest request, AuditContext auditContext);
	
}