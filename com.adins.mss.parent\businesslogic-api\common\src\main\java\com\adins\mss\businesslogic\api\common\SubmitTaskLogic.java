package com.adins.mss.businesslogic.api.common;

import java.math.BigDecimal;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.custom.MappingFormBean;
import com.adins.mss.model.custom.SubmitTaskResultBean;
import com.adins.mss.model.custom.SubmitTaskUpdateResultBean;
import com.adins.mss.services.model.common.AddTaskScoringCAERequest;
import com.adins.mss.services.model.common.AddTaskScoringCAEResponse;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.SubmitTaskHBean;

@SuppressWarnings("rawtypes")
public interface SubmitTaskLogic {
	@PreAuthorize("(hasRole('ROLE_MS') or hasRole('ROLE_MC')) and @mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public SubmitTaskResultBean submitTask(AuditContext auditContext, String application, SubmitTaskHBean taskHBean, SubmitTaskDBean taskDBean[], String imei, String androidId, Map<String, Map<String, String>> filter);
	@PreAuthorize("(hasRole('ROLE_MS')) and @mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public SubmitTaskUpdateResultBean submitTaskUpdate(AuditContext auditContext, String taskUpdateId, String feedbackNotes, String uploadDocument);
	@PreAuthorize("(hasRole('ROLE_MS') or hasRole('ROLE_MO')) and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public String submitAssign(long uuidTaskH, String application,  String notes, long uuidUserAssign, AuditContext callerId);
	@PreAuthorize("(hasRole('ROLE_MS') or hasRole('ROLE_MO')) and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public String submitReAssign(long uuidTaskH, String application, String notes, long uuidUserAssign, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_MS') and @mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public String submitPts(AuditContext auditContext, String application, SubmitTaskHBean taskHBean);
	public List<LocationBean> listEmptyCoordinate(AuditContext callerId);
	public void sendMessageToJms(AuditContext auditContext, String application, SubmitTaskDBean taskDBean[], String taskId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public String updatePromisedDate(long uuidTaskH, Date promisedDate, AuditContext callerId);
	public Map<Integer, MsQuestion> getAllMsQuestion(SubmitTaskDBean[] taskDBean, MsFormhistory formHist, AuditContext auditContext);
	public Map getLatLongSubmitData(TrTaskH taskH, AuditContext auditContext);
	public TrTaskH saveTaskHSurvey(AmMsuser userSubmit, TrTaskH trTaskHOrder,
			BigDecimal latHeader, BigDecimal longiHeader, MsPriority surveyPriority,
			MsBranch msBranchHO, AmMsuser userMh, MsBranch msBranchMfSelected,
			String ptsDate, MappingFormBean formQuestion, boolean isNewLead, AuditContext auditContext);
	public void saveGroupTaskSurveyByExistingTaskid(TrTaskH taskHSurvey, long groupTaskId, AuditContext auditContext);
	public void saveTaskDSurveyIntoRow(TrTaskH taskHSurvey, List<MappingFormBean> listQuestion, Map<Integer, MsQuestion> questions,
			SubmitTaskDBean[] taskDBeanOrder, ImageStorageLocation isl, Path imagePath, AuditContext auditContext);
	public AddTaskScoringCAEResponse addTaskScoringCAE(AddTaskScoringCAERequest request);
	public Map<String, String> submitPolo(AddTaskScoringCAERequest request);
	public Map<String, String> autoSubmitPolo(String taskId, AuditContext callerId);
	public String setMaxRetry(String taskId, AuditContext callerId);
}
