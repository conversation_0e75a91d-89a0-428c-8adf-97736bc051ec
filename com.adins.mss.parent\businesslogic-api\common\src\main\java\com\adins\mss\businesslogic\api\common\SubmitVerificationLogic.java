package com.adins.mss.businesslogic.api.common;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.SubmitVerificationDBean;
import com.adins.mss.services.model.common.SubmitVerificationHBean;

public interface SubmitVerificationLogic {
	@PreAuthorize("hasRole('ROLE_MS') and @mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public String verify(AuditContext auditContext, SubmitVerificationHBean taskHBean,
			SubmitVerificationDBean taskDBean[], String imei, String notes, String androidId);
	@PreAuthorize("hasRole('ROLE_MS') and @mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public void reject(long uuidTaskH, String notes, AuditContext auditContext, String imei, String androidId);
	@PreAuthorize("hasRole('ROLE_MS') and @mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public String rejectWithResurvey(long uuidTaskH, long uuidMsUser, String isSuggested, String notes, AuditContext auditContext, String imei, String androidId);
}
