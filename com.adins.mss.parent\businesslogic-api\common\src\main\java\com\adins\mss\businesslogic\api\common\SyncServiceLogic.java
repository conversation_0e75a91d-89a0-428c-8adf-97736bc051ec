package com.adins.mss.businesslogic.api.common;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsDealer;
import com.adins.mss.model.MsMobiledatafiles;
import com.adins.mss.model.custom.UserManagementBean;
import com.adins.mss.services.model.common.SyncLovBean;
import com.adins.mss.services.model.common.SyncTableProductOfferingBean;
@SuppressWarnings("rawtypes")
public interface SyncServiceLogic {
	//Sync table holiday, lov, form untuk mobile service
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncHoliday(AuditContext auditContext, String dtm_upd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncLov(AuditContext auditContext, SyncLovBean syncLovBean[], String init);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncCash(AuditContext auditContext, String loginId, String init);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public Map syncParamMaster(AuditContext auditContext, String action, String lovGroupName,
			String code, String description, String constraint1, String constraint2, String constraint3,
			String constraint4, String constraint5, String isActive, String isDeleted, 
			String sequence, String userUpdateName);
	
	//To SyncUserBean
	public UserManagementBean toUserSynBean(String subSys, String action,String userId,String userName,String isActive,String officeId, 
			String userParentId,String jobTitle,String isDealer,String userUpdateName, String cashLimit, AuditContext auditContext);
	public MsDealer toDealerSynBean(AuditContext auditContext, String action, String dealerID, String dealerName,
			String dealerAddress, String dealerParent, String isActive, String userUpdateName);
	public Map toBranchSynBean (String action, String branchId, String branchName,String branchAddress,
			String branchParentId,String isActive, String userUpdate,String longitude,String latitude, String userUpdateName, String cashLimitDefault, AuditContext auditContext);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List getLov(AuditContext auditContext, SyncLovBean[] syncLovBean, String init);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncLocation(AuditContext auditContext, String dtm_upd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncZone(AuditContext auditContext, String dtm_upd);
	
	/**
	 * Return list of MsMobiledatafiles
	 * 
	 * @param request Request contains max MsMobiledatafiles' timestamp of dtmCrt or dtmUpd
	 * @param auditContext AuditContext contains callerId (uuidUser) used for retrieving uuidBranch 
	 * @return list of MsMobiledatafiles greater than timestamp.
	 *         if timestamp param is empty, default parameter is '1990-01-01 00:00:00'.
	 *         and if no MsMobiledatafiles exists, an empty array will be returned.
	 *         
	 *         MsMobiledatafilesofbranch is mapping which branch get what link.
	 *         Link differentiation between branches is used because of limitation of cloud storage free account.
	 *         So creation of multiple free account is needed for large amount of download or size.
	 * 		
	 */
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List<MsMobiledatafiles> listDataFiles(Date timestamp, AuditContext auditContext);
	
	//Sync Confins
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncIndustryMargin(AuditContext auditContext, Date dtmUpd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncBlacklistData(AuditContext auditContext, Date dtmUpd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncPo(AuditContext auditContext, Date dtmUpd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncPoAsset(AuditContext auditContext, Date dtmUpd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncPoAssetScheme(AuditContext auditContext, Date dtmUpd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncPoDealer(AuditContext auditContext, Date dtmUpd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncPoOffice(AuditContext auditContext, Date dtmUpd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncRuleFile(AuditContext auditContext, Date dtmUpd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncOtr(AuditContext auditContext, Date dtmUpd);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public List syncPushTable(AuditContext auditContext);
	@PreAuthorize("@mssSecurity.isValidCallerId(#auditContext.callerId, authentication)")
	public void updateStatusSyncPushTable(AuditContext auditContext, long idPushSync);
	public SyncTableProductOfferingBean syncTablePoConstraint(String productOfferingCode, String branchCode);
}
