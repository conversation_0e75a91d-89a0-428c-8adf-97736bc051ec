package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TblProductCategory;
import com.adins.mss.model.TblTaskAssignMode;
@SuppressWarnings("rawtypes")
public interface TaskAssignmentLogic {
	Map<String, Object> listTaskAssignment(Object params, Object orders, int pageNumber, int pageSize,
			AuditContext callerId);
	Map<String, Object> getListMapProductAssign(long productCategoryId, int pageNumber, int pageSize, AuditContext callerId);
	List taskAssignModeList(Object params, AuditContext callerId);
	TblProductCategory getTblProductCategory(String uuid, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_FORM')")
	String insertTaskAssignment(TblProductCategory tblProductCategory, String taskAssignMode, String order,
			AuditContext auditContext);
	@PreAuthorize("hasRole('ROLE_UPD_FORM')")
	String updateTaskAssignment(TblProductCategory tblProductCategory, String taskAssignMode, String order,
			AuditContext auditContext);
	List getProductCategory(AuditContext auditContext);
	void insertMapProductAssign(String selectedTAModeArr, String order, String tblProductCategoryId,
			AuditContext callerId);
	void deleteTaskAssignment(String string, AuditContext auditContext);
	List<TblTaskAssignMode> getListSelectedTAM(String[] selectedTAMode, AuditContext callerId);
	TblTaskAssignMode getTblTaskAssignMode(String code, AuditContext callerId);
	String checkSequence(String selectedTAModeArr, String order, AuditContext callerId);
	String retrieveTaskAssignModeCode(String[] selectedTaskAssignMode);
	
}
