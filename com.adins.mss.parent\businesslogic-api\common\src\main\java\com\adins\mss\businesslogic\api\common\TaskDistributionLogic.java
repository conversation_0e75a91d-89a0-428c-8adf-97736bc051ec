package com.adins.mss.businesslogic.api.common;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import org.springframework.expression.ParseException;
import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrTaskH;

@SuppressWarnings("rawtypes")
public interface TaskDistributionLogic {
	Map<String, Object> listTaskDistribution(Object params, Object orders, AuditContext callerId);
	String getUuidTaskDistributionFk(long subsystemName, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_UPD_TASKDISTRIBUTION')")
	void updateTaskDistribution(long uuidSubsystem, String uuidTaskDistributionFk, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_INS_TASKDISTRIBUTION')")
	void insertTaskDistribution(long uuidSubsystem, String uuidTaskDistributionFk, AuditContext callerId);
	
	/**
     * Round-Robin task distribution using database LEAD syntax function.
     * Will also check againts task loads
     */
	Map<String, Object> distributeRoundRobin(List<TrTaskH> listTaskDist,
			AmMssubsystem subsystem, String uuidMh, String type,  String mode,AuditContext auditContext);
	
    /**
     * Distribute task based to lower load user in Branch.
     * Branch is retrieved from TrTaskH.MsBranch or if in "branch" mode from uuidMh.MsBranch
     * 
     * 
     * @param listTaskDist list of TrTaskH object to be distributed to user
     * @param subsystem subsystem of TrTaskH workflow
     * @param uuidMh uuidUser of Marketing Head or Surveyor itself
     * @param type screen/source of task distribution requestor
     *  <li>"verify" : TaskVerfication
     *  <li>null : else than TaskVerification
     * @param mode mode of screen/source requestor, ByBranch hierarchy or ByUser hierarchy
     *  <li>"branch" : ByBranch
     *  <li>null: else than ByBranch
     * @param auditContext
     * 
     * @return {@link Map<String, Object>} with keys:
     *      <li>"userAssign", AmMsuser.class : last AmMsuser of assigned
     *      <li>"taskH", TrTaskH.class : last TrTaskH element in listTaskDist argument
     *      <li>"uuidProcess", String.class : uuid process of last TrTaskH element
     */ 
	Map<String, Object> distributeLowTask(List<TrTaskH> listTaskDist,
			AmMssubsystem subsystem, String uuidMh, String type,  String mode,AuditContext auditContext);
	
    /**
     * Distribute task based on zipcode against users' zipcode area
     * where targeted user's task < max load setting.
     * If > 1 user have same zipcode, will distribute to lower load user.
     * 
     * @param listTaskDist list of TrTaskH object to be distributed to user
     * @param subsystem subsystem of TrTaskH workflow
     * @param zipCode zipcode of Task to be check against user's zipcode
     * @param uuidMh uuidUser of Marketing Head or Surveyor itself
     * @param type screen/source of task distribution requestor
     *  <li>"verify" : TaskVerfication
     *  <li>null : else than TaskVerification
     * @param mode mode of screen/source requestor, ByBranch hierarchy or ByUser hierarchy
     *  <li>"branch" : ByBranch
     *  <li>null: else than ByBranch
     * @param auditContext
     * 
     * @return {@link Map<String, Object>} with keys:
     *      <li>"userAssign", AmMsuser.class : last AmMsuser of assigned
     *      <li>"taskH", TrTaskH.class : last TrTaskH element in listTaskDist argument
     *      <li>"uuidProcess", String.class : uuid process of last TrTaskH element
     */
	Map<String, Object> distributeZipCode(List<TrTaskH> listTaskDist,
			AmMssubsystem subsystem, String zipCode, String uuidMh, String type, String mode,
			AuditContext auditContext);
	
    /**
     * Task assignment based-on Self assignment logic.
     * Use case example: Integration between Mobile Order (MO) -> Mobile Survey (MS)
     * where MO users' job is flagged with Self Assignment, then MS's task will be
     * assigned to themselves.
     * 
     * TaskH attribute after logic finished:
     * User: as passed in argument; Status: result from 1 wf commit; Assign Date: new Date()
     * 
     * @return {@link Map<String, Object>} with keys:
     *      <li>"taskH" : last TrTaskH element in listTaskDist argument
     *      <li>"uuidProcess" : uuid process of last TrTaskH element
     */	
	Map<String, Object> distributeToCMS(List<TrTaskH> listTaskDist, AmMssubsystem subsystem, AmMsuser user, AuditContext auditContext );
	
	/**
     * Distribute task based on GPS location against users' tracking area
     * where targeted user's task < max load setting.
     * 
     * @param listTaskDist list of TrTaskH object to be distributed to user
     * @param subsystem subsystem of TrTaskH workflow
     * @param latLong lat long of Task to be check against user's tracking area
     * @param uuidMh uuidUser of Marketing Head or Surveyor itself
     * @param type screen/source of task distribution requestor
     *  <li>"verify" : TaskVerfication
     *  <li>null : else than TaskVerification
     * @param mode mode of screen/source requestor, ByBranch hierarchy or ByUser hierarchy
     *  <li>"branch" : ByBranch
     *  <li>null: else than ByBranch
     * @param auditContext
     * 
     * @return {@link Map<String, Object>} with keys:
     *      <li>"userAssign", AmMsuser.class : last AmMsuser of assigned
     *      <li>"taskH", TrTaskH.class : last TrTaskH element in listTaskDist argument
     *      <li>"uuidProcess", String.class : uuid process of last TrTaskH element
     */
	Map<String, Object> distributeGeofencing(List<TrTaskH> listTaskDist,
			AmMssubsystem subsystem, String latLong, String uuidMh, String type, String mode,
			AuditContext auditContext);
	List<BigInteger> assignByKat(List<BigInteger> listUser, String zipCode, String formType, String currentUser, Long uuidProdCategory, String isPiloting, String formName, String jobCode, String isPilotingCae, AuditContext callerId);
	List<BigInteger> assignByDealer(List<BigInteger> listUser, String dealerCode, String mode, String formType, String currentUser, String isPiloting, String formName, String jobCode, String isPilotingCae, AuditContext callerId);
	List<BigInteger> assignByLoad(List<BigInteger> listUser, String formType, String currentUser, String isPiloting, String formName, String jobCode, String isPilotingCae, AuditContext callerId);
	Map poolingTaskDistribution(Long uuidForm, String nik, String zipCode, String dealerCode, String formType, String currentUser, String isPiloting, String formName, String jobCode, String isPilotingCae, AuditContext callerId);
	void doAlternate1(Map bean, AuditContext callerId) throws ParseException;
	void doAlternate2(Map bean, AuditContext callerId);
	List listTaskUnassign(AuditContext callerId);
	List listTaskUnassignCS(AuditContext callerId);
	List listTaskUnassignPreApproval(AuditContext callerId);
	List listTaskUnassignOTSbyCabang(AuditContext callerId);
	void doUnassign(Map bean, AmGeneralsetting gsDuration, AuditContext callerId);
	void doUnassignCS(Map bean, AmGeneralsetting gsDuration, AuditContext callerId);
	public String doAssignOts(Map bean, AuditContext callerId);
	List getTaskAlternate1(AuditContext callerId);
	List getTaskAlternate2(AuditContext callerId);
	public void updateReferantor(String answer, String refId, long uuidTaskH, AuditContext callerId);
	String getFieldPersonCaeFromTaskVisit(String taskIdPolo, String uuidMsUser, String uuidTaskVisit, AuditContext callerId);
}
