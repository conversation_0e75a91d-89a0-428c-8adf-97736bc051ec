package com.adins.mss.businesslogic.api.common;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.AssignTaskListBean;
import com.adins.mss.services.model.common.GetListInquiryHeaderRequest;
import com.adins.mss.services.model.common.RetrieveTaskPoBeanRequest;
import com.adins.mss.services.model.common.TaskListBean;
import com.adins.mss.services.model.common.TaskListMTResponse;
import com.adins.mss.services.model.common.TaskListPoBean;
import com.adins.mss.services.model.common.TaskStatusBean;
import com.adins.mss.services.model.common.TaskUpdateBean;

public interface TaskListLogic {
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<TaskListBean> getTaskListByUuid(AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<TaskStatusBean> getTaskStatusList(AuditContext callerId, List<TaskStatusBean> listTaskStatus);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<TaskListPoBean> getTaskListPo(AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<TaskListBean> retrieveTaskPo(List<RetrieveTaskPoBeanRequest> listUuidTaskH, AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<TaskUpdateBean> getTaskUpdateListByUuid(AuditContext callerId);
	@PreAuthorize("(hasRole('ROLE_MS') or hasRole('ROLE_MO')) and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<AssignTaskListBean> getHeaderTaskListAssign(Date startDate, Date endDate, AuditContext callerId);
	@PreAuthorize("(hasRole('ROLE_MS') or hasRole('ROLE_MO')) and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<AssignTaskListBean> getHeaderTaskListReAssign(Date startDate, Date endDate, AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<AssignTaskListBean> getDetailTask(long uuidTaskH, AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<AssignTaskListBean> getUserAssign(long uuidTaskH, AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<AssignTaskListBean> getUserReAssign(long uuidTaskH, AuditContext callerId);
	TaskListMTResponse getTaskListByUuidMT(AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	Map getListInquiryHeader(GetListInquiryHeaderRequest request, AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	Map getListInquiryDetail(String uuidTaskH, String flag, AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	Map getListInquiryDetailHistory(String uuidTaskHistory, String flag, String codeProcess, AuditContext callerId);
}