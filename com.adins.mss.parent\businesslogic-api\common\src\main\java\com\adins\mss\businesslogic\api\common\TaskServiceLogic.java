package com.adins.mss.businesslogic.api.common;

import java.math.BigDecimal;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsLocation;
import com.adins.mss.model.MsMappingTaskD;
import com.adins.mss.model.MsMappingformH;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.custom.MappingFormBean;
import com.adins.mss.model.custom.MappingTaskParamBean;
import com.adins.mss.services.model.common.AddTaskCAERequest;
import com.adins.mss.services.model.common.AddTaskCAEResponse;
import com.adins.mss.services.model.common.AddTaskOTSRequest;
import com.adins.mss.services.model.common.AddTaskOTSResponse;
import com.adins.mss.services.model.common.AddTaskPoloRequest;
import com.adins.mss.services.model.common.AddTaskPoloResponse;
import com.adins.mss.services.model.common.AddTaskScoringCAERequest;
import com.adins.mss.services.model.common.ApiTestRequest;
import com.adins.mss.services.model.common.ApiTestResponse;
import com.adins.mss.services.model.common.CheckReferantorResponse;
import com.adins.mss.services.model.common.GetOtrResponse;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.TelecheckRequest;
import com.adins.mss.services.model.common.UpdateDataPoloRequest;
import com.adins.mss.services.model.newconfins.AddTaskDetailBean;
import com.adins.mss.services.model.newconfins.SubmitNapRequest;
import com.adins.mss.services.model.newconfins.UpdateStatusBean;
import com.adins.mss.services.model.openpublic.common.AutoCancelTaskBean;
import com.adins.mss.services.model.openpublic.common.AutoCancelTaskResponse;
import com.adins.mss.services.model.openpublic.common.DeleteTaskVisitRequest;
import com.adins.mss.services.model.openpublic.common.UpdateApplNoRequest;
import com.adins.mss.services.model.openpublic.common.UpdateApplNoResponse;
@SuppressWarnings("rawtypes")
public interface TaskServiceLogic {
	public String getStatusTask(String taskId, AuditContext callerId);

	public List getResult(AuditContext callerId, String taskId, String loginId);

	public List getScheme(AuditContext callerId, String formName);

	/**
	 * returns HTML source of task's result answer
	 */
	public String retrieveResultView(String taskId, boolean isIpPublic,
			AuditContext callerId);

	//
	public Map getStatusTask(String taskId, String agreementNumber,
			AuditContext callerId);

	public Status cancelTask(String taskId, String agreementNumber,
			String userUpdateName, String SubSystemCode, AuditContext callerId,
			boolean isNC);

	public Map getSchema(AuditContext auditContext, String subSystemCode);

	public Map reassignTask(AuditContext auditContext, String subSystemCode,
			String refNumber, String fieldPersonId, String userUpdateName,
			boolean isNC);

	public Map getQuestionList(AuditContext auditContext, String schemaId);

	/**
	 * 
	 * @param auditContext
	 * @param flagSource  TerminalID or External System Name which producing the task
	 * @param subSystemCode  Subsystem Name ({@code AmMssubsystem.subsystemName})
	 * @param schemaID  Form Name ({@code MsForm.formName})
	 * @param priority  Priority Description ({@code MsPriority.priorityDesc}), default to "Normal" if empty
	 * @param customerName  Customer's Name
	 * @param customerAddress  Customer's Address
	 * @param customerPhone  Customer's Phone number
	 * @param zipCode  Zipcode for task
	 * @param notes  Notes for task
	 * @param branchID  Branch code ({@code MsBranch.branchCode})
	 * @param refNumber  Application Number in Survey subsystem, Agreement Number in Collection subsystem.
	 *     Or simply 'reference number' in external system.
	 * @param fieldPersonID  Targeted user for the task.
	 *     <p>If empty, task status will be Unassign.
	 *     <br>If ID is supervisor, task will be distributed by DistributionLogic of subsystem
	 *     <br>If ID is subordinate/field person, task will be distributed directly
	 * @param isNeedDefAnswer  Flag whether to process default/initial answers or not, default to "Y" if empty
	 *     <p>see enum {@code com.adins.mss.constants.enums.MobileDefAnswer}
	 *     <ul>
	 *     <li><b>"Y"</b> : have default/initial answers
	 *     <li><b>"N"</b> : no default/initial answers
	 *     <li><b>"DB"</b>: default/initial answers from prevTaskID
	 *     </ul>
	 * @param surveyAssignmentID  Survey taskID as task location (LatLng). Location will lookup for Home tagging.
	 *     Will replace latitude and longitude argument.
	 * @param latitude  Latitude for task location
	 * @param longitude  Longitude for task location
	 * @param taskID  TaskID for task
	 * @param userUpdateName  Actor's loginID who is doing addTask
	 * @param listTaskDetail  Default/initial answers
	 * @param prevTaskID  Source's taskID for "DB" default answers.
	 *     Also mean last survey's taskID for the resurvey.   
	 * @param mode
	 * 
	 * @exception  
	 *     com.adins.mss.exceptions.RemoteException<br>
	 *         if flagSource is empty or notfound<br>
	 *         if subSystemCode=Collection and same refNumber exists and outstanding for today<br> 
	 *         if fieldPersonID not found or not active<br>
	 *         if schemaID not found or not active<br>
	 *         if priority not found or not active<br>
	 *         if branchID not found or not active<br>
	 *         if surveyAssignmentID not found<br>
	 *         if subsystemCode=Survey and additionalTask has the same form in existing group<br>
	 *         if detail question is mandatory and readonly, but answer is empty<br>
	 *         if detail answer for lov code not found<br>
	 *         
	 * @return
	 * 
	 */
	public Map addTask(AuditContext auditContext, String flagSource,
			String subSystemCode, String schemaID, String formVersion, String priority,
			String customerName, String customerAddress, String customerPhone,
			String zipCode, String notes, String branchID, String refNumber,
			String fieldPersonID, String isNeedDefAnswer,
			String surveyAssignmentID, String latitude, String longitude,
			String taskID, String userUpdateName,
			AddTaskDetailBean[] listTaskDetail, String prevTaskID, String mode, String isPilotingCae);

	public Map addTaskMT(AuditContext auditContext, String flagSource,
			String subSystemCode, String schemaID, String priority,
			String branchID, String mobileUserID, String locationName,
			String phoneNo, String notes, Date datetimeAssignment,
			String groupSequence, String taskSequence, String isNeedDefAnswer,
			String taskID, String userUpdateName,
			AddTaskDetailBean[] listTaskDetail, String prevTaskId,
			Date dateAssign, String uploader);

	public Map addTaskGroup(AuditContext auditContext, String flagSource,
			String subSystemCode, String schemaID, String customerName,
			String branchID, String refNumber, String taskID,
			String isNeedDefAnswer, String fieldPersonID, String priority,
			String userUpdateName);

	Map addTaskGroupMT(AuditContext auditContext, String flagSource,
			String subSystemCode, String schemaID, String customerName,
			String branchID, String taskID, String priority,
			String userUpdateName, AmMsuser amMsUser, MsPriority msPriority,
			MsForm msForm, String phoneNo, String notes, MsLocation msLocation,
			Date dateAssign, String mobileUserID, String groupSeq,
			String taskSeq, String uploader);
	
	public Map addTaskUpdate(AuditContext auditContext,
			String taskID, String pendingNotes, String docuproFeedback, String userUpdateName, String category, String subCategory, String reasonDetail, String validasi);

	public Status updateOrderStatus(AuditContext auditContext,
			String orderNumber, UpdateStatusBean[] updateStatusBean);

	public Status reconcileStatus(AuditContext auditContext, List<String> taskID,
			String flag);

	public Map getAnswerDetail(AuditContext auditContext, String taskID);
	
	public TrTaskH getTaskHByTask(AuditContext auditContext, String taskID, String applNo);
	
	public List<TrTaskH> createTaskSurveyPiloting(AuditContext auditContext, AmMsuser userSubmit, TrTaskH trTaskHOrder,
			AmMssubsystem subsystemMs, MsMappingformH msMapFormH, long groupTaskId, AddTaskScoringCAERequest addTaskScoringReq);

	TrTaskH saveTaskHSurvey(AmMsuser userSubmit, TrTaskH trTaskHOrder, BigDecimal latHeader, BigDecimal longiHeader,
			MsPriority surveyPriority, MsBranch msBranchHO, AmMsuser userMh, MsBranch msBranchMfSelected,
			String ptsDate, MappingFormBean formQuestion, boolean isNewLead, boolean isCAE, AuditContext auditContext);

	void saveTaskDSurveyIntoRow(TrTaskH taskHSurvey, List<MappingFormBean> listQuestion,
			Map<Integer, MsQuestion> questions, SubmitTaskDBean[] taskDBeanOrder, ImageStorageLocation isl,
			Path imagePath, AddTaskScoringCAERequest addTaskScoringReq, AuditContext auditContext);

	void saveGroupTaskSurveyByExistingTaskid(TrTaskH taskHSurvey, long groupTaskId, AuditContext auditContext);

	Map getLatLongSubmitData(TrTaskH taskH, AuditContext auditContext);

	Map<Integer, MsQuestion> getAllMsQuestion(SubmitTaskDBean[] taskDBean, MsFormhistory formHist,
			AuditContext auditContext);

	SubmitTaskDBean[] getSubmitTaskDBean(long uuidTaskH, AuditContext callerId);
	
	public Map<String, Object> getLookupTaskList(AuditContext auditContext, String orderNo, 
			String tanggalAplikasi, String cmo, String cabang, String npwp, String penghasilan,
			String nik, String mobilePhone, String custName, String birthdate, String birthplace,
			String taskId, String userName, Integer rowPerPage, Integer page);
	
	// Integrasi Polo
	public AddTaskPoloResponse addTaskPoloVisit(AddTaskPoloRequest request);
	public AddTaskPoloResponse addTaskPoloPreIA(AddTaskPoloRequest request);
	public AddTaskPoloResponse addTaskPoloSurveyMotorku(AddTaskPoloRequest request);
	public AddTaskPoloResponse addTaskPoloSurveyNonMotorku(AddTaskPoloRequest request);
	
	public ApiTestResponse apiTest(ApiTestRequest req);
	
	void updateDataPoloAsync(UpdateDataPoloRequest req);
	
	void createTaskAutoAssignSurvey(TrTaskH trTaskH, String ref1Code, String ref1Name, String ref2Code, String ref2Name,
			String tanggalVisit, String prospectInterest, String notesTaskVisit, String notesCrm, AuditContext callerId);
	public void insertTaskDForReferantor (MsFormhistory formHistory, TrTaskH taskH, AuditContext auditContext);

	String submitPreIA(long uuidTaskH, long uuidUser, AuditContext auditContext);

	Map<String, Object> printPO(String uuidTask, AuditContext callerId);

	void autoAssignPreIA(String uuidTaskOrder, String uuidUser, String procCategoryCode, AuditContext callerId);
	
	MsFormhistory getLastFormHistory(String formName, AuditContext auditContext);
	
	//Integrasi CAE
	public AddTaskCAEResponse addTaskCaePromiseToSurvey(AddTaskCAERequest request, Integer isPA, String isIA, String fieldPersonFromCae, String jobCodeFromCae, String applNo, String uuidTaskHVisit, AuditContext auditContext);
	
	public AddTaskOTSResponse addTaskOts(AddTaskOTSRequest request);
	
	public void insertDefaultAnswerQuestionPilotingCae(TrTaskH taskH, boolean isTaskPilotingCae, AuditContext auditContext);
	
	public void createTaskCaeFromPoloVisit(TrTaskH trTaskH, String taskType, boolean isCompleted, String jsonRequest, String refCode, String refName, AuditContext callerId);
	
	boolean isExistTaskGuarantor(String applNo, AuditContext audit);

	Map requestOrGetResultTelecheckCAE(TelecheckRequest req, AuditContext auditContext);
	
	public AddTaskCAEResponse addTaskCaeOffline(AddTaskCAERequest request, AuditContext auditContext);
	
	public String createTaskCAEFromSubmitTask(TrTaskH trTaskH, String json, String taskType, boolean isFromPoloVisit,
			boolean isFromAddTaskCae, boolean isHavePreSurvey, AddTaskScoringCAERequest request, boolean isAutoCancel, AuditContext auditContext);
	
	SubmitTaskDBean[] getSubmitTaskDBean(long uuidTaskH);
	
	public Map<String, String> getDetailSurveyorForUpdatePolo(String uuidMsUser, AuditContext auditContext);
	
	public UpdateApplNoResponse doUpdateApplNo(UpdateApplNoRequest request, AuditContext auditContext);
	
	public AutoCancelTaskResponse doAutoCancelTask(AddTaskScoringCAERequest request, AuditContext auditContext);
	
	public void doCopyTaskReleased(String mobileTaskId, String oldApplNo, String newApplNo, String newTaskId, List<Map> allTaskByApplNo, AuditContext auditContext);
	
	public List<Map> doValidateTaskCopy(String mobileTaskId, String oldApplNo,  String newApplNo, AuditContext callerId);
	
	public void doDeleteTaskVisit(String orderNo, AuditContext callerId);
	
	public String getAnswerByApplNo(String refId, String applNo, AuditContext auditContext);
	
	public void submitDocuproAsync(UpdateApplNoRequest request, AuditContext auditContext);
	
	public List<TrTaskH> createTaskSurveyPilotingCAE(AuditContext auditContext, AmMsuser userSubmit, TrTaskH trTaskHOrder,
			AmMssubsystem subsystemMs, long groupTaskId,
			AddTaskScoringCAERequest addTaskScoringCAERequest, MappingTaskParamBean mappingParam);
	
	public void saveTaskDSurveyCAEIntoRow(TrTaskH taskHSurvey, List<MsMappingTaskD> listQuestion,
			Map<Integer, MsQuestion> questions, SubmitTaskDBean[] taskDBeanOrder, ImageStorageLocation isl,
			Path imagePath, AddTaskScoringCAERequest addTaskScoringCAERequest, String prodCat,
			AuditContext auditContext);
	
	void autoCancelUpdateIDE(String groupTaskId, AuditContext callerId);
	
	public GetOtrResponse getOtr(Map<String, String> request, String formName, AuditContext callerId);
 
	public CheckReferantorResponse checkReferantor(String taskId, String formName, Map<String, String> mapValue,
			AuditContext callerId);

	public boolean checkMappingSource(String sourceData, long uuidForm, String productCategoryCode, boolean isFromPoloVisit);

	public AddTaskPoloResponse addTaskPoloPromiseToVisit(AddTaskPoloRequest request, AuditContext auditContext);


}