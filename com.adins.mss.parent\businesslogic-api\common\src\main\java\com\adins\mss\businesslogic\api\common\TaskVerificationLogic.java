package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrTaskH;

@SuppressWarnings("rawtypes")
public interface TaskVerificationLogic {
	TrTaskH getTask(long uuidTaskH, AuditContext callerId);	
	Map<String, Object> listAnswer(TrTaskH taskH, AmMsuser userLoggedIn, AuditContext auditContext) ;
	List<Long> getUserList(long loginUser, AuditContext auditContext);
	Map<String, Object> listTask(String uuidForm, String dateStart, String dateEnd, int dateType, String branchAdvSearch, 
			String custNameSearch, String custAddressSearch, String applNoSearch, String fieldPersonSearch, 
			String uuidStatus, List<Long> userUuids, Long[] branchHiearchy, String mode, int pageNumber, 
			int pageSize, AuditContext auditContext);
	String getStatusId(Object[][] statusParams, AuditContext auditContext);
	@PreAuthorize("hasRole('ROLE_VERIFICATION') OR hasRole('ROLE_MS')")
	void updateTaskHStatus(String[] uuidAnswer, String[] textAnswer, String[] latAns,
			String[] longAns, long uuidTaskH, String[] uuidQuestions, AmMsuser loginBean, Integer index, 
			String notes, AuditContext callerId);
	List<Map<String, Object>> mapOptAnsByLov(Map<String, String> valueArr, long uuidForm, 
			long uuidQuestion, long uuidFormHistory, AuditContext callerId);
		
	Map<String, Object> getValue(long uuidQuestion, long uuidFormHistory, AuditContext callerId);
	Map<String, String> getBranchListCombo(String branchId,AuditContext callerId);
	List viewMapPhoto(String uuid, AuditContext callerId);
	void updateDetail(String textAnswer, String optionAnswerId, String latitude, String longitude, 
			String uuidForm, String uuidTaskH, String uuidQuestion, String answerType, AuditContext auditContext);
	List getWfEngineNextStep(TrTaskH trTaskH, AmMssubsystem msSubsystem, AuditContext callerId);
	Map<String, Object> getSuggestedUser(long uuidTaskH, String mode, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_RESURVEY') OR hasRole('ROLE_MS')")
	void rejectWithResurvey(long uuidTaskH, long uuidMsUser, String notes, String mode, AmMsuser loginBean, 
			AuditContext callerId);
	Long[] getBranchByLogin(long uuidBranch, AuditContext callerId);
	List<Map<String, Object>> autoCalculate(List<Map<String,String>> refIdsWithAnswer, long uuidQuestion, 
			long uuidFormHistory, AuditContext callerId);
	List getFormListCombo(AuditContext callerId);
	
	List<Map<String, Object>> copyValue(List<Map<String, String>> refIdsWithAnswer,
			long uuidQuestion, long uuidFormHistory, long uuidUser, int seqQuest, AuditContext callerId);
	
	List<Map<String, Object>> validation(List<Map<String, String>> refIdsWithAnswer, long uuidQuestion, 
			long uuidFormHistory, AuditContext callerId);
	Map<String, Object> luOnline(AmMsuser userLoggedIn, String refId, String lovGroup, String searchVal, 
			long uuidFormHistory, String choice, AuditContext callerId);
	List<Map<String, Object>> relevant(List<Map<String, String>> refIdsWithAnswer, String uuidForm, 
			String uuidTaskH,  AuditContext callerId);
	
}