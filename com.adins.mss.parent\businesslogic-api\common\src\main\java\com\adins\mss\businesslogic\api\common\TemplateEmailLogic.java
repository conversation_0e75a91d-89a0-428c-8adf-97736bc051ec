package com.adins.mss.businesslogic.api.common;

import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsTemplate;

public interface TemplateEmailLogic {
	MsTemplate getTemplate(String params, AuditContext callerId);
	Map<String, Object> listTemplate(Object params, Object orders, 
			int pageNumber, int pageSize, AuditContext callerId);
	void updateTemplate(MsTemplate mstemp,  AuditContext callerId);
	void insertTemplate (MsTemplate mstemp, AuditContext callerId);
	void deleteTemplate(String uuid, AuditContext callerId);
}
