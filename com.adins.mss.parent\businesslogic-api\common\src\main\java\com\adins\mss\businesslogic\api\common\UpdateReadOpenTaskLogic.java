package com.adins.mss.businesslogic.api.common;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.UpdateReadOpenTaskBean;

public interface UpdateReadOpenTaskLogic {
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication) and @mssSecurity.hasTaskUuid(#taskH.uuidTaskH, authentication)")
	public void updateReadOpenTask(UpdateReadOpenTaskBean taskH, AuditContext callerId);
	public void sendMessageToJms(UpdateReadOpenTaskBean taskH, AuditContext callerId);
}
