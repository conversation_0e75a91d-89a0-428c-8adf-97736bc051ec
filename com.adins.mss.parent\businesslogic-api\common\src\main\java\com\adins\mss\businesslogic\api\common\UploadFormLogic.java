package com.adins.mss.businesslogic.api.common;

import java.io.File;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings("rawtypes")
public interface UploadFormLogic {
	List<Map<String, Object>> listGroup(AuditContext callerId);
	byte[] exportExcel(AuditContext callerId);
	byte[] processSpreadSheetFile(File uploadedFile,AuditContext callerId);
	List listUploadForm(Object params, Object orders, AuditContext callerId);
	String getSubsystem(String callerId);
	List listQuestion(Object params, AuditContext callerId);
}
