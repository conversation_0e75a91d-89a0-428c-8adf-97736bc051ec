package com.adins.mss.businesslogic.api.common;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrUploadtasklog;

public interface UploadTaskLogic {
	Map<String, Object> processSpreadSheetSaveToFile(File uploadedFile, String fileName, AmMsuser loginBean, AuditContext callerId) throws IOException;
	Map<String, Object> processSpreadSheetFile(File uploadedFile, String fileName, AmMsuser loginBean, AuditContext callerId, String uploader) throws IOException;
	List<TrUploadtasklog> getUploadTaskLogList(AuditContext callerId);
	void doUploadTaskProcess(TrUploadtasklog trUploadTaskLog, AuditContext callerId);
}
