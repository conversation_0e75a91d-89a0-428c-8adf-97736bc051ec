package com.adins.mss.businesslogic.api.common;

import java.math.BigDecimal;
import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.services.model.common.UserBean;

public interface UserLogic {
	/**
	 * 
	 * @param uuidTaskH TrTaskH object to be distributed to user
	 * @param mode mode of screen/source requestor, ByBranch hierarchy or ByUser hierarchy
     *  <li>"branch" : ByBranch
     *  <li>else : Other than ByBranch
	 * @param callerId
	 * @return
	 */
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<UserBean> getListUser(long uuidTaskH, String mode, AuditContext callerId);
	public AmMsuser fetchUserByUuid(long uuidUser, AuditContext callerId);
	void substractCashOnHand(BigDecimal totSetoran, String loginId, AuditContext callerId);
	void updateCashOnHand(long uuidTaskH, String totalBayar, AuditContext callerId);
	BigDecimal getCashOnHand(AuditContext callerId);
}