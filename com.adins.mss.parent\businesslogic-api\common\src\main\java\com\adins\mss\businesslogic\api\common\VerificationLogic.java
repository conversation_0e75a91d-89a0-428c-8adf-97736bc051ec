package com.adins.mss.businesslogic.api.common;

import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
@SuppressWarnings("rawtypes")
public interface VerificationLogic {
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication) and @mssSecurity.hasTaskUuid(#uuidTaskH, authentication)")
	public List listTask(long uuidTaskH, AuditContext callerId);
	
	@PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication) and @mssSecurity.hasTaskUuid(#uuidTaskH, authentication)")
	public List getVerificationImage(long uuidTaskH, long uuidQuestion, AuditContext callerId);
}
