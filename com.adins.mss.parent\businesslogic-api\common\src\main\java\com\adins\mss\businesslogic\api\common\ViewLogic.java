package com.adins.mss.businesslogic.api.common;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface ViewLogic {
	Map<String, Object> getViewResullt(String taskId, String subsystemCode, String isIPPublic, int isTaskLink, AuditContext callerId);
	Map<String, Object> getViewGroupTask(String groupTaskId, AuditContext callerId);
	List getInquiryDetail(String uuid, AuditContext callerId, String codeProcess);
	String checkTaskH(Object[] params, AuditContext callerId);
}
