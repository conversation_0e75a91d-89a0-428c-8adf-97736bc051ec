package com.adins.mss.businesslogic.api.interfacing;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TblApiDashboard;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.custom.UploadImageRequestBean;
import com.adins.mss.model.custom.ViewImageRequestBean;
import com.adins.mss.model.custom.ViewImageResponseBean;
import com.adins.mss.services.model.collection.CollectionHistoryResponse;
import com.adins.mss.services.model.collection.InstallmentScheduleResponse;
import com.adins.mss.services.model.collection.PaymentHistoryResponse;
import com.adins.mss.services.model.common.CheckESignRegistrationResponse;
import com.adins.mss.services.model.common.CheckEsignRegistrationRequest;
import com.adins.mss.services.model.common.CheckOtrValueResponse;
import com.adins.mss.services.model.common.CheckStatusTaskRequest;
import com.adins.mss.services.model.common.CheckStatusTaskResponse;
import com.adins.mss.services.model.common.GenerateInvitationRequest;
import com.adins.mss.services.model.common.GetDsrFromPoloResponse;
import com.adins.mss.services.model.common.GetReferantorResponse;
import com.adins.mss.services.model.common.InstantApprovalResponse;
import com.adins.mss.services.model.common.OnlineLuResponse;
import com.adins.mss.services.model.common.SendDataPhotoToApiBean;
import com.adins.mss.services.model.common.SendDataPhotoToApiResponse;
import com.adins.mss.services.model.common.SubmitNegativeCustPoloRequest;
import com.adins.mss.services.model.common.SubmitNegativeCustPoloResponse;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.TaxPersonalResponse;
import com.adins.mss.services.model.common.TeleStatusCheckCallbackRequest;
import com.adins.mss.services.model.common.UpdateDataPoloRequest;
import com.adins.mss.services.model.common.UpdateDataPoloResponse;
import com.adins.mss.services.model.common.ValidateBiometrikResponse;
import com.adins.mss.services.model.common.VerifyReferantorResponse;
import com.adins.mss.services.model.newconfins.CIFBean;
import com.adins.mss.services.model.newconfins.CheckDukcapilResponse;
import com.adins.mss.services.model.newconfins.SubmitNapRequest;
import com.adins.mss.services.model.newconfins.SubmitPage1Request;
import com.adins.mss.services.model.newconfins.SubmitPage2Request;
import com.adins.mss.services.model.newconfins.SubmitPreIARequest;
import com.adins.mss.services.model.newconfins.UpdateStatusMainDealerResponse;

@SuppressWarnings("rawtypes")
public interface IntFormLogic {
    public String submitResult(AuditContext auditContext, String taskId, String isFinal);
    public byte[] requestPO (String orderNo, String callerId);
    public byte[] reportIncentive (String loginId, String startDate, String endDate, String callerId);
    public String synSchema (String schemaId, String action, String callerId);
    public String cancelTask (String taskID, String flagSource, String callerID);
    public String saveResult (AuditContext auditContext, String taskID, String flagSource, String subsystemCode, String callerID, String isFinal);
    public boolean authenticateUser(AmMsuser amMsUser, String password);
    public String saveDepositReport(String batchID);
    public CollectionHistoryResponse getListCollActHistByAgrNo(AuditContext auditContext, String taskID);
    public InstallmentScheduleResponse getListInstSchdlByAgrNo(AuditContext auditContext, String taskID);
    public PaymentHistoryResponse getListPayHistByAgrNo(AuditContext auditContext, String taskID) throws ParseException;
    public Map getPathApp(String isIPPublic);
    public String resetPassword(String loginId, Date dob, String email, String ktpNo, AuditContext auditContext);
    public String changePassword(String loginId, String oldPassword, String newPassword, AuditContext auditContext);
    public String insertLossDeal(AuditContext auditContext, String taskId, String isFinal);
    public String submitNegativeList(AuditContext auditContext, String taskId, String isFinal, String isRecommendation);
    
    public ViewImageResponseBean viewImageDMS(AuditContext auditContext, ViewImageRequestBean viewImageRequestBean);
    public String uploadImageResponse(AuditContext auditContext, UploadImageRequestBean uploadImageRequestBean);
    
    public String submitNap(AuditContext auditContext, String taskId, String isFinal);
    public String submitNapPilotingCae(AuditContext auditContext, String taskId, String isFinal);
    public String submitTaskUpdate(AuditContext auditContext, String taskUpdateId);
    public InstantApprovalResponse submitInstantApproval(AuditContext auditContext, String taskId, String isFinal);
    	
    //tambahan untuk LuOnline
    @PreAuthorize("@mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public OnlineLuResponse luOnline (String refId, String lovGroup, String searchVal, String choiceFilterVal, AuditContext callerId);
    public OnlineLuResponse luOnlineMaskapaiAsuransi (String refId, String lovGroup, String searchVal, String choiceFilterVal, AuditContext callerId);
    public boolean validateLuOnlineCode(String code, String lovGroup, AuditContext callerId);
    public CIFBean revampDukcapil(AuditContext auditContext, String nik, String name, String birthPlace, String birthDate, String userId, String password, String ipUser);
	public CheckDukcapilResponse checkDukcapil(AuditContext auditContext, String isPilotingCAE, String nik, String name, String birthPlace, String birthDate, String userId, String password, String ipUser, CIFBean cifBean, String taskIdPolo, Integer isPreApproval); //tambahan preApproval
	
	List<String> getPendingTaskId();
	Map requestOCR(String imagePath, String type, Boolean isPilotingCAE, AuditContext auditContext);
	Map validatePage1(SubmitPage1Request submitData, AuditContext callerId);
	Map validatePage2(SubmitPage2Request submitData, AuditContext callerId);

	//asliri verify tax personal
	TaxPersonalResponse verifyTaxPersonal(AuditContext audit,String nik,String npwp,String income);
	
	//advance ai, tele status check
	Map checkTeleStatus(AuditContext audit,String phoneNumber,int phoneOwner,Boolean isPilotingCAE, String taskIdPolo, Integer isPreApproval); //tambahan preApproval
	
	//tele check status
	MssResponseType teleStatusCheckCallback(TeleStatusCheckCallbackRequest request);
	
	//ws teleowner
	boolean requestTeleowner(String idNumber, String phoneNumber, Boolean isPilotingCAE, AuditContext auditContext);
	Map preparation(TrTaskH task, String flagStatus, AuditContext callerId);
	
	//pending preparation task
	public List<String> getPendingPreparationTaskIds();
	
	//auto submit task
	public List<String> getAutoSubmitTaskId(AmGeneralsetting maxRetry, AmGeneralsetting duration, AuditContext auditContext);
	
	//tambahan untuk biometrik
	public ValidateBiometrikResponse validateBiometric(AuditContext auditContext, String nik, String name, String birthdate, String birthplace,
					String address, String identity_photo, String selfie_photo);
	
	//Polo
	public	UpdateDataPoloResponse updateDataPolo(UpdateDataPoloRequest request, TrTaskH trTaskH, String statusMss,
			String status, String flagVoidSla, String negativeCustomer, String reasonReject, String flagAutoDelete, String reasonDelete,
			AuditContext auditContext);
	public SubmitNegativeCustPoloResponse submitNegativeCustomerPoloToWise(SubmitNegativeCustPoloRequest request);
	public CheckStatusTaskResponse getStatusProspectPolo(CheckStatusTaskRequest req);
	public void checkProspekPoloAsync(AuditContext callerId);
	Map submitPreIA(SubmitPreIARequest submitData, AuditContext callerId);
	
	public UpdateStatusMainDealerResponse updateStatusTaskMainDealer(TrTaskH taskH, AuditContext callerId);
	
	public MssResponseType GenerateInvitationLink(GenerateInvitationRequest request);
	
	//API OCR
	public SendDataPhotoToApiResponse uploadIdCard(String base64, AuditContext callerId);
	
	public String submitPolo(SubmitTaskDBean[] taskdBean, TrTaskH trTaskH, String taskType, String isPreOffline, AuditContext callerId);
	public boolean checkNegativeCust(String idNumber, String taskIdPolo, String name, String birthPlace, String birthDate, String motherName, Integer isPreApproval, String contractNo,AuditContext auditContext); //tambahan preApproval
	Map checkBiometricCae(String officeCode, String officeRegionCode, String productOfferingCode, String nik, String selfiePhoto, String nikPsgn, String selfiePhotoPsgn, String nikGrtr, 
			String selfiePhotoGrtr, String taskIdPolo, String contractNo, Integer isPreApproval, String includeSpouse, String includeGuarantor, AuditContext auditContext); //tambahan preApproval
	Map checkSlikCAE(String nik, String fullName, String birthdate, String birthplace, String mothername, 
			String nikPsgn, String fullNamePsgn, String birthdatePsgn, String birthplacePsgn, String mothernamePsgn, 
			String nikGrntr, String fullNameGrntr, String birthdateGrntr, String birthplaceGrntr, String mothernameGrntr, String ntf, 
			String officeCode, String officeRegionCode, String productOfferingCode, String callMode, String contractNo, AuditContext auditContext);
	Map checkIncome(String nik, String income, AuditContext auditContext);
	String submitTaskGuarantor(AuditContext auditContext, TrTaskH taskGuarantor);
	String submitTaskOTS(AuditContext auditContext, String flagSourceOts, TrTaskH taskOTS);
	public GetDsrFromPoloResponse getDsrFromPolo(Map<String, String> paramApi, AuditContext auditContext);
	public String updateMobileAssignmentId(String mobileTaskId, String newAppNo, AuditContext auditContext);
	public GetReferantorResponse getReferantorCodeFromNc(String uniqueId, AuditContext auditContext);
	public String updateFromMssCopyApp(String newTaskIdPolo, String orderNo, String groupTaskId, String uuidTaskH, AuditContext auditContext);
	
	String submitDocupro(AuditContext auditContext, TrTaskH trTaskH, SubmitNapRequest bean);
	
	String getHistoryCreditKBIJ(String request);
	void updateStatusIDE(TrTaskH trTaskH, String status, String type, AmMsuser loginBean, TblApiDashboard tblBean,
			AuditContext callerId);
	String updateStatusWiseIDE(TrTaskH trTaskH, String type, AmMsuser loginBean, TblApiDashboard tblBean,
			AuditContext callerId);
	public CheckESignRegistrationResponse CheckEsignRegistrationStatus(CheckEsignRegistrationRequest checkEsignRequest);
	public CheckOtrValueResponse GetOtrValue(String officeCode, String assetCode, String manufYear);
	UpdateDataPoloResponse updateDataPoloAsync(UpdateDataPoloRequest request, TrTaskH trTaskH, String statusMss,
			String status, String flagVoidSla, String negativeCustomer, AuditContext auditContext);

	 public VerifyReferantorResponse VerifyReferantor(String taskId);
	 public void updateDataPoloLayer(String json, TrTaskH trTaskH, AuditContext callerId);

 
}