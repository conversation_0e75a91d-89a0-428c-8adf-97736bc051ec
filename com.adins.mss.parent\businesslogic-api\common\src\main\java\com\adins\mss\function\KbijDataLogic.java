package com.adins.mss.function;

import java.util.Map;

import com.adins.mss.services.model.common.AddTaskCAERequest;
import com.adins.mss.services.model.common.AddTaskPoloRequest;
import com.adins.mss.services.model.common.AddTaskScoringCAERequest;
import com.adins.mss.services.model.common.KbijData;

public interface KbijDataLogic {
	
	public AddTaskCAERequest formatDataKBIJ_addTaskCAE(AddTaskCAERequest req);
	public AddTaskScoringCAERequest formatDataKBIJ_addTaskScoring(AddTaskScoringCAERequest req);
	public AddTaskPoloRequest formatDataKBIJ_addTaskPolo(AddTaskPoloRequest req);
	
	public KbijData formatDataKBIJ(KbijData dataKbij);
	
	public String getDataKBIJ(KbijData dataKbij, String kbijDataType);
	
	public Map<String, String> checkResultCode(String uuidTaskH);
	
	public String test();
}
