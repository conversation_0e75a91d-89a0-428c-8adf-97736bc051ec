package com.adins.mss.helper;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface TaskHelper {
    
    /**
     * Check whether task is MS subsystem and adhoc
     * @param uuidTaskH
     * @return
     *      <p><code>true</code> if task is subsystem of MS and not assigned from core system
     *      <p><code>false</code> if not subsystem of MS or assigned from core system
     */
    public boolean isTaskSurveyAndAdHoc(long uuidTaskH, AuditContext callerId);
    
    /**
     * Check whether survey task is adhoc
     * @param surveyUuidTaskH
     * @return
     *      <p><code>true</code> if not assigned from core system
     *      <p><code>false</code> if assigned from core system
     */
    public boolean isSurveyAdHoc(long surveyUuidTaskH, AuditContext callerId);

}
