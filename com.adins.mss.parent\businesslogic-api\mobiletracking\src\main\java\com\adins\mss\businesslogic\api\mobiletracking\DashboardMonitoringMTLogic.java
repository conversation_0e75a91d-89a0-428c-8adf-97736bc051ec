package com.adins.mss.businesslogic.api.mobiletracking;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrLocationhistory;
import com.adins.mss.model.custom.DashboardMonitoringBean;

@SuppressWarnings("rawtypes")
public interface DashboardMonitoringMTLogic {	
	List<DashboardMonitoringBean> getUser(String uuidUser, String uuidSubSystem, String modeMap, String startDate, String endDate, AuditContext callerId) throws ParseException;
	List getTaskMonitoring(String uuidColl, String uuidSubsystem, AuditContext callerId);
	AmMsuser getUser(String uuidColl, AuditContext callerId);
	DashboardMonitoringBean collectionTracking(String uuidColl, String startDate, String endDate, AuditContext callerId);    
    DashboardMonitoringBean retrieveInfoWindowData(String uuidCollector, String uuidSubsystem, AuditContext callerId);    
    AmMsuser getAmSpv(String uuidSpv, AuditContext auditContext);
	boolean checkIsSpv(AmMsuser user, AuditContext auditContext);
	Map<String, Object> getHierarkiBranchLogin(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	Map getSpvByBranch(String uuidBranch, int i, int rowPerPageLookup, AuditContext auditContext);
	List<DashboardMonitoringBean> retrieveCollectionTagging(String uuidSpv,
			String uuidSubsystem, AuditContext callerId);
    List<DashboardMonitoringBean> retrieveMobileLocation(long uuidSpv,
            long uuidSubsystem, AuditContext callerId);
    String getTrackingInterval(AuditContext callerId);
    List<DashboardMonitoringBean> retrieveLocationHistory(String[] uuidMobileUsers,
            Date startDate, Date endDate, AuditContext callerId);
	List<TrLocationhistory> retrieveLocationHistory(String uuidMobileUser,
			Date startDate, Date endDate, AuditContext callerId);
	DashboardMonitoringBean retrieveLocationHistoryMT(String uuidMobileUser,
			Date startDate, Date endDate, AuditContext callerId);
	List<DashboardMonitoringBean> retrieveMobileArea(String[] uuidMobileUsers,
	            AuditContext callerId);
	List userList(String uuidSpv);
}
