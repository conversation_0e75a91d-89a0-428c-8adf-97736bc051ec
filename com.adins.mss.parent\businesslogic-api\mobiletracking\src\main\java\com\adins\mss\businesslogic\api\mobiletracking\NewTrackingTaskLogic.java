package com.adins.mss.businesslogic.api.mobiletracking;

import java.util.Date;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsLocation;
import com.adins.mss.model.MsPriority;

@SuppressWarnings("rawtypes")
public interface NewTrackingTaskLogic {

	Map<String, Object> getCombo(AmMsuser amMsuser, AuditContext callerId);

	void insertNewTrackingTask(AuditContext auditContext, String uuidMsuser,
			String uuidPriority, String uuidForm, String dateAssign,
			String uuidLocation, String notes, String uuidGroupTask, String seqNo);

}
