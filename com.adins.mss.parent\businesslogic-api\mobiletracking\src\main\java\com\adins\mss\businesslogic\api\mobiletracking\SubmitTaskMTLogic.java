package com.adins.mss.businesslogic.api.mobiletracking;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.SubmitTaskHBean;
import com.adins.mss.services.model.common.SyncLocationBean;
import com.adins.mss.services.model.common.SyncZoneBean;

public interface SubmitTaskMTLogic {
	public String submitTask(AuditContext auditContext, String application, SubmitTaskHBean taskHBean, SubmitTaskDBean taskDBean[], 
			SyncLocationBean location, SyncZoneBean zone, String imei, String androidId, String groupTaskId);
	public void sendMessageToJms(AuditContext auditContext, String application, SubmitTaskDBean taskDBean[], String taskId);
}
