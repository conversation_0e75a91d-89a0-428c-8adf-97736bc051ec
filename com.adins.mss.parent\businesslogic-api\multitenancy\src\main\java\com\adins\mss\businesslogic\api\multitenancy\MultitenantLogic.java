package com.adins.mss.businesslogic.api.multitenancy;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.multitenancy.model.MsTenant;

public interface MultitenantLogic {

    public void logoutAll(AuditContext callerId);
    
    public List<MsTenant> listTenants(String tenantCodeFilter, AuditContext callerId);
    
    public int countTenants(AuditContext callerId);
}
