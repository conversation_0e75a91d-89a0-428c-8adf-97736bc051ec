package com.adins.mss.businesslogic.api.order;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.services.model.order.CancelOrderBean;
import com.adins.mss.services.model.order.CancelOrderDetail;

@SuppressWarnings("rawtypes")
public interface CancelOrderLogic {	
	List listCancelOrder(Object params, AuditContext callerId);
	Integer countListCancelOrder(Object params, AuditContext callerId);
	List getCancelOrder(long uuid, AuditContext callerId);
	Map detailCancelOrder(Object[][] params, AuditContext callerId);
	List viewMapPhoto(long uuid, AuditContext callerId);
	
	/* == CANCEL ORDER MOBILE INTERFACE == */
	@PreAuthorize("hasRole('ROLE_MO') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<CancelOrderBean> cancelOrderHeader(String orderNumber, Date endDate, Date startDate, String custName, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_MO') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	List<CancelOrderDetail> cancelOrderDetail(long uuidTaskH, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_CANCEL_ORDER') OR hasRole('ROLE_MO')")
	String cancelOrder(String orderNumber, String notes, AuditContext callerId);
	
	/* cancel order by hierarki user */
	List listCancelOrderByHierarkiUser(Object params, AuditContext callerId);
	Integer countListCancelOrderByHierarkiUser(Object params, AuditContext callerId);
}
