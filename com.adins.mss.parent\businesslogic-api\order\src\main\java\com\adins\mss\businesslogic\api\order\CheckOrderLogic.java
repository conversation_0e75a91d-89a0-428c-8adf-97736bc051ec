package com.adins.mss.businesslogic.api.order;

import java.util.Date;
import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.order.CheckOrderBean;

public interface CheckOrderLogic {
	@PreAuthorize("hasRole('ROLE_MO') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public List<CheckOrderBean> listResponseServer(Date startDate, Date endDate, String orderNumber, String custName, AuditContext callerId);
}
