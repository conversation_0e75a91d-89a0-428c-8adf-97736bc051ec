package com.adins.mss.businesslogic.api.order;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.services.model.order.ContentNewsHeader;
@SuppressWarnings("rawtypes")
public interface ContentNewsLogic {
	public List contentNewsHeader (AuditContext callerId, ContentNewsHeader[] listContentHeader);
	public List contentNewsDetail(String uuid_mobile_content_h, AuditContext callerId);
}
