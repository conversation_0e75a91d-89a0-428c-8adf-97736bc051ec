package com.adins.mss.businesslogic.api.order;

import java.util.List;



import com.adins.framework.persistence.dao.model.AuditContext;
@SuppressWarnings({ "rawtypes" })
public interface DashboardAnalyticsOrderLogic {
	List getPerformanceMTD(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	List getPerformanceToday(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	List getDealerStatusMTD(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	List getDealerStatusToday(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	List getTodayStatus(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	List getDataAnalyticMobile(String task, String diagram, AuditContext callerId);
	String getAutoupdateInterval(AuditContext callerId);
	
}
