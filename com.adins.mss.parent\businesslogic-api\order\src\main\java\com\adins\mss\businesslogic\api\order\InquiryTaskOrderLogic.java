package com.adins.mss.businesslogic.api.order;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings("rawtypes")
public interface InquiryTaskOrderLogic {
	List listInquiryTaskOrderNativeString(Object params, String isBranch, AuditContext callerId);
	List getHeader(long uuid, long idSubsystem, AuditContext callerId);
	List getHeaderFinal(long uuid, long idSubsystem, AuditContext callerId);
	List getHistory(long uuid, AuditContext callerId);
	List getHistoryFinal(long uuid, AuditContext callerId);
	List getAnswer(long uuid, AuditContext callerId);
	List getAnswerFinal(long uuid, AuditContext callerId);
	Integer countListInquiryTaskOrderNativeString(Object params, String isBranch, AuditContext callerId);
	List getListTaskSurvey(long uuid, AuditContext callerId);
	List getListTaskSurveyFinal(long uuid, AuditContext callerId);
}
