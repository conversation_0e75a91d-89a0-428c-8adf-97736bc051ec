package com.adins.mss.businesslogic.api.order;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface NewOrderLogic {
	List<Map<String,Object>> getForm(long uuidUser,AuditContext callerId);
	public List<Map<String, Object>> listAnswer(long uuidForm, AuditContext callerId);
	public List<Map<String, String>> getInitLov(String lovGroup,long uuidDealer,String[] constraints, AuditContext callerId);
	public List<Map<String, String>> getInitLovBranch(String lovGroup,long uuidDealer,String[] constraints, AuditContext callerId);
	
	public List<Map<String, String>> getLov(String[] answerCFilter,String lovGroup, long uuidDealer,String jobCode, AuditContext callerId);
	public List<Map<String, String>> getLovBranch(String[] answerCFilter,String lovGroup, long uuidDealer,String jobCode, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_INS_ORDER')")
	public void insert(String uniqueTaskId, String name, String phone, String address, String notes, String data, String form, long uuidUser,
			long formHistory, AuditContext callerId);

	public List<Map<String, Object>> validation(List<Map<String,String>> list, long uuidForm,
			long uuidQuestion, AuditContext callerId);
	
	List<Map<String, Object>> relevant(List<Map<String, String>> list,
			long uuidForm, AuditContext callerId);
			
	public List<Map<String, Object>> copyValue(List<Map<String, String>> list,
			long uuidForm, long uuidQuestion, long uuidUser,
			int seqQuest, AuditContext callerId);
}
