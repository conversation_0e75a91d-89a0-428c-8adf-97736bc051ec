package com.adins.mss.businesslogic.api.order;

import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;

public interface OrderMonitoringLogic {
	Map<String, Object> listOrderMonitoringBranchExc(Object params, AuditContext callerId);
	Map<String, Object> listOrderMonitoringBranchDob(Object params, AuditContext callerId);
	Map<String, Object> listOrderMonitoringDealerExc(Object params, AuditContext callerId);
	Map<String, Object> listOrderMonitoringDealerDob(Object params, AuditContext callerId);
	Object[] getSlaVal(AuditContext callerId);
	boolean checkMenuList (Object params, Boolean isBbranch, AuditContext callerId);
	
	Map<String, Object> listOrderMonitoringByDealer(Object params, AuditContext callerId);
}
