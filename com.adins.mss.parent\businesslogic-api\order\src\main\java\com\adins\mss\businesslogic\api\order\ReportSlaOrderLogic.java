package com.adins.mss.businesslogic.api.order;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportSlaOrderLogic {
	public List<Map<String, Object>> getBranchListCombo(String branchId, AuditContext callerId);
	public List<Map<String, Object>> getDealerListComboByBranch(String branchId, AuditContext callerId);
	public List<Map<String, Object>> getDealerListCombo(String branchId, AuditContext callerId);
	public List getReportSlaOrder(String[][] params, String type, String isBranch, AuditContext callerId);
	public List getReportSlaOrderDetail(String[][] params, String type,
			String type2, String isBranch, AuditContext callerId);
	public byte[] exportExcel(String[][] params, String task, String type,
			String type2, String isBranch, AuditContext callerId);
	String saveExportScheduler(String[][] params, String task, String type,
			String type2, String isBranch, AuditContext callerId);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
}
