package com.adins.mss.businesslogic.api.order;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings("rawtypes")
public interface RequestIncentiveLogic {
	List listRequestIncentive(Object params, AuditContext callerId);
	Integer countRequestIncentive(Object params, AuditContext callerId);
	byte[] printIncentive(String loginId, String startDate, String endDate, AuditContext auditContext);
}
