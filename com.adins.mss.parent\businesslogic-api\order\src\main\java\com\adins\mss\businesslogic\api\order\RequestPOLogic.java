package com.adins.mss.businesslogic.api.order;

import java.util.List;
import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings("rawtypes")
public interface RequestPOLogic {
	List listTaskDO(Object params, String isBranch, AuditContext callerId);
	Integer countListTaskDO(Object params, String isBranch, AuditContext callerId);
	byte[] getPrintPO(String orderNo, AuditContext callerId);
}
