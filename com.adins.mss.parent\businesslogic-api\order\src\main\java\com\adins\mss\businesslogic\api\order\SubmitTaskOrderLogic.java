package com.adins.mss.businesslogic.api.order;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.custom.SubmitTaskResultBean;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.SubmitTaskHBean;

public interface SubmitTaskOrderLogic {
    /**
     * High level Flow submit
     * a. Validasi
     *   1. cek IMEI/AndroidID by GS [subsystem]_MODE_LOGIN_SUBMIT, default IMEI
     *   2. cek MS active harus ada Mapping Form.
     * b. Proses Save
     * c. Create task survey (jika ada integrasi MS)
     * d. Interfacing data Order
     */
	@PreAuthorize("((hasRole('ROLE_MO') and @mssSecurity.isValidCallerId(#auditContext.callerId, authentication) and @mssSecurity.hasTaskUuid(#taskHBean.uuid_task_h, authentication)) OR hasRole('ROLE_INS_ORDER')) ")
	public SubmitTaskResultBean submitTask(AuditContext auditContext, String application, SubmitTaskHBean taskHBean, SubmitTaskDBean taskDBean[], String imei, String androidId);
}
