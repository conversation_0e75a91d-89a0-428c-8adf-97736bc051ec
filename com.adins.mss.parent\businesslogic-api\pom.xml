<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.adins.mss</groupId>
    <artifactId>com.adins.mss.parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>com.adins.mss.businesslogic-api</artifactId>
  <packaging>pom</packaging>
  <dependencies>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>
  			com.adins.framework.persistence.dao-model
  		</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.core</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.model</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
	<dependency>
		<groupId>org.springframework</groupId>
		<artifactId>spring-context</artifactId>
		<version>${spring-framework.version}</version>
	</dependency>
	<dependency>
	    <groupId>org.springframework.security</groupId>
	    <artifactId>spring-security-core</artifactId>
	    <version>${spring-security.version}</version>
	</dependency>
  </dependencies>
  <modules>
  	<module>am</module>
  	<module>common</module>
  	<module>survey</module>
  	<module>order</module>
  	<module>collection</module>
  	<module>multitenancy</module>
  	<module>mobiletracking</module>
  </modules>
</project>