package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrTaskdetaillob;

public interface AssetDstHeatMapLogic {
	Map<String, Object> getCombo(AmMsuser amMsuser, AuditContext callerId);
	Map<String, Object> branchMap(long uuidBranch, AuditContext callerId);
	List<TrTaskdetaillob> listTaskDetailLobs(String uuidAssetTag, String uuidBranch, String month, String year, String uuidSubsystem, AuditContext callerId);
}
