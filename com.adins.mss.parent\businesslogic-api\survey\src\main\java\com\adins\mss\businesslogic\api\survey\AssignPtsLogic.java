package com.adins.mss.businesslogic.api.survey;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings( "rawtypes" )
public interface AssignPtsLogic {
	Map<String, Object> listTaskH(AmMsuser amMsuser, Object params, Object paramsCnt, AuditContext callerId);
	Map<String, Object> listTaskToAssign(String[] selectedTask, AuditContext callerId);
	Map<String, Object> listUser(AmMsuser amMsuser, AuditContext callerId);
	AmMsuser getUser(long uuidMsUser, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_ASSIGN_PTS_TASK')")
	void assignTask(String[] selectedTask, long uuidMsUser, AmMsuser loginBean, long uuidPriority, AuditContext callerId);
	List getPriorityList(Object params,Object order,AuditContext callerId);
	void assignPtsTask(AuditContext callerId) throws ParseException;
	Map<String, Object> listTaskHByHierarkiUser(AmMsuser amMsuser, Object params, Object paramsCnt, AuditContext callerId);
}
