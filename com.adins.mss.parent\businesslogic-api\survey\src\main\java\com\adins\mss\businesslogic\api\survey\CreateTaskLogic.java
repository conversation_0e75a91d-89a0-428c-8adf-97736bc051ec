package com.adins.mss.businesslogic.api.survey;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.custom.TaskDukcapilBean;
import com.adins.mss.services.model.newconfins.CheckDukcapilBean;
import com.adins.mss.services.model.newconfins.CheckDukcapilResponse;

public interface CreateTaskLogic {
	List<Map<String,Object>> getForm(long uuidUser, String flagFormIA, AuditContext callerId);
	public List<Map<String, Object>> listAnswer(long uuidForm, AuditContext callerId);
	
	public List<Map<String, String>> getInitLov(String lovGroup,long uuidDealer,String[] constraints, AuditContext callerId);
	public List<Map<String, String>> getInitLovBranch(String lovGroup,long uuidDealer,String[] constraints, AuditContext callerId);
	
	public List<Map<String, String>> getLov(String[] answerCFilter,String lovGroup, long uuidDealer,String jobCode, AuditContext callerId);
	public List<Map<String, String>> getLookupOffline(String choiceFilter, String product, AuditContext callerId);
	public List<Map<String, String>> getLovBranch(String[] answerCFilter,String lovGroup, long uuidDealer,String jobCode, AuditContext callerId);
	
	public Map insert(String data, long uuidUser, String uuidForm, AmMsuser loginBean, String isSelfAssignment, String idHistCust, AuditContext callerId);

	public List<Map<String, Object>> validation(List<Map<String,String>> list, long uuidForm,
			long uuidQuestion, AuditContext callerId);
	
	List<Map<String, Object>> relevant(List<Map<String, String>> list,
			long uuidForm, AuditContext callerId);
			
	public List<Map<String, Object>> copyValue(List<Map<String, String>> list,
			long uuidForm, long uuidQuestion, long uuidUser,
			int seqQuest, AuditContext callerId, String isOpenLookup);
	
	public CheckDukcapilResponse dukcapil(AuditContext auditContext, String isPilotingCAE, String nik, String name, String birthPlace, String birthDate, String userId, String appSource, String taskIdPolo, Integer isPreApproval);
	Map validateDukcapil(TaskDukcapilBean taskDukcapilBean, String isPilotingCAE, boolean isReview, String idHistCust, AuditContext auditContext);
	public void insertTaskDukcapil(TaskDukcapilBean taskDukcapilBean, String isMatchDukcapil, String dbName, AuditContext auditContext);
	public TaskDukcapilBean validasiFormLead(String data, long uuidUser, boolean flagDukcapil, AuditContext callerId);
	public Map getCurrentUuidQuestionGroup(String uuidFormHistory, String uuidQuestionGroup, AuditContext callerId);
	
	public List<Map<String, Object>> listAnswer2(String uuidFormHistory, String uuidQuestionGroup, AuditContext callerId);
	public Map submitPage(String submitPage, String data, long uuidUser, String uuidForm, String isSelfAssignment, String uuidTaskH, boolean lastQuestionGroup, 
			String uuidFormHistory, String isValidateDukcapil, String isValidTelecheck, String customerRating, String isRO, String idHistCust, AuditContext callerId);
	public void regeneratePrintPO(AuditContext callerId);
	List<Map<String, Object>> copyValueOcr(List<Map<String, String>> list, long uuidForm, long uuidQuestion,
			long uuidUser, int seqQuest, AuditContext callerId);
	Map validateOCR(String base64, String type, AuditContext auditContext);
	Map imageQuality(AuditContext auditContext);
	Map checkDataOCR(String data, String idApiUsage, String idApiUsage2, boolean isCopyQuestion, String uuidForm, AuditContext auditContext);
	Map refreshTelecheck(String idApiUsage, AuditContext auditContext);
	Map checkTeleStatus(AuditContext audit, String phoneNumber, int phoneOwner);
	AmGeneralsetting retrieveMaxRetry(String gsCode, AuditContext callerId);
	Map getApiUsage(AuditContext audit, String idApiUsage);
	void setStatusApiUsage(AuditContext audit, String idApiUsage);
	
	public String fixCopyVal(String data, AuditContext callerId);
	List listPreIA(Object params, AuditContext callerId);
	Integer countlistPreIA(Object params, AuditContext callerId);
	Map<String, String> getBranchListCombo(long branchId, AuditContext callerId);
	List getFormListCombo(AuditContext callerId);
	Map<String, Object> detailPreIA(long uuidTaskH, AuditContext callerId);
	TrTaskH getTaskH(long uuid, AuditContext callerId);
	List getWfEngineNextStep(TrTaskH trTaskH, AmMssubsystem msSubsystem, AuditContext callerId);
	List viewMapPhoto(long uuid, AuditContext callerId) throws UnsupportedEncodingException;
	String getKelurahanFromSubmitPage(String data, AuditContext callerId);
	
}
