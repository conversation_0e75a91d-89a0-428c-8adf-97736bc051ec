package com.adins.mss.businesslogic.api.survey;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings({"rawtypes"})
public interface DashboardAnalyticsLogic {
	List getPerformanceMTD(long uuidBranch, AuditContext callerId);
	List getPerformanceToday(long uuidBranch, AuditContext callerId);
	List getDrillMonth(long uuidBranch, AuditContext callerId);
	List getSubmitMTD(long uuidBranch, AuditContext callerId);
	List getSubmitToday(long uuidBranch, AuditContext callerId);
	List getVerificationMTD(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	List getVerificationToday(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	List getSurveyorStatusToday(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	List userList(long uuidBranch, long uuidSubsystem, AuditContext callerId);
	String getAutoupdateInterval(AuditContext callerId);
	List getDataAnalyticMobile(String task, String diagram,	AuditContext callerId);
}
