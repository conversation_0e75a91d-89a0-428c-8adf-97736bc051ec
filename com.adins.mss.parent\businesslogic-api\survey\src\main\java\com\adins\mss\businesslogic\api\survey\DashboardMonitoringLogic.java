package com.adins.mss.businesslogic.api.survey;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.custom.DashboardBranchBean;
import com.adins.mss.model.custom.DashboardMonitoringBean;
import com.adins.mss.model.custom.DashboardSvyBean;
@SuppressWarnings("rawtypes")
public interface DashboardMonitoringLogic {
	DashboardBranchBean getBranch(long uuidBranch, long uuidSpv, AuditContext callerId);
	List getUsers(Object params, long idSubsystem, AuditContext callerId) throws ParseException;
	List getOtherSvy(Object params,String flag ,AuditContext callerId);
	int getOtherSvyCount(Object params,AuditContext callerId);
	Map getTaskLists(String assignType, String modeInOut, long idSvy, String idSvySelected, AmMsuser spv, List orherSvy, int pageNumber,
			int pageSize, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_SAVE_DM_ASSIGNIN')")
	void saveAssignIn(String idTasks, String assignType, long idSvy, AmMsuser actor, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_SAVE_DM_ASSIGNOUT')")
	void saveAssignOut(String idTasks, String idSvy, String idSvySelected, AmMsuser actor, AuditContext callerId);
	List getTaskMonitoring(String uuidSvy, String uuidSubsystem, AuditContext callerId);
	AmMsuser getUser(String uuidSvy, AuditContext callerId);
	List getSubmittedTaskList(long uuidSvy, long uuidSubsystem, AuditContext callerId);
	List getOutstandingTaskList(long uuidSvy, long uuidSubsystem, AuditContext callerId);
//	List getLuSurveyor(Object params, AuditContext callerId);
	DashboardMonitoringBean surveyorTracking(long uuidSvy, String startDate, String endDate, AuditContext callerId) throws ParseException;
	List getForms(long uuidSubsystem, AuditContext callerId);
	List getPriority(AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_SAVE_DM_NEWTASK')")
	void saveNewTask(TrTaskH newTask, long idSvy, AmMsuser actor, long groupTaskId, AuditContext callerId);
	
	DashboardMonitoringBean retrieveInfoWindowData(long uuidSurveyor, long uuidSubsystem, AuditContext callerId);
	
	List<DashboardSvyBean> retrieveTagLegal(long[] uuidUsers, Date startDate, Date endDate, AuditContext callerId);
	List<DashboardSvyBean> retrieveTagDriveway(long[] uuidUsers, Date startDate, Date endDate, AuditContext callerId);
	List<DashboardSvyBean> retrieveTagOffice(long[] uuidUsers, Date startDate, Date endDate, AuditContext callerId);
	List<DashboardSvyBean> retrieveTagVehicle(long[] uuidUsers, Date startDate, Date endDate, AuditContext callerId);
	List<DashboardSvyBean> retrieveTagHome(long[] uuidUsers, Date startDate, Date endDate, AuditContext callerId);
	List getDropDownList(Object params, Object order, AuditContext callerId);
	Map getGroupTaskList(Object[][] params, Object order, int pageNo, String load, AuditContext callerId);
	List getFormCombo(Object params, AuditContext callerId);
	
	AmMsuser getAmSpv(long uuidSpv, AuditContext auditContext);
	boolean checkIsSpv(AmMsuser user, AuditContext auditContext);
	Map getSpv(int i, int rowPerPageLookup, String uuidMsUser, AuditContext auditContext);
	Map<String, Object> getHierarkiBranchLogin(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId);
	Map getSpvByBranch(String uuidBranch, int i, int rowPerPageLookup, AuditContext auditContext);
	Map getGroupTaskListByHierarkiUser(Object[][] params, Object order, int pageNo, String load, AuditContext callerId);
	List userList(long uuidSpv, AuditContext callerId);
	
	List<?> getAllBranch(AuditContext callerId);
	int getCountAllBranch(AuditContext callerId);
	List<?> getSurveyorBranchWithColour(String uuidbranch, String idxBranch,AuditContext callerId);
	DashboardMonitoringBean getPercentageBattery(long uuidSvy, long idSubsystem, String atb_code, DashboardMonitoringBean bean);
	//int getCountSurveyorBranchWithColour(AuditContext callerId);
	String getAutoupdateInterval(String GsCode, AuditContext callerId);
}
