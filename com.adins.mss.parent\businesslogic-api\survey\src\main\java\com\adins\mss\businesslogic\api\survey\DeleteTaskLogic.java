package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrTaskH;

@SuppressWarnings("rawtypes")
public interface DeleteTaskLogic {
	public List listDeleteTask(Object params, AuditContext callerId);
	public Integer countDeleteTask(Object params, AuditContext callerId);
	public List getStatusList(Object params, Object orders, AuditContext callerId);
	public TrTaskH getDeleteTask(long uuidTaskH, AuditContext callerId);
	public Map detailDeleteTask(Object[][] params, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_DEL_TASK')")
	public void deleteTask(String[] selectedTask, TrTaskH trTaskH, <PERSON><PERSON><PERSON> loginBean, AuditContext callerId);
	
	public List listDeleteTaskByHierarkiBranch(Object params, AuditContext callerId);
	public Integer countDeleteTaskByHierarkiBranch(Object params, AuditContext callerId);
	
	Map<String, Object> listTaskToDelete(String[] selectedTask, AuditContext callerId);
	
	public void autoDeleteTask(long uuidTaskH, Map applNoDelete, AuditContext callerId);
	List getListTask(AuditContext callerId);
}
