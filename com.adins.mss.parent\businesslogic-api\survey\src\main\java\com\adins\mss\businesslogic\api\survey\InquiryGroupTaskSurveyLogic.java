package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.MsGrouptask;

@SuppressWarnings("rawtypes")
public interface InquiryGroupTaskSurveyLogic {
	
	Map<String, Object> listInquiryGroupTaskSurvey(Object params, AuditContext callerId);
	List getDropDownList(Object params,Object order,AuditContext callerId);
	@PreAuthorize("@mssSecurity.isValidInquiryGroupTaskId(#groupTaskId, #modeAction, #callerId.callerId)")
	List getDetailGroupTask(String uuidSubsystem, String groupTaskId, String modeAction, AuditContext callerId);
	Map<String, Object> listInquiryGroupTaskSurveyByUser(Object params,  AuditContext callerId);
	List getDetailGroupTaskWithEncript(String groupTaskId, AuditContext callerId);
	MsGrouptask getMsGroupTask(String groupTaskId, AuditContext callerId);
	
	List splitParams(String params);
}
