package com.adins.mss.businesslogic.api.survey;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;

@SuppressWarnings("rawtypes")
public interface InquiryTaskSurveyLogic {
	
	List listInquiryTaskSurveyNativeString(Object params, AuditContext callerId);
	List listInquiryTaskSurveybyBranchNativeString(Object params, AuditContext callerId);
	List listInquiryTaskSurveybyBranchRegionNativeString(Object params, AuditContext callerId);
	
	List getDropDownList(Object params,Object order,AuditContext callerId);
	List getDropDownRegionList(AuditContext callerId);
	List getDropDownRegionListExcludeRegionUserLogin(Long uuidBranch);
	List getStatusList(Object params,Object order,AuditContext callerId);
	List getPriorityList(Object params,Object order,AuditContext callerId);
	List getFormList(Object params,Object order,AuditContext callerId);
	List getInquiryTaskSurveyDetailAnswerHistory(Object params,Object order, String codeProcess,String uuidTaskRejectedHistory,AuditContext callerId) throws UnsupportedEncodingException;
	List getInquiryTaskSurveyDetailAnswerHistoryFinal(Object params,Object order, String codeProcess,String uuidTaskRejectedHistory, AuditContext callerId) throws UnsupportedEncodingException;
	List getInquiryTaskSurveyDetailTaskHistory(Object params,Object order,AuditContext callerId);
	List getInquiryTaskSurveyDetailTaskHistoryFinal(Object params,Object order,AuditContext callerId);

	public Integer countListInquiryTaskSurveyNativeString(Object params, AuditContext callerId);
	public Integer countListInquiryTaskSurveyBranchNativeString(Object params, AuditContext callerId);
	public Integer countListInquiryTaskSurveyBranchRegionNativeString(Object params, AuditContext callerId);
	
	@PreAuthorize("@mssSecurity.isValidInquiryTaskId(#params, #modeAction, #callerId.callerId)")
	Object[] getDetailTaskSurvey (Object params, String modeAction, AuditContext callerId) throws UnsupportedEncodingException;
	Object[] getDetailTaskSurveyFinal (Object params, AuditContext callerId) throws UnsupportedEncodingException;
	String getUuidSvyId(String svyId, String subsysCode, AuditContext callerId);
	
	String getTaskByHistroy(String uuidTaskHist, AuditContext callerId);
	String getTaskByHistroyFinal(String uuidTaskHist, AuditContext callerId);
	String checkMenu(AuditContext callerId);
	
	public Map<String, Object> exportPDF(String uuidTask, String codeProcess, String flagSource, 
			String uuidTaskRejectedHistory, AuditContext callerId) throws UnsupportedEncodingException;
	
	String retriveFileNamePDF(String uuidTask, String msSubSystem, boolean isFinal);
	String retrieveResultType(String uuidTaskH, String formName, String flagFinal, AuditContext auditContext);
	List listInquiryGeneralTask(Object params, AuditContext callerId);
	Integer countListGeneralTask(Object params, AuditContext callerId);
	Object[] getRegion(Long uuidBranch);
}
