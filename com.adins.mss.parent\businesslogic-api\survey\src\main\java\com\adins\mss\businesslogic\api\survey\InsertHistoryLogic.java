package com.adins.mss.businesslogic.api.survey;

import com.adins.mss.model.CheckCustomerHistory;
import com.adins.mss.model.TblCaeData;
import com.adins.mss.model.TblOtsData;
import com.adins.mss.model.TblPoloData;
import com.adins.mss.services.model.newconfins.CheckCustomerRequest;

public interface InsertHistoryLogic {
	CheckCustomerHistory insertCheckCustomerHistory (String userId, CheckCustomerRequest checkCustomerReq, String customerNo, boolean isWise, String notes, String retry, String jsonRequest, String jsonResponse);
	TblPoloData insertPoloHistory(String request, String response, int flag, String taskIdPolo, long groupTaskId, String productCategoryCode, String isFlagKawanInternal);
	TblCaeData insertCAEHistory(String request, String response, String isIA, String orderNoCae, long groupTaskId, String productCategoryCode, String isFlagKawanInternal);
	TblOtsData insertOTSHistory(String request, String response, String groupTaskId);
}
