package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;
import org.w3c.dom.Document;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.custom.TaskDukcapilBean;
import com.adins.mss.services.model.newconfins.CheckDukcapilBean;

public interface PrintPOLogic {	
	public List listPrintPO(Object[][] params, AuditContext callerId);	
	public Integer countListPrint(Object[][] params, AuditContext callerId);
	public Map<String, Object> getPrintPO(String idPO);
	public void updatePODownload(String uuidTaskH);
	public void updatePODownloadIdPo(String idPo);
}
