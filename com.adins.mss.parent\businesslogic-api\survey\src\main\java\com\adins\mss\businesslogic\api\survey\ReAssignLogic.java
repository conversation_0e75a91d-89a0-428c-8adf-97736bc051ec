package com.adins.mss.businesslogic.api.survey;

import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings("rawtypes" )
public interface ReAssignLogic {
	Map getUserList(String[][] params, String[][] paramsCount, AuditContext auditContext);
	Map getTaskList(long idSvy, int pageNumber, int pageSize, String[][] params, AmMsuser spv, boolean isNeedComboList, AuditContext auditContext);
	@PreAuthorize("hasRole('ROLE_SAVE_REASSIGN')")
	void saveReAssign(String idTasks, long idSelectedSvy, AmMsuser userLogin, AuditContext auditContext);
}