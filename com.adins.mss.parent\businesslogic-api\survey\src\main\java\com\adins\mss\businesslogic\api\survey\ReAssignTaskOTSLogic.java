package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings("rawtypes")
public interface ReAssignTaskOTSLogic {
	
	Map<String, Object> listTaskH(String type, AmMsuser amMsuser, Object params, Object paramsCnt, AuditContext callerId);
	List getStatusTaskList(Object params,Object order,AuditContext callerId);
	Map<String, Object> listTaskToReAssign(String[] selectedTask, AuditContext callerId);
	List getPriorityList(Object params, Object order, AuditContext auditContext);
	List getSpvList(String mode, Object params, Object order, AuditContext callerId);
	void reAssignTask(String[] selectedTask, Long uuidMsUser, AmMsuser loginBean, long uuidPriority, AuditContext callerId);
	Map<String, Object> listUserAssign(String mode, String uuidBranchTask, String flagSource, AuditContext callerId);
	AmMsuser getUser(long uuidMsUser, AuditContext callerId);
	Map<String, Object> listTaskToAssign(String[] selectedTask, AuditContext callerId);
}
