package com.adins.mss.businesslogic.api.survey;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportApiUsageLogic {	
	List getComboBranch(String branchId, AuditContext callerId);
	List getSummary(String branchId, String userId, String startDate, String endDate, 
			AuditContext callerId, String usageType);
	List getDetail(String branchId, String userId, String startDate, String endDate, 
			AuditContext callerId, long userBranch, String usageType);
	int getDetailCount(String branchId, String userId, String startDate, String endDate, 
			AuditContext callerId, long userBranch, String usageType);
	byte[] exportExcel(String branchId, String userId, String startDate, String endDate, 
			String type, AuditContext callerId, String usageType);
	String saveExportScheduler(String branchId, String userId, String startDate, String endDate, 
			String type, AuditContext callerId, String usageType);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
	List getFormListCombo(AuditContext callerId);
}
