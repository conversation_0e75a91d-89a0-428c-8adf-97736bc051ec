package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportBranchLogic {
	List<Map<String,Object>> getBranchListCombo(String branchId,AuditContext callerId);
	List getReportBranch(String[][] params,String type,AuditContext callerId);
	public byte[] exportExcel(String[][] params,String type,AuditContext callerId);
	String saveExportScheduler(String[][] params,String type,AuditContext callerId);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
	List<Map<String,Object>> getFormListCombo(AuditContext callerId);
}
