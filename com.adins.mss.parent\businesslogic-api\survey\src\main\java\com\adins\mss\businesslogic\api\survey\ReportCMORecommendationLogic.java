package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface ReportCMORecommendationLogic {
	List<Map<String,Object>> getCmoRecommendationList(String[][] params,AuditContext callerId);
	List<Map<String,Object>> getRegionListCombo(String uuidBranch, AuditContext callerId);
	List<Map<String,Object>> getProdOffTypeListCombo(AuditContext callerId);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
	public byte[] exportExcel(String[][] params,AuditContext callerId);
	String saveExportScheduler(String[][] params,AuditContext callerId);

}
