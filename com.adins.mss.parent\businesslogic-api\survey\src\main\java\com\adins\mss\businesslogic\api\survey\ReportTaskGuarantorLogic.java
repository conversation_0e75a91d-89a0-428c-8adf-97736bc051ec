package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrReportresultlog;


public interface ReportTaskGuarantorLogic {
	List<Map<String,Object>> getBranchListCombo(String branchId,AuditContext callerId);
	List<Map<String,Object>> getTaskGuarantorList(String[][] params,AuditContext callerId);
	List<Map<String,Object>> getUserList(String userId,AuditContext callerId);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
	public byte[] exportExcel(String[][] params,AuditContext callerId);
	String saveExportScheduler(String[][] params,AuditContext callerId);
}
