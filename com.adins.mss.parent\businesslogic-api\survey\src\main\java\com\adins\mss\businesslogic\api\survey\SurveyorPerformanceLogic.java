package com.adins.mss.businesslogic.api.survey;

import java.util.List;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrReportresultlog;

@SuppressWarnings("rawtypes")
public interface SurveyorPerformanceLogic {	
	List getComboBranch(String branchId, AuditContext callerId);
	List getSummary(String branchId, String userId, String startDate, String endDate, 
			AuditContext callerId, String uuidForm);
	List getDetail(String branchId, String userId, String startDate, String endDate, 
			AuditContext callerId, String uuidForm, long userBranch, int start, int end);
	int getDetailCount(String branchId, String userId, String startDate, String endDate, 
			AuditContext callerId, String uuidForm, long userBranch);
	byte[] exportExcel(String branchId, String userId, String startDate, String endDate, 
			String type, AuditContext callerId, String uuidForm);
	String saveExportScheduler(String branchId, String userId, String startDate, String endDate, 
			String type, AuditContext callerId, String uuidForm);
	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
	List getFormListCombo(AuditContext callerId);
}
