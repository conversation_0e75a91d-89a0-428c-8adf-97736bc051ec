package com.adins.mss.businesslogic.api.survey;

import java.util.Date;
import java.util.List;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.KeyValue;

public interface SvyPerformanceLogic {
	@PreAuthorize("hasRole('ROLE_MS') and @mssSecurity.isValidCallerId(#callerId.callerId, authentication)")
	public List<KeyValue> resultServer(Date date1, Date date2, Integer month, AuditContext callerId);
}
