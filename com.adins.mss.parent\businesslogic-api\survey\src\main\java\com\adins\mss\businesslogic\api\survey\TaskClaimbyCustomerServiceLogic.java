package com.adins.mss.businesslogic.api.survey;

import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;

public interface TaskClaimbyCustomerServiceLogic {
	
	Map<String, Object> listTaskH(Object params, AmMsuser amMsuser, Object paramsCnt, AuditContext callerId);
	String doClaimTaskpiloting(String uuidTaskH, String loginId, AuditContext callerId);
	
}
