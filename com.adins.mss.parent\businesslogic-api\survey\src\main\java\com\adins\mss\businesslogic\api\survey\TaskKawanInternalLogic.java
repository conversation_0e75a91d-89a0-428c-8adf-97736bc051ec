package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrReportresultlog;

public interface TaskKawanInternalLogic {

	List getComboBranch(String branchId, AuditContext callerId);

	List getReport(String branchId, String userId, String startDate, String endDate, AuditContext callerId);

	int getReportCount(String branchId, String userId, String startDate, String endDate, AuditContext callerId);

	String saveExportScheduler(String branchId, String userId, String startDate, String endDate,
			AuditContext callerId);

	byte[] exportExcel(String branchId, String userId, String startDate, String endDate, AuditContext callerId);

	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);
	
	List<Map<String, Object>> getReport_Masked(String branchId, String userId, String startDate, String endDate, AuditContext callerId);
	List<Map<String, Object>> getReport_MaskedJson(String branchId, String userId, String startDate, String endDate, AuditContext callerId);
	
	List getTemplateKawanInternal(AuditContext auditContext);

	List listInquiryTaskKawanInternalNativeString(Object[][] params, AuditContext callerId);

	Integer countInquiryTaskKawanInternalNativeString(Object[][] params, AuditContext callerId);
	
	String getValueDescription(String value, String lovGroup, AuditContext callerId);

}
