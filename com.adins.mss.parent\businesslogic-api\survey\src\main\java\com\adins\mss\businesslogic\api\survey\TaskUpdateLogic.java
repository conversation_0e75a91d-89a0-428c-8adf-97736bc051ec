package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.TrReportresultlog;

public interface TaskUpdateLogic {

	List getComboBranch(String branchId, AuditContext callerId);

	List getReport(String branchId, String userId, String startDate, String endDate, AuditContext callerId);

	String saveExportScheduler(String branchId, String userId, String startDate, String endDate,
			AuditContext callerId);

	byte[] exportExcel(String branchId, String userId, String startDate, String endDate, AuditContext callerId);

	void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId);

	List getDetailTaskUpdate(AuditContext auditContext, String taskUpdateId);

	Map<String, Object> listInquiryTaskUpdateNativeString(Object[][] params, AuditContext callerId);

	Integer countInquiryTaskUpdateNativeString(Object[][] params, AuditContext callerId);

}
