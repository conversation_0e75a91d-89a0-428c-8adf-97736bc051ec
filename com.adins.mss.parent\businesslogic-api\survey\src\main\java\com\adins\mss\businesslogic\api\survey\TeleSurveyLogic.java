package com.adins.mss.businesslogic.api.survey;

import java.io.File;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrTaskH;

@SuppressWarnings("rawtypes")
public interface TeleSurveyLogic {
	TrTaskH getTaskH(long uuid, AuditContext callerId);

	List<?> getComboForm(long subsystem, AuditContext callerId);

	List getComboPriority(AuditContext callerId);

	Map<String, Object> listTeleSurvey(String customerName, String startDate,
			String endDate, String form, String priority, String order,
			String typeOrder, int pageNo, int pageSize, AuditContext callerId)
			throws ParseException;
	
	public Map<String, Object> listAnswerByBranch(AmMsuser userLoggedIn, long uuidTrTaskH, AuditContext callerId);
	
	List<Map<String, Object>> mapOptAnsByLovByBranch(AmMsuser userLoggedIn, long uuidTaskH, long uuidForm,
			long uuidQuestion, List<Map<String, String>> refIdsWithAnswer, AuditContext auditContext);

	@PreAuthorize("hasRole('ROLE_SAVE_TELESURVEY')")
	void updateTaskHStatus(String[] uuidAnswers, String[] textAnswers,
			String[] cons, String[] latAns, String[] longAns, long uuidTaskH,
			String[] uuidQuestions, AmMsuser loginBean, AuditContext callerId);

	byte[] exportExcel(AmMsuser userLoggedIn, long uuidTaskH, AuditContext callerId);
	
	@PreAuthorize("hasRole('ROLE_UPLOAD_TELESURVEY')")
	byte[] processSpreadSheetFile(File uploadedFile, AmMsuser loginBean, AuditContext callerId, long uuidTaskH);

	public List<Map<String, Object>> autoCalculate(List<Map<String, String>> list,
			long uuidQuestion, long uuidFormHistory, AuditContext callerId);

	public List<Map<String, Object>> copyValue(List<Map<String, String>> list,
			long uuidForm, long uuidQuestion, long uuidUser, int seqQuest, long uuidTaskH, AuditContext callerId);

	public Map<String, Object> luOnline(AmMsuser userLoggedIn, String refId, String lovGroup,
			String searchVal, long uuidForm, String choice,  AuditContext callerId);

	public List<Map<String, Object>> validation(List<Map<String, String>> list,
			long uuidForm, long uuidQuestion, long uuidTaskH, AuditContext callerId);
	
	public List<Map<String, Object>> relevant(List<Map<String, String>> list, long uuidForm, long uuidTaskH,  AuditContext callerId);
}
