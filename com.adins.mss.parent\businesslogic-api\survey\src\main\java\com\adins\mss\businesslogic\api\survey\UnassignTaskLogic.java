package com.adins.mss.businesslogic.api.survey;

import java.util.List;
import java.util.Map;

import org.springframework.security.access.prepost.PreAuthorize;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings("rawtypes")
public interface UnassignTaskLogic {
	Map<String, Object> listTaskH(String type, AmMsuser amMsuser, Object params, Object paramsCnt, AuditContext callerId);
	Map<String, Object> listTaskToAssign(String[] selectedTask, AuditContext callerId);
	Map<String, Object> listUser(String mode, AmMsuser amMsuser, AuditContext callerId);
	Map<String, Object> listUserSurveyor(String mode, String formName, AmMsuser amMsuser, String isPilotingCae, String taskType, AuditContext callerId);
	AmMsuser getUser(long uuidMsUser, AuditContext callerId);
	@PreAuthorize("hasRole('ROLE_ASSIGNTASK')")
	String assignTask(String[] selectedTask, long uuidMsUser, AmMsuser loginBean, long uuidPriority, AuditContext callerId);
	List getPriorityList(Object params,Object order,AuditContext callerId);
	Integer[][] getAssignment(List<AmMsuser> listResult, AuditContext callerId);
	String[][] getLastLoc(List<AmMsuser> listResult, AuditContext callerId);
	List getSpvList(String mode, Object params, Object order, AuditContext callerId);
	String assignTaskpiloting(String[] selectedTask, long uuidMsUser, AmMsuser loginBean, long uuidPriority,
			AuditContext callerId);
}
