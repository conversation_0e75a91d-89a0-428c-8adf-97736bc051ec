<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.adins.mss</groupId>
    <artifactId>com.adins.mss.businesslogic-impl</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <artifactId>com.adins.mss.businesslogic-impl.am</artifactId>
  <dependencies>
  	<dependency>
  		<groupId>com.adins.mss</groupId>
  		<artifactId>com.adins.mss.businesslogic-api.am</artifactId>
  		<version>1.0.0-SNAPSHOT</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.tool.password</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.tool.ldap</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
	<dependency>
		<groupId>com.adins.mss</groupId>
		<artifactId>com.adins.mss.businesslogic-api.common</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</dependency>
	<dependency>
		<groupId>net.sourceforge.jtds</groupId>
		<artifactId>jtds</artifactId>
		<version>${jtds.version}</version>
		<scope>test</scope>
	</dependency>
  	<dependency>
  		<groupId>com.adins.framework</groupId>
  		<artifactId>com.adins.framework.persistence.dao-hibernate</artifactId>
  		<version>${adins-framework.version}</version>
  	</dependency>
	<dependency>
		<groupId>com.adins.foundation</groupId>
		<artifactId>com.adins.foundation.workflow.bussinesslogic-impl.engine</artifactId>
		<version>${adins-foundation.version}</version>
  		<scope>test</scope>
	</dependency>
	<dependency>
		<groupId>org.springframework</groupId>
		<artifactId>spring-aspects</artifactId>
		<version>${spring-framework.version}</version>
  		<scope>test</scope>
	</dependency>
  </dependencies>
</project>