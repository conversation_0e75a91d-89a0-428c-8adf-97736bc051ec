package com.adins.mss.businesslogic.impl.am;

import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.AboutLogic;
import com.adins.mss.model.AmGeneralsetting;

@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericAboutLogic extends BaseLogic implements AboutLogic {

	@Override
	public AmGeneralsetting getGeneralSetting(String gsCode, AuditContext callerId) {
		Object[][] params = {{ Restrictions.eq("gsCode", gsCode) } };
		AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		return result;
	}
	
}
