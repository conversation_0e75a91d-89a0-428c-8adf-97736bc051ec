package com.adins.mss.businesslogic.impl.am;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.ChangePasswordLogic;
import com.adins.mss.businesslogic.api.am.EventLogLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.am.LoginLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ChangePasswordType;
import com.adins.mss.exceptions.ChangePasswordException;
import com.adins.mss.exceptions.ChangePasswordException.Reason;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.AmUserpwdhistory;
import com.adins.framework.tool.password.PasswordHash;

@Transactional(readOnly = true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericChangePasswordLogic extends BaseLogic 
	implements ChangePasswordLogic, MessageSourceAware {
	
	@Autowired
	private EventLogLogic eventLogLogic;
	
	@Autowired
	private GlobalLogic globalLogic;
	
	@Autowired
	private LoginLogic loginLogic;
	
	private IntFormLogic intFormLogic;
	
	@Autowired
	private MessageSource messageSource;
	
	public void setEventLogLogic(EventLogLogic eventLogLogic) {
		this.eventLogLogic = eventLogLogic;
	}
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}
	
	public void setLoginLogic(LoginLogic loginLogic) {
		this.loginLogic = loginLogic;
	}	
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	
	
	@Transactional(noRollbackFor={ChangePasswordException.class}, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public AmMsuser changePassword(long uuidMsUser, String oldPassword,
		String newPassword, AuditContext callerId) {
		
		String totalGetPassword = globalLogic.getGsValue(GlobalKey.GENERALSETTING_LAST_PASSWORD, 
				callerId);
		AmMsuser user = selectUser(uuidMsUser);
		
		if (GlobalVal.FLAG_LOGIN_PROVIDER_NC.equals(user.getLoginProvider()) && validatePassword(newPassword)) {
			intFormLogic.changePassword(user.getUniqueId(), oldPassword, newPassword, callerId);
		} else {
			if (validatePassword(oldPassword, newPassword, user, totalGetPassword, callerId) && validatePassword(newPassword)) {				 
				updateUser(user,  newPassword, callerId);	
			}
			else {
				eventLogLogic.logUserEvent(user, GlobalVal.USEREVENT_ACTIVITY_PASSWORD_CHANGE, 
					GlobalVal.USEREVENT_CONSEQUENCE_PASSWORD_CHANGE_FAIL_NEW_PASSWORD_VIOLATION, 
					callerId);						
				throw new ChangePasswordException(
					this.messageSource.getMessage("businesslogic.changepassword.history", 
					new Object[]{totalGetPassword}, this.retrieveLocaleAudit(callerId)),
					Reason.CHANGE_PASSWORD_VIOLATION);
			}
		}
		return user;
	}
	
	public AmMsuser selectUser(long uuidMsUser) {
		AmMsuser result = (AmMsuser)this.getManagerDAO().selectOne(AmMsuser.class, uuidMsUser);
		return result;
	}

	private void updateUser(AmMsuser obj, String newPassword, AuditContext callerId) {	
		
		newPassword = PasswordHash.createHash(newPassword);			
		
		Object[][] paramsUser = { {Restrictions.eq("uniqueId", obj.getUniqueId())} };
		Map mapUserLogin = this.getManagerDAO().selectAll(AmMsuser.class, paramsUser, null);
		List<AmMsuser> listUserLogin = (List) mapUserLogin.get(GlobalKey.MAP_RESULT_LIST);
		
		for (AmMsuser usr : listUserLogin) {
//			if ("1".equals(obj.getChangePwdLogin())) {
				obj.setChangePwdLogin("0");
//			}
			obj.setIsPasswordExpired("0");
			obj.setUsrUpd(callerId.getCallerId());
			obj.setDtmUpd(new Date());
			obj.setPassword(newPassword);
			this.getManagerDAO().update(obj);
			insertPwdHistory(newPassword, obj, ChangePasswordType.CHANGE_PASSWORD.getPassType(), 
					callerId);	
		}
	}

	private List< Map<String, Object> > selectPwdHistory(long uuidMsUser, String totalGetPassword) {
		
		Object[][] params = {{"uuidMsUser", uuidMsUser}, {"totalGetPassword", totalGetPassword}};
		List< Map<String,Object> > result = this.getManagerDAO().selectAllNative(
			"am.changepassword.getPasswordChanges", params, null);
		return result;
	}

	private void insertPwdHistory(String newPassword, AmMsuser amMsuser, String changeType, 
			AuditContext callerId) {
		
		AmUserpwdhistory obj = new AmUserpwdhistory();
		obj.setDtmCrt(new Date());
		obj.setUsrCrt(callerId.getCallerId());
		obj.setAmMsuser(amMsuser);
		obj.setPassword(newPassword);
		obj.setChangeType(changeType);
		this.getManagerDAO().insert(obj);			
	}

	private boolean validatePassword(String oldPassword, String newPassword, AmMsuser user, 
			String gsLastPassword, AuditContext callerId) {
		
		List < Map<String,Object> > passwordHistory = null;
		boolean valid = true;
		if (newPassword.length() < 8 || newPassword.length() > 12) {
			throw new ChangePasswordException(
					this.messageSource.getMessage("businesslogic.changepassword.length.err", 
							null, this.retrieveLocaleAudit(callerId)),
					Reason.CHANGE_PASSWORD_VIOLATION);
		}
		if (PasswordHash.validatePassword(oldPassword, user.getPassword())) {
			passwordHistory = selectPwdHistory(user.getUuidMsUser(), gsLastPassword);
			if (passwordHistory == null) {
				passwordHistory = Collections.emptyList(); 
			}
			
			for (int i = 0; i < passwordHistory.size(); i++) {
				Map <String, Object> temp = (Map<String, Object>) passwordHistory.get(i);	
				if (PasswordHash.validatePassword(newPassword, (String) temp.get("d0"))) {
					valid = false;
					break;
				}		
			}
		}
		else{
			eventLogLogic.logUserEvent(user, GlobalVal.USEREVENT_ACTIVITY_PASSWORD_CHANGE, 
				GlobalVal.USEREVENT_CONSEQUENCE_PASSWORD_CHANGE_FAIL__WRONG_OLD_PASSWORD, callerId);			
			throw new ChangePasswordException(
					this.messageSource.getMessage("businesslogic.changepassword.incorrectold", 
							null, this.retrieveLocaleAudit(callerId)), Reason.INVALID_OLD_PASSWORD);
		}					
		return valid;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void doCancel(long uuid, AuditContext callerId) {
		loginLogic.doLogout(uuid, callerId);
	}

	@Override
	public boolean validatePassword(String password) {
		int flag = 0;
		Pattern p = Pattern.compile("[a-z]");
		Pattern q = Pattern.compile("[0-9]");
		Pattern r = Pattern.compile("[A-Z]");
		Pattern s = Pattern.compile("[[^A-Za-z0-9]]");
		Matcher a = p.matcher(password);
		Matcher b = q.matcher(password);
		Matcher c = r.matcher(password);
		Matcher d = s.matcher(password);
		if(a.find()){
			flag++;
		}
		if(b.find()){
			flag++;
		}
		if(c.find()){
			flag++;
		}
		if(d.find()){
			flag++;
		}
		
		if(flag >= 2){
			return true;
		}else{
			throw new ChangePasswordException(
					this.messageSource.getMessage("changepassword.error.newpassword",null,null),
					Reason.CHANGE_PASSWORD_VIOLATION);
		}
	}
}
