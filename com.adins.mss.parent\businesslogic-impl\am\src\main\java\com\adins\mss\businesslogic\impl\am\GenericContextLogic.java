package com.adins.mss.businesslogic.impl.am;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.ContextLogic;
import com.adins.mss.model.AmMsuser;

@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericContextLogic extends BaseLogic implements ContextLogic, MessageSourceAware {

	@Autowired
	private MessageSource messageSource;
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void logoutAll(AuditContext callerId) {	
		String[][] params = {{"usrUpd", callerId.getCallerId()}};
		this.getManagerDAO().updateNative("am.login.releaseAllUserLoggedIn", params);
	}
	
    @Override
    public List<String> retrieveMenuRightsByLoginId(String loginId, AuditContext callerId) {
        if (StringUtils.isBlank(loginId)){
            return Collections.<String> emptyList();
        }
        
        String[][] selectOneParams = {{"loginId", loginId}};
        BigInteger resultSelectOne = (BigInteger) this.getManagerDAO().selectOneNative(
                "am.context.getUuidUser", selectOneParams);
        
        if (resultSelectOne != null) {
        	long uuid = resultSelectOne.longValue();
            return this.doRetrieveMenuRights(uuid);
        }
        return Collections.<String> emptyList();
    }

    @Override
    public List<String> retrieveMenuRightsByUuid(long uuidUser, AuditContext callerId) {
        if (uuidUser == 0L){
            return Collections.<String> emptyList();
        }
        return this.doRetrieveMenuRights(uuidUser);
    }

    private List<String> doRetrieveMenuRights(long uuidUser) {
        Object[][] selectParams = {{"uuidUser", uuidUser}};        
        List<Map<String, Object>> queryResult = this.getManagerDAO().selectAllNative(
                "am.context.getMenuOfUser", selectParams, null);
        
        if (queryResult == null || queryResult.isEmpty()){
            return Collections.<String> emptyList();
        }
        
        List<String> resultList = new ArrayList<>();
        
        final String field0 = "d0";
        for (Iterator<Map<String,Object>> iterator = queryResult.iterator(); iterator.hasNext();) {
            Map<String,Object> map =  iterator.next();
            resultList.add((String) map.get(field0));
        }
        
        return resultList;
    }

    @Override
    public boolean isUserAccountActive(long uuidUser, AuditContext callerId) {
        AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, uuidUser);
        if (user == null){
            return false;
        }
        if (!"0".equals(user.getIsDeleted())){
            return false;
        }
        if (!"1".equals(user.getIsActive())){
            return false;
        }
        if (user.getIsLocked() != null &&
                !"0".equals(user.getIsLocked())){
            return false;
        }
        return true;
    }
    
    @Override
    public String isUserAccountCheckActive(long uuidUser, AuditContext callerId) {
        AmMsuser user = this.getManagerDAO().selectOne(
        	"from AmMsuser u join fetch u.msJob join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
        	new Object[][] {{"uuidMsUser", uuidUser}});
        if (user == null){
            return this.messageSource.getMessage("businesslogic.context.useralreadydeleted",
            		null, this.retrieveLocaleAudit(callerId));
        }
        else {
        	if ( "0".equals(user.getAmMssubsystem().getIsActive())){
        		return this.messageSource.getMessage("businesslogic.context.subsystemalreadydeleted",
                		null, this.retrieveLocaleAudit(callerId));
        	}
        	if ( !"0".equals(user.getIsDeleted()) ||
        		 !"1".equals(user.getIsActive()) ||
        		 ( user.getIsLocked() != null &&
                    !"0".equals( user.getIsLocked() ) ) ){
                return this.messageSource.getMessage("businesslogic.context.useralreadydeleted",
                		null, this.retrieveLocaleAudit(callerId));
        	}
        	if ( "0".equals(user.getMsJob().getIsActive())){
        		return this.messageSource.getMessage("businesslogic.context.jobalreadydeleted",
                		null, this.retrieveLocaleAudit(callerId));
        	}
        }
        return "1";
    }
    
    @Override
	public String actionUserCheck(long uuid, String nameSpace, AuditContext callerID) {
		
		if(!this.checkUserMenu(uuid, nameSpace, callerID)){
			return this.messageSource.getMessage("businesslogic.context.unauthorizedaccess",
            		null, this.retrieveLocaleAudit(callerID));
		}
		return "1";
	}
	
	private boolean checkUserMenu(long uuid, String nameSpace, AuditContext callerID){
		List<String> menu = this.doRetrieveMenuRights(uuid);
		boolean result = false;
		for(int i=0;i<menu.size();i++){
			if(menu.get(i)!=null && menu.get(i).contains(nameSpace)){
				result = true;
				continue;
			}
		}
		return result;
	}
}
