package com.adins.mss.businesslogic.impl.am;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.EventLogLogic;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.AmUsereventlog;
@SuppressWarnings("rawtypes")
@Transactional(readOnly = true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericEventLogLogic extends BaseLogic implements EventLogLogic {
	

	private List selectGroupByMember(long uuidMsuser) {
		Object[][] params = {{"uuidMsuser", uuidMsuser}};
		List listGbM = (List) this.getManagerDAO().selectAllNative("am.login.selectGroupByMember", 
				params, null);
		return listGbM;
	}

	@SuppressWarnings("static-access")
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void logUserEvent(String applicationName, String applicationVersion,
			String subsystemName, String subsystemVersion, AmMsuser amMsuser,
			String activity, String consequence, AuditContext auditContext) {
		
		Map<String, Object> parameters = auditContext.getParameters();
		this.insertUserEventLog(applicationName, applicationVersion, subsystemName, subsystemVersion,
				amMsuser, new Date(), activity, consequence, (String) parameters.get(auditContext.KEY_PARAMS_TERMINALADDRESS),
				(String) parameters.get(auditContext.KEY_PARAMS_TERMINALID), (String) parameters.get(auditContext.KEY_PARAMS_TERMINALUSERID));
		
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void logUserEvent(AmMsuser amMsuser, String activity,
			String consequence, AuditContext auditContext) {
		
		List list = java.util.Collections.EMPTY_LIST;
		if (amMsuser != null) {
			list = (List) this.selectGroupByMember(amMsuser.getUuidMsUser());
		}
		if (list != null && !list.isEmpty()) {
			Map map = (Map) list.get(0);
			this.logUserEvent(String.valueOf(map.get("d0")), String.valueOf(map.get("d1")), String.valueOf(map.get("d2")), String.valueOf(map.get("d3")), 
					amMsuser, activity, consequence, auditContext);
		} 
		else {
			this.logUserEvent(null, null, null, null, amMsuser, activity, consequence, auditContext);
		}
		
	}
	
	private void insertUserEventLog(String applicationName,
			String applicationVersion, String subsystemName,
			String subsystemVersion, AmMsuser amMsuser, Date activityDate,
			String activity, String consequence, String terminalAddress,
			String terminalId, String terminalUserId) {
		
		AmUsereventlog bean = new AmUsereventlog();
		bean.setApplicationName(applicationName);
		bean.setApplicationVersion(applicationVersion);
		bean.setSubsystemName(subsystemName);
		bean.setSubsystemVersion(subsystemVersion);
		bean.setAmMsuser(amMsuser);
		bean.setActivityDate(activityDate);
		bean.setActivity(activity);
		bean.setConsequence(consequence);
		bean.setTerminalAddress(terminalAddress);
		bean.setTerminalId(terminalId);
		bean.setTerminalUserId(terminalUserId);

		this.getManagerDAO().insert(bean);
	}
}
