package com.adins.mss.businesslogic.impl.am;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GeneralSettingLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericGeneralSettingLogic extends BaseLogic implements GeneralSettingLogic {
	private AuditInfo auditInfo;
	
	public GenericGeneralSettingLogic() {
		String[] pkCols = {"uuidGeneralSetting"};
		String[] pkDbCols = {"UUID_GENERAL_SETTING"};
		String[] cols = {"uuidGeneralSetting", "isActive", "gsCode", "gsPrompt", "gsType", "gsValue"};
		String[] dbCols = {"UUID_GENERAL_SETTING", "IS_ACTIVE", "GS_CODE", "GS_PROMPT", "GS_TYPE","GS_VALUE"};
		this.auditInfo = new AuditInfo("AM_GENERALSETTING", pkCols, pkDbCols, cols, dbCols);
	}

	@Override
	public Map<String, Object> listGeneralSetting(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;

		AmMsuser user = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
			new Object[][] {{"uuidMsUser", Long.parseLong(callerId.getCallerId())}});
		
		Object[][] tmpParam = (Object[][])params;
		Object[][] parameters = new Object[6][2];
		parameters[0] = tmpParam[0];
		parameters[1] = tmpParam[1];
		if (GlobalVal.SUBSYSTEM_MO.equals(user.getAmMssubsystem().getSubsystemName())) {
			parameters[2] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MS+"%"))};
			parameters[3] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MC+"%"))};
			parameters[4] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MT+"%"))};
		} else if (GlobalVal.SUBSYSTEM_MS.equals(user.getAmMssubsystem().getSubsystemName())) {
			parameters[2] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MO+"%"))};
			parameters[3] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MC+"%"))};
			parameters[4] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MT+"%"))};
		} else if (GlobalVal.SUBSYSTEM_MC.equals(user.getAmMssubsystem().getSubsystemName())) {
			parameters[2] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MS+"%"))};
			parameters[3] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MO+"%"))};
			parameters[4] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MT+"%"))};
		} else if (GlobalVal.SUBSYSTEM_MT.equals(user.getAmMssubsystem().getSubsystemName())) {
			parameters[2] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MS+"%"))};
			parameters[3] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MO+"%"))};
			parameters[4] = new Object[]{Restrictions.not(Restrictions.like("gsCode", GlobalVal.SUBSYSTEM_MC+"%"))};
		}
		
		parameters[5] = new Object[]{Restrictions.not(Restrictions.eq("gsCode", "AM_DEFAULT_PASSWORD"))};
					
		result = this.getManagerDAO().selectAll(AmGeneralsetting.class, parameters, orders, pageNumber, pageSize);
		return result;
	}

	@Override
	public AmGeneralsetting getGeneralSetting(long uuid, AuditContext callerId) {
		AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, uuid);
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String updateGeneralSetting(AmGeneralsetting obj, AuditContext callerId, long uuid) {
		String result = null;	
		
		AmMsuser user = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.parseLong(callerId.getCallerId())}});
		
		AmGeneralsetting dbModel = this.getManagerDAO().selectOne(AmGeneralsetting.class, uuid);
		dbModel.setIsActive(obj.getIsActive());
		dbModel.setGsCode(obj.getGsCode());
		dbModel.setGsPrompt(obj.getGsPrompt());
		dbModel.setGsType(obj.getGsType());
		dbModel.setGsValue(obj.getGsValue());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());
		
		this.auditManager.auditEdit(dbModel, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
			
		String subsystem = user.getAmMssubsystem().getSubsystemName();
		
		if ( (subsystem+GlobalKey.GENERALSETTING_AUTOSEND).equals(dbModel.getGsCode()) 
				&& "0".equals(dbModel.getGsValue()) ) {
			Object[][] params = { { Restrictions.eq("gsCode", subsystem+GlobalKey.GENERALSETTING_PARTIAL_IMAGE) } };
			AmGeneralsetting updPart = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
			updPart.setGsValue("0");
			this.auditManager.auditEdit(updPart, auditInfo, callerId.getCallerId(), "");
			this.getManagerDAO().update(updPart);
		}	
		else if ( (subsystem+GlobalKey.GENERALSETTING_PARTIAL_IMAGE).equals(dbModel.getGsCode()) 
				&& "1".equals(dbModel.getGsValue()) ) {
			Object[][] params = { { Restrictions.eq("gsCode", subsystem+GlobalKey.GENERALSETTING_AUTOSEND) } };
			AmGeneralsetting updAsin = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
			if ( "0".equals(updAsin.getGsValue()) ) {
				updAsin.setGsValue("300"); //default to 5 Minutes
				this.auditManager.auditEdit(updAsin, auditInfo, callerId.getCallerId(), "");
				this.getManagerDAO().update(updAsin);
			}
		}
		//penambahan apabila edit MSS build version number (PRM07_VERS), maka semua user mobile akan force logout
		else if ((subsystem+GlobalKey.GENERALSETTING_LAST_MOBILE_VERSION).equals(dbModel.getGsCode())) {
			Object[][] params = { { Restrictions.eq("amMssubsystem.uuidMsSubsystem", user.getAmMssubsystem().getUuidMsSubsystem()) },
								{ "msJob.isFieldPerson", "1" }};
			Map<String, Object> updLoginUser = this.getManagerDAO().list(AmMsuser.class, params, null);
			List<AmMsuser> listUpdLoginUser = (List) updLoginUser.get(GlobalKey.MAP_RESULT_LIST);
			if(listUpdLoginUser != null){
				for(AmMsuser bean : listUpdLoginUser){
					bean.setIsLoggedIn("0");
					this.getManagerDAO().update(bean);
				}
			}
		}
		// end force logout ketika user edit MSS build version number (PRM07_VERS)

		result = String.valueOf(obj.getUuidGeneralSetting());
		
		return result;
	}
}
