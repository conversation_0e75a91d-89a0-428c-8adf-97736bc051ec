package com.adins.mss.businesslogic.impl.am;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsJob;
@SuppressWarnings("rawtypes")
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericGlobalLogic extends BaseLogic implements GlobalLogic {

	@Override
	public String getGsValue(String gsCode, AuditContext callerId) {
		String gsVal=org.apache.commons.lang3.StringUtils.EMPTY;
		List result = java.util.Collections.EMPTY_LIST;

    	String[][] params = {{"gsCode",gsCode}};
    	result =  this.getManagerDAO().selectAllNative("common.getGsValue", params, null);
    	
    	for (int i=0;i<result.size();i++) {
    		Map temp = (Map) result.get(i);
    		gsVal = temp.get("d0").toString();
    	}
    	
    	return gsVal;
	}

	@Override
	public AmMsuser getAmMsuser(String loginId, AuditContext callerId) {
		if (StringUtils.isBlank(loginId)){
			return null;
		}
		
		Object[][] queryParams = { { Restrictions.eq("loginId", loginId)} };
		AmMsuser result = this.getManagerDAO().selectOne(AmMsuser.class, queryParams);
		return result;
	}
	
	@Override
	public AmMsuser getAmMsuserByUuid(String uuidMsUser, AuditContext callerId) {
		if (StringUtils.isBlank(uuidMsUser)){
			return null;
		}
		
		AmMsuser result = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(uuidMsUser)}});
		return result;
	}

	@Override
	public MsBranch getMsBranch(String branchCode, AuditContext callerId) {
		if (StringUtils.isBlank(branchCode)){
			return null;
		}
		Object[][] queryParams = { {Restrictions.eq("branchCode", branchCode)} };
		MsBranch result = this.getManagerDAO().selectOne(MsBranch.class, queryParams);
		return result;
	}

	@Override
	public MsJob getMsJob(String jobCode, AuditContext callerId) {
		if (StringUtils.isBlank(jobCode)){
			return null;
		}
		MsJob result = this.getManagerDAO().selectOne(
				"from MsJob mj join fetch mj.amMssubsystem where mj.jobCode = :jobCode", 
				new Object[][] {{"jobCode", jobCode}});
		return result;
	}
}
