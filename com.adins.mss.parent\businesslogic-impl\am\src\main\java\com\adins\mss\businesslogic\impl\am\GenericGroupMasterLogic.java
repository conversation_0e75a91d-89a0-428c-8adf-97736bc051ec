package com.adins.mss.businesslogic.impl.am;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GroupMasterLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMemberofgroup;
import com.adins.mss.model.AmMenufeatureofgroup;
import com.adins.mss.model.AmMenuofgroup;
import com.adins.mss.model.AmMobilemenuofgroup;
import com.adins.mss.model.AmMsgroup;
import com.adins.mss.model.AmMsmenu;
import com.adins.mss.model.AmMsmenufeature;
import com.adins.mss.model.AmMsmobilemenu;
import com.adins.mss.model.AmMstodolist;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.AmTodolistofgroup;
import com.adins.mss.util.MssTool;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericGroupMasterLogic extends BaseLogic implements
		GroupMasterLogic, MessageSourceAware {

	private AuditInfo auditInfoG;
	private AuditInfo auditInfoMMoG;
	private AuditInfo auditInfoTDLoG;
	private AuditInfo auditInfoMoG;
	private AuditInfo auditInfoMBoG;
	
	@Autowired
	private MessageSource messageSource;
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public GenericGroupMasterLogic() {
		String[] pkColsG = { "uuidMsGroup" };
		String[] pkDbColsG = { "UUID_MS_GROUP" };
		String[] colsG = { "uuidMsGroup", "isActive", "groupname", "groupDescription",
				"amMssubsystem.uuidMsSubsystem" };
		String[] dbColsG = { "UUID_MS_GROUP", "IS_ACTIVE", "GROUPNAME", "GROUP_DESCRIPTION",
				"UUID_MS_SUBSYSTEM" };
		this.auditInfoG = new AuditInfo("AM_MSGROUP", pkColsG, pkDbColsG,
				colsG, dbColsG);

		String[] pkColsMMoG = { "uuidMobileMenuOfGroup" };
		String[] pkDbColsMMoG = { "UUID_MOBILE_MENU_OF_GROUP" };
		String[] colsMMoG = { "uuidMobileMenuOfGroup", "amMsgroup.uuidMsGroup", "amMsmobilemenu.uuidMsMobileMenu" };
		String[] dbColsMMoG = { "UUID_MOBILE_MENU_OF_GROUP", "UUID_MS_GROUP", "UUID_MS_MOBILE_MENU" };
		this.auditInfoMMoG = new AuditInfo("AM_MOBILEMENUOFGROUP", pkColsMMoG,
				pkDbColsMMoG, colsMMoG, dbColsMMoG);

		String[] pkColsTDLoG = { "uuidToDoListOfGroup" };
		String[] pkDbColsTDLoG = { "UUID_TO_DO_LIST_OF_GROUP" };
		String[] colsTDLoG = { "uuidToDoListOfGroup", "amMsgroup.uuidMsGroup", "amMstodolist.uuidMsToDoList" };
		String[] dbColsTDLoG = { "UUID_TO_DO_LIST_OF_GROUP", "UUID_MS_GROUP", "UUID_MS_GROUP" };
		this.auditInfoTDLoG = new AuditInfo("AM_TODOLISTOFGROUP", pkColsTDLoG,
				pkDbColsTDLoG, colsTDLoG, dbColsTDLoG);
		
		String[] pkColsMoG = { "uuidMenuOfGroup" };
		String[] pkDbColsMoG = { "UUID_MENU_OF_GROUP" };
		String[] colsMoG = { "uuidMenuOfGroup", "amMsgroup.uuidMsGroup", "amMsmenu.uuidMsMenu" };
		String[] dbColsMoG = { "UUID_MENU_OF_GROUP", "UUID_MS_GROUP", "UUID_MS_MENU" };
		this.auditInfoMoG = new AuditInfo("AM_MENUOFGROUP", pkColsMoG,
				pkDbColsMoG, colsMoG, dbColsMoG);
		
		String[] pkColsMBoG = { "uuidMemberOfGroup" };
		String[] pkDbColsMBoG = { "UUID_MEMBER_OF_GROUP" };
		String[] colsMBoG = { "uuidMemberOfGroup", "amMsgroup.uuidMsGroup", "amMsuser.uuidMsUser" };
		String[] dbColsMBoG = { "UUID_MEMBER_OF_GROUP", "UUID_MS_GROUP", "UUID_MS_USER" };
		this.auditInfoMBoG = new AuditInfo("AM_MEMBEROFGROUP", pkColsMBoG,
				pkDbColsMBoG, colsMBoG, dbColsMBoG);
	}

	@Override
	public Map<String, Object> listGroup(String groupName, String groupDescription, 
			long uuidMsSubsystem, String isActive, int pageNumber, int pageSize, 
			AuditContext callerId) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		
		condition.append(" and mg.amMssubsystem.uuidMsSubsystem=:uuidMsSubsystem");
		condition.append(" and mg.isDeleted=:isDeleted");
		paramMap.put("uuidMsSubsystem", uuidMsSubsystem);
		paramMap.put("isDeleted", "0");
		
		if (StringUtils.isNotBlank(groupDescription) && !"%".equals(groupDescription)) {
			condition.append(" and mg.groupDescription LIKE :groupDescription");
			paramMap.put("groupDescription", "%" + groupDescription + "%");
		}
		if (StringUtils.isNotBlank(groupName) && !"%".equals(groupName)) {
			condition.append(" and mg.groupname LIKE :groupname");
			paramMap.put("groupname", "%" + groupName + "%");
		}
		if (StringUtils.isNotBlank(isActive) && !"%".equals(isActive)) {
			condition.append(" and mg.isActive=:isActive");
			paramMap.put("isActive", isActive);
		}

		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mg.isActive desc, mg.groupname asc");
		
		Map<String, Object> result = this.getManagerDAO().selectAll(
				"from AmMsgroup mg join fetch mg.amMssubsystem ms "
				+ "join fetch ms.amMsapplication ma where 1=1"
						+ condition.toString() + orderQuery.toString(),
				"select count(*) from AmMsgroup mg join mg.amMssubsystem where 1=1"
						+ condition.toString(),
				paramMap, pageNumber, pageSize);
		return result;
	}

	@Override
	public AmMsgroup getGroup(long uuid, AuditContext callerId) {
		AmMsgroup result = this.getManagerDAO().selectOne(
			"from AmMsgroup mg join fetch mg.amMssubsystem ms "
			+ "join fetch ms.amMsapplication where mg.uuidMsGroup = :uuidMsGroup", 
			new Object[][] {{"uuidMsGroup", uuid}});		
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public long insertGroup(AmMsgroup amMsgroup, AuditContext callerId) {
		boolean exist = this.checkGroupName(amMsgroup.getAmMssubsystem().getUuidMsSubsystem(), 
				amMsgroup.getGroupname());
		
		if (exist) {
			throw new EntityNotUniqueException(
					this.messageSource.getMessage("service.global.existed", 
							new Object[]{"Group ID"}, this.retrieveLocaleAudit(callerId)), amMsgroup.getGroupname());
		}

		amMsgroup.setDtmCrt(new Date());
		amMsgroup.setUsrCrt(callerId.getCallerId());
		amMsgroup.setIsDeleted("0");

		this.getManagerDAO().insert(amMsgroup);
		this.auditManager.auditAdd(amMsgroup, auditInfoG, callerId.getCallerId(), "");
		
		return amMsgroup.getUuidMsGroup();
	}
	
	boolean checkGroupName(long uuidMsSubsystem, String groupName) {
		Object[][] params = { {Restrictions.eq("amMssubsystem.uuidMsSubsystem" , uuidMsSubsystem)},
				{Restrictions.eq("groupname" , groupName)} };
		
		Map<String, Object> mapGroup = this.getManagerDAO().count(AmMsgroup.class, params);
		Long count = (Long) mapGroup.get(GlobalKey.MAP_RESULT_SIZE);
		
		return (count > 0) ? true : false;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateGroup(AmMsgroup amMsgroup, AuditContext callerId) {
		AmMsgroup dbModel = this.getManagerDAO().selectOne(AmMsgroup.class, amMsgroup.getUuidMsGroup());
		
		boolean exist = this.checkGroupName(amMsgroup.getAmMssubsystem().getUuidMsSubsystem(), 
				amMsgroup.getGroupname());
		
		if (exist) {
			if (!dbModel.getGroupname().equals(amMsgroup.getGroupname())) {
				throw new EntityNotUniqueException(
					this.messageSource.getMessage("businesslogic.group.groupidexist", 
						null, this.retrieveLocaleAudit(callerId)), amMsgroup.getGroupname());
			}
		}
		
		dbModel.setGroupDescription(amMsgroup.getGroupDescription());
		dbModel.setAmMssubsystem(amMsgroup.getAmMssubsystem());
		dbModel.setIsActive(amMsgroup.getIsActive());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());

		this.auditManager.auditEdit(dbModel, auditInfoG, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void delete(long uuidMsGroup, AuditContext callerId) {
		Object[][] params = { { "uuidMsGroup", uuidMsGroup } };
		BigInteger deletableGroup = (BigInteger) this.getManagerDAO().selectOneNative(
				"am.groupmaster.checkHasChild", params);
		if (deletableGroup != null) {
			AmMsgroup obj = this.getManagerDAO().selectOne(AmMsgroup.class, uuidMsGroup);
			obj.setIsDeleted("1");
			obj.setIsActive("0");
			this.auditManager.auditEdit(obj, auditInfoG, callerId.getCallerId(), "");
			this.getManagerDAO().update(obj);
		} 
		else {
			throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION,
					this.messageSource.getMessage("businesslogic.global.datahasachild", 
					null, this.retrieveLocaleAudit(callerId)), new Exception());
		}
	}

	@Override
	public Map<String, Object> listMobileMenuOfGroup(Object params,
			Object orders, AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO().selectAll(AmMobilemenuofgroup.class,
					params, orders);
		return result;
	}

	@Override
	public Map<String, Object> listMenuOfGroup(Object params, Object orders,
			AuditContext callerId) {
		 Map<String, Object> result = this.getManagerDAO().selectAll(AmMenuofgroup.class,
				params, orders);
		return result;
	}

	@Override
	public Map<String, Object> listToDoList(Object params, Object orders,
			int pageNumber, int pageSize, AuditContext callerId) {
		 Map<String, Object> result = this.getManagerDAO().selectAll(AmMstodolist.class, params,
				orders, pageNumber, pageSize);
		return result;
	}

	@Override
	public Map<String, Object> listToDoListOfGroup(Object params,
			Object orders, AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO().selectAll(AmTodolistofgroup.class,
					params, orders);
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertMobileMenuofGroup(long uuidMsGroup, long[] selectedGroupMobile, AuditContext callerId) {
		for (int i = 0; i < selectedGroupMobile.length; i++) {
			AmMobilemenuofgroup amMobilemenuofgroup = new AmMobilemenuofgroup();
			AmMsmobilemenu amMsmobilemenu = new AmMsmobilemenu();
			AmMsgroup amMsgroup = new AmMsgroup();

			amMsgroup.setUuidMsGroup(uuidMsGroup);

			amMsmobilemenu.setUuidMsMobileMenu(selectedGroupMobile[i]);

			amMobilemenuofgroup.setDtmCrt(new Date());
			amMobilemenuofgroup.setUsrCrt(callerId.getCallerId());
			amMobilemenuofgroup.setAmMsgroup(amMsgroup);
			amMobilemenuofgroup.setAmMsmobilemenu(amMsmobilemenu);

			this.getManagerDAO().insert(amMobilemenuofgroup);
			this.auditManager.auditAdd(amMobilemenuofgroup, auditInfoMMoG, callerId.getCallerId(), "");			
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteMobileMenuofGroup(long uuidMsGroup, AuditContext callerId) {
		Object[][] params = { { "uuidMsGroup", uuidMsGroup } };
		this.getManagerDAO().deleteNative("am.groupmaster.delAllMobileMenu", params);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertToDoListofGroup(long uuidMsGroup, long[] selectedToDoList, AuditContext callerId) {		
		for (int i = 0; i < selectedToDoList.length; i++) {
			AmTodolistofgroup amTodolistofgroup = new AmTodolistofgroup();
			AmMstodolist amMstodolist = new AmMstodolist();

			AmMsgroup amMsgroup = new AmMsgroup();

			amMsgroup.setUuidMsGroup(uuidMsGroup);
			amMstodolist.setUuidMsToDoList(selectedToDoList[i]);
			amTodolistofgroup.setAmMsgroup(amMsgroup);
			amTodolistofgroup.setAmMstodolist(amMstodolist);
			amTodolistofgroup.setUsrCrt(callerId.getCallerId());
			amTodolistofgroup.setDtmCrt(new Date());

			this.getManagerDAO().insert(amTodolistofgroup);
			this.auditManager.auditAdd(amTodolistofgroup, auditInfoTDLoG, callerId.getCallerId(), "");
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteToDoListofGroup(long uuidMsGroup, AuditContext callerId) {
		Object[][] params = { { "uuidMsGroup", uuidMsGroup } };
		this.getManagerDAO().deleteNative("am.groupmaster.delAllToDoList", params);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertMenuofGroup(long uuidMsGroup, long[] selectedMenu, AuditContext callerId) {
		for (int i = 0; i < selectedMenu.length; i++) {
			AmMenuofgroup amMenuofgroup = new AmMenuofgroup();
			AmMsmenu amMsmenu = new AmMsmenu();
			AmMsgroup amMsgroup = new AmMsgroup();

			amMsgroup.setUuidMsGroup(uuidMsGroup);
			amMsmenu.setUuidMsMenu(selectedMenu[i]);

			amMenuofgroup.setAmMsgroup(amMsgroup);
			amMenuofgroup.setAmMsmenu(amMsmenu);
			amMenuofgroup.setUsrCrt(callerId.getCallerId());
			amMenuofgroup.setDtmCrt(new Date());

			this.getManagerDAO().insert(amMenuofgroup);
			this.auditManager.auditAdd(amMenuofgroup, auditInfoMoG, callerId.getCallerId(), "");
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteMenuofGroup(long uuidMsGroup, AuditContext callerId) {
		Object[][] params = { { "uuidMsGroup", uuidMsGroup } };
		this.getManagerDAO().deleteNative("am.groupmaster.delAllMenu", params);
	}

	@Override
	public Map<String, Object> listMemberOfGroup(Object params, long uuidMsGroup, int pageNumber, 
			int pageSize, AuditContext callerId) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		
		condition.append(" and amog.amMsgroup.uuidMsGroup=:uuidMsGroup");
		paramMap.put("uuidMsGroup", uuidMsGroup);
		
		//---LOGIN ID
		if (!"%".equals(((Object[][]) params)[0][1])) {
			condition.append(" and amog.amMsuser.loginId LIKE :loginId");
			paramMap.put("loginId", "%" + ((Object[][]) params)[0][1] + "%");
		}
		
		//---FULL NAME
		if (!"%".equals(((Object[][]) params)[1][1])) {
			condition.append(" and amog.amMsuser.fullName LIKE :fullName");
			paramMap.put("fullName", "%" + ((Object[][]) params)[1][1] + "%");
		}
		
		Map<String, Object> result = this.getManagerDAO().selectAll(
				"from AmMemberofgroup amog join fetch amog.amMsgroup join fetch amog.amMsuser where 1=1"
						+ condition.toString(),
				"select count(*) from AmMemberofgroup amog join amog.amMsgroup where 1=1"
						+ condition.toString(),
				paramMap, pageNumber, pageSize);
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveGroupMobile(long uuidMsGroup, String[] selectedGroupMobileArr, AuditContext callerId) {
		this.deleteMobileMenuofGroup(uuidMsGroup, callerId);
		if (selectedGroupMobileArr != null && !selectedGroupMobileArr[0].isEmpty() ) {
			String[] selectedGroupMobile = selectedGroupMobileArr[0].split(",");
			this.insertMobileMenuofGroup(uuidMsGroup, 
					MssTool.strArrayToLongArray(selectedGroupMobile), callerId);
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveGroupMenu(long uuidMsGroup, String[] selectedMenuArr, AuditContext callerId) {
		this.deleteMenuofGroup(uuidMsGroup, callerId);
		if (selectedMenuArr != null && !selectedMenuArr[0].isEmpty()) {
			String[] selectedMenu = selectedMenuArr[0].split(",");
			this.insertMenuofGroup(uuidMsGroup, 
					MssTool.strArrayToLongArray(selectedMenu), callerId);
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveGroupToDoList(long uuidMsGroup, String[] selectedToDoListArr, AuditContext callerId) {
		this.deleteToDoListofGroup(uuidMsGroup, callerId);
		if (selectedToDoListArr != null && !selectedToDoListArr[0].isEmpty()) {
			String[] selectedToDoList = selectedToDoListArr[0].split(",");
			this.insertToDoListofGroup(uuidMsGroup, 
					MssTool.strArrayToLongArray(selectedToDoList), callerId);
		}
	}

	@Override
	public Map<String, Object> listUser(Object params, Object orders, int pageNumber, 
			int pageSize, AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO().selectAll(AmMsuser.class, params, 
				orders, pageNumber, pageSize);		
		return result;
	}

	@Override
	public List listUserAddMember(Object params, AuditContext callerId) {
		List list = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT * from ( ")
		.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
		.append("select UUID_MS_USER,LOGIN_ID,FULL_NAME,ROW_NUMBER() OVER "
				+ "( order by UUID_MS_USER ) AS rownum from am_msuser with (nolock) ")
		.append("WHERE UUID_MS_USER NOT IN ( ")
		.append("SELECT UUID_MS_USER FROM AM_MEMBEROFGROUP WHERE UUID_MS_GROUP = :uuidMsGroup ) ")
		.append(paramsQueryString)
		.append(") A WHERE a.ROWNUM <= :end ")
		.append(") B WHERE b.recnum >= :start ");
		paramsStack.push(new Object[]{"uuidMsGroup", ((Object[][]) params)[3][1]});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[6][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[7][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		list = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		return list;
	}
	
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null)
			return new StringBuilder();
		
		StringBuilder sb = new StringBuilder();
		//---LOGIN ID
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append(" AND LOGIN_ID LIKE lower('%' + :loginId + '%')");
			paramStack.push(new Object[]{"loginId", (String) params[0][1]});
		}
		
		//---FULL NAME
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append(" AND FULL_NAME LIKE lower('%' + :fullName + '%')");
			paramStack.push(new Object[]{"fullName", (String) params[1][1]});
		}
		
		sb.append(" AND UUID_MS_SUBSYSTEM = :uuidSubsystem");
		sb.append(" AND IS_ACTIVE = :isActive");
		paramStack.push(new Object[]{"uuidSubsystem", Long.valueOf((String) params[2][1])});
		paramStack.push(new Object[]{"isActive", String.valueOf(params[4][1])});
		
		if (!StringUtils.equals("%", (String) params[5][1])) {
			sb.append(" and UUID_MS_USER = :uuidMsUser");
			paramStack.push(new Object[]{"uuidMsUser", Long.valueOf((String) params[5][1])});
		}
		return sb;
	}
	
	@Override
	public List listUserAddByJob(Object params, AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative("am.groupmaster.getListAddUserByLogin", params, null);
		return list;
	}

	@Override
	public Map<String, Object> listUserByJob(Object params, long uuidMsUser, AuditContext callerId) {
		Map<String, Object> userMap = new HashMap<>();
			
		String[][] listUser = getUserByLogin(params);
		List<AmMsuser> amMsuserList = new ArrayList<>();
		
		if (null != listUser) {
			for (int i = 0; i < listUser.length; i++) {
				AmMsuser amMsuser = new AmMsuser();
				amMsuser.setUuidMsUser(Long.valueOf(listUser[i][0]));
				amMsuser.setLoginId(listUser[i][1]);
				amMsuser.setFullName(listUser[i][2]);
				amMsuserList.add(amMsuser);
			}
		}
		
		userMap.put(GlobalKey.MAP_RESULT_LIST, amMsuserList);
		userMap.put(GlobalKey.MAP_RESULT_SIZE, amMsuserList.size());
		
		return userMap;
	}
	
	String[][] getUserByLogin(Object params) {		
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.groupmaster.getUserByLogin", params, null);
		if (!list.isEmpty()) {
			String[][] stringResult = new String[list.size()][3];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i][0] = (String) map.get("d0");
				stringResult[i][1] = (String) map.get("d1");
				stringResult[i][2] = (String) map.get("d2");
			}
			return stringResult;
		} 
		else {
			return null;
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertMemberOfGroup(long uuidMsGroup, long[] selectedMember, AuditContext callerId) {
		for (int i = 0; i < selectedMember.length; i++) {
			
			AmMemberofgroup amMemberofgroup = new AmMemberofgroup();
			AmMsuser amMsuser = new AmMsuser();
			AmMsgroup amMsgroup = new AmMsgroup();
			
			amMsgroup.setUuidMsGroup(uuidMsGroup);
			amMsuser.setUuidMsUser(selectedMember[i]);

			amMemberofgroup.setAmMsgroup(amMsgroup);
			amMemberofgroup.setAmMsuser(amMsuser);
			amMemberofgroup.setUsrCrt(callerId.getCallerId());
			amMemberofgroup.setDtmCrt(new Date());

			this.getManagerDAO().insert(amMemberofgroup);
			this.auditManager.auditAdd(amMemberofgroup, auditInfoMBoG, callerId.getCallerId(), "");
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteMemberOfGroup(long uuidMsGroup, long uuidMsUser, AuditContext callerId) {
		Object[][] params = { { Restrictions.eq("amMsgroup.uuidMsGroup", uuidMsGroup) }, 
				{ Restrictions.eq("amMsuser.uuidMsUser" , uuidMsUser) } };
		AmMemberofgroup amMemberofgroup = this.getManagerDAO().selectOne(AmMemberofgroup.class, params);
		this.auditManager.auditDelete(amMemberofgroup, auditInfoMBoG, callerId.getCallerId(), "");
		this.getManagerDAO().delete(amMemberofgroup);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveGroupMember(long uuidMsGroup, String[] selectedMemberArr, String type, AuditContext callerId) {		
		if (selectedMemberArr != null && !selectedMemberArr[0].isEmpty()) {
			String[] selectedMember = selectedMemberArr[0].split(",");
			this.insertMemberOfGroup(uuidMsGroup, MssTool.strArrayToLongArray(selectedMember), callerId);
		}
	}

	@Override
	public List getAppCombo(Object params, Object orders, AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative("am.groupmaster.getAppCombo", params, orders);		
		return list;
	}

	@Override
	public Map<String, Object> listGroupMobileMenu(Object paramsGroup,
			Object paramsResultOfGroup, AuditContext callerId) {
		
		List<?> listMobileMenuHirarki = this.getManagerDAO().selectAllNative(
					"am.groupmaster.groupMobileMenuHirarkiList", paramsGroup, null);
			
		Map<String, Object> resultOfGroup = this.listMobileMenuOfGroup(
			paramsResultOfGroup, null, callerId);
		boolean[] flag = new boolean[listMobileMenuHirarki.size()];

		List<AmMobilemenuofgroup> listMobilemenuofgroup = (List) resultOfGroup.get(GlobalKey.MAP_RESULT_LIST);

		for (int i = 0; i < listMobileMenuHirarki.size(); i++) {
			Boolean checked = null;
			for (int j = 0; j < (Long) resultOfGroup.get(GlobalKey.MAP_RESULT_SIZE); j++) {
				Map mapMobileMenu = (Map) listMobileMenuHirarki.get(i);
				if(mapMobileMenu!=null){
					if (((BigInteger) mapMobileMenu.get("d0")).longValue() ==
						listMobilemenuofgroup.get(j).getAmMsmobilemenu().getUuidMsMobileMenu()) {
						checked = true;
					}
				}
			}
			if (null != checked) {
				flag[i] = checked;
			}
			else {
				flag[i] = false;
			}
		}
		
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("result", listMobileMenuHirarki);
		resultMap.put("flag", flag);

		return resultMap;
	}

	@Override
	public Map<String, Object> listGroupMenu(Object paramsGroup,
			Object paramsResultOfGroup, AuditContext callerId) {
		
		List<?> listMenuHirarki =  this.getManagerDAO().selectAllNative(
				"am.groupmaster.groupMenuHirarkiList", paramsGroup, null);
		
		Map<String, Object> resultOfGroup = this.listMenuOfGroup(
			paramsResultOfGroup, null, callerId);

		boolean[] flag = new boolean[listMenuHirarki.size()];
		List<AmMenuofgroup> listMenuofgroup = (List) resultOfGroup.get(GlobalKey.MAP_RESULT_LIST);

		for (int i = 0; i < listMenuHirarki.size(); i++) {
			Boolean checked = null;
			for (int j = 0; j < (Long) resultOfGroup.get(GlobalKey.MAP_RESULT_SIZE); j++) {
				Map mapMenu = (Map) listMenuHirarki.get(i);
				if (mapMenu!=null) {
					if (((BigInteger) mapMenu.get("d0")).longValue() == 
							listMenuofgroup.get(j).getAmMsmenu().getUuidMsMenu()) {
						checked = true;
					}
				}
			}
			if (null != checked) { 
				flag[i] = checked;
			}
			else {
				flag[i] = false;
			}
		}
		
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("result", listMenuHirarki);
		resultMap.put("flag", flag);

		return resultMap;
	}

	@Override
	public Map<String, Object> listGroupToDoList(Object paramsGroup,
			Object paramsResultOfGroup, int pageNumber, int pageSize, AuditContext callerId) {				
		Map<String, Object> result = this.listToDoList(
			paramsGroup, null, pageNumber, pageSize, callerId);

		Map<String, Object> resultOfGroup = this.listToDoListOfGroup(
			paramsResultOfGroup, null, callerId);

		boolean[] flag = new boolean[((Long) result.get(GlobalKey.MAP_RESULT_SIZE)).intValue()];
		List<AmMstodolist> listToDoList = (List) result.get(GlobalKey.MAP_RESULT_LIST);
		List<AmTodolistofgroup> listToDoListOfGroup = (List) resultOfGroup.get(GlobalKey.MAP_RESULT_LIST);
		
		int totRec = (listToDoList.size() < pageSize) ? listToDoList.size() : pageSize;

		for (int i = 0; i < totRec; i++) {
			Boolean checked = null;
			for (int j = 0; j < listToDoListOfGroup.size(); j++) {
				if (String.valueOf(listToDoList.get(i).getUuidMsToDoList()).equals(
						String.valueOf(listToDoListOfGroup.get(j).getAmMstodolist().getUuidMsToDoList()))) {
					checked = true;
				}
			}
			if (null != checked) {
				flag[i] = checked;
			}
			else {
				flag[i] = false;
			}
		}
			
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("result", result);
		resultMap.put("flag", flag);
			
		return resultMap;
	}
	
	@Override
	public Long countListAllUserMember(Object params, AuditContext callerId) {
		Long result = NumberUtils.LONG_ZERO;
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
		.append("select COUNT (UUID_MS_USER) as count from am_msuser with (nolock) ")
		.append("WHERE UUID_MS_USER NOT IN ( ")
		.append("SELECT UUID_MS_USER FROM AM_MEMBEROFGROUP WHERE UUID_MS_GROUP = :uuidMsGroup ) ")
		.append(paramsQueryString);
		paramsStack.push(new Object[]{"uuidMsGroup", ((Object[][]) params)[3][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		
		result = Long.valueOf((Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams));
		return result;
	}
	
	@Override
	public Long countListUserMemberByJob(Object params, AuditContext callerId) {
		Long result = (Long) this.getManagerDAO().selectOneNative("am.groupmaster.getCountListAddUserByLogin", params);
		return result;
	}
	
	@Override
	public Map<String, Object> listGroupMenu2(Object paramsGroup,
			Object paramsResultOfGroup, AuditContext callerId) {
		
		List<Map<String,Object>> listMenuHirarki = this.getManagerDAO().selectAllNative(
					"am.groupmaster.groupMenuHirarkiList", paramsGroup, null);
		
		Object[][] tmpObject = (Object[][]) paramsResultOfGroup;
		Object[][] paramsFeature = {{"uuidMsGroup", tmpObject[0][1]}};
		Object[][] paramsMoG = {{Restrictions.eq("amMsgroup.uuidMsGroup", tmpObject[0][1])}};
		
		Map<String, Object> resultOfGroup = this.listMenuOfGroup(
				paramsMoG, null, callerId);

		boolean[] flag = new boolean[listMenuHirarki.size()];
		List<AmMenuofgroup> listMenuofgroup = (List) resultOfGroup.get(GlobalKey.MAP_RESULT_LIST);
		
		List<Map<String,Object>> listFeature = this.getManagerDAO().selectAllNative("am.groupmaster.listFeature", 
				paramsFeature, null);

		for (int i = 0; i < listMenuHirarki.size(); i++) {
			Map mapMenu = (Map) listMenuHirarki.get(i);
			Boolean checked = null;
			for (int j = 0; j < (Long) resultOfGroup.get(GlobalKey.MAP_RESULT_SIZE); j++) {
				if (mapMenu!=null) {
					if (((BigInteger) mapMenu.get("d0")).longValue() ==
							listMenuofgroup.get(j).getAmMsmenu().getUuidMsMenu()) {
						checked = true;
					}
				}
			}
			if (null != checked) {
				flag[i] = checked;
			}
			else {
				flag[i] = false;
			}
			
			List<Map<String,Object>> tmpResult = new ArrayList<>();
			for (int k=0; k < listFeature.size(); k++){
				Map<String,Object> tmpData = new HashMap<>();
				Map<String,Object> tmp = listFeature.get(k);
				if (mapMenu!=null) {
					if (((BigInteger) mapMenu.get("d0")).equals((BigInteger) tmp.get("d0"))) {						
						tmpData.put("uuidMenu", tmp.get("d0").toString());
						tmpData.put("uuidFeature", tmp.get("d1").toString());
						tmpData.put("featureCode", tmp.get("d2").toString());
						tmpData.put("featureName", tmp.get("d3").toString());
						tmpData.put("isChecked", tmp.get("d4").toString());
						tmpResult.add(tmpData);
					}
				}
			}
			listMenuHirarki.get(i).put("feature", tmpResult);
		}
		
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("result", listMenuHirarki);
		resultMap.put("flag", flag);

		return resultMap;
	}

	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public void saveGroupMenuFeature(long uuidMsGroup, String[] selectedFeatureArr, AuditContext callerId) {
		this.deleteMenuFeatureofGroup(uuidMsGroup);
		if (selectedFeatureArr != null && !selectedFeatureArr[0].isEmpty()) {
			String[] selectedMenu = selectedFeatureArr[0].split(",");
			this.insertMenuFeatureOfGroup(uuidMsGroup, MssTool.strArrayToLongArray(selectedMenu), callerId);
		}
	}
	
	public void insertMenuFeatureOfGroup(long uuidMsGroup, long[] selectedMenu, AuditContext callerId) {
		for (int i = 0; i < selectedMenu.length; i++) {
			
			AmMenufeatureofgroup amMenufeatureofgroup = new AmMenufeatureofgroup();
			AmMsmenufeature amMsmenufeature = new AmMsmenufeature();
			AmMsgroup amMsgroup = new AmMsgroup();
			
			amMsgroup.setUuidMsGroup(uuidMsGroup);
			amMsmenufeature.setUuidMsmenuFeature(selectedMenu[i]);
			
			amMenufeatureofgroup.setAmMsgroup(amMsgroup);
			amMenufeatureofgroup.setAmMsmenufeature(amMsmenufeature);
			amMenufeatureofgroup.setUsrCrt(callerId.getCallerId());
			amMenufeatureofgroup.setDtmCrt(new Date());
			this.getManagerDAO().insert(amMenufeatureofgroup);
		}
	}
	
	public void deleteMenuFeatureofGroup(long uuidMsGroup) {
		Object[][] params = { { "uuidMsGroup", uuidMsGroup } };
		this.getManagerDAO().deleteNative("am.groupmaster.delAllMenuFeature", params);
	}

	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String getListJobAdmin(AuditContext callerId) {
		Object[][] paramsAdmin = { 
				{Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MS+GlobalKey.GENERALSETTING_JOBADMIN)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsAdmin);
		return amGeneralsetting.getGsValue();
	}
}
