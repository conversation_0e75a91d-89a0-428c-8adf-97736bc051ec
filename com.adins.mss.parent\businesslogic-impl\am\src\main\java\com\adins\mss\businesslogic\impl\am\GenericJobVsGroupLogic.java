package com.adins.mss.businesslogic.impl.am;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.JobVsGroupLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.AmMemberofgroup;
import com.adins.mss.model.AmMsgroup;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsGroupofjob;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsRoundrobintask;
import com.adins.mss.model.TempMapJobConfins;

@SuppressWarnings({"rawtypes", "unchecked"})
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericJobVsGroupLogic extends BaseLogic implements JobVsGroupLogic, MessageSourceAware{
	private AuditInfo auditInfo;
	private AuditInfo auditInfoGroupOfJob;
	private AuditInfo auditInfoMemberOfGroup;
	
	@Autowired
	private MessageSource messageSource;
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public GenericJobVsGroupLogic() {
		String[] pkCols = {"uuidJob"};
		String[] pkDbCols = {"UUID_JOB"};
		String[] cols = {"uuidJob", "amMssubsystem.uuidMsSubsystem", "msJob.uuidJob", "isActive", 
				"jobCode", "description" , "isFieldPerson", "isBranch", "isRoundRobin", 
				"isSelfAssignment", "isAutomaticDistribution"};
		String[] dbCols = {"UUID_JOB", "UUID_MS_SUBSYSTEM", "PARENT_JOB", "IS_ACTIVE", "JOB_CODE",
				"DESCRIPTION", "IS_FIELD_PERSON", "IS_BRANCH", "IS_ROUND_ROBIN", 
				"IS_SELF_ASSIGNMENT", "IS_AUTOMATIC_DISTRIBUTION"};
		this.auditInfo = new AuditInfo("MS_JOB", pkCols, pkDbCols, cols, dbCols);
		
		String[] pkColsGoj = {"uuidGroupOfJob"};
		String[] pkDbColsGoj = {"UUID_GROUP_OF_JOB"};
		String[] colsGoj = {"uuidGroupOfJob", "amMsgroup.uuidMsGroup", "msJob.uuidJob"};
		String[] dbColsGoj = {"UUID_GROUP_OF_JOB", "UUID_MS_GROUP", "UUID_JOB"};
		this.auditInfoGroupOfJob = new AuditInfo("MS_GROUPOFJOB", pkColsGoj, pkDbColsGoj, colsGoj, dbColsGoj);
		
		String[] pkColsMoG = { "uuidMemberOfGroup" };
		String[] pkDbColsMoG = { "UUID_MEMBER_OF_GROUP" };
		String[] colsMoG = { "uuidMemberOfGroup", "amMsgroup.uuidMsGroup", "amMsuser.uuidMsUser" };
		String[] dbColsMoG = { "UUID_MEMBER_OF_GROUP", "UUID_MS_GROUP", "UUID_MS_USER" };
		this.auditInfoMemberOfGroup = new AuditInfo("AM_MEMBEROFGROUP", pkColsMoG, pkDbColsMoG, colsMoG, dbColsMoG);
	}
	
	@Override
	public List getListJob(Object params, AuditContext callerId) {
		
		List result = this.getManagerDAO().selectAllNative("am.jobvsgroup.getJobHirarkiList", params, null);
		return result;
	}

	private Map <String, Object> getListGroup(Object params, Object orders) {
		
		Map<String, Object> result = this.getManagerDAO().selectAll(AmMsgroup.class, params, orders);
		return result;
	}

	@Override
	public MsJob selectOneJob(long uuidJob, AuditContext callerId) {
		
		MsJob result = (MsJob)this.getManagerDAO().selectOne( 
				"from MsJob mj left join fetch mj.msJob join fetch mj.amMssubsystem where mj.uuidJob = :uuidJob", 
				new Object[][] {{"uuidJob", uuidJob}});;
		return result;
	}
	
	private MsJob selectDefaultParent(Object[][] params) {
		
		MsJob result = (MsJob)this.getManagerDAO().selectOne(MsJob.class, params);	
		return result;
	}
	
	private AmMsgroup selectOneGroup(long uuidGroup) {
		
		AmMsgroup result = (AmMsgroup) this.getManagerDAO().selectOne(AmMsgroup.class, uuidGroup);	
		return result;
	}
	
	private boolean hasChild(long uuidJob) {
		Object[][] params = { { Restrictions.eq("msJob.uuidJob", uuidJob) } };
		Map<String, Object> check = this.getManagerDAO().count(MsGroupofjob.class, params);
		
		Object[][] params1 = { { Restrictions.eq("msJob.uuidJob", uuidJob) } };
		Map<String, Object> check1 = this.getManagerDAO().count(MsJob.class, params1);
		
		Object[][] params2 = { { Restrictions.eq("msJob.uuidJob", uuidJob) } };
		Map<String, Object> check2 = this.getManagerDAO().count(AmMsuser.class, params2);
		
		if ((Long) check.get(GlobalKey.MAP_RESULT_SIZE) > 0 || 
				(Long) check1.get(GlobalKey.MAP_RESULT_SIZE) > 0 ||
					(Long) check2.get(GlobalKey.MAP_RESULT_SIZE) > 0 ){
			return true;
		}
		else{ 
			return false;
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteJob(long uuidJob, AuditContext callerId) {
		MsJob job = new MsJob();
		job.setUuidJob(uuidJob);
		
		if (!this.hasChild(uuidJob)) {
			this.deleteJobMappingConfinsByUuidJob(String.valueOf(uuidJob));
			this.auditManager.auditDelete(job, auditInfo, callerId.getCallerId(), "");
			this.getManagerDAO().delete(job);
		}
		else{
			throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION, 
					this.messageSource.getMessage("businesslogic.global.datahasachild", null,
					this.retrieveLocaleAudit(callerId)), new Exception());
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertJob(MsJob obj, String task, AmMssubsystem subsystem, String selectedGroups, String mapJobCode, AuditContext callerId) {			
		obj.setAmMssubsystem(subsystem);
		Object[][] params = { {"jobCode",obj.getJobCode()}, {"uuidSubsystem", obj.getAmMssubsystem().getUuidMsSubsystem()} };
		if (newJobValidity(params)) { 
			if ("add".equals(task)) {
				Object[][] params2 = {{Restrictions.isNull("msJob")}, 
						{Restrictions.eq("amMssubsystem.uuidMsSubsystem", obj.getAmMssubsystem().getUuidMsSubsystem())}};
				obj.setMsJob(selectDefaultParent(params2));
			}
			if (!GlobalVal.SUBSYSTEM_MO.equals(obj.getAmMssubsystem().getSubsystemName())) {
				obj.setIsBranch("1");
				obj.setIsAutomaticDistribution(obj.getIsAutomaticDistribution());
				obj.setIsSelfAssignment(obj.getIsSelfAssignment());
			}
			
			if (!GlobalVal.SUBSYSTEM_MS.equals(obj.getAmMssubsystem().getSubsystemName())) {
				obj.setIsRoundRobin("0");
			}
			
			obj.setJobCode(obj.getJobCode().toUpperCase());
			obj.setDtmCrt(new Date());
			obj.setUsrCrt(callerId.getCallerId());
			this.getManagerDAO().insert(obj);
			this.auditManager.auditAdd(obj, auditInfo, callerId.getCallerId(), "");
			if (!selectedGroups.isEmpty()) {
				insertAllMsGroupJob(obj, selectedGroups, callerId);
			}
			
			this.insertJobMappingConfinsByMapJobCode(String.valueOf(obj.getUuidJob()), mapJobCode);
		} 
		else { 
			throw new EntityNotUniqueException(this.messageSource.getMessage(
					"businesslogic.job.entitynotuniquejob", new Object[]{obj.getJobCode()}, this.retrieveLocaleAudit(callerId)), obj.getJobCode());
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public MsJob updateJob(long uuidJob, MsJob job, String selectedGroups, String mapJobCode, AuditContext callerId) {
		
		MsJob obj = selectOneJob(uuidJob, callerId);
		String oldIsRoundRobin=StringUtils.EMPTY;
		//obj.setJobCode(job.getJobCode());
		obj.setDescription(job.getDescription());
		
		if ( GlobalVal.SUBSYSTEM_MO.equals(obj.getAmMssubsystem().getSubsystemName()) ) {
			obj.setIsBranch(job.getIsBranch());	
			obj.setIsAutomaticDistribution(job.getIsAutomaticDistribution());
			obj.setIsSelfAssignment(job.getIsSelfAssignment());
		}
		
		if(GlobalVal.SUBSYSTEM_MS.equals(obj.getAmMssubsystem().getSubsystemName())){
			oldIsRoundRobin = obj.getIsRoundRobin();
			obj.setIsRoundRobin(job.getIsRoundRobin());				
		}
		
		obj.setJobCode(obj.getJobCode().toUpperCase());
		obj.setIsActive(job.getIsActive());
		obj.setIsFieldPerson(job.getIsFieldPerson());
		obj.setUsrUpd(callerId.getCallerId());
		obj.setDtmUpd(new Date());
					
		this.auditManager.auditEdit(obj, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(obj);
		updateMsGroupOfJob(obj, selectedGroups, callerId);
		
		this.deleteJobMappingConfinsByUuidJob(String.valueOf(uuidJob));
		this.insertJobMappingConfinsByMapJobCode(String.valueOf(uuidJob), mapJobCode);
		
		if(GlobalVal.SUBSYSTEM_MS.equals(obj.getAmMssubsystem().getSubsystemName())){
			if(!StringUtils.isBlank(oldIsRoundRobin)){
			if(!oldIsRoundRobin.equals(job.getIsRoundRobin())){
				if("0".equals(job.getIsRoundRobin())){
					//delete all user job in round robin
					Object[][] param = {{"uuidJob", job.getUuidJob()}};
					this.getManagerDAO().deleteNative("am.jobvsgroup.deleteRoundRobinUser", param);
				}
				if("1".equals(job.getIsRoundRobin())){
					Object[][] prm = {{"uuidJob", job.getUuidJob()}};
					this.getManagerDAO().deleteNative("am.jobvsgroup.deleteRoundRobinUser", prm);
					//add all user job in round robin
					Object[][] param = {{Restrictions.eq("msJob.uuidJob", job.getUuidJob())},
										{Restrictions.eq("isActive", "1")},
										{Restrictions.eq("isDeleted","0")}};
					Map<String, Object> result = this.getManagerDAO().list(AmMsuser.class, param, null);
					List<AmMsuser> listUser = (List) result.get(GlobalKey.MAP_RESULT_LIST);
					for (int i = 0; i < listUser.size(); i++) {
						AmMsuser bean = listUser.get(i);
						insertMsRoundrobintask(bean, callerId);
					}
				}
			}
		}
		}
		return obj;
	}
	
	private void insertAllMsGroupJob(MsJob job, String groups, AuditContext callerId) {
		String[] uuidGroups= groups.split(",");	
//		Object[][] params = {{Restrictions.eq("msJob.uuidJob", job.getUuidJob())}}; 
//		List<AmMsuser> users = getUserByJob(params, callerId);
		for (int i = 0; i < uuidGroups.length; i++) {
			AmMsgroup group = selectOneGroup(Long.parseLong(uuidGroups[i]));
			MsGroupofjob groupOfJob = new MsGroupofjob();
			groupOfJob.setAmMsgroup(group);
			groupOfJob.setMsJob(job);
			groupOfJob.setUsrCrt(callerId.getCallerId());
			groupOfJob.setDtmCrt(new Date());
			
			this.getManagerDAO().insert(groupOfJob);
			this.auditManager.auditAdd(groupOfJob, auditInfoGroupOfJob, callerId.getCallerId(), "");
			Object[][] paramsInsertMog = {{"uuidJob", job.getUuidJob()}, {"uuidGroup",Long.parseLong(uuidGroups[i])}, {"callerId", callerId.getCallerId()}};
			this.getManagerDAO().insertNative("am.jobvsgroup.insertAmMemberOfGroup", paramsInsertMog);
//			insertMsGroupOfJob(job, Long.parseLong(uuidGroups[i]), users, callerId);
		}							
	}
	
	private void insertMsRoundrobintask(AmMsuser user, AuditContext callerId) {
		MsRoundrobintask bean = new MsRoundrobintask();
		bean.setUsrCrt(callerId.getCallerId());
		bean.setDtmCrt(new Date());
		bean.setAmMsuser(user);
		bean.setFlagTask("0");
		this.getManagerDAO().insert(bean);	
	}
	
	@SuppressWarnings("unused")
	private void insertMsGroupOfJob(MsJob job, long uuidGroup, List<AmMsuser> users, AuditContext callerId) {
		AmMsgroup group = selectOneGroup(uuidGroup);
		MsGroupofjob groupOfJob = new MsGroupofjob();
		groupOfJob.setAmMsgroup(group);
		groupOfJob.setMsJob(job);
		groupOfJob.setUsrCrt(callerId.getCallerId());
		groupOfJob.setDtmCrt(new Date());
		
		this.getManagerDAO().insert(groupOfJob);
		this.auditManager.auditAdd(groupOfJob, auditInfoGroupOfJob, callerId.getCallerId(), "");		
		for (int j=0; j<users.size(); j++) {
			insertAmMemberOfGroup(group, users.get(j), callerId);
		}
	}
	
	private void insertAmMemberOfGroup(AmMsgroup group, AmMsuser user, AuditContext callerId) {
		AmMemberofgroup obj = new AmMemberofgroup();
		obj.setUsrCrt(callerId.getCallerId());
		obj.setDtmCrt(new Date());
		obj.setAmMsuser(user);
		obj.setAmMsgroup(group);
		this.getManagerDAO().insert(obj);
		this.auditManager.auditAdd(obj, auditInfoMemberOfGroup, callerId.getCallerId(), "");
	}
	
	private void updateMsGroupOfJob(MsJob job, String groups, AuditContext callerId){
		Object[][] params = {{"uuidJob", job.getUuidJob()}};
		this.getManagerDAO().deleteNative("am.jobvsgroup.deleteAmMemberOfGroup", params);
		this.getManagerDAO().deleteNative("am.jobvsgroup.deleteMsGroupOfJob", params);
		if ( !groups.isEmpty() ){
			insertAllMsGroupJob(job, groups, callerId);
		}
	}

	@Override
	public List<AmMsgroup> getListComboGroup(Object params, Object orders,
			 AuditContext callerId) {
		
		Map<String, Object> listGroup = getListGroup(params, orders);
		List<AmMsgroup> group = (List<AmMsgroup>) listGroup.get(GlobalKey.MAP_RESULT_LIST);

		return group;
	}
	
	@Override
	public Map<String, Object> getGroupListByJob(long uuidJob, AuditContext callerId) {
		
		Map<String, Object> result = null;
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and mgoj.msJob.uuidJob=:uuidJob");
		paramMap.put("uuidJob", uuidJob);
		
		result = this.getManagerDAO().selectAll(
				"from MsGroupofjob mgoj join fetch mgoj.amMsgroup join fetch mgoj.msJob where 1=1"
						+ condition.toString(), paramMap);
		return result;
	}
	
	@Override
	public Map<String, Object> getGroupListByJob(long uuidJob,
			int pageNumber, int pageSize, AuditContext callerId) {
		
		Map<String, Object> result = null;
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and mgoj.msJob.uuidJob=:uuidJob");
		paramMap.put("uuidJob", uuidJob);
		
		result = this.getManagerDAO().selectAll(
				"from MsGroupofjob mgoj join fetch mgoj.amMsgroup join fetch mgoj.msJob where 1=1"
						+ condition.toString(), paramMap);
		return result;
	}

	private boolean newJobValidity(Object params) {
		Integer result = (Integer) this.getManagerDAO().selectOneNative("am.jobvsgroup.jobCodeValidity", params);	
		if (result != 0) {
			return false;
		}
		return true;
	}
	
	@Override
	public Integer countListJob(Object params, AuditContext callerId) {
		Integer result = (Integer) this.getManagerDAO().selectOneNative("am.jobvsgroup.getJobHirarkiListCount", params);;
		return result;
	}
	
	public List<AmMsuser> getUserByJob(Object params) {	
		Map resultMap = this.getManagerDAO().list(AmMsuser.class, params, null);
		List <AmMsuser>	result = (List) resultMap.get(GlobalKey.MAP_RESULT_LIST);	
		return result;
	}

	@Override
	public List getJobMappingConfins(String uuidJob, AuditContext ac) {
		List result = this.getManagerDAO().selectAllNative("am.jobvsgroup.getMappingJobConfins", new Object[][]{{"uuidJob",uuidJob}}, null);
		return result;
	}
	
	public void deleteJobMappingConfinsByUuidJob(String uuidJob) {
		this.getManagerDAO().deleteNative("am.jobvsgroup.deleteMappingJobConfinsByUuidjob", new Object[][]{{"uuidJob",uuidJob}});
	}
	
	public void insertJobMappingConfinsByMapJobCode(String uuidJob, String mapJobCode) {
		if (StringUtils.isNotBlank(mapJobCode)) {
			String[] arrMapJobCode = mapJobCode.split(";");
			for (int i = 0; i < arrMapJobCode.length; i++) {
				String confinsJobCode = StringUtils.trim(arrMapJobCode[i]);
				TempMapJobConfins mapJobConfins = new TempMapJobConfins();
				mapJobConfins.setUuidJob(Long.parseLong(uuidJob));
				mapJobConfins.setMapJobCode(confinsJobCode);
				mapJobConfins.setMapJobDescription("");
				this.getManagerDAO().insert(mapJobConfins);
			}
		}
	}
	
}
