package com.adins.mss.businesslogic.impl.am;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.LayoutLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericLayoutLogic extends BaseLogic implements LayoutLogic {
	
	@Override
	public List retrieveMenuTree(AuditContext callerId) {
		List result = new ArrayList<>();
		List<Map> finalResult = new ArrayList<>();

		Object[][] params = {{"uuidMsuser", Long.valueOf(callerId.getCallerId())}};
		result = (List) this.getManagerDAO().selectAllNative("am.layout.selectMenuByMember", params, null);
		
		String[][] paramsSubsystem = {{"subsystemName", GlobalVal.SUBSYSTEM_MS}, {"isActive", "1"}};
		BigInteger uuidSubsystem = (BigInteger) this.getManagerDAO().selectOneNativeString(
				"SELECT UUID_MS_SUBSYSTEM "
				+ "FROM AM_MSSUBSYSTEM with (nolock)"
				+ "WHERE SUBSYSTEM_NAME = :subsystemName AND IS_ACTIVE = :isActive",
				paramsSubsystem);
		
		if (null == uuidSubsystem) {
			for (int i = 0; i < result.size(); i++) {
				Map<String, Object> map = (Map) result.get(i);
				Map<String, Object> newMap = new HashMap<String, Object>();
				String menuPrompt = (String) map.get("d4");

				if (!"Mapping Form".equals(menuPrompt)) {
					newMap.putAll(map);
				}
				if (null != newMap && !newMap.isEmpty()) {
					finalResult.add(newMap);
				}
			}
			result = finalResult;
		}
		
		List<Map> removeMenu = new ArrayList<>();
		String isBranchPiloting = (String)this.getManagerDAO().selectOneNativeString("SELECT IS_PILOTING FROM AM_MSUSER AMU WITH (NOLOCK) JOIN MS_BRANCH MB WITH(NOLOCK) ON MB.UUID_BRANCH = AMU.UUID_BRANCH WHERE UUID_MS_USER = :uuidMsuser", params);
		String isBranchPilotingCAE = (String)this.getManagerDAO().selectOneNativeString("SELECT IS_PILOTING_CAE FROM AM_MSUSER AMU WITH (NOLOCK) JOIN MS_BRANCH MB WITH(NOLOCK) ON MB.UUID_BRANCH = AMU.UUID_BRANCH WHERE UUID_MS_USER = :uuidMsuser", params);
		for (int i = 0; i < result.size(); i++) {
			Map<String, Object> map = (Map) result.get(i);
			String isFormPiloting = map.get("d6") == null?null:(String) map.get("d6");
			if("1".equals(isFormPiloting)) {
				if(!"1".equals(isBranchPiloting)) {
					removeMenu.add(map);
				}
			}
			if ("1".equals(isBranchPilotingCAE) 
					&& map.get("d4")!= null 
					&& map.get("d4").toString().toLowerCase().contains("new lead")
					&& !map.get("d4").toString().toLowerCase().contains("pre instant approval")) {
				removeMenu.add(map);
			}
		}
		for(Map map:removeMenu) {
			result.remove(map);
		}			
		return result;
	}
	
	@Override
	public Integer getGSInterval(AuditContext auditContext){

		Object[][] params = { { Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_INTERVAL) } };
		AmGeneralsetting interval = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		Integer result = Integer.valueOf( interval.getGsValue() );
			
		return result;
	}
	
	@Override
	public Integer getGSMaxRangeDate(AuditContext auditContext){

		Object[][] params = { { Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_MAX_RANGEDATE_REPORT) } };
		AmGeneralsetting interval = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		Integer result = Integer.valueOf( interval.getGsValue() );
	
		return result;
	}
}
