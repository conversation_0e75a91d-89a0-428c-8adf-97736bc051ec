package com.adins.mss.businesslogic.impl.am;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.LdapException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.ldap.LdapLogic;
import com.adins.framework.tool.password.PasswordHash;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.EventLogLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.am.LoginLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.LoginProvider;
import com.adins.mss.exceptions.LoginException;
import com.adins.mss.exceptions.LoginException.Reason;
import com.adins.mss.model.AmAttributeofmember;
import com.adins.mss.model.AmMemberofgroup;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsJob;
import com.adins.mss.util.PropertiesHelper;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericLoginLogic extends BaseLogic implements LoginLogic, MessageSourceAware {

    private static final Logger LOG = LoggerFactory.getLogger(GenericLoginLogic.class); 
	private static final long DEFAULT_REQ_LOGOUT = 60_000;
	private static final int DEFAULT_MAX_FAIL_COUNT = 3;
	private static final int DEFAULT_SYSTEM_TIMEOUT = (int) TimeUnit.MINUTES.toSeconds(30);
	private static final String LOGIN_PROVIDER_ACTIVE_DIRECTORY = LoginProvider.ACTIVE_DIRECTORY.toString();
	private static final String LOGIN_PROVIDER_NEW_CONFINS = LoginProvider.NEW_CONFINS.toString();
	private static final String LOGIN_PROVIDER_DATABASE = LoginProvider.DATABASE.toString();

	private EventLogLogic eventLogLogic;
	private GlobalLogic globalLogic;
    private IntFormLogic intFormLogic;	
	private LdapLogic ldapLogic;
	private CommonLogic commonLogic;

	private String paramLoginId = "loginId";
	
	@Autowired
	private MessageSource messageSource;
	
	public void setEventLogLogic(EventLogLogic eventLogLogic) {
		this.eventLogLogic = eventLogLogic;
	}

	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}

	public void setIntFormLogic(IntFormLogic intFormLogic) {
        this.intFormLogic = intFormLogic;
    }

    public void setLdapLogic(LdapLogic ldapLogic) {
        this.ldapLogic = ldapLogic;
    }
    
    public void setCommonLogic(CommonLogic commonLogic) {
        this.commonLogic = commonLogic;
    }
    
    @Override
    public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

    public Map<String, Object> listMemberofGroup(long uuidMsuser) {

		Object[][] params = {{Restrictions.eq("amMsuser.uuidMsUser", uuidMsuser)}};
		Map<String, Object> result  = this.getManagerDAO().list(AmMemberofgroup.class, params, null);

		return result;
	}

	public Map listAttributeofMember(long uuidMsuser) {

		Map<String, Object> result = null;
		Map mapAttributeOfMember = new HashMap();
		result = this.getManagerDAO().list(
				"from AmAttributeofmember aa join fetch aa.amMsuser au join fetch aa.amMsattribute am where au.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", uuidMsuser}});
		List<AmAttributeofmember> listAttributeOfMember = (List) result.get(GlobalKey.MAP_RESULT_LIST);

		if (listAttributeOfMember != null) {
			for (AmAttributeofmember attributeOfMember : listAttributeOfMember) {
				mapAttributeOfMember.put(
					attributeOfMember.getAmMsattribute().getAtbCode(), 
					attributeOfMember.getAttributeValue()
				);
			}
		}
		return mapAttributeOfMember;
	}

	public AmMsuser getUser(long uuid) {
		
		AmMsuser result = null;
		result = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.msJob mj join fetch u.msBranch mb join fetch u.msDealer md "
			+ "join fetch u.amMssubsystem ms join fetch ms.amMsapplication ma where u.uuidMsUser = :uuidMsUser", 
			new Object[][] {{"uuidMsUser", uuid}});
		return result;
	}
	
	private AmMsuser logoutUser(long uuid, AuditContext callerId) {
            
        AmMsuser dbModel = this.getManagerDAO().selectOne(AmMsuser.class, uuid);
        dbModel.setDtmUpd(new Date());
        dbModel.setUsrUpd(callerId.getCallerId());
        dbModel.setIsLoggedIn("0");
        dbModel.setLastRequestOut(null);
        
        this.getManagerDAO().update(dbModel);
        
        return dbModel;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void doLogout(long uuid, AuditContext callerId) {
        AmMsuser amMsuser = this.logoutUser(uuid, callerId);
        eventLogLogic.logUserEvent(amMsuser,
                GlobalVal.USEREVENT_ACTIVITY_LOGOUT,
                GlobalVal.USEREVENT_CONSEQUENCE_LOGOUT_SUCCESS,
                callerId);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
    @Override
    public void doLogoutSessionTimeout(long uuidUser, AuditContext callerId) {
        AmMsuser amMsuser = this.logoutUser(uuidUser, callerId);
        eventLogLogic.logUserEvent(amMsuser,
                GlobalVal.USEREVENT_ACTIVITY_LOGOUT,
                GlobalVal.USEREVENT_CONSEQUENCE_USER_RELEASE_LOGGED,
                callerId);
    }

	public long getUuid(String loginId) {
		List result = null;
		long uuid = 0;

		String[][] param = { { paramLoginId, loginId.toUpperCase() } };

		result = this.getManagerDAO().selectAllNative("am.login.getUuid",
				param, null);

		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map) result.get(i);
			uuid = Long.parseLong(temp.get("d0").toString());
		}
		return uuid;
	}

	private boolean isPwdExpired (long uuidMsUser, AuditContext auditContext) {
		Object[][] params = { {"uuidMsUser", uuidMsUser} };
		Integer days = (Integer) this.getManagerDAO().selectOneNativeString(
			"SELECT DATEDIFF(DAY, MAX(DTM_CRT), CURRENT_TIMESTAMP) " +
			"from AM_USERPWDHISTORY with (nolock)" +
			"WHERE UUID_MS_USER = :uuidMsUser", params);
		
		days = (days == null) ? new Integer(0) : days;
		
		String generalSettingPwdExpired = globalLogic.getGsValue(
				GlobalKey.GENERALSETTING_PASSWORD_EXPIRED, auditContext);
		if (Integer.parseInt(generalSettingPwdExpired) < days){
			return true;
		}
		else{
			return false;
		}
		
	}
	
	@Transactional(noRollbackFor={LoginException.class})
	@Override
	public AmMsuser doLogin(String loginId, String password,
			AuditContext auditContext) {
			
		if (StringUtils.isBlank(loginId) || StringUtils.isBlank(password)) {
			throw new LoginException(this.messageSource.getMessage("businesslogic.login.userpasswordrequired", 
					null, this.retrieveLocaleAudit(auditContext)), Reason.LOGIN_REQUIRED);
		}
		
		long uuid = this.getUuid(loginId);
		
		AmMsuser amMsuser = (AmMsuser) this.getUser(uuid);
		
		if (amMsuser == null) { //LoginID not found
			eventLogLogic.logUserEvent(
							amMsuser,
							GlobalVal.USEREVENT_ACTIVITY_LOGIN,
							GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_NO_SUCH_USER,
							auditContext);
			throw new LoginException(this.messageSource.getMessage("businesslogic.login.invalididpassword", 
					null, this.retrieveLocaleAudit(auditContext)), Reason.LOGIN_INVALID);
		}

		boolean authenticated = false;
		if (PropertiesHelper.isBypassLogin()) {
			authenticated = true;
		}
		else {
			if (LOGIN_PROVIDER_ACTIVE_DIRECTORY.equals(amMsuser.getLoginProvider())) {
				authenticated = this.ldapAuth(loginId, password);
			}
			else if (LOGIN_PROVIDER_NEW_CONFINS.equals(amMsuser.getLoginProvider())) {
				authenticated = this.intFormLogic.authenticateUser(amMsuser, password);
			}
			else if (LOGIN_PROVIDER_DATABASE.equals(amMsuser.getLoginProvider())) {
				authenticated = this.databaseAuth(password, amMsuser.getPassword());
			}
		}

		if (authenticated) {
            AmMssubsystem amMssubsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, amMsuser.getAmMssubsystem().getUuidMsSubsystem());
            
            if (!"1".equals(amMssubsystem.getIsActive())) { //Subsystem not active
                eventLogLogic.logUserEvent(
                                amMsuser,
                                GlobalVal.USEREVENT_ACTIVITY_LOGIN,
                                GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_SUBSYSTEM_INACTIVE,
                                auditContext);
                throw new LoginException(this.messageSource.getMessage("businesslogic.login.inactivesubsystem", 
                		new Object[]{amMsuser.getLoginId()}, this.retrieveLocaleAudit(auditContext)), Reason.LOGIN_INACTIVE);
            }
            if (!"1".equals(amMsuser.getIsActive())) { //User not active
                eventLogLogic.logUserEvent(
                                amMsuser,
                                GlobalVal.USEREVENT_ACTIVITY_LOGIN,
                                GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_ACCOUNT_DISABLED,
                                auditContext);
                throw new LoginException(this.messageSource.getMessage("businesslogic.login.inactiveadmin", 
                		new Object[]{amMsuser.getLoginId()}, this.retrieveLocaleAudit(auditContext)), 
                        Reason.LOGIN_INACTIVE);
            }
            
            MsJob job = this.getManagerDAO().selectOne(MsJob.class, amMsuser.getMsJob().getUuidJob());
            if (!"1".equals(job.getIsActive())) { //Job not active
                eventLogLogic.logUserEvent(
                                amMsuser,
                                GlobalVal.USEREVENT_ACTIVITY_LOGIN,
                                GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_JOB_INACTIVE,
                                auditContext);
                throw new LoginException(this.messageSource.getMessage("businesslogic.login.inactivejob", 
                		new Object[]{amMsuser.getLoginId()}, this.retrieveLocaleAudit(auditContext)), 
                        Reason.LOGIN_INACTIVE);
            }       
            if ("1".equals(amMsuser.getIsDormant())) { //User is dormant
                eventLogLogic
                        .logUserEvent(
                                amMsuser,
                                GlobalVal.USEREVENT_ACTIVITY_LOGIN,
                                GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_ACCOUNT_DORMANT,
                                auditContext);
                throw new LoginException(this.messageSource.getMessage("businesslogic.login.dormantstatus", 
                		new Object[]{amMsuser.getLoginId()}, this.retrieveLocaleAudit(auditContext)),
                        Reason.LOGIN_DORMANT);
            }
            if (this.isPwdExpired(uuid, auditContext) && !amMsuser.getLoginProvider().equals(GlobalVal.NC)) {               
                Date currentDate = new Date();
                amMsuser.setUsrUpd(GlobalVal.SYSTEM_LOGINID);
                amMsuser.setDtmUpd(currentDate);
                amMsuser.setIsPasswordExpired("1");
                amMsuser.setChangePwdLogin("1");
                this.getManagerDAO().update(amMsuser);
                
                eventLogLogic.logUserEvent(
                                amMsuser,
                                GlobalVal.USEREVENT_ACTIVITY_LOGIN,
                                GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_PASSWORD_EXPIRED,
                                auditContext);
            }
            if ("1".equals(amMsuser.getIsLocked())) { //User is locked
                eventLogLogic.logUserEvent(
                                amMsuser,
                                GlobalVal.USEREVENT_ACTIVITY_LOGIN,
                                GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_ACCOUNT_LOCKED_OUT,
                                auditContext);
                throw new LoginException(this.messageSource.getMessage("businesslogic.login.locked", 
                		new Object[]{amMsuser.getLoginId()}, this.retrieveLocaleAudit(auditContext)),
                        Reason.LOGIN_LOCKED);
            }
            if ("1".equals(amMsuser.getIsLoggedIn())) { //Already logged In
                if (amMsuser.getLastRequestOut() == null) {
                    // request automatic log out
                    Date currentDate = new Date();
                    amMsuser.setUsrUpd(GlobalVal.SYSTEM_LOGINID);
                    amMsuser.setDtmUpd(currentDate);
                    amMsuser.setLastRequestOut(currentDate);
                    this.getManagerDAO().update(amMsuser);

                    eventLogLogic
                            .logUserEvent(
                                    amMsuser,
                                    GlobalVal.USEREVENT_ACTIVITY_LOGIN,
                                    GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_ACCOUNT_CONCURRENCE_REQUESTOUT,
                                    auditContext);
                } else {
                    Date currentDate = new Date();
                    Calendar calNow = Calendar.getInstance();
                    calNow.setTime(currentDate);
                    Calendar calThen = Calendar.getInstance();
                    calThen.setTime(amMsuser.getLastRequestOut());
                    String generalSettingReqLogout = globalLogic.getGsValue(
                            GlobalKey.GENERALSETTING_REQ_LOGOUT, auditContext);
                    long reqLogout = NumberUtils.toLong(
                            generalSettingReqLogout, DEFAULT_REQ_LOGOUT);
                    if (calNow.getTimeInMillis() - calThen.getTimeInMillis() < reqLogout) {
                    }
                }
            }
		    
			// LOGIN - Success
			Date currentDate = new Date();
			amMsuser.setUsrUpd(GlobalVal.SYSTEM_LOGINID);
			amMsuser.setDtmUpd(currentDate);
			amMsuser.setFailCount(0);
			amMsuser.setIsLoggedIn("1");
			amMsuser.setPrevLoggedIn(amMsuser.getLastLoggedIn());
			amMsuser.setLastLoggedIn(currentDate);
			amMsuser.setLastRequestOut(null);
			this.getManagerDAO().update(amMsuser);
			
			eventLogLogic.logUserEvent(amMsuser,
					GlobalVal.USEREVENT_ACTIVITY_LOGIN,
					GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_SUCCESS,
					auditContext);
			
			this.commonLogic.insertLastLocationTimestamp(uuid, amMsuser.getAmMssubsystem().getUuidMsSubsystem(), currentDate, auditContext);
			
			return amMsuser;				
		}
		
		String generalSettingMaxFailCount = globalLogic.getGsValue(
				GlobalKey.GENERALSETTING_MAX_FAIL_COUNT, auditContext);
		int maxFailCount = NumberUtils.toInt(
				generalSettingMaxFailCount, DEFAULT_MAX_FAIL_COUNT);
		if (Integer.compare(maxFailCount-1, amMsuser.getFailCount()) > 0) {
			// LOGIN - Fail Try
			Date currentDate = new Date();
			amMsuser.setUsrUpd(GlobalVal.SYSTEM_LOGINID);
			amMsuser.setDtmUpd(currentDate);
			amMsuser.setFailCount((amMsuser.getFailCount() == null ? 1 : amMsuser.getFailCount() + 1));
			amMsuser.setPrevLoggedFail(amMsuser.getLastLoggedFail());
			amMsuser.setLastLoggedFail(currentDate);
			this.getManagerDAO().update(amMsuser);

			eventLogLogic
					.logUserEvent(
							amMsuser,
							GlobalVal.USEREVENT_ACTIVITY_LOGIN,
							GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_WRONG_PASSWORD,
							auditContext);

			throw new LoginException(this.messageSource.getMessage("businesslogic.login.invalididpassword", 
            		new Object[]{amMsuser.getLoginId()}, this.retrieveLocaleAudit(auditContext)), Reason.LOGIN_INVALID);
		} 
		else {
			// LOGIN - Fail Locked
			Date currentDate = new Date();
			amMsuser.setUsrUpd(GlobalVal.SYSTEM_LOGINID);
			amMsuser.setDtmUpd(currentDate);
			amMsuser.setIsLocked("1");
			amMsuser.setFailCount((amMsuser.getFailCount() == null ? 1 : amMsuser.getFailCount() + 1));
			amMsuser.setPrevLoggedFail(amMsuser.getLastLoggedFail());
			amMsuser.setLastLoggedFail(currentDate);
			this.getManagerDAO().update(amMsuser);

			eventLogLogic
					.logUserEvent(
							amMsuser,
							GlobalVal.USEREVENT_ACTIVITY_LOGIN,
							GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_ACCOUNT_LOCKED_OUT,
							auditContext);
			throw new LoginException(this.messageSource.getMessage("businesslogic.login.locked", 
            		new Object[]{amMsuser.getLoginId()}, this.retrieveLocaleAudit(auditContext)),
					Reason.LOGIN_LOCKED);
		}			

	}
	
	public boolean databaseAuth(String password, String cipherText) {
	    if (PasswordHash.validatePassword(password, cipherText)) {
            return true;
        } 
	    else {
            return false;
        }
	}
	
	public boolean ldapAuth(String user, String password) throws LdapException {
	    boolean ldapEnabled = BooleanUtils.toBoolean(SpringPropertiesUtils.getProperty(GlobalKey.LDAP_ENABLED));
	    if (!ldapEnabled) {
	        LOG.debug("LDAP Authentication disabled! Return true");
	        return true;
	    }
	    
	    return this.ldapLogic.authenticate(
	    		SpringPropertiesUtils.getProperty(GlobalKey.LDAP_DOMAIN) + "\\" + user, password, false);
	}

	@Override
	public Map retrieveAttributeOfMember(long uuidMsuser,
			AuditContext auditContext) {
		
		Map result = new HashMap<>();
		
		Map<String, Object> result1 = this.listMemberofGroup(uuidMsuser);
		List listMemberOfGroup = (List) result1.get(GlobalKey.MAP_RESULT_LIST);
		if (listMemberOfGroup != null) {

			Map mapAttributeOfMember = this.listAttributeofMember(uuidMsuser);

			result.put("mapAttrOfMember", mapAttributeOfMember);
		}
		
		return result;
	}

	@Override
	public int retrieveSessionTimeout(AuditContext auditContext) {
		// activate system time out
		String generalSettingSystemTimeout = globalLogic.getGsValue(
				GlobalKey.GENERALSETTING_SYSTEM_TIMEOUT, auditContext);
		int systemTimeout = NumberUtils.toInt(generalSettingSystemTimeout,
				DEFAULT_SYSTEM_TIMEOUT);

		return systemTimeout;
	}
	
	@Transactional(noRollbackFor={LoginException.class})
	@Override
	public List<Map> doMobileMultiLogin(String uniqueId, String password, AuditContext callerId) {
	
		if (StringUtils.isBlank(uniqueId) || StringUtils.isBlank(password)) {
			throw new LoginException("User ID or Password is required", Reason.LOGIN_REQUIRED);
		}
		
    	String[][] paramMultiLogin = { {"uniqueId", uniqueId} };
    	List<Map<String, Object>> listLoginId = this.getManagerDAO().selectAllNativeString("select LOGIN_ID from AM_MSUSER with (nolock) where UNIQUE_ID = :uniqueId and IS_DELETED = 0", paramMultiLogin);
    	if (listLoginId.size() == 1) {
    		String loginId = (String) listLoginId.get(0).get("d0");
    		List listUser = new ArrayList();
    		Map map = new HashMap<>();
    		map.put("d0", loginId);
    		listUser.add(map);
    		return listUser;
    	} else if (listLoginId.isEmpty()) {
    		throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
    	}   	
    	
    	Integer ttlIsActive = (Integer) this.getManagerDAO().selectOneNativeString("select COUNT(1) from AM_MSUSER with (nolock) where UNIQUE_ID = '"+uniqueId+"' and IS_ACTIVE = 1", null);
    	if (ttlIsActive == 0) {
    		throw new LoginException("Login failed. User " + uniqueId
					+ " is not active",
		            Reason.LOGIN_INACTIVE);
    	}
    	
    	Integer ttlIsNotLocked = (Integer) this.getManagerDAO().selectOneNativeString("select COUNT(1) from AM_MSUSER with (nolock) where UNIQUE_ID = '"+uniqueId+"' and IS_ACTIVE = 1 and IS_DELETED = 0 and IS_LOCKED != 1", null);
    	if (ttlIsNotLocked == 0) {
    		throw new LoginException("ID " + uniqueId
					+ " is locked. Please contact your Administrator.",
					Reason.LOGIN_LOCKED);
    	}
    	
    	if (listLoginId.isEmpty()) {
    		eventLogLogic.logUserEvent(
					null,
					GlobalVal.USEREVENT_ACTIVITY_LOGIN,
					GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_NO_SUCH_USER,
					callerId);
    		throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
    	}
    	
//    	String loginId = (String) this.getManagerDAO().selectOneNativeString("select top 1 LOGIN_ID from MS_CONFIGUSERLOGIN with (nolock) where NIK = :nik and ACTIVE = 1", paramMultiLogin);
    	String loginId = (String) listLoginId.get(0).get("d0");
    	if (null == loginId) {
    		eventLogLogic.logUserEvent(
					null,
					GlobalVal.USEREVENT_ACTIVITY_LOGIN,
					GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_NO_SUCH_USER,
					callerId);
    		throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
    	}
    	
    	String[] arrId = new String[listLoginId.size()];
		for (int i=0; i<listLoginId.size(); i++ ) {
			Map mapId = listLoginId.get(i);
			arrId[i] = (String) mapId.get("d0");
		}
		
		Object[][] params = {{Restrictions.eq(paramLoginId, StringUtils.upperCase(loginId))}};
		AmMsuser bean = this.getManagerDAO().selectOne(AmMsuser.class, params);
		Object[][] paramsUser = { {Restrictions.in(paramLoginId, arrId)} };
		Map mapUserLogin = this.getManagerDAO().selectAll(AmMsuser.class, paramsUser, null);
		List<AmMsuser> listUserLogin = (List) mapUserLogin.get(GlobalKey.MAP_RESULT_LIST);
//		AmMsuser bean = listUserLogin.get(0);
		            
        //cek Login ID
        if (bean == null) {
        	throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
        }
        
        this.getManagerDAO().fetch(bean.getAmMssubsystem());
        
        if (!("1").equals(bean.getAmMssubsystem().getIsActive())) {
        	eventLogLogic.logUserEvent(
        			bean,
					GlobalVal.USEREVENT_ACTIVITY_LOGIN,
					GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_SUBSYSTEM_INACTIVE,
					callerId);
			throw new LoginException("ID " + bean.getLoginId()
					+ " is registered in inactive subsystem. Please contact your Administrator.", 
					Reason.LOGIN_INACTIVE);
		}
		
		String canLogin = "0";
		String gsValue = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MAX_FAIL_COUNT, callerId);
		Integer maxFailCount =  Integer.parseInt(gsValue);
		
		this.getManagerDAO().fetch(bean.getAmMssubsystem());
        this.getManagerDAO().fetch(bean.getMsJob());
        
        Integer failCount = bean.getFailCount();
		if (failCount == null) { failCount = 0; }
               
		if (PropertiesHelper.isBypassLogin()) {
			canLogin = "1";
		}
		else {
			if (GlobalVal.FLAG_LOGIN_PROVIDER_NC.equals(bean.getLoginProvider())){
				boolean result = intFormLogic.authenticateUser(bean, password);
				canLogin = (result) ? "1" : "0";
			}
			else if (GlobalVal.FLAG_LOGIN_PROVIDER_AD.equals(bean.getLoginProvider())) {
			    boolean authenticated = this.ldapAuth(uniqueId, password);
			    canLogin = (authenticated) ? "1" : "0";
			}
			else {
				//cek username, password, dan imei, jika sama maka bisa login
				if (PasswordHash.validatePassword(password, bean.getPassword()) && loginId.toUpperCase().equals(bean.getLoginId().toUpperCase())){
					canLogin = "1";
				}
			}
		}
		
		boolean isPwdExp = false;
		if (this.isPwdExpired(bean.getUuidMsUser(), callerId) && !bean.getLoginProvider().equals(GlobalVal.NC)) {				
			isPwdExp = true;
		}
		
		List listUser = new ArrayList();
		if ("1".equals(canLogin)) {
			List<Map<String, Object>> listMapUserLogin = this.getManagerDAO().selectAllNative("am.login.getMultiLogin", paramMultiLogin, null);
			if (!listMapUserLogin.isEmpty()) {
				for (Map<String, Object> mp : listMapUserLogin) {
					Map newMap = new HashMap<>();
					String name = StringUtils.EMPTY;
					String branchCode = StringUtils.trim((String) mp.get("d1"));
					String branchName = StringUtils.trim((String) mp.get("d2"));
					String jobDescription = StringUtils.trim((String) mp.get("d3"));
					name = branchName + " (" + branchCode + ")" + " - " + jobDescription;
					newMap.put("d0", mp.get("d0"));
					newMap.put("d1", name);
					listUser.add(newMap);
				}
			}
			
			for (AmMsuser usr : listUserLogin) {
				usr.setDtmUpd(new Date());
				usr.setUsrUpd(callerId.getCallerId());
				usr.setIsLocked("0");
				usr.setFailCount(0);
				
				if (isPwdExp) {
					usr.setIsPasswordExpired("1");
					usr.setChangePwdLogin("1");
					eventLogLogic
							.logUserEvent(
									usr,
									GlobalVal.USEREVENT_ACTIVITY_LOGIN,
									GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_PASSWORD_EXPIRED,
									callerId);
				}
				this.getManagerDAO().update(usr);
			}
		} else {
			Date dateFailed = new Date();
			boolean isUserLocked = false;
			for (AmMsuser usr : listUserLogin) {
				if (isPwdExp) {
					usr.setIsPasswordExpired("1");
					usr.setChangePwdLogin("1");
					eventLogLogic
							.logUserEvent(
									usr,
									GlobalVal.USEREVENT_ACTIVITY_LOGIN,
									GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_PASSWORD_EXPIRED,
									callerId);
				}
				
				if (Integer.compare(maxFailCount-1, failCount) > 0) {
					usr.setFailCount((failCount == null ? 1 : failCount + 1));
					usr.setPrevLoggedFail(usr.getLastLoggedFail());
					usr.setLastLoggedFail(dateFailed);
				} else {
					isUserLocked = true;
					usr.setIsLocked("1");
					usr.setFailCount((failCount == null ? 1 : failCount + 1));
					usr.setPrevLoggedFail(usr.getLastLoggedFail());
					usr.setLastLoggedFail(dateFailed);
				}
				//update failCount
				usr.setDtmUpd(new Date());
				usr.setUsrUpd(callerId.getCallerId());
				this.getManagerDAO().update(usr);
			}
			
			if (isUserLocked) {
				eventLogLogic
				.logUserEvent(
						bean,
						GlobalVal.USEREVENT_ACTIVITY_LOGIN,
						GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_ACCOUNT_LOCKED_OUT,
						callerId);
				throw new LoginException("ID " + uniqueId
						+ " is locked. Please contact your Administrator.",
						Reason.LOGIN_LOCKED);
			}

			eventLogLogic
			.logUserEvent(
					bean,
					GlobalVal.USEREVENT_ACTIVITY_LOGIN,
					GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_WRONG_PASSWORD,
					callerId);

			throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
		}
		return listUser;
	}
	
	@Override
	public void doResetPassword(String loginId, String dob, String email,
			String ktpNo, AuditContext auditContext) {
		try {
			Date date = DateUtils.parseDate(dob, "yyyy-MM-dd");
			intFormLogic.resetPassword(loginId, date, email, ktpNo, auditContext);
		} catch (ParseException e) {
			throw new RuntimeException(e.getMessage());
		}
		
	}
	
	@Transactional(readOnly=true)
	@Override
	public AmMsuser doValidateUserEmbed(String loginId, String password, AuditContext auditContext) {
		if (StringUtils.isBlank(loginId) || StringUtils.isBlank(password)) {
			throw new LoginException(this.messageSource.getMessage("businesslogic.login.userpasswordrequired", 
					null, this.retrieveLocaleAudit(auditContext)), Reason.LOGIN_REQUIRED);
		}
		
		long uuid = this.getUuid(loginId);
		
		AmMsuser amMsuser = (AmMsuser) this.getUser(uuid);
		
		if (amMsuser == null) { //LoginID not found
			eventLogLogic.logUserEvent(
							amMsuser,
							GlobalVal.USEREVENT_ACTIVITY_LOGIN,
							GlobalVal.USEREVENT_CONSEQUENCE_LOGIN_FAIL_NO_SUCH_USER,
							auditContext);
			throw new LoginException(this.messageSource.getMessage("businesslogic.login.invalididpassword", 
					null, this.retrieveLocaleAudit(auditContext)), Reason.LOGIN_INVALID);
		}

		boolean authenticated = false;
		if (PropertiesHelper.isBypassLogin()) {
			authenticated = true;
		}
		else {
			if (LOGIN_PROVIDER_ACTIVE_DIRECTORY.equals(amMsuser.getLoginProvider())) {
				authenticated = this.ldapAuth(loginId, password);
			}
			else if (LOGIN_PROVIDER_NEW_CONFINS.equals(amMsuser.getLoginProvider())) {
				authenticated = this.intFormLogic.authenticateUser(amMsuser, password);
			}
			else if (LOGIN_PROVIDER_DATABASE.equals(amMsuser.getLoginProvider())) {
				authenticated = this.databaseAuth(password, amMsuser.getPassword());
			}
		}

		if (authenticated) {
			return amMsuser;
		} else {
			throw new LoginException(this.messageSource.getMessage("Not Authorized", 
            		new Object[]{amMsuser.getLoginId()}, this.retrieveLocaleAudit(auditContext)), Reason.LOGIN_INVALID);
		}
	}
}
