package com.adins.mss.businesslogic.impl.am;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.PermissionLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;

@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericPermissionLogic extends BaseLogic implements PermissionLogic {

	//fix: 2018-01-11 SUM argument loginId diubah jadi <PERSON>, agar tidak select job lagi
	@Override
	public List<Map<String,Object>> getPermissions(AmMsuser userLogin, AuditContext callerId) {
		if (userLogin == null){
			return Collections.emptyList();
		}
		
		Object[][] params = {{"loginId", userLogin.getLoginId()}};
		List<Map<String,Object>> result = new ArrayList<>();
		if (GlobalVal.JOB_ADMIN.equalsIgnoreCase(userLogin.getMsJob().getJobCode())) {
			result = this.getManagerDAO().selectAllNative("am.permission.getpermissionsadmin", null, null);
		}
		else {
			result = this.getManagerDAO().selectAllNative("am.permission.getpermissions", params, null);
		}
		
		return result;
	}
}
