package com.adins.mss.businesslogic.impl.am;

import java.util.Date;
import java.util.Map;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.SubsystemLogic;
import com.adins.mss.model.AmMssubsystem;

public class GenericSubsystemLogic extends BaseLogic implements SubsystemLogic {
	
	public Map<String, Object> listSubsystem(Object params, Object orders, int pageNumber, int pageSize, AuditContext callerId) {
		
		Map<String, Object> result = this.getManagerDAO().selectAll(AmMssubsystem.class, params, orders, pageNumber, pageSize); 
		return result;
	}

	public String insertSubsystem(AmMssubsystem obj, AuditContext callerId) {
		
		AmMssubsystem amMssubsystem = (AmMssubsystem) obj;
		String uuid = this.getManagerDAO().getUUID();
		amMssubsystem.setDtmCrt(new Date());
		amMssubsystem.setDtmUpd(new Date());

		this.getManagerDAO().insert(amMssubsystem);
		String result = uuid;
			
		return result;
	}

	public String updateSubsystem(AmMssubsystem obj, String uuid, AuditContext callerId) {

		AmMssubsystem amMssubsystem = (AmMssubsystem) obj;
		amMssubsystem.setDtmUpd(new Date());

		this.getManagerDAO().update(amMssubsystem);
		String result = uuid;

		return result;
	}

	public String deleteSubsystem(AmMssubsystem obj, String uuid, AuditContext callerId) {
		
		AmMssubsystem amMssubsystem = (AmMssubsystem) obj;
		amMssubsystem.setDtmUpd(new Date());

		this.getManagerDAO().delete(amMssubsystem);
		String result = uuid;
			
		return result;
	}
}
