package com.adins.mss.businesslogic.impl.am;

import java.sql.Types;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.ToDoListLogic;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.custom.ToDoBean;

@SuppressWarnings("rawtypes")
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericToDoListLogic extends BaseLogic implements ToDoListLogic{

	@Override
	public List<ToDoBean> viewToDoList(AmMsuser user, AuditContext callerId) throws Exception {
		this.getManagerDAO().fetch(user.getMsBranch());
		String isPiloting = StringUtils.EMPTY;
		if (StringUtils.isNotBlank(user.getMsBranch().getIsPiloting())) {
			isPiloting = user.getMsBranch().getIsPiloting();
		} else {
			isPiloting = "0";
		}
		List<ToDoBean> result = new ArrayList<ToDoBean>();
			
		String[][] params = { { "uuidMsuser", String.valueOf(user.getUuidMsUser()) }, {"isPiloting", isPiloting} };
		List list = (List) this.getManagerDAO().selectAllNative("am.layout.toDoList", params, null);
		
		for (int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);
			
			ToDoBean bean = new ToDoBean();
			bean.setToDoListName(temp.get("d0").toString());
			bean.setToDoListUri(temp.get("d1").toString());
			bean.setCnt(this.getCount(temp.get("d2").toString(), user));
				
			result.add(bean);
		}
	
		return result;
	}
	
	private int getCount(String stmt, AmMsuser user) {
		
		int result = 0;
		Object[][] params = { {1, user.getUuidMsUser()}, { 2, user.getMsBranch().getUuidBranch() },
				{ 3, user.getAmMssubsystem().getSubsystemName() } };
		Object[][] outputs = { {4, Types.INTEGER} };
		Map<String, ?> map = this.getManagerDAO().callProcedureNativeString(stmt, params, outputs);
		
		result = (Integer) map.get("o1");
		return result;
	}
}
