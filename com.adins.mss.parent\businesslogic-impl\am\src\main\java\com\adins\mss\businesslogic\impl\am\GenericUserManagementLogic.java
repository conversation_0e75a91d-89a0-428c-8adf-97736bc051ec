package com.adins.mss.businesslogic.impl.am;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.framework.tool.password.PasswordHash;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.am.UserManagementLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ChangePasswordType;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.exceptions.UploadTaskException;
import com.adins.mss.exceptions.UploadTaskException.Reason;
import com.adins.mss.exceptions.UserManagementException;
import com.adins.mss.exceptions.UserManagementException.ReasonUser;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMemberofgroup;
import com.adins.mss.model.AmMsgroup;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.AmUserpwdhistory;
import com.adins.mss.model.MsArea;
import com.adins.mss.model.MsAreaofbranch;
import com.adins.mss.model.MsAreaofuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsDealer;
import com.adins.mss.model.MsGroupofjob;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsRoundrobintask;
import com.adins.mss.model.MsZipcodeofbranch;
import com.adins.mss.model.MsZipcodeofuser;
import com.adins.mss.model.custom.UserManagementBean;
import com.adins.mss.util.MssTool;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericUserManagementLogic extends BaseLogic implements
		UserManagementLogic, MessageSourceAware {
	
	private IntFormLogic intFormLogic;
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	private AuditInfo auditInfo;
	private AuditInfo auditInfoMemberOfGroup;
	private AuditInfo auditInfoZipcodeOfUser;
	private AuditInfo auditInfoAreaOfUser;
	private static final int NUMBER_ROW_IN_VALIDATION = 1000;
	private static final int BRANCH_COLUMN = 3;
	private static final int JOB_COLUMN = 2;
	private static final int DEALER_COLUMN = 4;

	private static final String[] TEMPLATE_HEADER_DATAMASTER_MO = {
			GlobalVal.HEADER_BRANCH_CODE, GlobalVal.HEADER_BRANCH_DESCRIPTION,
			GlobalVal.HEADER_NEW_COLUMN, GlobalVal.HEADER_JOB_CODE,
			GlobalVal.HEADER_JOB_DESCRIPTION, GlobalVal.HEADER_NEW_COLUMN, 
			GlobalVal.HEADER_TRACKING_FLAG, GlobalVal.HEADER_NEW_COLUMN, 
			GlobalVal.HEADER_DEALER_CODE, GlobalVal.HEADER_DEALER_NAME };

	private static final String[] TEMPLATE_HEADER_DATAMASTER = {
			GlobalVal.HEADER_BRANCH_CODE, GlobalVal.HEADER_BRANCH_DESCRIPTION,
			GlobalVal.HEADER_NEW_COLUMN, GlobalVal.HEADER_JOB_CODE,
			GlobalVal.HEADER_JOB_DESCRIPTION, GlobalVal.HEADER_NEW_COLUMN, GlobalVal.HEADER_TRACKING_FLAG};

	private static final String[] TEMPLATE_HEADER_MO = { "USER ID *)",
			"User Name *)", "Job *)", "Branch *)", "Dealer *)", "User Manager",
			"Phone", "Email", "IMEI", "IMEI 2", "Tracking Flag", "Start Time (Format: 'hh:mm Ex: '20:00)",
			"End Time (Format: 'hh:mm Ex: '20:00)", "Tracking Days (Ex: Sunday;Monday)" };

	private static final String[] TEMPLATE_HEADER_MS = { "USER ID *)",
			"User Name *)", "Job *)", "Branch *)", "User Manager", "Phone",
			"Email", "IMEI", "IMEI 2", "Tracking Flag", "Start Time (Format: 'hh:mm Ex: '20:00)",
			"End Time (Format: 'hh:mm Ex: '20:00)", "Tracking Days (Ex: Sunday;Monday)"  };

	private static final String[] TEMPLATE_HEADER_MC = { "USER ID *)",
			"User Name *)", "Job *)", "Branch *)", "User Manager",
			"Initial Name *)", "Phone", "Email", "IMEI", "IMEI 2",
			"Tracking Flag", "Start Time (Format: 'hh:mm Ex: '20:00)", "End Time (Format: 'hh:mm Ex: '20:00)", "Tracking Days (Ex: Sunday;Monday)"  };

	private static final String[] TEMPLATE_HEADER_MC_COH = { "USER ID *)",
			"User Name *)", "Job *)", "Branch *)", "User Manager",
			"Initial Name *)", "Phone", "Email", "IMEI", "IMEI 2", "Limit COH",
			"Tracking Flag", "Start Time (Format: 'hh:mm Ex: '20:00)", "End Time (Format: 'hh:mm Ex: '20:00)", "Tracking Days (Ex: Sunday;Monday)"  };

	// ERROR TEMPLATE
	private static final String[] TEMPLATE_HEADER_ERROR_MO = { "USER ID *)",
			"User Name *)", "Job *)", "Branch *)", "Dealer *)", "User Manager",
			"Phone", "Email", "IMEI", "IMEI 2", "Tracking Flag", "Start Time",
			"End Time", "Tracking Days", "Error"  };

	private static final String[] TEMPLATE_HEADER_ERROR_MS = { "USER ID *)",
			"User Name *)", "Job *)", "Branch *)", "User Manager", "Phone",
			"Email", "IMEI", "IMEI 2", "Tracking Flag", "Start Time",
			"End Time", "Tracking Days", "Error"  };

	private static final String[] TEMPLATE_HEADER_ERROR_MC = { "USER ID *)",
			"User Name *)", "Job *)", "Branch *)", "User Manager",
			"Initial Name *)", "Phone", "Email", "IMEI", "IMEI 2",
			"Tracking Flag", "Start Time", "End Time", "Tracking Days", "Error"  };

	private static final String[] TEMPLATE_HEADER_ERROR_MC_COH = {
			"USER ID *)", "User Name *)", "Job *)", "Branch *)",
			"User Manager", "Initial Name *)", "Phone", "Email", "IMEI",
			"IMEI 2", "Limit COH", "Tracking Flag", "Start Time", "End Time", 
			"Tracking Days", "Error"  };

	private static final int[] HEADER_COLUMN_WIDTH_MO = { 15 * 256, 20 * 256,
			15 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256 };

	private static final int[] HEADER_COLUMN_WIDTH_MS = { 15 * 256, 20 * 256,
			15 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256 };

	private static final int[] HEADER_COLUMN_WIDTH_MC = { 15 * 256, 20 * 256,
			15 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256 };

	private static final int[] HEADER_COLUMN_WIDTH_MC_COH = { 15 * 256,
			20 * 256, 15 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256 };

	private static final int[] HEADER_COLUMN_WIDTH_ERROR_MO = { 15 * 256,
			20 * 256, 15 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256 };

	private static final int[] HEADER_COLUMN_WIDTH_ERROR_MS = { 15 * 256,
			20 * 256, 15 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256 };

	private static final int[] HEADER_COLUMN_WIDTH_ERROR_MC = { 15 * 256,
			20 * 256, 15 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256 };

	private static final int[] HEADER_COLUMN_WIDTH_ERROR_MC_COH = { 15 * 256,
			20 * 256, 15 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256 };
	
	private static final String[] DAYS = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday",
		"Friday", "Saturday" };

	public GenericUserManagementLogic() {
		String[] pkCols = { "uuidMsUser" };
		String[] pkDbCols = { "UUID_MS_USER" };
		String[] cols = { "uuidMsUser", "loginId", "fullName",
				"msJob.uuidJob", "msBranch.uuidBranch", "msDealer.uuidDealer",
				"amMsuser", "email", "isActive", "imei", "phone",
				"maxTaskLoad", "usrCrt", "dtmCrt", "failCount",
				"loginProvider", "isLoggedIn", "isLocked", "isPasswordExpired",
				"isDormant", "amMssubsystem.uuidMsSubsystem", "changePwdLogin",
				"imei2", "cashLimit", "isDeleted",
				"isTracking", "trackingStartTime", "trackingEndTime", "trackingDays"};
		String[] dbCols = { "UUID_MS_USER", "LOGIN_ID", "FULL_NAME",
				"UUID_JOB", "UUID_BRANCH", "UUID_DEALER", "SPV_ID",
				"email", "IS_ACTIVE", "IMEI", "PHONE", "MAX_TASK_LOAD",
				"USR_CRT", "DTM_CRT", "FAIL_COUNT", "LOGIN_PROVIDER",
				"IS_LOGGED_IN", "IS_LOCKED", "IS_PASSWORD_EXPIRED",
				"IS_DORMANT", "UUID_MS_SUBSYSTEM", "CHANGE_PWD_LOGIN", "IMEI2",
				"CASH_LIMIT", "IS_DELETED",
				"IS_TRACKING", "TRACKING_START_TIME", "TRACKING_END_TIME", "TRACKING_DAYS"};
		this.auditInfo = new AuditInfo("AM_MSUSER", pkCols, pkDbCols, cols,
				dbCols);

		String[] pkColsMoG = { "uuidMemberOfGroup" };
		String[] pkDbColsMoG = { "UUID_MEMBER_OF_GROUP" };
		String[] colsMoG = { "uuidMemberOfGroup", "amMsgroup.uuidMsGroup",
				"amMsuser.uuidMsUser" };
		String[] dbColsMoG = { "UUID_MEMBER_OF_GROUP", "UUID_MS_GROUP",
				"UUID_MS_USER" };
		this.auditInfoMemberOfGroup = new AuditInfo("AM_MEMBEROFGROUP",
				pkColsMoG, pkDbColsMoG, colsMoG, dbColsMoG);

		String[] pkColsZoU = { "uuidZipCodeOfUser" };
		String[] pkDbColsZoU = { "UUID_ZIP_CODE_OF_USER" };
		String[] colsZou = { "uuidZipCodeOfUser", "zipCode",
				"amMsuser.uuidMsUser" };
		String[] dbColsZoU = { "UUID_ZIP_CODE_OF_USER", "ZIP_CODE",
				"UUID_MS_USER" };
		this.auditInfoZipcodeOfUser = new AuditInfo("MS_ZIPCODEOFUSER",
				pkColsZoU, pkDbColsZoU, colsZou, dbColsZoU);

		String[] pkColsAoU = { "uuidAreaOfUser" };
		String[] pkDbColsAoU = { "UUID_AREA_OF_USER" };
		String[] colsAou = { "uuidAreaOfUser", "msArea.uuidArea",
				"amMsuser.uuidMsUser" };
		String[] dbColsAoU = { "UUID_AREA_OF_USER", "UUID_AREA", "UUID_MS_USER" };
		this.auditInfoAreaOfUser = new AuditInfo("MS_AREAOFUSER", pkColsAoU,
				pkDbColsAoU, colsAou, dbColsAoU);

	}

	private static final Logger LOG = LoggerFactory
			.getLogger(GenericUserManagementLogic.class);

	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	@Autowired
	private GlobalLogic globalLogic;

	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}

	@Override
	public Map<String, Object> listUserManagement(Object params, int pageNumber, 
			int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;
		
		String[][] tmpParams = (String[][]) params;
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();

		// subsystem
		Object[][] prm = { { Restrictions.eq("subsystemName",
				tmpParams[7][1]) } };
		AmMssubsystem amMsSubsystem = this.getManagerDAO().selectOne(
				AmMssubsystem.class, prm);
		condition.append(" and amu.amMssubsystem.uuidMsSubsystem = :uuidMsSubsystem");
		paramMap.put("uuidMsSubsystem", amMsSubsystem.getUuidMsSubsystem());
		
		// isDeleted
		condition.append(" and amu.isDeleted = :isDeleted");
		paramMap.put("isDeleted", tmpParams[13][1]);

		// fullName
		if (cekParam(tmpParams, 0)) {
			condition.append(" and (amu.fullName like :fullName or amu.loginId like :fullName or amu.imei like :fullName)");
			paramMap.put("fullName", "%" + tmpParams[0][1] + "%");
		}

		// loginId
		if (cekParam(tmpParams, 1)) {
			condition.append(" and amu.loginId like :loginId");
			paramMap.put("loginId", "%" + tmpParams[1][1] + "%");
		}

		// job
		if (cekParam(tmpParams, 2)) {
			condition.append(" and amu.msJob.uuidJob = :uuidJob");
			paramMap.put("uuidJob", Long.valueOf(tmpParams[2][1]));
		} 
//		else {
//			if (!GlobalVal.JOB_ADMIN.equals(tmpParams[9][1])) {
//				String[] jobByLogin = getJobByLogin(tmpParams[9][1], callerId);
//				long [] largument = MssTool.strArrayToLongArray(jobByLogin);
//				Long job [] = new Long[largument.length];
//				int i=0;
//
//				for(long temp:largument){
//					job[i++] = temp;
//				}
//				if (jobByLogin != null && cekParam(tmpParams, 9, callerId)){
//					condition.append(" and amu.msJob.uuidJob in (:uuidJob)");
//					paramMap.put("uuidJob", job);
//				}
//			}
//		}

		if (GlobalVal.SUBSYSTEM_MO.equals(tmpParams[7][1])) {
			if (cekParam(tmpParams, 3)) {
				// branch
				Long branchSearch = Long.valueOf(tmpParams[3][1]);
				if(!"%".equals(tmpParams[3][1])){
					condition.append(" and amu.msBranch.uuidBranch = :uuidBranch");
					paramMap.put("uuidBranch", branchSearch);
				}
			} 
			else if (cekParam(tmpParams, 4)) {
				// dealer
				Long dealerSearch = Long.valueOf(tmpParams[4][1]);
				
				if(!"%".equals(tmpParams[4][1])){
					condition.append(" and amu.msDealer.uuidDealer = :uuidDealer");
					paramMap.put("uuidDealer", dealerSearch);
				}
			} 
			else {
				if (!GlobalVal.JOB_ADMIN.equals(tmpParams[9][1])) {
					if ("1".equals(tmpParams[12][1])) {
						String[] branchByLogin = getBranchByLogin(Long.valueOf(tmpParams[10][1]));
						long [] largument = MssTool.strArrayToLongArray(branchByLogin);
						Long branch [] = new Long[largument.length];
						int i=0;

						for(long temp:largument){
							branch[i++] = temp;
						}
						if (branchByLogin != null
								&& cekParam(tmpParams, 10)){
							condition.append(" and amu.msBranch.uuidBranch in (:uuidBranch)");
							paramMap.put("uuidBranch", branch);
						}
					}
					if ("0".equals(tmpParams[12][1])) {
						String[] dealerByLogin = getDealerByLogin(Long.valueOf(tmpParams[11][1]));
						long [] largument = MssTool.strArrayToLongArray(dealerByLogin);
						Long dealer [] = new Long[largument.length];
						int i=0;

						for(long temp:largument){
							dealer[i++] = temp;
						}
						if (dealerByLogin != null
								&& cekParam(tmpParams, 11)){
							condition.append(" and amu.msDealer.uuidDealer in (:uuidDealer)");
							paramMap.put("uuidDealer", dealer);
						}
					}
				}
			}
		} 
		else {
			// branch
			if (cekParam(tmpParams, 3)) {
				if(!"%".equals(tmpParams[3][1])){
					condition.append(" and amu.msBranch.uuidBranch = :uuidBranch");
					paramMap.put("uuidBranch", Long.valueOf(tmpParams[3][1]));
				}
			} 
			else {
				String[] branchByLogin = getBranchByLogin(Long.valueOf(tmpParams[10][1]));
				long [] largument = MssTool.strArrayToLongArray(branchByLogin);
				Long branch [] = new Long[largument.length];
				int i=0;

				for(long temp:largument){
					branch[i++] = temp;
				}
				if (branchByLogin != null && cekParam(tmpParams, 10)){
					condition.append(" and amu.msBranch.uuidBranch in (:uuidBranch)");
					paramMap.put("uuidBranch", branch);
				}
			}
		}

		// isActive
		if (cekParam(tmpParams, 5)) {
			condition.append(" and amu.isActive = :isActive");
			paramMap.put("isActive", tmpParams[5][1]);
		}

		// isLogin
		if (cekParam(tmpParams, 6)) {
			condition.append(" and amu.isLoggedIn = :isLoggedIn");
			paramMap.put("isLoggedIn", tmpParams[6][1]);
		}

		// user
		Object[][] paramsAdmin = { 
				{Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MS+GlobalKey.GENERALSETTING_JOBADMIN)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsAdmin);
		if (!ArrayUtils.contains(StringUtils.split(amGeneralsetting.getGsValue(), ";"), tmpParams[9][1])) {
			String[] userByLogin = getUserByLogin(Long.valueOf(tmpParams[8][1]));
			long [] largument = MssTool.strArrayToLongArray(userByLogin);
			Long user [] = new Long[largument.length];
			int i=0;

			for(long temp:largument){
				user[i++] = temp;
			}
			if (ArrayUtils.getLength(userByLogin) == 1) {
				condition.append(" and amu.uuidMsUser = :uuidMsUser");
				paramMap.put("uuidMsUser", Long.valueOf(tmpParams[8][1]));
			} 
			else if (userByLogin != null && cekParam(tmpParams, 8)) {
				condition.append(" and amu.uuidMsUser in (:uuidMsUser)");
				paramMap.put("uuidMsUser", user);
			}
		}
		
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by amu.isActive desc, amu.loginId asc");

		result = this.getManagerDAO().selectAll(
				"from AmMsuser amu join fetch amu.amMssubsystem ms join fetch amu.msJob mj join fetch amu.msBranch mb join fetch amu.msDealer md where 1=1"
						+ condition.toString() + orderQuery.toString(),
				"select count(*) from AmMsuser amu join amu.amMssubsystem ms join amu.msJob mj join amu.msBranch mb join amu.msDealer md where 1=1"
						+ condition.toString(),
				paramMap, pageNumber, pageSize);
		List<AmMsuser> listUser = (List) result
				.get(GlobalKey.MAP_RESULT_LIST);

		if (listUser != null) {
			for (AmMsuser amMsuser : listUser) {
				amMsuser.setPassword(null);
				amMsuser.setCashLimit(amMsuser.getCashLimit());
			}
		}
		result.put(GlobalKey.MAP_RESULT_LIST, listUser);
			
		return result;
	}

	public boolean cekParam(String[][] params, int x) {
		
		return (params[x].length == 2) && (params[x][0] != null)
				&& (!"".equals(params[x][0])) && (params[x][1] != null)
				&& (!"".equals(params[x][1]) && (!"%".equals(params[x][1])));
	}

	public String[] getJobByLogin(String uuid) {
		Object[][] params = { { Restrictions.eq("jobCode", uuid) } };
		MsJob bean = this.getManagerDAO().selectOne(MsJob.class, params);

		Object[][] param = { { "uuidJob", bean.getUuidJob() } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getJobByLogin", param, null);
		if (!list.isEmpty()) {
			String[] stringResult = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = map.get("d0").toString();
			}
			return stringResult;
		} 
		else {
			return null;
		}

	}

	public String[] getBranchByLogin(long uuid) {
		Object[][] params = { { "uuidBranch", uuid } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getBranchByLogin", params, null);
		
		if (!list.isEmpty()) {
			String[] stringResult = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = map.get("d0").toString();
			}
			return stringResult;
		} 
		else {
			return null;
		}

	}

	public String[] getDealerByLogin(long uuid) {
		Object[][] params = { { "uuidDealer", uuid } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getDealerByLogin", params, null);
		
		if (!list.isEmpty()) {
			String[] stringResult = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = map.get("d0").toString();
			}
			return stringResult;
		} 
		else {
			return null;
		}

	}

	public String[] getUserByLogin(long uuid) {
		Object[][] params = { { "uuidMsUser", uuid } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getUserByLoginAll", params, null);
		if (!list.isEmpty()) {
			String[] stringResult = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = map.get("d0").toString();
			}
			return stringResult;
		} 
		else {
			return null;
		}

	}

	public String getUserZipCode(long uuid) {
		Object[][] params = { { Restrictions.eq("amMsuser.uuidMsUser", uuid) } };
		Map<String, Object> result = this.getManagerDAO().list(MsZipcodeofuser.class, params, null);
		List<MsZipcodeofuser> listUserZipCode = (List) result
				.get(GlobalKey.MAP_RESULT_LIST);

		String stringResult = "";
		if (listUserZipCode != null && !listUserZipCode.isEmpty()) {
			for (MsZipcodeofuser bean : listUserZipCode) {
				stringResult += bean.getZipCode() + ",";
			}
			if (stringResult.length() > 1) {
				stringResult = stringResult.substring(0,
						stringResult.length() - 1);
			}
		}
		return stringResult;
	}

	public String getAreaOfUser(long uuid) {
		Map<String, Object> result = this.getManagerDAO().list(
			"from MsAreaofuser maou join fetch maou.msArea ma "
			+ "join fetch maou.amMsuser mu where mu.uuidMsUser = :uuidMsUser", 
			new Object[][] {{"uuidMsUser", uuid}});
		List<MsAreaofuser> list = (List) result.get(GlobalKey.MAP_RESULT_LIST);
		
		String stringResult = "";
		if (list != null && !list.isEmpty()) {
			for (MsAreaofuser bean : list) {
				stringResult += bean.getMsArea().getAreaName() + ",";
			}
			if (stringResult.length() > 1) {
				stringResult = stringResult.substring(0,
						stringResult.length() - 1);
			}
		}
		return stringResult;
	}

	@Override
	public UserManagementBean getUserManagementDetail(long uuid, String task,
			AuditContext callerId) {
		UserManagementBean result = new UserManagementBean();
		
		AmMsuser bean = this.getManagerDAO()
				.selectOne(
					"from AmMsuser u join fetch u.msJob join fetch u.msBranch "
					+ "join fetch u.msDealer left join fetch u.amMsuser "
					+ "where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", uuid}});

		if (bean.getCashLimit() == null) {
			bean.setCashLimit(new BigDecimal(0));
		}

		result.setBean(bean);
		result.setZipCode(getUserZipCode(bean.getUuidMsUser()));
		result.setAreaCode(getAreaOfUser(bean.getUuidMsUser()));
		if ("1".equals(bean.getMsJob().getIsBranch())) {
			result.setZipCodeBranch(getBranchZipCode(bean.getMsBranch()
					.getUuidBranch(), callerId));
		} 
		else {
			result.setZipCodeBranch("");
		}
		
		String trackingDays = result.getBean().getTrackingDays();
		if (StringUtils.isNotBlank(trackingDays)) {
			if(!GlobalVal.ACTION_NAME_DEFAULT_URI_EDIT.equalsIgnoreCase(task)){
				String[] trackDays = trackingDays.split(";");
				int i = 0;
				for (String numberDays : trackDays) {
					trackDays[i] = DAYS[Integer.parseInt(numberDays)];
					i++;
				}
				trackingDays = StringUtils.join(trackDays, ";");
			}
			result.getBean().setTrackingDays(trackingDays);
		}

		return result;
	}

	@Override
	public AmMsuser getUserManagement(long uuid, AuditContext callerId) {
		 
		AmMsuser result = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.msJob join fetch u.msBranch left join fetch u.amMsuser "
			+ "where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", uuid}});
		
		return result;
	}

	private void insertPwdHistory(String newPassword, AmMsuser amMsuser,
			String changeType, AuditContext callerId) {

		AmUserpwdhistory obj = new AmUserpwdhistory();
		obj.setDtmCrt(new Date());
		obj.setUsrCrt(callerId.getCallerId());
		obj.setAmMsuser(amMsuser);
		obj.setPassword(newPassword);
		obj.setChangeType(changeType);
		this.getManagerDAO().insert(obj);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertUserManagement(UserManagementBean obj,
			AuditContext callerId, long uuidMsSubsystem, boolean isNC) {

		AmMsuser userLoginBean = this.getManagerDAO().selectOne(
				AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		AmMsuser amMsUser = obj.getBean();

		if (amMsUser.getMaxTaskLoad()==null) {
			String maxTaskLoad = globalLogic.getGsValue(GlobalKey.GENERALSETTING_DEFAULT_MAX_TASK_LOAD, 
					callerId);
			amMsUser.setMaxTaskLoad(Integer.parseInt(maxTaskLoad));
		}
		
		if (amMsUser.getIsTracking().equals("1")) {
			if (StringUtils.isEmpty(amMsUser.getTrackingStartTime())) {
				String start = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_START_TIME, callerId);
				amMsUser.setTrackingStartTime(start);
			}
			if (StringUtils.isEmpty(amMsUser.getTrackingEndTime())) {
				String end = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_END_TIME, callerId);
				amMsUser.setTrackingEndTime(end);
			}
			if (StringUtils.isEmpty(amMsUser.getTrackingDays())) {
				String track = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_DAYS, callerId);
				amMsUser.setTrackingDays(track);
			}
		}
		
		if (userLoginBean != null) {
			if (!this.isJobList(userLoginBean.getMsJob().getUuidJob(),
					String.valueOf(amMsUser.getMsJob().getUuidJob()))) {
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.usermanagement.unauthorizedoperation",null,
						this.retrieveLocaleAudit(callerId)), Reason.ERROR_EXIST);
			}
		}

		if (this.isLoginIdValid(amMsUser.getLoginId())) {
			amMsUser.setPassword(PasswordHash.createHash(globalLogic
					.getGsValue(GlobalKey.GENERALSETTING_DEFAULT_PASSWORD, callerId)));
			amMsUser.setUsrCrt(callerId.getCallerId());
			amMsUser.setDtmCrt(new Date());

			if (GlobalVal.BRANCH_HO.equals(amMsUser.getMsBranch()
					.getUuidBranch())) {
				Object[][] param = { { Restrictions.eq("branchName",
						GlobalVal.BRANCH_HO) } };
				amMsUser.setMsBranch(this.getManagerDAO().selectOne(
						MsBranch.class, param));
			}

			if (GlobalVal.DEALER_HO.equals(amMsUser.getMsDealer()
					.getUuidDealer())) {
				Object[][] param = { { Restrictions.eq("dealerName",
						GlobalVal.DEALER_HO) } };
				amMsUser.setMsDealer(this.getManagerDAO().selectOne(
						MsDealer.class, param));
			}

			if (StringUtils.isNotBlank(String.valueOf(obj.getBean().getAmMsuser()
					.getUuidMsUser()))) {
				AmMsuser dbModel = this.getManagerDAO().selectOne(
						AmMsuser.class,
						amMsUser.getAmMsuser().getUuidMsUser());
				amMsUser.setAmMsuser(dbModel);
			} 
			else {
				amMsUser.setAmMsuser(null);
			}

			amMsUser.setLoginId(amMsUser.getLoginId().toUpperCase());
			amMsUser.setFailCount(0);
			if (isNC) {
				amMsUser.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_NC);
				amMsUser.setChangePwdLogin("0");
			} 
			else {
				amMsUser.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
				amMsUser.setChangePwdLogin("1");
			}
			amMsUser.setIsLoggedIn("0");
			amMsUser.setIsLocked("0");
			amMsUser.setIsPasswordExpired("0");
			amMsUser.setIsDormant("0");
			amMsUser.setIsDeleted("0");

			amMsUser.setImei(StringUtils.isEmpty(obj.getBean().getImei()) ? obj
					.getBean().getImei() : obj.getBean().getImei()
					.toUpperCase());
			amMsUser.setImei2(StringUtils.isEmpty(obj.getBean().getImei2()) ? obj
					.getBean().getImei2() : obj.getBean().getImei2()
					.toUpperCase());

			if (!isNC) {
				amMsUser.setIsActive("1");
			}
			
			AmMssubsystem subsystem = this.getManagerDAO().selectOne(
					AmMssubsystem.class, uuidMsSubsystem);
			amMsUser.setAmMssubsystem(subsystem);

			if (GlobalVal.SUBSYSTEM_MC.equals(subsystem.getSubsystemName())) {
				if ("1".equals(getLimitCohEnabled(
						GlobalKey.GENERALSETTING_LIMIT_COH_ENABLED,
						callerId))) {
					if (null == amMsUser.getCashLimit()
							|| "".equals(amMsUser.getCashLimit())) {
						amMsUser.setCashLimit(new BigDecimal(0));
					}
					amMsUser.setCashOnHand(new BigDecimal (0));
				} 
				else {
					amMsUser.setCashLimit(null);
					amMsUser.setCashOnHand(new BigDecimal(0));
				}

				// initial gk boleh ada yang sama
				if (!"".equals(obj.getBean().getInitialName())
						&& obj.getBean().getInitialName() != null) {
					Object[][] paramInitial = { { Restrictions.eq(
							"initialName", amMsUser.getInitialName()
									.toUpperCase()) } };
					AmMsuser dbInitial = this.getManagerDAO().selectOne(
							AmMsuser.class, paramInitial);

					if (dbInitial != null) {
						throw new EntityNotUniqueException(this.messageSource.getMessage(
								"businesslogic.usermanagement.initialexist",
								new Object[] { amMsUser.getInitialName().toUpperCase() }, this.retrieveLocaleAudit(callerId))
								, amMsUser.getInitialName().toUpperCase());
					} 
					else {
						amMsUser.setInitialName(amMsUser.getInitialName()
								.toUpperCase());
					}
				}
				// end initial gk boleh ada yg sama
			}

			this.getManagerDAO().insert(amMsUser);
			this.auditManager.auditAdd(amMsUser, auditInfo, callerId.getCallerId(), "");
			AmMsuser beanAmMsuser = this.getManagerDAO().selectOne(
					AmMsuser.class, amMsUser.getUuidMsUser());

			// insert member of group based on jobvsgroup
			Map<String, Object> mapGroupOfJob = getGroupOfJob(amMsUser
					.getMsJob().getUuidJob());
			Long countGroupOfJob = (Long) mapGroupOfJob
					.get(GlobalKey.MAP_RESULT_SIZE);
			if (countGroupOfJob > 0) {
				List<MsGroupofjob> listGroupOfJob = (List<MsGroupofjob>) mapGroupOfJob
						.get(GlobalKey.MAP_RESULT_LIST);
				for (int i = 0; i < listGroupOfJob.size(); i++) {
					MsGroupofjob bean = listGroupOfJob.get(i);
					insertMemberOfGroup(bean.getAmMsgroup(), beanAmMsuser,
							callerId);
				}
			}

			// insert zipcode of user
			if (obj.getZipCode() != null && !"".equals(obj.getZipCode())) {
				String[] zipCodes = obj.getZipCode().split(",");

				for (int i = 0; i < zipCodes.length; i++) {
					insertZipCodeOfUser(zipCodes[i], beanAmMsuser, callerId);
				}
			}

			// insert area of user
			if (obj.getAreaCode() != null && !"".equals(obj.getAreaCode())) {
				String[] area = obj.getAreaCode().split(",");
				for (int i = 0; i < area.length; i++) {
					insertAreaOfUser(Long.valueOf(area[i]), beanAmMsuser, callerId);
				}
			} 
			else {
				if (GlobalVal.SUBSYSTEM_MS.equals(subsystem
						.getSubsystemName())
						|| GlobalVal.SUBSYSTEM_MC.equals(subsystem
								.getSubsystemName())) {
					MsJob job = this.getManagerDAO().selectOne(MsJob.class,
							amMsUser.getMsJob().getUuidJob());
					if ("1".equals(job.getIsBranch())) {
						String area = getAreaBranch(amMsUser.getMsBranch()
								.getUuidBranch());
						if (!area.isEmpty()){
							insertAreaOfUser(Long.valueOf(area), beanAmMsuser, callerId);
						}
					}
				}
			}

			// insert ms_lov if job mh subsytem order
			if (GlobalVal.SUBSYSTEM_MS.equals(subsystem.getSubsystemName())) {
				String gsVal = globalLogic.getGsValue(
						GlobalKey.GENERALSETTING_MO_MHJOB, callerId);
				MsJob job = this.getManagerDAO().selectOne(MsJob.class,
						amMsUser.getMsJob().getUuidJob());
				if (gsVal.equals(job.getJobCode())) {
					MsLov msl = new MsLov();
					msl.setLovGroup(GlobalVal.LOV_TAG_JOB_MH);
					msl.setCode(String.valueOf(amMsUser.getUuidMsUser()));
					msl.setDescription(amMsUser.getFullName());
					msl.setSequence(getSeqMsLovMh(String.valueOf(amMsUser.getMsBranch()
							.getUuidBranch())));
					msl.setIsActive("1");
					msl.setIsDeleted("0");
					msl.setDtmCrt(new Date());
					msl.setUsrCrt(callerId.getCallerId().toString());

					msl.setConstraint1(String.valueOf(amMsUser.getMsBranch()
							.getUuidBranch()));
					msl.setConstraint2(null);
					msl.setConstraint3(null);
					msl.setConstraint4(null);
					msl.setConstraint5(null);

					this.getManagerDAO().insert(msl);
				}

				// is round robin user
				if ("1".equals(job.getIsRoundRobin())) {
					MsRoundrobintask bean = new MsRoundrobintask();
					bean.setUsrCrt(callerId.getCallerId());
					bean.setDtmCrt(new Date());
					bean.setAmMsuser(beanAmMsuser);
					bean.setFlagTask("0");
					this.getManagerDAO().insert(bean);
				}
			}
		} 
		else {
			throw new EntityNotUniqueException(this.messageSource.getMessage(
					"businesslogic.usermanagement.loginexist",
					new Object[] { amMsUser.getLoginId() }, this.retrieveLocaleAudit(callerId)),
					amMsUser.getLoginId());
		}

		this.insertPwdHistory(amMsUser.getPassword(), amMsUser,
				ChangePasswordType.NEW_PASSWORD.getPassType(), callerId);
			
	}

	public int getSeqMsLovMh(String uuidBranch) {
		
		Object[][] param = { { Restrictions.eq("lovGroup", "LOOKUPMH") },
				{ Restrictions.eq("constraint1", uuidBranch) } };
		Map<String, Object> map = this.getManagerDAO()
				.count(MsLov.class, param);
		Long result = (Long) map.get(GlobalKey.MAP_RESULT_SIZE);
		return result.intValue() + 1;
	}

	public String getAreaBranch(long uuidBranch) {
		String result = "";
		MsAreaofbranch msAreaofbranch = (MsAreaofbranch) this.getManagerDAO()
				.selectOne(
				"from MsAreaofbranch maob join fetch maob.msBranch mb join fetch maob.msArea ma where mb.uuidBranch = :uuidBranch", 
				new Object[][] {{"uuidBranch", uuidBranch}});
		if (msAreaofbranch != null) {
			result = String.valueOf(msAreaofbranch.getMsArea().getUuidArea());
		}
		return result;
	}

	public Map<String, Object> getGroupOfJob(long uuidJob) {
		Map<String, Object> result = new HashMap<String, Object>();
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and mgoj.msJob.uuidJob=:uuidJob");
		paramMap.put("uuidJob", uuidJob);
		result = this.getManagerDAO().selectAll(
				"from MsGroupofjob mgoj join fetch mgoj.msJob join fetch mgoj.amMsgroup where 1=1"
						+ condition.toString(), paramMap);
		List<MsGroupofjob> listGroupOfJob = (List) result
				.get(GlobalKey.MAP_RESULT_LIST);
		result.put(GlobalKey.MAP_RESULT_LIST, listGroupOfJob);
		return result;
	}

	public void insertMemberOfGroup(AmMsgroup amMsgroup, AmMsuser amMsUser,
			AuditContext callerId) {
		AmMemberofgroup dbModel = new AmMemberofgroup();
		dbModel.setUsrCrt(callerId.getCallerId());
		dbModel.setDtmCrt(new Date());
		dbModel.setAmMsgroup(amMsgroup);
		dbModel.setAmMsuser(amMsUser);
		this.auditManager.auditAdd(dbModel, auditInfoMemberOfGroup,
				callerId.getCallerId(), "");
		this.getManagerDAO().insert(dbModel);
	}

	public void insertZipCodeOfUser(String zipCode, AmMsuser amMsUser,
			AuditContext callerId) {
		MsZipcodeofuser dbModel = new MsZipcodeofuser();
		dbModel.setUsrCrt(callerId.getCallerId());
		dbModel.setDtmCrt(new Date());
		dbModel.setZipCode(zipCode);
		dbModel.setAmMsuser(amMsUser);
		this.getManagerDAO().insert(dbModel);
		this.auditManager.auditAdd(dbModel, auditInfoZipcodeOfUser, callerId.getCallerId(), "");
	}

	public void insertAreaOfUser(long uuidArea, AmMsuser amMsuser,
			AuditContext callerId) {
		MsAreaofuser dbModel = new MsAreaofuser();
		dbModel.setUsrCrt(callerId.getCallerId());
		dbModel.setDtmCrt(new Date());
		dbModel.setMsArea(new MsArea());
		dbModel.getMsArea().setUuidArea(uuidArea);
		dbModel.setAmMsuser(amMsuser);
		this.auditManager.auditAdd(dbModel, auditInfoAreaOfUser,
				callerId.getCallerId(), "");
		this.getManagerDAO().insert(dbModel);
	}

	public void deleteMemberOfGroup(long uuidMsUser) {
		Object[][] params = { { "uuidMsUser", uuidMsUser } };
		this.getManagerDAO().deleteNative(
				"am.usermanagement.deleteMemberOfGroup", params);
	}

	public void deleteZipCodeOfUser(long uuidMsUser) {
		Object[][] params = { { "uuidMsUser", uuidMsUser } };
		this.getManagerDAO().deleteNative(
				"am.usermanagement.deleteZipCodeOfUser", params);
	}

	public void deleteAreaOfUser(long uuidMsUser) {
		Object[][] params = { { "uuidMsUser", uuidMsUser } };
		this.getManagerDAO().deleteNative("am.usermanagement.deleteAreaOfUser",
				params);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateUserManagement(UserManagementBean obj,
			AuditContext callerId, boolean isNC) {					

		AmMsuser dbModel = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", obj.getBean().getUuidMsUser()}});
		AmMsuser userLoginBean = this.getManagerDAO().selectOne(
				AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		if (userLoginBean != null) {
			if (!this.isJobList(userLoginBean.getMsJob().getUuidJob(), String.valueOf(obj
					.getBean().getMsJob().getUuidJob()))) {
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.usermanagement.unauthorizedoperation",null,
						this.retrieveLocaleAudit(callerId)), Reason.ERROR_EXIST);
			}
		}
		long uuidMsUser = obj.getBean().getUuidMsUser();
		//dbModel.setLoginId(obj.getBean().getLoginId());
		dbModel.setFullName(obj.getBean().getFullName());

		boolean isChangePassword = false;
		if (StringUtils.isNotBlank(obj.getBean().getPassword())) {
			isChangePassword = true;
			if (GlobalVal.FLAG_LOGIN_PROVIDER_NC.equals(dbModel.getLoginProvider())) {
				intFormLogic.changePassword(dbModel.getUniqueId(), null, obj.getBean().getPassword(), callerId);
			}
			if (!validatePassword(obj.getBean().getPassword())) {
				throw new UserManagementException(this.messageSource.getMessage(
						"msg.password.failed",null,
						this.retrieveLocaleAudit(callerId)),
						ReasonUser.TEMPLATE_MISMATCH);
			}
			
			dbModel.setPassword(PasswordHash.createHash(obj.getBean()
					.getPassword()));
		}

		//dbModel.setLoginId(obj.getBean().getLoginId().toUpperCase());
		dbModel.setMsBranch(obj.getBean().getMsBranch());
		dbModel.setMsDealer(obj.getBean().getMsDealer());

		if (GlobalVal.BRANCH_HO.equals(obj.getBean().getMsBranch()
				.getUuidBranch())) {
			Object[][] param = { { Restrictions.eq("branchName",
					GlobalVal.BRANCH_HO) } };
			dbModel.setMsBranch(this.getManagerDAO().selectOne(
					MsBranch.class, param));
		}

		if (GlobalVal.DEALER_HO.equals(obj.getBean().getMsDealer()
				.getUuidDealer())) {
			Object[][] param = { { Restrictions.eq("dealerName",
					GlobalVal.DEALER_HO) } };
			dbModel.setMsDealer(this.getManagerDAO().selectOne(
					MsDealer.class, param));
		}

		if (StringUtils.isNotBlank(String.valueOf(obj.getBean().getAmMsuser()
				.getUuidMsUser()))) {
			AmMsuser amMsUser = this.getManagerDAO().selectOne(
					AmMsuser.class,
					obj.getBean().getAmMsuser().getUuidMsUser());
			dbModel.setAmMsuser(amMsUser);
		} 
		else {
			dbModel.setAmMsuser(null);
		}

		boolean isSetUnlock = false;
		if ("1".equals(dbModel.getIsLocked())
				&& "0".equals(obj.getBean().getIsLocked())) {
			isSetUnlock = true;
			dbModel.setFailCount(0);
		}

		if (GlobalVal.SUBSYSTEM_MC.equals(dbModel.getAmMssubsystem()
				.getSubsystemName())) {
			if ("1".equals(getLimitCohEnabled(
					GlobalKey.GENERALSETTING_LIMIT_COH_ENABLED, callerId))) {
				if (null == obj.getBean().getCashLimit()
						|| "".equals(obj.getBean().getCashLimit())) {
					dbModel.setCashLimit(new BigDecimal(0));
				} 
				else {
					dbModel.setCashLimit(new BigDecimal(String
							.valueOf(obj.getBean().getCashLimit())
							.replace(",", "").replace(".", "")));
				}
			} 
			else {
				dbModel.setCashLimit(null);
				dbModel.setCashOnHand(new BigDecimal(0));
			}

			// cek initial tidak boleh ada yg sama
			if (!"".equals(obj.getBean().getInitialName())
					&& obj.getBean().getInitialName() != null) {
				Object[][] paramInitialUser = {
						{ Restrictions.eq("initialName", obj.getBean()
								.getInitialName().toUpperCase()) },
						{ Restrictions.eq("uuidMsUser", obj.getBean()
								.getUuidMsUser()) } };
				AmMsuser dbInitialUser = this.getManagerDAO().selectOne(
						AmMsuser.class, paramInitialUser);
				Object[][] paramInitial = { { Restrictions.eq(
						"initialName", obj.getBean().getInitialName()
								.toUpperCase()) } };
				AmMsuser dbInitial = this.getManagerDAO().selectOne(
						AmMsuser.class, paramInitial);

				if (dbInitialUser != null) {
					// abaikan
				} 
				else {
					if (dbInitial != null) {
						throw new EntityNotUniqueException(this.messageSource.getMessage(
								"businesslogic.usermanagement.initialexist",
								new Object[] { obj.getBean().getInitialName().toUpperCase() }, this.retrieveLocaleAudit(callerId)),
								obj.getBean().getInitialName().toUpperCase());
					} 
					else {
						dbModel.setInitialName(obj.getBean()
								.getInitialName().toUpperCase());
					}
				}
			}
			// end initial tidak boleh ada yg sama
		}

		dbModel.setIsLocked(obj.getBean().getIsLocked());
		dbModel.setPhone(obj.getBean().getPhone());
		//dbModel.setEmail(obj.getBean().getEmail());

		if (GlobalVal.SUBSYSTEM_MS.equals(dbModel.getAmMssubsystem()
				.getSubsystemName())) {
			if (!dbModel.getIsActive().equals(obj.getBean().getIsActive())) {
				MsJob job;
				if (!String.valueOf(dbModel.getMsJob().getUuidJob())
						.equals(String.valueOf(obj.getBean().getMsJob().getUuidJob()))) {
					job = this.getManagerDAO().selectOne(MsJob.class,
							obj.getBean().getMsJob().getUuidJob());
				} 
				else {
					job = this.getManagerDAO().selectOne(MsJob.class,
							dbModel.getMsJob().getUuidJob());
				}

				if ("1".equals(job.getIsRoundRobin())) {
					// delete is round roben job user lama
					if ("0".equals(obj.getBean().getIsActive())) {
						Object[][] prm = { { "uuidUser",
								dbModel.getUuidMsUser() } };
						this.getManagerDAO()
								.deleteNativeString(
										"Delete MS_ROUNDROBINTASK where UUID_MS_USER = :uuidUser",
										prm);
					}

					// insert is round robin job user baru
					if ("1".equals(obj.getBean().getIsActive())) {
						MsRoundrobintask bean = new MsRoundrobintask();
						bean.setUsrCrt(callerId.getCallerId());
						bean.setDtmCrt(new Date());
						bean.setAmMsuser(dbModel);
						bean.setFlagTask("0");
						this.getManagerDAO().insert(bean);
					}
				}
			}
		}

		if ("0".equals(obj.getBean().getIsActive())
				&& "1".equals(dbModel.getIsActive())) {
			dbModel.setIsLoggedIn("0");
		}

		dbModel.setIsActive(obj.getBean().getIsActive());
		if (obj.getBean().getImei() == null
				|| obj.getBean().getImei().isEmpty()) {
			dbModel.setImei(null);
			dbModel.setAndroidId(null);
			dbModel.setIsLoggedIn("0");
		} 
		else {
			dbModel.setImei(obj.getBean().getImei().toUpperCase());
		}

		dbModel.setIsTracking(obj.getBean().getIsTracking());
		if (obj.getBean().getIsTracking().equals("1")) {
			if (!StringUtils.isEmpty(obj.getBean().getTrackingStartTime())) {
				dbModel.setTrackingStartTime(obj.getBean()
						.getTrackingStartTime());
			} 
			else {
				String start = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_START_TIME, callerId);
				dbModel.setTrackingStartTime(start);
			}
			if (!StringUtils.isEmpty(obj.getBean().getTrackingEndTime())) {
				dbModel.setTrackingEndTime(obj.getBean()
						.getTrackingEndTime());
			} 
			else {
				String end = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_END_TIME, callerId);
				dbModel.setTrackingEndTime(end);
			}
			if (!StringUtils.isEmpty(obj.getBean().getTrackingDays())) {
				dbModel.setTrackingDays(obj.getBean().getTrackingDays());
			} 
			else {
				String track = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_DAYS, callerId);
				dbModel.setTrackingDays(track);
			}
		} 
		else {
			dbModel.setTrackingStartTime(null);
			dbModel.setTrackingEndTime(null);
			dbModel.setTrackingDays(null);
		}

		dbModel.setImei2(StringUtils.isEmpty(obj.getBean().getImei2()) ? obj
				.getBean().getImei2() : obj.getBean().getImei2()
				.toUpperCase());
		dbModel.setMaxTaskLoad(obj.getBean().getMaxTaskLoad());
		if (dbModel.getMaxTaskLoad()==null) {
			String maxTaskLoad = globalLogic.getGsValue(GlobalKey.GENERALSETTING_DEFAULT_MAX_TASK_LOAD, callerId);
			dbModel.setMaxTaskLoad(Integer.parseInt(maxTaskLoad));
		}
		dbModel.setUsrUpd(callerId.getCallerId());
		dbModel.setDtmUpd(new Date());
		dbModel.setIsDeleted("0");

		// insert member of group based on jobvsgroup
		if (!String.valueOf(dbModel.getMsJob().getUuidJob())
				.equals(String.valueOf(obj.getBean().getMsJob().getUuidJob()))) {
			if (GlobalVal.SUBSYSTEM_MS.equals(dbModel.getAmMssubsystem()
					.getSubsystemName())) {
				// delete ms_lov if dbModel.getMsJob().getUuidJob() = job mh
				String gsVal = globalLogic.getGsValue(
						GlobalKey.GENERALSETTING_MO_MHJOB, callerId);
				MsJob jobOld = this.getManagerDAO().selectOne(MsJob.class,
						dbModel.getMsJob().getUuidJob());

				if (jobOld.getJobCode().equals(gsVal)) {
					Object[][] prm = {
							{ Restrictions.eq("lovGroup", "LOOKUPMH") },
							{ Restrictions.eq("code",
									dbModel.getUuidMsUser()) } };
					MsLov msl = this.getManagerDAO().selectOne(MsLov.class,
							prm);
					if (msl != null){
						this.getManagerDAO().delete(msl);
					}
				}

				MsJob jobNew = this.getManagerDAO().selectOne(MsJob.class,
						obj.getBean().getMsJob().getUuidJob());
				// insert ms_lov if job mh subsytem order
				if (gsVal.equals(jobNew.getJobCode())) {
					MsLov msl = new MsLov();;
					msl.setLovGroup(GlobalVal.LOV_TAG_JOB_MH);
					msl.setCode(String.valueOf(dbModel.getUuidMsUser()));
					msl.setDescription(dbModel.getFullName());
					msl.setSequence(getSeqMsLovMh(String.valueOf(dbModel.getMsBranch()
							.getUuidBranch())));
					msl.setIsActive("1");
					msl.setIsDeleted("0");
					msl.setDtmCrt(new Date());
					msl.setUsrCrt(callerId.getCallerId().toString());
					msl.setConstraint1(String.valueOf(dbModel.getMsBranch()
							.getUuidBranch()));
					msl.setConstraint2(null);
					msl.setConstraint3(null);
					msl.setConstraint4(null);
					msl.setConstraint5(null);

					this.getManagerDAO().insert(msl);
				}

				// delete is round roben job user lama
				if ("1".equals(jobOld.getIsRoundRobin())) {
					Object[][] prm = { { "uuidUser",
							dbModel.getUuidMsUser() } };
					this.getManagerDAO()
							.deleteNativeString(
									"Delete MS_ROUNDROBINTASK where UUID_MS_USER = :uuidUser",
									prm);
				}

				// insert is round robin job user baru
				if ("1".equals(jobNew.getIsRoundRobin())) {
					MsRoundrobintask bean = new MsRoundrobintask();
					bean.setUsrCrt(callerId.getCallerId());
					bean.setDtmCrt(new Date());
					bean.setAmMsuser(dbModel);
					bean.setFlagTask("0");
					this.getManagerDAO().insert(bean);
				}
			}

			long uuidJob = obj.getBean().getMsJob().getUuidJob();
			MsJob jobNew = this.getManagerDAO().selectOne(MsJob.class,
					uuidJob);
			dbModel.setMsJob(jobNew);
			Map<String, Object> mapGroupOfJob = getGroupOfJob(uuidJob);
			Long countGroupOfJob = (Long) mapGroupOfJob
					.get(GlobalKey.MAP_RESULT_SIZE);
			if (countGroupOfJob > 0) {
				List<MsGroupofjob> listGroupOfJob = (List<MsGroupofjob>) mapGroupOfJob
						.get(GlobalKey.MAP_RESULT_LIST);
				deleteMemberOfGroup(uuidMsUser);
				for (int i = 0; i < listGroupOfJob.size(); i++) {
					MsGroupofjob bean = listGroupOfJob.get(i);
					insertMemberOfGroup(bean.getAmMsgroup(), dbModel,
							callerId);
				}
			}
		} 
		else {
			Object[][] prm = { { Restrictions.eq("lovGroup", "LOOKUPMH") },
					{ Restrictions.eq("code", String.valueOf(dbModel.getUuidMsUser())) } };
			MsLov msl = this.getManagerDAO().selectOne(MsLov.class, prm);
			if (msl != null) {
				if ("0".equals(obj.getBean().getIsActive())){
					msl.setIsActive("0");
				}
				else{
					msl.setIsActive("1");
				}
				msl.setUsrUpd(callerId.getCallerId().toString());
				msl.setDtmUpd(new Date());
				this.getManagerDAO().update(msl);
			}
		}
		this.auditManager.auditEdit(dbModel, auditInfo,
				callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);

		if (!isNC) {
			AmMsuser userLogin = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
					new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
			if (!userLogin.getAmMssubsystem().getSubsystemName()
					.equals(GlobalVal.SUBSYSTEM_MO)) {
				// update zipcode of user
				if (!obj.getZipCode().equals(getUserZipCode(uuidMsUser))) {
					deleteZipCodeOfUser(uuidMsUser);
					if (obj.getZipCode() != null
							&& !"".equals(obj.getZipCode())) {
						String[] zipCodes = obj.getZipCode().split(",");
						for (int i = 0; i < zipCodes.length; i++) {
							insertZipCodeOfUser(zipCodes[i], dbModel,
									callerId);
						}
					}
				}

				// update area of user
				if (obj.getAreaCode() != null
						&& !"".equals(obj.getAreaCode())) {
					if (!obj.getAreaCode().equals(
							this.getAreaOfUser(uuidMsUser))) {
						this.deleteAreaOfUser(uuidMsUser);
						String[] area = obj.getAreaCode().split(",");
						for (int i = 0; i < area.length; i++) {
							insertAreaOfUser(Long.valueOf(area[i]), dbModel, callerId);
						}
					}
				} 
				else {
					if (GlobalVal.SUBSYSTEM_MS.equals(dbModel
							.getAmMssubsystem().getSubsystemName())
							|| GlobalVal.SUBSYSTEM_MC.equals(dbModel
									.getAmMssubsystem().getSubsystemName())) {
						this.deleteAreaOfUser(uuidMsUser);
						MsJob job = this.getManagerDAO().selectOne(
								MsJob.class,
								dbModel.getMsJob().getUuidJob());
						if ("1".equals(job.getIsBranch())) {
							String area = getAreaBranch(dbModel
									.getMsBranch().getUuidBranch());
							if (!area.isEmpty()){
								insertAreaOfUser(Long.valueOf(area), dbModel, callerId);
							}
						}
					}
				}
			}
		}
		
			Object[][] paramMultiLogin = { {Restrictions.eq("uniqueId", dbModel.getUniqueId())} };
			Map<String, Object> mapUser = this.getManagerDAO().selectAll(AmMsuser.class, paramMultiLogin, null);
			List<AmMsuser> listLoginId = (List<AmMsuser>) mapUser.get(GlobalKey.MAP_RESULT_LIST);
			
			for (AmMsuser usr : listLoginId) {
				usr.setDtmUpd(new Date());
				usr.setUsrUpd(callerId.getCallerId());
				
				if (obj.getBean().getImei() == null
						|| obj.getBean().getImei().isEmpty()) {
					usr.setImei(null);
					usr.setAndroidId(null);
					usr.setIsLoggedIn("0");
				} 
				else {
					usr.setImei(obj.getBean().getImei().toUpperCase());
				}
				
				usr.setImei2(StringUtils.isEmpty(obj.getBean().getImei2()) 
							? obj.getBean().getImei2() : obj.getBean().getImei2().toUpperCase());
				
				if (isSetUnlock) {
					usr.setFailCount(0);
					usr.setIsLocked("0");
				}
				
				if (isChangePassword) {
					usr.setPassword(dbModel.getPassword());
					this.insertPwdHistory(usr.getPassword(), usr, ChangePasswordType.CHANGE_PASSWORD.getPassType(), callerId);
				}
				this.getManagerDAO().update(usr);
			}
			
		if (StringUtils.isNotBlank(obj.getBean().getPassword())) {
			this.insertPwdHistory(dbModel.getPassword(), dbModel,
					ChangePasswordType.CHANGE_PASSWORD.getPassType(),
					callerId);
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteUserManagement(long uuid, AuditContext callerId) {

		AmMsuser dbModel = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", uuid}});

		dbModel.setIsActive("0");
		dbModel.setIsDeleted("1");
		dbModel.setUsrUpd(callerId.getCallerId());
		dbModel.setDtmUpd(new Date());

		this.auditManager.auditEdit(dbModel, auditInfo,
				callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);

		if (GlobalVal.SUBSYSTEM_MS.equals(dbModel.getAmMssubsystem()
				.getSubsystemName())) {
			// delete ms_lov if dbModel.getMsJob().getUuidJob() = job mh
			String gsVal = globalLogic.getGsValue(
					GlobalKey.GENERALSETTING_MO_MHJOB, callerId);
			MsJob job = this.getManagerDAO().selectOne(MsJob.class,
					dbModel.getMsJob().getUuidJob());

			if (job.getJobCode().equals(gsVal)) {
				Object[][] prm = {
						{ Restrictions.eq("lovGroup", "LOOKUPMH") },
						{ Restrictions.eq("code", String.valueOf(dbModel.getUuidMsUser())) } };
				MsLov msl = this.getManagerDAO()
						.selectOne(MsLov.class, prm);
				if (msl != null) {
					msl.setIsActive("0");
					msl.setIsDeleted("1");
					this.getManagerDAO().update(msl);
				}
			}

			// delete is round roben job user lama
			if ("1".equals(job.getIsRoundRobin())) {
				Object[][] prm = { { "uuidUser", dbModel.getUuidMsUser() } };
				this.getManagerDAO()
						.deleteNativeString(
								"Delete MS_ROUNDROBINTASK where UUID_MS_USER = :uuidUser",
								prm);
			}
		}
		
		//delete member of group
		deleteMemberOfGroup(dbModel.getUuidMsUser());
			
	}

	public boolean isLoginIdValid(String loginId) {
		Map<String, Object> result = new HashMap<>();
		boolean x = true;

		Object[][] param = new Object[2][1];

		param[0][0] = Restrictions.eq("loginId", loginId.toUpperCase());
		result = this.getManagerDAO().count(AmMsuser.class, param);

		Long listSize = (Long) result.get(GlobalKey.MAP_RESULT_SIZE);

		if (listSize > 0) {
			x = false;
		}

		return x;
	}

	@Override
	public String getBranchZipCode(long uuidBranch, AuditContext callerId) {
		Map<String, Object> result = null;
		String stringResult = "";

		Object[][] params = { { Restrictions.eq("msBranch.uuidBranch",
				uuidBranch) } };

		result = this.getManagerDAO().list(MsZipcodeofbranch.class, params,
				null);
		List<MsZipcodeofbranch> listBranchZipCode = (List) result
				.get(GlobalKey.MAP_RESULT_LIST);

		if (listBranchZipCode != null && !listBranchZipCode.isEmpty()) {
			for (MsZipcodeofbranch bean : listBranchZipCode) {
				stringResult += bean.getZipCode() + ",";
			}
			if (stringResult.length() > 1) {
				stringResult = stringResult.substring(0,
						stringResult.length() - 1);
			}
		} 
		else {
			stringResult = "";
		}
		
		return stringResult;
	}

	@Override
	public String isBranchAreaEmpty(long uuidBranch, AuditContext callerId) {
		String isEmpty = "1";

		Object[][] param = { { Restrictions.eq("msBranch.uuidBranch",
				uuidBranch) } };
		MsAreaofbranch msAreaofbranch = (MsAreaofbranch) this
				.getManagerDAO().selectOne(MsAreaofbranch.class, param);
		if (msAreaofbranch != null) {
			isEmpty = "0";
		}
			
		return isEmpty;
	}

	private boolean isJobList(long uuidJobUser, String uuidJob) {
		
		String queryListName = StringUtils.EMPTY;
		List<Map<String, Object>> list = new ArrayList();
		boolean result = false;
		queryListName = "am.usermanagement.getJobList";
		MsJob msJobUser = this.getManagerDAO().selectOne(MsJob.class,
				uuidJobUser);
		
		Object[][] paramsAdmin = { 
				{Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MS+GlobalKey.GENERALSETTING_JOBADMIN)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsAdmin);
		if (ArrayUtils.contains(StringUtils.split(amGeneralsetting.getGsValue(), ";"), msJobUser.getJobCode())) {
			queryListName = "am.usermanagement.getJobListAdmin";
		}
		Object[][] params = { { "uuidJob", uuidJobUser } };
		list = this.getManagerDAO()
				.selectAllNative(queryListName, params, null);
		for (int i = 0; i < list.size(); i++) {
			Map map = list.get(i);
			if (uuidJob.equals(map.get("d0").toString())) {
				result = true;
				break;
			}
		}
		return result;
	}

	@Override
	public String getLimitCohEnabled(String gsCode, AuditContext callerId) {
		String limitCohEnabled = "0";
		
		Object[][] params = { { Restrictions.eq("gsCode", gsCode) } };
		AmGeneralsetting result = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, params);
		if (result != null) {
			limitCohEnabled = result.getGsValue();
		}

		return limitCohEnabled;
	}

	@Override
	public byte[] exportExcel(String subsystemName, String flagMc,
			AuditContext callerId) {
		HSSFWorkbook workbook = null;
		if (GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(subsystemName)) {
			workbook = this.createXlsTemplate(subsystemName, flagMc,
					TEMPLATE_HEADER_DATAMASTER_MO, callerId);
		} 
		else {
			workbook = this.createXlsTemplate(subsystemName, flagMc,
					TEMPLATE_HEADER_DATAMASTER, callerId);
		}

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	public HSSFWorkbook createXlsTemplate(String subsystem, String flagMc,
			String[] template2, AuditContext callerId) {
		
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("User List");
			int headerLength = this.createHeader(workbook, sheet, subsystem,
					flagMc);
			this.setTextDataFormat(workbook, sheet, headerLength);

			// buat Data Master untuk menyimpan isi dropdown
			HSSFSheet sheetDM = workbook.createSheet("Data Master");
			this.createDataMaster(workbook, sheetDM, template2, subsystem, callerId);

			List branch = this.getBranch(callerId);
			CellRangeAddressList addressListBranch = new CellRangeAddressList(
					1, NUMBER_ROW_IN_VALIDATION, BRANCH_COLUMN, BRANCH_COLUMN);
			DVConstraint dvConstraintBranch = DVConstraint
					.createFormulaListConstraint("'Data Master'!$A$3:$A$"
							+ (branch.size() + 2));

			HSSFDataValidation dataValidBranch = new HSSFDataValidation(
					addressListBranch, dvConstraintBranch);
			dataValidBranch.setSuppressDropDownArrow(false);
			dataValidBranch.setEmptyCellAllowed(false);
			sheet.addValidationData(dataValidBranch);
			
			List job = this.getJob(subsystem, callerId);
			CellRangeAddressList addressListJob = new CellRangeAddressList(1,
					NUMBER_ROW_IN_VALIDATION, JOB_COLUMN, JOB_COLUMN);
			DVConstraint dvConstraintJob = DVConstraint
					.createFormulaListConstraint("'Data Master'!$D$3:$D$"
							+ (job.size() + 2));

			HSSFDataValidation dataValidJob = new HSSFDataValidation(
					addressListJob, dvConstraintJob);
			dataValidJob.setSuppressDropDownArrow(false);
			dataValidJob.setEmptyCellAllowed(false);
			sheet.addValidationData(dataValidJob);
			
			int flagTrackingColumn = 9;
			if(GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(subsystem)){
				flagTrackingColumn = 10;
			}
			else if(GlobalVal.SUBSYSTEM_MS.equalsIgnoreCase(subsystem)){
				flagTrackingColumn = 9;
			}
			else if(GlobalVal.SUBSYSTEM_MC.equalsIgnoreCase(subsystem)){
				flagTrackingColumn = 11;
			}
			CellRangeAddressList flagTrckingVal = new CellRangeAddressList(
					1, NUMBER_ROW_IN_VALIDATION, flagTrackingColumn, flagTrackingColumn);
			DVConstraint dvConstraintFlagTrackring = DVConstraint
					.createFormulaListConstraint("'Data Master'!$G$3:$G$"
							+ (2 + 2));

			HSSFDataValidation dataValidFlagTracking = new HSSFDataValidation(
					flagTrckingVal, dvConstraintFlagTrackring);
			dataValidFlagTracking.setSuppressDropDownArrow(false);
			dataValidFlagTracking.setEmptyCellAllowed(false);
			sheet.addValidationData(dataValidFlagTracking);

			if (GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(subsystem)) {
				List delaer = this.getDealer(callerId);
				CellRangeAddressList addressListDealer = new CellRangeAddressList(
						1, NUMBER_ROW_IN_VALIDATION, DEALER_COLUMN,
						DEALER_COLUMN);
				DVConstraint dvConstraintDealer = DVConstraint
						.createFormulaListConstraint("'Data Master'!$I$3:$I$"
								+ (delaer.size() + 2));

				HSSFDataValidation dataValidDealer = new HSSFDataValidation(
						addressListDealer, dvConstraintDealer);
				dataValidJob.setSuppressDropDownArrow(false);
				dataValidJob.setEmptyCellAllowed(false);
				sheet.addValidationData(dataValidDealer);
			}

		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	@Override
	public List getBranch(AuditContext callerId) {
		
		List list = this.getManagerDAO().selectAllNative(
			"am.usermanagement.getBranch", null, null);

		return list;
	}

	private int createHeader(HSSFWorkbook workbook, HSSFSheet sheet,
			String subsystem, String flagMc) {
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);

		HSSFRow row = sheet.createRow(0);

		int headerLength = 0;

		if (GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(subsystem)) {
			headerLength = TEMPLATE_HEADER_MO.length;
			for (int i = 0; i < TEMPLATE_HEADER_MO.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MO[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MO[i]);
			}
		} 
		else if (GlobalVal.SUBSYSTEM_MC.equalsIgnoreCase(subsystem)
				&& "1".equalsIgnoreCase(flagMc)) {
			headerLength = TEMPLATE_HEADER_MC_COH.length;
			for (int i = 0; i < TEMPLATE_HEADER_MC_COH.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MC_COH[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MC_COH[i]);
			}
		} 
		else if (GlobalVal.SUBSYSTEM_MS.equalsIgnoreCase(subsystem)) {
			headerLength = TEMPLATE_HEADER_MS.length;
			for (int i = 0; i < TEMPLATE_HEADER_MS.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MS[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MS[i]);
			}
		} 
		else if (GlobalVal.SUBSYSTEM_MC.equalsIgnoreCase(subsystem)) {
			headerLength = TEMPLATE_HEADER_MC.length;
			for (int i = 0; i < TEMPLATE_HEADER_MC.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MC[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MC[i]);
			}
		}
		return headerLength;
	}

	private void setTextDataFormat(HSSFWorkbook workbook, HSSFSheet sheet,
			int headerLength) {
		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("General"));

		for (int i = 0; i < headerLength; i++) {
			sheet.setDefaultColumnStyle(i, style);
		}
	}

	private int columnSize(List list) {
		Map map = (Map) list.get(0);
		return map.size();
	}

	private void createDataMaster(HSSFWorkbook workbook, HSSFSheet sheet,
			String[] template, String subsystem, AuditContext callerId) {

		List listBranch = this.getBranch(callerId);
		List listJob = this.getJob(subsystem, callerId);
		List listDealer = new ArrayList<>();
		if (GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(subsystem)) {
			listDealer = this.getDealer(callerId);
		}

		// title
		HSSFCellStyle titleStyle = workbook.createCellStyle();
		titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
		titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
		titleStyle.setFillBackgroundColor(HSSFColor.BLACK.index);
		titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
		HSSFFont titleFont = workbook.createFont();
		titleFont.setBoldweight((short) 1000);
		titleFont.setColor(HSSFColor.WHITE.index);
		HSSFRow rowLabel = sheet.createRow(1);
		DataFormat format = workbook.createDataFormat();
		titleStyle.setDataFormat(format.getFormat("@"));

		for (int i = 0; i < template.length; i++) {
			if (!template[i].equals(" ")) {
				HSSFCell cell = rowLabel.createCell(i);
				cell.setCellValue(template[i]);
				titleStyle.setFont(titleFont);
				cell.setCellStyle(titleStyle);
				sheet.autoSizeColumn(i);
			}
		}

		// value
		HSSFCellStyle valStyle = workbook.createCellStyle();
		valStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
		valStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		valStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		valStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);

		int rowSize1 = Math.max(listBranch.size(), listJob.size());
		int rowSize = Math.max(rowSize1, listDealer.size());

		Map valBranch = null, valJob = null, valDealer = null;
		int idCol = 0, colSizeBranch = columnSize(listBranch), colSizeJob = columnSize(listJob), colSizeDealer = 0;

		if (GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(subsystem)) {
			colSizeDealer = columnSize(listDealer);
		}

		for (int i = 0; i < rowSize; i++) {
			HSSFRow rowLabelVal = sheet.createRow(i + 2);

			idCol = 0;

			if (i < listBranch.size()) {
				valBranch = (Map) listBranch.get(i);
				for (int j = 0; j < valBranch.size(); j++) {
					HSSFCell cell = rowLabelVal.createCell(j + idCol);
					cell.setCellValue(valBranch.get("d" + j).toString());
					cell.setCellStyle(valStyle);
					sheet.autoSizeColumn(i);
				}
				idCol += colSizeBranch;
			} 
			else {
				idCol += colSizeBranch;
			}

			idCol++;

			if (i < listJob.size()) {
				valJob = (Map) listJob.get(i);
				for (int j = 0; j < valJob.size(); j++) {
					HSSFCell cell = rowLabelVal.createCell(j + idCol);
					cell.setCellValue(valJob.get("d" + j).toString());
					cell.setCellStyle(valStyle);
					sheet.autoSizeColumn(i);
				}
				idCol += colSizeJob;
			} 
			else {
				idCol += colSizeJob;
			}

			idCol++;
			
			if (i < 2) {	
				HSSFCell cell = rowLabelVal.createCell(idCol);
				cell.setCellValue(i+"");
				cell.setCellStyle(valStyle);
				sheet.autoSizeColumn(i);
				idCol ++;
			} 
			else {
				idCol ++;
			}
			
			idCol++;

			if (i < listDealer.size()) {
				valDealer = (Map) listDealer.get(i);
				for (int j = 0; j < valDealer.size(); j++) {
					HSSFCell cell = rowLabelVal.createCell(j + idCol);
					cell.setCellValue(valDealer.get("d" + j).toString());
					cell.setCellStyle(valStyle);
					sheet.autoSizeColumn(i);
				}
				idCol += colSizeDealer;
			} 
			else {
				idCol += colSizeDealer;
			}

			idCol++;
		}
	}

	@Override
	public List getJob(String subsystemName, AuditContext callerId) {

		String params[][] = { { "SUBSYSTEM_NAME", subsystemName } };
		List list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getJob", params, null);

		return list;
	}

	@Override
	public List getDealer(AuditContext callerId) {
		
		List list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getDealer", null, null);

		return list;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public byte[] processSpreadSheetFile(File uploadedFile, String subsystem,
			String flagCoh, AuditContext callerId) {
		byte[] errorUploadByte = null;

		Map parseMap;
		try {
			parseMap = this.parseSpreadsheetToUserManagementBeans(uploadedFile,
					subsystem, flagCoh, callerId);
		} 
		catch (IOException e) {
			errorUploadByte = this.errorUpload();
			return errorUploadByte;
		}

		List listofUser = (List) parseMap.get("result");
		List listofErrorUser = (List) parseMap.get("resultError");
		List listErrorText = (List) parseMap.get("errorText");
		List<String> listofErrorValueUserManager = (List<String>) parseMap
				.get("resultErrorValueUserManager");

		for (Iterator iterator = listofUser.iterator(); iterator.hasNext();) {
			AmMsuser user = (AmMsuser) iterator.next();
			String uuidExist = this.getUserExist(user);
			// check existing identical data
			if (StringUtils.isNotBlank(uuidExist)) {
				// update to active
				AmMsuser dbModel = this.getManagerDAO().selectOne(
						AmMsuser.class, Long.valueOf(uuidExist));
				dbModel.setLoginId(user.getLoginId());
				dbModel.setFullName(user.getFullName());
				dbModel.setMsJob(user.getMsJob());
				dbModel.setAmMsuser(user.getAmMsuser());
				dbModel.setPhone(user.getPhone());
				dbModel.setImei(StringUtils.isEmpty(user.getImei()) ? user
						.getImei() : user.getImei().toUpperCase());
				dbModel.setImei2(StringUtils.isEmpty(user.getImei2()) ? user
						.getImei2() : user.getImei2().toUpperCase());
				if (GlobalVal.SUBSYSTEM_MO.equals(subsystem)) {
					if ("1".equals(user.getMsJob().getIsBranch())) {
						dbModel.setMsBranch(user.getMsBranch());
					} 
					else {
						dbModel.setMsDealer(user.getMsDealer());
					}
				} 
				else {
					dbModel.setMsBranch(user.getMsBranch());
				}
				dbModel.setCashLimit(user.getCashLimit());
				dbModel.setIsActive("1");
				dbModel.setIsDeleted("0");
				dbModel.setIsLocked("0");
				dbModel.setFailCount(0);
				dbModel.setUsrUpd(callerId.getCallerId());
				dbModel.setDtmUpd(new Date());
				dbModel.setIsTracking(user.getIsTracking());
				dbModel.setTrackingStartTime(user.getTrackingStartTime());
				dbModel.setTrackingEndTime(user.getTrackingEndTime());
				dbModel.setTrackingEndTime(user.getTrackingEndTime());
				dbModel.setTrackingDays(user.getTrackingDays());

				this.getManagerDAO().update(dbModel);
			} 
			else if (StringUtils.isBlank(uuidExist)) {
				String interfaceType = SpringPropertiesUtils.getProperty(GlobalKey.INTERFACE_TYPE);
				this.insertUserManagement(user, callerId, interfaceType);
			}
		}
		if (!listofErrorUser.isEmpty()) {
			try {
				errorUploadByte = exportErrorUserManagement(
						listofErrorUser, listofUser.size(), listErrorText,
						subsystem, flagCoh, listofErrorValueUserManager, callerId);
			} 
			catch (SQLException e) {
				throw new UserManagementException(this.messageSource.getMessage(
						"businesslogic.error.generatexls",null,
						this.retrieveLocaleAudit(callerId)),
						ReasonUser.ERROR_GENERATE);
			}
			return errorUploadByte;
		}
		else{
			return errorUploadByte;
		}	
	}

	// error condition untuk di action
	private byte[] errorUpload() {
		byte[] tmp = new byte[1];
		tmp[0] = 1;// for condition in action
		return tmp;
	}

	private Map parseSpreadsheetToUserManagementBeans(File uploadedFile,
			String subsystem, String flagCoh, AuditContext callerId)
			throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<AmMsuser> result = new ArrayList<>();
		List<AmMsuser> resultError = new ArrayList<>();
		List<String> ErrorText = new ArrayList<>();
		List<String> resultErrorValueUserManager = new ArrayList<>();

		FileInputStream inputStream = new FileInputStream(uploadedFile);

		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		HSSFSheet sheet = wb.getSheetAt(0);
		int rows = sheet.getPhysicalNumberOfRows();

		int status = checkingTemplate(uploadedFile, subsystem, flagCoh);
		if (status == 1) {
			wb.close();
			throw new IOException();
		}

		for (int r = 1; r < rows; r++) {
			HSSFRow row = sheet.getRow(r);
			if (row == null) {
				continue;
			}

			boolean isEmptyRow = checkEmptyRow(row);

			if (isEmptyRow == true) {
				continue;
			}
			
			AmMsuser user = new AmMsuser();
			String userManager = null;

			Object prm[][] = { { Restrictions.eq("subsystemName", subsystem) } };
			AmMssubsystem msSubsystem = this.getManagerDAO().selectOne(
					AmMssubsystem.class, prm);
			user.setAmMssubsystem(msSubsystem);

			int totalCell = row.getLastCellNum();
			for (int c = 0; c < totalCell; c++) {
				HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);

				String value = "";

				// if intValue -1, then sequence is posted with string
				user.setDtmCrt(new Date());
				user.setUsrCrt(callerId.getCallerId().toString());
				
				if (cell != null) {
					switch (cell.getCellType()) {
					case HSSFCell.CELL_TYPE_NUMERIC:
						value = String
								.valueOf((int) cell.getNumericCellValue());
						break;

					case HSSFCell.CELL_TYPE_STRING:
						value = cell.getStringCellValue();
						break;
					default:
					}
				}
				if (GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(subsystem)) {
					switch (c) {
					case 0:
						user.setLoginId(value);
						break;
					case 1:
						user.setFullName(value);
						break;
					case 2:
						if (StringUtils.isNotBlank(value)) {
							Object param1[][] = {
									{ Restrictions.eq("jobCode", value) },
									{ Restrictions.eq(
											"amMssubsystem.uuidMsSubsystem",
											msSubsystem.getUuidMsSubsystem()) } };
							MsJob job = this.getManagerDAO().selectOne(
									MsJob.class, param1);
							user.setMsJob(job);
						}
						break;
					case 3:
						if (StringUtils.isNotBlank(value) && null != user.getMsJob() 
								&& "1".equals(user.getMsJob().getIsBranch())) {
							Object param2[][] = { { Restrictions.eq(
									"branchCode", value) } };
							MsBranch branch = this.getManagerDAO().selectOne(
									MsBranch.class, param2);
							user.setMsBranch(branch);
						}
						break;
					case 4:
						if (StringUtils.isNotBlank(value) && null != user.getMsJob()
								&& "0".equals(user.getMsJob().getIsBranch())) {
							Object param2[][] = { { Restrictions.eq(
									"dealerCode", value) } };
							MsDealer dealer = this.getManagerDAO().selectOne(
									MsDealer.class, param2);
							user.setMsDealer(dealer);
						}
						break;
					case 5:
						if (StringUtils.isNotBlank(value)) {
							AmMsuser manager = this.getManagerDAO().selectOne(
								"from AmMsuser u join fetch u.msBranch "
								+ "join fetch u.msDealer where u.loginId = :loginId", 
								new Object[][] {{"loginId", value}});
							AmMsuser managerFromExcel = this.checkManagerFromExcel(result, value);
							if (manager != null) {
								user.setAmMsuser(manager);
							}
							else if(managerFromExcel != null){
								user.setAmMsuser(managerFromExcel);
							}
							else {
								userManager = value;
							}
						} 
						else {
							userManager = "";
						}
						break;
					case 6:
						if (StringUtils.isNotBlank(value)) {
							user.setPhone(value);
						}
						break;
					case 7:
						if (StringUtils.isNotBlank(value)) {
							user.setEmail(value);
						}
						break;
					case 8:
						if (StringUtils.isNotBlank(value)) {
							user.setImei(value.toUpperCase());
						}
						break;
					case 9:
						if (StringUtils.isNotBlank(value)) {
							user.setImei2(value.toUpperCase());
						}
						break;
					case 10:
						if (StringUtils.isNotBlank(value)) {
							if ("1".equals(value)) {
								user.setTrackingStartTime("08:00");
								user.setTrackingEndTime("23:59");
							}
							user.setIsTracking(value);
						}
						break;
					case 11:
						if (StringUtils.isNotBlank(value)
								&& "1".equals(user.getIsTracking())) {
							user.setTrackingStartTime(value);
						} 
						else if (StringUtils.isBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String start = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_START_TIME, callerId);
							user.setTrackingStartTime(start);
						}
						break;
					case 12:
						if (StringUtils.isNotBlank(value)
								&& "1".equals(user.getIsTracking())) {
							user.setTrackingEndTime(value);
						} 
						else if (StringUtils.isBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String end = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_END_TIME, callerId);
							user.setTrackingEndTime(end);
						}
						break;
					case 13:
						if (StringUtils.isNotBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String [] days = value.split(";");
							int [] numDays = new int [days.length];
							List dayList = Arrays.asList(DAYS);
							for (int i=0; i<days.length; i++){
								int idx = dayList.indexOf(days[i]);
								numDays[i] = idx;
							}
							String track = StringUtils.join(numDays, ";");
							user.setTrackingDays(track);
						} 
						else if (StringUtils.isBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String track = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_DAYS, callerId);
							user.setTrackingDays(track);
						}						
						break;
					}
					wb.close();
				} 
				else if (GlobalVal.SUBSYSTEM_MS.equalsIgnoreCase(subsystem)) {
					switch (c) {
					case 0:
						user.setLoginId(value);
						break;
					case 1:
						user.setFullName(value);
						break;
					case 2:
						if (StringUtils.isNotBlank(value)) {
							Object param1[][] = {
									{ Restrictions.eq("jobCode", value) },
									{ Restrictions.eq(
											"amMssubsystem.uuidMsSubsystem",
											msSubsystem.getUuidMsSubsystem()) } };
							MsJob job = this.getManagerDAO().selectOne(
									MsJob.class, param1);
							user.setMsJob(job);
						}
						break;
					case 3:
						if (StringUtils.isNotBlank(value)) {
							Object param2[][] = { { Restrictions.eq(
									"branchCode", value) } };
							MsBranch branch = this.getManagerDAO().selectOne(
									MsBranch.class, param2);
							user.setMsBranch(branch);
						}
						break;
					case 4:
						if (StringUtils.isNotBlank(value)) {
							Object param3[][] = { { Restrictions.eq("loginId",
									value) } };
							AmMsuser manager = this.getManagerDAO().selectOne(
									AmMsuser.class, param3);
							AmMsuser managerFromExcel = this.checkManagerFromExcel(result, value);
							if (manager != null) {
								user.setAmMsuser(manager);
							}
							else if(managerFromExcel != null){
								user.setAmMsuser(managerFromExcel);
							}
							else {
								userManager = value;
							}
						} 
						else {
							userManager = "";
						}
						break;
					case 5:
						if (StringUtils.isNotBlank(value)) {
							user.setPhone(value);
						}
						break;
					case 6:
						if (StringUtils.isNotBlank(value)) {
							user.setEmail(value);
						}
						break;
					case 7:
						if (StringUtils.isNotBlank(value)) {
							user.setImei(value.toUpperCase());
						}
						break;
					case 8:
						if (StringUtils.isNotBlank(value)) {
							user.setImei2(value.toUpperCase());
						}
						break;
					case 9:
						if (StringUtils.isNotBlank(value)) {
							if ("1".equals(value)) {
								user.setTrackingStartTime("08:00");
								user.setTrackingEndTime("23:59");
							}
							user.setIsTracking(value);
						}
						break;
					case 10:
						if (StringUtils.isNotBlank(value)
								&& "1".equals(user.getIsTracking())) {
							user.setTrackingStartTime(value);
						} 
						else if (StringUtils.isBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String start = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_START_TIME, callerId);
							user.setTrackingStartTime(start);
						}
						break;
					case 11:
						if (StringUtils.isNotBlank(value)
								&& "1".equals(user.getIsTracking())) {
							user.setTrackingEndTime(value);
						} 
						else if (StringUtils.isBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String end = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_END_TIME, callerId);
							user.setTrackingEndTime(end);
						}
						break;
					case 12:
						if (StringUtils.isNotBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String [] days = value.split(";");
							String [] numDays = new String [days.length];
							List dayList = Arrays.asList(DAYS);
							for (int i=0; i<days.length; i++){
								int idx = dayList.indexOf(days[i]);
								numDays[i] = idx +"";
							}
							String track = StringUtils.join(numDays, ";");
							user.setTrackingDays(track);
						} 
						else if (StringUtils.isBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String track = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_DAYS, callerId);
							user.setTrackingDays(track);
						}		
						break;
					}
					wb.close();
				} 
				else if (GlobalVal.SUBSYSTEM_MC.equalsIgnoreCase(subsystem)) {
					switch (c) {
					case 0:
						user.setLoginId(value);
						break;
					case 1:
						user.setFullName(value);
						break;
					case 2:
						if (StringUtils.isNotBlank(value)) {
							Object param1[][] = {
									{ Restrictions.eq("jobCode", value) },
									{ Restrictions.eq(
											"amMssubsystem.uuidMsSubsystem",
											msSubsystem.getUuidMsSubsystem()) } };
							MsJob job = this.getManagerDAO().selectOne(
									MsJob.class, param1);
							user.setMsJob(job);
						}
						break;
					case 3:
						if (StringUtils.isNotBlank(value)) {
							Object param2[][] = { { Restrictions.eq(
									"branchCode", value) } };
							MsBranch branch = this.getManagerDAO().selectOne(
									MsBranch.class, param2);
							user.setMsBranch(branch);
						}
						break;
					case 4:
						if (StringUtils.isNotBlank(value)) {
							Object param3[][] = { { Restrictions.eq("loginId",
									value) } };
							AmMsuser manager = this.getManagerDAO().selectOne(
									AmMsuser.class, param3);
							AmMsuser managerFromExcel = this.checkManagerFromExcel(result, value);
							if (manager != null) {
								user.setAmMsuser(manager);
							}
							else if(managerFromExcel != null){
								user.setAmMsuser(managerFromExcel);
							}
							else {
								userManager = value;
							}
						} 
						else {
							userManager = "";
						}
						break;
					case 5:
						if (StringUtils.isNotBlank(value)) {
							user.setInitialName(value);
						}
						break;
					case 6:
						if (StringUtils.isNotBlank(value)) {
							user.setPhone(value);
						}
						break;
					case 7:
						if (StringUtils.isNotBlank(value)) {
							user.setEmail(value);
						}
						break;
					case 8:
						if (StringUtils.isNotBlank(value)) {
							user.setImei(value.toUpperCase());
						}
						break;
					case 9:
						if (StringUtils.isNotBlank(value)) {
							user.setImei2(value.toUpperCase());
						}
						break;
					case 10:
						if ("1".equals(flagCoh)) {
							if (StringUtils.isNotBlank(value)) {
								if (isInteger(value)) {
									user.setCashLimit(new BigDecimal(value));
								} 
								else {
									user.setFacebookId(value);
									user.setCashLimit(new BigDecimal(-1));
								}
							}
						} else {
							if (StringUtils.isNotBlank(value)) {
								if ("1".equals(value)) {
									user.setTrackingStartTime("08:00");
									user.setTrackingEndTime("23:59");
								}
								user.setIsTracking(value);
							}
						}
						break;
					case 11:
						if ("1".equals(flagCoh)) {
							if (StringUtils.isNotBlank(value)) {
								if ("1".equals(value)) {
									user.setTrackingStartTime("08:00");
									user.setTrackingEndTime("23:59");
								}
								user.setIsTracking(value);
							}
						} 
						else {
							if (StringUtils.isNotBlank(value)
									&& "1".equals(user.getIsTracking())) {
								if (StringUtils.isNotBlank(value)
										&& "1".equals(user.getIsTracking())) {
									user.setTrackingStartTime(value);
								} 
								else if (StringUtils.isBlank(value)
										&& "1".equals(user.getIsTracking())) {
									String start = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_START_TIME, callerId);
									user.setTrackingStartTime(start);
								}
							}
						}
						break;
					case 12:
						if ("1".equals(flagCoh)) {
							if (StringUtils.isNotBlank(value)
									&& "1".equals(user.getIsTracking())) {
								user.setTrackingStartTime(value);
							} 
							else if (StringUtils.isBlank(value)
									&& "1".equals(user.getIsTracking())) {
								String start = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_START_TIME, callerId);
								user.setTrackingStartTime(start);
							}
						} 
						else {
							if (StringUtils.isNotBlank(value)
									&& "1".equals(user.getIsTracking())) {
								user.setTrackingEndTime(value);
							} 
							else if (StringUtils.isBlank(value)
									&& "1".equals(user.getIsTracking())) {
								String end = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_END_TIME, callerId);
								user.setTrackingEndTime(end);
							}
						}
						break;
					case 13:
						if ("1".equals(flagCoh)) {
							if (StringUtils.isNotBlank(value)
									&& "1".equals(user.getIsTracking())) {
								user.setTrackingEndTime(value);
							} 
							else if (StringUtils.isBlank(value)
									&& "1".equals(user.getIsTracking())) {
								String end = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_END_TIME, callerId);
								user.setTrackingEndTime(end);
							}
						}
						else {
							if (StringUtils.isNotBlank(value)
									&& "1".equals(user.getIsTracking())) {
								String [] days = value.split(";");
								int [] numDays = new int [days.length];
								List dayList = Arrays.asList(DAYS);
								for (int i=0; i<days.length; i++){
									int idx = dayList.indexOf(days[i]);
									numDays[i] = idx;
								}
								String track = StringUtils.join(numDays, ";");
								user.setTrackingDays(track);
							} 
							else if (StringUtils.isBlank(value)
									&& "1".equals(user.getIsTracking())) {
								String track = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_DAYS, callerId);
								user.setTrackingDays(track);
							}
						}
						break;
					case 14:
						if (StringUtils.isNotBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String [] days = value.split(";");
							int [] numDays = new int [days.length];
							List dayList = Arrays.asList(DAYS);
							for (int i=0; i<days.length; i++){
								int idx = dayList.indexOf(days[i]);
								numDays[i] = idx;
							}
							String track = StringUtils.join(numDays, ";");
							user.setTrackingDays(track);
						} 
						else if (StringUtils.isBlank(value)
								&& "1".equals(user.getIsTracking())) {
							String track = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_TRACKING_DAYS, callerId);
							user.setTrackingDays(track);
						}
					}
					wb.close();
				}
			}

			StringBuilder errorText = checkingUserUpload(user, subsystem,
					userManager, callerId);
			if (errorText.length() == 0) {
				result.add(user);
			} 
			else {
				resultError.add(user);
				if (userManager != null) {
					resultErrorValueUserManager.add(userManager);
				}
				ErrorText.add(errorText.toString());
			}
			
		}
		paramParse.put("result", result);
		paramParse.put("resultError", resultError);
		paramParse.put("errorText", ErrorText);
		paramParse.put("resultErrorValueUserManager",
				resultErrorValueUserManager);
		return paramParse;
	}
	
	private AmMsuser checkManagerFromExcel(List<AmMsuser> result, String value){
		AmMsuser userManager = null;
		for(int i = 0;i<result.size();i++){
			AmMsuser user = result.get(i);
			if(value.equalsIgnoreCase(user.getLoginId())){
				userManager = user;
			}
		}
		return userManager;
	}

	public static boolean isInteger(String s) {
		try {
			Integer.parseInt(s);
		} 
		catch (NumberFormatException e) {
			return false;
		} 
		catch (NullPointerException e) {
			return false;
		}
		return true;
	}

	public String getUserExist(AmMsuser user) {

		String loginId = StringUtils.trimToNull(user.getLoginId());

		Object[][] queryParams = { { Restrictions.eq("loginId", loginId) } };

		AmMsuser result = this.getManagerDAO().selectOne(AmMsuser.class,
				queryParams);

		String uuid = "";
		if (result != null) {
			uuid = String.valueOf(result.getUuidMsUser());
		}

		return uuid;
	}

	public int checkingTemplate(File uploadedFile, String subsystem,
			String flagCoh) throws IOException {
		int count = 0;

		FileInputStream inputStream = new FileInputStream(uploadedFile);

		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		HSSFSheet sheet = wb.getSheetAt(0);

		HSSFRow header = sheet.getRow(0);

		if (GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(subsystem)) {
			if (header != null) {
				for (int c = 0; c < TEMPLATE_HEADER_MO.length; c++) {
					HSSFCell cell = header.getCell(c, Row.RETURN_BLANK_AS_NULL);
					String value = "";
					value = cell.getStringCellValue();

					if ((value).equals(TEMPLATE_HEADER_MO[c])) {
						count = 0;
					} 
					else {
						count = 1;
						wb.close();
						break;
					}
				}
			}
		} else if (GlobalVal.SUBSYSTEM_MC.equals(subsystem)
				&& "1".equals(flagCoh)) {
			if (header != null) {
				for (int c = 0; c < TEMPLATE_HEADER_MC_COH.length; c++) {
					HSSFCell cell = header.getCell(c, Row.RETURN_BLANK_AS_NULL);
					String value = "";
					value = cell.getStringCellValue();

					if ((value).equals(TEMPLATE_HEADER_MC_COH[c])) {
						count = 0;
					} 
					else {
						count = 1;
						wb.close();
						break;
					}
				}
			}
		} else if (GlobalVal.SUBSYSTEM_MS.equalsIgnoreCase(subsystem)) {
			if (header != null) {
				for (int c = 0; c < TEMPLATE_HEADER_MS.length; c++) {
					HSSFCell cell = header.getCell(c, Row.RETURN_BLANK_AS_NULL);
					String value = "";
					value = cell.getStringCellValue();

					if ((value).equals(TEMPLATE_HEADER_MS[c])) {
						count = 0;
					} 
					else {
						count = 1;
						wb.close();
						break;
					}
				}
			}
		} 
		else if (GlobalVal.SUBSYSTEM_MC.equalsIgnoreCase(subsystem)) {
			if (header != null) {
				for (int c = 0; c < TEMPLATE_HEADER_MC.length; c++) {
					HSSFCell cell = header.getCell(c, Row.RETURN_BLANK_AS_NULL);
					String value = "";
					value = cell.getStringCellValue();

					if ((value).equals(TEMPLATE_HEADER_MC[c])) {
						count = 0;
					} 
					else {
						count = 1;
						wb.close();
						break;
					}
				}
			}
		}
		return count;
	}

	private byte[] exportErrorUserManagement(List listUser, int userSuccess,
			List listErrorText, String subsystem, String flagCoh,
			List<String> valueUserManager, AuditContext callerId) throws SQLException {

		HSSFWorkbook workbook = this.createXlsTemplateErrorUserManagement(
				listUser, userSuccess, listErrorText, subsystem, flagCoh,
				valueUserManager, callerId);

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private boolean checkEmptyRow(HSSFRow row) {
		String[] isEmptyCell = new String[10];
		Arrays.fill(isEmptyCell, "");

		for (int c = 0; c < 10; c++) {
			HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
			if (cell == null) {
				isEmptyCell[c] = "empty";
			}
		}

		if ("empty".equals(isEmptyCell[0]) && "empty".equals(isEmptyCell[1])
				&& "empty".equals(isEmptyCell[2])
				&& "empty".equals(isEmptyCell[3])
				&& "empty".equals(isEmptyCell[4])
				&& "empty".equals(isEmptyCell[5])
				&& "empty".equals(isEmptyCell[6])
				&& "empty".equals(isEmptyCell[7])
				&& "empty".equals(isEmptyCell[8])
				&& "empty".equals(isEmptyCell[9])) {
			return true;
		} 
		else {
			return false;
		}
	}

	public StringBuilder checkingUserUpload(AmMsuser user, String subsystem,
			String userManager, AuditContext callerId) {
		StringBuilder errorUpload = new StringBuilder();
		if (user.getLoginId() == null || ("").equals(user.getLoginId())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"UserID"}, this.retrieveLocaleAudit(callerId)));
		} 
		else {
			if (user.getLoginId().length() > 36) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", 
						new Object[]{"UserID","36"}, this.retrieveLocaleAudit(callerId)));
			}
		}
		if (user.getFullName() == null || ("").equals(user.getFullName())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"User Name"}, this.retrieveLocaleAudit(callerId)));
		} 
		else {
			if (user.getFullName().length() > 200) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", 
						new Object[]{"User Name","200"}, this.retrieveLocaleAudit(callerId)));
			}
		}
		if (user.getMsJob() == null || ("").equals(user.getMsJob())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"Job User"}, this.retrieveLocaleAudit(callerId)));
		}

		if (user.getEmail() != null) {
			String[] kata = user.getEmail().split("@");
			if (kata.length == 2) {
				if (!user.getEmail().endsWith(".co.id")
						&& !user.getEmail().endsWith(".com")) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(this.messageSource.getMessage("service.global.notvalid", 
							new Object[]{"Data Email"}, this.retrieveLocaleAudit(callerId)));
				}
			} 
			else {
				errorUpload.append(this.messageSource.getMessage("service.global.notvalid", 
						new Object[]{"Data Email"}, this.retrieveLocaleAudit(callerId)));
			}
		}

		if (user.getPhone() != null) {
			if (Pattern.matches("[0-9]+", user.getPhone()) == false) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.numeric", 
						new Object[]{"Phone"}, this.retrieveLocaleAudit(callerId)));
			}
		}

		if (userManager != null && !("").equals(userManager)) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.notexist", 
					new Object[]{"User Manager"}, this.retrieveLocaleAudit(callerId)));
		}

		if (GlobalVal.SUBSYSTEM_MO.equals(subsystem)) {
			MsJob job = user.getMsJob();
			if (job != null && "1".equals(job.getIsBranch())) {
				if (user.getMsBranch() == null
						|| ("").equals(user.getMsBranch())) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
							new Object[]{"Branch"}, this.retrieveLocaleAudit(callerId)));
				}
			} 
			else {
				if (user.getMsDealer() == null
						|| ("").equals(user.getMsDealer())) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
							new Object[]{"Dealer"}, this.retrieveLocaleAudit(callerId)));
				}
			}
		} 
		else if(GlobalVal.SUBSYSTEM_MS.equals(subsystem)){
			if (user.getMsBranch() == null || ("").equals(user.getMsBranch())) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
						new Object[]{"Branch"}, this.retrieveLocaleAudit(callerId)));
			}
		}
		else{
			if (user.getMsBranch() == null || ("").equals(user.getMsBranch())) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
						new Object[]{"Branch"}, this.retrieveLocaleAudit(callerId)));
			}
			if (user.getInitialName() == null
					|| ("").equals(user.getInitialName())) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
						new Object[]{"Initial Name"}, this.retrieveLocaleAudit(callerId)));
			}
		}

		if (user.getPhone() != null && user.getPhone().length() > 20) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", 
					new Object[]{"Phone","20"}, this.retrieveLocaleAudit(callerId)));
		}
		if (user.getEmail() != null && user.getEmail().length() > 64) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", 
					new Object[]{"Email","64"}, this.retrieveLocaleAudit(callerId)));
		}
		if (user.getImei() != null && user.getImei().length() > 15) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", 
					new Object[]{"Nomor IMEI","15"}, this.retrieveLocaleAudit(callerId)));
		}
		if (user.getImei2() != null && user.getImei2().length() > 15) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", 
					new Object[]{"Nomor IMEI2","15"}, this.retrieveLocaleAudit(callerId)));
		}
		if (user.getCashLimit() != null && user.getCashLimit().compareTo(new BigDecimal(0)) < 0) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.numeric", 
					new Object[]{"Limit COH"}, this.retrieveLocaleAudit(callerId)));
		}
		if (!("1".equals(user.getIsTracking()) || ("0".equals(user
				.getIsTracking()) || "".equals(user.getIsTracking()) || null == user.getIsTracking() ))) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.trackingflag", 
					null, this.retrieveLocaleAudit(callerId)));
		}
		if (user.getTrackingStartTime() != null || user.getTrackingEndTime() != null) {
			String[] start = user.getTrackingStartTime().split(":");
			String[] end = user.getTrackingEndTime().split(":");
			if (start.length == 2 && end.length == 2) {
				int hourS = Integer.valueOf(start[0]);
				int minutS = Integer.valueOf(start[1]);
				int hourE = Integer.valueOf(end[0]);
				int minutE = Integer.valueOf(end[1]);
				if (hourS < hourE) {
					if (!((8 <= hourS && hourS <= 23)
							&& (0 <= minutS && minutS <= 59)
							&& (8 <= hourE && hourE <= 23) && (0 <= minutE && minutE <= 59))) {
						if (!("").equals(errorUpload.toString())) {
							errorUpload.append(" | ");
						}
						errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.time", 
								new Object[]{"Start time and End time", "hh:mm"}, this.retrieveLocaleAudit(callerId)));
					}
				} 
				else {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.time", 
							new Object[]{"Start time and End time", "hh:mm"}, this.retrieveLocaleAudit(callerId)));
				}
			} 
			else {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.time", 
						new Object[]{"Start time and End time", "hh:mm"}, this.retrieveLocaleAudit(callerId)));
			}
		}
		if (user.getAmMsuser() != null) {
			AmMsuser manager = user.getAmMsuser();			
			if (user.getMsJob().getMsJob() != null && !user.getMsJob().getMsJob().getJobCode().equals(manager.getMsJob().getJobCode())) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.jobuser", 
						null, this.retrieveLocaleAudit(callerId)));
			}
			else if (!user.getMsJob().getIsBranch().equals(manager.getMsJob().getIsBranch()) ) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.jobuser", 
						null, this.retrieveLocaleAudit(callerId)));
			}
			if (user.getMsJob() != null && "1".equals(user.getMsJob().getIsBranch()) && user.getMsBranch() != null) {				
				if (user.getMsBranch().getUuidBranch() != manager.getMsBranch().getUuidBranch()) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.managerbranch", 
							null, this.retrieveLocaleAudit(callerId)));
				}
			}
			else if (user.getMsJob() != null && !"1".equals(user.getMsJob().getIsBranch()) && user.getMsDealer() != null) {
				if (user.getMsDealer().getUuidDealer() != manager.getMsDealer().getUuidDealer()) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.managerdealer", 
							null, this.retrieveLocaleAudit(callerId)));
				}
			}
		}
		return errorUpload;
	}
	
	@Override
	public void insertUserManagement(AmMsuser obj, AuditContext callerId, String interfaceType) {
		obj.setIsActive("1");
		obj.setDtmCrt(new Date());
		obj.setUsrCrt(callerId.getCallerId());
		obj.setPassword(PasswordHash.createHash(globalLogic.getGsValue(GlobalKey.GENERALSETTING_DEFAULT_PASSWORD, callerId)));
		obj.setFailCount(0);
		if(GlobalVal.FLAG_LOGIN_PROVIDER_NC.equals(interfaceType)){
			obj.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_NC);
			obj.setChangePwdLogin("0");
		} 
		else{
			obj.setLoginProvider(GlobalVal.FLAG_LOGIN_PROVIDER_DB);
			obj.setChangePwdLogin("1");
		}
		obj.setIsLoggedIn("0");
		obj.setIsLocked("0");
		obj.setIsPasswordExpired("0");
		obj.setIsDormant("0");
		obj.setIsDeleted("0");
		if (obj.getMsBranch() == null) {
			Object[][] param = { { Restrictions.eq("branchName",
					GlobalVal.BRANCH_HO) } };
			obj.setMsBranch(this.getManagerDAO().selectOne(MsBranch.class,
					param));
		}
		if (obj.getMsDealer() == null) {
			Object[][] param = { { Restrictions.eq("dealerName",
					GlobalVal.DEALER_HO) } };
			obj.setMsDealer(this.getManagerDAO().selectOne(MsDealer.class,
					param));
		}
		if (obj.getMaxTaskLoad()==null) {
			String maxTaskLoad = globalLogic.getGsValue(GlobalKey.GENERALSETTING_DEFAULT_MAX_TASK_LOAD, callerId);
			obj.setMaxTaskLoad(Integer.parseInt(maxTaskLoad));
		}
		this.getManagerDAO().insert(obj);
		this.auditManager.auditAdd(obj, auditInfo, callerId.getCallerId(), "");
		AmMsuser beanAmMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
				obj.getUuidMsUser());

		// insert member of group based on jobvsgroup
		Map<String, Object> mapGroupOfJob = getGroupOfJob(obj.getMsJob()
				.getUuidJob());
		Long countGroupOfJob = (Long) mapGroupOfJob
				.get(GlobalKey.MAP_RESULT_SIZE);
		if (countGroupOfJob > 0) {
			List<MsGroupofjob> listGroupOfJob = (List<MsGroupofjob>) mapGroupOfJob
					.get(GlobalKey.MAP_RESULT_LIST);
			for (int i = 0; i < listGroupOfJob.size(); i++) {
				MsGroupofjob bean = listGroupOfJob.get(i);
				insertMemberOfGroup(bean.getAmMsgroup(), obj, callerId);
			}
		}

		// insert ms_lov if job mh subsytem order
		if (GlobalVal.SUBSYSTEM_MS.equals(obj.getAmMssubsystem()
				.getSubsystemName())) {
			String gsVal = globalLogic.getGsValue(
					GlobalKey.GENERALSETTING_MO_MHJOB, callerId);
			MsJob job = this.getManagerDAO().selectOne(MsJob.class,
					obj.getMsJob().getUuidJob());
			if (gsVal.equals(job.getJobCode())) {
				MsLov msl = new MsLov();
				msl.setLovGroup(GlobalVal.LOV_TAG_JOB_MH);
				msl.setCode(String.valueOf(obj.getUuidMsUser()));
				msl.setDescription(obj.getFullName());
				msl.setSequence(getSeqMsLovMh(String.valueOf(obj.getMsBranch().getUuidBranch())));
				msl.setIsActive("1");
				msl.setIsDeleted("0");
				msl.setDtmCrt(new Date());
				msl.setUsrCrt(callerId.getCallerId().toString());

				msl.setConstraint1(String.valueOf(obj.getMsBranch().getUuidBranch()));
				msl.setConstraint2(null);
				msl.setConstraint3(null);
				msl.setConstraint4(null);
				msl.setConstraint5(null);

				this.getManagerDAO().insert(msl);
			}

			// is round robin user
			if ("1".equals(job.getIsRoundRobin())) {
				MsRoundrobintask bean = new MsRoundrobintask();
				bean.setUsrCrt(callerId.getCallerId());
				bean.setDtmCrt(new Date());
				bean.setAmMsuser(beanAmMsuser);
				bean.setFlagTask("0");
				this.getManagerDAO().insert(bean);
			}
		}
	}

	private HSSFWorkbook createXlsTemplateErrorUserManagement(List listUser,
			int userSuccess, List listErrorText, String subsystem,
			String flagCoh, List<String> valueUserManager, AuditContext callerId) {

		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("User List");
			this.createHeaderErrorUpload(workbook, sheet, userSuccess,
					listUser.size(), subsystem, flagCoh, callerId);
			this.setDataErrorUpload(sheet, listUser, listErrorText,
					subsystem, flagCoh, valueUserManager);
		} 
		catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}

	private void createHeaderErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet, int userSuccess, int userError, String subsystem,
			String flagCoh, AuditContext callerId) {

		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);
		DataFormat format = workbook.createDataFormat();
		style.setDataFormat(format.getFormat("@"));

		HSSFRow row0 = sheet.createRow(0);
		HSSFRow row1 = sheet.createRow(1);
		HSSFRow row2 = sheet.createRow(2);

		HSSFCell cell0 = row0.createCell(0);
		cell0.setCellValue(this.messageSource.getMessage(
				"businesslogic.uploadtask.tottaskupload", new Object[]{"User",userSuccess}, this.retrieveLocaleAudit(callerId)));
		cell0.setCellStyle(style);

		HSSFCell cell1 = row1.createCell(0);
		cell1.setCellValue(this.messageSource.getMessage(
				"businesslogic.uploadtask.tottaskerr", new Object[]{"User",userError}, this.retrieveLocaleAudit(callerId)));
		cell1.setCellStyle(style);

		HSSFCell cell2 = row2.createCell(0);
		cell2.setCellValue(this.messageSource.getMessage(
				"businesslogic.uploadtask.errupload", new Object[]{"User"}, this.retrieveLocaleAudit(callerId)));
		cell2.setCellStyle(style);

		HSSFRow row4 = sheet.createRow(4);

		if (GlobalVal.SUBSYSTEM_MO.equals(subsystem)) {
			for (int i = 0; i < TEMPLATE_HEADER_ERROR_MO.length; i++) {
				HSSFCell cell = row4.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_ERROR_MO[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_ERROR_MO[i]);
				sheet.setDefaultColumnStyle(i, style);
			}
		} 
		else if (GlobalVal.SUBSYSTEM_MS.equals(subsystem)) {
			for (int i = 0; i < TEMPLATE_HEADER_ERROR_MS.length; i++) {
				HSSFCell cell = row4.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_ERROR_MS[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_ERROR_MS[i]);
				sheet.setDefaultColumnStyle(i, style);
			}
		} 
		else if ((GlobalVal.SUBSYSTEM_MC.equals(subsystem))
				&& "1".equals(flagCoh)) {
			for (int i = 0; i < TEMPLATE_HEADER_ERROR_MC_COH.length; i++) {
				HSSFCell cell = row4.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_ERROR_MC_COH[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_ERROR_MC_COH[i]);
				sheet.setDefaultColumnStyle(i, style);
			}
		} 
		else if (GlobalVal.SUBSYSTEM_MC.equals(subsystem)) {
			for (int i = 0; i < TEMPLATE_HEADER_ERROR_MC.length; i++) {
				HSSFCell cell = row4.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_ERROR_MC[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_ERROR_MC[i]);
				sheet.setDefaultColumnStyle(i, style);
			}
		}
	}

	private void setDataErrorUpload(HSSFSheet sheet,
			List listUser, List listErrorText, String subsystem,
			String flagCoh, List<String> valueUserManager) {
		List userList = listUser;
		List<String> usermanagersValue = valueUserManager;

		int j = 5, k = 0;
		Iterator iterator = userList.iterator();
		while (iterator.hasNext()) {
			HSSFRow row = sheet.createRow(j);
			String valueusermanager = new String();
			AmMsuser user = (AmMsuser) iterator.next();
			if (!usermanagersValue.isEmpty()) {
				valueusermanager = usermanagersValue.get(0);
			}

			if (GlobalVal.SUBSYSTEM_MO.equals(subsystem)) {
				HSSFCell cell = row.createCell(0);
				cell.setCellValue(user.getLoginId());
				sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH_ERROR_MO[0]);

				HSSFCell cell1 = row.createCell(1);
				cell1.setCellValue(user.getFullName());
				sheet.setColumnWidth(1, HEADER_COLUMN_WIDTH_ERROR_MO[1]);

				HSSFCell cell2 = row.createCell(2);
				if (user.getMsJob() != null) {
					cell2.setCellValue(user.getMsJob().getJobCode());
				}
				sheet.setColumnWidth(2, HEADER_COLUMN_WIDTH_ERROR_MO[2]);
				
				if (user.getMsJob() != null && "1".equals(user.getMsJob().getIsBranch())) {
					HSSFCell cell3 = row.createCell(3);
					if (user.getMsBranch() != null)
						cell3.setCellValue(user.getMsBranch().getBranchCode());
					sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH_ERROR_MO[3]);

					sheet.setColumnWidth(4, HEADER_COLUMN_WIDTH_ERROR_MO[4]);
				} 
				else {
					sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH_ERROR_MO[3]);

					HSSFCell cell4 = row.createCell(4);
					if (user.getMsDealer() != null)
						cell4.setCellValue(user.getMsDealer().getDealerCode());
					sheet.setColumnWidth(4, HEADER_COLUMN_WIDTH_ERROR_MO[4]);
				}

				HSSFCell cell5 = row.createCell(5);
				if (user.getAmMsuser() == null) {
					cell5.setCellValue(valueusermanager);
				} 
				else {
					cell5.setCellValue(user.getAmMsuser().getLoginId());
				}
				sheet.setColumnWidth(5, HEADER_COLUMN_WIDTH_ERROR_MO[5]);

				HSSFCell cell6 = row.createCell(6);
				cell6.setCellValue(user.getPhone());
				sheet.setColumnWidth(6, HEADER_COLUMN_WIDTH_ERROR_MO[6]);

				HSSFCell cell7 = row.createCell(7);
				cell7.setCellValue(user.getEmail());
				sheet.setColumnWidth(7, HEADER_COLUMN_WIDTH_ERROR_MO[7]);

				HSSFCell cell8 = row.createCell(8);
				cell8.setCellValue(user.getImei());
				sheet.setColumnWidth(8, HEADER_COLUMN_WIDTH_ERROR_MO[8]);

				HSSFCell cell9 = row.createCell(9);
				cell9.setCellValue(user.getImei2());
				sheet.setColumnWidth(9, HEADER_COLUMN_WIDTH_ERROR_MO[9]);

				HSSFCell cell10 = row.createCell(10);
				cell10.setCellValue(user.getIsTracking());
				sheet.setColumnWidth(10, HEADER_COLUMN_WIDTH_ERROR_MO[10]);

				HSSFCell cell11 = row.createCell(11);
				cell11.setCellValue(user.getTrackingStartTime());
				sheet.setColumnWidth(11, HEADER_COLUMN_WIDTH_ERROR_MO[11]);

				HSSFCell cell12 = row.createCell(12);
				cell12.setCellValue(user.getTrackingEndTime());
				sheet.setColumnWidth(12, HEADER_COLUMN_WIDTH_ERROR_MO[12]);

				HSSFCell cell13 = row.createCell(13);
				String[] trackDays = user.getTrackingDays().split(";");
				int i = 0;
				for (String numberDays : trackDays) {
					trackDays[i] = DAYS[Integer.parseInt(numberDays)];
					i++;
				}
				String trackingDays = StringUtils.join(trackDays, ";");
				cell13.setCellValue(trackingDays);
				sheet.setColumnWidth(13, HEADER_COLUMN_WIDTH_ERROR_MO[13]);
				
				HSSFCell cell14 = row.createCell(14);
				cell14.setCellValue(listErrorText.get(k).toString());
				sheet.setColumnWidth(14, HEADER_COLUMN_WIDTH_ERROR_MO[14]);
				
			} 
			else if (GlobalVal.SUBSYSTEM_MS.equals(subsystem)) {
				HSSFCell cell = row.createCell(0);
				cell.setCellValue(user.getLoginId());
				sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH_ERROR_MS[0]);

				HSSFCell cell1 = row.createCell(1);
				cell1.setCellValue(user.getFullName());
				sheet.setColumnWidth(1, HEADER_COLUMN_WIDTH_ERROR_MS[1]);

				HSSFCell cell2 = row.createCell(2);
				if (user.getMsJob() != null) {					
					cell2.setCellValue(user.getMsJob().getJobCode());
				}
				sheet.setColumnWidth(2, HEADER_COLUMN_WIDTH_ERROR_MS[2]);

				HSSFCell cell3 = row.createCell(3);
				if (user.getMsBranch() != null)
					cell3.setCellValue(user.getMsBranch().getBranchCode());
				sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH_ERROR_MS[3]);

				HSSFCell cell4 = row.createCell(4);
				if (user.getAmMsuser() == null) {
					cell4.setCellValue(valueusermanager);
				} 
				else {
					cell4.setCellValue(user.getAmMsuser().getLoginId());
				}
				sheet.setColumnWidth(4, HEADER_COLUMN_WIDTH_ERROR_MS[4]);

				HSSFCell cell5 = row.createCell(5);
				cell5.setCellValue(user.getPhone());
				sheet.setColumnWidth(5, HEADER_COLUMN_WIDTH_ERROR_MS[5]);

				HSSFCell cell6 = row.createCell(6);
				cell6.setCellValue(user.getEmail());
				sheet.setColumnWidth(6, HEADER_COLUMN_WIDTH_ERROR_MS[6]);

				HSSFCell cell7 = row.createCell(7);
				cell7.setCellValue(user.getImei());
				sheet.setColumnWidth(7, HEADER_COLUMN_WIDTH_ERROR_MS[7]);

				HSSFCell cell8 = row.createCell(8);
				cell8.setCellValue(user.getImei2());
				sheet.setColumnWidth(8, HEADER_COLUMN_WIDTH_ERROR_MS[8]);

				HSSFCell cell9 = row.createCell(9);
				cell9.setCellValue(user.getIsTracking());
				sheet.setColumnWidth(9, HEADER_COLUMN_WIDTH_ERROR_MS[9]);

				HSSFCell cell10 = row.createCell(10);
				cell10.setCellValue(user.getTrackingStartTime());
				sheet.setColumnWidth(10, HEADER_COLUMN_WIDTH_ERROR_MS[10]);

				HSSFCell cell11 = row.createCell(11);
				cell11.setCellValue(user.getTrackingEndTime());
				sheet.setColumnWidth(11, HEADER_COLUMN_WIDTH_ERROR_MS[11]);
				
				HSSFCell cell12 = row.createCell(12);
				String[] trackDays = user.getTrackingDays().split(";");
				int i = 0;
				for (String numberDays : trackDays) {
					trackDays[i] = DAYS[Integer.parseInt(numberDays)];
					i++;
				}
				String trackingDays = StringUtils.join(trackDays, ";");
				cell12.setCellValue(trackingDays);
				sheet.setColumnWidth(12, HEADER_COLUMN_WIDTH_ERROR_MS[12]);

				HSSFCell cell13 = row.createCell(13);
				cell13.setCellValue(listErrorText.get(k).toString());
				sheet.setColumnWidth(13, HEADER_COLUMN_WIDTH_ERROR_MS[13]);
			} 
			else if (GlobalVal.SUBSYSTEM_MC.equals(subsystem)
					&& "1".equals(flagCoh)) {
				HSSFCell cell = row.createCell(0);
				cell.setCellValue(user.getLoginId());
				sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH_ERROR_MC_COH[0]);

				HSSFCell cell1 = row.createCell(1);
				cell1.setCellValue(user.getFullName());
				sheet.setColumnWidth(1, HEADER_COLUMN_WIDTH_ERROR_MC_COH[1]);

				HSSFCell cell2 = row.createCell(2);
				if (user.getMsJob() != null) {					
					cell2.setCellValue(user.getMsJob().getJobCode());					
				}
				sheet.setColumnWidth(2, HEADER_COLUMN_WIDTH_ERROR_MC_COH[2]);

				HSSFCell cell3 = row.createCell(3);
				if (user.getMsBranch() != null)
					cell3.setCellValue(user.getMsBranch().getBranchCode());
				sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH_ERROR_MC_COH[3]);

				HSSFCell cell4 = row.createCell(4);
				if (user.getAmMsuser() == null) {
					cell4.setCellValue(valueusermanager);
				} 
				else {
					cell4.setCellValue(user.getAmMsuser().getLoginId());
				}
				sheet.setColumnWidth(4, HEADER_COLUMN_WIDTH_ERROR_MC_COH[4]);

				HSSFCell cell5 = row.createCell(5);
				cell5.setCellValue(user.getInitialName());
				sheet.setColumnWidth(5, HEADER_COLUMN_WIDTH_ERROR_MC_COH[5]);

				HSSFCell cell6 = row.createCell(6);
				cell6.setCellValue(user.getPhone());
				sheet.setColumnWidth(6, HEADER_COLUMN_WIDTH_ERROR_MC_COH[6]);

				HSSFCell cell7 = row.createCell(7);
				cell7.setCellValue(user.getEmail());
				sheet.setColumnWidth(7, HEADER_COLUMN_WIDTH_ERROR_MC_COH[7]);

				HSSFCell cell8 = row.createCell(8);
				cell8.setCellValue(user.getImei());
				sheet.setColumnWidth(8, HEADER_COLUMN_WIDTH_ERROR_MC_COH[8]);

				HSSFCell cell9 = row.createCell(9);
				cell9.setCellValue(user.getImei2());
				sheet.setColumnWidth(9, HEADER_COLUMN_WIDTH_ERROR_MC_COH[9]);

				HSSFCell cell10 = row.createCell(10);
				if (!StringUtils.isBlank(user.getFacebookId())) {
					cell10.setCellValue(user.getFacebookId());
				} 
				else {
					if(null == user.getCashLimit()
							|| "".equals(user.getCashLimit())) {
						cell10.setCellValue("");
					}
					else{
						cell10.setCellValue(user.getCashLimit().doubleValue());
					}
				}
				sheet.setColumnWidth(10, HEADER_COLUMN_WIDTH_ERROR_MC_COH[10]);

				HSSFCell cell11 = row.createCell(11);
				cell11.setCellValue(user.getIsTracking());
				sheet.setColumnWidth(11, HEADER_COLUMN_WIDTH_ERROR_MC_COH[11]);

				HSSFCell cell12 = row.createCell(12);
				cell12.setCellValue(user.getTrackingStartTime());
				sheet.setColumnWidth(12, HEADER_COLUMN_WIDTH_ERROR_MC_COH[12]);

				HSSFCell cell13 = row.createCell(13);
				cell13.setCellValue(user.getTrackingEndTime());
				sheet.setColumnWidth(13, HEADER_COLUMN_WIDTH_ERROR_MC_COH[13]);
				
				HSSFCell cell14 = row.createCell(14);
				String[] trackDays = user.getTrackingDays().split(";");
				int i = 0;
				for (String numberDays : trackDays) {
					trackDays[i] = DAYS[Integer.parseInt(numberDays)];
					i++;
				}
				String trackingDays = StringUtils.join(trackDays, ";");
				cell14.setCellValue(trackingDays);
				sheet.setColumnWidth(14, HEADER_COLUMN_WIDTH_ERROR_MC_COH[13]);

				HSSFCell cell15 = row.createCell(15);
				cell15.setCellValue(listErrorText.get(k).toString());
				sheet.setColumnWidth(15, HEADER_COLUMN_WIDTH_ERROR_MC_COH[15]);
				
			} 
			else if (GlobalVal.SUBSYSTEM_MC.equals(subsystem)) {
				HSSFCell cell = row.createCell(0);
				cell.setCellValue(user.getLoginId());
				sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH_ERROR_MC[0]);

				HSSFCell cell1 = row.createCell(1);
				cell1.setCellValue(user.getFullName());
				sheet.setColumnWidth(1, HEADER_COLUMN_WIDTH_ERROR_MC[1]);

				HSSFCell cell2 = row.createCell(2);
				if (user.getMsJob() != null) {					
					cell2.setCellValue(user.getMsJob().getJobCode());					
				}
				sheet.setColumnWidth(2, HEADER_COLUMN_WIDTH_ERROR_MC[2]);

				HSSFCell cell3 = row.createCell(3);
				if (user.getMsBranch() != null)
					cell3.setCellValue(user.getMsBranch().getBranchCode());
				sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH_ERROR_MC[3]);

				HSSFCell cell4 = row.createCell(4);
				if (user.getAmMsuser() == null) {
					cell4.setCellValue(valueusermanager);
				}
				else {
					cell4.setCellValue(user.getAmMsuser().getLoginId());
				}
				sheet.setColumnWidth(4, HEADER_COLUMN_WIDTH_ERROR_MC[4]);

				HSSFCell cell5 = row.createCell(5);
				cell5.setCellValue(user.getInitialName());
				sheet.setColumnWidth(5, HEADER_COLUMN_WIDTH_ERROR_MC[5]);

				HSSFCell cell6 = row.createCell(6);
				cell6.setCellValue(user.getPhone());
				sheet.setColumnWidth(6, HEADER_COLUMN_WIDTH_ERROR_MC[6]);

				HSSFCell cell7 = row.createCell(7);
				cell7.setCellValue(user.getEmail());
				sheet.setColumnWidth(7, HEADER_COLUMN_WIDTH_ERROR_MC[7]);

				HSSFCell cell8 = row.createCell(8);
				cell8.setCellValue(user.getImei());
				sheet.setColumnWidth(8, HEADER_COLUMN_WIDTH_ERROR_MC[8]);

				HSSFCell cell9 = row.createCell(9);
				cell9.setCellValue(user.getImei2());
				sheet.setColumnWidth(9, HEADER_COLUMN_WIDTH_ERROR_MC[9]);

				HSSFCell cell10 = row.createCell(10);
				cell10.setCellValue(user.getIsTracking());
				sheet.setColumnWidth(10, HEADER_COLUMN_WIDTH_ERROR_MC[10]);

				HSSFCell cell11 = row.createCell(11);
				cell11.setCellValue(user.getTrackingStartTime());
				sheet.setColumnWidth(11, HEADER_COLUMN_WIDTH_ERROR_MC[11]);

				HSSFCell cell12 = row.createCell(12);
				cell12.setCellValue(user.getTrackingEndTime());
				sheet.setColumnWidth(12, HEADER_COLUMN_WIDTH_ERROR_MC[12]);

				HSSFCell cell13 = row.createCell(13);
				cell13.setCellValue(user.getTrackingDays());
				sheet.setColumnWidth(13, HEADER_COLUMN_WIDTH_ERROR_MC[13]);
				
				HSSFCell cell14 = row.createCell(14);
				cell14.setCellValue(listErrorText.get(k).toString());
				sheet.setColumnWidth(14, HEADER_COLUMN_WIDTH_ERROR_MC[14]);
			}
			j++;
			k++;
		}
	}

	@Override
	public String getUpDownEnabled(String gsCode, AuditContext callerId) {
		String UpDownEnabled = "0";

			Object[][] params = { { Restrictions.eq("gsCode", gsCode) } };
			AmGeneralsetting result = this.getManagerDAO().selectOne(
					AmGeneralsetting.class, params);
			if (result != null) {
				UpDownEnabled = result.getGsValue();
			}

		return UpDownEnabled;
	}
	
	@Override
	public boolean validatePassword(String password){
		int flag = 0;
		Pattern p = Pattern.compile("[a-z]");
		Pattern q = Pattern.compile("[0-9]");
		Pattern r = Pattern.compile("[A-Z]");
		Pattern s = Pattern.compile("[[^A-Za-z0-9]]");
		Matcher a = p.matcher(password);
		Matcher b = q.matcher(password);
		Matcher c = r.matcher(password);
		Matcher d = s.matcher(password);
		if(a.find()){
			flag++;
		}
		if(b.find()){
			flag++;
		}
		if(c.find()){
			flag++;
		}
		if(d.find()){
			flag++;
		}
		
		if(flag >= 2){
			return true;
		}else{
			return false;
		}
	}
}
