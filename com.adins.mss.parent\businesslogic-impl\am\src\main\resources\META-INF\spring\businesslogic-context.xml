<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
    xmlns:tx="http://www.springframework.org/schema/tx"
    xmlns:task="http://www.springframework.org/schema/task"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
	http://www.springframework.org/schema/aop
	http://www.springframework.org/schema/aop/spring-aop.xsd
    http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx.xsd
    http://www.springframework.org/schema/task
    http://www.springframework.org/schema/task/spring-task-3.0.xsd ">

	<bean class="org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor" /> 
	<bean id="framework.DaoAspect" class="com.adins.framework.persistence.dao.aspect.DaoAspect" />
    <aop:aspectj-autoproxy />
	
	<tx:annotation-driven order="5" />
	
	<!-- Task Scheduling -->
	<task:annotation-driven/>
	
	<bean id="transactionManager" class="org.springframework.orm.hibernate4.HibernateTransactionManager">
	    <property name="sessionFactory" ref="hibernateSessionFactoryBean" />
	</bean>
	
    <bean id="GlobalManagerDAO" class="com.adins.framework.persistence.dao.hibernate.manager.global.GlobalManagerDao" scope="singleton">
		<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    </bean>

    <bean id="AuditlogLogicBean" class="com.adins.mss.base.businesslogic.AuditlogLogic" scope="singleton">
     	<property name="managerDAO" ref="GlobalManagerDAO"/>
    </bean>
    
    <bean id="AuditTrail" class="com.adins.framework.persistence.dao.hibernate.interceptor.audit.GenericAuditLog" scope="singleton">
    	<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    	<property name="auditManager" ref="AuditlogLogicBean"/>
    </bean>
        
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
	    <property name="staticMethod" value="com.adins.framework.persistence.dao.hibernate.session.GlobalSessionFactory.setAuditManagerInstance"/>
	    <property name="arguments">
	        <list>
	            <ref bean="AuditlogLogicBean" />
	        </list>
	   </property>
	</bean>

	
	<!-- RULES -->
    <bean id="GenericRuleRepositoryLogicBean" class="com.adins.foundation.rules.businesslogic.impl.GenericRuleRepositoryLogic" scope="singleton">
     	<property name="managerDao" ref="GlobalManagerDAO"/>
    </bean>
    <bean id="GenericRuleLogicBean" class="com.adins.foundation.rules.businesslogic.impl.GenericRuleLogic" scope="singleton">
     	<property name="repositoryLogic" ref="GenericRuleRepositoryLogicBean"/>
     	<property name="debug" value="true"/>
    </bean>
	
	<!-- 	MENGGUNAKAN JMS -->
    <!-- START JMS TEMPLATE -->
	<bean id="jndiTemplate" class="org.springframework.jndi.JndiTemplate">
		<property name="environment">
			<props>
				<prop key="java.naming.factory.initial">org.jboss.naming.remote.client.InitialContextFactory
				</prop>
				<prop key="java.naming.provider.url">http-remoting:${jms.uri}</prop>
				<prop key="java.naming.security.principal">${jms.principal}</prop>
				<prop key="java.naming.security.credentials">${jms.credential}</prop>
			</props>
		</property>
	</bean>
	<bean id="connectionFactory" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiTemplate" ref="jndiTemplate" />
		<property name="jndiName" value="jms/RemoteConnectionFactory" />
	</bean>
	<bean id="queueConnectionFactory" class="org.springframework.jms.connection.UserCredentialsConnectionFactoryAdapter">
		<property name="targetConnectionFactory" ref="connectionFactory" />
		<property name="username" value="${jms.principal}" />
		<property name="password" value="${jms.credential}" />
	</bean>
	<bean id="destination" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiTemplate" ref="jndiTemplate" />
		<property name="jndiName" value="${jms.queue}" />
	</bean>
	<bean id="telecheckDestination" class="org.springframework.jndi.JndiObjectFactoryBean">
		<property name="jndiTemplate" ref="jndiTemplate" />
		<property name="jndiName" value="${jms.queue.telecheck}" />
	</bean>
	<bean id="jmsConnectionFactory" class="org.springframework.jms.connection.CachingConnectionFactory">
		<property name="targetConnectionFactory" ref="queueConnectionFactory" />
		<property name="sessionCacheSize" value="50" />
	</bean>
	<bean id="jmsTemplate" class="org.springframework.jms.core.JmsTemplate">
		<property name="connectionFactory" ref="jmsConnectionFactory" />
	</bean>
    
	<bean id="BaseLogicBean" class="com.adins.mss.base.businesslogic.BaseLogic" scope="singleton">
     	<property name="managerDAO" ref="GlobalManagerDAO"/>
     	<property name="auditManager" ref="AuditTrail"/>
     	<property name="jmsTemplate" ref="jmsTemplate"/> 
     	<property name="queue" ref="destination"/>
     	<property name="queueTelecheck" ref="telecheckDestination"/>
    </bean>
	<!-- END JMS TEMPLATE -->
	
	
	
	<!-- 	BASELOGIC TANPA JMS  -->
	<!-- <bean id="BaseLogicBean" class="com.adins.mss.base.businesslogic.BaseLogic" scope="singleton">
     	<property name="managerDAO" ref="GlobalManagerDAO"/>
     	<property name="auditManager" ref="AuditTrail"/>
    </bean> -->
    
    <bean id="DefaultLdapBean" class="com.adins.framework.tool.ldap.UnboundidLdapLogicImpl">
    	<property name="host" value="${ldap.host}" />
    	<property name="port" value="${ldap.port}" />
    </bean>
    
	<!-- Modul AM-->
	<bean id="GenericLoginLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericLoginLogic" scope="singleton" parent="BaseLogicBean">
		<property name="eventLogLogic" ref="GenericEventLogLogicBean"/>
		<property name="globalLogic" ref="GenericGlobalLogicBean"/>
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="ldapLogic" ref="DefaultLdapBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>
	<bean id="GenericLayoutLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericLayoutLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="ChangePasswordLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericChangePasswordLogic" scope="singleton" parent="BaseLogicBean">
		<property name="eventLogLogic" ref="GenericEventLogLogicBean"/>
		<property name="globalLogic" ref="GenericGlobalLogicBean"/>
		<property name="loginLogic" ref="GenericLoginLogicBean"/>
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericEventLogLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericEventLogLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericGlobalLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericGlobalLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericUserManagementLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericUserManagementLogic" scope="singleton" parent="BaseLogicBean">
		<property name="globalLogic" ref="GenericGlobalLogicBean"/>
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericGroupMasterLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericGroupMasterLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericJobVsGroupLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericJobVsGroupLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericGeneralSettingLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericGeneralSettingLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericToDoListLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericToDoListLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericAboutLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericAboutLogic" scope="singleton" parent="BaseLogicBean"/>
    
    <!-- Initializer -->
    <bean id="GenericContextLogic" class="com.adins.mss.businesslogic.impl.am.GenericContextLogic" scope="singleton" parent="BaseLogicBean" />
    
    <bean id="GenericPermissionLogicBean" class="com.adins.mss.businesslogic.impl.am.GenericPermissionLogic" scope="singleton" parent="BaseLogicBean" />
    
    <!-- AOP -->
	<bean id="AuditContextAspect" class="com.adins.framework.service.base.aspect.AuditContextAspect" scope="singleton" />
	
	<aop:config>
	  <aop:aspect id="aspect.auditContext" ref="AuditContextAspect">
	    <aop:pointcut id="blPointCut" expression="execution(* com.adins.mss.businesslogic.api..*(..))" />
	    <aop:before method="putAuditContext" pointcut-ref="blPointCut" />
	    <aop:after method="removeAuditContext" pointcut-ref="blPointCut" />
	  </aop:aspect>
	</aop:config>
</beans>