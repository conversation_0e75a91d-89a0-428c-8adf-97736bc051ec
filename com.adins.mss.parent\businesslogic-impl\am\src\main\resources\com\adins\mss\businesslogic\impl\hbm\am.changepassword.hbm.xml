<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="am.changepassword.getPasswordChanges">
	<query-param name="uuidMsUser" type="string"/>
	<query-param name="totalGetPassword" type="string"/>
		SELECT TOP (select cast(:totalGetPassword as int) ) PASSWORD, CHANGE_TYPE
		FROM dbo.AM_USERPWDHISTORY with (nolock)
		WHERE UUID_MS_USER = :uuidMsUser
		ORDER BY DTM_CRT DESC
	</sql-query>
</hibernate-mapping>