<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="am.context.getUuidUser">
		<query-param name="loginId" type="string" />
	SELECT UUID_MS_USER
	FROM AM_MSUSER with (nolock)
	WHERE LOGIN_ID = :loginId
	</sql-query>
	
	<sql-query name="am.context.getMenuOfUser">
		<query-param name="uuidUser" type="long" />
	select mn.menu_reff
	from am_memberofgroup member with (nolock)
		left join am_menuofgroup menu with (nolock) on member.uuid_ms_group = menu.uuid_ms_group
	    left join am_msmenu mn with (nolock) on menu.uuid_ms_menu = mn.uuid_ms_menu
	    left join AM_MSGROUP mg with (nolock) on mg.UUID_MS_GROUP = menu.UUID_MS_GROUP
	where member.uuid_ms_user = :uuidUser
		and mg.IS_ACTIVE = '1' and mg.IS_DELETED = '0'
	</sql-query>

</hibernate-mapping>