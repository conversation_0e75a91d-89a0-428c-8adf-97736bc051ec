<?xml version="1.0"?>
<!DOCTY<PERSON><PERSON> hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	 <sql-query  name="am.groupmaster.groupMobileMenuHirarkiList">
		 <query-param  name="uuidMsSubsystem" type="long"/>
		  WITH N AS (
				SELECT mobmenu.UUID_MS_MOBILE_MENU, mobmenu.PARENT_MENU_ID, 
					mobmenu.UUID_MS_SUBSYSTEM, mobmenu.IS_ACTIVE, mobmenu.MENU_TYPE, 
					mobmenu.MENU_LEVEL, mobmenu.MENU_ORDER, mobmenu.MENU_PROMPT, 
					mobmenu.USR_CRT, mobmenu.DTM_CRT, mobmenu.USR_UPD, mobmenu.DTM_UPD,
					mobmenu.UUID_MS_MOBILE_MENU AS HIRARKI,
					CAST(mobmenu.UUID_MS_MOBILE_MENU AS VARCHAR(MAX)) AS HIRARKI2, 
					0 as LEVEL
				FROM  AM_MSMOBILEMENU mobmenu with (nolock)
				WHERE mobmenu.PARENT_MENU_ID IS NULL
				UNION ALL
				SELECT mobmenu2.UUID_MS_MOBILE_MENU, mobmenu2.PARENT_MENU_ID, 
					mobmenu2.UUID_MS_SUBSYSTEM, mobmenu2.IS_ACTIVE, mobmenu2.MENU_TYPE, 
					mobmenu2.MENU_LEVEL, mobmenu2.MENU_ORDER, mobmenu2.MENU_PROMPT,
					mobmenu2.USR_CRT, mobmenu2.DTM_CRT, mobmenu2.USR_UPD, mobmenu2.DTM_UPD,
					N.HIRARKI, N.HIRARKI2+'/'+CAST(mobmenu2.MENU_PROMPT AS VARCHAR(MAX)), 
					N.LEVEL+1
				FROM   AM_MSMOBILEMENU mobmenu2 with (nolock),N
				WHERE N.UUID_MS_MOBILE_MENU=mobmenu2.PARENT_MENU_ID
				)
		SELECT
		  	N.UUID_MS_MOBILE_MENU, N.PARENT_MENU_ID, N.UUID_MS_SUBSYSTEM, 
		  	N.IS_ACTIVE, N.MENU_TYPE, N.MENU_LEVEL, N.MENU_ORDER, N.MENU_PROMPT,  
		  	N.LEVEL, ROW_NUMBER() OVER (ORDER BY N.HIRARKI, N.HIRARKI2) AS rownum 
		FROM N left outer 
			join AM_MSMOBILEMENU D with (nolock) on N.PARENT_MENU_ID=D.UUID_MS_MOBILE_MENU
		WHERE N.UUID_MS_SUBSYSTEM = :uuidMsSubsystem AND N.IS_ACTIVE = '1'
 	</sql-query>
 
  <sql-query  name="am.groupmaster.groupMenuHirarkiList">
		<query-param  name="uuidMsSubsystem" type="string"/>
	  	WITH N AS (
			SELECT msmenu.UUID_MS_MENU, msmenu.IS_ACTIVE, msmenu.UUID_MS_SUBSYSTEM, 
				msmenu.PARENT_MENU_ID, msmenu.MENU_SIDE, msmenu.MENU_TYPE, msmenu.MENU_LEVEL, 
				msmenu.MENU_ORDER, msmenu.MENU_PROMPT, msmenu.MENU_REFF, msmenu.USR_CRT, 
				msmenu.DTM_CRT, msmenu.USR_UPD, msmenu.DTM_UPD, msmenu.UUID_MS_MENU AS HIRARKI,
				CAST(msmenu.UUID_MS_MENU AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
			FROM  AM_MSMENU msmenu with (nolock)
			WHERE msmenu.PARENT_MENU_ID IS NULL
			UNION ALL
			SELECT msmenu2.UUID_MS_MENU, msmenu2.IS_ACTIVE, msmenu2.UUID_MS_SUBSYSTEM, 
				msmenu2.PARENT_MENU_ID, msmenu2.MENU_SIDE, msmenu2.MENU_TYPE, 
				msmenu2.MENU_LEVEL, msmenu2.MENU_ORDER, msmenu2.MENU_PROMPT, 
				msmenu2.MENU_REFF, msmenu2.USR_CRT, msmenu2.DTM_CRT, msmenu2.USR_UPD, 
				msmenu2.DTM_UPD, N.HIRARKI, N.HIRARKI2+'/'+CAST(msmenu2.MENU_PROMPT AS VARCHAR(MAX)), 
				N.LEVEL+1
			FROM AM_MSMENU msmenu2 with (nolock),N
			WHERE N.UUID_MS_MENU = msmenu2.PARENT_MENU_ID
			)
		SELECT
		  	N.UUID_MS_MENU, N.IS_ACTIVE, N.UUID_MS_SUBSYSTEM, N.PARENT_MENU_ID, 
		  	N.MENU_SIDE, N.MENU_TYPE, N.MENU_LEVEL, N.MENU_ORDER, N.MENU_PROMPT, 
		  	N.LEVEL, ROW_NUMBER() OVER (ORDER BY N.HIRARKI, N.HIRARKI2) AS rownum 
		FROM N left outer 
			join AM_MSMENU D with (nolock) on N.PARENT_MENU_ID=D.UUID_MS_MENU
		WHERE N.UUID_MS_SUBSYSTEM = :uuidMsSubsystem AND N.IS_ACTIVE = '1'
 	</sql-query>
 
	<sql-query name="am.groupmaster.delAllMobileMenu">
		<query-param name="uuidMsGroup" type="string" />
		delete from AM_MOBILEMENUOFGROUP where UUID_MS_GROUP = :uuidMsGroup
	</sql-query>
	
	<sql-query name="am.groupmaster.delAllToDoList">
		<query-param name="uuidMsGroup" type="string" />
		delete from AM_TODOLISTOFGROUP where UUID_MS_GROUP = :uuidMsGroup
	</sql-query>
	
	<sql-query name="am.groupmaster.delAllMenu">
		<query-param name="uuidMsGroup" type="string" />
		delete from AM_MENUOFGROUP where UUID_MS_GROUP = :uuidMsGroup
	</sql-query>
	
	<sql-query name="am.groupmaster.getAppCombo">
		select UUID_MS_APPLICATION, APPLICATION_NAME from AM_MSAPPLICATION with (nolock)
	</sql-query>
	
	<sql-query name="am.groupmaster.checkHasChild">
		<query-param name="uuidMsGroup" type="string" />
		SELECT mg.UUID_MS_GROUP
		FROM AM_MSGROUP mg with (nolock)
		WHERE mg.UUID_MS_GROUP NOT IN (SELECT mog.UUID_MS_GROUP FROM AM_MEMBEROFGROUP mog)
			AND mg.UUID_MS_GROUP NOT IN (SELECT menog.UUID_MS_GROUP FROM AM_MENUOFGROUP menog)
			AND mg.UUID_MS_GROUP NOT IN (SELECT mmog.UUID_MS_GROUP FROM AM_MOBILEMENUOFGROUP mmog)
			AND mg.UUID_MS_GROUP NOT IN (SELECT tdlog.UUID_MS_GROUP FROM AM_TODOLISTOFGROUP tdlog)
			AND mg.UUID_MS_GROUP NOT IN (SELECT fog.UUID_MS_GROUP FROM MS_FORMOFGROUP fog)
			AND mg.UUID_MS_GROUP NOT IN (SELECT gog.UUID_MS_GROUP FROM MS_GROUPOFJOB gog)
			AND mg.UUID_MS_GROUP = :uuidMsGroup 
	</sql-query>
	
	<sql-query name="am.groupmaster.getUserByLogin">
		<query-param name="uuidMsUser" type="string"/>
		<query-param name="loginId" type="string"/>
		<query-param name="fullName" type="string"/>
		select keyValue, loginId, fullName, userLevel, ROW_NUMBER() over(order by userLevel) as row
		from dbo.getUserByLogin(:uuidMsUser)
		where keyValue != :uuidMsUser
			AND lower(loginId) like lower('%' + :loginId + '%') 
			AND lower(fullName) like lower('%' + :fullName + '%')
	</sql-query>
	
	<sql-query name="am.groupmaster.getListAddUserByLogin">
		<query-param name="uuidMsUser" type="string"/>
		<query-param name="uuidMsGroup" type="string"/>
		<query-param name="loginId" type="string"/>
		<query-param name="fullName" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * FROM 
		(
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM 
			(
				select keyValue as UUID_MS_USER, loginId as LOGIN_ID, fullName as FULL_NAME,
					ROW_NUMBER() OVER ( order by keyValue ) AS rownum
				from dbo.getUserByLogin(:uuidMsUser)
				where keyValue != :uuidMsUser
					AND lower(loginId) like lower('%' + :loginId + '%') 
					AND lower(fullName) like lower('%' + :fullName + '%')
					AND keyValue not in
					(
						SELECT UUID_MS_USER 
						FROM AM_MEMBEROFGROUP with (nolock) 
						WHERE UUID_MS_GROUP = :uuidMsGroup
					)
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="am.groupmaster.getCountListAddUserByLogin">
		<query-param name="uuidMsUser" type="string"/>
		<query-param name="uuidMsGroup" type="string"/>
		<query-param name="loginId" type="string"/>
		<query-param name="fullName" type="string"/>
		<return-scalar column="count" type="long"/>
		select count(keyValue) as count
		from dbo.getUserByLogin(:uuidMsUser)
		where keyValue != :uuidMsUser
			AND lower(loginId) like lower('%' + :loginId + '%') 
			AND lower(fullName) like lower('%' + :fullName + '%')
			AND keyValue not in
			(
				SELECT UUID_MS_USER 
				FROM AM_MEMBEROFGROUP with (nolock) 
				WHERE UUID_MS_GROUP = :uuidMsGroup
			)
	</sql-query>
	
	<sql-query name="am.groupmaster.listFeature">
		<query-param name="uuidMsGroup" type="long" />
		select amuf.UUID_MS_MENU, amuf.UUID_MSMENU_FEATURE, amuf.FEATURE_CODE, 
			amuf.FEATURE_NAME, 
			case when 
			(
				select AMFOG.UUID_MENUFEATOFGROUP 
				from AM_MENUFEATUREOFGROUP amfog with (nolock)
				where amfog.UUID_MSMENU_FEATURE = amuf.UUID_MSMENU_FEATURE
					and amfog.UUID_MS_GROUP = :uuidMsGroup
			) 
			is null then '0' else '1' end 
		from AM_MSMENUFEATURE amuf with (nolock)
		where amuf.IS_ACTIVE = '1'
		order by amuf.FEATURE_CODE asc
	</sql-query>
	
	<sql-query name="am.groupmaster.delAllMenuFeature">
		<query-param name="uuidMsGroup" type="long" />
		delete from AM_MENUFEATUREOFGROUP where UUID_MS_GROUP = :uuidMsGroup
	</sql-query>
</hibernate-mapping>