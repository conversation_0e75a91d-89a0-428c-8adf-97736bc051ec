<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="am.jobvsgroup.getJobHirarkiList">
		<query-param name="uuidSubsystem" type="string"/>
		<query-param name="jobCode" type="string"/>
		<query-param name="description" type="string"/>
		<query-param name="isBranch" type="string"/>
		<query-param name="isFieldPerson" type="string"/>
		<query-param name="isActive" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
			SELECT msj.UUID_JOB, msj.PARENT_JOB, msj.UUID_MS_SUBSYSTEM, msj.IS_ACTIVE, msj.JOB_CODE, 
				msj.DESCRIPTION, msj.IS_FIELD_PERSON, msj.IS_BRANCH,msj.USR_CRT, msj.DTM_CRT, 
				msj.USR_UPD, msj.DTM_UPD, msj.UUID_JOB AS HIRARKI,
				CAST(msj.UUID_JOB AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
			FROM  MS_JOB msj with (nolock)
			WHERE msj.PARENT_JOB IS NULL
			UNION ALL
			SELECT msj2.UUID_JOB, msj2.PARENT_JOB, msj2.UUID_MS_SUBSYSTEM, msj2.IS_ACTIVE, 
				msj2.JOB_CODE, msj2.DESCRIPTION, msj2.IS_FIELD_PERSON, msj2.IS_BRANCH,msj2.USR_CRT, 
				msj2.DTM_CRT, msj2.USR_UPD, msj2.DTM_UPD, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msj2.JOB_CODE AS VARCHAR(MAX)), N.LEVEL+1
			FROM MS_JOB msj2 with (nolock),N
			WHERE N.UUID_JOB = MSJ2.PARENT_JOB
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY a.IS_ACTIVE desc, ROWNUM ASC) AS recnum FROM (
				SELECT N.UUID_JOB, N.JOB_CODE, N.DESCRIPTION, N.PARENT_JOB, N.UUID_MS_SUBSYSTEM, 
					N.IS_ACTIVE, N.IS_FIELD_PERSON, N.IS_BRANCH, N.LEVEL, 
					ROW_NUMBER() OVER (ORDER BY N.HIRARKI, N.HIRARKI2) AS rownum 
				FROM N left outer join MS_JOB D with (nolock) on N.PARENT_JOB=D.UUID_JOB
				WHERE N.IS_ACTIVE like '%'+ :isActive +'%'
					AND N.UUID_MS_SUBSYSTEM = :uuidSubsystem
					AND N.JOB_CODE like lower('%'+ :jobCode +'%') 
					AND N.DESCRIPTION like lower('%'+ :description +'%')
					AND N.IS_BRANCH like lower('%'+ :isBranch +'%')
					AND N.IS_FIELD_PERSON like lower('%'+ :isFieldPerson +'%')
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="am.jobvsgroup.getJobHirarkiListCount">
		<query-param name="uuidSubsystem" type="string"/>
		<query-param name="jobCode" type="string"/>
		<query-param name="description" type="string"/>
		<query-param name="isBranch" type="string"/>
		<query-param name="isFieldPerson" type="string"/>
		<query-param name="isActive" type="string"/>
		WITH N AS (
			SELECT msj.UUID_JOB, msj.PARENT_JOB, msj.UUID_MS_SUBSYSTEM, msj.IS_ACTIVE, msj.JOB_CODE, 
				msj.DESCRIPTION, msj.IS_FIELD_PERSON, msj.IS_BRANCH,msj.USR_CRT, msj.DTM_CRT, 
				msj.USR_UPD, msj.DTM_UPD, msj.UUID_JOB AS HIRARKI,
				CAST(msj.UUID_JOB AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		   	FROM  MS_JOB msj with (nolock)
		   	WHERE msj.PARENT_JOB IS NULL
		   	UNION ALL
		   	SELECT msj2.UUID_JOB, msj2.PARENT_JOB, msj2.UUID_MS_SUBSYSTEM, msj2.IS_ACTIVE, 
		   		msj2.JOB_CODE, msj2.DESCRIPTION, msj2.IS_FIELD_PERSON, msj2.IS_BRANCH,
		   		msj2.USR_CRT, msj2.DTM_CRT, msj2.USR_UPD, msj2.DTM_UPD, N.HIRARKI,
		   		N.HIRARKI2+'/'+CAST(msj2.JOB_CODE AS VARCHAR(MAX)), N.LEVEL+1
		 	FROM   MS_JOB msj2 with (nolock),N
		   	WHERE N.UUID_JOB = MSJ2.PARENT_JOB
		)
		SELECT count(N.UUID_JOB)
		FROM N left outer join MS_JOB D with (nolock) on N.PARENT_JOB=D.UUID_JOB
		WHERE N.IS_ACTIVE like '%'+ :isActive +'%'
			AND N.UUID_MS_SUBSYSTEM = :uuidSubsystem
			AND N.JOB_CODE like lower('%'+ :jobCode +'%') 
			AND N.DESCRIPTION like lower('%'+ :description +'%')
			AND N.IS_BRANCH like lower('%'+ :isBranch +'%')
			AND N.IS_FIELD_PERSON like lower('%'+ :isFieldPerson +'%')
	</sql-query>
	<sql-query name="am.jobvsgroup.jobCodeValidity">
		<query-param name="jobCode" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		SELECT COUNT(1) 
		FROM MS_JOB with (nolock) 
		WHERE JOB_CODE = :jobCode and UUID_MS_SUBSYSTEM = :uuidSubsystem
	</sql-query>
	<sql-query name="am.jobvsgroup.deleteMsGroupOfJob">
		<query-param name="uuidJob" type="long"/>
		DELETE FROM MS_GROUPOFJOB
		WHERE UUID_JOB = :uuidJob
	</sql-query>
	<sql-query name="am.jobvsgroup.deleteAmMemberOfGroup">
		<query-param name="uuidJob" type="long"/>
		DELETE amog
		FROM AM_MEMBEROFGROUP  amog
		JOIN AM_MSUSER amu WITH(NOLOCK) ON amu.UUID_MS_USER = amog.UUID_MS_USER AND amu.UUID_JOB = :uuidJob
	</sql-query>
	
	<sql-query name="am.jobvsgroup.deleteRoundRobinUser">
		<query-param name="uuidJob" type="long"/>
		DELETE MS_ROUNDROBINTASK  
		where UUID_MS_USER 
		in (select msu.UUID_MS_USER from AM_MSUSER msu with (nolock) where UUID_JOB = :uuidJob)
	</sql-query>
	
	<sql-query name="am.jobvsgroup.insertAmMemberOfGroup">
		<query-param name="uuidJob" type="long"/>
		<query-param name="uuidGroup" type="long"/>
		<query-param name="callerId" type="String"/>
		INSERT INTO AM_MEMBEROFGROUP (USR_CRT, DTM_CRT, UUID_MS_GROUP, UUID_MS_USER)
		SELECT :callerId , GETDATE() , :uuidGroup, UUID_MS_USER
		FROM AM_MSUSER WITH(NOLOCK) WHERE UUID_JOB = :uuidJob
	</sql-query>
	
	<sql-query name="am.jobvsgroup.getMappingJobConfins">
		<query-param name="uuidJob" type="String"/>
		SELECT MAP_JOB_CODE
		FROM TEMP_MAP_JOB_CONFINS with (nolock)  
		where UUID_JOB = :uuidJob
	</sql-query>
	
	<sql-query name="am.jobvsgroup.deleteMappingJobConfinsByUuidjob">
		<query-param name="uuidJob" type="String"/>
		DELETE FROM TEMP_MAP_JOB_CONFINS  
		where UUID_JOB = :uuidJob
	</sql-query>
	
</hibernate-mapping>