<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="am.login.releaseAllUserLoggedIn">
		<query-param name="usrUpd" type="string" />
		UPDATE AM_MSUSER SET
			USR_UPD = :usrUpd,
			DTM_UPD = current_timestamp,
			IS_LOGGED_IN = '0',
			LAST_REQUEST_OUT = null
		WHERE IS_LOGGED_IN = '1'
	</sql-query>

	<sql-query name="am.login.selectGroupByMember">
		<query-param name="uuidMsuser" type="long" />
		select ama.APPLICATION_NAME, ama.APPLICATION_VERSION, ams.SUBSYSTEM_NAME, ams.SUBSYSTEM_VERSION
		from AM_MEMBEROFGROUP amog with (nolock)
			left join AM_MSGROUP amg with (nolock) on amog.UUID_MS_GROUP=amg.UUID_MS_GROUP
			inner join AM_MSSUBSYSTEM ams with (nolock) on
				amg.UUID_MS_SUBSYSTEM=ams.UUID_MS_SUBSYSTEM
			inner join AM_MSAPPLICATION ama with (nolock) on
				ams.UUID_MS_APPLICATION=ama.UUID_MS_APPLICATION
		where amog.UUID_MS_USER = :uuidMsuser
	</sql-query>

	<sql-query name="am.login.getUuid">
		<query-param name="loginId" type="string" />
		SELECT UUID_MS_USER, LOGIN_ID
		FROM AM_MSUSER with (nolock)
		WHERE LOGIN_ID = :loginId
	</sql-query>

	<sql-query name="am.login.getMultiLogin">
		<query-param name="uniqueId" type="string" />
		SELECT usr.LOGIN_ID, br.BRANCH_CODE, br.BRANCH_NAME, j.DESCRIPTION
		FROM AM_MSUSER usr
		join MS_BRANCH br on usr.UUID_BRANCH = br.UUID_BRANCH
		join MS_JOB j on usr.UUID_JOB = j.UUID_JOB
		WHERE UNIQUE_ID = :uniqueId
		and usr.IS_ACTIVE = 1 and usr.IS_DELETED = 0
	</sql-query>
</hibernate-mapping>