<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
    
<hibernate-mapping>
	<sql-query name="am.permission.getpermissions">
		<query-param name="loginId" type="string" />
		select amuf.FEATURE_CODE from AM_MENUFEATUREOFGROUP amfog with (nolock)
			left join AM_MEMBEROFGROUP amog with (nolock) on amfog.UUID_MS_GROUP = amog.UUID_MS_GROUP
			left join AM_MSMENUFEATURE amuf with (nolock) on amfog.UUID_MSMENU_FEATURE = amuf.UUID_MSMENU_FEATURE
		  	left join AM_MSUSER amu with (nolock) on amog.UUID_MS_USER = amu.UUID_MS_USER
		where amu.LOGIN_ID = :loginId
	</sql-query>
	<sql-query name="am.permission.getpermissionsadmin">
		select FEATURE_CODE 
		from AM_MSMENUFEATURE with (nolock)
	</sql-query>
</hibernate-mapping>