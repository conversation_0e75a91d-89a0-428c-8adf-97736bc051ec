<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="am.usermanagement.getBranchByLogin">
		<query-param name="uuidBranch" type="long"/>
		select keyValue, BRANCH_CODE,BRANCH_NAME, branchLevel 
		from dbo.getCabangByLogin(:uuidBranch)
	</sql-query>
	
	<sql-query name="am.usermanagement.getDealerByLogin">
		<query-param name="uuidDealer" type="long"/>
		select keyValue, dealerName, dealerAddress, dealerLevel 
		from dbo.getDealerByLogin(:uuidDealer)
	</sql-query>
	
	<sql-query name="am.usermanagement.getJobByLogin">
		<query-param name="uuidJob" type="long"/>
		select keyValue, jobCode, description, jobLevel 
		from dbo.getJobByLogin(:uuidJob)
	</sql-query>
	
	<sql-query name="am.usermanagement.getUserByLogin">
		<query-param name="uuidMsUser" type="long"/>
		WITH n(UUID_MS_USER,LOGIN_ID, FULL_NAME,UUID_JOB, level) AS 
		(
			SELECT UUID_MS_USER,LOGIN_ID, FULL_NAME, UUID_JOB , 1 level
			FROM AM_MSUSER with (nolock)
			WHERE UUID_MS_USER = :uuidMsUser 
 				AND is_active = '1'
				AND is_deleted = '0'
				
			UNION ALL
			
			SELECT nplus1.UUID_MS_USER, nplus1.LOGIN_ID, nplus1.FULL_NAME, nplus1.UUID_JOB,n.level+1
			FROM AM_MSUSER as nplus1 with (nolock), n
			WHERE n.UUID_MS_USER = nplus1.SPV_ID 
 				AND nplus1.is_active = '1'
    			AND nplus1.is_deleted = '0'
		)
		SELECT UUID_MS_USER as keyValue, LOGIN_ID as loginId, FULL_NAME as fullName, 
			level as userLevel, ROW_NUMBER() over(order by level) as row, j.JOB_CODE
	    FROM n left join MS_JOB j with (nolock) on n.UUID_JOB = j.UUID_JOB
	    order by N.FULL_NAME ASC
	</sql-query>
	
	<sql-query name="am.usermanagement.deleteMemberOfGroup">
		<query-param name="uuidMsUser" type="long"/>
		DELETE FROM AM_MEMBEROFGROUP
		WHERE UUID_MS_USER = :uuidMsUser
	</sql-query>
	
	<sql-query name="am.usermanagement.deleteZipCodeOfUser">
		<query-param name="uuidMsUser" type="long"/>
		DELETE FROM MS_ZIPCODEOFUSER
		WHERE UUID_MS_USER = :uuidMsUser
	</sql-query>
	
	<sql-query name="am.usermanagement.deleteAreaOfUser">
		<query-param name="uuidMsUser" type="long"/>
		DELETE FROM MS_AREAOFUSER
		WHERE UUID_MS_USER = :uuidMsUser
	</sql-query>

	<sql-query name="am.usermanagement.getUserByBranch">
		<query-param name="uuidBranch" type="long"/>
		<query-param name="jobCode" type="string"/>
		SELECT UUID_MS_USER, LOGIN_ID, FULL_NAME, 2
		FROM AM_MSUSER amu with (nolock)
			JOIN MS_JOB mj with (nolock) ON amu.UUID_JOB = mj.UUID_JOB AND amu.UUID_MS_SUBSYSTEM = mj.UUID_MS_SUBSYSTEM
			JOIN 
			(
				SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
				FROM dbo.getCabangByLogin(:uuidBranch)
			)msb 
			on amu.UUID_BRANCH = msb.UUID_BRANCH
			AND mj.JOB_CODE = :jobCode
			AND amu.is_active = '1' AND amu.is_deleted = '0'
	</sql-query>
	
	<sql-query name="am.usermanagement.getJobListAdmin">
		<query-param name="uuidJob" type="long"/>
			SELECT n.keyValue 
			from dbo.getJobByLogin(:uuidJob) n
	</sql-query>
	
	<sql-query name="am.usermanagement.getJobList">
		<query-param name="uuidJob" type="long"/>
			SELECT n.keyValue 
			from dbo.getJobByLogin(:uuidJob) n
			where n.keyValue != :uuidJob
	</sql-query>
	
	<sql-query name="am.usermanagement.getBranch">
		SELECT Branch_code, BRANCH_NAME AS 'BRANCH DESCRIPTION'
		FROM MS_BRANCH WITH (NOLOCK) 
		WHERE IS_ACTIVE = 1
	</sql-query>
	
	<sql-query name="am.usermanagement.getJob">
	<query-param name="SUBSYSTEM_NAME" type="string"/>
		select job.job_code, job.DESCRIPTION 
		from MS_JOB job WITH (NOLOCK) 
			join AM_MSSUBSYSTEM sb WITH (NOLOCK)
			on sb.uuid_ms_subsystem = job.uuid_ms_subsystem
		where job.is_active ='1'
			and sb.SUBSYSTEM_NAME = :SUBSYSTEM_NAME
	</sql-query>
	
	<sql-query name="am.usermanagement.getDealer">
		select dealer_code, dealer_name 
		from MS_DEALER WITH (NOLOCK)
		where is_active = '1'
	</sql-query>
	
	<sql-query name="am.usermanagement.getUserByLoginAll">
		<query-param name="uuidMsUser" type="long"/>
		WITH n(UUID_MS_USER,LOGIN_ID, FULL_NAME,UUID_JOB, level) AS 
		(
			SELECT UUID_MS_USER,LOGIN_ID, FULL_NAME, UUID_JOB , 1 level
			FROM AM_MSUSER with (nolock)
			WHERE UUID_MS_USER = :uuidMsUser
				AND is_deleted = '0'
			
			UNION ALL
			
			SELECT nplus1.UUID_MS_USER, nplus1.LOGIN_ID, nplus1.FULL_NAME, nplus1.UUID_JOB,n.level+1
			FROM AM_MSUSER as nplus1 with (nolock), n
			WHERE n.UUID_MS_USER = nplus1.SPV_ID
    			AND nplus1.is_deleted = '0'
		)
		SELECT UUID_MS_USER as keyValue, LOGIN_ID as loginId, FULL_NAME as fullName, 
			level as userLevel, ROW_NUMBER() over(order by level) as row, j.JOB_CODE
	    FROM n left 
	    	join MS_JOB j with (nolock) on n.UUID_JOB = j.UUID_JOB
	    order by N.FULL_NAME ASC
	</sql-query>
</hibernate-mapping>