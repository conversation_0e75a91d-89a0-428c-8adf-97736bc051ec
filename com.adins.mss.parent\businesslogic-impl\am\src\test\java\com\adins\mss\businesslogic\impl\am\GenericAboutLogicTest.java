package com.adins.mss.businesslogic.impl.am;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.Assert;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.businesslogic.api.am.AboutLogic;
import com.adins.mss.model.AmGeneralsetting;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath*:META-INF/spring/*-context.xml"
})
@Ignore
public class GenericAboutLogicTest {
    @Autowired
    private AboutLogic aboutLogic;
    
//    @Test
//    public void retrieveGeneralSettingTest() {       
//        AmGeneralsetting gs = aboutLogic.getGeneralSetting("PRM12_PART", new AuditContext("ADMIN-MO"));
//        Assert.notNull(gs);
//    }
    
    
}
