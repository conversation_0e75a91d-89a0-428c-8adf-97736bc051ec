<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="net.sourceforge.jtds.jdbc.Driver"/>

		<property name="url" value="************************************************"/>
        <property name="username" value="sa"/>
        <property name="password" value="AdIns2014"/>
	</bean>

	<bean id="hibernateSessionFactoryBean" class="org.springframework.orm.hibernate3.annotation.AnnotationSessionFactoryBean">
		<property name="dataSource" ref="dataSource"/>
        <property name="useTransactionAwareDataSource" value="true"/>
        <property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.SQLServerDialect</prop>
                <prop key="hibernate.show_sql">true</prop>
                <prop key="hibernate.format_sql">false</prop>
                <prop key="dao.jdbc.use_streams_for_binary">true</prop>
            </props>
        </property>
		<property name="packagesToScan">
			<list>
				<value>com.adins.mss.model</value>
			</list>
		</property>
		<property name="mappingLocations">
			<list>
				<value>classpath*:/com/adins/mss/businesslogic/impl/hbm/*.hbm.xml</value>
			</list>
		</property>
    </bean>
    
<!--     <bean id="propHolder" class="com.adins.framework.tool.properties.SpringPropertiesUtils"> -->
<!-- 		<property name="locations"> -->
<!-- 		  <list> -->
<!-- 			<value>classpath:application.properties</value> -->
<!-- 		  </list> -->
<!-- 		</property> -->
<!-- 	</bean> -->

</beans>