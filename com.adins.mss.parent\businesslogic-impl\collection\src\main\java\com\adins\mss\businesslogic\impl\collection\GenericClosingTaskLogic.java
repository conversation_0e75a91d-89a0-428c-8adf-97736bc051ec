package com.adins.mss.businesslogic.impl.collection;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.ClosingTaskLogic;
import com.adins.mss.businesslogic.api.collection.RVNumberLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.ClosingTaskException;
import com.adins.mss.exceptions.ClosingTaskException.Reason;
import com.adins.mss.model.AmMsmobilemenu;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.custom.ClosingTaskBean;
@SuppressWarnings({ "unchecked", "rawtypes" })
public class GenericClosingTaskLogic extends BaseLogic implements ClosingTaskLogic, MessageSourceAware{
	private static final Logger LOG = LoggerFactory.getLogger(GenericClosingTaskLogic.class);
	private RVNumberLogic rvNumberLogic;
	private MessageSource messageSource;
	
	public void setRvNumberLogic(RVNumberLogic rvNumberLogic) {
		this.rvNumberLogic = rvNumberLogic;
	}
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}


	@Override
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	public String closingTask(AuditContext callerId) {
		int countClosingTask = 0;
		String resultClosingTask=StringUtils.EMPTY;
			//cek apakah menu deposit report mobile aktif atau tidak
			String active = StringUtils.EMPTY;
			Object[][] paramMenuDeposit = {{ Restrictions.eq("menuPrompt",GlobalVal.MOBILE_DEPOSIT_REPORT) }};
			AmMsmobilemenu menu = this.getManagerDAO().selectOne(AmMsmobilemenu.class, paramMenuDeposit);
			if(menu != null){
				active = menu.getIsActive();
			}
			else{
				active = "0";
			}
			//end cek apakah menu deposit report mobile aktif atau tidak
			
			//jika menu deposit report aktif, maka cek apakah semua task sudah dideposit
			if("1".equals(active)){
				Object [][] paramsCek = {{"uuidUser", Long.valueOf(callerId.getCallerId())}};
				Object countResult = this.getManagerDAO().selectOneNative("collection.closingtask.cekdeposit", paramsCek);
				if (countResult != null && ((Integer) countResult).intValue() > 0){
					throw new ClosingTaskException(this.messageSource.getMessage("businesslogic.closingtask.mustdeposit", null, this.retrieveLocaleAudit(callerId)), Reason.CLOSINGTASK_VIOLATION);
				}
			}
			//end cek deposit task
			
			/*CLOSING TASK SUBMIT YANG UNVISIT*/
			Object paramsStatusN[][] = {{Restrictions.eq("statusCode", GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION)}, {"amMssubsystem.subsystemName", GlobalVal.SUBSYSTEM_MC}};
			
			MsStatustask statusTaskPending = this.getManagerDAO().selectOne(MsStatustask.class, paramsStatusN);
			
			Object params[][]={{Restrictions.eq("amMsuser.uuidMsUser", Long.valueOf(callerId.getCallerId()))}, 
					{Restrictions.eq("msStatustask.uuidStatusTask", statusTaskPending.getUuidStatusTask())}}; 
			
			Object paramsStatusUnvisit[][] = {{Restrictions.eq("statusCode", GlobalVal.COLLECTION_STATUS_TASK_DELETED)}, {"amMssubsystem.subsystemName", GlobalVal.SUBSYSTEM_MC}};
			MsStatustask unvisit = this.getManagerDAO().selectOne(MsStatustask.class, paramsStatusUnvisit);
			
			Map listTaskResult = this.getManagerDAO().list(TrTaskH.class, params, null);
			List<TrTaskH> listTask = (List)listTaskResult.get(GlobalKey.MAP_RESULT_LIST);
			if(!listTask.isEmpty()){
				LOG.info("Caller Id = {}, List Task Unvisit Size = {}", callerId.getCallerId(), listTask.size());
				for( TrTaskH task : listTask){
					LOG.info("Caller Id = {}, No Kontrak = {}, Status = {}", callerId.getCallerId(), task.getAgreementNo(), task.getMsStatustask().getUuidStatusTask());
					/*Tambahan insert TR TASK HISTORY*/
					AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(callerId.getCallerId()));
					String fieldPerson = (null == amMsUser) ? null : amMsUser.getFullName();
					
					TrTaskhistory trTaskHistory = new TrTaskhistory(
							task.getMsStatustask() , task, callerId.getCallerId(), new Date()
							, "Closing task", fieldPerson, callerId.getCallerId(), GlobalVal.CODE_PROCESS_DELETED);
					
					LOG.info("Update Print Count: Agreement Number = {}", task.getAgreementNo());
					task.setMsStatustask(unvisit);
					task.setPrintCount(-2);
					task.setUsrUpd(String.valueOf(task.getAmMsuser().getUuidMsUser()));
					task.setDtmUpd(new Date());
					this.getManagerDAO().update(task);
					this.getManagerDAO().insert(trTaskHistory);
					LOG.info("Update Print Count Finish: Agreement Number = {}", task.getAgreementNo());
					countClosingTask++;
				}
			}
			
			
			if(listTask.isEmpty()){
				resultClosingTask += listTask.size()+ " " +
						this.messageSource.getMessage("businesslogic.closingtask.successclosed", 
						null, this.retrieveLocaleAudit(callerId));
			} 
			else {
				if(!listTask.isEmpty()){
					resultClosingTask+=countClosingTask+"/"+listTask.size()+ " " + 
							this.messageSource.getMessage("businesslogic.closingtask.alreadyclosed", 
									null, this.retrieveLocaleAudit(callerId));
				}
			}
			
			rvNumberLogic.clearRVNumber(callerId);		
		return resultClosingTask;
	}

	@Override
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	public List getClosingTaskList(AuditContext callerId) {
		Object[][] params = {{"uuidUser", Long.valueOf(callerId.getCallerId())}, {"statusPending", GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION}};
		List closingTaskList = new ArrayList<>();
		List resultList = this.getManagerDAO().selectAllNative("collection.closingtask.list", params, null);
		if(null != resultList){
			for(int i=0; i<resultList.size();i++){
				Map task = (Map)resultList.get(i);
				ClosingTaskBean bean = new ClosingTaskBean();
				bean.setNoKontrak(task.get("d0").toString());
				bean.setCustomerName(task.get("d1").toString());
				closingTaskList.add(bean);
			}
		}
		return closingTaskList;
	}

}
