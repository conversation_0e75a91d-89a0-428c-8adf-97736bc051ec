package com.adins.mss.businesslogic.impl.collection;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.CollectionResultLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.MsCollectionresult;
import com.adins.mss.model.MsCollresultcategory;
import com.adins.mss.model.MsLov;

@SuppressWarnings({ "unchecked", "rawtypes"})
public class GenericCollectionResultLogic  extends BaseLogic implements CollectionResultLogic{

	@Transactional(readOnly=true,isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List getCollResultList(Object params, AuditContext callerId) {
		List list = new ArrayList();
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT * FROM ( ")
		.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
		.append("SELECT distinct *, ROW_NUMBER() OVER (ORDER BY c.CODE) AS rownum FROM ( ")
		.append("SELECT mcr.RESULT_CODE AS 'CODE', mcr.RESULT_DESC AS 'DESCRIPTION', ")
		.append("mcrc.COLL_RESULT_CATEGORY_DESC AS 'GROUP', mcr.IS_ACTIVE, ")
		.append("mcr.UUID_COLLECTION_RESULT ")
		.append("FROM MS_COLLRESULTCATEGORY mcrc WITH(NOLOCK) ")
		.append("JOIN MS_COLLECTIONRESULT mcr WITH(NOLOCK) ")
		.append("ON mcrc.UUID_COLL_RESULT_CATEGORY = mcr.UUID_COLL_RESULT_CATEGORY ")
		.append("WHERE 1=1 ")  
		.append(paramsQueryString)
		.append(") c ")
		.append(") a WHERE a.rownum <= :end ")
		.append(") b WHERE b.recnum >= :start ");
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[5][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[6][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		list = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		return list;
	}
	
	/*0	"resultCode", 
	 *1	"resultDesc",
	 *2	"collResultCategoryDesc",
	 *3	"isActive",
	 *4	"uuidCollResultCategory",
	 *5	"start", 
	 *6	"end"*/
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null)
			return new StringBuilder();
		StringBuilder sb = new StringBuilder();
		//---RESULT_CODE
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and mcr.RESULT_CODE like '%'+ :resultCode +'%' ");
			paramStack.push(new Object[]{"resultCode", (String) params[0][1]});
		}
		
		//---RESULT_DESC
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("AND mcr.RESULT_DESC like '%'+ :resultDesc +'%' ");
			paramStack.push(new Object[]{"resultDesc", (String) params[1][1]});
		}
		
		//---collResultCategoryDesc
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("AND mcrc.UUID_COLL_RESULT_CATEGORY = :collResultCategoryDesc ");
			paramStack.push(new Object[]{"collResultCategoryDesc", Long.valueOf((String) params[2][1])});
		}
		
		//---IS_ACTIVE
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append("AND mcr.IS_ACTIVE = :isActive ");
			paramStack.push(new Object[]{"isActive", (String) params[3][1]});
		}
		return sb;
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Integer countListCollectionResult(Object params, AuditContext callerId) {
		Integer listCount = NumberUtils.INTEGER_ZERO;
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT COUNT(1) FROM MS_COLLRESULTCATEGORY mcrc WITH(NOLOCK) ")
		.append("JOIN MS_COLLECTIONRESULT mcr WITH(NOLOCK) ")
		.append("ON mcrc.UUID_COLL_RESULT_CATEGORY = mcr.UUID_COLL_RESULT_CATEGORY ")
		.append("WHERE 1=1 ")
		.append(paramsQueryString);
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    listCount = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return listCount;
	}
		
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Map<String, String> getResultListCombo(String uuidCollResultCategory, AuditContext callerId) {
		Map<String, String> result = Collections.emptyMap();
		List list = Collections.emptyList();
		Object[][] params = { {"uuidCollResultCategory", uuidCollResultCategory} };			
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilderResultCategory((Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT UUID_COLL_RESULT_CATEGORY, COLL_RESULT_CATEGORY_DESC ")
		.append("FROM MS_COLLRESULTCATEGORY WITH(NOLOCK) ")
		.append("WHERE 1=1 ")
		.append(paramsQueryString);
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		list = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		Map<String, String> comboList = new HashMap<String, String>();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				Map temp = (Map) list.get(i);
				comboList.put(temp.get("d0").toString(), (String) temp.get("d1"));
			}
			result = this.sortByValues(comboList);
		} 
		else {
			result.put("%", "All");
		}
		return result;
	}
	
	/*
	 * 0 "uuidCollResultCategory", 
	 */
	private StringBuilder sqlPagingBuilderResultCategory(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null){
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---uuidCollResultCategory
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and UUID_COLL_RESULT_CATEGORY = :uuidCollResultCategory ");
			paramStack.push(new Object[]{"uuidCollResultCategory", Long.valueOf((String) params[0][1])});
		}
		return sb;
	}
	
	Map sortByValues(Map<String, String> comboList) {
		List list = new LinkedList(comboList.entrySet());

		Collections.sort(list, new Comparator() {
			public int compare(Object o1, Object o2) {
				return ((Comparable) ((Map.Entry) (o1)).getValue())
						.compareTo(((Map.Entry) (o2)).getValue());
			}
		});

		Map sortedHashMap = new LinkedHashMap();
		sortedHashMap.put("%", "All");
		for (Iterator it = list.iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			sortedHashMap.put(entry.getKey(), entry.getValue());
		}
		return sortedHashMap;
	}
	
	@Override
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	public List getCollResultCategory(AuditContext callerId) {
		List<MsCollresultcategory> result = Collections.EMPTY_LIST;
		String[][] orders = {{"collResultCategoryDesc", GlobalVal.ROW_ORDER_ASC}};
		Map<String, Object> listCollResultCategory = this.getManagerDAO().list(MsCollresultcategory.class, null, orders);
		result = (List) listCollResultCategory.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}

	@Override
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	public MsCollectionresult getOneCollectionResult(
			long uuidCollectionResult, AuditContext callerId) {
		MsCollectionresult collectionResult = this.getManagerDAO().selectOne(MsCollectionresult.class, uuidCollectionResult);
		return collectionResult;
	}

	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public void insertCollectionResult(MsCollectionresult collectionResult,
			AuditContext callerId) {
		Object params[][] = {{Restrictions.eq("resultCode", collectionResult.getResultCode())}};
		MsCollectionresult dbModel = this.getManagerDAO().selectOne(MsCollectionresult.class, params);
		if(dbModel != null){
			throw new EntityNotUniqueException("Code "+collectionResult.getResultCode()+" already exist", collectionResult.getResultCode());
		} 
		else {
			collectionResult.setDtmCrt(new Date());
			collectionResult.setUsrCrt(callerId.getCallerId());
			
			this.getManagerDAO().insert(collectionResult);
			
			String paramLov [][] = {{"lovGroup", GlobalVal.LOV_TAG_COLLECTION_RESULT}};
			Integer lastSeq = (Integer) this.getManagerDAO().selectOneNativeString("SELECT TOP 1 ISNULL(SEQUENCE,0) FROM MS_LOV with (nolock) WHERE LOV_GROUP = :lovGroup ORDER BY SEQUENCE DESC", paramLov);

			if(lastSeq == null){
				lastSeq = 0;
			}
			lastSeq += 10;
			
			MsLov newLov = new MsLov();
			newLov.setUsrCrt(callerId.getCallerId());
			newLov.setDtmCrt(new Date());
			newLov.setCode(collectionResult.getResultCode());
			newLov.setDescription(collectionResult.getResultDesc());
			newLov.setLovGroup(GlobalVal.LOV_TAG_COLLECTION_RESULT);
			newLov.setIsActive(collectionResult.getIsActive());
			newLov.setSequence(lastSeq);
			newLov.setIsDeleted("0");
			
			this.getManagerDAO().insert(newLov);
			
		}		
	}

	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public void updateCollectionResult(MsCollectionresult collectionResult,
			AuditContext callerId) {
		MsCollectionresult dbModel = this.getManagerDAO().selectOne(MsCollectionresult.class, collectionResult.getUuidCollectionResult());
		if(dbModel != null){
			dbModel.setUsrUpd(callerId.getCallerId());
			dbModel.setDtmUpd(new Date());
			dbModel.setResultDesc(collectionResult.getResultDesc());
			MsCollresultcategory category = new MsCollresultcategory();
			category.setUuidCollResultCategory(collectionResult.getMsCollresultcategory().getUuidCollResultCategory());
			dbModel.setMsCollresultcategory(category);
			dbModel.setIsActive(collectionResult.getIsActive());
			
			this.getManagerDAO().update(dbModel);
			
			Object paramsLov[][] = {{Restrictions.eq("lovGroup", GlobalVal.LOV_TAG_COLLECTION_RESULT)}, {Restrictions.eq("code", collectionResult.getResultCode())}};
			MsLov dbLov = this.getManagerDAO().selectOne(MsLov.class, paramsLov);
			if(dbLov != null){
				dbLov.setDescription(collectionResult.getResultDesc());
				dbLov.setIsActive(collectionResult.getIsActive());
				dbLov.setUsrUpd(callerId.getCallerId());
				dbLov.setDtmUpd(new Date());
				
				this.getManagerDAO().update(dbLov);
			} 
			else {
				throw new EntityNotFoundException("Lov Code "+collectionResult.getResultCode()+" is not found", collectionResult.getResultCode());
			}
		} 
		else {
			throw new EntityNotFoundException("Code "+collectionResult.getResultCode()+" is not found", collectionResult.getResultCode());
		}
		
	}
	
}
