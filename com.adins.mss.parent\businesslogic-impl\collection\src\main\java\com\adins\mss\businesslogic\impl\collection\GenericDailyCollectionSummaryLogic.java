package com.adins.mss.businesslogic.impl.collection;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.DailyCollectionSummaryLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsmobilemenu;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrColldailysummary;
@SuppressWarnings({ "rawtypes"})
public class GenericDailyCollectionSummaryLogic extends BaseLogic implements
		DailyCollectionSummaryLogic {
	private Logger logger = LoggerFactory
			.getLogger(GenericDailyCollectionSummaryLogic.class);


	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void dailyCollSummary(AuditContext callerId) {
			List collList = this.countTot();
			if(null!=collList){
				for(int i=0; i<collList.size(); i++){
					Map map = (Map)collList.get(i);
					long uuidUser = ((BigInteger) map.get("d0")).longValue();
					long uuidBranch = ((BigInteger) map.get("d1")).longValue();
					Object[] cntResult = new Object[7];
					cntResult[0] = map.get("d2")==null?new BigDecimal(0):map.get("d2");
					cntResult[1] = map.get("d3")==null?new BigDecimal(0):map.get("d3");
					cntResult[2] = map.get("d4")==null?new BigDecimal(0):map.get("d4");
					cntResult[3] = map.get("d5")==null?new BigDecimal(0):map.get("d5");
					cntResult[4] = map.get("d6")==null?new BigDecimal(0):map.get("d6");
					cntResult[5] = map.get("d7")==null?new BigDecimal(0):map.get("d7");
					cntResult[6] = map.get("d8")==null?new BigDecimal(0):map.get("d8");
					
					String uuidCollDailySummary = this.isExists(uuidUser, uuidBranch);
					if (uuidCollDailySummary != null) {
						this.updateCollDailySummary(uuidCollDailySummary, cntResult);
					}
					else{
						AmMsuser user = new AmMsuser();
						user.setUuidMsUser(uuidUser);
						MsBranch branch = new MsBranch();
						branch.setUuidBranch(uuidBranch);
						user.setMsBranch(branch);
						
						this.insertCollDailySummary(user, cntResult, callerId);
					}	
				}
				String[][] params = { {"start", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 00:00:00.000"},
						{"end", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 23:59:59.997"} };
				this.getManagerDAO().deleteNative("coll.deleteEmptySummary", params);
			}
		
	}

	private String isExists(long uuidMsUser, long uuidBranch) {
		Object[][] params = { { "uuidUser", uuidMsUser },
				{ "uuidBranch", uuidBranch } };
		Object rslt = this.getManagerDAO().selectOneNative("coll.isExists", params);
		String result = (rslt == null) ? null : rslt.toString();
		return result;
	}

	private List countTot() {
		String[][] params = {
				{"start", DateFormatUtils.format(new Date(), "yyyy-MM-dd") + " 00:00:00.000" },
				{"end", DateFormatUtils.format(new Date(), "yyyy-MM-dd") + " 23:59:59.997" } };

		List cnt = (List) this.getManagerDAO().selectAllNative("coll.dailysummary", params, null);
		return cnt;
	}

	private void insertCollDailySummary(AmMsuser user, Object[] calculate,
			AuditContext callerId) {
		TrColldailysummary obj = new TrColldailysummary();
		obj.setUsrCrt(callerId.getCallerId());
		obj.setDtmCrt(new Date());
		obj.setAmMsuser(user);
		obj.setDailyDate(new Date());
		obj.setMsBranch(user.getMsBranch());
		obj.setTotalTobecollect((BigDecimal) (calculate[0] == null ? new BigDecimal(
				0) : calculate[0]));
		obj.setTotalPaid((BigDecimal) (calculate[1] == null ? new BigDecimal(0)
				: calculate[1]));
		obj.setNumberOfDeposited((Integer) calculate[2]);
		obj.setTotalDeposited((BigDecimal) (calculate[3] == null ? new BigDecimal(
				0) : calculate[3]));
		obj.setTotalAssignedTask((Integer) (calculate[4] == null ? 0
				: calculate[4]));
		obj.setTotalSubmittedTask((Integer) (calculate[5] == null ? 0
				: calculate[5]));
		obj.setTotalPaidTask((Integer) (calculate[6] == null ? 0 : calculate[6]));

		this.getManagerDAO().insert(obj);
	}

	private void updateCollDailySummary(String uuidCollDailySummary,
			Object[] calculate) {
		TrColldailysummary obj = this.getManagerDAO().selectOne(
				TrColldailysummary.class, Long.valueOf(uuidCollDailySummary));
		obj.setTotalTobecollect((BigDecimal) (calculate[0] == null ? new BigDecimal(
				0) : calculate[0]));
		obj.setTotalPaid((BigDecimal) (calculate[1] == null ? new BigDecimal(0)
				: calculate[1]));
		obj.setNumberOfDeposited((Integer) calculate[2]);
		obj.setTotalDeposited((BigDecimal) (calculate[3] == null ? new BigDecimal(
				0) : calculate[3]));
		obj.setTotalAssignedTask((Integer) calculate[4]);
		obj.setTotalSubmittedTask((Integer) calculate[5]);
		obj.setTotalPaidTask((Integer) calculate[6]);

		this.getManagerDAO().update(obj);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void dailyDeleteTask(int od, AuditContext callerId) {
		DateTime currentTime = new DateTime();
		DateTime odDate = currentTime.plusDays(od);
		this.logger.info(
				"OD date (dd/MM/yyyy) for delete collection task: {}",
				odDate.toString("dd/MM/yyyy"));

		Object[][] paramsSub = { { Restrictions.eq("subsystemName",
				GlobalVal.SUBSYSTEM_MC) } };
		AmMssubsystem subs = this.getManagerDAO().selectOne(
				AmMssubsystem.class, paramsSub);

		Object[][] paramsST = {
				{ Restrictions.eq("statusCode",
						GlobalVal.COLLECTION_STATUS_TASK_DELETED) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
						subs.getUuidMsSubsystem()) } };
		MsStatustask stts = this.getManagerDAO().selectOne(
				MsStatustask.class, paramsST);
		
		Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_DELETED) } };
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
		
		Object[][] params = {
				{ "status", stts.getUuidStatusTask() },
				{ "subsystem", subs.getUuidMsSubsystem() },
				{ "start", DateFormatUtils.format(odDate.toDate(), "yyyy-MM-dd") + " 00:00:00.000" },
				{ "end", DateFormatUtils.format(odDate.toDate(), "yyyy-MM-dd") + " 23:59:59.997" },
				{ "statusMobileSeqNo", msm.getStatusMobileSeqNo() }};
		this.getManagerDAO().updateNative("coll.deleteTaskDaily", params);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void resetCohCollector(AuditContext callerId) {
		Object[][] paramsSub = { { Restrictions.eq("subsystemName",
				GlobalVal.SUBSYSTEM_MC) } };
		AmMssubsystem subs = this.getManagerDAO().selectOne(
				AmMssubsystem.class, paramsSub);

		Object[][] paramsJob = { { Restrictions.eq("jobCode",
				GlobalVal.JOB_COLLECTOR) } };
		MsJob mj = this.getManagerDAO().selectOne(MsJob.class, paramsJob);

		String[][] params = { { "uuidJob", String.valueOf(mj.getUuidJob()) },
				{ "subsystem", String.valueOf(subs.getUuidMsSubsystem()) } };
		this.getManagerDAO().updateNative("coll.resetCohCollector", params);
	}

	@Transactional(readOnly = true,isolation=Isolation.READ_UNCOMMITTED)
	public String isDepositActive(AuditContext callerId) {
		String active = StringUtils.EMPTY;
		Object[][] paramMenuDeposit = { { Restrictions.eq("menuPrompt",
				GlobalVal.MOBILE_DEPOSIT_REPORT) } };
		AmMsmobilemenu menu = this.getManagerDAO().selectOne(AmMsmobilemenu.class, paramMenuDeposit);
		if (menu != null) {
			active = menu.getIsActive();
		} 
		else {
			active = "0";
		}
		return active;
	}
}
