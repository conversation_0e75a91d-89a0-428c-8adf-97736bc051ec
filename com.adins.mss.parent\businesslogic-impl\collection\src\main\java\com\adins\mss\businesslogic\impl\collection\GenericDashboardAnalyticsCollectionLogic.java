package com.adins.mss.businesslogic.impl.collection;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.DashboardAnalyticsCollectionLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsJob;
import com.adins.mss.services.model.common.DataAnalyticResponse;

@SuppressWarnings({ "rawtypes" })
public class GenericDashboardAnalyticsCollectionLogic extends BaseLogic implements
		DashboardAnalyticsCollectionLogic {

	@Transactional(readOnly=true)
	@Override
	public List getPerformanceMTD(long uuidBranch, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch } };
		List result = this.getManagerDAO().selectAllNative(
				"collection.dm.getPerformanceMTD", params, null);
		return result;
	}
	
	@Transactional(readOnly=true)
	@Override
	public List getPerformanceToday(long uuidBranch, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch } };
		List result = this.getManagerDAO().selectAllNative(
				"collection.dm.getPerformanceToday", params, null);
		return result;
	}
	
	@Transactional(readOnly=true)
	@Override
	public List getTopMTD(long uuidBranch, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }};
		List result = this.getManagerDAO().selectAllNative(
				"collection.dm.getTopMTD", params, null);
		return result;
	}
	@Transactional(readOnly=true)
	@Override
	public List getTopToday(long uuidBranch, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }};
		List result = this.getManagerDAO().selectAllNative(
				"collection.dm.getTopToday", params, null);
		return result;
	}
	
	@Transactional(readOnly=true)
	@Override
	public List getCollectorStatusToday(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }, { "uuidSubsystem", uuidSubsystem }};
		List result = this.getManagerDAO().selectAllNative(
				"collection.dm.getSurveyorStatusToday", params, null);
		return result;
	}
	
	@Override
	@Transactional(readOnly=true)
	public List userList(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		List userList = Collections.EMPTY_LIST;
		Object[][] param = { { Restrictions.eq("gsCode", "MC_JOBSVY") } };
		AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
		if (amGeneralSetting!=null){
			Object[][] paramJob = { { Restrictions.eq("jobCode", amGeneralSetting.getGsValue()) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidSubsystem) } };
			MsJob msJob =  this.getManagerDAO().selectOne(MsJob.class,paramJob );
			if (msJob!=null){
				Object params[][] = {{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidSubsystem) },
						{Restrictions.eq("isActive", "1")},
						{Restrictions.eq("msBranch.uuidBranch", uuidBranch)},
						{Restrictions.eq("msJob.uuidJob", msJob.getUuidJob())} };
				Map result = this.getManagerDAO().list(AmMsuser.class, params, null);
				userList = (List) result.get(GlobalKey.MAP_RESULT_LIST);
			}
		}
				
		return userList;
	}
	
	@Transactional(readOnly=true)
	@Override
	public String getAutoupdateInterval(AuditContext callerId){
		Object params[][] = {{Restrictions.eq("gsCode", "MC_PRM22_DAN")}};
		AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		return result.getGsValue();
	}

	@Transactional(readOnly=true)
	@Override
	public List getDataAnalyticMobile(String task, String diagram, AuditContext callerId) {
		final String SUBSYSTEM = "MC";
		final String TASK_ALLMTD = "All MTD", TASK_ALLTODAY = "All Today", TASK_TODAY = "Today", TASK_MTD = "MTD";
		final String DIAG_PERFORMANCE = "performance", DIAG_TOP = "top", DIAG_COLSTATUS = "collectorStatus";
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(callerId.getCallerId()));
		long uuidBranch = user.getMsBranch().getUuidBranch();
		long uuidSubsystem = user.getAmMssubsystem().getUuidMsSubsystem();
		List<DataAnalyticResponse> listResult = new ArrayList<DataAnalyticResponse>();
		if (TASK_ALLMTD.equalsIgnoreCase(task) || TASK_ALLTODAY.equalsIgnoreCase(task) || TASK_TODAY.equalsIgnoreCase(task)) {
			if ((TASK_TODAY.equalsIgnoreCase(task) && DIAG_PERFORMANCE.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List performanceToday = this.getPerformanceToday(uuidBranch, callerId);
				for (int i = 0; i < performanceToday.size(); i++) {
					Map mapResult = (Map) performanceToday.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_PERFORMANCE);
					bean.setSubsystem(SUBSYSTEM);
					bean.setDays(String.valueOf(mapResult.get("d0")));
					bean.setAssignIn(String.valueOf(mapResult.get("d1")));
					bean.setVisited(String.valueOf(mapResult.get("d2")));
					bean.setCollected(String.valueOf(mapResult.get("d3")));
					bean.setTotalToBeCollect(String.valueOf(mapResult.get("d4")));
					bean.setTotalPaid(String.valueOf(mapResult.get("d5")));
					listResult.add(bean);
				}
			}
			if ((TASK_TODAY.equalsIgnoreCase(task) && DIAG_TOP.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List dealerStatusToday = this.getTopToday(uuidBranch, callerId);
				for (int i = 0; i < dealerStatusToday.size(); i++) {
					Map mapResult = (Map) dealerStatusToday.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_TOP);
					bean.setSubsystem(SUBSYSTEM);
					bean.setUuidUser(String.valueOf(mapResult.get("d0")));
					bean.setFullName(String.valueOf(mapResult.get("d1")));
					bean.setAssignIn(String.valueOf(mapResult.get("d2")));
					bean.setVisited(String.valueOf(mapResult.get("d3")));
					bean.setCollected(String.valueOf(mapResult.get("d4")));
					bean.setTotalToBeCollect(String.valueOf(mapResult.get("d5")));
					bean.setTotalPaid(String.valueOf(mapResult.get("d6")));
					listResult.add(bean);
				}
			}
			if ((TASK_TODAY.equalsIgnoreCase(task) && DIAG_COLSTATUS.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List todayStatus = this.getCollectorStatusToday(uuidBranch, uuidSubsystem, callerId);
				for (int i = 0; i < todayStatus.size(); i++) {
					Map mapResult = (Map) todayStatus.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_COLSTATUS);
					bean.setSubsystem(SUBSYSTEM);
					bean.setUuidUser(String.valueOf(mapResult.get("d0")));
					bean.setFullName(String.valueOf(mapResult.get("d1")));
					bean.setSpvName(String.valueOf(mapResult.get("d2")));
					bean.setNewTask(String.valueOf(mapResult.get("d3")));
					bean.setDownload(String.valueOf(mapResult.get("d4")));
					bean.setRead(String.valueOf(mapResult.get("d5")));
					bean.setSurvey(String.valueOf(mapResult.get("d6")));
					bean.setUpload(String.valueOf(mapResult.get("d7")));
					bean.setSubmit(String.valueOf(mapResult.get("d8")));
					listResult.add(bean);
				}
			}
			if (TASK_ALLMTD.equalsIgnoreCase(task)){
				List performanceMTD = this.getPerformanceMTD(uuidBranch, callerId);
				for (int i = 0; i < performanceMTD.size(); i++) {
					Map mapResult = (Map) performanceMTD.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_MTD);
					bean.setDiagram(DIAG_PERFORMANCE);
					bean.setSubsystem(SUBSYSTEM);
					bean.setDays(String.valueOf(mapResult.get("d0")));
					bean.setAssignIn(String.valueOf(mapResult.get("d1")));
					bean.setVisited(String.valueOf(mapResult.get("d2")));
					bean.setCollected(String.valueOf(mapResult.get("d3")));
					bean.setTotalToBeCollect(String.valueOf(mapResult.get("d4")));
					bean.setTotalPaid(String.valueOf(mapResult.get("d5")));
					listResult.add(bean);
				}
				List dealerStatusMTD = this.getTopMTD(uuidBranch, callerId);
				for (int i = 0; i < dealerStatusMTD.size(); i++) {
					Map mapResult = (Map) dealerStatusMTD.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_MTD);
					bean.setDiagram(DIAG_TOP);
					bean.setSubsystem(SUBSYSTEM);
					bean.setUuidUser(String.valueOf(mapResult.get("d0")));
					bean.setFullName(String.valueOf(mapResult.get("d1")));
					bean.setAssignIn(String.valueOf(mapResult.get("d2")));
					bean.setVisited(String.valueOf(mapResult.get("d3")));
					bean.setCollected(String.valueOf(mapResult.get("d4")));
					bean.setTotalToBeCollect(String.valueOf(mapResult.get("d5")));
					bean.setTotalPaid(String.valueOf(mapResult.get("d6")));
					listResult.add(bean);
				}
			}
		}
		return listResult;
	}
}
