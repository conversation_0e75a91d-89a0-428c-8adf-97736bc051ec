package com.adins.mss.businesslogic.impl.collection;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.lang.FormatterUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.DashboardMonitoringLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrLocationhistory;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.custom.DashboardMonitoringBean;

@SuppressWarnings({ "rawtypes", "unchecked", "deprecation" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericDashboardMonitoringLogic extends BaseLogic implements DashboardMonitoringLogic{
    
	@Override
	public List<DashboardMonitoringBean> getUser(long uuidUser, long uuidSubSystem,
			String modeMap, String startDate, String endDate, AuditContext callerId) throws ParseException {
		List<DashboardMonitoringBean> result = null;
		Object[][] params = { {"uuidSPV", uuidUser} };
		Date minDate = null;
		Date maxDate = null;
        List list = this.getUserList(params, modeMap, callerId);
        
        List<DashboardMonitoringBean> dmBean = new ArrayList<DashboardMonitoringBean>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		for (int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);
			
			String[][] paramsArea = { {"uuidCollector", temp.get("d0").toString()} };
            List listArea = this.getManagerDAO().selectAllNative("collection.dm.getArea", paramsArea, null);
			
            if ("TR".equals(modeMap)) {
				minDate = formatter.parse(startDate);
				maxDate = formatter.parse(endDate);
			}
			
			if ("DM".equals(modeMap)) {
				DateTime currentTime = new DateTime();
			    currentTime = currentTime.minusMillis(currentTime.getMillisOfDay());
			    minDate = currentTime.toDate();
			    currentTime = currentTime.plusDays(1).minusMillis(3);
			    maxDate = currentTime.toDate();
			}
		   			    
			Object[][] paramsTrack = {{ Restrictions.eq("amMsuser.uuidMsUser", temp.get("d0").toString())}, 
					{Restrictions.between("datetime", minDate, maxDate)} };
			String[][] orderTrack = { {"datetime", GlobalVal.ROW_ORDER_DESC} };
			
			Map<String, Object> mapTrack = this.getManagerDAO().list(TrLocationhistory.class, paramsTrack, orderTrack);
			List<TrLocationhistory> listTrlh = (List<TrLocationhistory>) mapTrack.get(GlobalKey.MAP_RESULT_LIST);
			BigDecimal[][] area = new BigDecimal[listArea.size()][2];
			
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
			
			for (int x = 0; x < listArea.size(); x++) {
				Map tempArea = (Map) listArea.get(x);
				if ( x == 0 ) {
					bean.setAreaType(tempArea.get("d4").toString());
					if ( listTrlh.isEmpty() ) {
						bean.setLatitude(new BigDecimal(tempArea.get("d0").toString()));
						bean.setLongitude(new BigDecimal(tempArea.get("d1").toString()));
					}
					else {
						bean.setLatitude(listTrlh.get(0).getLatitude());
						bean.setLongitude(listTrlh.get(0).getLongitude());
					}
				}
				
				if("circle".equalsIgnoreCase(bean.getAreaType())){
					bean.setRadius( Integer.parseInt(tempArea.get("d5").toString()) );
					area[0][0] = new BigDecimal(tempArea.get("d0").toString());
					area[0][1] = new BigDecimal(tempArea.get("d1").toString());
				}
				else {
					area[x][0] = new BigDecimal(tempArea.get("d2").toString());
					area[x][1] = new BigDecimal(tempArea.get("d3").toString());
				}
			}
			
			bean.setUuidMsUser(Long.parseLong(temp.get("d0").toString()));
			bean.setFullName(temp.get("d1").toString());
			bean.setIsLoggedIn(temp.get("d2").toString());
			bean.setInitialName(temp.get("d3").toString());
			bean.setTracking(listTrlh);
			bean.setArea(area);
			if ("DM".equals(modeMap)) {
			    String[][]paramsUuid = {{"uuidUser", String.valueOf(bean.getUuidMsUser())}, 
			    		{"start", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 00:00:00.000"},
						{"end", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 23:59:59.997"}};
				Object[] calculation = (Object[]) this.getManagerDAO()
						.selectOneNative("collection.dm.calculation", paramsUuid);
				bean.setCurrentDate(DateFormatUtils.format(new Date(), "dd MMMM yyyy"));
				
				bean.setAttendanceIn((String)this.getManagerDAO().selectOneNative(
						"survey.dm.getAttendanceIn", paramsUuid));
				
				bean.setLastTimeDetected(!listTrlh.isEmpty()?DateFormatUtils.format(listTrlh.get(0).getDtmCrt(),"hh:mm:ss"):"-");
				bean.setTotalPayment(formatKurs.format(Double.valueOf(calculation==null?"0":calculation[0].toString())));
				bean.setCollectedReceived(formatKurs.format(Double.valueOf(calculation==null?"0":calculation[1].toString())));
				bean.setNumberOfDeposited((Integer)(calculation==null?0:calculation[2]));
				
				bean.setLastDepositTime((String)this.getManagerDAO().selectOneNative(
						"collection.dm.lastDepositTime", paramsUuid));
				
				bean.setTotalDeposited(formatKurs.format(Double.valueOf(calculation==null?"0":calculation[3].toString())));
				AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, bean.getUuidMsUser());
				bean.setCashOnHandSvy(formatKurs.format(user.getCashOnHand()==null?0:user.getCashOnHand()));
				bean = getTaskRecapitulation(bean.getUuidMsUser(), uuidSubSystem, bean, callerId);
			}
			dmBean.add(bean);
		}
		result = dmBean;
		return result;
	}
	
	private List getUserList(Object params, String modeMap,  AuditContext callerId) {
		List result = null;
		if ("DM".equals(modeMap)) {
			result = this.getManagerDAO().selectAllNative("collection.dm.getUser", params, null);
		}
		else if ("TR".equals(modeMap)) {
			result = this.getManagerDAO().selectAllNative("collection.dm.getUserTR", params, null);
		}
		return result;
	}

	private List getUserList2(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("collection.dm.getUser2", params, null);
		return result;
	}
	
	public List getUndoneTask(String uuidUser, String uuidSubSystem, AuditContext callerId) {	
		String[][] params = { {"uuidSPV", uuidUser}, {"uuidSubSystem", uuidSubSystem} };
		List result = this.getManagerDAO().selectAllNative("collection.dm.undoneTask", params, null);	
		return result;
	}
	
	public List getCollectedLocation(String uuidUser, String uuidSubSystem, String currentDate,
			AuditContext callerId) {
		String[][] params = { {"uuidSPV", uuidUser}, {"uuidSubSystem", uuidSubSystem},
					{"start", currentDate+" 00:00:00.000"}, {"end", currentDate+" 23:59:59.997"} };
		List result = this.getManagerDAO().selectAllNative("collection.dm.collectedLocation", params, null);	
		return result;
	}
	
	private DashboardMonitoringBean getTaskRecapitulation(long uuidColl, long idSubsystem, 
			DashboardMonitoringBean bean, AuditContext callerId) {
		Object [][] params = {{"uuidUser", uuidColl}, {"idSubsystem",  idSubsystem}, 
				{"start", DateFormatUtils.format(new Date(), "dd MMMM yyyy")+" 00:00:00.000"}, 
				{"end", DateFormatUtils.format(new Date(), "dd MMMM yyyy")+" 23:59:59.997"}};
		Object[] taskRecapitulation = (Object[]) this.getManagerDAO().selectOneNative(
				"collection.dm.getTaskRecapitulation", params);		
		bean.setCountNewTask((int)taskRecapitulation[0]);
		bean.setCountRead((int)taskRecapitulation[1]);
		bean.setCountDownloaded((int)taskRecapitulation[2]);
		bean.setCountTaskSubmitted((int)taskRecapitulation[3]);
		bean.setCountUploadingImg((int)taskRecapitulation[4]);
		bean.setCountOutVer((int)taskRecapitulation[5]);
		bean.setCountStart((int)taskRecapitulation[6]);
		return bean;
	}

	@Override
	public List getTaskMonitoring(String uuidColl, String uuidSubsystem,
			AuditContext callerId) {
		String[][] params = { {"uuidColl", uuidColl}, 
				{"start", DateFormatUtils.format(new Date(), "dd MMMM yyyy")+" 00:00:00.000"}, 
				{"end", DateFormatUtils.format(new Date(), "dd MMMM yyyy")+" 23:59:59.997"}};
		List result = this.getManagerDAO().selectAllNative("collection.dm.taskmonitoring.load", params, null);
		return result;
	}

	@Override
	public AmMsuser getUser(String uuidColl, AuditContext callerId) {
		if (uuidColl.contains("@")){
			uuidColl = uuidColl.substring(0,uuidColl.indexOf("@"));
		}
		
		AmMsuser result = this.getManagerDAO().selectOne(
				"from AmMsuser u left join fetch u.amMsuser where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(uuidColl)}});
		return result;
	}
	
	@Override
	public List getOtherColl(Object params, AuditContext callerId) {
		List result = getUserList2(params, callerId);
		return result;
	}
	
	@Override
	public Map<String, Object> getTaskLists(String modeInOut, long idColl, 
			String idCollSelected, AmMsuser spv, List otherColl, int pageNumber, int pageSize, AuditContext callerId) {
		Map result = Collections.EMPTY_MAP;
		long idSubsystem = spv.getAmMssubsystem().getUuidMsSubsystem();
		if ("1".equals(modeInOut)) {
			long idStatus = getIdStatusTask(
					GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION, idSubsystem);
			Object[][] params = { { Restrictions.eq("msStatustask.uuidStatusTask", idStatus) },
			        { Restrictions.eq("amMsuser.uuidMsUser", idColl) } };
			result = this.getManagerDAO().selectAll(TrTaskH.class, params, null, pageNumber, pageSize);
		} 
		else {
			if(idCollSelected==null||idCollSelected.isEmpty()){
				Map temp= (Map)otherColl.get(0);
				idCollSelected = temp.get("d0").toString();
			}
			long idStatus = getIdStatusTask(GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION, idSubsystem);
			Object[][] params = {{ Restrictions.eq("msStatustask.uuidStatusTask", idStatus) },
			        {Restrictions.eq("amMsuser.uuidMsUser", Long.valueOf(idCollSelected))}};
			result = this.getManagerDAO().selectAll(TrTaskH.class, params, null, pageNumber, pageSize);
		}
		return result;
	}
	
	private long getIdStatusTask(String statusTask, long idSubsystem) {
		Object[][] params = {{Restrictions.eq("statusCode", statusTask)}, {Restrictions.eq("amMssubsystem.uuidMsSubsystem", idSubsystem)}};
		MsStatustask result = this.getManagerDAO().selectOne(MsStatustask.class, params);
		return result.getUuidStatusTask();
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveAssignIn(String idTasks, long idColl, AmMsuser actor,
			AuditContext callerId) {
		String idTask[]=idTasks.split(",");
		if (ArrayUtils.isNotEmpty(idTask)) {
		    AmMsuser fieldPerson = this.getManagerDAO().selectOne(AmMsuser.class, idColl);
	        Object[][] params = {
	                {Restrictions.eq("statusCode", GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION)},
	                {Restrictions.eq("amMssubsystem.uuidMsSubsystem", actor.getAmMssubsystem().getUuidMsSubsystem())} };
	        MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
		    
			for (int i = 0; i < idTask.length; i++) {
				if ( idTask[i].isEmpty()) continue;
				TrTaskH obj = this.getManagerDAO().selectOne(
						"from TrTaskH u join fetch u.msStatustask where u.uuidTaskH = :uuidTaskH", 
						new Object[][] {{"uuidTaskH", Long.valueOf(idTask[i])}});
				this.updateAssignIn(obj, idColl, actor, msStatustask, callerId);
				this.insertTaskHistory(obj, GlobalVal.NOTES_ASSIGN_IN, actor.getFullName(),
				        fieldPerson, GlobalVal.CODE_PROCESS_REASSIGNMENT, callerId);
			}
		}	
	}
	
	private TrTaskH updateAssignIn(TrTaskH obj, long idColl, AmMsuser actor,
			MsStatustask taskDisribute, AuditContext callerId) {
		obj.setMsStatustask(taskDisribute);
		obj.setApprovalDate(null);
		obj.setDownloadDate(null);
		obj.setSubmitDate(null);
		obj.setReadDate(null);
		obj.setStartDtm(null);
		obj.setRfaDate(null);
		obj.setSubmitDate(null);
		obj.setAssignDate(new Date());
		obj.setAmMsuser(this.getManagerDAO().selectOne(AmMsuser.class, idColl));
		obj.setUsrUpd(callerId.getCallerId());
		obj.setDtmUpd(new Date());
		this.getManagerDAO().update(obj);
		return obj;
	}
	
	private void insertTaskHistory(TrTaskH task, String note, String actor, AmMsuser fieldPerson, 
			String codeProcess, AuditContext callerId){
		TrTaskhistory obj = new TrTaskhistory();
		obj.setDtmCrt(new Date());
		obj.setUsrCrt(callerId.getCallerId());
		obj.setActor(actor);
		obj.setFieldPerson(fieldPerson.getFullName());
		obj.setNotes(note);
		obj.setTrTaskH(task);
		obj.setMsStatustask(task.getMsStatustask());
		obj.setCodeProcess(codeProcess);
		this.getManagerDAO().insert(obj);
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveAssignOut(String idTasks, String idColl, long idCollSelected, 
			AmMsuser actor, AuditContext callerId) {
		String idTask[]=idTasks.split(",");
        if (ArrayUtils.isNotEmpty(idTask)) {
            AmMsuser fieldPerson = this.getManagerDAO().selectOne(AmMsuser.class, idCollSelected);                    
            Object[][] params = { {Restrictions.eq("statusCode",  GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION)}, 
                    {Restrictions.eq("amMssubsystem.uuidMsSubsystem", actor.getAmMssubsystem().getUuidMsSubsystem())} };                       
            MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
            
			for (int i = 0; i < idTask.length; i++) {
				if (StringUtils.isEmpty(idTask[i]))
				    continue;
				TrTaskH obj = this.getManagerDAO().selectOne(TrTaskH.class, Long.valueOf(idTask[i]));
				this.updateAssignOut(obj, idCollSelected, actor, msStatustask, callerId);
				this.insertTaskHistory(obj, GlobalVal.NOTES_ASSIGN_OUT, actor.getFullName(),
				        fieldPerson, GlobalVal.CODE_PROCESS_REASSIGNMENT, callerId);
			}
        }
	}
	
	private void updateAssignOut(TrTaskH obj, long idColl, AmMsuser actor, 
			MsStatustask taskDistribute, AuditContext callerId ) {
		obj.setAmMsuser(this.getManagerDAO().selectOne(AmMsuser.class, idColl));
		obj.setMsStatustask(taskDistribute);
		obj.setAssignDate(new Date());
		obj.setDownloadDate(null);
		obj.setApprovalDate(null);
		obj.setReadDate(null);
		obj.setSubmitDate(null);
		obj.setStartDtm(null);
		obj.setUsrUpd(callerId.getCallerId());
		obj.setDtmUpd(new Date());
		this.getManagerDAO().update(obj);
	}

	@Override
	public List getSubmittedTaskList(long uuidColl, long uuidSubsystem,
			AuditContext callerId) {
		DateTime currentTime = new DateTime();
		currentTime = currentTime.minusMillis(currentTime.getMillisOfDay());
		Date minDate = currentTime.toDate();
		currentTime = currentTime.plusDays(1).minusMillis(3);
		Date maxDate = currentTime.toDate();
		Object[][] params = {{Restrictions.eq("amMsuser.uuidMsUser", uuidColl)},
		        {Restrictions.eq("msStatustask.uuidStatusTask", 
		        		getIdStatusTask(GlobalVal.COLLECTION_STATUS_TASK_RELEASED, uuidSubsystem))}, 
			    {Restrictions.between("submitDate", minDate, maxDate) }};
		String[][] orders = { { "submitDate", GlobalVal.ROW_ORDER_ASC } };
		
		Map resultMap = this.getManagerDAO().list(TrTaskH.class, params, orders);
		List result = (List) resultMap.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}

	@Override
	public List getOutstandingTaskList(long uuidColl, long uuidSubsystem,
			AuditContext callerId) {
		String[] status = {GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION};
		Object[][] prm = {{Restrictions.in("statusCode", status)}, {Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidSubsystem)}};
		MsStatustask statusResult = this.getManagerDAO().selectOne(MsStatustask.class, prm);
		
		Map resultMap = this.getManagerDAO().list("from TrTaskH u join fetch u.amMsuser join fetch u.msStatustask "
				+ "where u.amMsuser.uuidMsUser = :uuidMsUser and u.msStatustask.uuidStatusTask = :uuidStatusTask order by u.assignDate", 
				new Object[][] {{"uuidMsUser", uuidColl}, {"uuidStatusTask", statusResult.getUuidStatusTask()}});
		List<TrTaskH> result = (List) resultMap.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}

	@Override
	public List<DashboardMonitoringBean> getTagColl(String uuidUser,
			String uuidSubSystem, AuditContext callerId) {
		List<DashboardMonitoringBean> result = new ArrayList<DashboardMonitoringBean>();
		BigDecimal lat;
		BigDecimal lng;		
		List undoneTask = getUndoneTask(uuidUser, uuidSubSystem, callerId);
		String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
		NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
		for (int i = 0; i < undoneTask.size(); i++) {
			Map mapUndoneTask = (Map) undoneTask.get(i);
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setUuidTaskH(mapUndoneTask.get("d0").toString());
			bean.setCurrentDate(DateFormatUtils.format(new Date(), "dd MMMM yyyy"));
			bean.setCustName(mapUndoneTask.get("d1").toString());
			bean.setCustAddress(mapUndoneTask.get("d2").toString());
			bean.setApplNo(mapUndoneTask.get("d3").toString());				
			if (null == mapUndoneTask.get("d4")){
				lat = new BigDecimal(0);
			}
			else{
				lat = new BigDecimal(mapUndoneTask.get("d4").toString());
			}
			bean.setLatitude(lat);
			
			if (null == mapUndoneTask.get("d5")){
				lng = new BigDecimal(0);
			}
			else {
				lng = new BigDecimal(mapUndoneTask.get("d5").toString());
			}
			bean.setLongitude(lng);
			bean.setFlag("UT");
			bean.setInitialName(mapUndoneTask.get("d6").toString());
			String[][]paramsUuid = {{"uuidTaskH", mapUndoneTask.get("d0").toString()}};
			bean.setTotalPayment(formatKurs.format((BigDecimal)this.getManagerDAO().selectOneNative(
					"collection.dm.totalPaymentTask", paramsUuid)));
			result.add(bean);
		}
		List collLocation = getCollectedLocation(uuidUser, uuidSubSystem, currentDate, callerId);
		for (int i = 0; i < collLocation.size(); i++) {
			Map mapCollLocation = (Map)collLocation.get(i);
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setUuidTaskH(mapCollLocation.get("d0").toString());
			bean.setCurrentDate(DateFormatUtils.format(new Date(), "dd MMMM yyyy"));
			bean.setCustName(mapCollLocation.get("d1").toString());
			bean.setCustAddress(mapCollLocation.get("d2").toString());
			bean.setApplNo(mapCollLocation.get("d3").toString());
			if (null == mapCollLocation.get("d4")){
				lat = new BigDecimal(0);
			}
			else{
				lat = new BigDecimal(mapCollLocation.get("d4").toString());
			}
			bean.setLatitude(lat);
			
			if (null == mapCollLocation.get("d5")){
				lng = new BigDecimal(0);
			}
			else{
				lng = new BigDecimal(mapCollLocation.get("d5").toString());
			}
			bean.setLongitude(lng);
			bean.setFlag("CL");
			bean.setInitialName(mapCollLocation.get("d6").toString());
			String[][]paramsUuid = {{"uuidTaskH", mapCollLocation.get("d0").toString()}};
			BigDecimal totalPaymentTask = (BigDecimal)this.getManagerDAO().selectOneNative(
					"collection.dm.totalPaymentTask", paramsUuid);
			bean.setTotalPayment(totalPaymentTask==null?"0":formatKurs.format(totalPaymentTask));
			BigDecimal paymentCollTask = (BigDecimal)this.getManagerDAO().selectOneNative(
					"collection.dm.paymentCollectedTask", paramsUuid);
			bean.setCollectedReceived(paymentCollTask==null?"0":formatKurs.format(paymentCollTask));
			result.add(bean);
		}
		return result;
	}

	@Override
	public DashboardMonitoringBean collectionTracking(String uuidColl,
			String startDate, String endDate, AuditContext callerId) {
		DashboardMonitoringBean result = new DashboardMonitoringBean();
		AmMsuser collUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(uuidColl));
		result.setUuidMsUser(collUser.getUuidMsUser());
		result.setFullName(collUser.getFullName());
		result.setIsLoggedIn(collUser.getIsLoggedIn());
		result = getCollArea(result, uuidColl);
		result = getTracking(result, uuidColl, new Date(startDate), new Date(endDate));	
		return result;
	}
	
	private DashboardMonitoringBean getCollArea(
			DashboardMonitoringBean bean, String uuidUser) {
		String[][] paramsArea = { { "uuidUser", uuidUser } };
		List areaList = this.getManagerDAO().selectAllNative(
				"survey.dm.getArea", paramsArea, null);
		BigDecimal[][] areaLoc = new BigDecimal[areaList.size()][2];
		for (int i = 0; i < areaList.size(); i++) {
			Map tempArea = (Map) areaList.get(i);
			if (i == 0) {
				bean.setAreaName((String) tempArea.get("d0"));
				bean.setAreaType((String) tempArea.get("d1"));
				bean.setRadius((int) tempArea.get("d2"));
				bean.setLatitude(new BigDecimal(tempArea.get("d3").toString()));
				bean.setLongitude(new BigDecimal(tempArea.get("d4").toString()));
			}
			if (tempArea.get("d5") != null && tempArea.get("d6") != null) {
				areaLoc[i][0] = new BigDecimal(tempArea.get("d5").toString());
				areaLoc[i][1] = new BigDecimal(tempArea.get("d6").toString());
			}
		}
		bean.setArea(areaLoc);
		return bean;
	}
	
	private DashboardMonitoringBean getTracking(DashboardMonitoringBean bean,
			String uuidUser, Date startDate, Date endDate){
		Object[][] paramsTrack = { { Restrictions.eq("amMsuser.uuidMsUser", uuidUser) },
				{ Restrictions.between("datetime", startDate, endDate) } };
		String[][] orderTrack = { { "datetime", GlobalVal.ROW_ORDER_DESC } };
		Map track = this.getManagerDAO().list(TrLocationhistory.class,
				paramsTrack, orderTrack);
		List<TrLocationhistory> trackingList = (List) track
				.get(GlobalKey.MAP_RESULT_LIST);
		if (!trackingList.isEmpty()) {
			bean.setLatitude(trackingList.get(0).getLatitude());
			bean.setLongitude(trackingList.get(0).getLongitude());
		}
		bean.setTracking(trackingList);
		return bean;
	}

    @Override
    public List<DashboardMonitoringBean> retrieveCollectionTagging(String uuidSpv,
            String uuidSubsystem, AuditContext callerId) {
        List<DashboardMonitoringBean> result = new ArrayList<>();
        result.addAll(this.fetchUndoneTask(uuidSpv, uuidSubsystem, callerId));
        result.addAll(this.fetchCollected(uuidSpv, uuidSubsystem, callerId));
        return result;
    }
    
    private List<DashboardMonitoringBean> fetchUndoneTask(String uuidSpv, String uuidSubSystem,
            AuditContext callerId) {
        if (StringUtils.isBlank(uuidSpv) || StringUtils.isBlank(uuidSubSystem)){
        	return Collections.emptyList();
        }
        
        List undoneTask = this.getUndoneTask(uuidSpv, uuidSubSystem, callerId);
        if (undoneTask == null || undoneTask.isEmpty()){
        	return Collections.emptyList();
        }
        
        List<DashboardMonitoringBean> result = new ArrayList<>();
        
        BigDecimal lat = null;
        BigDecimal lng = null;
        
        for (int i = 0; i < undoneTask.size(); i++) {
            Map mapUndoneTask = (Map) undoneTask.get(i);
            DashboardMonitoringBean bean = new DashboardMonitoringBean();
            bean.setUuidTaskH(mapUndoneTask.get("d0").toString());
            bean.setCustName((String) mapUndoneTask.get("d1"));
            bean.setCustAddress((String) mapUndoneTask.get("d2"));
            bean.setApplNo((String) mapUndoneTask.get("d3"));
            
            if (null == mapUndoneTask.get("d4")){
            	lat = null;
            }
            else{
            	lat = (BigDecimal) mapUndoneTask.get("d4");
            }
            bean.setLatitude(lat);
            
            if (null == mapUndoneTask.get("d5")){
            	lng = null;
            }
            else{
            	lng = (BigDecimal) mapUndoneTask.get("d5");
            }
            bean.setLongitude(lng);
            
            bean.setFlag("UT");
            bean.setInitialName((String) mapUndoneTask.get("d6"));
            String[][]paramsUuid = {{"uuidTaskH", mapUndoneTask.get("d0").toString()}};
            
            BigDecimal totalPayment = (BigDecimal) this.getManagerDAO().selectOneNative(
            		"collection.dm.totalPaymentTask", paramsUuid);
            bean.setTotalPayment(toNumberFormatted(totalPayment));
            result.add(bean);
        }
        
        return result;
    }
    
    private List<DashboardMonitoringBean> fetchCollected(String uuidSpv, String uuidSubSystem,
            AuditContext callerId) {
        if (StringUtils.isBlank(uuidSpv) || StringUtils.isBlank(uuidSubSystem))
            return Collections.emptyList();
        
        String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");        
        List collLocation = getCollectedLocation(uuidSpv, uuidSubSystem, currentDate, callerId);
        if (collLocation == null || collLocation.isEmpty()){
        	return Collections.emptyList();
        }
        
        List<DashboardMonitoringBean> result = new ArrayList<>();
        
        BigDecimal lat = null;
        BigDecimal lng = null;
        
        for (int i = 0; i < collLocation.size(); i++) {
            Map mapCollLocation = (Map) collLocation.get(i);
            DashboardMonitoringBean bean = new DashboardMonitoringBean();
            bean.setUuidTaskH((String) mapCollLocation.get("d0"));
            bean.setCustName((String) mapCollLocation.get("d1"));
            bean.setCustAddress((String) mapCollLocation.get("d2"));
            bean.setApplNo((String) mapCollLocation.get("d3"));
            
            if (null == mapCollLocation.get("d4")){
            	lat = null;
            }
            else{
            	lat = (BigDecimal) mapCollLocation.get("d4");
            }
            bean.setLatitude(lat);
            
            if (null == mapCollLocation.get("d5")){
            	lng = null;
            }
            else{
            	lng = (BigDecimal) mapCollLocation.get("d5");
            }
            bean.setLongitude(lng);
            
            bean.setFlag("CL");
            bean.setInitialName((String) mapCollLocation.get("d6"));
            
            String[][]paramsUuid = {{"uuidTaskH", mapCollLocation.get("d0").toString()}};
            BigDecimal totalPaymentTask = (BigDecimal)this.getManagerDAO().selectOneNative(
            		"collection.dm.totalPaymentTask", paramsUuid);            
            bean.setTotalPayment(toNumberFormatted(totalPaymentTask));
            
            BigDecimal paymentCollTask = (BigDecimal)this.getManagerDAO().selectOneNative(
            		"collection.dm.paymentCollectedTask", paramsUuid);
            bean.setCollectedReceived(toNumberFormatted(paymentCollTask));
            result.add(bean);
        }
        
        return result;
    }

    @Override
    public DashboardMonitoringBean retrieveInfoWindowData(long uuidCollector, long uuidSubsystem, 
    		AuditContext callerId) {
        if (0l == uuidCollector){
        	return null;
        }
        
        Date sysdate = new Date();
        Object[][]paramsUuid = { {"uuidUser", uuidCollector},
                {"start", DateFormatUtils.format(sysdate, "yyyy-MM-dd")+" 00:00:00.000"},
                {"end", DateFormatUtils.format(sysdate, "yyyy-MM-dd")+" 23:59:59.997"} };
        Object[] calculation = (Object[]) this.getManagerDAO().selectOneNative(
        		"collection.dm.calculation", paramsUuid);
        
        DashboardMonitoringBean bean = new DashboardMonitoringBean();
        bean.setCurrentDate(DateFormatUtils.format(sysdate, "dd MMMM yyyy"));
        bean.setAttendanceIn((String) this.getManagerDAO().selectOneNative("survey.dm.getAttendanceIn", paramsUuid));
        bean.setTotalPayment(toNumberFormatted(calculation == null ? null : calculation[0]));
        bean.setCollectedReceived(toNumberFormatted(calculation == null ? null : calculation[1]));
        bean.setNumberOfDeposited((Integer) (calculation == null ? 0 : calculation[2] == null ? 0 : calculation[2]));
        
        bean.setLastDepositTime((String) this.getManagerDAO().selectOneNative(
        		"collection.dm.lastDepositTime", paramsUuid));
        
        bean.setTotalDeposited(toNumberFormatted(calculation == null ? null : calculation[3]));
        AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, uuidCollector);
		bean.setCashOnHandSvy(toNumberFormatted(user.getCashOnHand()==null?0:user.getCashOnHand()));

        bean = this.getTaskRecapitulation(uuidCollector, uuidSubsystem, bean, callerId);
        String atb_code= GlobalKey.MSATTR_LAST_PERCENTAGE_BATTERY;
        this.getPercentageBattery(uuidCollector, uuidSubsystem, atb_code ,bean);
        
        return bean;
    }
    
    private String toNumberFormatted(Object obj) {
        if (obj == null || !NumberUtils.isNumber(String.valueOf(obj))){
        	obj = NumberUtils.DOUBLE_ZERO;
        }
        
        return FormatterUtils.getNumberFormat(FormatterUtils.NFORMAT_US_2).format(obj);
    }
    				
	@Override				
	public String overLimits(long uuidColl, AuditContext callerId) {				
		String over = StringUtils.EMPTY;			
		BigDecimal limit = null;				
		AmMsuser user = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", uuidColl}});

		if(getLimitCohEnabled(GlobalKey.GENERALSETTING_LIMIT_COH_ENABLED).equals("1")){
			BigDecimal cash = (user.getCashOnHand()==null)?BigDecimal.valueOf(0):user.getCashOnHand();
			if(!"0".equals(user.getCashLimit()) && user.getCashLimit()!= null){
				limit = user.getCashLimit();
			}else{
				limit = user.getMsBranch().getCashLimitDefault();
			}	

			if(cash.compareTo(limit) > 0 && limit.compareTo(BigDecimal.valueOf(0)) != 0){		
				over = "red";	
			}else{		
				over = "darkblue";	
			}
		}else{
			over = "darkblue";	
		}	
		return over;			
	}
    
	@Override
	public boolean checkIsSpv(AmMsuser amMsUser, AuditContext auditContext) {
		Object[][] params = { {Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MC+GlobalKey.GENERALSETTING_JOBSPV)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if (amGeneralsetting.getGsValue().equalsIgnoreCase(amMsUser.getMsJob().getJobCode())) {
			return true;
		}
		return false;
	}
	
	@Override
	public AmMsuser getAmSpv(long uuidSpv, AuditContext auditContext) {
		AmMsuser amMsUser = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch join fetch u.amMssubsystem "
				+ "where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", uuidSpv}});
		return amMsUser;
	}
	
	@Override
	public Map<String, Object> getHierarkiBranchLogin(Object params, Object orders,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String,Object>();
		String[][] tempParam = (String[][])params;
		List listCriteria = new ArrayList();
		Integer countCriteria = 0;

		if(StringUtils.isBlank(tempParam[0][1])){
			tempParam[0][1] = "%";
		}
		if(StringUtils.isBlank(tempParam[1][1])){
			tempParam[1][1] = "%";
		}	
		
		String[][] queryParamList = {{ tempParam[0][0], tempParam[0][1]},
				{ tempParam[1][0], tempParam[1][1]}, { tempParam[2][0], tempParam[2][1]},
				{ "start",  String.valueOf((pageNumber-1)*pageSize+1)},
				{ "end",   String.valueOf((pageNumber-1)*pageSize+pageSize)}};
		String[][] queryParamCount = {{ tempParam[0][0], tempParam[0][1]},
				{ tempParam[1][0], tempParam[1][1]}, { tempParam[2][0], tempParam[2][1]}};
		
		String queryListName = "survey.dm.getListHierarkiCabang";
		String queryCountName = "survey.dm.getListHierarkiCabangCount";
		
		listCriteria = this.getManagerDAO().selectAllNative(queryListName, queryParamList, null);
		countCriteria = (Integer)this.getManagerDAO().selectOneNative(queryCountName, queryParamCount);
	
		result.put(GlobalKey.MAP_RESULT_LIST, listCriteria);
		result.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(countCriteria));
		
		return result;
	}
	
	@Override
	public Map getSpvByBranch(String uuidBranch, int pageNo, int pageSize, AuditContext auditContext) {
		Map mapResult = new HashMap();
		Object[][] paramsJob = { {Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MC+GlobalKey.GENERALSETTING_JOBSPV)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsJob);
		Object[][] params = { {"start", ( pageNo-1 ) * pageSize + 1 }, 
				{ "end", ( pageNo-1 ) * pageSize + pageSize }, {"jobCode", amGeneralsetting.getGsValue()}, 
				{"uuidBranch", uuidBranch} };
		List result = this.getManagerDAO().selectAllNative("survey.dm.getSpvListByBranch", params, null);
		
		Object[][] paramsCount = { {"jobCode", amGeneralsetting.getGsValue()}, {"uuidBranch", uuidBranch} };
		Integer total = (Integer)this.getManagerDAO().selectOneNative("survey.dm.getSpvListByBranchCount", paramsCount);	
		
		mapResult.put(GlobalKey.MAP_RESULT_LIST, result);
		mapResult.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(total));
		return mapResult;
	}
	
	public String getLimitCohEnabled(String gsCode) {
		String limitCohEnabled = "0";
		Object[][] params = {{ Restrictions.eq("gsCode", gsCode) } };
		AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if(result != null){
			limitCohEnabled = result.getGsValue();
		}
		return limitCohEnabled;
	}
	
	@Override
	public List userList(long uuidSpv, AuditContext callerId) {
		List userList = Collections.EMPTY_LIST;
		Object params[][] = {{Restrictions.eq("amMsuser.uuidMsUser", uuidSpv)}, {Restrictions.eq("isActive", "1")}};
		Map result = this.getManagerDAO().list(AmMsuser.class, params, null);
		userList = (List)result.get(GlobalKey.MAP_RESULT_LIST);
		return userList;
	}

	@Override
	public List<?> getAllBranch(AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"collection.dm.getBranchAll", null, null);
		return result;
	}

	@Override
	public int getCountAllBranch(AuditContext callerId) {
		return 0;
	}

	@Override
	public List<?> getCollectorBranchWithColour(String uuidbranch, String idxBranch,AuditContext callerId) {
		String[][] param = {{"uuidBranch",uuidbranch }, {"idxBranch", idxBranch}};
		List result = this.getManagerDAO().selectAllNative("collection.dm.getCollectorBranchWithColour", param, null);
		return result;
	}
	
	public DashboardMonitoringBean getPercentageBattery(long uuidColl, long idSubsystem, String atb_code, DashboardMonitoringBean bean) {
		Object[][] params = { { "uuidUser", uuidColl },
				{ "idSubsystem", idSubsystem }, {"atb_code", atb_code}};
		String precentageBattery = (String) this.getManagerDAO().selectOneNative(
				"collection.dm.getPrecentagebattery", params);
		if(precentageBattery == null){
			precentageBattery = "-";
		}
		bean.setPrecentageBattery(precentageBattery);
		return bean;
	}
	
	@Override
	public String getAutoupdateInterval(String gsCode, AuditContext callerId) {
		Object params[][] = {{Restrictions.eq("gsCode", gsCode)}};
		String result = StringUtils.EMPTY;
		AmGeneralsetting bean = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if(bean!=null) {
			result = bean.getGsValue(); 
		}
		else {
			result = 	"0";
		}
		return result ;
	}

}
