package com.adins.mss.businesslogic.impl.collection;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.DepositReportLogic;
import com.adins.mss.businesslogic.api.common.UserLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.model.FinalTrDepositreportD;
import com.adins.mss.model.FinalTrTaskH;
import com.adins.mss.model.TrDepositreportD;
import com.adins.mss.model.TrDepositreportH;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.services.model.collection.DepositReportBean;
import com.adins.mss.services.model.collection.ListReportDetail;
import com.google.common.io.BaseEncoding;

@SuppressWarnings({"unchecked", "rawtypes"})
public class GenericDepositReportLogic extends BaseLogic implements DepositReportLogic {
//	private static final Logger LOG = LoggerFactory.getLogger(GenericDepositReportLogic.class);

	private IntFormLogic intFormLogic;
	private UserLogic userLogic;
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public void setUserLogic(UserLogic userLogic) {
		this.userLogic = userLogic;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void report(AuditContext callerId, DepositReportBean reportHeader, ListReportDetail[] listReportDetail) {
		BigDecimal totalSetoran = new BigDecimal(0);
			
		/* Start Insert TR_DEPOSITREPORTH */
		TrDepositreportH trDepositreportH;
		trDepositreportH = new TrDepositreportH();
		trDepositreportH.setBatchId(reportHeader.getBatchId());
		trDepositreportH.setBankAccount(reportHeader.getBankAccount());
		trDepositreportH.setBankName(reportHeader.getBankName());
		trDepositreportH.setCashierName(reportHeader.getCashierName());
		trDepositreportH.setTransferredDate(reportHeader.getTransferredDate());
		trDepositreportH.setDtmCrt(reportHeader.getDtmCrt());
		trDepositreportH.setUsrCrt(reportHeader.getUsrCrt());
		if (reportHeader.getImage() != null) {
			trDepositreportH.setLobFile(BaseEncoding.base64().decode(reportHeader.getImage()));
		}
		else {
			trDepositreportH.setLobFile(null);
		}
		trDepositreportH.setTrDepositreportDs(null);
		
		this.getManagerDAO().insert(trDepositreportH);
		/* End Insert TR_DEPOSITREPORTH */
		
		for (int i = 0; i < listReportDetail.length; i++) {
			TrDepositreportD trDepositreportD;
			trDepositreportD = new TrDepositreportD();
			trDepositreportD.setUsrCrt(listReportDetail[i].getUsrCrt());
			trDepositreportD.setDtmCrt(listReportDetail[i].getDtmCrt());
			trDepositreportD.setUuidTaskH(Long.parseLong(listReportDetail[i].getTaskId()));
			if (listReportDetail[i].getDepositAmt() != null) {
				trDepositreportD.setDepositAmt(BigDecimal.valueOf(Long.parseLong(listReportDetail[i].getDepositAmt())));
				totalSetoran = totalSetoran.add(trDepositreportD.getDepositAmt());
			}
			else {
				trDepositreportD.setDepositAmt(null);
			}
			trDepositreportD.setTrDepositreportH(trDepositreportH);
			
			this.getManagerDAO().insert(trDepositreportD);
		}
		
		intFormLogic.saveDepositReport(reportHeader.getBatchId());
		userLogic.substractCashOnHand(totalSetoran, reportHeader.getUsrCrt(), callerId);
	}

	@Transactional(readOnly=true,isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List getRecapitulation(AuditContext callerId, String[] taskId) {
		List result = new ArrayList<>();

		for (String id : taskId){
			Object[][] paramTaskH = { {Restrictions.eq("taskId", id)} };
			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, paramTaskH);
			if (trTaskH == null) {
				FinalTrTaskH finalTrTaskH = this.getManagerDAO().selectOne(FinalTrTaskH.class, paramTaskH);
				Object[][] paramDepositReport = { {Restrictions.eq("uuidTaskH", finalTrTaskH.getUuidTaskH())} };
				FinalTrDepositreportD trDepositreportD = this.getManagerDAO().selectOne(FinalTrDepositreportD.class, paramDepositReport);

				if (trDepositreportD != null)
					result.add(id);
				
			}
			else {
				Object[][] paramDepositReport = { {Restrictions.eq("uuidTaskH", trTaskH.getUuidTaskH())} };
				TrDepositreportD trDepositreportD = this.getManagerDAO().selectOne(TrDepositreportD.class, paramDepositReport);

				if (trDepositreportD != null)
					result.add(id);
			}
		}
		
		return result;
	}	
}

