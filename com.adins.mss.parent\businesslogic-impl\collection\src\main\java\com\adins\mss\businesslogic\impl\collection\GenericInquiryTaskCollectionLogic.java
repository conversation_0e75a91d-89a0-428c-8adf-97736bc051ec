package com.adins.mss.businesslogic.impl.collection;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bouncycastle.util.encoders.Base64;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.InquiryTaskCollectionLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.TrTaskrejecteddetail;
import com.adins.mss.model.custom.InquiryTaskCollectionFinalBean;
import com.adins.mss.model.custom.TrTaskBean;

@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
@SuppressWarnings({ "unchecked", "rawtypes" })
public class GenericInquiryTaskCollectionLogic extends BaseLogic implements InquiryTaskCollectionLogic{
	
	@Override
	public List listInquiryTaskCollection(Object params, AuditContext callerId) {
		List resultList = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		StringBuilder ordersQueryString = this.sqlPagingOrderBuilder((String)((Object[][]) params)[12][1]);
		
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT UUID_TASK_H, CUSTOMER_NAME, AGREEMENT_NO, BRANCH_NAME,")
		.append(" ASSIGN_DATE, SUBMIT_DATE, FULL_NAME, STATUS_TASK_DESC,")
		.append(" SEND_DATE, FLAG FROM (")
		.append("SELECT a.UUID_TASK_H, a.CUSTOMER_NAME, a.AGREEMENT_NO, a.BRANCH_NAME,")
		.append(" a.ASSIGN_DATE, a.SUBMIT_DATE, a.FULL_NAME, a.STATUS_TASK_DESC,")
		.append(" SEND_DATE, FLAG, ROW_NUMBER() OVER(ORDER BY rownum) AS recnum FROM (")
		.append(" SELECT b.UUID_TASK_H, b.CUSTOMER_NAME, b.AGREEMENT_NO, b.BRANCH_NAME, ")
		.append("  b.ASSIGN_DATE, b.SUBMIT_DATE, b.FULL_NAME, b.STATUS_TASK_DESC, ")
		.append("  b.SEND_DATE, b.FLAG, :odr as FLAGORDER, ")
		.append("  ROW_NUMBER() OVER ( ").append(ordersQueryString) 
		.append("  ) as rownum FROM ( ")
		.append(" SELECT trth.UUID_TASK_H, ")
		.append("  isnull(trth.CUSTOMER_NAME,'-') as CUSTOMER_NAME, ")
		.append("  isnull(trth.AGREEMENT_NO,'-') as AGREEMENT_NO, ")
		.append("  isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME, ")
		.append("  trth.ASSIGN_DATE as ASSIGN_DATE, ")
		.append("  trth.SUBMIT_DATE as SUBMIT_DATE, ")
		.append("  isnull(ammsu.FULL_NAME,'-') as FULL_NAME, ")
		.append("  isnull(msst.STATUS_TASK_DESC,'-') as STATUS_TASK_DESC, ")
		.append("  trth.send_date as SEND_DATE, ")
		.append("  ammsu.UUID_MS_SUBSYSTEM, ")
		.append("  trth.UUID_BRANCH as UUID_BRANCH, ")
		.append("  trth.UUID_STATUS_TASK as UUID_STATUS_TASK, ")
		.append("  '1' as FLAG		 ")		
		.append("  FROM TR_TASK_H trth ")
		.append("  left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
		.append("  left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
		.append("  join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchLogin)) msb on  ") 
		.append("  trth.UUID_BRANCH = msb.UUID_BRANCH ")
		.append("  union all ")
		.append("  SELECT trth.UUID_TASK_H, ")
		.append("  isnull(trth.CUSTOMER_NAME,'-') as CUSTOMER_NAME, ")
		.append("  isnull(trth.AGREEMENT_NO,'-') as AGREEMENT_NO, ")
		.append("  isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME, ")
		.append("  trth.ASSIGN_DATE as ASSIGN_DATE, ")
		.append("  trth.SUBMIT_DATE as SUBMIT_DATE, ")
		.append("  isnull(ammsu.FULL_NAME,'-') as FULL_NAME, ")
		.append("  isnull(msst.STATUS_TASK_DESC,'-') as STATUS_TASK_DESC, ")
		.append("  trth.send_date as SEND_DATE, ")
		.append("  ammsu.UUID_MS_SUBSYSTEM, ")
		.append("  trth.UUID_BRANCH as UUID_BRANCH, ")
		.append("  trth.UUID_STATUS_TASK as UUID_STATUS_TASK, ")
		.append("  '2' as FLAG	 ")			
		.append("  FROM final_TR_TASK_H trth ")
		.append("  left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
		.append("  left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
		.append("  join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchLogin)) msb on  ") 
		.append("  trth.UUID_BRANCH = msb.UUID_BRANCH ")
		.append("  ) b ")
		.append("  where 1=1 ")
		.append(paramsQueryString);
		
		queryBuilder.append(") a WHERE a.rownum <= :end");
		queryBuilder.append(") b WHERE b.recnum >= :start");

		paramsStack.push(new Object[]{"odr", (String) ((Object[][]) params)[12][1]});	
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[16][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[17][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    resultList = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		return resultList;
	}
	
	@Override
	public Integer countListInquiryTaskCollection(Object params, AuditContext callerId) {
		Integer result = NumberUtils.INTEGER_ZERO;
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
		StringBuilder queryBuilder = new StringBuilder()
		.append(" SELECT COUNT(1) ")
		.append("  FROM ( ")
		.append(" SELECT trth.UUID_TASK_H, ")
		.append("  isnull(trth.CUSTOMER_NAME,'-') as CUSTOMER_NAME, ")
		.append("  isnull(trth.AGREEMENT_NO,'-') as AGREEMENT_NO, ")
		.append("  isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME, ")
		.append("  trth.ASSIGN_DATE as ASSIGN_DATE, ")
		.append("  trth.SUBMIT_DATE as SUBMIT_DATE, ")
		.append("  isnull(ammsu.FULL_NAME,'-') as FULL_NAME, ")
		.append("  isnull(msst.STATUS_TASK_DESC,'-') as STATUS_TASK_DESC, ")
		.append("  trth.send_date as SEND_DATE, ")
		.append("  isnull(trth.RV_NUMBER,'-') as RV_NUMBER, ")
		.append("  trth.UUID_BRANCH as UUID_BRANCH, ")
		.append("  ammsu.UUID_MS_SUBSYSTEM, ")
		.append("  trth.UUID_STATUS_TASK as UUID_STATUS_TASK, ")
		.append("  msst.STATUS_CODE, ")
		.append("  '1' as FLAG		 ")		
		.append("  FROM TR_TASK_H trth ")
		.append("  left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
		.append("  left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
		.append("  join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchLogin)) msb on  ") 
		.append("  trth.UUID_BRANCH = msb.UUID_BRANCH ")
		.append("  union all ")
		.append("  SELECT trth.UUID_TASK_H, ")
		.append("  isnull(trth.CUSTOMER_NAME,'-') as CUSTOMER_NAME, ")
		.append("  isnull(trth.AGREEMENT_NO,'-') as AGREEMENT_NO, ")
		.append("  isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME, ")
		.append("  trth.ASSIGN_DATE as ASSIGN_DATE, ")
		.append("  trth.SUBMIT_DATE as SUBMIT_DATE, ")
		.append("  isnull(ammsu.FULL_NAME,'-') as FULL_NAME, ")
		.append("  isnull(msst.STATUS_TASK_DESC,'-') as STATUS_TASK_DESC, ")
		.append("  trth.send_date as SEND_DATE, ")
		.append("  isnull(trth.RV_NUMBER,'-') as RV_NUMBER, ")
		.append("  trth.UUID_BRANCH as UUID_BRANCH, ")
		.append("  ammsu.UUID_MS_SUBSYSTEM, ")
		.append("  trth.UUID_STATUS_TASK as UUID_STATUS_TASK, ")
		.append("  msst.STATUS_CODE, ")
		.append("  '2' as FLAG	 ")			
		.append("  FROM final_TR_TASK_H trth ")
		.append("  left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
		.append("  left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
		.append("  join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchLogin)) msb on  ") 
		.append("  trth.UUID_BRANCH = msb.UUID_BRANCH ")
		.append("  ) b ")
		.append("  where 1=1 ")
		.append(paramsQueryString);
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}
	
	/*
	 * 1 trth.CUSTOMER_NAME
	 * 2 trth.AGREEMENT_NO
	 * 3 msb.BRANCH_NAME
	 * 4 trth.ASSIGN_DATE
	 * 5 trth.SUBMIT_DATE
	 * 6 ammsu.FULL_NAME
	 * 7 msst.STATUS_TASK_DESC
	 * 8 trth.send_date 
	 */
	private StringBuilder sqlPagingOrderBuilder(String orders) {
		if (StringUtils.isEmpty(orders) || "0".equals(orders)) {
			orders = "4D"; //set default order by assignDate DESC
		}
				
		String[] orderCols = {
				"b.CUSTOMER_NAME", "b.AGREEMENT_NO",
				"b.BRANCH_NAME", "b.ASSIGN_DATE",
				"b.SUBMIT_DATE", "b.FULL_NAME",
				"b.STATUS_TASK_DESC", "b.send_date"};
		StringBuilder queryOrder = new StringBuilder(" ORDER BY ");
		
		int colIdx = NumberUtils.toInt(StringUtils.substring(orders, 0, StringUtils.length(orders) - 1));
		colIdx = (colIdx > 0) ? colIdx -1 : colIdx;
		queryOrder.append(orderCols[colIdx]);
		if (StringUtils.endsWith(orders, "D")) {
			queryOrder.append(" DESC");
		}
		else {
			queryOrder.append(" ASC");
		}		
		
		return queryOrder;
	}
	
	/*
	 * 0 uuidBranch
	 * 1 uuidStatusTask
	 * 2 customerName
	 * 3 agreementNo
	 * 4 fieldPerson
	 * 5 assignDateStart
	 * 6 assignDateEnd
	 * 7 submitDateStart
	 * 8 submitDateEnd
	 * 9 uuidBranchLogin
	 * 10 uuidSubsystem
	 * 11 currentDate
	 * 14 odr (12)
	 * 15 sendDateStart (13)
	 * 16 sendDateEnd (14)
	 * 17 uuidUser (15)
	 * 12 start (16)
	 * 13 end (17)
	 */
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		
		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
		//---ASSIGN_DATE
		DateTime assignDateStart = null, assignDateEnd = null;
		if (params.length > 5 && !StringUtils.equals("%", (String) params[5][1])) {
			assignDateStart = formatter.parseDateTime((String) params[5][1]);
			assignDateStart = assignDateStart.withMillisOfDay(0);
		}
		if (params.length > 5 && !StringUtils.equals("%", (String) params[6][1])) {
			assignDateEnd = formatter.parseDateTime((String) params[6][1]);
			assignDateEnd = assignDateEnd.minusMillis(3);
		}
		if (assignDateStart != null && assignDateEnd != null) {
			sb.append(" AND b.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd");
			paramStack.push(new Object[]{ "assignDateStart", assignDateStart.toDate() });
			paramStack.push(new Object[]{ "assignDateEnd", assignDateEnd.toDate() });
		}
		else if (assignDateStart == null && assignDateEnd != null) {
			sb.append(" AND b.ASSIGN_DATE <= :assignDateEnd");
			paramStack.push(new Object[]{ "assignDateEnd", assignDateEnd.toDate() });
		}
		else if (assignDateStart != null && assignDateEnd == null) {
			sb.append(" AND b.ASSIGN_DATE >= :assignDateStart");
			paramStack.push(new Object[]{ "assignDateStart", assignDateStart.toDate() });
		}
		
		//---SUBMIT_DATE
		DateTime submitDateStart = null, submitDateEnd = null;
		if (params.length > 7 && !StringUtils.equals("%", (String) params[7][1])) {
			submitDateStart = formatter.parseDateTime((String) params[7][1]);
			submitDateStart = submitDateStart.withMillisOfDay(0);
		}
		if (params.length > 7 && !StringUtils.equals("%", (String) params[8][1])) {
			submitDateEnd = formatter.parseDateTime((String) params[8][1]);
			submitDateEnd = submitDateEnd.plusDays(1).minusMillis(3);
		}
		if (submitDateStart != null && submitDateEnd != null) {
			sb.append(" AND b.SUBMIT_DATE BETWEEN :submitDateStart AND :submitDateEnd");
			paramStack.push(new Object[]{ "submitDateStart", submitDateStart.toDate() });
			paramStack.push(new Object[]{ "submitDateEnd", submitDateEnd.toDate() });
		}
		else if (submitDateStart == null && submitDateEnd != null) {
			sb.append(" AND b.SUBMIT_DATE <= :submitDateEnd");
			paramStack.push(new Object[]{ "submitDateEnd", submitDateEnd.toDate() });
		}
		else if (submitDateStart != null && submitDateEnd == null) {
			sb.append(" AND b.SUBMIT_DATE >= :submitDateStart");
			paramStack.push(new Object[]{ "submitDateStart", submitDateStart.toDate() });
		}

		//---SEND_DATE
		DateTime sendDateStart = null, sendDateEnd = null;
		if (params.length > 13 && !StringUtils.equals("%", (String) params[13][1])) {
			sendDateStart = formatter.parseDateTime((String) params[13][1]);
			sendDateStart = sendDateStart.withMillisOfDay(0);
		}
		if (params.length > 14 && !StringUtils.equals("%", (String) params[14][1])) {
			sendDateEnd = formatter.parseDateTime((String) params[14][1]);
			sendDateEnd = sendDateEnd.plusDays(1).minusMillis(3);
		}
		if (sendDateStart != null && sendDateEnd != null) {
			sb.append(" AND b.SEND_DATE BETWEEN :sendDateStart AND :sendDateEnd");
			paramStack.push(new Object[]{ "sendDateStart", sendDateStart.toDate() });
			paramStack.push(new Object[]{ "sendDateEnd", sendDateEnd.toDate() });
		}
		else if (sendDateStart == null && sendDateEnd != null) {
			sb.append(" AND b.SEND_DATE <= :sendDateEnd");
			paramStack.push(new Object[]{ "sendDateEnd", sendDateEnd.toDate() });
		}
		else if (sendDateStart != null && sendDateEnd == null) {
			sb.append(" AND b.SEND_DATE >= :sendDateStart");
			paramStack.push(new Object[]{ "sendDateStart", sendDateStart.toDate() });
		}
		
		//---OTHERS
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append(" AND b.UUID_BRANCH = :uuidBranch");
			paramStack.push(new Object[]{"uuidBranch", (String) params[0][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append(" AND b.UUID_STATUS_TASK = :uuidStatusTask");
			paramStack.push(new Object[]{"uuidStatusTask", (String) params[1][1]});
		}
		
		sb.append(" AND b.UUID_MS_SUBSYSTEM = :uuidSubsystem");
		paramStack.push(new Object[]{"uuidSubsystem", (String) params[10][1]});
		
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append(" AND b.CUSTOMER_NAME LIKE '%' + :customerName + '%'");
			paramStack.push(new Object[]{"customerName", (String) params[2][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append(" AND b.AGREEMENT_NO LIKE '%' + :agreementNo + '%'");
			paramStack.push(new Object[]{"agreementNo", (String) params[3][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[4][1])) {
			sb.append(" AND b.FULL_NAME LIKE '%' + :fieldPerson + '%'");
			paramStack.push(new Object[]{"fieldPerson", (String) params[4][1]});
		}
		
		paramStack.push(new Object[]{"uuidBranchLogin", (String) params[9][1]}); //subquery hierarchy branch	
				
		return sb;
	}
	
	@Override
	public Map<String, String> getBranchListCombo(long branchId, AuditContext callerId) {
		Map<String, String> result = Collections.emptyMap();
			Object[][] params = { { "branchId", branchId } };
			List list = this.getManagerDAO().selectAllNative("task.inquirytaskcollection.getBranchListCombo", params,null);
			Map<String, String> comboList = new HashMap<String, String>();
			if (!list.isEmpty()) {
				for (int i = 0; i < list.size(); i++) {
					Map temp2 = (Map) list.get(i);
					comboList.put(temp2.get("d0").toString(), (String) temp2.get("d1"));
				}
				result = this.sortByValues(comboList);
			} 
			else {
				result.put("%", "ALL");
			}

		return result;
	}
	
	@Override
	public Map<String, String> getStatusListCombo(long subsystemId, AuditContext callerId) {
		Map<String, String> result = Collections.emptyMap();
			Object[][] params = { { "subsystemId", subsystemId } };
			List list = this.getManagerDAO().selectAllNative("task.inquirytaskcollection.getStatusListCombo", params,null);
			
			Map<String, String> comboList = new HashMap<String, String>();
			if (!list.isEmpty()) {
				for (int i = 0; i < list.size(); i++) {
					Map temp2 = (Map) list.get(i);
					comboList.put(temp2.get("d0").toString(),
							(String) temp2.get("d1"));
				}
				result = sortByValues(comboList);
			} 
			else {
				result.put("%", "ALL");
			}
		
		return result;
	}
	
	Map sortByValues(Map<String, String> comboList) {
		List list = new LinkedList(comboList.entrySet());

		Collections.sort(list, new Comparator() {
			public int compare(Object o1, Object o2) {
				return ((Comparable) ((Map.Entry) (o1)).getValue())
						.compareTo(((Map.Entry) (o2)).getValue());
			}
		});

		Map sortedHashMap = new LinkedHashMap();
		sortedHashMap.put("%", "ALL");
		for (Iterator it = list.iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			sortedHashMap.put(entry.getKey(), entry.getValue());
		}
		return sortedHashMap;
	}
	
	@Override
	public List getHeader(long uuid, AuditContext callerId) throws UnsupportedEncodingException {
		List result = new ArrayList<>();
			Object[][] params = { { "uuidTask", uuid } };
			List list = this.getManagerDAO().selectAllNative("task.inquirytaskcollection.getHeader", params, null);	
			for (int i = 0; i < list.size(); i++) {
				Map map = (HashMap) list.get(i);
				result.add(map);
			}
		return result;
	}

	@Override
	public List getHistory(long uuid, AuditContext callerId) {
		List result = null;
			Object[][] params = { { "uuidTask", uuid } };
			result = this.getManagerDAO().selectAllNative("task.inquirytaskcollection.getHistory", params, null);	
		return result;
	}

	@Override
	public List getAnswer(long uuid, AuditContext callerId) throws UnsupportedEncodingException {
		List <TrTaskBean> result = new ArrayList<TrTaskBean>();
		Object[][] params = { { "uuidTask", uuid } };
		List list = this.getManagerDAO().selectAllNative("task.inquirytaskcollection.getAnswerQSet", params, null);
				
		for (int i = 0; i < list.size(); i++) {
			TrTaskBean taskCollection = new TrTaskBean();
			Map map = (HashMap) list.get(i);
							
			if (StringUtils.isNotBlank(map.get("d8").toString())) {
				String[][] accDetailLobParams = { {"uuidTaskD", map.get("d8").toString()} };
				Integer accuracy = (Integer) this.getManagerDAO().selectOneNative("common.retrieveAccuraryDetailLob", accDetailLobParams);
				taskCollection.setAccuracy(accuracy);
                taskCollection.setLob(map.get("d8").toString());
				// while accuracy is queried from DB, image itself is loaded using lazyload mechanism 
			}
			
			taskCollection.setQuestionText(map.get("d1").toString());
			taskCollection.setTextAnswer(map.get("d2").toString());
			taskCollection.setIntTextAnswer(map.get("d10").toString());
			if(GlobalVal.COL_TAG_RVNUMBER.equals(map.get("d14"))){
				taskCollection.setOptionText((String) map.get("d15"));
			} 
			else {
				taskCollection.setOptionText(map.get("d3").toString());
			}
			taskCollection.setIntOptionText(map.get("d11").toString());
			taskCollection.setHasImage(map.get("d4").toString());
			if(null != map.get("d12")){
				taskCollection.setAccuracy(Integer.parseInt(map.get("d12").toString()));
			}
			if (null != map.get("d5")) {
				taskCollection.setLat(map.get("d5").toString());
			}
			if (null != map.get("d6")) {
				taskCollection.setLng(map.get("d6").toString());
			}
			if (result.contains(taskCollection)) {
			    continue;
			}
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("d9").toString())){
				NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
				if (StringUtils.isNotBlank((String) map.get("d2"))) {
					taskCollection.setTextAnswer("Rp "+formatKurs.format(NumberUtils.toDouble(map.get("d2").toString())));
				}
				if (StringUtils.isNotBlank((String) map.get("d10"))) {
					taskCollection.setIntTextAnswer("Rp "+formatKurs.format(NumberUtils.toDouble(map.get("d10").toString())));
				}
			}
			
			MsQuestion msQuestion = new MsQuestion();
			MsAnswertype msAnswertype = new MsAnswertype();
			msAnswertype.setCodeAnswerType(map.get("d9").toString());
			msQuestion.setMsAnswertype(msAnswertype);
			
			taskCollection.setMsQuestion(msQuestion);
			taskCollection.setFlagSource(map.get("d13").toString());
			result.add(taskCollection);
		}
		return result;
	}

	@Override
	public List getHeaderFinal(long uuid, AuditContext callerId) throws UnsupportedEncodingException {
		List result = new ArrayList<>();
			Object[][] params = { { "uuidTask", uuid } };
			List list = this.getManagerDAO().selectAllNative("task.inquirytaskcollection.getHeaderFinal", params, null);	
			for (int i = 0; i < list.size(); i++) {
				Map map = (HashMap) list.get(i);
				result.add(map);
			}
		return result;
	}

	@Override
	public List getHistoryFinal(long uuid, AuditContext callerId) {
		Object[][] params = { { "uuidTask", uuid } };
		List result = this.getManagerDAO().selectAllNative("task.inquirytaskcollection.getHistoryFinal", params, null);	
		return result;
	}
	
	@Override
	public List getAnswerFinal(long uuid, AuditContext callerId) throws UnsupportedEncodingException {
		List <InquiryTaskCollectionFinalBean> result = new ArrayList<InquiryTaskCollectionFinalBean>();
		Object[][] params = { { "uuidTask", uuid } };
		List list = this.getManagerDAO().selectAllNative("task.inquirytaskcollection.getAnswerFinalQSet", params, null);
		
		for (int i = 0; i < list.size(); i++) {
			InquiryTaskCollectionFinalBean taskCollection = new InquiryTaskCollectionFinalBean();
			Map map = (HashMap) list.get(i);
            
            if (StringUtils.isNotBlank(map.get("d8").toString())) {                    
                String[][] accDetailLobParams = { {"uuidTaskD", map.get("d8").toString()} };
                Integer accuracy = (Integer) this.getManagerDAO().selectOneNative("common.retrieveAccuraryDetailLobFinal", accDetailLobParams);
                taskCollection.setAccuracy(accuracy);
                taskCollection.setLob(map.get("d8").toString());
                //while accuracy is queried from DB, image itself is loaded using lazyload mechanism 
            }

			taskCollection.setQuestionText(map.get("d1").toString());
			taskCollection.setTextAnswer(map.get("d2").toString());
			taskCollection.setIntTextAnswer(map.get("d10").toString());
			if(GlobalVal.COL_TAG_RVNUMBER.equals(map.get("d14"))){
				taskCollection.setOptionText((String) map.get("d15"));
			} 
			else {
				taskCollection.setOptionText(map.get("d3").toString());
			}
			taskCollection.setIntOptionText(map.get("d11").toString());
			taskCollection.setHasImage(map.get("d4").toString());
			if(null != map.get("d12")){
				taskCollection.setAccuracy(Integer.parseInt(map.get("d12").toString()));
			}
			if (null != map.get("d5")) {
				taskCollection.setLat(map.get("d5").toString());
			}
			if (null != map.get("d6")) {
				taskCollection.setLng(map.get("d6").toString());
			}
			if (result.contains(taskCollection)){
			    continue;
			}
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("d9").toString())){
				NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
				if (StringUtils.isNotBlank((String) map.get("d2"))) {
					taskCollection.setTextAnswer("Rp "+formatKurs.format(NumberUtils.toDouble(map.get("d2").toString())));
				}
				if (StringUtils.isNotBlank((String) map.get("d10"))) {
					taskCollection.setIntTextAnswer("Rp "+formatKurs.format(NumberUtils.toDouble(map.get("d10").toString())));
				}
			}
			
			MsQuestion msQuestion = new MsQuestion();
			MsAnswertype msAnswertype = new MsAnswertype();
			msAnswertype.setCodeAnswerType(map.get("d9").toString());
			msQuestion.setMsAnswertype(msAnswertype);
			
			taskCollection.setMsQuestion(msQuestion);
			taskCollection.setFlagSource(map.get("d13").toString());
			result.add(taskCollection);
		}
		return result;
	}
	
	@Override
	public List getInquiryTaskDetailAnswerHistoryRejected2(Object params,
			Object order, AuditContext callerId) throws UnsupportedEncodingException {
		List <TrTaskBean> result = new ArrayList<TrTaskBean>();
		List list = this.getManagerDAO().selectAllNative("task.inquirytaskcollection.answerHistoryRejected2", params,order);
		for (int i = 0; i < list.size(); i++) {
			TrTaskBean taskCollection = new TrTaskBean();
			Map map = (HashMap) list.get(i);
			
			if (null != map.get("d4") && StringUtils.isNotBlank((String) map.get("d7"))) {
				TrTaskrejecteddetail mapResult = this.getManagerDAO().selectOne(TrTaskrejecteddetail.class, Long.valueOf(map.get("d7").toString()));
				taskCollection.setImgBase64(new String(Base64.encode(mapResult.getImageBlob()), "ASCII"));
			}
			taskCollection.setQuestionText(map.get("d0").toString());
			taskCollection.setTextAnswer(map.get("d1").toString());
			taskCollection.setOptionText(map.get("d2").toString());
			taskCollection.setHasImage(map.get("d3").toString());
			
			if (null != map.get("d5")) {
			    taskCollection.setLatitude(new BigDecimal(map.get("d5").toString()));
			}
			if (null != map.get("d6")) {
			    taskCollection.setLongitude(new BigDecimal(map.get("d6").toString())); 
			}
			if (result.contains(taskCollection)) {
			    continue;
			}
			result.add(taskCollection);
		}
		return result;
	}
	
	@Override
	public List listInquiryTaskCollectionByHierarkiUser(Object params, AuditContext callerId) {
		List resultList = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		StringBuilder ordersQueryString = this.sqlPagingOrderBuilder((String)((Object[][]) params)[12][1]);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT UUID_TASK_H, CUSTOMER_NAME, AGREEMENT_NO, BRANCH_NAME,")
				.append(" ASSIGN_DATE, SUBMIT_DATE, FULL_NAME, STATUS_TASK_DESC,")
				.append(" SEND_DATE, FLAG FROM (")
				.append("SELECT a.UUID_TASK_H, a.CUSTOMER_NAME, a.AGREEMENT_NO, a.BRANCH_NAME,")
				.append(" a.ASSIGN_DATE, a.SUBMIT_DATE, a.FULL_NAME, a.STATUS_TASK_DESC,")
				.append(" SEND_DATE, FLAG, ROW_NUMBER() OVER(ORDER BY rownum) AS recnum FROM (")
				.append(" SELECT b.UUID_TASK_H, b.CUSTOMER_NAME, b.AGREEMENT_NO, b.BRANCH_NAME, b.ASSIGN_DATE, b.SUBMIT_DATE, b.FULL_NAME, b.STATUS_TASK_DESC, b.SEND_DATE, b.FLAG, :odr as FLAGORDER, ROW_NUMBER() OVER ( ").append(ordersQueryString).append(" )as rownum FROM (")
				.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
				.append(" isnull(trth.CUSTOMER_NAME,'-') as CUSTOMER_NAME,")
				.append(" isnull(trth.AGREEMENT_NO,'-') as AGREEMENT_NO,")
				.append(" isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME,")
				.append(" trth.ASSIGN_DATE as ASSIGN_DATE,")
				.append(" trth.SUBMIT_DATE as SUBMIT_DATE,")
				.append(" isnull(ammsu.FULL_NAME,'-') as FULL_NAME,")
				.append(" isnull(msst.STATUS_TASK_DESC,'-') as STATUS_TASK_DESC,")
				.append(" trth.send_date as SEND_DATE,")
				.append(" trth.uuid_branch as UUID_BRANCH,")
				.append(" ammsu.uuid_ms_user as UUID_MS_USER,")
				.append(" ammsu.UUID_MS_SUBSYSTEM as UUID_MS_SUBSYSTEM,")
				.append("  trth.UUID_STATUS_TASK as UUID_STATUS_TASK, ")
				.append(" '1' as FLAG")
				.append(" FROM TR_TASK_H trth")
				.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
				.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER")
				.append(" inner join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = ammsu.UUID_MS_USER")
				.append(" left join MS_BRANCH msb on trth.UUID_BRANCH = msb.UUID_BRANCH")
				.append(" UNION ALL")
				.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
				.append(" isnull(trth.CUSTOMER_NAME,'-') as CUSTOMER_NAME,")
				.append(" isnull(trth.AGREEMENT_NO,'-') as AGREEMENT_NO,")
				.append(" isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME,")
				.append(" trth.ASSIGN_DATE as ASSIGN_DATE,")
				.append(" trth.SUBMIT_DATE as SUBMIT_DATE,")
				.append(" isnull(ammsu.FULL_NAME,'-') as FULL_NAME,")
				.append(" isnull(msst.STATUS_TASK_DESC,'-') as STATUS_TASK_DESC,")
				.append(" trth.send_date as SEND_DATE,")
				.append(" trth.uuid_branch as UUID_BRANCH,")
				.append(" ammsu.uuid_ms_user as UUID_MS_USER,")
				.append(" ammsu.UUID_MS_SUBSYSTEM as UUID_MS_SUBSYSTEM,")
				.append("  trth.UUID_STATUS_TASK as UUID_STATUS_TASK, ")
				.append(" '2' as FLAG")
				.append(" FROM FINAL_TR_TASK_H trth")
				.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
				.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER")
				.append(" inner join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = ammsu.UUID_MS_USER")
				.append(" left join MS_BRANCH msb on trth.UUID_BRANCH = msb.UUID_BRANCH")
				.append(" )b")
				.append(" WHERE 1=1 and b.uuid_branch = :uuidBranchLogin ")
				.append(paramsQueryString);
		
		queryBuilder.append(") a WHERE a.rownum <= :end");
		queryBuilder.append(") b WHERE b.recnum >= :start");

		paramsStack.push(new Object[]{"odr", (String) ((Object[][]) params)[12][1]});
		paramsStack.push(new Object[]{"uuidUser", ((Object[][]) params)[15][1]});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[16][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[17][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    resultList = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		return resultList;
	}
	
	@Override
	public Integer countListInquiryTaskCollectionByHierarkiUser(Object params, AuditContext callerId) {
		Integer result = NumberUtils.INTEGER_ZERO;
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT COUNT(1) FROM(")
				.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
				.append(" isnull(trth.CUSTOMER_NAME,'-') as CUSTOMER_NAME,")
				.append(" isnull(trth.AGREEMENT_NO,'-') as AGREEMENT_NO,")
				.append(" isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME,")
				.append(" trth.ASSIGN_DATE as ASSIGN_DATE,")
				.append(" trth.SUBMIT_DATE as SUBMIT_DATE,")
				.append(" isnull(ammsu.FULL_NAME,'-') as FULL_NAME,")
				.append(" isnull(msst.STATUS_TASK_DESC,'-') as STATUS_TASK_DESC,")
				.append(" trth.send_date as SEND_DATE,")
				.append(" trth.uuid_branch as UUID_BRANCH,")
				.append(" ammsu.uuid_ms_user as UUID_MS_USER,")
				.append(" ammsu.UUID_MS_SUBSYSTEM as UUID_MS_SUBSYSTEM,")
				.append("  trth.UUID_STATUS_TASK as UUID_STATUS_TASK, ")
				.append(" '1' as FLAG")
				.append(" FROM TR_TASK_H trth")
				.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
				.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER")
				.append(" inner join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = ammsu.UUID_MS_USER")
				.append(" left join MS_BRANCH msb on trth.UUID_BRANCH = msb.UUID_BRANCH")
				.append(" UNION ALL")
				.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
				.append(" isnull(trth.CUSTOMER_NAME,'-') as CUSTOMER_NAME,")
				.append(" isnull(trth.AGREEMENT_NO,'-') as AGREEMENT_NO,")
				.append(" isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME,")
				.append(" trth.ASSIGN_DATE as ASSIGN_DATE,")
				.append(" trth.SUBMIT_DATE as SUBMIT_DATE,")
				.append(" isnull(ammsu.FULL_NAME,'-') as FULL_NAME,")
				.append(" isnull(msst.STATUS_TASK_DESC,'-') as STATUS_TASK_DESC,")
				.append(" trth.send_date as SEND_DATE,")
				.append(" trth.uuid_branch as UUID_BRANCH,")
				.append(" ammsu.uuid_ms_user as UUID_MS_USER,")
				.append(" ammsu.UUID_MS_SUBSYSTEM as UUID_MS_SUBSYSTEM,")
				.append("  trth.UUID_STATUS_TASK as UUID_STATUS_TASK, ")
				.append(" '2' as FLAG")
				.append(" FROM FINAL_TR_TASK_H trth")
				.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
				.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER")
				.append(" inner join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = ammsu.UUID_MS_USER")
				.append(" left join MS_BRANCH msb on trth.UUID_BRANCH = msb.UUID_BRANCH")
				.append(" )b")
				.append(" WHERE 1=1 and b.uuid_branch = :uuidBranchLogin ")
				.append(paramsQueryString);
		
		paramsStack.push(new Object[]{"uuidUser", (String) ((Object[][]) params)[12][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}
}

