package com.adins.mss.businesslogic.impl.collection;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.collection.ReportLkpLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportLkpLogic extends BaseLogic implements ReportLkpLogic {
	private static final String[] HEADER_SUMMARY = { 
		"No", "Branch Name", "Total Assigned Task", "Total Submitted Task", 
		"Total Payment To Be Collected", "Total Collected Task", "Total Collection Received", 
		"Total Task Failed", "Total Collection Failed" };
	private static final int[] SUMMARY_COLUMN_WIDTH = {10 * 256, 30 * 256, 20 * 256, 20 * 256, 
		40 * 256, 40 * 256, 40 * 256,  40 * 256,  40 * 256 };
	private static final String[] HEADER_DETAIL = { "No", "Branch Name", "Collector Name", 
		"Collected Date", "Total Payment To Be Collected", "Total Collected Task", 
		"Total Collection Received", "Total Task Failed", "Total Collection Failed"  };
	private static final int[] DETAIL_COLUMN_WIDTH = { 10 * 256, 30 * 256, 30 * 256, 20 * 256, 
		40 * 256, 40 * 256, 40 * 256,  40 * 256,  40 * 256 };
	private static final Logger LOG = LoggerFactory.getLogger(GenericReportLkpLogic.class);
	
	@Autowired
    private MessageSource messageSource;
    
    @Autowired
	private GlobalLogic globalLogic;
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}
	
	@Override
	public List getComboBranch(String branchId, AuditContext callerId) {
		List result = null;
		String[][] params = { { "branchId", branchId } };
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}

	@Override
	public List getSummary(String branchId, String startDate, String endDate, 
			AuditContext callerId, AmMsuser user) {
		List result = null;
		Object[][] params = null;
		String queryName = null;
			
		String spvMc = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MC_JOBSPV, callerId);
			
		if (spvMc.equalsIgnoreCase(user.getMsJob().getJobCode())) {
			Object [][] temp = { { "branchId", branchId }, { "userId", user.getUuidMsUser() }, 
					{ "startDate", startDate+" 00:00:00.000" }, { "endDate", endDate+" 23:59:59.997" }};
			queryName = "report.lkp.getListSummaryBySpv";
			params = temp;
		} 
		else {
			Object [][] temp = { { "branchId", branchId }, { "startDate", startDate+" 00:00:00.000" }, 
					{ "endDate", endDate+" 23:59:59.997" }, 
					{"subsystemId", user.getAmMssubsystem().getUuidMsSubsystem()} };
			queryName = "report.lkp.getListSummary";
			params = temp;
		}
		result = this.getManagerDAO().selectAllNative(queryName, params, null);
		return result;
	}
	
	@Override
	public List getDetail(String branchId, String userId, String startDate,
			String endDate, AuditContext callerId, AmMsuser userLogin) {
		List result = new ArrayList<>();
			
		Object[][] params = null;
		List list = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		
		String paramUserId = StringUtils.EMPTY;
		if (null != userId) {
			if (!userId.isEmpty()) {
				paramUserId = userId;
			}
		}
			
		String spvMc = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MC_JOBSPV, callerId);
			
		if (spvMc.equalsIgnoreCase(userLogin.getMsJob().getJobCode())) {
			Object[][] temp = { { "branchId", branchId }, 
					{ "branchLogin", userLogin.getMsBranch().getUuidBranch() },
					{ "userId", paramUserId }, { "loginId", userLogin.getUuidMsUser() }, 
					{ "startDate", startDate+" 00:00:00.000" }, 
					{ "endDate", endDate+" 23:59:59.997" }};
			params = temp;
			StringBuilder paramsQueryString = this.sqlPagingBuilderDateBranchSpv(
					(Object[][]) params, paramsStack);
			StringBuilder paramsQueryStringKeyValue = this.sqlPagingBuilderKeyValueSpv(
					(Object[][]) params, paramsStack);
			StringBuilder queryBuilder = new StringBuilder()
				.append("WITH N AS (select trtcd.UUID_TASK_ID as UUID_TASK_D,  h.DTM_CRT as DTM_CRT, ")
				.append("h.uuid_ms_user as uuid_ms_user ")
				.append("from TR_TASK_H h with (nolock) ")
				.append("join TR_TASKCOLLDATA trtcd with (nolock) on trtcd.UUID_TASK_ID=h.UUID_TASK_H ")
				.append("where h.DTM_CRT BETWEEN :startDate and :endDate  ")
				.append("and trtcd.payment_received is not null ")
				.append("UNION ALL ")
				.append("select ftrtcd.UUID_TASK_ID as UUID_TASK_D,   h.DTM_CRT as DTM_CRT, ")
				.append("h.uuid_ms_user as uuid_ms_user ")
				.append("from FINAL_TR_TASK_H h with (nolock) ")
				.append("join FINAL_TR_TASKCOLLDATA ftrtcd with (nolock) on ftrtcd.UUID_TASK_ID=h.UUID_TASK_H ")
				.append("where h.DTM_CRT BETWEEN :startDate and :endDate  ")
				.append("and ftrtcd.payment_received is not null ")
				.append(") ")
				.append("SELECT BRANCH_CODE, BRANCH_NAME, FULL_NAME, DAILY_DATE, TOTAL_TOBECOLLECT, ") 
				.append("(select count(UUID_TASK_D) as countTask  ")
				.append("from N ")
				.append("where DTM_CRT between  CONVERT(char(10), DAILY_DATE,126)+' 00:00:00' ")
				.append("and CONVERT(char(10), DAILY_DATE,126)+' 23:59:59' ")
				.append("and N.UUID_MS_USER = tc.UUID_MS_USER ")
				.append(")TASK_COLLECTED, ")
				.append("TOTAL_PAID,  ")
				.append("COALESCE(COALESCE(TOTAL_ASSIGNED_TASK,0)-(select count(UUID_TASK_D) as countTask ") 
				.append("from N ")
				.append("where DTM_CRT between  CONVERT(char(10), DAILY_DATE,126)+' 00:00:00' ")
				.append("and CONVERT(char(10), DAILY_DATE,126)+' 23:59:59' ")
				.append("and N.UUID_MS_USER = tc.UUID_MS_USER ")
				.append("),0) TASK_FAILED, ")
				.append("CASE ")
				.append("WHEN ( COALESCE(COALESCE(TOTAL_TOBECOLLECT,0)- COALESCE(TOTAL_PAID,0), 0)) < 0 THEN 0 ")  
				.append("ELSE COALESCE(COALESCE(TOTAL_TOBECOLLECT,0)- COALESCE(TOTAL_PAID,0), 0) ")
				.append("END ")
				.append("UNPAID, ")  
				.append("LOGIN_ID ")
				.append("FROM TR_COLLDAILYSUMMARY TC with (nolock) ")
				.append("JOIN MS_BRANCH MB with (nolock)ON TC.UUID_BRANCH = MB.UUID_BRANCH ")
				.append("JOIN AM_MSUSER AU with (nolock)ON TC.UUID_MS_USER = AU.UUID_MS_USER ")
				.append("JOIN (select keyValue from dbo.getCabangByLogin(:branchLogin)) cb ")
				.append("on tc.UUID_BRANCH = cb.keyValue ")
				.append("JOIN (select amu.keyValue from dbo.getAllUserByLogin(:loginId) as amu ")
				.append("where 1=1 ") 
				.append(paramsQueryStringKeyValue)
				.append(") usr on tc.UUID_MS_USER = usr.keyValue ")
				.append("WHERE 1=1 ")
				.append(paramsQueryString)
				.append("ORDER BY BRANCH_CODE, FULL_NAME, DAILY_DATE ");
				
			paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[4][1]});
			paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[5][1]});
			paramsStack.push(new Object[]{"branchLogin", ((Object[][]) params)[1][1]});
			paramsStack.push(new Object[]{"loginId", ((Object[][]) params)[3][1]});
			Object[][] sqlParams = new Object[paramsStack.size()][2];
		    for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
		    list = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		} 
		else {
			Object[][] temp = { { "branchId", branchId }, 
					{ "branchLogin", userLogin.getMsBranch().getUuidBranch() }, 
					{ "userId", paramUserId }, { "startDate", startDate+" 00:00:00.000" }, 
					{ "endDate", endDate+" 23:59:59.997" },  
					{"subsystemId", userLogin.getAmMssubsystem().getUuidMsSubsystem()}};
			params = temp;
				
			StringBuilder paramsQueryString = this.sqlPagingBuilderDateBranch(
					(Object[][]) params, paramsStack);
			StringBuilder paramsQueryStringKeyValue = this.sqlPagingBuilderKeyValue(
					(Object[][]) params, paramsStack);
				
			StringBuilder queryBuilder = new StringBuilder()
				.append("WITH N AS ( ")
				.append("select trtcd.UUID_TASK_ID as UUID_TASK_D,  h.DTM_CRT as DTM_CRT, ")
				.append("h.uuid_ms_user as uuid_ms_user ")
				.append("from TR_TASK_H h with (nolock) ")
				.append("join TR_TASKCOLLDATA trtcd with (nolock) on trtcd.UUID_TASK_ID=h.UUID_TASK_H ")
				.append("where h.DTM_CRT BETWEEN :startDate and :endDate ")
				.append("and trtcd.payment_received is not null ")
				.append("UNION ALL ")
				.append("select ftrtcd.UUID_TASK_ID as UUID_TASK_D,   h.DTM_CRT as DTM_CRT, ")
				.append("h.uuid_ms_user as uuid_ms_user ")
				.append("from FINAL_TR_TASK_H h with (nolock) ")
				.append("join FINAL_TR_TASKCOLLDATA ftrtcd with (nolock) on ftrtcd.UUID_TASK_ID=h.UUID_TASK_H ")
				.append("where h.DTM_CRT BETWEEN :startDate and :endDate ")
				.append("and ftrtcd.payment_received is not null ")
				.append(") ")
				.append("SELECT * FROM (SELECT BRANCH_CODE, BRANCH_NAME, FULL_NAME, DAILY_DATE, ")
				.append("TOTAL_TOBECOLLECT, ") 
				.append("(select count(UUID_TASK_D) as countTask ")
				.append("from N ")
				.append("where DTM_CRT between  CONVERT(char(10), DAILY_DATE,126)+' 00:00:00' ")
				.append("and CONVERT(char(10), DAILY_DATE,126)+' 23:59:59' ")
				.append("and N.UUID_MS_USER = tc.UUID_MS_USER ")
				.append(")TASK_COLLECTED, ")
				.append("TOTAL_PAID,  ")
				.append("COALESCE(COALESCE(TOTAL_ASSIGNED_TASK,0)-(select count(UUID_TASK_D) as countTask ") 
				.append("from N ")
				.append("where DTM_CRT between  CONVERT(char(10), DAILY_DATE,126)+' 00:00:00' ")
				.append("and CONVERT(char(10), DAILY_DATE,126)+' 23:59:59' ")
				.append("and N.UUID_MS_USER = tc.UUID_MS_USER ")
				.append("),0) TASK_FAILED, ")
				.append("CASE ")
				.append("WHEN ( COALESCE(COALESCE(TOTAL_TOBECOLLECT,0)- COALESCE(TOTAL_PAID,0), 0)) < 0 ")
				.append("THEN 0 ")  
				.append("ELSE COALESCE(COALESCE(TOTAL_TOBECOLLECT,0)- COALESCE(TOTAL_PAID,0), 0) ")
				.append("END ")
				.append("UNPAID, ") 
				.append("LOGIN_ID ")
				.append("FROM MS_BRANCH MB with (nolock) ")
				.append("JOIN TR_COLLDAILYSUMMARY TC with (nolock) ON TC.UUID_BRANCH = MB.UUID_BRANCH ")
				.append("JOIN AM_MSUSER AU with (nolock) ON TC.UUID_MS_USER = AU.UUID_MS_USER ")
				.append("JOIN (select keyValue from dbo.getCabangByLogin(:branchLogin)) cb ")
				.append("on tc.UUID_BRANCH = cb.keyValue ")
				.append("JOIN (select keyValue from dbo.getAllUserByBranchLogin(")
				.append(":branchLogin, :subsystemId) ")
				.append("where 1=1 ")
				.append(paramsQueryStringKeyValue)
				.append(") usr on tc.UUID_MS_USER = usr.keyValue ")
				.append("where 1=1 ")
				.append(paramsQueryString)
				.append(") B ")
				.append("WHERE B.TOTAL_TOBECOLLECT + B.TASK_COLLECTED + B.TOTAL_PAID + ")
				.append("B.TASK_FAILED + B.UNPAID > 0 ")
				.append("ORDER BY BRANCH_CODE, FULL_NAME, DAILY_DATE ");
				
			paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[3][1]});
			paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[4][1]});
			paramsStack.push(new Object[]{"branchLogin", ((Object[][]) params)[1][1]});
			paramsStack.push(new Object[]{"subsystemId", ((Object[][]) params)[5][1]});
			Object[][] sqlParams = new Object[paramsStack.size()][2];
		    for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
		    list = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		}
			
		String branch = StringUtils.EMPTY;
		String user = StringUtils.EMPTY;
		List tempList = new ArrayList<>();
		List tempUser = new ArrayList<>();
		Map tempMap = new HashMap<>();
		Map mapUser = new HashMap<>();
			
		if (list.isEmpty()) {
			return null;
		}
			
		for (int i = 0, j = 1, jml = 1; i < list.size(); i++, jml++) {
			Map map = (Map) list.get(i);
				
			Map temp = new HashMap();
				
			temp.put("d0", map.get("d3").toString());
			temp.put("d1", map.get("d4").toString());
			temp.put("d2", map.get("d5").toString());
			temp.put("d3", map.get("d6").toString());
			temp.put("d4", map.get("d7").toString());
			temp.put("d5", map.get("d8").toString());
			tempUser.add(temp);
				
			user = map.get("d9").toString();
			if ((i + 1) == list.size() || !user.equals(((Map)list.get(i+1)).get("d9").toString())) {
				mapUser.put("user", map.get("d2").toString());
				mapUser.put("listUser", tempUser);
				if (!user.isEmpty()) {
					mapUser.put("total", jml);
					tempList.add(mapUser);
					tempUser = new ArrayList<>();
					mapUser = new HashMap<>();
					jml = 0;
				}
					
				branch = map.get("d0").toString();
				if ((i + 1) == list.size() || !branch.equals(((Map)list.get(i+1)).get("d0").toString())) {
					tempMap.put("branch", branch + " - " + map.get("d1").toString());
					tempMap.put("list", tempList);
					tempMap.put("seq", j);
					j++;
					if (!branch.isEmpty()) {
						tempMap.put("size", list.size());
						result.add(tempMap);
						tempList = new ArrayList<>();
						tempMap = new HashMap<>();
					}
				}
			}
		}
		return result;
	}
	
	/*
	 * 0 { "branchId", branchId }, 
	 * 1 { "branchLogin", userLogin.getMsBranch().getUuidBranch() }, 
	 * 2 { "userId", paramUserId },   
	 * 3 { "startDate", startDate+" 00:00:00.000" }, 
	 * 4 { "endDate", endDate+" 23:59:59.997" },  
	 * 5 {"subsystemId", userLogin.getAmMssubsystem().getUuidMsSubsystem()}};
	 */
	private StringBuilder sqlPagingBuilderDateBranch(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID BRANCH / branchId
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append(" AND MB.UUID_BRANCH = :branchId ");
			paramStack.push(new Object[]{"branchId", Long.valueOf((String) params[0][1])});
		}
		
		//---DAILY_DATE
		if ((String) params[3][1] != null && (String) params[4][1] != null) {
			sb.append(" AND DAILY_DATE BETWEEN :assignDateStart AND :assignDateEnd ");
			paramStack.push(new Object[]{ "assignDateStart", (String) params[3][1] });
			paramStack.push(new Object[]{ "assignDateEnd", (String) params[4][1] });
		}
		return sb;
	}
	
	/*
	 * 0 { "branchId", branchId }, 
	 * 1 { "branchLogin", userLogin.getMsBranch().getUuidBranch() }, 
	 * 2 { "userId", paramUserId },   
	 * 3 { "startDate", startDate+" 00:00:00.000" }, 
	 * 4 { "endDate", endDate+" 23:59:59.997" },  
	 * 5 {"subsystemId", userLogin.getAmMssubsystem().getUuidMsSubsystem()}};
	 */
	private StringBuilder sqlPagingBuilderKeyValue(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---user ID
		if (!StringUtils.equals("%", (String) params[2][1]) && !StringUtils.equals("", (String) params[2][1])) {
			sb.append(" AND keyValue = :userId ");
			paramStack.push(new Object[]{"userId", Long.valueOf((String) params[2][1])});
		}
		return sb;
	}
	
	/*
	 * 0 { { "branchId", branchId }, 
	 * 1 { "branchLogin", userLogin.getMsBranch().getUuidBranch() },
	 * 2 { "userId", paramUserId }, 
	 * 3 { "loginId", userLogin.getUuidMsUser() }, 
	 * 4 { "startDate", startDate+" 00:00:00.000" }, 
	 * 5 { "endDate", endDate+" 23:59:59.997" }};
	 */
	private StringBuilder sqlPagingBuilderDateBranchSpv(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID BRANCH / branchId
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append(" AND MB.UUID_BRANCH = :branchId ");
			paramStack.push(new Object[]{"branchId", Long.valueOf((String) params[0][1])});
		}
		
		//---DAILY_DATE
		if ((String) params[4][1] != null && (String) params[5][1] != null) {
			sb.append(" AND DAILY_DATE BETWEEN :startDate AND :endDate ");
			paramStack.push(new Object[]{ "startDate", (String) params[4][1] });
			paramStack.push(new Object[]{ "endDate", (String) params[5][1] });
		}
		return sb;
	}
	
	/*
	 * 0 { { "branchId", branchId }, 
	 * 1 { "branchLogin", userLogin.getMsBranch().getUuidBranch() },
	 * 2 { "userId", paramUserId }, 
	 * 3 { "loginId", userLogin.getUuidMsUser() }, 
	 * 4 { "startDate", startDate+" 00:00:00.000" }, 
	 * 5 { "endDate", endDate+" 23:59:59.997" }};
	 */
	private StringBuilder sqlPagingBuilderKeyValueSpv(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		sb.append(" AND amu.keyValue != :loginId ");
		paramStack.push(new Object[]{"loginId", params[3][1]});
		//---user ID
		if (!StringUtils.equals("%", (String) params[2][1]) && !StringUtils.equals("", (String) params[2][1])) {
			sb.append(" AND amu.keyValue = :userId ");
			paramStack.push(new Object[]{"userId", Long.valueOf((String) params[2][1])});
		}
		return sb;
	}
	
	@Override
	public byte[] exportExcel(String branchId, String userId, String startDate,
			String endDate, String type, AuditContext callerId, AmMsuser user) {	
		XSSFWorkbook workbook = this.createXlsTemplate(branchId, userId, startDate,
				endDate, type, callerId, user);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(String branchId, String userId,
			String startDate, String endDate, String type, AuditContext callerId) {
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setUuidLoginId(callerId.getCallerId());
		reportBean.setUuidBranch(branchId);
		reportBean.setUuidUser(userId);
		reportBean.setStartDate(startDate);
		reportBean.setEndDate(endDate);
		reportBean.setType(type);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

		AmMsuser amMsUser = new AmMsuser();
		amMsUser.setUuidMsUser(Long.parseLong(callerId.getCallerId()));
		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("Billing Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_LKP_COLLECTION.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}
	
	private XSSFWorkbook createXlsTemplate(String branchId, String userId, String startDate,
			String endDate, String type, AuditContext callerId, AmMsuser user) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Billing Worksheet Report");
			if (type.equalsIgnoreCase("0")) {
				List result= this.getSummary(branchId, startDate, endDate, callerId, user);
				this.createDataSummary(workbook, sheet, result, startDate, endDate);
			}
			else {
				List result= this.getDetail(branchId, userId, startDate, endDate, callerId, user);
				this.createDataDetail(workbook, sheet, result, startDate, endDate);
			}
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private void createDataSummary(XSSFWorkbook workbook, XSSFSheet sheet, List result, 
			String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_SUMMARY.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT SUMMARY PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(HEADER_SUMMARY[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, SUMMARY_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_SUMMARY.length-1));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			//data cell
			for (int j = 1; j < HEADER_SUMMARY.length; j++) {
				XSSFCell cell = rowData.createCell(j);
				if (j == 1) { //Set data to string
					cell.setCellValue(temp.get("d"+j).toString());
					cell.setCellStyle(styles.get("cell"));
				}
				else if (j <=3 ) {
					cell.setCellValue(Integer.parseInt(temp.get("d"+j).toString()));
					cell.setCellStyle(styles.get("cell"));
				}
				else {
					cell.setCellValue(Double.parseDouble(temp.get("d"+j).toString()));
					cell.setCellStyle(styles.get("cell"));
				}
			}
		} 
		//Total Data
		if(!result.isEmpty()){
			XSSFRow rowData = sheet.createRow(rowcell++);
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue("Total");
			cellNo.setCellStyle(styles.get("header"));
			XSSFCell cell = rowData.createCell(1);
	        String ref = (char)('B') + "3:" + (char)('B') + Integer.toString(result.size()+2);
	        cell.setCellFormula("COUNTA(" + ref + ")");	
	        cell.setCellStyle(styles.get("header"));
	        
	        for (int i = 0; i < HEADER_SUMMARY.length - 2; i++){    // -2  karena sudah cell A dan B sudah diisi
	        	cell = rowData.createCell(i+2);
	        	ref = (char)('C'+i) + "3:" + (char)('C'+i) + Integer.toString(result.size()+2);
	            cell.setCellFormula("SUM(" + ref + ")");
	            cell.setCellStyle(styles.get("header"));
	        }
		}
        //End Total Data       	
        XSSFFormulaEvaluator.evaluateAllFormulaCells(workbook);
	}
	
	private void createDataDetail(XSSFWorkbook workbook, XSSFSheet sheet, List result, 
			String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_DETAIL.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT DETAIL PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(HEADER_DETAIL[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, DETAIL_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_DETAIL.length-1));
		//Data row
		if (result == null) {
			return;
		}
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));			
			
			//Branch cell
			XSSFCell cellBranch = rowData.createCell(1);
			cellBranch.setCellValue(temp.get("branch").toString());
			cellBranch.setCellStyle(styles.get("cell"));
			
			int tot = 0;
			List list = (List) temp.get("list");
			for (int k = 0; k < list.size(); k++) {
				Map map = (Map) list.get(k);
				XSSFRow rowData1 = rowData;
				//User cell
				XSSFCell cellUser = rowData1.createCell(2);
				cellUser.setCellValue(map.get("user").toString());
				cellUser.setCellStyle(styles.get("cell"));
				
				List listCal = (List) map.get("listUser");
				for (int l = 0; l < listCal.size(); l++) {
					if (l != 0) {
						rowData1 = sheet.createRow(rowcell++);
					}
					if (k != 0 || l!=0) {
						XSSFCell cellUser1 = rowData1.createCell(0);
						cellUser1.setCellStyle(styles.get("cell"));
						
						XSSFCell cellUser2 = rowData1.createCell(1);
						cellUser2.setCellStyle(styles.get("cell"));
					}
					
					XSSFCell cellUser3 = rowData1.createCell(2);
					cellUser3.setCellValue(map.get("user").toString());
					cellUser3.setCellStyle(styles.get("cell"));
					
					Map tempCal = (Map) listCal.get(l);
					for (int j = 3; j < HEADER_DETAIL.length; j++) {
						XSSFCell cell = rowData1.createCell(j);
						if (j == 3) {
							cell.setCellValue(tempCal.get("d"+(j-3)).toString());
							cell.setCellStyle(styles.get("cell"));
						}
						else {
							cell.setCellValue(Double.parseDouble(tempCal.get("d"+(j-3)).toString()));
							cell.setCellStyle(styles.get("cell"));
						}
					}
				}			
				tot += Integer.parseInt(map.get("total").toString());
				rowData = sheet.createRow(rowcell++);
			}
			sheet.addMergedRegion(new CellRangeAddress(rowcell-tot-1, rowcell-2, 0, 0));
			sheet.addMergedRegion(new CellRangeAddress(rowcell-tot-1, rowcell-2, 1, 1));			
		} 	
	}	
	
	private static Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style;

        style = wb.createCellStyle();
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styles.put("cell", style);

        style = wb.createCellStyle();
        Font font = wb.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(CellStyle.SOLID_FOREGROUND);
        styles.put("header",style );
        return styles;
    }

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
				Long.valueOf(reportBean.getUuidLoginId()));
		XSSFWorkbook workbook = this.createXlsTemplate(reportBean.getUuidBranch(), 
				reportBean.getUuidUser(), reportBean.getStartDate(),
				reportBean.getEndDate(), reportBean.getType(), callerId, user);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("BillingReport_");
			
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
				reportBean.setUuidBranch(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
						Long.valueOf(reportBean.getUuidBranch()));
				sb.append(msBranch.getBranchCode());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if ("0".equals(reportBean.getUuidUser()) || "%".equals(reportBean.getUuidUser())) {
				reportBean.setUuidUser(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
				AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, 
						Long.valueOf(reportBean.getUuidUser()));
				sb.append(amMsUser.getLoginId());
			} 
			else {
				sb.append("ALL");
			}
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(
				trReportResultLog.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime()
				.getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}
}
