package com.adins.mss.businesslogic.impl.collection;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.ReportSlaCollectionLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({"rawtypes","unchecked"})
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportSlaCollectionLogic extends BaseLogic 
		implements ReportSlaCollectionLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericReportSlaCollectionLogic.class);
	private static final String[] HEADER_SUMMARY = { 
		"Branch",
		"Total Submitted Task", 
		"Average Download - Read (HH:mm)", 
		"Average Read - On Progress (HH:mm)", 
		"Average On Progress - Submit (HH:mm)", 
		"Average Time Download-Submit(HH:mm)",
		"Total Task Under SLA",
		"Average Time Under SLA (HH:mm)",
		"Total Task Over SLA",
		"Average Time Over SLA (HH:mm)"
	};
	private static final int[] SUMMARY_COLUMN_WIDTH = { 
		30 * 256, 
		20 * 256,
		20 * 256,
		20 * 256, 
		20 * 256, 
		20 * 256, 
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256
	};
	private static final String[] HEADER_DETAIL = { 
		"No", 
		"Branch", 
		"Collector Name",
		"Application No",
		"Assignment Date",
		"Submitted Date",
		"Download-Read (HH:mm)", 
		"Read-On Progress (HH:mm)", 
		"On Progress-Submit (HH:mm)", 
		"Total Duration Task (HH:mm)",
	};
	private static final int[] DETAIL_COLUMN_WIDTH = { 
		10 * 256, 
		30 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256 
	};

	@Autowired
    private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	@Override
	public List getComboBranch(String branchId, AuditContext callerId) {
		List result = null;
		String[][] params = { { "branchId", branchId } };
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}

	@Override
	public List getReportSla(String subsystemId, String branchId, String startDate, 
			String endDate, AuditContext callerId) {
		List result = new ArrayList<>();
		String gsSla = this.retrieveSlaTime();
		String[][] params = { 
				{ "branchId", branchId }, 
				{"subsystemId",subsystemId }, 
				{ "startDate", startDate }, 
				{ "endDate", endDate },
				{ "gsSla", gsSla } };
		List resultReportSla = this.getManagerDAO().selectAllNative(
				"report.slacollection.getSlaSummary", params, null);
			
		for (int i = 0; i < resultReportSla.size(); i++) {
			Map temp = (Map) resultReportSla.get(i);
			Map tempResult = new HashMap();
			
			tempResult.put("d0", temp.get("d0").toString() + " - " + temp.get("d1").toString());
			tempResult.put("d1", temp.get("d2").toString());
			tempResult.put("d2", String.format("%02d", Integer.parseInt(temp.get("d3").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d4").toString())));
			tempResult.put("d3", String.format("%02d", Integer.parseInt(temp.get("d5").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d6").toString())));
			tempResult.put("d4", String.format("%02d", Integer.parseInt(temp.get("d7").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d8").toString())));
			tempResult.put("d5", String.format("%02d", Integer.parseInt(temp.get("d9").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d10").toString())));
			tempResult.put("d6", temp.get("d11").toString());
			tempResult.put("d7", String.format("%02d", Integer.parseInt(temp.get("d12").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d13").toString())));
			tempResult.put("d8", temp.get("d14").toString());
			tempResult.put("d9", String.format("%02d", Integer.parseInt(temp.get("d15").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d16").toString())));
			tempResult.put("d10", temp.get("d17").toString());
			
			result.add(i, tempResult);
		}
		return result;
	}

	@Override
	public Map getReportSlaDetail(AmMsuser user, String type, String branchId, String userId, 
			String startDate, String endDate, AuditContext callerId) {
		Map result = new HashMap<>();
		String gsSla = this.retrieveSlaTime();
		String[][] params = { 
				{ "branchId", "0".equals(branchId) 
					? String.valueOf(user.getMsBranch().getUuidBranch()) : branchId },
				{ "subsystemId", String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem()) }, 
				{ "userId", userId.isEmpty() ? "%" : userId }, 
				{ "startDate", startDate }, 
				{ "endDate", endDate } ,
				{ "gsSla", gsSla }};
			
		String branch = "";
		if (!"0".equals(branchId)) {
			MsBranch brc = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(branchId));
			branch = brc.getBranchCode() + " - " + brc.getBranchName();
		}
		else if ("0".equals(type)) {
			MsBranch brc = this.getManagerDAO().selectOne(MsBranch.class, user.getMsBranch().getUuidBranch());
			branch = brc.getBranchCode() + " - " + brc.getBranchName();
		}
		else {
			branch = "All Branches";
		}
		result.put("branch", branch);

		List list = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		if ("0".equals(branchId)) {
			StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
			StringBuilder queryBuilder = new StringBuilder()
				.append("WITH N as ( ")
				.append("select * from TR_TASK_H ttrh ")
				.append("where ttrh.ASSIGN_DATE between :startDate and :endDate ")
				.append("UNION ALL ")
				.append("select * from FINAL_TR_TASK_H fttrh ")
				.append("where fttrh.ASSIGN_DATE between :startDate and :endDate ")
				.append(") ")
				.append("select ")
				.append("ROW_NUMBER() OVER(ORDER BY msb.branch_code ASC) rownum, ")
				.append("branch_code, ")
				.append("branch_name, ")
				.append("full_name, ")
				.append("isnull(agreement_no,''), ")
				.append("format(assign_date,'yyyy-MM-dd hh:mm:ss') as assign_date, ")
				.append("format(submit_date,'yyyy-MM-dd hh:mm:ss') as submit_date, ")
				.append("(DATEDIFF(minute, download_date, read_date))/60 as hour_dr, ")
				.append("(DATEDIFF(minute, download_date, read_date))%60 as min_dr, ")
				.append("(DATEDIFF(minute, read_date, start_dtm))/60 as hour_rp, ")
				.append("(DATEDIFF(minute, read_date, start_dtm))%60 as min_rp, ")
				.append("(DATEDIFF(minute, start_dtm, submit_date))/60 as hour_ps, ")
				.append("(DATEDIFF(minute, start_dtm, submit_date))%60 as min_ps, ")
				.append("(DATEDIFF(minute, assign_date, submit_date))/60 as hour_ds, ")
				.append("(DATEDIFF(minute, assign_date, submit_date))%60 as min_ds, ")
				.append("N.uuid_branch, ")
				.append("case when (DATEDIFF(minute, assign_date, submit_date)) > :gsSla *60 ")
				.append("then 1 else 0 end isSla ")
				.append("from N ")
				.append("join ( ")
				.append("select keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
				.append("from dbo.getCabangByLogin(:branchId) ")
				.append(") msb on N.uuid_branch = msb.uuid_branch ")
				.append("join am_msuser msu on N.uuid_ms_user = msu.uuid_ms_user ")
				.append("join ms_statustask mst on N.uuid_status_task = mst.uuid_status_task ")
				.append("where 1=1 ")
				.append(paramsQueryString);
						
			paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[3][1]});
			paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[4][1]});
			paramsStack.push(new Object[]{"gsSla", ((Object[][]) params)[5][1]});
			paramsStack.push(new Object[]{"branchId", ((Object[][]) params)[0][1]});
			Object[][] sqlParams = new Object[paramsStack.size()][2];
		    for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
		    list = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		}
		else {
			StringBuilder paramsQueryStringByBranch = this.sqlPagingBuilderByBranch(
					(Object[][]) params, paramsStack);
			StringBuilder queryBuilder = new StringBuilder()
				.append("WITH N as ( ")
				.append("select * from TR_TASK_H ttrh ")
				.append("where ttrh.ASSIGN_DATE between :startDate and :endDate ")
				.append("UNION ALL ")
				.append("select * from FINAL_TR_TASK_H fttrh ")
				.append("where fttrh.ASSIGN_DATE between :startDate and :endDate ")
				.append(") ")
				.append("select ")
				.append("ROW_NUMBER() OVER(ORDER BY full_name ASC) rownum, ")
				.append("branch_code, ")
				.append("branch_name, ")
				.append("full_name, ")
				.append("isnull(agreement_no,''), ")
				.append("format(assign_date,'yyyy-MM-dd hh:mm:ss') as assign_date, ")
				.append("format(submit_date,'yyyy-MM-dd hh:mm:ss') as submit_date, ")
				.append("(DATEDIFF(minute, download_date, read_date))/60 as hour_dr, ")
				.append("(DATEDIFF(minute, download_date, read_date))%60 as min_dr, ")
				.append("(DATEDIFF(minute, read_date, start_dtm))/60 as hour_rp, ")
				.append("(DATEDIFF(minute, read_date, start_dtm))%60 as min_rp, ")
				.append("(DATEDIFF(minute, start_dtm, submit_date))/60 as hour_ps, ")
				.append("(DATEDIFF(minute, start_dtm, submit_date))%60 as min_ps, ")
				.append("(DATEDIFF(minute, assign_date, submit_date))/60 as hour_ds, ")
				.append("(DATEDIFF(minute, assign_date, submit_date))%60 as min_ds, ")
				.append("N.uuid_branch, ")
				.append("case when (DATEDIFF(minute, assign_date, submit_date)) > :gsSla *60 ")
				.append("then 1 else 0 end isSla ")
				.append("from N ")
				.append("join ms_branch msb on N.uuid_branch = msb.uuid_branch ")
				.append("join am_msuser msu on N.uuid_ms_user = msu.uuid_ms_user ")
				.append("join ms_statustask mst on N.uuid_status_task = mst.uuid_status_task ")
				.append("where 1=1 ")
				.append(paramsQueryStringByBranch);
			paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[3][1]});
			paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[4][1]});
			paramsStack.push(new Object[]{"gsSla", ((Object[][]) params)[5][1]});
			Object[][] sqlParams = new Object[paramsStack.size()][2];
		    for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
		    list = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		}
			
		List resultList = new ArrayList<>();
		for(int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);
			Map tempResult = new HashMap();
				
			tempResult.put("d0", temp.get("d0").toString());
			tempResult.put("d1", temp.get("d1").toString() + " - " + temp.get("d2").toString());
			tempResult.put("d2", temp.get("d3").toString());
			tempResult.put("d3", temp.get("d4").toString());
			tempResult.put("d4", temp.get("d5").toString());
			tempResult.put("d5", temp.get("d6").toString());
			tempResult.put("d6", String.format("%02d", Integer.parseInt(temp.get("d7").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d8").toString())));
			tempResult.put("d7", String.format("%02d", Integer.parseInt(temp.get("d9").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d10").toString())));
			tempResult.put("d8", String.format("%02d", Integer.parseInt(temp.get("d11").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d12").toString())));
			tempResult.put("d9", String.format("%02d", Integer.parseInt(temp.get("d13").toString())) 
					+ " : " + String.format("%02d", Integer.parseInt(temp.get("d14").toString())));
			tempResult.put("d10", temp.get("d15").toString());
			tempResult.put("d11", temp.get("d16").toString());
			resultList.add(i, tempResult);
		}
		result.put("resultList", resultList);
		return result;
	}
	
	/*
	 * 0 { "branchId", "0".equals(branchId) ? String.valueOf(user.getMsBranch().getUuidBranch()) : branchId },
	 * 1 { "subsystemId", String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem()) }, 
	 * 2 { "userId", userId.isEmpty() ? "%" : userId }, 
	 * 3 { "startDate", startDate }, 
	 * 4 { "endDate", endDate } ,
	 * 5 { "gsSla", gsSla }
	 */
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---ASSIGN DATE
		if ((String) params[3][1] != null && (String) params[4][1] != null) {
			sb.append("and N.assign_date between :startDate and :endDate ");
			paramStack.push(new Object[]{"startDate", (String) params[3][1]});
			paramStack.push(new Object[]{"endDate", (String) params[4][1]});
		}
		
		//---UUID USER
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("and N.uuid_ms_user = :userId ");
			paramStack.push(new Object[]{"userId", Long.valueOf((String) params[2][1])});
		}
		
		//---STATUS CODE
		sb.append("and mst.status_code IN ('V','P','S') ");
		
		//---UUID SUBSYSTEM
		sb.append("and mst.uuid_ms_subsystem = :subsystemId ");
		paramStack.push(new Object[]{"subsystemId", Long.valueOf((String) params[1][1])});
		return sb;
	}
	
	/*
	 * 0 { "branchId", "0".equals(branchId) ? String.valueOf(user.getMsBranch().getUuidBranch()) : branchId },
	 * 1 { "subsystemId", String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem()) }, 
	 * 2 { "userId", userId.isEmpty() ? "%" : userId }, 
	 * 3 { "startDate", startDate }, 
	 * 4 { "endDate", endDate } ,
	 * 5 { "gsSla", gsSla }
	 */
	private StringBuilder sqlPagingBuilderByBranch(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID BRANCH
		sb.append("and N.uuid_branch = :branchId ");
		paramStack.push(new Object[]{"branchId", Long.valueOf((String) params[0][1])});
		
		//---ASSIGN DATE
		if ((String) params[3][1] != null && (String) params[4][1] != null) {
			sb.append("and N.assign_date between :startDate and :endDate ");
			paramStack.push(new Object[]{"startDate", (String) params[3][1]});
			paramStack.push(new Object[]{"endDate", (String) params[4][1]});
		}
		
		//---UUID USER
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("and N.uuid_ms_user = :userId ");
			paramStack.push(new Object[]{"userId", Long.valueOf((String) params[2][1])});
		}
		
		//---STATUS CODE
		sb.append("and mst.status_code IN ('V','P','S') ");
		
		//---UUID SUBSYSTEM
		sb.append("and mst.uuid_ms_subsystem = :subsystemId ");
		paramStack.push(new Object[]{"subsystemId", Long.valueOf((String) params[1][1])});
		return sb;
	}

	@Override
	public byte[] exportExcel(AmMsuser user, String branchId, String userId,
			String startDate, String endDate, String type, AuditContext callerId) {
		XSSFWorkbook workbook = this.createXlsTemplate(user, branchId, userId, startDate, 
				endDate, type, callerId);
		
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(AmMsuser user, String branchId,
			String userId, String startDate, String endDate, String type, AuditContext callerId) {
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setSubsystemCode(user.getAmMssubsystem().getSubsystemName());
		reportBean.setUuidLoginId(String.valueOf(user.getUuidMsUser()));
		reportBean.setUuidBranch(branchId);
		reportBean.setUuidUser(userId);
		reportBean.setStartDate(startDate);
		reportBean.setEndDate(endDate);
		reportBean.setType(type);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(user);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("SLA Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_SLA_COLLECTION.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}
	
	private XSSFWorkbook createXlsTemplate(AmMsuser user, String branchId, String userId, String startDate,
			String endDate, String type, AuditContext callerId) {
		
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Sla Report");
			List resultReportSla = new ArrayList<>();
			
			if (type.equalsIgnoreCase("0")) {
				resultReportSla = this.getReportSla(String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem()), 
						String.valueOf(user.getMsBranch().getUuidBranch()), startDate, endDate, callerId);
			}
			else {
				resultReportSla = (List) this.getReportSlaDetail(user, type, branchId, userId, 
						startDate, endDate, callerId).get("resultList");
			}
				
			this.createData(workbook, sheet, resultReportSla, 
					type.equalsIgnoreCase("0") ? HEADER_SUMMARY : HEADER_DETAIL, 
					type.equalsIgnoreCase("0") ? SUMMARY_COLUMN_WIDTH : DETAIL_COLUMN_WIDTH, 
					type.equals("0")?"SUMMARY":"DETAIL",
					startDate, endDate);
		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createData(XSSFWorkbook workbook, XSSFSheet sheet, List result, 
			String[] header, int[] columnSetting, String type, String startDate, String endDate) {
		
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell = 0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < header.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT SLA " + type + " PERIOD "+ startDate.substring(0, 10) 
					+ " - " + endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(header[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, columnSetting[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, header.length-1));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//data cell
			for (int j = 0; j < header.length; j++) {
				XSSFCell cell = rowData.createCell(j);
				cell.setCellValue(temp.get("d"+(j)).toString());
				cell.setCellStyle(styles.get("cell"));
			}
		}
	}

	private static Map<String, CellStyle> createStyles(Workbook wb) {
		
		Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
		CellStyle style;

		style = wb.createCellStyle();
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		styles.put("cell", style);

		style = wb.createCellStyle();
		Font font = wb.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setFillPattern(CellStyle.SOLID_FOREGROUND);
		styles.put("header",style );
		return styles;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(trReportResultLog.getAmMsuser(), 
				reportBean.getUuidBranch(), reportBean.getUuidUser(), 
				reportBean.getStartDate(), reportBean.getEndDate(), reportBean.getType(), callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("SLAReport_");
			
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
				reportBean.setUuidBranch(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(reportBean.getUuidBranch()));
				sb.append(msBranch.getBranchCode());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if ("0".equals(reportBean.getUuidUser()) || "%".equals(reportBean.getUuidUser())) {
				reportBean.setUuidUser(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
				AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(reportBean.getUuidUser()));
				sb.append(user.getLoginId());
			} 
			else {
				sb.append("ALL");
			}
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(trReportResultLog
				.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime().getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}
	
	private String retrieveSlaTime() {
		String gsSla = (String) this.getManagerDAO().selectOneNativeString(
				"SELECT GS_VALUE FROM AM_GENERALSETTING WHERE GS_CODE = :gsCode",
				new String[][]{{"gsCode", GlobalKey.GENERALSETTING_SLA_TIME}});
		return gsSla;
	}
}
