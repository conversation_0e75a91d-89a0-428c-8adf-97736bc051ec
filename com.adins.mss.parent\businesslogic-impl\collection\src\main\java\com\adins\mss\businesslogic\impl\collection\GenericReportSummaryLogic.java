package com.adins.mss.businesslogic.impl.collection;

import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.ReportSummaryLogic;
import com.adins.mss.constants.GlobalVal;

public class GenericReportSummaryLogic extends BaseLogic implements ReportSummaryLogic{
//	private static final Logger LOG = LoggerFactory.getLogger(GenericReportSummaryLogic.class);

	@SuppressWarnings("rawtypes")
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Map<String, Object> reportSummary(AuditContext callerId){
		String uuid = callerId.getCallerId();
		String[][] id = {{"uuid", uuid}};
		String [][] param = {{"start", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 00:00:00.000"},
				{"end", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 23:59:59.997"},
				{"uuid", uuid}, {"failed", GlobalVal.COL_CATEGORY_RESULT_FAILED}};
		List report = this.getManagerDAO().selectAllNative("services.collection.reportSum", id, null);
		List submitTaskReprot = this.getManagerDAO().selectAllNative("services.collection.submitTaskReport", param, null);
		
		Iterator itr = report.iterator();
		Map<String, Object> summary = new HashMap<String, Object>();
		while(itr.hasNext()){
			Map mp = (Map) itr.next();
			summary.put((String) mp.get("d0"), (Object) mp.get("d1"));
		}
		summary.put("submitTaskReport", submitTaskReprot);
		return summary;	
	}
}

