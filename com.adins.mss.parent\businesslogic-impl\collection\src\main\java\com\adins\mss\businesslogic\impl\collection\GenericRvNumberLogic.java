package com.adins.mss.businesslogic.impl.collection;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.AdInsException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.RVNumberLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrRvnumber;
import com.adins.mss.model.custom.RvNumberBean;
import com.adins.mss.services.model.newconfins.AddRVNumberListReceiptBean;
import com.adins.mss.services.model.newconfins.AddRVNumberListUserBean;
@SuppressWarnings({ "unchecked", "rawtypes" })
public class GenericRvNumberLogic extends BaseLogic implements RVNumberLogic{
	private static final Logger LOG = LoggerFactory.getLogger(GenericRvNumberLogic.class);
	
	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public void clearRVNumber(AuditContext callerId) {
		Object params[][] = {{"uuidUser", callerId.getCallerId()}};
		this.getManagerDAO().deleteNativeString("delete TR_RVNUMBER where UUID_MS_USER = :uuidUser", params);
		LOG.info("Success clearRVNumber for user {}", callerId.getCallerId());
	}

	@Override
	public void getRVfromCore(AuditContext callerId) {
		return;		
	}

	@Override
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	public List<RvNumberBean> syncRvNumber(AuditContext callerId, String dtmCrt) {
		List rvNumberBeanList = new ArrayList<>();
		List rvNumberList = null; 
		try{
			Date datetime = null;
			SimpleDateFormat format = new SimpleDateFormat("ddMMyyyyHHmmss");
			if(StringUtils.isNotBlank(dtmCrt)){
				try {
					datetime = format.parse(dtmCrt);
				} 
				catch (ParseException e) {
					LOG.error(e.getMessage(), e);
					e.printStackTrace();
				}
			}
			if(null != datetime){
				Object params[][] = {{"uuidUser", callerId.getCallerId()}, {"dtmcrt",datetime}, {"status", GlobalVal.STATUS_RV_NUMBER_NEW}};
				rvNumberList = this.getManagerDAO().selectAllNativeString("select UUID_RV_NUMBER, UUID_MS_USER,RV_NUMBER,format(DTM_CRT,'ddMMyyyyHHmmss') "
						+ "from TR_RVNUMBER where format(DTM_CRT,'yyyyMMddHHmmss') > format(:dtmcrt,'yyyyMMddHHmmss') "
						+ "and UUID_MS_USER = :uuidUser "
						+ "and STATUS_RV = :status order by RV_NUMBER", params);
			} 
			else {
				Object params[][] = {{"uuidUser", callerId.getCallerId()}, {"status", GlobalVal.STATUS_RV_NUMBER_NEW}};
				rvNumberList = this.getManagerDAO().selectAllNativeString("select UUID_RV_NUMBER, UUID_MS_USER,RV_NUMBER,format(DTM_CRT,'ddMMyyyyHHmmss') "
						+ "from TR_RVNUMBER where UUID_MS_USER = :uuidUser "
						+ "and STATUS_RV = :status order by RV_NUMBER", params);
			}
			
			if(null!=rvNumberList){
				for(int i = 0; i< rvNumberList.size(); i++){
					Map rvNumber = (Map)rvNumberList.get(i);
					RvNumberBean bean = new RvNumberBean();
					bean.setUuid_rv_number(rvNumber.get("d0").toString());
					bean.setUuid_ms_user(rvNumber.get("d1").toString());
					bean.setRv_number(rvNumber.get("d2").toString());
					bean.setDtm_crt(rvNumber.get("d3").toString());
					rvNumberBeanList.add(bean);
				}
			}
		} 
		catch (AdInsException e) {
			LOG.error("Exception on syncRvNumber", e);
			throw e;
		}
		return rvNumberBeanList;
	}
	
	//================== SYNC RV =============================
	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public void clearRVNumberByCollector(long uuidUser) {
		Object params[][] = {{"uuidUser", uuidUser}};
		this.getManagerDAO().deleteNativeString("delete TR_RVNUMBER where UUID_MS_USER = :uuidUser", params);
		LOG.info("Success clearRVNumber for user {}", uuidUser);
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void syncRV(AuditContext auditContext, String loginId, List<String> listRv) {
		LOG.info("WS SYNC RV");
			
		Object[][] params = {{ Restrictions.eq("loginId", loginId) }};
		AmMsuser dbModel = this.getManagerDAO().selectOne(AmMsuser.class, params);
			
		if(dbModel == null){
			LOG.info("Collector ID is invalid");
			throw new EntityNotFoundException("Collector ID : " 
					+ loginId+ " is not exist.", loginId);
		}
	
		clearRVNumberByCollector(dbModel.getUuidMsUser());
		if(!listRv.isEmpty()){
			for(String rv : listRv){
				TrRvnumber objRvNumber = this.getManagerDAO().selectOne(TrRvnumber.class, 
						new Object[][]{ {Restrictions.eq("amMsuser.uuidMsUser", dbModel.getUuidMsUser())}, 
						{Restrictions.eq("rvNumber", rv)}});
				if(objRvNumber == null){
					TrRvnumber beanRvNumber = new TrRvnumber();
					beanRvNumber.setAmMsuser(dbModel);
					beanRvNumber.setDtmCrt(new Date());
					beanRvNumber.setRvNumber(rv);
					beanRvNumber.setStatusRv(GlobalVal.STATUS_RV_NUMBER_NEW);
					this.getManagerDAO().insert(beanRvNumber);
					LOG.info("WS SYNC RV, Insert RV with LOGIN ID : {}, RV NUMBER : {}",
							loginId, rv);
				}
				else{
					throw new EntityNotUniqueException("Collector ID " + loginId +
							" with RV Number " + rv + " is already exist.", 
							loginId + " - " + rv);
				}
			}
		}
		else{
			LOG.info("List RV Number is null");
			throw new IllegalArgumentException("Argument List RV Number cannot be null");
		}
			
	}
	
	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public String addRVNumber(List<AddRVNumberListUserBean> rvNumberUserList, AuditContext callerId) {
	
		if(rvNumberUserList.size() > 0){
			for(AddRVNumberListUserBean userBean : rvNumberUserList){
				String action = userBean.getAction();
				Object paramsUser[][] = {{Restrictions.eq("loginId", userBean.getUserId())}};
				AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, paramsUser);
				if(null == user) {
					throw new EntityNotFoundException("User Not Found", userBean.getUserId());
				}
				
				List<AddRVNumberListReceiptBean> rvReceiptList = userBean.getListReceiptForm();
				LOG.info("Add RV Number for User {}, receiptNo size: {}", user.getLoginId(), rvReceiptList.size());
				if(rvReceiptList.size() > 0){
					for(AddRVNumberListReceiptBean receiptBean : rvReceiptList){
						LOG.info("Add RV Number for User {}, receiptNo: {}", user.getLoginId(), receiptBean.getReceiptNo());
						Object paramRV [][] = {{"rvNumber", receiptBean.getReceiptNo()}};
						TrRvnumber isExist = this.getManagerDAO().selectOne(TrRvnumber.class, paramRV);
						
						if(GlobalVal.ACTION_RETURN.equals(action) || GlobalVal.ACTION_REASSIGN.equals(action)){
							if(null != isExist){
								TrRvnumber trn = isExist;
								if(GlobalVal.ACTION_RETURN.equals(action) && GlobalVal.STATUS_RV_NUMBER_NEW.equals(trn.getStatusRv())){
									this.getManagerDAO().delete(trn);
								}
								else if(GlobalVal.ACTION_REASSIGN.equals(action) && GlobalVal.STATUS_RV_NUMBER_NEW.equals(trn.getStatusRv())){
									trn.setDtmCrt(new Date());
									trn.setAmMsuser(user);
									this.getManagerDAO().update(trn);
								}								
							}
							else{
								if(GlobalVal.ACTION_REASSIGN.equals(action)){
									TrRvnumber trn = new TrRvnumber();
									trn.setDtmCrt(new Date());
									trn.setAmMsuser(user);
									trn.setStatusRv(GlobalVal.STATUS_RV_NUMBER_NEW);
									trn.setRvNumber(receiptBean.getReceiptNo());
									this.getManagerDAO().insert(trn);
								}
							}
						} 
						else if(GlobalVal.ACTION_ADD.equals(action)){
							if(null == isExist){
								TrRvnumber trn = new TrRvnumber();
								trn.setDtmCrt(new Date());
								trn.setAmMsuser(user);
								trn.setStatusRv(GlobalVal.STATUS_RV_NUMBER_NEW);
								trn.setRvNumber(receiptBean.getReceiptNo());
								this.getManagerDAO().insert(trn);
							}
							else{
								TrRvnumber trn = isExist;
								if(GlobalVal.STATUS_RV_NUMBER_NEW.equals(trn.getStatusRv())){
									trn.setDtmCrt(new Date());
									trn.setAmMsuser(user);
									this.getManagerDAO().update(trn);
								}
							}
						}
					}
				}
			}
		}
		return GlobalVal.SERVICES_RESULT_SUCCESS;
	}
}
