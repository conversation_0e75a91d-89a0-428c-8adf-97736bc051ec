package com.adins.mss.businesslogic.impl.collection;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.TaskCollectionLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.FinalTrTaskH;
import com.adins.mss.model.FinalTrTaskdocument;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.TrRvnumber;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.LocationBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.collection.SubmitPrintCountBean;
import com.adins.mss.services.model.common.TaskListBean;
import com.adins.mss.services.model.common.VerificationDBean;
import com.adins.mss.services.model.common.VerificationLBean;
import com.adins.mss.util.PropertiesHelper;
import com.google.gson.Gson;

@SuppressWarnings({"rawtypes","unchecked"})
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericTaskCollectionLogic extends BaseLogic implements TaskCollectionLogic{
	private static final Logger LOG = LoggerFactory.getLogger(GenericTaskCollectionLogic.class);
	private CommonLogic commonLogic;
	private IntFormLogic intFormLogic;
	
	public void setCommonLogic(CommonLogic commonLogic) {
        this.commonLogic = commonLogic;
    }
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public int updatePrintCount(List<SubmitPrintCountBean> print, AuditContext auditContext) {
		for (SubmitPrintCountBean bean : print) {	
			TrTaskH task = this.getManagerDAO().selectOne(TrTaskH.class, Long.valueOf(bean.getUuidTaskH()));
			if (task.getPrintCount() == null) {
				task.setPrintCount(0);
			}
			
			if (task.getPrintCount()==0) {
				task.setPrintCount(task.getPrintCount()+1);
				task.setUsrUpd(auditContext.getCallerId());
				task.setDtmUpd(new Date());
				
				AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(auditContext.getCallerId()));
				intFormLogic.saveResult(auditContext, String.valueOf(task.getTaskId()), task.getFlagSource(), usr.getAmMssubsystem().getSubsystemName(), auditContext.getCallerId(), "1");
				this.getManagerDAO().update(task);
			}
			else {
				if (task.getPrintCount() > 0) {
					task.setPrintCount(task.getPrintCount()+1);
					this.getManagerDAO().update(task);
				}
			}
		}
		
		return print.size();
	}

	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public String updateRVNumber(long uuidTaskH, String rvNumber, String dtm_use, AuditContext auditContext) {
		TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTaskH);
		if (null != taskH) {
			if (taskH.getPrintCount() == null) {
				taskH.setPrintCount(0);
			}
			
			if (taskH.getPrintCount() == 0) {
				Object params[][] = {{Restrictions.eq("amMsuser.uuidMsUser", NumberUtils.toLong(auditContext.getCallerId()))}, {Restrictions.eq("rvNumber", rvNumber)}};
				TrRvnumber trRv = this.getManagerDAO().selectOne(TrRvnumber.class, params);
				if (null == trRv) {
					throw new EntityNotFoundException("Rv Number Not Found", rvNumber);
				}
				LOG.info("Update RV Number: Task Id = {} Agreement No = {} RV Number = {}", taskH.getTaskId(), taskH.getAgreementNo(), trRv.getRvNumber());
				if (null == taskH.getRvNumber()) {
					taskH.setRvNumber(trRv.getRvNumber());
					taskH.setUsrUpd(auditContext.getCallerId());
					taskH.setDtmUpd(new Date());
					this.getManagerDAO().update(taskH);
					LOG.info("Success Update RV Number: Task Id = {} Agreement No = {} RV Number = {}", taskH.getTaskId(), taskH.getAgreementNo(), rvNumber);
					
					SimpleDateFormat format = new SimpleDateFormat("ddMMyyyyHHmmss");
					try {
						trRv.setDtmUse(format.parse(dtm_use));
					} 
					catch (ParseException e) {
						LOG.error(e.getMessage(),e);
					}
					trRv.setStatusRv(GlobalVal.STATUS_RV_NUMBER_USED);
					
					//begin send to core
					AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(auditContext.getCallerId()));
					intFormLogic.saveResult(auditContext, String.valueOf(taskH.getTaskId()), taskH.getFlagSource(), usr.getAmMssubsystem().getSubsystemName(), auditContext.getCallerId(), "1");
					//end send to core
					
					this.getManagerDAO().update(trRv);
					LOG.info("Success Update TrRvnumber: RV Number = {}, Status = {}", trRv.getRvNumber(), trRv.getStatusRv());
					
					return GlobalVal.SERVICES_RESULT_SUCCESS;
				}
			}
		}
		return null;
	}
	

	@Override
	public List<TaskListBean> getTaskLog(AuditContext callerId) {
		List<TaskListBean> listTask = new ArrayList<TaskListBean>();

		Object[][] params = {{"uuidUser", NumberUtils.toLong(callerId.getCallerId())}};
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative("services.collection.taskColl.getTaskLog", params, null);
		if (!list.isEmpty()) {
			for(int i=0;i<list.size();i++){
				Map mp = list.get(i);
				TaskListBean tlb = new TaskListBean();
				tlb.setUuidTaskH(mp.get("d0").toString());
				tlb.setCustomerName((String) mp.get("d1"));
				tlb.setCustomerPhone((String) mp.get("d2"));
				tlb.setCustomerAddress((String) mp.get("d3"));
				tlb.setNotes((String) mp.get("d4"));
				tlb.setLatitude(String.valueOf((BigDecimal) mp.get("d5")));
				tlb.setLongitude(String.valueOf((BigDecimal) mp.get("d6")));
				if (mp.get("d7") != null) {
					tlb.setAssignmentDate((Date) mp.get("d7"));
				}
				tlb.setPriority((String) mp.get("d8"));
				tlb.setSchemeId(mp.get("d9").toString());
				if (mp.get("d110") != null) {
					tlb.setFormLastUpdate((Date) mp.get("d10"));
				}
				tlb.setApplNo((String) mp.get("d11"));
				Integer isVerification = (Integer) mp.get("d12");
				tlb.setIsVerification(isVerification.toString());
				tlb.setIsPreviewServer((String) mp.get("d13"));
				if (mp.get("d14") != null) {
					tlb.setDtmcrt((Date) mp.get("d14"));
				}
				tlb.setIsPrintable((String) mp.get("d15"));
				tlb.setTaskId((String) mp.get("d16"));
				tlb.setSubmitDate((Date) mp.get("d17"));
				tlb.setPrintCount((Integer) mp.get("d18"));
				tlb.setRvNumber((String) mp.get("d19"));
				tlb.setFlag((String) mp.get("d20"));
				tlb.setFormVersion((String) mp.get("d21"));
				listTask.add(tlb);
			}
		}
		
		return listTask;
	}
	
	@Override
	public List<TaskListBean> getTaskLogDetail(long uuidH, String flag, AuditContext callerId) {
		List listTask = null;
		
		boolean loadFromJson = PropertiesHelper.isTaskDJson();
		if (loadFromJson) {
			long uuidForm = 0l;
			int formVersion = 0;
			
			if ("2".equals(flag)) {
				FinalTrTaskH finalTrTaskH = this.getManagerDAO().selectOne(
						"from FinalTrTaskH h join fetch h.msForm where h.uuidTaskH = :uuidTaskH",
						new Object[][]{ {"uuidTaskH", uuidH} });
				Assert.notNull(finalTrTaskH);
				uuidForm = finalTrTaskH.getMsForm().getUuidForm();
				formVersion = finalTrTaskH.getFormVersion();
			}
			else {
				TrTaskH trTaskH = this.getManagerDAO().selectOne(
						"from TrTaskH h join fetch h.msForm where h.uuidTaskH = :uuidTaskH",
						new Object[][]{ {"uuidTaskH", uuidH} });
				Assert.notNull(trTaskH);
				uuidForm = trTaskH.getMsForm().getUuidForm();
				formVersion = trTaskH.getFormVersion();
			}
			
			List<MsFormquestionset> formQuestionset = this.commonLogic.retrieveMsFormquestionset(
					uuidForm, formVersion, callerId);
			listTask = this.loadFromJson(uuidH, formQuestionset, flag, callerId);
		}
		else {
			listTask = this.loadLogDetailFromRow(uuidH, flag);
		}		
		
		return listTask;
	}
	
	private List loadFromJson(long uuidTaskH, List<MsFormquestionset> formQuestionset, String flag, AuditContext callerId) {		
		String jsonDocument = null;		
		if ("2".equals(flag)) {
			FinalTrTaskdocument docDb = this.getManagerDAO().selectOne(
					"from FinalTrTaskdocument doc join fetch doc.finalTrTaskH where doc.uuidTaskId = :uuidTaskId",
					new Object[][]{{"uuidTaskId", uuidTaskH}});
			if (docDb == null || StringUtils.isBlank(docDb.getDocument())){
				return Collections.emptyList();
			}
		}
		else {
			TrTaskdocument docDb = this.getManagerDAO().selectOne(
					"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
					new Object[][]{{"uuidTaskId", uuidTaskH}});
			if (docDb == null || StringUtils.isBlank(docDb.getDocument())){
				return Collections.emptyList();
			}
			
			jsonDocument = docDb.getDocument();
		}		
		
		Gson gson = new Gson();
		TaskDocumentBean document = gson.fromJson(jsonDocument, TaskDocumentBean.class);
		List<AnswerBean> answers = document.getAnswers();
		if (answers == null || answers.isEmpty()){
			return Collections.emptyList();
		}
		
		List listTaskD = new ArrayList<>();
		for (Iterator<AnswerBean> iterator = answers.iterator(); iterator.hasNext();) {
			AnswerBean answer = iterator.next();			
			VerificationDBean taskD = new VerificationDBean();
			
			MsFormquestionset msFormquestion = this.findMsFormquestion(formQuestionset, answer.getQuestion().getUuidQuestion());
			String uuidQuestionGroup = String.valueOf(msFormquestion.getMsQuestiongroup().getUuidQuestionGroup());
			String uuidQuestion = String.valueOf(answer.getQuestion().getUuidQuestion());
			
			taskD.setUuidTaskD(StringUtils.join(String.valueOf(uuidTaskH), "-", uuidQuestionGroup, "-", uuidQuestion));
			taskD.setUuidQuestionGroup(uuidQuestionGroup);
			taskD.setUuidQuestion(uuidQuestion);
			taskD.setTagName(answer.getQuestion().getTagOrderName());
			taskD.setUuidTaskH(String.valueOf(uuidTaskH));
			taskD.setQuestionText(answer.getQuestion().getLabel());
			taskD.setTimestackTask(null); //timestamp_task digunakan di tele/verif saat load, tapi tidak pernah save
			taskD.setRegex(null);
			taskD.setIsReadonly(null);
			taskD.setLovId("");
			if (answer.getLocation() != null) {
				LocationBean location = answer.getLocation();
				taskD.setLatitude((location.getLat() == null) ? null : location.getLat().toString());
				taskD.setLongitude((location.getLng() == null) ? null : location.getLng().toString());
				taskD.setMcc(String.valueOf(location.getMcc()));
				taskD.setMnc(String.valueOf(location.getMnc()));
				taskD.setLac(String.valueOf(location.getLac()));
				taskD.setCid(String.valueOf(location.getCid()));
				taskD.setAccuracy(location.getAccuracy());				
			}
			if (answer.getLobAnswer() != null && answer.getLobAnswer().isHasImage()) {				
				taskD.setImage("1");
			}
			
			Iterator<OptionBean> iterOptions = (answer.getOptAnswers() == null)
					? null : answer.getOptAnswers().iterator();
			listTaskD.add(taskD);
			
			if (iterOptions == null){
				continue;
			}
			
			int ctr = 0;
			while (iterOptions.hasNext()) {
				OptionBean option = iterOptions.next();
				if (ctr > 0) {
					try {
						VerificationDBean clone = (VerificationDBean) BeanUtils.cloneBean(taskD);
						clone.setLovId(String.valueOf(option.getUuid()));
						clone.setCode(option.getCode());
						listTaskD.add(clone);
					} 
					catch (IllegalAccessException | InstantiationException | InvocationTargetException | NoSuchMethodException e) {
						LOG.error("Error on cloning taskD", e);
					}
				}
				else {
					taskD.setLovId(String.valueOf(option.getUuid()));
					taskD.setCode(option.getCode());
				}
				ctr++;
			}
		}
		
		return listTaskD;
	}
	
	private MsFormquestionset findMsFormquestion(List<MsFormquestionset> msFormquestionset, long uuidQuestion) {
		if (msFormquestionset == null || msFormquestionset.isEmpty()){
			return null;
		}
		
		for (Iterator<MsFormquestionset> iterator = msFormquestionset.iterator(); iterator.hasNext();) {
			MsFormquestionset msFormquestion = iterator.next();
			if (msFormquestion.getMsQuestion().getUuidQuestion() == uuidQuestion) {
				return msFormquestion;
			}
		}
		
		return null;
	}
	
	private List loadLogDetailFromRow(long uuidH, String flag) {
		List listTask = new ArrayList();
		
		Object[][] param = { {"uuidTaskH", uuidH} };
		List taskDs = null;
		List taskLs = null;
		
		if ("2".equals(flag)) {
			taskDs = this.getManagerDAO().selectForList(VerificationDBean.class, "services.collection.taskColl.getTaskLogDetailDFinal", param, null);
		}
		else {
			taskDs = this.getManagerDAO().selectForList(VerificationDBean.class, "services.collection.taskColl.getTaskLogDetailD", param, null);
		}
		listTask.addAll(taskDs);
		
		if ("2".equals(flag)) {
			taskLs = this.getManagerDAO().selectForList(VerificationLBean.class, "services.collection.taskColl.getTaskLogDetailLFinal", param, null);
		}
		else {
			taskLs = this.getManagerDAO().selectForList(VerificationLBean.class, "services.collection.taskColl.getTaskLogDetailL", param, null);
		}
		listTask.addAll(taskLs);
		
		return listTask;
	}
	

	@Override
	public Map getTaskListBean(AuditContext callerId, long uuidTaskH) {
		Map map = new HashMap ();
			
		Object[][] param = { {"uuidTaskH", uuidTaskH} };
		String tth = (String) this.getManagerDAO().selectOneNativeString(
				"select (case when convert(date,ASSIGN_DATE) = convert(date, GETDATE()) then '1' else '0' end) AS date" +
				" from TR_TASK_H" +
				" where UUID_TASK_H = :uuidTaskH", param);	
		map.put("tth", tth);

		return map;
	}
}
