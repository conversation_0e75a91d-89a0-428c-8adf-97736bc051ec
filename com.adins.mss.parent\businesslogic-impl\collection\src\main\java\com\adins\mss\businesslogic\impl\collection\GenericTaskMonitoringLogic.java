package com.adins.mss.businesslogic.impl.collection;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.collection.TaskMonitoringLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings({ "rawtypes", "unchecked"})
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericTaskMonitoringLogic extends BaseLogic implements TaskMonitoringLogic {
	@Autowired
	private GlobalLogic globalLogic;
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}

	@Override
	public Map<String, String> getSpvCombo(String uuidBranch, AuditContext callerId) {
		Map<String, String> result = MapUtils.EMPTY_MAP;
			
		String tagJob = globalLogic.getGsValue(GlobalVal.SUBSYSTEM_MC + GlobalKey.GENERALSETTING_JOBSPV, callerId);
		String[][] params = { { "uuidBranch", uuidBranch }, { "jobCode", tagJob } };
		List list = this.getManagerDAO().selectAllNative("collection.taskmonitoring.spvCombo", params, null);
		Map<String, String> comboList = new HashMap<String, String>();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				Map temp2 = (Map) list.get(i);
				comboList.put((String) temp2.get("d0"), (String) temp2.get("d1"));
			}
			result = sortByValues(comboList);
		}
		return result;
	}
	

	private Map sortByValues(Map<String, String> comboList) {
		List list = new LinkedList(comboList.entrySet());

		Collections.sort(list, new Comparator() {
			public int compare(Object o1, Object o2) {
				return ((Comparable) ((Map.Entry) (o1)).getValue())
						.compareTo(((Map.Entry) (o2)).getValue());
			}
		});

		Map sortedHashMap = new LinkedHashMap();
		for (Iterator it = list.iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			sortedHashMap.put(entry.getKey(), entry.getValue());
		}
		return sortedHashMap;
	}
	
	@Override
	public Map getTaskMonitoring(String id, String uuidSubsystem, AuditContext callerId) {
		Map<String, Object> result = new HashMap();
		List monitoringList = new ArrayList();
		String[][] params = {{"uuidSpv", id}};
		
		List<?> userList = this.getManagerDAO().selectAllNative(
				"collection.taskmonitoring.listUser", params, null);
		
		String startDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 00:00:00.000";
		String endDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 23:59:59.997";
		
		NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
		
		for (int i = 0; i < userList.size(); i++) {
			Map<?, ?> mapUser = (HashMap<?, ?>) userList.get(i);
		    String uuidSvy= mapUser.get("d0").toString();
		    String[][] params2 = { { "uuidSvy", uuidSvy },{"start", startDate}, {"end", endDate} };
			
			Map<Object, Object> eachUser= new HashMap<>();
			eachUser.put("loginId", mapUser.get("d3").toString());
			eachUser.put("fullName", mapUser.get("d1").toString());
			eachUser.put("loginIdSpv", mapUser.get("d4") == null ? StringUtils.EMPTY : mapUser.get("d4").toString());
			eachUser.put("fullNameSpv", mapUser.get("d5") == null ? StringUtils.EMPTY : mapUser.get("d5").toString());
			
			String[][] paramsDetail = { { "uuidSvy", uuidSvy }};
			List<?> sumDetail = this.getManagerDAO().selectAllNative(
					"collection.taskmonitoring.sumDetail", paramsDetail, null);
			String[][]paramsUuid = {{"uuidUser", uuidSvy}, {"start", startDate}, {"end", endDate}};
			
			AmMsuser user = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", 
					new Object[][] {{"uuidMsUser", Long.valueOf(uuidSvy)}});
			BigDecimal limit = null;
			
			if(getLimitCohEnabled(GlobalKey.GENERALSETTING_LIMIT_COH_ENABLED).equals("1")){
				if(user.getCashLimit()!=null && user.getCashLimit().compareTo(BigDecimal.valueOf(0)) != 0){
					limit = user.getCashLimit();
				}
				else{
					limit = user.getMsBranch().getCashLimitDefault();
				}
			}
			
			for (int j = 0; j < sumDetail.size(); j++) {
				Map<?, ?> mapDetailColl = (HashMap<?, ?>) sumDetail.get(j);					
				
				if(getLimitCohEnabled(GlobalKey.GENERALSETTING_LIMIT_COH_ENABLED).equals("1")){
					BigDecimal cashOnHand = user.getCashOnHand()==null?BigDecimal.valueOf(0):user.getCashOnHand();
					if(limit == null) {
						limit = BigDecimal.valueOf(0);
					}					
				
					if(cashOnHand.compareTo(limit) > 0 && limit.compareTo(BigDecimal.valueOf(0)) != 0) {
						eachUser.put("bgColor", "red");
						eachUser.put("limitBg", "#e64b3c");
					}
					else{
						eachUser.put("bgColor", "darkblue");
						eachUser.put("limitBg", "#2c3045");
					}
				}else{
					eachUser.put("bgColor", "darkblue");
					eachUser.put("limitBg", "#2c3045");
				}
				eachUser.put("toBeCollect",formatKurs.format(
						Double.valueOf(mapDetailColl.get("d0").toString().isEmpty()? 
								"0": mapDetailColl.get("d0").toString())));
				eachUser.put("totalPaid",formatKurs.format(
						Double.valueOf(mapDetailColl.get("d1").toString().isEmpty()? 
								"0": mapDetailColl.get("d1").toString())));
				eachUser.put("totalDeposit",formatKurs.format(
						Double.valueOf(mapDetailColl.get("d2").toString().isEmpty()? 
								"0": mapDetailColl.get("d2").toString())));
				eachUser.put("lastDep", (String)this.getManagerDAO().selectOneNative(
						"collection.taskmonitoring.lastDepositTime", paramsUuid));
				eachUser.put("cashOnHand", formatKurs.format((user.getCashOnHand()==null)?0:user.getCashOnHand()));
			}
			List<?> task = this.getManagerDAO().selectAllNative(
					"collection.taskmonitoring.listTask", params2, null);
			
			eachUser.put("taskList", task);
			monitoringList.add(eachUser);
		}
		
		String[][] params3 = { { "uuidUser", id },
				{"start", startDate}, {"end", endDate} };
		
		Object[] summary = (Object[]) this.getManagerDAO().selectOneNative(
				"collection.taskmonitoring.sumtobecollectandpaid", params3);
		
		result.put("totalCollected", formatKurs.format(
				Double.valueOf((null == summary[0]) ? "0" : summary[0].toString())));
		result.put("totalPayment", formatKurs.format(
				Double.valueOf((null == summary[1]) ? "0" : summary[1].toString())));
		result.put("monitoringList", monitoringList);
		return result ;
	}
		
	@Override
	public Map<String, String> getSpvComboByHierarkiUser(String uuidUser, AuditContext callerId) {
		Map<String, String> result = MapUtils.EMPTY_MAP;
		String tagJob = globalLogic.getGsValue(GlobalVal.SUBSYSTEM_MC + GlobalKey.GENERALSETTING_JOBSPV, callerId);
		String[][] params = { { "uuidUser", uuidUser }, { "jobCode", tagJob } };
		List list = this.getManagerDAO().selectAllNative(
				"collection.taskmonitoring.spvComboByHierarkiUser", params, null);
		Map<String, String> comboList = new HashMap<String, String>();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				Map temp2 = (Map) list.get(i);
				comboList.put((String) temp2.get("d0"), (String) temp2.get("d1"));
			}
			result = sortByValues(comboList);
		} 	
		return result;
	}
	
	public String getLimitCohEnabled(String gsCode) {
		String limitCohEnabled = "0";
		Object[][] params = {{ Restrictions.eq("gsCode", gsCode) } };
		AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if(result != null){
			limitCohEnabled = result.getGsValue();
		}
		return limitCohEnabled;
	}
	
	public boolean checkIsSpv(AmMsuser amMsUser, AuditContext auditContext) {
		Object[][] params = { {Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MC+GlobalKey.GENERALSETTING_JOBSPV)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if (amGeneralsetting.getGsValue().equalsIgnoreCase(amMsUser.getMsJob().getJobCode())) {
			return true;
		}
		return false;
	}
	
	@Override
	public String getIntervalDuration(AuditContext callerId){
		Object params[][] = {{Restrictions.eq("gsCode", "INTERVAL")}};
		String result = StringUtils.EMPTY;
		AmGeneralsetting bean = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if(bean!=null) {
			result = bean.getGsValue(); 
		}
		else {
			result = "0";
		}
		return result ;
	}
}
