package com.adins.mss.businesslogic.impl.collection;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.collection.UnassignTaskCollectionLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;

@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
@SuppressWarnings({ "rawtypes", "unchecked"})
public class GenericUnassignTaskCollectionLogic extends BaseLogic implements
		UnassignTaskCollectionLogic, MessageSourceAware {

	@Autowired
	private GeolocationLogic geolocationLogic;
	
	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	private GlobalLogic globalLogic;
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}
	
	public void setGeolocationLogic(GeolocationLogic geolocationLogic) {
		this.geolocationLogic = geolocationLogic;
	}

	@Override
	public Map<String, Object> listTaskH(AmMsuser amMsuser, Object params,
			Object paramsCnt, int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String, Object>();

		List<TrTaskH> listTaskHs = new ArrayList<TrTaskH>();
		String uuidStatusTask = this.getUuidStatusTask(
				GlobalVal.COLLECTION_STATUS_TASK_UNASSIGN, amMsuser);
		Object[][] prm = (Object[][]) params;
		prm[2][1] = uuidStatusTask;
		Object[][] prmCnt = (Object[][]) paramsCnt;
		prmCnt[2][1] = uuidStatusTask;

		List<Map<String, Object>> resultUnassigned = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder(
				(Object[][]) prm, paramsStack);
		StringBuilder paramsQueryStringUser = this.sqlPagingBuilderUser(
				(Object[][]) prm, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT * from ( ")
				.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
				.append("select DISTINCT *, ROW_NUMBER() OVER (ORDER BY task_id) AS rownum from ")
				.append("(select * from tr_task_h with (nolock) ")
				.append("where 1=1 ").append(paramsQueryString)
				.append("UNION ")
				.append("select * from tr_task_h with (nolock) ")
				.append("where 1=1 ").append(paramsQueryStringUser)
				.append(") as UT ").append(") a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start ");
		paramsStack.push(new Object[] { "start", ((Object[][]) params)[6][1] });
		paramsStack.push(new Object[] { "end", ((Object[][]) params)[7][1] });
		Object[][] sqlParams = new Object[paramsStack.size()][2];
		for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		resultUnassigned = this.getManagerDAO().selectAllNativeString(
				queryBuilder.toString(), sqlParams);

		Integer resultUnassignedCnt = NumberUtils.INTEGER_ZERO;
		Stack<Object[]> paramsStackCount = new Stack<>();
		StringBuilder paramsQueryStringCount = this.sqlPagingBuilder(
				(Object[][]) prm, paramsStackCount);
		StringBuilder paramsQueryStringUserCount = this.sqlPagingBuilderUser(
				(Object[][]) prm, paramsStackCount);
		StringBuilder queryBuilderCount = new StringBuilder()
				.append("select count(1) from ")
				.append("(select DISTINCT *, ROW_NUMBER() OVER (ORDER BY task_id) AS rownum from ")
				.append("(select * from tr_task_h with (nolock) ")
				.append("where 1=1 ").append(paramsQueryStringCount)
				.append("UNION ")
				.append("select * from tr_task_h with (nolock) ")
				.append("where 1=1 ").append(paramsQueryStringUserCount)
				.append(") as UT2 ) as UT ");
		Object[][] sqlParamsCount = new Object[paramsStackCount.size()][2];
		for (int i = 0; i < paramsStackCount.size(); i++) {
			Object[] objects = paramsStackCount.get(i);
			sqlParamsCount[i] = objects;
		}

		resultUnassignedCnt = (Integer) this.getManagerDAO()
				.selectOneNativeString(queryBuilderCount.toString(),
						sqlParamsCount);

		Object[][] paramPH = { { Restrictions.eq("priorityDesc", "High") } };
		Object[][] paramPN = { { Restrictions.eq("priorityDesc", "Normal") } };

		MsPriority msPriorityH = this.getManagerDAO().selectOne(
				MsPriority.class, paramPH);
		MsPriority msPriorityN = this.getManagerDAO().selectOne(
				MsPriority.class, paramPN);

		for (int i = 0; i < resultUnassigned.size(); i++) {
			Map temp = resultUnassigned.get(i);
			TrTaskH trTaskH = new TrTaskH();
			trTaskH.setUuidTaskH(Long.valueOf(temp.get("d0").toString()));
			trTaskH.setTaskId(temp.get("d32").toString());
			trTaskH.setAgreementNo(temp.get("d34") == null ? StringUtils.EMPTY
					: temp.get("d34").toString());
			trTaskH.setCustomerName(temp.get("d18").toString());
			trTaskH.setCustomerAddress(temp.get("d20").toString());
			trTaskH.setCustomerPhone(temp.get("d19").toString());
			if (temp.get("d10").toString()
					.equals(msPriorityH.getUuidPriority())) {
				trTaskH.setMsPriority(msPriorityH);
			} 
			else {
				trTaskH.setMsPriority(msPriorityN);
			}
			listTaskHs.add(trTaskH);
		}

		result.put(GlobalKey.MAP_RESULT_LIST, listTaskHs);
		result.put(GlobalKey.MAP_RESULT_SIZE, resultUnassignedCnt);

		return result;
	}
	
	/*
	 * 0 "uuidMsUser",
	 * 1 "uuidBranch",
	 * 2 "uuidStatusTask",
	 * 3 "customerName",
	 * 4 "customerAddress",
	 * 5 "agreementNo",
	 * 6 "start",
	 * 7 "end"
	*/
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		// ---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("AND uuid_branch = :uuidBranch ");
			paramStack.push(new Object[] { "uuidBranch",
					Long.valueOf((String) params[1][1]) });
		}

		// ---UUID_STATUS_TASK
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("and uuid_status_task = :uuidStatusTask ");
			paramStack.push(new Object[] { "uuidStatusTask",Long.valueOf((String) params[2][1]) });
		}

		// ---CUSTOMER_NAME
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append("and customer_name like '%' + :customerName + '%' ");
			paramStack.push(new Object[] { "customerName",(String) params[3][1] });
		}

		// ---CUSTOMER_ADDRESS
		if (!StringUtils.equals("%", (String) params[4][1])) {
			sb.append("and customer_address like '%' + :customerAddress + '%' ");
			paramStack.push(new Object[] { "customerAddress",(String) params[4][1] });
		}

		// ---AGREEMENT_NO
		if (!StringUtils.equals("%", (String) params[5][1])) {
			sb.append("and (agreement_no like '%' + :agreementNo + '%') ");
			paramStack.push(new Object[] { "agreementNo", (String) params[5][1] });
		}
		return sb;
	}
	
	/*
	 * 0 "uuidMsUser",
	 * 1 "uuidBranch",
	 * 2 "uuidStatusTask",
	 * 3 "customerName",
	 * 4 "customerAddress",
	 * 5 "agreementNo",
	 * 6 "start",
	 * 7 "end"
	*/
	private StringBuilder sqlPagingBuilderUser(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		// ---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("AND uuid_branch = :uuidBranch ");
			paramStack.push(new Object[] { "uuidBranch",Long.valueOf((String) params[1][1]) });
		}

		// ---UUID_STATUS_TASK
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("and uuid_status_task = :uuidStatusTask ");
			paramStack.push(new Object[] { "uuidStatusTask",Long.valueOf((String) params[2][1]) });
		}

		// ---UUID_USER
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and uuid_ms_user = :uuidMsUser ");
			paramStack.push(new Object[] { "uuidMsUser",Long.valueOf((String) params[0][1]) });
		}

		// ---CUSTOMER_NAME
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append("and customer_name like '%' + :customerName + '%' ");
			paramStack.push(new Object[] { "customerName",(String) params[3][1] });
		}

		// ---CUSTOMER_ADDRESS
		if (!StringUtils.equals("%", (String) params[4][1])) {
			sb.append("and customer_address like '%' + :customerAddress + '%' ");
			paramStack.push(new Object[] { "customerAddress",(String) params[4][1] });
		}

		// ---AGREEMENT_NO
		if (!StringUtils.equals("%", (String) params[5][1])) {
			sb.append("and (agreement_no like '%' + :agreementNo + '%') ");
			paramStack.push(new Object[] { "agreementNo", (String) params[5][1] });
		}
		return sb;
	}

	@Override
	public Map<String, Object> listTaskToAssign(String[] selectedTask,
			AuditContext callerId) {
		Map<String, Object> result = new HashMap<String, Object>();
		List<TrTaskH> listTaskHs = new ArrayList<TrTaskH>();
		String[] tasks = selectedTask[0].split(",");
		for (int i = 0; i < tasks.length; i++) {
			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class,
					Long.valueOf(tasks[i]));
			listTaskHs.add(trTaskH);
		}
		result.put(GlobalKey.MAP_RESULT_LIST, listTaskHs);
		result.put(GlobalKey.MAP_RESULT_SIZE, listTaskHs.size());

		return result;
	}

	@Override
	public Map<String, Object> listUser(String mode, AmMsuser amMsuser, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String, Object>();
		Map<String, Object> resultUser = new HashMap<String, Object>();

		if (GlobalVal.JOB_ADMIN.equals(amMsuser.getMsJob().getJobCode())) {
			String collJobCode = globalLogic.getGsValue(
					GlobalKey.GENERALSETTING_JOBCOLLECTOR, callerId);
			MsJob job = globalLogic.getMsJob(collJobCode, callerId);

			Object[][] params = {
					{ Restrictions.eq("msJob.uuidJob", job.getUuidJob()) },
					{ Restrictions.eq("isActive", "1") },
					{ Restrictions.eq("isDeleted", "0") } };
			String[][] orders = { { "fullName", "ASC" } };
			resultUser = this.getManagerDAO().selectAll(AmMsuser.class, params,
					orders);
		} 
		else {
			String[][] users = null;
			if ("branch".equals(mode)) {
				users = getUserByBranch(amMsuser.getMsBranch().getUuidBranch());
			} 
			else {
				users = getUserByLogin(amMsuser.getUuidMsUser());
			}
			List<AmMsuser> listUser = new ArrayList<AmMsuser>();

			for (int i = 0; i < users.length; i++) {
				if (!"1".equals(users[i][3])) {
					if (null != users[i][0]) {
						AmMsuser bean = new AmMsuser();
						bean.setUuidMsUser(Long.valueOf(users[i][0]));
						bean.setLoginId(users[i][1]);
						bean.setFullName(users[i][2]);
						listUser.add(bean);
					}
				}
			}
			resultUser.put(GlobalKey.MAP_RESULT_LIST, listUser);
			resultUser.put(GlobalKey.MAP_RESULT_SIZE, listUser.size());
		}
		Integer[][] assignment = this.getAssignment((List<AmMsuser>) resultUser
				.get(GlobalKey.MAP_RESULT_LIST));
		String[][] lastLoc = this.getLastLoc(
				(List<AmMsuser>) resultUser.get(GlobalKey.MAP_RESULT_LIST),
				callerId);
		result.put("result", resultUser);
		result.put("assignment", assignment);
		result.put("location", lastLoc);

		return result;
	}

	@Override
	public AmMsuser getUser(long uuidMsUser, AuditContext callerId) {
		AmMsuser amMsuser = this.getManagerDAO().selectOne(
						"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser",
						new Object[][] { { "uuidMsUser", uuidMsUser } });
		return amMsuser;
	}

	@Override
	public Integer[][] getAssignment(List<AmMsuser> listResult) {
		Integer[][] result = new Integer[listResult.size()][3];
		String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
		for (int i = 0; i < listResult.size(); i++) {
			AmMsuser usr = this.getManagerDAO().selectOne("from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser",
							new Object[][] { { "uuidMsUser",listResult.get(i).getUuidMsUser() } });
			Object[][] params = { { "uuidMsUser", usr.getUuidMsUser() },{ "start", currentDate + " 00:00:00.000" },
								{ "end", currentDate + " 23:59:59.997" } };
			Integer taskAssignment = (Integer) this.getManagerDAO().selectOneNative("task.unassigntaskcollection.getTotalTaskAssignment",params);
			Integer submittedTask = (Integer) this.getManagerDAO().selectOneNative("task.unassigntaskcollection.getTotalSubmittedTask",params);
			Object[][] paramsStatus = {{ Restrictions.eq("statusCode",GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION) },
										{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", usr.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, paramsStatus);
			Object[][] paramsPending = { { "uuidMsUser", usr.getUuidMsUser() },{ "uuidStatusTask", msStatustask.getUuidStatusTask() } };
			Integer pendingTask = (Integer) this.getManagerDAO().selectOneNative("task.unassigntaskcollection.getTotalPendingTask",paramsPending);
			result[i][0] = taskAssignment;
			result[i][1] = submittedTask;
			result[i][2] = pendingTask;
		}
		return result;
	}
	
	private String[][] getLastLoc(List<AmMsuser> listResult, AuditContext callerId) {
		String[][] result = new String[listResult.size()][2];
		for (int i = 0; i < listResult.size(); i++) {
			Object[][] params = { { "uuidMsUser",listResult.get(i).getUuidMsUser() } };
			Object[] lastLoc = (Object[]) this.getManagerDAO().selectOneNative("task.unassigntaskcollection.getLastLoc", params);
			if (null != lastLoc) {

				BigDecimal lat = new BigDecimal(lastLoc[4].toString());
				BigDecimal lng = new BigDecimal(lastLoc[5].toString());
				Integer mcc = Integer.parseInt(lastLoc[7].toString());
				Integer mnc = Integer.parseInt(lastLoc[8].toString());
				Integer lac = Integer.parseInt(lastLoc[9].toString());
				Integer cellId = Integer.parseInt(lastLoc[10].toString());

				if (null != lat && null != lng) {
					if (0 == lat.compareTo((BigDecimal.ZERO))
							&& 0 == lng.compareTo(BigDecimal.ZERO)) {
						lat = null;
						lng = null;
						if (null != mcc && null != mnc && null != lac
								&& null != cellId) {
							List<LocationBean> listLocation = new ArrayList<LocationBean>();
							LocationBean locationBean = new LocationBean();
							locationBean.setCellid(cellId);
							locationBean.setLac(lac);
							locationBean.setMcc(mcc);
							locationBean.setMnc(mnc);
							listLocation.add(locationBean);
							this.geolocationLogic.geocodeCellId(listLocation,
									callerId);
							if (null != listLocation.get(0).getCoordinate()) {
								result[i][0] = new BigDecimal(locationBean
										.getCoordinate().getLatitude())
										.toString();
								result[i][1] = new BigDecimal(locationBean
										.getCoordinate().getLongitude())
										.toString();
							}
						}
					} 
					else {
						result[i][0] = lat.toString();
						result[i][1] = lng.toString();
					}
				}
			}
			// 2015-11-26 SUM will show no location on maps instead of latLong
			// of area, misleading information
			else {
				result[i][0] = null;
				result[i][1] = null;
			}
		}
		return result;
	}
	
	private String[][] getUserByBranch(long uuidBranch) {
		String collJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBCOLLECTOR, null);
		Object[][] params = { { "uuidBranch", uuidBranch },{ "jobCode", collJobCode } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative("am.usermanagement.getUserByBranch", params, null);
		if (!list.isEmpty()) {
			String[][] stringResult = new String[list.size()][4];
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				stringResult[i][0] = (String) map.get("d0");
				stringResult[i][1] = (String) map.get("d1");
				stringResult[i][2] = (String) map.get("d2");
				stringResult[i][3] = ((Integer) map.get("d3")).toString();
			}
			return stringResult;
		} 
		else {
			return null;
		}
	}

	private String[][] getUserByLogin(long uuidUser) {
		String collJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBCOLLECTOR, null);
		Object[][] params = { { "uuidMsUser", uuidUser } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative("am.usermanagement.getUserByLogin", params, null);
		if (!list.isEmpty()) {
			String[][] stringResult = new String[list.size()][4];
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				String jobCode = (String) map.get("d5");
				if (jobCode.equals(collJobCode)) {
					stringResult[i][0] = map.get("d0").toString();
					stringResult[i][1] = (String) map.get("d1");
					stringResult[i][2] = (String) map.get("d2");
					stringResult[i][3] = ((Integer) map.get("d3")).toString();
				}
			}
			return stringResult;
		} 
		else {
			return null;
		}
	}

	public String getUuidStatusTask(String statusCode, AmMsuser loginBean) {
		String uuidStatusTask = StringUtils.EMPTY;
		Object[][] params = {
				{ Restrictions.eq("statusCode", statusCode) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
		MsStatustask msStatustask = (MsStatustask) this.getManagerDAO()
				.selectOne(MsStatustask.class, params);
		uuidStatusTask = String.valueOf(msStatustask.getUuidStatusTask());
		return uuidStatusTask;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void assignTask(String[] selectedTask, long uuidMsUser,
			AmMsuser loginBean, long uuidPriority, AuditContext callerId) {

		MsPriority msPriority = this.getManagerDAO().selectOne(MsPriority.class, uuidPriority);
		Object[][] paramMsm = { { Restrictions.eq("statusMobileCode",GlobalVal.STATUS_MOBILE_NEW) } };
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
		String[] tasks = selectedTask[0].split(",");
		for (int i = 0; i < tasks.length; i++) {
			AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,uuidMsUser);
			TrTaskH trTaskH = this.getManagerDAO().selectOne(
							"from TrTaskH u join fetch u.msStatustask m join fetch m.amMssubsystem where u.uuidTaskH = :uuidTaskH",
							new Object[][] { { "uuidTaskH",Long.valueOf(tasks[i]) } });
			MsStatustask sts = this.getManagerDAO().selectOne(MsStatustask.class,trTaskH.getMsStatustask().getUuidStatusTask());
			if (!GlobalVal.COLLECTION_STATUS_TASK_UNASSIGN.equals(sts.getStatusCode())) {
				throw new ChangeException(this.messageSource.getMessage("businesslogic.unassigntaskcollection.statustask",
						null, this.retrieveLocaleAudit(callerId)), sts.getStatusCode());
			}
			trTaskH.setAmMsuser(amMsuser);
			trTaskH.setAssignDate(new Date());
			trTaskH.setMsPriority(msPriority);
			trTaskH.setDtmUpd(new Date());
			trTaskH.setUsrCrt(String.valueOf(loginBean.getUuidMsUser()));
			trTaskH.setMsStatusmobile(msm);
			this.getManagerDAO().update(trTaskH);

			long uuidProcess = this.getUuidProcess(trTaskH, trTaskH
					.getMsStatustask().getAmMssubsystem());
			String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess,
					trTaskH.getUuidTaskH());
			Object[][] params2 = {
					{ Restrictions.eq("statusCode", statusCode) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
							loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(
					MsStatustask.class, params2);
			trTaskH.setMsStatustask(msStatustask);

			TrTaskhistory trTaskhistory = new TrTaskhistory();
			trTaskhistory.setCodeProcess(GlobalVal.CODE_PROCESS_ASSIGNMENT);
			trTaskhistory.setActor(loginBean.getFullName());
			trTaskhistory.setFieldPerson(amMsuser.getFullName());
			trTaskhistory.setMsStatustask(msStatustask);
			trTaskhistory.setTrTaskH(trTaskH);
			trTaskhistory.setNotes(trTaskH.getNotes());
			trTaskhistory.setUsrCrt(String.valueOf(loginBean.getUuidMsUser()));
			trTaskhistory.setDtmCrt(new Date());

			this.getManagerDAO().update(trTaskH);
			this.getManagerDAO().insert(trTaskhistory);
		}

	}

	@Override
	public List getPriorityList(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"task.inquirytaskcollection.priorityList", params, null);
		return result;
	}
	
	@Override
	public List getSpvList(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"task.unassigntaskcollection.getSpvList", params, null);
		return result;
	}

}
