package com.adins.mss.businesslogic.impl.collection;

import java.io.UnsupportedEncodingException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bouncycastle.util.encoders.Base64;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.ViewDepositReportLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.FinalTrDepositreportH;
import com.adins.mss.model.TrDepositreportH;
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
@SuppressWarnings({ "rawtypes", "unchecked" })
public class GenericViewDepositReportLogic extends BaseLogic implements ViewDepositReportLogic{
	
	@Override
	public List getBatchList(AuditContext callerId, Object params) throws SQLException {
		List list = new ArrayList();

		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder(
				(Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
				.append("select * from ( ")
				.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
				.append("SELECT distinct *, ROW_NUMBER() OVER (ORDER BY c.UUID_DEPOSIT_REPORT_H desc) AS rownum FROM ( ")
				.append("SELECT distinct tdh.UUID_DEPOSIT_REPORT_H, tdh.BATCH_ID, br.BRANCH_CODE, usr.FULL_NAME, tdh.BANK_NAME, tdh.BANK_ACCOUNT, tdh.CASHIER_NAME, ")
				.append("(Select SUM(DEPOSIT_AMT) from TR_DEPOSITREPORT_D td with (nolock) where td.UUID_DEPOSIT_REPORT_H = tdh.UUID_DEPOSIT_REPORT_H) as Total, ")
				.append("(Case  when tdh.LOB_FILE is null ")
				.append("then '0' ")
				.append("else '1' end) hasImage, '1' as Flag ")
				.append("FROM TR_DEPOSITREPORT_H tdh with (nolock) ")
				.append("join TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H ")
				.append("join TR_TASK_H th with (nolock) on tdd.UUID_TASK_H = th.UUID_TASK_H ")
				.append("join AM_MSUSER usr with (nolock) on th.UUID_MS_USER = usr.UUID_MS_USER ")
				.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) br on th.UUID_BRANCH = br.UUID_BRANCH ")
				.append("WHERE 1=1 ")
				.append(paramsQueryString)
				.append("UNION ALL ")
				.append("SELECT distinct tdh.UUID_DEPOSIT_REPORT_H, tdh.BATCH_ID, br.BRANCH_CODE, usr.FULL_NAME, tdh.BANK_NAME, tdh.BANK_ACCOUNT, tdh.CASHIER_NAME,  ")
				.append("(Select SUM(DEPOSIT_AMT) from FINAL_TR_DEPOSITREPORT_D td with (nolock) where td.UUID_DEPOSIT_REPORT_H = tdh.UUID_DEPOSIT_REPORT_H) as Total, ")
				.append("(Case  when tdh.LOB_FILE is null ")
				.append("then '0' ")
				.append("else '1' end) hasImage, '2' as Flag ")
				.append("FROM FINAL_TR_DEPOSITREPORT_H tdh with (nolock) ")
				.append("join FINAL_TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H ")
				.append("join FINAL_TR_TASK_H th with (nolock) on tdd.UUID_TASK_H = th.UUID_TASK_H ")
				.append("join AM_MSUSER usr with (nolock) on th.UUID_MS_USER = usr.UUID_MS_USER ")
				.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) br on th.UUID_BRANCH = br.UUID_BRANCH ")
				.append("WHERE 1=1 ").append(paramsQueryString).append(") c ")
				.append(") a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start ");
		paramsStack.push(new Object[] { "start", ((Object[][]) params)[6][1] });
		paramsStack.push(new Object[] { "end", ((Object[][]) params)[7][1] });
		paramsStack.push(new Object[] { "uuidBranch",
				((Object[][]) params)[1][1] });
		Object[][] sqlParams = new Object[paramsStack.size()][2];
		for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		list = this.getManagerDAO().selectAllNativeString(
				queryBuilder.toString(), sqlParams);

		return list;
		
	}
	
	/*0	"collectorName",*/
	/*1	"uuidBranch", */
	/*2	"uuidUser", */
	/*3	"startDate", */
	/*4	"endDate", */
	/*5	"currentDate", */
	/*6	"start", */
	/*7	"end" */
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---LOGIN ID
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("AND usr.FULL_NAME like lower('%'+ :collectorName +'%') ");
			paramStack.push(new Object[]{"collectorName", (String) params[0][1]});
		}
		else if(StringUtils.equals("%", (String) params[0][1]) && !StringUtils.equals("%", (String) params[2][1])){
			sb.append("AND usr.UUID_MS_USER = :uuidUser ");
			paramStack.push(new Object[]{"uuidUser", Long.valueOf((String) params[2][1])});
		}

		sb.append("and COALESCE(tdh.TRANSFERRED_DATE, '1990-01-01 00:00:00') BETWEEN (CASE WHEN :startDate = '%' then '1990-01-01 00:00:00' ");
		sb.append("else :startDate END) ");
		sb.append("and (CASE WHEN :endDate = '%' then :currentDate else :endDate END) ");
		paramStack.push(new Object[]{"startDate", (String) params[3][1]});
		paramStack.push(new Object[]{"endDate", (String) params[4][1]});
		paramStack.push(new Object[]{"currentDate", (String) params[5][1]});
		return sb;
	}

	@Override
	public Integer getBatchListCount(AuditContext callerId, Object params) {
		Integer result = NumberUtils.INTEGER_ZERO;

		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder(
				(Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT Count(1) FROM ( ")
				.append("SELECT distinct * from ( ")
				.append("SELECT distinct tdh.UUID_DEPOSIT_REPORT_H, tdh.BATCH_ID, br.BRANCH_CODE, usr.FULL_NAME, tdh.BANK_NAME, tdh.BANK_ACCOUNT, tdh.CASHIER_NAME, ")
				.append("(Select SUM(DEPOSIT_AMT) from TR_DEPOSITREPORT_D td with (nolock) where td.UUID_DEPOSIT_REPORT_H = tdh.UUID_DEPOSIT_REPORT_H) as Total, ")
				.append("(Case  when tdh.LOB_FILE is null ")
				.append("then '0' ")
				.append("else '1' end) hasImage ")
				.append("FROM TR_DEPOSITREPORT_H tdh with (nolock) ")
				.append("join TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H ")
				.append("join TR_TASK_H th with (nolock) on tdd.UUID_TASK_H = th.UUID_TASK_H ")
				.append("join AM_MSUSER usr with (nolock) on th.UUID_MS_USER = usr.UUID_MS_USER ")
				.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) br on th.UUID_BRANCH = br.UUID_BRANCH ")
				.append("WHERE 1=1 ")
				.append(paramsQueryString)
				.append("UNION ALL ")
				.append("SELECT distinct tdh.UUID_DEPOSIT_REPORT_H, tdh.BATCH_ID, br.BRANCH_CODE, usr.FULL_NAME, tdh.BANK_NAME, tdh.BANK_ACCOUNT, tdh.CASHIER_NAME, ")
				.append("(Select SUM(DEPOSIT_AMT) from FINAL_TR_DEPOSITREPORT_D td with (nolock) where td.UUID_DEPOSIT_REPORT_H = tdh.UUID_DEPOSIT_REPORT_H) as Total, ")
				.append("(Case  when tdh.LOB_FILE is null ")
				.append("then '0' ")
				.append("else '1' end) hasImage ")
				.append("FROM FINAL_TR_DEPOSITREPORT_H tdh with (nolock) ")
				.append("join FINAL_TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H ")
				.append("join FINAL_TR_TASK_H th with (nolock) on tdd.UUID_TASK_H = th.UUID_TASK_H ")
				.append("join AM_MSUSER usr with (nolock) on th.UUID_MS_USER = usr.UUID_MS_USER ")
				.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) br on th.UUID_BRANCH = br.UUID_BRANCH ")
				.append("WHERE 1=1 ").append(paramsQueryString)
				.append(") outtable ").append(") c ");
		paramsStack.push(new Object[] { "uuidBranch",
				((Object[][]) params)[1][1] });
		Object[][] sqlParams = new Object[paramsStack.size()][2];
		for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);

		return result;

	}
	
	@Override
	public List getBatchDetail(AuditContext callerId, Object params) {
		List list = this.getManagerDAO().selectAllNative("task.viewdeposit.listdetailbatch", params, null);

		return list;
	}
	
	@Override
	public List getBatchDetailFinal(AuditContext callerId, Object params) {
		List list = this.getManagerDAO().selectAllNative("task.viewdeposit.listdetailbatchFinal", params, null);
		
		return list;
	}
	
	@Override
	public Integer getBatchDetailCount(AuditContext callerId, Object params) {
		Integer result= (Integer)this.getManagerDAO().selectOneNative("task.viewdeposit.listdetailbatchCount", params);	
		
		return result;
	}
	
	@Override
	public Integer getBatchDetailCountFinal(AuditContext callerId, Object params) {
		Integer result= (Integer)this.getManagerDAO().selectOneNative("task.viewdeposit.listdetailbatchCountFinal", params);	
		
		return result;
	}

	@Override
	public String getImageBase64(long uuidDepositH, AuditContext callerId) throws UnsupportedEncodingException {
		String img = StringUtils.EMPTY;
		TrDepositreportH trDepositH = this.getManagerDAO().selectOne(TrDepositreportH.class, uuidDepositH);
		if (trDepositH != null) {
			img = new String(Base64.encode(trDepositH.getLobFile()), "ASCII");
		} 
		else {
			FinalTrDepositreportH finalTrDepositH = this.getManagerDAO()
					.selectOne(FinalTrDepositreportH.class, uuidDepositH);
			img = new String(Base64.encode(finalTrDepositH.getLobFile()),
					"ASCII");
		}
		return img;
	}
	
	@Override
	public TrDepositreportH getDetailH(long uuidDepositH, AuditContext callerId) {
		TrDepositreportH tdh = this.getManagerDAO().selectOne(TrDepositreportH.class, uuidDepositH);
		
		return tdh;
	}
	
	@Override
	public FinalTrDepositreportH getDetailHFinal(long uuidDepositH, AuditContext callerId) {
		FinalTrDepositreportH tdh = this.getManagerDAO().selectOne(FinalTrDepositreportH.class, uuidDepositH);
	
		return tdh;
	}
	
	@Override
	public Map<String, String> getBranchListCombo(long branchId, AuditContext callerId) {
		Map<String, String> result = Collections.emptyMap();
			Object[][] params = { { "branchId", branchId } };
			List list = this.getManagerDAO().selectAllNative("task.viewdeposit.getBranchListCombo", params,null);
			Map<String, String> comboList = new HashMap<String, String>();
			if (!list.isEmpty()) {
				for (int i = 0; i < list.size(); i++) {
					Map temp2 = (Map) list.get(i);
					comboList.put(temp2.get("d0").toString(), (String) temp2.get("d1"));
				}
				result = this.sortByValues(comboList);
			} 
			else {
				result.put("%", "ALL");
			}
		return result;
		
	}
	
	@Override
	public Map<String, Object> getComboUser(String uuidBranch, AuditContext callerId) {
		
		List listUserCombo = new ArrayList();
		//Combo List User
		Map<String, Object> userCombo = new HashMap<String, Object>();
		if(!StringUtils.isBlank(uuidBranch)){
			AmMsuser amMsUser = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser",
					new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
			String subsystemName = amMsUser.getAmMssubsystem().getSubsystemName();
			
			if("%".equals(uuidBranch)){
				Object[][] paramUser = { {"uuidBranch", amMsUser.getMsBranch().getUuidBranch()}, {"subsystemName", subsystemName} };
				listUserCombo = this.getManagerDAO().selectAllNative(
						"task.viewdeposit.getalllistuser", paramUser, null);
			}
			else {
				String[][] paramUser = { {"uuidBranch", uuidBranch}, {"subsystemName", subsystemName} };
				Stack<Object[]> paramsStack = new Stack<>();		
				StringBuilder paramsQueryString = this.sqlPagingBuilderListUser((Object[][]) paramUser, paramsStack);
				StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT usr.UUID_MS_USER as [key], usr.LOGIN_ID + ' - ' + usr.FULL_NAME as [value] FROM AM_MSUSER usr with (nolock) ")
				.append("join MS_JOB jb with (nolock) on usr.UUID_JOB = jb.UUID_JOB ")
				.append("join AM_MSSUBSYSTEM subsys with (nolock) on usr.UUID_MS_SUBSYSTEM = subsys.UUID_MS_SUBSYSTEM ")
				.append("WHERE usr.IS_ACTIVE = 1 AND jb.IS_FIELD_PERSON = 1 ")
				.append(paramsQueryString)
				.append("ORDER BY usr.FULL_NAME ");
				
				Object[][] sqlParams = new Object[paramsStack.size()][2];
			    for (int i = 0; i < paramsStack.size(); i++) {
					Object[] objects = paramsStack.get(i);
					sqlParams[i] = objects;
				}
			    listUserCombo = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
			}
			String key[] = new String[listUserCombo.size()+1];
			String value[] = new String[listUserCombo.size()+1];
			if (!listUserCombo.isEmpty()) {
				key[0] = "%";
				value[0] = "ALL";
				for (int i = 1; i < listUserCombo.size()+1; i++) {
					Map temp = (Map) listUserCombo.get(i-1);
					key[i] = (String) temp.get("d0");
					value[i] = (String) temp.get("d1");
				}
				userCombo.put("key",key);
				userCombo.put("value",value);
			}
			else {
				key[0] = "";
				value[0] = "---";
				userCombo.put("key",key);
				userCombo.put("value",value);
			}
		}
		return userCombo;
	}
	
	Map sortByValues(Map<String, String> comboList) {
		List list = new LinkedList(comboList.entrySet());

		Collections.sort(list, new Comparator() {
			public int compare(Object o1, Object o2) {
				return ((Comparable) ((Map.Entry) (o1)).getValue())
						.compareTo(((Map.Entry) (o2)).getValue());
			}
		});

		Map sortedHashMap = new LinkedHashMap();
		sortedHashMap.put("%", "ALL");
		for (Iterator it = list.iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			sortedHashMap.put(entry.getKey(), entry.getValue());
		}
		return sortedHashMap;
	}
	
	@Override
	public List getBatchListByHierarkiUser(AuditContext callerId, Object params) throws SQLException {
		List list = this.getManagerDAO().selectAllNative("task.viewdeposit.listbatchByHierarkiUser", params, null);

		return list;
	}

	@Override
	public Integer getBatchListCountByHierarkiUser(AuditContext callerId, Object params) {
		Integer result= (Integer)this.getManagerDAO().selectOneNative("task.viewdeposit.listbatchCountByHierarkiUser", params);

		return result;
	}
	
	@Override
	public Map<String, Object> getComboUserHierarki(String uuidBranch, AuditContext callerId) {
		
		List listUserCombo = new ArrayList();
		//Combo List User
		Map<String, Object> userCombo = new HashMap<String, Object>();
		if(!StringUtils.isBlank(uuidBranch)){
			AmMsuser amMsUser = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
					new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
			String subsystemName = amMsUser.getAmMssubsystem().getSubsystemName();

			Object[][] paramUser = { {"uuidBranch", amMsUser.getMsBranch().getUuidBranch()}, 
					{"subsystemName", subsystemName}, {"uuidUser", amMsUser.getUuidMsUser()} };
			listUserCombo = this.getManagerDAO().selectAllNative(
					"task.viewdeposit.getalllistuserhierarki", paramUser, null);
			
			String key[] = new String[listUserCombo.size()+1];
			String value[] = new String[listUserCombo.size()+1];
			if (!listUserCombo.isEmpty()) {
				key[0] = "%";
				value[0] = "ALL";
				for (int i = 1; i < listUserCombo.size()+1; i++) {
					Map temp = (Map) listUserCombo.get(i-1);
					key[i] = (String) temp.get("d0");
					value[i] = (String) temp.get("d1");
				}
				userCombo.put("key",key);
				userCombo.put("value",value);
			}
			else {
				key[0] = "";
				value[0] = "---";
				userCombo.put("key",key);
				userCombo.put("value",value);
			}
		}
		return userCombo;
	}
	
	@Override
	public Map<String, String> getComboUserLoad(String uuidBranch, AuditContext callerId) {
		
		List listUserCombo = new ArrayList();
		//Combo List User
		Map<String, String> userCombo = new HashMap<String, String>();
		if(!StringUtils.isBlank(uuidBranch)){
			AmMsuser amMsUser = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
					new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
			String subsystemName = amMsUser.getAmMssubsystem().getSubsystemName();
			
			if("%".equals(uuidBranch)){
				Object[][] paramUser = { {"uuidBranch", amMsUser.getMsBranch().getUuidBranch()}, {"subsystemName", subsystemName} };
				listUserCombo = this.getManagerDAO().selectAllNative(
						"task.viewdeposit.getalllistuser", paramUser, null);
			}
			else {
				String[][] paramUser = { {"uuidBranch", uuidBranch}, {"subsystemName", subsystemName} };		
				Stack<Object[]> paramsStack = new Stack<>();		
				StringBuilder paramsQueryString = this.sqlPagingBuilderListUser((Object[][]) paramUser, paramsStack);
				StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT usr.UUID_MS_USER as [key], usr.LOGIN_ID + ' - ' + usr.FULL_NAME as [value] FROM AM_MSUSER usr with (nolock) ")
				.append("join MS_JOB jb with (nolock) on usr.UUID_JOB = jb.UUID_JOB ")
				.append("join AM_MSSUBSYSTEM subsys with (nolock) on usr.UUID_MS_SUBSYSTEM = subsys.UUID_MS_SUBSYSTEM ")
				.append("WHERE usr.IS_ACTIVE = 1 AND jb.IS_FIELD_PERSON = 1 ")
				.append(paramsQueryString)
				.append("ORDER BY usr.FULL_NAME ");
				
				Object[][] sqlParams = new Object[paramsStack.size()][2];
			    for (int i = 0; i < paramsStack.size(); i++) {
					Object[] objects = paramsStack.get(i);
					sqlParams[i] = objects;
				}
			    listUserCombo = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
			}
			if (!listUserCombo.isEmpty()) {
				for (int i = 1; i < listUserCombo.size()+1; i++) {
					Map temp = (Map) listUserCombo.get(i-1);
					userCombo.put(temp.get("d0").toString(),(String) temp.get("d1"));
				}
				userCombo = this.sortByValues(userCombo);
			}
			else {
				userCombo.put("","---");
			}
		}
		return userCombo;
	}
	
	/*
	 * 0 uuidBranch
	 * 1 subsystemName
	 */
	private StringBuilder sqlPagingBuilderListUser(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---uuidBranch
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("AND usr.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{"uuidBranch", Long.valueOf((String) params[0][1])});
		}
		
		sb.append("AND subsys.SUBSYSTEM_NAME = :subsystemName ");
		paramStack.push(new Object[]{"subsystemName", (String) params[1][1]});
		return sb;
	}
	
	@Override
	public Map<String, String> getComboUserHierarkiLoad(String uuidBranch, AuditContext callerId) {
		
		List listUserCombo = new ArrayList();
		//Combo List User
		Map<String, String> userCombo = new HashMap<String, String>();
		if(!StringUtils.isBlank(uuidBranch)){
			AmMsuser amMsUser = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
					new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
			String subsystemName = amMsUser.getAmMssubsystem().getSubsystemName();

			String[][] paramUser = { {"uuidBranch", String.valueOf(amMsUser.getMsBranch().getUuidBranch())}, 
					{"subsystemName", subsystemName}, {"uuidUser", String.valueOf(amMsUser.getUuidMsUser())} };
			listUserCombo = this.getManagerDAO().selectAllNative(
					"task.viewdeposit.getalllistuserhierarki", paramUser, null);
			
			if (!listUserCombo.isEmpty()) {
				for (int i = 1; i < listUserCombo.size()+1; i++) {
					Map temp = (Map) listUserCombo.get(i-1);
					userCombo.put(temp.get("d0").toString(),(String) temp.get("d1"));
				}
				userCombo = this.sortByValues(userCombo);
			}
			else {
				userCombo.put("","---");
			}
		}
		return userCombo;
	}
	
	@Override
	public Map<String, Object> getCollByBranch(long uuidBranch, int pageNo, int pageSize, AuditContext auditContext) {
		Map<String, Object> mapResult = new HashMap<String, Object>();
		Object[][] paramsJob = { {Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MC+"_JOBSVY")} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsJob);
		Object[][] params = { {"start", ( pageNo-1 ) * pageSize + 1 }, 
				{ "end", ( pageNo-1 ) * pageSize + pageSize }, {"jobCode", amGeneralsetting.getGsValue()}, {"uuidBranch", uuidBranch} };
		List result = this.getManagerDAO().selectAllNative("survey.dm.getSpvListByBranch", params, null);
		
		Object[][] paramsCount = { {"jobCode", amGeneralsetting.getGsValue()}, {"uuidBranch", uuidBranch} };
		Integer total = (Integer)this.getManagerDAO().selectOneNative("survey.dm.getSpvListByBranchCount", paramsCount);	
		
		mapResult.put(GlobalKey.MAP_RESULT_LIST, result);
		mapResult.put(GlobalKey.MAP_RESULT_SIZE, total);
		return mapResult;
	}
}


