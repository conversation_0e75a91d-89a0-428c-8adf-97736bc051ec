package com.adins.mss.businesslogic.impl.collection;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.lang.FormatterUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.ViewHistoryLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.exceptions.ServicesAgreementException;
import com.adins.mss.exceptions.ServicesAgreementException.Reason;
import com.adins.mss.model.TrViewcollactivity;
import com.adins.mss.model.TrViewinstallmentcard;
import com.adins.mss.model.TrViewpaymenthistoryD;
import com.adins.mss.model.TrViewpaymenthistoryH;
import com.adins.mss.model.custom.TrViewpaymenthistoryWrapper;
import com.adins.mss.services.model.collection.CollectionHistoryBean;
import com.adins.mss.services.model.collection.InstallmentScheduleBean;
import com.adins.mss.services.model.collection.PaymentHistoryDBean;
import com.adins.mss.services.model.collection.PaymentHistoryHBean;
import com.adins.mss.services.model.collection.PaymentHistoryHList;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericViewHistoryLogic extends BaseLogic implements ViewHistoryLogic, MessageSourceAware {

    private MessageSource messageSource;
    
	public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }


	@Override
	public List<CollectionHistoryBean> collectionHistory(AuditContext auditContext, String taskID) {
		List<CollectionHistoryBean> collActivityList = new ArrayList<CollectionHistoryBean>();
		String paramI[][] = { {"taskID", taskID} };
		Object[] queryResult = (Object[]) this.getManagerDAO().selectOneNativeString(
		        "select AGREEMENT_NO, UUID_TASK_H from tr_task_h where task_id = :taskID", paramI);
		String agreementNo = (String) queryResult[0];
		String uuidTaskId = (String) queryResult[1];
		
		Object paramA[][] = { {Restrictions.eq("agreementNo", agreementNo)} };
		Map<String, Object> collHistList = this.getManagerDAO().list(TrViewcollactivity.class, paramA, null);			
		List<TrViewcollactivity> trViewCollList = (List<TrViewcollactivity>) collHistList.get(GlobalKey.MAP_RESULT_LIST);
		
		for (TrViewcollactivity trViewColl : trViewCollList) {
			CollectionHistoryBean bean = new CollectionHistoryBean();
			bean.setUuidCollectionactivity(this.getManagerDAO().getUUID());
			bean.setUuidTaskId(uuidTaskId);
			bean.setDtmCrt(trViewColl.getDtmCrt());
			bean.setUsrCrt(trViewColl.getUsrCrt());
			bean.setDtmUpd(trViewColl.getDtmUpd());
			bean.setUsrUpd(trViewColl.getUsrUpd());
			bean.setAgreementNo(trViewColl.getAgreementNo());
			bean.setBranchCode(trViewColl.getBranchCode());
			bean.setActivityDate(trViewColl.getActivityDate());
			bean.setCollectorName(trViewColl.getCollectorName());
			bean.setActivity(trViewColl.getActivity());
			bean.setResult(trViewColl.getResult());
			bean.setPtpDate(trViewColl.getPtpDate());
			bean.setNotes(trViewColl.getNotes());
			bean.setOverDueDays(trViewColl.getOverDueDays()==null ?
			        null : String.valueOf(trViewColl.getOverDueDays()));
			collActivityList.add(bean);
		}
		return collActivityList;
	}

	@Override
	public List<PaymentHistoryHList> paymentHistoryH(AuditContext auditContext, String taskID) throws ParseException {
		List<PaymentHistoryHList> listResult = new ArrayList<PaymentHistoryHList>();

		String paramI[][] = { {"taskID", taskID} };
		Object[] queryResult = (Object[]) this.getManagerDAO().selectOneNativeString(
		        "select AGREEMENT_NO, UUID_TASK_H from tr_task_h where task_id = :taskID", paramI);
		String agreementNo = (String) queryResult[0];
		String uuidTaskId = (String) queryResult[1];
		
		Object paramH[][] = { {Restrictions.eq("agreementNo", agreementNo)} };
		Map<String, Object> paymentHistList = this.getManagerDAO().list(TrViewpaymenthistoryH.class, paramH, null);			
		List<TrViewpaymenthistoryH> trViewPaymentHistoryHList = (List<TrViewpaymenthistoryH>) paymentHistList.get(GlobalKey.MAP_RESULT_LIST);
		
		
		for (TrViewpaymenthistoryH trViewPaymentHistoryH : trViewPaymentHistoryHList) {
			PaymentHistoryHList paymentHList = new PaymentHistoryHList();
			
			List<PaymentHistoryDBean> paymentHistoryDList = Collections.<PaymentHistoryDBean>emptyList();
			PaymentHistoryHBean bean = new PaymentHistoryHBean();
			bean.setUuidPaymentHistoryH(this.getManagerDAO().getUUID());
			bean.setUuidTaskId(uuidTaskId);
			bean.setUsrCrt(trViewPaymentHistoryH.getUsrCrt());
			bean.setDtmCrt(trViewPaymentHistoryH.getDtmCrt());
			bean.setUsrUpd(trViewPaymentHistoryH.getUsrUpd());
			bean.setDtmUpd(trViewPaymentHistoryH.getDtmUpd());
			bean.setAgreementNo(trViewPaymentHistoryH.getAgreementNo());
			bean.setBranchCode(trViewPaymentHistoryH.getBranchCode());
			bean.setReceiptNo(trViewPaymentHistoryH.getReceiptNo());
			bean.setValueDate(trViewPaymentHistoryH.getValueDate());
			bean.setPostingDate(trViewPaymentHistoryH.getPostingDate());
			bean.setPaymentAmount(toNumberFormatted(trViewPaymentHistoryH.getPaymentAmount()));
			bean.setInstallmentAmount(toNumberFormatted(trViewPaymentHistoryH.getInstallmentAmount()));
			bean.setInstallmentNumber(trViewPaymentHistoryH.getInstallmentNumber()==null ?
			        null : trViewPaymentHistoryH.getInstallmentNumber().toString());
			bean.setTransactionType(trViewPaymentHistoryH.getTransactionType());
			bean.setWopCode(trViewPaymentHistoryH.getWopCode());
			
			paymentHistoryDList = this.paymentHistoryD(auditContext, taskID, bean.getUuidPaymentHistoryH());
			
			paymentHList.setPaymentHistoryH(bean);
			paymentHList.setPaymentHistoryDList(paymentHistoryDList);
			
			listResult.add(paymentHList);
		}
		return listResult;
	}

	private List<PaymentHistoryDBean> paymentHistoryD(AuditContext auditContext,
			String taskID, String uuidHeader) throws ParseException {
		List<PaymentHistoryDBean> listResult = new ArrayList<PaymentHistoryDBean>();
		String paramI[][] = { {"taskID", taskID} };
		Object[] queryResult = (Object[]) this.getManagerDAO().selectOneNativeString(
		        "select AGREEMENT_NO, UUID_TASK_H from tr_task_h where task_id = :taskID", paramI);
        String uuidTaskId = (String) queryResult[1];
		
		String paramH[][] = { {"uuidHeader", uuidHeader} };
		List<Map<String, Object>> paymentDList = this.getManagerDAO().selectAllNativeString("select UUID_VIEW_PAYMENT_HISTORY_D, "+
				"NULL, DTM_CRT, USR_CRT, coalesce(DTM_UPD,'') DTM_UPD, coalesce(USR_UPD,'') USR_UPD,  "+
				"coalesce(PAYMENT_ALLOCATION_NAME,'') PAYMENT_ALLOCATION_NAME, coalesce(OS_AMOUNT_OD,0) OS_AMOUNT_OD, "+
				"coalesce(RECEIVE_AMOUNT,0) RECEIVE_AMOUNT from TR_VIEWPAYMENTHISTORY_D with (nolock) " +
				"where UUID_VIEW_PAYMENT_HISTORY_H = :uuidHeader", paramH);
		
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		for ( int i = 0; i < paymentDList.size(); i++) {
			Map temp = (Map) paymentDList.get(i);
			
			PaymentHistoryDBean bean = new PaymentHistoryDBean();
			bean.setUuidViewPaymentHistoryD(temp.get("d0").toString());
			bean.setUuidTaskId(uuidTaskId);
			bean.setDtmCrt(df.parse(temp.get("d2").toString()));
			bean.setUsrCrt(temp.get("d3").toString());
			bean.setDtmUpd(df.parse(temp.get("d4").toString()));
			bean.setUsrUpd(temp.get("d5").toString());
			bean.setPaymentAllocationName(temp.get("d6").toString());
			bean.setOsAmountOd(toNumberFormatted(temp.get("d7")));
			bean.setReceiveAmount(toNumberFormatted(temp.get("d8")));
			
			listResult.add(bean);
		}	
		return listResult;
	}

	@Override
	public List<InstallmentScheduleBean> installmentSchedule(AuditContext auditContext, String taskID) {
		List<InstallmentScheduleBean> listResult = new ArrayList<InstallmentScheduleBean>();

		String paramI[][] = { {"taskID", taskID} };
		Object[] queryResult = (Object[]) this.getManagerDAO().selectOneNativeString(
		        "select AGREEMENT_NO, UUID_TASK_H from tr_task_h where task_id = :taskID", paramI);
		String agreementNo = (String) queryResult[0];
		String uuidTaskId = (String) queryResult[1];
		
		Object paramA[][] = { {Restrictions.eq("agreementNo", agreementNo)} };
		Map<String, Object> trViewInsList = this.getManagerDAO().list(TrViewinstallmentcard.class, paramA, null);
		List<TrViewinstallmentcard> trViewInstallmentCardList = (List<TrViewinstallmentcard>) trViewInsList.get(GlobalKey.MAP_RESULT_LIST);
		
		for (TrViewinstallmentcard trViewInstallmentCard : trViewInstallmentCardList){
			InstallmentScheduleBean bean = new InstallmentScheduleBean();
			bean.setUuidInstallmentSchedule(this.getManagerDAO().getUUID());
			bean.setUuidTaskId(uuidTaskId);
			bean.setUsrCrt(trViewInstallmentCard.getUsrCrt());
			bean.setDtmCrt(trViewInstallmentCard.getDtmCrt());
			bean.setAgreementNo(trViewInstallmentCard.getAgreementNo());
			bean.setBranchCode(trViewInstallmentCard.getBranchCode());
			bean.setInstallmentNo(String.valueOf(trViewInstallmentCard.getInstallmentNo()));
			bean.setInstallmentAmount(toNumberFormatted(trViewInstallmentCard.getInstallmentAmount()));
			bean.setInstlPaidAmount(toNumberFormatted(trViewInstallmentCard.getInstlPaidAmount()));
			bean.setDueDate(trViewInstallmentCard.getDueDate());
			bean.setInstallmentAmount(toNumberFormatted(trViewInstallmentCard.getInstallmentAmount()));
			bean.setLcInstlAmount(toNumberFormatted(trViewInstallmentCard.getLcInstlAmount()));
			bean.setLcInstlPaid(toNumberFormatted(trViewInstallmentCard.getLcInstlPaid()));
			bean.setLcInstlWaived(toNumberFormatted(trViewInstallmentCard.getLcInstlWaived()));
			bean.setPrincipalAmount(toNumberFormatted(trViewInstallmentCard.getPrincipalAmount()));
			bean.setInterestAmount(toNumberFormatted(trViewInstallmentCard.getInterestAmount()));
			bean.setOsPrincipalAmount(toNumberFormatted(trViewInstallmentCard.getOsPrincipalAmount()));
			bean.setOsInterestAmount(toNumberFormatted(trViewInstallmentCard.getOsInterestAmount()));
			bean.setLcDays(trViewInstallmentCard.getLcDays()==null ? null : trViewInstallmentCard.getLcDays().toString());
			bean.setLcAdminFee(toNumberFormatted(trViewInstallmentCard.getLcAdminFee()));
			bean.setLcAdminFeePaid(toNumberFormatted(trViewInstallmentCard.getLcAdminFeePaid()));
			bean.setLcAdminFeeWaive(toNumberFormatted(trViewInstallmentCard.getLcAdminFeeWaive()));
			
			listResult.add(bean);
		}
		return listResult;
	}

	public String toDate(Date obj){
		String s = null;
		if (obj != null){
			s = new SimpleDateFormat("ddMMyyyyHHmmss").format(obj);
		}
		return s;
	}


	@Override
	public String getAgreementNo(AuditContext auditContext, String taskID) {	
		String paramI[][] = { {"taskID",taskID} };
		String agreementNo = (String) this.getManagerDAO().selectOneNativeString("select AGREEMENT_NO from tr_task_h where task_id = :taskID", paramI);
		return agreementNo;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Map addInstallmetSchedule(AuditContext auditContext, InstallmentScheduleBean bean) throws ParseException {
		Map map = new HashMap();
		String message = ""; 
		if (bean.getAgreementNo() == null || bean.getAgreementNo().isEmpty()){
			message = this.messageSource.getMessage("service.global.emptyparam",  new Object[]{"Agreement No"}, this.retrieveLocaleAudit(auditContext));
		}
		if (bean.getInstallmentNo() == null || bean.getInstallmentNo().isEmpty()){
			message = message + this.messageSource.getMessage("service.global.emptyparam",  new Object[]{"Installment No"}, this.retrieveLocaleAudit(auditContext));
		}
		
		if (StringUtils.isNotBlank(message)) {
			map.put("message", message);
			return map;
		}
		
		TrViewinstallmentcard installment = new TrViewinstallmentcard();
		installment.setAgreementNo(bean.getAgreementNo());
		installment.setDtmCrt(new Date());
        installment.setUsrCrt(auditContext.getCallerId());
        installment.setInstallmentNo(Short.parseShort(bean.getInstallmentNo()));
		
		installment.setBranchCode(bean.getBranchCode());
		installment.setDueDate( bean.getDueDate()==null?null:bean.getDueDate() );
		installment.setInstallmentAmount(bean.getInstallmentAmount()==null?null:new BigDecimal(bean.getInstallmentAmount()));
		installment.setInstlPaidAmount(bean.getInstlPaidAmount()==null?null:new BigDecimal(bean.getInstlPaidAmount()));
		installment.setInstlPaidDate( bean.getInstlPaidDate() );
		installment.setInterestAmount(bean.getInterestAmount()==null?null:new BigDecimal(bean.getInterestAmount()));
		installment.setLcAdminFee(bean.getLcAdminFee()==null?null:new BigDecimal(bean.getLcAdminFee()));
		installment.setLcAdminFeePaid(bean.getLcAdminFeePaid()==null?null:new BigDecimal(bean.getLcAdminFeePaid()));
		installment.setLcAdminFeeWaive(bean.getLcAdminFeeWaive()==null?null:new BigDecimal(bean.getLcAdminFeeWaive()));
		installment.setLcDays(bean.getLcDays()==null?null:Integer.parseInt(bean.getLcDays()));
		installment.setLcInstlAmount(bean.getLcInstlAmount()==null?null:new BigDecimal(bean.getLcInstlAmount()));
		installment.setLcInstlPaid(bean.getLcInstlPaid()==null?null:new BigDecimal(bean.getLcInstlPaid()));
		installment.setLcInstlWaived(bean.getLcInstlWaived()==null?null:new BigDecimal(bean.getLcInstlWaived()));
		installment.setOsInterestAmount(bean.getOsInterestAmount()==null?null:new BigDecimal(bean.getOsInterestAmount()));
		installment.setOsPrincipalAmount(bean.getOsPrincipalAmount()==null?null:new BigDecimal(bean.getOsPrincipalAmount()));
		installment.setPrincipalAmount(bean.getPrincipalAmount()==null?null:new BigDecimal(bean.getPrincipalAmount()));
		
		this.getManagerDAO().insert(installment);
			
		map.put("message", this.messageSource.getMessage("msg.success.insert", null, this.retrieveLocaleAudit(auditContext)));
		return map;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Map addCollectionHistory(AuditContext auditContext, CollectionHistoryBean bean) throws ParseException {
		Map map = new HashMap();		
		String message = ""; 
		if (bean.getAgreementNo() == null || bean.getAgreementNo().isEmpty()){
			message = this.messageSource.getMessage("service.global.emptyparam",  new Object[]{"Agreement No"}, this.retrieveLocaleAudit(auditContext));
		}
		
		if (StringUtils.isNotBlank(message)) {
			map.put("message", message);
			return map;
		}
		
		TrViewcollactivity collActivity = new TrViewcollactivity();
		collActivity.setAgreementNo(bean.getAgreementNo());
		collActivity.setUsrCrt(auditContext.getCallerId());
		collActivity.setActivity(bean.getActivity());
		collActivity.setActivityDate(bean.getActivityDate()==null?null:bean.getActivityDate());
		collActivity.setBranchCode(bean.getBranchCode());
		collActivity.setCollectorName(bean.getCollectorName());
		collActivity.setDtmCrt(new Date());
		collActivity.setNotes(bean.getNotes());
		collActivity.setOverDueDays(bean.getOverDueDays()==null?null:Integer.parseInt(bean.getOverDueDays()));
		collActivity.setPtpDate(bean.getPtpDate()==null?null:bean.getPtpDate());
		collActivity.setResult(bean.getResult());
		
		this.getManagerDAO().insert(collActivity);
		
		map.put("message", this.messageSource.getMessage("msg.success.insert", null, this.retrieveLocaleAudit(auditContext)));
		return map;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Map addPaymentHistoryH(AuditContext auditContext, PaymentHistoryHBean hBean, PaymentHistoryDBean dBean) throws ParseException {
		Map map = new HashMap();
			
		String message = ""; 
		if (hBean.getAgreementNo() == null || hBean.getAgreementNo().isEmpty()){
			message = this.messageSource.getMessage("service.global.emptyparam",  new Object[]{"Agreement No"}, this.retrieveLocaleAudit(auditContext));
		}
		
		if (StringUtils.isNotBlank(message)) {
			map.put("message", message);
			return map;
		}
		
		TrViewpaymenthistoryH paymentH = new TrViewpaymenthistoryH();
		paymentH.setAgreementNo(hBean.getAgreementNo());
		paymentH.setDtmCrt(new Date());
		paymentH.setUsrCrt(auditContext.getCallerId());
		paymentH.setBranchCode(hBean.getBranchCode());
		paymentH.setInstallmentAmount(hBean.getInstallmentAmount()==null?null:new BigDecimal(hBean.getInstallmentAmount()));
		paymentH.setInstallmentNumber(hBean.getInstallmentNumber()==null?null:Integer.parseInt(hBean.getInstallmentNumber()));
		paymentH.setPaymentAmount(hBean.getPaymentAmount()==null?null:new BigDecimal(hBean.getPaymentAmount()));
		paymentH.setPostingDate(hBean.getPostingDate());
		paymentH.setReceiptNo(hBean.getReceiptNo());
		paymentH.setTransactionType(hBean.getTransactionType());
		paymentH.setValueDate(hBean.getValueDate());
		paymentH.setWopCode(hBean.getWopCode());
		
		this.getManagerDAO().insert(paymentH);
		
		this.addPaymentHistoryD(paymentH, dBean, auditContext);

		map.put("message", this.messageSource.getMessage("msg.success.insert", null, this.retrieveLocaleAudit(auditContext)));		
		
		return map;
	}

	private void addPaymentHistoryD(TrViewpaymenthistoryH paymentH, PaymentHistoryDBean bean, AuditContext auditContext) throws ParseException {		
		TrViewpaymenthistoryD paymentD = new TrViewpaymenthistoryD();
        paymentD.setTrViewpaymenthistoryH(paymentH);
		paymentD.setDtmCrt(new Date());
		paymentD.setOsAmountOd(bean.getOsAmountOd()==null?null:new BigDecimal(bean.getOsAmountOd()));
		paymentD.setPaymentAllocationName(bean.getPaymentAllocationName());
		paymentD.setReceiveAmount(bean.getReceiveAmount()==null?null:new BigDecimal(bean.getReceiveAmount()));
        paymentD.setUsrCrt(auditContext.getCallerId());        
		
		this.getManagerDAO().insert(paymentD);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
    @Override
    public void insertInstallmentCard(TrViewinstallmentcard[] instCards, AuditContext auditContext) {
	    if (ArrayUtils.isEmpty(instCards)){
	        return;
	    }
        
        Set<String> uniqueAgreementNos = new HashSet<>();
        
        for (TrViewinstallmentcard instCard : instCards) {
            String agreementNo = instCard.getAgreementNo();
            
            if (StringUtils.isBlank(agreementNo))
                throw new ServicesAgreementException(
                        this.messageSource.getMessage("businesslogic.viewhistory.emptyagreement", null, this.retrieveLocaleAudit(auditContext)),
                        Reason.EMPTY_AGREEMENT_NO);
            
            if (!uniqueAgreementNos.contains(agreementNo)) {
                this.deleteInstallmentCard(agreementNo);                    
            }
            uniqueAgreementNos.add(agreementNo);
            
            instCard.setDtmCrt(new Date());
            instCard.setUsrCrt(auditContext.getCallerId());
            this.getManagerDAO().insert(instCard);
        }
    }
	
	private void deleteInstallmentCard(String agreementNo) {
	    String[][] params = { {"agreementNo", agreementNo} };
	    this.getManagerDAO().deleteNativeString(
	            "DELETE FROM TR_VIEWINSTALLMENTCARD WHERE AGREEMENT_NO = :agreementNo", params);	    
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
    @Override
    public void insertCollectionActivity(TrViewcollactivity[] collActivities, AuditContext auditContext) {
        if (ArrayUtils.isEmpty(collActivities)){
        	return;
        }
            
        Set<String> uniqueAgreementNos = new HashSet<>();
        
        for (TrViewcollactivity activity : collActivities) {
            String agreementNo = activity.getAgreementNo();
            
            if (StringUtils.isBlank(agreementNo)){
                throw new ServicesAgreementException(
                        this.messageSource.getMessage("businesslogic.viewhistory.emptyagreement", null, this.retrieveLocaleAudit(auditContext)),
                        Reason.EMPTY_AGREEMENT_NO);
            }
            
            if (!uniqueAgreementNos.contains(agreementNo)) {
                this.deleteCollectionActivity(agreementNo);                    
            }
            uniqueAgreementNos.add(agreementNo);
            
            activity.setDtmCrt(new Date());
            activity.setUsrCrt(auditContext.getCallerId());
            this.getManagerDAO().insert(activity);
        }
    }
    
    private void deleteCollectionActivity(String agreementNo) {
        String[][] params = { {"agreementNo", agreementNo} };
        this.getManagerDAO().deleteNativeString(
                "DELETE FROM TR_VIEWCOLLACTIVITY WHERE AGREEMENT_NO = :agreementNo", params);        
    }

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
    @Override
    public void insertPaymentHistory(TrViewpaymenthistoryWrapper[] paymentHistories,
            AuditContext auditContext) {
        if (ArrayUtils.isEmpty(paymentHistories)){
            return;
        }
            
        Set<String> uniqueAgreementNos = new HashSet<>();
        
        for (TrViewpaymenthistoryWrapper paymentHistory : paymentHistories) {
            TrViewpaymenthistoryH header = paymentHistory.getHeader();
            TrViewpaymenthistoryD[] details = paymentHistory.getDetail();
            String agreementNo = header.getAgreementNo();
            
            if (StringUtils.isBlank(agreementNo)){
                throw new ServicesAgreementException(
                        this.messageSource.getMessage("businesslogic.viewhistory.emptyagreement", null, this.retrieveLocaleAudit(auditContext)),
                        Reason.EMPTY_AGREEMENT_NO);
            }
            if (!uniqueAgreementNos.contains(agreementNo)) {
                this.deletePaymentHistory(agreementNo);                    
            }
            uniqueAgreementNos.add(agreementNo);
            
            header.setDtmCrt(new Date());
            header.setUsrCrt(auditContext.getCallerId());
            this.getManagerDAO().insert(header);
            
            if (ArrayUtils.isEmpty(details)){
                continue;
            }
            
            for (TrViewpaymenthistoryD detail : details) {
                detail.setDtmCrt(new Date());
                detail.setUsrCrt(auditContext.getCallerId());
                this.getManagerDAO().insert(detail);
            }
        }
    }
    
    private void deletePaymentHistory(String agreementNo) {
        String[][] params = { {"agreementNo", agreementNo} };
        this.getManagerDAO().deleteNativeString(
                "DELETE FROM TR_VIEWPAYMENTHISTORY_D WHERE UUID_VIEW_PAYMENT_HISTORY_H IN " +
                "(SELECT UUID_VIEW_PAYMENT_HISTORY_H FROM TR_VIEWPAYMENTHISTORY_H "
                + "WHERE AGREEMENT_NO = :agreementNo)", params);
        this.getManagerDAO().deleteNativeString(
                "DELETE FROM TR_VIEWPAYMENTHISTORY_H WHERE AGREEMENT_NO = :agreementNo", params);        
    }
    
    private String toNumberFormatted(Object obj) {
        if (obj == null){
        	return null;
        }
        
        if (!NumberUtils.isNumber(String.valueOf(obj))){
        	return null;
        }
        
        return FormatterUtils.getNumberFormat(FormatterUtils.NFORMAT_US_2).format(obj);
    }
}
