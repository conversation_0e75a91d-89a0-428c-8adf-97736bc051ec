<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

    <bean id="GenericDMLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericDashboardMonitoringLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericDashboardAnalyticsCollectionLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericDashboardAnalyticsCollectionLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericInquiryTaskCollectionLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericInquiryTaskCollectionLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericDepositReportLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericDepositReportLogic" scope="singleton" parent="BaseLogicBean">
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="userLogic" ref="GenericUserLogicBean" />
	</bean>
	<bean id="GenericReportSummaryLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericReportSummaryLogic" scope="singleton" parent="BaseLogicBean" />
	<bean id="GenericViewHistoryLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericViewHistoryLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericTaskMonitoringLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericTaskMonitoringLogic" scope="singleton" parent="BaseLogicBean">
		<property name="globalLogic" ref="GenericGlobalLogicBean" />
	</bean>
	<bean id="GenericTaskCollectionLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericTaskCollectionLogic" scope="singleton" parent="BaseLogicBean">
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericDailyCollectionSummaryLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericDailyCollectionSummaryLogic" scope="singleton" parent="BaseLogicBean">
	</bean>
	<bean id="GenericReportLkpLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericReportLkpLogic" scope="singleton" parent="BaseLogicBean">
		<property name="globalLogic" ref="GenericGlobalLogicBean" />
	</bean>
	
	<bean id="GenericReportSlaCollectionLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericReportSlaCollectionLogic" scope="singleton" parent="BaseLogicBean" />
	<bean id="GenericViewDepositReportLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericViewDepositReportLogic" scope="singleton" parent="BaseLogicBean" />
	<bean id="GenericClosingTaskLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericClosingTaskLogic" scope="singleton" parent="BaseLogicBean" >
		<property name="rvNumberLogic" ref="GenericRvNumberLogicBean" />
	</bean>
	<bean id="GenericUnassignTaskCollectionLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericUnassignTaskCollectionLogic" scope="singleton" parent="BaseLogicBean">
		<property name="globalLogic" ref="GenericGlobalLogicBean"/>
		<property name="geolocationLogic" ref="GenericGeolocationLogicBean"/>
	</bean>
	<bean id="GenericRvNumberLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericRvNumberLogic" scope="singleton" parent="BaseLogicBean">
	</bean>
	<bean id="GenericCollectionResultLogicBean" class="com.adins.mss.businesslogic.impl.collection.GenericCollectionResultLogic" scope="singleton" parent="BaseLogicBean">
	</bean>
</beans>