<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="collection.closingtask.cekdeposit">
		<query-param name="uuidUser" type="long" />
		select COUNT(1) 
		from tr_task_h trth with (nolock)
			join tr_taskcolldata ttcd with (nolock) 
			on ttcd.UUID_TASK_ID = trth.uuid_task_h
		where trth.UUID_MS_USER = :uuidUser
			and ttcd.payment_received IS NOT NULL
			and trth.SUBMIT_DATE IS NOT NULL
			and trth.UUID_TASK_H not in (
				select TR_DEPOSITREPORT_D.UUID_TASK_H from TR_DEPOSITREPORT_D with (nolock)
			)
	</sql-query>
	<sql-query name="collection.closingtask.list">
		<query-param name="uuidUser" type="long" />
		<query-param name="statusPending" type="string" />
		select AGREEMENT_NO as kontrak, CUSTOMER_NAME as name
		from TR_TASK_H trth with (nolock) 
			join MS_STATUSTASK ms with (nolock) on ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK
		where UUID_MS_USER = :uuidUser
			and ms.STATUS_CODE = :statusPending
	</sql-query>
	<sql-query name="collection.closingtask.cekmenudepositaktif">
		<query-param name="uuidUser" type="long" />
		<query-param name="menuPrompt" type="string" />
			select UUID_MOBILE_MENU_OF_GROUP 
			from AM_MOBILEMENUOFGROUP amog
				inner join AM_MSMOBILEMENU amm on amm.UUID_MS_MOBILE_MENU = amog.UUID_MS_MOBILE_MENU
				inner join AM_MEMBEROFGROUP amo on amo.UUID_MS_GROUP = amog.UUID_MS_GROUP
			where amo.UUID_MS_USER = :uuidUser
				and amm.MENU_PROMPT = :menuPrompt
	</sql-query>
</hibernate-mapping>