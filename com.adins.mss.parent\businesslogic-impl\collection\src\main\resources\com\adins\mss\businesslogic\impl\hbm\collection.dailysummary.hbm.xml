<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="coll.dailysummary">
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select th.UUID_MS_USER as idUser, th.UUID_BRANCH as branch, 
		  (
		    SELECT SUM(AMOUNT_DUE)
				FROM TR_TASKCOLLDATA TRTD with (nolock) 
		    JOIN TR_TASK_H TRTH with (nolock) ON TRTH.UUID_TASK_H = TRTD.UUID_TASK_ID
				JOIN ms_statustask mst on mst.uuid_status_task = trth.uuid_status_task
				AND TRTH.UUID_MS_USER = th.UUID_MS_USER
				and trth.uuid_branch = th.uuid_branch
				AND mst.status_code != 'C'
				AND ASSIGN_DATE BETWEEN :start AND :end
		  ) AS TOTPAY,
		
			(
			    SELECT SUM(PAYMENT_RECEIVED)
				  FROM TR_TASKCOLLDATA TTCD with (nolock) 
			    JOIN TR_TASK_H TRTH with (nolock) ON TRTH.UUID_TASK_H = TTCD.UUID_TASK_ID
				AND TRTH.UUID_MS_USER = th.UUID_MS_USER
				and trth.uuid_branch = th.uuid_branch
				AND ISNULL(SUBMIT_DATE,CURRENT_TIMESTAMP) BETWEEN :start AND :end
		  ) AS COLLREC,
		
			(
		    SELECT COUNT(UUID_DEPOSIT_REPORT_H)
				FROM TR_DEPOSITREPORT_H with (nolock) 
				WHERE USR_CRT = CAST(th.UUID_MS_USER as varchar(36))
				AND TRANSFERRED_DATE BETWEEN :start AND :end
		  ) AS NUMDEP,
		
			(
		    SELECT SUM(ISNULL(TRDD.DEPOSIT_AMT,0))
				FROM TR_DEPOSITREPORT_H TRDH with (nolock) 
		    JOIN TR_DEPOSITREPORT_D TRDD with (nolock) ON TRDH.UUID_DEPOSIT_REPORT_H = TRDD.UUID_DEPOSIT_REPORT_H
				WHERE TRDH.USR_CRT = CAST(th.UUID_MS_USER as varchar(36))
				AND ISNULL(TRANSFERRED_DATE, CURRENT_TIMESTAMP) BETWEEN :start AND :end
		  ) AS TOTDEP,
		
			(
		    SELECT COUNT(1)
				FROM TR_TASK_H trth with (nolock)
				WHERE TRTH.UUID_MS_USER = th.UUID_MS_USER
				and trth.uuid_branch = th.uuid_branch
				AND ASSIGN_DATE BETWEEN :start AND :end 
		  ) assign,
		
			(
		    SELECT COUNT(1)
				FROM TR_TASK_H trth with (nolock)
				WHERE TRTH.UUID_MS_USER = th.UUID_MS_USER
				and trth.uuid_branch = th.uuid_branch
				AND SUBMIT_DATE BETWEEN :start AND :end 
		  ) submitted,
		
			(
				SELECT COUNT(TRTH.UUID_TASK_H)
				FROM TR_TASKCOLLDATA TTCD WITH (NOLOCK)
				JOIN TR_TASK_H TRTH WITH (NOLOCK) ON TRTH.UUID_TASK_H = TTCD.UUID_TASK_ID
				AND TRTH.UUID_MS_USER = th.UUID_MS_USER
				AND trth.uuid_branch = th.uuid_branch
				AND ISNULL(SUBMIT_DATE, CURRENT_TIMESTAMP) BETWEEN :start AND :end 
			)AS TOTALPAIDTASK	
		
			from TR_TASK_H th with (nolock) join am_msuser amu with (nolock) on th.UUID_MS_USER = amu.UUID_MS_USER
			join am_mssubsystem ams on amu.uuid_ms_subsystem = ams.uuid_ms_subsystem
			where th.ASSIGN_DATE BETWEEN :start AND :end
			and ams.subsystem_name = 'MC'
			group by th.UUID_MS_USER, th.UUID_BRANCH
	</sql-query>
	<sql-query name="coll.deleteEmptySummary">
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	delete TR_COLLDAILYSUMMARY
		where uuid_coll_daily_summary in (
		    select uuid_coll_daily_summary
		    from (
		       select tcds.uuid_coll_daily_summary, tcds.uuid_ms_user,
		           ( select count (uuid_task_h) 
				     FROM TR_TASK_H tth with (nolock)
		             where tth.assign_date BETWEEN  :start AND :end
					 and tth.uuid_ms_user = tcds.uuid_ms_user
		           ) as cnt
		        from TR_COLLDAILYSUMMARY tcds with (nolock)
		        WHERE tcds.daily_date BETWEEN  :start AND :end
		    ) a
			where a.cnt = 0
		)
	</sql-query>
	<sql-query name="coll.isExists">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	select UUID_COLL_DAILY_SUMMARY
		from TR_COLLDAILYSUMMARY with (nolock)
		where UUID_MS_USER = :uuidUser
			and UUID_BRANCH = :uuidBranch
			and DAILY_DATE = CONVERT(DATE, CURRENT_TIMESTAMP) 
	</sql-query>
	<sql-query name="coll.deleteTaskDaily">
	   	<query-param name="status" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	<query-param name="subsystem" type="string" />
	   	<query-param name="statusMobileSeqNo" type="long" />
	   	UPDATE TR_TASK_H
		SET UUID_STATUS_TASK = :status,
			USR_UPD = 'SCHEDULER',
			DTM_UPD = CURRENT_TIMESTAMP,
			STATUS_MOBILE_SEQ_NO = :statusMobileSeqNo
		WHERE DTM_CRT BETWEEN :start AND :end
			AND UUID_MS_USER IN 
				(SELECT UUID_MS_USER FROM AM_MSUSER with (nolock) WHERE UUID_MS_SUBSYSTEM = :subsystem)
			AND UUID_STATUS_TASK = 
				(SELECT UUID_STATUS_TASK FROM MS_STATUSTASK with (nolock) WHERE UUID_MS_SUBSYSTEM = :subsystem AND STATUS_CODE = 'N')
	</sql-query>
	
	<sql-query name="coll.resetCohCollector">
	   	<query-param name="uuidJob" type="string" />
	   	<query-param name="subsystem" type="string" />
	   	UPDATE AM_MSUSER
		SET CASH_ON_HAND = '0',
			USR_UPD = 'SCHEDULER',
			DTM_UPD = CURRENT_TIMESTAMP
		WHERE UUID_MS_SUBSYSTEM = :subsystem
		AND UUID_JOB = :uuidJob
	</sql-query>
	
</hibernate-mapping>