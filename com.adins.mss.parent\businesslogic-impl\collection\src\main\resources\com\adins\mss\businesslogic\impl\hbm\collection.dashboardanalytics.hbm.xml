<?xml version="1.0"?>
<!DOCTYP<PERSON> hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="collection.dm.getPerformanceMTD">
	   	<query-param name="uuidBranch" type="long" />
	   	SELECT DAY(TDS.DTM_CRT) Days, SUM(ISNULL(TOTAL_ASSIGNED_TASK,0)) assign_in, SUM(ISNULL(TOTAL_SUBMITTED_TASK,0)) visited, SUM(ISNULL(TOTAL_PAID_TASK,0)) collected,
	   	SUM(ISNULL(TOTAL_TOBECOLLECT,0)) TOTAL_TOBECOLLECT, SUM(ISNULL(TOTAL_PAID,0)) TOTAL_PAID
	    FROM TR_COLLDAILYSUMMARY TDS WITH (NOLOCK)
	    WHERE TDS.UUID_BRANCH = :uuidBranch
	  	AND TDS.DTM_CRT between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate(),112))
	   	GROUP BY DAY(TDS.DTM_CRT)
	    ORDER BY DAY(TDS.DTM_CRT)
	</sql-query>
	<sql-query name="collection.dm.getPerformanceToday">
	   	<query-param name="uuidBranch" type="long" />
		 SELECT DAY(TDS.DTM_CRT) Days, SUM(ISNULL(TOTAL_ASSIGNED_TASK,0)) assign_in, SUM(ISNULL(TOTAL_SUBMITTED_TASK,0)) visited, SUM(ISNULL(TOTAL_PAID_TASK,0)) collected,
		 SUM(ISNULL(TOTAL_TOBECOLLECT,0)) TOTAL_TOBECOLLECT, SUM(ISNULL(TOTAL_PAID,0)) TOTAL_PAID
	    FROM TR_COLLDAILYSUMMARY TDS WITH (NOLOCK)
	    WHERE TDS.UUID_BRANCH = :uuidBranch
	   	AND TDS.DTM_CRT between convert(datetime,convert(varchar(8),getdate(),112)) and  convert(datetime,convert(varchar(8),getdate()+1,112))
	   	GROUP BY DAY(TDS.DTM_CRT)
	    ORDER BY DAY(TDS.DTM_CRT) 
	</sql-query>
	<sql-query name="collection.dm.getTopMTD">
	   	<query-param name="uuidBranch" type="long" />
		SELECT AMU.UUID_MS_USER, AMU.FULL_NAME, SUM(ISNULL(TOTAL_ASSIGNED_TASK,0)) assign_in, 
  		SUM(ISNULL(TOTAL_SUBMITTED_TASK,0)) visited, SUM(ISNULL(TOTAL_PAID_TASK,0)) collected,
  		SUM(ISNULL(TOTAL_TOBECOLLECT,0)) TOTAL_TOBECOLLECT, SUM(ISNULL(TOTAL_PAID,0)) TOTAL_PAID
     	FROM TR_COLLDAILYSUMMARY TDS WITH (NOLOCK) join AM_MSUSER AMU WITH (NOLOCK)
     	ON AMU.UUID_MS_USER = TDS.UUID_MS_USER
     	WHERE TDS.UUID_BRANCH = :uuidBranch
     	AND TDS.DTM_CRT between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate(),112))
     	GROUP BY AMU.UUID_MS_USER, AMU.FULL_NAME
	</sql-query>
	<sql-query name="collection.dm.getTopToday">
	   	<query-param name="uuidBranch" type="long" />
		  SELECT AMU.UUID_MS_USER, AMU.FULL_NAME, SUM(ISNULL(TOTAL_ASSIGNED_TASK,0)) assign_in, 
  		  SUM(ISNULL(TOTAL_SUBMITTED_TASK,0)) visited, SUM(ISNULL(TOTAL_PAID_TASK,0)) collected,
  		  SUM(ISNULL(TOTAL_TOBECOLLECT,0)) TOTAL_TOBECOLLECT, SUM(ISNULL(TOTAL_PAID,0)) TOTAL_PAID
	      FROM TR_COLLDAILYSUMMARY TDS WITH (NOLOCK) join AM_MSUSER AMU WITH (NOLOCK)
	      ON AMU.UUID_MS_USER = TDS.UUID_MS_USER
	      WHERE TDS.UUID_BRANCH = :uuidBranch
	  	  AND TDS.DTM_CRT between convert(datetime,convert(varchar(8),getdate(),112)) and  convert(datetime,convert(varchar(8),getdate()+1,112))
	      GROUP BY AMU.UUID_MS_USER, AMU.FULL_NAME
	</sql-query>
	<sql-query name="collection.dm.getSurveyorStatusToday">
		<query-param name="uuidSubsystem" type="long" />
	   	<query-param name="uuidBranch" type="long" />
		select amu.uuid_ms_user, amu.full_name, spv.full_name spvname,
	   (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	   		JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			where msm.STATUS_MOBILE_CODE = 'N'
		    and UUID_MS_USER = amu.uuid_ms_user
		    and ASSIGN_DATE between convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	   ) as newTask,
	   (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	   		JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			where msm.STATUS_MOBILE_CODE = 'W'
		    and UUID_MS_USER = amu.uuid_ms_user
		    and ASSIGN_DATE between convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	   ) as download, 
	   (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	   		JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			where msm.STATUS_MOBILE_CODE = 'R'
		    and UUID_MS_USER = amu.uuid_ms_user
		    and ASSIGN_DATE between convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	   ) as reads, 
	   (select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
	   		JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			where msm.STATUS_MOBILE_CODE = 'O'
		    and trth.uuid_ms_user = amu.uuid_ms_user
		    and ASSIGN_DATE between convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	   ) as onProgress,
	   (select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
	   		JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			where msm.STATUS_MOBILE_CODE = 'U'
		    and SUBMIT_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
		    and trth.uuid_ms_user = amu.uuid_ms_user
	   ) as upload,  
	   (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
		    JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			where msm.STATUS_MOBILE_CODE = 'S'
		    and SUBMIT_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
		    and UUID_MS_USER = amu.uuid_ms_user
	   ) as submit   
	 from am_msuser amu
	  left outer JOIN AM_MSUSER spv on amu.spv_id = spv.uuid_ms_user
	 where amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MC_JOBSVY'))
	   and amu.UUID_BRANCH = :uuidBranch
	</sql-query>
</hibernate-mapping>
