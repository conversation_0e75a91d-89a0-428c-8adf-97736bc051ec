<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="collection.dm.getUser">
	    <query-param name="uuidSPV" type="string" />
	    WITH N AS (
		SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.INITIAL_NAME, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSPV AND msu.IS_ACTIVE = '1'
		
		UNION ALL
		
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.INITIAL_NAME, N.HIRARK<PERSON>, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, INITIAL_NAME from N order by FULL_NAME
	</sql-query>
	
	<sql-query name="collection.dm.getUserTR">
    <query-param name="uuidSPV" type="string" />
	    WITH N AS (
		SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.INITIAL_NAME, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.UUID_MS_USER = :uuidSPV AND msu.IS_ACTIVE = '1'
		
		UNION ALL
		
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.INITIAL_NAME, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, INITIAL_NAME from N order by FULL_NAME
	</sql-query>
	
	<sql-query name="collection.dm.getUser2">
    <query-param name="uuidSPV" type="string" />
   	<query-param name="uuidColl" type="string" />
	    WITH N AS (
		SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_BRANCH, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSPV AND msu.IS_ACTIVE = '1'
		
		UNION ALL
		
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.LOGIN_ID, msu2.UUID_BRANCH, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID, msb.BRANCH_NAME 
		from N JOIN MS_BRANCH msb with (nolock) ON N.UUID_BRANCH = msb.UUID_BRANCH where UUID_MS_USER != :uuidColl order by FULL_NAME
	</sql-query>
	
	<sql-query name="collection.dm.getArea">
	    <query-param name="uuidCollector" type="string" />
		SELECT MA.LATITUDE, MA.LONGITUDE, MAP.LATITUDE lat, MAP.LONGITUDE lng, MA.AREA_TYPE_CODE, MA.RADIUS
		FROM MS_AREAOFUSER MAU with (nolock) 
			LEFT OUTER JOIN MS_AREA MA with (nolock) ON MAU.UUID_AREA = MA.UUID_AREA
			LEFT OUTER JOIN MS_AREAPATH MAP with (nolock) ON MA.UUID_AREA = MAP.UUID_AREA
		WHERE MAU.UUID_MS_USER = :uuidCollector
	</sql-query>
	
	<sql-query name="collection.dm.undoneTask">
    <query-param name="uuidSPV" type="string" />
    <query-param name="uuidSubSystem" type="string" />
		SELECT trth.UUID_TASK_H, 
			ISNULL(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,
			ISNULL(trth.CUSTOMER_ADDRESS,'-') CUSTOMER_ADDRESS,
			ISNULL(trth.AGREEMENT_NO,'-') APPL_NO,   
			trth.LATITUDE, 
			trth.LONGITUDE,
			ammu.INITIAL_NAME
		FROM TR_TASK_H trth with (nolock) 
		LEFT JOIN MS_STATUSTASK msst with (nolock)
			ON trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
		LEFT JOIN AM_MSUSER ammu with (nolock)
			ON trth.UUID_MS_USER = ammu.UUID_MS_USER
		WHERE msst.STATUS_CODE = 'N'
			AND ammu.SPV_ID = :uuidSPV
			AND ammu.UUID_MS_SUBSYSTEM = :uuidSubSystem
	</sql-query>
	
	<sql-query name="collection.dm.surveyLocation">
    <query-param name="uuidSPV" type="string" />
    <query-param name="uuidSubSystem" type="string" />
    <query-param name="start" type="string" />
    <query-param name="end" type="string" />
		SELECT trth.UUID_TASK_H, 
			ISNULL(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,
			ISNULL(trth.CUSTOMER_ADDRESS,'-') CUSTOMER_ADDRESS,
			ISNULL(trth.AGREEMENT_NO,'-') APPL_NO,   
			trth.LATITUDE, 
			trth.LONGITUDE,
			ammu.INITIAL_NAME
		FROM TR_TASK_H trth with (nolock) 
		LEFT JOIN AM_MSUSER ammu with (nolock)
			ON trth.UUID_MS_USER = ammu.UUID_MS_USER
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			AND ammu.SPV_ID = :uuidSPV
			AND ammu.UUID_MS_SUBSYSTEM = :uuidSubSystem
	</sql-query>
	
	<sql-query name="collection.dm.collectedLocation">
    <query-param name="uuidSPV" type="string" />
    <query-param name="uuidSubSystem" type="string" />
    <query-param name="start" type="string" />
    <query-param name="end" type="string" />
		SELECT trth.UUID_TASK_H, 
			ISNULL(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,
			ISNULL(trth.CUSTOMER_ADDRESS,'-') CUSTOMER_ADDRESS,
			ISNULL(trth.AGREEMENT_NO,'-') APPL_NO,   
			trtcd.COLLECTION_LOCATION_LAT, 
			trtcd.COLLECTION_LOCATION_LONG,
			ammu.INITIAL_NAME
		FROM TR_TASK_H trth with (nolock) INNER JOIN TR_TASKCOLLDATA trtcd with (nolock)
			ON trth.UUID_TASK_H = trtcd.UUID_TASK_ID
		LEFT JOIN AM_MSUSER ammu with (nolock)
			ON trth.UUID_MS_USER = ammu.UUID_MS_USER
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			AND trtcd.COLLECTION_LOCATION_LAT is not null
			AND trtcd.COLLECTION_LOCATION_LONG is not null
			AND ammu.SPV_ID = :uuidSPV
			AND ammu.UUID_MS_SUBSYSTEM = :uuidSubSystem
	</sql-query>
	
	<sql-query name="collection.dm.totalPayment">
   	<query-param name="uuidUser" type="string" />
   	<query-param name="start" type="string" />
   	<query-param name="end" type="string" />
		SELECT ISNULL(SUM(ISNULL(trtcd.AMOUNT_DUE, 0)), 0) JUMLAH
		FROM dbo.TR_TASK_H AS trth with (nolock) INNER JOIN dbo.TR_TASKCOLLDATA AS trtcd with (nolock) 
			ON trtcd.UUID_TASK_ID = trth.UUID_TASK_H
		INNER JOIN dbo.AM_MSUSER AS ammsu with (nolock)
			ON trth.UUID_MS_USER = ammsu.UUID_MS_USER
		WHERE ammsu.UUID_MS_USER = :uuidUser
			AND trtcd.DTM_CRT BETWEEN :start AND :end
	</sql-query>
	
	<sql-query name="collection.dm.lastDepositTime">
   	<query-param name="uuidUser" type="string" />
   	<query-param name="start" type="string" />
   	<query-param name="end" type="string" />
		SELECT TOP 1 CONVERT(VARCHAR(5),TRANSFERRED_DATE,108) 
		FROM TR_DEPOSITREPORT_H trdeph with (nolock)
		WHERE trdeph.DTM_CRT BETWEEN :start AND :end
			AND trdeph.USR_CRT = :uuidUser
		ORDER BY DTM_CRT DESC
	</sql-query>
	
	<sql-query name="collection.dm.calculation">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select SUM(ISNULL(TOTAL_TOBECOLLECT, 0)) as TOTAL_TOBECOLLECT, 
			SUM(ISNULL(TOTAL_PAID, 0)) as TOTAL_PAID, 
			SUM(ISNULL(NUMBER_OF_DEPOSITED, 0)) as NUMBER_OF_DEPOSITED, 
			SUM(ISNULL(TOTAL_DEPOSITED, 0)) as TOTAL_DEPOSITED
		from TR_COLLDAILYSUMMARY with (nolock)
		WHERE UUID_MS_USER = :uuidUser
			and DAILY_DATE BETWEEN :start AND :end
	</sql-query>
	<sql-query name="collection.dm.getTaskRecapitulation">
	   	<query-param name="uuidUser" type="long" />
	   	<query-param name="idSubsystem" type="long" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select 
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'N'
				and UUID_MS_USER = :uuidUser
			) as newTask,
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'R'
				and UUID_MS_USER = :uuidUser
			) as reads,
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'W'
				and UUID_MS_USER = :uuidUser
			) as download, 
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'S'
				and SUBMIT_DATE BETWEEN :start AND :end
				and UUID_MS_USER = :uuidUser
			) as submit,
			(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'U'
				and trth.uuid_ms_user = :uuidUser
			) as upload, 
			(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
				join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
				where mst.STATUS_CODE = 'V'
				and START_DTM IS NOT NULL
				and mst.UUID_MS_SUBSYSTEM = :idSubsystem
				and trth.uuid_ms_user = :uuidUser
			) as verifikasi,
			(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'O'
				and trth.uuid_ms_user = :uuidUser
			) as survey
	</sql-query>
	
	<sql-query name="collection.dm.taskmonitoring.load">
	   	<query-param name="uuidColl" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	SELECT trth.UUID_TASK_H, trth.TASK_ID, trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, trth.CUSTOMER_PHONE, trth.ZIP_CODE, msf.FORM_NAME,
			CASE 
				WHEN msm.STATUS_MOBILE_CODE = 'N'
					THEN 'red' 
				WHEN msm.STATUS_MOBILE_CODE = 'W'
					THEN 'orange'
				WHEN msm.STATUS_MOBILE_CODE = 'R' 
					THEN 'blue'
				WHEN msm.STATUS_MOBILE_CODE = 'O'   
					THEN 'purple'
				WHEN msm.STATUS_MOBILE_CODE = 'U'
					THEN 'yellow'
				WHEN trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end and msm.STATUS_MOBILE_CODE = 'S'
					THEN 'green'
			END as status,
			CASE
		 	<![CDATA[WHEN DATEDIFF(hour, trth.ASSIGN_DATE, CURRENT_TIMESTAMP) <= (select GS_VALUE from AM_GENERALSETTING WHERE GS_CODE = 'SLA_TIME')]]>	
				THEN '0'
				ELSE '1'
			END as sla,
				trtcd.RESULT_CODE, mscr.RESULT_DESC, mscrc.COLL_RESULT_CATEGORY_CODE, trtcd.OD, trtcd.INST_NO, trtcd.AMOUNT_DUE, trtcd.PAYMENT_RECEIVED, trtcd.PTP_DATE
			FROM TR_TASK_H trth with (nolock) JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
			JOIN TR_TASKCOLLDATA trtcd with (nolock) ON trth.UUID_TASK_H = trtcd.UUID_TASK_ID
			LEFT JOIN MS_COLLECTIONRESULT mscr with (nolock) ON mscr.RESULT_CODE = trtcd.RESULT_CODE
			LEFT JOIN MS_COLLRESULTCATEGORY mscrc with (nolock) ON mscrc.UUID_COLL_RESULT_CATEGORY = mscr.UUID_COLL_RESULT_CATEGORY
			WHERE trth.UUID_MS_USER = :uuidColl 
			AND ((trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end) OR (trth.SUBMIT_DATE IS NULL))
			AND msm.STATUS_MOBILE_CODE != 'D'
	</sql-query>
	
	<sql-query name="collection.dm.totalPaymentTask">
   	<query-param name="uuidTaskH" type="string" />
		SELECT ISNULL(SUM(ISNULL(trtcd.AMOUNT_DUE, 0)), 0) JUMLAH
		FROM dbo.TR_TASK_H AS trth with (nolock) INNER JOIN dbo.TR_TASKCOLLDATA AS trtcd with (nolock) 
			ON trtcd.UUID_TASK_ID = trth.UUID_TASK_H
		WHERE trth.UUID_TASK_H = :uuidTaskH
	</sql-query>
	
	<sql-query name="collection.dm.paymentCollectedTask">
   	<query-param name="uuidTaskH" type="string" />
		SELECT ISNULL(SUM(ISNULL(trtcd.PAYMENT_RECEIVED, 0)), 0) JUMLAH
		FROM dbo.TR_TASK_H AS trth with (nolock) INNER JOIN dbo.TR_TASKCOLLDATA AS trtcd with (nolock) 
			ON trtcd.UUID_TASK_ID = trth.UUID_TASK_H
		INNER JOIN dbo.MS_STATUSTASK AS msst with (nolock) 
			ON trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
		WHERE msst.STATUS_CODE = 'S'
			AND trth.UUID_TASK_H = :uuidTaskH
	</sql-query>
	
	<sql-query name="collection.dm.getBranchAll">
	select branch.uuid_branch ,branch.latitude,branch.longitude,branch.color, branch.branch_name,   
	(select count(UUID_MS_USER) 
					from am_msuser amu join ms_job mj 
					on amu.uuid_job = mj.uuid_job
					join  am_mssubsystem ams on amu.uuid_ms_subsystem = ams.uuid_ms_subsystem
					 where amu.uuid_branch = branch.uuid_branch
					 and mj.IS_FIELD_PERSON = '1'  
					 and amu.IS_ACTIVE = '1' 
					 and mj.IS_ACTIVE = '1'
					 and ams.subsystem_name = 'MC') FROM MS_BRANCH branch	
	</sql-query>
	
	<sql-query name="collection.dm.getCollectorBranchWithColour">
	<query-param name="uuidBranch" type="string" />
	<query-param name="idxBranch" type="string" />
		WITH cte AS 
		( 
	    select trlh.*,
	     ROW_NUMBER() OVER (PARTITION BY trlh.UUID_MS_USER ORDER BY trlh.datetime DESC) AS rn 
			from tr_locationhistory trlh 
			where trlh.uuid_ms_user in 
			 (
				select UUID_MS_USER 
				from am_msuser amu join ms_job mj 
				on amu.uuid_job = mj.uuid_job
				join  am_mssubsystem ams on amu.uuid_ms_subsystem = ams.uuid_ms_subsystem
				 where amu.uuid_branch = :uuidBranch
				 and mj.IS_FIELD_PERSON = '1'  
				 and amu.IS_ACTIVE = '1' 
				 and mj.IS_ACTIVE = '1'
				 and ams.subsystem_name = 'MC'
			)and latitude is not null and longitude is not null
			) 
			SELECT amur.uuid_ms_user ,amur.FULL_NAME, cte.latitude, cte.longitude,  mb.COLOR, :idxBranch
			FROM cte join AM_MSUSER amur on cte.uuid_ms_user = amur.uuid_ms_user
			join MS_BRANCH mb on mb.uuid_branch = amur.uuid_branch
			WHERE rn = 1
	</sql-query>
	<sql-query name="collection.dm.getBranchCountAll">
		select count (branch.uuid_branch) FROM MS_BRANCH branch			
	</sql-query>
	<sql-query name="collection.dm.getPrecentagebattery">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="idSubsystem" type="string" />
	    <query-param name="atb_code" type="string" />
		select memberAtb.ATTRIBUTE_VALUE from AM_ATTRIBUTEOFMEMBER memberAtb
		join AM_MSUSER usr
		on usr.UUID_MS_USER = memberAtb.UUID_MS_USER
		join AM_MSATTRIBUTE atb
		on atb.UUID_MS_ATTRIBUTE = memberAtb.UUID_MS_ATTRIBUTE
		join AM_MSSUBSYSTEM subs
		on subs.UUID_MS_SUBSYSTEM = atb.UUID_MS_SUBSYSTEM
		where usr.UUID_MS_USER = :uuidUser
		and subs.UUID_MS_SUBSYSTEM = :idSubsystem
		and atb.ATB_CODE = :atb_code
	</sql-query>
	

</hibernate-mapping>