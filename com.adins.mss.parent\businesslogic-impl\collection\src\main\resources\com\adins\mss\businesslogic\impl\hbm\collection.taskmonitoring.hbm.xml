<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>	
	<sql-query name="collection.taskmonitoring.listUser">
			<query-param name="uuidSpv" type="string" />
		WITH N AS (
			SELECT 
			msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, msu_spv.LOGIN_ID login_id_spv, msu_spv.FULL_NAME full_name_spv, msu.UUID_JOB
			FROM  AM_MSUSER msu with (nolock) join AM_MSUSER msu_spv with (nolock) ON msu.SPV_ID = msu_spv.UUID_MS_USER
			WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME,	msu2.IS_LOGGED_IN, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, N.LOGIN_ID login_id_spv, N.FULL_NAME full_name_spv, msu2.UUID_JOB
			FROM   AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID, login_id_spv, full_name_spv 
		from N join ms_job J with (nolock) on N.uuid_job = j.uuid_job
		where j.is_field_person = '1'
		order by FULL_NAME
	</sql-query>
	
	<sql-query name="collection.taskmonitoring.lastDepositTime">
   	<query-param name="uuidUser" type="string" />
   	<query-param name="start" type="string" />
   	<query-param name="end" type="string" />
		SELECT TOP 1 CONVERT(VARCHAR(5),TRANSFERRED_DATE,108) 
		FROM TR_DEPOSITREPORT_H trdeph with (nolock)
		WHERE trdeph.DTM_CRT BETWEEN :start AND :end
			AND trdeph.USR_CRT = :uuidUser
		ORDER BY DTM_CRT DESC
	</sql-query>
	
	<sql-query name="collection.taskmonitoring.sumDetail">
		<query-param name="uuidSvy" type="string" />
			SELECT ISNULL(SUM(ISNULL(TOTAL_TOBECOLLECT, 0)), 0) TOTAL_TOBECOLLECT, 
				ISNULL(SUM(ISNULL(TOTAL_PAID, 0)), 0) TOTAL_PAID, 
				ISNULL(SUM(ISNULL(TOTAL_DEPOSITED, 0)) , 0) TOTAL_DEPOSITED
			FROM TR_COLLDAILYSUMMARY with (nolock)
			WHERE UUID_MS_USER = :uuidSvy
				and DAILY_DATE = CONVERT(date, CURRENT_TIMESTAMP) 
	</sql-query>
	
	<sql-query name="collection.taskmonitoring.listTask">
	   	<query-param name="uuidSvy" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
 		SELECT trth.UUID_TASK_H, trth.TASK_ID, trth.AGREEMENT_NO, trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, trth.CUSTOMER_PHONE, trth.ZIP_CODE, '' as FORM_NAME,
			CASE 
				WHEN msm.STATUS_MOBILE_CODE = 'N'
					THEN 'red' 
				WHEN msm.STATUS_MOBILE_CODE = 'W'
					THEN 'orange'
				WHEN msm.STATUS_MOBILE_CODE = 'R'
					THEN 'blue'
				WHEN msm.STATUS_MOBILE_CODE = 'O'   
					THEN 'purple'
				WHEN msm.STATUS_MOBILE_CODE = 'U'
					THEN 'yellow'
				WHEN trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end and msm.STATUS_MOBILE_CODE = 'S'
					THEN 'green'
			END as status,
			CASE
		 	<![CDATA[WHEN DATEDIFF(hour, trth.ASSIGN_DATE, CURRENT_TIMESTAMP) <= (select GS_VALUE from AM_GENERALSETTING with (nolock) WHERE GS_CODE = 'SLA_TIME')]]>	
				THEN '0'
				ELSE '1'
			END as sla, tcd.OD, tcd.INST_NO, tcd.TENOR, tcd.AMOUNT_DUE, tcd.PAYMENT_RECEIVED, tcd.PTP_DATE, mcr.RESULT_DESC, mcrc.COLL_RESULT_CATEGORY_CODE, mcrc.COLL_RESULT_CATEGORY_DESC
			FROM TR_TASK_H trth with (nolock) 
			JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			JOIN TR_TASKCOLLDATA tcd with (nolock) ON trth.uuid_task_h = tcd.uuid_task_id
			LEFT JOIN MS_COLLECTIONRESULT mcr with (nolock) ON tcd.RESULT_CODE = mcr.RESULT_CODE
			LEFT JOIN MS_COLLRESULTCATEGORY mcrc with (nolock) ON mcr.UUID_COLL_RESULT_CATEGORY = mcrc.UUID_COLL_RESULT_CATEGORY
			WHERE trth.UUID_MS_USER = :uuidSvy
			AND msm.STATUS_MOBILE_CODE != 'D'
			AND ((trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end) OR (trth.SUBMIT_DATE IS NULL))
	</sql-query>
	
	<sql-query name="collection.taskmonitoring.sumtobecollectandpaid">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
 		select ISNULL(sum(ISNULL(TOTAL_TOBECOLLECT, 0)),0) as TOTAL_TOBECOLLECT, ISNULL(sum(ISNULL(TOTAL_PAID, 0)),0) as TOTAL_PAID
		from TR_COLLDAILYSUMMARY with (nolock)
			 join (select keyValue from getUserByLogin(:uuidUser)) msu on msu.keyValue = UUID_MS_USER 
		where DTM_CRT between :start and :end
	</sql-query>
		
	<sql-query name="collection.taskmonitoring.spvCombo">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="jobCode" type="string" />
 		SELECT amu.UUID_MS_USER, amu.FULL_NAME + ' - ' + msb.branch_name as FULL_NAME
		FROM AM_MSUSER amu with (nolock)
			JOIN MS_JOB mj with (nolock) ON amu.UUID_JOB = mj.UUID_JOB AND amu.UUID_MS_SUBSYSTEM = mj.UUID_MS_SUBSYSTEM
			JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
		WHERE mj.JOB_CODE = :jobCode
			AND amu.is_active = '1'
			AND amu.is_deleted = '0'
	</sql-query>
	
	<sql-query name="collection.taskmonitoring.spvComboByHierarkiUser">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="jobCode" type="string" />
 		SELECT amu.UUID_MS_USER, amu.FULL_NAME + ' - ' + mb.branch_name as FULL_NAME
		FROM AM_MSUSER amu with (nolock)
			JOIN MS_BRANCH mb with (nolock) on mb.uuid_branch = amu.uuid_branch 
			JOIN MS_JOB mj with (nolock) ON amu.UUID_JOB = mj.UUID_JOB
				AND amu.UUID_MS_SUBSYSTEM = mj.UUID_MS_SUBSYSTEM
			JOIN (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
		WHERE mj.JOB_CODE = :jobCode
			AND amu.is_active = '1'
			AND amu.is_deleted = '0'
	</sql-query>
</hibernate-mapping>