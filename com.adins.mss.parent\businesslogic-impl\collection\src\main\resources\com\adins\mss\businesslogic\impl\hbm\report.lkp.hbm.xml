<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="report.lkp.getListSummary">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="subsystemId" type="string"/>
		WITH N as (
			select trtcd.UUID_TASK_ID as UUID_TASK_ID, h.DTM_CRT as DTM_CRT, 
				h.uuid_ms_user as uuid_ms_user, h.UUID_BRANCH
			from TR_TASK_H h with (nolock)
				join TR_TASKCOLLDATA trtcd with (nolock) on trtcd.UUID_TASK_ID=h.UUID_TASK_H
			where h.DTM_CRT BETWEEN :startDate and :endDate 
				and trtcd.payment_received is not null
			UNION ALL
			select ftrtcd.UUID_TASK_ID as UUID_TASK_ID, h.DTM_CRT as DTM_CRT, 
				h.uuid_ms_user as uuid_ms_user, h.UUID_BRANCH
			from FINAL_TR_TASK_H h with (nolock)
				join FINAL_TR_TASKCOLLDATA ftrtcd with (nolock) on ftrtcd.UUID_TASK_ID=h.UUID_TASK_H
			where h.DTM_CRT BETWEEN :startDate and :endDate 
				and ftrtcd.payment_received is not null
		)
		SELECT * 
		FROM (
			SELECT MB.UUID_BRANCH, MB.BRANCH_CODE + ' - ' + MB.BRANCH_NAME BRANCH,
				SUM(COALESCE(TC.TOTAL_ASSIGNED_TASK,0)) ASSIGN, 
				SUM(COALESCE(TC.TOTAL_SUBMITTED_TASK,0)) SUBMITTED, 
				SUM(COALESCE(CAST(TOTAL_TOBECOLLECT AS bigint),0)) TOBE, 
				coalesce(countTask,0) TASK_COLLECTED,
				SUM(COALESCE(CAST(TOTAL_PAID AS bigint),0)) PAID, 
				SUM(COALESCE(TC.TOTAL_ASSIGNED_TASK,0))-coalesce(countTask,0) TASK_FAILED,
				SUM(CASE
					WHEN <![CDATA[ (CAST(TOTAL_TOBECOLLECT AS bigint)-CAST(TOTAL_PAID AS bigint)) < 0 THEN 0 ]]>
					ELSE COALESCE(CAST(TOTAL_TOBECOLLECT AS bigint)-CAST(TOTAL_PAID AS bigint),0)
					END
				) UNPAID
			FROM MS_BRANCH MB with (nolock)
				JOIN TR_COLLDAILYSUMMARY TC with (nolock) ON TC.UUID_BRANCH = MB.UUID_BRANCH
				JOIN (
					select keyValue 
					from dbo.getCabangByLogin(:branchId)
				) cb on tc.UUID_BRANCH = cb.keyValue
				JOIN (
					select keyValue 
					from dbo.getAllUserByBranchLogin(:branchId, :subsystemId)
				) usr on tc.UUID_MS_USER = usr.keyValue
				LEFT JOIN (
					select UUID_BRANCH, count(1) as countTask 
					from N 
						JOIN (
							select keyValue 
							from dbo.getAllUserByBranchLogin(:branchId, :subsystemId)
						) usr2 on N.uuid_ms_user = usr2.keyValue
					group by UUID_BRANCH
				) MM on TC.UUID_BRANCH = MM.UUID_BRANCH
			where DAILY_DATE BETWEEN :startDate and :endDate
			GROUP BY MB.UUID_BRANCH, MB.BRANCH_CODE + ' - ' + MB.BRANCH_NAME, coalesce(countTask,0)
		) B
		WHERE B.ASSIGN + B.SUBMITTED + B.TOBE + B.PAID + B.UNPAID > 0
		ORDER BY BRANCH
	</sql-query>
	
	<sql-query name="report.lkp.getListSummaryBySpv">
		<query-param name="branchId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		SELECT MB.UUID_BRANCH, MB.BRANCH_CODE + ' - ' + MB.BRANCH_NAME BRANCH,
			SUM(COALESCE(TC.TOTAL_ASSIGNED_TASK,0)) ASSIGN, 
			SUM(COALESCE(TC.TOTAL_SUBMITTED_TASK,0)) SUBMITTED, 
			SUM(CAST(TOTAL_TOBECOLLECT AS bigint)) TOBE, 
			coalesce(countTask,0) TASK_COLLECTED,
			SUM(CAST(TOTAL_PAID AS bigint)) PAID, 
			SUM(COALESCE(TC.TOTAL_ASSIGNED_TASK,0))-coalesce(countTask,0) TASK_FAILED,
			SUM(CAST(TOTAL_TOBECOLLECT AS bigint)-CAST(TOTAL_PAID AS bigint)) UNPAID
		FROM MS_BRANCH MB with (nolock)
			JOIN TR_COLLDAILYSUMMARY TC with (nolock) ON TC.UUID_BRANCH = MB.UUID_BRANCH
			left outer join ( 
				select j.UUID_BRANCH, count(1) as countTask 
				from (
					select h.UUID_BRANCH
					from TR_TASK_H h with (nolock)
						join TR_TASKCOLLDATA trtcd with (nolock) on trtcd.UUID_TASK_ID=h.UUID_TASK_H
						JOIN (
							select amu.keyValue 
							from dbo.getAllUserByLogin(:userId) as amu
							where amu.keyValue != :userId
						) usr on h.UUID_MS_USER = usr.keyValue
					where h.DTM_CRT BETWEEN :startDate and :endDate
						and trtcd.payment_received is not null
					union all
					select h.UUID_BRANCH
					from final_TR_TASK_H h with (nolock)
						join FINAL_TR_TASKCOLLDATA ftrtcd with (nolock) on ftrtcd.UUID_TASK_ID=h.UUID_TASK_H 
						JOIN (
							select amu.keyValue 
							from dbo.getAllUserByLogin(:userId) as amu
							where amu.keyValue != :userId
						) usr on h.UUID_MS_USER = usr.keyValue
					where h.DTM_CRT BETWEEN :startDate and :endDate
						and ftrtcd.payment_received is not null
				) j	
				group by j.UUID_BRANCH
			) MM on TC.UUID_BRANCH = MM.UUID_BRANCH 
			JOIN (
				select keyValue 
				from dbo.getCabangByLogin(:branchId)
			) cb on tc.UUID_BRANCH = cb.keyValue
			JOIN (
				select amu.keyValue 
				from dbo.getAllUserByLogin(:userId) as amu
				where amu.keyValue != :userId
			) usr on tc.UUID_MS_USER = usr.keyValue
				AND DAILY_DATE BETWEEN :startDate and :endDate
		GROUP BY MB.UUID_BRANCH, MB.BRANCH_CODE + ' - ' + MB.BRANCH_NAME, coalesce(countTask,0)
		ORDER BY MB.BRANCH_CODE + ' - ' + MB.BRANCH_NAME
	</sql-query>
</hibernate-mapping>