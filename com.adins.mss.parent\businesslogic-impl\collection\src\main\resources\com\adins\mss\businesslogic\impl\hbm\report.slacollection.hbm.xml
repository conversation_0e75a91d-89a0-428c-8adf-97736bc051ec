<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="report.slacollection.getSlaSummary">
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="branchId" type="string"/>
		<query-param name="subsystemId" type="string"/>
		<query-param name="gsSla" type="string"/>
			WITH hBranch as (
				SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME
			  	  FROM dbo.getCabangByLogin(:branchId)
			),
			N as (
				select
				   DATEDIFF(minute, download_date, read_date) as total_minutes_dr,
				   DATEDIFF(minute, read_date, start_dtm) as total_minutes_rp,
				   DATEDIFF(minute, start_dtm, submit_date) as total_minutes_ps,
				   DATEDIFF(minute, download_date, submit_date) as total_minutes_s,
				   DATEDIFF(minute, assign_date, submit_date) as total_minutes_sla,
				   cbl.uuid_branch, cbl.branch_code, cbl.branch_name
				from TR_TASK_H ttrh
					JOIN ms_statustask mst with (nolock) on ttrh.uuid_status_task = mst.uuid_status_task
					JOIN hBranch cbl on cbl.UUID_BRANCH = ttrh.UUID_BRANCH
				where ttrh.ASSIGN_DATE between :startDate and :endDate
					and mst.status_code IN ('V','P','S')
					and mst.uuid_ms_subsystem = :subsystemId
				UNION ALL
				select
				   DATEDIFF(minute, download_date, read_date) as total_minutes_dr,
				   DATEDIFF(minute, read_date, start_dtm) as total_minutes_rp,
				   DATEDIFF(minute, start_dtm, submit_date) as total_minutes_ps,
				   DATEDIFF(minute, download_date, submit_date) as total_minutes_s,
				   DATEDIFF(minute, assign_date, submit_date) as total_minutes_sla,
				   cbl.uuid_branch, cbl.branch_code, cbl.branch_name
				from FINAL_TR_TASK_H fttrh
					JOIN ms_statustask mst with (nolock) on fttrh.uuid_status_task = mst.uuid_status_task
					JOIN hBranch cbl on cbl.UUID_BRANCH = fttrh.UUID_BRANCH
				where fttrh.ASSIGN_DATE between :startDate and :endDate
					and mst.status_code IN ('V','P','S')
					and mst.uuid_ms_subsystem = :subsystemId
			)
			select a.branch_code, a.branch_name, isnull(a.totalSubmitTask,0) totalSubmitTask, 
				isnull(a.hour_dr,0) hour_dr, isnull(a.min_dr,0) min_dr, isnull(a.hour_rp,0) hour_rp, 
				isnull(a.min_rp,0) min_rp, isnull(a.hour_ps,0) hour_ps, isnull(a.min_ps,0) min_ps, 
				isnull(a.hour_s,0) hour_s, isnull(a.min_s,0) min_s, isnull(b.task_bf_sla,0) task_bf_sla, 
				isnull(b.hour_bf_sla,0) hour_bf_sla, isnull(b.min_bf_sla,0) min_bf_sla, 
				isnull(c.task_ov_sla,0) task_ov_sla, isnull(c.hour_ov_sla,0) hour_ov_sla, 
				isnull(c.min_ov_sla,0) min_ov_sla, a.uuid_branch 
			from (
				select branch_code, branch_name, count(*) totalSubmitTask,
					AVG(N.total_minutes_dr)/60 as hour_dr,
					AVG(N.total_minutes_dr)%60 as min_dr,
					AVG(N.total_minutes_rp)/60 as hour_rp,
					AVG(N.total_minutes_rp)%60 as min_rp,
					AVG(N.total_minutes_ps)/60 as hour_ps,
					AVG(N.total_minutes_ps)%60 as min_ps,
					(AVG(N.total_minutes_dr) + AVG(N.total_minutes_rp) + AVG(N.total_minutes_ps))/60 as hour_s,
					(AVG(N.total_minutes_dr) + AVG(N.total_minutes_rp) + AVG(N.total_minutes_ps))%60 as min_s,
					uuid_branch
				from N
				group by branch_code, branch_name, uuid_branch
			) a
			left outer join (
				select branch_code, branch_name, count(*) task_bf_sla,
					AVG(N.total_minutes_sla)/60 as hour_bf_sla,
					AVG(N.total_minutes_sla)%60 as min_bf_sla
				from N
				where N.total_minutes_sla between 0 and :gsSla *60
				group by branch_code, branch_name
			) b on a.branch_code = b.branch_code
			left outer join (
				select branch_code, branch_name, count(*) task_ov_sla,
					AVG(N.total_minutes_sla)/60 as hour_ov_sla,
					AVG(N.total_minutes_sla)%60 as min_ov_sla
				from N
				where N.total_minutes_sla > :gsSla *60
				group by branch_code, branch_name
			) c	on a.branch_code = c.branch_code
		order by BRANCH_CODE
	</sql-query>
</hibernate-mapping>