<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYP<PERSON> hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN" "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping>
	<sql-query name="services.collection.reportSum">
		<query-param name="uuid" type="String"/>
			SELECT 'total_to_be_paid' as "key", 
				CASE
					WHEN (SELECT count(1) from TR_COLLDAILYSUMMARY cds with (nolock) inner join AM_MSUSER msu with (nolock)
								on cds.UUID_MS_USER = msu.UUID_MS_USER
								where msu.UUID_MS_USER = :uuid
								AND DAILY_DATE = CONVERT(DATE, CURRENT_TIMESTAMP)) = 0 
					THEN '0'
					ELSE (SELECT isnull(cast(cds.TOTAL_TOBECOLLECT as varchar), '') as "value"
						from TR_COLLDAILYSUMMARY cds with (nolock) inner join AM_MSUSER msu with (nolock)
							on cds.UUID_MS_USER = msu.UUID_MS_USER
						 where msu.UUID_MS_USER = :uuid
						 AND DAILY_DATE = CONVERT(DATE, CURRENT_TIMESTAMP))
				END as "value"
			UNION
			SELECT 'total_received' as "key",
				CASE
					WHEN (SELECT count(1) from TR_COLLDAILYSUMMARY cds with (nolock) inner join AM_MSUSER msu with (nolock)
							on cds.UUID_MS_USER = msu.UUID_MS_USER
						 where msu.UUID_MS_USER = :uuid 
						 AND DAILY_DATE = CONVERT(DATE, CURRENT_TIMESTAMP)) = 0 
					THEN '0'
					ELSE (SELECT isnull(cast(cds.TOTAL_PAID as varchar), '') as "value"
						from TR_COLLDAILYSUMMARY cds with (nolock) inner join AM_MSUSER msu with (nolock)
							on cds.UUID_MS_USER = msu.UUID_MS_USER
						 where msu.UUID_MS_USER = :uuid 
						 AND DAILY_DATE = CONVERT(DATE, CURRENT_TIMESTAMP))
				END as "value"
			UNION
			SELECT 'current_date' as "key",
				CASE
					WHEN (SELECT count(1) from TR_COLLDAILYSUMMARY cds with (nolock) inner join AM_MSUSER msu with (nolock)
							on cds.UUID_MS_USER = msu.UUID_MS_USER
						 where msu.UUID_MS_USER = :uuid 
						 AND DAILY_DATE = CONVERT(DATE, CURRENT_TIMESTAMP)) = 0 
					THEN CONVERT(VARCHAR,CONVERT(DATE, CURRENT_TIMESTAMP))
					ELSE (SELECT isnull(replace(convert(varchar(10),getDate(),103),'/','-'), '') as "value"
						from TR_COLLDAILYSUMMARY cds with (nolock) inner join AM_MSUSER msu with (nolock)
							on cds.UUID_MS_USER = msu.UUID_MS_USER
						 where msu.UUID_MS_USER = :uuid 
						 AND DAILY_DATE = CONVERT(DATE, CURRENT_TIMESTAMP))
				END as "value"
			UNION
			SELECT 'collector' as "key",
				CASE
					WHEN (SELECT count(1) from TR_COLLDAILYSUMMARY cds with (nolock) inner join AM_MSUSER msu with (nolock)
						on cds.UUID_MS_USER = msu.UUID_MS_USER
					  where msu.UUID_MS_USER = :uuid AND DAILY_DATE = CONVERT(DATE, CURRENT_TIMESTAMP)) = 0 
					THEN (SELECT msu.FULL_NAME FROM AM_MSUSER msu with (nolock) WHERE msu.UUID_MS_USER = :uuid)
					ELSE (SELECT isnull(msu.FULL_NAME, '') as "value"
					  from TR_COLLDAILYSUMMARY cds with (nolock) inner join AM_MSUSER msu with (nolock)
						on cds.UUID_MS_USER = msu.UUID_MS_USER
					  where msu.UUID_MS_USER = :uuid AND DAILY_DATE = CONVERT(DATE, CURRENT_TIMESTAMP))
				END as "value" 
	</sql-query>
	
	<sql-query name="services.collection.submitTaskReport">
		<query-param name="failed" type="String"/>
		<query-param name="start" type="String"/>
		<query-param name="end" type="String"/>
		<query-param name="uuid" type="String"/>
			select	th.AGREEMENT_NO,
					category.COLL_RESULT_CATEGORY_DESC as status,
					taskcol.PAYMENT_RECEIVED as amount_paid,
					taskcol.PTP_DATE,
					CASE  category.COLL_RESULT_CATEGORY_CODE
						WHEN :failed THEN colres.RESULT_DESC 
						ELSE '-' 
					END as note
			from TR_TASKCOLLDATA taskcol
				join tr_task_h th on th.uuid_task_h = taskcol.UUID_TASK_ID
				join MS_COLLECTIONRESULT colres on taskcol.RESULT_CODE = colres.RESULT_CODE
				join MS_COLLRESULTCATEGORY category on category.UUID_COLL_RESULT_CATEGORY = colres.UUID_COLL_RESULT_CATEGORY
				join am_msuser amu with (nolock) on th.UUID_MS_USER = amu.UUID_MS_USER
				join am_mssubsystem ams on amu.uuid_ms_subsystem = ams.uuid_ms_subsystem
				join MS_STATUSTASK stat on stat.UUID_STATUS_TASK = th.UUID_STATUS_TASK
			where th.dtm_crt BETWEEN :start AND :end
					and ams.subsystem_name = 'MC'
					and amu.uuid_ms_user = :uuid
					and stat.STATUS_CODE = 'S'
	</sql-query>

</hibernate-mapping>