<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="services.collection.taskColl.getTaskLog">
		<query-param name="uuidUser" type="String"/>
			SELECT 
				TTH.UUID_TASK_H uuidTaskH,
				TTH.CUSTOMER_NAME customerName,
				TTH.CUSTOMER_PHONE customerPhone, 
				TTH.CUSTOMER_ADDRESS customerAddress,
				TTH.NOTES notes,
				TTH.LATITUDE latitude,
				TTH.LONGITUDE longitude,
				TTH.ASSIGN_DATE assignmentDate,
				MP.PRIORITY_DESC priority,
				TTH.UUID_FORM schemeId,
				msf.form_last_update schemeLastUpdate,
				TTH.AGREEMENT_NO applNo,
				dbo.F_IS_VERIFICATION(TTH.UUID_TASK_H) as isVerification,
				case when msf.preprocessing_sp is null then '0' else '1' end as isPreviewServer,
				TTH.dtm_crt as dtmCrt,
				ISNULL(msf.is_printable,0) as isPrintable,
				TTH.task_id as taskId,
				TTH.SUBMIT_DATE as submitDate,
				ISNULL(TTH.PRINT_COUNT, '0') as printCount,
				TTH.RV_NUMBER as rvNum,
				'1' as flag,
				convert(varchar(5),TTH.form_version) as form_version
			FROM TR_TASK_H TTH with (nolock)
				JOIN TR_TASKCOLLDATA TTCD with (nolock) ON TTH.UUID_TASK_H = TTCD.UUID_TASK_ID
				JOIN MS_PRIORITY MP with (nolock) on MP.uuid_priority = TTH.uuid_priority
				join ms_form msf with (nolock) ON TTH.uuid_form = msf.uuid_form
				left join tr_depositreport_d tdd with (nolock) on tdd.uuid_task_h = tth.uuid_task_h
			WHERE TTH.UUID_MS_USER = :uuidUser AND
				TTCD.PAYMENT_RECEIVED IS NOT NULL
				AND UUID_DEPOSIT_REPORT_D IS NULL

			UNION ALL

			SELECT 
				FTTH.UUID_TASK_H uuidTaskH,
				FTTH.CUSTOMER_NAME customerName,
				FTTH.CUSTOMER_PHONE customerPhone, 
				FTTH.CUSTOMER_ADDRESS customerAddress,
				FTTH.NOTES notes,
				FTTH.LATITUDE latitude,
				FTTH.LONGITUDE longitude,
				FTTH.ASSIGN_DATE assignmentDate,
				MP.PRIORITY_DESC priority,
				FTTH.UUID_FORM schemeId,
				msf.form_last_update schemeLastUpdate,
				FTTH.AGREEMENT_NO applNo,
				dbo.F_IS_VERIFICATION(FTTH.UUID_TASK_H) as isVerification,
				case when msf.preprocessing_sp is null then '0' else '1' end as isPreviewServer,
				FTTH.dtm_crt as dtmCrt,
				ISNULL(msf.is_printable,0) as isPrintable,
				FTTH.task_id as taskId,
				FTTH.SUBMIT_DATE as submitDate,
				ISNULL(FTTH.PRINT_COUNT, '0') as printCount,
				FTTH.RV_NUMBER as rvNum,
				'2' as flag,
				convert(varchar(5),FTTH.form_version) as form_version
			FROM FINAL_TR_TASK_H FTTH with (nolock)
				JOIN FINAL_TR_TASKCOLLDATA FTTCD with (nolock) ON FTTH.UUID_TASK_H = FTTCD.UUID_TASK_ID
				JOIN MS_PRIORITY MP with (nolock) on MP.uuid_priority = FTTH.uuid_priority
				join ms_form msf with (nolock) ON FTTH.uuid_form = msf.uuid_form
				left join final_tr_depositreport_d ftdd with (nolock) on ftdd.uuid_task_h = ftth.uuid_task_h
			WHERE FTTH.UUID_MS_USER = :uuidUser AND
				FTTCD.PAYMENT_RECEIVED IS NOT NULL
				AND UUID_DEPOSIT_REPORT_D IS NULL
	</sql-query>
	
	<sql-query name="services.collection.taskColl.getTaskLogDetailD">
		<query-param name="uuidTaskH" type="long"/>
			select cast(td.UUID_TASK_D as varchar) as uuidTaskD,
				   cast(qg.UUID_QUESTION_GROUP as varchar) as uuidQuestionGroup,
				   cast(td.UUID_QUESTION as varchar) as uuidQuestion,
				   ISNULL(td.TEXT_ANSWER, '') as textAnswer,
				   ISNULL(cast(td.LOV_ID as varchar), '') as lovId,
				   '' as tagName,
				   l.CODE as code,
				   cast(td.UUID_TASK_H as varchar) as uuidTaskH,
				   td.QUESTION_TEXT as questionText,
				   cast(td.LATITUDE as varchar) as latitude,
				   cast(td.LONGITUDE as varchar) as longitude,
				   cast(td.MCC as varchar) as mcc,
				   cast(td.MNC as varchar) as mnc,
				   cast(td.LAC as varchar) as lac,
				   cast(td.CELL_ID as varchar)as cid,
				   replace(convert(varchar(10),td.TIMESTAMP_TASK,103),'/','')+replace(convert(varchar(8),td.TIMESTAMP_TASK,14),':','') as timestackTask,
				   td.ACCURACY as accuracy,
				   td.regex as regex,
				   td.is_readonly as isReadonly
			  from TR_TASK_D td with (nolock)
			  	   inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
				   inner join MS_QUESTION q with (nolock) on td.UUID_QUESTION = q.UUID_QUESTION
				   left join MS_ANSWERTYPE mat with (nolock) ON q.UUID_ANSWER_TYPE =mat.UUID_ANSWER_TYPE
				   left join MS_QUESTIONOFGROUP qg with (nolock) on td.UUID_QUESTION = qg.UUID_QUESTION
				   left join MS_QUESTIONGROUPOFFORM qgof with (nolock) ON qg.UUID_QUESTION_GROUP = qgof.UUID_QUESTION_GROUP
				   left join MS_LOV l with (nolock) on td.LOV_ID = l.UUID_LOV
			 where td.UUID_TASK_H = :uuidTaskH 
			 	AND qgof.UUID_FORM = th.UUID_FORM
			   and mat.code_answer_type in ('001','002','003','004','005','006','007','008','009','010','011','012','013','015','024','025','026')
			 order by qgof.LINE_SEQ_ORDER, qg.SEQ_ORDER
	</sql-query>
	
	<sql-query name="services.collection.taskColl.getTaskLogDetailDFinal">
		<query-param name="uuidTaskH" type="long"/>
			select cast(fttd.UUID_TASK_D as varchar) as uuidTaskD,
				   cast(qg.UUID_QUESTION_GROUP as varchar) as uuidQuestionGroup,
				   cast(fttd.UUID_QUESTION as varchar) as uuidQuestion,
				   ISNULL(fttd.TEXT_ANSWER, '') as textAnswer,
				   ISNULL(cast(fttd.LOV_ID as varchar), '') as lovId,
				   '' as tagName,
				   l.CODE as code,
				   cast(fttd.UUID_TASK_H as varchar) as uuidTaskH,
				   fttd.QUESTION_TEXT as questionText,
				   cast(fttd.LATITUDE as varchar) as latitude,
				   cast(fttd.LONGITUDE as varchar) as longitude,
				   cast(fttd.MCC as varchar) as mcc,
				   cast(fttd.MNC as varchar) as mnc,
				   cast(fttd.LAC as varchar) as lac,
				   cast(fttd.CELL_ID as varchar)as cid,
				   replace(convert(varchar(10),fttd.TIMESTAMP_TASK,103),'/','')+replace(convert(varchar(8),fttd.TIMESTAMP_TASK,14),':','') as timestackTask,
				   fttd.ACCURACY as accuracy,
				   fttd.regex as regex,
				   fttd.is_readonly as isReadonly
			  from FINAL_TR_TASK_D fttd with (nolock)
			  	   inner join FINAL_TR_TASK_H ftth with (nolock) on fttd.UUID_TASK_H = ftth.UUID_TASK_H
				   inner join MS_QUESTION q with (nolock) on fttd.UUID_QUESTION = q.UUID_QUESTION
				   left join MS_ANSWERTYPE mat with (nolock) ON q.UUID_ANSWER_TYPE =mat.UUID_ANSWER_TYPE
				   left join MS_QUESTIONOFGROUP qg with (nolock) on fttd.UUID_QUESTION = qg.UUID_QUESTION
				   left join MS_QUESTIONGROUPOFFORM qgof with (nolock) ON qg.UUID_QUESTION_GROUP = qgof.UUID_QUESTION_GROUP
				   left join MS_LOV l with (nolock) on fttd.LOV_ID = l.UUID_LOV
			 where fttd.UUID_TASK_H = :uuidTaskH
			 	AND qgof.UUID_FORM = ftth.UUID_FORM
			    and mat.code_answer_type in ('001','002','003','004','005','006','007','008','009','010','011','012','013','015','024','025','026')
			    order by qgof.LINE_SEQ_ORDER, qg.SEQ_ORDER
	</sql-query>
	
	
	<sql-query name="services.collection.taskColl.getTaskLogDetailL">
		<query-param name="uuidTaskH" type="long"/>
			select cast(tdl.UUID_TASK_DETAIL_LOB as varchar) as uuidTaskD,
				   cast(qg.UUID_QUESTION_GROUP as varchar) as uuidQuestionGroup,
				   cast(tdl.QUESTION_ID as varchar) as uuidQuestion,
				   'hasImage' as textAnswer,
				   cast(tdl.LOV_ID as varchar) as lovId,
				   '' as tagName,
				   l.CODE as code,
				   cast(tdl.UUID_TASK_H as varchar) as uuidTaskH,
				   tdl.QUESTION_TEXT as questionText,
				   cast(tdl.LATITUDE as varchar) as latitude,
				   cast(tdl.LONGITUDE as varchar) as longitude,
				   cast(tdl.MCC as varchar) as mcc,
				   cast(tdl.MNC as varchar) as mnc,
				   cast(tdl.LAC as varchar) as lac,
				   cast(tdl.CELL_ID as varchar)as cid,
				   replace(convert(varchar(10),tdl.TIMESTAMP_DETAIL,103),'/','')+replace(convert(varchar(8),tdl.TIMESTAMP_DETAIL,14),':','') as timestackTask,
				   tdl.ACCURACY as accuracy,
				   '1' as image,
				   tdl.image_path as imgPath
			  from TR_TASKDETAILLOB tdl with (nolock) inner join TR_TASK_H th on tdl.UUID_TASK_H = th.UUID_TASK_H
				   inner join MS_QUESTION q with (nolock) on tdl.QUESTION_ID = q.UUID_QUESTION
				   left join MS_ANSWERTYPE mat with (nolock) ON q.UUID_ANSWER_TYPE =mat.UUID_ANSWER_TYPE
				   left join MS_QUESTIONOFGROUP qg with (nolock) on tdl.QUESTION_ID = qg.UUID_QUESTION
				   left join MS_QUESTIONGROUPOFFORM qgof with (nolock) ON qg.UUID_QUESTION_GROUP = qgof.UUID_QUESTION_GROUP
				   left join MS_LOV l with (nolock) on tdl.LOV_ID = l.UUID_LOV
			 where tdl.UUID_TASK_H = :uuidTaskH
			   and mat.code_answer_type in ('016','017','018','021')
	</sql-query>
	
	<sql-query name="services.collection.taskColl.getTaskLogDetailLFinal">
		<query-param name="uuidTaskH" type="long"/>
			select cast(ftdl.UUID_TASK_DETAIL_LOB as varchar) as uuidTaskD,
				   cast(qg.UUID_QUESTION_GROUP as varchar) as uuidQuestionGroup,
				   cast(ftdl.QUESTION_ID as varchar) as uuidQuestion,
				   'hasImage' as textAnswer,
				   cast(ftdl.LOV_ID as varchar) as lovId,
				   '' as tagName,
				   l.CODE as code,
				   cast(ftdl.UUID_TASK_H as varchar) as uuidTaskH,
				   ftdl.QUESTION_TEXT as questionText,
				   cast(ftdl.LATITUDE as varchar) as latitude,
				   cast(ftdl.LONGITUDE as varchar) as longitude,
				   cast(ftdl.MCC as varchar) as mcc,
				   cast(ftdl.MNC as varchar) as mnc,
				   cast(ftdl.LAC as varchar) as lac,
				   cast(ftdl.CELL_ID as varchar)as cid,
				   replace(convert(varchar(10),ftdl.TIMESTAMP_DETAIL,103),'/','')+replace(convert(varchar(8),ftdl.TIMESTAMP_DETAIL,14),':','') as timestackTask,
				   ftdl.ACCURACY as accuracy,
				   '1' as image,
				   ftdl.image_path as imgPath
			  from FINAL_TR_TASKDETAILLOB ftdl with (nolock) inner join TR_TASK_H th on ftdl.UUID_TASK_H = th.UUID_TASK_H
				   inner join MS_QUESTION q with (nolock) on ftdl.QUESTION_ID = q.UUID_QUESTION
				   left join MS_ANSWERTYPE mat with (nolock) ON q.UUID_ANSWER_TYPE =mat.UUID_ANSWER_TYPE
				   left join MS_QUESTIONOFGROUP qg with (nolock) on ftdl.QUESTION_ID = qg.UUID_QUESTION
				   left join MS_LOV l with (nolock) on ftdl.LOV_ID = l.UUID_LOV
			 where ftdl.UUID_TASK_H = :uuidTaskH
			   and mat.code_answer_type in ('016','017','018','021')
	</sql-query>
</hibernate-mapping>