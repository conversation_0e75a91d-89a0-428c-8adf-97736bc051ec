<?xml version="1.0"?>
<!DOCTY<PERSON>E hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>	
	<sql-query name="task.inquirytaskcollection.getStatusListCombo">
    <query-param name="subsystemId" type="long" />
		SELECT UUID_STATUS_TASK, 
			STATUS_TASK_DESC
		 FROM MS_STATUSTASK
		WHERE UUID_MS_SUBSYSTEM = :subsystemId AND IS_ACTIVE = '1'
	</sql-query>
	
	<sql-query name="task.inquirytaskcollection.getHeader">
    <query-param name="uuidTask" type="long" />
		SELECT trth.UUID_TASK_H, 
			trth.TASK_ID, 
			trth.AGREEMENT_NO, 
			msf.FORM_NAME, 
			msb.<PERSON><PERSON>CH_NAME,
			msp.PRIORITY_DESC, 
			trth.NOTES, 
			trth.RESULT, 
			trth.CUSTOMER_NAME, 
			trth.CUSTOMER_ADDRESS, 
			trth.CUSTOMER_PHONE, 
			trth.ZIP_CODE, 
			LEFT(CONVERT(VARCHAR, trth.ASSIGN_DATE, 113), 17) as assign_date, 
			isnull(LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17),'-') as submit_date, 
			ammsu.FULL_NAME, 
			msst.STATUS_TASK_DESC,
			'' as VOICE_NOTE,
			isnull(LEFT(CONVERT(VARCHAR, trth.send_date, 113),17),'-') as send_date
		FROM TR_TASK_H trth with (nolock) left join TR_TASKORDERDATA trtod with (nolock) 
				on trth.UUID_TASK_H = trtod.UUID_TASK_ID
			left join MS_BRANCH msb with (nolock) 
				on trth.UUID_BRANCH = msb.UUID_BRANCH
			left join MS_FORM msf with (nolock) 
				on trth.UUID_FORM = msf.UUID_FORM
			LEFT JOIN MS_PRIORITY msp with (nolock) 
				on trth.UUID_PRIORITY = msp.UUID_PRIORITY
			left join MS_STATUSTASK msst with (nolock)  
				on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			left join AM_MSUSER ammsu with (nolock) 
				on ammsu.UUID_MS_USER = trth.UUID_MS_USER
		WHERE trth.UUID_TASK_H = :uuidTask
	</sql-query>
	
	<sql-query name="task.inquirytaskcollection.getBranchListCombo">
    <query-param name="branchId" type="long" />
		select keyValue, BRANCH_CODE + '-' + BRANCH_NAME 
		from dbo.getCabangByLogin(:branchId)
	</sql-query>
	
	<sql-query name="task.inquirytaskcollection.getHistory">
    <query-param name="uuidTask" type="long" />
		SELECT CASE 
				WHEN trths.CODE_PROCESS = '001' THEN 'Assignment' 
				WHEN trths.CODE_PROCESS = '002' THEN 'Re-assignment'
				WHEN trths.CODE_PROCESS = '003' THEN 'Submitted' 
				WHEN trths.CODE_PROCESS = '004' THEN 'Verified' 
				WHEN trths.CODE_PROCESS = '005' THEN 'Approval' 
				WHEN trths.CODE_PROCESS = '006' THEN 'Released'
				WHEN trths.code_process = '007' THEN 'Deleted'
				WHEN trths.CODE_PROCESS = '008' THEN 'Rejected'
				WHEN trths.CODE_PROCESS = '009' THEN 'Rejected on Verification'
				WHEN trths.CODE_PROCESS = '010' THEN 'Rejected on Approval'
				WHEN trths.CODE_PROCESS = '011' THEN 'On Core'
				WHEN trths.CODE_PROCESS = '015' THEN 'Unassigned'
				WHEN trths.CODE_PROCESS = '013' THEN 'Rejected with Re-Survey'
				WHEN trths.CODE_PROCESS = '014' THEN 'Promise to Survey'
				WHEN trths.CODE_PROCESS = '012' THEN 'Canceled'
				WHEN trths.CODE_PROCESS = '016' THEN 'Revisit'
			END TASK_PROCESS,
			trths.ACTOR, trths.FIELD_PERSON,
			trths.DTM_CRT,
			trths.NOTES, msst.STATUS_TASK_DESC, trths.uuid_task_h, trths.CODE_PROCESS, '1' AS FLAG,
			trths.UUID_TASK_REJECTED_HISTORY
		FROM TR_TASKHISTORY trths with (nolock)
			LEFT JOIN MS_STATUSTASK msst with (nolock) ON trths.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
		WHERE trths.UUID_TASK_H = :uuidTask
		ORDER BY DTM_CRT desc
	</sql-query>
	
	<sql-query name="task.inquirytaskcollection.getHeaderFinal">
    <query-param name="uuidTask" type="long" />
		SELECT trth.UUID_TASK_H, 
			trth.TASK_ID, 
			trth.AGREEMENT_NO, 
			msf.FORM_NAME, 
			msb.BRANCH_NAME,
			msp.PRIORITY_DESC, 
			trth.NOTES, 
			trth.RESULT, 
			trth.CUSTOMER_NAME, 
			trth.CUSTOMER_ADDRESS, 
			trth.CUSTOMER_PHONE, 
			trth.ZIP_CODE, 
			LEFT(CONVERT(VARCHAR, trth.ASSIGN_DATE, 113), 17) as assign_date, 
			isnull(LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17),'-') as submit_date, 
			ammsu.FULL_NAME, 
			msst.STATUS_TASK_DESC,
			'' as VOICE_NOTE,
			isnull(LEFT(CONVERT(VARCHAR, trth.send_date, 113),17),'-') as send_date
		FROM FINAL_TR_TASK_H trth with (nolock) left join FINAL_TR_TASKORDERDATA trtod with (nolock) 
				on trth.UUID_TASK_H = trtod.UUID_TASK_ID
			left join MS_BRANCH msb with (nolock) 
				on trth.UUID_BRANCH = msb.UUID_BRANCH
			left join MS_FORM msf with (nolock) 
				on trth.UUID_FORM = msf.UUID_FORM
			LEFT JOIN MS_PRIORITY msp with (nolock) 
				on trth.UUID_PRIORITY = msp.UUID_PRIORITY
			left join MS_STATUSTASK msst with (nolock)  
				on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			left join AM_MSUSER ammsu with (nolock) 
				on ammsu.UUID_MS_USER = trth.UUID_MS_USER
		WHERE trth.UUID_TASK_H = :uuidTask
	</sql-query>
	
	<sql-query name="task.inquirytaskcollection.getHistoryFinal">
    <query-param name="uuidTask" type="long" />
		SELECT CASE 
				WHEN trths.CODE_PROCESS = '001' THEN 'Assignment' 
				WHEN trths.CODE_PROCESS = '002' THEN 'Re-assignment'
				WHEN trths.CODE_PROCESS = '003' THEN 'Submitted' 
				WHEN trths.CODE_PROCESS = '004' THEN 'Verified' 
				WHEN trths.CODE_PROCESS = '005' THEN 'Approval' 
				WHEN trths.CODE_PROCESS = '006' THEN 'Released'
				WHEN trths.code_process = '007' THEN 'Deleted'
				WHEN trths.CODE_PROCESS = '008' THEN 'Rejected'
				WHEN trths.CODE_PROCESS = '009' THEN 'Rejected on Verification'
				WHEN trths.CODE_PROCESS = '010' THEN 'Rejected on Approval'
				WHEN trths.CODE_PROCESS = '011' THEN 'On Core'
				WHEN trths.CODE_PROCESS = '015' THEN 'Unassigned'
				WHEN trths.CODE_PROCESS = '013' THEN 'Rejected with Re-Survey'
				WHEN trths.CODE_PROCESS = '014' THEN 'Promise to Survey'
				WHEN trths.CODE_PROCESS = '012' THEN 'Canceled'
				WHEN trths.CODE_PROCESS = '016' THEN 'Revisit'
			END TASK_PROCESS,
			trths.ACTOR, trths.FIELD_PERSON,
			trths.DTM_CRT,
			trths.NOTES, msst.STATUS_TASK_DESC, trths.uuid_task_h, trths.CODE_PROCESS, '2' AS FLAG,
			trths.UUID_TASK_REJECTED_HISTORY
		FROM FINAL_TR_TASKHISTORY trths with (nolock) 
			LEFT JOIN MS_STATUSTASK msst with (nolock) ON trths.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
		WHERE trths.UUID_TASK_H = :uuidTask
		ORDER BY DTM_CRT desc
	</sql-query>
	
	<sql-query name="task.inquirytaskcollection.answerHistoryRejected2">
		<query-param name="paramUuidTaskH" type="long" />
		SELECT ISNULL(rejdet.question_text, ''), ISNULL(rejdet.text_answer, ''), ISNULL(rejdet.OPTION_TEXT, ''),
			CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND rejdet.IMAGE_BLOB IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND rejdet.IMAGE_BLOB IS NULL) THEN '0'
				ELSE '10'
			END AS HAS_IMAGE,
			rejdet.IMAGE_BLOB, rejdet.LATITUDE, rejdet.LONGITUDE, rejdet.UUID_TASK_REJECTED_DETAIL
		 FROM TR_TASKREJECTEDDETAIL rejdet with (nolock)
		 JOIN TR_TASK_H trth with (nolock) on rejdet.UUID_TASK = trth.UUID_TASK_H
		 JOIN MS_FORMHISTORY mfh with (nolock) on mfh.uuid_form = trth.uuid_form and mfh.form_version = trth.form_version
		 JOIN MS_FORMQUESTIONSET mfqs with (nolock) on mfqs.uuid_form_history = mfh.uuid_form_history
		 and rejdet.QUESTION_ID = mfqs.UUID_QUESTION
		 JOIN MS_ANSWERTYPE msan with (nolock) on msan.uuid_answer_type = mfqs.UUID_ANSWER_TYPE 
		 WHERE rejdet.UUID_TASK = :paramUuidTaskH
		 ORDER BY mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ
	</sql-query>
	
	<sql-query name="task.inquirytaskcollection.priorityList">
		SELECT UUID_PRIORITY, PRIORITY_DESC
		FROM MS_PRIORITY with (nolock)
		WHERE IS_ACTIVE = '1'
		ORDER BY UUID_PRIORITY ASC
	</sql-query>
	
	<sql-query name="task.inquirytaskcollection.getAnswerQSet">
    <query-param name="uuidTask" type="long" />
	SELECT UUID_TASK_H,
		   ISNULL(rd.QUESTION_TEXT, mfqs.QUESTION_LABEL) as QUESTION_TEXT, ISNULL(TEXT_ANSWER, '') as TEXT_ANSWER, ISNULL(OPTION_TEXT, '') as OPTION_TEXT,
		   CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND IMAGE_PATH IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NULL) THEN '0'
				WHEN (msan.code_answer_type = '024') THEN '01'
				ELSE '10'
		   END AS HAS_IMAGE,
		   LATITUDE, LONGITUDE, LOB_FILE, ISNULL(UUID_TASK_DETAIL_LOB, '') as UUID_TASK_DETAIL_LOB,
		   msan.code_answer_type,
		   ISNULL(INT_TEXT_ANSWER, '') as INT_TEXT_ANSWER, ISNULL(INT_OPTION_TEXT, '') as INT_OPTION_TEXT,
		   ACCURACY, '1' AS FLAG, msct.TAG_NAME as TAG,
		   CASE WHEN msct.TAG_NAME = 'RV NUMBER' THEN (select RV_NUMBER FROM TR_TASK_H trth WHERE trth.UUID_TASK_H = rd.UUID_TASK_H)
		   		ELSE NULL END as RV
	FROM (
		SELECT td.UUID_TASK_H, UUID_QUESTION as uuidQuestion,
			   QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
			   IMAGE_PATH, td.LATITUDE, td.LONGITUDE, NULL AS LOB_FILE, NULL AS UUID_TASK_DETAIL_LOB,
			   INT_TEXT_ANSWER, INT_OPTION_TEXT,
			   FIN_TEXT_ANSWER, FIN_OPTION_TEXT,
			   NULL AS LATBRANCH, NULL AS LNGBRANCH,
			   MCC, MNC, LAC, CELL_ID, ACCURACY, td.DTM_CRT, th.UUID_FORM, th.FORM_VERSION
		  FROM TR_TASK_D td with (nolock) inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
		 WHERE td.UUID_TASK_H = :uuidTask
		UNION
		SELECT td.UUID_TASK_H, QUESTION_ID as uuidQuestion,
			   QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
			   IMAGE_PATH, td.LATITUDE, td.LONGITUDE, '1' AS LOB_FILE, UUID_TASK_DETAIL_LOB AS UUID_TASK_DETAIL_LOB,
			   INT_TEXT_ANSWER, INT_OPTION_TEXT,
			   FIN_TEXT_ANSWER, FIN_OPTION_TEXT,
			   NULL AS LATBRANCH, NULL AS LNGBRANCH,
			   MCC, MNC, LAC, CELL_ID, ACCURACY, td.DTM_CRT, th.UUID_FORM, th.FORM_VERSION
		  FROM TR_TASKDETAILLOB td with (nolock) inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
		 WHERE td.UUID_TASK_H = :uuidTask
	) rd
	INNER JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = rd.UUID_FORM AND mfh.FORM_VERSION = rd.FORM_VERSION
		  INNER JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
		  and mfqs.UUID_QUESTION = rd.uuidQuestion
		  INNER JOIN MS_ANSWERTYPE msan with (nolock) ON mfqs.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
	  LEFT JOIN MS_COLLECTIONTAG msct with (nolock) ON msct.UUID_COLLECTION_TAG = mfqs.UUID_COLLECTION_TAG
	WHERE mfqs.QUESTION_GROUP_IS_ACTIVE = '1' 
		AND mfqs.IS_VISIBLE ='1'
	ORDER BY mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ
	</sql-query>
	
	<sql-query name="task.inquirytaskcollection.getAnswerFinalQSet">
    <query-param name="uuidTask" type="long" />
	SELECT UUID_TASK_H,
		   ISNULL(rd.QUESTION_TEXT, mfqs.QUESTION_LABEL) as QUESTION_TEXT, ISNULL(TEXT_ANSWER, '') as TEXT_ANSWER, ISNULL(OPTION_TEXT, '') as OPTION_TEXT,
		   CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND IMAGE_PATH IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NULL) THEN '0'
				WHEN (msan.code_answer_type = '024') THEN '01'
				ELSE '10'
		   END AS HAS_IMAGE,
		   LATITUDE, LONGITUDE, LOB_FILE, ISNULL(UUID_TASK_DETAIL_LOB, '') as UUID_TASK_DETAIL_LOB,
		   msan.code_answer_type,
		   ISNULL(INT_TEXT_ANSWER, '') as INT_TEXT_ANSWER, ISNULL(INT_OPTION_TEXT, '') as INT_OPTION_TEXT,
		   ACCURACY, '1' AS FLAG, msct.TAG_NAME as TAG,
		   CASE WHEN msct.TAG_NAME = 'RV NUMBER' THEN (select RV_NUMBER FROM TR_TASK_H trth WHERE trth.UUID_TASK_H = rd.UUID_TASK_H)
		   		ELSE NULL END as RV
	FROM (
		SELECT td.UUID_TASK_H, UUID_QUESTION as uuidQuestion,
			   QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
			   IMAGE_PATH, td.LATITUDE, td.LONGITUDE, NULL AS LOB_FILE, NULL AS UUID_TASK_DETAIL_LOB,
			   INT_TEXT_ANSWER, INT_OPTION_TEXT,
			   FIN_TEXT_ANSWER, FIN_OPTION_TEXT,
			   NULL AS LATBRANCH, NULL AS LNGBRANCH,
			   MCC, MNC, LAC, CELL_ID, ACCURACY, td.DTM_CRT, th.UUID_FORM, th.FORM_VERSION
		  FROM FINAL_TR_TASK_D td with (nolock) inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
		 WHERE td.UUID_TASK_H = :uuidTask
		UNION
		SELECT td.UUID_TASK_H, QUESTION_ID as uuidQuestion,
			   QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
			   IMAGE_PATH, td.LATITUDE, td.LONGITUDE, '1' AS LOB_FILE, UUID_TASK_DETAIL_LOB AS UUID_TASK_DETAIL_LOB,
			   INT_TEXT_ANSWER, INT_OPTION_TEXT,
			   FIN_TEXT_ANSWER, FIN_OPTION_TEXT,
			   NULL AS LATBRANCH, NULL AS LNGBRANCH,
			   MCC, MNC, LAC, CELL_ID, ACCURACY, td.DTM_CRT, th.UUID_FORM, th.FORM_VERSION
		  FROM FINAL_TR_TASKDETAILLOB td with (nolock) inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
		 WHERE td.UUID_TASK_H = :uuidTask
	) rd
	INNER JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = rd.UUID_FORM AND mfh.FORM_VERSION = rd.FORM_VERSION
		  INNER JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
		  and mfqs.UUID_QUESTION = rd.uuidQuestion
		  INNER JOIN MS_ANSWERTYPE msan with (nolock) ON mfqs.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
	  LEFT JOIN MS_COLLECTIONTAG msct with (nolock) ON msct.UUID_COLLECTION_TAG = mfqs.UUID_COLLECTION_TAG
	WHERE mfqs.QUESTION_GROUP_IS_ACTIVE = '1' 
			AND mfqs.IS_VISIBLE ='1'
	ORDER BY mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ
	</sql-query>
</hibernate-mapping>