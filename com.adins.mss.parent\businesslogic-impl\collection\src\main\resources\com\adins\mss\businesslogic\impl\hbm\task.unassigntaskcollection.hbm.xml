<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

 	<!-- ////// get total task assignment ////// -->
	<sql-query name="task.unassigntaskcollection.getTotalTaskAssignment">
		<query-param name="uuidMsUser" type="long"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
			SELECT COUNT(UUID_TASK_H) taskAssignment
			FROM TR_TASK_H with (nolock)
			WHERE ASSIGN_DATE IS NOT NULL 
			AND ASSIGN_DATE BETWEEN :start AND :end
			AND UUID_MS_USER = :uuidMsUser
	</sql-query>
	
	<!-- ////// get total submitted task ////// -->
	<sql-query name="task.unassigntaskcollection.getTotalSubmittedTask">
		<query-param name="uuidMsUser" type="long"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
			SELECT COUNT(UUID_TASK_H) submittedTask
			FROM TR_TASK_H with (nolock)
			WHERE SUBMIT_DATE IS NOT NULL 
			AND SUBMIT_DATE BETWEEN :start AND :end
			AND UUID_MS_USER = :uuidMsUser
	</sql-query>
	
	<!-- ////// get total pending task ////// -->
	<sql-query name="task.unassigntaskcollection.getTotalPendingTask">
		<query-param name="uuidMsUser" type="long"/>
		<query-param name="uuidStatusTask" type="long"/>
			SELECT COUNT(1)
			FROM TR_TASK_H with (nolock)
			WHERE UUID_MS_USER = :uuidMsUser
				AND UUID_STATUS_TASK = :uuidStatusTask
	</sql-query>
	
	<!-- ////// get Last Location ////// -->
	<sql-query name="task.unassigntaskcollection.getLastLoc">
		<query-param name="uuidMsUser" type="long"/>
			select TOP 1 * from TR_LOCATIONHISTORY with (nolock) where UUID_MS_USER = :uuidMsUser order by datetime DESC
	</sql-query>
	
	<sql-query name="task.unassigntaskcollection.getSpvList">
		<query-param name="uuidBranch" type="long"/>
		<query-param name="jobCode" type="string"/>
		SELECT amu.UUID_MS_USER, amu.FULL_NAME, amu.IS_LOGGED_IN, amu.LOGIN_ID, amu.INITIAL_NAME
		FROM AM_MSUSER amu with (nolock)
			JOIN MS_JOB mj with (nolock) ON amu.UUID_JOB = mj.UUID_JOB 
				AND amu.UUID_MS_SUBSYSTEM = mj.UUID_MS_SUBSYSTEM
			join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
		WHERE mj.JOB_CODE = :jobCode
		AND amu.IS_ACTIVE = '1'
	</sql-query>

</hibernate-mapping>
