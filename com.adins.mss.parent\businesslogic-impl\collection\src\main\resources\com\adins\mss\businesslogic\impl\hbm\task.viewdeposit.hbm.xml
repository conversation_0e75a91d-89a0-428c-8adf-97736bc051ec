<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="task.viewdeposit.listdetailbatch">
		<query-param name="uuidDepositH" type="long"/>
		<query-param name="start" type="String"/>
		<query-param name="end" type="String"/>
	   SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT *, ROW_NUMBER() OVER (ORDER BY c.UUID_TASK_H desc) AS rownum FROM (
					SELECT distinct trh.UUID_TASK_H, trh.TASK_ID, trh.AGREEMENT_NO, 
						(Select AMOUNT_DUE From TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Total Tagihan',
						(Select PAYMENT_RECEIVED From TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Payment Amount', '1' as Flag
					FROM TR_DEPOSITREPORT_H tdh with (nolock)
						join TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
						join TR_TASK_H trh with (nolock) on tdd.UUID_TASK_H = trh.UUID_TASK_H
					Where tdh.UUID_DEPOSIT_REPORT_H = :uuidDepositH
					UNION ALL
					SELECT distinct trh.UUID_TASK_H, trh.TASK_ID, trh.AGREEMENT_NO, 
						(Select AMOUNT_DUE From FINAL_TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Total Tagihan',
						(Select  PAYMENT_RECEIVED From FINAL_TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Payment Amount', '2' as Flag
					FROM FINAL_TR_DEPOSITREPORT_H tdh with (nolock)
						join FINAL_TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
						join FINAL_TR_TASK_H trh with (nolock) on tdd.UUID_TASK_H = trh.UUID_TASK_H
					Where tdh.UUID_DEPOSIT_REPORT_H = :uuidDepositH
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.viewdeposit.listdetailbatchFinal">
		<query-param name="uuidDepositH" type="long"/>
		<query-param name="start" type="String"/>
		<query-param name="end" type="String"/>
	    SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT *, ROW_NUMBER() OVER (ORDER BY c.UUID_TASK_H desc) AS rownum FROM (
					SELECT distinct trh.UUID_TASK_H, trh.TASK_ID, trh.AGREEMENT_NO, 
						(Select AMOUNT_DUE From FINAL_TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Total Tagihan',
						(Select PAYMENT_RECEIVED From FINAL_TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Payment Amount'
					FROM FINAL_TR_DEPOSITREPORT_H tdh with (nolock)
						join FINAL_TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
						join FINAL_TR_TASK_H trh with (nolock) on tdd.UUID_TASK_H = trh.UUID_TASK_H
					Where tdh.UUID_DEPOSIT_REPORT_H = :uuidDepositH
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.viewdeposit.listdetailbatchCount">
		<query-param name="uuidDepositH" type="long"/>
	SELECT Count(1) FROM (
			SELECT distinct trh.UUID_TASK_H, trh.TASK_ID, trh.AGREEMENT_NO, 
						(Select AMOUNT_DUE From TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Total Tagihan',
						(Select PAYMENT_RECEIVED From TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Payment Amount', '1' as Flag
			FROM TR_DEPOSITREPORT_H tdh with (nolock)
						join TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
						join TR_TASK_H trh with (nolock) on tdd.UUID_TASK_H = trh.UUID_TASK_H
			Where tdh.UUID_DEPOSIT_REPORT_H = :uuidDepositH
			UNION ALL
			SELECT distinct trh.UUID_TASK_H, trh.TASK_ID, trh.AGREEMENT_NO, 
						(Select AMOUNT_DUE From FINAL_TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Total Tagihan',
						(Select PAYMENT_RECEIVED From FINAL_TR_TASKCOLLDATA td with (nolock)
							where td.UUID_TASK_ID = trh.UUID_TASK_H
						) as 'Payment Amount', '2' as Flag
			FROM FINAL_TR_DEPOSITREPORT_H tdh with (nolock)
						join FINAL_TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
						join FINAL_TR_TASK_H trh with (nolock) on tdd.UUID_TASK_H = trh.UUID_TASK_H
			Where tdh.UUID_DEPOSIT_REPORT_H = :uuidDepositH
		) c
	</sql-query>
	
	<sql-query name="task.viewdeposit.listdetailbatchCountFinal">
		<query-param name="uuidDepositH" type="long"/>
	   SELECT Count(1) FROM (
			SELECT distinct trh.UUID_TASK_H, trh.TASK_ID, trh.AGREEMENT_NO, 
				(Select AMOUNT_DUE From FINAL_TR_TASKCOLLDATA td with (nolock)
					where td.UUID_TASK_ID = trh.UUID_TASK_H
				) as 'Total Tagihan',
				(Select PAYMENT_HISTORY From FINAL_TR_TASKCOLLDATA td with (nolock)
					where td.UUID_TASK_ID = trh.UUID_TASK_H
				) as 'Payment Amount'
			FROM FINAL_TR_DEPOSITREPORT_H tdh with (nolock)
				join FINAL_TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
				join FINAL_TR_TASK_H trh with (nolock) on tdd.UUID_TASK_H = trh.UUID_TASK_H
			Where tdh.UUID_DEPOSIT_REPORT_H = :uuidDepositH
		) c
	</sql-query>
	
	<sql-query name="task.viewdeposit.getBranchListCombo">
	    <query-param name="branchId" type="long" />
			select keyValue, BRANCH_CODE + '-' + BRANCH_NAME 
			from dbo.getCabangByLogin(:branchId)
	</sql-query>
	
	<sql-query name="task.viewdeposit.getalllistuser">
		<query-param name="uuidBranch" type="long"/>
		<query-param name="subsystemName" type="string"/>
		SELECT usr.UUID_MS_USER as [key], usr.LOGIN_ID + ' - ' + usr.FULL_NAME as [value] FROM AM_MSUSER usr with (nolock)
			join MS_JOB jb with (nolock) on usr.UUID_JOB = jb.UUID_JOB
			join AM_MSSUBSYSTEM subsys with (nolock) on usr.UUID_MS_SUBSYSTEM = subsys.UUID_MS_SUBSYSTEM
			join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) br on usr.UUID_BRANCH = br.UUID_BRANCH	
		WHERE usr.IS_ACTIVE = 1 AND jb.IS_FIELD_PERSON = 1 
				AND subsys.SUBSYSTEM_NAME = :subsystemName
		ORDER BY usr.FULL_NAME
	</sql-query>  
	
	<!-- penambahan untuk hierarki by user -->
	<sql-query name="task.viewdeposit.listbatchByHierarkiUser">
		<query-param name="collectorName" type="String"/>
		<query-param name="uuidBranch" type="long"/>
		<query-param name="uuidUser" type="long"/>
		<query-param name="startDate" type="String"/>
		<query-param name="endDate" type="String"/>
		<query-param name="currentDate" type="String"/>
		<query-param name="start" type="String"/>
		<query-param name="end" type="String"/>
	    select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT distinct *, ROW_NUMBER() OVER (ORDER BY c.UUID_DEPOSIT_REPORT_H desc) AS rownum FROM (
					SELECT distinct tdh.UUID_DEPOSIT_REPORT_H, tdh.BATCH_ID, br.BRANCH_CODE, usr.FULL_NAME, tdh.BANK_NAME, tdh.BANK_ACCOUNT, tdh.CASHIER_NAME, 
						(Select SUM(DEPOSIT_AMT) from TR_DEPOSITREPORT_D td with (nolock) where td.UUID_DEPOSIT_REPORT_H = tdh.UUID_DEPOSIT_REPORT_H) as Total,
					 	(Case  when tdh.LOB_FILE is null 
								then '0'
								else '1' end) hasImage, '1' as Flag
					FROM TR_DEPOSITREPORT_H tdh with (nolock)
						join TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
						join TR_TASK_H th with (nolock) on tdd.UUID_TASK_H = th.UUID_TASK_H
						join AM_MSUSER usr with (nolock) on th.UUID_MS_USER = usr.UUID_MS_USER
						join MS_BRANCH br with (nolock) on usr.UUID_BRANCH = br.UUID_BRANCH
						join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = usr.UUID_MS_USER
					WHERE 
						lower(br.UUID_BRANCH) = :uuidBranch
						and usr.FULL_NAME like lower('%'+ :collectorName +'%')
						and COALESCE(tdh.TRANSFERRED_DATE, '1990-01-01 00:00:00') BETWEEN (CASE WHEN :startDate = '%' then '1990-01-01 00:00:00' 
							else :startDate END) 
							and (CASE WHEN :endDate = '%' then :currentDate else :endDate END)
					UNION ALL
					SELECT distinct tdh.UUID_DEPOSIT_REPORT_H, tdh.BATCH_ID, br.BRANCH_CODE, usr.FULL_NAME, tdh.BANK_NAME, tdh.BANK_ACCOUNT, tdh.CASHIER_NAME, 
						(Select SUM(DEPOSIT_AMT) from FINAL_TR_DEPOSITREPORT_D td with (nolock) where td.UUID_DEPOSIT_REPORT_H = tdh.UUID_DEPOSIT_REPORT_H) as Total,
					 	(Case  when tdh.LOB_FILE is null 
								then '0'
								else '1' end) hasImage, '2' as Flag
					FROM FINAL_TR_DEPOSITREPORT_H tdh with (nolock)
						join FINAL_TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
						join FINAL_TR_TASK_H th with (nolock) on tdd.UUID_TASK_H = th.UUID_TASK_H
						join AM_MSUSER usr with (nolock) on th.UUID_MS_USER = usr.UUID_MS_USER
						join MS_BRANCH br with (nolock) on usr.UUID_BRANCH = br.UUID_BRANCH
						join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = usr.UUID_MS_USER
					WHERE 
						lower(br.UUID_BRANCH) = :uuidBranch
						and usr.FULL_NAME like lower('%'+ :collectorName +'%')
						and COALESCE(tdh.TRANSFERRED_DATE, '1990-01-01 00:00:00') BETWEEN (CASE WHEN :startDate = '%' then '1990-01-01 00:00:00' 
						else :startDate END) 
						and (CASE WHEN :endDate = '%' then :currentDate else :endDate END)
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.viewdeposit.listbatchCountByHierarkiUser">
		<query-param name="collectorName" type="String"/>
		<query-param name="uuidBranch" type="long"/>
		<query-param name="uuidUser" type="long"/>
		<query-param name="startDate" type="String"/>
		<query-param name="endDate" type="String"/>
		<query-param name="currentDate" type="String"/>
	    SELECT Count(1) FROM (
	    SELECT distinct * FROM (
			SELECT distinct tdh.UUID_DEPOSIT_REPORT_H, tdh.BATCH_ID, br.BRANCH_CODE, usr.FULL_NAME, tdh.BANK_NAME, tdh.BANK_ACCOUNT, tdh.CASHIER_NAME,
						(Select SUM(DEPOSIT_AMT) from TR_DEPOSITREPORT_D td with (nolock) where td.UUID_DEPOSIT_REPORT_H = tdh.UUID_DEPOSIT_REPORT_H) as Total,
					 	(Case  when tdh.LOB_FILE is null 
								then '0'
								else '1' end) hasImage
			FROM TR_DEPOSITREPORT_H tdh with (nolock)
						join TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
						join TR_TASK_H th with (nolock) on tdd.UUID_TASK_H = th.UUID_TASK_H
						join AM_MSUSER usr with (nolock) on th.UUID_MS_USER = usr.UUID_MS_USER
						join MS_BRANCH br with (nolock) on usr.UUID_BRANCH = br.UUID_BRANCH
						join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = usr.UUID_MS_USER
			WHERE lower(br.UUID_BRANCH) = :uuidBranch
						and usr.FULL_NAME like lower('%'+ :collectorName +'%')
						and COALESCE(tdh.TRANSFERRED_DATE, '1990-01-01 00:00:00') BETWEEN (CASE WHEN :startDate = '%' then '1990-01-01 00:00:00' 
						else :startDate END) 
						and (CASE WHEN :endDate = '%' then :currentDate else :endDate END)
			UNION ALL
			SELECT distinct tdh.UUID_DEPOSIT_REPORT_H, tdh.BATCH_ID, br.BRANCH_CODE, usr.FULL_NAME, tdh.BANK_NAME, tdh.BANK_ACCOUNT, tdh.CASHIER_NAME,
						(Select SUM(DEPOSIT_AMT) from FINAL_TR_DEPOSITREPORT_D td with (nolock) where td.UUID_DEPOSIT_REPORT_H = tdh.UUID_DEPOSIT_REPORT_H) as Total,
					 	(Case  when tdh.LOB_FILE is null 
								then '0'
								else '1' end) hasImage
			FROM FINAL_TR_DEPOSITREPORT_H tdh with (nolock)
						join FINAL_TR_DEPOSITREPORT_D tdd with (nolock) on tdh.UUID_DEPOSIT_REPORT_H = tdd.UUID_DEPOSIT_REPORT_H
						join FINAL_TR_TASK_H th with (nolock) on tdd.UUID_TASK_H = th.UUID_TASK_H
						join AM_MSUSER usr with (nolock) on th.UUID_MS_USER = usr.UUID_MS_USER
						join MS_BRANCH br with (nolock) on usr.UUID_BRANCH = br.UUID_BRANCH
						join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = usr.UUID_MS_USER
			WHERE lower(br.UUID_BRANCH) = :uuidBranch
						and usr.FULL_NAME like lower('%'+ :collectorName +'%')
						and COALESCE(tdh.TRANSFERRED_DATE, '1990-01-01 00:00:00') BETWEEN (CASE WHEN :startDate = '%' then '1990-01-01 00:00:00' 
						else :startDate END) 
						and (CASE WHEN :endDate = '%' then :currentDate else :endDate END)
				) out
		) c
	</sql-query>
	
	<sql-query name="task.viewdeposit.getalllistuserhierarki">
		<query-param name="uuidBranch" type="long"/>
		<query-param name="uuidUser" type="long"/>
		<query-param name="subsystemName" type="string"/>
		SELECT usr.UUID_MS_USER as [key], usr.LOGIN_ID + ' - ' + usr.FULL_NAME as [value] FROM AM_MSUSER usr with (nolock)
			join MS_JOB jb with (nolock) on usr.UUID_JOB = jb.UUID_JOB
			join AM_MSSUBSYSTEM subsys with (nolock) on usr.UUID_MS_SUBSYSTEM = subsys.UUID_MS_SUBSYSTEM
			join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = usr.UUID_MS_USER
		WHERE usr.IS_ACTIVE = 1 AND jb.IS_FIELD_PERSON = 1 
			AND usr.UUID_BRANCH = :uuidBranch
			AND subsys.SUBSYSTEM_NAME = :subsystemName
		ORDER BY usr.FULL_NAME
	</sql-query>
</hibernate-mapping>