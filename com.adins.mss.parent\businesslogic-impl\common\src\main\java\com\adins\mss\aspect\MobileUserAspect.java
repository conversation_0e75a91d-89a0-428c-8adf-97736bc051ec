package com.adins.mss.aspect;

import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.security.access.AccessDeniedException;

import com.adins.framework.exception.AdInsException;
import com.adins.framework.exception.StatusCodes;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.MobileEncryptionBase;
import com.adins.framework.service.base.model.AuditDataType;
import com.adins.framework.service.base.model.KeyValue;
import com.adins.framework.service.base.model.MssRequestType;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.mss.businesslogic.api.common.UserLogic;
import com.adins.mss.exceptions.LoginException;
import com.adins.mss.exceptions.LoginException.Reason;
import com.adins.mss.model.AmMsuser;
import com.google.gson.Gson;

public class MobileUserAspect implements MessageSourceAware {
	private static final String KEY_MDC_CALLERID = "callerId";
	private static final Logger LOG = LoggerFactory.getLogger(MobileUserAspect.class);
	private MobileEncryptionBase encryptionHelper;
	private UserLogic userLogic;
	private MessageSource messageSource;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setEncryptionHelper(MobileEncryptionBase encryptionHelper) {
		this.encryptionHelper = encryptionHelper;
	}
	
	public void setUserLogic(UserLogic userLogic) {
		this.userLogic = userLogic;
	}

	public Object invoke(ProceedingJoinPoint joinpoint) throws Throwable {
		final Object[] args = joinpoint.getArgs();
		
		if (args == null || args.length != 1 || !(args[0] instanceof String)) {
			return joinpoint.proceed();
		}
		
		final String arg0 = (String) args[0];
		Gson gson = new Gson();
		try {
			String data = encryptionHelper.read(arg0);
			MssRequestType genericRequest = gson.fromJson(data, MssRequestType.class);
			AuditDataType audit = genericRequest.getAudit();
			String callerId = null;
			AuditContext ac = null;
			if (audit != null) {
				MDC.put(KEY_MDC_CALLERID, audit.getCallerId());
				callerId = audit.getCallerId();
				ac = new AuditContext(callerId);
			}
			
			AmMsuser usr = this.userLogic.fetchUserByUuid(NumberUtils.toLong(callerId), ac);
			if (usr != null) {
				if (!"1".equals(usr.getIsActive())) {
					throw new LoginException(this.messageSource.getMessage(
							"businesslogic.login.inactiveadmin", new Object[]{usr.getLoginId()}, 
							Locale.ENGLISH),Reason.LOGIN_INACTIVE);
				}
				//only for mobile user
				else if ("0".equals(usr.getIsLoggedIn()) && "1".equals(usr.getMsJob().getIsFieldPerson())) {
					throw new LoginException(this.messageSource.getMessage(
							"businesslogic.login.relogin", new Object[]{usr.getLoginId()}, 
							Locale.ENGLISH), 
							Reason.LOGIN_REQUIRED);
				}
				else if (!"0".equals(usr.getIsLocked())) {
					throw new LoginException(this.messageSource.getMessage(
							"businesslogic.context.useralreadylocked", new Object[]{usr.getLoginId()}, 
							Locale.ENGLISH), 
							Reason.LOGIN_REQUIRED);
				}
				
				if (LOG.isTraceEnabled()) {
					LOG.trace("User is active and logged-in.");
				}
			}					
			
			final Object result = joinpoint.proceed(args);			
			return result;
		}
		catch (Throwable e) {
			if (e instanceof AccessDeniedException) {
				throw e;
			}
			
			try {			    			    
				final MssResponseType newInstance = new MssResponseType();
				final Status status = new MssResponseType.Status();

			    if (e instanceof AdInsException) {
					final AdInsException aie = (AdInsException) e;

					status.setCode(aie.getErrorCode());
					status.setMessage(aie.getMessage());

					final Map<String, String> parameters = aie.getParameters();

					for (final Entry<String, String> entry : parameters.entrySet()) {
					    final KeyValue keyVal = new KeyValue();
					    keyVal.setKey(entry.getKey());
					    keyVal.setValue(entry.getValue());

					    ArrayUtils.add(newInstance.getUnstructured(), keyVal);
					}
			    }
			    else {
					status.setCode(StatusCodes.UNKNOWN);
					status.setMessage("Unknown System Error");
			    }

			    LOG.error("Mapped exception to Status: {} / {}",
			    		status.getCode(), status.getMessage(), e);

			    newInstance.setStatus(status);
				
				return encryptionHelper.write(gson.toJson(newInstance));				
			}
			catch (Throwable e2) {
				LOG.error("Unexpected exception while processing endpoint return type", e2);
				throw e2;
			}
		}
		finally {
			MDC.remove(KEY_MDC_CALLERID);
		}
	}

	
}
