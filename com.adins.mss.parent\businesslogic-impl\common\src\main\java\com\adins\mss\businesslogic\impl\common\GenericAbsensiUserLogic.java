package com.adins.mss.businesslogic.impl.common;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.DistanceUtils;
import com.adins.framework.tool.geolocation.PolygonUtils;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.AbsensiLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsArea;
import com.adins.mss.model.MsAreaofbranch;
import com.adins.mss.model.MsAreaofuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrAttendance;
import com.adins.mss.model.TrAttendancefailed;
import com.adins.mss.model.custom.MessageEventBean;
import com.adins.mss.services.model.common.AbsensiBean;
import com.google.gson.Gson;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericAbsensiUserLogic extends BaseLogic implements AbsensiLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericAbsensiUserLogic.class);
	private GeolocationLogic geocoder;
	
	private MessageSource messageSource;

	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}

	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	@Transactional
	@Override
	public Map<String, String> doAbsensi(List<AbsensiBean> locationInfo, String attdAddress, AuditContext callerId, String flagAttd) {

		AbsensiBean bean = null;
		
		for (int i = 0; i < locationInfo.size(); i++) {
			bean = new AbsensiBean();
			AbsensiBean beanLocation = (AbsensiBean) locationInfo.get(i);
			bean.setMcc(beanLocation.getMcc());
			bean.setMnc(beanLocation.getMnc());
			bean.setCid(beanLocation.getCid());
			bean.setLac(beanLocation.getLac());
			if (beanLocation.getLatitude() != null) {
				bean.setLatitude(beanLocation.getLatitude());
			}
			if (beanLocation.getLongitude() != null) {
				bean.setLongitude(beanLocation.getLongitude());
			}
			bean.setAccuracy(beanLocation.getAccuracy());
		}
		
		Map<String, String> result = new HashMap<>(2);

		Date timestamp = new Date(System.currentTimeMillis());
		bean.setTimestamp(timestamp);

		AmMsuser amUser = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch join fetch u.msJob where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", NumberUtils.toLong(callerId.getCallerId())}});
		
		this.processLocation(bean, callerId);
		
		
		boolean isGps = "1".equals(bean.getIsGps());
		if (!isGps && bean.getLatitude() == null) {
			result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.nolocation");
			result.put(KEY_DOABSENSI_MESSAGERESULT,
					this.messageSource.getMessage("businesslogic.absensi.nolocation", null, this.retrieveLocaleAudit(callerId)));
			result.put(KEY_DOABSENSI_SUCCESS, "0");
			return result;
		}
		String source = isGps ? "GPS" : "BTS";

		if ("0".equals(amUser.getMsJob().getIsBranch())) {
			result = this.isiAbsensi(bean, source, amUser, attdAddress, callerId, flagAttd);
		} 
		else {
			List<AbsensiBean> listArea = this.getUserPath(amUser.getUuidMsUser());

			if (listArea.isEmpty() || listArea == null) {
				listArea = this.getBranchPath(amUser.getMsBranch().getUuidBranch());
			}
			if (listArea.isEmpty() || listArea == null) {
				result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.noarea");
				result.put(KEY_DOABSENSI_MESSAGERESULT, this.messageSource.getMessage("businesslogic.absensi.noarea",
						null, this.retrieveLocaleAudit(callerId)));
				result.put(KEY_DOABSENSI_SUCCESS, "0");
			} 
			else {
				boolean isInArea=false;
				if (listArea.get(0).getRadius()==null){
					Map map = this.convertListPathToArray(listArea);
					double lat[] = (double[]) map.get("latitude");
					double lng[] = (double[]) map.get("longitude");
					isInArea = PolygonUtils.isPointInPolygon(lat, lng,
							bean.getLatitude().doubleValue(), bean.getLongitude().doubleValue());
				}
				else{
					AbsensiBean areaCircle= listArea.get(0);
					isInArea = this.isPointInCircle(areaCircle.getLatitude().doubleValue(),areaCircle.getLongitude().doubleValue(),
							areaCircle.getRadius(), bean.getLatitude().doubleValue(), bean.getLongitude().doubleValue());
				}
				if (!isInArea) {
					// gagal absen
					TrAttendancefailed beanFailed = new TrAttendancefailed();

					beanFailed.setAmMsuser(amUser);
					beanFailed.setLatitude((BigDecimal) bean.getLatitude());
					beanFailed.setLongitude((BigDecimal) bean.getLongitude());
					beanFailed.setAttendanceDate(new Date());
					beanFailed.setAccuracy(bean.getAccuracy());
					beanFailed.setMcc(bean.getMcc());
					beanFailed.setMnc(bean.getMnc());
					beanFailed.setLac(bean.getLac());
					beanFailed.setCellId(bean.getCid());
					beanFailed.setUsrCrt(callerId.getCallerId());
					beanFailed.setDtmCrt(timestamp);
					beanFailed.setMsBranch(amUser.getMsBranch());
					beanFailed.setDatetime(timestamp);
					beanFailed.setGeolocationProvider(bean.getProvider());
					if ("GPS".equals(source)) {
						beanFailed.setIsGps("1");
					} 
					else {
						beanFailed.setIsGps("0");
					}
					if (NumberUtils.INTEGER_ZERO.equals(bean.getMcc())
							&& NumberUtils.INTEGER_ZERO.equals(bean.getMnc())
							&& NumberUtils.INTEGER_ZERO.equals(bean.getLac())
							&& NumberUtils.INTEGER_ZERO.equals(bean.getCid())) {
						beanFailed.setIsGsm("0");
					} 
					else {
						beanFailed.setIsGsm("1");
					}
					this.getManagerDAO().insert(beanFailed);
					result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.outofarea");
					result.put(KEY_DOABSENSI_MESSAGERESULT, this.messageSource.getMessage("businesslogic.absensi.outofarea",
							new Object[]{source}, this.retrieveLocaleAudit(callerId)));
					result.put(KEY_DOABSENSI_SUCCESS, "0");
				} else {
					result = this.isiAbsensi(bean, source, amUser, attdAddress, callerId, flagAttd);
				}

			}
		}

		return result;
	}

	private void processLocation(AbsensiBean bean, AuditContext auditContext) {
		boolean isGps = false;
		if (bean.getLatitude() != null || bean.getLongitude() != null) {
			isGps = !(bean.getLatitude().doubleValue() == 0d && bean
					.getLongitude().doubleValue() == 0d) ? true : false;
		}
		if (!isGps) {
			
			bean.setLatitude(null); // set Null first rather than 0 in table
									// record
			bean.setLongitude(null);// set Null first rather than 0 in table
									// record
			if (bean.getMcc() != null && bean.getMnc() != null
					&& bean.getLac() != null && bean.getCid() != null) {

				List<LocationBean> listLocations = new ArrayList<LocationBean>();
				LocationBean locationBean = new LocationBean();
				locationBean.setCellid(bean.getCid().intValue());
				locationBean.setLac(bean.getLac().intValue());
				locationBean.setMcc(bean.getMcc().intValue());
				locationBean.setMnc(bean.getMnc().intValue());
				listLocations.add(locationBean);
				this.geocoder.geocodeCellId(listLocations, auditContext);

				if (locationBean.getCoordinate() != null) {
					bean.setLatitude(new BigDecimal(locationBean.getCoordinate()
							.getLatitude()));
					bean.setLongitude(new BigDecimal(locationBean.getCoordinate()
							.getLongitude()));
					bean.setAccuracy(new Integer(locationBean.getAccuracy()));
					bean.setProvider("GOOGLE");
				}

				bean.setIsGps("0");
			}
		} 
		else {
			bean.setIsGps("1");
		}
	}


	private List<AbsensiBean> getUserPath(long userId) {
		List listPathOfArea = null;
		List<AbsensiBean> listOfPath = new ArrayList<AbsensiBean>();
		MsAreaofuser uuidAreaUser = null;
			
		// cari area dari user
		uuidAreaUser = this.getManagerDAO().selectOne(
				"from MsAreaofuser u join fetch u.msArea m join fetch u.amMsuser a where a.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", userId}});
		if (uuidAreaUser != null) {
			if(uuidAreaUser.getMsArea().getAreaTypeCode().equalsIgnoreCase("poly")){
				String[][] paramPath = { { "uuidArea",
					String.valueOf(uuidAreaUser.getMsArea().getUuidArea()) } };
	
				// cari path of area dari user
				listPathOfArea = this.getManagerDAO().selectAllNative(
						"services.common.user.getListPath", paramPath, null);
	
				if (listPathOfArea != null) {
					Iterator list = listPathOfArea.iterator();
					while (list.hasNext()) {
						Map mapPath = (Map) list.next();
						AbsensiBean path = new AbsensiBean();
						path.setUuidArea(mapPath.get("d0").toString());
						path.setSequence((Integer)mapPath.get("d1"));
						path.setLatitude((BigDecimal)mapPath.get("d2"));
						path.setLongitude((BigDecimal) mapPath.get("d3"));
						listOfPath.add(path);
					}
				}
			}
			else{
				AbsensiBean circle = new AbsensiBean();
				MsArea area = uuidAreaUser.getMsArea();
				circle.setUuidArea(String.valueOf(area.getUuidArea()));
				circle.setLatitude(area.getLatitude());
				circle.setLongitude(area.getLongitude());
				circle.setRadius(area.getRadius());
				listOfPath.add(circle);
			}
		}
		return listOfPath;
	}

	private List<AbsensiBean> getBranchPath(long uuidBranch) {
			List<AbsensiBean> listOfPath = new ArrayList<AbsensiBean>();
		// cari area dari branch
		MsAreaofbranch areaOfBranch = this.getManagerDAO().selectOne(
				"from MsAreaofbranch u join fetch u.msBranch b join fetch u.msArea a where b.uuidBranch = :uuidBranch", new Object[][] {{"uuidBranch", uuidBranch}});
		if (areaOfBranch != null) {
			if ("poly".equalsIgnoreCase(areaOfBranch.getMsArea().getAreaTypeCode())){
				// cari path of area dari branch
				Object[][] paramsPath = { { "uuidArea", areaOfBranch.getMsArea().getUuidArea() } };
				List listPathOfBranch = this.getManagerDAO().selectAllNative(
						"services.common.user.getListPath", paramsPath, null);
	
				if (listPathOfBranch != null) {
					Iterator list = listPathOfBranch.iterator();
					while (list.hasNext()) {
						Map mapPath = (Map) list.next();
						AbsensiBean path = new AbsensiBean();
						path.setUuidArea(mapPath.get("d0").toString());
						path.setSequence((Integer) mapPath.get("d1"));
						path.setLatitude((BigDecimal)(mapPath.get("d2")));
						path.setLongitude((BigDecimal) mapPath.get("d3"));
						listOfPath.add(path);
					}
				}
			}
			else{
				AbsensiBean circle = new AbsensiBean();
				MsArea area = areaOfBranch.getMsArea();
				circle.setUuidArea(String.valueOf(area.getUuidArea()));
				circle.setLatitude(area.getLatitude());
				circle.setLongitude(area.getLongitude());
				circle.setRadius(area.getRadius());
				listOfPath.add(circle);
			}
		}
		return listOfPath;
	}

	private Map convertListPathToArray(List list) {
		double lat[] = new double[list.size()];
		double lng[] = new double[list.size()];

		Map map = new HashMap(2, 1f);
		for (int i = 0; i < list.size(); i++) {
			AbsensiBean bean = (AbsensiBean) list.get(i);
			lat[i] = bean.getLatitude().doubleValue();
			lng[i] = bean.getLongitude().doubleValue();
		}

		map.put("latitude", lat);
		map.put("longitude", lng);

		return map;
	}

	private Map<String, String> isiAbsensi(AbsensiBean bean, String source, AmMsuser amUser, String attdAddress, AuditContext auditContext, String flagAttd) {
		String dateFormatted = DateFormatUtils.format(bean.getTimestamp(),
				"dd-MM-yyyy HH:mm:ss");
		if(GlobalVal.ATTENDANCE_IN.equals(flagAttd)||StringUtils.isBlank(flagAttd)){
			String absensiTmp = this.getCheckInTime(amUser.getUuidMsUser(), bean.getTimestamp(),
					true);
			Map<String, String> result = new HashMap<>(2);
			if (absensiTmp.isEmpty()) {
				if(GlobalVal.SUBSYSTEM_MT.equals(amUser.getAmMssubsystem().getSubsystemName())){
					DateTime dateBf = new DateTime(bean.getTimestamp()).minusDays(1);
					String absensiTmpBf = this.getCheckInTime(amUser.getUuidMsUser(), dateBf.toDate(),
							true);
					if(!absensiTmpBf.isEmpty()){
						TrAttendance attendanceBean = getAttendance(amUser.getUuidMsUser(), dateBf.toDate());
						if(attendanceBean.getDatetimeOut()==null){
							result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.needclosingtask");
							result.put(KEY_DOABSENSI_MESSAGERESULT, 
									this.messageSource.getMessage("businesslogic.absensi.needclosingtask", null, 
											this.retrieveLocaleAudit(auditContext)));
							result.put(KEY_DOABSENSI_SUCCESS, "0");
							return result;
						}
						
					} 
				}
								
				TrAttendance attendanceBean = new TrAttendance();
				AmMsuser amUserBean = new AmMsuser();
				MsBranch branchBean = new MsBranch();

				amUserBean.setUuidMsUser(amUser.getUuidMsUser());
				branchBean.setUuidBranch(amUser.getMsBranch().getUuidBranch());
				attendanceBean.setUsrCrt(String.valueOf(amUser.getUuidMsUser()));
				attendanceBean.setDtmCrt(bean.getTimestamp());
				attendanceBean.setAmMsuser(amUserBean);
				attendanceBean.setMsBranch(branchBean);
				attendanceBean.setAttendanceDate(bean.getTimestamp());
				attendanceBean.setLatitudeIn(bean.getLatitude());
				attendanceBean.setLongitudeIn(bean.getLongitude());
				attendanceBean.setAccuracyIn(bean.getAccuracy());
				attendanceBean.setMccIn(bean.getMcc());
				attendanceBean.setMncIn(bean.getMnc());
				attendanceBean.setLacIn(bean.getLac());
				attendanceBean.setCellIdIn(bean.getCid());
				attendanceBean.setDatetimeIn(bean.getTimestamp());
				attendanceBean.setAttdAddress(attdAddress);
				if ("GPS".equals(source)) {
					attendanceBean.setIsGpsIn("1");
				} 
				else {
					attendanceBean.setIsGpsIn("0");
				}
				if (NumberUtils.INTEGER_ZERO.equals(bean.getMcc()) && NumberUtils.INTEGER_ZERO.equals(bean.getMnc())
						&& NumberUtils.INTEGER_ZERO.equals(bean.getLac()) 
						&& NumberUtils.INTEGER_ZERO.equals(bean.getCid())) {
					attendanceBean.setIsGsmIn("0");
				} 
				else {
					attendanceBean.setIsGsmIn("1");
				}
				attendanceBean.setProviderIn(bean.getProvider());
				this.getManagerDAO().insert(attendanceBean);
				
				Object[] msgArgs = {amUser.getFullName(), dateFormatted};
				result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.success");
				result.put(KEY_DOABSENSI_MESSAGERESULT,
						this.messageSource.getMessage("businesslogic.absensi.success", msgArgs, 
								this.retrieveLocaleAudit(auditContext)));
				result.put(KEY_DOABSENSI_SUCCESS, "1");
			} 
			else {
				Object[] msgArgs = {amUser.getFullName(), absensiTmp};
				result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.duplicate");
				result.put(KEY_DOABSENSI_MESSAGERESULT,
						this.messageSource.getMessage("businesslogic.absensi.duplicate", msgArgs, 
								this.retrieveLocaleAudit(auditContext)));
				result.put(KEY_DOABSENSI_SUCCESS, "0");
			}
			return result;
		} 
		else if (GlobalVal.ATTENDANCE_OUT.equals(flagAttd)) {
			String absensiTmp = this.getCheckInTime(amUser.getUuidMsUser(), bean.getTimestamp(),
					true);
			Map<String, String> result = new HashMap<>(2);
			if (absensiTmp.isEmpty()) {
				DateTime dateBf = new DateTime(bean.getTimestamp()).minusDays(1);
				String absensiTmpBf = this.getCheckInTime(amUser.getUuidMsUser(), dateBf.toDate(),
						true);
				if(absensiTmpBf.isEmpty()){
					result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.needattendance");
					result.put(KEY_DOABSENSI_MESSAGERESULT,
							this.messageSource.getMessage("businesslogic.absensi.needattendance", null, 
									this.retrieveLocaleAudit(auditContext)));
					result.put(KEY_DOABSENSI_SUCCESS, "0");
				} 
				else {
					TrAttendance attendanceBean = getAttendance(amUser.getUuidMsUser(), dateBf.toDate());
					if (attendanceBean.getDatetimeOut()!=null){
						result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.needattendance");
						result.put(KEY_DOABSENSI_MESSAGERESULT,
								this.messageSource.getMessage("businesslogic.absensi.needattendance", null, 
										this.retrieveLocaleAudit(auditContext)));
						result.put(KEY_DOABSENSI_SUCCESS, "0");
					} else {
						 attendanceBean.setLatitudeOut(bean.getLatitude());
						 attendanceBean.setLongitudeOut(bean.getLongitude());
						 attendanceBean.setAccuracyOut(bean.getAccuracy());
						 attendanceBean.setMccOut(bean.getMcc());
						 attendanceBean.setMncOut(bean.getMnc());
						 attendanceBean.setLacOut(bean.getLac());
						 attendanceBean.setCellIdOut(bean.getCid());
						 attendanceBean.setDatetimeOut(bean.getTimestamp()); if
						 ("GPS".equals(source)) { attendanceBean.setIsGpsOut("1"); } else {
						 attendanceBean.setIsGpsOut("0"); } if ("0".equals(bean.getMcc()) &&
						 "0".equals(bean.getMnc()) && "0".equals(bean.getLac()) &&
						 "0".equals(bean.getCid())) { attendanceBean.setIsGsmOut("0"); }
						 else { attendanceBean.setIsGsmOut("1"); }
						 attendanceBean.setProviderOut(bean.getProvider());
						 attendanceBean.setAttdAddressOut(attdAddress);
						 this.getManagerDAO().update(attendanceBean);
						 
						 Object[] msgArgs = {amUser.getFullName(), dateFormatted};
						 result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.successclosingtask");
						 result.put(KEY_DOABSENSI_MESSAGERESULT,
								 this.messageSource.getMessage("businesslogic.absensi.successclosingtask", msgArgs, 
										 this.retrieveLocaleAudit(auditContext)));
						 result.put(KEY_DOABSENSI_SUCCESS, "1");
					}
				}
				
			} else {
				TrAttendance attendanceBean = getAttendance(amUser.getUuidMsUser(), bean.getTimestamp());
				if(attendanceBean!=null){
					if(attendanceBean.getDatetimeOut()!=null){
						Object[] msgArgs = {amUser.getFullName(), dateFormatted};
						result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.duplicateclosingtask");
						result.put(KEY_DOABSENSI_MESSAGERESULT,
								this.messageSource.getMessage("businesslogic.absensi.duplicateclosingtask", msgArgs, 
										this.retrieveLocaleAudit(auditContext)));
						result.put(KEY_DOABSENSI_SUCCESS, "0");
						return result;
					}
					 attendanceBean.setAttendanceDate(bean.getTimestamp());
					 attendanceBean.setLatitudeOut(bean.getLatitude());
					 attendanceBean.setLongitudeOut(bean.getLongitude());
					 attendanceBean.setAccuracyOut(bean.getAccuracy());
					 attendanceBean.setMccOut(bean.getMcc());
					 attendanceBean.setMncOut(bean.getMnc());
					 attendanceBean.setLacOut(bean.getLac());
					 attendanceBean.setCellIdOut(bean.getCid());
					 attendanceBean.setDatetimeOut(bean.getTimestamp()); if
					 ("GPS".equals(source)) { attendanceBean.setIsGpsOut("1"); } else {
					 attendanceBean.setIsGpsOut("0"); } if ("0".equals(bean.getMcc()) &&
					 "0".equals(bean.getMnc()) && "0".equals(bean.getLac()) &&
					 "0".equals(bean.getCid())) { attendanceBean.setIsGsmOut("0"); }
					 else { attendanceBean.setIsGsmOut("1"); }
					 attendanceBean.setProviderOut(bean.getProvider());
					 attendanceBean.setAttdAddressOut(attdAddress);
					 this.getManagerDAO().update(attendanceBean);
				}
				
				Object[] msgArgs = {amUser.getFullName(), dateFormatted};
				result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.successclosingtask");
				result.put(KEY_DOABSENSI_MESSAGERESULT, 
						this.messageSource.getMessage("businesslogic.absensi.successclosingtask", msgArgs, 
								this.retrieveLocaleAudit(auditContext)));
				result.put(KEY_DOABSENSI_SUCCESS, "1");
			
			}
			return result;
		} 
		else if (GlobalVal.ATTENDANCE_CHECK.equals(flagAttd)) {
			Map<String, String> result = new HashMap<>(2);
			TrAttendance attendanceBean = getAttendance(amUser.getUuidMsUser(), bean.getTimestamp());
			if(attendanceBean!=null){
				if(attendanceBean.getDatetimeOut()!= null){
					Object[] msgArgs = {amUser.getFullName(), dateFormatted};
					result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.successclosingtask");
					result.put(KEY_DOABSENSI_MESSAGERESULT,
							this.messageSource.getMessage("businesslogic.absensi.successclosingtask", msgArgs, 
									this.retrieveLocaleAudit(auditContext)));
					result.put(KEY_DOABSENSI_SUCCESS, "1");
					return result;
				}
				else{
					result.put(KEY_DOABSENSI_MESSAGEKEY, "businesslogic.absensi.checkclosing");
					result.put(KEY_DOABSENSI_MESSAGERESULT,
							this.messageSource.getMessage("businesslogic.absensi.checkclosing", null, 
									this.retrieveLocaleAudit(auditContext)));
					result.put(KEY_DOABSENSI_SUCCESS, "1");
					return result;
				}
			}
			return result;
		}
		
		return null;
		

		/**
		 * attendance out untuk sementara exclude
		 * 
		 * else { TrAttendance attendanceBean = new TrAttendance();
		 * 
		 * attendanceBean.setUuidAttendance(this.getManagerDAO().getUUID());
		 * attendanceBean.setUsrCrt(bean.getUserId());
		 * attendanceBean.setDtmCrt(bean.getTimestamp());
		 * attendanceBean.getAmMsuser() .setUuidMsUser(amUser.getUuidMsUser());
		 * attendanceBean.getMsBranch().setUuidBranch(
		 * amUser.getMsBranch().getUuidBranch());
		 * attendanceBean.setAttendanceDate(bean.getTimestamp());
		 * attendanceBean.setLatitudeOut(new BigDecimal(bean.getLatitude()));
		 * attendanceBean.setLongitudeOut(new BigDecimal(bean.getLongitude()));
		 * attendanceBean.setAccuracyOut(bean.getAccuracy());
		 * attendanceBean.setMccOut(bean.getMCC());
		 * attendanceBean.setMncOut(bean.getMNC());
		 * attendanceBean.setLacOut(bean.getLAC());
		 * attendanceBean.setCellIdOut(bean.getCellId());
		 * attendanceBean.setDatetimeOut(bean.getTimestamp()); if
		 * ("GPS".equals(source)) { attendanceBean.setIsGpsOut("1"); } else {
		 * attendanceBean.setIsGpsOut("0"); } if ("0".equals(bean.getMCC()) &&
		 * "0".equals(bean.getMNC()) && "0".equals(bean.getLAC()) &&
		 * "0".equals(bean.getCellId())) { attendanceBean.setIsGsmOut("0"); }
		 * else { attendanceBean.setIsGsmOut("1"); }
		 * attendanceBean.setProviderOut("Google");
		 * 
		 * this.getManagerDAO().insert(attendanceBean); }
		 * 
		 */

	}

	/**
	 * Cek apakah sudah ada absen masuk/keluar per hari tersebut (system date)
	 * 
	 * @param userId
	 *            userId
	 * @param absenMasuk
	 *            : true = absenMasuk | false = absenKeluar (untuk sementara
	 *            exclude)
	 * @return boolean record exists / not
	 */
	private String getCheckInTime(long userId, Date timestamp,
			boolean absenMasuk) {
		String dateIn = "";
		String dateFormatted = DateFormatUtils.format(timestamp, "yyyy-MM-dd");
		String startDate = dateFormatted+" 00:00:00.000";
		String endDate = dateFormatted+" 23:59:59.997";
		Object[][] params = { { "userId", userId },
				{ "startDate", startDate },
				{"endDate",endDate}};
		if (absenMasuk) {
			Date dateTmp= (Date) this.getManagerDAO().selectOneNative(
					"services.common.user.getCheckInTime", params);
			if(dateTmp!=null){
				dateIn = DateFormatUtils.format(dateTmp, "yyyy-MM-dd HH:mm:ss");
			}
		}
		return dateIn;
	}

	private TrAttendance getAttendance(long userId, Date timestamp) {
		TrAttendance attendance = null;
		String dateFormatted = DateFormatUtils.format(timestamp, "yyyy-MM-dd");
		String startDate = dateFormatted+" 00:00:00.000";
		String endDate = dateFormatted+" 23:59:59.997";
		Object[][] params = { { "userId", userId },
				{ "startDate", startDate },
				{"endDate",endDate}};
		BigInteger uuidAttendance = (BigInteger) this.getManagerDAO().selectOneNative(
					"services.common.user.getUuidAttendance", params);
		if (uuidAttendance != null){
			attendance = this.getManagerDAO().selectOne(TrAttendance.class, uuidAttendance);
		}
		return attendance;
	}
	
	private boolean isPointInCircle(double lat, double lng, int rad, double x, double y){
		double meter = DistanceUtils.getDistance(lat, lng, x, y);
		if ((double) meter <= rad){
			return true;
		}
		else{
			return false;
		}
		
	}

	@Override
	@Async
	public void sendMessageToJms(List<AbsensiBean> locationInfo, String messageKey, AuditContext callerId) {
		AmMsuser amUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		AbsensiBean bean = null;
		for (int i = 0; i < locationInfo.size(); i++) {
			bean = new AbsensiBean();
			AbsensiBean beanLocation = (AbsensiBean) locationInfo.get(i);
			bean.setMcc(beanLocation.getMcc());
			bean.setMnc(beanLocation.getMnc());
			bean.setCid(beanLocation.getCid());
			bean.setLac(beanLocation.getLac());
			if (beanLocation.getLatitude() != null) {
				bean.setLatitude(beanLocation.getLatitude());
			}
			if (beanLocation.getLongitude() != null) {
				bean.setLongitude(beanLocation.getLongitude());
			}
			bean.setAccuracy(beanLocation.getAccuracy());
		}
		
		try {
			if(bean!=null){
				String dateFormatted = DateFormatUtils.format(new Date(),
						"yyyy-MM-dd HH:mm:ss");
				Object[] msgArgs = {amUser.getLoginId(), amUser.getFullName(), dateFormatted};
				String notes = StringUtils.EMPTY;
				if ("businesslogic.absensi.successclosingtask".equals(messageKey)){
					notes = this.messageSource.getMessage("businesslogic.jms.closingtask", 
							msgArgs, this.retrieveLocaleAudit(callerId));
				}
				else if ("businesslogic.absensi.success".equals(messageKey)){
					notes = this.messageSource.getMessage("businesslogic.jms.absensi", 
							msgArgs, this.retrieveLocaleAudit(callerId));
				}
				if(StringUtils.isNotBlank(notes)){
					Gson gson = new Gson();
					MessageEventBean msgBean = new MessageEventBean();
					msgBean.setEvent(GlobalVal.EVENT_NEWS);
					msgBean.setUuidUser(amUser.getImei()+amUser.getUuidMsUser());
					msgBean.setDate(DateFormatUtils.format(new Date(), "HH:mm:ss"));
					msgBean.setUuidMessageBean(this.getManagerDAO().getUUID());
					msgBean.setNotes(notes);
					try{
						sendMessage(gson.toJson(msgBean));
						LOG.info("Send message to JMS Success");
					}catch(Exception e){
						LOG.error("Send message to JMS Failed", e);
					}
				}
			}
		} catch (Exception ex) {
			LOG.error("Error on sending message to JMS", ex);
		}
	}
}
