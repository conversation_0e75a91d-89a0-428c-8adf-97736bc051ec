package com.adins.mss.businesslogic.impl.common;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.AddTaskFromStagingLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.common.TaskServiceLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.MobileDefAnswer;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.exceptions.UploadTaskException;
import com.adins.mss.exceptions.UploadTaskException.Reason;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.StgPoolingToMobileSrvyX;
import com.adins.mss.model.TblProductCategory;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.services.model.newconfins.AddTaskDetailBean;

@SuppressWarnings({"rawtypes", "unchecked"})
public class GenericAddTaskFromStagingLogic extends BaseLogic implements AddTaskFromStagingLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericAddTaskFromStagingLogic.class);
	
	@Autowired
	private TaskServiceLogic taskServiceLogic;
	
	@Autowired
	private TaskDistributionLogic taskDistributionLogic;
	
	public void setTaskServiceLogic(TaskServiceLogic taskServiceLogic) {
		this.taskServiceLogic = taskServiceLogic;
	}
	
	public void setTaskDistributionLogic(TaskDistributionLogic taskDistributionLogic) {
		this.taskDistributionLogic = taskDistributionLogic;
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List getTaskFromStaging(AuditContext auditContext) {
		List mapResult = this.getManagerDAO().selectAllNativeString(
				"SELECT STG_POOLING_TO_MOBILE_SRVY_X_ID "
				+ " from STG_POOLING_TO_MOBILE_SRVY_X stg "
				+ " where stg.status = :status", 
				new Object[][] {{"status", "REQ"}});
		return mapResult;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public void doAddTaskFromStaging(String stagingTaskId, AuditContext auditContext) {
		// TODO Auto-generated method stub
		String isPiloting = StringUtils.EMPTY;
		String loginId = StringUtils.EMPTY;
		String branchId = StringUtils.EMPTY;
		String isSelfAssignment = StringUtils.EMPTY;
		String note = null;
		
		Object [][] paramTaskH = { {Restrictions.eq("stgPoolingToMobileSrvyXId", Long.valueOf(stagingTaskId))} };
		StgPoolingToMobileSrvyX taskDetail = this.getManagerDAO().selectOne(StgPoolingToMobileSrvyX.class, paramTaskH);
		Object [][] paramGenSet = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_BLACKLIST_ACTIVE)} };
		AmGeneralsetting amGenSet = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramGenSet);
		
		Object [][] paramTaskOrder = { {Restrictions.eq("applNo", taskDetail.getOrderNo())} };
		TrTaskH taskOrder = this.getManagerDAO().selectOne(TrTaskH.class, paramTaskOrder);
		if(taskOrder!=null) {
			isSelfAssignment = taskOrder.getIsSelfassignment();
		}
		if ("1".equals(taskDetail.getIsNegCust()) && "1".equals(amGenSet.getGsValue())) {			
			throw new UploadTaskException("Negative Customer", Reason.ERROR_GENERATE);
			
		}else if("1".equals(taskDetail.getIsNegCust()) && "0".equals(amGenSet.getGsValue())){
			note = "Negative Customer";
			
			if (null != taskOrder) {
				taskOrder.setDtmUpd(new Date());
				taskOrder.setUsrUpd("SYSTEM");
				taskOrder.setNotes(note);				
				this.getManagerDAO().update(taskOrder);
			}
			
		}
			
		Object [][] paramMapForm = { {Restrictions.eq("productCategoryCode", taskDetail.getProdCode())} };
		TblProductCategory tblProductCategory = this.getManagerDAO().selectOne(TblProductCategory.class, paramMapForm);
		if (null == tblProductCategory) {
			throw new UploadTaskException("Product Category Code doesn't exist", Reason.ERROR_GENERATE);
		} else {
			if (0 == tblProductCategory.getIsMapped()) {
				throw new UploadTaskException("Product Category is not mapped", Reason.ERROR_GENERATE);
			}
			
			if (null == tblProductCategory.getMsForm()) {
				throw new UploadTaskException("Product Category have not been mapped with form", Reason.ERROR_GENERATE);
			} else {
				this.getManagerDAO().fetch(tblProductCategory.getMsForm());
				if ("0".equalsIgnoreCase(tblProductCategory.getMsForm().getIsActive())) {
					throw new UploadTaskException("Form is not active", Reason.ERROR_GENERATE);
				}
			}
		}
			
		if("1".equals(isSelfAssignment)) {
			AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, taskOrder.getAmMsuser().getUuidMsUser());
			MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class,
					new Object[][] { {Restrictions.eq("uuidBranch", taskOrder.getMsBranch().getUuidBranch())} });
			loginId = amMsUser.getLoginId();
			branchId = msBranch.getBranchCode();
			isPiloting = msBranch.getIsPiloting();
		} else {
			Map distributionResult = taskDistributionLogic.poolingTaskDistribution(tblProductCategory.getTblProductCategoryId(), taskDetail.getNik(),
					taskDetail.getSubzipcode(), taskDetail.getDealerCode(), tblProductCategory.getJenisPembiayaan(), null, null, null, null, "0", auditContext);
			if(distributionResult.get("errMsg") != null) {
				if(StringUtils.isNotBlank(distributionResult.get("errMsg").toString())) {
					throw new UploadTaskException(distributionResult.get("errMsg").toString(), Reason.ERROR_GENERATE);
				}
			}
				
			List <BigInteger> listUser = (List<BigInteger>) distributionResult.get("listUser");
				
			if (listUser.isEmpty()) {
//				throw new UploadTaskException("User for distribution not found", Reason.ERROR_GENERATE);
				if (null != taskOrder) {
					this.getManagerDAO().fetch(taskOrder.getMsBranch());
					branchId = taskOrder.getMsBranch().getBranchCode();
					isPiloting = taskOrder.getMsBranch().getIsPiloting();
				} else {
					branchId = "0011";
					isPiloting = "0";
				}
			} else {
				if (1 != listUser.size()) {
					listUser = taskDistributionLogic.assignByLoad(listUser, tblProductCategory.getJenisPembiayaan(), null, null, null, null, "0", auditContext);
				}
				AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class,
						new Object[][] { {Restrictions.eq("uuidMsUser", listUser.get(0).longValue())} });
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class,
						new Object[][] { {Restrictions.eq("uuidBranch", amMsUser.getMsBranch().getUuidBranch())} });
				loginId = amMsUser.getLoginId();
				branchId = msBranch.getBranchCode();
				isPiloting = msBranch.getIsPiloting();
			}
		}
		String orderNo = taskDetail.getOrderNo();
		String priorityDesc = GlobalVal.TAKS_PRIORTY_NORMAL;
		String customerName = taskDetail.getCustName();
		String customerAddress = taskDetail.getLegalAddr();
		String customerPhone = taskDetail.getMobilePhnNo();
		String zipCode = taskDetail.getLegalZipcode();
		
		Object[][] paramsSP = { { "stgPoolingToMobileSrvyXId", stagingTaskId } };
		Map<String, String> detailFromStaging = new HashMap<>();
		List taskDFromStaging =  this.getManagerDAO().selectAllNativeString("select ref_id, nilai from dbo.getStagingTaskDetail(:stgPoolingToMobileSrvyXId)", paramsSP);
		for (int i = 0; i < taskDFromStaging.size(); i++) {
			Map taskD = (Map) taskDFromStaging.get(i);
			String stagingColName = String.valueOf(taskD.get("d0"));
			String value = String.valueOf(taskD.get("d1"));
			detailFromStaging.put(stagingColName, value);
		}
		List listColWithRef = this.getManagerDAO().selectAllNativeString("select COL_NAME, REF_ID from TBL_MAP_TASK_D WITH(NOLOCK) WHERE ANSWER_VALUE IS NULL AND SOURCE_DATA IS NULL ", null);
		List <AddTaskDetailBean> listTemp = new ArrayList<AddTaskDetailBean>();
		for (int i = 0; i < listColWithRef.size(); i++) {
			Map mapColWithRef = (Map) listColWithRef.get(i);
			String colName = String.valueOf(mapColWithRef.get("d0"));
			if (null != detailFromStaging.get(colName)) {
				String refId = String.valueOf(mapColWithRef.get("d1"));
				String value = String.valueOf(detailFromStaging.get(colName));
				if (!StringUtils.isBlank(value)) {
					AddTaskDetailBean detailTask = new AddTaskDetailBean();
					detailTask.setRefID(refId);
					detailTask.setValue(value);
					listTemp.add(detailTask);
				}
			}
		}
		AddTaskDetailBean[] listTaskDetail = new AddTaskDetailBean[listTemp.size()];
		for (int k = 0; k < listTemp.size(); k++) {
			listTaskDetail[k] = listTemp.get(k);
		}
		
		if ("1".equals(isPiloting)) {
			String formNameFoto = tblProductCategory.getMsFormFoto().getFormName();
			Long uuidFormFoto = tblProductCategory.getMsFormFoto().getUuidForm();
			Object [][] paramFormVersionFoto = {{"uuidForm", uuidFormFoto }};
			String formVersionFoto = String.valueOf(this.getManagerDAO().selectOneNativeString("SELECT TOP(1) MSFH.FORM_VERSION "
					+ " FROM MS_FORMHISTORY MSFH WITH (NOLOCK) "
					+ " JOIN MS_FORM MSF WITH (NOLOCK) ON MSF.UUID_FORM = MSFH.UUID_FORM "
					+ " WHERE MSF.UUID_FORM = :uuidForm "
					+ " ORDER BY FORM_VERSION DESC", paramFormVersionFoto));
			
			String formNameText = tblProductCategory.getMsFormText().getFormName();
			Long uuidFormText = tblProductCategory.getMsFormText().getUuidForm();
			Object [][] paramFormVersionText = {{"uuidForm", uuidFormText }};
			String formVersionText = String.valueOf(this.getManagerDAO().selectOneNativeString("SELECT TOP(1) MSFH.FORM_VERSION "
					+ " FROM MS_FORMHISTORY MSFH WITH (NOLOCK) "
					+ " JOIN MS_FORM MSF WITH (NOLOCK) ON MSF.UUID_FORM = MSFH.UUID_FORM "
					+ " WHERE MSF.UUID_FORM = :uuidForm "
					+ " ORDER BY FORM_VERSION DESC", paramFormVersionText));

			Map resultText = new HashMap<>();
			Map resultFoto = new HashMap<>();
			try {
				resultText = taskServiceLogic.addTask(auditContext, "MSCOREP",
						GlobalVal.SUBSYSTEM_MS, formNameText,
						formVersionText, priorityDesc, customerName, 
						customerAddress, customerPhone, zipCode,
						note, branchId, orderNo, loginId, MobileDefAnswer.YES.toString(), null,
						null, null, null, "SYSTEM", listTaskDetail, null, null, null);
			} catch (Exception ex) {
				ex.printStackTrace();
				throw new RemoteException("Error Add Form Text :" + ex.getMessage());
			}
			try {
				resultFoto = taskServiceLogic.addTask(auditContext, "MSCOREF",
						GlobalVal.SUBSYSTEM_MS, formNameFoto,
						formVersionFoto, priorityDesc, customerName, 
						customerAddress, customerPhone, zipCode,
						note, branchId, orderNo, loginId, MobileDefAnswer.NO.toString(), null,
						null, null, null, "SYSTEM", null, null, null, null);
			} catch (Exception ex) {
				ex.printStackTrace();
				throw new RemoteException("Error Add Form Foto :" + ex.getMessage());
			}
			
			Status resultStatusText = (Status) resultText.get("status");
			TrTaskH taskText = (TrTaskH)resultText.get("task");
			if(taskText!=null) {
				if("1".equals(isSelfAssignment)) {
					taskText.setIsSelfassignment("1");
					this.getManagerDAO().update(taskText);
				}
				
				if(taskText.getAmMsuser() == null) {
					Object[][] paramsSub = { { Restrictions.eq("subsystemName", GlobalVal.SUBSYSTEM_MS) } };
					AmMssubsystem amMssubsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, paramsSub);
					
					String statusCode = GlobalVal.SURVEY_STATUS_TASK_UNASSIGN;
					if (!GlobalVal.SURVEY_STATUS_TASK_UNASSIGN.equalsIgnoreCase(taskText.getMsStatustask().getStatusCode())) {
						long uuidProcess = this.getUuidProcess(taskText, taskText.getMsStatustask().getAmMssubsystem());
		    			statusCode = wfEngineLogic.reverseCurrentTask(uuidProcess, taskText.getUuidTaskH());
					}
					
	    			Object[][] params2 = {
	    					{ Restrictions.eq("statusCode", statusCode) },
	    					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", amMssubsystem.getUuidMsSubsystem()) } };
	    			MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
	    			
	    			this.getManagerDAO().fetch(taskOrder.getAmMsuser());
					AmMsuser spv = taskOrder.getAmMsuser().getAmMsuser();
					
	    			taskText.setMsStatustask(msStatustask);
	    			taskText.setAmMsuser(spv);
   			
	    			this.getManagerDAO().update(taskText);
				}
			}
			
			Status resultStatusFoto = (Status) resultFoto.get("status");
			TrTaskH taskFoto = (TrTaskH)resultFoto.get("task");
			if(taskFoto!=null) {
				if("1".equals(isSelfAssignment)) {
					taskFoto.setIsSelfassignment("1");
					this.getManagerDAO().update(taskFoto);
				}
				
				if(taskFoto.getAmMsuser() == null) {
					Object[][] paramsSub = { { Restrictions.eq("subsystemName", GlobalVal.SUBSYSTEM_MS) } };
					AmMssubsystem amMssubsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, paramsSub);
					
					String statusCode = GlobalVal.SURVEY_STATUS_TASK_UNASSIGN;
					if (!GlobalVal.SURVEY_STATUS_TASK_UNASSIGN.equalsIgnoreCase(taskText.getMsStatustask().getStatusCode())) {
						long uuidProcess = this.getUuidProcess(taskText, taskText.getMsStatustask().getAmMssubsystem());
		    			statusCode = wfEngineLogic.reverseCurrentTask(uuidProcess, taskText.getUuidTaskH());
					}
	    			Object[][] params2 = {
	    					{ Restrictions.eq("statusCode", statusCode) },
	    					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", amMssubsystem.getUuidMsSubsystem()) } };
	    			MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
	    			
	    			this.getManagerDAO().fetch(taskOrder.getAmMsuser());
					AmMsuser spv = taskOrder.getAmMsuser().getAmMsuser();
					
					taskFoto.setMsStatustask(msStatustask);
					taskFoto.setAmMsuser(spv);
   			
	    			this.getManagerDAO().update(taskFoto);
				}
			}
			
			if (1 == resultStatusText.getCode() && 1 == resultStatusFoto.getCode()) {
				taskDetail.setStatus("EXE");
				taskDetail.setUsrUpd("SYSTEM");
				taskDetail.setDtmUpd(new Date());
				this.getManagerDAO().update(taskDetail);
				
				String uuidTaskHText = String.valueOf(resultText.get("taskID"));
				String uuidTaskHFoto = String.valueOf(resultFoto.get("taskID"));
				Object [][] paramTrTaskHText = { {Restrictions.eq("uuidTaskH", Long.valueOf(uuidTaskHText))} };
				TrTaskH trTaskHText = this.getManagerDAO().selectOne(TrTaskH.class, paramTrTaskHText);
				Object [][] paramTrTaskHFoto = { {Restrictions.eq("uuidTaskH", Long.valueOf(uuidTaskHFoto))} };
				TrTaskH trTaskHFoto = this.getManagerDAO().selectOne(TrTaskH.class, paramTrTaskHFoto);
					
				trTaskHText.setIsMatchDukcapil(taskDetail.getIsMatchDukcapil());
				trTaskHText.setSubzipcode(taskDetail.getSubzipcode());
				trTaskHText.setDealerCode(taskDetail.getDealerCode());
				// ADD DATA KELURAHAN UNTUK TASK TEXT
				trTaskHText.setKelurahan(taskDetail.getResidenceKelurahan());
				this.getManagerDAO().update(trTaskHText);
				
				taskServiceLogic.insertDefaultAnswerQuestionPilotingCae(trTaskHText, false, auditContext);
				
				trTaskHFoto.setIsMatchDukcapil(taskDetail.getIsMatchDukcapil());
				trTaskHFoto.setSubzipcode(taskDetail.getSubzipcode());
				trTaskHFoto.setDealerCode(taskDetail.getDealerCode());
				// ADD DATA KELURAHAN UNTUK TASK FOTO
				trTaskHFoto.setKelurahan(taskDetail.getResidenceKelurahan());
				this.getManagerDAO().update(trTaskHFoto);
				
				taskServiceLogic.insertDefaultAnswerQuestionPilotingCae(trTaskHFoto, false, auditContext);
			} else if (0 == resultStatusText.getCode() && 1 == resultStatusFoto.getCode()) {
				throw new UploadTaskException(resultText.get("message").toString(), Reason.ERROR_GENERATE);
			} else if (1 == resultStatusText.getCode() && 0 == resultStatusFoto.getCode()) {
				throw new UploadTaskException(resultFoto.get("message").toString(), Reason.ERROR_GENERATE);
			} else {
				throw new UploadTaskException(resultText.get("message").toString() + " | " + resultFoto.get("message").toString(), Reason.ERROR_GENERATE);
			}
		} else {
			String formName = StringUtils.EMPTY;
			formName = tblProductCategory.getMsForm().getFormName();
			Long uuidForm = tblProductCategory.getMsForm().getUuidForm();
			Object [][] paramFormVersion = {{"uuidForm", uuidForm }};
			String formVersion = String.valueOf(this.getManagerDAO().selectOneNativeString("SELECT TOP(1) MSFH.FORM_VERSION "
					+ " FROM MS_FORMHISTORY MSFH WITH (NOLOCK) "
					+ " JOIN MS_FORM MSF WITH (NOLOCK) ON MSF.UUID_FORM = MSFH.UUID_FORM "
					+ " WHERE MSF.UUID_FORM = :uuidForm "
					+ " ORDER BY FORM_VERSION DESC", paramFormVersion));
			
			Map result = taskServiceLogic.addTask(auditContext, "MSCORE",
					GlobalVal.SUBSYSTEM_MS, formName,
					formVersion, priorityDesc, customerName, 
					customerAddress, customerPhone, zipCode,
					note, branchId, orderNo, loginId, MobileDefAnswer.YES.toString(), null,
					null, null, null, "SYSTEM", listTaskDetail, null, null, null);
			
			Status resultStatus = (Status) result.get("status");
			TrTaskH task = (TrTaskH) result.get("task");
			if(task!=null) {
				if("1".equals(isSelfAssignment)) {
					task.setIsSelfassignment("1");
					this.getManagerDAO().update(task);
				}
			}
			if (1 == resultStatus.getCode()) {
				taskDetail.setStatus("EXE");
				taskDetail.setUsrUpd("SYSTEM");
				taskDetail.setDtmUpd(new Date());
				this.getManagerDAO().update(taskDetail);
				
				String uuidTaskH = String.valueOf(result.get("taskID"));
				Object [][] paramTrTaskH = { {Restrictions.eq("uuidTaskH", Long.valueOf(uuidTaskH))} };
				TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, paramTrTaskH);
				
				trTaskH.setIsMatchDukcapil(taskDetail.getIsMatchDukcapil());
				trTaskH.setSubzipcode(taskDetail.getSubzipcode());
				trTaskH.setDealerCode(taskDetail.getDealerCode());
				this.getManagerDAO().update(trTaskH);
			}
		}
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public void updateTaskFail(String message, String stagingTaskId, AuditContext auditContext) {
		LOG.info("Begin Update Task Fail with message {} - id {}", message, stagingTaskId);
		Object [][] paramTaskH = { {Restrictions.eq("stgPoolingToMobileSrvyXId", Long.valueOf(stagingTaskId))} };
		StgPoolingToMobileSrvyX taskDetail = this.getManagerDAO().selectOne(StgPoolingToMobileSrvyX.class, paramTaskH);
		
		taskDetail.setStatus("ERR");
		taskDetail.setErrMsg(message);
		taskDetail.setUsrUpd("SYSTEM");
		taskDetail.setDtmUpd(new Date());
		this.getManagerDAO().update(taskDetail);
		
		Object [][] paramTaskOrder = { {Restrictions.eq("applNo", taskDetail.getOrderNo())} };
		TrTaskH taskOrder = this.getManagerDAO().selectOne(TrTaskH.class, paramTaskOrder);
		
		if (null != taskOrder) {
			this.getManagerDAO().fetch(taskOrder.getAmMsuser());
			Object[][] paramsSub = { { Restrictions.eq("subsystemName", GlobalVal.SUBSYSTEM_MS) } };
			AmMssubsystem amMssubsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, paramsSub);
			
			Object[][] params2 = {
					{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_FAILED_ASSIGNMENT) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", amMssubsystem.getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
			taskOrder.setMsStatustask(msStatustask);
			taskOrder.setDtmUpd(new Date());
			taskOrder.setUsrUpd("SYSTEM");
			String fullNotes = StringUtils.EMPTY; 
			if (StringUtils.isNotBlank(taskOrder.getNotes())) {
				fullNotes += taskOrder.getNotes() + " | ";
			}
			fullNotes += message;
			taskOrder.setNotes(fullNotes);
			this.getManagerDAO().update(taskOrder);

			//INSERT INTO CHECK HISTORY
			String codeProcess = GlobalVal.CODE_PROCESS_FAILED;
			AmMsuser actor = new AmMsuser();
			actor.setFullName("SYSTEM");
			insertTaskHistory(auditContext, msStatustask, taskOrder, message, 
					codeProcess, taskOrder.getAmMsuser().getFullName(), actor, null);
		}
	}
	
}
