package com.adins.mss.businesslogic.impl.common;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.UnknownHostException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.AddTaskMainDealerLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.common.TaskServiceLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.exceptions.SubmitTaskException;
import com.adins.mss.exceptions.UploadTaskException;
import com.adins.mss.exceptions.UploadTaskException.Reason;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.CheckCustomerHistory;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsMappingformD;
import com.adins.mss.model.MsMappingformH;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TblProductCategory;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskcolldata;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.TrTasksurveydata;
import com.adins.mss.model.custom.MappingFormBean;
import com.adins.mss.model.custom.TaskDukcapilBean;
import com.adins.mss.model.custom.UploadImageBean;
import com.adins.mss.model.custom.UploadImageRequestBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.ImageBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.QuestionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.AddTaskMainDealerRequest;
import com.adins.mss.services.model.common.AddTaskMainDealerResponse;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.newconfins.CIFBean;
import com.adins.mss.services.model.newconfins.CheckDukcapilResponse;
import com.adins.mss.services.model.newconfins.UpdateStatusMainDealerResponse;
import com.adins.mss.services.model.newconfins.ZipcodeBean;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

@SuppressWarnings({ "rawtypes", "unchecked" })
public class GenericAddTaskMainDealerLogic extends BaseLogic implements AddTaskMainDealerLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericAddTaskMainDealerLogic.class);
	
	private Gson gson = new Gson();
	
	@Autowired
	private CommonLogic commonLogic;
	@Autowired
	private ImageStorageLogic imageStorageLogic;
	@Autowired
	private GeolocationLogic geocoder;
	private IntFormLogic intFormLogic;
	@Autowired
	private MessageSource messageSource;
	@Autowired
	private TaskDistributionLogic taskDistributionLogic;
	@Autowired
	private TaskServiceLogic taskServiceLogic;

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }
	
	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setTaskDistributionLogic(TaskDistributionLogic taskDistributionLogic) {
		this.taskDistributionLogic = taskDistributionLogic;
	}
	
	public void setTaskServiceLogic(TaskServiceLogic taskServiceLogic) {
		this.taskServiceLogic = taskServiceLogic;
	}

	@Transactional
	@Override
	public AddTaskMainDealerResponse doAddTaskMainDealer(AddTaskMainDealerRequest request, AuditContext callerId) {
		AddTaskMainDealerResponse response = new AddTaskMainDealerResponse();
		String applNo = StringUtils.EMPTY;
		String message = "Success";
		int code = 0;
		try {
			request = fillMainDealerRequest(request);
			String jsonRequest = gson.toJson(request, AddTaskMainDealerRequest.class);
			Map<String, String> mapJsonRequest = gson.fromJson(jsonRequest, new TypeToken<Map<String, String>>() {}.getType());
			Map<String, Object> mapDetailBean = this.generateDetailBean(mapJsonRequest, callerId);
			SubmitTaskDBean[] submitTaskDBean = (SubmitTaskDBean[]) mapDetailBean.get("submitTaskDBean");
			Map<Integer, MsQuestion> msQuestions = (Map<Integer, MsQuestion>) mapDetailBean.get("msQuestions");
			TaskDukcapilBean taskDukcapilBean = (TaskDukcapilBean) mapDetailBean.get("taskDukcapilBean");
			
			//start distribusi task
			String officeCode = getOfficeByKat(request.getSubzipcode(), taskDukcapilBean.getProdCode());
			
			AmMsuser amMsuser = null;
			MsBranch msBranch = null;
			Object [][] paramMapForm = { {Restrictions.eq("productCategoryCode", taskDukcapilBean.getProdCode())} };
			TblProductCategory tblProductCategory = this.getManagerDAO().selectOne(TblProductCategory.class, paramMapForm);
			if (null == tblProductCategory) {
				throw new UploadTaskException("Product Category Code doesn't exist", Reason.ERROR_GENERATE);
			} else {
				if (0 == tblProductCategory.getIsMapped()) {
					throw new UploadTaskException("Product Category is not mapped", Reason.ERROR_GENERATE);
				}
				
				if (null == tblProductCategory.getMsForm()) {
					throw new UploadTaskException("Product Category have not been mapped with form", Reason.ERROR_GENERATE);
				} else {
					this.getManagerDAO().fetch(tblProductCategory.getMsForm());
					if ("0".equalsIgnoreCase(tblProductCategory.getMsForm().getIsActive())) {
						throw new UploadTaskException("Form is not active", Reason.ERROR_GENERATE);
					}
				}
			}
				
			Map distributionResult = taskDistributionLogic.poolingTaskDistribution(tblProductCategory.getTblProductCategoryId(), taskDukcapilBean.getNik(),
					taskDukcapilBean.getSubzipcode(), taskDukcapilBean.getDealerCode(), tblProductCategory.getJenisPembiayaan(), null, "1", null, null, "0", callerId);
			if (distributionResult.get("errMsg") != null && StringUtils.isNotBlank(distributionResult.get("errMsg").toString())) {
				distributionResult = taskDistributionLogic.poolingTaskDistribution(tblProductCategory.getTblProductCategoryId(), taskDukcapilBean.getNik(),
					taskDukcapilBean.getSubzipcode(), taskDukcapilBean.getDealerCode(), tblProductCategory.getJenisPembiayaan(), null, "0", null, null, "0", callerId);
				
				if (distributionResult.get("errMsg") != null && StringUtils.isNotBlank(distributionResult.get("errMsg").toString())) {
					throw new UploadTaskException(messageSource.getMessage("businesslogic.uploadtask.mappingnotfound",
							null, this.retrieveLocaleAudit(callerId)), Reason.ERROR_GENERATE);
				}
				
			} else {
				List <BigInteger> listUser = (List<BigInteger>) distributionResult.get("listUser");
				if (listUser.isEmpty()) {
					distributionResult = taskDistributionLogic.poolingTaskDistribution(tblProductCategory.getTblProductCategoryId(), taskDukcapilBean.getNik(),
							taskDukcapilBean.getSubzipcode(), taskDukcapilBean.getDealerCode(), tblProductCategory.getJenisPembiayaan(), null, "0", null, null, "0", callerId);
					
					if (distributionResult.get("errMsg") != null && StringUtils.isNotBlank(distributionResult.get("errMsg").toString())) {
						throw new UploadTaskException(messageSource.getMessage("businesslogic.uploadtask.mappingnotfound",
								null, this.retrieveLocaleAudit(callerId)), Reason.ERROR_GENERATE);
					} 
				}  
			}
				
			List <BigInteger> listUser = (List<BigInteger>) distributionResult.get("listUser");
				
			if (!listUser.isEmpty()) {
				if (1 != listUser.size()) {
					listUser = taskDistributionLogic.assignByLoad(listUser, tblProductCategory.getJenisPembiayaan(), null, null, null, null, "0", callerId);
				}
				amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
						new Object[][] { {Restrictions.eq("uuidMsUser", listUser.get(0).longValue())} });
				msBranch = amMsuser.getMsBranch();
				
			} else if (StringUtils.isNotBlank(officeCode)) {
				msBranch = commonLogic.retrieveBranchByCode(officeCode, true, callerId);
			} else {
				
				throw new UploadTaskException(messageSource.getMessage("businesslogic.uploadtask.mappingnotfound",
						null, this.retrieveLocaleAudit(callerId)), Reason.ERROR_GENERATE);
			}
			
			//end distribusi task
			
			AmMssubsystem amMssubsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, null);
			MsForm msForm = commonLogic.retrieveMsFormByName(GlobalVal.FORM_NEW_LEAD, true, amMssubsystem.getUuidMsSubsystem(), null);
			
			Map mapValidateDukcapil = this.validateDukcapil(taskDukcapilBean, callerId);
			if(mapValidateDukcapil.get("d0") == null){
				throw new UploadTaskException(messageSource.getMessage("businesslogic.uploadtask.dukcapilerror",
						null, this.retrieveLocaleAudit(callerId)), Reason.ERROR_GENERATE);
			}	
			int validateDukcapil = (int) mapValidateDukcapil.get("d0");
			taskDukcapilBean.setIsMatchDukcapil(String.valueOf(validateDukcapil));
			String idHistCust = null != mapValidateDukcapil.get("idHistCust") ? String.valueOf(mapValidateDukcapil.get("idHistCust")): null;
			taskDukcapilBean.setIdHistCust(idHistCust);
			
			Date dateNow = new Date();
			TrTaskH taskHOrder = new TrTaskH();
			taskHOrder.setCustomerName(request.getCustName());
			taskHOrder.setCustomerAddress(request.getLegalAddr());
			taskHOrder.setCustomerPhone(request.getMobilePhnNo());
			taskHOrder.setUsrCrt(callerId.getCallerId());
			taskHOrder.setDtmCrt(dateNow);
			taskHOrder.setMsForm(msForm);
			taskHOrder.setFormVersion(Integer.parseInt(mapDetailBean.get("formVersion").toString()));
			applNo = this.commonLogic.retrieveNewTaskId(GlobalVal.SEQUENCE_ORDER_NO_CODE);
			taskHOrder.setApplNo(applNo);
			taskHOrder.setFlagSource(GlobalVal.SUBSYSTEM_MS);
			taskHOrder.setSubmitDate(dateNow);
			if (StringUtils.isNotBlank(taskDukcapilBean.getDealerCode())) {
				taskDukcapilBean.setDealerCode(StringUtils.deleteWhitespace((taskDukcapilBean.getDealerCode().split("-"))[0]));
			}
			taskHOrder.setDealerCode(StringUtils.deleteWhitespace((request.getDealerCode().split("-"))[1]));
			taskHOrder.setSubzipcode(request.getSubzipcode());
			taskHOrder.setZipCode(request.getLegalZipcode());
			
			taskHOrder.setAmMsuser(amMsuser);
			taskHOrder.setMsBranch(msBranch);
			
			//Priority
			MsPriority normalPriority = this.commonLogic.retrieveMsPriority(GlobalVal.TAKS_PRIORTY_NORMAL, true, null);
			taskHOrder.setMsPriority(normalPriority);
			
			taskHOrder.setIsDraft("0");
			taskHOrder.setReadDate(null);
			taskHOrder.setIsAppNotified("0");
			taskHOrder.setOrderId(request.getOrderId());
			
			//Update CIF DUKCAPIL taskH, update orderNo history
			if(taskDukcapilBean.getIdHistCust() != null){
			CheckCustomerHistory history = this.getManagerDAO().selectOne(CheckCustomerHistory.class, Long.valueOf(taskDukcapilBean.getIdHistCust()));
			if (null != history) {
				taskHOrder.setCifDukcapil("1".equals(history.getIsWise()) ? history.getCustomerNo() : history.getOrderNoTemp());
				history.setOrderNo(applNo);
				this.getManagerDAO().update(history);
			}
			}
			
			this.getManagerDAO().insert(taskHOrder);
			
			taskHOrder.setTaskId(String.valueOf(taskHOrder.getUuidTaskH()));
			this.getManagerDAO().update(taskHOrder);
			
			this.saveGroupTaskSurvey(taskHOrder, callerId);
			
			//set default answer
			if (null != submitTaskDBean) {			
				ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(callerId);
				Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
						? this.imageStorageLogic.retrieveGsImageFileSystemPath(callerId) : null;
						
				boolean saveAsJson = PropertiesHelper.isTaskDJson();
				if (saveAsJson) {
	    			this.saveTaskDIntoJson(submitTaskDBean, taskHOrder, true, msQuestions, isl, imagePath, callerId);
	    		} else {
	    			Map<Long, TrTaskD> listTaskD = this.listTaskDAsMap(String.valueOf(taskHOrder.getUuidTaskH()));
	    		    this.saveTaskDIntoRow(submitTaskDBean, taskHOrder, false, true, msQuestions, listTaskD, isl, imagePath, callerId);
	    		}
			}
			//commit wf 
			long uuidProcess = this.getUuidProcess(taskHOrder, amMssubsystem);
			commitWfAndUpdateTaskH(taskHOrder, uuidProcess, amMssubsystem, 0);
			MsStatustask msStatustask = commitWfAndUpdateTaskH(taskHOrder, uuidProcess, amMssubsystem, 0);
			//get status from workflow
			Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_SUBMITTED) } };
			MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
			taskHOrder.setMsStatusmobile(msm);

			String notes = "Task New Lead has been submitted.";
			String codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
			
			//INSERT INTO CHECK HISTORY
			AmMsuser userHistory = new AmMsuser();
			userHistory.setFullName(callerId.getCallerId());
			insertTaskHistory(callerId, msStatustask, taskHOrder, notes, codeProcess, userHistory.getFullName(), userHistory, null);
			
			if (validateDukcapil != 0) {
				if (validateDukcapil == 2) {
					message = "Not Found Data Dukcapil";
					code = 2;
					taskHOrder.setNotes(appendNotes(taskHOrder.getNotes(), "Result: " + message) );
					taskHOrder.setIsMatchDukcapil("0");
					this.getManagerDAO().update(taskHOrder);
				} else {
					message = "SUCCESS";
					code = 1;
					taskHOrder.setNotes(appendNotes(taskHOrder.getNotes(), "Result: Match Data Dukcapil") );
					taskHOrder.setIsMatchDukcapil("1");
					this.getManagerDAO().update(taskHOrder);
					this.createTaskSurvey(taskHOrder, tblProductCategory, amMsuser, msBranch, callerId, request.getResKelurahan());
				}
			} else {
				message = "No Match Data Dukcapil";
				code = 2;
				taskHOrder.setNotes(appendNotes(taskHOrder.getNotes(), "Result: " + message) );
				taskHOrder.setIsMatchDukcapil("0");
				AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
				Object[][] params2 = {
						{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_FAILED_ASSIGNMENT) },
						{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidMsSub.getUuidMsSubsystem()) } };
				msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
				taskHOrder.setMsStatustask(msStatustask);
				taskHOrder.setDtmUpd(new Date());
				taskHOrder.setUsrUpd("SYSTEM");
		
				//INSERT INTO CHECK HISTORY
				codeProcess = GlobalVal.CODE_PROCESS_FAILED;
				insertTaskHistory(callerId, msStatustask, taskHOrder, taskHOrder.getNotes(), 
						codeProcess, userHistory.getFullName(), userHistory, null);
				this.getManagerDAO().update(taskHOrder);
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new RemoteException(e.getMessage());
		}
		
		response.setCode(code);
		response.setMessage(message);
		response.setOrderId(request.getOrderId());
		response.setOrderNo(applNo);
		response.setMtType("Api Feedback Main Dealer");
		
		return response;
	}
	
	private Map<String, Object> generateDetailBean(Map<String, String> mapJsonRequest, AuditContext callerId) {
		Map<String, Object> mapResult = new HashMap<>();
		
		List<SubmitTaskDBean> listTaskDBean = new ArrayList<>();
		Map<Integer, MsQuestion> mapAllQuestion = new HashMap<>();
		Map<String, String> mapJsonRequestByRefId = new HashMap<>();
		
		TaskDukcapilBean taskDukcapilBean = new TaskDukcapilBean();
		taskDukcapilBean.setOrderDt(new Date());
		taskDukcapilBean.setStatus("REQ");
		taskDukcapilBean.setUsrCrt(callerId.getCallerId());
		taskDukcapilBean.setDtmCrt(new Date());
		
		Object[] formVersion = (Object[]) this.getManagerDAO().selectOneNativeString(
				"SELECT     TOP(1) UUID_FORM_HISTORY, FORM_VERSION " +
				"FROM       MS_FORMHISTORY MSFH WITH (NOLOCK) " +
				"JOIN       MS_FORM MSF WITH (NOLOCK) ON MSF.UUID_FORM = MSFH.UUID_FORM " +
				"WHERE      MSF.FORM_NAME = :formName " +
				"ORDER BY   FORM_VERSION DESC", new Object[][] { {"formName", GlobalVal.FORM_NEW_LEAD} });
		
		int index = 0;
		int size = mapJsonRequest.size();
		for (Map.Entry<String, String> entry : mapJsonRequest.entrySet()) {
			String mapKey = entry.getKey();
			
			BigInteger idQuestion = null;
			List listQuestionIdFromColName = this.getManagerDAO().selectAllNativeString(
					"SELECT UUID_QUESTION, TMTD.REF_ID " +
					"FROM   TBL_MAP_TASK_D TMTD WITH(NOLOCK) " +
					"JOIN   MS_QUESTION MSQ WITH (NOLOCK) ON TMTD.REF_ID = MSQ.REF_ID " +
					"WHERE  TMTD.COL_NAME = :colName AND ANSWER_VALUE IS NULL AND SOURCE_DATA IS NULL ", new Object[][] { {"colName", mapKey} });
				for (int i = 0; i < listQuestionIdFromColName.size(); i++) {
				Map mapQuestion = (Map) listQuestionIdFromColName.get(i);
				if (StringUtils.contains(String.valueOf(mapQuestion.get("d1")), "STG")) {
					idQuestion = (BigInteger) mapQuestion.get("d0");
				}
			}
			
			if (StringUtils.isBlank(mapKey) || null == idQuestion) {
				size = size - 1;
				continue;
			}
			
			Object[][] prm = { {Restrictions.eq("msFormhistory.uuidFormHistory", Long.parseLong(formVersion[0].toString()))},
					{Restrictions.eq("msQuestion.uuidQuestion", idQuestion.longValue())} };
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);			
			this.getManagerDAO().fetch(qset.getMsAnswertype());
			
			if (StringUtils.isBlank(entry.getValue()) && "1".equals(qset.getIsMandatory())) {
				Object[] obj = {entry.getKey()};
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)),
						Reason.ERROR_NOT_EXIST);
			}
			
			MsQuestion msQuestion = qset.getMsQuestion();
			
			mapAllQuestion.put(index, msQuestion);
			
			SubmitTaskDBean bean = new SubmitTaskDBean();
			String answer = entry.getValue();
			
			if (GlobalVal.STG_PROD_CODE.equalsIgnoreCase(msQuestion.getRefId())) {
				Object [][] paramProdCode = { {"prodName", answer} };
				Object code[] = (Object[]) this.getManagerDAO().selectOneNativeString(
						"SELECT PRODUCT_CATEGORY_CODE, JENIS_PEMBIAYAAN FROM TBL_PRODUCT_CATEGORY WITH (NOLOCK) WHERE PRODUCT_CATEGORY_NAME = :prodName", paramProdCode);
				taskDukcapilBean.setProdCode((String)code[0]);
				
				//add support choice filter {$BRANCH_TYPE}
				String konvenSyariah = (String)code[1];
				if ("K".equalsIgnoreCase(konvenSyariah)) {
					konvenSyariah = "KONVENSIONAL";
				} else if ("S".equalsIgnoreCase(konvenSyariah)) {
					konvenSyariah = "SYARIAH";
				}
				mapJsonRequestByRefId.put("$BRANCH_TYPE", konvenSyariah);
			}
			
			
			bean.setUuid_task_d(this.getManagerDAO().getUUID());
			bean.setQuestion_id(String.valueOf(msQuestion.getUuidQuestion()));
			bean.setQuestion_group_id(String.valueOf(qset.getMsQuestiongroup().getUuidQuestionGroup()));
			if (qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO) ||
					qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION) ||
					qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN) ||
					qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION) ||
					qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION) ) {
				Object [] msLov = this.getMsLovByDescription(qset.getChoiceFilter(), qset.getLovGroup(), answer, mapJsonRequestByRefId, msQuestion.getRefId());
				if (null != msLov) {
					bean.setOption_answer_id(String.valueOf(msLov[0]));
					bean.setLov(String.valueOf(msLov[1]));
					
					if (GlobalVal.STG_SOA.equalsIgnoreCase(msQuestion.getRefId())) {
						answer = String.valueOf(msLov[2]);
					} else {
						answer = String.valueOf(msLov[1]);
					}
				} else {
					Object[] obj = {entry.getKey()};
					throw new UploadTaskException(this.messageSource.getMessage(
							"businesslogic.uploadtask.valnotfound", obj, this.retrieveLocaleAudit(callerId)),
							Reason.ERROR_NOT_EXIST);
				}
			} else {
				bean.setText_answer(answer);
			}
			
			bean.setQuestion_label(qset.getQuestionLabel());
			bean.setIs_final("0");
			listTaskDBean.add(bean);
			
			mapJsonRequestByRefId.put(qset.getRefId(), answer);
			
			if (GlobalVal.STG_NIK.equals(msQuestion.getRefId())) {
				taskDukcapilBean.setNik(answer);
			} else if (GlobalVal.STG_CUST_NAME.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setCustName(answer);
			} else if (GlobalVal.STG_BIRTH_PLACE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setBirthPlace(answer);
			} else if (GlobalVal.STG_BIRTH_DT.equalsIgnoreCase(msQuestion.getRefId())) {
				try {
					SimpleDateFormat format1 = new SimpleDateFormat("dd/MM/yyyy");
					SimpleDateFormat format2 = new SimpleDateFormat("dd MMMM yyyy");
					Date date = format1.parse(answer);
					taskDukcapilBean.setBirthDt(format2.format(date));
				} catch (ParseException e) {
					LOG.error(e.getMessage(), e);
				}
			} else if (GlobalVal.STG_LEGAL_ADDR.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.STG_LEGAL_ADDR_PMHON.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalAddr(answer);
			} else if (GlobalVal.STG_LEGAL_RT.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_RT.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalRt(answer);
			} else if (GlobalVal.STG_LEGAL_RW.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_RW.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalRw(answer);
			} else if (GlobalVal.STG_LEGAL_PROVINSI.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_PROVINSI.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalProvinsi(answer);
			} else if (GlobalVal.STG_LEGAL_CITY.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_KOTA.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalCity(answer);
			} else if (GlobalVal.STG_LEGAL_KECAMATAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_KECAMATAN.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalKecamatan(answer);
			} else if (GlobalVal.STG_LEGAL_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalKelurahan(answer);
			} else if (GlobalVal.STG_LEGAL_ZIPCODE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalZipcode(answer);
			} else if (GlobalVal.STG_NAMA_IBU.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setMotherName(answer);
			} else if (GlobalVal.STG_RES_ADDR.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.STG_ADDR_PMHON.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceAddr(answer);
			} else if (GlobalVal.STG_RES_RT.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_RT.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceRt(answer);
			} else if (GlobalVal.STG_RES_RW.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_RW.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceRw(answer);
			} else if (GlobalVal.STG_RES_PROVINSI.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_PROVINSI.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceProvinsi(answer);
			} else if (GlobalVal.STG_RES_CITY.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_CITY.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceCity(answer);
			} else if (GlobalVal.STG_RES_KECAMATAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_KECAMATN.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceKecamatan(answer);
			} else if (GlobalVal.STG_RES_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_KELURAHN.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceKelurahan(answer);
			} else if (GlobalVal.STG_RES_ZIPCODE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceZipcode(answer);
			} else if (GlobalVal.STG_MOBILE_PHN_NO.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.STG_NO_HANDPHONE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setMobilePhnNo(answer);
			} else if (GlobalVal.STG_PROD_CODE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setProdCode(answer);
			} else if (GlobalVal.STG_SUBZIPCODE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setSubzipcode(answer);
			} else if (GlobalVal.STG_DEALER_CODE.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.STG_DEALERCODE_LEAD.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setDealerCode(answer);
			} else if (GlobalVal.STG_SOA.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setSoa(answer);
			} else if (GlobalVal.STG_EXT_AGR_NO.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setExistingAgrmntNo(answer);
			} else if (GlobalVal.NOTES.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setCatatanCust(answer);
			}
			
			index++;
		}
		
		MsQuestion msq = this.commonLogic.retrieveQuestionByRefId(GlobalVal.STG_IS_EQ_ALMT_TGL, callerId);
		Object[][] prm = { {Restrictions.eq("msFormhistory.uuidFormHistory", Long.parseLong(formVersion[0].toString()))},
				{Restrictions.eq("msQuestion.uuidQuestion", msq.getUuidQuestion())} };
		MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
		if (null != qset) {
			MsQuestion msQuestion = qset.getMsQuestion();
			
			mapAllQuestion.put(index, msQuestion);
			
			SubmitTaskDBean bean = new SubmitTaskDBean();
			String answer = "0";
			
			bean.setUuid_task_d(this.getManagerDAO().getUUID());
			bean.setQuestion_id(String.valueOf(msQuestion.getUuidQuestion()));
			bean.setQuestion_group_id(String.valueOf(qset.getMsQuestiongroup().getUuidQuestionGroup()));
			if (qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO) ||
					qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION) ||
					qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN) ||
					qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION) ||
					qset.getMsAnswertype().getCodeAnswerType().equalsIgnoreCase(GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION) ) {
				Object[] msLov = this.getMsLovByDescription(qset.getChoiceFilter(), qset.getLovGroup(), answer, mapJsonRequestByRefId, qset.getRefId());
				if (null != msLov) {
					bean.setOption_answer_id(String.valueOf(msLov[0]));
					bean.setLov(String.valueOf(msLov[1]));
				}
			} else {
				bean.setText_answer(answer);
			}
			
			bean.setQuestion_label(qset.getQuestionLabel());
			bean.setIs_final("0");
			
			listTaskDBean.add(bean);
		}
		
		listTaskDBean.get(listTaskDBean.size() - 1).setIs_final("1");
		
		mapResult.put("formVersion", formVersion[1]);
		mapResult.put("submitTaskDBean", listTaskDBean.toArray(new SubmitTaskDBean[listTaskDBean.size()]));
		mapResult.put("msQuestions", mapAllQuestion);
		mapResult.put("taskDukcapilBean", taskDukcapilBean);
		
		return mapResult;
	}
	
	private Object[] getMsLovByDescription(String choiceFilter, String lovGroup, String value, Map<String, String> mapQuestionByRefId, String refId) {
		Stack<Object[]> paramStack = new Stack<>();
		String whereConstraint = StringUtils.EMPTY;
		if (StringUtils.isNotBlank(choiceFilter)) {
			StringBuilder sb = new StringBuilder();
			String[] choiceFilterArr = choiceFilter.split(",");
			for (int i = 0; i < choiceFilterArr.length; i++) {
				String cons = choiceFilterArr[i].substring(1, choiceFilterArr[i].length() - 1);
				String consValue = String.valueOf(mapQuestionByRefId.get(cons));
				
				sb.append("       AND CONSTRAINT_").append(i + 1).append(" = :constraint").append(i + 1);
				paramStack.push(new Object[] {"constraint" + (i + 1), consValue});
			}
			whereConstraint = sb.toString();
		}
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT TOP 1 UUID_LOV, CODE, DESCRIPTION ")
			.append("FROM   MS_LOV WITH (NOLOCK) ")
			.append("WHERE  LOV_GROUP = :lovGroup ")
			.append("       AND IS_ACTIVE = :isActive ")
			.append("       AND IS_DELETED = :isDeleted ");
		if(GlobalVal.STG_SUBZIPCODE.equalsIgnoreCase(refId)) {
			queryBuilder.append("       AND CODE = :code ");
			paramStack.push(new Object[] {"code", value});
		} else {
			queryBuilder.append("       AND DESCRIPTION = :description ");
			paramStack.push(new Object[] {"description", value});
		}
			
		queryBuilder.append(whereConstraint);
		paramStack.push(new Object[] {"lovGroup", lovGroup});
		paramStack.push(new Object[] {"isActive", "1"});
		paramStack.push(new Object[] {"isDeleted", "0"});
		
		Object[][] sqlParams = new Object[paramStack.size()][2];
	    for (int k = 0; k < paramStack.size(); k++) {
			Object[] objects = paramStack.get(k);
			sqlParams[k] = objects;
		}
	    
	    return (Object[]) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
	}
	
	private void saveGroupTaskSurvey(TrTaskH taskHSurvey, AuditContext auditContext) {
		MsGrouptask msGrouptask = new MsGrouptask();
		msGrouptask.setGroupTaskId(taskHSurvey.getUuidTaskH());
		msGrouptask.setTrTaskH(taskHSurvey);
		msGrouptask.setMsBranch(taskHSurvey.getMsBranch());
		msGrouptask.setUsrCrt(auditContext.getCallerId());
		msGrouptask.setDtmCrt(new Date());
		msGrouptask.setCustomerName(taskHSurvey.getCustomerName());
		msGrouptask.setApplNo(taskHSurvey.getApplNo());
		this.getManagerDAO().insert(msGrouptask);
	}
	
	private void saveTaskDIntoJson(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, boolean flag,
			Map<Integer, MsQuestion> msQuestions, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {		
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		TaskDocumentBean document = null;
		boolean newDoc = false;
		if (docDb == null) {
			docDb = new TrTaskdocument(trTaskH, new Date(), auditContext.getCallerId());
			document = new TaskDocumentBean();
			newDoc = true;
		} else {
			document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
			docDb.setDtmUpd(new Date());
			docDb.setUsrUpd(auditContext.getCallerId());
			newDoc = false;
		}
		
		List<AnswerBean> answers = (newDoc) ? new ArrayList<>(taskDBean.length) : document.getAnswers();		
		if (newDoc) {
			document.setAnswers(answers);
		}
		
		TrTaskcolldata taskCollData = this.getManagerDAO().selectOne(TrTaskcolldata.class, trTaskH.getUuidTaskH());
		if (taskCollData == null){
			taskCollData = new TrTaskcolldata();
			taskCollData.setTrTaskH(trTaskH);
		}
		for (int i=0; i < taskDBean.length; i++) {			
			MsQuestion msQuestion = msQuestions.get(i);
			String answerTypeCode = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String optionAnswerId = StringUtils.stripToEmpty(taskDBean[i].getOption_answer_id());
			String questionText = StringUtils.stripToEmpty(taskDBean[i].getQuestion_label());
			String textAnswer = StringUtils.stripToEmpty(taskDBean[i].getText_answer());
			String latitude = StringUtils.stripToNull(taskDBean[i].getLatitude());						
			String longitude = StringUtils.stripToNull(taskDBean[i].getLongitude());
			String mcc = StringUtils.stripToNull(taskDBean[i].getMcc());
			String mnc = StringUtils.stripToNull(taskDBean[i].getMnc());
			String lac = StringUtils.stripToNull(taskDBean[i].getLac());				
			String cellId = StringUtils.stripToNull(taskDBean[i].getCid());				
			String accuracy = StringUtils.stripToNull(taskDBean[i].getAccuracy());	
			String image = StringUtils.stripToNull(taskDBean[i].getImage());
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerTypeCode) &&
					StringUtils.isNotEmpty(textAnswer) && StringUtils.containsAny(textAnswer, ',', '.')) {
				textAnswer = textAnswer.substring(0, textAnswer.length()-2).replace(",", "").replace(".", "");				
			}
			
			int idxAnswer = document.findAnswerIndex(msQuestion.getUuidQuestion());
			
			AnswerBean answer = this.constructAnswer(
					document, idxAnswer, msQuestion, answerTypeCode, questionText, textAnswer, optionAnswerId,
					latitude, longitude, accuracy, mcc, mnc, lac, cellId, trTaskH, image, isl, imagePath, auditContext);
			
			if (idxAnswer == -1) {
				answers.add(answer);
			}
		}
		docDb.setDocument(gson.toJson(document, TaskDocumentBean.class));
		
		if (newDoc) {
			this.getManagerDAO().insert(docDb);
		} else {
			this.getManagerDAO().update(docDb);
		}
	}
	
	private AnswerBean constructAnswer(TaskDocumentBean document, int idxAnswer, MsQuestion msQuestion,
			String answerTypeCode, String questionText, String textAnswer, String optionAnswerId,
			String latitude, String longitude, String accuracy, String mcc, String mnc, String lac, String cellId,
			TrTaskH trTaskH, String image, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		AnswerBean answer = (idxAnswer == -1) ? new AnswerBean() : document.getAnswers().get(idxAnswer);
		QuestionBean questionBean = new QuestionBean(
				msQuestion.getUuidQuestion(), questionText, msQuestion.getRefId(), answerTypeCode,
				(msQuestion.getMsOrdertag() == null ? null : msQuestion.getMsOrdertag().getTagName()),
				(msQuestion.getMsAssettag() == null ? null : msQuestion.getMsAssettag().getAssetTagName()),
				(msQuestion.getMsCollectiontag() == null ? null : msQuestion.getMsCollectiontag().getTagName()));
		answer.setQuestion(questionBean);
		
		if (MssTool.isChoiceQuestion(answerTypeCode)) {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			List<OptionBean> options = (answer.getOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getOptAnswers();
			
			MsLov msLovByLovId = null; 
			if (StringUtils.isNotBlank(optionAnswerId)) {
				msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
			}
			if (msLovByLovId != null) {
				OptionBean option = new OptionBean();
				option.setUuid(msLovByLovId.getUuidLov());
				option.setCode(msLovByLovId.getCode());
				option.setDesc(msLovByLovId.getDescription());
				option.setFreeText(StringUtils.trimToNull(textAnswer));
				if (!options.contains(option)) {
					options.add(option);
				}
				
				answer.setOptAnswers(options);
			}				
		} else if (MssTool.isImageQuestion(answerTypeCode) || GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
			answer.setOptAnswers(null);
			Boolean isGps = null;
			Boolean isConverted = null;
			
			if (mcc != null) {
				com.adins.mss.model.taskdjson.LocationBean locationBean = (answer.getLocation() == null) ?
						new com.adins.mss.model.taskdjson.LocationBean() : answer.getLocation();
				locationBean.setMcc(checkEmptyInteger(mcc));
				locationBean.setMnc(checkEmptyInteger(mnc));
				locationBean.setLac(checkEmptyInteger(lac));
				locationBean.setCid(checkEmptyInteger(cellId));
				locationBean.setAccuracy(checkEmptyInteger(accuracy));
				
				if (latitude != null && longitude != null
						&& new BigDecimal(latitude).intValue() != 0 && new BigDecimal(longitude).intValue() != 0) {
					locationBean.setLat(checkEmptyBigdecimal(latitude).doubleValue());
					locationBean.setLng(checkEmptyBigdecimal(longitude).doubleValue());
					locationBean.setIsGps(1);
				} else {
					LocationBean convertedLocation = this.getLocationByCellId(locationBean.getMcc(), locationBean.getMnc(),
							locationBean.getLac(), locationBean.getCid(), auditContext);
					if (convertedLocation.getCoordinate() != null) {
						locationBean.setAccuracy(convertedLocation.getAccuracy());
						locationBean.setLat(convertedLocation.getCoordinate().getLatitude());
						locationBean.setLng(convertedLocation.getCoordinate().getLongitude());
						locationBean.setIsGps(0);
						locationBean.setIsConverted(1);
						
						latitude = Double.toString(convertedLocation.getCoordinate().getLatitude());
						longitude = Double.toString(convertedLocation.getCoordinate().getLongitude());
						accuracy = Integer.toString(convertedLocation.getAccuracy());
					}
					isGps = Boolean.FALSE;
					isConverted = Boolean.TRUE;
				}
				
				answer.setLocation(locationBean);
			}

			if (MssTool.isImageQuestion(answerTypeCode)) {
				ImageBean imageBean = (answer.getLobAnswer() == null) ? new ImageBean() : answer.getLobAnswer();
				if (image != null && !image.isEmpty()) {
					imageBean.setHasImage(true);
					answer.setLobAnswer(imageBean);
				}
				
				//insert into Table TR_TASKDETAILLOB
				String uuidTaskD = this.getManagerDAO().getUUID();
				long idLob = this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, isGps, isConverted, isl, imagePath);
				if (idLob > 0L){
					imageBean.setId(idLob);
				}
			} else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
				answer.setTxtAnswer(textAnswer);
			}
		} else {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)
					|| GlobalVal.ANSWER_TYPE_DATETIME.equals(answerTypeCode)) {
				textAnswer = this.dateAnswerToText(textAnswer, answerTypeCode);
			}
			answer.setOptAnswers(null);
			answer.setTxtAnswer(textAnswer);
		}
		
		return answer;
	}
	
	private BigDecimal checkEmptyBigdecimal(String in){
		if (StringUtils.isBlank(in)) {
			return new BigDecimal(0);
		}
		else{
			return new BigDecimal(in);		
		}
	}
	
	private Integer checkEmptyInteger(String in){		
		if (StringUtils.isBlank(in)){
			return null;
		} else {
			return Integer.valueOf((int) Math.round(new Double(in)));		
		}
	}
	
	private String dateAnswerToText(String dateAnswer, String answerTypeCode) {
		DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
		try {
			Date result = df.parse(dateAnswer);
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)) {
				return (null != result) ? new SimpleDateFormat("dd/MM/yyyy").format(result) : null;
			} else {
				return (null != result) ? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result) : null;
			}
		} catch (ParseException e) {
			LOG.warn("Cannot parse '{}' to answer type {}", dateAnswer, answerTypeCode, e);
			return null;
		}
	}
	
	private Map<Long, TrTaskD> listTaskDAsMap(String uuidTaskH) {
		List<TrTaskD> result = this.listTaskD(uuidTaskH);
		
		if (result == null || result.isEmpty()){
			return Collections.emptyMap();
		}
		
		Map<Long, TrTaskD> resultMap = new HashMap<>(result.size());
		for (Iterator iterator = result.iterator(); iterator.hasNext();) {
			TrTaskD trTaskD = (TrTaskD) iterator.next();
			resultMap.put(trTaskD.getMsQuestion().getUuidQuestion(), trTaskD);
		}
		
		return resultMap;
	}
	
	private List<TrTaskD> listTaskD(String uuidTaskH) {
		if (StringUtils.isBlank(uuidTaskH)) {
			return Collections.emptyList();
		}
		
		Object[][] params = { {Restrictions.eq("trTaskH.uuidTaskH", Long.valueOf(uuidTaskH))} };
		Map<String, Object> result = this.getManagerDAO().list(TrTaskD.class, params, null);
		
		return (List<TrTaskD>) result.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	private void getLocationByCellId(TrTaskD taskD, AuditContext auditContext){
		List<LocationBean> listLocations = new ArrayList<>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(taskD.getCellId());
		locationBean.setLac(taskD.getLac());
		locationBean.setMcc(taskD.getMcc());
		locationBean.setMnc(taskD.getMnc());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			taskD.setLatitude(BigDecimal.valueOf(locationBean.getCoordinate().getLatitude()));
			taskD.setLongitude(BigDecimal.valueOf(locationBean.getCoordinate().getLongitude()));
			taskD.setAccuracy(locationBean.getAccuracy());
			
			taskD.setTextAnswer("Coord : "+taskD.getLatitude().setScale(6, RoundingMode.HALF_UP)+", "+taskD.getLongitude().setScale(6, RoundingMode.HALF_UP)+" Accuracy : "+taskD.getAccuracy()+" m");
			taskD.setIsConverted("1");
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private long insertDetailLob(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD,
			String questionText, String base64Image, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Boolean isGps, Boolean isConverted, ImageStorageLocation saveImgLoc, Path imgLoc){
		
		Object[][] params = { 
				{Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
				{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())} 
		};
		TrTaskdetaillob trTaskdetaillob = this.getManagerDAO().selectOne(TrTaskdetaillob.class, params);

		if (trTaskdetaillob == null) {
			trTaskdetaillob = new TrTaskdetaillob();
			trTaskdetaillob.setUsrCrt(auditContext.getCallerId());
			trTaskdetaillob.setDtmCrt(new Date());
		} 
		else {
			trTaskdetaillob.setUsrUpd(auditContext.getCallerId());
			trTaskdetaillob.setDtmUpd(new Date());
		}
		
		trTaskdetaillob.setTrTaskH(trTaskH);
		trTaskdetaillob.setMsQuestion(msQuestion);
		
		if(GlobalVal.ANSWER_TYPE_OCR.equalsIgnoreCase(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			String[] ocr = base64Image.split("@@@");
			base64Image = ocr[0];
			String result = URLDecoder.decode(ocr[1]);
			
			trTaskdetaillob.setTextAnswer(result);
		}
		
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				trTaskdetaillob.setLobFile(BaseEncoding.base64().decode(base64Image));
			}
		}
		else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
				Date date = new Date();
				Path outputPath = Paths.get(imgLoc.toString(), dateFormat.format(date));

				String absoluteFilename = trTaskH.getTaskId() + "_" + msQuestion.getRefId() + "_"
				        + uuidTaskD + ".jpg";
				
				String outputFile = this.imageStorageLogic.storeImageFileSystem(
				        BaseEncoding.base64().decode(base64Image), outputPath, absoluteFilename);
				trTaskdetaillob.setImagePath(outputFile);
			}
		}
		trTaskdetaillob.setQuestionText(questionText);
		trTaskdetaillob.setLatitude(checkEmptyBigdecimal(latitude));
		trTaskdetaillob.setLongitude(checkEmptyBigdecimal(longitude));
		
		if (Boolean.TRUE.equals(isGps) || (trTaskdetaillob.getLatitude()!=null && trTaskdetaillob.getLongitude()!=null &&
				trTaskdetaillob.getLatitude().intValue()!=0 && trTaskdetaillob.getLongitude().intValue()!=0) &&
				!Boolean.TRUE.equals(isConverted)) {
			trTaskdetaillob.setIsGps("1");
		} else if (Boolean.FALSE.equals(isGps)) {
			trTaskdetaillob.setIsGps("0");
		}
		
		trTaskdetaillob.setMcc(checkEmptyInteger(mcc));
		trTaskdetaillob.setMnc(checkEmptyInteger(mnc));
		trTaskdetaillob.setLac(checkEmptyInteger(lac));
		trTaskdetaillob.setCellId(checkEmptyInteger(cellId));
		trTaskdetaillob.setAccuracy(checkEmptyInteger(accuracy));
		if (!Boolean.TRUE.equals(isConverted) && !"1".equals(trTaskdetaillob.getIsGps()) && trTaskdetaillob.getMcc() != null && trTaskdetaillob.getMnc() != null
				&& trTaskdetaillob.getLac() != null && trTaskdetaillob.getCellId() != null) {
			this.getLocationByCellId(trTaskdetaillob, auditContext);
		} else if (Boolean.TRUE.equals(isConverted)) {
			trTaskdetaillob.setIsConverted("1");
		}
		
		//penambahan save ke table TR_TASKSURVEYDATA
		if (msQuestion.getMsAssettag() != null) {
			Object paramsSurveyData[][] = { 
					{Restrictions.eq("uuidTaskId", trTaskH.getUuidTaskH())} 
			};
			TrTasksurveydata trTasksurveydata = this.getManagerDAO().selectOne(TrTasksurveydata.class, paramsSurveyData);
			TrTasksurveydata taskSurveyDataInsert = new TrTasksurveydata();
			if (trTasksurveydata == null) {
				taskSurveyDataInsert.setUsrCrt(auditContext.getCallerId());
				taskSurveyDataInsert.setDtmCrt(new Date());
				taskSurveyDataInsert.setTrTaskH(trTaskH);
				taskSurveyDataInsert.setUuidTaskId(trTaskH.getUuidTaskH());
			} else {
				taskSurveyDataInsert = trTasksurveydata;
				taskSurveyDataInsert.setUsrUpd(auditContext.getCallerId());
				taskSurveyDataInsert.setDtmUpd(new Date());
			}
			
			if (GlobalVal.ASSET_TAG_HOME.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setHomeLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setHomeLongitude(trTaskdetaillob.getLongitude());
			} else if (GlobalVal.ASSET_TAG_IDENTITY.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setLegalAddrLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setLegalAddrLongitude(trTaskdetaillob.getLongitude());
			} else if (GlobalVal.ASSET_TAG_OFFICE.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setOfficeLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setOfficeLongitude(trTaskdetaillob.getLongitude());
			} else if (GlobalVal.ASSET_TAG_STREET.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setDrivewayLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setDrivewayLongitude(trTaskdetaillob.getLongitude());
			} else if (GlobalVal.ASSET_TAG_VEHICLE.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setVehicleLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setVehicleLongitude(trTaskdetaillob.getLongitude());
			}
			
			if (trTasksurveydata == null) {
				this.getManagerDAO().insert(taskSurveyDataInsert);
			} else {
				this.getManagerDAO().update(taskSurveyDataInsert); //DAO will do saveOrUpdate
			}
		}	

		//end penambahan save ke table TR_TASKSURVEYDATA
		this.getManagerDAO().update(trTaskdetaillob); //DAO will do saveOrUpdate
		
		if (ImageStorageLocation.DMS == saveImgLoc) {
			UploadImageRequestBean request = new UploadImageRequestBean();
			UploadImageBean uploadBean  = new UploadImageBean();
			
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Object [][] param = {{"uuidTaskH", trTaskH.getTaskId()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT GROUP_TASK_ID " + 
					"FROM   MS_GROUPTASK with(nolock) " + 
					"WHERE  UUID_TASK_H = :uuidTaskH ", param);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(trTaskH.getMsForm().getUuidForm()));
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != trTaskH.getSubmitDate()) {
	        	taskDate = trTaskH.getSubmitDate();
	        } else {
	        	taskDate = new Date();
	        }
			try {
				cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
			} catch (ParseException e1) {
				LOG.error(e1.getMessage(), e1);
				e1.printStackTrace();
			}
			if(taskDate.before(cutoffDate)) {
				uploadBean.setId(trTaskH.getTaskId());
				uploadBean.setTaskId(trTaskH.getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				uploadBean.setId(groupTaskIdForm);
				uploadBean.setTaskId(groupTaskIdForm);
			}
	
	
			uploadBean.setType("survey");
			uploadBean.setJenisDoc(msQuestion.getQuestionLabel());
			uploadBean.setRefId(msQuestion.getRefId());
			uploadBean.setUsername(trTaskH.getAmMsuser().getUniqueId());
			if (StringUtils.isNotEmpty(base64Image)){
				trTaskdetaillob.setImagePath(String.valueOf(trTaskdetaillob.getUuidTaskDetailLob()));
				this.getManagerDAO().update(trTaskdetaillob);
				request.setByteImage(BaseEncoding.base64().decode(base64Image));
			}
			request.setFileName(trTaskdetaillob.getUuidTaskDetailLob()+".jpg");
			try {
				request.setIpAddress(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e) {
				LOG.error("Logging error DMS Host : {}",e.getMessage());
			}
			request.setUploadImageBean(uploadBean); 
			if (StringUtils.isNotEmpty(base64Image)){
				
				//PANGGIL KE INTNCFORMLOGIC
//				intFormLogic.uploadImageResponse(auditContext,request);
			}
		}
		
		return trTaskdetaillob.getUuidTaskDetailLob();
	}
	
	private void getLocationByCellId(TrTaskdetaillob detailLob, AuditContext auditContext){
		List<LocationBean> listLocations = new ArrayList<>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(detailLob.getCellId());
		locationBean.setLac(detailLob.getLac());
		locationBean.setMcc(detailLob.getMcc());
		locationBean.setMnc(detailLob.getMnc());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			detailLob.setLatitude(BigDecimal.valueOf(locationBean.getCoordinate().getLatitude()));
			detailLob.setLongitude(BigDecimal.valueOf(locationBean.getCoordinate().getLongitude()));
			detailLob.setAccuracy(locationBean.getAccuracy());
			
			detailLob.setTextAnswer("Coord : "+detailLob.getLatitude().setScale(6, RoundingMode.HALF_UP)+", "+detailLob.getLongitude().setScale(6, RoundingMode.HALF_UP)+" Accuracy : "+detailLob.getAccuracy()+" m");
			detailLob.setIsConverted("1");
		}
	}
	
	private LocationBean getLocationByCellId(int mcc, int mnc, int lac, int cellId,
			AuditContext auditContext) {
		List<LocationBean> listLocations = new ArrayList<>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(cellId);
		locationBean.setLac(lac);
		locationBean.setMnc(mnc);
		locationBean.setMcc(mcc);
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);
		return locationBean;
	}
	
	private void saveTaskDIntoRow(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, boolean flagRevisit, boolean flag,
			Map<Integer, MsQuestion> msQuestions, Map<Long, TrTaskD> listTaskD, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {				
		
		boolean hasBrand = false;
		boolean hasModel = false;
		boolean hasGroup = false;
		boolean hasType = false;
		String idAsset = StringUtils.EMPTY;
		
		Map<String, Object> mapAssetProduct = new HashMap<>();
		
		if (taskDBean.length > 1) {
			//delete multiple detail
			String[] answerTypeArr = {GlobalVal.ANSWER_TYPE_MULTIPLE, GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION,
					GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION};
			Object[][] paramsMulti = {
					{"uuidTaskH", trTaskH.getUuidTaskH()},
					{"answerType", Arrays.asList(answerTypeArr)} };
			this.getManagerDAO().deleteNativeString("delete d from tr_task_d d "
					+"left join ms_question q on d.uuid_question = q.uuid_question "
					+"left join ms_answertype at on q.uuid_answer_type = at.uuid_answer_type "
					+"where d.uuid_task_h = :uuidTaskH and "
					+"at.code_answer_type in (:answerType)", paramsMulti);
		}
		
		for (int i = 0; i < taskDBean.length; i++) {
			MsQuestion msQuestion = msQuestions.get(i);
			
			String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String uuidTaskD = taskDBean[i].getUuid_task_d();
			String optionAnswerId = taskDBean[i].getOption_answer_id();
			String lov = taskDBean[i].getLov();
			String textAnswer = taskDBean[i].getText_answer();
			String questionText = taskDBean[i].getQuestion_label();
			String latitude = taskDBean[i].getLatitude();
			String longitude = taskDBean[i].getLongitude();
			String mcc = taskDBean[i].getMcc();
			String mnc = taskDBean[i].getMnc();
			String lac = taskDBean[i].getLac();
			String cellId = taskDBean[i].getCid();
			String accuracy = taskDBean[i].getAccuracy();
			String image = taskDBean[i].getImage();
			String uuidLookup = taskDBean[i].getUuid_lookup();
			
			String assetType = StringUtils.EMPTY;
			if (null != msQuestion.getMsAssettag()) {
				assetType = msQuestion.getMsAssettag().getAssetTagName();
			}
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType) &&
					(textAnswer.contains(",") || textAnswer.contains("."))) {
				textAnswer = textAnswer.substring(0,textAnswer.length()-2).replace(",", "").replace(".", "");
			}
			
			if (GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(answerType) && textAnswer.contains("|")) {
				String[] temp = textAnswer.split("[|]");
				textAnswer = temp[temp.length-1];
			}
			
			if (MssTool.isImageQuestion(answerType)){										
					//insert into Table TR_TASKDETAILLOB
				this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
							latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, isl, imagePath);						
			} else if (GlobalVal.ANSWER_TYPE_LU_OFFLINE.equals(answerType) || 
					GlobalVal.ANSWER_TYPE_LU_OFFLINE_WITH_SUGGESTION.equals(answerType)) {
				// Check product answer
				if (GlobalVal.ASSET_TAG_BRAND.equals(assetType) ||
						GlobalVal.ASSET_TAG_MODEL.equals(assetType) ||
						GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType) ||
						GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
					if (GlobalVal.ASSET_TAG_BRAND.equals(assetType)) {
						hasBrand = true;
					} else if (GlobalVal.ASSET_TAG_MODEL.equals(assetType)) {
						hasModel = true;
					} else if (GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType)) {
						hasGroup = true;
					} else if (GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
						hasType = true;
						idAsset = taskDBean[i].getUuid_lookup();
					}
					mapAssetProduct.put(assetType, i);
				} else {
					Map<String, Object> mapAns = new HashMap<>();
					if (null != uuidLookup) {
						Long idStagingAsset = Long.valueOf(uuidLookup);
						String optionText = null;
						if (null != idStagingAsset) {
							if (GlobalVal.ASSET_TAG_PRODUCT.equals(assetType)) {
								Object[][] paramLu = { {"idLu", idStagingAsset} };
								List<Map<String, Object>> luAnswer = this.getManagerDAO().selectAllNativeString("select PRODUCT_OFFERING_ID, PRODUCT_OFFERING_NAME, ID from STAGING_PRODUCT_OFFERING where PRODUCT_OFFERING_ID = :idLu", paramLu);
								if (!luAnswer.isEmpty()) {
									mapAns = luAnswer.get(0);
									BigInteger prodId = (BigInteger) mapAns.get("d0");
									mapAns.put("d0", String.valueOf(prodId));
								}
								optionText = (String) mapAns.get("d0");
								textAnswer = (String) mapAns.get("d1");
								idStagingAsset = ((BigInteger) mapAns.get("d2")).longValue();
								optionAnswerId = null;
							} else {
								optionText = lov;
								optionAnswerId = null;
							}
							
							insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
									latitude, longitude, mcc, mnc, lac, cellId, accuracy, idStagingAsset, optionText, listTaskD);
						}
					}
				}
			} else if (GlobalVal.ANSWER_TYPE_SCORING.equals(answerType) ) {
				String optionText = lov;
				optionAnswerId = null;
				insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, optionText, listTaskD);
			} else {
				//insert into Table TR_TASK_D
				this.insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, listTaskD);
			}

		}
		
		//Insert into Task D for Product 
		if (hasBrand && hasModel && hasGroup && hasType) {
			Long longIdAsset = new Long(idAsset);
			
			Object[][] paramPo = { {"idPo", longIdAsset} };
			List<Map<String, Object>> listAssetAns = this.getManagerDAO().selectAllNativeString(
					"select HIERARCHY_L1_CODE, ASSET_HIERARCHY_L1_NAME, "
					+ " HIERARCHY_L2_CODE, ASSET_HIERARCHY_L2_NAME, "
					+ " GROUP_TYPE, MASTER_CODE, MASTER_NAME "
					+ " from STAGING_PO_ASSET where id = :idPo", paramPo);
			
			Map<String, Object> mapAsset = new HashMap<>();
			if (!listAssetAns.isEmpty()) {
				mapAsset = listAssetAns.get(0);
			}
			
			for (Map.Entry<String, Object> mp : mapAssetProduct.entrySet()) {
				int i = (int) mp.getValue();
				MsQuestion msQuestion = msQuestions.get(i);
				String assetType = msQuestion.getMsAssettag().getAssetTagName();
				String uuidTaskD = taskDBean[i].getUuid_task_d();
				String optionAnswerId = null;
				String textAnswer = taskDBean[i].getText_answer();
				String questionText = taskDBean[i].getQuestion_label();
				String latitude = taskDBean[i].getLatitude();
				String longitude = taskDBean[i].getLongitude();
				String mcc = taskDBean[i].getMcc();
				String mnc = taskDBean[i].getMnc();
				String lac = taskDBean[i].getLac();
				String cellId = taskDBean[i].getCid();
				String accuracy = taskDBean[i].getAccuracy();
				String optionText = null;
				
				if (GlobalVal.ASSET_TAG_BRAND.equals(assetType)) {
					optionText = (String) mapAsset.get("d0");
					textAnswer = (String) mapAsset.get("d1");
				} else if (GlobalVal.ASSET_TAG_MODEL.equals(assetType)) {
					optionText = (String) mapAsset.get("d2");
					textAnswer = (String) mapAsset.get("d3");
				} else if (GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType)) {
					optionText = (String) mapAsset.get("d4");
					textAnswer = (String) mapAsset.get("d4");
				} else if (GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
					optionText = (String) mapAsset.get("d5");
					textAnswer = (String) mapAsset.get("d6");
				}
				insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, longIdAsset, optionText, listTaskD);
			}
		}
	}
	
	private void insertTaskD(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String optionAnswerId,
			String questionText, String textAnswer, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Long idStagingAsset, String optionText, Map<Long, TrTaskD> listTaskD){
		DateTime startDateTime = new DateTime();
		LOG.info("Inserting task detail...");
		MsLov msLovByLovId =  null;
		TrTaskD taskD = null;
		if (!GlobalVal.ANSWER_TYPE_MULTIPLE.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			taskD = listTaskD.get(msQuestion.getUuidQuestion());
		}
		if (StringUtils.isNotBlank(optionAnswerId)){
			msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
		}
		if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())
				|| GlobalVal.ANSWER_TYPE_DATETIME.equals(msQuestion.getMsAnswertype().getCodeAnswerType())){
			if (textAnswer != null){
				DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
				try {
					Date result = df.parse(textAnswer);
					if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
						textAnswer = (null != result) 
								? new SimpleDateFormat("dd/MM/yyyy").format(result)
								: null;
					} else {
						textAnswer = (null != result) 
								? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result)
								: null;
					}
				} catch (ParseException e) {
					LOG.error(e.getMessage(), e);
					e.printStackTrace();
				} 
			}
		}
		if (null != taskD) {
			
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setDtmUpd(new Date());
			if (msLovByLovId != null) {
				taskD.setMsLovByLovId(msLovByLovId);
				taskD.setOptionText(msLovByLovId.getDescription());
			} else {
				taskD.setOptionText(optionText);
			}
			taskD.setQuestionText(questionText);
			taskD.setTextAnswer(textAnswer);
			taskD.setLatitude(checkEmptyBigdecimal(latitude));
			taskD.setLongitude(checkEmptyBigdecimal(longitude));
			if (taskD.getLatitude()!=null && taskD.getLongitude()!=null &&
					taskD.getLatitude().intValue() != 0 && taskD.getLongitude().intValue() != 0) {
				taskD.setIsGps("1");
			}
			taskD.setMcc(checkEmptyInteger(mcc));
			taskD.setMnc(checkEmptyInteger(mnc));
			taskD.setLac(checkEmptyInteger(lac));
			taskD.setCellId(checkEmptyInteger(cellId));
			taskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(taskD.getIsGps()) && taskD.getMcc() != null && taskD.getMnc() != null
					&& taskD.getLac() != null && taskD.getCellId() != null) {
				this.getLocationByCellId(taskD, auditContext);
			}
			taskD.setIdStagingAsset(idStagingAsset);
			this.getManagerDAO().update(taskD);
		} else {				
			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			trTaskD.setTrTaskH(trTaskH);
			trTaskD.setMsQuestion(msQuestion);
			if (msLovByLovId != null) {
				trTaskD.setMsLovByLovId(msLovByLovId);
				trTaskD.setOptionText(msLovByLovId.getDescription());
			} else {
				trTaskD.setOptionText(optionText);
			}
			trTaskD.setQuestionText(questionText);
			trTaskD.setTextAnswer(textAnswer);
			trTaskD.setLatitude(checkEmptyBigdecimal(latitude));
			trTaskD.setLongitude(checkEmptyBigdecimal(longitude));
			if (trTaskD.getLatitude()!=null && trTaskD.getLongitude()!=null &&
					trTaskD.getLatitude().intValue() != 0 && trTaskD.getLongitude().intValue() != 0) {
				trTaskD.setIsGps("1");
			}
			trTaskD.setMcc(checkEmptyInteger(mcc));
			trTaskD.setMnc(checkEmptyInteger(mnc));
			trTaskD.setLac(checkEmptyInteger(lac));
			trTaskD.setCellId(checkEmptyInteger(cellId));
			trTaskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(trTaskD.getIsGps()) && trTaskD.getMcc() != null && trTaskD.getMnc() != null
					&& trTaskD.getLac() != null && trTaskD.getCellId() != null) {
				this.getLocationByCellId(trTaskD, auditContext);
			}
			trTaskD.setIdStagingAsset(idStagingAsset);
			this.getManagerDAO().insert(trTaskD);
			
		}
		LOG.info("End of insert task detail : {}", new DateTime().minusMillis(startDateTime.getMillisOfDay()).getMillisOfDay());
	}
	
	private Map validateDukcapil(TaskDukcapilBean taskDukcapilBean, AuditContext auditContext) {
		Map mapResult = new HashMap<>();
		 
		AmGeneralsetting amGeneralSetting = this.commonLogic.retrieveGs(GlobalKey.GENERALSETTING_CHECK_DUKCAPIL, auditContext);
		if (!"1".equals(amGeneralSetting.getGsValue())) {
			mapResult.put("d0", 1);
			return mapResult;
		} else {
			String appSource = SpringPropertiesUtils.getProperty(GlobalKey.NC_CHECK_DUKCAPIL_APP_SOURCE);
			CheckDukcapilResponse dukcapil = this.dukcapil(auditContext, taskDukcapilBean.getNik(),
					taskDukcapilBean.getCustName(), taskDukcapilBean.getBirthPlace(), taskDukcapilBean.getBirthDt(), auditContext.getCallerId(), appSource);
			
			if ("Match".equalsIgnoreCase(dukcapil.getFinalResult()) && "00".equals(dukcapil.getResponseCode())) {
				mapResult.put("d0", 1);
				mapResult.put("cif", dukcapil.getCif());
				mapResult.put("idHistCust", dukcapil.getIdCheckCustHistory());
				return mapResult;
			} else {
				// not match 0 not found 2
				if ("Not Match".equalsIgnoreCase(dukcapil.getFinalResult())) {
					mapResult.put("d0", 0);	
				} else  if ("Not Found".equalsIgnoreCase(dukcapil.getFinalResult())){
					mapResult.put("d0", 2);
				} else {
					mapResult.put("d0", 2);
				}
				
				mapResult.put("d1", dukcapil.getResponseMessage() + ":" + taskDukcapilBean.getNik());
				mapResult.put("cif", dukcapil.getCif());
				mapResult.put("idHistCust", dukcapil.getIdCheckCustHistory());
				return mapResult;
			}
		}
	}
	
	private CheckDukcapilResponse dukcapil(AuditContext auditContext, String nik, String name, String birthPlace, String birthDate, String userId, String appSource) {
		CIFBean result = intFormLogic.revampDukcapil(auditContext, nik, name, birthPlace, birthDate, userId, appSource, null);
		CheckDukcapilResponse checkdukcapilResponse = intFormLogic.checkDukcapil(auditContext, "0", nik, name, birthPlace, birthDate, userId, appSource, null, result, null, null);
		checkdukcapilResponse.setCif(result.getCustNo());
		return checkdukcapilResponse;
	}
	
	private void createTaskSurvey(TrTaskH trTaskH, TblProductCategory tblProductCategory, AmMsuser amMsuser, MsBranch msBranch, AuditContext callerId, String kelurahan) {
		this.getManagerDAO().fetch(trTaskH.getMsForm());
		AmMssubsystem amMssubsystem = this.commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		
		//this.getManagerDAO().fetch(amMsuser.getMsBranch());
		
		Object [][] paramsGroupTask = {{"uuidTaskH", trTaskH.getUuidTaskH()}};
		BigInteger groupTaskIdLead = (BigInteger)this.getManagerDAO().selectOneNativeString(
				"SELECT GROUP_TASK_ID FROM MS_GROUPTASK WITH(NOLOCK) WHERE UUID_TASK_H = :uuidTaskH ", paramsGroupTask);
		long groupTaskId = trTaskH.getUuidTaskH();
		if (null != groupTaskIdLead) {
			groupTaskId = groupTaskIdLead.longValue();
		}
		
		List<TrTaskH> listTaskSvy = new ArrayList<>();
		if("1".equalsIgnoreCase(msBranch.getIsPiloting())) {
			Object [][] paramMap = { {Restrictions.eq("msForm.uuidForm", Long.valueOf(trTaskH.getMsForm().getUuidForm()))},
					{Restrictions.eq("isActive", "1")}, {Restrictions.eq("productCategoryCode", tblProductCategory.getProductCategoryCode())},
					{Restrictions.eq("isPiloting", "1")} };
			MsMappingformH msMapFormH = this.getManagerDAO().selectOne(MsMappingformH.class, paramMap);
			
			try {
				listTaskSvy = taskServiceLogic.createTaskSurveyPiloting(callerId, amMsuser, trTaskH, amMssubsystem, msMapFormH, groupTaskId, null);
				for (int i = 0; i < listTaskSvy.size(); i++) {
					TrTaskH taskH = listTaskSvy.get(i);
					taskServiceLogic.insertDefaultAnswerQuestionPilotingCae(taskH, false, callerId);
				}
			} catch (Exception e) {
				e.printStackTrace();
				throw new UploadTaskException("Error Create Task Survey :" + e.getMessage(), Reason.ERROR_GENERATE);
			}
		} else {
			listTaskSvy = createTaskSurveyNonPiloting(trTaskH, tblProductCategory, amMsuser, groupTaskId, callerId);
		}
		
		// UPDATE TASK UNTUK DATA KELURAHANNYA
		for(int i=0; i<listTaskSvy.size(); i++) {
			TrTaskH taskBean = listTaskSvy.get(i);
			taskBean.setKelurahan(kelurahan);
			this.getManagerDAO().update(taskBean);
		}
	}
	
	public String appendNotes(String notes, String appended) {
		LOG.info("Appending notes {} {}", notes, appended);
		try {
			String newNotes = "";
			
			if (StringUtils.isNotBlank(notes)){ // have old notes
				newNotes = notes + "|" + appended;
			} else { // new notes
				newNotes = appended;
			}
			
			return newNotes;
		} catch(Exception e) {
			e.printStackTrace();
			return appended;
		}
	}
	
	private List<TrTaskH> createTaskSurveyNonPiloting(TrTaskH trTaskH, TblProductCategory tblProductCategory, AmMsuser amMsuser, long groupTaskId, AuditContext callerId) {
		String formName = StringUtils.EMPTY;
		formName = tblProductCategory.getMsForm().getFormName();
		Long uuidForm = tblProductCategory.getMsForm().getUuidForm();
		StringBuilder queryMappingForm = new StringBuilder()
				.append(" SELECT UUID_MAPPING_FORM_H, MAPPING_NAME FROM MS_MAPPINGFORM_H with(nolock) ")
				.append(" WHERE UUID_FORM = :uuidForm and PRODUCT_CATEGORY_CODE = :product ");
		List resultMappingH = this.getManagerDAO().selectAllNativeString(queryMappingForm.toString(), new Object[][]{{"uuidForm", trTaskH.getMsForm().getUuidForm()}, {"product", tblProductCategory.getProduct()}});
		if(resultMappingH == null) {
			throw new SubmitTaskException(
					this.messageSource.getMessage("businesslogic.submit.mappingnotexists",
							new Object[] { formName }, this.retrieveLocaleAudit(callerId)),
					com.adins.mss.exceptions.SubmitTaskException.Reason.NO_ORDER_SURVEY_MAPPING);
		}
		
		MsPriority priority = commonLogic.retrieveMsPriority(GlobalVal.TAKS_PRIORTY_NORMAL, true, callerId);
		MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(trTaskH.getMsForm().getUuidForm(),
				trTaskH.getFormVersion(), callerId);
		SubmitTaskDBean[] taskDBeanOrder = taskServiceLogic.getSubmitTaskDBean(trTaskH.getUuidTaskH(), callerId);
		Map<Integer, MsQuestion> msQuestions =taskServiceLogic.getAllMsQuestion(taskDBeanOrder, formHist, callerId);
		AmMssubsystem subsystemMs = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		boolean isMapping = false;
		List<TrTaskH> listTaskDist = new ArrayList<>();
		
		for(int i=0; i<resultMappingH.size(); i++) {
			Map mappingH = (Map) resultMappingH.get(i);
			Object[][] mapFormDParam = {
					{ Restrictions.eq("msMappingformH.uuidMappingFormH", ((BigInteger) mappingH.get("d0")).longValue()) } };
			List<MsMappingformD> msMappingFormD = (List<MsMappingformD>) this.getManagerDAO()
					.selectAll(MsMappingformD.class, mapFormDParam, null).get(GlobalKey.MAP_RESULT_LIST);
			if (msMappingFormD == null) {
				throw new SubmitTaskException(
						this.messageSource.getMessage("businesslogic.submit.mappingnotexists",
								new Object[] { mappingH.get("d1").toString() }, this.retrieveLocaleAudit(callerId)),
						com.adins.mss.exceptions.SubmitTaskException.Reason.NO_ORDER_SURVEY_MAPPING);
			}
			
			for (int x = 0; x < msMappingFormD.size(); x++) {
				MsMappingformD bean = msMappingFormD.get(x);
				Type collectionType = new TypeToken<Collection<MappingFormBean>>() {
				}.getType();
				List<MappingFormBean> listFormQuestion = gson.fromJson(bean.getQuestionMapping(), collectionType);
				if (listFormQuestion == null || listFormQuestion.isEmpty()) {
					LOG.info("No Question Mapping");
					return null;
				}
				
				for (int j = 0; j < listFormQuestion.size(); j++) {
					MappingFormBean formQuestion = listFormQuestion.get(j);
					if(!formQuestion.getFormId().equalsIgnoreCase(String.valueOf(uuidForm))) {
						continue;
					}
					
					TrTaskH taskHSurvey = taskServiceLogic.saveTaskHSurvey(amMsuser, trTaskH, null, null, priority, null, null, null, null, formQuestion, true, false, callerId);
					
					taskServiceLogic.saveGroupTaskSurveyByExistingTaskid(taskHSurvey, groupTaskId, callerId);
					
					ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(callerId);
					Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
							? this.imageStorageLogic.retrieveGsImageFileSystemPath(callerId)
							: null;

					List<MappingFormBean> listQuestion = formQuestion.getListQuestionMapping();
					taskServiceLogic.saveTaskDSurveyIntoRow(taskHSurvey, listQuestion, msQuestions, taskDBeanOrder, isl, imagePath, null,
							callerId);
					listTaskDist.add(taskHSurvey);
					
					long uuidProcess = this.getUuidProcess(taskHSurvey, subsystemMs);
					MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(taskHSurvey, uuidProcess, subsystemMs, 0);
					
					String prevNotes =  GlobalVal.NOTES_TASK_SUBMITTED_HIST + trTaskH.getTaskId();
				    TrTaskhistory trTaskHistory = new TrTaskhistory(msStatustaskSurvey, taskHSurvey, callerId.getCallerId(), new Date(),
				    			StringUtils.left(prevNotes, 2048), amMsuser.getFullName(),  callerId.getCallerId(), GlobalVal.CODE_PROCESS_UNASSIGNED);
			        this.getManagerDAO().insert(trTaskHistory);
					
					msStatustaskSurvey = commitWfAndUpdateTaskH(taskHSurvey, uuidProcess, subsystemMs, 0);

					// Update status task when task unassigned or pending
					if (StringUtils.isNotBlank(taskHSurvey.getOrderId())) {
//						UpdateStatusMainDealerResponse updStatusTaskMainDealer = this.intFormLogic.updateStatusTaskMainDealer(taskHSurvey, callerId);
//						if (!"00".equals(updStatusTaskMainDealer.getResponseCode())) {
//							throw new RemoteException(updStatusTaskMainDealer.getResponseMessage());
//						}
					}

					String notes = GlobalVal.NOTES_TASK_CREATED_HIST + trTaskH.getTaskId();
					// INSERT INTO CHECK HISTORY
					insertTaskHistory(callerId, msStatustaskSurvey, taskHSurvey, notes,
							GlobalVal.CODE_PROCESS_ASSIGNMENT, amMsuser.getFullName(), amMsuser, null);
					isMapping = true;
				}
			}
		}
		
		if(!isMapping) {
			throw new SubmitTaskException(
					this.messageSource.getMessage("businesslogic.submit.mappingnotexists",
							new Object[] { formName }, this.retrieveLocaleAudit(callerId)),
					com.adins.mss.exceptions.SubmitTaskException.Reason.NO_ORDER_SURVEY_MAPPING);
		}
		
		return listTaskDist;
	}
	
	private String getOfficeByKat(String kat, String prodCat) {
		String[][] arg0 = { { "kat", kat }, { "prod", "" }, { "prodCat", prodCat } };
		String qry = "select OFFICE_CODE from [dbo].[getOfficeByKAT](:kat , :prod, :prodCat)";
		List<Map<String, Object>> result = this.getManagerDAO().selectAllNativeString(qry, arg0);

		String office = "";
		if (!result.isEmpty()) {
			office = (String) result.get(0).get("d0");
		}

		return office;

	}
	
	private AddTaskMainDealerRequest fillMainDealerRequest(AddTaskMainDealerRequest bean) {
		// fix provinsi kota legal
		if (null != bean.getLegalZipcode() && null != bean.getLegalKelurahan()) {
			ZipcodeBean bn = getZipcodeDetail(bean.getLegalZipcode(), bean.getLegalKelurahan(), "KODE POS");

			if (null != bn) {
				bean.setLegalProvinsi(bn.getProv());
				bean.setLegalKecamatan(bn.getKecamatan());
				bean.setLegalCity(bn.getKota());
			}
		} else {
			if (null != bean.getLegalProvinsi()) {
				String[][] prm = { { "code", bean.getLegalProvinsi() } };
				String qry = "select distinct CONSTRAINT_1 from MS_LOV (nolock) where IS_ACTIVE = '1' AND CODE = :code AND LOV_GROUP = 'KOTA'";
				String provLegal = (String) this.getManagerDAO().selectOneNativeString(qry, prm);

				bean.setLegalCity(bean.getLegalProvinsi());
				bean.setLegalProvinsi(provLegal);
			}
		}

		if (null != bean.getResZipcode() && null != bean.getResKelurahan()) {
			ZipcodeBean bn = getZipcodeDetail(bean.getResZipcode(), bean.getResKelurahan(), "KODE POS");

			if (null != bn) {
				bean.setResProvinsi(bn.getProv());
				bean.setResKecamatan(bn.getKecamatan());
				bean.setResCity(bn.getKota());
			}
		} else {
			if (null != bean.getResProvinsi()) {
				String[][] prm = { { "code", bean.getResProvinsi() } };
				String qry = "select distinct CONSTRAINT_1 from MS_LOV (nolock) where IS_ACTIVE = '1' AND CODE = :code AND LOV_GROUP = 'KOTA'";
				String provSurvey = (String) this.getManagerDAO().selectOneNativeString(qry, prm);

				bean.setResCity(bean.getResProvinsi());
				bean.setResProvinsi(provSurvey);
			}
		}
		// end
		return bean;
	}
	
	@Transactional
	private ZipcodeBean getZipcodeDetail(String zipcode, String kelurahan, String lovGroup) {
		ZipcodeBean bn = new ZipcodeBean();

		try {
			Object[][] prm = { { Restrictions.eq("lovGroup", lovGroup) }, { Restrictions.eq("isActive", "1") },
					{ Restrictions.eq("code", zipcode) }, { Restrictions.eq("constraint1", kelurahan) } };
			MsLov lov = this.getManagerDAO().selectOne(MsLov.class, prm);

			bn.setProv(lov.getConstraint4());
			bn.setKota(lov.getConstraint3());
			bn.setKecamatan(lov.getConstraint2());
			bn.setKelurahan(lov.getConstraint1());
			bn.setKodePos(lov.getCode());
		} catch (Exception e) {
			e.printStackTrace();
			LOG.warn("Failed get zipcode detail {} - {} - {}", zipcode, kelurahan, lovGroup);
			return null;
		}

		return bn;
	}
	
}
