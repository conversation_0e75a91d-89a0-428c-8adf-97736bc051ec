package com.adins.mss.businesslogic.impl.common;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Stack;
import java.util.concurrent.CountDownLatch;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ApprovalLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.TaskServiceLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.MobileDefAnswer;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsMappingformD;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TblApiDashboard;
import com.adins.mss.model.TblCaeData;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.custom.Content;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.AddTaskCAERequest;
import com.adins.mss.services.model.common.ApprovalHeadBean;
import com.adins.mss.util.CipherTool;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.gson.Gson;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericApprovalLogic extends BaseLogic implements ApprovalLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericApprovalLogic.class);
	private AuditInfo auditInfo;
	
	public GenericApprovalLogic() {
		String[] pkCols = { "uuidTaskH" };
		String[] pkDbCols = { "UUID_TASK_H" };
		String[] cols = { "uuidTaskH", "msStatustask.uuidStatusTask", "approvalDate", "approvalNotes" };
		String[] dbCols = { "UUID_TASK_H", "UUID_STATUS_TASK", "APPROVAL_DATE", "APPROVAL_NOTES" };
		this.auditInfo = new AuditInfo("TR_TASK_H", pkCols, pkDbCols, cols,
				dbCols);
	}
	
    private IntFormLogic intFormLogic;

    private String paramSNotes = "SNOTES";
    private String paramStatusCode = "statusCode";
    private String paramMsSubSystem = "amMssubsystem.uuidMsSubsystem";
    private String select = "select ";
    private String from = "from ";
    
	@Autowired
	private MessageSource messageSource;
	private TaskServiceLogic taskServiceLogic;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public void setTaskServiceLogic(TaskServiceLogic taskServiceLogic) {
		this.taskServiceLogic = taskServiceLogic;
	}

    private GeolocationLogic geocoder;

	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
		
		
	}
	
	@Override
	public List listApproval(String mode, Object params, AuditContext callerId) {
		List result = new ArrayList<>();
		if ("branch".equals(mode)) {
			result = this.getManagerDAO().selectAllNative(
					"task.approval.approvalListByBranch", params, null);
		} 
		else {
			result = this.getManagerDAO().selectAllNative(
					"task.approval.approvalList", params, null);
		}
		return result;
	}
		
	@Override
	public Integer countlistApproval(String mode, Object params, AuditContext callerId) {
		Integer result;
		if ("branch".equals(mode)) {
			result = (Integer) this.getManagerDAO().selectOneNative(
					"task.approval.countApprovalListbyBranch", params);
		} 
		else {
			result = (Integer) this.getManagerDAO().selectOneNative(
					"task.approval.countApprovalList", params);
		} 
		return result;
	}
	
	Map sortByValues(Map<String, String> comboList) {
		List list = new LinkedList(comboList.entrySet());

		Collections.sort(list, new Comparator() {
			public int compare(Object o1, Object o2) {
				return ((Comparable) ((Map.Entry) (o1)).getValue())
						.compareTo(((Map.Entry) (o2)).getValue());
			}
		});

		Map sortedHashMap = new LinkedHashMap();
		sortedHashMap.put("%", "ALL");
		for (Iterator it = list.iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			sortedHashMap.put(entry.getKey(), entry.getValue());
		}
		return sortedHashMap;
	}
		
	@Override
	public Map<String, String> getBranchListCombo(long branchId, AuditContext callerId) {
		Map<String, String> result = MapUtils.EMPTY_MAP;
		
		Object[][] params = { { "branchId", branchId } };
		List list = this.getManagerDAO().selectAllNative(
				"task.verification.getBranchListCombo", params, null);
		
		Map<String, String> comboList = new HashMap<String, String>();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				Map temp2 = (Map) list.get(i);
				comboList.put((String) temp2.get("d1"),
						(String) temp2.get("d2")+" - "+(String) temp2.get("d1"));
			}
			result = sortByValues(comboList);
		} 
		else {
			result.put("%", "ALL");
		}
		
		return result;
	}
		
	@Override
	public TrTaskH getApproval(long uuid, AuditContext callerId) {
		TrTaskH result = this.getManagerDAO().selectOne(
				"from TrTaskH t "
				+ "join fetch t.msBranch b "
				+ "join fetch t.msPriority p "
				+ "join fetch t.msForm f "
				+ "join fetch t.amMsuser u "
				+ "join fetch u.msJob j "
				+ "join fetch u.amMssubsystem s "
				+ "where t.uuidTaskH = :uuidTaskH", 
				new Object[][] {{"uuidTaskH", uuid}});
		if (result.getResult() == null || result.getResult().isEmpty()) {
			result.setResult("-");
		}
		if (result.getNotes() == null || result.getNotes().isEmpty()) {
			result.setNotes("-");
		}
		return result;
	}
		
	@Override
	public Map<String, Object> detailApproval(long uuidTaskH, AuditContext callerId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Boolean hasImage = Boolean.FALSE;
		
		Object[][] params = {{"uuidTaskH", uuidTaskH}};
		List<Map<String, Object>> result = this.getManagerDAO().selectForListOfMap("task.approval.approvalDetailQSet", params, null);
		
		for (int i = 0; i < result.size(); i++) {
			Map map = (HashMap) result.get(i);
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("msQuestion.msAnswertype.codeAnswerType").toString())){
				NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);				
				if (StringUtils.isNotBlank((String) map.get("textAnswer"))) {
					map.put("textAnswer", "Rp "+ formatKurs.format(NumberUtils.toDouble((String) map.get("textAnswer"))));
				}
				else {
					map.put("textAnswer", StringUtils.EMPTY);
				}
				map.put("accuracy", StringUtils.EMPTY);
			}
			
			if (MssTool.isImageQuestion(map.get("msQuestion.msAnswertype.codeAnswerType").toString()) && "1".equals(map.get("hasImage").toString())
					&& null != map.get("latitude") && null != map.get("longitude")) { //Lat Lng
				String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
				if("1".equals(link_encrypt)){
					String uuidTaskLob = (String) map.get("textAnswer");
					String[] toBeEncrypt = {uuidTaskLob};
					uuidTaskLob = CipherTool.encryptData(toBeEncrypt).get(0).toString();	
					map.put("textAnswer", uuidTaskLob);
				}
				hasImage = Boolean.TRUE;
			}
			else if (MssTool.isImageQuestion(map.get("msQuestion.msAnswertype.codeAnswerType").toString())
					&& !"1".equals(map.get("hasImage").toString())) {
				map.put("textAnswer", StringUtils.EMPTY);
			}else if (MssTool.isImageQuestion(map.get("msQuestion.msAnswertype.codeAnswerType").toString())
					&& "1".equals(map.get("hasImage").toString())) {
				String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
				if("1".equals(link_encrypt)){
					String uuidTaskLob = (String) map.get("textAnswer");
					String[] toBeEncrypt = {uuidTaskLob};
					uuidTaskLob = CipherTool.encryptData(toBeEncrypt).get(0).toString();	
					map.put("textAnswer", uuidTaskLob);
				}
				hasImage = Boolean.TRUE;
			}
		}
		
		resultMap.put("resultList", result);
		resultMap.put("hasImage", hasImage);
		
		return resultMap;
	}
	
	@Override
	public List viewMapPhoto(long uuid, AuditContext callerId) throws UnsupportedEncodingException {
		List <TrTaskBean> result = new ArrayList<TrTaskBean>();
		Object[][] params = {{ "uuidTaskH", uuid } };
		List list = this.getManagerDAO().selectAllNative("task.approval.viewMapPhoto", params, null);

		for (int i = 0; i < list.size(); i++) {
			TrTaskBean approval = new TrTaskBean();
			Map map = (HashMap) list.get(i);
			approval.setHasImage((String) map.get("d4"));
			if ("1".equals((String) map.get("d4"))) {
				approval.setLob(map.get("d8").toString());
			}
			
			if (map.get("d5")!=null && map.get("d6") != null) {
				approval.setLatitude((BigDecimal) map.get("d5"));
				approval.setLongitude((BigDecimal) map.get("d6"));
				result.add(approval);
			}	
		}
		return result;
	}
	
	@Override
	public List getWfEngineNextStep(TrTaskH trTaskH, AmMssubsystem msSubsystem, AmMsuser loginUser, AuditContext callerId) {
		List<String> result = new ArrayList<>();
		List wfDesc= new ArrayList<>() ;
		List uuidNextTask = new ArrayList<>();
		
		String[][] param = { {"uuidTaskH", String.valueOf(trTaskH.getUuidTaskH())} };
	    Object[] task = (Object[]) this.getManagerDAO().selectOneNativeString(
	    		"SELECT mst.STATUS_CODE, amu.UUID_JOB " +
	    		"FROM   TR_TASK_H trh WITH (NOLOCK) " + 
	    		"JOIN   MS_STATUSTASK mst WITH (NOLOCK) ON trh.UUID_STATUS_TASK = mst.UUID_STATUS_TASK " +
	    		"LEFT JOIN AM_MSUSER amu WITH (NOLOCK) ON amu.UUID_MS_USER = trh.UUID_MS_USER " +
	    		"WHERE  trh.UUID_TASK_H = :uuidTaskH", param);
	    
	    String statusCode = (String) task[0];
	    if (GlobalVal.SURVEY_STATUS_TASK_APPROVALDATAENTRY.equals(statusCode)) {
	    	BigInteger uuidJobAssign = (BigInteger) task[1];
	    	String flagSourceOts = null;
			if (GlobalVal.FORM_OTS.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
				flagSourceOts = (String) this.getManagerDAO().selectOneNativeString("SELECT FLAG_SOURCE_OTS FROM TBL_OTS_DATA WITH(NOLOCK) WHERE GROUP_TASK_ID = :uuidTaskH ", new Object[][] {{"uuidTaskH", trTaskH.getUuidTaskH()}});
			}
	    	int count = this.getStatusBySettingApprovalOts(trTaskH.getMsForm().getUuidForm(), "IS_APPROVAL_ON_WOM", uuidJobAssign.longValue(), flagSourceOts);
	    	if (count > 0) {
				if ("1".equalsIgnoreCase(trTaskH.getIsPilotingCae())) {
					uuidNextTask.add(66);
					uuidNextTask.add(66);
					uuidNextTask.add(67);
					result.add("S");
					result.add(paramSNotes);
					result.add("RS");
				} else {
					uuidNextTask.add(66);
					uuidNextTask.add(67);
					result.add("PW");
					result.add("RS");
				}
	    	}
	    }
	    
	    if (uuidNextTask.isEmpty()) {
			if ("1".equalsIgnoreCase(trTaskH.getIsPilotingCae())) {
				uuidNextTask.add(68);
				uuidNextTask.add(68);
				uuidNextTask.add(65);
				result.add("S");
				result.add(paramSNotes);
				result.add("RS");
			} else {
				uuidNextTask.add(68);
				uuidNextTask.add(65);
				result.add("S");
				result.add("RS");
				
			}
	    }
		
	    if ("1".equalsIgnoreCase(trTaskH.getIsPilotingCae())) {
	    	for (int i = 0; i < result.size(); i++) {
	    		Map a = new HashMap<>();
				a.put("uuidNextTask", uuidNextTask.get(i) + "&" + result.get(i));
				
				String description = "Pass";
				if ("RS".equalsIgnoreCase(result.get(i))) {
					description = "Not Pass";
				} else if (result.get(i).toUpperCase().contains("NOTE")) {
					description = "Pass With Notes";
				}
				a.put("descNextTask", description);
				wfDesc.add(a);
	    	}
	    	
	    } else {
	    	for (int i = 0; i < result.size(); i++) {
				String nextWfStatusCode = result.get(i);
				String[][] params = { {"statusCode", nextWfStatusCode}, {"msSubsystem", String.valueOf(msSubsystem.getUuidMsSubsystem())} };
				String descNexttask = (String) this.getManagerDAO().selectOneNativeString(
						"SELECT STATUS_TASK_DESC " +
						"FROM   MS_STATUSTASK WITH (NOLOCK) " +
						"WHERE  STATUS_CODE = :statusCode " +
						"       AND UUID_MS_SUBSYSTEM = :msSubsystem ", params);
				
				Map a = new HashMap<>();
				a.put("uuidNextTask", uuidNextTask.get(i) + "&" + nextWfStatusCode);
				a.put("descNextTask", descNexttask);
				wfDesc.add(a);
			}
	    }
		
		
	    return wfDesc ;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String updateTaskH(TrTaskhistory objTaskHist, long uuidTaskH, String flagStatusTask, 
			AmMsuser loginUser, String flagApproval, String approvalReason, AuditContext callerId) {
		
		try {
			TrTaskH taskHBean = this.getManagerDAO().selectOne(TrTaskH.class,
					new Object[][] {{Restrictions.eq("uuidTaskH", uuidTaskH)}});
			
			if(null == taskHBean) {
				throw new IllegalArgumentException(this.messageSource.getMessage("businesslogic.approval.tasknotfound",
						null, this.retrieveLocaleAudit(callerId)));
			}
			
			taskHBean.setEndApproval(new Date());
	
			this.getManagerDAO().update(taskHBean);
		}catch(Exception e) {
			throw e;
		}
		
		String ret = null;
		boolean isTextReleased = false;
		String statusTask = flagStatusTask;
		TrTaskH dbModel = this.getManagerDAO().selectOne(
				"from TrTaskH t "
				+ "join fetch t.amMsuser u "
				+ "join fetch t.msStatustask s "
				+ "join fetch t.msForm f "
				+ "join fetch u.amMssubsystem ss "
				+ "where t.uuidTaskH = :uuidTaskH",
				new Object[][] {{"uuidTaskH", uuidTaskH}});
		this.getManagerDAO().fetch(dbModel.getAmMsuser().getMsBranch());
		String isPilotingCae = dbModel.getIsPilotingCae();
		MsStatustask sts = this.getManagerDAO().selectOne(
				MsStatustask.class, dbModel.getMsStatustask().getUuidStatusTask());
		
		if("ApprovalDataEntry".equalsIgnoreCase(flagApproval)) {
			if (paramSNotes.equalsIgnoreCase(flagStatusTask) || "S".equalsIgnoreCase(flagStatusTask)) {
				statusTask = GlobalVal.SURVEY_STATUS_TASK_APPROVALONWOMF;
			}
			
            if (!GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(sts.getStatusCode())
                    && !GlobalVal.SURVEY_STATUS_TASK_APPROVALDATAENTRY.equals(sts.getStatusCode())) {
               throw new ChangeException(this.messageSource.getMessage(
            		   "businesslogic.error.changeexception", null, this.retrieveLocaleAudit(callerId)), sts.getStatusCode());
            }
		} else {
			if (paramSNotes.equalsIgnoreCase(flagStatusTask)) {
				statusTask = GlobalVal.SURVEY_STATUS_TASK_RELEASED;
			}
			if (!GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(sts.getStatusCode())
					&& !GlobalVal.SURVEY_STATUS_TASK_APPROVALONWOMF.equals(sts.getStatusCode())) {
				throw new ChangeException(this.messageSource.getMessage(
						"businesslogic.error.changeexception", null, this.retrieveLocaleAudit(callerId)), sts.getStatusCode());
			}
		}
		
		Object[][] params = { {Restrictions.eq(paramStatusCode, statusTask)},
				{Restrictions.eq(paramMsSubSystem, loginUser.getAmMssubsystem().getUuidMsSubsystem())} };
		String statusTaskDesc = sts.getStatusTaskDesc();
		String fullNotes = StringUtils.EMPTY; 
		if (StringUtils.isNotBlank(dbModel.getNotes())) {
			fullNotes += dbModel.getNotes() + " | ";
		}
		fullNotes += statusTaskDesc + " Notes : " + objTaskHist.getNotes();
		dbModel.setMsStatustask(this.getManagerDAO().selectOne(MsStatustask.class, params));
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());
		dbModel.setApprovalDate(new Date());
		String approvalNotes = StringUtils.EMPTY;
		if (StringUtils.isNotBlank(dbModel.getApprovalNotes())) {
			approvalNotes += dbModel.getApprovalNotes() + " | ";
		}
		approvalNotes += statusTaskDesc + " Notes : " + objTaskHist.getNotes();
		dbModel.setApprovalNotes(approvalNotes);
		dbModel.setNotes(fullNotes);
		
		this.auditManager.auditEdit(dbModel, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
		
		if (StringUtils.isNotBlank(approvalReason)) {
			this.insertDetailApprovalReason(approvalReason, dbModel, flagStatusTask, callerId);
		}
		
		String notes = StringUtils.EMPTY;
		String codeProcess = StringUtils.EMPTY;
		Integer flag = 0;
		if (GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(statusTask)
				|| GlobalVal.SURVEY_STATUS_TASK_APPROVALONWOMF.equals(statusTask)) {
			if (GlobalVal.PROCESS_CODE_MSCOREF.equals(dbModel.getFlagSource())) {
				Object[][] paramsStatusDeleted = { {Restrictions.eq(paramStatusCode, GlobalVal.SURVEY_STATUS_TASK_DELETED)},
						{Restrictions.eq(paramMsSubSystem, loginUser.getAmMssubsystem().getUuidMsSubsystem())} };
				MsStatustask statustaskDeleted = this.getManagerDAO().selectOne(MsStatustask.class, paramsStatusDeleted);
				
				Object [][] paramTaskH = { {Restrictions.eq("applNo", dbModel.getApplNo())},
						{Restrictions.eq("flagSource", GlobalVal.PROCESS_CODE_MSCOREP)},
						{Restrictions.ne("msStatustask.uuidStatusTask", statustaskDeleted.getUuidStatusTask())}};
				TrTaskH taskText = this.getManagerDAO().selectOne(TrTaskH.class, paramTaskH);
				this.getManagerDAO().fetch(taskText.getMsStatustask());
				if (GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(statusTask) &&
						GlobalVal.SURVEY_STATUS_TASK_RELEASED_ON_PENDING.equals(taskText.getMsStatustask().getStatusCode())) {
					Object[][] paramsStatusReleased = { {Restrictions.eq(paramStatusCode, GlobalVal.SURVEY_STATUS_TASK_RELEASED)},
							{Restrictions.eq(paramMsSubSystem, loginUser.getAmMssubsystem().getUuidMsSubsystem())} };
					MsStatustask statustaskReleased = this.getManagerDAO().selectOne(MsStatustask.class, paramsStatusReleased);
					taskText.setMsStatustask(statustaskReleased);
					this.getManagerDAO().update(taskText);
					
					isTextReleased = true;
				}
			}
					
			notes = this.messageSource.getMessage("businesslogic.approval.notesapproval",
					new Object[] {dbModel.getTaskId() }, this.retrieveLocaleAudit(callerId));
			codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
			ret = this.messageSource.getMessage("businesslogic.approval.approved",
					new Object[] {dbModel.getTaskId() }, this.retrieveLocaleAudit(callerId));
		} else {
			notes = this.messageSource.getMessage("businesslogic.approval.notesrejected",
					new Object[] {dbModel.getTaskId() }, this.retrieveLocaleAudit(callerId));
			flag = 1;
			ret = this.messageSource.getMessage("businesslogic.approval.rejected",
					new Object[] {dbModel.getTaskId() }, this.retrieveLocaleAudit(callerId));
		}
		
		insertTaskHistory(callerId, dbModel.getMsStatustask(), dbModel, notes + " | " 
					+ objTaskHist.getNotes(), codeProcess, dbModel.getAmMsuser().getFullName());
		
		if (GlobalVal.SUBSYSTEM_MO.equals(dbModel.getFlagSource())) {
			commitOrder(loginUser, objTaskHist.getNotes(), dbModel, loginUser.getAmMssubsystem(), 
					flag, codeProcess, callerId);
		}
		
		//06-10-2015  Approval To Interface NC
		String taskType = GlobalVal.FLAG_TASK_PRE_SURVEY;
		if (GlobalVal.MSCOREF.equals(dbModel.getFlagSource()) || GlobalVal.MSCOREP.equals(dbModel.getFlagSource())) {
			taskType = GlobalVal.FLAG_TASK_SURVEY_PILOTING;
		} else if (GlobalVal.MSIAFOTO.equals(dbModel.getFlagSource())) {
			taskType = GlobalVal.FLAG_TASK_COMPLETED;
		}
		
		if (GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(statusTask)) {
			if (GlobalVal.FORM_OTS.equals(dbModel.getMsForm().getFormName())) {
				String flagSourceOts = getOtsFlagSource(String.valueOf(dbModel.getUuidTaskH()), null != dbModel.getTrTaskH());
				intFormLogic.submitTaskOTS(callerId, flagSourceOts, dbModel);
			} else if ((isTextReleased && GlobalVal.FLAG_TASK_SURVEY_PILOTING.equals(taskType) || GlobalVal.FLAG_TASK_COMPLETED.equals(taskType)
					|| GlobalVal.FORM_PRE_SURVEY.equals(dbModel.getMsForm().getFormName()))) {
				String isFinal = "1";
				if ("1".equals(isPilotingCae)) {
					intFormLogic.submitPolo(null, dbModel, taskType, null, callerId);
				} else {
					intFormLogic.submitNap(callerId, dbModel.getTaskId(), isFinal);
				}
				this.sendApprovalNotification(dbModel.getAmMsuser().getFcmToken(), dbModel.getCustomerName(), callerId);
			} else {
				this.sendApprovalNotification(dbModel.getAmMsuser().getFcmToken(), dbModel.getCustomerName(), callerId);
			}
		}
		intFormLogic.updateStatusIDE(dbModel, GlobalVal.UPDATE_STATUS_IDE_RELEASE_SURVEY, GlobalVal.UPDATE_STATUS_IDE_RELEASE_SURVEY, 
				loginUser, null, callerId);
		return ret;
	}
	
/* ==================================*** Mobile Servlet Approval ***================================== */
	@Override
	public List<ApprovalHeadBean> approvalHeader(String mode, AuditContext callerId){
		List<ApprovalHeadBean> listTaskList = new ArrayList<ApprovalHeadBean>();

		Object[][] head = { { "uuidUser", callerId.getCallerId() } };
		if ("branch".equals(mode)) {
			AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
					NumberUtils.toLong(callerId.getCallerId()));
			head = new Object[][]{ { "branchId", user.getMsBranch().getUuidBranch() }, 
					{ "subsystemId", user.getAmMssubsystem().getUuidMsSubsystem() } };
		}
		List header = this.getManagerDAO().selectAllNative(
				"branch".equals(mode) ? "task.approval.headerByBranch"
						: "task.approval.header", head, null);
		if (header != null && !header.isEmpty()) {
			Iterator itr = header.iterator();
			while (itr.hasNext()) {
				Map mp = (Map) itr.next();
				ApprovalHeadBean hb = new ApprovalHeadBean();
				hb.setUuidTaskH(mp.get("d0").toString());
				hb.setCustomerName((String)mp.get("d1"));
				hb.setCustomerPhone((String)mp.get("d2"));
				hb.setCustomerAddress((String)mp.get("d3"));
				hb.setNotes((String)mp.get("d4"));
				hb.setLatitude((String)mp.get("d5"));
				hb.setLongitude((String)mp.get("d6"));
				hb.setUuidForm(mp.get("d7").toString());
				hb.setFormLastUpdate((String)mp.get("d8"));
				hb.setApplNo((String)mp.get("d9"));
				hb.setIsVerification((String)mp.get("d10"));
				hb.setStatus((String)mp.get("d15"));
				hb.setPreprocessingSp((String)mp.get("d11"));
				hb.setDtmCrt((String)mp.get("d12"));
				hb.setIsPrintable((String)mp.get("d13"));
				hb.setTaskId((String)mp.get("d14"));
				hb.setZipCode((String) mp.get("d16"));
				hb.setAssignDate((String) mp.get("d17"));
				hb.setFormVersion((String) mp.get("d18"));
				listTaskList.add(hb);
			}
		}
		return listTaskList;
	}
		
	@Override
	public List<ApprovalHeadBean> approvalDetail(long uuidTaskH, AuditContext callerId){
		if (PropertiesHelper.isTaskDJson()) {
			return this.detailFromJson(uuidTaskH, callerId);
		}
		else {
			return this.detailFromRow(uuidTaskH, callerId);
		}
	}
	
	private List<ApprovalHeadBean> detailFromJson(long uuidTaskH, AuditContext callerId) {
		Assert.isTrue(uuidTaskH > 0L);
		
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", uuidTaskH}});
		
		if (docDb == null || StringUtils.isBlank(docDb.getDocument()))
			return Collections.emptyList();
		
		Gson gson = new Gson();
		TaskDocumentBean tdb = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
		if (tdb.getAnswers() == null || tdb.getAnswers().isEmpty())
			return Collections.emptyList();

		List<ApprovalHeadBean> resultDetails = new ArrayList<ApprovalHeadBean>();
		List<AnswerBean> answers = tdb.getAnswers();
		for (AnswerBean answer : answers) {
			ApprovalHeadBean vb = new ApprovalHeadBean();
			vb.setKey(answer.getQuestion().getLabel());
			
			String answerType = answer.getQuestion().getAnswerTypeCode();
			if (MssTool.isChoiceQuestion(answerType) && answer.getFinOptAnswers() != null) {
				vb.setFlag("0");
				
				for (int i = 0; i < answer.getFinOptAnswers().size(); i++) {
					if (i > 0) {
						try {
							ApprovalHeadBean cloneBean = (ApprovalHeadBean) BeanUtils.cloneBean(vb);
							cloneBean.setValue(answer.getFinOptAnswers().get(i).getDesc());
							resultDetails.add(cloneBean);
						} catch (IllegalAccessException | InstantiationException | InvocationTargetException
								| NoSuchMethodException e) {
							LOG.error("Error on cloning multipleAnswer", e);
						}
					}
					else {
						vb.setValue(answer.getFinOptAnswers().get(i).getDesc());
						resultDetails.add(vb);
					}
				}
								
				continue;
			}
			else if (MssTool.isImageQuestion(answerType) ||
					GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType)) {				
				if (MssTool.isImageQuestion(answerType)) {
					vb.setFlag("1");
				}
				else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType)) {
					vb.setFlag("2");
				}
				
				if (answer.getLobAnswer() != null && answer.getLobAnswer().isHasImage()) {
					vb.setValue(String.valueOf(answer.getQuestion().getUuidQuestion()));
				}
				else {
					vb.setValue("");
				}
				
				if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType)) {
					com.adins.mss.model.taskdjson.LocationBean location = answer.getLocation();
					if (null != location && null != location.getLat() && null != location.getLng()) {
						vb.setValue(StringUtils.join(location.getLat(), ",", location.getLng()));
					}
					else if (location != null) {
						LocationBean bean = new LocationBean();
						bean.setMcc(location.getMcc());
						bean.setMnc(location.getMnc());
						bean.setLac(location.getLac());
						bean.setCellid(location.getCid());
						bean = this.processLocation(bean, callerId);
						if (bean.getCoordinate() != null) {
							vb.setValue(bean.getCoordinate().getLatitude() + "," + bean.getCoordinate().getLongitude());
						}
						else {
							vb.setValue("");
						}
					}
					else {
						vb.setValue("");
					}
				}
			}
			else {
				vb.setFlag("0");
				vb.setValue(StringUtils.stripToEmpty(answer.getFinTxtAnswer()));
			}
			
			resultDetails.add(vb);
		}
		
		return resultDetails;
	}
	
	private List<ApprovalHeadBean> detailFromRow(long uuidTaskH, AuditContext callerId) {		
		Object[][] id = {{"uuidTaskH", uuidTaskH}};
		List detail = this.getManagerDAO().selectAllNative("task.approval.detail", id, null);
		
		if (detail == null || detail.isEmpty()) {
			return Collections.emptyList();
		}
		
		List<ApprovalHeadBean> resultDetails = new ArrayList<ApprovalHeadBean>();
		Iterator itr = detail.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			ApprovalHeadBean hb = new ApprovalHeadBean();
			hb.setKey((String) mp.get("d0"));
			hb.setValue((String) mp.get("d1"));
			hb.setFlag((String) mp.get("d2"));
			if ("2".equals(hb.getFlag())) {
				String[] val = hb.getValue().split(",");
				if (val.length > 2) {
					LocationBean bean = new LocationBean();
					bean.setMcc(Integer.parseInt(val[0]));
					bean.setMnc(Integer.parseInt(val[1]));
					bean.setLac(Integer.parseInt(val[2]));
					bean.setCellid(Integer.parseInt(val[3]));
					bean = this.processLocation(bean, callerId);
					if (bean.getCoordinate() != null) {
						hb.setValue(
						        bean.getCoordinate().getLatitude() + "," +
						        bean.getCoordinate().getLongitude());
					} 
					else {
						hb.setValue("");
					}
				}
			}					
			resultDetails.add(hb);
		}
		
		return resultDetails;
	}
	
	public LocationBean processLocation(LocationBean bean, AuditContext callerId) {		
		List<LocationBean> listLocations = new ArrayList<LocationBean>();		
		listLocations.add(bean);
		this.geocoder.geocodeCellId(listLocations, callerId);		
		return bean;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String submit(long uuidTaskH, String flag, AuditContext auditContext) {
		TrTaskhistory objTaskHist = new TrTaskhistory();
		objTaskHist.setNotes("1".equals(flag)?"Approved":"Rejected");
		String flagStatusTask = "1".equals(flag)?"&S":"&R";
		AmMsuser loginUser = this.getManagerDAO().selectOne(
				"from AmMsuser u "
				+ "join fetch u.amMssubsystem "
				+ "where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", NumberUtils.toLong(auditContext.getCallerId())}});
		String result = this.updateTaskH(objTaskHist, uuidTaskH, flagStatusTask, loginUser, null, null, auditContext);
		return result;
	}
		
	@Override
	public String getNotes(long uuid, AuditContext callerId) {
		String notes = StringUtils.EMPTY;
		String[] status = {GlobalVal.CODE_PROCESS_VERIFIED, GlobalVal.CODE_PROCESS_REJECTED_VERIFICATION};
		Object[][] params = { {Restrictions.eq("trTaskH.uuidTaskH", Long.valueOf(uuid))}, 
				{Restrictions.in("codeProcess", status)}};
		TrTaskhistory trTaskhistory = this.getManagerDAO().selectOne(TrTaskhistory.class, params);
		if (trTaskhistory == null) {
			notes = "";
		}
		else {
			notes = trTaskhistory.getNotes();
		}		
		return notes;
	}
		
	@Override
	public List getFormListCombo(AuditContext callerId) {
		List result = null;
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
		String[][] paramsForm = { { "isActive", "1" }, {"subsystemName", subsysName}};
		result = this.getManagerDAO()
				.selectAllNativeString(
						"SELECT form.UUID_FORM, form.FORM_NAME FROM MS_FORM "
						+ "form WITH (NOLOCK) JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
						+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
						+ "WHERE form.IS_ACTIVE = :isActive "
						+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName",
						paramsForm);
		return result;
	}
	
	@Override
	public List listApprovalDataEntry(String mode, Object params, AuditContext callerId) {
		List result = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		
		Map<String, String> paramsQuery = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		String where = paramsQuery.get("sb");
		
		StringBuilder queryBuilder = new StringBuilder();

		queryBuilder.append("SELECT * FROM ( ");
		queryBuilder.append("    SELECT b.*, ");
		queryBuilder.append("    ROW_NUMBER() OVER(PARTITION BY UUID_TASK_H ORDER BY CUSTOMER_NAME) dstnct ");
		queryBuilder.append("    FROM ( ");
		queryBuilder.append("        SELECT a.*, ");
		queryBuilder.append("        ROW_NUMBER() OVER (ORDER BY rownum) AS recnum ");
		queryBuilder.append("        FROM ( ");
		queryBuilder.append("            SELECT DISTINCT ");
		queryBuilder.append("                trth.UUID_TASK_H, ");
		queryBuilder.append("                msb.BRANCH_NAME, ");
		queryBuilder.append("                msr.REGION_NAME, ");
		queryBuilder.append("                trth.CUSTOMER_NAME, ");
		queryBuilder.append("                trth.APPL_NO, ");
		queryBuilder.append("                msu.FULL_NAME AS 'Customer Full Name', ");
		queryBuilder.append("                trth.ASSIGN_DATE, ");
		queryBuilder.append("                trth.SUBMIT_DATE, ");
		queryBuilder.append("                ISNULL(CAST(msucde.UNIQUE_ID AS VARCHAR), ' - ') AS 'NIK CDE', ");
		queryBuilder.append("                ISNULL(msucde.FULL_NAME, ' - ') AS 'CDE Full Name', ");
		queryBuilder.append("                ISNULL(CAST(trth.DTM_CLAIMED AS VARCHAR), ' - ') AS 'Claim Date', ");
		queryBuilder.append("                msp.PRIORITY_DESC, ");
		queryBuilder.append("                msp.ICON_PRIORITY, ");
		queryBuilder.append("                ROW_NUMBER() OVER ( ");
		queryBuilder.append("                    ORDER BY msp.PRIORITY_DESC ASC, trth.SUBMIT_DATE ASC ");
		queryBuilder.append("                ) AS rownum ");
		queryBuilder.append("            FROM tr_task_h trth WITH (NOLOCK) ");
		queryBuilder.append("            JOIN MS_BRANCH msb WITH (NOLOCK) ON trth.UUID_BRANCH = msb.UUID_BRANCH ");
		queryBuilder.append("            JOIN MS_PRIORITY msp WITH (NOLOCK) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY ");
		queryBuilder.append("            JOIN MS_STATUSTASK mst WITH (NOLOCK) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ");
		queryBuilder.append("            JOIN MS_FORM msf WITH (NOLOCK) ON trth.UUID_FORM = msf.UUID_FORM ");
		queryBuilder.append("            JOIN MS_FORMCATEGORY msfc WITH (NOLOCK) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY ");
		queryBuilder.append("            JOIN AM_MSUSER msu WITH (NOLOCK) ON msu.UUID_MS_USER = trth.UUID_MS_USER ");
		queryBuilder.append("            LEFT JOIN AM_MSUSER msucde WITH (NOLOCK) ON trth.UUID_CDE = msucde.UUID_MS_USER ");
		queryBuilder.append("            JOIN MS_SETTINGAPPROVALOTS msa WITH (NOLOCK) ON trth.UUID_FORM = msa.UUID_FORM ");
		queryBuilder.append("            AND msa.UUID_JOB_ASSIGN = msu.UUID_JOB ");
		queryBuilder.append("            JOIN MS_REGION msr WITH (NOLOCK) ON msb.UUID_REGION = msr.UUID_REGION ");
		queryBuilder.append("            WHERE mst.STATUS_CODE = 'PD' ");
		queryBuilder.append("            AND msa.IS_APPROVAL_ON_DE = '1' ");
		queryBuilder.append(where);
		queryBuilder.append("        ) a ");
		queryBuilder.append("        WHERE a.rownum <= :end ");
		queryBuilder.append("    ) b ");
		queryBuilder.append("    WHERE b.recnum >= :start ");
		queryBuilder.append(") c ");
		queryBuilder.append("WHERE dstnct = 1 ");
		
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[14][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[15][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}

		result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		/* Backup query lama
		 	result = this.getManagerDAO().selectAllNative(
					"task.approval.approvalDataEntry", params, null);
					*/
		return result;
	}
		
	@Override
	public Integer countlistApprovalDataEntry(String mode, Object params, AuditContext callerId) {
		Integer result = 0;
		Stack<Object[]> paramsStack = new Stack<>();
		Map<String, String> paramsQuery = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		String where = paramsQuery.get("sb");
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT COUNT(1) ")
				.append("from tr_task_h trth with (nolock) ")
				.append("JOIN MS_BRANCH msb with (nolock) ON trth.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY ")
				.append("JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM ")
				.append("JOIN MS_FORMCATEGORY msfc with (nolock) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY ")
				.append("JOIN AM_MSUSER msu with (nolock) ON msu.UUID_MS_USER = trth.UUID_MS_USER ")
				.append("JOIN MS_SETTINGAPPROVALOTS msa with (nolock) ON trth.UUID_FORM = msa.UUID_FORM AND msa.UUID_JOB_ASSIGN = msu.UUID_JOB ")
				.append("where mst.STATUS_CODE = 'PD' ")
				.append(where)
				.append("AND msa.IS_APPROVAL_ON_DE = '1' ");
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}

		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		
		/*
		   result = (Integer) this.getManagerDAO().selectOneNative(
					"task.approval.countApprovalDataEntry", params);*/
		return result;
	}
	
	@Override
	public List listApprovalOnWomf(String mode, Object params, AuditContext callerId) {
		List result = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		
		Map<String, String> paramsQuery = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		String where = paramsQuery.get("sb");
		StringBuilder queryBuilder = new StringBuilder();

		queryBuilder.append("SELECT * FROM ( ");
		queryBuilder.append("    SELECT b.*, ");
		queryBuilder.append("    ROW_NUMBER() OVER(PARTITION BY UUID_TASK_H ORDER BY CUSTOMER_NAME) dstnct ");
		queryBuilder.append("    FROM ( ");
		queryBuilder.append("        SELECT a.*, ");
		queryBuilder.append("        ROW_NUMBER() OVER (ORDER BY rownum) AS recnum ");
		queryBuilder.append("        FROM ( ");
		queryBuilder.append("            SELECT DISTINCT ");
		queryBuilder.append("                trth.UUID_TASK_H, ");
		queryBuilder.append("                msb.BRANCH_NAME, ");
		queryBuilder.append("                msr.REGION_NAME, ");
		queryBuilder.append("                trth.CUSTOMER_NAME, ");
		queryBuilder.append("                trth.APPL_NO, ");
		queryBuilder.append("                msu.FULL_NAME AS 'Customer Full Name', ");
		queryBuilder.append("                trth.ASSIGN_DATE, ");
		queryBuilder.append("                trth.SUBMIT_DATE, ");
		queryBuilder.append("                ISNULL(CAST(msucde.UNIQUE_ID AS VARCHAR), ' - ') AS 'NIK CDE', ");
		queryBuilder.append("                ISNULL(msucde.FULL_NAME, ' - ') AS 'CDE Full Name', ");
		queryBuilder.append("                ISNULL(CAST(trth.DTM_CLAIMED AS VARCHAR), ' - ') AS 'Claim Date', ");
		queryBuilder.append("                msp.PRIORITY_DESC, ");
		queryBuilder.append("                msp.ICON_PRIORITY, ");
		queryBuilder.append("                ROW_NUMBER() OVER ( ");
		queryBuilder.append("                    ORDER BY msp.PRIORITY_DESC ASC, trth.SUBMIT_DATE ASC ");
		queryBuilder.append("                ) AS rownum ");
		queryBuilder.append("            FROM tr_task_h trth WITH (NOLOCK) ");
		queryBuilder.append("            JOIN MS_BRANCH msb WITH (NOLOCK) ON trth.UUID_BRANCH = msb.UUID_BRANCH ");
		queryBuilder.append("            JOIN MS_PRIORITY msp WITH (NOLOCK) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY ");
		queryBuilder.append("            JOIN MS_STATUSTASK mst WITH (NOLOCK) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ");
		queryBuilder.append("            JOIN MS_FORM msf WITH (NOLOCK) ON trth.UUID_FORM = msf.UUID_FORM ");
		queryBuilder.append("            JOIN MS_FORMCATEGORY msfc WITH (NOLOCK) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY ");
		queryBuilder.append("            JOIN AM_MSUSER msu WITH (NOLOCK) ON msu.UUID_MS_USER = trth.UUID_MS_USER ");
		queryBuilder.append("            LEFT JOIN AM_MSUSER msucde WITH (NOLOCK) ON trth.UUID_CDE = msucde.UUID_MS_USER ");
		queryBuilder.append("            JOIN MS_SETTINGAPPROVALOTS msa WITH (NOLOCK) ON trth.UUID_FORM = msa.UUID_FORM ");
		queryBuilder.append("            AND msa.UUID_JOB_ASSIGN = msu.UUID_JOB ");
		queryBuilder.append("            JOIN MS_REGION msr WITH (NOLOCK) ON msb.UUID_REGION = msr.UUID_REGION ");
		queryBuilder.append("            WHERE mst.STATUS_CODE = 'PW' ");
		queryBuilder.append("            AND msa.IS_APPROVAL_ON_WOM = '1' ");
		queryBuilder.append(where);
		queryBuilder.append("        ) a ");
		queryBuilder.append("        WHERE a.rownum <= :end ");
		queryBuilder.append("    ) b ");
		queryBuilder.append("    WHERE b.recnum >= :start ");
		queryBuilder.append(") c ");
		queryBuilder.append("WHERE dstnct = 1 ");
		
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[14][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[15][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    String qr = queryBuilder.toString();
		result = this.getManagerDAO().selectAllNativeString(qr, sqlParams);
		/* Backup query lama
		 	result = this.getManagerDAO().selectAllNative(
					"task.approval.approvalList.onWomf", params, null);
					*/
		
		return result;
	}
		
	@Override
	public Integer countlistApprovalOnWomf(String mode, Object params, AuditContext callerId) {
		Integer result = 0;
		Stack<Object[]> paramsStack = new Stack<>();
		Map<String, String> paramsQuery = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		String where = paramsQuery.get("sb");

		StringBuilder sql = new StringBuilder();

		sql.append("SELECT COUNT(1) ");
		sql.append("FROM ( ");
		sql.append("    SELECT b.*, ");
		sql.append("           ROW_NUMBER() OVER (PARTITION BY b.UUID_TASK_H ORDER BY b.CUSTOMER_NAME) AS dstnct ");
		sql.append("    FROM ( ");
		sql.append("        SELECT trth.uuid_task_h, ");
		sql.append("               trth.customer_name ");
		sql.append("        FROM tr_task_h trth WITH (NOLOCK) ");
		sql.append("        JOIN MS_BRANCH msb WITH (NOLOCK) ON trth.UUID_BRANCH = msb.UUID_BRANCH ");
		sql.append("        JOIN MS_PRIORITY msp WITH (NOLOCK) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY ");
		sql.append("        JOIN MS_STATUSTASK mst WITH (NOLOCK) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ");
		sql.append("        JOIN MS_FORM msf WITH (NOLOCK) ON trth.UUID_FORM = msf.UUID_FORM ");
		sql.append("        JOIN MS_FORMCATEGORY msfc WITH (NOLOCK) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY ");
		sql.append("        JOIN AM_MSUSER msu WITH (NOLOCK) ON msu.UUID_MS_USER = trth.UUID_MS_USER ");
		sql.append("        LEFT JOIN AM_MSUSER msucde WITH (NOLOCK) ON trth.UUID_CDE = msucde.UUID_MS_USER ");
		sql.append("        JOIN MS_SETTINGAPPROVALOTS msa WITH (NOLOCK) ON trth.UUID_FORM = msa.UUID_FORM ");
		sql.append("        AND msa.UUID_JOB_ASSIGN = msu.UUID_JOB ");
		sql.append("        JOIN MS_REGION msr WITH (NOLOCK) ON msb.UUID_REGION = msr.UUID_REGION ");
		sql.append("        WHERE mst.STATUS_CODE = 'PW' ");
		sql.append("        AND msa.IS_APPROVAL_ON_WOM = '1' ");
		sql.append(where);
		sql.append("    ) b ");
		sql.append(") c ");
		sql.append("WHERE dstnct = 1;");

		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}

		result = (Integer) this.getManagerDAO().selectOneNativeString(sql.toString(), sqlParams);
		/*
		   result = (Integer) this.getManagerDAO().selectOneNative(
					"task.approval.countApprovalList.onWomf", params);*/			
		return result;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void rejectWithResurvey(long uuidTaskH, long uuidMsUser, String notes, String mode,
			AmMsuser loginBean, String flagApproval, String approvalReason, AuditContext callerId) {
		
		Gson gson = new Gson();
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH tth " + "join fetch tth.msBranch mb "
				+ "join fetch tth.msForm mf "
				+ "join fetch tth.amMsuser amu "
				+ "join fetch tth.msPriority mp "
				+ "join fetch tth.msStatustask ms "
				+ "join fetch ms.amMssubsystem ams "
				+ "where tth.uuidTaskH = :uuidTaskH",
				new Object[][] { { "uuidTaskH", uuidTaskH } });

		MsStatustask sts = this.getManagerDAO().selectOne(MsStatustask.class,
				trTaskH.getMsStatustask().getUuidStatusTask());
		
		if ("ApprovalDataEntry".equalsIgnoreCase(flagApproval)) {
			if (!GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equals(sts.getStatusCode())
					&& !GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(sts.getStatusCode())
					&& !GlobalVal.SURVEY_STATUS_TASK_APPROVALDATAENTRY.equals(sts.getStatusCode()))
				throw new ChangeException(this.messageSource.getMessage("businesslogic.error.changeexception", null,
						this.retrieveLocaleAudit(callerId)), sts.getStatusCode());
		} else {
		if (!GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equals(sts.getStatusCode())
				&& !GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(sts.getStatusCode())
				&& !GlobalVal.SURVEY_STATUS_TASK_APPROVALONWOMF.equals(sts.getStatusCode()))
			throw new ChangeException(this.messageSource.getMessage(
					"businesslogic.error.changeexception", null, this.retrieveLocaleAudit(callerId)), sts.getStatusCode());
		}
		
		AmMsuser amMsuser = this.getManagerDAO().selectOne(
				"from AmMsuser u " 
				+ "join fetch u.amMssubsystem "
				+ "where u.uuidMsUser = :uuidMsUser",
				new Object[][] { { "uuidMsUser", uuidMsUser } });

		String prevTaskId = trTaskH.getTaskId();

		String formVersion = String.valueOf(trTaskH.getFormVersion());
		
		String statusTaskDesc = sts.getStatusTaskDesc();
		String fullNotes = StringUtils.EMPTY;
		String description = StringUtils.EMPTY;
		if (StringUtils.isNotBlank(trTaskH.getNotes())) {
			fullNotes += trTaskH.getNotes() + " | ";
		}
		fullNotes += statusTaskDesc + " Notes : " + notes;
		
		if (StringUtils.isNotBlank(approvalReason)) {
			this.insertDetailApprovalReason(approvalReason, trTaskH, GlobalVal.SURVEY_STATUS_TASK_REJECTED_WITH_RESURVEY, callerId);
			
			MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, new Object [][] {
				{Restrictions.eq("code", approvalReason)},{Restrictions.eq("lovGroup", GlobalVal.LOV_TAG_APPROVAL_REASON)}, {Restrictions.eq("constraint1", GlobalVal.SURVEY_STATUS_TASK_REJECTED_WITH_RESURVEY)} });
			description = msLov.getDescription();

		}
		
		MsStatustask msStatustask = null;
		String notesH = StringUtils.EMPTY; 
		String codeProcess = StringUtils.EMPTY;
		
		if (!this.countResurveyTask(uuidTaskH)) {
			
			Object[][] paramsStatus = {
					{ Restrictions.eq(paramStatusCode, GlobalVal.SURVEY_STATUS_TASK_REJECTED) },
					{ Restrictions.eq(paramMsSubSystem, 
							amMsuser.getAmMssubsystem().getUuidMsSubsystem()) } };
			
			msStatustask = this.getManagerDAO().selectOne(
					MsStatustask.class, paramsStatus);
			
			notesH = "Task with ID: " + trTaskH.getTaskId() + " has been drop";
			codeProcess = GlobalVal.CODE_PROCESS_REJECTED;
			
			if (GlobalVal.FORM_OTS.equals(trTaskH.getMsForm().getFormName())) {
				String flagSourceOts = getOtsFlagSource(String.valueOf(trTaskH.getUuidTaskH()), null != trTaskH.getTrTaskH());
				if (null != trTaskH.getAmMsuser()) {
					this.getManagerDAO().fetch(trTaskH.getAmMsuser());
					this.getManagerDAO().fetch(trTaskH.getAmMsuser().getMsJob());
				}
				intFormLogic.submitTaskOTS(callerId, flagSourceOts, trTaskH);
				this.intFormLogic.updateStatusIDE(trTaskH,  GlobalVal.UPDATE_STATUS_IDE_REJECT,  GlobalVal.UPDATE_STATUS_IDE_REJECT, loginBean, null, callerId);
				
			} else if ("1".equalsIgnoreCase(trTaskH.getIsPilotingCae())) {
				intFormLogic.updateDataPolo(null, trTaskH, null, "Deleted", "F", null, description, null, null, callerId);				
				this.intFormLogic.updateStatusIDE(trTaskH,  GlobalVal.UPDATE_STATUS_IDE_REJECT,  GlobalVal.UPDATE_STATUS_IDE_REJECT, loginBean, null, callerId);
			}
			
			//taskSurveyText
			if (GlobalVal.MSCOREF.equalsIgnoreCase(trTaskH.getFlagSource()) || GlobalVal.MSCOREP.equalsIgnoreCase(trTaskH.getFlagSource())) {
				Object[][] paramsStatusDelete = {
						{ Restrictions.eq(paramStatusCode, GlobalVal.SURVEY_STATUS_TASK_DELETED) },
						{ Restrictions.eq(paramMsSubSystem, 
								amMsuser.getAmMssubsystem().getUuidMsSubsystem()) } };
				
				MsStatustask msStatustaskDelete = this.getManagerDAO().selectOne(
						MsStatustask.class, paramsStatusDelete);
				
				Object params[][] = new Object[][] {
					{Restrictions.ne("uuidTaskH", trTaskH.getUuidTaskH())}, 
					{Restrictions.eq("applNo", trTaskH.getApplNo())},
					{Restrictions.ne("msStatustask.uuidStatusTask", msStatustaskDelete.getUuidStatusTask())},
					{Restrictions.in("flagSource", new Object[] {GlobalVal.MSCOREF, GlobalVal.MSCOREP})}
				};
				Map taskHSurveyResult = this.getManagerDAO().selectAll(TrTaskH.class, params, null);
				if ((long)taskHSurveyResult.get(GlobalKey.MAP_RESULT_SIZE)>0) {
					List<TrTaskH> listTask = (List<TrTaskH>) taskHSurveyResult.get(GlobalKey.MAP_RESULT_LIST);
					for (TrTaskH taskH:listTask) {
						taskH.setMsStatustask(msStatustask);
						taskH.setDtmUpd(new Date());
						taskH.setUsrUpd(callerId.getCallerId());
						this.getManagerDAO().update(taskH);
						this.insertTaskHistory(callerId, msStatustask, taskH, notesH + " | " + notes, 
								codeProcess, amMsuser.getLoginId(), loginBean, null);
					}
				}
			}


		} else {

			taskServiceLogic.addTask(callerId, trTaskH.getFlagSource(),
					GlobalVal.SUBSYSTEM_MS, trTaskH.getMsForm().getFormName(),
					formVersion, trTaskH.getMsPriority().getPriorityDesc(), trTaskH.getCustomerName(), 
					trTaskH.getCustomerAddress(), trTaskH.getCustomerPhone(), trTaskH.getZipCode(),
					fullNotes,
					trTaskH.getMsBranch().getBranchCode(), trTaskH.getApplNo(),
					amMsuser.getLoginId(), MobileDefAnswer.DB.toString(), null,
					trTaskH.getLatitude() == null ? StringUtils.EMPTY : trTaskH.getLatitude().toString(),
					trTaskH.getLongitude() == null ? StringUtils.EMPTY : trTaskH.getLongitude().toString(), 
					null, loginBean.getFullName(), null, prevTaskId, mode, trTaskH.getIsPilotingCae());

			Object[][] paramsStatus = {
					{ Restrictions.eq(paramStatusCode, GlobalVal.SURVEY_STATUS_TASK_REJECTED_WITH_RESURVEY) },
					{ Restrictions.eq(paramMsSubSystem, 
							amMsuser.getAmMssubsystem().getUuidMsSubsystem()) } };
			
			msStatustask = this.getManagerDAO().selectOne(
					MsStatustask.class, paramsStatus);
			
			notesH = "Task with ID: " + trTaskH.getTaskId() + " has been rejected with resurvey";
			codeProcess = GlobalVal.CODE_PROCESS_REJECTED_WITH_RESURVEY;
			this.intFormLogic.updateStatusIDE(trTaskH, GlobalVal.UPDATE_STATUS_IDE_RESURVEY, GlobalVal.UPDATE_STATUS_IDE_RESURVEY,
												loginBean, null, callerId);
		}
		
		trTaskH.setMsStatustask(msStatustask);
		trTaskH.setDtmUpd(new Date());
		trTaskH.setUsrUpd(callerId.getCallerId());
		String approvalNotes = StringUtils.EMPTY;
		if (StringUtils.isNotBlank(trTaskH.getApprovalNotes())) {
			approvalNotes += trTaskH.getApprovalNotes() + " | ";
		}
		approvalNotes += statusTaskDesc + " Notes : " + notes;
		trTaskH.setApprovalNotes(approvalNotes);
		trTaskH.setNotes(fullNotes);
		this.getManagerDAO().update(trTaskH);

		this.insertTaskHistory(callerId, msStatustask, trTaskH, notesH + " | " + notes, 
				codeProcess, amMsuser.getLoginId(), loginBean, null);
	}
	
	public int sendApprovalNotification(String fcmToken, String customerName, AuditContext auditContext) {
		String apiKey = SpringPropertiesUtils
				.getProperty(GlobalKey.GOOGLE_API_FIREBASE);
		String message = "Your Task With Customer Name : " + customerName + " has been Approved";
		int result = 0;
		
		Content content = new Content();
		content.createData("Timeline", message);
		content.addRegId(fcmToken);
		
		try {
			URL url = new URL("https://fcm.googleapis.com/fcm/send");

			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-Type", "application/json");
			conn.setRequestProperty("Authorization", "key=" + apiKey);
			conn.setDoOutput(true);
			ObjectMapper mapper = new ObjectMapper();
			DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
			mapper.writeValue(wr, content);
			wr.flush();
			wr.close();
			int responseCode = conn.getResponseCode();
			LOG.trace("\nSending 'POST' request to URL : {}", url);
			LOG.trace("Response Code : {}", responseCode);

			BufferedReader in = new BufferedReader(new InputStreamReader(
					conn.getInputStream()));
			String inputLine;
			StringBuffer response = new StringBuffer();

			while ((inputLine = in.readLine()) != null) {
				response.append(inputLine);
			}
			in.close();
			LOG.trace(response.toString());
			JSONObject jsonObject = new JSONObject(response.toString());
			LOG.trace(jsonObject.toString());
			
			try {
				result = (int) jsonObject.get("failure");
			} catch (Exception e) {
				result = 0;
			}
		} catch (Exception e) {
			LOG.error("Error sending approval notifications", e);
		}
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateImageResurvey(String uuidTaskH, String listResurvey, AuditContext callerId) {
		if(!listResurvey.isEmpty()) {
			String[] listTaskD = listResurvey.split(",");
			int size = listTaskD.length;
			if(size > 0) {
				Object[][] params = {{ Restrictions.eq("trTaskH.uuidTaskH", Long.parseLong(uuidTaskH)) }};
				TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, params);
				this.getManagerDAO().updateNativeString("UPDATE TR_TASKDETAILLOB SET IS_RESURVEY='0' WHERE UUID_TASK_H = :uuidTaskH", new Object[][] {{"uuidTaskH", taskH.getUuidTaskH()}});
				for(String uuidQuestion : listTaskD) {
					Object[][] paramsD = {{ Restrictions.eq("trTaskH.uuidTaskH", taskH.getUuidTaskH()) },
											{ Restrictions.eq("msQuestion.uuidQuestion", Long.parseLong(uuidQuestion)) }};
					
					TrTaskdetaillob lob = this.getManagerDAO().selectOne(TrTaskdetaillob.class, paramsD);
					if(null != lob) {
						lob.setIsResurvey("1");
						this.getManagerDAO().update(lob);
					}
					
				}
			}
		}
	}
	
	public boolean countResurveyTask(long uuidTaskH) {
		StringBuilder query = new StringBuilder();
		query.append("WITH N (UUID_TASK_H, RESURVEY_ID) AS ( ");
		query.append(select);
		query.append("    UUID_TASK_H, ");
		query.append("    RESURVEY_ID ");
		query.append(from);
		query.append("    TR_TASK_H WITH (NOLOCK) ");
		query.append("where ");
		query.append("    UUID_TASK_H = :uuidTaskH ");
		query.append("union all ");
		query.append(select);
		query.append("    TR_TASK_H.UUID_TASK_H, ");
		query.append("    TR_TASK_H.RESURVEY_ID ");
		query.append(from);
		query.append("    N ");
		query.append("    JOIN TR_TASK_H WITH (NOLOCK) ON N.RESURVEY_ID = TR_TASK_H.UUID_TASK_H ");
		query.append(") ");
		query.append(select);
		query.append("  count(1) - 1 ");
		query.append(from);
		query.append("  N ");
		int recreateCount = (int) this.getManagerDAO().selectOneNativeString(query.toString(),
				new Object[][] { {"uuidTaskH", uuidTaskH} });
		
		AmGeneralsetting guarantorRecreateCountGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class,
				new Object[][] { {"gsCode", GlobalKey.GENERALSETTING_MAX_APPR_REJECT} });

		return (recreateCount < Integer.parseInt(guarantorRecreateCountGeneralSetting.getGsValue()));
	}

	@Transactional(readOnly=true)
	@Override
	public List getListLovReason(long uuidTaskH, String params, AuditContext callerId) {
		if (StringUtils.isNotBlank(params)) {
			StringBuilder query = new StringBuilder();
			
			query.append("SELECT CODE, DESCRIPTION FROM MS_LOV WITH (NOLOCK) WHERE LOV_GROUP = :lovGroup AND CONSTRAINT_1 = :filter ");
			if(!this.countResurveyTask(uuidTaskH)) {
				query.append("AND CODE != 'PASS' ");
			}
			query.append("AND IS_ACTIVE = '1' ORDER BY SEQUENCE");
			
			return this.getManagerDAO().selectAllNativeString(
					query.toString(),
					new Object [][] { {"lovGroup", GlobalVal.LOV_TAG_APPROVAL_REASON}, {"filter", params} });
		} else {
			return new ArrayList<>();
		}
	}
	
	private void insertDetailApprovalReason(String answer, TrTaskH trTaskH, String constraint1, AuditContext auditContext) {
		MsQuestion msq = this.getManagerDAO().selectOne(MsQuestion.class,
				new Object [][] { {Restrictions.eq("refId", GlobalVal.CODE_SVY_APPROVAL_REASON)} });
		MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, new Object [][] {
				{Restrictions.eq("code", answer)},{Restrictions.eq("lovGroup", GlobalVal.LOV_TAG_APPROVAL_REASON)}, {Restrictions.eq("constraint1", constraint1)} });
		
		TrTaskD trTaskD = this.getManagerDAO().selectOne(TrTaskD.class, new Object [][] {
				{Restrictions.eq("msQuestion.uuidQuestion", msq.getUuidQuestion())}, {Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())} });
		if (null != trTaskD) {
			trTaskD.setUsrUpd(auditContext.getCallerId());
			trTaskD.setDtmUpd(new Date());
			trTaskD.setMsLovByLovId(msLov);
			trTaskD.setOptionText(msLov.getDescription());
			
			this.getManagerDAO().update(trTaskD);
		} else {
			trTaskD = new TrTaskD();
			
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			trTaskD.setTrTaskH(trTaskH);
			trTaskD.setMsQuestion(msq);
			trTaskD.setQuestionText(msq.getQuestionLabel());
			trTaskD.setMsLovByLovId(msLov);
			trTaskD.setOptionText(msLov.getDescription());
			
			this.getManagerDAO().insert(trTaskD);
		}
	}
	
	/*
	0.  uuidUser
	1.  branchName 
	2.  customer 
	3.  address
	4.  applno
	5.  fieldPersonName
	6.  assignmentDateStart 
	7.  assignmentDateEnd 
	8.  submittedDateStart 
	9.  submittedDateEnd 
	10. uuidSubsystem 
	11. currentDate 
	12. uuidForm
	13. uuidJob
	14. start
	15. end
	 */
	private Map<String, String> sqlPagingBuilder(Object[][] params,Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null){
			return new HashMap<String, String>();
		}

		Map<String, String> mapResult = new HashMap<String, String>();
		StringBuilder sb = new StringBuilder();
		
		sb.append("AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem ");
		paramStack.push(new Object[] { "uuidSubsystem", Long.valueOf((String) params[10][1]) });
		
		sb.append("AND trth.SUBMIT_DATE IS NOT NULL ");
		
		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
		DateTime currentDate = formatter.parseDateTime((String) params[11][1]);
		currentDate = currentDate.minusMillis(3);
		// ---ASSIGN_DATE
		DateTime assignDateStart = null, assignDateEnd = null;
		if (params.length > 5&& !StringUtils.equals("%", (String) params[6][1])) {
			assignDateStart = formatter.parseDateTime((String) params[6][1]);
			assignDateStart = assignDateStart.withMillisOfDay(0);
		}
		if (params.length > 5&& !StringUtils.equals("%", (String) params[7][1])) {
			assignDateEnd = formatter.parseDateTime((String) params[7][1]);
			assignDateEnd = assignDateEnd.minusMillis(3);
		}
		
		if (assignDateStart != null && assignDateEnd != null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart == null && assignDateEnd != null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN '1990-01-01 00:00:00' AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart != null && assignDateEnd == null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN :assignDateStart AND :currentDate ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 


		// ---SUBMIT_DATE
		DateTime submitDateStart = null, submitDateEnd = null;
		if (params.length > 7&& !StringUtils.equals("%", (String) params[8][1])) {
			submitDateStart = formatter.parseDateTime((String) params[8][1]);
			submitDateStart = submitDateStart.withMillisOfDay(0);
		}
		if (params.length > 7&& !StringUtils.equals("%", (String) params[9][1])) {
			submitDateEnd = formatter.parseDateTime((String) params[9][1]);
			submitDateEnd = submitDateEnd.minusMillis(3);
		}
		if (submitDateStart != null && submitDateEnd != null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN :submitDateStart AND :submitDateEnd ");
			paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
			paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
		} 
		else if (submitDateStart == null && submitDateEnd != null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN '1990-01-01 00:00:00' AND :submitDateEnd ");
			paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
		} 
		else if (submitDateStart != null && submitDateEnd == null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN :submitDateStart AND :currentDate ");
			paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
		
		// ---OTHERS
		if(params[2][1] != null && !StringUtils.equals("%", (String) params[2][1])){
			sb.append("AND trth.customer_name LIKE '%' + :customerName + '%' ");
			paramStack.push(new Object[] { "customerName",(String) params[2][1] });
		}
		
		if (params[1][1] != null && !StringUtils.equals("%", (String) params[1][1])) {
			sb.append("AND msb.BRANCH_NAME = :branchName ");
			paramStack.push(new Object[] { "branchName", (String) params[1][1] });
		} 
		
		if(params[3][1] != null &&  !StringUtils.equals("%", (String) params[3][1])){
			sb.append("AND trth.customer_address LIKE '%' + :customerAddress + '%' ");
			paramStack.push(new Object[] { "customerAddress", (String) params[3][1] });
		}
		
		if(params[4][1] != null && !StringUtils.equals("%", (String) params[4][1])){
			sb.append("AND (trth.appl_no LIKE '%' + :applNo + '%') ");
			paramStack.push(new Object[] { "applNo", (String) params[4][1] });
		}
		
		if(params[5][1] != null && !StringUtils.equals("%", (String) params[5][1])){
			sb.append("AND msu.FULL_NAME LIKE '%'+ :fieldPersonName +'%' ");
			paramStack.push(new Object[] { "fieldPersonName", (String) params[5][1] });
		}
				
		if(params[12][1] != null && !StringUtils.equals("%", String.valueOf(params[12][1]))){
			sb.append("AND msf.UUID_FORM = :uuidForm ");
			paramStack.push(new Object[] { "uuidForm",  Long.valueOf(String.valueOf(params[12][1]))  });
		}
		
		if(params[13][1] != null && !StringUtils.equals("%", String.valueOf(params[13][1]))){
			sb.append("AND msa.UUID_JOB = :uuidJob ");
			paramStack.push(new Object[] { "uuidJob", (long) params[13][1] });
		}
		if(params[16][1] != null && !StringUtils.equals("%", String.valueOf(params[16][1]))){
			sb.append("AND msr.UUID_REGION = :uuidRegion ");
			paramStack.push(new Object[] { "uuidRegion", Long.valueOf(String.valueOf(params[16][1])) });
		}
		
		mapResult.put("sb", sb.toString());
		return mapResult;
	}
	
	@Override
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	public String claimTask(AmMsuser loginBean, String uuidTaskH,  AuditContext callerId) {
		LOG.info("Start Claim Task : {} oleh user dengan id : {}", uuidTaskH, loginBean.getUuidMsUser());
		try {	
			TrTaskH taskHBean = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] {{Restrictions.eq("uuidTaskH", Long.valueOf(uuidTaskH))}});
			if(null == taskHBean) {
				throw new IllegalArgumentException(this.messageSource.getMessage("businesslogic.approval.tasknotfound", 
						null, this.retrieveLocaleAudit(callerId)));
			} else {
				LOG.info("Task Info : ID {}, USR_CRT {}, AGR {}", taskHBean.getUuidTaskH(), taskHBean.getUsrCrt(), taskHBean.getApplNo());
			}
			
			if((taskHBean.getIsClaimByCde() != null) && (!BigInteger.valueOf(loginBean.getUuidMsUser()).equals(taskHBean.getUuidCde()))){
				return this.messageSource.getMessage("businesslogic.approval.invaliduserclaimed", null, this.retrieveLocaleAudit(callerId));
			}

			if(null == taskHBean.getIsClaimByCde()) {
				taskHBean.setUuidCde(BigInteger.valueOf(loginBean.getUuidMsUser()));
				taskHBean.setIsClaimByCde(1);
				taskHBean.setDtmClaimed(new Date());
				taskHBean.setDtmUpd(new Date());
				taskHBean.setUsrUpd(String.valueOf(loginBean.getUuidMsUser()));
				
				this.getManagerDAO().update(taskHBean);
				this.intFormLogic.updateStatusIDE(taskHBean, GlobalVal.UPDATE_STATUS_IDE_START_SLA, GlobalVal.UPDATE_STATUS_IDE_START_SLA, loginBean, null, callerId);
			}
		} catch(Exception e) {
			throw e;
		}
		try {
			Thread.sleep(3000);
		} catch (InterruptedException e) {
			LOG.warn("Interrupt", e.getMessage());
			Thread.currentThread().interrupt();
		}
		LOG.info("End Claim Task untuk task id : {} oleh user dengan id : {}", uuidTaskH, loginBean.getUuidMsUser());
		return GlobalVal.SERVICES_RESULT_SUCCESS;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String validateAllowApprovalOfTask(AmMsuser loginBean, String uuidApproval, boolean saveApproval, AuditContext callerId) {
		LOG.info("Begin validate allow approval of Task {}", uuidApproval);
		try {
			TrTaskH trTaskHBean = this.getManagerDAO().selectOne(TrTaskH.class, 
					new Object[][] {{Restrictions.eq("uuidTaskH", Long.valueOf(uuidApproval))}});
			
			if(null == trTaskHBean) {
				throw new IllegalArgumentException(this.messageSource.getMessage("businesslogic.approval.tasknotfound", 
						null, this.retrieveLocaleAudit(callerId)));
			}
			
			if(saveApproval && null == trTaskHBean.getIsClaimByCde()) {
				return this.messageSource.getMessage("businesslogic.approval.slaunclaimed", null, this.retrieveLocaleAudit(callerId));
			} else if(saveApproval) {
				AmGeneralsetting gsSLA = this.getManagerDAO().selectOne(AmGeneralsetting.class, 
						new Object[][] {{Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_SLA_APPROVAL_ON_WOM)}});
				Object[][] param = {{"uuidTaskH", trTaskHBean.getUuidTaskH()}};
				Integer difTimeMilis = (Integer) this.getManagerDAO().
					selectOneNativeString("select  DATEDIFF(MILLISECOND, DTM_CLAIMED, GETDATE()) from TR_TASK_H where UUID_TASK_H = :uuidTaskH", param);
				Integer durationFromGenset =  (Integer.parseInt(gsSLA.getGsValue())) * 60 * 1000;
				
				if (difTimeMilis > durationFromGenset) {    	
					LOG.info("Unclaiming Task: {}", trTaskHBean.getUuidTaskH());
					
					trTaskHBean.setUuidCde(null);
					trTaskHBean.setIsClaimByCde(null);
					trTaskHBean.setStartApproval(null);
					trTaskHBean.setEndApproval(null);
					trTaskHBean.setDtmUpd(new Date());
					trTaskHBean.setUsrUpd("Scheduler");
					
					this.getManagerDAO().update(trTaskHBean);
					this.intFormLogic.updateStatusIDE(trTaskHBean, GlobalVal.UPDATE_STATUS_IDE_OPEN, GlobalVal.UPDATE_STATUS_IDE_OPEN,
							null, null, callerId);
					
					return this.messageSource.getMessage("businesslogic.approval.slaunclaimed", null, this.retrieveLocaleAudit(callerId));
				}
			}
			
		} catch(Exception e) {
			throw e;
		}
		return GlobalVal.SERVICES_RESULT_SUCCESS;
	}
	
	@Transactional
	@Override
	public void insertStartApprovalDate(String uuidApproval, AuditContext callerId) {
		try {
			TrTaskH taskHBean = this.getManagerDAO().selectOne(TrTaskH.class,
					new Object[][] {{Restrictions.eq("uuidTaskH", Long.valueOf(uuidApproval))}});
			
			if(null == taskHBean) {
				throw new IllegalArgumentException(this.messageSource.getMessage("businesslogic.approval.tasknotfound", 
						null, this.retrieveLocaleAudit(callerId)));
			}
			taskHBean.setStartApproval(new Date());
			
			this.getManagerDAO().update(taskHBean);
			
		}catch(Exception e) {
			throw e;
		}	
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public List listTaskApprovalOnWomUnclaim(AuditContext callerId) {
		List result = Collections.emptyList();
		
		StringBuilder queryBuilder = new StringBuilder();
		queryBuilder.append("SELECT DISTINCT ");
		queryBuilder.append("    DATEDIFF(MILLISECOND, DTM_CLAIMED, GETDATE()) AS Minute, ");
		queryBuilder.append("    trth.UUID_TASK_H, ");
		queryBuilder.append("    msb.BRANCH_NAME, ");
		queryBuilder.append("    trth.CUSTOMER_NAME, ");
		queryBuilder.append("    trth.APPL_NO, ");
		queryBuilder.append("    msu.FULL_NAME AS 'Customer Full Name', ");
		queryBuilder.append("    trth.ASSIGN_DATE, ");
		queryBuilder.append("    trth.SUBMIT_DATE, ");
		queryBuilder.append("    msucde.UNIQUE_ID AS 'NIK CDE', ");
		queryBuilder.append("    msucde.FULL_NAME AS 'CDE Full Name', ");
		queryBuilder.append("    trth.DTM_UPD, ");
		queryBuilder.append("    msp.PRIORITY_DESC, ");
		queryBuilder.append("    msp.ICON_PRIORITY ");
		queryBuilder.append("FROM ");
		queryBuilder.append("    tr_task_h trth WITH (NOLOCK) ");
		queryBuilder.append("    JOIN MS_BRANCH msb WITH (NOLOCK) ON trth.UUID_BRANCH = msb.UUID_BRANCH ");
		queryBuilder.append("    JOIN MS_PRIORITY msp WITH (NOLOCK) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY ");
		queryBuilder.append("    JOIN MS_STATUSTASK mst WITH (NOLOCK) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ");
		queryBuilder.append("    JOIN MS_FORM msf WITH (NOLOCK) ON trth.UUID_FORM = msf.UUID_FORM ");
		queryBuilder.append("    JOIN MS_FORMCATEGORY msfc WITH (NOLOCK) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY ");
		queryBuilder.append("    JOIN AM_MSUSER msu WITH (NOLOCK) ON msu.UUID_MS_USER = trth.UUID_MS_USER ");
		queryBuilder.append("    JOIN AM_MSUSER msucde WITH (NOLOCK) ON msucde.UUID_MS_USER = trth.UUID_CDE ");
		queryBuilder.append("    JOIN MS_SETTINGAPPROVALOTS msa WITH (NOLOCK) ON trth.UUID_FORM = msa.UUID_FORM ");
		queryBuilder.append("      AND msa.UUID_JOB_ASSIGN = msu.UUID_JOB ");
		queryBuilder.append("WHERE ");
		queryBuilder.append("    mst.STATUS_CODE = 'PW' ");
		queryBuilder.append("    AND msa.IS_APPROVAL_ON_WOM = '1' ");
		queryBuilder.append("    AND mst.UUID_MS_SUBSYSTEM = (SELECT UUID_MS_SUBSYSTEM FROM AM_MSSUBSYSTEM WITH(NOLOCK) WHERE SUBSYSTEM_NAME = 'MS') ");
		queryBuilder.append("    AND trth.SUBMIT_DATE IS NOT NULL ");
		queryBuilder.append("    AND trth.IS_CLAIM_BY_CDE IS NOT NULL ");
		queryBuilder.append("    AND trth.END_APPROVAL IS NULL ");


    	return this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), null);
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doUnclaim(Map taskMap, AmGeneralsetting gsDuration, AuditContext callerId) {
		Map temp = taskMap;
		String uuidTaskH = temp.get("d1").toString();
		Integer difTime = Integer.parseInt(temp.get("d0").toString());
		Integer durationFromGenset =  (Integer.parseInt(gsDuration.getGsValue())) * 60 * 1000;
		if (difTime > durationFromGenset) {    	
			try {
				LOG.info("Start unclaim for task with id {}", uuidTaskH);
				TrTaskH taskHBean = this.getManagerDAO().selectOne(TrTaskH.class,
											new Object[][] {{Restrictions.eq("uuidTaskH", Long.valueOf(uuidTaskH))}});
				taskHBean.setUuidCde(null);
				taskHBean.setIsClaimByCde(null);
				taskHBean.setStartApproval(null);
				taskHBean.setEndApproval(null);
				taskHBean.setDtmUpd(new Date());
				taskHBean.setUsrUpd("Scheduler");
				
				this.getManagerDAO().update(taskHBean);
				this.intFormLogic.updateStatusIDE(taskHBean, GlobalVal.UPDATE_STATUS_IDE_OPEN, GlobalVal.UPDATE_STATUS_IDE_OPEN,
						null, null, callerId);
				
			} catch(Exception e) {
				throw e;
			}
			LOG.info("End unclaim for task with id {}", uuidTaskH);
		}
	}
	
	private AmMssubsystem retrieveSubsystemByName(String subsystemName,
			AuditContext callerId) {
		Object[][] msSubSystemParam = { { Restrictions.eq("subsystemName",
				subsystemName) } };
		return this.getManagerDAO().selectOne(AmMssubsystem.class, msSubSystemParam);
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public List<Map<String, Object>> getRegionListCombo(AuditContext callerId) {
		List<Map<String,Object>> result;
		StringBuilder query = new StringBuilder();
		query.append("SELECT REGION_NAME, UUID_REGION ");
		query.append("FROM MS_REGION WITH (NOLOCK) ");
		query.append("WHERE IS_ACTIVE = 1;");
		result = this.getManagerDAO().selectAllNativeString(query.toString(), null);
		return result;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public List<TblApiDashboard> listFailedUpdateStatusIDE(AuditContext callerId) {
		Object[][] params = {{Restrictions.eq("status", "0")}, {Restrictions.eq("isRetry", 1)}};
		return (List<TblApiDashboard>) this.getManagerDAO()
				.selectAll(TblApiDashboard.class, params, null).get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void retryHitUpdateStatusIDE(TblApiDashboard bean, AuditContext callerId) {
	
		Object[][] params = {{Restrictions.eq("uuidTaskH", bean.getTaskId())}};
		TrTaskH taskHBean = this.getManagerDAO().selectOne(TrTaskH.class, params);
		if(GlobalVal.UPDATE_TYPE_TASK_UPDATE.equalsIgnoreCase(bean.getType())) {
			intFormLogic.updateStatusWiseIDE(taskHBean, "Task Update", taskHBean.getAmMsuser(), bean, callerId);
		}else {
			this.intFormLogic.updateStatusIDE(taskHBean, bean.getType(), bean.getType(), null, bean, callerId);
		}
	}
	
	private String getDescription(String answer, String constraint1) {
		
		String description = StringUtils.EMPTY;
		MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, new Object [][] {
				{Restrictions.eq("code", answer)},{Restrictions.eq("lovGroup", GlobalVal.LOV_TAG_APPROVAL_REASON)}, {Restrictions.eq("constraint1", constraint1)} });
		
		if (null != msLov) {
			description = msLov.getDescription();
		}
		return description;
	}
	

}