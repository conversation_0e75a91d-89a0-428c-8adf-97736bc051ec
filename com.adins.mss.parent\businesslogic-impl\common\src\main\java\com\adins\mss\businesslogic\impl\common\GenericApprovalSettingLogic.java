package com.adins.mss.businesslogic.impl.common;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ApprovalSettingLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsSettingapprovalots;

@SuppressWarnings({"rawtypes", "unchecked"})
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericApprovalSettingLogic extends BaseLogic implements ApprovalSettingLogic {

	@Override
	public List listApprovalSettingOts(Object params, AuditContext callerId) {
		List result = null;
		result = this.getManagerDAO().selectAllNative("setting.approvalsetting.listApprovalSettingOts", params, null);
		return result;
	}
	
	@Override
	public Integer countListApprovalSettingOts(Object params, AuditContext callerId) {
		Integer result;
		result = (Integer) this.getManagerDAO().selectOneNative(
				"setting.approvalsetting.cntListApprovalSettingOts", params);
		return result;
	}
	
	@Override
	public MsSettingapprovalots getOneOts(long uuid, AuditContext callerId) {
		return this.getManagerDAO().selectOne(MsSettingapprovalots.class, uuid);
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateOts(MsSettingapprovalots obj, AuditContext callerId) {
		MsSettingapprovalots existingBean = this.getManagerDAO().selectOne(MsSettingapprovalots.class, obj.getUuidSettingApprovalOts());
		
		existingBean.setIsApprovalOnWom(obj.getIsApprovalOnWom());
		existingBean.setIsApprovalOnDe(obj.getIsApprovalOnDe());
		existingBean.setDtmUpd(new Date());
		existingBean.setUsrUpd(callerId.getCallerId());
		if("".equals(obj.getFlagSource())) {
			existingBean.setFlagSource(null);
		} else {
			existingBean.setFlagSource(obj.getFlagSource());
		}
		this.getManagerDAO().update(existingBean);
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void insertOts(MsSettingapprovalots obj, AuditContext callerId) {
		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[] { Restrictions.eq("uuidJob", obj.getUuidJob()) });
		paramStack.push(new Object[] { Restrictions.eq("uuidForm", obj.getUuidForm()) });
		
		if (obj.getUuidJobAssign() != 0L) {
			paramStack.push(new Object[] { Restrictions.eq("uuidJobAssign", obj.getUuidJobAssign()) });
		} 
		if (StringUtils.isNotBlank(obj.getFlagSource())) {
			paramStack.push(new Object[] { Restrictions.eq("flagSource", obj.getFlagSource())});
		}
		
		Object[][] sqlParams = new Object[paramStack.size()][2];
		for (int i = 0; i < paramStack.size(); i++) {
			Object[] objects = paramStack.get(i);
			sqlParams[i] = objects;
		}
		
		MsSettingapprovalots existingBean = this.getManagerDAO().selectOne(MsSettingapprovalots.class, sqlParams);
		if (null!=existingBean) {
			throw new EntityNotUniqueException("Setting Approval Already Exist", String.valueOf(obj.getUuidJob()));
		} 
		else {
			obj.setDtmCrt(new Date());
			obj.setUsrCrt(callerId.getCallerId());
			if("".equals(obj.getFlagSource())) {
				obj.setFlagSource(null);
			}
			this.getManagerDAO().insert(obj);
		}
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteOts(long uuid, AuditContext callerId) {
		MsSettingapprovalots bean = new MsSettingapprovalots();
		bean.setUuidSettingApprovalOts(uuid);
		this.getManagerDAO().delete(bean);
	}
	
	@Override
	public List<MsJob> getListJob(Object params, Object orders,
			 AuditContext callerId) {
		Map<String, Object> listJob = this.getManagerDAO().selectAll(MsJob.class, params, orders);
		
		return (List<MsJob>) listJob.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Override
	public List<MsForm> getFormList(Object params, Object order, AuditContext callerId) {
		Map<String, Object> formList = this.getManagerDAO().selectAll(MsForm.class, params, order);
		
		return (List<MsForm>) formList.get(GlobalKey.MAP_RESULT_LIST);
	}
	
}