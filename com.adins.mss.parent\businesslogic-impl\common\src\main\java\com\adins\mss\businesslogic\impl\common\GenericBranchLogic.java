package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Row;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.BranchLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.BranchException;
import com.adins.mss.exceptions.BranchException.Reason;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsArea;
import com.adins.mss.model.MsAreaofbranch;
import com.adins.mss.model.MsAreaofuser;
import com.adins.mss.model.MsAreapath;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsDealerofbranch;
import com.adins.mss.model.MsPromoofbranch;
import com.adins.mss.model.MsZipcodeofbranch;
import com.adins.mss.model.custom.BranchsBean;
import com.adins.mss.services.model.common.BranchBean;
import com.adins.mss.services.model.newconfins.ZipOfBranchSyncBean;
import com.adins.mss.util.MssTool;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericBranchLogic extends BaseLogic implements BranchLogic, MessageSourceAware {
	private AuditInfo auditInfo;
	private AuditInfo auditInfoMsArea;
	private AuditInfo auditInfoAreaOfBranch;
	private AuditInfo auditInfoAreaOfUser;
	private AuditInfo auditInfoAreaPath;
	private AuditInfo auditInfoZipcodeOfBranch;
	
	public GenericBranchLogic() {
		String[] pkCols = { "uuidBranch" };
		String[] pkDbCols = { "UUID_BRANCH" };
		String[] cols = { "uuidBranch", "msBranch.uuidBranch", "branchCode",
				"branchName", "branchAddress", "latitude", "longitude",
				"cashLimitDefault", "color", "isActive" };
		String[] dbCols = { "UUID_BRANCH", "PARENT_ID", "BRANCH_CODE",
				"BRANCH_NAME", "BRANCH_ADDRESS", "LATITUDE", "LONGITUDE",
				"CASH_LIMIT_DEFAULT", "COLOR", "IS_ACTIVE" };
		this.auditInfo = new AuditInfo("MS_BRANCH", pkCols, pkDbCols, cols,
				dbCols);

		String[] pkColsArea = { "uuidArea" };
		String[] pkDbColsArea = { "UUID_AREA" };
		String[] colsArea = { "uuidArea", "msArea", "areaName", "areaTypeCode",
				"latitude", "longitude", "radius", "isActive" };
		String[] dbColsArea = { "UUID_AREA", "PARENT_AREA_ID", "AREA_NAME",
				"AREA_TYPE_CODE", "LATITUDE", "LONGITUDE", "RADIUS",
				"IS_ACTIVE" };
		this.auditInfoMsArea = new AuditInfo("MS_AREA", pkColsArea,
				pkDbColsArea, colsArea, dbColsArea);

		String[] pkColsAoB = { "uuidAreaOfBranch" };
		String[] pkDbColsAoB = { "UUID_AREA_OF_BRANCH" };
		String[] colsAoB = { "uuidAreaOfBranch", "msBranch.uuidBranch",
				"msArea.uuidArea" };
		String[] dbColsAoB = { "UUID_AREA_OF_BRANCH", "UUID_BRANCH",
				"UUID_AREA" };
		this.auditInfoAreaOfBranch = new AuditInfo("MS_AREAOFBRANCH",
				pkColsAoB, pkDbColsAoB, colsAoB, dbColsAoB);

		String[] pkColsAp = { "uuidAreaPath" };
		String[] pkDbColsAp = { "UUID_AREA_PATH" };
		String[] colsAp = { "uuidAreaPath", "msArea.uuidArea", "sequence",
				"latitude", "longitude" };
		String[] dbColsAp = { "UUID_AREA_PATH", "UUID_AREA", "SEQUENCE",
				"LATITUDE", "LONGITUDE" };
		this.auditInfoAreaPath = new AuditInfo("MS_AREAPATH", pkColsAp,
				pkDbColsAp, colsAp, dbColsAp);

		String[] pkColsZoB = { "uuidZipCodeOfBranch" };
		String[] pkDbColsZoB = { "UUID_ZIP_CODE_OF_BRANCH" };
		String[] colsZoB = { "uuidZipCodeOfBranch", "zipCode", "subZipCode",
				"msBranch.uuidBranch" };
		String[] dbColsZoB = { "UUID_ZIP_CODE_OF_BRANCH", "ZIP_CODE",
				"SUB_ZIP_CODE", "UUID_BRANCH" };
		this.auditInfoZipcodeOfBranch = new AuditInfo("MS_ZIPCODEOFBRANCH",
				pkColsZoB, pkDbColsZoB, colsZoB, dbColsZoB);

		String[] pkColsAoU = { "uuidAreaOfUser" };
		String[] pkDbColsAoU = { "UUID_AREA_OF_USER" };
		String[] colsAou = { "uuidAreaOfUser", "msArea.uuidArea",
				"amMsuser.uuidMsUser" };
		String[] dbColsAoU = { "UUID_AREA_OF_USER", "UUID_AREA", "UUID_MS_USER" };
		this.auditInfoAreaOfUser = new AuditInfo("MS_AREAOFUSER", pkColsAoU,
				pkDbColsAoU, colsAou, dbColsAoU);
	}
	
	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
  
    private static final Logger LOG = LoggerFactory.getLogger(GenericBranchLogic.class);
    private static final String[] TEMPLATE_HEADER_MC = { "Branch Code (*)", "Branch Name (*)",
		"Branch Address (*)", "Branch Parent Code", "Zipcode Coverage (xxxxx;xxxxx;xxxxx)", "Limit COH (*)" };
    private static final String[] TEMPLATE_HEADER = { "Branch Code (*)", "Branch Name (*)",
		"Branch Address (*)", "Branch Parent Code", "Zipcode Coverage (xxxxx;xxxxx;xxxxx)" };
	private static final int[] HEADER_COLUMN_WIDTH_MC = { 20 * 256, 30 * 256,
		50 * 256, 20 * 256, 20 * 256, 20 * 256 };
	private static final int[] HEADER_COLUMN_WIDTH = { 20 * 256, 30 * 256,
		50 * 256, 20 * 256, 20 * 256 };
	
	private static final String[] TEMPLATE_HEADER_MC_ERROR = { "Branch Code (*)", "Branch Name (*)",
		"Branch Address (*)", "Branch Parent Code", "Zipcode Coverage (xxxxx;xxxxx;xxxxx)", "Limit COH (*)", "Error" };
	private static final String[] TEMPLATE_HEADER_ERROR = { "Branch Code (*)", "Branch Name (*)",
		"Branch Address (*)", "Branch Parent Code", "Zipcode Coverage (xxxxx;xxxxx;xxxxx)", "Error" };
	private static final int[] HEADER_COLUMN_WIDTH_MC_ERROR = { 20 * 256, 30 * 256,
		50 * 256, 20 * 256, 20 * 256, 20 * 256, 80 * 256 };
	private static final int[] HEADER_COLUMN_WIDTH_ERROR = { 20 * 256, 30 * 256,
		50 * 256, 20 * 256, 20 * 256, 80 * 256 };
	
	private String entityMessage = "businesslogic.branch.entitynotexistbranchcode";
	
	@Override
	public List listBranch(Object params, AuditContext callerId) {
		List result = new ArrayList();
		List list = this.getManagerDAO().selectAllNative("setting.branch.listBranch2", params, null);
		Iterator itr = list.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			BranchBean bean = new BranchBean();
			bean.setUuidBranch(Long.valueOf(mp.get("d0").toString()));
			bean.setBranchCode((String) mp.get("d1"));
			bean.setBranchName((String) mp.get("d2"));
			if(mp.get("d3") != null){
				bean.setParentId(Long.valueOf(mp.get("d3").toString()));
			}
			bean.setParentName((String) mp.get("d4"));
			bean.setBranchAddress((String) mp.get("d5"));
			bean.setColor((String) mp.get("d6"));
			bean.setLevel((Integer) mp.get("d7"));
			bean.setRownum((BigInteger) mp.get("d8"));
			bean.setCashLimitDefault(convertCurrency(String.valueOf(mp.get("d9"))));
			bean.setIsActive((String)mp.get("d10"));
			bean.setIsPilotingCAE(mp.get("d11")==null?"0": (String)mp.get("d11"));
			result.add(bean);
		}
		return result;
	}

	@Override
	public MsBranch getBranch(long uuid, AuditContext callerId) {
		MsBranch result = this.getManagerDAO().selectOne(
			"from MsBranch b left join fetch b.msBranch where b.uuidBranch = :uuidBranch", 
			new Object[][] {{"uuidBranch", uuid}});
		return result;
	}
	
	@Override
	public MsBranch getBranch(String branchCode, AuditContext callerId) {
		Boolean exist = this.checkBranchCode(branchCode);
		
		if (!exist) {
			throw new EntityNotFoundException(this.messageSource.getMessage(
					entityMessage,
					new Object[] { branchCode }, this.retrieveLocaleAudit(callerId)), branchCode);
		}
		
		MsBranch result = this.getManagerDAO().selectOne(
			"from MsBranch b left join fetch b.msBranch where b.branchCode = :branchCode", 
			new Object[][] {{"branchCode", branchCode}});
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertBranch(MsBranch objBranch, MsArea objArea, String polyPath, long uuidParent, String zipCode, AuditContext callerId) {
		Boolean exist = this.checkBranchCode(objBranch.getBranchCode());
		if (null != objArea.getAreaName()) {
			Object[][] paramsArea = {{Restrictions.eq("areaName", objArea.getAreaName())}};
			MsArea areaExist = this.getManagerDAO().selectOne(MsArea.class, paramsArea);
			
			if (null != areaExist) {
				throw new EntityNotUniqueException(
						this.messageSource.getMessage(
								"businesslogic.branch.entitynotuniquearea",
								new Object[] { objArea.getAreaName() },
								this.retrieveLocaleAudit(callerId)), objArea.getAreaName());
			}
		}
		
		if (exist) {
			throw new EntityNotUniqueException(
					this.messageSource.getMessage(
							"businesslogic.branch.entitynotuniquebranchcode",
							new Object[] { objBranch.getBranchCode() },
							this.retrieveLocaleAudit(callerId)), objBranch.getBranchCode());
		}
		
		if ("0".equals(String.valueOf(uuidParent))) {
			Object[][] params = {{ Restrictions.eq("branchName", GlobalVal.BRANCH_HO) }};
			MsBranch parent = this.getManagerDAO().selectOne(MsBranch.class, params);
			uuidParent = parent.getUuidBranch();
		}
		
		MsBranch parent = this.getManagerDAO().selectOne(MsBranch.class, uuidParent);		
		objBranch.setMsBranch(parent);
		objBranch.setUsrCrt(callerId.getCallerId());
		objBranch.setDtmCrt(new Date());
		
		if (StringUtils.isBlank(objBranch.getColor())){
			objBranch.setColor("#ffffff");
		}
		
		if("0".equals(getLimitCohEnabled(GlobalKey.GENERALSETTING_LIMIT_COH_ENABLED, callerId))){
			objBranch.setCashLimitDefault(null);
		}
		else{
			AmMsuser au = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId().toString()));
			if(au != null){
				if(GlobalVal.SUBSYSTEM_MC.equals(au.getAmMssubsystem().getSubsystemName())){
					if(StringUtils.isEmpty(String.valueOf(objBranch.getCashLimitDefault()))){
						throw new IllegalArgumentException(this.messageSource.getMessage(
								"businesslogic.branch.illegalargumentcashlimit",
								new Object[] { objBranch.getBranchCode() },
								this.retrieveLocaleAudit(callerId)));
					}
				}
				else if(GlobalVal.SUBSYSTEM_MS.equals(au.getAmMssubsystem().getSubsystemName()) ||
						GlobalVal.SUBSYSTEM_MO.equals(au.getAmMssubsystem().getSubsystemName()) ||
						GlobalVal.SUBSYSTEM_MT.equals(au.getAmMssubsystem().getSubsystemName())){
							objBranch.setCashLimitDefault(null);
				}
			}
		}
		
		this.getManagerDAO().insert(objBranch);
		this.auditManager.auditAdd(objBranch, auditInfo, callerId.getCallerId(), "");
		
		if (objArea.getAreaName() != null) {
			objArea.setUsrCrt(callerId.getCallerId());
			objArea.setDtmCrt(new Date());
			
			MsAreaofbranch objMsab = new MsAreaofbranch();
			objMsab.setUsrCrt(callerId.getCallerId());
			objMsab.setDtmCrt(new Date());
			objMsab.setMsArea(objArea);
			objMsab.setMsBranch(objBranch);
			this.getManagerDAO().insert(objArea);
			this.auditManager.auditAdd(objArea, auditInfoMsArea, callerId.getCallerId(), "");
			this.getManagerDAO().insert(objMsab);
			this.auditManager.auditAdd(objMsab, auditInfoAreaOfBranch, callerId.getCallerId(), "");
						
			if (!StringUtils.isBlank(polyPath)) {
				String[] latLngPoly = polyPath.split("&");
				for (int i = 0; i < latLngPoly.length; i++) {
					String[] latLngPoly2 = latLngPoly[i].split(",");
					MsAreapath objAreaPath = new MsAreapath();
					objAreaPath.setMsArea(objArea);
					objAreaPath.setSequence(NumberUtils.toShort(latLngPoly2[2]));
					objAreaPath.setLatitude(BigDecimal.valueOf(NumberUtils.toDouble(latLngPoly2[0])));
					objAreaPath.setLongitude(BigDecimal.valueOf(NumberUtils.toDouble(latLngPoly2[1])));
					objAreaPath.setUsrCrt(callerId.getCallerId());
					objAreaPath.setDtmCrt(new Date());
					this.getManagerDAO().insert(objAreaPath);
					this.auditManager.auditAdd(objAreaPath, auditInfoAreaPath, callerId.getCallerId(), "");
				}
			}
		}
		if (!StringUtils.isBlank(zipCode)) {
			String[] arrZipCode = zipCode.split(",");
			for (int i = 0; i < arrZipCode.length; i++) {
				MsZipcodeofbranch objZipCode = new MsZipcodeofbranch();
				objZipCode.setMsBranch(objBranch);
				objZipCode.setZipCode(arrZipCode[i]);
				objZipCode.setSubZipCode(arrZipCode[i]);
				objZipCode.setUsrCrt(callerId.getCallerId());
				objZipCode.setDtmCrt(new Date());
				this.getManagerDAO().insert(objZipCode);
				this.auditManager.auditAdd(objZipCode, auditInfoZipcodeOfBranch, callerId.getCallerId(), "");
			}
		}
	}
	
	public void insertBranch(MsBranch msb, AuditContext callerId) {
		String uuidBranch = this.getBranchExist(msb);
		if ( StringUtils.isBlank(uuidBranch) ) {
			msb.setBranchCode(msb.getBranchCode());
			msb.setBranchName(msb.getBranchName());
			msb.setBranchAddress(msb.getBranchAddress());

			msb.setMsBranch(msb.getMsBranch());
			msb.setIsActive("1");
			msb.setCashLimitDefault(msb.getCashLimitDefault());
			msb.setColor("#ffffff");
			msb.setDtmCrt(new Date());
			msb.setUsrCrt(callerId.getCallerId().toString());

			this.getManagerDAO().insert(msb);
			this.auditManager.auditAdd(msb, auditInfo, callerId.getCallerId(), "");
		}
		else {
			MsBranch branch = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(uuidBranch));
			branch.setBranchCode(msb.getBranchCode());
			branch.setBranchName(msb.getBranchName());
			branch.setBranchAddress(msb.getBranchAddress());

			branch.setMsBranch(msb.getMsBranch());
			msb.setDtmUpd(new Date());
			msb.setUsrUpd(callerId.getCallerId().toString());
			this.auditManager.auditEdit(branch, auditInfo, callerId.getCallerId(), "");
			this.getManagerDAO().update(branch);

		}
	}
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateBranch(MsBranch obj, MsArea objArea, String polyPath, String zipCode, AuditContext callerId) {
		if(checkAreaParent(objArea.getUuidArea()) && objArea.getAreaName() == null){
			throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION, this.messageSource.getMessage(
					"businesslogic.branch.areahaschild",
					null, this.retrieveLocaleAudit(callerId)), new Exception());
		}

		MsBranch dbBranch = this.getManagerDAO().selectOne(MsBranch.class, obj.getUuidBranch());
		
		Boolean exist = this.checkBranchCode(obj.getBranchCode());
		
		if (exist) {
			if (!dbBranch.getBranchCode().equals(obj.getBranchCode())) {
				throw new EntityNotUniqueException(
						this.messageSource.getMessage(
								"businesslogic.branch.entitynotuniquebranchcode",
								new Object[] { obj.getBranchCode() },
								this.retrieveLocaleAudit(callerId)), obj.getBranchCode());
			}
		}
		
		/* 2022-04-09 Perubahan is_piloting dari centralized ke is_piloting_cae pada menu setting branch
		//pengecekan perubahan piloting
		if(!obj.getIsPiloting().equalsIgnoreCase(dbBranch.getIsPiloting()==null?"0":dbBranch.getIsPiloting())) {
			Object[][] paramDelete = {{"uuidBranch", dbBranch.getUuidBranch()}};
			this.getManagerDAO().deleteNativeString("delete ort\r\n" + 
					"from oauth_access_token oat\r\n" + 
					"join AM_MSUSER amu with(nolock) on amu.login_id = oat.user_name\r\n" + 
					"join OAUTH_REFRESH_TOKEN ort on oat.refresh_token = ort.token_id\r\n" + 
					"where uuid_branch= :uuidBranch ", paramDelete);
			
			this.getManagerDAO().deleteNativeString("delete oat\r\n" + 
					"from oauth_access_token oat\r\n" + 
					"join AM_MSUSER amu with(nolock) on amu.login_id = oat.user_name\r\n" + 
					"where uuid_branch= :uuidBranch ", paramDelete);	
		}
		*/
		
		//pengecekan perubahan pilotingCAE
		if(!obj.getIsPilotingCAE().equalsIgnoreCase(dbBranch.getIsPilotingCAE()==null?"1":dbBranch.getIsPilotingCAE())) {
			Object[][] paramDelete = {{"uuidBranch", dbBranch.getUuidBranch()}};
			this.getManagerDAO().deleteNativeString("delete ort\r\n" + 
					"from oauth_access_token oat\r\n" + 
					"join AM_MSUSER amu with(nolock) on amu.login_id = oat.user_name\r\n" + 
					"join OAUTH_REFRESH_TOKEN ort on oat.refresh_token = ort.token_id\r\n" + 
					"where uuid_branch= :uuidBranch ", paramDelete);
			
			this.getManagerDAO().deleteNativeString("delete oat\r\n" + 
					"from oauth_access_token oat\r\n" + 
					"join AM_MSUSER amu with(nolock) on amu.login_id = oat.user_name\r\n" + 
					"where uuid_branch= :uuidBranch ", paramDelete);	
		}
		
		//pengecekan area name
		if (null != objArea.getAreaName()) {
			Object[][] paramsArea = {{Restrictions.eq("areaName", objArea.getAreaName())}};
			MsArea areaExist = this.getManagerDAO().selectOne(MsArea.class, paramsArea);
			if(areaExist != null){
				MsAreaofbranch msab = this.getManagerDAO().selectOne(MsAreaofbranch.class, 
						new Object[][]{ {Restrictions.eq("msBranch.uuidBranch", obj.getUuidBranch())}, {Restrictions.eq("msArea.uuidArea", areaExist.getUuidArea())} });
					
				if (null == msab){
					throw new EntityNotUniqueException(
							this.messageSource.getMessage(
									"businesslogic.branch.entitynotuniquearea",
									new Object[] { objArea.getAreaName() },
									this.retrieveLocaleAudit(callerId)), objArea.getAreaName());
				}
			}
		}
		//end pengecekan area name
		
		AmMsuser au = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId().toString()));
		if("0".equals(getLimitCohEnabled(GlobalKey.GENERALSETTING_LIMIT_COH_ENABLED, callerId))){
			obj.setCashLimitDefault(null);
		}
		else{
			if(au != null){
				if(GlobalVal.SUBSYSTEM_MC.equals(au.getAmMssubsystem().getSubsystemName())){
					if(StringUtils.isEmpty(String.valueOf(obj.getCashLimitDefault()))){
						throw new IllegalArgumentException(this.messageSource.getMessage(
								"businesslogic.branch.illegalargumentcashlimit",
								new Object[] { obj.getBranchCode() },
								this.retrieveLocaleAudit(callerId)));
					}
				}
			}
		}
		dbBranch.setIsPilotingCAE(obj.getIsPilotingCAE());
		dbBranch.setIsActive(obj.getIsActive());
		dbBranch.setBranchCode(obj.getBranchCode());
		dbBranch.setBranchName(obj.getBranchName());
		dbBranch.setBranchAddress(obj.getBranchAddress());
		if(StringUtils.isNotBlank(obj.getColor())){
			dbBranch.setColor(obj.getColor());
		}
		dbBranch.setLatitude(obj.getLatitude());
		dbBranch.setLongitude(obj.getLongitude());
		dbBranch.setCashLimitDefault(obj.getCashLimitDefault());
		dbBranch.setMsHolidayH(obj.getMsHolidayH());
		dbBranch.setUsrUpd(callerId.getCallerId());
		dbBranch.setDtmUpd(new Date());
		
		this.auditManager.auditEdit(dbBranch, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbBranch);
		
		if (objArea.getAreaName() != null) {
			MsArea dbArea = this.getManagerDAO().selectOne(MsArea.class, objArea.getUuidArea());
			if(dbArea == null) {
				dbArea = new MsArea();
				dbArea.setDtmCrt(new Date());
				dbArea.setUsrCrt(callerId.getCallerId());
				dbArea.setAreaTypeCode(objArea.getAreaTypeCode());
				dbArea.setAreaName(objArea.getAreaName());
				dbArea.setRadius(objArea.getRadius());
				dbArea.setLatitude(objArea.getLatitude());
				dbArea.setLongitude(objArea.getLongitude());
				dbArea.setIsActive("1");
				this.getManagerDAO().insert(dbArea);
				this.auditManager.auditAdd(dbArea, auditInfoMsArea, callerId.getCallerId(), "");
				MsAreaofbranch msaob = new MsAreaofbranch();
				msaob.setDtmCrt(new Date());
				msaob.setUsrCrt(callerId.getCallerId());
				msaob.setMsArea(dbArea);
				msaob.setMsBranch(obj);
				this.getManagerDAO().insert(msaob);
				this.auditManager.auditAdd(msaob, auditInfoAreaOfBranch, callerId.getCallerId(), "");
			} 
			else {
				dbArea.setUsrUpd(callerId.getCallerId());
				dbArea.setDtmUpd(new Date());
				dbArea.setAreaTypeCode(objArea.getAreaTypeCode());
				dbArea.setAreaName(objArea.getAreaName());
				dbArea.setRadius(objArea.getRadius());
				dbArea.setLatitude(objArea.getLatitude());
				dbArea.setLongitude(objArea.getLongitude());
				this.auditManager.auditEdit(dbArea, auditInfoMsArea, callerId.getCallerId(), "");
				this.getManagerDAO().update(dbArea);
			}
			
			if (!StringUtils.isBlank(polyPath)) {
				if (!StringUtils.isBlank(String.valueOf(objArea.getUuidArea())))
					deleteAreaPath(String.valueOf(objArea.getUuidArea()));
				
				String[] latLngPoly = polyPath.split("&");
				for (int i = 0; i < latLngPoly.length; i++) {
					String[] latLngPoly2 = latLngPoly[i].split(",");
					MsAreapath objAreaPath = new MsAreapath();
					objAreaPath.setMsArea(dbArea);
					objAreaPath.setSequence(NumberUtils.toShort(latLngPoly2[2]));
					objAreaPath.setLatitude(BigDecimal.valueOf(NumberUtils.toDouble(latLngPoly2[0])));
					objAreaPath.setLongitude(BigDecimal.valueOf(NumberUtils.toDouble(latLngPoly2[1])));
					objAreaPath.setUsrCrt(callerId.getCallerId());
					objAreaPath.setDtmCrt(new Date());
					this.getManagerDAO().insert(objAreaPath);
					this.auditManager.auditAdd(objAreaPath, auditInfoAreaPath, callerId.getCallerId(), "");
				}
			} 
			else {
				deleteAreaPath(String.valueOf(objArea.getUuidArea()));
			}
			
			//add user area bila pada cabang ditambahkan area
			if ((GlobalVal.SUBSYSTEM_MS.equals(au.getAmMssubsystem().getSubsystemName()) ||
					GlobalVal.SUBSYSTEM_MC.equals(au.getAmMssubsystem().getSubsystemName())) && objArea.getAreaName() != null) {
				Object[][] paramsBranch = { { Restrictions.eq("msBranch.uuidBranch", obj.getUuidBranch()) } };
				Map<String, Object> resultUser = this.getManagerDAO().list(AmMsuser.class,
						paramsBranch, null);
				List<AmMsuser> listUser = (List) resultUser.get(GlobalKey.MAP_RESULT_LIST);

				if (listUser != null) {
					for (AmMsuser amMsuser : listUser) {
						Object[][] params = { { Restrictions.eq("amMsuser.uuidMsUser", amMsuser.getUuidMsUser()) } };
						Map<String, Object> resultArea = this.getManagerDAO().list(MsAreaofuser.class, params, null);
						List<MsAreaofuser> listArea = (List) resultArea.get(GlobalKey.MAP_RESULT_LIST);
						if (listArea == null || listArea.isEmpty()) {
							insertAreaOfUser(String.valueOf(dbArea.getUuidArea()), amMsuser, callerId);
						}
					}
				}
			}
			//end add user area bila pada cabang ditambahkan area	
		} 
		else {
			if ( !checkAreaParent(objArea.getUuidArea())) {
				MsAreaofbranch msab = this.getManagerDAO().selectOne(MsAreaofbranch.class, 
					new Object[][]{ {Restrictions.eq("msBranch.uuidBranch", obj.getUuidBranch())}, {Restrictions.eq("msArea.uuidArea", objArea.getUuidArea())} });
				
				Map<String, Object> checkList = this.getManagerDAO().count(MsAreaofuser.class, new Object[][]{ {Restrictions.eq( "msArea.uuidArea", objArea.getUuidArea() )} });

				if (msab != null) {
					if((Long) checkList.get(GlobalKey.MAP_RESULT_SIZE) == 0){
						this.getManagerDAO().delete(msab);
						
						this.getManagerDAO().deleteNativeString("delete from MS_AREAPATH where uuid_area = :uuidArea", new String[][]{ {"uuidArea", String.valueOf(objArea.getUuidArea())} });
						
						MsArea msa = this.getManagerDAO().selectOne(MsArea.class, objArea.getUuidArea());
						this.getManagerDAO().delete(msa);
					}
					else{
						throw new DatabaseException(
								DatabaseException.Reason.CONSTRAINT_VIOLATION,
								this.messageSource.getMessage(
										"businesslogic.error.haschild", null,
										this.retrieveLocaleAudit(callerId)), new Exception());
					}
				}
			}
		}
		
		if (!StringUtils.isBlank(zipCode)) {
			String[][] paramsUuid = { { "uuidBranch", String.valueOf(obj.getUuidBranch()) } };
			this.getManagerDAO().deleteNative("setting.branch.deleteZipCode", paramsUuid);
			
			String[] arrZipCode = zipCode.split(",");
			for (int i = 0; i < arrZipCode.length; i++) {
				MsZipcodeofbranch objZipCode = new MsZipcodeofbranch();
				objZipCode.setMsBranch(obj);
				objZipCode.setZipCode(arrZipCode[i]);
				objZipCode.setSubZipCode(arrZipCode[i]);
				objZipCode.setUsrCrt(callerId.getCallerId());
				objZipCode.setDtmCrt(new Date());
				this.getManagerDAO().insert(objZipCode);
				this.auditManager.auditAdd(objZipCode, auditInfoZipcodeOfBranch, callerId.getCallerId(), "");
			}
		} 
		else {
			String[][] paramsUuid = { { "uuidBranch", String.valueOf(obj.getUuidBranch()) } };
			this.getManagerDAO().deleteNative("setting.branch.deleteZipCode", paramsUuid);
		}
		
	}
	
	@Override
	public void updateBranch(MsBranch obj, AuditContext callerId) {
		MsBranch dbBranch = this.getManagerDAO().selectOne(MsBranch.class, obj.getUuidBranch());
		Boolean exist = this.checkBranchCode(obj.getBranchCode());
		
		if (exist) {
			if (!dbBranch.getBranchCode().equals(obj.getBranchCode())) {
				throw new EntityNotUniqueException(
						this.messageSource.getMessage(
								"businesslogic.branch.entitynotuniquebranchcode",
								new Object[] { obj.getBranchCode() },
								this.retrieveLocaleAudit(callerId)), obj.getBranchCode());
			}
		}
		
		if("0".equals(getLimitCohEnabled(GlobalKey.GENERALSETTING_LIMIT_COH_ENABLED, callerId))){
			obj.setCashLimitDefault(null);
		}
		else{
			AmMsuser au = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
			if(au != null){
				if(GlobalVal.SUBSYSTEM_MC.equals(au.getAmMssubsystem().getSubsystemName())){
					if(StringUtils.isEmpty(String.valueOf(obj.getCashLimitDefault()))){
						throw new IllegalArgumentException(this.messageSource.getMessage(
								"businesslogic.branch.illegalargumentcashlimit",
								new Object[] { obj.getBranchCode() },
								this.retrieveLocaleAudit(callerId)));
					}
				}
				else if(GlobalVal.SUBSYSTEM_MS.equals(au.getAmMssubsystem().getSubsystemName()) ||
						GlobalVal.SUBSYSTEM_MC.equals(au.getAmMssubsystem().getSubsystemName()) ||
						GlobalVal.SUBSYSTEM_MT.equals(au.getAmMssubsystem().getSubsystemName())){
							obj.setCashLimitDefault(null);
				}
			}
		}

		dbBranch.setIsActive(obj.getIsActive());
		dbBranch.setBranchCode(obj.getBranchCode());
		dbBranch.setBranchName(obj.getBranchName());
		dbBranch.setBranchAddress(obj.getBranchAddress());
		dbBranch.setColor(obj.getColor());
		dbBranch.setCashLimitDefault(obj.getCashLimitDefault());
		dbBranch.setUsrUpd(callerId.getCallerId());
		dbBranch.setDtmUpd(new Date());
		
		this.auditManager.auditEdit(dbBranch, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbBranch);
		
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateZipCode(MsBranch obj, String zipCode, AuditContext callerId) {
		String[][] paramsUuid = { { "uuidBranch", String.valueOf(obj.getUuidBranch()) } };
		this.getManagerDAO().deleteNative("setting.branch.deleteZipCode", paramsUuid);
		if (!StringUtils.isBlank(zipCode)) {
			String[] arrZipCode = zipCode.split(";");
			for (int i = 0; i < arrZipCode.length; i++) {
				MsZipcodeofbranch objZipCode = new MsZipcodeofbranch();
				objZipCode.setMsBranch(obj);
				objZipCode.setZipCode(arrZipCode[i]);
				objZipCode.setSubZipCode(arrZipCode[i]);
				objZipCode.setUsrCrt(callerId.getCallerId());
				objZipCode.setDtmCrt(new Date());
				this.getManagerDAO().insert(objZipCode);
				this.auditManager.auditAdd(objZipCode, auditInfoZipcodeOfBranch, callerId.getCallerId(), "");
			}
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updatePilotingCAE(String branchCode, String isPiloting, AuditContext callerId) {

		Boolean exist = this.checkBranchCode(branchCode);
		
		if (!exist) {
			throw new EntityNotFoundException(this.messageSource.getMessage(
					entityMessage,
					new Object[] { branchCode }, this.retrieveLocaleAudit(callerId)), branchCode);
		}
			
		Object[][] params = {{ Restrictions.eq("branchCode", branchCode) }};
		MsBranch objBranch = this.getManagerDAO().selectOne(MsBranch.class, params);
		
		objBranch.setIsPilotingCAE(isPiloting);
		objBranch.setUsrUpd(callerId.getCallerId());
		objBranch.setDtmUpd(new Date());
		
		this.auditManager.auditEdit(objBranch, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(objBranch);
	}

	

	private boolean checkAreaParent(long uuidArea){
		Map<String, Object> checkList = this.getManagerDAO().count(MsArea.class, new Object[][]{ {Restrictions.eq( "msArea.uuidArea", uuidArea )} });
		if ((Long) checkList.get(GlobalKey.MAP_RESULT_SIZE) > 0){
			return true;
		}
		else{
			return false;
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteBranch(long uuidBranch, AuditContext callerId) {
		MsBranch objBranch = this.getManagerDAO().selectOne(MsBranch.class, uuidBranch);
		
		Object[][] paramsUuid = { { "uuidBranch", uuidBranch } };
		Integer existUser = (Integer)this.getManagerDAO().selectOneNative("setting.branch.cekUserExist", paramsUuid);	
		Integer hasChild = (Integer)this.getManagerDAO().selectOneNative("setting.branch.cekHasChild", paramsUuid);
		Map hasPromo = (Map)this.getManagerDAO().count(MsPromoofbranch.class, new Object[][]{{Restrictions.eq("msBranch.uuidBranch", uuidBranch)}});
		Map hasDealerOfBranch = (Map)this.getManagerDAO().count(MsDealerofbranch.class, new Object[][]{{Restrictions.eq("msBranch.uuidBranch", uuidBranch)}});
		
		if (existUser > 0 || hasChild > 0 || (Long) hasPromo.get(GlobalKey.MAP_RESULT_SIZE) > 0 || (Long) hasDealerOfBranch.get(GlobalKey.MAP_RESULT_SIZE) > 0 ) {
			throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION, this.messageSource.getMessage(
					"businesslogic.error.haschild", null,
					this.retrieveLocaleAudit(callerId)), new Exception());
		}
		
		this.getManagerDAO().deleteNative("setting.branch.deleteZipCode", paramsUuid);
		Object[][] params = {{Restrictions.eq("msBranch.uuidBranch", uuidBranch)}};
		MsAreaofbranch msAreaofbranch = (MsAreaofbranch) this.getManagerDAO().selectOne(MsAreaofbranch.class, params);
		
		if (msAreaofbranch != null) {
			Map parentArea = (Map)this.getManagerDAO().count(MsArea.class,  new Object[][]{{Restrictions.eq("msArea.uuidArea", msAreaofbranch.getMsArea().getUuidArea())}});
			if ((Long)parentArea.get(GlobalKey.MAP_RESULT_SIZE) > 0) {
				throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION, this.messageSource.getMessage(
						"businesslogic.error.haschild", null,
						this.retrieveLocaleAudit(callerId)), new Exception());
			}
			MsArea objArea = this.getManagerDAO().selectOne(MsArea.class, msAreaofbranch.getMsArea().getUuidArea());
						
			deleteAreaPath(String.valueOf(msAreaofbranch.getMsArea().getUuidArea()));
			this.getManagerDAO().delete(msAreaofbranch);
			this.getManagerDAO().delete(objArea);
		}
		
		this.auditManager.auditDelete(objBranch, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().delete(objBranch);	
	}

	@Override
	public Map<String, Object> listMemberOfBranch(Object params, int pageNumber, int pageSize, AuditContext callerId) {
		String[][] orders = {{"fullName", "ASC"}};
		Map<String, Object> result = this.getManagerDAO().selectAll(AmMsuser.class,	params, orders, pageNumber, pageSize);
		return result;
	}

	@Override
	public Integer countListBranch(Object params, AuditContext callerId) {
		Integer result = (Integer)this.getManagerDAO().selectOneNative("setting.branch.listBranchCount2", params);
		return result;
	}
	
	public void deleteAreaPath(String uuidArea){
		String[][] params = { { "uuidArea", uuidArea } };
		this.getManagerDAO().deleteNative("setting.branch.deleteAreaPath", params);
	}

	@Override
	public String getZipCode(long uuid, AuditContext callerId) {
		Map<String, Object> result = null;
		String stringResult = "";
			Object[][] params = { { Restrictions.eq("msBranch.uuidBranch", uuid) } };
	
			result = this.getManagerDAO().list(MsZipcodeofbranch.class, params,
					null);
			List<MsZipcodeofbranch> listBranchZipCode = (List) result
					.get(GlobalKey.MAP_RESULT_LIST);
	
			if (listBranchZipCode != null) {
				for (MsZipcodeofbranch bean : listBranchZipCode) {
					stringResult += bean.getZipCode() + ",";
				}
				if (stringResult.length() > 2)
					stringResult = stringResult.substring(0, (stringResult.length() - 1));
			} 
			else {
				stringResult = "";
			}
		return stringResult;
	}

	@Override
	public List getSubBranchArea(long uuid, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuid } };
		List result = this.getManagerDAO().selectAllNative("setting.branch.getSubBranchArea", params, null);
		return result;
	}

	@Override
	public String getUuidArea(List listOfBA, AuditContext callerId) {
		String result = new String();
		for (int i = 0; i < listOfBA.size(); i++) {
			Map mapUuidArea = (HashMap) listOfBA.get(i);
			result = result + mapUuidArea.get("d1") + ",";			
		}
		return result.substring(0, (result.length() - 1));
	}
	
	@Override
	public List getPathArea(String listUuidArea, AuditContext callerId) {
		List<MsAreapath> listOfAreaPath = new ArrayList<>();
		String[] uuids = listUuidArea.split(",");
		long [] uuidLong = (long[])MssTool.strArrayToLongArray(uuids);
		Long uuidArea [] = new Long[uuidLong.length];
		int i=0;
		for(long temp:uuidLong){
			uuidArea[i++] = temp;
		}
		Object[][] params = { {Restrictions.in("msArea.uuidArea", uuidArea)} };
		Map<String, Object> map = this.getManagerDAO().list(MsAreapath.class, params, null);
		listOfAreaPath = (List) map.get(GlobalKey.MAP_RESULT_LIST);
		return listOfAreaPath;
	}

	@Override
	public List getParentBranchArea(long uuid, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuid } };
		List result = this.getManagerDAO().selectAllNative("setting.branch.getParentBranchArea", params, null);	
		return result;
	}

	@Override
	public List getUserBranchArea(long uuid, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuid } };
		List result = this.getManagerDAO().selectAllNative("setting.branch.getUserBranchArea", params, null);	
		return result;
	}
	
	@Override
	public List getBranchArea(long uuid, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuid } };
		List result = this.getManagerDAO().selectAllNative("setting.branch.getBranchArea", params, null);	
		return result;
	}	
	
	public boolean checkBranchCode(String branchCode) {
		Map<String, Object> mapBranch = this.getManagerDAO().list(MsBranch.class, null, null);
		List<MsBranch> listBranch = (List<MsBranch>) mapBranch.get(GlobalKey.MAP_RESULT_LIST);
		
		for (int i = 0; i < listBranch.size(); i++) {
			if (listBranch.get(i).getBranchCode().equals(branchCode)) {
				return true;
			}
		}
		return false;
	}

	@Override
	public boolean isExistsAreaName(long uuidBranch, String areaName, AuditContext auditContext){
		Object[][] param = { {Restrictions.eq("uuidBranch", uuidBranch)} };
		Object[][] params = { {Restrictions.eq("areaName", areaName)} };
		
		Map mapB = this.getManagerDAO().count(MsBranch.class, param);
		Long countB = (long) mapB.get(GlobalKey.MAP_RESULT_SIZE);
		boolean branchExists = (countB > 0) ? false : true;
		
		if (branchExists) {
			Map map = this.getManagerDAO().count(MsArea.class, params);
			Long count = (long) map.get(GlobalKey.MAP_RESULT_SIZE);
			boolean isExists = (count > 0) ? true : false;
			return isExists;
		} 
		else {
			return branchExists;
		}
	}
	
	//method for insert or delete ZipcodeOfBranch
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertZipOfBranch(String branchId, ZipOfBranchSyncBean[] zipcode, AuditContext callerId){
		Boolean exist = this.checkBranchCode(branchId);
					
		if (!exist) {
			throw new EntityNotFoundException(this.messageSource.getMessage(
					entityMessage,
					new Object[] { branchId }, this.retrieveLocaleAudit(callerId)), branchId);
		}
			
		Object[][] params = {{ Restrictions.eq("branchCode", branchId) }};
		MsBranch objBranch = this.getManagerDAO().selectOne(MsBranch.class, params);
		
		if (zipcode.length > 0) {
			for (int i = 0; i < zipcode.length; i++) {
				ZipOfBranchSyncBean zipBranchBean = zipcode[i];
				if(zipBranchBean.getSubZipCode() == null){
					zipBranchBean.setSubZipCode("");
				}
				MsZipcodeofbranch objZip = this.getManagerDAO().selectOne(MsZipcodeofbranch.class, 
						new Object[][]{ {Restrictions.eq("msBranch.uuidBranch", objBranch.getUuidBranch())}, 
										{Restrictions.eq("zipCode", zipBranchBean.getZipCode())},
										{Restrictions.eq("subZipCode", zipBranchBean.getSubZipCode())}});
				
				if(objZip == null){
					MsZipcodeofbranch objZipCode = new MsZipcodeofbranch();
					objZipCode.setMsBranch(objBranch);
					objZipCode.setZipCode(zipBranchBean.getZipCode());
					objZipCode.setSubZipCode(zipBranchBean.getSubZipCode());
					objZipCode.setUsrCrt(callerId.getCallerId());
					objZipCode.setDtmCrt(new Date());
					this.getManagerDAO().insert(objZipCode);
					this.auditManager.auditAdd(objZipCode, auditInfoZipcodeOfBranch, callerId.getCallerId(), "");
				}
			}
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteZipOfBranch(String branchId, ZipOfBranchSyncBean[] zipcode, AuditContext callerId){
		Object[][] params = {{ Restrictions.eq("branchCode", branchId) }};
        MsBranch objBranch = this.getManagerDAO().selectOne(MsBranch.class, params);
		
		if (zipcode.length > 0) {
			for (int i = 0; i < zipcode.length; i++) {
				ZipOfBranchSyncBean zipBranchBean = zipcode[i];
				if(zipBranchBean.getSubZipCode() == null){
					zipBranchBean.setSubZipCode("");
				}
				MsZipcodeofbranch objZip = this.getManagerDAO().selectOne(MsZipcodeofbranch.class, 
						new Object[][]{ {Restrictions.eq("msBranch.uuidBranch", objBranch.getUuidBranch())}, 
										{Restrictions.eq("zipCode", zipBranchBean.getZipCode())},
										{Restrictions.eq("subZipCode", zipBranchBean.getSubZipCode())}});
				if(objZip == null){
					throw new EntityNotFoundException("Branch Code - Zipcode - SubZipCode : " 
								+ branchId+ " - " + zipBranchBean.getZipCode() + " - " 
								+ zipBranchBean.getSubZipCode() + " is not exist.", branchId+ " - " 
								+ zipBranchBean.getZipCode() + " - " + zipBranchBean.getSubZipCode());
				}
				else{
					this.getManagerDAO().delete(objZip);	
				}
			}
		}		

	}
	
	private String convertCurrency(String nominal) {
		String currency = StringUtils.EMPTY;
		DecimalFormat formatKurs = (DecimalFormat)DecimalFormat.getInstance(new Locale("en", "US"));
		formatKurs.applyPattern("###,##0");
		BigDecimal convert = new BigDecimal(StringUtils.isBlank(nominal)? "0": nominal);
		currency = formatKurs.format(convert);
		return currency;
	}
	
	@Override
	public String getLimitCohEnabled(String gsCode, AuditContext callerId) {
		String limitCohEnabled = "0";
		Object[][] params = {{ Restrictions.eq("gsCode", gsCode) } };
		AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if(result != null){
			limitCohEnabled = result.getGsValue();
		}		
		return limitCohEnabled;
	}
	
	@Override
	public String getUpDownEnabled(String gsCode, AuditContext callerId) {
		String UpDownEnabled = "0";
		Object[][] params = {{ Restrictions.eq("gsCode", gsCode) } };
		AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if(result != null){
			UpDownEnabled = result.getGsValue();
		}		
		return UpDownEnabled;
	}
	
	@Override
	public byte[] exportExcel(String subsystem, String flagEnabledCoh, AuditContext callerId) {
		
		HSSFWorkbook workbook = this.createXlsTemplate(subsystem, flagEnabledCoh);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	public HSSFWorkbook createXlsTemplate(String subsystem, String flagEnabledCoh) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Branch List");
			this.createHeader(workbook, sheet, subsystem, flagEnabledCoh);
			this.setTextDataFormat(workbook, sheet, subsystem, flagEnabledCoh);
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createHeader(HSSFWorkbook workbook, HSSFSheet sheet, String subsystem, String flagEnabledCoh) {
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);

		HSSFRow row = sheet.createRow(0);
		
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(flagEnabledCoh)){
			for (int i = 0; i < TEMPLATE_HEADER_MC.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MC[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MC[i]);
			}
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH[i]);
			}			
		}
	}

	private void setTextDataFormat(HSSFWorkbook workbook, HSSFSheet sheet, String subsystem, String flagEnabledCoh) {
		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("General"));
		
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(flagEnabledCoh) ){
			for (int i = 0; i < TEMPLATE_HEADER_MC.length; i++) {
				sheet.setDefaultColumnStyle(i, style);
			}
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
				sheet.setDefaultColumnStyle(i, style);
			}
		}
		
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public byte[] processSpreadSheetFile(String subsystem, String coh, File uploadedFile, AuditContext callerId) {
		byte[] errorUploadByte = null;

		List <BranchsBean> branchBeanError = new ArrayList<>();
		Map parseMap;
		int y = 0;
		try{
			parseMap = this.parseSpreadsheetToBranchBeans(subsystem, coh, uploadedFile,callerId, branchBeanError); 
		} catch (IOException e) {
			errorUploadByte = this.errorUpload();
			return errorUploadByte;
		}
			
		List listofBranch = (List) parseMap.get("result");
		List<String> listofZipCode = (List<String>) parseMap.get("resultZipCode");
		List listofErrorBranch = (List) parseMap.get("resultError");
		List<String> listofErrorZipCode = (List<String>) parseMap.get("resultErrorZipCode");
		List<String> listofErrorValueParent = (List<String>) parseMap.get("resultErrorValueParent");
		
		for (Iterator iterator = listofBranch.iterator(); iterator.hasNext(); ) {
			MsBranch msb = (MsBranch) iterator.next();
			String uuidExist = this.getBranchExist(msb);
			// check existing identical data
			if (StringUtils.isNotBlank(uuidExist)) {
				// update to active
				MsBranch dbModel = this.getManagerDAO().selectOne( MsBranch.class, Long.valueOf(uuidExist) );
				dbModel.setBranchName(msb.getBranchName());
				dbModel.setBranchAddress(msb.getBranchAddress());
				dbModel.setMsBranch(msb.getMsBranch());
				if(GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(coh)){
					dbModel.setCashLimitDefault(msb.getCashLimitDefault());
				}
				else{
					dbModel.setCashLimitDefault(new BigDecimal(0));
				}
				dbModel.setIsActive("1");
				dbModel.setUsrUpd(callerId.getCallerId());
				dbModel.setDtmUpd(new Date());
				
				updateBranch(dbModel, callerId); 
				updateZipCode(dbModel, listofZipCode.get(y), callerId);
				
				
			} 
			else if (StringUtils.isBlank(uuidExist)) {
				// insert new
					insertBranch(msb, callerId);
					if (!listofZipCode.isEmpty()){
						String[] arrayZipcode = listofZipCode.get(y).split(";");
						if(arrayZipcode.length>0){
							ZipOfBranchSyncBean[] beanZip  = new ZipOfBranchSyncBean[arrayZipcode.length];
								for (int i = 0; i < arrayZipcode.length; i++) {
									ZipOfBranchSyncBean bean = new ZipOfBranchSyncBean();
									bean.setZipCode(arrayZipcode[i]);
									beanZip[i] = bean;
								}
								insertZipOfBranch(msb.getBranchCode(), beanZip, callerId);
						}							
					}
			}
			y++;
		}
		
		if (!listofErrorBranch.isEmpty()) {
			try {
				errorUploadByte = exportErrorBranch(listofErrorBranch, listofErrorZipCode,
						listofBranch.size(), subsystem, coh, listofErrorValueParent, branchBeanError, callerId);
			} catch (SQLException e) {
				throw new BranchException(this.messageSource.getMessage(
						"businesslogic.error.generatexls",null, this.retrieveLocaleAudit(callerId)), Reason.ERROR_GENERATE);
			}
		}
		return errorUploadByte;
	}
	
	//error condition untuk di action
	private byte[] errorUpload(){
		byte[] tmp = new byte[1];
		tmp[0]=1;//for condition in action
		return tmp;
	}

	public String getBranchExist(MsBranch msb) {
		String branchCode = StringUtils.trimToNull(msb.getBranchCode());
		Object[] bcode = (branchCode == null) ? new Object[] { Restrictions.isNull("branchCode") }
				: new Object[] { Restrictions.eq("branchCode", branchCode) };
		Object[][] queryParams = {  bcode };	
		MsBranch result = (MsBranch) this.getManagerDAO().selectOne(MsBranch.class, queryParams);
		String uuid = "";
		if (result != null) {
			uuid = String.valueOf(result.getUuidBranch());
		}
		return uuid;
	}

	private Map parseSpreadsheetToBranchBeans(String subsystem, String coh, File uploadedFile, AuditContext callerId, List<BranchsBean> branchBeanError) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsBranch> result = new ArrayList<>();
		List<String> resultZipCode = new ArrayList<>();
		List<MsBranch> resultError = new ArrayList<>();
		List<String> resultErrorZipCode = new ArrayList<>();
		List<String> resultErrorValueParent = new ArrayList<>();

		
		FileInputStream inputStream = new FileInputStream(uploadedFile);

		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		HSSFSheet sheet = wb.getSheetAt(0);
		int rows = sheet.getPhysicalNumberOfRows();
		
		int status = checkingTemplate(uploadedFile, subsystem, coh);
		
		if (status == 1) {
			wb.close();
			throw new IOException();
		}		
				
		for (int r = 1; r < rows; r++) {
			HSSFRow row = sheet.getRow(r);
			if (row == null) {
				continue;
			}
			
    		boolean isEmptyRow = checkEmptyRow(row,subsystem,coh); 
    				
    		if (isEmptyRow == true){
    			continue;
    		}
    		
			MsBranch msb = new MsBranch();
			String zipcode = null;
			String ValueParent = null;
			msb.setDtmCrt(new Date());
			msb.setUsrCrt(callerId.getCallerId().toString());
			if (GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(coh)){			
				for (int c = 0; c < 6; c++) {
					HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
					// if intValue -1, then sequence is posted with string				
					String value = "";

					if (cell != null) {
						switch (cell.getCellType()) {
	    					case HSSFCell.CELL_TYPE_NUMERIC:
	    						value = String.valueOf((int) cell.getNumericCellValue());
	    						break;
	    					case HSSFCell.CELL_TYPE_STRING:
	    						value = cell.getStringCellValue();
	    						break;
	    						default:
						}
					}

					switch (c) {
	    				case 0:
	    					msb.setBranchCode(value);
	    					break;
	    				case 1:
	    					msb.setBranchName(value);
	    					break;
	    				case 2:
	    					msb.setBranchAddress(value);
	    					break;
	    				case 3:
	    					if ("".equals(value) || "-".equals(value) || "0".equals(value)) {
	    						Object[][] params = {{ Restrictions.eq("branchName", GlobalVal.BRANCH_HO) }};
		    					MsBranch parent = this.getManagerDAO().selectOne(MsBranch.class, params);
		    					msb.setMsBranch(parent);
		    					ValueParent = "";		    					
	    					} 
	    					else {
		    					Object[][] params = {{ Restrictions.eq("branchCode", value) }};
		    					MsBranch parent = this.getManagerDAO().selectOne(MsBranch.class, params);
		    					if(parent!=null){
		    						msb.setMsBranch(parent);
		    					} 
		    					else if(result!=null) {
		    						for(MsBranch branchParent : result){
		    							if(value.equals(branchParent.getBranchCode())){
		    								msb.setMsBranch(branchParent);
		    							} else {
		    								ValueParent = value;
		    							}
		    						}
		    					} else {
		    						ValueParent = value;
		    					}
	    					}
	    					break;
	    				case 4:
	    					zipcode = value;
	    					break;
	    				case 5:
	    					if (!"".equalsIgnoreCase(value)) {
		    					msb.setCashLimitDefault(new BigDecimal(value));	    						
	    					} 
	    					else {
	    						msb.setCashLimitDefault(new BigDecimal(0));
	    					}
	    					break;
					}
					wb.close();
				}
			} 
			else {
				
				for (int c = 0; c < 5; c++) {
					HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
					// if intValue -1, then sequence is posted with string				
					String value = "";
					if (cell != null) {
						switch (cell.getCellType()) {
	    					case HSSFCell.CELL_TYPE_NUMERIC:
	    						value = String.valueOf((int) cell.getNumericCellValue());
	    						break;
	    
	    					case HSSFCell.CELL_TYPE_STRING:
	    						value = cell.getStringCellValue();
	    						break;
	    
	    					default:
						}
					}

					switch (c) {
	    				case 0:
	    					msb.setBranchCode(value);
	    					break;
	    				case 1:
	    					msb.setBranchName(value);
	    					break;
	    				case 2:
	    					msb.setBranchAddress(value);
	    					break;
	    				case 3:
	    					if ("".equals(value) || "-".equals(value) || "0".equals(value)) {
		    					Object[][] params = {{ Restrictions.eq("branchName", GlobalVal.BRANCH_HO) }};
		    					MsBranch parent = this.getManagerDAO().selectOne(MsBranch.class, params);
		    					msb.setMsBranch(parent);
		    					ValueParent = "";
	    					} 
	    					else {
		    					Object[][] params = {{ Restrictions.eq("branchCode", value) }};
		    					MsBranch parent = this.getManagerDAO().selectOne(MsBranch.class, params);
		    					if(parent!=null){
		    						msb.setMsBranch(parent);
		    					} else if(result!=null) {
		    						for(MsBranch branchParent : result){
		    							if(value.equals(branchParent.getBranchCode())){
		    								msb.setMsBranch(branchParent);
		    							} else {
		    								ValueParent = value;
		    							}
		    						}
		    					} else {
		    						ValueParent = value;
		    					}
	    					}
	    					break;
	    				case 4:
	    					zipcode = value;
	    					break;
					}
					wb.close();
				}
			}
			
			StringBuilder errorText = checkingBranchUpload(msb,zipcode,subsystem,coh, callerId);
			if (errorText.length() == 0) {
				result.add(msb);
				resultZipCode.add(zipcode);
			} 
			else {
				BranchsBean branchBean = new BranchsBean();
				resultError.add(msb);
				resultErrorZipCode.add(zipcode);
				resultErrorValueParent.add(ValueParent);
				branchBean.setErrorText(errorText.toString());
				branchBeanError.add(branchBean);
			}
		}
		
		paramParse.put("result", result);
		paramParse.put("resultZipCode", resultZipCode);
		paramParse.put("resultError", resultError);
		paramParse.put("resultErrorZipCode", resultErrorZipCode);
		paramParse.put("resultErrorValueParent", resultErrorValueParent);
		return paramParse;

	}


	private boolean checkEmptyRow(HSSFRow row, String subsystem, String coh) {
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(coh)){
			String[] isEmptyCell = new String [6]  ;
			Arrays.fill(isEmptyCell, "");

			for (int c = 0; c < 6; c++) {
				HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
				if (cell == null){
					isEmptyCell[c]="empty";
				}
			}
			
			if ("empty".equals(isEmptyCell[0]) && "empty".equals(isEmptyCell[1]) 
					&& "empty".equals(isEmptyCell[2])&& "empty".equals(isEmptyCell[3])
					&& "empty".equals(isEmptyCell[4])&& "empty".equals(isEmptyCell[5])){
				return true;
			}
			else{
				return false;
			}			
		} 
		else {
			String[] isEmptyCell = new String [5];
			Arrays.fill(isEmptyCell, "");

			for (int c = 0; c < 5; c++) {
				HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
				if (cell == null){
					isEmptyCell[c]="empty";
				}
			}
			
			if ("empty".equals(isEmptyCell[0]) && "empty".equals(isEmptyCell[1]) 
					&& "empty".equals(isEmptyCell[2])&& "empty".equals(isEmptyCell[3])
					&& "empty".equals(isEmptyCell[4])){
				return true;
			}
			else{
				return false;
			}			
		}

	}

	
	public int checkingTemplate(File uploadedFile, String subsystem, String coh) throws IOException {
		int count = 0;
		
		FileInputStream inputStream = new FileInputStream(uploadedFile);

		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		HSSFSheet sheet = wb.getSheetAt(0);
		HSSFRow header = sheet.getRow(0);
		
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(coh)) {
			if (header != null) {
				for (int c = 0; c < 6; c++) {
					HSSFCell cell = header.getCell(c, Row.RETURN_BLANK_AS_NULL);
					String value = "";
					value = cell.getStringCellValue();
					
					if ((value).equals(TEMPLATE_HEADER_MC[c])){
						count = 0;
					} 
					else {
						count = 1;
						wb.close();
						break;
					}
				}			
			}
		} 
		else {
			if (header != null) {
				for (int c = 0; c < 5; c++) {
					HSSFCell cell = header.getCell(c, Row.RETURN_BLANK_AS_NULL);
					String value = "";
					value = cell.getStringCellValue();
					
					if ((value).equals(TEMPLATE_HEADER_MC[c])){
						count = 0;
					} 
					else {
						count = 1;
						wb.close();
						break;
					}					
				}			
			}
		}
		
		return count;
	}
	
	public StringBuilder checkingBranchUpload(MsBranch msb, String zipcode, String subsystem, String coh, AuditContext callerId) {
	    StringBuilder errorUpload = new StringBuilder();
	    //ditambahkan penjagaan lenght dan format number pada zipcode
	    //Branch Code
		if (msb.getBranchCode() == null || ("").equals(msb.getBranchCode())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"Branch Code"}, this.retrieveLocaleAudit(callerId)));
		} 
		else if (msb.getBranchCode().length() > 20) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", 
					new Object[]{"Branch Code","20"}, this.retrieveLocaleAudit(callerId)));
		}
		//Branch Name
		if (msb.getBranchName() == null || ("").equals(msb.getBranchName())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"Branch Name"}, this.retrieveLocaleAudit(callerId)));
		} 
		else if (msb.getBranchName().length() > 80){
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", 
					new Object[]{"Branch Name","80"}, this.retrieveLocaleAudit(callerId)));
		}	
		//Branch Parent
		if (msb.getMsBranch() == null) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.notexist", 
					new Object[]{"Branch Parent Code"}, this.retrieveLocaleAudit(callerId)));					
		}
		//Branch Address
		if (msb.getBranchAddress() == null || ("").equals(msb.getBranchAddress())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"Branch Address"}, this.retrieveLocaleAudit(callerId)));
		} 
		else if (msb.getBranchAddress().length() > 200){
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", 
					new Object[]{"Branch Name","200"}, this.retrieveLocaleAudit(callerId)));
		}
		//Limit COH
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(coh)){
			if (msb.getCashLimitDefault() == null || ("").equals(msb.getCashLimitDefault())){
				if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");											
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
						new Object[]{"Limit COH"}, this.retrieveLocaleAudit(callerId)));
			} 
			else {
				if (Pattern.matches("[0-9]+", String.valueOf(msb.getCashLimitDefault())) == false ) {
					errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.numeric", 
							new Object[]{"Limit COH"}, this.retrieveLocaleAudit(callerId)));
				}
			}
		}
		//ZipCode
		if (!"".equals(zipcode)){
			if (Pattern.matches("[0-9;]+", zipcode) == false ) {
				if(!("").equals(errorUpload.toString())){
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.numeric", 
						null, this.retrieveLocaleAudit(callerId)));
			} else {
				String[] zCode = zipcode.split(";");
				for(int i=0; i<zCode.length; i++){
					if(zCode[i].trim().length()!= 5){
						if(!("").equals(errorUpload.toString())){
							errorUpload.append(" | ");
						}
						errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.lengthzipcode", 
								null, Locale.ENGLISH));
						}
				}
			}
		}
				
		return errorUpload;
	}

	private byte[] exportErrorBranch(List listBranch, List<String> listZipCode, int branchSuccess, 
			String subsystem, String coh, List<String> valueParent, List<BranchsBean> branchBeanError, AuditContext callerId)
			throws SQLException {

		HSSFWorkbook workbook = this.createXlsTemplateErrorBranch(listBranch, listZipCode,
				branchSuccess, subsystem, coh, valueParent, branchBeanError, callerId);

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} catch (IOException e) {
            LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private HSSFWorkbook createXlsTemplateErrorBranch(List listBranch, List<String> listZipCode, 
			int branchSuccess, String subsystem, String coh, List<String> valueParent, List<BranchsBean> branchBeanError, AuditContext callerId) {

		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Branch List");
			this.createHeaderErrorUpload(workbook, sheet, branchSuccess,
					listBranch.size(), subsystem, coh, callerId);
			this.setTextDataFormatErrorUpload(workbook, sheet, subsystem, coh);
			this.setDataErrorUpload(workbook, sheet, listBranch, listZipCode, subsystem, coh, valueParent, branchBeanError);
		} catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}

	private void setDataErrorUpload(HSSFWorkbook workbook, HSSFSheet sheet, List listBranch, List<String> listZipCode,
			String subsystem, String coh, List<String> valueParent, List<BranchsBean> branchBeanError) {
		List branchList = listBranch;
		List<String> zipcodeList = listZipCode;
		List<String> parentsValue = valueParent; 
		
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(coh)){
			int j = 5;
			int idx = 0;
			Iterator iterator = branchList.iterator();

			while (iterator.hasNext()) {
				HSSFRow row = sheet.createRow(j);
				MsBranch msb = (MsBranch) iterator.next();
				String mzc = zipcodeList.get(idx);
				String valueparent = parentsValue.get(idx);

				HSSFCell cell = row.createCell(0);
				cell.setCellValue(msb.getBranchCode());
				sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH_MC_ERROR[0]);

				HSSFCell cell1 = row.createCell(1);
				cell1.setCellValue(msb.getBranchName());
				sheet.setColumnWidth(1, HEADER_COLUMN_WIDTH_MC_ERROR[1]);

				HSSFCell cell2 = row.createCell(2);
				cell2.setCellValue(msb.getBranchAddress());
				sheet.setColumnWidth(2, HEADER_COLUMN_WIDTH_MC_ERROR[2]);
				
				HSSFCell cell3 = row.createCell(3);
				if (msb.getMsBranch() == null) {
					cell3.setCellValue(valueparent);					
				} 
				else {
					cell3.setCellValue(msb.getMsBranch().getBranchCode().toString());
				}
				sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH_MC_ERROR[3]);
				
				HSSFCell cell4 = row.createCell(4);
				cell4.setCellValue(mzc);
				sheet.setColumnWidth(4, HEADER_COLUMN_WIDTH_MC_ERROR[4]);
				
				HSSFCell cell5 = row.createCell(5);
				cell5.setCellValue(msb.getCashLimitDefault().doubleValue());
				sheet.setColumnWidth(5, HEADER_COLUMN_WIDTH_MC_ERROR[5]);
				
				HSSFCell cell6 = row.createCell(6);
				cell6.setCellValue(branchBeanError.get(idx).getErrorText().toString());
				sheet.setColumnWidth(6, HEADER_COLUMN_WIDTH_MC_ERROR[6]);

				j++;
				idx++;
			}
			
		} 
		else {
			int j = 5;
			int idx = 0;
			Iterator iterator = branchList.iterator();

			while (iterator.hasNext()) {
				HSSFRow row = sheet.createRow(j);
				MsBranch msb = (MsBranch) iterator.next();
				String mzc = zipcodeList.get(idx);
				String valueparent = parentsValue.get(idx);

				HSSFCell cell = row.createCell(0);
				cell.setCellValue(msb.getBranchCode());
				sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH_ERROR[0]);

				HSSFCell cell1 = row.createCell(1);
				cell1.setCellValue(msb.getBranchName());
				sheet.setColumnWidth(1, HEADER_COLUMN_WIDTH_ERROR[1]);

				HSSFCell cell2 = row.createCell(2);
				cell2.setCellValue(msb.getBranchAddress());
				sheet.setColumnWidth(2, HEADER_COLUMN_WIDTH_ERROR[2]);
				
				HSSFCell cell3 = row.createCell(3);
				if (msb.getMsBranch() == null) {
					cell3.setCellValue(valueparent);					
				} 
				else {
					cell3.setCellValue(msb.getMsBranch().getBranchCode());
				}
				sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH_ERROR[3]);
				
				HSSFCell cell4 = row.createCell(4);
				cell4.setCellValue(mzc);
				sheet.setColumnWidth(4, HEADER_COLUMN_WIDTH_ERROR[4]);
				
				HSSFCell cell5 = row.createCell(5);
				cell5.setCellValue(branchBeanError.get(idx).getErrorText().toString());
				sheet.setColumnWidth(5, HEADER_COLUMN_WIDTH_ERROR[5]);

				j++;
				idx++;
		}
	}
	}

	private void setTextDataFormatErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet, String subsystem, String coh) {

		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("@"));
		
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(coh)){
			for (int i = 0; i < TEMPLATE_HEADER_MC_ERROR.length; i++) {
				sheet.setDefaultColumnStyle(i, style);
			}			
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER_ERROR.length; i++) {
				sheet.setDefaultColumnStyle(i, style);
			}
		}

	}

	private void createHeaderErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet, int branchSuccess, int branchError, String subsystem, String coh, AuditContext callerId) {

		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);

		HSSFRow row0 = sheet.createRow(0);
		HSSFRow row1 = sheet.createRow(1);
		HSSFRow row2 = sheet.createRow(2);

		HSSFCell cell0 = row0.createCell(0);
		
		cell0.setCellValue(this.messageSource.getMessage("businesslogic.uploadtask.tottaskupload", new Object[]{"BRANCH",branchSuccess}, this.retrieveLocaleAudit(callerId))) ;
		cell0.setCellStyle(style);

		HSSFCell cell1 = row1.createCell(0);
		cell1.setCellValue(this.messageSource.getMessage("businesslogic.uploadtask.tottaskerr", new Object[]{"BRANCH",branchError}, this.retrieveLocaleAudit(callerId)));
		cell1.setCellStyle(style);
	
		HSSFCell cell2 = row2.createCell(0);
		cell2.setCellValue(this.messageSource.getMessage("businesslogic.uploadtask.errupload", new Object[]{"BRANCH"}, this.retrieveLocaleAudit(callerId)));
		cell2.setCellStyle(style);

		HSSFRow row4 = sheet.createRow(4);
		
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystem) && "1".equals(coh)){
			for (int i = 0; i < TEMPLATE_HEADER_MC_ERROR.length; i++) {
				HSSFCell cell = row4.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MC_ERROR[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MC_ERROR[i]);
			}			
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER_ERROR.length; i++) {
				HSSFCell cell = row4.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_ERROR[i]);
				cell.setCellStyle(style);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_ERROR[i]);
			}			
		}
	}	
	
	public void insertAreaOfUser(String uuidArea, AmMsuser amMsuser,
			AuditContext callerId) {
		MsAreaofuser dbModel = new MsAreaofuser();
		dbModel.setUsrCrt(callerId.getCallerId());
		dbModel.setDtmCrt(new Date());
		dbModel.setMsArea(new MsArea());
		dbModel.getMsArea().setUuidArea(Long.valueOf(uuidArea));
		dbModel.setAmMsuser(amMsuser);
		this.getManagerDAO().insert(dbModel);
		this.auditManager.auditAdd(dbModel, auditInfoAreaOfUser, callerId.getCallerId(), "");

	}
}
