package com.adins.mss.businesslogic.impl.common;

import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.bouncycastle.util.encoders.Base64;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CmsLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsDealer;
import com.adins.mss.model.MsPromoofbranch;
import com.adins.mss.model.MsPromoofdealer;
import com.adins.mss.model.MsStkpromo;
import com.adins.mss.model.MsStkpromocontent;
import com.adins.mss.model.custom.CmsBean;
import com.adins.mss.model.custom.CmsBranchBean;
import com.adins.mss.model.custom.CmsContentBean;
import com.adins.mss.model.custom.CmsDealerBean;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericCmsLogic extends BaseLogic implements CmsLogic, MessageSourceAware{
	private AuditInfo auditInfoStk;
	private AuditInfo auditInfoCtn;
	private AuditInfo auditInfoPromoOfDealer;
	private AuditInfo auditInfoPromoOfBranch;
	
	public GenericCmsLogic() {
		String[] pkColsStk = { "uuidStkPromo" };
		String[] pkDbColsStk = { "UUID_STK_PROMO" };
		String[] colsStk = { "uuidStkPromo", "promoName", "promoShortDesc", "starteffDate", "endeffDate", "msStkpromo", "isBranch" };
		String[] dbColsStk = { "UUID_STK_PROMO", "PROMO_NAME", "PROMO_SHORT_DESC", "STARTEFF_DATE", "ENDEFF_DATE", "PARENT_PROMO_ID", "IS_BRANCH" };
		this.auditInfoStk = new AuditInfo("MS_STKPROMO", pkColsStk,
				pkDbColsStk, colsStk, dbColsStk);

		String[] pkColsCtn = { "uuidStkPromoContent" };
		String[] pkDbColsCtn = { "UUID_STK_PROMO_CONTENT" };
		String[] colsCtn = { "uuidStkPromoContent", "content", "contentType", "sequence", "starteffDate",
				"endeffDate", "msStkpromo.uuidStkPromo" };
		String[] dbColsCtn = { "UUID_STK_PROMO_CONTENT", "CONTENT", "CONTENT_TYPE", "SEQUENCE",
				"STARTEFF_DATE", "ENDEFF_DATE", "UUID_STK_PROMO" };
		this.auditInfoCtn = new AuditInfo("MS_STKPROMOCONTENT", pkColsCtn,
				pkDbColsCtn, colsCtn, dbColsCtn);

		String[] pkColsPoD = { "uuidPromoOfDealer" };
		String[] pkDbColsPoD = { "UUID_PROMO_OF_DEALER" };
		String[] colsPoD = { "uuidPromoOfDealer", "msDealer.uuidDealer", "msStkpromo.uuidStkPromo" };
		String[] dbColsPoD = { "UUID_PROMO_OF_DEALER", "UUID_DEALER", "UUID_STK_PROMO" };
		this.auditInfoPromoOfDealer = new AuditInfo("MS_PROMOOFDEALER", pkColsPoD, pkDbColsPoD, colsPoD, dbColsPoD);

		String[] pkColsPoBr = { "uuidPromoOfBranch" };
		String[] pkDbColsPoBr = { "UUID_PROMO_OF_BRANCH" };
		String[] colsPoBr = { "uuidPromoOfBranch", "msBranch.uuidBranch", "msStkpromo.uuidStkPromo" };
		String[] dbColsPoBr = { "UUID_PROMO_OF_BRANCH", "UUID_BRANCH", "UUID_STK_PROMO" };
		this.auditInfoPromoOfBranch = new AuditInfo("MS_PROMOOFBRANCH", pkColsPoBr, pkDbColsPoBr, colsPoBr, dbColsPoBr);
	}
	
	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    
	@Override
	public List<CmsBean> listStock(int targetPageNo, int pageSize, AuditContext callerId) 
			throws ParseException, UnsupportedEncodingException {
		Object[][] params = { { "start", (targetPageNo-1)*pageSize+1 }, 
				{ "end", targetPageNo*pageSize } };		
		List list = this.getManagerDAO().selectAllNative("setting.cms.listStok", params, null);
		
		List<CmsBean> promoList = new ArrayList<CmsBean>();
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        for(int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);
			
			List<CmsContentBean> contentBeanList = new ArrayList<CmsContentBean>();
			List<MsStkpromocontent> contentList = this.listStkpromocontent(temp.get("d0").toString(), callerId);
			
			CmsBean bean = new CmsBean();
			if(contentList == null){
				contentList = new ArrayList<MsStkpromocontent>();
			}
			else {
				for( int x = 0; x < contentList.size(); x++ ){
					CmsContentBean cBean = new CmsContentBean();
					cBean.setUuidStkPromoContent(contentList.get(x).getUuidStkPromoContent());
					cBean.setContentType(contentList.get(x).getContentType());
					cBean.setSequence(contentList.get(x).getSequence());
					cBean.setImgBase64(new String(Base64.encode(contentList.get(x).getContent()),"ASCII"));
					cBean.setMsStkpromo(contentList.get(x).getMsStkpromo());
					contentBeanList.add(cBean);
				}
			}	
			
			if(temp.get("d4") != null) {
				MsStkpromo parent = new MsStkpromo();
				parent.setUuidStkPromo(Long.valueOf(temp.get("d4").toString()));
				parent.setPromoName(temp.get("d5").toString());
				bean.setMsStkpromo(parent);
			}
			else{
				bean.setMsStkpromo(null);
			}
			
			bean.setUuidStkPromo(Long.valueOf(temp.get("d0").toString()));
			bean.setPromoName(temp.get("d1").toString());
			bean.setPromoShortDesc(temp.get("d2").toString());
			bean.setStartDate(DateFormatUtils.format(df.parse(temp.get("d3").toString()), "yyyy-MM-dd"));
			bean.setEndDate(DateFormatUtils.format(df.parse(temp.get("d8").toString()), "yyyy-MM-dd"));
			bean.setLevel(temp.get("d6").toString());
			bean.setHasChild(this.hasChild(temp.get("d0").toString()));
			bean.setHasContent(contentList.isEmpty() ? false : true);
			bean.setMsStkpromocontent(contentBeanList);
			bean.setIsBranch(temp.get("d9").toString());
			promoList.add(bean);				
		}
		
		return promoList;
	}

	@Override
	public List<CmsBean> listStockByDealer(int targetPageNo, int pageSize, AuditContext callerId) 
			throws ParseException, UnsupportedEncodingException {
		Object[][] params = { { "start", (targetPageNo-1)*pageSize+1 }, 
				{ "end", targetPageNo*pageSize } };		
		List list = this.getManagerDAO().selectAllNative("setting.cms.listStokByDealer", params, null);
		
		List<CmsBean> promoList = new ArrayList<CmsBean>();
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        for(int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);
			
			List<CmsContentBean> contentBeanList = new ArrayList<CmsContentBean>();
			List<MsStkpromocontent> contentList = this.listStkpromocontent(temp.get("d0").toString(), callerId);
			
			CmsBean bean = new CmsBean();
			if(contentList == null){ 
				contentList = new ArrayList<MsStkpromocontent>();
			}
			else {
				for( int x = 0; x < contentList.size(); x++ ){
					CmsContentBean cBean = new CmsContentBean();
					cBean.setUuidStkPromoContent(contentList.get(x).getUuidStkPromoContent());
					cBean.setContentType(contentList.get(x).getContentType());
					cBean.setSequence(contentList.get(x).getSequence());
					cBean.setImgBase64(new String(Base64.encode(contentList.get(x).getContent()),"ASCII"));
					cBean.setMsStkpromo(contentList.get(x).getMsStkpromo());
					contentBeanList.add(cBean);
				}
			}	
			
			if(temp.get("d4") != null) {
				MsStkpromo parent = new MsStkpromo();
				parent.setUuidStkPromo(Long.valueOf(temp.get("d4").toString()));
				parent.setPromoName(temp.get("d5").toString());
				bean.setMsStkpromo(parent);
			}
			else{
				bean.setMsStkpromo(null);
			}
			
			bean.setUuidStkPromo(Long.valueOf(temp.get("d0").toString()));
			bean.setPromoName(temp.get("d1").toString());
			bean.setPromoShortDesc(temp.get("d2").toString());
			bean.setStartDate(DateFormatUtils.format(df.parse(temp.get("d3").toString()), "yyyy-MM-dd"));
			bean.setEndDate(DateFormatUtils.format(df.parse(temp.get("d8").toString()), "yyyy-MM-dd"));
			bean.setLevel(temp.get("d6").toString());
			bean.setHasChild(this.hasChild(temp.get("d0").toString()));
			bean.setHasContent(contentList.isEmpty() ? false : true);
			bean.setMsStkpromocontent(contentBeanList);
			bean.setIsBranch(temp.get("d9").toString());
			promoList.add(bean);				
		}
        
		return promoList;
	}
	
	private boolean hasChild(String uuid) {
		Object[][] params = { { Restrictions.eq("msStkpromo.uuidStkPromo", Long.valueOf(uuid)) } };
		Map<String, Object> result = this.getManagerDAO().count(MsStkpromo.class, params);
		if((Long)result.get(GlobalKey.MAP_RESULT_SIZE) == 0){
			return false;
		}
		else {
			return true;
		}
	}
	
	@Override
	public Integer countListStok(Object params, AuditContext callerId) {
		Integer result = (Integer)this.getManagerDAO().selectOneNative("setting.cms.cntStok", params);	
		return result;
	}
	
	private List<MsStkpromocontent> listStkpromocontent(String uuidStkPromo, AuditContext callerId) {
		Object[][] params = { { Restrictions.eq("msStkpromo.uuidStkPromo", Long.valueOf(uuidStkPromo)) } };
		List<MsStkpromocontent> listResult = Collections.EMPTY_LIST;
		
		Map<String, Object> result = this.getManagerDAO().list(MsStkpromocontent.class, params, null);
		listResult = (List) result.get(GlobalKey.MAP_RESULT_LIST);
		
		return listResult;
	}
	
	private boolean validateDelPromo(String uuidStkpromo){
		Object[][] params = { { Restrictions.eq("msStkpromo.uuidStkPromo", Long.valueOf(uuidStkpromo)) } };
		Map<String, Object> checkBranch = this.getManagerDAO().count(MsPromoofbranch.class, params);
		if ((Long)checkBranch.get(GlobalKey.MAP_RESULT_SIZE) != 0) {
			String[][] param = { { "uuidStkPromo", uuidStkpromo } };
			List result = this.getManagerDAO().selectAllNative("setting.cms.valDelPromo", param, null);
			if (!result.isEmpty()){
				return false;
			}
			else{
				return true;
			}
		}
		else{
			return true;
		}
	}
	
	private boolean validateDelPromoByDealer(String uuidStkpromo){
		Object[][] params = { { Restrictions.eq("msStkpromo.uuidStkPromo", Long.valueOf(uuidStkpromo)) } };
		Map<String, Object> checkDealer = this.getManagerDAO().count(MsPromoofdealer.class, params);
		if ((Long)checkDealer.get(GlobalKey.MAP_RESULT_SIZE) != 0) {
			String[][] param = { { "uuidStkPromo", uuidStkpromo } };
			List result = this.getManagerDAO().selectAllNative("setting.cms.valDelPromoByDealer", param, null);
			if (!result.isEmpty()){
				return false;
			}
			else{
				return true;
			}
		}
		else{
			return true;
		}
	}
	
	private void delBranchParent(String uuidStkPromo) {
		String[][] params = { { "uuidStkPromo", uuidStkPromo } };
		this.getManagerDAO().deleteNativeString("delete from MS_PROMOOFBRANCH where UUID_STK_PROMO = :uuidStkPromo", params);
	}
	
	private void delDealerParent(String uuidStkPromo) {
		String[][] params = { { "uuidStkPromo", uuidStkPromo } };
		this.getManagerDAO().deleteNativeString("delete from MS_PROMOOFDEALER where UUID_STK_PROMO = :uuidStkPromo", params);
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteStkpromo(String uuid, AuditContext callerId) {
		if (!this.hasChild(uuid)){
			if (this.validateDelPromo(uuid)) {
				this.delBranchParent(uuid);
				
				MsStkpromo obj = this.getManagerDAO().selectOne(MsStkpromo.class, Long.valueOf(uuid));;
				//delete content
				String[][] params = { {"uuidStkpromo", uuid} };
				this.getManagerDAO().deleteNative("setting.cms.deleteContentPromo", params);
				//end delete content
				this.auditManager.auditDelete(obj, auditInfoStk, callerId.getCallerId(), "");
				this.getManagerDAO().delete(obj);
			}
			else{
				throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION, 
						this.messageSource.getMessage(
								"businesslogic.error.haschild", null,
								this.retrieveLocaleAudit(callerId)), new Exception());
			}
		}
		else{
			throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION, 
					this.messageSource.getMessage(
							"businesslogic.error.haschild", null,
							this.retrieveLocaleAudit(callerId)), new Exception());
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteStkpromoByDealer(String uuid, AuditContext callerId) {
		if (!this.hasChild(uuid)){
			if (this.validateDelPromoByDealer(uuid)) {
				this.delDealerParent(uuid);
				
				MsStkpromo obj = this.getManagerDAO().selectOne(MsStkpromo.class, Long.valueOf(uuid));
				//delete content
				String[][] params = { {"uuidStkpromo", uuid} };
				this.getManagerDAO().deleteNative("setting.cms.deleteContentPromo", params);
				//end delete content
				this.auditManager.auditDelete(obj, auditInfoStk, callerId.getCallerId(), "");
				this.getManagerDAO().delete(obj);
			}
			else{
				throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION, 
						this.messageSource.getMessage(
								"businesslogic.error.haschild", null,
								this.retrieveLocaleAudit(callerId)), new Exception());
			}
		}
		else{
			throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION, 
					this.messageSource.getMessage(
							"businesslogic.error.haschild", null,
							this.retrieveLocaleAudit(callerId)), new Exception());
		}
	}
	
	@Override
	public MsStkpromo getStkpromo(String uuid, AuditContext callerId) {
		MsStkpromo result = this.getManagerDAO().selectOne(MsStkpromo.class, Long.valueOf(uuid));
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertStkpromo(MsStkpromo stkPromo, String uuidParent, String[] uuidBranches,
			AuditContext callerId) {
		if (uuidParent != null && !"".equals(uuidParent)) {
			MsStkpromo parent = this.getManagerDAO().selectOne(
					MsStkpromo.class, Long.valueOf(uuidParent));
			stkPromo.setMsStkpromo(parent);
		} else{
			stkPromo.setMsStkpromo(null);
		}
		stkPromo.setIsActive("1");
		stkPromo.setDtmCrt(new Date());
		stkPromo.setUsrCrt(callerId.getCallerId());
		stkPromo.setIsBranch("1");
		
		this.getManagerDAO().insert(stkPromo);
		this.auditManager.auditAdd(stkPromo, auditInfoStk, callerId.getCallerId(), "");
		
		if(uuidBranches != null){
			this.insertPromoOfBranch(stkPromo, uuidBranches, callerId);
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateStkpromo(MsStkpromo stkPromo, String[] uuidBranches, 
			AuditContext callerId) {			
		MsStkpromo dbModel = this.getManagerDAO().selectOne(MsStkpromo.class, stkPromo.getUuidStkPromo());
		
		dbModel.setPromoName(stkPromo.getPromoName());
		dbModel.setPromoShortDesc(stkPromo.getPromoShortDesc());
		dbModel.setStarteffDate(stkPromo.getStarteffDate());
		dbModel.setEndeffDate(stkPromo.getEndeffDate());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());
		dbModel.setIsBranch("1");
		this.auditManager.auditEdit(dbModel, auditInfoStk, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
		
		Object[][] paramsStkPromo = {{"uuidStkPromo", stkPromo.getUuidStkPromo()}};
		
		List<Map<String, Object>> childPromoList = this.getManagerDAO().selectAllNative("setting.cms.getChildPromo", paramsStkPromo, null);
		List updateStart = new ArrayList<>();
		List updateEnd = new ArrayList<>();
		
		for(int i = 0; i < childPromoList.size(); i++) {
			Map<String, Object> bean = (Map<String, Object>) childPromoList.get(i);
			if(!(((Date) bean.get("d1")).after(stkPromo.getStarteffDate()) && ((Date) bean.get("d1")).before(stkPromo.getEndeffDate()))) {
				updateStart.add((BigInteger) bean.get("d0"));
			}
			if(!(((Date) bean.get("d2")).after(stkPromo.getStarteffDate()) && ((Date) bean.get("d2")).before(stkPromo.getEndeffDate()))) {
				updateEnd.add((BigInteger) bean.get("d0"));
			}
		}
		
		if(!updateStart.isEmpty()) {
			String[] uuidUpdate = new String[updateStart.size()];
			for (int j = 0; j < updateStart.size(); j++) {
				uuidUpdate[j] = (String) updateStart.get(j).toString();
			}
			Object[][] paramStart = {
				{"uuidStkpromo", Arrays.asList(uuidUpdate)},
				{"startEffDate", DateFormatUtils.format(stkPromo.getStarteffDate(), "yyyy-MM-dd")+" 00:00:00.000"}
			};
			this.getManagerDAO().updateNative("setting.cms.updateStart", paramStart);
		}
		if(!updateEnd.isEmpty()) {
			String[] uuidUpdate = new String[updateEnd.size()];
			for (int j = 0; j < updateEnd.size(); j++) {
				uuidUpdate[j] = (String) updateEnd.get(j);
			}
			Object[][] paramEnd = {
				{"uuidStkpromo", Arrays.asList(uuidUpdate)},
				{"endEffDate", DateFormatUtils.format(stkPromo.getEndeffDate(), "yyyy-MM-dd")+" 23:59:59.997"}
			};
			this.getManagerDAO().updateNative("setting.cms.updateEnd", paramEnd);
		}	
		
		if(uuidBranches != null && uuidBranches.length > 0){
			this.insertPromoOfBranch(stkPromo, uuidBranches, callerId);
		}		
	}

	private void insertPromoOfBranch(MsStkpromo stkPromo, String[] uuidBranches,
			AuditContext callerId){		
		for(int i = 0; i < uuidBranches.length; i++) {
			if(!uuidBranches[i].isEmpty()) {
				MsBranch msBranch = new MsBranch();
				msBranch.setUuidBranch(Long.valueOf(uuidBranches[i]));
				
				MsPromoofbranch msPromoOfBranch = new MsPromoofbranch();
				
				msPromoOfBranch.setMsBranch(msBranch);
				msPromoOfBranch.setMsStkpromo(stkPromo);
				msPromoOfBranch.setUsrCrt(callerId.getCallerId());
				msPromoOfBranch.setDtmCrt(new Date());
				this.getManagerDAO().insert(msPromoOfBranch);
				this.auditManager.auditAdd(msPromoOfBranch, auditInfoPromoOfBranch, callerId.getCallerId(), "");
			}
		}
	}
	
	private void insertPromoOfDealer(MsStkpromo stkPromo, String[] uuidDealers,
			AuditContext callerId){		
		for(int i = 0; i < uuidDealers.length; i++) {
			if(!uuidDealers[i].isEmpty()) {
				MsDealer msDealer = new MsDealer();
				msDealer.setUuidDealer(Long.valueOf(uuidDealers[i]));
				
				MsPromoofdealer msPromoOfDealer = new MsPromoofdealer();
				
				msPromoOfDealer.setMsDealer(msDealer);
				msPromoOfDealer.setMsStkpromo(stkPromo);
				msPromoOfDealer.setUsrCrt(callerId.getCallerId());
				msPromoOfDealer.setDtmCrt(new Date());
				this.getManagerDAO().insert(msPromoOfDealer);
				this.auditManager.auditAdd(msPromoOfDealer, auditInfoPromoOfDealer, callerId.getCallerId(), "");
			}
		}
	}

	@Override
	public Integer countListPromoOfBranch(String uuid, String uuidParent, String[] uuidBranches,
			String branchName, String branchCode, AuditContext callerId) {
		if(uuidBranches == null){
			uuidBranches = new String[]{"-"};
		}
		
		Object[][] params = { { "uuidStkpromo", uuid == null ? "%" : uuid },				
				{ "branchCode", branchCode == null ? "%" : branchCode },
				{ "branchName", branchName == null ? "%" : branchName }, 
				{ "uuidParent", uuidParent == null ? "%" : uuidParent }, 
				{ "uuidSelectedBranch", Arrays.asList(uuidBranches) } };
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilderBranch((Object[][]) params, paramsStack);
		
		StringBuilder queryBuilder = new StringBuilder()		
		.append(" SELECT COUNT(1)")
		.append(" FROM (")
		.append(" SELECT DISTINCT MB.UUID_BRANCH, MB.BRANCH_CODE, MB.BRANCH_NAME,")
		.append(" (case when MPB.UUID_BRANCH IS null then 'false' else 'true' end) as FLAG")
		.append(" FROM ms_branch MB with (nolock) LEFT JOIN MS_PROMOOFBRANCH MPB with (nolock) ON MB.UUID_BRANCH=MPB.UUID_BRANCH")
		.append(" where 1=1 ").append(paramsQueryString).append(" ) C"); 
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		
		Integer result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}
	
	@Override
	public List listPromoOfBranch(String uuid, String uuidParent, String[] uuidBranches,
			String branchName, String branchCode, int targetPageNo, int pageSize, AuditContext callerId) {
		if(uuidBranches == null){
			uuidBranches = new String[]{"-"};
		}
		
		Object[][] params = { { "uuidStkpromo", uuid == null ? "@" : uuid },				
				{ "branchCode", branchCode == null ? "%" : branchCode },
				{ "branchName", branchName == null ? "%" : branchName }, 
				{ "uuidParent", uuidParent == null ? "%" : uuidParent }, 
				{ "uuidSelectedBranch", Arrays.asList(uuidBranches) },
				{ "start", ( targetPageNo - 1 ) * pageSize + 1 }, 
				{ "end", ( targetPageNo - 1 ) * pageSize + pageSize } };
		
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilderBranch((Object[][]) params, paramsStack);
		
		StringBuilder queryBuilder = new StringBuilder()
		.append(" select * from (")
		.append(" SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (")
		.append(" SELECT *, ROW_NUMBER() OVER (ORDER BY BRANCH_CODE, BRANCH_NAME) AS rownum FROM (")
		.append(" SELECT DISTINCT MB.UUID_BRANCH, MB.BRANCH_CODE, MB.BRANCH_NAME, ")
		.append(" (case when MPB.UUID_BRANCH IS null then 'false' else 'true' end) as FLAG")
		.append(" FROM ms_branch MB with (nolock) LEFT JOIN MS_PROMOOFBRANCH MPB with (nolock) ")
		.append(" ON MB.UUID_BRANCH=MPB.UUID_BRANCH")
		.append(" where 1=1 ").append(paramsQueryString).append(" ) C");
		
	    queryBuilder.append(") a WHERE a.rownum <= :end");
		queryBuilder.append(") b WHERE b.recnum >= :start");

		paramsStack.push(new Object[]{"start", ((Object[][]) params)[5][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[6][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		List result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
	
		return result;
	}
	
	private StringBuilder sqlPagingBuilderBranch(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null){
			return new StringBuilder();
		}
		
		StringBuilder sb = new StringBuilder();	
		sb.append(" AND MB.UUID_BRANCH NOT IN ( SELECT UUID_BRANCH FROM MS_PROMOOFBRANCH with (nolock) WHERE UUID_STK_PROMO = :uuidStkpromo )");
		paramStack.push(new Object[]{"uuidStkpromo", (String) params[0][1]});
		sb.append(" AND MB.BRANCH_CODE LIKE '%'+ :branchCode +'%' AND MB.BRANCH_NAME LIKE '%'+ :branchName +'%'");
		paramStack.push(new Object[]{"branchCode", (String) params[1][1]});
		paramStack.push(new Object[]{"branchName", (String) params[2][1]});
		
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append(" AND coalesce(CAST(MPB.UUID_STK_PROMO as VARCHAR(36)), '#') like '%'+ :uuidParent +'%'");
			paramStack.push(new Object[]{"uuidParent", (String) params[3][1]});
		}
		
		sb.append(" AND MB.UUID_BRANCH NOT IN ( :uuidSelectedBranch )");
		paramStack.push(new Object[]{"uuidSelectedBranch", params[4][1]});
		
	
		return sb;
	}
	
	
	@Override
	public Integer countListStkPromoOfBranch(String uuid, String uuidParent, String[] uuidBranches, AuditContext callerId) {
		uuid = uuid != null ? uuid : "-";
		uuidParent = uuidParent != null ? uuidParent : "-";
		
		if(uuidBranches == null){
			uuidBranches = new String[]{"-"};
		}
		
		Object[][] params = { { "uuidStkpromos", Arrays.asList(uuid) }, 
			{ "uuidBranches", Arrays.asList(uuidBranches) } };
		Integer result = (Integer)this.getManagerDAO().selectOneNative("setting.cms.cntPromoOfBranch", params);	
		return result;
	}	
	
	@Override
	public List listStkPromoOfBranch(String uuid, String uuidParent, String[] uuidBranches,
			int targetPageNo, int pageSize, AuditContext callerId) {
//		uuid = uuid != null ? uuid : "-";
		Long[] data;
		if (uuid == null || "".equals(uuid)) {
			uuid = "0";
		}
		long uid = Long.valueOf(uuid);
		uuidParent = uuidParent != null ? uuidParent : "-";
		
		if(uuidBranches == null){
			uuidBranches = new String[]{"0"};
		}
		data = new Long[uuidBranches.length];
		for (int i = 0; i < uuidBranches.length; i++) {
			if ("".equals(uuidBranches[i])) {
				uuidBranches[i] = "0";
			}
			data[i] = Long.valueOf(uuidBranches[i]);
		}
		Object[][] params = { { "uuidStkpromos", Arrays.asList(uid) }, 
			{ "uuidBranches", Arrays.asList(data) }, 
			{ "start", (targetPageNo*pageSize)+1 }, 
			{ "end", (targetPageNo+1)*pageSize } };
		
		List list = this.getManagerDAO().selectAllNative("setting.cms.listPromoOfBranch", params, null);		
		
		List<CmsBranchBean> cmsBranchBean = new ArrayList<CmsBranchBean>();
		for(int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);
			
			CmsBranchBean bean = new CmsBranchBean();
			bean.setUuidBranch(Long.valueOf(temp.get("d0").toString()));
			bean.setBranchCode(temp.get("d1").toString());
			bean.setBranchName(temp.get("d2").toString());
			bean.setIsParentS(this.isParentS(uuid, temp.get("d0").toString(), uuidBranches));
				
			cmsBranchBean.add(bean);
		}
		
		return cmsBranchBean;
	}
	
	@Override
	public List<CmsDealerBean> listStkPromoOfDealer(String uuid, String uuidParent, String[] uuidDealers,
			int targetPageNo, int pageSize, AuditContext callerId) {
		uuid = uuid != null ? uuid : "-";
		uuidParent = uuidParent != null ? uuidParent : "-";
		
		if(uuidDealers == null)
			uuidDealers = new String[]{"-"};
		
		Object[][] params = { { "uuidStkpromos", Arrays.asList(uuid) }, 
			{ "uuidDealers", Arrays.asList(uuidDealers) }, 
			{ "start", (targetPageNo*pageSize)+1 }, 
			{ "end", (targetPageNo+1)*pageSize } };
		
		
		List<CmsDealerBean> result = null;
		List list = this.getManagerDAO().selectAllNative("setting.cms.listPromoOfDealer", params, null);		
		
		List<CmsDealerBean> cmsDealerBean = new ArrayList<CmsDealerBean>();
		for(int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);
			
			CmsDealerBean bean = new CmsDealerBean();
			bean.setUuidDealer(Long.valueOf(temp.get("d0").toString()));
			bean.setDealerCode(temp.get("d1").toString());
			bean.setDealerName(temp.get("d2").toString());
			bean.setIsParentS(this.isParentS(uuid, temp.get("d0").toString(), uuidDealers));
				
			cmsDealerBean.add(bean);
		}
		result = cmsDealerBean;
		
		return result;
	}
	
	private boolean isParentS(String uuid, String uuidBranch, String[] uuidBranches){
		if (Arrays.asList(uuidBranches).contains(uuidBranch)){
			return false;
		}
		else {
			Object[][] paramPromo = { { Restrictions.eq("msStkpromo.uuidStkPromo", Long.valueOf(uuid)) } };
			Map<String, Object> promo = this.getManagerDAO().selectAll(MsStkpromo.class, paramPromo, null);
			if( (Long) promo.get(GlobalKey.MAP_RESULT_SIZE) == 0 ){
				return false;
			}
			else {
				Object[][] params = { { Restrictions.in("msStkpromo", (List) promo.get(GlobalKey.MAP_RESULT_LIST)) }, 
					{ Restrictions.eq("msBranch.uuidBranch", Long.valueOf(uuidBranch)) } };
				Map<String, Object> test = this.getManagerDAO().count(MsPromoofbranch.class, params);
				if((Long) test.get(GlobalKey.MAP_RESULT_SIZE) != 0){
					return true;
				}
				else {
					return false;
				}
			}
		}
	}
	
	private byte[] compressImg(byte[] imgToCompress) throws IOException {
		InputStream in = new ByteArrayInputStream(imgToCompress);
		BufferedImage oriImage = ImageIO.read(in);
		
		BufferedImage resizedImage = new BufferedImage((int) (oriImage.getWidth()*0.5), (int) (oriImage.getHeight()*0.5), Image.SCALE_SMOOTH);
		Graphics2D g = resizedImage.createGraphics();
		g.drawImage(oriImage, 0, 0, (int) (oriImage.getWidth()*0.5), (int) (oriImage.getHeight()*0.5), null);
		g.dispose();
	 
		ByteArrayOutputStream baos = new ByteArrayOutputStream();		
		ImageWriter writer = (ImageWriter) ImageIO.getImageWritersByFormatName("jpeg").next();

		ImageWriteParam param = writer.getDefaultWriteParam();
		param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
		param.setCompressionQuality(0.1f); // Change this, float between 0.0 and 1.0

		writer.setOutput(ImageIO.createImageOutputStream(baos));
		writer.write(null, new IIOImage(resizedImage, null, null), param);
		writer.dispose();
		
		return baos.toByteArray();
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertPromoContent(String uuidStkpromo, String[][] contentTypeAndBase64, 
			Date startDate, Date endDate, AuditContext callerId) throws IOException {
		MsStkpromo msStkpromo = new MsStkpromo();
		msStkpromo.setUuidStkPromo(Long.valueOf(uuidStkpromo));
		
		String[][] param = { {"uuid", uuidStkpromo} };
		Short lastSeq = (Short) this.getManagerDAO().selectOneNativeString("SELECT MAX(SEQUENCE) FROM MS_STKPROMOCONTENT WITH (NOLOCK) WHERE UUID_STK_PROMO = :uuid", param);
		
		lastSeq = lastSeq == null ? 0 : lastSeq;
		for(int i = 0; i < contentTypeAndBase64.length; i++){		
			MsStkpromocontent promocontent = new MsStkpromocontent();				
			promocontent.setContentType(contentTypeAndBase64[i][0]);
			promocontent.setContent(this.compressImg(Base64.decode(contentTypeAndBase64[i][1])));
			promocontent.setSequence((short) (lastSeq.shortValue()+(10*(i+1))));
			promocontent.setIsActive("1");
			promocontent.setStarteffDate(startDate);
			promocontent.setEndeffDate(endDate);
			promocontent.setMsStkpromo(msStkpromo);
			promocontent.setUsrCrt(callerId.getCallerId());
			promocontent.setDtmCrt(new Date());
			
			this.getManagerDAO().insert(promocontent);
			this.auditManager.auditAdd(promocontent, auditInfoCtn, callerId.getCallerId(), "");	
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)	
	@Override
	public String[] deletePromoOfBranch(String[] uuidBranches, String uuidBranch, String uuidStkPromo, AuditContext callerId) {
		String[] result = ArrayUtils.removeElement(uuidBranches, uuidBranch);

	    if(result.length < uuidBranches.length){   	
	    	return result;
	    }
	    else {
			String[][] params = { {"uuidStkpromo", uuidStkPromo}, {"uuidBranch", uuidBranch} };
			this.getManagerDAO().deleteNative("setting.cms.deleteBranchToChild", params);
			
			return uuidBranches;
	    }	    	
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteContent(String uuidContent, AuditContext callerId) {
		long uuid = Long.valueOf(uuidContent);
		MsStkpromocontent content = this.getManagerDAO().selectOne(MsStkpromocontent.class, uuid);
		String[][] params = { {"uuidStkpromo", String.valueOf(content.getMsStkpromo().getUuidStkPromo())}
			,{"uuidContent", String.valueOf(content.getUuidStkPromoContent())} };
		
		this.auditManager.auditDelete(content, auditInfoCtn, callerId.getCallerId(), "");
		this.getManagerDAO().delete(content);
		
		this.getManagerDAO().updateNative("setting.cms.updateSequence", params);
	}

	@Override
	public List listPromoOfDealer(String uuidBranch, String uuid, String uuidParent,
			String[] uuidDealers, String dealerName, String dealerCode,
			int targetPageNo, int pageSize, AuditContext callerId) {
		if(uuidDealers == null){
			uuidDealers = new String[]{"-"};
		}
		
		Object[][] params = { { "uuidBranch", uuidBranch == null ? "@" : uuidBranch },
				{ "uuidStkpromo", uuid == null ? "@" : uuid },				
				{ "dealerCode", dealerCode == null ? "%" : dealerCode },
				{ "dealerName", dealerName == null ? "%" : dealerName }, 
				{ "uuidParent", uuidParent == null ? "%" : uuidParent }, 
				{ "uuidSelectedDealer", Arrays.asList(uuidDealers) },
				{ "start", ( targetPageNo - 1 ) * pageSize + 1 }, 
				{ "end", ( targetPageNo - 1 ) * pageSize + pageSize } };
		
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilderDealer((Object[][]) params, paramsStack);
		
		StringBuilder queryBuilder = new StringBuilder()
		.append(" select * from (")
		.append(" SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (")
		.append(" SELECT *, ROW_NUMBER() OVER (ORDER BY DEALER_CODE, DEALER_NAME) AS rownum FROM (")
		.append(" SELECT DISTINCT MD.UUID_DEALER	, MD.DEALER_CODE, MD.DEALER_NAME, ")
		.append(" (case when MPD.UUID_DEALER IS null then 'false' else 'true' end) as FLAG")
		.append(" FROM ms_dealer MD with (nolock) LEFT JOIN MS_PROMOOFDEALER MPD with (nolock) ON MD.UUID_DEALER=MPD.UUID_DEALER")
		.append(" JOIN MS_DEALEROFBRANCH MDB ON MDB.UUID_DEALER = MD.UUID_DEALER ")
		.append(" where 1=1 ").append(paramsQueryString).append(" ) C");
		
	    queryBuilder.append(") a WHERE a.rownum <= :end");
		queryBuilder.append(") b WHERE b.recnum >= :start");

		paramsStack.push(new Object[]{"start", ((Object[][]) params)[6][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[7][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		List result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
	
		return result;
	}

	@Override
	public Integer countListPromoOfDealer(String uuidBranch, String uuid, String uuidParent,
			String[] uuidDealers, String dealerName, String dealerCode,
			AuditContext callerId) {
		if(uuidDealers == null){
			uuidDealers = new String[]{"-"};
		}
		
		Object[][] params = { {"uuidBranch", uuidBranch == null ? "%" : uuidBranch},
				{ "uuidStkpromo", uuid == null ? "%" : uuid },				
				{ "dealerCode", dealerCode == null ? "%" : dealerCode },
				{ "dealerName", dealerName == null ? "%" : dealerName }, 
				{ "uuidParent", uuidParent == null ? "%" : uuidParent }, 
				{ "uuidSelectedDealer", Arrays.asList(uuidDealers) } };
		
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilderDealer((Object[][]) params, paramsStack);
		
		StringBuilder queryBuilder = new StringBuilder()		
		.append(" SELECT COUNT(1)")
		.append(" FROM (")
		.append(" SELECT DISTINCT MD.UUID_DEALER	, MD.DEALER_CODE, MD.DEALER_NAME, ")
		.append(" (case when MPD.UUID_DEALER IS null then 'false' else 'true' end) as FLAG")
		.append(" FROM ms_dealer MD with (nolock) LEFT JOIN MS_PROMOOFDEALER MPD with (nolock) ON MD.UUID_DEALER=MPD.UUID_DEALER")
		.append(" JOIN MS_DEALEROFBRANCH MDB ON MDB.UUID_DEALER = MD.UUID_DEALER")
		.append(" where 1=1 ").append(paramsQueryString).append(" ) C"); 
			
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		
		Integer result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}

	private StringBuilder sqlPagingBuilderDealer(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null){
			return new StringBuilder();
		}
		
		StringBuilder sb = new StringBuilder();	
		sb.append(" AND MDB.UUID_BRANCH = :uuidBranch");
		paramStack.push(new Object[]{"uuidBranch", (String) params[0][1]});
		sb.append(" AND MD.UUID_DEALER NOT IN ( SELECT UUID_DEALER FROM MS_PROMOOFDEALER with (nolock) WHERE UUID_STK_PROMO  = :uuidStkpromo )");
		paramStack.push(new Object[]{"uuidStkpromo", (String) params[1][1]});
		sb.append(" AND MD.DEALER_CODE LIKE '%'+ :dealerCode +'%' AND MD.DEALER_NAME LIKE '%'+ :dealerName +'%'");
		paramStack.push(new Object[]{"dealerCode", (String) params[2][1]});
		paramStack.push(new Object[]{"dealerName", (String) params[3][1]});
		
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append(" AND coalesce(CAST(MPD.UUID_STK_PROMO as VARCHAR(36)), '#') like '%'+ :uuidParent +'%'");
			paramStack.push(new Object[]{"uuidParent", (String) params[4][1]});
		}
		
		sb.append(" AND MD.UUID_DEALER NOT IN ( :uuidSelectedDealer )");
		paramStack.push(new Object[]{"uuidSelectedDealer", params[5][1]});
		
	
		return sb;
	}
	
	@Override
	public Integer countListStkPromoOfDealer(String uuid, String uuidParent,
			String[] uuidDealers, AuditContext callerId) {
		uuid = uuid != null ? uuid : "-";
		uuidParent = uuidParent != null ? uuidParent : "-";
		
		if(uuidDealers == null){
			uuidDealers = new String[]{"-"};
		}
		
		Object[][] params = { { "uuidStkpromos", Arrays.asList(uuid) }, 
			{ "uuidDealers", Arrays.asList(uuidDealers) } };
		Integer result = (Integer)this.getManagerDAO().selectOneNative("setting.cms.cntPromoOfDealer", params);	
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String[] deletePromoOfDealer(String[] uuidDealers,
			String uuidDealer, String uuidStkPromo, AuditContext callerId) {
		String[] result = ArrayUtils.removeElement(uuidDealers, uuidDealer);
		if(result.length < uuidDealers.length){	    	
	    	return result;
		}
	    else {
			String[][] params = { {"uuidStkpromo", uuidStkPromo}, {"uuidDealer", uuidDealer} };
			this.getManagerDAO().deleteNative("setting.cms.deleteDealerToChild", params);
			
	    	return uuidDealers;
	    }
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertStkpromoByDealer(MsStkpromo stkPromo, String uuidParent,
			String[] uuidDealers, AuditContext callerId) {
		if (uuidParent != null && !"".equals(uuidParent)) {
			MsStkpromo parent = this.getManagerDAO().selectOne(
					MsStkpromo.class, Long.valueOf(uuidParent));
			stkPromo.setMsStkpromo(parent);
		} 
		else{
			stkPromo.setMsStkpromo(null);
		}
		stkPromo.setIsActive("1");
		stkPromo.setDtmCrt(new Date());
		stkPromo.setUsrCrt(callerId.getCallerId());
		stkPromo.setIsBranch("0");
		
		this.getManagerDAO().insert(stkPromo);	
		this.auditManager.auditAdd(stkPromo, auditInfoStk, callerId.getCallerId(), "");		
		
		if(uuidDealers != null){
			this.insertPromoOfDealer(stkPromo, uuidDealers, callerId);
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateStkpromoByDealer(MsStkpromo stkPromo,
			String[] uuidDealers, AuditContext callerId) {
		MsStkpromo dbModel = this.getManagerDAO().selectOne(MsStkpromo.class, stkPromo.getUuidStkPromo());
		
		dbModel.setPromoName(stkPromo.getPromoName());
		dbModel.setPromoShortDesc(stkPromo.getPromoShortDesc());
		dbModel.setStarteffDate(stkPromo.getStarteffDate());
		dbModel.setEndeffDate(stkPromo.getEndeffDate());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());
		dbModel.setIsBranch("0");
		this.auditManager.auditEdit(dbModel, auditInfoStk, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
		
		Object[][] paramsStkPromo = {{"uuidStkPromo", stkPromo.getUuidStkPromo()}};
		
		List<Map<String, Object>> childPromoList = this.getManagerDAO().selectAllNative("setting.cms.getChildPromo", paramsStkPromo, null);
		List updateStart = new ArrayList<>();
		List updateEnd = new ArrayList<>();
		
		for(int i = 0; i < childPromoList.size(); i++) {
			Map<String, Object> bean = (Map<String, Object>) childPromoList.get(i);
			if(!(((Date) bean.get("d1")).after(stkPromo.getStarteffDate()) && ((Date) bean.get("d1")).before(stkPromo.getEndeffDate()))) {
				updateStart.add((BigInteger) bean.get("d0"));
			}
			if(!(((Date) bean.get("d2")).after(stkPromo.getStarteffDate()) && ((Date) bean.get("d2")).before(stkPromo.getEndeffDate()))) {
				updateEnd.add((BigInteger) bean.get("d0"));
			}
		}
			
		if(!updateStart.isEmpty()) {
			String[] uuidUpdate = new String[updateStart.size()];
			for (int j = 0; j < updateStart.size(); j++) {
				uuidUpdate[j] = (String) updateStart.get(j).toString();
			}
			Object[][] paramStart = {
				{"uuidStkpromo", Arrays.asList(uuidUpdate)},
				{"startEffDate", DateFormatUtils.format(stkPromo.getStarteffDate(), "yyyy-MM-dd")+" 00:00:00.000"}
			};
			this.getManagerDAO().updateNative("setting.cms.updateStart", paramStart);
		}
		if(!updateEnd.isEmpty()) {
			String[] uuidUpdate = new String[updateEnd.size()];
			for (int j = 0; j < updateEnd.size(); j++) {
				uuidUpdate[j] = (String) updateEnd.get(j);
			}
			Object[][] paramEnd = {
				{"uuidStkpromo", Arrays.asList(uuidUpdate)},
				{"endEffDate", DateFormatUtils.format(stkPromo.getEndeffDate(), "yyyy-MM-dd")+" 23:59:59.997"}
			};
			this.getManagerDAO().updateNative("setting.cms.updateEnd", paramEnd);
		}	
		
		if(uuidDealers != null && uuidDealers.length > 0){
			this.insertPromoOfDealer(stkPromo, uuidDealers, callerId);
		}
	}

	@Override
	public Integer countListStokByDealer(Object params, AuditContext callerId) {
		Integer result = (Integer)this.getManagerDAO().selectOneNative("setting.cms.cntStokByDealer", params);	
		return result;
	}
}
