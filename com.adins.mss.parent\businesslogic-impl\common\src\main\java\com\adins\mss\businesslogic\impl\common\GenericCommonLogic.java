package com.adins.mss.businesslogic.impl.common;


import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.DistanceUtils;
import com.adins.framework.tool.lang.FormatterUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.ReportDownloadLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.model.AmAttributeofmember;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsattribute;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.FinalTrTaskdocument;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsAssettag;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsCollectiontag;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsLocation;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsQuestionrelevant;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.MsTskdistributionofmodule;
import com.adins.mss.model.TrLocationhistory;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.custom.DashboardMonitoringBean;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.ImageBean;
import com.adins.mss.model.taskdjson.LocationBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.util.MssTool;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;

@SuppressWarnings({"unchecked"})
public class GenericCommonLogic extends BaseLogic implements CommonLogic {
	private final Logger logger = LoggerFactory.getLogger(GenericCommonLogic.class);
	private final boolean debugCache = false;
	
	private GeolocationLogic geocoder;
	private ImageStorageLogic imageStorageLogic;

	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}

	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
		this.imageStorageLogic = imageStorageLogic;
	}

	@SuppressWarnings("rawtypes")
	@Transactional(readOnly = true)
	@Override
	public List<DashboardMonitoringBean> retrieveMobileLocation(long uuidSpv, long uuidSubsytem, AuditContext callerId) {

		List<DashboardMonitoringBean> userList = this.retrieveSubordinate(uuidSpv);

		AmMsuser spv = this.getManagerDAO().selectOne(AmMsuser.class, uuidSpv);
		MsBranch msBranch = spv.getMsBranch();
		
		if(null == msBranch.getLatitude() && null == msBranch.getLongitude() ||
				msBranch.getLongitude().toString().isEmpty() && msBranch.getLatitude().toString().isEmpty())
		{	// set user's LatLng to HO's LatLng
			boolean flag = true;
			Map<String, Object> latlong = new HashMap();
			
			String idBranch = String.valueOf(msBranch.getUuidBranch());
			StringBuilder sb = new StringBuilder(); 
			sb.append("select top 1 PARENT_ID, LATITUDE, LONGITUDE from MS_BRANCH ");
			sb.append("where UUID_BRANCH = isnull((select PARENT_ID from MS_BRANCH ");
			sb.append("where UUID_BRANCH = :UUID_MS_BRANCH),");
			sb.append(":UUID_MS_BRANCH)");
			
			while(flag){
				Object[][] params = { { "UUID_MS_BRANCH" , idBranch } };
				List temp = this.getManagerDAO().selectAllNativeString(sb.toString(), params);
				latlong = (Map) temp.get(0);
				if(null != latlong.get("d1") && null != latlong.get("d2"))
				{
					flag = false;
					msBranch.setLatitude((BigDecimal) latlong.get("d1"));
					msBranch.setLongitude((BigDecimal) latlong.get("d2"));
				} else {
					idBranch = latlong.get("d0").toString();
				}
			}
		}

		for (Iterator<DashboardMonitoringBean> iterator = userList.iterator(); iterator.hasNext();) {
			DashboardMonitoringBean bean = iterator.next();

			String timestamp = this.getLastLocationTimestamp(bean.getUuidMsUser(), uuidSubsytem, callerId);

			// user's lastLocation attribute not found, set default to
			// area's LatLng
			if (StringUtils.isBlank(timestamp) || !this.isTimestampToday(timestamp, GlobalVal.FORMAT_DM_TIMESTAMP)) {
				List<DashboardMonitoringBean> listAreaOfUser = this.retrieveArea(String.valueOf(bean.getUuidMsUser()));

				if (listAreaOfUser == null || listAreaOfUser.isEmpty()) {
//					 iterator.remove(); //user will not be shown on map
					bean.setLatitude(msBranch.getLatitude());
					bean.setLongitude(msBranch.getLongitude());
					bean.setLastTimeDetected("-");
					continue;
				}

				DashboardMonitoringBean area1 = listAreaOfUser.get(0);
				bean.setLatitude(area1.getLatitude());
				bean.setLongitude(area1.getLongitude());
				bean.setLastTimeDetected("-");
			} else {
				Date date = this.timestampToDate(timestamp,
						GlobalVal.FORMAT_DM_TIMESTAMP);
				String lastTimeDetected = FormatterUtils.formatDate(date,
						GlobalVal.FORMAT_DM_LAST_DETECTED);
				bean.setLastTimeDetected(lastTimeDetected);

				// if timestamp found, lastLoc is assumed exists too
				String lastLoc = this.getLastLocation(bean.getUuidMsUser(), uuidSubsytem, callerId);
				String[] latLng = StringUtils.split(lastLoc, ',');
				bean.setLatitude(new BigDecimal(latLng[0]));
				bean.setLongitude(new BigDecimal(latLng[1]));
			}
		}
		return userList;
	}

	@Transactional(readOnly = true)
	@Override
	public List<DashboardMonitoringBean> retrieveMobileArea(long[] uuidMobileUsers, AuditContext callerId) {
		if (ArrayUtils.isEmpty(uuidMobileUsers))
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		for (long uuidUser : uuidMobileUsers) {
			DashboardMonitoringBean bean = this.retrieveMobileArea(uuidUser);
			if (bean != null) {
				result.add(bean);
			}
		}

		return result;
	}

	private DashboardMonitoringBean retrieveMobileArea(long uuidUser) {
		Object[][] paramsArea = { { "uuidUser", uuidUser } };
		List<Map<String, Object>> listArea = this.getManagerDAO().selectAllNative(
				"common.dashboard.getArea", paramsArea, null);
		BigDecimal[][] area = new BigDecimal[listArea.size()][2];

		DashboardMonitoringBean resultBean = new DashboardMonitoringBean();
		String areaType = null;
		for (int x = 0; x < listArea.size(); x++) {
			Map<String, Object> record = listArea.get(x);
			if (areaType == null) {
				areaType = (String) record.get("d4");
				resultBean.setAreaType(areaType);
			}

			if (GlobalVal.AREA_CIRCLE.equalsIgnoreCase(areaType)) {
				resultBean.setRadius((record.get("d5") == null) ? 0
						: ((Integer) record.get("d5")).intValue());
				area[0][0] = (BigDecimal) record.get("d0");
				area[0][1] = (BigDecimal) record.get("d1");
			} else {
				area[x][0] = (BigDecimal) record.get("d2");
				area[x][1] = (BigDecimal) record.get("d3");
			}
		}
		resultBean.setArea(area);
		resultBean.setUuidMsUser(Long.valueOf(uuidUser));

		return resultBean;
	}

	@Transactional(readOnly = true)
	@Override
	public List<DashboardMonitoringBean> retrieveLocationHistory(long[] uuidMobileUsers,
			Date startDate, Date endDate, AuditContext callerId) {
		if (ArrayUtils.isEmpty(uuidMobileUsers) || startDate == null || endDate == null)
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		for (long uuidUser : uuidMobileUsers) {
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setUuidMsUser(uuidUser);
			List<TrLocationhistory> tracking = this.retrieveLocationHistory(uuidUser, startDate, endDate, callerId);
			bean.setTracking(tracking);
			result.add(bean);
		}

		return result;
	}

	@Transactional(readOnly = true)
	@Override
	public DashboardMonitoringBean retrieveLocationHistoryMT(
			long uuidMobileUser, Date startDate, Date endDate, AuditContext callerId) {
		if (startDate == null || endDate == null)
			return null;

		DashboardMonitoringBean bean = new DashboardMonitoringBean();
		bean.setUuidMsUser(Long.valueOf(uuidMobileUser));
		List<TrLocationhistory> tracking = this.retrieveLocationHistory(
				uuidMobileUser, startDate, endDate, callerId);
		bean.setTracking(tracking);

		return bean;
	}

	@Transactional(readOnly = true)
	@Override
	public List<TrLocationhistory> retrieveLocationHistory(
			long uuidMobileUser, Date startDate, Date endDate, AuditContext callerId) {
		if (startDate == null || endDate == null)
			return Collections.emptyList();

		Object[][] paramsTrack = {
				{ Restrictions.eq("amMsuser.uuidMsUser", uuidMobileUser) },
				{ Restrictions.between("datetime", startDate, endDate) } };
		String[][] orderTrack = { { "datetime", GlobalVal.ROW_ORDER_DESC } };

		Map<String, Object> mapTrack = this.getManagerDAO().list(
				TrLocationhistory.class, paramsTrack, orderTrack);

		return (List<TrLocationhistory>) mapTrack.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Transactional(readOnly = true)
	@Override
	public String getLastLocation(long uuidMsUser, long uuidMsSubsystem, AuditContext callerId) {
		AmMsattribute attrLastLocation = this.retrieveAttribute(GlobalKey.MSATTR_LASTLOCATION, uuidMsSubsystem);
		AmAttributeofmember result = this.retrieveMembersAttribute(attrLastLocation, uuidMsUser);
		return (result == null) ? null : result.getAttributeValue();
	}

	@Transactional
	@Override
	public void insertLastLocation(long uuidMsUser, long uuidMsSubsystem,
			BigDecimal latitude, BigDecimal longitude, AuditContext callerId) {
		if (latitude == null || longitude == null) {
			throw new IllegalArgumentException("latitude, longitude are required!!");
		}

		AmMsattribute attrLastLocation = this.retrieveAttribute(GlobalKey.MSATTR_LASTLOCATION, uuidMsSubsystem);

		AmAttributeofmember model = this.retrieveMembersAttribute(attrLastLocation, uuidMsUser);
		boolean exists = true;
		if (model == null) {
			model = new AmAttributeofmember();
			exists = false;
		}

		model.setAttributeValue(StringUtils.join(latitude.toString(), ",", longitude.toString()));

		if (exists) {
			model.setUsrUpd(callerId.getCallerId());
			model.setDtmUpd(new Date());

			this.getManagerDAO().update(model);
		} else {
			model.setUsrCrt(callerId.getCallerId());
			model.setDtmCrt(new Date());
			AmMsuser user = new AmMsuser();
			user.setUuidMsUser(Long.valueOf(uuidMsUser));
			model.setAmMsuser(user);
			model.setAmMsattribute(attrLastLocation);

			this.getManagerDAO().insert(model);
		}
	}

	@Transactional(readOnly = true)
	@Override
	public String getLastLocationTimestamp(long uuidMsUser, long uuidMsSubsystem, AuditContext callerId) {
		AmMsattribute attrLastLocationTs = this.retrieveAttribute(
				GlobalKey.MSATTR_LASTLOCATION_TIMESTAMP, uuidMsSubsystem);
		AmAttributeofmember result = this.retrieveMembersAttribute(
				attrLastLocationTs, uuidMsUser);
		return (result == null) ? null : result.getAttributeValue();
	}

	@Transactional
	@Override
	public void insertLastLocationTimestamp(long uuidMsUser, long uuidMsSubsystem, Date timestamp, AuditContext callerId) {
		if (timestamp == null) {
			throw new IllegalArgumentException("timestamp are required!!");
		}

		AmMsattribute attrLastLocationTs = this.retrieveAttribute(
				GlobalKey.MSATTR_LASTLOCATION_TIMESTAMP, uuidMsSubsystem);

		AmAttributeofmember model = this.retrieveMembersAttribute(
				attrLastLocationTs, uuidMsUser);
		boolean exists = true;
		if (model == null) {
			model = new AmAttributeofmember();
			exists = false;
		}

		model.setAttributeValue(DateFormatUtils.format(timestamp,
				GlobalVal.FORMAT_DM_TIMESTAMP));

		if (exists) {
			model.setUsrUpd(callerId.getCallerId());
			model.setDtmUpd(new Date());

			this.getManagerDAO().update(model);
		} else {
			model.setUsrCrt(callerId.getCallerId());
			model.setDtmCrt(new Date());
			AmMsuser user = new AmMsuser();
			user.setUuidMsUser(Long.valueOf(uuidMsUser));
			model.setAmMsuser(user);
			model.setAmMsattribute(attrLastLocationTs);

			this.getManagerDAO().insert(model);
		}
	}

	@Transactional(readOnly = true)
	@Override
	public String getTrackingInterval(AuditContext callerId) {
		String subsystemName = (String) callerId.getParameters().get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
		return this.getGsValue(subsystemName + GlobalKey.GENERALSETTING_TRACKING_INTERVAL);
	}

	private AmAttributeofmember retrieveMembersAttribute(
			AmMsattribute attribute, long uuidMsUser) {
		if (attribute == null)
			return null;

		Object[][] params = { { Restrictions.eq("amMsattribute", attribute) },
				{ Restrictions.eq("amMsuser.uuidMsUser", uuidMsUser) } };

		return this.getManagerDAO().selectOne(AmAttributeofmember.class, params);
	}

	private AmMsattribute retrieveAttribute(String attributeCode,
			long uuidMsSubsystem) {
		if (StringUtils.isBlank(attributeCode))
			return null;

		Object[][] params = {
				{ Restrictions.eq("atbCode", attributeCode) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidMsSubsystem) } };

		return this.getManagerDAO().selectOne(AmMsattribute.class, params);
	}

	private List<DashboardMonitoringBean> retrieveSubordinate(long uuidSpv) {
		Object[][] params = { { "uuidSpv", uuidSpv } };
		List<Map<String, Object>> queryResultList = this.getManagerDAO()
				.selectAllNative("common.dashboard.getUsers", params, null);

		if (queryResultList == null || queryResultList.isEmpty())
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		for (Map<String, Object> user : queryResultList) {
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setUuidMsUser(Long.valueOf(user.get("d0").toString()));
			bean.setFullName((String) user.get("d1"));
			bean.setIsLoggedIn((String) user.get("d2"));
			bean.setLoginId((String) user.get("d3"));
			bean.setInitialName((String) user.get("d4"));

			AmMsuser beanSpv = new AmMsuser();

			beanSpv.setLoginId((String) user.get("d5"));
			beanSpv.setFullName((String) user.get("d6"));

			bean.setAmMsuser(beanSpv);

			result.add(bean);
		}

		return result;
	}

	private List<DashboardMonitoringBean> retrieveArea(String uuidUser) {
		String[][] params = { { "uuidUser", uuidUser } };
		List<Map<String, Object>> queryResultList = this
				.getManagerDAO()
				.selectAllNative("common.dashboard.getAreaOfUser", params, null);

		if (queryResultList == null || queryResultList.isEmpty())
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		for (Map<String, Object> area : queryResultList) {
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setAreaType((String) area.get("d0"));
			bean.setLatitude((BigDecimal) area.get("d1"));
			bean.setLongitude((BigDecimal) area.get("d2"));
			if (GlobalVal.AREA_CIRCLE.equalsIgnoreCase(bean.getAreaType())) {
				bean.setRadius((area.get("d3") == null) ? 0 : ((Integer) area
						.get("d3")).intValue());
			}

			result.add(bean);
		}

		return result;
	}

	private boolean isTimestampToday(String timestamp, String format) {
		if (StringUtils.isBlank(timestamp))
			return false;

		try {
			Date date = FormatterUtils.parseDate(timestamp, format);
			return (new DateTime(date).toLocalDate()).equals(new LocalDate());
		} catch (ParseException pe) {
			return false;
		}
	}

	private Date timestampToDate(String timestamp, String format) {
		try {
			return FormatterUtils.parseDate(timestamp, format);
		} catch (ParseException pe) {
			return null;
		}
	}

	private String getGsValue(String gsCode) {
		String[][] params = { { "gsCode", gsCode } };
		List<Map<String, Object>> result = this.getManagerDAO().selectAllNative("common.getGsValue", params, null);

		for (Map<String, Object> gs : result) {
			return (String) gs.get("d0");
		}

		return null;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public String retrieveNewTaskId(String seqCode) {
		this.logger.debug("Generating task id '{}'... ", seqCode);
		
		Object[][] params = { { 1, seqCode }, { 2, "" } };
		Object[][] outputs = { { 3, java.sql.Types.VARCHAR } };

		Map<String, ?> map = this.getManagerDAO().callProcedureNativeString(
				"{ call GET_NEXT_SEQ_NO(?,?,?) } ", params, outputs);
		String result = (String) map.get("o1");
		if (debugCache) {
			this.logger.debug("End of generate task id '{}': {}", seqCode, result);
		}
		return result;
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public String retrieveNewTaskIdTemp(String seqCode) {
		this.logger.debug("Generating task id '{}'... ", seqCode);
		
		Object[][] params = { { 1, seqCode }, { 2, "" } };
		Object[][] outputs = { { 3, java.sql.Types.VARCHAR } };

		Map<String, ?> map = this.getManagerDAO().callProcedureNativeString(
				"{ call GET_NEXT_ODR_NO_TEMP(?,?,?) } ", params, outputs);
		String result = (String) map.get("o1");
		if (debugCache) {
			this.logger.debug("End of generate task id '{}': {}", seqCode, result);
		}
		return result;
	}

	@Cacheable(value = "scheduler.cache", key = "'LOGINID-'.concat(#loginId)")
	@Override
	public AmMsuser retrieveUserByLoginId(String loginId, boolean isActive,
			AuditContext callerId) {
		String active = StringUtils.EMPTY;
		if (isActive) {
			active = "1";
		}
		Object[][] userParam = { { Restrictions.eq("loginId", loginId) },
				{ Restrictions.eq("isActive", active) } };
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		return this.getManagerDAO().selectOne(AmMsuser.class, userParam);
	}

	@Cacheable(value = "scheduler.cache", key = "'SUBSYSTEM-'.concat(#subsystemName)")
	@Override
	public AmMssubsystem retrieveSubsystemByName(String subsystemName,
			AuditContext callerId) {
		Object[][] msSubSystemParam = { { Restrictions.eq("subsystemName",
				subsystemName) } };
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		return this.getManagerDAO().selectOne(AmMssubsystem.class, msSubSystemParam);
	}

	@Cacheable(value = "scheduler.cache", key = "'SUBSYSTEM-'.concat(#subsystemName).concat(#isActive)")
	@Override
	public AmMssubsystem retrieveSubsystemByName(String subsystemName, boolean isActive, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		String active = "1";
		if (isActive) {
			active = "1";
		}
		Object[][] queryParam = { { Restrictions.eq("subsystemName", subsystemName) },
				{ Restrictions.eq("isActive", active) } };
		
		return this.getManagerDAO().selectOne(AmMssubsystem.class, queryParam);
	}
	
	@Cacheable(value = "scheduler.cache", key = "'-FORM-'.concat(#uuidForm)")
	@Override
	public MsForm retrieveMsFormByUuid(long uuidForm, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		return this.getManagerDAO().selectOne(MsForm.class, uuidForm);
	}

//	@Cacheable(value = "scheduler.cache", key = "'-FORM-'.concat(#uuidMsSubsystem).concat('-').concat(#formName)")
	@Override
	public MsForm retrieveMsFormByName(String formName, boolean isActive, long uuidMsSubsystem, AuditContext callerId) {
		String active = StringUtils.EMPTY;
		if (isActive) {
			active = "1";
		}
		Object[][] schemaParam = {
				{ Restrictions.eq("formName", formName) },
				{ Restrictions.eq("isActive", active) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
						uuidMsSubsystem) } };
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		return this.getManagerDAO().selectOne(MsForm.class, schemaParam);
	}

	@Cacheable(value = "scheduler.cache", key = "'-PR-'.concat(#priorityDesc)")
	@Override
	public MsPriority retrieveMsPriority(String priorityDesc, boolean isActive, AuditContext callerId) {
		String active = StringUtils.EMPTY;
		if (isActive) {
			active = "1";
		}
		Object[][] priorityParam = {
				{ Restrictions.eq("priorityDesc", priorityDesc) },
				{ Restrictions.eq("isActive", active) } };
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		return this.getManagerDAO().selectOne(MsPriority.class, priorityParam);

	}

	@Cacheable(value = "scheduler.cache", key = "'-BR-'.concat(#branchCode)")
	@Override
	public MsBranch retrieveBranchByCode(String branchCode, boolean isActive, AuditContext callerId) {
		String active = StringUtils.EMPTY;
		if (isActive) {
			active = "1";
		}
		Object[][] branchParam = {
				{ Restrictions.eq("branchCode", branchCode) },
				{ Restrictions.eq("isActive", active) } };
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		return this.getManagerDAO().selectOne(MsBranch.class, branchParam);

	}

	@Cacheable(value = "scheduler.cache", key = "'-LOC-'.concat(#locationName)")
	@Override
	public MsLocation retrieveLocationByName(String locationName, boolean isActive, AuditContext callerId) {
		String active = StringUtils.EMPTY;
		if (isActive) {
			active = "1";
		}
		Object[][] schemaParam = {
				{ Restrictions.eq("locationName", locationName) },
				{ Restrictions.eq("isActive", active) } };
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		return this.getManagerDAO().selectOne(MsLocation.class, schemaParam);
	}

	@Cacheable(value = "scheduler.cache", key = "'-QSTN-'.concat(#refId)")
	@Override
	public MsQuestion retrieveQuestionByRefId(String refId, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		MsQuestion result = this.getManagerDAO().selectOne(
				"from MsQuestion q join fetch q.msAnswertype where q.refId = :refId", new Object[][] {{"refId", refId}});	
		if (null != result.getMsAssettag()) {
			this.getManagerDAO().fetch(result.getMsAssettag());
		}
		return result;
	}

	@Cacheable(value = "scheduler.cache", key = "'-QSTN-'.concat(#uuidMsSubsystem).concat('-').concat(#refId)")
	@Override
	public MsQuestion retrieveQuestionByRefId(String refId, long uuidMsSubsystem, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		MsQuestion result = this.getManagerDAO().selectOne(
				"from MsQuestion q join fetch q.msAnswertype a join fetch q.amMssubsystem s where q.refId = :refId and s.uuidMsSubsystem = :uuidMsSubsystem", 
				new Object[][] {{"refId", refId}, {"uuidMsSubsystem", uuidMsSubsystem}});		
		return result;
	}
	
	@Cacheable(value = "scheduler.cache", key = "'-QSTN-'.concat(#uuidMsSubsystem).concat('-').concat(#uuidQuestion)")
	@Override
	public MsQuestion retrieveQuestionByuuidQuestion(long uuidQuestion, long uuidMsSubsystem, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		MsQuestion result = this.getManagerDAO().selectOne(
				"from MsQuestion q join fetch q.msAnswertype a join fetch q.amMssubsystem s where q.uuidQuestion = :uuidQuestion and s.uuidMsSubsystem = :uuidMsSubsystem", 
				new Object[][] {{"uuidQuestion", uuidQuestion}, {"uuidMsSubsystem", uuidMsSubsystem}});
		return result;
	}
	
	@SuppressWarnings("unused")
	private MsQuestionrelevant retrieveQuestionRelevant(long uuidQuestion,
			long uuidMsSubsystem, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		MsQuestionrelevant result = this.getManagerDAO().selectOne(
				"from MsQuestionrelevant qr left join fetch qr.msQuestion q join fetch q.msAnswertype a join fetch q.amMssubsystem s where q.uuidQuestion = :uuidQuestion and s.uuidMsSubsystem = :uuidMsSubsystem", 
				new Object[][] {{"uuidQuestion", uuidQuestion}, {"uuidMsSubsystem", uuidMsSubsystem}});
		return result;
	}
	
	@Cacheable(value = "scheduler.cache", key = "'-QSTN-'.concat(#uuidMsSubsystem).concat('-').concat(#refId).concat('-').concat(#uuidFormHistory)")
	@Override
	public MsQuestion retrieveQuestionByRefIdQset(String refId,
			long uuidMsSubsystem, long uuidFormHistory, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		MsFormquestionset qSet = this.getManagerDAO().selectOne(
				"from MsFormquestionset qs join fetch qs.amMssubsystem s join fetch qs.msFormhistory f join fetch qs.msAnswertype a "
				+ "left join fetch qs.msCollectiontag mc left join fetch qs.msAssettag ma left join fetch qs.msOrdertag mo "
				+ "where qs.refId= :refId and s.uuidMsSubsystem = :uuidMsSubsystem and f.uuidFormHistory = :uuidFormHistory",
				new Object[][] {{"refId", refId}, {"uuidMsSubsystem", uuidMsSubsystem}, {"uuidFormHistory", uuidFormHistory}});
		MsQuestion result = this.getQuestionFromQset(qSet, callerId);
		
		return result;
	}
	
	@Cacheable(value = "scheduler.cache", key = "'-QSTN-'.concat(#uuidMsSubsystem).concat('-').concat(#uuidQuestion).concat('-').concat(#uuidFormHistory)")
	@Override
	public MsQuestion retrieveQuestionByuuidQuestionQset(long uuidQuestion,
			long uuidMsSubsystem, long uuidFormHistory, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		MsFormquestionset qSet = this.getManagerDAO().selectOne(
				"from MsFormquestionset qs join fetch qs.msQuestion q join fetch qs.amMssubsystem s join fetch qs.msFormhistory f join fetch qs.msAnswertype a where s.uuidMsSubsystem = :uuidMsSubsystem and f.uuidFormHistory = :uuidFormHistory and q.uuidQuestion = :uuidQuestion",
				new Object[][] {{"uuidQuestion", uuidQuestion}, {"uuidMsSubsystem", uuidMsSubsystem}, {"uuidFormHistory", uuidFormHistory}});
		
		MsQuestion result = this.getQuestionFromQset(qSet, callerId);
		
		return result;
	}
	
	@Cacheable(value = "scheduler.cache", key = "'-QSTNRLVN-'.concat(#uuidMsSubsystem).concat('-').concat(#uuidQuestion).concat('-').concat(#uuidFormHistory)")
	@Override
	public MsQuestionrelevant retrieveQuestionRelevantQset(long uuidQuestion,
			long uuidMsSubsystem, long uuidFormHistory, AuditContext callerId) {
		if (debugCache)
			this.logger.trace("-------------------------nocache-------------------------");
		MsFormquestionset qSet = this.getManagerDAO().selectOne(
				"from MsFormquestionset qs join fetch qs.msQuestion q join fetch qs.amMssubsystem s join fetch qs.msFormhistory f join fetch qs.msAnswertype a where qs.refId= :refId and s.uuidMsSubsystem = :uuidMsSubsystem and f.uuidFormHistory = :uuidFormHistory and q.uuidQuestion = :uuidQuestion",
				new Object[][] {{"uuidQuestion", uuidQuestion}, {"uuidMsSubsystem", uuidMsSubsystem}, {"uuidFormHistory", uuidFormHistory}});

		MsQuestionrelevant result = this.getQuestionRelevantFromQset(new MsQuestionrelevant(), qSet, callerId);
		
		return result;
	}
	
	@Override
	public MsQuestion getQuestionFromQset(MsFormquestionset qSet, AuditContext callerId) {
		MsQuestion bean = null;
		if (qSet!=null){
			bean = new MsQuestion();
			bean.setUuidQuestion(qSet.getMsQuestion().getUuidQuestion());
			bean.setMsCollectiontag(qSet.getMsCollectiontag());
			bean.setMsAssettag(qSet.getMsAssettag());
			bean.setMsAnswertype(qSet.getMsAnswertype());
			bean.setMsOrdertag(qSet.getMsOrdertag());
			bean.setAmMssubsystem(qSet.getAmMssubsystem());
			bean.setIsActive("1");
			bean.setRefId(qSet.getRefId());
			bean.setQuestionLabel(qSet.getQuestionLabel());
			bean.setLovGroup(qSet.getLovGroup());
			bean.setIsVisible(qSet.getIsVisible());
			bean.setIsMandatory(qSet.getIsMandatory());
			bean.setIsReadonly(qSet.getIsReadonly());
			bean.setIsHolidayAllowed(qSet.getIsHolidayAllowed());
			bean.setMaxLength(qSet.getMaxLength());
			bean.setRegexPattern(qSet.getRegexPattern());
			bean.setImgQlt(qSet.getImgQlt());
		}
		return bean;
	}
	
	
	@Override
	public MsQuestionrelevant getQuestionRelevantFromQset(MsQuestionrelevant bean, MsFormquestionset qSet, AuditContext callerId) {
		if (qSet!=null){
			bean.setUuidQuestionRelevant(Long.valueOf(""));
			bean.setRelevant(qSet.getRelevant());
			bean.setCalculate(qSet.getCalculate());
			bean.setChoiceFilter(qSet.getChoiceFilter());
			bean.setQuestionValidation(qSet.getQuestionValidation());
			bean.setQuestionErrorMessage(qSet.getQuestionErrorMessage());
			bean.setQuestionValue(qSet.getQuestionValue());
			bean.setRelevantMandatory(qSet.getRelevantMandatory());
			
//			bean.setUuidQuestion(qSet.getUuidQuestion());
//			bean.setMsCollectiontag(qSet.getMsCollectiontag());
//			bean.setMsAssettag(qSet.getMsAssettag());
//			bean.setMsAnswertype(qSet.getMsAnswertype());
//			bean.setMsOrdertag(qSet.getMsOrdertag());
//			bean.setAmMssubsystem(qSet.getAmMssubsystem());
//			bean.setIsActive("1");
//			bean.setRefId(qSet.getRefId());
//			bean.setQuestionLabel(qSet.getQuestionLabel());
//			bean.setLovGroup(qSet.getLovGroup());
//			bean.setIsVisible(qSet.getIsVisible());
//			bean.setIsMandatory(qSet.getIsMandatory());
//			bean.setIsReadonly(qSet.getIsReadonly());
//			bean.setIsHolidayAllowed(qSet.getIsHolidayAllowed());
//			bean.setMaxLength(qSet.getMaxLength());
//			bean.setRegexPattern(qSet.getRegexPattern());
//			bean.setImgQlt(qSet.getImgQlt());
		}
		return bean;
	}
	
	
	@Cacheable(value = "scheduler.cache", key = "'-QSTN-'.concat(#uuidQuestion)")
	@Override
	public MsQuestion retrieveQuestionByUuid(long uuidQuestion, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		MsQuestion result = this.getManagerDAO().selectOne("from MsQuestion q join fetch q.msAnswertype a left join fetch q.msOrdertag o where q.uuidQuestion = :uuidQuestion", 
				new Object[][] {{"uuidQuestion", uuidQuestion}});
		return result;
	}

	@Cacheable(value = "scheduler.cache", key = "'-STTS-'.concat(#uuidSubsystem).concat('-').concat(#statusCode)")
	@Override
	public MsStatustask retrieveStatusTask(String statusCode,
			long uuidSubsystem, AuditContext callerId) {
		Object[][] params2 = {
				{ Restrictions.eq("statusCode", statusCode) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
						uuidSubsystem) } };
		if (debugCache)
			this.logger
					.trace("-------------------------nocache-------------------------");
		return this.getManagerDAO().selectOne(MsStatustask.class, params2);
	}
	
	@Cacheable(value = "scheduler.cache", key = "'-STM-'.concat('-').concat(#mobileStatusCode)")
	@Override
	public MsStatusmobile retrieveStatusMobile(String mobileStatusCode, AuditContext callerId) {
		Object[][] params = {{Restrictions.eq("statusMobileCode", mobileStatusCode)}};
		if (debugCache)
			this.logger.trace("-------------------------nocache-------------------------");
		return this.getManagerDAO().selectOne(MsStatusmobile.class, params);
	}

//	@Cacheable(value = "scheduler.cache", key = "'-GS-'.concat(#gsCode)")
	@Transactional
	@Override
	public AmGeneralsetting retrieveGs(String gsCode, AuditContext callerId) {
		Object[][] paramGeneralSetting = { { Restrictions.eq("gsCode", gsCode) } };
//		if (debugCache)
//			this.logger
//					.trace("-------------------------nocache-------------------------");
		return this.getManagerDAO().selectOne(AmGeneralsetting.class,
				paramGeneralSetting);
	}

	@Cacheable(value = "scheduler.cache", key = "'-TDOM-'.concat(#uuidMsSubsystem)")
	@Override
	public MsTskdistributionofmodule retrieveTaskDistributionMode(
			String uuidMsSubsystem, AuditContext callerId) {
		MsTskdistributionofmodule result = this.getManagerDAO().selectOne(
				"from MsTskdistributionofmodule mt join fetch mt.amMssubsystem ms join fetch mt.msTaskdistribution md "
				+ "where ms.uuidMsSubsystem = :uuidMsSubsystem", new Object[][] {{"uuidMsSubsystem", Long.valueOf(uuidMsSubsystem)}});
		if (result == null)
			return null;

		result.getMsTaskdistribution();
		if (debugCache)
			this.logger
					.trace("-------------------------nocache-------------------------");
		return result;
	}

	@Cacheable(value = "scheduler.cache", key = "'-CTAG-'.concat(#tagName)")
	@Override
	public MsCollectiontag retrieveCollectionTag(String tagName,
			AuditContext callerId) {
		Object[][] collTag = { { Restrictions.eq("tagName", tagName) } };
		if (debugCache)
			this.logger
					.trace("-------------------------nocache-------------------------");
		return this.getManagerDAO().selectOne(MsCollectiontag.class, collTag);
	}
		
	private MsQuestionrelevant retrieveQuestionRelevant(MsQuestion msQuestion, MsForm msForm, AuditContext callerId) {
		Object[][] params = { {Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())},
				{Restrictions.eq("msForm.uuidForm", msForm.getUuidForm())} };
		Map<String, Object> queryResult = this.getManagerDAO().list(MsQuestionrelevant.class, params, null);
		List<?> list = (List<?>) queryResult.get(GlobalKey.MAP_RESULT_LIST);
		if (list.isEmpty())
			return null;
		else
			return (MsQuestionrelevant) list.get(0);
	}
	
	@Override
	public MsQuestionrelevant retrieveQuestionRelevantQset(MsQuestion msQuestion, MsForm msForm, int formVersion, AuditContext callerId) {
		MsFormhistory bean = this.retrieveMsFormhistory(msForm.getUuidForm(), formVersion, callerId);
		Object[][] params = {
				{ Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())},
				{ Restrictions.eq("msFormhistory.uuidFormHistory", bean.getUuidFormHistory())} };
		MsFormquestionset qSet = this.getManagerDAO().selectOne(MsFormquestionset.class, params);

		if (qSet == null) {
			return null;
		}
		else {
			MsQuestionrelevant qRelevant = retrieveQuestionRelevant(msQuestion, msForm, callerId);
			if (qRelevant == null) {
				return null;
			} else {
				if (StringUtils.isNotBlank(qSet.getRelevant()) || StringUtils.isNotBlank(qSet.getChoiceFilter())
						||StringUtils.isNotBlank(qSet.getQuestionValidation()) || StringUtils.isNotBlank(qSet.getQuestionErrorMessage())
						||StringUtils.isNotBlank(qSet.getQuestionValue())||StringUtils.isNotBlank(qSet.getCalculate())){
					qRelevant.setRelevant(qSet.getRelevant());
					qRelevant.setQuestionValidation(qSet.getQuestionValidation());
					qRelevant.setQuestionValue(qSet.getQuestionValue());
					qRelevant.setQuestionErrorMessage(qSet.getQuestionErrorMessage());
					qRelevant.setChoiceFilter(qSet.getChoiceFilter());
					qRelevant.setCalculate(qSet.getCalculate());
				} else {
					return null;
				}
			}
			return qRelevant;
		}
	}


	@Transactional
	@Override
	public void insertPercentageBattery(long uuidMsUser,
			long uuidMsSubsystem, String precentageBattery, AuditContext callerId) {

		AmMsattribute attrLastPrecentageBattery = this.retrieveAttribute(
				GlobalKey.MSATTR_LAST_PERCENTAGE_BATTERY, uuidMsSubsystem);

		AmAttributeofmember model = this.retrieveMembersAttribute(
				attrLastPrecentageBattery, uuidMsUser);
		boolean exists = true;
		if (model == null) {
			model = new AmAttributeofmember();
			exists = false;
		}

		model.setAttributeValue(precentageBattery);

		if (exists) {
			model.setUsrUpd(callerId.getCallerId());
			model.setDtmUpd(new Date());

			this.getManagerDAO().update(model);
		} else {
			model.setUsrCrt(callerId.getCallerId());
			model.setDtmCrt(new Date());
			AmMsuser user = new AmMsuser();
			user.setUuidMsUser(Long.valueOf(uuidMsUser));
			model.setAmMsuser(user);
			model.setAmMsattribute(attrLastPrecentageBattery);

			this.getManagerDAO().insert(model);
		}
	}

	@Cacheable(value = "scheduler.cache", key = "#root.methodName.concat(#startCode)")
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<String> retrieveWfNextStatusCodes(long uuidProcess, String startCode) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		
		Object[][] nativeParams = {{"uuidProcess", uuidProcess}, {"startCode", startCode}};
		List<Map<String, Object>> queryResult = this.getManagerDAO().selectAllNative("common.retrieveWfNextStatusCodes", nativeParams, null);
		if (queryResult == null || queryResult.isEmpty())
			return Collections.emptyList();
		
		List<String> resultList = new ArrayList<>(queryResult.size());
		for (Iterator<Map<String, Object>> iterator = queryResult.iterator(); iterator.hasNext();) {
			Map<String, Object> record = iterator.next();
			resultList.add((String) record.get("d0"));
		}
		
		return resultList;
	}

	@Cacheable(value = "scheduler.cache", key = "'-FORMHISTORY-'.concat(#uuidForm).concat('-').concat(#formVersion)")
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public MsFormhistory retrieveMsFormhistory(long uuidForm, int formVersion, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		
		MsFormhistory history = this.getManagerDAO().selectOne(MsFormhistory.class,
				new Object[][]{ {Restrictions.eq("msForm.uuidForm", uuidForm)}, {Restrictions.eq("formVersion", formVersion)}});
		
		return history;
	}
		
	@Cacheable(value = "scheduler.cache", key = "'-FORMQUESTIONSETBYHISTORY-'.concat(#uuidFormHistory)")
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<MsFormquestionset> retrieveMsFormquestionset(long uuidFormHistory, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("uuidFormHistory", uuidFormHistory);
		
		Map<String, Object> resultMap = this.getManagerDAO().list(
				"from MsFormquestionset qs join fetch qs.msQuestiongroup join fetch qs.msQuestion q " +
						" join fetch q.msAnswertype where qs.msFormhistory.uuidFormHistory = :uuidFormHistory" +
						" order by qs.questionGroupOfFormSeq, qs.questionOfGroupSeq", paramMap);
		List<MsFormquestionset> resultList = (List<MsFormquestionset>) resultMap.get(GlobalKey.MAP_RESULT_LIST);
		return resultList;
	}

	@Cacheable(value = "scheduler.cache", key = "'-FORMQUESTIONSET-'.concat(#uuidForm).concat('-').concat(#formVersion)")
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<MsFormquestionset> retrieveMsFormquestionset(long uuidForm, int formVersion, AuditContext callerId) {
		if (debugCache) {
			this.logger.trace("-------------------------nocache-------------------------");
		}
		
		MsFormhistory history = this.retrieveMsFormhistory(uuidForm, formVersion, callerId);
		
		if (history == null)
			return Collections.emptyList();
		
		return this.retrieveMsFormquestionset(history.getUuidFormHistory(), callerId);
	}
		
	@Cacheable(value = "scheduler.cache", key = "'-STAG-'.concat(#assetName)")
	@Override
	public MsAssettag retrieveAssetTag(String assetName){
		Object[][] paramAssetTagName = { { Restrictions.eq("assetTagName", assetName) } };
		return this.getManagerDAO().selectOne(MsAssettag.class, paramAssetTagName);
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public TaskDocumentBean retrieveJsonDocument(long uuidTaskH, boolean isFinal, AuditContext callerId) {
		Gson gson = null;
		
		if (isFinal) {
			FinalTrTaskdocument ttd = this.getManagerDAO().selectOne(
					"from FinalTrTaskdocument doc join fetch doc.finalTrTaskH where doc.uuidTaskId = :uuidTaskId",
					new Object[][]{{"uuidTaskId", uuidTaskH}});
			if (ttd == null || StringUtils.isBlank(ttd.getDocument())) {
				return null;
			}
			else {
				gson = new Gson();
				return gson.fromJson(ttd.getDocument(), TaskDocumentBean.class);
			}
		}
		else {
			TrTaskdocument ttd = this.getManagerDAO().selectOne(
					"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
					new Object[][]{{"uuidTaskId", uuidTaskH}});
			if (ttd == null || StringUtils.isBlank(ttd.getDocument())) {
				return null;
			}
			else {
				gson = new Gson();
				return gson.fromJson(ttd.getDocument(), TaskDocumentBean.class);
			}
		}
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<String> listAnswersFromJsonForDownloadResult(long uuidTaskH, String baseUrlImage,
			boolean isLoadIntVal, AuditContext callerId) {
		if (uuidTaskH < 1)
			return Collections.emptyList();
		
		boolean isFinal = false;
		TaskDocumentBean document = this.retrieveJsonDocument(uuidTaskH, isFinal, callerId);
		
		if (document == null) {
			isFinal = true;
			document = this.retrieveJsonDocument(uuidTaskH, isFinal, callerId);
		}
		
		if (document == null) {
			return Collections.emptyList();
		}
		
		long uuidFormHistory = this.getUuidFormHistory(uuidTaskH);
		List<MsFormquestionset> questionset = this.retrieveMsFormquestionset(uuidFormHistory, callerId);
		if (questionset == null || questionset.isEmpty())
			return Collections.emptyList();
		
		List<String> resultList = this.matchQuestionsetWithAnswersForDownloadResult(
				questionset, document, baseUrlImage, isFinal, isLoadIntVal, callerId);
		return resultList;
	}

	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<TrTaskBean> listAnswersFromJson(long uuidTaskH, boolean isVerified, boolean isFinal, boolean isProcessAssetSurvey, AuditContext callerId) {
		if (uuidTaskH < 1)
			return Collections.emptyList();
		
		TaskDocumentBean document = this.retrieveJsonDocument(uuidTaskH, isFinal, callerId);
		if (document == null)
			return Collections.emptyList();
		
		long uuidFormHistory = this.getUuidFormHistory(uuidTaskH);
		List<MsFormquestionset> questionset = this.retrieveMsFormquestionset(uuidFormHistory, callerId);
		if (questionset == null || questionset.isEmpty())
			return Collections.emptyList();
		
		List<TrTaskBean> resultList = this.matchQuestionsetWithAnswers(questionset, document, isFinal, callerId);
		
		if (isProcessAssetSurvey) {
			Map<String, Object> branchLocation = this.getBranchCoordinate(uuidTaskH);
			String latitude = (branchLocation.get("latitude") == null) ? null : branchLocation.get("latitude").toString();
			String longitude = (branchLocation.get("longitude") == null) ? null : branchLocation.get("longitude").toString();
			for (TrTaskBean detail : resultList) {
				detail.setLatBranch(latitude);
				detail.setLngBranch(longitude);
				
				if ("1".equals(detail.getIsAsset()) && detail.getLatBranch() != null && detail.getLngBranch() != null
						&& detail.getLatitude() != null && detail.getLongitude() != null) {					
					double distance = DistanceUtils.getDistanceInKm(
							detail.getLatitude().doubleValue(), detail.getLongitude().doubleValue(), 
							NumberUtils.toDouble(detail.getLatBranch()), NumberUtils.toDouble(detail.getLngBranch()));
					NumberFormat nf = DecimalFormat.getInstance(Locale.US);
					String dtc = nf.format(distance);
					
					detail.setDistance(dtc);
				}
			}
		}
		
		if (isVerified) {
			for (Iterator<TrTaskBean> iterator = resultList.iterator(); iterator.hasNext();) {
				TrTaskBean bean = iterator.next();
								
				bean.setTextAnswer(bean.getFinTextAnswer());
				bean.setMsLovByLovId(bean.getMsLovByFinLovId());
				bean.setOptionText(bean.getFinOptionText());
			}
		}
		
		return resultList;
	}
	
	private List<String> matchQuestionsetWithAnswersForDownloadResult(List<MsFormquestionset> questionset,
			TaskDocumentBean document, String baseUrlImage,
			boolean isFinal, boolean isLoadIntVal, AuditContext auditContext) {
		if (questionset == null || questionset.isEmpty())
			return Collections.emptyList();
		
		List<String> resultList = new ArrayList<>();
		for (Iterator<MsFormquestionset> iterator = questionset.iterator(); iterator.hasNext();) {
			MsFormquestionset question = iterator.next();
			
			int idxQ = document.findAnswerIndex(question.getMsQuestion().getUuidQuestion());
			String refId = question.getRefId();
			if (idxQ == -1) {
				if(refId.contains("COL_JMLKTR_BDEB")) {
					for(int i=0;i<5;i++) {
						resultList.add("");						
					}
				}else {
					resultList.add("");
				}
				continue;
			}
			
			AnswerBean answerBean = document.getAnswers().get(idxQ);
			String cellValue = StringUtils.EMPTY;
			String answerType = answerBean.getQuestion().getAnswerTypeCode();
			
			if (answerBean.getLobAnswer() != null && answerBean.getLobAnswer().isHasImage()) {
				String queryParam = (isFinal) ? ReportDownloadLogic.QUERY_PARAM_FINAL : StringUtils.EMPTY;				
				cellValue = baseUrlImage + answerBean.getLobAnswer().getId() + queryParam;
			}
			else if (MssTool.isImageQuestion(answerType) && null == answerBean.getLobAnswer()) {
				cellValue = StringUtils.EMPTY;
			}
			else if (null != answerBean.getLocation() && null != answerBean.getLocation().getLat()
					&& null != answerBean.getLocation().getLng()) {
				LocationBean location = answerBean.getLocation();
				String latitude = location.getLat().toString();
				String longitude = location.getLng().toString();
				String acc = (location.getAccuracy() == null) ? "0" : location.getAccuracy().toString();
				cellValue = StringUtils.join(latitude, ", ", longitude, " accuracy: ", acc, "m");				
			}
			else {
				String txtAnswer = StringUtils.EMPTY;
				List<OptionBean> options = null;
				
				// check if get INT
				if (isLoadIntVal) {
					txtAnswer = answerBean.getIntTxtAnswer();
					options = answerBean.getIntOptAnswers();
				}
				else {
					txtAnswer = answerBean.getTxtAnswer();
					options = answerBean.getOptAnswers();
				}

				if (MssTool.isMultipleQuestion(answerType)) {
					if (options != null) {
						int j=0;
						for (Iterator<OptionBean> iterOpt = options.iterator(); iterOpt.hasNext();) {
							OptionBean option = iterOpt.next();
							if (j > 0) {
								cellValue += ";";
							}
							cellValue += option.getCode() + " | " + option.getDesc();
							if (StringUtils.isNotBlank(option.getFreeText())) {
								cellValue += " - " + option.getFreeText();
							}
							
							j++;
						}
					}
				}
				else if (MssTool.isChoiceQuestion(answerType) && !MssTool.isMultipleQuestion(answerType)) { //dropdown, radio
					if (GlobalVal.COL_TAG_RVNUMBER.equals(answerBean.getQuestion().getTagCollName())
							&& options != null && !options.isEmpty()) {
						cellValue = options.get(0).getDesc();
					}
					else if (options != null && !options.isEmpty()) {						
						OptionBean option = options.get(0);
						
						cellValue += option.getCode() + " | " + option.getDesc();
						if (StringUtils.isNotBlank(option.getFreeText())) {
							cellValue += " - " + option.getFreeText();
						}
					}
				}
				else if (GlobalVal.ANSWER_TYPE_TIME.equals(answerType)) {
					cellValue = changeDateFormat(txtAnswer, "HH:mm:ss", ReportDownloadLogic.DATE_FORMAT_TM);
				}
				else if (GlobalVal.ANSWER_TYPE_DATETIME.equals(answerType)) {
					cellValue = changeDateFormat(txtAnswer, "dd/MM/yyyy HH:mm:ss", ReportDownloadLogic.DATE_FORMAT_DTM);
				}
				else if (GlobalVal.ANSWER_TYPE_DATE.equals(answerType)) {
					cellValue = changeDateFormat(txtAnswer, "dd/MM/yyyy", ReportDownloadLogic.DATE_FORMAT_DT);
				}
				else if (GlobalVal.ANSWER_TYPE_TEXT_MULTILINE.equals(answerType)) {
					String[] temp = StringUtils.split(txtAnswer, "\n");
					cellValue = StringUtils.join(temp, " ");
				}
				else {
					cellValue = txtAnswer;
				}
			}

			if (null == cellValue) {
				cellValue = StringUtils.EMPTY;
			}
			
			if(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE_SEPERATE.equals(answerType)) {
				if(refId.contains("COL_JMLKTR_BDEB")) {
					String[] tempValue = StringUtils.split(cellValue, "\n");
					int len = tempValue.length;
					for (int i = 0; i < 5; i++) {
						resultList.add(i < len ? tempValue[i] : StringUtils.EMPTY);
					}					
				}
			}else {
				cellValue = cellValue.replace("\n", " ");
				resultList.add(cellValue);				
			}
			
			answerBean = null;
		}
		
		return resultList;
	}
	
	private List<TrTaskBean> matchQuestionsetWithAnswers(List<MsFormquestionset> questionset, TaskDocumentBean document,
			boolean isFinal, AuditContext auditContext) {
		if (questionset == null || questionset.isEmpty())
			return Collections.emptyList();
		
		List<TrTaskBean> resultList = new ArrayList<>();
		String[] assetsSurvey = {GlobalVal.ASSET_TAG_HOME, GlobalVal.ASSET_TAG_OFFICE, GlobalVal.ASSET_TAG_IDENTITY};

		NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
		for (Iterator<MsFormquestionset> iterator = questionset.iterator(); iterator.hasNext();) {
			MsFormquestionset question = iterator.next();
			
			int idxQ = document.findAnswerIndex(question.getMsQuestion().getUuidQuestion());
			if (idxQ == -1) {
				continue; //hanya menampilkan question yang dijawab (relevant)
			}
			
			AnswerBean answer = document.getAnswers().get(idxQ);
			String answerType = answer.getQuestion().getAnswerTypeCode();
			
			MsQuestion msQuestion = new MsQuestion();
			MsAnswertype msAnswertype = new MsAnswertype();
			msAnswertype.setCodeAnswerType(answerType);
			msQuestion.setMsAnswertype(msAnswertype);
			
			TrTaskBean detail = new TrTaskBean();
			detail.setQuestionText(answer.getQuestion().getLabel());
			detail.setMsQuestion(msQuestion);
			detail.setIsAsset((ArrayUtils.contains(assetsSurvey, answer.getQuestion().getTagAssetName())) ? "1" : "0");
			detail.setFlagSource((isFinal) ? "2" : "1");
			
			String intTextAnswer = answer.getIntTxtAnswer();
			String textAnswer = answer.getTxtAnswer();
			String finTextAnswer = answer.getFinTxtAnswer();
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType)) {
				if (StringUtils.isNotBlank(intTextAnswer))
					intTextAnswer = "Rp " + formatKurs.format(NumberUtils.toDouble(intTextAnswer));
				if (StringUtils.isNotBlank(textAnswer))
					textAnswer = "Rp " + formatKurs.format(NumberUtils.toDouble(textAnswer));
				if (StringUtils.isNotBlank(finTextAnswer))
					finTextAnswer = "Rp " + formatKurs.format(NumberUtils.toDouble(finTextAnswer));
			}
			
			detail.setIntTextAnswer(intTextAnswer);
			detail.setTextAnswer(textAnswer);
			detail.setFinTextAnswer(finTextAnswer);
			
			ImageBean image = answer.getLobAnswer();
			if ((MssTool.isImageQuestion(answerType) && image == null)
					|| (image != null && !image.isHasImage())) {
				detail.setHasImage("0");
			}
			else if (image != null && image.isHasImage()) {
				detail.setHasImage("1");
				detail.setLob(String.valueOf(image.getId()));
			}
			
			LocationBean location = answer.getLocation();
			if (location != null) {
				detail.setLat((location.getLat() == null) ? null : location.getLat().toString());
				detail.setLatitude((location.getLat() == null) ? null : new BigDecimal(location.getLat().doubleValue()));
				detail.setLng((location.getLng() == null) ? null : location.getLng().toString());
				detail.setLongitude((location.getLng() == null) ? null : new BigDecimal(location.getLng().doubleValue()));
				detail.setAccuracy(location.getAccuracy());
								
				if (null == detail.getLat() && null == detail.getLng() &&
						0 != answer.getLocation().getCid() && 0 != answer.getLocation().getLac() &&
						0 != answer.getLocation().getMnc() && 0 != answer.getLocation().getMcc()) {
					List<com.adins.framework.tool.geolocation.model.LocationBean> listLocations = new ArrayList<>();
					com.adins.framework.tool.geolocation.model.LocationBean locationBean = new com.adins.framework.tool.geolocation.model.LocationBean();
					locationBean.setCellid(answer.getLocation().getCid());
					locationBean.setLac(answer.getLocation().getLac());
					locationBean.setMnc(answer.getLocation().getMnc());
					locationBean.setMcc(answer.getLocation().getMcc());
					listLocations.add(locationBean);
					this.geocoder.geocodeCellId(listLocations, auditContext);
					
					if (locationBean.getCoordinate() != null) {
						detail.setLat(String.valueOf(locationBean.getCoordinate().getLatitude()));
						detail.setLatitude(new BigDecimal(locationBean.getCoordinate().getLatitude()));
						detail.setLng(String.valueOf(locationBean.getCoordinate().getLongitude()));
						detail.setLongitude(new BigDecimal(locationBean.getCoordinate().getLongitude()));
					}
				}
				
				if (detail.getLatitude() != null && image == null) {
					detail.setHasImage("01");
				}
			}
			
			if (answer.getIntOptAnswers() != null && answer.getIntOptAnswers().size() > 0
					&& !MssTool.isMultipleQuestion(answerType)) {
				OptionBean option = answer.getIntOptAnswers().get(0);
				detail.setIntOptionText(option.getDesc());
				detail.setIntTextAnswer(StringUtils.stripToEmpty(option.getFreeText())); //overwrite
			}
			
			if (answer.getOptAnswers() != null && answer.getOptAnswers().size() > 0
					&& !MssTool.isMultipleQuestion(answerType)) {
				OptionBean option = answer.getOptAnswers().get(0);
				detail.setOptionText(option.getDesc());
				detail.setTextAnswer(StringUtils.stripToEmpty(option.getFreeText())); //overwrite
			}
			
			if (answer.getFinOptAnswers() != null && answer.getFinOptAnswers().size() > 0
					&& !MssTool.isMultipleQuestion(answerType)) {				
				OptionBean option = answer.getFinOptAnswers().get(0);
				detail.setFinOptionText(option.getDesc());
				detail.setFinTextAnswer(StringUtils.stripToEmpty(option.getFreeText())); //overwrite				
			}
			
			if (MssTool.isMultipleQuestion(answerType)) {
				List<TrTaskBean> multipleDetails = this.multipleAnswerToList(detail,
						answer.getIntOptAnswers(), answer.getOptAnswers(), answer.getFinOptAnswers());
				resultList.addAll(multipleDetails);
			}
			else {
				resultList.add(detail);
			}
		}
		
		return resultList;
	}
	
	private List<TrTaskBean> multipleAnswerToList(TrTaskBean origin, List<OptionBean> intOptions,
			List<OptionBean> options, List<OptionBean> finOptions) {		
		if (intOptions == null)
			intOptions = Collections.emptyList();
		if (options == null)
			options = Collections.emptyList();
		if (finOptions == null)
			finOptions = Collections.emptyList();
		
		if (origin == null || (intOptions.isEmpty() && options.isEmpty() && finOptions.isEmpty()))
			return Collections.emptyList();
		
		int maxSize = NumberUtils.max(intOptions.size(), options.size(), finOptions.size());
		
		List<TrTaskBean> resultList = new ArrayList<>();
		
		for (int i = 0; i < maxSize; i++) {
			try {
				TrTaskBean clone = (TrTaskBean) BeanUtils.cloneBean(origin);
				
				if (intOptions.size() > i) {
					clone.setIntOptionText(intOptions.get(i).getDesc());
					clone.setIntTextAnswer(StringUtils.stripToEmpty(intOptions.get(i).getFreeText()));
				}
				
				if (options.size() > i) {
					clone.setOptionText(options.get(i).getDesc());
					clone.setTextAnswer(StringUtils.stripToEmpty(options.get(i).getFreeText()));
				}
				
				if (finOptions.size() > i) {
					clone.setFinOptionText(finOptions.get(i).getDesc());
					clone.setFinTextAnswer(StringUtils.stripToEmpty(finOptions.get(i).getFreeText()));
				}
				
				clone.setMsQuestion(origin.getMsQuestion());
				
				resultList.add(clone);
			}
			catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
		
		return resultList;
	}
	
	private long getUuidFormHistory(long uuidTaskH) {
		Object uuidFormHistory = this.getManagerDAO().selectOneNative("common.getUuidFormHistory",
				new Object[][]{{"uuidTaskH", uuidTaskH}});
		if (uuidFormHistory == null)
			return 0L;
		
		return ((Long) uuidFormHistory).longValue();
	}
	
	private Map<String, Object> getBranchCoordinate(long uuidTaskH) {
		Map<String, Object> result = this.getManagerDAO().selectForMap("common.getBranchCoordinate", new Object[][]{{"uuidTaskH", uuidTaskH}});
		if (result == null)
			return Collections.emptyMap();
		
		return result;
	}

	@Transactional(readOnly=true)
	@Override
	public List<TrTaskBean> listAnswersInTextFromJson(long uuidTaskH, boolean lazyloadImage, boolean isVerified, boolean isFinal, AuditContext callerId) {
		List<TrTaskBean> resultList = this.listAnswersFromJson(uuidTaskH, isVerified, isFinal, false, callerId);
		
		ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);
				
		for (Iterator<TrTaskBean> iterator = resultList.iterator(); iterator.hasNext();) {
			TrTaskBean bean = iterator.next();
			String answerType = bean.getMsQuestion().getMsAnswertype().getCodeAnswerType();
			
			if (isVerified && MssTool.isChoiceWithDescriptionQuestion(answerType)) {
				bean.setTextAnswer(bean.getFinOptionText() + "; " + bean.getFinTextAnswer());
			}
			else if (isVerified && MssTool.isChoiceWithoutDescriptionQuestion(answerType)) {
				bean.setTextAnswer(bean.getFinOptionText());
			}
			else if (!isVerified && MssTool.isChoiceWithDescriptionQuestion(answerType)) {
				bean.setTextAnswer(bean.getOptionText() + "; " + bean.getTextAnswer());
			}
			else if (!isVerified && MssTool.isChoiceWithoutDescriptionQuestion(answerType)) {
				bean.setTextAnswer(bean.getOptionText());
			}
			else if (!lazyloadImage && MssTool.isImageQuestion(answerType) && "1".equals(bean.getHasImage())) {
				byte[] image = null; 
				if (ImageStorageLocation.DATABASE == saveImgLoc) {
					image = imageStorageLogic.retrieveImageBlob(Long.valueOf(bean.getLob()), isFinal);
				}
				else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
					image = imageStorageLogic.retrieveImageFileSystemByTaskD(Long.valueOf(bean.getLob()), isFinal);
				}
				
				if (image != null) {
					bean.setTextAnswer(BaseEncoding.base64().encode(image));
				}
			}
			else if (lazyloadImage && MssTool.isImageQuestion(answerType) && "1".equals(bean.getHasImage())) {
				bean.setTextAnswer(bean.getLob());
			}
			else if (isVerified) {
				bean.setTextAnswer(bean.getFinTextAnswer());
			}
			
			//empty data-for memory
			if (!isVerified) {
				bean.setFinOptionText(null);
				bean.setFinTextAnswer(null);
			}
		}
		
		return resultList;
	}
	
	private String changeDateFormat(String date, String fromDateFormat, String toDateformat) {
		if (StringUtils.isBlank(date)) {
			return null;
		}
		String newdate = StringUtils.EMPTY;
		DateFormat df = new SimpleDateFormat(fromDateFormat);
		
		try {
			Date result = df.parse(date);
			newdate = (null != result) ? new SimpleDateFormat(toDateformat).format(result) : "-";
		} 
		catch (ParseException e) {
			e.printStackTrace();
		} 
		return newdate;
	}

	@Transactional(readOnly=true)
	@Override
	public String dynamicChangeDateFormatterByGenset(String date, String format, AuditContext callerId) {
		String formattedDate = StringUtils.EMPTY;
		if (StringUtils.isNotBlank(date)) {
			formattedDate = this.dynamicDateFormatterByGenset(date, format);
		}
		return formattedDate;
	}
}
