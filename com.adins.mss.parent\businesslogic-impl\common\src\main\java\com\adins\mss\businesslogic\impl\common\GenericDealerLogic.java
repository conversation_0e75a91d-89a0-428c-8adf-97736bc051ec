package com.adins.mss.businesslogic.impl.common;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.DealerLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsDealer;
import com.adins.mss.model.MsDealerofbranch;
import com.adins.mss.model.MsLov;

@SuppressWarnings({"rawtypes"})
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericDealerLogic extends BaseLogic implements DealerLogic, MessageSourceAware {
	private AuditInfo auditInfoD;
	private AuditInfo auditInfoDoB;
	private AuditInfo auditInfo;
	
	@Autowired
	private MessageSource messageSource;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	public GenericDealerLogic() {
		String[] pkColsD = { "uuidDealer" };
		String[] pkDbColsD = { "UUID_DEALER" };
		String[] colsD = { "uuidDealer", "dealerName", "dealerAddress",
				"orderTarget", "msDealer.uuidDealer", "dealerCode" };
		String[] dbColsD = { "UUID_DEALER", "DEALER_NAME", "DEALER_ADDRESS",
				"ORDER_TARGET", "PARENT_ID", "DEALER_CODE" };
		this.auditInfoD = new AuditInfo("MS_DEALER", pkColsD, pkDbColsD, colsD,
				dbColsD);

		String[] pkColsDoB = { "uuidDealerOfBranch" };
		String[] pkDbColsDoB = { "UUID_DEALER_OF_BRANCH" };
		String[] colsDoB = { "uuidDealerOfBranch", "msBranch.uuidBranch",
				"msDealer.uuidDealer" };
		String[] dbColsDoB = { "UUID_DEALER_OF_BRANCH", "UUID_BRANCH",
				"UUID_DEALER" };
		this.auditInfoDoB = new AuditInfo("MS_DEALEROFBRANCH", pkColsDoB,
				pkDbColsDoB, colsDoB, dbColsDoB);

		String[] pkCols = { "uuidLov" };
		String[] pkDbCols = { "UUID_LOV" };
		String[] cols = { "uuidLov", "isActive", "isDeleted", "lovGroup",
				"code", "description", "sequence", "constraint1",
				"constraint2", "constraint3", "constraint4", "constraint5" };
		String[] dbCols = { "UUID_LOV", "IS_ACTIVE", "IS_DELETED", "LOV_GROUP",
				"CODE", "DESCRIPTION", "SEQUENCE", "CONSTRAINT_1",
				"CONSTRAINT_2", "CONSTRAINT_3", "CONSTRAINT_4", "CONSTRAINT_5" };
		this.auditInfo = new AuditInfo("MS_LOV", pkCols, pkDbCols, cols, dbCols);
	}

	@Override
	public List listDealer(Object params, AuditContext callerId) {
		List result = null;
		result = this.getManagerDAO().selectAllNative("setting.dealer.listDealer", params, null);
		return result;
	}

	@Override
	public Integer countListDealer(Object params, AuditContext callerId) {
		Integer result;
		result = (Integer) this.getManagerDAO().selectOneNative(
				"setting.dealer.cntDealer", params);
		return result;
	}

	@Override
	public MsDealer getDealer(long uuid, AuditContext callerId) {
		MsDealer result = null;
		result = this.getManagerDAO().selectOne("from MsDealer md "
						+ "left join fetch md.msDealer md2 "
						+ "where md.uuidDealer = :uuidDealer", 
						new Object[][] { { "uuidDealer", uuid } });
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateDealer(MsDealer obj, AuditContext callerId) {

		MsDealer dbModel = this.getManagerDAO().selectOne(MsDealer.class,
				obj.getUuidDealer());
		dbModel.setDealerCode(obj.getDealerCode());
		dbModel.setDealerName(obj.getDealerName());
		dbModel.setDealerAddress(obj.getDealerAddress());
		dbModel.setOrderTarget(obj.getOrderTarget());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());
		this.auditManager.auditEdit(dbModel, auditInfoD,
				callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
	}

	private boolean validateCode(Object params) {
		Map<String, Object> result = this.getManagerDAO().count(MsDealer.class,
				params);
		if ((Long) result.get(GlobalKey.MAP_RESULT_SIZE) > 0) {
			return false;
		} 
		else {
			return true;
		}
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void insertDealer(MsDealer obj, String uuidParent,
			AuditContext callerId) {

		Object[][] params = { { Restrictions.eq("dealerCode",
				obj.getDealerCode()) } };
		if (this.validateCode(params)) {
			MsDealer parent = null;
			if (uuidParent == null || "".equals(uuidParent)) {
				Object[][] param = { { Restrictions.eq("dealerName",
						GlobalVal.DEALER_HO) } };// default uuid parent dealer HO
				parent = this.getManagerDAO().selectOne(MsDealer.class, param);
			} 
			else {
				parent = this.getManagerDAO().selectOne(MsDealer.class,
						Long.valueOf(uuidParent));
			}
			obj.setMsDealer(parent);
			obj.setIsActive("1");
			obj.setDtmCrt(new Date());
			obj.setUsrCrt(callerId.getCallerId());

			this.getManagerDAO().insert(obj);
			this.auditManager.auditAdd(obj, auditInfoD, callerId.getCallerId(),	"");

			// insert into dealerofbranch default ho branch
			// insert into ms_lov default ho branch
			insertDealerOfBranchDefaultHo(obj, callerId);
		} 
		else {
			Object[] object = { obj.getDealerCode() };
			throw new EntityNotUniqueException((this.messageSource.getMessage(
					"businesslogic.dealer.dealercodeexist", object, this.retrieveLocaleAudit(callerId))),
					obj.getDealerCode());
		}
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	public void insertDealerOfBranchDefaultHo(MsDealer msDealer,
			AuditContext callerId) {

		Object[][] param = { { Restrictions.eq("branchName",
				GlobalVal.BRANCH_HO) } };
		MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class,
				param);

		MsDealerofbranch dealerOfBranch = new MsDealerofbranch();

		dealerOfBranch.setUsrCrt(callerId.getCallerId());
		dealerOfBranch.setDtmCrt(new Date());
		dealerOfBranch.setMsBranch(msBranch);
		dealerOfBranch.setMsDealer(msDealer);
		this.getManagerDAO().insert(dealerOfBranch);
		this.auditManager.auditAdd(dealerOfBranch, auditInfoDoB,
				callerId.getCallerId(), "");

		MsLov msl = new MsLov();
		msl.setLovGroup(GlobalVal.LOV_TAG_DEALER_OF_BRANCH);
		msl.setCode(String.valueOf(msBranch.getUuidBranch()));
		msl.setDescription(msBranch.getBranchName());
		msl.setSequence(1);
		msl.setIsActive("1");
		msl.setIsDeleted("0");
		msl.setDtmCrt(new Date());
		msl.setUsrCrt(callerId.getCallerId().toString());

		msl.setConstraint1(String.valueOf(msDealer.getUuidDealer()));
		msl.setConstraint2(null);
		msl.setConstraint3(null);
		msl.setConstraint4(null);
		msl.setConstraint5(null);

		this.getManagerDAO().insert(msl);
		// BRANCH OF DEALER
		MsLov msl2 = new MsLov();
		msl2.setLovGroup(GlobalVal.LOV_TAG_BRANCH_OF_DEALER);
		msl2.setCode(String.valueOf(msDealer.getUuidDealer()));
		msl2.setDescription(msDealer.getDealerName());
		msl2.setSequence(getSeqMsLovDealerOfBranch(String.valueOf(msDealer
				.getUuidDealer())));
		msl2.setIsActive("1");
		msl2.setIsDeleted("0");
		msl2.setDtmCrt(new Date());
		msl2.setUsrCrt(callerId.getCallerId().toString());

		msl2.setConstraint1(String.valueOf(msBranch.getUuidBranch()));
		msl2.setConstraint2(null);
		msl2.setConstraint3(null);
		msl2.setConstraint4(null);
		msl2.setConstraint5(null);
		this.getManagerDAO().insert(msl2);
	}

	@Override
	public Map<String, Object> listMemberOfDealer(String uuidDealer,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();

		condition.append(" and mdob.msDealer.uuidDealer=:uuidDealer");
		paramMap.put("uuidDealer", Long.valueOf(uuidDealer));

		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mdob.msBranch.branchCode asc, mdob.msBranch.branchName asc");

		result = this.getManagerDAO().selectAll(
						"from MsDealerofbranch mdob "
						+ "join fetch mdob.msBranch "
						+ "join fetch mdob.msDealer "
						+ "where 1=1"
								+ condition.toString() + orderQuery.toString(),
						"select count(*) "
						+ "from MsDealerofbranch mdob "
						+ "join mdob.msBranch join mdob.msDealer "
						+ "where 1=1"
								+ condition.toString(), paramMap, pageNumber, pageSize);
		return result;
	}

	private boolean hasChild(long uuid) {
		Object[][] params = { { Restrictions.eq("msDealer.uuidDealer", uuid) } };
		Map<String, Object> map = this.getManagerDAO().count(MsDealer.class,
				params);
		Map<String, Object> mapDOB = this.getManagerDAO().count(
				MsDealerofbranch.class, params);
		Map<String, Object> mapUser = this.getManagerDAO().count(
				AmMsuser.class, params);

		if ((Long) map.get(GlobalKey.MAP_RESULT_SIZE) > 0
				|| (Long) mapDOB.get(GlobalKey.MAP_RESULT_SIZE) > 0
				|| (Long) mapUser.get(GlobalKey.MAP_RESULT_SIZE) > 0) {
			return true;
		} 
		else {
			return false;
		}
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteDealer(long uuid, AuditContext callerId) {
		if (!this.hasChild(uuid)) {
			MsDealer obj = this.getManagerDAO().selectOne(MsDealer.class, uuid);
			this.auditManager.auditDelete(obj, auditInfoD, callerId.getCallerId(), "");
			this.getManagerDAO().delete(obj);
		} 
		else {
			throw new DatabaseException(
					DatabaseException.Reason.CONSTRAINT_VIOLATION,
					(this.messageSource.getMessage("businesslogic.error.haschild", 
					null, this.retrieveLocaleAudit(callerId))), new Exception());
		}
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteMemberOfDealer(long uuid, AuditContext callerId) {

		MsDealerofbranch obj = this.getManagerDAO().selectOne(
				MsDealerofbranch.class, uuid);
		Object[][] paramsDealerOfBranch = {
				{ Restrictions.eq("lovGroup",
						GlobalVal.LOV_TAG_DEALER_OF_BRANCH) },
				{ Restrictions.eq("code",
						String.valueOf(obj.getMsBranch().getUuidBranch())) },
				{ Restrictions.eq("constraint1",
						String.valueOf(obj.getMsDealer().getUuidDealer())) } };
		MsLov mslDealerOfBranch = this.getManagerDAO().selectOne(MsLov.class,
				paramsDealerOfBranch);
		Object[][] paramsBranchOfDealer = {
				{ Restrictions.eq("lovGroup",
						GlobalVal.LOV_TAG_BRANCH_OF_DEALER) },
				{ Restrictions.eq("code",
						String.valueOf(obj.getMsDealer().getUuidDealer())) },
				{ Restrictions.eq("constraint1",
						String.valueOf(obj.getMsBranch().getUuidBranch())) } };
		MsLov mslBranchOfDealer = this.getManagerDAO().selectOne(MsLov.class,
				paramsBranchOfDealer);
		this.auditManager.auditDelete(obj, auditInfoDoB,
				callerId.getCallerId(), "");
		mslDealerOfBranch.setIsActive("0");
		mslDealerOfBranch.setIsDeleted("1");
		mslDealerOfBranch.setDtmUpd(new Date());
		mslDealerOfBranch.setUsrUpd(callerId.getCallerId());
		this.auditManager.auditEdit(mslDealerOfBranch, auditInfo,
				callerId.getCallerId(), "");
		this.getManagerDAO().update(mslDealerOfBranch);
		mslBranchOfDealer.setIsActive("0");
		mslBranchOfDealer.setIsDeleted("1");
		mslBranchOfDealer.setDtmUpd(new Date());
		mslBranchOfDealer.setUsrUpd(callerId.getCallerId());
		this.auditManager.auditEdit(mslBranchOfDealer, auditInfo,
				callerId.getCallerId(), "");
		this.getManagerDAO().update(mslBranchOfDealer);
		this.getManagerDAO().delete(obj);
	}

	@Override
	public List listBranch(String[][] params, AuditContext callerId) {
		List result = null;
		result = this.getManagerDAO().selectAllNative(
				"setting.dealer.listBranch", params, null);
		return result;
	}

	@Override
	public Integer countListBranch(Object params, AuditContext callerId) {
		Integer result;
		result = (Integer) this.getManagerDAO().selectOneNative(
				"setting.dealer.cntBranch", params);
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void insertDealerOfBranch(long uuidDealer, String[] uuidBranches,
			AuditContext callerId) {

		MsDealer msDealer = this.getManagerDAO().selectOne(MsDealer.class,
				uuidDealer);

		for (int i = 0; i < uuidBranches.length; i++) {
			if (uuidBranches[i].isEmpty()) {
				continue;
			}

			MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class,
					Long.valueOf(uuidBranches[i]));

			MsDealerofbranch dealerOfBranch = new MsDealerofbranch();

			dealerOfBranch.setUsrCrt(callerId.getCallerId());
			dealerOfBranch.setDtmCrt(new Date());
			dealerOfBranch.setMsBranch(msBranch);
			dealerOfBranch.setMsDealer(msDealer);

			this.getManagerDAO().insert(dealerOfBranch);
			this.auditManager.auditAdd(dealerOfBranch, auditInfoDoB,
					callerId.getCallerId(), "");

			Object[][] paramsDealerOfBranch = {
					{ Restrictions.eq("lovGroup",
							GlobalVal.LOV_TAG_DEALER_OF_BRANCH) },
					{ Restrictions.eq("code",
							String.valueOf(msBranch.getUuidBranch())) },
					{ Restrictions.eq("constraint1",
							String.valueOf(msDealer.getUuidDealer())) } };
			MsLov mslDealerOfBranch = this.getManagerDAO().selectOne(
					MsLov.class, paramsDealerOfBranch);
			if (mslDealerOfBranch == null) {
				MsLov msl = new MsLov();
				msl.setLovGroup(GlobalVal.LOV_TAG_DEALER_OF_BRANCH);
				msl.setCode(String.valueOf(msBranch.getUuidBranch()));
				msl.setDescription(msBranch.getBranchName());
				msl.setSequence(getSeqMsLovDealerOfBranch(String
						.valueOf(msDealer.getUuidDealer())));
				msl.setIsActive("1");
				msl.setIsDeleted("0");
				msl.setDtmCrt(new Date());
				msl.setUsrCrt(callerId.getCallerId().toString());

				msl.setConstraint1(String.valueOf(msDealer.getUuidDealer()));
				msl.setConstraint2(null);
				msl.setConstraint3(null);
				msl.setConstraint4(null);
				msl.setConstraint5(null);
				this.getManagerDAO().insert(msl);
			} 
			else {
				mslDealerOfBranch.setIsActive("1");
				mslDealerOfBranch.setIsDeleted("0");
				mslDealerOfBranch.setDtmUpd(new Date());
				mslDealerOfBranch.setUsrUpd(callerId.getCallerId().toString());
				this.getManagerDAO().update(mslDealerOfBranch);
			}

			// Insert LOV BRANCHOFDEALER
			Object[][] paramsBranchOfDealer = {
					{ Restrictions.eq("lovGroup",
							GlobalVal.LOV_TAG_BRANCH_OF_DEALER) },
					{ Restrictions.eq("code",
							String.valueOf(msDealer.getUuidDealer())) },
					{ Restrictions.eq("constraint1",
							String.valueOf(msBranch.getUuidBranch())) } };
			MsLov mslBranchOfDealer = this.getManagerDAO().selectOne(
					MsLov.class, paramsBranchOfDealer);
			if (mslBranchOfDealer == null) {
				MsLov mslBOD = new MsLov();
				mslBOD.setLovGroup(GlobalVal.LOV_TAG_BRANCH_OF_DEALER);
				mslBOD.setCode(String.valueOf(msDealer.getUuidDealer()));
				mslBOD.setDescription(msDealer.getDealerName());
				mslBOD.setSequence(getSeqMsLovDealerOfBranch(String
						.valueOf(msDealer.getUuidDealer())));
				mslBOD.setIsActive("1");
				mslBOD.setIsDeleted("0");
				mslBOD.setDtmCrt(new Date());
				mslBOD.setUsrCrt(callerId.getCallerId().toString());

				mslBOD.setConstraint1(String.valueOf(msBranch.getUuidBranch()));
				mslBOD.setConstraint2(null);
				mslBOD.setConstraint3(null);
				mslBOD.setConstraint4(null);
				mslBOD.setConstraint5(null);

				this.getManagerDAO().insert(mslBOD);
			} 
			else {
				mslBranchOfDealer.setIsActive("1");
				mslBranchOfDealer.setIsDeleted("0");
				mslBranchOfDealer.setDtmUpd(new Date());
				mslBranchOfDealer.setUsrUpd(callerId.getCallerId().toString());
				this.getManagerDAO().update(mslBranchOfDealer);
			}
		}
	}

	public int getSeqMsLovDealerOfBranch(String uuidDealer) {
		Object[][] param = {
				{ Restrictions.eq("lovGroup",
						GlobalVal.LOV_TAG_DEALER_OF_BRANCH) },
				{ Restrictions.eq("constraint1", uuidDealer) } };
		Map<String, Object> map = this.getManagerDAO()
				.count(MsLov.class, param);
		Long result = (Long) map.get(GlobalKey.MAP_RESULT_SIZE);
		return result.intValue() + 1;
	}
}