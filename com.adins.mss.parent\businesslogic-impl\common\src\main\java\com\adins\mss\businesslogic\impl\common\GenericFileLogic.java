package com.adins.mss.businesslogic.impl.common;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Blob;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.FileLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsMobilePushsyncFiles;
import com.opencsv.CSVWriter;

@SuppressWarnings({"rawtypes"})
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericFileLogic extends BaseLogic implements FileLogic{
	private static final Logger LOG = LoggerFactory.getLogger(GenericFileLogic.class);
	
	private static final String URI_RULE_PATH = SpringPropertiesUtils.getProperty(GlobalKey.RULE_FILE_PATH);
	private static final String URI_PUSHSYNC_FILES_PATH = SpringPropertiesUtils.getProperty(GlobalKey.RULE_FILE_PATH)+SystemUtils.FILE_SEPARATOR+"pushsync";
	private static final String URI_PUSHSYNC_FILES = SpringPropertiesUtils.getProperty(GlobalKey.RULE_FILE_URI)+"/pushsync/";
	
	//template header for generate file csv for push sync
	private static final String[] TEMPLATE_HEADER_MS_ASSETSCHEME = { "ID", "ASSET_SCHEME_ID", "TYPE_CODE", "TYPE_NAME", "IS_DELETED", "DTM_UPD" };
	private static final String[] TEMPLATE_HEADER_MS_BLACKLIST = { "ID", "EXCLUDE_INFO1", "EXCLUDE_INFO2", "EXCLUDE_TYPE_CODE", "EXCLUDE_TYPE_NAME", "IS_DELETED", "DTM_UPD" };
	private static final String[] TEMPLATE_HEADER_MS_INDUSTRY = { "ID", "IS_DELETED", "TYPE_CODE", "MARGIN", "DTM_UPD" };
	private static final String[] TEMPLATE_HEADER_MS_MARKETPRICE = { "ID", "ASSET_CODE", "MANUFACTURING_YEAR", "TOLERANCE_PRCTG", "MARKET_PRICE", "EFFECTIVE_DATE", "IS_DELETED", "DTM_UPD", "OFFICE_CODE" };
	private static final String[] TEMPLATE_HEADER_MS_PO = { "ID", "ASSET_SCHEME_ID", "DEALER_SCHEME_ID", "BRANCH_ID", "PROD_OFF_ID", "PROD_OFF_NAME", "PROD_CAT_CODE", "PROD_CAT_NAME", 
			"JNS_PEMBIAYAAN", "MIN_TENOR", "MAX_TENOR", "SC_ID", "COMPONENT", "RULE_DATA_MIN_TDP", "RULE_DATA_MANF_YEAR", "RULE_ASSET_PRICE", "RULE_MIN_DP", "RULE_APP_TC", "RULE_SCORING", 
			"RULE_MATRIX", "IS_DELETED", "DTM_UPD"};
	private static final String[] TEMPLATE_HEADER_MS_PO_ASSET = { "ID", "IS_DELETED", "ASSET_SCHEME_ID", "BRAND_CODE", "BRAND_NAME", "MODEL_CODE", "MODEL_NAME", "GROUP_TYPE", "MASTER_CODE", "MASTER_NAME", "DTM_UPD" };
	private static final String[] TEMPLATE_HEADER_MS_PO_DEALER = { "ID", "DEALER_ID", "DEALER_SCHEME_ID", "DEALER_NAME", "DTM_UPD", "IS_DELETED"};
	
	@Autowired
	private CommonLogic commonLogic;
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
	private String formatDate = "yyyyMMddHHmmss";
	private String filePrefix = "PUSHSYNC_";
	
	@Override
	@Transactional
	public void GenerateRuleFileExcel(AuditContext auditContext) {
		Date date = new Date();
		
		String datetime = DateFormatUtils.format(date, "yyyy-MM-dd") + " 00:00:00.000";
		LOG.info("Begin export rule file, Time : {}", datetime);
		String[][] paramGetListUpdatePo = { {"isDeleted", "0"}, {"datetime", datetime} };
		List<Map<String, Object>> listPo = this.getManagerDAO().selectAllNative("common.file.getListUpdatedPO", paramGetListUpdatePo, null);
		LOG.info("Total export rule file : {}", listPo.size());
		for (Map<String, Object> mp : listPo) {
			BigInteger id = (BigInteger) mp.get("d0");
			BigInteger ruleId = (BigInteger) mp.get("d1");
			String ruleName = (String) mp.get("d2");
			String folderPath = URI_RULE_PATH + SystemUtils.FILE_SEPARATOR;
			File directory = new File(folderPath);
			if (! directory.exists()){
		        directory.mkdirs();
		    }
			String filePath = folderPath + ruleId + "_" + ruleName + ".xls";
			
			Object[][] paramIdPo = { {"id", id} };
			
			Blob blobRUle = (Blob) this.getManagerDAO().selectOneNative("common.file.getFileData", paramIdPo);
			
			LOG.info("Exporting ID : {}, ruleId : {}, ruleName : {}", id, ruleId, ruleName);
			exportToExcel(blobRUle, filePath, paramIdPo);
		}
		LOG.info("End export rule file");
	}
	
	private void exportToExcel(Blob blob, String pathFile, Object[][] paramIdPo) {
		try {
			if (blob != null) {
				byte[] bFile = blob.getBytes(1, (int) blob.length());
                Path path = Paths.get(pathFile);
                Files.write(path, bFile);
			}
			this.getManagerDAO().updateNative("common.file.updateIsExport", paramIdPo);
		} catch (SQLException e) {
			LOG.error("Error message SqlException : {}", e.getMessage());
		} catch (IOException e) {
			LOG.error("Error message IoException : {}", e.getMessage());
        } catch (Exception e) {
			LOG.error("Error message : {}", e.getMessage());
        }
	}

	@Transactional
	@Override
	public void generateAssetSchemeFileCsv(AuditContext auditContext) {
		Date maxTimestamps = getLastMaxTimestamp(GlobalVal.PUSHSYNC_MS_ASSETSCHEME, auditContext);
		
		AmGeneralsetting minRecords = commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_PUSHSYNC_MINRECORDS, auditContext);
		int minTotRecordToGenerate = Integer.parseInt(minRecords.getGsValue());
		
		Object params[][] = {{"lastMaxTimestamps", maxTimestamps}};
		StringBuilder queryRecords = new StringBuilder(" select distinct ID, ASSET_SCHM_H_ID, ASSET_TYPE_CODE, ASSET_TYPE_NAME, IS_DELETED, ")
				.append(" FORMAT(isnull(dtm_upd,dtm_crt), 'ddMMyyyyHHmmss') as DTM_UPD ")
				.append(" FROM STAGING_ASSET_SCHEME with(nolock) ")
				.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
		List result = this.getManagerDAO().selectAllNativeString(queryRecords.toString(), params);
		if (!result.isEmpty()) {
			if (result.size() >= minTotRecordToGenerate) {
				StringBuilder queryMaxTimestamps = new StringBuilder(" select MAX(ISNULL(DTM_UPD, DTM_CRT)) ")
						.append(" FROM STAGING_ASSET_SCHEME with(nolock) ")
						.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
				Date maxTimestampsDate = (Date) this.getManagerDAO().selectOneNativeString(queryMaxTimestamps.toString(), params);
				boolean isExported = isPushSyncFilesExist(GlobalVal.PUSHSYNC_MS_ASSETSCHEME, DateFormatUtils.format(maxTimestampsDate, formatDate), null);
				if (isExported) {
					return;
				}
				writeCSVFile(GlobalVal.PUSHSYNC_MS_ASSETSCHEME, result, DateFormatUtils.format(maxTimestampsDate, formatDate), null, auditContext);
			}
		}
	}

	@Transactional
	@Override
	public void generateBlacklistFileCsv(AuditContext auditContext) {
		Date maxTimestamps = getLastMaxTimestamp(GlobalVal.PUSHSYNC_MS_BLACKLIST, auditContext);

		AmGeneralsetting minRecords = commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_PUSHSYNC_MINRECORDS, auditContext);
		int minTotRecordToGenerate = Integer.parseInt(minRecords.getGsValue());
		
		Object params[][] = {{"lastMaxTimestamps", maxTimestamps}};
		StringBuilder queryRecords = new StringBuilder(" select DISTINCT ID, EXCLUDE_INFO_1, EXCLUDE_INFO_2, EXCLUDE_TYPE_CODE, EXCLUDE_TYPE_NAME, IS_DELETED, ")
				.append(" FORMAT(isnull(dtm_upd,dtm_crt), 'ddMMyyyyHHmmss') as DTM_UPD ")
				.append(" from STAGING_BLACKLIST_DATA with (nolock) ")
				.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
		List result = this.getManagerDAO().selectAllNativeString(queryRecords.toString(), params);
		if (!result.isEmpty()) {
			if (result.size() >= minTotRecordToGenerate) {
				StringBuilder queryMaxTimestamps = new StringBuilder(" select MAX(ISNULL(DTM_UPD, DTM_CRT)) ")
						.append(" from STAGING_BLACKLIST_DATA with (nolock) ")
						.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
				Date maxTimestampsDate = (Date) this.getManagerDAO().selectOneNativeString(queryMaxTimestamps.toString(), params);
				boolean isExported = isPushSyncFilesExist(GlobalVal.PUSHSYNC_MS_BLACKLIST, DateFormatUtils.format(maxTimestampsDate, formatDate), null);
				if (isExported) {
					return;
				}
				writeCSVFile(GlobalVal.PUSHSYNC_MS_BLACKLIST, result, DateFormatUtils.format(maxTimestampsDate, formatDate), null, auditContext);
			}
		}
		
	}

	@Transactional
	@Override
	public void generateIndustryFileCsv(AuditContext auditContext) {
		Date maxTimestamps = getLastMaxTimestamp(GlobalVal.PUSHSYNC_MS_INDUSTRY, auditContext);

		AmGeneralsetting minRecords = commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_PUSHSYNC_MINRECORDS, auditContext);
		int minTotRecordToGenerate = Integer.parseInt(minRecords.getGsValue());
		
		Object params[][] = {{"lastMaxTimestamps", maxTimestamps}};
		StringBuilder queryRecords = new StringBuilder(" select DISTINCT ID, IS_DELETED, INDUSTRY_TYPE_CODE, COALESCE(TOTAL_MARGIN, 0) as TOTAL_MARGIN, ")
				.append(" FORMAT(isnull(dtm_upd,dtm_crt), 'ddMMyyyyHHmmss') as DTM_UPD ")
				.append(" from STAGING_INDUSTRY_MARGIN with (nolock) ")
				.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
		List result = this.getManagerDAO().selectAllNativeString(queryRecords.toString(), params);
		if (!result.isEmpty()) {
			if (result.size() >= minTotRecordToGenerate) {
				StringBuilder queryMaxTimestamps = new StringBuilder(" select MAX(ISNULL(DTM_UPD, DTM_CRT)) ")
						.append(" from STAGING_INDUSTRY_MARGIN with (nolock) ")
						.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
				Date maxTimestampsDate = (Date) this.getManagerDAO().selectOneNativeString(queryMaxTimestamps.toString(), params);
				boolean isExported = isPushSyncFilesExist(GlobalVal.PUSHSYNC_MS_INDUSTRY, DateFormatUtils.format(maxTimestampsDate, formatDate), null);
				if (isExported) {
					return;
				}
				writeCSVFile(GlobalVal.PUSHSYNC_MS_INDUSTRY, result, DateFormatUtils.format(maxTimestampsDate, formatDate), null, auditContext);
			}
		}
		
	}

	@Transactional
	@Override
	public void generateMarketPriceFileCsv(AuditContext auditContext) {
		Date maxTimestamps = getLastMaxTimestamp(GlobalVal.PUSHSYNC_MS_MARKETPRICE, auditContext);
		AmGeneralsetting minRecords = commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_PUSHSYNC_MINRECORDS, auditContext);
		int minTotRecordToGenerate = Integer.parseInt(minRecords.getGsValue());
		
		Object [][] params = { {"lastMaxTimestamps", maxTimestamps} };
		StringBuilder queryRecords = new StringBuilder();
		queryRecords.append("SELECT    OFFICE_CODE ");
		queryRecords.append("FROM      STAGING_OTR_WITH_OFFICE WITH (NOLOCK) ");
		queryRecords.append("WHERE     ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps ");
		queryRecords.append("GROUP BY  OFFICE_CODE");
		List<Map<String, Object>> result = this.getManagerDAO().selectAllNativeString(queryRecords.toString(), params);
		if (null != result && !result.isEmpty()) {
			for (int i = 0; i < result.size(); i++) {
				Map<String, Object> map = result.get(i);
				String officeCode = String.valueOf(map.get("d0"));
				
				Object [][] paramsOffice = { {"lastMaxTimestamps", maxTimestamps}, {"officeCode", officeCode} };
				StringBuilder queryRecordsOffice = new StringBuilder();
				queryRecordsOffice.append("SELECT ID, ASSET_CODE, MANUFACTURING_YEAR, TOLERANCE_PRCTG, MARKET_PRICE, ");
				queryRecordsOffice.append("       FORMAT(EFFECTIVE_DT, 'ddMMyyyyHHmmss') as EFFECTIVE_DT, IS_DELETED, ");
				queryRecordsOffice.append("       FORMAT(isnull(dtm_upd,dtm_crt), 'ddMMyyyyHHmmss') as DTM_UPD, OFFICE_CODE ");
				queryRecordsOffice.append("FROM   STAGING_OTR_WITH_OFFICE WITH (NOLOCK) ");
				queryRecordsOffice.append("WHERE  ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps ");
				queryRecordsOffice.append("       AND OFFICE_CODE = :officeCode");
				
				List resultOffice = this.getManagerDAO().selectAllNativeString(queryRecordsOffice.toString(), paramsOffice);
				
				if (null != resultOffice && !resultOffice.isEmpty()) {
					if (resultOffice.size() >= minTotRecordToGenerate) {
						StringBuilder queryMaxTimestamps = new StringBuilder();
						queryMaxTimestamps.append("SELECT MAX(ISNULL(DTM_UPD, DTM_CRT)) ");
						queryMaxTimestamps.append("FROM   STAGING_OTR_WITH_OFFICE WITH (NOLOCK) ");
						queryMaxTimestamps.append("WHERE  ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps ");
						queryMaxTimestamps.append("       AND OFFICE_CODE = :officeCode");
						Object [][] paramsMaxTimestamps = { {"lastMaxTimestamps", maxTimestamps}, {"officeCode", officeCode} };
						Date maxTimestampsDate = (Date) this.getManagerDAO().selectOneNativeString(queryMaxTimestamps.toString(), paramsMaxTimestamps);
						boolean isExported = isPushSyncFilesExist(GlobalVal.PUSHSYNC_MS_MARKETPRICE, DateFormatUtils.format(maxTimestampsDate, formatDate), officeCode);
						if (isExported) {
							return;
						}
						writeCSVFile(GlobalVal.PUSHSYNC_MS_MARKETPRICE, resultOffice, DateFormatUtils.format(maxTimestampsDate, formatDate), officeCode, auditContext);
					}
				}
			}
		}
	}

	@Transactional
	@Override
	public void generatePOFileCsv(AuditContext auditContext) {
		Date maxTimestamps = getLastMaxTimestamp(GlobalVal.PUSHSYNC_MS_PO, auditContext);
		AmGeneralsetting minRecords = commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_PUSHSYNC_MINRECORDS, auditContext);
		int minTotRecordToGenerate = Integer.parseInt(minRecords.getGsValue());
		
		Object params[][] = {{"lastMaxTimestamps", maxTimestamps}};
		StringBuilder queryRecords = new StringBuilder(" SELECT DISTINCT ID, ASSET_SCHM_H_ID, DEALER_SCHM_H_ID, PO_OFFICE_CODE, PRODUCT_OFFERING_CODE, PRODUCT_OFFERING_NAME,  ")
				.append(" PRODUCT_CATEGORY_CODE, PRODUCT_CATEGORY_NAME, JENIS_PEMBIAYAAN, ISNULL(MIN_TENOR, '0') as min_tenor, ISNULL(MAX_TENOR, '999') as max_tenor, ")
				.append(" SC_ID, COMPONENT, ISNULL(RULE_SET_ID_RULE_MINIMUM_TDP, 0) RULE_SET_ID_RULE_MINIMUM_TDP, RULE_SET_ID_RULE_MANUFACTURING_YEAR, ")
				.append(" ISNULL(RULE_SET_ID_RULE_DATA_ASSET_PRICE, 0) RULE_SET_ID_RULE_DATA_ASSET_PRICE, RULE_SET_ID_RULE_DATA_MIN_DP, RULE_SET_ID_RULE_DATA_APP_TC, ")
				.append(" RULE_SET_ID_RULE_SCORING, RULE_SET_ID_SCORING_MATRIX, IS_DELETED, ")
				.append(" FORMAT(isnull(dtm_upd,dtm_crt), 'ddMMyyyyHHmmss') as DTM_UPD ")
				.append(" FROM STAGING_PRODUCT_OFFERING with (nolock) ")
				.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
		List result = this.getManagerDAO().selectAllNativeString(queryRecords.toString(), params);
		if (!result.isEmpty()) {
			if (result.size() >= minTotRecordToGenerate) {
				StringBuilder queryMaxTimestamps = new StringBuilder(" select MAX(ISNULL(DTM_UPD, DTM_CRT)) ")
						.append(" FROM STAGING_PRODUCT_OFFERING with (nolock) ")
						.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
				Date maxTimestampsDate = (Date) this.getManagerDAO().selectOneNativeString(queryMaxTimestamps.toString(), params);
				boolean isExported = isPushSyncFilesExist(GlobalVal.PUSHSYNC_MS_PO, DateFormatUtils.format(maxTimestampsDate, formatDate), null);
				if (isExported) {
					return;
				}
				writeCSVFile(GlobalVal.PUSHSYNC_MS_PO, result, DateFormatUtils.format(maxTimestampsDate, formatDate), null, auditContext);
			}
		}
	}

	@Transactional
	@Override
	public void generatePOAssetFileCsv(AuditContext auditContext) {
		Date maxTimestamps = getLastMaxTimestamp(GlobalVal.PUSHSYNC_MS_PO_ASSET, auditContext);
		AmGeneralsetting minRecords = commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_PUSHSYNC_MINRECORDS, auditContext);
		int minTotRecordToGenerate = Integer.parseInt(minRecords.getGsValue());
		
		Object params[][] = {{"lastMaxTimestamps", maxTimestamps}};
		StringBuilder queryRecords = new StringBuilder(" SELECT DISTINCT ID, IS_DELETED, ASSET_SCHM_H_ID, HIERARCHY_L1_CODE, ASSET_HIERARCHY_L1_NAME,  ")
				.append(" HIERARCHY_L2_CODE, ASSET_HIERARCHY_L2_NAME, GROUP_TYPE, MASTER_CODE, MASTER_NAME, ")
				.append(" FORMAT(isnull(dtm_upd,dtm_crt), 'ddMMyyyyHHmmss') as DTM_UPD ")
				.append(" FROM STAGING_PO_ASSET with (nolock) ")
				.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
		List result = this.getManagerDAO().selectAllNativeString(queryRecords.toString(), params);
		if (!result.isEmpty()) {
			if (result.size() >= minTotRecordToGenerate) {
				StringBuilder queryMaxTimestamps = new StringBuilder(" select MAX(ISNULL(DTM_UPD, DTM_CRT)) ")
						.append(" FROM STAGING_PO_ASSET with (nolock) ")
						.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
				Date maxTimestampsDate = (Date) this.getManagerDAO().selectOneNativeString(queryMaxTimestamps.toString(), params);
				boolean isExported = isPushSyncFilesExist(GlobalVal.PUSHSYNC_MS_PO_ASSET, DateFormatUtils.format(maxTimestampsDate, formatDate), null);
				if (isExported) {
					return;
				}
				writeCSVFile(GlobalVal.PUSHSYNC_MS_PO_ASSET, result, DateFormatUtils.format(maxTimestampsDate, formatDate), null, auditContext);
			}
		}
		
	}

	@Transactional
	@Override
	public void generatePODealerFileCsv(AuditContext auditContext) {
		Date maxTimestamps = getLastMaxTimestamp(GlobalVal.PUSHSYNC_MS_PO_DEALER, auditContext);
		
		AmGeneralsetting minRecords = commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_PUSHSYNC_MINRECORDS, auditContext);
		int minTotRecordToGenerate = Integer.parseInt(minRecords.getGsValue());
		
		
		Object params[][] = {{"lastMaxTimestamps", maxTimestamps}};
		StringBuilder queryRecords = new StringBuilder(" SELECT DISTINCT ID, DEALER_CODE, DEALER_SCHM_H_ID, DEALER_NAME, FORMAT(isnull(dtm_upd,dtm_crt), 'ddMMyyyyHHmmss') as DTM_UPD , IS_DELETED  ")
				.append(" FROM STAGING_PO_DEALER with (nolock) ")
				.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
		List result = this.getManagerDAO().selectAllNativeString(queryRecords.toString(), params);
		if (!result.isEmpty()) {
			if (result.size() >= minTotRecordToGenerate) {
				StringBuilder queryMaxTimestamps = new StringBuilder(" select MAX(ISNULL(DTM_UPD, DTM_CRT)) ")
						.append(" FROM STAGING_PO_DEALER with (nolock) ")
						.append(" WHERE ISNULL(DTM_UPD, DTM_CRT) > :lastMaxTimestamps");
				Date maxTimestampsDate = (Date) this.getManagerDAO().selectOneNativeString(queryMaxTimestamps.toString(), params);
				boolean isExported = isPushSyncFilesExist(GlobalVal.PUSHSYNC_MS_PO_DEALER, DateFormatUtils.format(maxTimestampsDate, formatDate), null);
				if (isExported) {
					return;
				}
				writeCSVFile(GlobalVal.PUSHSYNC_MS_PO_DEALER, result, DateFormatUtils.format(maxTimestampsDate, formatDate), null, auditContext);
			}
		}
	}
	
	private Date getLastMaxTimestamp(String tableName, AuditContext auditContext) {
		Object[][] params = {{"tableName", tableName}};
		StringBuilder queryLastMaxTimestamp = new StringBuilder(" select max(MAX_TIMESTAMPS) ")
				.append(" from MS_MOBILE_PUSHSYNC_FILES with(nolock) ")
				.append(" where TABLE_NAME = :tableName ");
		Date maxTimestamps = (Date) this.getManagerDAO().selectOneNativeString(queryLastMaxTimestamp.toString(), params);
		if (maxTimestamps == null) {
			String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			try {
				maxTimestamps = format.parse(currentDate);
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		
		return maxTimestamps;
	}
	
	private boolean isPushSyncFilesExist(String tableName, String lastMaxTimestamps, String officeCode) {
		String folderPath = URI_RULE_PATH + SystemUtils.FILE_SEPARATOR + "PUSHSYNC" +SystemUtils.FILE_SEPARATOR;
		String fileName = null;
		if (StringUtils.isBlank(officeCode)) {
			fileName = filePrefix + tableName + "_"+ lastMaxTimestamps+ ".csv";
		} else {
			fileName = filePrefix + tableName + "_" + officeCode + "_" + lastMaxTimestamps+ ".csv";
		}
		try {
			File file = new File(folderPath+fileName);
			if(file.exists()) {
				return true;
			}
		}catch (Exception e) {
			e.printStackTrace();
		}
		return false;
	}
	
	private void writeCSVFile(String tableSync, List<Map<String, Object>> listRecords, String maxTimestamps, String officeCode, AuditContext auditContext) {
		AmGeneralsetting maxRecords = commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_PUSHSYNC_MAXRECORDS, auditContext);
		int maxRowsInFile = Integer.parseInt(maxRecords.getGsValue());
		
		//split for sublist
		List<List<Map<String, Object>>> partsOfRecords = new ArrayList<>();
	    for (int i = 0; i < listRecords.size(); i += maxRowsInFile) {
	    	partsOfRecords.add(new ArrayList<>(
	    			listRecords.subList(i, Math.min(listRecords.size(), i + maxRowsInFile)))
	        );
	    }
	    
		String[] headerTemplate = null;
		if (GlobalVal.PUSHSYNC_MS_ASSETSCHEME.equalsIgnoreCase(tableSync)) {
			headerTemplate = TEMPLATE_HEADER_MS_ASSETSCHEME;
		} else if (GlobalVal.PUSHSYNC_MS_BLACKLIST.equalsIgnoreCase(tableSync)) {
			headerTemplate = TEMPLATE_HEADER_MS_BLACKLIST;
		} else if (GlobalVal.PUSHSYNC_MS_INDUSTRY.equalsIgnoreCase(tableSync)) {
			headerTemplate = TEMPLATE_HEADER_MS_INDUSTRY;
		} else if (GlobalVal.PUSHSYNC_MS_MARKETPRICE.equalsIgnoreCase(tableSync)) {
			headerTemplate = TEMPLATE_HEADER_MS_MARKETPRICE;
		} else if (GlobalVal.PUSHSYNC_MS_PO.equalsIgnoreCase(tableSync)) {
			headerTemplate = TEMPLATE_HEADER_MS_PO;
		} else if (GlobalVal.PUSHSYNC_MS_PO_ASSET.equalsIgnoreCase(tableSync)) {
			headerTemplate = TEMPLATE_HEADER_MS_PO_ASSET;
		} else if (GlobalVal.PUSHSYNC_MS_PO_DEALER.equalsIgnoreCase(tableSync)) {
			headerTemplate = TEMPLATE_HEADER_MS_PO_DEALER;
		}
		
		String folderPath = URI_PUSHSYNC_FILES_PATH +SystemUtils.FILE_SEPARATOR;
		File directory = new File(folderPath);
		if (! directory.exists()){
	        directory.mkdirs();
	    }
		String filename = filePrefix + tableSync + "_"+ maxTimestamps+ ".csv";
		if (StringUtils.isNotBlank(officeCode)) {
			filename = filePrefix + tableSync + "_" + officeCode + "_" + maxTimestamps+ ".csv";
		}
		String pathFile = folderPath + filename;
		CSVWriter writer = null;
		try {
			writer = new CSVWriter(new FileWriter(pathFile), '|');
			this.writeHeaderCSV(writer, headerTemplate);
			for (int i=0; i<partsOfRecords.size(); i++) {
				this.writeRecordsCSV(writer, partsOfRecords.get(i), auditContext);
			}
			writer.close();
		} 
		catch (IOException e) {
			LOG.error("Error write Push Sync CSV file");
			e.printStackTrace();
			try {
				writer.close();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		} 
		
		SimpleDateFormat format = new SimpleDateFormat(formatDate);
		MsMobilePushsyncFiles file = new MsMobilePushsyncFiles();
		file.setDtmCrt(new Date());
		file.setUsrCrt(auditContext.getCallerId());
		file.setIsActive("1");
		file.setTableName(tableSync);
		file.setLovGroup(tableSync);
		file.setTotalRecord(listRecords.size());
		file.setFilename(filename);
		file.setFileUrl(URI_PUSHSYNC_FILES+filename);
		try {
			file.setMaxTimestamps(format.parse(maxTimestamps));
		} catch (ParseException e) {
			e.printStackTrace();
		}
		this.getManagerDAO().insert(file);
		
		//insert need sync for all user isfieldPerson and active
		Object[][] paramInsertPushsync = null;
		StringBuilder queryInsertToTPushSync = new StringBuilder();
		queryInsertToTPushSync.append(" INSERT INTO TR_PUSHSYNC(LOV_GROUP, DTM_CRT, USR_CRT, UUID_MS_USER, ID_PUSHSYNC_FILE, FLAG) ")
			.append(" SELECT :lovGroup, GETDATE(), :usrCrt, amu.UUID_MS_USER, :idPushsyncFile , '0' ")
			.append(" FROM AM_MSUSER amu WITH(NOLOCK) ")
			.append(" JOIN MS_JOB mj WITH(NOLOCK) ON mj.UUID_JOB = amu.UUID_JOB")
			.append(" WHERE amu.IS_ACTIVE=1 ")
			.append(" AND mj.IS_FIELD_PERSON=1 ");
		if (StringUtils.isNotBlank(officeCode)) {
			MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, new Object[][] { {Restrictions.eq("branchCode", officeCode)} });
			queryInsertToTPushSync.append(" AND amu.UUID_BRANCH = :uuidBranch ");
			Object [][] paramInsertPushsyncTemp = { {"lovGroup", tableSync}, {"usrCrt", auditContext.getCallerId()},
					{"idPushsyncFile", file.getIdPushsyncFile()}, {"uuidBranch", msBranch.getUuidBranch()} };
			paramInsertPushsync = paramInsertPushsyncTemp;
		} else {
			Object [][] paramInsertPushsyncTemp = { {"lovGroup", tableSync}, {"usrCrt", auditContext.getCallerId()}, {"idPushsyncFile", file.getIdPushsyncFile()} };
			paramInsertPushsync = paramInsertPushsyncTemp;
		}
		this.getManagerDAO().insertNativeString(queryInsertToTPushSync.toString(), paramInsertPushsync);
		
	}
	private void writeHeaderCSV(CSVWriter writer, final String[] templateHeader) {
		List<String> tmpHeader = new ArrayList<String>(Arrays.asList(templateHeader));
		Object[] tmpArray = tmpHeader.toArray();
		writer.writeNext(Arrays.copyOf(tmpArray, tmpArray.length, String[].class));
	}
	
	private int writeRecordsCSV(CSVWriter writer, List<Map<String, Object>> listRecords, AuditContext callerId) {
		for (int i = 0; i < listRecords.size(); i++) {
			Map<String, Object> records = listRecords.get(i);
			List<String> tmpRecordsList = new ArrayList<>();
			for (int j = 0; j < records.size(); j++) {
				String	cellValue = records.get("d"+j) == null ? StringUtils.EMPTY : records.get("d"+j).toString();
				cellValue = cellValue.replace("\n", " ");
				cellValue = "=\""+cellValue+"\"";
				tmpRecordsList.add(StringUtils.upperCase(cellValue));								
			}
			
			
			Object[] tmpArray = tmpRecordsList.toArray();
			writer.writeNext(Arrays.copyOf(tmpArray, tmpArray.length, String[].class));
		}
		return 1;
	}
}
