package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.jexl2.JexlContext;
import org.apache.commons.jexl2.JexlEngine;
import org.apache.commons.jexl2.MapContext;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.base.dynamicform.Constant;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.FormLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.dao.QuestionSet;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.exceptions.FormException;
import com.adins.mss.foundation.questiongenerator.QuestionBean;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsgroup;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsAssettag;
import com.adins.mss.model.MsCollectiontag;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormcategory;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormofgroup;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsOrdertag;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsQuestiongroup;
import com.adins.mss.model.MsQuestiongroupofform;
import com.adins.mss.model.MsQuestionofgroup;
import com.adins.mss.model.MsQuestionrelevant;
import com.adins.mss.model.custom.FormQuestionBean;
import com.adins.mss.model.custom.FormQuestionContentBean;
import com.adins.mss.model.custom.UploadFormBean;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericFormLogic extends BaseLogic implements FormLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory
			.getLogger(GenericFormLogic.class);
	private static JexlEngine jexlEngine;
	private AuditInfo auditInfoF;
	private AuditInfo auditInfoFoG;
	private AuditInfo auditInfoQ;
	private AuditInfo auditInfoQoG;
	private AuditInfo auditInfoQR;
	private AuditInfo auditInfoQG;
	private AuditInfo auditInfoQGoF;
	private static final int NUMBER_ROW_IN_VALIDATION = 1000;
	private static final int PRIORITY_COLUMN = 0;
	private static final int BRANCH_COLUMN = 1;
	private static final int DRIVER_COLUMN = 2;
	private static final int LOCATION_COLUMN = 3;

	public GenericFormLogic() {
		String[] pkColsF = { "uuidForm" };
		String[] pkDbColsF = { "UUID_FORM" };
		String[] colsF = { "uuidForm", "isActive", "version",
				"msFormcategory.uuidFormCategory", "amMssubsystem.uuidMsSubsystem", "formName",
				"isPrintable", "preprocessingSp", "formLastUpdate" };
		String[] dbColsF = { "UUID_FORM", "IS_ACTIVE", "VERSION",
				"UUID_FORM_CATEGORY", "UUID_MS_SUBSYSTEM", "FORM_NAME",
				"IS_PRINTABLE", "PREPROCESSING_SP", "FORM_LAST_UPDATE" };
		this.auditInfoF = new AuditInfo("MS_FORM", pkColsF, pkDbColsF, colsF,
				dbColsF);

		String[] pkColsFoG = { "uuidFormOfGroup" };
		String[] pkDbColsFoG = { "UUID_FORM_OF_GROUP" };
		String[] colsFoG = { "uuidFormOfGroup", "amMsgroup.uuidMsGroup", "msForm.uuidForm" };
		String[] dbColsFoG = { "UUID_FORM_OF_GROUP", "UUID_MS_GROUP",
				"UUID_FORM" };
		this.auditInfoFoG = new AuditInfo("MS_FORMOFGROUP", pkColsFoG,
				pkDbColsFoG, colsFoG, dbColsFoG);
		
		String[] pkColsQ = { "uuidQuestion" };
		String[] pkDbColsQ = { "UUID_QUESTION" };
		String[] colsQ = { "uuidQuestion", "msCollectiontag.uuidCollectionTag", "msAssettag.uuidAssetTag", 
				"msOrdertag.uuidOrderTag", "msAnswertype.uuidAnswerType",
				"lovGroup", "isActive", "refId", "questionLabel", "isVisible",
				"isMandatory", "isReadonly", "isHolidayAllowed", "maxLength",
				"regexPattern", "imgQlt" };
		String[] dbColsQ = { "UUID_QUESTION", "UUID_COLLECTION_TAG", "UUID_ASSET_TAG", "UUID_ORDER_TAG", "UUID_ANSWER_TYPE",
				"LOV_GROUP", "IS_ACTIVE", "REF_ID", "QUESTION_LABEL", "IS_VISIBLE",
				"IS_MANDATORY", "IS_READONLY", "IS_HOLIDAY_ALLOWED", "MAX_LENGTH",
				"REGEX_PATTERN", "IMG_QLT" };
		this.auditInfoQ = new AuditInfo("MS_QUESTION", pkColsQ, pkDbColsQ, colsQ, dbColsQ);
		
		String[] pkColsQoG = { "uuidQuestionOfGroup" };
		String[] pkDbColsQoG = { "UUID_QUESTION_OF_GROUP" };
		String[] colsQoG = { "uuidQuestionOfGroup", "msQuestiongroup.uuidQuestionGroup", "msQuestion.uuidQuestion", "seqOrder" };
		String[] dbColsQoG = { "UUID_QUESTION_OF_GROUP", "UUID_QUESTION_GROUP", "UUID_QUESTION", "SEQ_ORDER" };
		this.auditInfoQoG = new AuditInfo("MS_QUESTIONOFGROUP", pkColsQoG, pkDbColsQoG, colsQoG, dbColsQoG);
		
		String[] pkColsQR = { "uuidQuestionRelevant" };
		String[] pkDbColsQR = { "UUID_QUESTION_RELEVANT" };
		String[] colsQR = { "uuidQuestionRelevant", "msQuestion.uuidQuestion",
				"msQuestiongroup.uuidQuestionGroup", "msForm.uuidForm",
				"relevant", "choiceFilter", "questionValidation", "questionErrorMessage", "questionValue", "relevantMandatory" };
		String[] dbColsQR = { "UUID_QUESTION_RELEVANT", "UUID_QUESTION",
				"UUID_QUESTION_GROUP", "UUID_FORM", "RELEVANT", "CHOICE_FILTER", "QUESTION_VALIDATION", 
				"QUESTION_ERROR_MESSAGE", "QUESTION_VALUE", "RELEVANT_MANDATORY" };
		this.auditInfoQR = new AuditInfo("MS_QUESTIONRELEVANT", pkColsQR,
				pkDbColsQR, colsQR, dbColsQR);

		String[] pkColsQG = { "uuidQuestionGroup" };
		String[] pkDbColsQG = { "UUID_QUESTION_GROUP" };
		String[] colsQG = { "uuidQuestionGroup", "isActive", "questionGroupLabel" };
		String[] dbColsQG = { "UUID_QUESTION_GROUP", "IS_ACTIVE", "QUESTION_GROUP_LABEL" };
		this.auditInfoQG = new AuditInfo("MS_QUESTIONGROUP", pkColsQG, pkDbColsQG, colsQG, dbColsQG);
		
		String[] pkColsQGoF = { "uuidQuestionGroupOfForm" };
		String[] pkDbColsQGoF = { "UUID_QUESTION_GROUP_OF_FORM" };
		String[] colsQGoF = { "uuidQuestionGroupOfForm", "msQuestiongroup.uuidQuestionGroup",
				"msForm.uuidForm", "lineSeqOrder" };
		String[] dbColsQGoF = { "UUID_QUESTION_GROUP_OF_FORM",
				"UUID_QUESTION_GROUP", "UUID_FORM", "LINE_SEQ_ORDER" };
		this.auditInfoQGoF = new AuditInfo("MS_QUESTIONGROUPOFFORM",
				pkColsQGoF, pkDbColsQGoF, colsQGoF, dbColsQGoF);
	}

	private IntFormLogic intFormLogic;
	private String emptyRowSheetRelevant;

	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	@Autowired
	private MessageSource messageSource;

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	@Autowired
	private CommonLogic commonLogic;
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	private static final String[] TEMPLATE_HEADER_MS = {
			GlobalVal.HEADER_APPLICATION_NO, GlobalVal.HEADER_PRIORITY_COLUMN,
			GlobalVal.HEADER_BRANCH_ID, GlobalVal.HEADER_MOBILE_USER_ID_COLUMN,
			GlobalVal.HEADER_CUSTOMER_NAME, GlobalVal.HEADER_CUSTOMER_PHONE,
			GlobalVal.HEADER_CUSTOMER_ADDRESS, GlobalVal.HEADER_NOTES,
			GlobalVal.HEADER_LATITUDE, GlobalVal.HEADER_LONGITUDE };
	private static final String[] TEMPLATE_HEADER_MC = {
			GlobalVal.HEADER_AGREEMENT_NO, GlobalVal.HEADER_PRIORITY_COLUMN,
			GlobalVal.HEADER_MOBILE_USER_ID_COLUMN, GlobalVal.HEADER_CUSTOMER_NAME,
			GlobalVal.HEADER_CUSTOMER_PHONE, GlobalVal.HEADER_CUSTOMER_ADDRESS,
			GlobalVal.HEADER_NOTES, GlobalVal.HEADER_SURVEY_ASSIGNMENT_ID,
			GlobalVal.HEADER_LATITUDE, GlobalVal.HEADER_LONGITUDE };
	private static final String[] TEMPLATE_HEADER_MC_INSTALLMENT_SCEDULER = {
			GlobalVal.HEADER_AGREEMENT_NO, GlobalVal.HEADER_BRANCH_CODE,
			GlobalVal.HEADER_INSTALLMENT_NO, GlobalVal.HEADER_DUE_DATE,
			GlobalVal.HEADER_INSTALLMENT_AMOUNT, GlobalVal.HEADER_INSTL_PAID_DATE,
			GlobalVal.HEADER_INSTL_PAID_AMOUNT, GlobalVal.HEADER_LC_INSTL_AMOUNT,
			GlobalVal.HEADER_LC_INSTL_PAID, GlobalVal.HEADER_LC_INSTL_WAIVED,
			GlobalVal.HEADER_PRINCIPAL_AMOUNT, GlobalVal.HEADER_INTEREST_AMOUNT,
			GlobalVal.HEADER_OS_PRINCIPAL_AMOUNT, GlobalVal.HEADER_OS_INTEREST_AMOUNT,
			GlobalVal.HEADER_LC_DAYS, GlobalVal.HEADER_LC_ADMIN_FEE,
			GlobalVal.HEADER_LC_ADMIN_FEE_PAID, GlobalVal.HEADER_LC_ADMIN_FEE_WAIVED };
	private static final String[] TEMPLATE_HEADER_MC_COLLECTION_HISTORY = {
			GlobalVal.HEADER_AGREEMENT_NO, GlobalVal.HEADER_BRANCH_CODE,
			GlobalVal.HEADER_ACTIVITY_DATE,GlobalVal.HEADER_COLLECTOR_NAME,
			GlobalVal.HEADER_ACTIVITY, GlobalVal.HEADER_RESULT, GlobalVal.HEADER_PTP_DATE,
			GlobalVal.HEADER_NOTES, GlobalVal.HEADER_OVER_DUE_DAYS,
			GlobalVal.HEADER_NEXT_PLAN_DATE, GlobalVal.HEADER_NEXT_PALAN_ACTION};
	private static final String[] TEMPLATE_HEADER_MC_PAYMENT_HISTORY = {
			GlobalVal.HEADER_AGREEMENT_NO, GlobalVal.HEADER_BRANCH_CODE,
			GlobalVal.HEADER_RECEIPT_NO,GlobalVal.HEADER_VALUE_DATE,
			GlobalVal.HEADER_POSTING_DATE, GlobalVal.HEADER_PAYMENT_AMOUNT,
			GlobalVal.HEADER_INSTALLMENT_AMOUNT, GlobalVal.HEADER_INSTALLMENT_NUMBER,
			GlobalVal.HEADER_TRANSACTION_TYPE, GlobalVal.HEADER_WOP_CODE, 
			GlobalVal.HEADER_TRANSACTION_ID, GlobalVal.HEADER_PAYMENT_ALLOCATION_NAME,
			GlobalVal.HEADER_OS_AMOUNT_OD, GlobalVal.HEADER_RECEIVE_AMOUNT  };
	
	private static final String[] TEMPLATE_HEADER_MT = {
			GlobalVal.HEADER_PRIORITY_COLUMN, GlobalVal.HEADER_BRANCH_ID,
			GlobalVal.HEADER_MOBILE_USER_ID_COLUMN, GlobalVal.HEADER_LOCATION_NAME,
			GlobalVal.HEADER_CUSTOMER_PHONE, GlobalVal.HEADER_NOTES,
			GlobalVal.HEADER_DATETIME_ASSIGNMENT, GlobalVal.HEADER_TRIP,
			GlobalVal.HEADER_STOP };

	private static final String[] TEMPLATE_HEADER_MT_DATAMASTER = {
			GlobalVal.HEADER_PRIORITY_COLUMN, GlobalVal.HEADER_NEW_COLUMN,
			GlobalVal.HEADER_BRANCH_CODE, GlobalVal.HEADER_BRANCH_DESCRIPTION,
			GlobalVal.HEADER_NEW_COLUMN, GlobalVal.HEADER_BRANCH_CODE,
			GlobalVal.HEADER_DRIVER_NAME, GlobalVal.HEADER_NEW_COLUMN,
			GlobalVal.HEADER_LOCATION_NAME, GlobalVal.HEADER_ZONE,
			GlobalVal.HEADER_CUSTOMER_ADDRESS, GlobalVal.HEADER_LATITUDE,
			GlobalVal.HEADER_LONGITUDE, GlobalVal.HEADER_NEW_COLUMN,
			GlobalVal.HEADER_BRANCH_LIST, GlobalVal.HEADER_BRANCH_LOOKUP };


	private static final String[] TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET = {
			GlobalVal.HEADER_QUESTION_GROUP, GlobalVal.HEADER_IS_ACTIVE,
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL, };

	private static final String[] TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET_EDIT = {
			GlobalVal.HEADER_QUESTION_GROUP, GlobalVal.HEADER_IS_ACTIVE,
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_EDIT, GlobalVal.HEADER_SEQUENCE_ORDER,
			GlobalVal.HEADER_UUID_QUESTION_OF_GROUP,

	};

	private static final String[] TEMPLATE_HEADER_MO_QESTION_LIST_SHEET = {
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ANSWER_TYPE, GlobalVal.HEADER_IMAGE_QUALITY,
			GlobalVal.HEADER_LOV_GROUP, GlobalVal.HEADER_MAX_LENGTH,
			GlobalVal.HEADER_ORDER_TAGGING, GlobalVal.HEADER_MANDATORY,
			GlobalVal.HEADER_READ_ONLY, GlobalVal.HEADER_IS_VISIBLE,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_REGEX_PATTERN,
			GlobalVal.HEADER_IS_HOLIDAY_ALLOWED };

	private static final String[] TEMPLATE_HEADER_MS_QESTION_LIST_SHEET = {
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ANSWER_TYPE, GlobalVal.HEADER_IMAGE_QUALITY,
			GlobalVal.HEADER_LOV_GROUP, GlobalVal.HEADER_MAX_LENGTH,
			GlobalVal.HEADER_SURVEY_TAGGING, GlobalVal.HEADER_MANDATORY,
			GlobalVal.HEADER_READ_ONLY, GlobalVal.HEADER_IS_VISIBLE,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_REGEX_PATTERN,
			GlobalVal.HEADER_IS_HOLIDAY_ALLOWED };

	private static final String[] TEMPLATE_HEADER_MC_QESTION_LIST_SHEET = {
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ANSWER_TYPE, GlobalVal.HEADER_IMAGE_QUALITY,
			GlobalVal.HEADER_LOV_GROUP, GlobalVal.HEADER_MAX_LENGTH,
			GlobalVal.HEADER_COLLECTION_TAGGING, GlobalVal.HEADER_MANDATORY,
			GlobalVal.HEADER_READ_ONLY, GlobalVal.HEADER_IS_VISIBLE,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_REGEX_PATTERN,
			GlobalVal.HEADER_IS_HOLIDAY_ALLOWED };

	private static final String[] TEMPLATE_HEADER_MO_FORM_SHEET = {
			GlobalVal.HEADER_FORM_NAME_COLUMN, GlobalVal.HEADER_FORM_CATEGORY,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_QUESTION_GROUP, };

	private static final String[] TEMPLATE_HEADER_MO_FORM_SHEET_EDIT = {
			GlobalVal.HEADER_FORM_NAME_COLUMN, GlobalVal.HEADER_FORM_CATEGORY,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_QUESTION_GROUP,
			GlobalVal.HEADER_EDIT, GlobalVal.HEADER_SEQUENCE_ORDER,
			GlobalVal.HEADER_UUID_QUESTION_GROUP_OF_FORM };

	private static final String[] TEMPLATE_HEADER_MO_RELEVANSI_SHEET = {
			GlobalVal.HEADER_FORM_NAME_COLUMN, GlobalVal.HEADER_QUESTION_GROUP,
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_RELEVANT, GlobalVal.HEADER_CHOICE_FILTER,
			GlobalVal.HEADER_CALCULATE, GlobalVal.HEADER_QUESTION_VALIDATION,
			GlobalVal.HEADER_QUESTION_VALUE, GlobalVal.HEADER_ERROR_MESSAGE, GlobalVal.HEADER_DYANMIC_MANDATORY };

	private static final String[] TEMPLATE_HEADER_MO_RELEVANSI_SHEET_EDIT = {
			GlobalVal.HEADER_FORM_NAME_COLUMN, GlobalVal.HEADER_QUESTION_GROUP,
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_RELEVANT, GlobalVal.HEADER_CHOICE_FILTER,
			GlobalVal.HEADER_CALCULATE, GlobalVal.HEADER_QUESTION_VALIDATION,
			GlobalVal.HEADER_QUESTION_VALUE, GlobalVal.HEADER_ERROR_MESSAGE, GlobalVal.HEADER_DYANMIC_MANDATORY,
			GlobalVal.HEADER_EDIT, GlobalVal.HEADER_UUID_RELEVANT };

	private static final String[] TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET_ERROR = {
			GlobalVal.HEADER_QUESTION_GROUP, GlobalVal.HEADER_IS_ACTIVE,
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ERROR };

	private static final String[] TEMPLATE_HEADER_MO_QESTION_LIST_SHEET_ERROR = {
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ANSWER_TYPE, GlobalVal.HEADER_IMAGE_QUALITY,
			GlobalVal.HEADER_LOV_GROUP, GlobalVal.HEADER_MAX_LENGTH,
			GlobalVal.HEADER_ORDER_TAGGING, GlobalVal.HEADER_MANDATORY,
			GlobalVal.HEADER_READ_ONLY, GlobalVal.HEADER_IS_VISIBLE,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_REGEX_PATTERN,
			GlobalVal.HEADER_IS_HOLIDAY_ALLOWED, GlobalVal.HEADER_ERROR };

	private static final String[] TEMPLATE_HEADER_MS_QESTION_LIST_SHEET_ERROR = {
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ANSWER_TYPE, GlobalVal.HEADER_IMAGE_QUALITY,
			GlobalVal.HEADER_LOV_GROUP, GlobalVal.HEADER_MAX_LENGTH,
			GlobalVal.HEADER_SURVEY_TAGGING, GlobalVal.HEADER_MANDATORY,
			GlobalVal.HEADER_READ_ONLY, GlobalVal.HEADER_IS_VISIBLE,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_REGEX_PATTERN,
			GlobalVal.HEADER_IS_HOLIDAY_ALLOWED, GlobalVal.HEADER_ERROR };

	private static final String[] TEMPLATE_HEADER_MC_QESTION_LIST_SHEET_ERROR = {
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ANSWER_TYPE, GlobalVal.HEADER_IMAGE_QUALITY,
			GlobalVal.HEADER_LOV_GROUP, GlobalVal.HEADER_MAX_LENGTH,
			GlobalVal.HEADER_COLLECTION_TAGGING, GlobalVal.HEADER_MANDATORY,
			GlobalVal.HEADER_READ_ONLY, GlobalVal.HEADER_IS_VISIBLE,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_REGEX_PATTERN,
			GlobalVal.HEADER_IS_HOLIDAY_ALLOWED, GlobalVal.HEADER_ERROR };

	private static final String[] TEMPLATE_HEADER_MO_FORM_SHEET_ERROR = {
			GlobalVal.HEADER_FORM_NAME_COLUMN, GlobalVal.HEADER_FORM_CATEGORY,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_QUESTION_GROUP,
			/*GlobalVal.HEADER_SEQUENCE_ORDER,*/ GlobalVal.HEADER_ERROR };

	private static final String[] TEMPLATE_HEADER_MO_RELEVANSI_SHEET_ERROR = {
			GlobalVal.HEADER_FORM_NAME_COLUMN, GlobalVal.HEADER_QUESTION_GROUP,
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_RELEVANT, GlobalVal.HEADER_CHOICE_FILTER,
			GlobalVal.HEADER_CALCULATE, GlobalVal.HEADER_QUESTION_VALIDATION,
			GlobalVal.HEADER_QUESTION_VALUE, GlobalVal.HEADER_ERROR_MESSAGE, GlobalVal.HEADER_DYANMIC_MANDATORY,
			GlobalVal.HEADER_ERROR };

	private static final String[] TEMPLATE_HEADER_MO_QESTION_LIST_SHEET_EDIT = {
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ANSWER_TYPE, GlobalVal.HEADER_IMAGE_QUALITY,
			GlobalVal.HEADER_LOV_GROUP, GlobalVal.HEADER_MAX_LENGTH,
			GlobalVal.HEADER_ORDER_TAGGING, GlobalVal.HEADER_MANDATORY,
			GlobalVal.HEADER_READ_ONLY, GlobalVal.HEADER_IS_VISIBLE,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_REGEX_PATTERN,
			GlobalVal.HEADER_IS_HOLIDAY_ALLOWED, GlobalVal.HEADER_EDIT,
			GlobalVal.HEADER_UUID_QUESTION

	};

	private static final String[] TEMPLATE_HEADER_MS_QESTION_LIST_SHEET_EDIT = {
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ANSWER_TYPE, GlobalVal.HEADER_IMAGE_QUALITY,
			GlobalVal.HEADER_LOV_GROUP, GlobalVal.HEADER_MAX_LENGTH,
			GlobalVal.HEADER_SURVEY_TAGGING, GlobalVal.HEADER_MANDATORY,
			GlobalVal.HEADER_READ_ONLY, GlobalVal.HEADER_IS_VISIBLE,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_REGEX_PATTERN,
			GlobalVal.HEADER_IS_HOLIDAY_ALLOWED, GlobalVal.HEADER_EDIT,
			GlobalVal.HEADER_UUID_QUESTION };

	private static final String[] TEMPLATE_HEADER_MC_QESTION_LIST_SHEET_EDIT = {
			GlobalVal.HEADER_IDENTIFIER, GlobalVal.HEADER_QUESTION_LABEL,
			GlobalVal.HEADER_ANSWER_TYPE, GlobalVal.HEADER_IMAGE_QUALITY,
			GlobalVal.HEADER_LOV_GROUP, GlobalVal.HEADER_MAX_LENGTH,
			GlobalVal.HEADER_COLLECTION_TAGGING, GlobalVal.HEADER_MANDATORY,
			GlobalVal.HEADER_READ_ONLY, GlobalVal.HEADER_IS_VISIBLE,
			GlobalVal.HEADER_IS_ACTIVE, GlobalVal.HEADER_REGEX_PATTERN,
			GlobalVal.HEADER_IS_HOLIDAY_ALLOWED, GlobalVal.HEADER_EDIT,
			GlobalVal.HEADER_UUID_QUESTION };

	UploadFormBean uploadBean = new UploadFormBean();

	@Override
	public Map<String, Object> listForm(Object params, Object orders,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO().selectAll(MsForm.class, params,
					orders, pageNumber, pageSize);
		List <MsForm> list = (List<MsForm>) result.get(GlobalKey.MAP_RESULT_LIST);
		for(int i=0; i< list.size();i++){
			String uuidForm = String.valueOf(list.get(i).getUuidForm());
			Object [][] prm = {{Restrictions.eq("msForm.uuidForm", Long.valueOf(uuidForm))}};
			Map<String, Object> rslt = this.getManagerDAO().selectAll(MsFormhistory.class, prm, null);
			if ((Long) rslt.get(GlobalKey.MAP_RESULT_SIZE) != 0){
				list.get(i).setVersion(1);
			} 
			else{
				list.get(i).setVersion(0);
			}
		}
		result.put(GlobalKey.MAP_RESULT_LIST, list);

		return result;
	}

	@Override
	public MsForm getForm(String uuid, AuditContext callerId) {
		MsForm result = this.getManagerDAO().selectOne(
				"from MsForm mf join fetch mf.msFormcategory where mf.uuidForm = :uuidForm", 
				new Object[][] {{"uuidForm", Long.valueOf(uuid)}});
		return result;
	}

	@Override
	public List getComboFormCtg(String uuidSubsystem, AuditContext callerId) {
		String[][] params = { { "uuidSubsystem", uuidSubsystem } };
		List list = this.getManagerDAO().selectAllNative(
					"eform.form.getComboFormCtg", params, null);
		return list;
	}

	@Override
	public Map<String, Object> listQuestionGroupOfForm(String uuidForm,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;		
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		
		condition.append(" and mqgof.msForm.uuidForm=:uuidForm");
		paramMap.put("uuidForm", Long.valueOf(uuidForm));
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mqgof.lineSeqOrder asc");
		
		if (pageNumber == 0 && pageSize == 0) {
			result = this.getManagerDAO().selectAll(
					"from MsQuestiongroupofform mqgof join fetch mqgof.msForm join fetch mqgof.msQuestiongroup where 1=1"
							+ condition.toString() + orderQuery.toString(),
					paramMap);
		} 
		else {
			result = this.getManagerDAO().selectAll(
					"from MsQuestiongroupofform mqgof join fetch mqgof.msForm join fetch mqgof.msQuestiongroup where 1=1"
							+ condition.toString() + orderQuery.toString(),
					"select count(*) from MsQuestiongroupofform mqgof join mqgof.msForm join mqgof.msQuestiongroup where 1=1"
							+ condition.toString(),
					paramMap, pageNumber, pageSize);
		}
		return result;
	}

	@Override
	public Map<String, Object> listFormOfGroup(String uuidForm,
			AuditContext callerId) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();			
		condition.append(" and mfog.msForm.uuidForm=:uuidForm");
		paramMap.put("uuidForm", Long.valueOf(uuidForm));
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mfog.amMsgroup.isActive desc");
					
		Map<String, Object> result = this.getManagerDAO().selectAll(
				"from MsFormofgroup mfog join fetch mfog.msForm join fetch mfog.amMsgroup where 1=1"
						+ condition.toString() + orderQuery.toString(), paramMap);
		List<MsFormofgroup> resultList = (List) result
				.get(GlobalKey.MAP_RESULT_LIST);

		List<MsFormofgroup> finalResult = new ArrayList<MsFormofgroup>();

		for (MsFormofgroup msFormofgroup : resultList) {
			if ("0".equals(msFormofgroup.getAmMsgroup().getIsDeleted())) {
				finalResult.add(msFormofgroup);
			}
		}

		result.put(GlobalKey.MAP_RESULT_LIST, finalResult);
		return result;
	}

	@Override
	public List<AmMsgroup> getListComboGroup(Object params, Object orders,
			AuditContext callerId) {
		Map<String, Object> listGroup = getListGroup(params, orders, callerId);
		List<AmMsgroup> group = (List<AmMsgroup>) listGroup
				.get(GlobalKey.MAP_RESULT_LIST);
		return group;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String insertForm(MsForm msForm, String selectedGroup,
			String selectedQGroup, String order, AuditContext callerId) {
		// check ketersediaan form name di database
		boolean exist = this.checkFormName(msForm.getFormName(), String.valueOf(msForm
				.getAmMssubsystem().getUuidMsSubsystem()), callerId);
		if (exist) {
			throw new EntityNotUniqueException(
					this.messageSource.getMessage("service.global.existed", 
							new Object[]{"Form Description"}, this.retrieveLocaleAudit(callerId))
					, msForm.getFormName());
		}

		msForm.setDtmCrt(new Date());
		msForm.setUsrCrt(callerId.getCallerId());
		msForm.setFormLastUpdate(new Date());
		this.getManagerDAO().insert(msForm);
		this.auditManager.auditAdd(msForm, auditInfoF, callerId.getCallerId(), "");
		this.insertFormOfGroup(String.valueOf(msForm.getUuidForm()), selectedGroup, String.valueOf(msForm
				.getAmMssubsystem().getUuidMsSubsystem()), callerId);
		this.insertQGroupOfForm(selectedQGroup, order, String.valueOf(msForm.getUuidForm()), callerId);
		String result = String.valueOf(msForm.getUuidForm());
		/*intFormLogic.synSchema(msForm.getFormName(), "ins",
				callerId.getCallerId());*/
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String updateForm(MsForm msForm, String selectedGroup,
			String selectedQGroup, String order, AuditContext callerId) {
		MsForm dbModel = this.getManagerDAO().selectOne(MsForm.class,
				msForm.getUuidForm());

		// check ketersediaan form name di database
		boolean exist = this.checkFormName(msForm.getFormName(), String.valueOf(msForm
				.getAmMssubsystem().getUuidMsSubsystem()), callerId);
		if (exist) {
			if (!StringUtils.equalsIgnoreCase(dbModel.getFormName(),
					msForm.getFormName())) {
				throw new EntityNotUniqueException(
						this.messageSource.getMessage("service.global.existed", 
								new Object[]{"Form Description"}, this.retrieveLocaleAudit(callerId)),
					msForm.getFormName());
			}
		}

		dbModel.setFormName(msForm.getFormName());
		dbModel.setAmMssubsystem(msForm.getAmMssubsystem());
		dbModel.setMsFormcategory(msForm.getMsFormcategory());
		dbModel.setIsActive(msForm.getIsActive());
		dbModel.setIsPrintable(msForm.getIsPrintable());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());
		dbModel.setFormLastUpdate(new Date());

		this.auditManager.auditEdit(dbModel, auditInfoF,
				callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
		this.insertFormOfGroup(String.valueOf(msForm.getUuidForm()), selectedGroup, String.valueOf(msForm
				.getAmMssubsystem().getUuidMsSubsystem()), callerId);
		this.insertQGroupOfForm(selectedQGroup, order,
				String.valueOf(msForm.getUuidForm()), callerId);
		String result = String.valueOf(msForm.getUuidForm());
		/*intFormLogic.synSchema(msForm.getFormName(), "upd",
				callerId.getCallerId());*/
		return result;
	}

	private boolean checkFormName(String formName, String uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { Restrictions.eq(
				"amMssubsystem.uuidMsSubsystem", Long.valueOf(uuidSubsystem)) } };

		Map<String, Object> mapForm = this.getManagerDAO().list(MsForm.class,
				params, null);
		List<MsForm> listForm = (List<MsForm>) mapForm
				.get(GlobalKey.MAP_RESULT_LIST);

		for (int i = 0; i < listForm.size(); i++) {
			if (StringUtils.equalsIgnoreCase(listForm.get(i).getFormName(),
					formName)) {
				return true;
			}
		}

		return false;
	}

	private void insertFormOfGroup(String uuidForm, String selectedGroup,
			String uuidSubsystem, AuditContext callerId) {

		// ambil semua group dalam satu subsystem
		Object[][] paramsGrp = { { Restrictions.eq(
				"amMssubsystem.uuidMsSubsystem", Long.valueOf(uuidSubsystem)) } };
		Map<String, Object> mapGroup = this.getManagerDAO().list(
				AmMsgroup.class, paramsGrp, null);
		List<AmMsgroup> listAmMsgroups = (List<AmMsgroup>) mapGroup
				.get(GlobalKey.MAP_RESULT_LIST);

		// delete semua group dalam ms_formofgroup dalam satu subsystem dan satu form
		for (int i = 0; i < listAmMsgroups.size(); i++) {
			LOG.info("deleting uuidMsGroup={}", listAmMsgroups.get(i)
					.getUuidMsGroup());
			String[][] params = {
					{ "uuidMsGroup", String.valueOf(listAmMsgroups.get(i).getUuidMsGroup()) },
					{ "uuidForm", uuidForm } };
			this.getManagerDAO().deleteNative("eform.form.delAllGroup", params);
		}

		if (!StringUtils.EMPTY.equals(selectedGroup) && null != selectedGroup) {
			// insert ulang semua group yang dipilih
			String[] groups = selectedGroup.split(",");
			for (int i = 0; i < groups.length; i++) {
				LOG.info("inserting uuidMsGroup={}", groups[i]);
				AmMsgroup amMsgroup = this.getManagerDAO().selectOne(
						AmMsgroup.class, Long.valueOf(groups[i]));
				MsForm msForm = this.getManagerDAO().selectOne(MsForm.class,
						Long.valueOf(uuidForm));

				MsFormofgroup msFormofgroup = new MsFormofgroup();
				msFormofgroup.setAmMsgroup(amMsgroup);
				msFormofgroup.setMsForm(msForm);
				msFormofgroup.setUsrCrt(callerId.getCallerId());
				msFormofgroup.setDtmCrt(new Date());

				this.getManagerDAO().insert(msFormofgroup);
				this.auditManager.auditAdd(msFormofgroup, auditInfoFoG, callerId.getCallerId(), "");				
			}
		}
	}

	private Map<String, Object> getListGroup(Object params, Object orders,
			AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO()
				.selectAll(AmMsgroup.class, params, orders);
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertQGroupOfForm(String selectedQGroupArr, String order,
			String uuidForm, AuditContext callerId) {
		String[][] param = { { "uuidForm", uuidForm } };
		this.getManagerDAO()
				.deleteNativeString(
						"delete from MS_QUESTIONGROUPOFFORM where UUID_FORM = :uuidForm",
						param);

		if (selectedQGroupArr != null && !selectedQGroupArr.isEmpty()) {
			String[] selectedQGroup = selectedQGroupArr.split(",");
			String[] orderQGroup = order.split(",");
			for (int i = 0; i < selectedQGroup.length; i++) {
				MsForm msForm = this.getManagerDAO().selectOne(MsForm.class,
						Long.valueOf(uuidForm));
				MsQuestiongroup msQuestiongroup = this.getManagerDAO()
						.selectOne(MsQuestiongroup.class, Long.valueOf(selectedQGroup[i]));
				MsQuestiongroupofform bean = new MsQuestiongroupofform();
				bean.setMsForm(msForm);
				bean.setMsQuestiongroup(msQuestiongroup);
				bean.setUsrCrt(callerId.getCallerId());
				bean.setDtmCrt(new Date());
				bean.setLineSeqOrder(Integer.parseInt(orderQGroup[i]));
				this.getManagerDAO().insert(bean);
				this.auditManager.auditAdd(bean, auditInfoQGoF, callerId.getCallerId(), "");
			}
		}
	}

	@Override
	public List<MsQuestiongroup> getListSelectedQG(String[] selectedQGroupArr,
			AuditContext callerId) {
		List<MsQuestiongroup> result = new ArrayList<MsQuestiongroup>();
		if (selectedQGroupArr != null
				&& !ArrayUtils.isEmpty(selectedQGroupArr)) {
			for (int i = 0; i < selectedQGroupArr.length; i++) {
				LOG.info("Select Question Group UUID {} ",
						selectedQGroupArr[i]);
				MsQuestiongroup msQuestiongroup = this.getManagerDAO()
						.selectOne(MsQuestiongroup.class,
								Long.valueOf(selectedQGroupArr[i]));
				result.add(msQuestiongroup);
			}
		}
		return result;
	}

	@Override
	public Map<String, Object> listQuestionOfGroup(long uuidQuestionGroup, int pageNumber, 
			int pageSize, AuditContext callerId) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and mqog.msQuestiongroup.uuidQuestionGroup=:uuidQuestionGroup");
		condition.append(" and mqog.msQuestion.isActive=:isActive");
		paramMap.put("uuidQuestionGroup", uuidQuestionGroup);
		paramMap.put("isActive", "1");
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mqog.seqOrder asc");
		Map<String, Object> result = this.getManagerDAO().selectAll(
				"from MsQuestionofgroup mqog join fetch mqog.msQuestiongroup join fetch mqog.msQuestion where 1=1"
						+ condition.toString() + orderQuery.toString(),
				"select count(*) from MsQuestionofgroup mqog join mqog.msQuestiongroup join mqog.msQuestion where 1=1"
						+ condition.toString(),
				paramMap, pageNumber, pageSize);
		return result;
	}

	@Override
	public MsQuestiongroup getQuestiongroup(String uuid, AuditContext callerId) {
		MsQuestiongroup result = this.getManagerDAO()
					.selectOne(MsQuestiongroup.class, Long.valueOf(uuid));
		return result;
	}

	@Override
	public MsQuestion getQuestion(String uuid, AuditContext callerId) {
		MsQuestion result = this.getManagerDAO().selectOne(
				"from MsQuestion mq join fetch mq.msAnswertype where mq.uuidQuestion = :uuidQuestion", 
				new Object[][] {{"uuidQuestion", Long.valueOf(uuid)}});
		return result;
	}

	@Override
	public MsQuestionrelevant getQRelevant(String uuidQuestion,
			String uuidQuestionGroup, String uuidForm, AuditContext calledId) {
		Object[][] params = {
				{ Restrictions.eq("msQuestiongroup.uuidQuestionGroup",
						Long.valueOf(uuidQuestionGroup)) },
				{ Restrictions.eq("msForm.uuidForm", Long.valueOf(uuidForm)) },
				{ Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(uuidQuestion)) } };
		MsQuestionrelevant	result = (MsQuestionrelevant) this.getManagerDAO().selectOne(
				MsQuestionrelevant.class, params);
		return result;
	}
	
	@Override
	public MsQuestionrelevant getQRelevantPublish(String uuidQuestion,
		String uuidQuestionGroup, String uuidForm, AuditContext calledId) {
		BigInteger uuidFormHist = (BigInteger) this.getManagerDAO().selectOneNativeString("select TOP 1 UUID_FORM_HISTORY from MS_FORMHISTORY where UUID_FORM = '"+uuidForm+"' order by FORM_VERSION desc", null);
		
		Object[][] params = {
				{ Restrictions.eq("msFormhistory.uuidFormHistory",
						Long.valueOf(uuidFormHist.toString())) },
				{ Restrictions.eq("msQuestiongroup.uuidQuestionGroup",
						Long.valueOf(uuidQuestionGroup)) },
				{ Restrictions.eq("msForm.uuidForm", Long.valueOf(uuidForm)) },
				{ Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(uuidQuestion)) } };
		
		MsFormquestionset fqs = this.getManagerDAO().selectOne(MsFormquestionset.class, params);
		MsQuestionrelevant result = new MsQuestionrelevant();
		result.setMsQuestion(fqs.getMsQuestion());
		result.setMsForm(fqs.getMsForm());
		result.setMsQuestiongroup(fqs.getMsQuestiongroup());
		result.setRelevant(fqs.getRelevant());
		result.setCalculate(fqs.getCalculate());
		result.setChoiceFilter(fqs.getChoiceFilter());
		result.setQuestionValidation(fqs.getQuestionValidation());
		result.setQuestionErrorMessage(fqs.getQuestionErrorMessage());
		result.setQuestionValue(fqs.getQuestionValue());
		result.setRelevantMandatory(fqs.getRelevantMandatory());
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteFormOfGroup(String uuidFormOfGroup, AuditContext callerId) {
		LOG.info("Delete Form of Group UUID {}", uuidFormOfGroup);
		MsFormofgroup msFormofgroup = this.getManagerDAO().selectOne(
				MsFormofgroup.class, Long.valueOf(uuidFormOfGroup));
		this.auditManager.auditDelete(msFormofgroup, auditInfoFoG, callerId.getCallerId(), "");
		this.getManagerDAO().delete(msFormofgroup);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveFormOfGroup(String uuidForm, String[] selectedGroupArr,
			AuditContext callerId) {
		if (selectedGroupArr != null && !selectedGroupArr[0].isEmpty()) {
			String[] selectedGroup = selectedGroupArr[0].split(",");
			this.insertFormOfGroup(uuidForm, selectedGroup, callerId);
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertFormOfGroup(String uuidForm, String[] selectedGroup,
			AuditContext callerId) {
		MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, Long.valueOf(uuidForm));
		for (int i = 0; i < selectedGroup.length; i++) {
			AmMsgroup amMsgroup = this.getManagerDAO().selectOne(
					AmMsgroup.class, Long.valueOf(selectedGroup[i]));
			MsFormofgroup msFormofgroup = new MsFormofgroup();
			msFormofgroup.setMsForm(msForm);
			msFormofgroup.setAmMsgroup(amMsgroup);
			msFormofgroup.setDtmCrt(new Date());
			msFormofgroup.setUsrCrt(callerId.getCallerId());
			this.getManagerDAO().insert(msFormofgroup);
			this.auditManager.auditAdd(msFormofgroup, auditInfoFoG, callerId.getCallerId(), "");
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String insertQRelevant(MsQuestionrelevant msQuestionrelevant,
			String uuidForm, String uuidQuestion, String uuidQuestionGroup,
			AuditContext callerId) {
	

		MsForm msForm = this.getManagerDAO().selectOne(MsForm.class,
				Long.valueOf(uuidForm));
		MsQuestion msQuestion = this.getManagerDAO().selectOne(
				MsQuestion.class, Long.valueOf(uuidQuestion));
		MsQuestiongroup msQuestiongroup = this.getManagerDAO().selectOne(
				MsQuestiongroup.class, Long.valueOf(uuidQuestionGroup));

		String relevant = msQuestionrelevant.getRelevant();
		String[] choiceFilter = msQuestionrelevant.getChoiceFilter()
				.replace(" ", StringUtils.EMPTY)
				.replaceAll("[{}]+", StringUtils.EMPTY).split(",");
		String questionValidation = msQuestionrelevant
				.getQuestionValidation();
		String questionValue = msQuestionrelevant.getQuestionValue()
				.replace(" ", StringUtils.EMPTY)
				.replaceAll("[{}]+", StringUtils.EMPTY);
		String relevantMandatory = msQuestionrelevant.getRelevantMandatory();

		// check refId relevant yang dimasukkan ada atau tidak
		if (relevant != null && !relevant.isEmpty()) {
			List index1 = new ArrayList<>();
			List index2 = new ArrayList<>();
			for (int idx1 = relevant.indexOf("{"); idx1 >= 0; idx1 = relevant
					.indexOf("{", idx1 + 1)) {
				index1.add(idx1);
			}
			for (int idx2 = relevant.indexOf("}"); idx2 >= 0; idx2 = relevant
					.indexOf("}", idx2 + 1)) {
				index2.add(idx2);
			}

			if (index1.size() == index2.size() && !index1.isEmpty()) {
				for (int x = 0; x < index1.size(); x++) {
					Integer startIdx = (Integer) index1.get(x);
					Integer endIdx = (Integer) index2.get(x);
					if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
						throw new FormException(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Identifier"}, this.retrieveLocaleAudit(callerId)),
								FormException.Reason.INVALID_IDENTIFIER);
					}
					String refIdRel = relevant.substring(startIdx + 1,
							endIdx);
					String logic = relevant.substring(endIdx + 1,
							endIdx + 3);
					if (!"==".equals(logic) && !"!=".equals(logic)) {
						throw new FormException(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Identifier"}, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.INVALID_IDENTIFIER);
					}

					if (!refIdRel.contains("$")) {
						Object[][] paramsRel = {
								{ Restrictions.eq("refId", refIdRel) },
								{ Restrictions.eq("isActive", "1") },
								{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
										msForm.getAmMssubsystem().getUuidMsSubsystem())} };
						MsQuestion checkRefIdRel = this.getManagerDAO()
								.selectOne(MsQuestion.class, paramsRel);
						if (checkRefIdRel == null) {
							throw new EntityNotFoundException(
								this.messageSource.getMessage("businesslogic.global.entitynotfound", 
										new Object[]{"Question Identifier Relevant"}, this.retrieveLocaleAudit(callerId)),
								refIdRel);
						}
					}
				}
			} 
			else {
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Relevant"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_SCRIPT);
			}
		}

		// check refId choice filter yang dimasukkan ada atau tidak
		if (choiceFilter != null && choiceFilter.length > 0) {
			String current = choiceFilter[0];
			boolean found = false;
			for (int j = 1; j < choiceFilter.length; j++) {
				if (current.equals(choiceFilter[j]) && !found) {
					found = true;
					throw new FormException(
							this.messageSource.getMessage("businesslogic.global.errorduplicate", 
									new Object[]{"Identifier"}, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.INVALID_IDENTIFIER);
				}
			}
			for (int i = 0; i < choiceFilter.length
					&& choiceFilter.length <= 5; i++) {
				if (!choiceFilter[i].isEmpty()
						&& !choiceFilter[i].contains("$")) {
					Object[][] paramsCf = {
							{ Restrictions.eq("refId", choiceFilter[i]) },
							{ Restrictions.eq("isActive", "1") },
							{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
									msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
					MsQuestion checkRefIdCf = this.getManagerDAO()
							.selectOne(MsQuestion.class, paramsCf);
					if (checkRefIdCf == null) {
						throw new EntityNotFoundException(
							this.messageSource.getMessage("businesslogic.global.entitynotfound", 
										new Object[]{"Question Identifier Choice Filter"}, this.retrieveLocaleAudit(callerId)),
							choiceFilter[i]);
					}
				}
			}
		}

		// check refId questionValidation yang dimasukkan ada atau tidak
		if (questionValidation != null && !questionValidation.isEmpty()) {
			if (StringUtils.startsWithIgnoreCase(questionValidation, "{\"file\"")) {
				
			} else if (StringUtils.startsWithIgnoreCase(questionValidation, "BETWEEN(")) {
				
			} else if (questionValidation.indexOf("{") >= 0
					&& questionValidation.indexOf("}") >= 0) {
				List index1 = new ArrayList<>();
				List index2 = new ArrayList<>();
				List index3 = new ArrayList<>();
				List index4 = new ArrayList<>();
				List index5 = new ArrayList<>();
				for (int idx1 = questionValidation.indexOf("{"); idx1 >= 0; idx1 = questionValidation
						.indexOf("{", idx1 + 1)) {
					index1.add(idx1);
				}
				for (int idx2 = questionValidation.indexOf("}"); idx2 >= 0; idx2 = questionValidation
						.indexOf("}", idx2 + 1)) {
					index2.add(idx2);
				}

				// index of ( dan )
				for (int idx3 = questionValidation.indexOf("("); idx3 >= 0; idx3 = questionValidation
						.indexOf("(", idx3 + 1)) {
					index3.add(idx3);
				}
				for (int idx4 = questionValidation.indexOf(")"); idx4 >= 0; idx4 = questionValidation
						.indexOf(")", idx4 + 1)) {
					index4.add(idx4);
				}

				// index of |
				for (int idx5 = questionValidation.indexOf("|"); idx5 >= 0; idx5 = questionValidation
						.indexOf("|", idx5 + 1)) {
					index5.add(idx5);
				}

				if (index1.size() == index2.size()
						&& index3.size() == index4.size()
						&& index5.size() % 2 == 0) {
					for (int x = 0; x < index3.size(); x++) {
						Integer startIdx2 = (Integer) index3.get(x);
						Integer endIdx2 = (Integer) index4.get(x);
						if (startIdx2 < 0 || endIdx2 < 0
								|| endIdx2 < startIdx2) {
							throw new FormException(
									this.messageSource.getMessage("businesslogic.global.error", 
											new Object[]{"Format Identifier"}, this.retrieveLocaleAudit(callerId)),
								FormException.Reason.INVALID_IDENTIFIER);
						}
					}

					for (int x = 0; x < index1.size(); x++) {
						Integer startIdx = (Integer) index1.get(x);
						Integer endIdx = (Integer) index2.get(x);
						if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
							throw new FormException(
									this.messageSource.getMessage("businesslogic.global.error", 
											new Object[]{"Format Identifier"}, this.retrieveLocaleAudit(callerId)),
								FormException.Reason.INVALID_IDENTIFIER);
						}

						String refIdValidation = questionValidation
								.substring(startIdx + 1, endIdx);
						if (!refIdValidation.contains("$")) {
							Object[][] paramsVal = {
									{ Restrictions.eq("refId", refIdValidation) },
									{ Restrictions.eq("isActive", "1") },
									{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
											msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
							MsQuestion checkRefIdVal = this.getManagerDAO()
									.selectOne(MsQuestion.class, paramsVal);
							if (checkRefIdVal == null) {
								throw new EntityNotFoundException(
										this.messageSource.getMessage("businesslogic.form.fornotexist", 
												new Object[]{"Question Identifier","Validation"}, this.retrieveLocaleAudit(callerId)),
									refIdValidation);
							}
						}
					}
				} 
				else {
					throw new FormException(
							this.messageSource.getMessage("businesslogic.global.error", 
									new Object[]{"Format Question Validation"}, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.INVALID_SCRIPT);
				}
			} 
			else if (questionValidation.indexOf("BLACKLIST") >= 0) {
				
			}
			else {
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Question Validation"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_SCRIPT);
			}
		}

		// check refId questionValue yang dimasukkan ada atau tidak
		int kurungBukaValue = countCharacter(msQuestionrelevant.getQuestionValue(),
				'(', callerId);
		int kurungTutupValue = countCharacter(msQuestionrelevant.getQuestionValue(),
				')', callerId);
		int kurawalBukaValue = countCharacter(msQuestionrelevant.getQuestionValue(),
				'{', callerId);
		int kurawalTutupValue = countCharacter(
				msQuestionrelevant.getQuestionValue(), '}', callerId);
		if (kurungBukaValue != kurungTutupValue || kurawalBukaValue != kurawalTutupValue) {
			throw new FormException(
					this.messageSource.getMessage("businesslogic.global.error", 
							new Object[]{"Format Question Value"}, this.retrieveLocaleAudit(callerId)),
				FormException.Reason.INVALID_SCRIPT);
		}
		if (!StringUtils.isEmpty(questionValue)) {
			msQuestionrelevant.setQuestionValue(msQuestionrelevant.getQuestionValue().replaceAll("(?i)COPY\\(", "copy\\("));
			msQuestionrelevant.setQuestionValue(msQuestionrelevant.getQuestionValue().replaceAll("(?i)COPYQUESTION\\(", "copyQuestion\\("));
			msQuestionrelevant.setQuestionValue(msQuestionrelevant.getQuestionValue().replaceAll("(?i)COPYDUKCAPIL\\(", "copyDukcapil\\("));
			boolean isCopy = false;
			if (msQuestionrelevant.getQuestionValue().toUpperCase()
					.indexOf("COPY(") != 0) {
				if (msQuestionrelevant.getQuestionValue().toUpperCase()
						.indexOf("COPYQUESTION(") != 0) {
					if (msQuestionrelevant.getQuestionValue().toUpperCase()
							.indexOf("COPYDUKCAPIL(") != 0) {
						if (msQuestionrelevant.getQuestionValue().indexOf("{") != 0
								&& msQuestionrelevant.getQuestionValue().indexOf(
										"}") != (msQuestionrelevant
										.getQuestionValue().length() - 1)) {
							throw new FormException(
									this.messageSource.getMessage("businesslogic.global.error", 
											new Object[]{"Format Question Value"}, this.retrieveLocaleAudit(callerId)),
								FormException.Reason.INVALID_SCRIPT);
						}
					}else{
						isCopy = true;
					}
				}
			} 
			else {
				isCopy = true;
			}

			if (!isCopy) {
				if (StringUtils.startsWithIgnoreCase(msQuestionrelevant.getQuestionValue(), "{\"file\"")) {
					
				} else if (StringUtils.startsWithIgnoreCase(msQuestionrelevant.getQuestionValue(), "{\"table\"")) {
					
				} else if (!questionValue.isEmpty()
						&& !questionValue.contains("$")) {
					if(GlobalVal.ANSWER_TYPE_ADDRESS_MULTILINE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
						String oneExpression[] = msQuestionrelevant.getQuestionValue().split(";");
						for(int idx=0; idx<oneExpression.length; idx++) {
							//cek apakah argument2 dan argument3 memiliki answer type yang sama
							String[] splitQValue = oneExpression[idx].split(",");
							for(int j=0; j<splitQValue.length; j++) {
								if (!StringUtils.isEmpty(splitQValue[j])){
									String argument2 = StringUtils.EMPTY;
									if(oneExpression[idx].contains("copy(")) {
										if(j==0) {
											j=1;
										}
										argument2 = splitQValue[j].replace("{", "").replace("}", "").replace("copyQuestion(", "").replace(")", "").replace("copy(", "");
									}else if(oneExpression[idx].contains("copyQuestion")) {
										argument2 = splitQValue[j].replace("{", "").replace("}", "").replace("copyQuestion(", "").replace(")", "").replace("copy(", "");
									}
									if(StringUtils.isBlank(argument2)) {
										continue;
									}
									Object[][] paramsArg2 = {
											{ Restrictions.eq("refId", argument2) },
											{ Restrictions.eq("isActive", "1") },
											{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
													msForm.getAmMssubsystem() .getUuidMsSubsystem()) } };
									MsQuestion checkArg2 = this.getManagerDAO()
											.selectOne(MsQuestion.class, paramsArg2);
									if (checkArg2 == null) {
										throw new EntityNotFoundException(
												this.messageSource.getMessage("businesslogic.form.fornotexist", 
														new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
											questionValue);
									}
								}
							}
						}
					}else {
						Object[][] paramsQv = {
								{ Restrictions.eq("refId", questionValue) },
								{ Restrictions.eq("isActive", "1") },
								{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
										msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
						MsQuestion checkRefIdQv = this.getManagerDAO()
								.selectOne(MsQuestion.class, paramsQv);
						if (checkRefIdQv == null) {
							throw new EntityNotFoundException(
									this.messageSource.getMessage("businesslogic.form.fornotexist", 
											new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
								questionValue);
						}
						else{
							//cek apakah answer type yang dicopy sama atau tidak
							this.getManagerDAO().fetch(checkRefIdQv.getMsAnswertype());
							if (!String.valueOf(checkRefIdQv.getMsAnswertype().getUuidAnswerType()).equals(
									String.valueOf(msQuestion.getMsAnswertype().getUuidAnswerType())) &&
									!GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(checkRefIdQv.getMsAnswertype().getCodeAnswerType())){
								throw new EntityNotFoundException(
									this.messageSource.getMessage("businesslogic.form.answertypenotmath", 
											new Object[]{"Question Identifier of Question Value"}, this.retrieveLocaleAudit(callerId)),
									questionValue);
							}
						}
					}
				}
			}
			else{
				if(GlobalVal.ANSWER_TYPE_ADDRESS_MULTILINE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
					String oneExpression[] = msQuestionrelevant.getQuestionValue().split(";");
					for(int idx=0; idx<oneExpression.length; idx++) {
						//cek apakah argument2 dan argument3 memiliki answer type yang sama
						String[] splitQValue = oneExpression[idx].split(",");
						for(int j=0; j<splitQValue.length; j++) {
							if (!StringUtils.isEmpty(splitQValue[j])){
								String argument2 = StringUtils.EMPTY;
								if(oneExpression[idx].contains("copy(")) {
									if(j==0) {
										j=1;
									}
									argument2 = splitQValue[j].replace("{", "").replace("}", "").replace("copyQuestion(", "").replace(")", "").replace("copy(", "");
								}else if(oneExpression[idx].contains("copyQuestion")) {
									argument2 = splitQValue[j].replace("{", "").replace("}", "").replace("copyQuestion(", "").replace(")", "").replace("copy(", "");
								}
								if(StringUtils.isBlank(argument2)) {
									continue;
								}
								Object[][] paramsArg2 = {
										{ Restrictions.eq("refId", argument2) },
										{ Restrictions.eq("isActive", "1") },
										{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
												msForm.getAmMssubsystem() .getUuidMsSubsystem()) } };
								MsQuestion checkArg2 = this.getManagerDAO()
										.selectOne(MsQuestion.class, paramsArg2);
								if (checkArg2 == null) {
									throw new EntityNotFoundException(
											this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
										questionValue);
								}
							}
						}
					}
				}else {
					if(!msQuestionrelevant.getQuestionValue().contains("copyDukcapil")) {
						//cek apakah argument2 dan argument3 memiliki answer type yang sama
						String[] splitQValue = msQuestionrelevant.getQuestionValue().split(",");
						if (!StringUtils.isEmpty(splitQValue[1])){
							String argument2 = splitQValue[1].replace("{", "").replace("}", "");
							Object[][] paramsArg2 = {
									{ Restrictions.eq("refId", argument2) },
									{ Restrictions.eq("isActive", "1") },
									{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
											msForm.getAmMssubsystem() .getUuidMsSubsystem()) } };
							MsQuestion checkArg2 = this.getManagerDAO()
									.selectOne(MsQuestion.class, paramsArg2);
							if (checkArg2 == null) {
								throw new EntityNotFoundException(
										this.messageSource.getMessage("businesslogic.form.fornotexist", 
												new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
									questionValue);
							}
							else{
								//cek apakah answer type yang dicopy sama atau tidak
								this.getManagerDAO().fetch(checkArg2.getMsAnswertype());
								if (!String.valueOf(checkArg2.getMsAnswertype().getUuidAnswerType()).equals(
										String.valueOf(msQuestion.getMsAnswertype().getUuidAnswerType())) &&
										!GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(checkArg2.getMsAnswertype().getCodeAnswerType())){
									throw new EntityNotFoundException(
										this.messageSource.getMessage("businesslogic.form.answertypenotmath", 
												new Object[]{"Question Identifier of Question Value"}, this.retrieveLocaleAudit(callerId)),
										questionValue);
								}
							}
						}
						if (!StringUtils.isEmpty(splitQValue[2])){
							String argument3 = splitQValue[2].replace("{", "").replace("}", "").replace(")", "");
							if (!StringUtils.isEmpty(argument3)){
								Object[][] paramsArg3 = {
										{ Restrictions.eq("refId", argument3) },
										{ Restrictions.eq("isActive", "1") },
										{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
												msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
								MsQuestion checkArg3 = this.getManagerDAO()
										.selectOne(MsQuestion.class, paramsArg3);
								if (checkArg3 == null) {
									throw new EntityNotFoundException(
											this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
										questionValue);
								}
								else{
									//cek apakah answer type yang dicopy sama atau tidak
									this.getManagerDAO().fetch(checkArg3.getMsAnswertype());
									if (!String.valueOf(checkArg3.getMsAnswertype().getUuidAnswerType()).equals(
											String.valueOf(msQuestion.getMsAnswertype().getUuidAnswerType())) &&
											!GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(checkArg3.getMsAnswertype().getCodeAnswerType())){
										throw new EntityNotFoundException(
											this.messageSource.getMessage("businesslogic.form.answertypenotmath", 
													new Object[]{"Question Identifier of Question Value"}, this.retrieveLocaleAudit(callerId)),
											questionValue);
									}
								}
							}
						}
					}
				}
			}
		}

		// generate script for calculate
		int kurungBuka = countCharacter(msQuestionrelevant.getCalculate(),
				'(', callerId);
		int kurungTutup = countCharacter(msQuestionrelevant.getCalculate(),
				')', callerId);
		int kurawalBuka = countCharacter(msQuestionrelevant.getCalculate(),
				'{', callerId);
		int kurawalTutup = countCharacter(
				msQuestionrelevant.getCalculate(), '}', callerId);
		if (kurungBuka != kurungTutup || kurawalBuka != kurawalTutup) {
			throw new FormException(
					this.messageSource.getMessage("businesslogic.global.error", 
							new Object[]{"Format Calculate"}, this.retrieveLocaleAudit(callerId)),
				FormException.Reason.INVALID_SCRIPT);
		}

		StringBuilder cal = this.generateScript(msQuestionrelevant, String.valueOf(msForm
				.getAmMssubsystem().getUuidMsSubsystem()), callerId);
		
		// check refId relevantMandatory yang dimasukkan ada atau tidak
		if (relevantMandatory != null && !relevantMandatory.isEmpty()) {
			List index1 = new ArrayList<>();
			List index2 = new ArrayList<>();
			for (int idx1 = relevantMandatory.indexOf("{"); idx1 >= 0; idx1 = relevantMandatory
					.indexOf("{", idx1 + 1)) {
				index1.add(idx1);
			}
			for (int idx2 = relevantMandatory.indexOf("}"); idx2 >= 0; idx2 = relevantMandatory
					.indexOf("}", idx2 + 1)) {
				index2.add(idx2);
			}

			if (index1.size() == index2.size() && !index1.isEmpty()) {
				for (int x = 0; x < index1.size(); x++) {
					Integer startIdx = (Integer) index1.get(x);
					Integer endIdx = (Integer) index2.get(x);
					if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
						throw new FormException(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Identifier Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.INVALID_IDENTIFIER);
					}
					String refIdRel = relevantMandatory.substring(startIdx + 1,
							endIdx);
					String logic = relevantMandatory.substring(endIdx + 1,
							endIdx + 3);
					if (!"==".equals(logic) && !"!=".equals(logic)) {
						throw new FormException(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Identifier Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.INVALID_IDENTIFIER);
					}

					if (!refIdRel.contains("$")) {
						Object[][] paramsRel = {
								{ Restrictions.eq("refId", refIdRel) },
								{ Restrictions.eq("isActive", "1") },
								{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
										msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
						MsQuestion checkRefIdRel = this.getManagerDAO()
								.selectOne(MsQuestion.class, paramsRel);

						if (checkRefIdRel == null) {
							throw new EntityNotFoundException(
								this.messageSource.getMessage("businesslogic.global.entitynotfound", 
										new Object[]{"Question Identifier Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)),
								refIdRel);
						}
					}
				}
			} 
			else {
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Identifier Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_SCRIPT);
			}
		}
		//end check relevantMandatory

		msQuestionrelevant.setMsForm(msForm);
		msQuestionrelevant.setMsQuestion(msQuestion);
		msQuestionrelevant.setMsQuestiongroup(msQuestiongroup);
		if (!StringUtils.EMPTY.equals(msQuestionrelevant.getCalculate())){
			msQuestionrelevant.setCalculate(cal.toString());
		}
		else{
			msQuestionrelevant.setCalculate(StringUtils.EMPTY);
		}

		msQuestionrelevant.getMsForm().setFormLastUpdate(new Date());
		this.auditManager.auditAdd(msQuestionrelevant, auditInfoQR, callerId.getCallerId(), "");
		Object prm [][] = {{"uuidQuest", uuidQuestion}, {"uuidForm", uuidForm}};
		String isExistRel = (String) this.getManagerDAO().selectOneNativeString(
				" select cast(uuid_question_group as varchar)"
				+ " from MS_QUESTIONRELEVANT with (nolock)"
				+ " where uuid_form = :uuidForm"
				+ " and uuid_question = :uuidQuest", prm);

		if (StringUtils.isNotBlank(isExistRel) && !isExistRel.equalsIgnoreCase(uuidQuestionGroup)){
			Object prm2 [][] = {{"uuidQuest", uuidQuestion}, {"uuidForm", uuidForm},
					{"uuidGrup", isExistRel}};
			this.getManagerDAO().deleteNativeString("Delete From MS_QUESTIONRELEVANT"
					+ " where uuid_form = :uuidForm"
					+ " and uuid_question = :uuidQuest"
					+ " and uuid_question_group = :uuidGrup", prm2);
		}
		this.getManagerDAO().insert(msQuestionrelevant);
		String uuidRelevant = String.valueOf(msQuestionrelevant.getUuidQuestionRelevant());
		
		return uuidRelevant;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String updateQRelevant(MsQuestionrelevant msQuestionrelevant,
			String uuidForm, String uuidQuestion, String uuidQuestionGroup,
			AuditContext callerId) {
		MsForm msForm = this.getManagerDAO().selectOne(MsForm.class,
				Long.valueOf(uuidForm));
		MsQuestion msQuestion = this.getManagerDAO().selectOne(
				MsQuestion.class, Long.valueOf(uuidQuestion));
		MsQuestiongroup msQuestiongroup = this.getManagerDAO().selectOne(
				MsQuestiongroup.class, Long.valueOf(uuidQuestionGroup));
		
		String pattern = "^[A-Za-z 0-9 $()'{}=\"|&!+_-]*$";
		String relevant = msQuestionrelevant.getRelevant();
		String[] choiceFilter = msQuestionrelevant.getChoiceFilter()
				.replace(" ", StringUtils.EMPTY)
				.replace("{", StringUtils.EMPTY)
				.replace("}", StringUtils.EMPTY).split(",");
		String questionValidation = msQuestionrelevant
				.getQuestionValidation();
		String questionValue = msQuestionrelevant.getQuestionValue()
				.replace(" ", StringUtils.EMPTY)
				.replaceAll("[{}]+", StringUtils.EMPTY);
		String relevantMandatory = msQuestionrelevant.getRelevantMandatory();

		// check refId relevant yang dimasukkan ada atau tidak
		if (relevant != null && !relevant.isEmpty()) {
			List index1 = new ArrayList<>();
			List index2 = new ArrayList<>();
			for (int idx1 = relevant.indexOf("{"); idx1 >= 0; idx1 = relevant
					.indexOf("{", idx1 + 1)) {
				index1.add(idx1);
			}
			for (int idx2 = relevant.indexOf("}"); idx2 >= 0; idx2 = relevant
					.indexOf("}", idx2 + 1)) {
				index2.add(idx2);
			}

			if (index1.size() == index2.size() && !index1.isEmpty()) {
				for (int x = 0; x < index1.size(); x++) {
					Integer startIdx = (Integer) index1.get(x);
					Integer endIdx = (Integer) index2.get(x);
					if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
						throw new FormException(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Identifier"}, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.INVALID_IDENTIFIER);
					}
					String refIdRel = relevant.substring(startIdx + 1,
							endIdx);
					
					String logic = "";
					if((endIdx + 3) < relevant.length()){
						logic = relevant.substring(endIdx + 1, endIdx + 3);
					}

					if (!"==".equals(logic) && !"!=".equals(logic)) {
						throw new FormException(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Identifier"}, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.INVALID_IDENTIFIER);
					}

					if (!refIdRel.contains("$")) {
						Object[][] paramsRel = {
								{ Restrictions.eq("refId", refIdRel) },
								{ Restrictions.eq("isActive", "1") },
								{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
										msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
						MsQuestion checkRefIdRel = this.getManagerDAO()
								.selectOne(MsQuestion.class, paramsRel);
						if (checkRefIdRel == null) {
							throw new EntityNotFoundException(
								this.messageSource.getMessage("businesslogic.global.entitynotfound", 
										new Object[]{"Question Identifier Relevant"}, this.retrieveLocaleAudit(callerId)),
								refIdRel);
						}
					}
				}
			} 
			else {
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Relevant"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_SCRIPT);
			}
			
			boolean match = relevant.matches(pattern);
			if(!match){
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Relevant"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_SCRIPT);
			}
		}

		// check refId choice filter yang dimasukkan ada atau tidak
		if (choiceFilter != null && choiceFilter.length > 0) {
			String current = choiceFilter[0];
			boolean found = false;
			for (int j = 1; j < choiceFilter.length; j++) {
				if (!choiceFilter[j].isEmpty()) {
					if (current.equals(choiceFilter[j]) && !found) {
						found = true;
						throw new FormException(
								this.messageSource.getMessage("businesslogic.global.errorduplicate", 
										new Object[]{"Identifier"}, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.INVALID_IDENTIFIER);
					}
				}
			}
			for (int i = 0; i < choiceFilter.length
					&& choiceFilter.length <= 5; i++) {
				if (!choiceFilter[i].isEmpty()
						&& !choiceFilter[i].contains("$")) {
					Object[][] paramsCf = {
							{ Restrictions.eq("refId", choiceFilter[i]) },
							{ Restrictions.eq("isActive", "1") },
							{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
									msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
					MsQuestion checkRefIdCf = this.getManagerDAO()
							.selectOne(MsQuestion.class, paramsCf);
					if (checkRefIdCf == null) {
						throw new EntityNotFoundException(
							this.messageSource.getMessage("businesslogic.global.entitynotfound", 
										new Object[]{"Question Identifier Choice Filter"}, this.retrieveLocaleAudit(callerId)),
							choiceFilter[i]);
					}
				}
			}
		}

		// check refId questionValidation yang dimasukkan ada atau tidak
		if (questionValidation != null && !questionValidation.isEmpty()) {
			if (StringUtils.startsWithIgnoreCase(questionValidation, "{\"file\"")) {
				
			} else if (StringUtils.startsWithIgnoreCase(questionValidation, "BETWEEN(")) {
				
			} else if (questionValidation.indexOf("{") >= 0
					&& questionValidation.indexOf("}") >= 0) {
				List index1 = new ArrayList<>();
				List index2 = new ArrayList<>();
				List index3 = new ArrayList<>();
				List index4 = new ArrayList<>();
				List index5 = new ArrayList<>();
				for (int idx1 = questionValidation.indexOf("{"); idx1 >= 0; idx1 = questionValidation
						.indexOf("{", idx1 + 1)) {
					index1.add(idx1);
				}
				for (int idx2 = questionValidation.indexOf("}"); idx2 >= 0; idx2 = questionValidation
						.indexOf("}", idx2 + 1)) {
					index2.add(idx2);
				}

				// index of ( dan )
				for (int idx3 = questionValidation.indexOf("("); idx3 >= 0; idx3 = questionValidation
						.indexOf("(", idx3 + 1)) {
					index3.add(idx3);
				}
				for (int idx4 = questionValidation.indexOf(")"); idx4 >= 0; idx4 = questionValidation
						.indexOf(")", idx4 + 1)) {
					index4.add(idx4);
				}

				// index of |
				for (int idx5 = questionValidation.indexOf("|"); idx5 >= 0; idx5 = questionValidation
						.indexOf("|", idx5 + 1)) {
					index5.add(idx5);
				}

				if (index1.size() == index2.size()
						&& index3.size() == index4.size()
						&& index5.size() % 2 == 0) {
					for (int x = 0; x < index3.size(); x++) {
						Integer startIdx2 = (Integer) index3.get(x);
						Integer endIdx2 = (Integer) index4.get(x);
						if (startIdx2 < 0 || endIdx2 < 0
								|| endIdx2 < startIdx2) {
							throw new FormException(
									this.messageSource.getMessage("businesslogic.global.error", 
											new Object[]{"Format Identifier"}, this.retrieveLocaleAudit(callerId)),
								FormException.Reason.INVALID_IDENTIFIER);
						}
					}

					for (int x = 0; x < index1.size(); x++) {
						Integer startIdx = (Integer) index1.get(x);
						Integer endIdx = (Integer) index2.get(x);

						if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
							throw new FormException(
									this.messageSource.getMessage("businesslogic.global.error", 
											new Object[]{"Format Identifier"}, this.retrieveLocaleAudit(callerId)),
								FormException.Reason.INVALID_IDENTIFIER);
						}

						String refIdValidation = questionValidation
								.substring(startIdx + 1, endIdx);
						if (!refIdValidation.contains("$")) {
							Object[][] paramsVal = {
									{ Restrictions.eq("refId", refIdValidation) },
									{ Restrictions.eq("isActive", "1") },
									{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
											msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
							MsQuestion checkRefIdVal = this.getManagerDAO()
									.selectOne(MsQuestion.class, paramsVal);
							if (checkRefIdVal == null) {
								throw new EntityNotFoundException(
										this.messageSource.getMessage("businesslogic.form.fornotexist", 
												new Object[]{"Question Identifier","Validation"}, this.retrieveLocaleAudit(callerId)),
									refIdValidation);
							}
						}
					}
				} 
				else {
					throw new FormException(
							this.messageSource.getMessage("businesslogic.global.error", 
									new Object[]{"Format Question Validation"}, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.INVALID_SCRIPT);
				}
			} 
			else if (questionValidation.indexOf("BLACKLIST") >= 0) {
				
			}
			else {
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Question Validation"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_SCRIPT);
			}
		}

		// check refId questionValue yang dimasukkan ada atau tidak
		int kurungBukaValue = countCharacter(msQuestionrelevant.getQuestionValue(),
				'(', callerId);
		int kurungTutupValue = countCharacter(msQuestionrelevant.getQuestionValue(),
				')', callerId);
		int kurawalBukaValue = countCharacter(msQuestionrelevant.getQuestionValue(),
				'{', callerId);
		int kurawalTutupValue = countCharacter(
				msQuestionrelevant.getQuestionValue(), '}', callerId);
		if (kurungBukaValue != kurungTutupValue || kurawalBukaValue != kurawalTutupValue) {
			throw new FormException(
					this.messageSource.getMessage("businesslogic.global.error", 
							new Object[]{"Format Question Value"}, this.retrieveLocaleAudit(callerId)),
				FormException.Reason.INVALID_SCRIPT);
		}
		if (!StringUtils.isEmpty(questionValue)) {
			msQuestionrelevant.setQuestionValue(msQuestionrelevant.getQuestionValue().replaceAll("(?i)COPY\\(", "copy\\("));
			msQuestionrelevant.setQuestionValue(msQuestionrelevant.getQuestionValue().replaceAll("(?i)COPYQUESTION\\(", "copyQuestion\\("));
			msQuestionrelevant.setQuestionValue(msQuestionrelevant.getQuestionValue().replaceAll("(?i)COPYDUKCAPIL\\(", "copyDukcapil\\("));
			boolean isCopy = false;
			if (msQuestionrelevant.getQuestionValue().toUpperCase()
					.indexOf("COPY(") != 0) {
				if (msQuestionrelevant.getQuestionValue().toUpperCase()
						.indexOf("COPYQUESTION(") != 0) {
					if (msQuestionrelevant.getQuestionValue().toUpperCase()
							.indexOf("COPYDUKCAPIL(") != 0) {
						if (msQuestionrelevant.getQuestionValue().indexOf("{") != 0
								&& msQuestionrelevant.getQuestionValue().indexOf(
										"}") != (msQuestionrelevant
										.getQuestionValue().length() - 1)) {
							throw new FormException(
									this.messageSource.getMessage("businesslogic.global.error", 
											new Object[]{"Format Question Value"}, this.retrieveLocaleAudit(callerId)),
								FormException.Reason.INVALID_SCRIPT);
						}
					}else{
						isCopy = true;
					}
				}
			} 
			else {
				isCopy = true;
			}

			if (!isCopy) {
				if (StringUtils.startsWithIgnoreCase(msQuestionrelevant.getQuestionValue(), "{\"file\"")) {
					
				} else if (StringUtils.startsWithIgnoreCase(msQuestionrelevant.getQuestionValue(), "{\"table\"")) {
					
				} else if (!questionValue.isEmpty()
						&& !questionValue.contains("$")) {
					if(GlobalVal.ANSWER_TYPE_ADDRESS_MULTILINE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
						String oneExpression[] = msQuestionrelevant.getQuestionValue().split(";");
						for(int idx=0; idx<oneExpression.length; idx++) {
							//cek apakah argument2 dan argument3 memiliki answer type yang sama
							String[] splitQValue = oneExpression[idx].split(",");
							for(int j=0; j<splitQValue.length; j++) {
								if (!StringUtils.isEmpty(splitQValue[j])){
									String argument2 = StringUtils.EMPTY;
									if(oneExpression[idx].contains("copy(")) {
										if(j==0) {
											j=1;
										}
										argument2 = splitQValue[j].replace("{", "").replace("}", "").replace("copyQuestion(", "").replace(")", "").replace("copy(", "");
									}else if(oneExpression[idx].contains("copyQuestion")) {
										argument2 = splitQValue[j].replace("{", "").replace("}", "").replace("copyQuestion(", "").replace(")", "").replace("copy(", "");
									}
									if(StringUtils.isBlank(argument2)) {
										continue;
									}
									Object[][] paramsArg2 = {
											{ Restrictions.eq("refId", argument2) },
											{ Restrictions.eq("isActive", "1") },
											{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
													msForm.getAmMssubsystem() .getUuidMsSubsystem()) } };
									MsQuestion checkArg2 = this.getManagerDAO()
											.selectOne(MsQuestion.class, paramsArg2);
									if (checkArg2 == null) {
										throw new EntityNotFoundException(
												this.messageSource.getMessage("businesslogic.form.fornotexist", 
														new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
											questionValue);
									}
								}
							}
						}
					} else {
						Object[][] paramsQv = {
								{ Restrictions.eq("refId", questionValue) },
								{ Restrictions.eq("isActive", "1") },
								{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
										msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
						MsQuestion checkRefIdQv = this.getManagerDAO()
								.selectOne(MsQuestion.class, paramsQv);
						if (checkRefIdQv == null) {
							throw new EntityNotFoundException(
									this.messageSource.getMessage("businesslogic.form.fornotexist", 
											new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
								questionValue);
						}
						else{
							//cek apakah answer type yang dicopy sama atau tidak
							this.getManagerDAO().fetch(checkRefIdQv.getMsAnswertype());
							if (!String.valueOf(checkRefIdQv.getMsAnswertype().getUuidAnswerType()).equals(
									String.valueOf(msQuestion.getMsAnswertype().getUuidAnswerType())) &&
									!GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(checkRefIdQv.getMsAnswertype().getCodeAnswerType())){
								throw new EntityNotFoundException(
									this.messageSource.getMessage("businesslogic.form.answertypenotmath", 
											new Object[]{"Question Identifier of Question Value"}, this.retrieveLocaleAudit(callerId)),
									questionValue);
							}
						}
					}
				}
			}
			else{
				if(GlobalVal.ANSWER_TYPE_ADDRESS_MULTILINE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
					String oneExpression[] = msQuestionrelevant.getQuestionValue().split(";");
					for(int idx=0; idx<oneExpression.length; idx++) {
						//cek apakah argument2 dan argument3 memiliki answer type yang sama
						String[] splitQValue = oneExpression[idx].split(",");
						for(int j=0; j<splitQValue.length; j++) {
							if (!StringUtils.isEmpty(splitQValue[j])){
								String argument2 = StringUtils.EMPTY;
								if(oneExpression[idx].contains("copy(")) {
									if(j==0) {
										j=1;
									}
									argument2 = splitQValue[j].replace("{", "").replace("}", "").replace("copyQuestion(", "").replace(")", "").replace("copy(", "");
								}else if(oneExpression[idx].contains("copyQuestion")) {
									argument2 = splitQValue[j].replace("{", "").replace("}", "").replace("copyQuestion(", "").replace(")", "").replace("copy(", "");
								}
								if(StringUtils.isBlank(argument2)) {
									continue;
								}
								Object[][] paramsArg2 = {
										{ Restrictions.eq("refId", argument2) },
										{ Restrictions.eq("isActive", "1") },
										{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
												msForm.getAmMssubsystem() .getUuidMsSubsystem()) } };
								MsQuestion checkArg2 = this.getManagerDAO()
										.selectOne(MsQuestion.class, paramsArg2);
								if (checkArg2 == null) {
									throw new EntityNotFoundException(
											this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
										questionValue);
								}
							}
						}
					}
				}else {
					if(!msQuestionrelevant.getQuestionValue().contains("copyDukcapil")) {
						//cek apakah argument2 dan argument3 memiliki answer type yang sama
						String[] splitQValue = msQuestionrelevant.getQuestionValue().split(",");
						if (!StringUtils.isEmpty(splitQValue[1])){
							String argument2 = splitQValue[1].replace("{", "").replace("}", "");
							Object[][] paramsArg2 = {
									{ Restrictions.eq("refId", argument2) },
									{ Restrictions.eq("isActive", "1") },
									{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
											msForm.getAmMssubsystem() .getUuidMsSubsystem()) } };
							MsQuestion checkArg2 = this.getManagerDAO()
									.selectOne(MsQuestion.class, paramsArg2);
							if (checkArg2 == null) {
								throw new EntityNotFoundException(
										this.messageSource.getMessage("businesslogic.form.fornotexist", 
												new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
									questionValue);
							}
							else{
								//cek apakah answer type yang dicopy sama atau tidak
								this.getManagerDAO().fetch(checkArg2.getMsAnswertype());
								if (!String.valueOf(checkArg2.getMsAnswertype().getUuidAnswerType()).equals(
										String.valueOf(msQuestion.getMsAnswertype().getUuidAnswerType())) &&
										!GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(checkArg2.getMsAnswertype().getCodeAnswerType())){
									throw new EntityNotFoundException(
										this.messageSource.getMessage("businesslogic.form.answertypenotmath", 
												new Object[]{"Question Identifier of Question Value"}, this.retrieveLocaleAudit(callerId)),
										questionValue);
								}
							}
						}
						if (!StringUtils.isEmpty(splitQValue[2])){
							String argument3 = splitQValue[2].replace("{", "").replace("}", "").replace(")", "");
							if (!StringUtils.isEmpty(argument3)){
								Object[][] paramsArg3 = {
										{ Restrictions.eq("refId", argument3) },
										{ Restrictions.eq("isActive", "1") },
										{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
												msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
								MsQuestion checkArg3 = this.getManagerDAO()
										.selectOne(MsQuestion.class, paramsArg3);
								if (checkArg3 == null) {
									throw new EntityNotFoundException(
											this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Question Identifier","Question Value"}, this.retrieveLocaleAudit(callerId)),
										questionValue);
								}
								else{
									//cek apakah answer type yang dicopy sama atau tidak
									this.getManagerDAO().fetch(checkArg3.getMsAnswertype());
									if (!String.valueOf(checkArg3.getMsAnswertype().getUuidAnswerType()).equals(
											String.valueOf(msQuestion.getMsAnswertype().getUuidAnswerType())) &&
											!GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(checkArg3.getMsAnswertype().getCodeAnswerType())){
										throw new EntityNotFoundException(
											this.messageSource.getMessage("businesslogic.form.answertypenotmath", 
													new Object[]{"Question Identifier of Question Value"}, this.retrieveLocaleAudit(callerId)),
											questionValue);
									}
								}
							}
						}
					}
				}
				
			}
		}

		// generate script for calculate
		int kurungBuka = countCharacter(msQuestionrelevant.getCalculate(),
				'(', callerId);
		int kurungTutup = countCharacter(msQuestionrelevant.getCalculate(),
				')', callerId);
		int kurawalBuka = countCharacter(msQuestionrelevant.getCalculate(),
				'{', callerId);
		int kurawalTutup = countCharacter(
				msQuestionrelevant.getCalculate(), '}', callerId);
		if (kurungBuka != kurungTutup || kurawalBuka != kurawalTutup) {
			throw new FormException(
					this.messageSource.getMessage("businesslogic.global.error", 
							new Object[]{"Format Calculate"}, this.retrieveLocaleAudit(callerId)),
				FormException.Reason.INVALID_SCRIPT);
		}

		StringBuilder cal = this.generateScript(msQuestionrelevant, String.valueOf(msForm
				.getAmMssubsystem().getUuidMsSubsystem()), callerId);
		
		// check refId relevantMandatory yang dimasukkan ada atau tidak
		if (relevantMandatory != null && !relevantMandatory.isEmpty()) {
			List index1 = new ArrayList<>();
			List index2 = new ArrayList<>();
			for (int idx1 = relevantMandatory.indexOf("{"); idx1 >= 0; idx1 = relevantMandatory
					.indexOf("{", idx1 + 1)) {
				index1.add(idx1);
			}
			for (int idx2 = relevantMandatory.indexOf("}"); idx2 >= 0; idx2 = relevantMandatory
					.indexOf("}", idx2 + 1)) {
				index2.add(idx2);
			}

			if (index1.size() == index2.size() && !index1.isEmpty()) {
				for (int x = 0; x < index1.size(); x++) {
					Integer startIdx = (Integer) index1.get(x);
					Integer endIdx = (Integer) index2.get(x);
					if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
						throw new FormException(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Identifier Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.INVALID_IDENTIFIER);
					}
					String refIdRel = relevantMandatory.substring(startIdx + 1,
							endIdx);
					String logic = relevantMandatory.substring(endIdx + 1,
							endIdx + 3);
					if (!"==".equals(logic) && !"!=".equals(logic)) {
						throw new FormException(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Identifier Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.INVALID_IDENTIFIER);
					}

					if (!refIdRel.contains("$")) {
						Object[][] paramsRel = {
								{ Restrictions.eq("refId", refIdRel) },
								{ Restrictions.eq("isActive", "1") },
								{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
										msForm.getAmMssubsystem().getUuidMsSubsystem()) } };
						MsQuestion checkRefIdRel = this.getManagerDAO()
								.selectOne(MsQuestion.class, paramsRel);
						if (checkRefIdRel == null) {
							throw new EntityNotFoundException(
								this.messageSource.getMessage("businesslogic.global.entitynotfound", 
										new Object[]{"Question Identifier Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)),
								refIdRel);
						}
					}
				}
			} 
			else {
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_SCRIPT);
			}
		}
		//end relevant mandatory

		MsQuestionrelevant bean = this.getManagerDAO().selectOne(
				MsQuestionrelevant.class, msQuestionrelevant.getUuidQuestionRelevant());

		bean.setMsQuestion(msQuestion);
		bean.setMsForm(msForm);
		
		bean.setMsQuestiongroup(msQuestiongroup);
		bean.setRelevant(relevant);
		if (!StringUtils.EMPTY.equals(msQuestionrelevant.getCalculate())){
			bean.setCalculate(cal.toString());
		}
		else{
			bean.setCalculate(StringUtils.EMPTY);
		}
		bean.setChoiceFilter(msQuestionrelevant.getChoiceFilter().replace(
				" ", StringUtils.EMPTY));

		bean.getMsForm().setFormLastUpdate(new Date());
		msForm.setFormLastUpdate(new Date());
		msForm.setVersion(msForm.getVersion() + 1);
		bean.setQuestionErrorMessage(msQuestionrelevant
				.getQuestionErrorMessage());
		bean.setQuestionValidation(questionValidation);
		bean.setQuestionValue(msQuestionrelevant.getQuestionValue());
		bean.setRelevantMandatory(msQuestionrelevant.getRelevantMandatory());
		this.auditManager.auditEdit(msForm, auditInfoF, callerId.getCallerId(), "");
		this.getManagerDAO().update(msForm);
		this.auditManager.auditEdit(bean, auditInfoQR, callerId.getCallerId(), "");
		this.getManagerDAO().update(bean);
		String uuidRelevant = String.valueOf(bean.getUuidQuestionRelevant());
		return uuidRelevant;
	}

	@Override
	public Map<String, Object> questionGroup(Object params, AuditContext callerId) {
		Map<String, Object> result = new HashMap<>();
		List list  = this.getManagerDAO().selectAllNative("eform.form.listForm", params, null);
		int	size = (int) this.getManagerDAO().selectOneNative("eform.form.cntlistForm", params);
		result.put(GlobalKey.MAP_RESULT_LIST, list);
		result.put(GlobalKey.MAP_RESULT_SIZE, size);
		return result;
	}
	
	@Override
	public List questionGroupList(Object params, AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative("eform.form.listForm", params, null);
		return list;
	}
	
	@Override
	public Integer cntQuestionGroup(Object params, AuditContext callerId) {
		Integer result = (Integer)this.getManagerDAO().selectOneNative("eform.form.cntlistForm", params);
		return result;
	}

	@Override
	public Map<String, Object> changeAuthorize(Object params,
			String uuidForm, String groupDesc, int pageNumber, int pageSize,
			AuditContext callerId) {
		String[][] temp = (String[][]) params;
		Map<String, Object> result = new HashMap<>();
		int start = (pageSize * pageNumber) - (pageSize - 1);
		int end = pageSize * pageNumber;
		String[][] realParams = { { "uuidMsSubsystem", temp[0][1] },
				{ "uuidForm", uuidForm }, { "groupDesc", groupDesc },
				{ "start", String.valueOf(start) },
				{ "end", String.valueOf(end) } };
		List list = this.getManagerDAO().selectAllNative(
				"eform.form.getUnChoosenGroup", realParams, null);
		result.put(GlobalKey.MAP_RESULT_LIST, list);

		return result;
	}

	@Override
	public int countReturnOfChangeAuthorize(Object params,
			String uuidForm, String groupDesc, AuditContext callerId) {
		String[][] temp = (String[][]) params;
		String[][] realParams = { { "uuidMsSubsystem", temp[0][1] },
				{ "uuidForm", uuidForm }, { "groupDesc", groupDesc } };
		int result = (Integer) this.getManagerDAO().selectOneNative(
					"eform.form.getCountUnChoosenGroup", realParams);
		return result;
	}

	@Override
	public Map<String, Object> getListQuestionGroupOfForm(String uuidForm,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = this.listQuestionGroupOfForm(uuidForm, pageNumber,
					pageSize, callerId);
		return result;
	}
	
	@Override
	public List getPriority(AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative(
					"eform.form.getPriority", null, null);
		return list;
	}

	@Override
	public List getBranchDescription(AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative(
					"eform.form.getBranchDescription", null, null);
		return list;
	}

	@Override
	public List getBranchDriver(AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative(
					"eform.form.getBranchDriver", null, null);
		return list;
	}

	@Override
	public List getZoneOfLocation(AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative(
					"eform.form.getZoneOfLocation", null, null);
		return list;
	}

	public List getBranchListLU(AuditContext callerId) {
		List<Map> branchListLU = new ArrayList<Map>();
		List listBranch = this.getBranchDescription(callerId);
		List listBranchDriver = this.getBranchDriver(callerId);
		int countBranch = 1;
		for (int i = 0; i < listBranch.size(); i++) {
			Map mapBranch = (Map) listBranch.get(i);
			Map mapBranchListLU = new HashMap();
			int tempBegin = this.beginOfCol(listBranchDriver,
					mapBranch.get("d0").toString(), callerId);
			if (tempBegin >= 0) {
				for (int j = 0; j < 2; j++) {
					if (j == 0) {
						mapBranchListLU.put("d" + j, mapBranch.get("d0")
								.toString());
					} 
					else {
						mapBranchListLU.put("d" + j, "BR" + (countBranch)
								+ "LU");
						countBranch++;
					}
				}
				branchListLU.add(mapBranchListLU);
			}
		}
		return branchListLU;
	}

	@Override
	public List<AmMsgroup> getComboGroup(Object params, Object orders,
			AuditContext callerId) {
		List<AmMsgroup> result =  this.getListComboGroup(params, orders, callerId);
		return result;
	}

	public StringBuilder generateScript(MsQuestionrelevant msQuestionrelevant,
			String uuidSubsystem, AuditContext callerId) {
		StringBuilder cal = new StringBuilder();
		if (!StringUtils.EMPTY.equals(msQuestionrelevant.getCalculate())
				&& msQuestionrelevant.getCalculate() != null) {
			msQuestionrelevant.setCalculate(msQuestionrelevant.getCalculate().
					replaceAll("(?i)dateformatter.age", "dateformatter.age"));

			String calc = msQuestionrelevant.getCalculate();

			String rsl;
			try {	
				rsl = calc;
			} 
			catch (Exception e) {
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Calculate"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_SCRIPT);
			}
			
			if (!(rsl.trim().toLowerCase().contains("result="))) {
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Calculate"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_SCRIPT);
			}

			// get all index of $
			String idx = StringUtils.EMPTY;
			for (int i = calc.indexOf("$"); i >= 0; i = calc
					.indexOf("$", i + 1)) {
				idx += i + ",";
			}

			// jika jumlah $ ganjil, format identifier error
			String[] index = idx.split(",");
			if (index.length % 2 == 1) {
				throw new FormException(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Identifier"}, this.retrieveLocaleAudit(callerId)),
					FormException.Reason.INVALID_IDENTIFIER);
			}

			// get identifier
			HashSet<String> hset = new HashSet<String>();
			for (int a = 0, b = 0; a < index.length / 2; a++, b = b + 2) {
				hset.add(calc.substring(Integer.parseInt(index[b]) + 1,
						Integer.parseInt(index[b + 1])));
			}

			Iterator iter = hset.iterator();
			int urutan = 0;
			String[] identifier = new String[hset.size()];
			while (iter.hasNext()) {
				identifier[urutan] = "" + iter.next();
				urutan++;
			}

			// check refId calculate yang dimasukkan ada atau tidak
			if (identifier != null && identifier.length > 0) {
				for (int i = 0; i < identifier.length; i++) {
					if (!StringUtils.isEmpty(identifier[i])) {
						Object[][] paramsCal = {
								{ Restrictions.eq("refId", identifier[i]) },
								{ Restrictions.eq("isActive", "1") },
								{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", Long.valueOf(uuidSubsystem)) } };
						MsQuestion checkRefIdCal = this.getManagerDAO()
								.selectOne(MsQuestion.class, paramsCal);
						if (checkRefIdCal == null) {
							throw new EntityNotUniqueException(
								this.messageSource.getMessage("businesslogic.global.entitynotfound", 
										new Object[]{"Question Identifier Calculate"}, this.retrieveLocaleAudit(callerId)),
								identifier[i]);
						}
					}
				}
			}

			// generate script for calculate
			String result = StringUtils.EMPTY;

			for (int i = 0; i < identifier.length; i++) {
				cal.append("var " + identifier[i].toString() + "_var = 0; ");
			}

			cal.append("for ( qBean : listOfQuestion ) { ");

			for (int i = 0; i < identifier.length; i++) {
				cal.append(" if ('" + identifier[i].toString()
						+ "'.equalsIgnoreCase(qBean.identifier_name)) { ");
				cal.append(" if (qBean.answer_type.equalsIgnoreCase('009') || qBean.answer_type.equalsIgnoreCase('010') ||");
				cal.append(" qBean.answer_type.equalsIgnoreCase('011') || qBean.answer_type.equalsIgnoreCase('012'))");
				cal.append(" { "
						+ identifier[i].toString()
						+ "_var = qBean.selectedOptionAnswers[0].code; } else { ");
				cal.append(identifier[i].toString()
						+ "_var = qBean.answer; } } ");
			}

			cal.append(" } ");

			cal.append(" /*start*/ ");
			result = msQuestionrelevant.getCalculate().replace("$", " /*$*/ ")
					.trim();

			for (int i = 0; i < identifier.length; i++) {
				result = result.replace(identifier[i].toString() + " /*$*/",
						identifier[i].toString() + "_var /*$*/");
			}

			cal.append(result);
			cal.append(" /*end*/ ");
			// end generate script

			// compile script
			if (! (
					msQuestionrelevant.getCalculate().toLowerCase().contains("dateformatter.age") ||
					msQuestionrelevant.getCalculate().toLowerCase().contains("dateformatter.month")
					) ) {
				int total = 0;
				jexlEngine = new JexlEngine();

				JexlContext context = new MapContext();

				context.set("listOfQuestion", Constant.listOfQuestion);
				context.set("qBean", new QuestionBean(new QuestionSet()));
				context.set("bean", new QuestionBean(new QuestionSet()));
				context.set("result", total);

				try {
					jexlEngine.createScript(cal.toString()).execute(context);
				} 
				catch (Exception e) {
					LOG.trace("Script generated --> {}", cal.toString());
					// get error location
					String indx = StringUtils.EMPTY;
					for (int i = e.getMessage().indexOf("'"); i >= 0; i = e
							.getMessage().indexOf("'", i + 1)) {
						indx += i + ",";
					}

					String[] indexChar = indx.split(",");
					throw new FormException(
							this.messageSource.getMessage("businesslogic.form.scripterror", 
									new Object[]{e.getMessage()
									.substring(Integer.parseInt(indexChar[0]),
											Integer.parseInt(indexChar[1]) + 1)
									.replace("/*", StringUtils.EMPTY)
									.replace("*/", StringUtils.EMPTY)}, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.INVALID_SCRIPT);
				}
			}
			// end compile
		}
		return cal;
	}

	@Override
	public Map<String, Object> exportExcel(String uuidForm, AmMsuser loginBean,
			AuditContext callerId) {
		Map<String, Object> ret = new HashMap<String, Object>();
		List<MsQuestion> questionList = new ArrayList<MsQuestion>();

		Object[][] paramsIdentifier = { { "uuidForm", uuidForm } };
		List<Map<String, Object>> identifierList = this.getManagerDAO()
				.selectAllNative("eform.form.getIdentifierByForm",
						paramsIdentifier, null);

		for (int i = 0; i < identifierList.size(); i++) {
			Map<String, Object> temp = (Map) identifierList.get(i);
			MsQuestion bean = this.getManagerDAO().selectOne(
					"from MsQuestion mq join fetch mq.msAnswertype ma join fetch mq.amMssubsystem ms "
					+ "where mq.refId = :refId and ms.uuidMsSubsystem = :uuidMsSubsystem", 
					new Object[][] {{"refId", temp.get("d0").toString()}, {"uuidMsSubsystem", 
						loginBean.getAmMssubsystem().getUuidMsSubsystem()}});
			questionList.add(bean);
		}
		
		// BIKIN EXCEL
		MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, Long.valueOf(uuidForm));
		Object[][] paramUuid = { {"uuidForm", uuidForm} };
		Integer formVersion = (Integer) this.getManagerDAO().selectOneNativeString(
				"SELECT TOP 1 FORM_VERSION "
				+ "FROM MS_FORMHISTORY"
				+ " WHERE UUID_FORM = :uuidForm ORDER BY DTM_CRT DESC", paramUuid);
		String msFormhistory = String.valueOf(formVersion);
				
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			HSSFWorkbook workbook;
			if (GlobalVal.SUBSYSTEM_MS.equals(loginBean.getAmMssubsystem()
					.getSubsystemName())) {
				
				if (formVersion == null) {
					ret.put("msg", "Version is null");
					return ret;
				} 
				else {
					workbook = this.createXlsTemplate(questionList, msForm, msFormhistory,
							TEMPLATE_HEADER_MS, callerId); 
				}
				
			} 
			else if (GlobalVal.SUBSYSTEM_MT.equals(loginBean
					.getAmMssubsystem().getSubsystemName())) {
				
				if (formVersion == null) {
					ret.put("msg", this.messageSource.getMessage("businesslogic.form.versionisnull", 
							null, this.retrieveLocaleAudit(callerId)));
					return ret;
				} 
				else {
					workbook = this.createXlsTemplateMT(questionList, msForm, msFormhistory,
							TEMPLATE_HEADER_MT, TEMPLATE_HEADER_MT_DATAMASTER,
							loginBean, callerId); 
				}
				
			}
			else if (GlobalVal.SUBSYSTEM_MC.equals(loginBean
					.getAmMssubsystem().getSubsystemName())) {
				
				if (formVersion == null) {
					ret.put("msg", this.messageSource.getMessage("businesslogic.form.versionisnull", 
							null, this.retrieveLocaleAudit(callerId)));
					return ret;
				} 
				else {
					workbook = this.createXlsTemplateMC(questionList, msForm, 
							msFormhistory, TEMPLATE_HEADER_MC, callerId);
				}
				
			}
			else {
				
				if (formVersion == null) {
					ret.put("msg", this.messageSource.getMessage("businesslogic.form.versionisnull", 
							null, this.retrieveLocaleAudit(callerId)));
					return ret;
				} 
				else {
					workbook = this.createXlsTemplate(questionList, msForm, 
							msFormhistory, TEMPLATE_HEADER_MC, callerId);
				}
				
			}
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		ret.put("name", msForm.getFormName());
		ret.put("version", formVersion);
		ret.put("result", stream.toByteArray());
		return ret;
	}
	
	public HSSFWorkbook createXlsTemplateMC(List questionList, MsForm msForm, String msFormhistory,
			String[] template, AuditContext callerId) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Form Template");
			this.createHeader(workbook, sheet, questionList, msForm, 
					msFormhistory, template, callerId);
			
			HSSFSheet sheet2 = workbook.createSheet("Installment Schedule");
			HSSFSheet sheet3 = workbook.createSheet("Collection History");
			HSSFSheet sheet4 = workbook.createSheet("Payment History");
			this.createHeader(workbook, sheet2, sheet3, sheet4, callerId);
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private void createHeader(HSSFWorkbook workbook, HSSFSheet sheet2,
			HSSFSheet sheet3, HSSFSheet sheet4, AuditContext callerId) {
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);
		
		HSSFRow row2 = sheet2.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_MC_INSTALLMENT_SCEDULER.length; i++) {
			HSSFCell cell = row2.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_MC_INSTALLMENT_SCEDULER[i]);
			cell.setCellStyle(style);
			sheet2.setColumnWidth(i, 20 * 256);
		}
		
		HSSFRow row3 = sheet3.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_MC_COLLECTION_HISTORY.length; i++) {
			HSSFCell cell = row3.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_MC_COLLECTION_HISTORY[i]);
			cell.setCellStyle(style);
			sheet3.setColumnWidth(i, 20 * 256);
		}
		
		HSSFRow row4 = sheet4.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_MC_PAYMENT_HISTORY.length; i++) {
			HSSFCell cell = row4.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_MC_PAYMENT_HISTORY[i]);
			cell.setCellStyle(style);
			sheet4.setColumnWidth(i, 20 * 256);
			
		}
	}

	public int beginOfCol(List list, String branchName, AuditContext callerId) {
		// mengetahui posisi awal kolom dengan 'branch code' x pd bag driver
		for (int i = 0; i < list.size(); i++) {
			Map map = (Map) list.get(i);
			if (map.get("d0").toString().equals(branchName)) {
				return i;
			}
		}
		return -1;
	}

	public int endOfCol(List list, String branchName, AuditContext callerId) {
		// mengetahui posisi akhir kolom dengan 'branch code' x pd bag driver
		int size = 0;
		for (int i = 0; i < list.size(); i++) {
			Map map = (Map) list.get(i);
			if (map.get("d0").toString().equals(branchName)) {
				size++;
			}
		}
		return size;
	}

	public HSSFWorkbook createXlsTemplate(List questionList, MsForm msForm, String msFormhistory,
			String[] template, AuditContext callerId) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Form Template");
			this.createHeader(workbook, sheet, questionList, msForm, msFormhistory,template, callerId);
		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	@SuppressWarnings("static-access")
	public HSSFWorkbook createXlsTemplateMT(List questionList, MsForm msForm, String msFormhistory,
			String[] template1, String[] template2, AmMsuser loginBean, AuditContext callerId) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Form Template");
			this.createHeader(workbook, sheet, questionList, msForm, msFormhistory,template1, callerId);

			if (GlobalVal.SUBSYSTEM_MT.equals(loginBean.getAmMssubsystem()
					.getSubsystemName())) {
				HSSFSheet sheetDM = workbook.createSheet("Data Master");
				this.createDataMaster(workbook, sheetDM, template2, callerId);
			}

			List priority = this.getPriority(callerId);
			CellRangeAddressList addressListPriority = new CellRangeAddressList(
					4, NUMBER_ROW_IN_VALIDATION, PRIORITY_COLUMN,
					PRIORITY_COLUMN);
			DVConstraint dvConstraintPriority = DVConstraint
					.createFormulaListConstraint("'Data Master'!$A$3:$A$"
							+ (priority.size() + 2));
			HSSFDataValidation dataValidPriority = new HSSFDataValidation(
					addressListPriority, dvConstraintPriority);
			dataValidPriority.setSuppressDropDownArrow(false);
			dataValidPriority.setEmptyCellAllowed(false);
			sheet.addValidationData(dataValidPriority);

			List branchDescription = this.getBranchDescription(callerId);
			CellRangeAddressList addressListBranch = new CellRangeAddressList(
					4, NUMBER_ROW_IN_VALIDATION, BRANCH_COLUMN, BRANCH_COLUMN);
			DVConstraint dvConstraintBranch = DVConstraint
					.createFormulaListConstraint("'Data Master'!$C$3:$C$"
							+ (branchDescription.size() + 2));
			HSSFDataValidation dataValidBranch = new HSSFDataValidation(
					addressListBranch, dvConstraintBranch);
			dataValidBranch.setSuppressDropDownArrow(false);
			dataValidBranch.setEmptyCellAllowed(false);
			sheet.addValidationData(dataValidBranch);

			List branchDriver = this.getBranchDriver(callerId);

			Name name = null;
			int countBranch = 1;
			for (int i = 0; i < branchDescription.size(); i++) {
				Map map = (Map) branchDescription.get(i);
				int tempBegin = this.beginOfCol(branchDriver, map.get("d0")
						.toString(), callerId);
				if (tempBegin >= 0) {
					int begin = tempBegin + 3;
					int end = tempBegin
							+ this.endOfCol(branchDriver, map.get("d0")
									.toString(), callerId) + 2;
					name = sheet.getWorkbook().createName();
					name.setRefersToFormula("'Data Master'!$G$" + (begin)
							+ ":$G$" + (end));
					// String nameName = map.get("d0").toString();
					// nameName = nameName.replaceAll("[^A-Za-z0-9]", "");
					name.setNameName("BR" + (countBranch) + "LU");
					countBranch++;
				}
			}

			name = sheet.getWorkbook().createName();
			name.setRefersToFormula("'Data Master'!$O$3:$P$"
					+ (countBranch - 1 + 2));
			name.setNameName("BRANCHLU");

			for (int i = 0; i < this.NUMBER_ROW_IN_VALIDATION; i++) {
				DataValidationHelper dvHelper = sheet.getDataValidationHelper();
				CellRangeAddressList addressListDriver = new CellRangeAddressList(
						i + 4, i + 4, DRIVER_COLUMN, DRIVER_COLUMN);
				String formula = "INDIRECT(VLOOKUP($B$" + (5 + i)
						+ ",BRANCHLU,2,0))";
				DataValidationConstraint dvConstraintDriver = dvHelper
						.createFormulaListConstraint(formula);
				DataValidation validation = dvHelper.createValidation(
						dvConstraintDriver, addressListDriver);
				sheet.addValidationData(validation);
			}

			List zoneOfLocation = this.getZoneOfLocation(callerId);
			CellRangeAddressList addressListLocation = new CellRangeAddressList(
					4, NUMBER_ROW_IN_VALIDATION, LOCATION_COLUMN,
					LOCATION_COLUMN);
			DVConstraint dvConstraintLocation = DVConstraint
					.createFormulaListConstraint("'Data Master'!$I$3:$I$"
							+ (zoneOfLocation.size() + 2));
			HSSFDataValidation dataValidLocation = new HSSFDataValidation(
					addressListLocation, dvConstraintLocation);
			dataValidLocation.setSuppressDropDownArrow(false);
			dataValidLocation.setEmptyCellAllowed(false);
			sheet.addValidationData(dataValidLocation);
		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createHeader(HSSFWorkbook workbook, HSSFSheet sheet,
			List questionList, MsForm msForm, String msFormhistory,String[] template
			, AuditContext callerId) {
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		
		sheet.protectSheet("password");

		HSSFRow rowForm = sheet.createRow(0);		
		HSSFCell cellFormLabel = rowForm.createCell(0);
		cellFormLabel.setCellValue(GlobalVal.HEADER_FORM_NAME);
		font.setBoldweight((short) 1000);
		style.setFont(font);
		cellFormLabel.setCellStyle(style);

		HSSFCell cellFormName = rowForm.createCell(1);
		cellFormName.setCellValue(msForm.getFormName());
		cellFormName.setCellStyle(style);
		
		HSSFRow rowVersion = sheet.createRow(1);
		HSSFCell cellVersionLabel = rowVersion.createCell(0);
		cellVersionLabel.setCellValue(GlobalVal.HEADER_FORM_VERSION);
		font.setBoldweight((short) 1000);
		style.setFont(font);
		cellVersionLabel.setCellStyle(style);
		
		HSSFCell cellFormVersion = rowVersion.createCell(1);
		cellFormVersion.setCellValue(msFormhistory);
		HSSFCellStyle styleVersion = workbook.createCellStyle(); 
		styleVersion.setAlignment(XSSFCellStyle.ALIGN_LEFT); 
		font.setBoldweight((short) 1000);
		styleVersion.setFont(font);
		styleVersion.setLocked(true); 
		cellFormVersion.setCellStyle(styleVersion);
		
		HSSFRow rowIdentifier = sheet.createRow(3);
		HSSFRow rowLabel = sheet.createRow(4);
		int idxIdentifier = 0;
		int idx = template.length;
		DataFormat format = workbook.createDataFormat();

		for (int i = 0; i < template.length + questionList.size(); i++) {

			if (i < template.length) {
				HSSFCell cell = rowLabel.createCell(i);
				cell.setCellValue(template[i]);
				HSSFCellStyle styleHeader = workbook.createCellStyle();
				styleHeader.setLocked(false);
				style.setDataFormat(format.getFormat("@"));
				sheet.setDefaultColumnStyle(i, styleHeader);

				font.setBoldweight((short) 1000);
				style.setFont(font);

				cell.setCellStyle(style);
				sheet.setColumnWidth(i, 20 * 256);
			} 
			else {
				MsQuestion bean = (MsQuestion) questionList.get(idxIdentifier);

				if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(bean
						.getMsAnswertype().getCodeAnswerType())
						|| GlobalVal.ANSWER_TYPE_DECIMAL.equals(bean
								.getMsAnswertype().getCodeAnswerType())
						|| GlobalVal.ANSWER_TYPE_NUMERIC.equals(bean
								.getMsAnswertype().getCodeAnswerType())) {
					Object[][] paramsRelevant = {
							{ Restrictions.eq("msQuestion.uuidQuestion",
									bean.getUuidQuestion()) },
							{ Restrictions.eq("msForm.uuidForm",
									msForm.getUuidForm()) } };

					MsQuestionrelevant msQuestionrelevant = this
							.getManagerDAO().selectOne(
									MsQuestionrelevant.class, paramsRelevant);
					if (null != msQuestionrelevant) {
						if (null != msQuestionrelevant.getCalculate()
								&& !StringUtils.EMPTY.equals(msQuestionrelevant
										.getCalculate())) {
							idxIdentifier++;
							continue;
						}
					}
				}

				HSSFCell cell = rowLabel.createCell(idx);
				HSSFCell cell2 = rowIdentifier.createCell(idx);
				cell2.setCellValue(bean.getRefId().toString());
				
				if ("1".equals(bean.getIsReadonly())) {
					HSSFCellStyle greyStyle = workbook.createCellStyle();
					greyStyle
							.setFillForegroundColor(IndexedColors.GREY_25_PERCENT
									.getIndex());
					greyStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
					cell2.setCellStyle(greyStyle);
					
					HSSFCellStyle greyStyle2 = workbook.createCellStyle();
					greyStyle2
							.setFillForegroundColor(IndexedColors.GREY_25_PERCENT
									.getIndex());
					greyStyle2.setFillPattern(CellStyle.SOLID_FOREGROUND);
					greyStyle2.setLocked(false);
					sheet.setDefaultColumnStyle(idx, greyStyle2);
				}
				else{
					HSSFCellStyle generalStyle = workbook.createCellStyle();
					generalStyle.setLocked(false);
					sheet.setDefaultColumnStyle(idx, generalStyle);
				}

				
				if ("1".equals(bean.getIsMandatory())) {
					cell.setCellValue(bean.getQuestionLabel().toString() + " *");
				} 
				else {
					cell.setCellValue(bean.getQuestionLabel().toString());
				}

				font.setBoldweight((short) 1000);
				style.setDataFormat(format.getFormat("@"));
				style.setFont(font);

				cell.setCellStyle(style);
				sheet.setColumnWidth(idx, 20 * 256);
				idxIdentifier++;
				idx++;
			}
		}
	}

	private int columnSize(List list, AuditContext callerId) {
		Map map = (Map) list.get(0);
		return map.size();
	}

	private void createDataMaster(HSSFWorkbook workbook, HSSFSheet sheet,
			String[] template, AuditContext callerId) {

		List listPriority = this.getPriority(callerId);
		List listBranchDescription = this.getBranchDescription(callerId);
		List listBranchDriver = this.getBranchDriver(callerId);
		List listZoneOfLocation = this.getZoneOfLocation(callerId);
		List listBranchListLU = this.getBranchListLU(callerId);

		// title
		HSSFCellStyle titleStyle = workbook.createCellStyle();
		titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
		titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
		titleStyle.setFillBackgroundColor(HSSFColor.BLACK.index);
		titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
		HSSFFont titleFont = workbook.createFont();
		titleFont.setBoldweight((short) 1000);
		titleFont.setColor(HSSFColor.WHITE.index);
		HSSFRow rowLabel = sheet.createRow(1);
		DataFormat format = workbook.createDataFormat();
		titleStyle.setDataFormat(format.getFormat("@")); 

		for (int i = 0; i < template.length; i++) {
			if (!template[i].equals(" ")) {
				HSSFCell cell = rowLabel.createCell(i);
				cell.setCellValue(template[i]);
				titleStyle.setFont(titleFont);
				cell.setCellStyle(titleStyle);
				sheet.autoSizeColumn(i);
			}
		}

		// value
		HSSFCellStyle valStyle = workbook.createCellStyle();
		valStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
		valStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		valStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		valStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);

		int rowSize1 = Math.max(listPriority.size(),
				listBranchDescription.size());
		int rowSize2 = Math.max(listBranchDriver.size(),
				listZoneOfLocation.size());
		int rowSize3 = Math.max(rowSize1, rowSize2);
		int rowSize = Math.max(rowSize3, listBranchListLU.size());

		Map valPriority = null, valBranchDesc = null, valBranchDriver = null, valZone = null, valBranchListLU = null;
		int idCol = 0, colSizePriority = columnSize(listPriority, callerId), colSizeBranchDesc = columnSize(listBranchDescription, callerId), 
			colSizeDriver = columnSize(listBranchDriver, callerId), colSizeZone = columnSize(listZoneOfLocation, callerId), 
			colSizeBranchListLU = columnSize(listBranchListLU, callerId);
		for (int i = 0; i < rowSize; i++) {
			HSSFRow rowLabelVal = sheet.createRow(i + 2);

			idCol = 0;

			if (i < listPriority.size()) {
				valPriority = (Map) listPriority.get(i);
				for (int j = 0; j < valPriority.size(); j++) {
					HSSFCell cell = rowLabelVal.createCell(j + idCol);
					cell.setCellValue(valPriority.get("d" + j).toString());
					cell.setCellStyle(valStyle);
					sheet.autoSizeColumn(i);
				}
				idCol += colSizePriority;
			} 
			else {
				idCol += colSizePriority;
			}

			idCol++;

			if (i < listBranchDescription.size()) {
				valBranchDesc = (Map) listBranchDescription.get(i);
				for (int j = 0; j < valBranchDesc.size(); j++) {
					HSSFCell cell = rowLabelVal.createCell(j + idCol);
					cell.setCellValue(valBranchDesc.get("d" + j).toString());
					cell.setCellStyle(valStyle);
					sheet.autoSizeColumn(i);
				}
				idCol += colSizeBranchDesc;
			} 
			else {
				idCol += colSizeBranchDesc;
			}

			idCol++;

			if (i < listBranchDriver.size()) {
				valBranchDriver = (Map) listBranchDriver.get(i);
				for (int j = 0; j < valBranchDriver.size(); j++) {
					HSSFCell cell = rowLabelVal.createCell(j + idCol);
					cell.setCellValue(valBranchDriver.get("d" + j).toString());
					cell.setCellStyle(valStyle);
					sheet.autoSizeColumn(i);
				}
				idCol += colSizeDriver;
			} 
			else {
				idCol += colSizeDriver;
			}

			idCol++;

			if (i < listZoneOfLocation.size()) {
				valZone = (Map) listZoneOfLocation.get(i);
				for (int j = 0; j < valZone.size(); j++) {
					HSSFCell cell = rowLabelVal.createCell(j + idCol);
					cell.setCellValue(valZone.get("d" + j).toString());
					cell.setCellStyle(valStyle);
					sheet.autoSizeColumn(i);
				}
				idCol += colSizeZone;
			} 
			else {
				idCol += colSizeZone;
			}

			idCol++;

			if (i < listBranchListLU.size()) {
				valBranchListLU = (Map) listBranchListLU.get(i);
				for (int j = 0; j < valBranchListLU.size(); j++) {
					HSSFCell cell = rowLabelVal.createCell(j + idCol);
					cell.setCellValue(valBranchListLU.get("d" + j).toString());
					cell.setCellStyle(valStyle);
					sheet.autoSizeColumn(i);
				}
				idCol++;
			} 
			else {
				idCol += colSizeBranchListLU;
			}
		}
	}

	private int countCharacter(String content, char character, AuditContext callerId) {
		String s = content;
		int counter = 0;
		for (int i = 0; i < s.length(); i++) {
			if (s.charAt(i) == character) {
				counter++;
			}
		}
		return counter;
	}

	// tambahan 3+1
	public XSSFWorkbook createEditXlsTemplate(String uuidForm,
			AuditContext callerId) {
		XSSFWorkbook workbookEdit = new XSSFWorkbook();
		DataValidation dataValidation = null;
		DataValidationConstraint constraint = null;
		DataValidationHelper validationHelper = null;

		try {
			XSSFSheet sheetList = workbookEdit.createSheet("Question List");
			XSSFSheet sheetGroup = workbookEdit.createSheet("Question Group");
			XSSFSheet sheetForm = workbookEdit.createSheet("Form");
			XSSFSheet sheetRelevansi = workbookEdit.createSheet("Relevansi");
			XSSFSheet sheetDropDownQuestionGroup = workbookEdit
					.createSheet("ListQuestionGroup");
			XSSFSheet sheetDropDownForm = workbookEdit.createSheet("ListForm");
			XSSFSheet sheetMasterType = workbookEdit.createSheet("Master Type");
			XSSFSheet sheetPanduan = workbookEdit.createSheet("Panduan");

			this.createMasterTypeSheet(workbookEdit, sheetMasterType, callerId);
			sheetMasterType.protectSheet("AdIns2016");

			this.createPanduanSheet(workbookEdit, sheetPanduan, callerId, true);
			sheetPanduan.protectSheet("AdIns2016");

			this.editHeader(uuidForm, workbookEdit, sheetList, sheetGroup,
					sheetForm, sheetRelevansi, sheetDropDownQuestionGroup,
				
					sheetDropDownForm, callerId);

			// Data validation for QuestionList Sheet
			Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser",
					Long.valueOf(callerId.getCallerId())) } };
			AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
					paramSubsystem);

			CellRangeAddressList addressListTagging = new CellRangeAddressList(
					1, 1048575, 6, 6);
			if (amMsuser.getAmMssubsystem().getSubsystemName()
					.equals(GlobalVal.SUBSYSTEM_MO)) {

				String formulaSheetQListTaggingOrder = "=OFFSET('Master Type'!$K$5,0,,COUNTA('Master Type'!$K:$K)-1)";
				dropDownValidationExcel(constraint, dataValidation,
						validationHelper, sheetList, addressListTagging,
						formulaSheetQListTaggingOrder, callerId);

			} 
			else if (amMsuser.getAmMssubsystem().getSubsystemName()
					.equals(GlobalVal.SUBSYSTEM_MS)) {

				String formulaSheetQListTaggingSurvey = "=OFFSET('Master Type'!$L$5,0,,COUNTA('Master Type'!$L:$L)-1)";
				dropDownValidationExcel(constraint, dataValidation,
						validationHelper, sheetList, addressListTagging,
						formulaSheetQListTaggingSurvey, callerId);
			} 
			else {

				String formulaSheetQListTaggingCollection = "=OFFSET('Master Type'!$M$5,0,,COUNTA('Master Type'!$M:$M)-1)";
				dropDownValidationExcel(constraint, dataValidation,
						validationHelper, sheetList, addressListTagging,
						formulaSheetQListTaggingCollection, callerId);
			}

			CellRangeAddressList addressListAnswerType = new CellRangeAddressList(
					1, 1048575, 2, 2);
			String formulaSheetQListAnswerType = "=OFFSET('Master Type'!$C$5,0,,COUNTA('Master Type'!$C:$C)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListAnswerType,
					formulaSheetQListAnswerType, callerId);

			CellRangeAddressList addressListImgQlty = new CellRangeAddressList(
					1, 1048575, 3, 3);
			String formulaSheetQListImgQlty = "=OFFSET('Master Type'!$I$5,0,,COUNTA('Master Type'!$I:$I)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListImgQlty,
					formulaSheetQListImgQlty, callerId);

			CellRangeAddressList addressListLovGroup = new CellRangeAddressList(
					1, 1048575, 4, 4);
			String formulaSheetQListLovGroup = "=OFFSET('Master Type'!$E$5,0,,COUNTA('Master Type'!$E:$E)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListLovGroup,
					formulaSheetQListLovGroup, callerId);

			CellRangeAddressList addressListMandatory = new CellRangeAddressList(
					1, 1048575, 7, 7);
			String formulaSheetQListOption = "=OFFSET('Master Type'!$O$5,0,,COUNTA('Master Type'!$O:$O)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListMandatory,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListReadOnly = new CellRangeAddressList(
					1, 1048575, 8, 8);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListReadOnly,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListIsVisible = new CellRangeAddressList(
					1, 1048575, 9, 9);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListIsVisible,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListIsActive = new CellRangeAddressList(
					1, 1048575, 10, 10);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListIsActive,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListIsHolidayAllowed = new CellRangeAddressList(
					1, 1048575, 12, 12);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListIsHolidayAllowed,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListIsChangedQuestion = new CellRangeAddressList(
					1, 1048575, 13, 13);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListIsChangedQuestion,
					formulaSheetQListOption, callerId);

			// Data validation for QuestionGroup Sheet
			CellRangeAddressList addressListQGroup = new CellRangeAddressList(
					1, 1048575, 2, 2);
			String formulaSheetQGroup = "=OFFSET('Question List'!$A$2,0,,COUNTA('Question List'!$A:$A)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetGroup, addressListQGroup,
					formulaSheetQGroup, callerId);

			CellRangeAddressList addressListSheetQGroupIsActive = new CellRangeAddressList(
					1, 1048575, 1, 1);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetGroup,
					addressListSheetQGroupIsActive, formulaSheetQListOption, callerId);

			CellRangeAddressList addressListIsChangedQGroup = new CellRangeAddressList(
					1, 1048575, 4, 4);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetGroup, addressListIsChangedQGroup,
					formulaSheetQListOption, callerId);

			// Data Validation for Form Sheet
			CellRangeAddressList addressListForm = new CellRangeAddressList(1,
					1048575, 3, 3);
			String formulaSheetForm = "=OFFSET(ListQuestionGroup!$A$3,0,0,ListQuestionGroup!$A$2)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetForm, addressListForm,
					formulaSheetForm, callerId);

			CellRangeAddressList addressListFormCategory = new CellRangeAddressList(
					1, 1048575, 1, 1);
			String formulaSheetFormCategory = "=OFFSET('Master Type'!$G$5,0,,COUNTA('Master Type'!$G:$G)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetForm, addressListFormCategory,
					formulaSheetFormCategory, callerId);

			CellRangeAddressList addressListSheetFormIsActive = new CellRangeAddressList(
					1, 1048575, 2, 2);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetForm, addressListSheetFormIsActive,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListIsChangeForm = new CellRangeAddressList(
					1, 1048575, 4, 4);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetForm, addressListIsChangeForm,
					formulaSheetQListOption, callerId);

			// Data Validation for Relevant Sheet

			CellRangeAddressList addressListDropDownForm = new CellRangeAddressList(
					1, 1048575, 0, 0);
			String formulaSheetRelevansi = "=OFFSET(ListForm!$A$3,0,0,ListForm!$A$2)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetRelevansi, addressListDropDownForm,
					formulaSheetRelevansi, callerId);

			CellRangeAddressList addressListDropDownQGroup = new CellRangeAddressList(
					1, 1048575, 1, 1);
			String formulaSheetRelevansi2 = "=OFFSET(ListQuestionGroup!$A$3,0,0,ListQuestionGroup!$A$2)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetRelevansi,
					addressListDropDownQGroup, formulaSheetRelevansi2, callerId);

			CellRangeAddressList addressListDropDownQLabel = new CellRangeAddressList(
					1, 1048575, 2, 2);
			String formulaSheetRelevansi3 = "=OFFSET('Question Group'!$C$2,0,,COUNTA('Question Group'!$C:$C)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetRelevansi,
					addressListDropDownQLabel, formulaSheetRelevansi3, callerId);

			CellRangeAddressList addressListIsChangedQRelevant = new CellRangeAddressList(
					1, 1048575, 10, 10);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetRelevansi,
					addressListIsChangedQRelevant, formulaSheetQListOption, callerId);
		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbookEdit;
	}

	public XSSFWorkbook createXlsTemplateWithManySheets(AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		DataValidation dataValidation = null;
		DataValidationConstraint constraint = null;
		DataValidationHelper validationHelper = null;

		try {
			XSSFSheet sheetList = workbook.createSheet("Question List");
			XSSFSheet sheetGroup = workbook.createSheet("Question Group");
			XSSFSheet sheetForm = workbook.createSheet("Form");
			XSSFSheet sheetRelevansi = workbook.createSheet("Relevansi");
			XSSFSheet sheetDropDownQuestionGroup = workbook
					.createSheet("ListQuestionGroup");
			XSSFSheet sheetDropDownForm = workbook.createSheet("ListForm");
			XSSFSheet sheetMasterType = workbook.createSheet("Master Type");
			XSSFSheet sheetPanduan = workbook.createSheet("Panduan");

			this.createMasterTypeSheet(workbook, sheetMasterType, callerId);
			sheetMasterType.protectSheet("AdIns2016");

			this.createPanduanSheet(workbook, sheetPanduan, callerId, false);
			sheetPanduan.protectSheet("AdIns2016");

			this.createHeader(workbook, sheetList, sheetGroup, sheetForm,
					sheetRelevansi, sheetDropDownQuestionGroup,
					sheetDropDownForm, callerId);

			// Data validation for QuestionList Sheet
			Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser",
					Long.valueOf(callerId.getCallerId())) } };
			AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
					paramSubsystem);

			CellRangeAddressList addressListTagging = new CellRangeAddressList(
					1, 1048575, 6, 6);
			if (amMsuser.getAmMssubsystem().getSubsystemName()
					.equals(GlobalVal.SUBSYSTEM_MO)) {

				String formulaSheetQListTaggingOrder = "=OFFSET('Master Type'!$K$5,0,,COUNTA('Master Type'!$K:$K)-1)";
				dropDownValidationExcel(constraint, dataValidation,
						validationHelper, sheetList, addressListTagging,
						formulaSheetQListTaggingOrder, callerId);

			} 
			else if (amMsuser.getAmMssubsystem().getSubsystemName()
					.equals(GlobalVal.SUBSYSTEM_MS)) {

				String formulaSheetQListTaggingSurvey = "=OFFSET('Master Type'!$L$5,0,,COUNTA('Master Type'!$L:$L)-1)";
				dropDownValidationExcel(constraint, dataValidation,
						validationHelper, sheetList, addressListTagging,
						formulaSheetQListTaggingSurvey, callerId);

			} 
			else {

				String formulaSheetQListTaggingCollection = "=OFFSET('Master Type'!$M$5,0,,COUNTA('Master Type'!$M:$M)-1)";
				dropDownValidationExcel(constraint, dataValidation,
						validationHelper, sheetList, addressListTagging,
						formulaSheetQListTaggingCollection, callerId);
			}

			CellRangeAddressList addressListAnswerType = new CellRangeAddressList(
					1, 1048575, 2, 2);
			String formulaSheetQListAnswerType = "=OFFSET('Master Type'!$C$5,0,,COUNTA('Master Type'!$C:$C)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListAnswerType,
					formulaSheetQListAnswerType, callerId);

			CellRangeAddressList addressListImgQlty = new CellRangeAddressList(
					1, 1048575, 3, 3);
			String formulaSheetQListImgQlty = "=OFFSET('Master Type'!$I$5,0,,COUNTA('Master Type'!$I:$I)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListImgQlty,
					formulaSheetQListImgQlty, callerId);

			CellRangeAddressList addressListLovGroup = new CellRangeAddressList(
					1, 1048575, 4, 4);
			String formulaSheetQListLovGroup = "=OFFSET('Master Type'!$E$5,0,,COUNTA('Master Type'!$E:$E)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListLovGroup,
					formulaSheetQListLovGroup, callerId);

			CellRangeAddressList addressListMandatory = new CellRangeAddressList(
					1, 1048575, 7, 7);
			String formulaSheetQListOption = "=OFFSET('Master Type'!$O$5,0,,COUNTA('Master Type'!$O:$O)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListMandatory,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListReadOnly = new CellRangeAddressList(
					1, 1048575, 8, 8);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListReadOnly,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListIsVisible = new CellRangeAddressList(
					1, 1048575, 9, 9);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListIsVisible,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListIsActive = new CellRangeAddressList(
					1, 1048575, 10, 10);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListIsActive,
					formulaSheetQListOption, callerId);

			CellRangeAddressList addressListIsHolidayAllowed = new CellRangeAddressList(
					1, 1048575, 12, 12);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetList, addressListIsHolidayAllowed,
					formulaSheetQListOption, callerId);

			// Data validation for QuestionGroup Sheet
			CellRangeAddressList addressListQGroup = new CellRangeAddressList(
					1, 1048575, 2, 2);
			String formulaSheetQGroup = "=OFFSET('Question List'!$A$2,0,,COUNTA('Question List'!$A:$A)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetGroup, addressListQGroup,
					formulaSheetQGroup, callerId);

			CellRangeAddressList addressListSheetQGroupIsActive = new CellRangeAddressList(
					1, 1048575, 1, 1);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetGroup,
					addressListSheetQGroupIsActive, formulaSheetQListOption, callerId);

			// Data Validation for Form Sheet
			CellRangeAddressList addressListForm = new CellRangeAddressList(1,
					1048575, 3, 3);
			String formulaSheetForm = "=OFFSET(ListQuestionGroup!$A$3,0,0,ListQuestionGroup!$A$2)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetForm, addressListForm,
					formulaSheetForm, callerId);

			CellRangeAddressList addressListFormCategory = new CellRangeAddressList(
					1, 1048575, 1, 1);
			String formulaSheetFormCategory = "=OFFSET('Master Type'!$G$5,0,,COUNTA('Master Type'!$G:$G)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetForm, addressListFormCategory,
					formulaSheetFormCategory, callerId);

			CellRangeAddressList addressListSheetFormIsActive = new CellRangeAddressList(
					1, 1048575, 2, 2);
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetForm, addressListSheetFormIsActive,
					formulaSheetQListOption, callerId);

			// Data Validation for Relevant Sheet

			CellRangeAddressList addressListDropDownForm = new CellRangeAddressList(
					1, 1048575, 0, 0);
			String formulaSheetRelevansi = "=OFFSET(ListForm!$A$3,0,0,ListForm!$A$2)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetRelevansi, addressListDropDownForm,
					formulaSheetRelevansi, callerId);

			CellRangeAddressList addressListDropDownQGroup = new CellRangeAddressList(
					1, 1048575, 1, 1);
			String formulaSheetRelevansi2 = "=OFFSET(ListQuestionGroup!$A$3,0,0,ListQuestionGroup!$A$2)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetRelevansi,
					addressListDropDownQGroup, formulaSheetRelevansi2, callerId);

			CellRangeAddressList addressListDropDownQLabel = new CellRangeAddressList(
					1, 1048575, 2, 2);
			String formulaSheetRelevansi3 = "=OFFSET('Question Group'!$C$2,0,,COUNTA('Question Group'!$C:$C)-1)";
			dropDownValidationExcel(constraint, dataValidation,
					validationHelper, sheetRelevansi,
					addressListDropDownQLabel, formulaSheetRelevansi3, callerId);
		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void dropDownValidationExcel(DataValidationConstraint constraint,
			DataValidation dataValidation,
			DataValidationHelper validationHelper, XSSFSheet sheet,
			CellRangeAddressList cellRange, String formula, AuditContext callerId) {
		
		validationHelper = new XSSFDataValidationHelper(sheet);
		constraint = validationHelper.createFormulaListConstraint(formula);
		dataValidation = validationHelper.createValidation(constraint,
				cellRange);
		dataValidation.setSuppressDropDownArrow(true);
		dataValidation
				.createErrorBox(
						"Error Value",
						this.messageSource.getMessage("businesslogic.global.valuenotmatch", 
								null, this.retrieveLocaleAudit(callerId)));
		dataValidation.setShowErrorBox(true);
		sheet.addValidationData(dataValidation);
	}

	private void createMasterTypeSheet(XSSFWorkbook workbook, XSSFSheet sheet,
			AuditContext callerId) {
		XSSFCellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.CENTER);

		Object[][] paramMsUser = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
				paramMsUser);

		// --Create Customize Header For Master Sheet only--
		sheet.setColumnWidth(10, 20 * 250);
		sheet.setColumnWidth(11, 20 * 250);
		sheet.setColumnWidth(12, 20 * 275);

		// Make Row
		XSSFRow rowMaster = sheet.createRow(2);
		XSSFRow rowHeaderMaster = sheet.createRow(3);
		XSSFRow rowTable = null;

		// Make Cell
		XSSFCell cellMasterAnswerType = rowMaster.createCell(1);
		XSSFCell cellMasterLovGroup = rowMaster.createCell(4);
		XSSFCell cellFormCategory = rowMaster.createCell(6);
		XSSFCell cellCode = rowHeaderMaster.createCell(1);
		XSSFCell cellAnswerType = rowHeaderMaster.createCell(2);
		XSSFCell cellLovGroup = rowHeaderMaster.createCell(4);
		XSSFCell cellCategory = rowHeaderMaster.createCell(6);
		XSSFCell cellImageQlty = rowHeaderMaster.createCell(8);
		XSSFCell cellAssetMO = rowHeaderMaster.createCell(10);
		XSSFCell cellAssetMS = rowHeaderMaster.createCell(11);
		XSSFCell cellAssetMC = rowHeaderMaster.createCell(12);
		XSSFCell cellOption = rowHeaderMaster.createCell(14);

		// put value
		cellMasterAnswerType.setCellValue("Master Answer Type");
		cellMasterLovGroup.setCellValue("Master LOV Group");
		cellFormCategory.setCellValue("Form Category");

		cellCode.setCellValue("Code");
		cellCode.setCellStyle(style);

		cellAnswerType.setCellValue("Answer Type");
		cellAnswerType.setCellStyle(style);

		cellLovGroup.setCellValue("LOV Group");
		cellLovGroup.setCellStyle(style);

		cellCategory.setCellValue("Category Description");
		cellCategory.setCellStyle(style);

		cellImageQlty.setCellValue("Image Quality");
		cellImageQlty.setCellStyle(style);

		cellAssetMO.setCellValue("Tagging MO-Order");
		cellAssetMO.setCellStyle(style);

		cellAssetMS.setCellValue("Tagging MS-Survey");
		cellAssetMS.setCellStyle(style);

		cellAssetMC.setCellValue("Tagging MC-Collection");
		cellAssetMC.setCellStyle(style);

		cellOption.setCellValue("Option");
		cellOption.setCellStyle(style);

		// Merging cell
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 1, 2));

		// row++
		int rowIterator = 0;

		// hbm list get table
		String[][] params = { { "isActive", "1" } };
		String[][] paramSubsystem = {
				{ "uuidMsSubsystem",
					String.valueOf(amMsuser.getAmMssubsystem().getUuidMsSubsystem()) },
				{ "isActive", "1" } };

		List tableAnswerType = this.getManagerDAO().selectAllNative(
				"eform.form.getAnswerType", params, null);
		List tableLovGroup = this.getManagerDAO().selectAllNative(
				"eform.form.getLovGroup", params, null);
		List tableFormCategory = this.getManagerDAO().selectAllNative(
				"eform.form.getFormCategory", paramSubsystem, null);
		List tableTaggingMS = this.getManagerDAO().selectAllNative(
				"eform.form.getTaggingMS", params, null);
		List tableTaggingMO = this.getManagerDAO().selectAllNative(
				"eform.form.getTaggingMO", params, null);
		List tableTaggingMC = this.getManagerDAO().selectAllNative(
				"eform.form.getTaggingMC", params, null);
		
		// Table Master Lov
		List lovGroup = new ArrayList();
		if (!tableLovGroup.isEmpty()) {
			for (int i = 0; i < tableLovGroup.size(); i++) {
				rowTable = sheet.createRow(4 + rowIterator);
				rowIterator++;
				if (rowTable == null) {
					rowTable = sheet.createRow(i);
				}

				Map map = (Map) tableLovGroup.get(i);
				lovGroup.add(map.get("d0"));

				XSSFCell cellTableLov = rowTable.createCell(4);
				cellTableLov.setCellValue(lovGroup.get(i).toString());
				sheet.setColumnWidth(4, 20 * 300);

			}
			rowIterator = 0;
		}

		// Table Master Answer Type

		List codeAnswerType = new ArrayList();
		List answerType = new ArrayList();
		if (!tableAnswerType.isEmpty()) {
			for (int i = 0; i < tableAnswerType.size(); i++) {
				rowTable = sheet.getRow(4 + rowIterator);
				if (rowTable == null) {
					rowTable = sheet.createRow(4 + rowIterator);
				}
				rowIterator++;

				Map map = (Map) tableAnswerType.get(i);
				answerType.add(map.get("d0"));
				codeAnswerType.add(map.get("d1"));
				XSSFCell cellTableAnswerType = rowTable.createCell(2);
				sheet.setColumnWidth(2, 20 * 275);
				XSSFCell cellTableCodeAnswerType = rowTable.createCell(1);
				cellTableAnswerType.setCellValue(answerType.get(i).toString());
				cellTableCodeAnswerType.setCellValue(codeAnswerType.get(i)
						.toString());
			}
			rowIterator = 0;
		}

		// Table Form Category
		List formCategory = new ArrayList();
		if (!tableFormCategory.isEmpty()) {
			for (int i = 0; i < tableFormCategory.size(); i++) {
				rowTable = sheet.getRow(4 + rowIterator);
				if (rowTable == null) {
					rowTable = sheet.createRow(4 + rowIterator);
				}
				rowIterator++;

				Map map = (Map) tableFormCategory.get(i);
				formCategory.add(map.get("d0"));
				XSSFCell cellTableCategory = rowTable.createCell(6);
				sheet.setColumnWidth(6, 20 * 256);

				cellTableCategory.setCellValue(formCategory.get(i).toString());
			}
			rowIterator = 0;
		}

		// Table for Image Quality
		String[] imageQlty = { "HQ", "NQ" };
		for (int i = 0; i < imageQlty.length; i++) {
			rowTable = sheet.getRow(4 + rowIterator);
			rowIterator++;
			XSSFCell cellTableImgeQlty = rowTable.createCell(8);
			sheet.setColumnWidth(8, 20 * 150);
			cellTableImgeQlty.setCellValue(imageQlty[i]);

		}

		if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MS)) {
			// Table For TaggingMS
			List taggingMS = new ArrayList();

			if (!tableTaggingMS.isEmpty()) {
				rowIterator = 0;
				for (int i = 0; i < tableTaggingMS.size(); i++) {
					rowTable = sheet.getRow(4 + rowIterator);
					if (rowTable == null) {
						rowTable = sheet.createRow(4 + rowIterator);
					}
					rowIterator++;

					Map map = (Map) tableTaggingMS.get(i);

					if (null == map.get("d0") || "" == map.get("d0")) {
						taggingMS.add(map.get(""));
					} 
					else {
						taggingMS.add(map.get("d0"));
					}

					XSSFCell cellTableTaggingMS = rowTable.createCell(11);
					cellTableTaggingMS
							.setCellValue(taggingMS.get(i).toString());
				}
				rowIterator = 0;
			}
		} 
		else if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MO)) {
			// Table for TaggingMO
			List taggingMO = new ArrayList();
			if (!tableTaggingMO.isEmpty()) {
				rowIterator = 0;
				for (int i = 0; i < tableTaggingMO.size(); i++) {
					rowTable = sheet.getRow(4 + rowIterator);
					if (rowTable == null) {
						rowTable = sheet.createRow(4 + rowIterator);
					}
					rowIterator++;

					Map map = (Map) tableTaggingMO.get(i);
					if (null == map.get("d0") || "" == map.get("d0")) {
						taggingMO.add(map.get(""));
					} 
					else {
						taggingMO.add(map.get("d0"));
					}

					XSSFCell cellTableTaggingMO = rowTable.createCell(10);
					cellTableTaggingMO
							.setCellValue(taggingMO.get(i).toString());
				}
				rowIterator = 0;
			}
		} 
		else {

			// Table for TaggingMC
			List taggingMC = new ArrayList();
			if (!tableTaggingMO.isEmpty()) {
				for (int i = 0; i < tableTaggingMC.size(); i++) {
					rowTable = sheet.getRow(2 + rowIterator);
					if (rowTable == null) {
						rowTable = sheet.createRow(4 + rowIterator);
					}
					rowIterator++;

					Map map = (Map) tableTaggingMC.get(i);
					if (null == map.get("d0") || "" == map.get("d0")) {
						taggingMC.add(map.get(""));
					} 
					else {
						taggingMC.add(map.get("d0"));
					}

					XSSFCell cellTableTaggingMC = rowTable.createCell(12);
					cellTableTaggingMC
							.setCellValue(taggingMC.get(i).toString());

				}
				rowIterator = 0;
			}
		}

		// Table for Option
		String[] optional = { "1", "0" };
		for (int i = 0; i < optional.length; i++) {
			rowTable = sheet.getRow(4 + rowIterator);
			rowIterator++;
			XSSFCell cellOptional = rowTable.createCell(14);
			sheet.setColumnWidth(14, 20 * 150);
			cellOptional.setCellValue(optional[i]);
		}
	}

	private void createPanduanSheet(XSSFWorkbook workbook, XSSFSheet sheet,
			AuditContext callerId, boolean edit) {
		XSSFCellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.LEFT);

		XSSFCellStyle styleHeader = workbook.createCellStyle();
		styleHeader.setFont(font);
		styleHeader.setFillForegroundColor(IndexedColors.GREY_25_PERCENT
				.getIndex());
		styleHeader.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		styleHeader.setAlignment(HorizontalAlignment.CENTER);

		// --Create Customize Header For Master Sheet only--
		sheet.setColumnWidth(4, 40 * 450);
		sheet.setColumnWidth(6, 40 * 320);

		// Make Row
		XSSFRow rowPanduan1 = sheet.createRow(2);
		XSSFRow rowPanduan2 = sheet.createRow(4);
		XSSFRow rowPanduan3 = sheet.createRow(5);
		XSSFRow rowPanduan4 = sheet.createRow(6);
		XSSFRow rowPanduan5 = sheet.createRow(7);
		XSSFRow rowPanduan6 = sheet.createRow(8);
		XSSFRow rowPanduan7 = sheet.createRow(9);
		XSSFRow rowPanduan8 = sheet.createRow(10);
		XSSFRow rowPanduan9 = sheet.createRow(11);
		XSSFRow rowPanduan10 = sheet.createRow(12);
		XSSFRow rowPanduan11 = sheet.createRow(13);
		XSSFRow rowPanduan12 = sheet.createRow(14);
		XSSFRow rowPanduan13 = sheet.createRow(15);
		XSSFRow rowPanduan14 = sheet.createRow(16);
		XSSFRow rowPanduan15 = sheet.createRow(17);
		XSSFRow rowPanduan16 = sheet.createRow(18);
		XSSFRow rowPanduan17 = sheet.createRow(19);
		XSSFRow rowPanduan18 = sheet.createRow(20);
		XSSFRow rowPanduan19 = sheet.createRow(21);
		XSSFRow rowPanduan20 = sheet.createRow(22);
		XSSFRow rowPanduan21 = sheet.createRow(23);
		XSSFRow rowPanduan22 = sheet.createRow(24);
		XSSFRow rowPanduan23 = sheet.createRow(25);
		XSSFRow rowPanduan24 = sheet.createRow(26);
		XSSFRow rowPanduan25 = sheet.createRow(27);
		XSSFRow rowPanduan26 = sheet.createRow(28);
		XSSFRow rowPanduan27 = sheet.createRow(30);

		// Make Cell
		XSSFCell cellPanduan = rowPanduan1.createCell(4);

		XSSFCell cellQList1 = rowPanduan2.createCell(4);
		XSSFCell cellQList2 = rowPanduan3.createCell(4);
		XSSFCell cellQList3 = rowPanduan4.createCell(4);
		XSSFCell cellQList4 = rowPanduan5.createCell(4);
		XSSFCell cellQList5 = rowPanduan6.createCell(4);
		XSSFCell cellQList6 = rowPanduan7.createCell(4);
		XSSFCell cellQList7 = rowPanduan8.createCell(4);
		XSSFCell cellQList8 = rowPanduan9.createCell(4);
		XSSFCell cellQList9 = rowPanduan10.createCell(4);
		XSSFCell cellQList10 = rowPanduan11.createCell(4);
		XSSFCell cellQList11 = rowPanduan12.createCell(4);
		XSSFCell cellQList12 = rowPanduan13.createCell(4);
		XSSFCell cellQList13 = rowPanduan14.createCell(4);
		XSSFCell cellQList14 = rowPanduan15.createCell(4);
		XSSFCell cellQList15 = rowPanduan16.createCell(4);
		XSSFCell cellQList16 = rowPanduan17.createCell(4);
		XSSFCell cellQList17 = rowPanduan18.createCell(4);
		XSSFCell cellQList18 = rowPanduan19.createCell(4);
		XSSFCell cellQList19 = rowPanduan20.createCell(4);
		XSSFCell cellQList20 = rowPanduan21.createCell(4);
		XSSFCell cellQList21 = rowPanduan22.createCell(4);
		XSSFCell cellQList22 = rowPanduan23.createCell(4);
		XSSFCell cellQList23 = rowPanduan24.createCell(4);
		XSSFCell cellQList24 = rowPanduan25.createCell(4);
		XSSFCell cellQList25 = rowPanduan26.createCell(4);
		XSSFCell cellQList26 = rowPanduan27.createCell(4);

		XSSFCell cellQGroup1 = rowPanduan2.createCell(6);
		XSSFCell cellQGroup2 = rowPanduan3.createCell(6);

		XSSFCell cellForm1 = rowPanduan5.createCell(6);
		XSSFCell cellForm2 = rowPanduan6.createCell(6);

		XSSFCell cellRelevansi1 = rowPanduan8.createCell(6);
		XSSFCell cellRelevansi2 = rowPanduan9.createCell(6);
		XSSFCell cellRelevansi3 = rowPanduan10.createCell(6);
		XSSFCell cellRelevansi4 = rowPanduan11.createCell(6);

		XSSFCell cellListQGroup1 = rowPanduan13.createCell(6);
		XSSFCell cellListQGroup2 = rowPanduan14.createCell(6);
		XSSFCell cellListQGroup3 = rowPanduan15.createCell(6);

		XSSFCell cellListForm1 = rowPanduan17.createCell(6);
		XSSFCell cellListForm2 = rowPanduan18.createCell(6);
		XSSFCell cellListForm3 = rowPanduan19.createCell(6);

		XSSFCell cellIsChanged1 = rowPanduan21.createCell(6);
		XSSFCell cellIsChanged2 = rowPanduan22.createCell(6);

		// put value
		// question list
		cellPanduan.setCellValue("Ketentuan untuk mengisi masing-masing sheet");
		cellPanduan.setCellStyle(styleHeader);

		cellQList1.setCellValue("Sheet Question List :");
		cellQList1.setCellStyle(style);

		cellQList2.setCellValue("1. Image Quality diisi jika answer type");
		cellQList3.setCellValue("     - Image");
		cellQList4.setCellValue("     - Image with Geodata");
		cellQList5.setCellValue("2. LOV GROUP diisi jika answer type");
		cellQList6.setCellValue("     - Multiple");
		cellQList7.setCellValue("     - Multiple one description");
		cellQList8.setCellValue("     - Multiple with description");
		cellQList9.setCellValue("     - Dropdown");
		cellQList10.setCellValue("     - Dropdown with description");
		cellQList11.setCellValue("     - Radio");
		cellQList12.setCellValue("     - Radio with description ");
		cellQList13.setCellValue("     - Text with Suggestion");
		cellQList14.setCellValue("     - LuOnline");
		cellQList15.setCellValue("3. Max Length diisi jika answer type");
		cellQList16.setCellValue("     - Currency");
		cellQList17.setCellValue("     - Numeric");
		cellQList18.setCellValue("     - Decimal");
		cellQList19.setCellValue("     - Text");
		cellQList20.setCellValue("     - Text Multiline");
		cellQList21.setCellValue("     - Text with Suggestion");
		cellQList22.setCellValue("     - Date");
		cellQList23.setCellValue("     - Datetime");
		cellQList24.setCellValue("4. Asset Tagging tidak harus diisi");
		cellQList25.setCellValue("5. Identifier maksimal 20");
		cellQList26.setCellValue("Semua kolom harus diisi kecuali ketentuan diatas");

		// question group
		cellQGroup1.setCellValue("Sheet Question Group");
		cellQGroup1.setCellStyle(style);

		cellQGroup2.setCellValue("Semua kolom harus diisi");

		// form
		cellForm1.setCellValue("Sheet Form");
		cellForm1.setCellStyle(style);

		cellForm2.setCellValue("Semua kolom harus diisi");

		// relevansi
		cellRelevansi1.setCellValue("Sheet Relevansi yang harus diisi :");
		cellRelevansi1.setCellStyle(style);

		cellRelevansi2.setCellValue("1. Form Name");
		cellRelevansi3.setCellValue("2. Question Group");
		cellRelevansi4.setCellValue("3. Identifier");

		// sheet ListQuestionGroup
		cellListQGroup1.setCellValue("Sheet ListQuestionGroup");
		cellListQGroup1.setCellStyle(style);

		cellListQGroup2
				.setCellValue("untuk menampilkan isi kolom unique Question Group:");
		cellListQGroup3
				.setCellValue("tekan tombol(ctrl + shift + enter) pada formula cell,drag cell sebanyak angka yang ditampilkan");

		// sheet ListQuestionGroup
		cellListForm1.setCellValue("Sheet ListForm");
		cellListForm1.setCellStyle(style);

		cellListForm2
				.setCellValue("untuk menampilkan isi kolom unique Form Name:");
		cellListForm3
				.setCellValue("tekan tombol(ctrl + shift + enter) pada formula cell, drag cell sebanyak angka yang ditampilkan");

		if (edit == true) {
			// isChanged
			cellIsChanged1.setCellValue("Is Changed");
			cellIsChanged1.setCellStyle(style);

			cellIsChanged2.setCellValue("Pilih 1 jika melakukan edit");
		}

		// Merging cell
		sheet.addMergedRegion(new CellRangeAddress(2, 2, 4, 6));

	}

	private String getRelevantOfForm(String uuidForm, AuditContext callerId,
			XSSFSheet sheet, XSSFRow row) {
		Object[][] params = { { "uuidForm", uuidForm } };
		List tableRelevant = this.getManagerDAO().selectAllNative(
				"eform.form.getRelevantOfForm", params, null);
		row = sheet.getRow(1);
		if (tableRelevant.equals("") || tableRelevant.equals(null)
				|| tableRelevant.isEmpty() || tableRelevant.equals("[]")) {
			emptyRowSheetRelevant = "EMPTY";
		} 
		else {
			emptyRowSheetRelevant = "SUCCESS";
		}
		int rowIterator = 0;
		// For FormName Table
		List formName = new ArrayList();
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.createRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);
				if (null == map.get("d0") || "" == map.get("d0")) {
					formName.add("");
				} 
				else {
					formName.add(map.get("d0"));
				}

				XSSFCell cellTableQuestionList = row.createCell(0);
				cellTableQuestionList.setCellValue(formName.get(i).toString());

			}
			rowIterator = 0;
		}

		// For questionGroup Table
		List questionGroup = new ArrayList();
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);
				if (null == map.get("d1") || "" == map.get("d1")) {
					questionGroup.add("");
				} 
				else {
					questionGroup.add(map.get("d1"));
				}

				XSSFCell cellTableQuestionList = row.createCell(1);
				cellTableQuestionList.setCellValue(questionGroup.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// For refId Table
		List refId = new ArrayList();
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);

				if (null == map.get("d3") || "" == map.get("d3")) {
					refId.add("");
				} 
				else {
					refId.add(map.get("d3"));
				}

				XSSFCell cellTableQuestionList = row.createCell(2);
				cellTableQuestionList.setCellValue(refId.get(i).toString());

			}
			rowIterator = 0;
		}
		// For relevant Table
		List relevant = new ArrayList();
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);

				if (null == map.get("d4") || "" == map.get("d4")) {
					relevant.add("");
				} 
				else {
					relevant.add(map.get("d4"));
				}

				XSSFCell cellTableQuestionList = row.createCell(4);
				cellTableQuestionList.setCellValue(relevant.get(i).toString());

			}
			rowIterator = 0;
		}

		// For choiceFilter Table
		List choiceFilter = new ArrayList();
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);

				if (null == map.get("d5") || "" == map.get("d5")) {
					choiceFilter.add("");
				} 
				else {
					choiceFilter.add(map.get("d5"));
				}

				XSSFCell cellTableQuestionList = row.createCell(5);
				cellTableQuestionList.setCellValue(choiceFilter.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// For calculate Table
		List calculate = new ArrayList();
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) (tableRelevant.get(i));

				if (null == map.get("d6") || "" == map.get("d6")) {
					calculate.add("");
				} 
				else {
					if (map.get("d6").toString().equals("")) {
						calculate.add(map.get("d6").toString());
					} 
					else {
						String calculateReplaced = map.get("d6").toString()
								.replace("_var", "").replace("/*", "")
								.replace("*/", "").replace(" ", "");
						String[] calculateSplit = calculateReplaced
								.split("start");
						calculateSplit = calculateSplit[calculateSplit.length - 1]
								.split("end");
						calculate.add(calculateSplit[0]);
					}
				}

				XSSFCell cellTableQuestionList = row.createCell(6);

				cellTableQuestionList.setCellValue(calculate.get(i).toString());
			}
			rowIterator = 0;
		}

		// For questionValidation Table
		List questionValidation = new ArrayList();
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);

				if (null == map.get("d7") || "" == map.get("d7")) {
					questionValidation.add("");
				} 
				else {
					questionValidation.add(map.get("d7"));
				}

				XSSFCell cellTableQuestionList = row.createCell(7);
				cellTableQuestionList.setCellValue(questionValidation.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// For errorMessage Table
		List errorMessage = new ArrayList();
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);

				if (null == map.get("d9") || "" == map.get("d9")) {
					errorMessage.add("");
				} 
				else {
					errorMessage.add(map.get("d9"));
				}

				XSSFCell cellTableQuestionList = row.createCell(9);
				cellTableQuestionList.setCellValue(errorMessage.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// For questionValue Table
		List questionValue = new ArrayList();
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);

				if (null == map.get("d8") || "" == map.get("d8")) {
					questionValue.add("");
				} 
				else {
					questionValue.add(map.get("d8"));
				}

				XSSFCell cellTableQuestionList = row.createCell(8);
				cellTableQuestionList.setCellValue(questionValue.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// For isChanged
		String isChanged = "0";
		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				XSSFCell cellTableQuestionList = row.createCell(11);
				cellTableQuestionList.setCellValue(isChanged);

			}
			rowIterator = 0;
		}

		// For uuidQuestionRelevant Table
		List uuidQuestionRelevant = new ArrayList();

		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);

				if (null == map.get("d10") || "" == map.get("d10")) {
					uuidQuestionRelevant.add("");
				}
				else {
					uuidQuestionRelevant.add(map.get("d10"));
				}

				XSSFCell cellTableQuestionList = row.createCell(12);
				cellTableQuestionList.setCellValue(uuidQuestionRelevant.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// For relevantMandatory
		List relevantMandatoryList = new ArrayList();

		if (!tableRelevant.isEmpty()) {
			for (int i = 0; i < tableRelevant.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableRelevant.get(i);
				if (null == map.get("d13") || "" == map.get("d13")) {
					relevantMandatoryList.add("");
				}
				else {
					relevantMandatoryList.add(map.get("d13"));
				}

				XSSFCell cellTableRlvMandatory = row.createCell(10);
				cellTableRlvMandatory.setCellValue(relevantMandatoryList.get(i).toString());

			}
			rowIterator = 0;
		}
		return emptyRowSheetRelevant;
	}

	private void getFormOfForm(String uuidForm, AuditContext callerId,
			XSSFSheet sheet, XSSFRow row) {
		Object[][] params = { { "uuidForm", uuidForm } };
		List tableForm = this.getManagerDAO().selectAllNative(
				"eform.form.getFormByUuid", params, null);
		int rowIterator = 0;

		// For FormName Table
		List formName = new ArrayList();
		if (!tableForm.isEmpty()) {
			for (int i = 0; i < tableForm.size(); i++) {
				row = sheet.createRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableForm.get(i);
				if (null == map.get("d0") || "" == map.get("do")) {
					formName.add("");
				}
				else {
					formName.add(map.get("d0"));
				}

				XSSFCell cellTableQuestionList = row.createCell(0);
				cellTableQuestionList.setCellValue(formName.get(i).toString());

			}
			rowIterator = 0;
		}

		// For formCategory Table
		List formCategory = new ArrayList();
		if (!tableForm.isEmpty()) {
			for (int i = 0; i < tableForm.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableForm.get(i);

				if (null == map.get("d1") || "" == map.get("d1")) {
					formCategory.add("");
				}
				else {
					formCategory.add(map.get("d1"));
				}

				XSSFCell cellTableQuestionList = row.createCell(1);
				cellTableQuestionList.setCellValue(formCategory.get(i)
						.toString());

			}
			rowIterator = 0;
		}
		// For isActive Table
		List isActive = new ArrayList();
		if (!tableForm.isEmpty()) {
			for (int i = 0; i < tableForm.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableForm.get(i);

				if (null == map.get("d2") || "" == map.get("d2")) {
					isActive.add("");
				}
				else {
					isActive.add(map.get("d2"));
				}

				XSSFCell cellTableQuestionList = row.createCell(2);
				cellTableQuestionList.setCellValue(isActive.get(i).toString());

			}
			rowIterator = 0;
		}

		// For questionGroup Table
		List questionGroup = new ArrayList();
		if (!tableForm.isEmpty()) {
			for (int i = 0; i < tableForm.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableForm.get(i);

				if (null == map.get("d3") || "" == map.get("d3")) {
					questionGroup.add("");
				}
				else {
					questionGroup.add(map.get("d3"));

				}

				XSSFCell cellTableQuestionList = row.createCell(3);
				cellTableQuestionList.setCellValue(questionGroup.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// For isChanged
		String isChanged = "0";
		if (!tableForm.isEmpty()) {
			for (int i = 0; i < tableForm.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				XSSFCell cellTableQuestionList = row.createCell(4);
				cellTableQuestionList.setCellValue(isChanged);

			}
			rowIterator = 0;
		}

		// For uuidQuestionGroupOfForm
		List uuidQuestionGroupOfForm = new ArrayList();
		if (!tableForm.isEmpty()) {
			for (int i = 0; i < tableForm.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableForm.get(i);
				if (null == map.get("d4")) {
					uuidQuestionGroupOfForm.add("");
				}
				else {
					uuidQuestionGroupOfForm.add(map.get("d4"));
				}

				XSSFCell cellTableQuestionList = row.createCell(6);
				cellTableQuestionList.setCellValue(uuidQuestionGroupOfForm.get(
						i).toString());

			}
			rowIterator = 0;
		}

		// For LineSeqOrder
		List lineSeqOrder = new ArrayList();
		if (!tableForm.isEmpty()) {
			for (int i = 0; i < tableForm.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableForm.get(i);
				if (null == map.get("d5")) {
					lineSeqOrder.add("");
				}
				else {
					lineSeqOrder.add(map.get("d5"));
				}

				XSSFCell cellTableQuestionList = row.createCell(5);
				cellTableQuestionList.setCellValue(lineSeqOrder.get(i)
						.toString());

			}
			rowIterator = 0;
		}

	}

	private void getQuestionGroupOfForm(String uuidForm, AuditContext callerId,
			XSSFSheet sheet, XSSFRow row) {
		Object[][] params = { { "uuidForm", uuidForm } };
		List tableQuestionGroup = this.getManagerDAO().selectAllNative(
				"eform.form.getQuestionGroup", params, null);
		int rowIterator = 0;
		// For QuestionGroupLabel Table
		List questionGroupLabel = new ArrayList();
		if (!tableQuestionGroup.isEmpty()) {
			for (int i = 0; i < tableQuestionGroup.size(); i++) {
				row = sheet.createRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionGroup.get(i);

				if (null == map.get("d0") || "" == map.get("d0")) {
					questionGroupLabel.add("");
				} 
				else {
					questionGroupLabel.add(map.get("d0"));
				}

				XSSFCell cellTableQuestionList = row.createCell(0);
				cellTableQuestionList.setCellValue(questionGroupLabel.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// For isActive Table
		List isActive = new ArrayList();
		if (!tableQuestionGroup.isEmpty()) {
			for (int i = 0; i < tableQuestionGroup.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionGroup.get(i);

				if (null == map.get("d1") || "" == map.get("d1")) {
					questionGroupLabel.add("");
				} 
				else {
					isActive.add(map.get("d1"));
				}

				XSSFCell cellTableQuestionList = row.createCell(1);
				cellTableQuestionList.setCellValue(isActive.get(i).toString());

			}
			rowIterator = 0;
		}

		// For RefId Table
		List refId = new ArrayList();
		if (!tableQuestionGroup.isEmpty()) {
			for (int i = 0; i < tableQuestionGroup.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionGroup.get(i);

				if (null == map.get("d3") || "" == map.get("d3")) {
					refId.add("");
				} 
				else {
					refId.add(map.get("d3"));
				}

				XSSFCell cellTableQuestionList = row.createCell(2);
				cellTableQuestionList.setCellValue(refId.get(i).toString());

			}
			rowIterator = 0;
		}

		// For isChanged
		String isChanged = "0";
		if (!tableQuestionGroup.isEmpty()) {
			for (int i = 0; i < tableQuestionGroup.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				XSSFCell cellTableQuestionList = row.createCell(4);
				cellTableQuestionList.setCellValue(isChanged);
				sheet.setColumnWidth(4, 20 * 300);
			}
			rowIterator = 0;
		}

		// For uuidQuestionOfGroup
		List uuidQuestionOfGroup = new ArrayList();
		if (!tableQuestionGroup.isEmpty()) {
			for (int i = 0; i < tableQuestionGroup.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionGroup.get(i);
				if (null == map.get("d4") || "" == map.get("d4")) {
					uuidQuestionOfGroup.add("");
				} 
				else {
					uuidQuestionOfGroup.add(map.get("d4"));
				}

				XSSFCell cellTableQuestionList = row.createCell(6);
				cellTableQuestionList.setCellValue(uuidQuestionOfGroup.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// For sequence order
		List seqOrder = new ArrayList();
		if (!tableQuestionGroup.isEmpty()) {
			for (int i = 0; i < tableQuestionGroup.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionGroup.get(i);
				if (null == map.get("d5") || "" == map.get("d5")) {
					seqOrder.add("");
				} 
				else {
					seqOrder.add(map.get("d5"));
				}

				XSSFCell cellTableQuestionList = row.createCell(5);
				cellTableQuestionList.setCellValue(seqOrder.get(i).toString());

			}
			rowIterator = 0;
		}

	}

	private void getQuestionListOfForm(String uuidForm, AuditContext callerId,
			XSSFSheet sheet, XSSFRow row) {
		Object[][] params = { {"uuidForm", uuidForm} };
		List tableQuestionList = this.getManagerDAO().selectAllNative(
				"eform.form.getQuestionList11", params, null);
		int rowIterator = 0;

		// For QuestionLabel Table
		List questionLabel = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.createRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);

				if (null == map.get("d0") || "" == map.get("d0")) {
					questionLabel.add("");
				}
				else {
					questionLabel.add(map.get("d0"));
				}

				XSSFCell cellTableQuestionList = row.createCell(1);
				cellTableQuestionList.setCellValue(questionLabel.get(i)
						.toString());
			}
			rowIterator = 0;
		}

		// For RefId Table
		List refId = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);

				if (null == map.get("d1") || "" == map.get("d1")) {
					refId.add("");
				}
				else {
					refId.add(map.get("d1"));
				}

				XSSFCell cellTableQuestionList = row.createCell(0);
				cellTableQuestionList.setCellValue(refId.get(i).toString());

			}
			rowIterator = 0;
		}

		// For AnswerType table
		List answerType = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);

				if (null == map.get("d2") || "" == map.get("d2")) {
					answerType.add(map.get(""));
				}
				else {
					answerType.add(map.get("d2"));
				}

				XSSFCell cellTableQuestionList = row.createCell(2);
				cellTableQuestionList
						.setCellValue(answerType.get(i).toString());

			}
			rowIterator = 0;
		}

		// For LovGroup table
		List lovGroup = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);
				if (null == map.get("d3") || "" == map.get("d3")) {
					lovGroup.add(map.get(""));
				}
				else {
					lovGroup.add(map.get("d3"));
				}

				XSSFCell cellTableQuestionList = row.createCell(4);
				cellTableQuestionList.setCellValue(lovGroup.get(i).toString());

			}
			rowIterator = 0;
		}

		// For maxLength table
		List maxLength = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);

				if (null == map.get("d4") || "" == map.get("d4")) {
					maxLength.add("");
				} 
				else {
					maxLength.add(map.get("d4"));
				}

				XSSFCell cellTableQuestionList = row.createCell(5);
				cellTableQuestionList.setCellValue(maxLength.get(i).toString());

			}
			rowIterator = 0;
		}

		// For tagging table
		Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
				paramSubsystem);

		if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MO)) {
			// For Order Tagging
			List orderTag = new ArrayList();
			if (!tableQuestionList.isEmpty()) {
				for (int i = 0; i < tableQuestionList.size(); i++) {
					row = sheet.getRow(1 + rowIterator);
					rowIterator++;
					if (row == null) {
						row = sheet.createRow(i);
					}

					Map map = (Map) tableQuestionList.get(i);
					if (null == map.get("d5") || "" == map.get("d5")) {
						orderTag.add("");
					} 
					else {
						orderTag.add(map.get("d5"));
					}

					XSSFCell cellTableQuestionList = row.createCell(6);
					cellTableQuestionList.setCellValue(orderTag.get(i)
							.toString());

				}
				rowIterator = 0;
			}

		} 
		else if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MS)) {
			// For Asset Tagging
			List assetTag = new ArrayList();
			if (!tableQuestionList.isEmpty()) {
				for (int i = 0; i < tableQuestionList.size(); i++) {
					row = sheet.getRow(1 + rowIterator);
					rowIterator++;
					if (row == null) {
						row = sheet.createRow(i);
					}

					Map map = (Map) tableQuestionList.get(i);

					if (null == map.get("d6") || "" == map.get("d6")) {
						assetTag.add("");
					} 
					else {
						assetTag.add(map.get("d6"));
					}

					XSSFCell cellTableQuestionList = row.createCell(6);
					cellTableQuestionList.setCellValue(assetTag.get(i)
							.toString());

				}
				rowIterator = 0;
			}
		} 
		else {
			// For Collection Tagging
			List collectionTag = new ArrayList();
			if (!tableQuestionList.isEmpty()) {
				for (int i = 0; i < tableQuestionList.size(); i++) {
					row = sheet.getRow(1 + rowIterator);
					rowIterator++;
					if (row == null) {
						row = sheet.createRow(i);
					}

					Map map = (Map) tableQuestionList.get(i);

					if (null == map.get("d7") || "" == map.get("d7")) {
						collectionTag.add("");
					} 
					else {
						collectionTag.add(map.get("d7"));
					}

					XSSFCell cellTableQuestionList = row.createCell(6);
					cellTableQuestionList.setCellValue(collectionTag.get(i)
							.toString());

				}
				rowIterator = 0;
			}
		}

		// For MandatoryTable
		List mandatory = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);

				if (null == map.get("d8") || "" == map.get("d8")) {
					mandatory.add("");
				} 
				else {
					mandatory.add(map.get("d8"));
				}

				XSSFCell cellTableQuestionList = row.createCell(7);
				cellTableQuestionList.setCellValue(mandatory.get(i).toString());

			}
			rowIterator = 0;
		}

		// For isReadOnly
		List isReadOnly = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);
				if (null == map.get("d9") || "" == map.get("d9")) {
					isReadOnly.add("");
				} 
				else {
					isReadOnly.add(map.get("d9"));
				}

				XSSFCell cellTableQuestionList = row.createCell(8);
				cellTableQuestionList
						.setCellValue(isReadOnly.get(i).toString());

			}
			rowIterator = 0;
		}

		// For isVisible
		List isVisible = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);
				if (null == map.get("d10") || "" == map.get("d10")) {
					isVisible.add(map.get(""));
				} 
				else {
					isVisible.add(map.get("d10"));
				}

				XSSFCell cellTableQuestionList = row.createCell(9);
				cellTableQuestionList.setCellValue(isVisible.get(i).toString());

			}
			rowIterator = 0;
		}

		// For isActive
		List isActive = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);
				if (null == map.get("d11") || "" == map.get("d11")) {
					isActive.add(map.get(""));
				} 
				else {
					isActive.add(map.get("d11"));
				}

				XSSFCell cellTableQuestionList = row.createCell(10);
				cellTableQuestionList.setCellValue(isActive.get(i).toString());

			}
			rowIterator = 0;
		}

		// For isChanged
		String isChanged = "0";
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				XSSFCell cellTableQuestionList = row.createCell(13);
				cellTableQuestionList.setCellValue(isChanged);

			}
			rowIterator = 0;
		}
		// For uuidQuestion table
		List uuid_question = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);

				if (null == map.get("d12") || "" == map.get("d12")) {
					uuid_question.add("");
				} 
				else {
					uuid_question.add(map.get("d12"));
				}

				XSSFCell cellTableQuestionList = row.createCell(14);
				cellTableQuestionList.setCellValue(uuid_question.get(i)
						.toString());

			}
			rowIterator = 0;
		}
		// Table Regex pattern
		List regex_pattern = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);

				if (null == map.get("d13") || "" == map.get("d13")) {
					regex_pattern.add("");
				} 
				else {
					regex_pattern.add(map.get("d13"));
				}

				XSSFCell cellTableQuestionList = row.createCell(11);
				cellTableQuestionList.setCellValue(regex_pattern.get(i)
						.toString());

			}
			rowIterator = 0;
		}
		// table for isHolidayAllowed
		List is_holiday_allowed = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);

				if (null == map.get("d14") || "" == map.get("d14")) {
					is_holiday_allowed.add("");
				} 
				else {
					is_holiday_allowed.add(map.get("d14"));
				}

				XSSFCell cellTableQuestionList = row.createCell(12);
				cellTableQuestionList.setCellValue(is_holiday_allowed.get(i)
						.toString());

			}
			rowIterator = 0;
		}

		// table for isHolidayAllowed
		List imgQlt = new ArrayList();
		if (!tableQuestionList.isEmpty()) {
			for (int i = 0; i < tableQuestionList.size(); i++) {
				row = sheet.getRow(1 + rowIterator);
				rowIterator++;
				if (row == null) {
					row = sheet.createRow(i);
				}

				Map map = (Map) tableQuestionList.get(i);

				if (null == map.get("d15") || "" == map.get("d15")) {
					imgQlt.add("");
				}
				else {
					imgQlt.add(map.get("d15"));
				}

				XSSFCell cellTableQuestionList = row.createCell(3);
				cellTableQuestionList.setCellValue(imgQlt.get(i).toString());

			}
			rowIterator = 0;
		}
	}

	private void editHeader(String uuidForm, XSSFWorkbook workbook,
			XSSFSheet sheetQuestionList, XSSFSheet sheetQgroup, XSSFSheet sheetForm,
			XSSFSheet sheetRelevansi, XSSFSheet sheetMsQg, XSSFSheet sheetMsForm,
			AuditContext callerId) {
		
		XSSFCellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.CENTER);

		XSSFRow row1 = sheetQuestionList.createRow(0);
		Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
				paramSubsystem);

		if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MO)) {
			for (int i = 0; i < TEMPLATE_HEADER_MO_QESTION_LIST_SHEET_EDIT.length; i++) {
				XSSFCell cell = row1.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MO_QESTION_LIST_SHEET_EDIT[i]);
				cell.setCellStyle(style);
				if (TEMPLATE_HEADER_MO_QESTION_LIST_SHEET_EDIT[i] == GlobalVal.HEADER_UUID_QUESTION) {
					sheetQuestionList.setColumnWidth(i, 20 * 500);
				} 
				else {
					sheetQuestionList.setColumnWidth(i, 20 * 256);
				}
			}
		} 
		else if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MS)) {
			for (int i = 0; i < TEMPLATE_HEADER_MS_QESTION_LIST_SHEET_EDIT.length; i++) {
				XSSFCell cell = row1.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MS_QESTION_LIST_SHEET_EDIT[i]);
				cell.setCellStyle(style);
				if (TEMPLATE_HEADER_MS_QESTION_LIST_SHEET_EDIT[i] == GlobalVal.HEADER_UUID_QUESTION) {
					sheetQuestionList.setColumnWidth(i, 20 * 500);
				} 
				else {
					sheetQuestionList.setColumnWidth(i, 20 * 256);
				}
			}
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER_MC_QESTION_LIST_SHEET_EDIT.length; i++) {
				XSSFCell cell = row1.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MC_QESTION_LIST_SHEET_EDIT[i]);
				cell.setCellStyle(style);
				if (TEMPLATE_HEADER_MC_QESTION_LIST_SHEET_EDIT[i] == GlobalVal.HEADER_UUID_QUESTION) {
					sheetQuestionList.setColumnWidth(i, 20 * 500);
				} 
				else {
					sheetQuestionList.setColumnWidth(i, 20 * 256);
				}
			}
		}
		this.getQuestionListOfForm(uuidForm, callerId, sheetQuestionList, row1);

		XSSFRow row2 = sheetQgroup.createRow(0);
		XSSFCell cellQGroup = null;
		this.getQuestionGroupOfForm(uuidForm, callerId, sheetQgroup, row2);
		for (int i = 0; i < TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET_EDIT.length; i++) {

			if (TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET_EDIT[i] == GlobalVal.HEADER_QUESTION_LABEL) {
				int formulaRow = 2;
				int rowIt = 1;
				for (int x = 0; x < sheetQgroup.getLastRowNum(); x++) {
					XSSFRow newRow = sheetQgroup.getRow(rowIt);
					XSSFCell cellId = newRow.createCell(i);
					cellId.setCellType(XSSFCell.CELL_TYPE_FORMULA);
					cellId.setCellFormula("IFERROR(VLOOKUP(C" + formulaRow
							+ ",'Question List'!$A$2:$B$1048575,2,0),\"\")");
					formulaRow++;
					rowIt++;
				}

			}
			cellQGroup = row2.createCell(i);
			cellQGroup
					.setCellValue(TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET_EDIT[i]);
			cellQGroup.setCellStyle(style);
			if (TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET_EDIT[i] == GlobalVal.HEADER_UUID_QUESTION_OF_GROUP) {
				sheetQgroup.setColumnWidth(i, 20 * 500);
			} 
			else {
				sheetQgroup.setColumnWidth(i, 20 * 256);
			}
		}

		XSSFRow row3 = sheetForm.createRow(0);
		for (int i = 0; i < TEMPLATE_HEADER_MO_FORM_SHEET_EDIT.length; i++) {
			XSSFCell cell = row3.createCell(i);
			sheetForm.setColumnWidth(i, 20 * 256);
			cell.setCellValue(TEMPLATE_HEADER_MO_FORM_SHEET_EDIT[i]);
			cell.setCellStyle(style);

			if (TEMPLATE_HEADER_MO_FORM_SHEET_EDIT[i] == GlobalVal.HEADER_UUID_QUESTION_GROUP_OF_FORM) {
				sheetForm.setColumnWidth(i, 20 * 500);
			} 
			else {
				sheetForm.setColumnWidth(i, 20 * 256);
			}

		}
		this.getFormOfForm(uuidForm, callerId, sheetForm, row3);

		XSSFRow row4 = sheetRelevansi.createRow(0);

		XSSFCell cell = null;
		String resultRelevant = this.getRelevantOfForm(uuidForm, callerId,
				sheetRelevansi, row4);
		if (resultRelevant.equals("Empty")) {
			for (int i = 0; i < TEMPLATE_HEADER_MO_RELEVANSI_SHEET_EDIT.length; i++) {
				cell = row4.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MO_RELEVANSI_SHEET_EDIT[i]);
				cell.setCellStyle(style);
				if (TEMPLATE_HEADER_MO_RELEVANSI_SHEET_EDIT[i] == GlobalVal.HEADER_QUESTION_LABEL) {
					XSSFRow rowForEdit = sheetRelevansi.createRow(1);
					XSSFCell cellEdit = rowForEdit.createCell(i);
					cellEdit.setCellType(XSSFCell.CELL_TYPE_FORMULA);
					cellEdit.setCellFormula("IFERROR(VLOOKUP(C2,'Question List'!$A$2:$B$1048575,2,0),\"\")");
				}
				sheetRelevansi.setColumnWidth(i, 20 * 256);
			}
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER_MO_RELEVANSI_SHEET_EDIT.length; i++) {
				if (TEMPLATE_HEADER_MO_RELEVANSI_SHEET_EDIT[i] == GlobalVal.HEADER_QUESTION_LABEL) {
					if ("EMPTY".equals(emptyRowSheetRelevant)) {
						XSSFRow newRow = sheetRelevansi.createRow(1);
						XSSFCell cellRefId = newRow.createCell(i);
						cellRefId.setCellType(XSSFCell.CELL_TYPE_FORMULA);
						cellRefId
								.setCellFormula("IFERROR(VLOOKUP(C2,'Question List'!$A$2:$B$1048575,2,0),\"\")");
					} 
					else {
						int currRow = 1;
						int formulaRow = 2;
						for (int x = 0; x < sheetRelevansi.getLastRowNum(); x++) {
							XSSFRow newRow = sheetRelevansi.getRow(currRow);
							XSSFCell cellRefId = newRow.createCell(i);
							cellRefId.setCellType(XSSFCell.CELL_TYPE_FORMULA);
							cellRefId
									.setCellFormula("IFERROR(VLOOKUP(C"
											+ formulaRow
											+ ",'Question List'!$A$2:$B$1048575,2,0),\"\")");
							formulaRow++;
							currRow++;
						}
					}
				}
				cell = row4.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MO_RELEVANSI_SHEET_EDIT[i]);
				cell.setCellStyle(style);
				if (TEMPLATE_HEADER_MO_RELEVANSI_SHEET_EDIT[i] == GlobalVal.HEADER_UUID_RELEVANT) {
					sheetRelevansi.setColumnWidth(i, 20 * 500);
				} 
				else {
					sheetRelevansi.setColumnWidth(i, 20 * 256);
				}
			}
		}

		// for sheet dropdown questionGroup
		XSSFRow row5 = sheetMsQg.createRow(0);
		XSSFCell cellHeaderDropGroup = row5.createCell(0);
		cellHeaderDropGroup.setCellValue("Unique Question Group");
		sheetMsQg.setColumnWidth(0, 20 * 280);
		cellHeaderDropGroup.setCellStyle(style);

		// count unique value in questionGroup
		XSSFRow countRow5 = sheetMsQg.createRow(1);
		XSSFCell cellCountQGroup = countRow5.createCell(0);
		cellCountQGroup.setCellType(XSSFCell.CELL_TYPE_FORMULA);
		String formulaIndexSumListDropDownGroup = "SUM(if (FREQUENCY("
				+ "if ('Question Group'!$A$2:$A$1048575<>"
				+ "\"\""
				+ ","
				+ "MATCH("
				+ "\"~\""
				+ "&'Question Group'!$A$2:$A$1048575,'Question Group'!$A$2:$A$1048575&"
				+ "\"\""
				+ ",0)),"
				+ "ROW('Question Group'!$A$2:$A$1048575)-ROW('Question Group'!$A$2)+1),1))";
		XSSFCellStyle style2 = workbook.createCellStyle();
		style2.setAlignment(HorizontalAlignment.CENTER);
		style2.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
		style2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		cellCountQGroup.setCellFormula(formulaIndexSumListDropDownGroup);
		cellCountQGroup.setCellStyle(style2);

		// list unique value in qGroup
		XSSFRow listCount5 = sheetMsQg.createRow(2);
		XSSFCell cellListQGroup = listCount5.createCell(0);
		cellListQGroup.setCellType(XSSFCell.CELL_TYPE_FORMULA);
		String formulaListQGroup = "if (ROWS($A$3:A3)<=$A$2,"
				+ "INDEX('Question Group'!$A$2:$A$1048575,"
				+ "SMALL(if (FREQUENCY(if ('Question Group'!$A$2:$A$1048575<>"
				+ "\"\""
				+ ","
				+ "MATCH("
				+ "\"~\""
				+ "&'Question Group'!$A$2:$A$1048575,'Question Group'!$A$2:$A$1048575&"
				+ "\"\""
				+ ","
				+ "0)),"
				+ "ROW('Question Group'!$A$2:$A$1048575)"
				+ "-ROW('Question Group'!$A$2)+1),ROW('Question Group'!$A$2:$A$1048575)"
				+ "-ROW('Question Group'!$A$2)+1),ROWS($A$3:A3)))," + "\"\""
				+ ")";
		cellListQGroup.setCellFormula(formulaListQGroup);

		// For Sheet dropdown Form
		XSSFRow row6 = sheetMsForm.createRow(0);
		XSSFCell cellHeaderDropForm = row6.createCell(0);
		cellHeaderDropForm.setCellValue("Unique Form Name");
		cellHeaderDropForm.setCellStyle(style);
		sheetMsForm.setColumnWidth(0, 20 * 280);

		// For count dropdown Form
		XSSFRow countRow6 = sheetMsForm.createRow(1);
		XSSFCell cellCountForm = countRow6.createCell(0);
		cellCountForm.setCellType(XSSFCell.CELL_TYPE_FORMULA);
		String formulaIndexSumListDropDownForm = "SUM(if (FREQUENCY("
				+ "if (Form!$A$2:$A$1048575<>" + "\"\"" + "," + "MATCH("
				+ "\"~\"" + "&Form!$A$2:$A$1048575,Form!$A$2:$A$1048575&"
				+ "\"\"" + ",0)),"
				+ "ROW(Form!$A$2:$A$1048575)-ROW(Form!$A$2)+1),1))";
		cellCountForm.setCellFormula(formulaIndexSumListDropDownForm);
		cellCountForm.setCellStyle(style2);

		// For list dropdown Form
		XSSFRow listCount6 = sheetMsForm.createRow(2);
		XSSFCell cellListForm = listCount6.createCell(0);
		cellListForm.setCellType(XSSFCell.CELL_TYPE_FORMULA);
		String formulaListForm = "if (ROWS($A$3:A3)<=$A$2,"
				+ "INDEX(Form!$A$2:$A$1048575,"
				+ "SMALL(if (FREQUENCY(if (Form!$A$2:$A$1048575<>" + "\"\"" + ","
				+ "MATCH(" + "\"~\""
				+ "&Form!$A$2:$A$1048575,Form!$A$2:$A$1048575&" + "\"\"" + ","
				+ "0))," + "ROW(Form!$A$2:$A$1048575)"
				+ "-ROW(Form!$A$2)+1),ROW(Form!$A$2:$A$1048575)"
				+ "-ROW(Form!$A$2)+1),ROWS($A$3:A3)))," + "\"\"" + ")";
		cellListForm.setCellFormula(formulaListForm);

	}

	private void createHeader(XSSFWorkbook workbook, XSSFSheet sheet,
			XSSFSheet sheet2, XSSFSheet sheet3, XSSFSheet sheet4,
			XSSFSheet sheet5, XSSFSheet sheet6, AuditContext callerId) {
		XSSFCellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.CENTER);

		XSSFRow row1 = sheet.createRow(0);

		Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
				paramSubsystem);

		if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MO)) {
			for (int i = 0; i < TEMPLATE_HEADER_MO_QESTION_LIST_SHEET.length; i++) {
				XSSFCell cell = row1.createCell(i);
				sheet.setColumnWidth(i, 20 * 256);
				cell.setCellValue(TEMPLATE_HEADER_MO_QESTION_LIST_SHEET[i]);
				cell.setCellStyle(style);
			}
		} 
		else if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MS)) {
			for (int i = 0; i < TEMPLATE_HEADER_MS_QESTION_LIST_SHEET.length; i++) {
				XSSFCell cell = row1.createCell(i);
				sheet.setColumnWidth(i, 20 * 256);
				cell.setCellValue(TEMPLATE_HEADER_MS_QESTION_LIST_SHEET[i]);
				cell.setCellStyle(style);
			}
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER_MC_QESTION_LIST_SHEET.length; i++) {
				XSSFCell cell = row1.createCell(i);
				sheet.setColumnWidth(i, 20 * 256);
				cell.setCellValue(TEMPLATE_HEADER_MC_QESTION_LIST_SHEET[i]);
				cell.setCellStyle(style);
			}
		}

		XSSFRow row2 = sheet2.createRow(0);
		XSSFCell cellQGroup = null;
		for (int i = 0; i < TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET.length; i++) {

			if (i == 3) {
				XSSFRow newRow = sheet2.createRow(1);
				XSSFCell cellId = newRow.createCell(i);
				cellId.setCellType(XSSFCell.CELL_TYPE_FORMULA);
				cellId.setCellFormula("IFERROR(VLOOKUP(C2,'Question List'!$A$2:$B$1048575,2,0),\"\")");
			}
			cellQGroup = row2.createCell(i);
			cellQGroup.setCellValue(TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET[i]);
			cellQGroup.setCellStyle(style);
			sheet2.setColumnWidth(i, 20 * 256);
		}

		XSSFRow row3 = sheet3.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_MO_FORM_SHEET.length; i++) {
			XSSFCell cell = row3.createCell(i);
			sheet3.setColumnWidth(i, 20 * 256);
			cell.setCellValue(TEMPLATE_HEADER_MO_FORM_SHEET[i]);
			cell.setCellStyle(style);
		}

		XSSFRow row4 = sheet4.createRow(0);
		XSSFCell cell = null;
		for (int i = 0; i < TEMPLATE_HEADER_MO_RELEVANSI_SHEET.length; i++) {

			if (i == 3) {
				XSSFRow newRow = sheet4.createRow(1);
				XSSFCell cellRefId = newRow.createCell(i);
				cellRefId.setCellType(XSSFCell.CELL_TYPE_FORMULA);
				cellRefId
						.setCellFormula("IFERROR(VLOOKUP(C2,'Question List'!$A$2:$B$1048575,2,0),\"\")");
			}
			cell = row4.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_MO_RELEVANSI_SHEET[i]);
			cell.setCellStyle(style);
			sheet4.setColumnWidth(i, 20 * 256);
		}

		// for sheet dropdown questionGroup
		XSSFRow row5 = sheet5.createRow(0);
		XSSFCell cellHeaderDropGroup = row5.createCell(0);
		cellHeaderDropGroup.setCellValue("Unique Question Group");
		sheet5.setColumnWidth(0, 20 * 280);
		cellHeaderDropGroup.setCellStyle(style);

		// count unique value in questionGroup
		XSSFRow countRow5 = sheet5.createRow(1);
		XSSFCell cellCountQGroup = countRow5.createCell(0);
		cellCountQGroup.setCellType(XSSFCell.CELL_TYPE_FORMULA);
		String formulaIndexSumListDropDownGroup = "SUM(IF(FREQUENCY("
				+ "IF('Question Group'!$A$2:$A$1048575<>"
				+ "\"\""
				+ ","
				+ "MATCH("
				+ "\"~\""
				+ "&'Question Group'!$A$2:$A$1048575,'Question Group'!$A$2:$A$1048575&"
				+ "\"\""
				+ ",0)),"
				+ "ROW('Question Group'!$A$2:$A$1048575)-ROW('Question Group'!$A$2)+1),1))";
		XSSFCellStyle style2 = workbook.createCellStyle();
		style2.setAlignment(HorizontalAlignment.CENTER);
		style2.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
		style2.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		cellCountQGroup.setCellFormula(formulaIndexSumListDropDownGroup);
		cellCountQGroup.setCellStyle(style2);

		// list unique value in qGroup
		XSSFRow listCount5 = sheet5.createRow(2);
		XSSFCell cellListQGroup = listCount5.createCell(0);
		cellListQGroup.setCellType(XSSFCell.CELL_TYPE_FORMULA);
		String formulaListQGroup = "IF(ROWS($A$3:A3)<=$A$2,"
				+ "INDEX('Question Group'!$A$2:$A$1048575,"
				+ "SMALL(IF(FREQUENCY(IF('Question Group'!$A$2:$A$1048575<>"
				+ "\"\""
				+ ","
				+ "MATCH("
				+ "\"~\""
				+ "&'Question Group'!$A$2:$A$1048575,'Question Group'!$A$2:$A$1048575&"
				+ "\"\""
				+ ","
				+ "0)),"
				+ "ROW('Question Group'!$A$2:$A$1048575)"
				+ "-ROW('Question Group'!$A$2)+1),ROW('Question Group'!$A$2:$A$1048575)"
				+ "-ROW('Question Group'!$A$2)+1),ROWS($A$3:A3)))," + "\"\""
				+ ")";
		cellListQGroup.setCellFormula(formulaListQGroup);

		// For Sheet dropdown Form
		XSSFRow row6 = sheet6.createRow(0);
		XSSFCell cellHeaderDropForm = row6.createCell(0);
		cellHeaderDropForm.setCellValue("Unique Form Name");
		cellHeaderDropForm.setCellStyle(style);
		sheet6.setColumnWidth(0, 20 * 280);
		// For count dropdown Form
		XSSFRow countRow6 = sheet6.createRow(1);
		XSSFCell cellCountForm = countRow6.createCell(0);
		cellCountForm.setCellType(XSSFCell.CELL_TYPE_FORMULA);
		String formulaIndexSumListDropDownForm = "SUM(IF(FREQUENCY("
				+ "IF(Form!$A$2:$A$1048575<>" + "\"\"" + "," + "MATCH("
				+ "\"~\"" + "&Form!$A$2:$A$1048575,Form!$A$2:$A$1048575&"
				+ "\"\"" + ",0)),"
				+ "ROW(Form!$A$2:$A$1048575)-ROW(Form!$A$2)+1),1))";
		cellCountForm.setCellFormula(formulaIndexSumListDropDownForm);
		cellCountForm.setCellStyle(style2);

		// For list dropdown Form
		XSSFRow listCount6 = sheet6.createRow(2);
		XSSFCell cellListForm = listCount6.createCell(0);
		cellListForm.setCellType(XSSFCell.CELL_TYPE_FORMULA);
		String formulaListForm = "IF(ROWS($A$3:A3)<=$A$2,"
				+ "INDEX(Form!$A$2:$A$1048575,"
				+ "SMALL(IF(FREQUENCY(IF(Form!$A$2:$A$1048575<>" + "\"\"" + ","
				+ "MATCH(" + "\"~\""
				+ "&Form!$A$2:$A$1048575,Form!$A$2:$A$1048575&" + "\"\"" + ","
				+ "0))," + "ROW(Form!$A$2:$A$1048575)"
				+ "-ROW(Form!$A$2)+1),ROW(Form!$A$2:$A$1048575)"
				+ "-ROW(Form!$A$2)+1),ROWS($A$3:A3)))," + "\"\"" + ")";
		cellListForm.setCellFormula(formulaListForm);
	}

	private void createHeaderErrorUploadQuestion(XSSFWorkbook workbook,
			XSSFSheet sheet, AuditContext callerId) {
		XSSFCellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.CENTER);

		XSSFRow row1 = sheet.createRow(0);

		Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
				paramSubsystem);

		if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MO)) {
			for (int i = 0; i < TEMPLATE_HEADER_MO_QESTION_LIST_SHEET_ERROR.length; i++) {
				XSSFCell cell = row1.createCell(i);
				sheet.setColumnWidth(i, 20 * 256);
				cell.setCellValue(TEMPLATE_HEADER_MO_QESTION_LIST_SHEET_ERROR[i]);
				cell.setCellStyle(style);
			}
		} 
		else if (amMsuser.getAmMssubsystem().getSubsystemName()
				.equals(GlobalVal.SUBSYSTEM_MS)) {
			for (int i = 0; i < TEMPLATE_HEADER_MS_QESTION_LIST_SHEET_ERROR.length; i++) {
				XSSFCell cell = row1.createCell(i);
				sheet.setColumnWidth(i, 20 * 256);
				cell.setCellValue(TEMPLATE_HEADER_MS_QESTION_LIST_SHEET_ERROR[i]);
				cell.setCellStyle(style);
			}
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER_MC_QESTION_LIST_SHEET_ERROR.length; i++) {
				XSSFCell cell = row1.createCell(i);
				sheet.setColumnWidth(i, 20 * 256);
				cell.setCellValue(TEMPLATE_HEADER_MC_QESTION_LIST_SHEET_ERROR[i]);
				cell.setCellStyle(style);
			}
		}
	}

	@Override
	public byte[] exportEditExcel(String uuidForm, AuditContext callerId) {

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			XSSFWorkbook workbook;
			workbook = this.createEditXlsTemplate(uuidForm, callerId);
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}


	private List listQuestionLabel(Object uuid, AuditContext callerId) {
		Object[][] prm = { { "uuidQuestionGroup", uuid } };
		List list = this.getManagerDAO().selectAllNative(
				"eform.form.listQuestionForViewScheme", prm, null);

		return list;
	}

	@Override
	public List<FormQuestionBean> listQuestionDetailforView(Object params,
			String uuidGroup, AuditContext callerId) {
		List<FormQuestionBean> result = null;
		List list = this.getManagerDAO().selectAllNative(
				"eform.form.listQuestionForViewScheme", params, null);

		List<FormQuestionBean> questionDetail = new ArrayList<FormQuestionBean>();

		for (int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);

			List<FormQuestionContentBean> contentBeanList = new ArrayList<FormQuestionContentBean>();

			FormQuestionBean bean = new FormQuestionBean();

			List questionList = this.listQuestionLabel(uuidGroup, callerId);

			if (questionList == null){
				questionList = new ArrayList<MsQuestionofgroup>();
			}
			else {
				for (int x = 0; x < questionList.size(); x++) {
					Map tmp = (Map) questionList.get(x);
					FormQuestionContentBean cBean = new FormQuestionContentBean();
					cBean.setUuidQGroup(tmp.get("d0").toString());
					cBean.setQuestionGroupLabel(tmp.get("d1").toString());
					cBean.setQuestionLabel(tmp.get("d2").toString());
					cBean.setIdentifier(tmp.get("d3").toString());

					if (null == tmp.get("d4")) {
						cBean.setRelevant("");
					} 
					else {
						cBean.setRelevant(tmp.get("d4").toString());
					}

					contentBeanList.add(cBean);
				}
			}

			bean.setUuidQuestionGroup(Long.valueOf(temp.get("d0").toString()));
			bean.setQuestionGroupLabel(temp.get("d1").toString());
			bean.setHasContent(questionList.isEmpty() ? false : true);
			bean.setMsQuestioncontent(contentBeanList);
			questionDetail.add(bean);
		}
			result = questionDetail;
		return result;
	}

	@Override
	public byte[] exportTemplateUploadExcel(AuditContext callerId) {

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			XSSFWorkbook workbook;
			workbook = this.createXlsTemplateWithManySheets(callerId);
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private void createHeaderErrorUploadQGroup(XSSFWorkbook workbook,
			XSSFSheet sheet, AuditContext callerId) {
		XSSFCellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.CENTER);

		XSSFRow row1 = sheet.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET_ERROR.length; i++) {
			XSSFCell cell = row1.createCell(i);
			sheet.setColumnWidth(i, 20 * 256);
			cell.setCellValue(TEMPLATE_HEADER_MO_QUESTION_GROUP_SHEET_ERROR[i]);
			cell.setCellStyle(style);
		}

	}

	private void createHeaderErrorUploadForm(XSSFWorkbook workbook,
			XSSFSheet sheet, AuditContext callerId) {
		XSSFCellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.CENTER);

		XSSFRow row = sheet.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_MO_FORM_SHEET_ERROR.length; i++) {
			XSSFCell cell = row.createCell(i);
			sheet.setColumnWidth(i, 20 * 256);
			cell.setCellValue(TEMPLATE_HEADER_MO_FORM_SHEET_ERROR[i]);
			cell.setCellStyle(style);
		}
	}

	private void createHeaderErrorUploadRelevant(XSSFWorkbook workbook,
			XSSFSheet sheet, AuditContext callerId) {
		XSSFCellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		style.setAlignment(HorizontalAlignment.CENTER);

		XSSFRow row = sheet.createRow(0);
		XSSFCell cell = null;
		for (int i = 0; i < TEMPLATE_HEADER_MO_RELEVANSI_SHEET_ERROR.length; i++) {

			if (i == 3) {
				XSSFRow newRow = sheet.createRow(1);
				XSSFCell cellRefId = newRow.createCell(i);
				cellRefId.setCellType(XSSFCell.CELL_TYPE_FORMULA);
				cellRefId
						.setCellFormula("IFERROR(VLOOKUP(C2,'Question List'!$A$2:$B$1048575,2,0),\"\")");
			} 
			else {
				row = sheet.getRow(0);
			}
			cell = row.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_MO_RELEVANSI_SHEET_ERROR[i]);
			cell.setCellStyle(style);
			sheet.setColumnWidth(i, 20 * 256);
		}
	}

	@Override
	public List<FormQuestionBean> listQuestionDetail(String uuidForm,
			int pageNumber, int pageSize, AuditContext callerId) {
		List<FormQuestionBean> result = null;
		Object[][] prm = { { "start", (pageNumber - 1) * pageSize + 1 },
				{ "end", pageNumber * pageSize }, { "uuidForm", uuidForm } };
		List list = this.getManagerDAO().selectAllNative(
				"eform.form.listQuestionGroup", prm, null);

		List<FormQuestionBean> questionDetail = new ArrayList<FormQuestionBean>();
		for (int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);

			List<FormQuestionContentBean> contentBeanList = new ArrayList<FormQuestionContentBean>();
			List questionList = this.listQuestion(
					temp.get("d0").toString(), uuidForm, callerId);

			FormQuestionBean bean = new FormQuestionBean();
			if (questionList == null){
				questionList = new ArrayList<MsQuestionofgroup>();
			}
			else {
				for (int x = 0; x < questionList.size(); x++) {
					Map tmp = (Map) questionList.get(x);
					FormQuestionContentBean cBean = new FormQuestionContentBean();
					cBean.setQuestionLabel(tmp.get("d1").toString());
					cBean.setAnswerType(tmp.get("d2").toString());
					cBean.setRelevant(tmp.get("d3").toString());
					cBean.setChoiceFilter(tmp.get("d4").toString());
					cBean.setCalculate(tmp.get("d5").toString());
					cBean.setQuestionValidation(tmp.get("d6").toString());
					cBean.setQuestionErrMessage(tmp.get("d7").toString());
					cBean.setQuestionValue(tmp.get("d8").toString());
					cBean.setUuidQuestion(tmp.get("d9").toString());
					MsQuestion msQuestion = this.getManagerDAO().selectOne(
							MsQuestion.class, Long.valueOf(cBean.getUuidQuestion()));
					if (tmp.get("d10") != null) {
						cBean.setUuidForm(tmp.get("d10").toString());
					} 
					else {
						cBean.setUuidForm(null);
					}
					cBean.setUuidQGroup(tmp.get("d11").toString());
					cBean.setRelevantMandatory(tmp.get("d12").toString());
					contentBeanList.add(cBean);
					cBean.setMsQuestion(msQuestion);
				}
			}

			bean.setUuidQuestionGroup(Long.valueOf(temp.get("d0").toString()));
			bean.setQuestionGroupLabel(temp.get("d1").toString());
			bean.setHasContent(questionList.isEmpty() ? false : true);
			bean.setMsQuestioncontent(contentBeanList);
			bean.setIsActive(temp.get("d3").toString());
			questionDetail.add(bean);
		}
		result = questionDetail;
		return result;
	}
	
	@Override
	public List<FormQuestionBean> listQuestionDetailPublish(String uuidForm,
			int pageNumber, int pageSize, AuditContext callerId) {
		List<FormQuestionBean> result = null;
		Object[][] prm = { { "start", (pageNumber - 1) * pageSize + 1 },
				{ "end", pageNumber * pageSize }, { "uuidForm", uuidForm } };
		List list = this.getManagerDAO().selectAllNative(
				"eform.form.listQuestionGroupByVersioning", prm, null);

		List<FormQuestionBean> questionDetail = new ArrayList<FormQuestionBean>();
		for (int i = 0; i < list.size(); i++) {
			Map temp = (Map) list.get(i);

			List<FormQuestionContentBean> contentBeanList = new ArrayList<FormQuestionContentBean>();
			List questionList = this.listQuestionPublish(
					temp.get("d0").toString(), uuidForm, callerId);

			FormQuestionBean bean = new FormQuestionBean();
			if (questionList == null){
				questionList = new ArrayList<MsQuestionofgroup>();
			}
			else {
				for (int x = 0; x < questionList.size(); x++) {
					Map tmp = (Map) questionList.get(x);
					FormQuestionContentBean cBean = new FormQuestionContentBean();
					cBean.setQuestionLabel(tmp.get("d1").toString());
					cBean.setAnswerType(tmp.get("d2").toString());
					cBean.setRelevant(tmp.get("d3").toString());
					cBean.setChoiceFilter(tmp.get("d4").toString());
					cBean.setCalculate(tmp.get("d5").toString());
					cBean.setQuestionValidation(tmp.get("d6").toString());
					cBean.setQuestionErrMessage(tmp.get("d7").toString());
					cBean.setQuestionValue(tmp.get("d8").toString());
					cBean.setUuidQuestion(tmp.get("d9").toString());
					Object [][] params ={{Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(cBean.getUuidQuestion()))},
							{Restrictions.eq("msFormhistory.uuidFormHistory", Long.valueOf(temp.get("d3").toString()))},
							{Restrictions.eq("msQuestiongroup.uuidQuestionGroup", Long.valueOf(tmp.get("d11").toString()))}};
					MsFormquestionset qSet = this.getManagerDAO().selectOne(MsFormquestionset.class, params);
					MsQuestion msQuestion = commonLogic.getQuestionFromQset(qSet, callerId);
					if (tmp.get("d10") != null) {
						cBean.setUuidForm(tmp.get("d10").toString());
					} 
					else {
						cBean.setUuidForm(null);
					}
					cBean.setUuidQGroup(tmp.get("d11").toString());
					cBean.setRelevantMandatory(tmp.get("d12").toString());
					contentBeanList.add(cBean);
					cBean.setMsQuestion(msQuestion);
				}
			}

			bean.setUuidQuestionGroup(Long.valueOf(temp.get("d0").toString()));
			bean.setQuestionGroupLabel(temp.get("d1").toString());
			bean.setHasContent(questionList.isEmpty() ? false : true);
			bean.setMsQuestioncontent(contentBeanList);
			bean.setIsActive(temp.get("d2").toString());
			questionDetail.add(bean);
		}
		result = questionDetail;
		return result;
	}

	public List listQuestion(String uuid, String params, AuditContext callerId) {
		Object[][] prm = { { "uuidQGroup", uuid }, { "uuidForm", params } };
		List list = this.getManagerDAO().selectAllNative(
				"eform.form.listQuestionDetail", prm, null);

		return list;
	}
	
	public List listQuestionPublish(String uuid, String params, AuditContext callerId) {
		Object[][] prm = { { "uuidQGroup", uuid }, { "uuidForm", params } };
		List list = this.getManagerDAO().selectAllNative(
				"eform.form.listQDetailPublish", prm, null);

		return list;
	}

	@Override
	public Integer countQuestionGroup(Object params, AuditContext callerId) {
		Object[][] prm = { { "uuidForm", params } };
		Integer result = (Integer) this.getManagerDAO().selectOneNative(
					"eform.form.cntListQuestionGroup", prm);
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public byte[] processSpreadSheetFile(File uploadedFile,
			AuditContext callerId, String uuidForm) {
		byte[] errorUploadByte = null;
		Map parseMapQuestion;
		Map parseMapQGroup;
		Map parseMapForm;
		Map parseMapRelevant;
		String tempMapUuidForm;

		// Getting form first to get uuid
		try {
			tempMapUuidForm = this.parseSpreadSheeToGetUuidForm(uploadedFile,
					callerId);
		} 
		catch (IOException e) {
			errorUploadByte = this.errorUpload(callerId);
			return errorUploadByte;
		}
		uuidForm = tempMapUuidForm;

		// Upload Question
		try {
			parseMapQuestion = this.parseSpreadsheetToUploadQuestion(
					uploadedFile, callerId, uuidForm);
		} 
		catch (IOException e) {
			errorUploadByte = this.errorUpload(callerId);
			return errorUploadByte;
		}

		List listofQuestion = (List) parseMapQuestion.get("resultQuestion");
		List listofErrorQuestion = (List) parseMapQuestion
				.get("resultErrorQuestion");
		List listofEditQuestion = (List) parseMapQuestion
				.get("resultEditQuestion");
		List listofErrorEditQuestion = (List) parseMapQuestion
				.get("resultErrorEditQuestion");

		if (listofQuestion != null || !listofErrorQuestion.isEmpty()) {
				if (listofErrorQuestion.isEmpty()) {
					for (Iterator iterator = listofQuestion.iterator(); iterator
							.hasNext();) {
						MsQuestion msQuestion = (MsQuestion) iterator.next();
						this.getManagerDAO().insert(msQuestion);
						this.auditManager.auditAdd(msQuestion, auditInfoQ, callerId.getCallerId(), "");
					}
				}

				if (!listofErrorQuestion.isEmpty()) {
					try {
						errorUploadByte = exportErrorUploadQuestion(
								listofErrorQuestion, callerId);
					} 
					catch (SQLException e) {
						throw new FormException(
							this.messageSource.getMessage("businesslogic.error.generatexls", 
									null, this.retrieveLocaleAudit(callerId)),
							FormException.Reason.UNKNOWN);
					}
					return errorUploadByte;
				}

		} 
		else {
			if (listofErrorQuestion.isEmpty()
					&& !listofEditQuestion.isEmpty()) {
				for (Iterator iterator = listofEditQuestion.iterator(); iterator
						.hasNext();) {
					MsQuestion msQuestion = (MsQuestion) iterator.next();
					MsQuestion msQ = this.getManagerDAO().selectOne(
							MsQuestion.class, msQuestion.getUuidQuestion());
					if (msQ != null) {
						msQ.setDtmUpd(new Date());
						msQ.setUsrUpd(callerId.getCallerId().toString());
						msQ.setQuestionLabel(msQuestion.getQuestionLabel());
						msQ.setRefId(msQuestion.getRefId());
						msQ.setMsAnswertype(msQuestion.getMsAnswertype());
						msQ.setImgQlt(msQuestion.getImgQlt());
						msQ.setLovGroup(msQuestion.getLovGroup());
						msQ.setMaxLength(msQuestion.getMaxLength());
						msQ.setMsAssettag(msQuestion.getMsAssettag());
						msQ.setMsOrdertag(msQuestion.getMsOrdertag());
						msQ.setMsCollectiontag(msQuestion
								.getMsCollectiontag());
						msQ.setRegexPattern(msQuestion.getRegexPattern());
						msQ.setIsHolidayAllowed(msQuestion
								.getIsHolidayAllowed());
						msQ.setIsVisible(msQuestion.getIsVisible());
						msQ.setIsReadonly(msQuestion.getIsReadonly());
						msQ.setIsActive(msQuestion.getIsActive());
						msQ.setIsMandatory(msQuestion.getIsMandatory());
						this.auditManager.auditEdit(msQ, auditInfoQ, callerId.getCallerId(), "");
						this.getManagerDAO().update(msQ);
					}
				}
			}

			if (!listofErrorEditQuestion.isEmpty()) {
				try {
					errorUploadByte = exportErrorUploadQuestion(
							listofErrorEditQuestion, callerId);

				} 
				catch (SQLException e) {
					throw new FormException(
						this.messageSource.getMessage("businesslogic.error.generatexls", 
								null, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.INVALID_SCRIPT);
				}
				return errorUploadByte;
			}
		}

		// Upload Question Group
		try {
			parseMapQGroup = this.parseSpreadsheetToUploadQGroup(uploadedFile,
					callerId);
		} 
		catch (IOException e) {
			errorUploadByte = this.errorUpload(callerId);
			return errorUploadByte;
		}

		List listofQGorup = (List) parseMapQGroup.get("resultQGroup");
		List listofErrorQGroup = (List) parseMapQGroup.get("resultErrorQGroup");
		List listofQOGorup = (List) parseMapQGroup.get("resultQOGroup");
		List listofErrorQOGroup = (List) parseMapQGroup
				.get("resultErrorQOGroup");
		List listofEditQOGorup = (List) parseMapQGroup.get("resultEditQOGroup");
		List listofErrorEditQOGroup = (List) parseMapQGroup
				.get("resultErrorEditQOGroup");
		List insertQGroup = new ArrayList<>();

		if (listofQOGorup != null || listofQGorup != null
				|| !listofErrorQGroup.isEmpty()
				|| !listofErrorQOGroup.isEmpty()) {
			if (listofErrorQOGroup.isEmpty()) {

				for (Iterator iterator = listofQGorup.iterator(); iterator
						.hasNext();) {
					MsQuestiongroup msQuestiongroup = (MsQuestiongroup) iterator
							.next();
					Object[][] param = { { Restrictions.eq(
							"questionGroupLabel",
							msQuestiongroup.getQuestionGroupLabel()) } };
					MsQuestiongroup msqQuestion = this.getManagerDAO()
							.selectOne(MsQuestiongroup.class, param);
					if (msqQuestion == null) {
						this.getManagerDAO().insert(msQuestiongroup);
						this.auditManager.auditAdd(msQuestiongroup, auditInfoQG, callerId.getCallerId(), "");
						insertQGroup.add(msQuestiongroup);
					}

				}

				int sequence = 10;
				MsQuestiongroupofform msqOForm = new MsQuestiongroupofform();
				boolean firstIdx = true;

				for (Iterator iterator = listofQOGorup.iterator(); iterator
						.hasNext();) {
					MsQuestionofgroup msQuestionofgroup = (MsQuestionofgroup) iterator
							.next();

					Object[][] param = { { Restrictions.eq(
							"questionGroupLabel", msQuestionofgroup
									.getMsQuestiongroup()
									.getQuestionGroupLabel()) } };
					MsQuestiongroup msqOGroup = this.getManagerDAO()
							.selectOne(MsQuestiongroup.class, param);
					msQuestionofgroup.setMsQuestiongroup(msqOGroup);

					Object[][] prm = {
							{
									Restrictions.eq("msQuestion.uuidQuestion",
									msQuestionofgroup.getMsQuestion()
											.getUuidQuestion()) },
							{
									Restrictions.eq("msQuestiongroup.uuidQuestionGroup",
									msQuestionofgroup.getMsQuestiongroup()
											.getUuidQuestionGroup()) } };
					MsQuestionofgroup msQuestionofgroup2 = this
							.getManagerDAO().selectOne(
									MsQuestionofgroup.class, prm);

					if (msQuestionofgroup2 == null) {
						if (firstIdx == true) {
							msQuestionofgroup.setSeqOrder(sequence);
							this.getManagerDAO().insert(msQuestionofgroup);
							this.auditManager.auditAdd(msQuestionofgroup, auditInfoQoG, callerId.getCallerId(), "");
							msqOForm.setMsQuestiongroup(msQuestionofgroup
									.getMsQuestiongroup());
							sequence += 10;
							firstIdx = false;
						} 
						else {
							if (msqOForm != null) {
								if (msqOForm.getMsQuestiongroup().getUuidQuestionGroup()
										== msQuestionofgroup.getMsQuestiongroup().getUuidQuestionGroup()) {
									msQuestionofgroup.setSeqOrder(sequence);
									this.getManagerDAO().insert(msQuestionofgroup);
									this.auditManager.auditAdd(msQuestionofgroup, auditInfoQoG, callerId.getCallerId(), "");
									sequence += 10;
									msqOForm.setMsQuestiongroup(msQuestionofgroup
											.getMsQuestiongroup());
								} 
								else {
									sequence = 10;
									msQuestionofgroup.setSeqOrder(sequence);
									this.getManagerDAO().insert(msQuestionofgroup);
									this.auditManager.auditAdd(msQuestionofgroup, auditInfoQoG, callerId.getCallerId(), "");
									sequence += 10;
									msqOForm.setMsQuestiongroup(msQuestionofgroup
											.getMsQuestiongroup());
								}
							}
						}
					}
				}
			}

			if (!listofErrorQOGroup.isEmpty()) {
				try {
					errorUploadByte = exportErrorUploadQOGroup(listofErrorQOGroup, callerId);
				} 
				catch (SQLException e) {
					throw new FormException(
						this.messageSource.getMessage("businesslogic.error.generatexls", 
								null, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.UNKNOWN);
				}

				if (!listofQuestion.isEmpty()) {
					this.deleteQuestion(listofQuestion, callerId.getCallerId());
				}

				return errorUploadByte;
			}
		} 
		else {
			if (listofErrorEditQOGroup.isEmpty()
					&& !listofEditQOGorup.isEmpty()) {
				for (Iterator iterator = listofEditQOGorup.iterator(); iterator
						.hasNext();) {
					MsQuestionofgroup msQuestionofgroup = (MsQuestionofgroup) iterator
							.next();
					MsQuestionofgroup msQOGroup = this.getManagerDAO()
							.selectOne(
									MsQuestionofgroup.class,
									msQuestionofgroup
											.getUuidQuestionOfGroup());
					msQOGroup.setMsQuestion(msQuestionofgroup
							.getMsQuestion());
					msQOGroup.setMsQuestiongroup(msQuestionofgroup
							.getMsQuestiongroup());
					msQOGroup.setSeqOrder(msQuestionofgroup.getSeqOrder());
					Object[][] paramsQGLabel = { {
							Restrictions.eq("uuidQuestionGroup",
							msQuestionofgroup.getMsQuestiongroup()
									.getUuidQuestionGroup()) } };
					MsQuestiongroup msQGroup = this
							.getManagerDAO()
							.selectOne(MsQuestiongroup.class, paramsQGLabel);

					if (!msQGroup.getQuestionGroupLabel().equals(
							msQuestionofgroup.getMsQuestiongroup()
									.getQuestionGroupLabel())) {
						msQGroup.setQuestionGroupLabel(msQuestionofgroup
								.getMsQuestiongroup()
								.getQuestionGroupLabel());
						msQGroup.setDtmUpd(new Date());
						msQGroup.setUsrUpd(callerId.getCallerId()
								.toString());
						this.auditManager.auditEdit(msQGroup, auditInfoQG, callerId.getCallerId(), "");
						this.getManagerDAO().update(msQGroup);
					}
					
					this.auditManager.auditEdit(msQOGroup, auditInfoQoG, callerId.getCallerId(), "");
					this.getManagerDAO().update(msQOGroup);
				}
			}

			if (!listofErrorEditQOGroup.isEmpty()) {
				try {
					errorUploadByte = exportErrorUploadQOGroup(listofErrorEditQOGroup, callerId);
				} 
				catch (SQLException e) {
					throw new FormException(
						this.messageSource.getMessage("businesslogic.error.generatexls", 
								null, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.UNKNOWN);
				}
				return errorUploadByte;
			}
		}

		// Upload Form
		try {
			parseMapForm = this.parseSpreadsheetToUploadForm(uploadedFile,
					callerId);
		} 
		catch (IOException e) {
			errorUploadByte = this.errorUpload(callerId);
			return errorUploadByte;
		}

		List listofForm = (List) parseMapForm.get("resultForm");
		List listofErrorForm = (List) parseMapForm.get("resultErrorForm");
		List listofEditForm = (List) parseMapForm.get("resultEditForm");
		List insertForm = new ArrayList<>();

		List listofQOForm = (List) parseMapForm.get("resultQGForm");
		List listofErrorQOForm = (List) parseMapForm.get("resultErrorQGForm");
		List listofEditQOForm = (List) parseMapForm.get("resultEditQGForm");
		List listofErrorEditQOForm = (List) parseMapForm
				.get("resultErrorEditQGForm");

		if (listofForm != null || listofQOForm != null
				|| !listofErrorQOForm.isEmpty() || !listofErrorForm.isEmpty()) {
			if (listofErrorQOForm.isEmpty()) {
				for (Iterator iterator = listofForm.iterator(); iterator
						.hasNext();) {
					MsForm msForm = (MsForm) iterator.next();
					Object[][] prm = { { Restrictions.eq("formName", msForm.getFormName()) } };
					MsForm msf = this.getManagerDAO().selectOne(
							MsForm.class, prm);

					if (msf == null) {
						this.getManagerDAO().insert(msForm);
						this.auditManager.auditAdd(msForm, auditInfoF, callerId.getCallerId(), "");
						insertForm.add(msForm);
					}
				}

				int seqOrder = 10;
				MsQuestiongroupofform msqOForm = new MsQuestiongroupofform();
				boolean firstIdx = true;

				for (Iterator iterator = listofQOForm.iterator(); iterator
						.hasNext();) {
					MsQuestiongroupofform msQForm = (MsQuestiongroupofform) iterator
							.next();
					Object[][] params = { { Restrictions.eq("formName",
							msQForm.getMsForm().getFormName()) } };
					MsForm msf = this.getManagerDAO().selectOne(
							MsForm.class, params);
					msQForm.setMsForm(msf);

					Object[][] param = {
							{
									Restrictions.eq("msQuestiongroup.uuidQuestionGroup",
									msQForm.getMsQuestiongroup()
											.getUuidQuestionGroup()) },
							{ Restrictions.eq("msForm.uuidForm",
									msQForm.getMsForm().getUuidForm()) } };
					MsQuestiongroupofform msqGForm = this.getManagerDAO()
							.selectOne(MsQuestiongroupofform.class, param);

					if (msqGForm == null) {
						if (firstIdx == true) {
							msQForm.setLineSeqOrder(seqOrder);
							this.getManagerDAO().insert(msQForm);
							this.auditManager.auditAdd(msQForm, auditInfoQGoF, callerId.getCallerId(), "");
							seqOrder += 10;
							msqOForm.setMsForm(msf);
							firstIdx = false;
						} 
						else {
							if (msqOForm.getMsForm().getUuidForm()
									== msQForm.getMsForm().getUuidForm()) {
								msQForm.setLineSeqOrder(seqOrder);
								this.getManagerDAO().insert(msQForm);
								this.auditManager.auditAdd(msQForm, auditInfoQGoF, callerId.getCallerId(), "");
								seqOrder += 10;
								msqOForm.setMsForm(msf);
							} 
							else {
								seqOrder = 10;
								msQForm.setLineSeqOrder(seqOrder);
								this.getManagerDAO().insert(msQForm);
								this.auditManager.auditAdd(msQForm, auditInfoQGoF, callerId.getCallerId(), "");
								seqOrder += 10;
								msqOForm.setMsForm(msf);
							}
						}
					}
				}
			}

			if (!listofErrorQOForm.isEmpty()) {
				try {
					errorUploadByte = exportErrorUploadForm(listofErrorQOForm, callerId);
				} 
				catch (SQLException e) {
					throw new FormException(
						this.messageSource.getMessage("businesslogic.error.generatexls", 
								null, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.UNKNOWN);
				}

				if (!listofQOGorup.isEmpty()) {
					this.deleteQOGroup(listofQOGorup, callerId.getCallerId());
				}
				if (!insertQGroup.isEmpty()) {
					this.deleteQGroup(insertQGroup, callerId.getCallerId());
				}
				if (!listofQuestion.isEmpty()) {
					this.deleteQuestion(listofQuestion, callerId.getCallerId());
				}

				return errorUploadByte;
			}
		} 
		else {
			if (listofErrorEditQOForm.isEmpty()
					&& (!listofEditForm.isEmpty() || listofEditForm.isEmpty())
					&& (!listofEditQOForm.isEmpty() || listofEditQOForm
							.isEmpty())) {
				for (Iterator iterator = listofEditForm.iterator(); iterator
						.hasNext();) {
					MsForm msForm = (MsForm) iterator.next();
					MsForm msF = this.getManagerDAO().selectOne(
							MsForm.class, msForm.getUuidForm());
					if (msF != null) {
						msF.setFormName(msForm.getFormName());
						msF.setIsActive(msForm.getIsActive());
						msF.setIsPrintable(msForm.getIsPrintable());
						msF.setAmMssubsystem(msForm.getAmMssubsystem());
						msF.setMsFormcategory(msForm.getMsFormcategory());
						msF.setDtmUpd(new Date());
						msF.setUsrUpd(callerId.getCallerId().toString());
						this.auditManager.auditEdit(msF, auditInfoF, callerId.getCallerId(), "");
						this.getManagerDAO().update(msF);
					}
				}

				for (Iterator iterator = listofEditQOForm.iterator(); iterator
						.hasNext();) {
					MsQuestiongroupofform msQuestiongroupofform = (MsQuestiongroupofform) iterator
							.next();
					MsQuestiongroupofform msQOForm = this.getManagerDAO()
							.selectOne(
									MsQuestiongroupofform.class,
									msQuestiongroupofform
											.getUuidQuestionGroupOfForm());
					msQOForm.setMsQuestiongroup(msQuestiongroupofform
							.getMsQuestiongroup());
					msQOForm.setMsForm(msQuestiongroupofform.getMsForm());
					msQOForm.setLineSeqOrder(msQuestiongroupofform
							.getLineSeqOrder());
					this.auditManager.auditEdit(msQOForm, auditInfoQGoF, callerId.getCallerId(), "");
					this.getManagerDAO().update(msQOForm);

				}
			}

			if (!listofErrorEditQOForm.isEmpty()) {
				try {
					errorUploadByte = exportErrorUploadForm(listofErrorQOForm, callerId);
				} 
				catch (SQLException e) {
					throw new FormException(
						this.messageSource.getMessage("businesslogic.error.generatexls", 
								null, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.UNKNOWN);
				}
				return errorUploadByte;
			}
		}

		// Upload Relevant
		try {
			parseMapRelevant = this.parseSpreadsheetToUploadRelevant(
					uploadedFile, callerId);
		} 
		catch (IOException e) {
			errorUploadByte = this.errorUpload(callerId);
			return errorUploadByte;
		}

		List listofRelevant = (List) parseMapRelevant.get("resultRelevant");
		List listofErrorRelevant = (List) parseMapRelevant
				.get("resultErrorRelevant");
		List listofEditRelevant = (List) parseMapRelevant
				.get("resultEditRelevant");
		List listofErrorEditRelevant = (List) parseMapRelevant
				.get("resultErrorEditRelevant");

		if (listofRelevant != null || !listofErrorRelevant.isEmpty()) {
			if (listofErrorRelevant.isEmpty()) {
				for (Iterator iterator = listofRelevant.iterator(); iterator.hasNext();) {
					MsQuestionrelevant msQuestionrelevant = (MsQuestionrelevant) iterator.next();
					this.getManagerDAO().insert(msQuestionrelevant);
					this.auditManager.auditAdd(msQuestionrelevant, auditInfoQR, callerId.getCallerId(), "");
				}
			}

			if (!listofErrorRelevant.isEmpty()) {
				try {
					errorUploadByte = exportErrorUploadRelevant(listofErrorRelevant, callerId);
				} 
				catch (SQLException e) {
					throw new FormException(
						this.messageSource.getMessage("businesslogic.error.generatexls", 
								null, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.UNKNOWN);
				}

				if (null != listofQOForm) {
					if (!listofQOForm.isEmpty()) {
						this.deleteQGForm(listofQOForm, callerId.getCallerId());
					}
				}

				if (!insertForm.isEmpty()) {
					this.deleteForm(insertForm, callerId.getCallerId());
				}

				if (null != listofQOGorup) {
					if (!listofQOGorup.isEmpty()) {
						this.deleteQOGroup(listofQOGorup, callerId.getCallerId());
					}
				}

				if (!insertQGroup.isEmpty()) {
					this.deleteQGroup(insertQGroup, callerId.getCallerId());
				}

				if (null != listofQuestion) {
					if (!listofQuestion.isEmpty()) {
						this.deleteQuestion(listofQuestion, callerId.getCallerId());
					}
				}

				return errorUploadByte;
			}
		} 
		else {
			if (listofErrorEditRelevant.isEmpty()
					&& !listofEditRelevant.isEmpty()) {
				if (listofErrorEditRelevant.isEmpty()) {
					for (Iterator iterator = listofEditRelevant.iterator(); iterator
							.hasNext();) {
						MsQuestionrelevant msQuestionrelevant = (MsQuestionrelevant) iterator
								.next();
						MsQuestionrelevant msQRelevant = this
								.getManagerDAO().selectOne(
										MsQuestionrelevant.class,
										msQuestionrelevant
												.getUuidQuestionRelevant());
						this.auditManager.auditEdit(msQRelevant, auditInfoQR, callerId.getCallerId(), "");
						this.getManagerDAO().update(msQRelevant);
					}
				}
			}

			if (!listofErrorEditRelevant.isEmpty()) {
				try {
					errorUploadByte = exportErrorUploadRelevant(listofErrorEditRelevant, callerId);
				} 
				catch (SQLException e) {
					throw new FormException(
						this.messageSource.getMessage("businesslogic.error.generatexls", 
								null, this.retrieveLocaleAudit(callerId)),
						FormException.Reason.UNKNOWN);
				}
				return errorUploadByte;
			}
		}

		return errorUploadByte;
	}

	private List deleteQuestion(List listQuestion, String callerId) {
		for (Iterator iterator = listQuestion.iterator(); iterator.hasNext();) {
			MsQuestion msQuestion = (MsQuestion) iterator.next();
			this.auditManager.auditDelete(msQuestion, auditInfoQ, callerId, "");
			this.getManagerDAO().delete(msQuestion);
		}
		return listQuestion;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private List deleteQGroup(List listQGroup, String callerId) {
		for (Iterator iterator = listQGroup.iterator(); iterator.hasNext();) {
			MsQuestiongroup msQuestiongroup = (MsQuestiongroup) iterator.next();
			Object[][] param = { { Restrictions.eq("uuidQuestionGroup",
					msQuestiongroup.getUuidQuestionGroup()) } };
			MsQuestiongroup msqGroup = this.getManagerDAO().selectOne(
					MsQuestiongroup.class, param);
			if (msqGroup != null) {
				this.auditManager.auditDelete(msQuestiongroup, auditInfoQG, callerId, "");
				this.getManagerDAO().delete(msQuestiongroup);
			}
		}
		return listQGroup;
	}

	private List deleteQOGroup(List listQOGroup, String callerId) {
		for (Iterator iterator = listQOGroup.iterator(); iterator.hasNext();) {
			MsQuestionofgroup msQuestionofgroup = (MsQuestionofgroup) iterator
					.next();
			this.auditManager.auditDelete(msQuestionofgroup, auditInfoQoG, callerId, "");
			this.getManagerDAO().delete(msQuestionofgroup);
		}
		return listQOGroup;
	}

	private List deleteForm(List listForm, String callerId) {
		for (Iterator iterator = listForm.iterator(); iterator.hasNext();) {
			MsForm msForm = (MsForm) iterator.next();
			Object[][] prm = { { Restrictions.eq("uuidForm", msForm.getUuidForm()) } };
			MsForm msf = this.getManagerDAO().selectOne(MsForm.class, prm);
			if (msf != null) {
				this.auditManager.auditDelete(msForm, auditInfoF, callerId, "");
				this.getManagerDAO().delete(msForm);
			}
		}
		return listForm;
	}

	private List deleteQGForm(List listQGForm, String callerId) {
		for (Iterator iterator = listQGForm.iterator(); iterator.hasNext();) {
			MsQuestiongroupofform msQForm = (MsQuestiongroupofform) iterator
					.next();
			Object[][] params = { { Restrictions.eq("formName",
					msQForm.getMsForm().getFormName()) } };
			MsForm msf = this.getManagerDAO().selectOne(MsForm.class, params);
			msQForm.setMsForm(msf);
			Object[][] param = {
					{ Restrictions.eq("msQuestiongroup.uuidQuestionGroup",
							msQForm.getMsQuestiongroup().getUuidQuestionGroup()) },
					{ Restrictions.eq("msForm.uuidForm", msQForm.getMsForm().getUuidForm()) } };
			MsQuestiongroupofform msqGForm = this.getManagerDAO().selectOne(
					MsQuestiongroupofform.class, param);
			if (msqGForm != null) {
				this.auditManager.auditDelete(msQForm, auditInfoQGoF, callerId, "");
				this.getManagerDAO().delete(msQForm);
			}
		}
		return listQGForm;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private Map parseSpreadsheetToUploadQuestion(File uploadedFile,
			AuditContext callerId, String uuidForm) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsQuestion> resultQuestion = new ArrayList<>();
		List<MsQuestion> resultErrorQuestion = new ArrayList<>();
		List<MsQuestion> resultEditQuestion = new ArrayList<>();
		List<MsQuestion> resultErrorEditQuestion = new ArrayList<>();

		FileInputStream inputStream = new FileInputStream(uploadedFile);
		XSSFWorkbook wb = new XSSFWorkbook(inputStream);
		int numberOfSheets = wb.getNumberOfSheets();

		Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
				paramSubsystem);

		ArrayList listTag = new ArrayList();
		for (int sheetIdx = 0; sheetIdx < numberOfSheets; sheetIdx++) {
			if (sheetIdx == 0) {
				XSSFSheet sheet = wb.getSheetAt(sheetIdx);
				int rows = sheet.getPhysicalNumberOfRows();
				for (int r = 1; r < rows; r++) {
					XSSFRow row = sheet.getRow(r);

					if (row == null) {
						continue;
					}

					boolean isEmptyRow = checkEmptyRow(row, callerId);

					if (isEmptyRow == true) {
						continue;
					}
					MsQuestion msQuestion = new MsQuestion();
					for (int c = 0; c < 15; c++) { // kolom di excel
						XSSFCell cell = row
								.getCell(c, Row.RETURN_BLANK_AS_NULL);

						String value = "";

						// if intValue -1, then sequence is posted with string
						if (cell != null) {
							switch (cell.getCellType()) {
							case XSSFCell.CELL_TYPE_NUMERIC:
								value = String.valueOf((int) cell
										.getNumericCellValue());
								break;

							case XSSFCell.CELL_TYPE_STRING:
								value = cell.getStringCellValue();
								break;

							default:
							}
						}

						switch (c) {
						case 0:
							msQuestion.setRefId(value);
							break;
						case 1:
							msQuestion.setQuestionLabel(value);
							break;
						case 2:
							Object[][] prms = { { Restrictions.eq(
									"answerTypeName", value) } };
							MsAnswertype msAnswertype = this.getManagerDAO()
									.selectOne(MsAnswertype.class, prms);

							if (null != msAnswertype) {
								msQuestion.setMsAnswertype(msAnswertype);
							}
							break;
						case 3:
							if (value.equals(null) || value.equals("")) {
								msQuestion.setImgQlt(null);
							} 
							else {
								msQuestion.setImgQlt(value);
							}

							break;
						case 4:
							if (!value.equals(null) || !value.equals("")) {
								String[][] prm = { { "lovGroup", value } };
								List lovGroup = this.getManagerDAO()
										.selectAllNative(
												"eform.form.getMsLovGroup",
												prm, null);

								if (null != lovGroup) {
									msQuestion.setLovGroup(value);
								}
							}
							break;
						case 5:
							if (value.equals("")) {
								msQuestion.setMaxLength(null);
							} 
							else {
								msQuestion.setMaxLength(Integer.valueOf(value));
							}

							break;
						case 6:
							if (amMsuser.getAmMssubsystem().getSubsystemName()
									.equals(GlobalVal.SUBSYSTEM_MO)) {
								Object[][] orderTag = { { Restrictions.eq(
										"tagName", value) } };
								MsOrdertag msOrdertag = this.getManagerDAO()
										.selectOne(MsOrdertag.class, orderTag);

								if (null != msOrdertag) {
									msQuestion.setMsOrdertag(msOrdertag);
								} 
								else {
									msQuestion.setMsOrdertag(null);
								}

							} 
							else if (amMsuser.getAmMssubsystem()
									.getSubsystemName()
									.equals(GlobalVal.SUBSYSTEM_MS)) {
								Object[][] assetTag = { { Restrictions.eq(
										"assetTagName", value) } };
								MsAssettag msAssettag = this.getManagerDAO()
										.selectOne(MsAssettag.class, assetTag);

								if (null != msAssettag) {
									msQuestion.setMsAssettag(msAssettag);
								} 
								else {
									msQuestion.setMsAssettag(null);
								}

							} 
							else {
								Object[][] collTag = { { Restrictions.eq(
										"tagName", value) } };
								MsCollectiontag msCollectiontag = this
										.getManagerDAO().selectOne(
												MsCollectiontag.class, collTag);

								if (null != msCollectiontag) {
									msQuestion
											.setMsCollectiontag(msCollectiontag);
								} 
								else {
									msQuestion.setMsCollectiontag(null);
								}

							}
							break;
						case 7:
							if ("".equals(value)) {
								value = "0";
							}
							msQuestion.setIsMandatory(value);
							break;
						case 8:
							if ("".equals(value)) {
								value = "0";
							}
							msQuestion.setIsReadonly(value);
							break;
						case 9:
							if ("".equals(value)) {
								value = "0";
							}
							msQuestion.setIsVisible(value);
							break;
						case 10:
							if ("".equals(value)) {
								value = "0";
							}
							msQuestion.setIsActive(value);
							break;
						case 11:
							msQuestion.setRegexPattern(value);
							break;
						case 12:
							if ("".equals(value)) {
								value = "0";
							}
							msQuestion.setIsHolidayAllowed(value);
							break;
						case 13:
							uploadBean.setValue(value);
							break;
						case 14:
							if (uploadBean.getValue().toString().equals("1")) {
								msQuestion.setUuidQuestion(Long.valueOf(value));
							}

							if (!uploadBean.getValue().equals("1")
									&& !uploadBean.getValue().toString()
											.equals("0")) {
								msQuestion.setDtmCrt(new Date());
								msQuestion.setUsrCrt(callerId.getCallerId()
										.toString());
								msQuestion.setAmMssubsystem(amMsuser
										.getAmMssubsystem());
							}

							break;
						}
						wb.close();
					}
					if (uploadBean.getValue().toString().equals("1")) {
						StringBuilder errorText = checkingUploadQuestion(
								msQuestion, uploadBean.getValue()
										.toString(), callerId, uuidForm, listTag);
						if (errorText.length() == 0) {
							resultEditQuestion.add(msQuestion);
						} 
						else {
							resultErrorEditQuestion.add(msQuestion);
							uploadBean.setErrorText(errorText.toString());
							paramParse.put("resultErrorEditQuestion",
									resultErrorEditQuestion);
							paramParse.put("resultErrorQuestion",
									resultErrorQuestion);
							inputStream.close();
							return paramParse;
						}
					}
					if (!uploadBean.getValue().toString().equals("1")
							&& !uploadBean.getValue().toString().equals("0")) {

						StringBuilder errorText = checkingUploadQuestion(
								msQuestion, uploadBean.getValue()
										.toString(), callerId, uuidForm, listTag);
						if (errorText.length() == 0) {
							resultQuestion.add(msQuestion);
							paramParse
									.put("resultQuestion", resultQuestion);
						} 
						else {
							resultErrorQuestion.add(msQuestion);
							uploadBean.setErrorText(errorText.toString());
							paramParse.put("resultErrorQuestion",
									resultErrorQuestion);
							inputStream.close();
							return paramParse;
						}
					}
				}
				paramParse.put("resultEditQuestion", resultEditQuestion);
				paramParse.put("resultErrorEditQuestion",
						resultErrorEditQuestion);
				paramParse.put("resultErrorQuestion", resultErrorQuestion);
			}
		}
		return paramParse;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private Map parseSpreadsheetToUploadQGroup(File uploadedFile,
			AuditContext callerId) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsQuestiongroup> resultQGroup = new ArrayList<>();
		List<MsQuestiongroup> resultErrorQGroup = new ArrayList<>();
		List<MsQuestionofgroup> resultQOGroup = new ArrayList<>();
		List<MsQuestionofgroup> resultErrorQOGroup = new ArrayList<>();
		List<MsQuestionofgroup> resultEditQOGroup = new ArrayList<>();
		List<MsQuestionofgroup> resultErrorEditQOGroup = new ArrayList<>();

		FileInputStream inputStream = new FileInputStream(uploadedFile);

		XSSFWorkbook wb = new XSSFWorkbook(inputStream);

		int numberOfSheets = wb.getNumberOfSheets();

		AmMsuser amMsuser = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
		for (int sheetIdx = 0; sheetIdx < numberOfSheets; sheetIdx++) {
			if (sheetIdx == 1) {
				XSSFSheet sheet = wb.getSheetAt(sheetIdx);
				int rows = sheet.getPhysicalNumberOfRows();
				for (int r = 1; r < rows; r++) {
					XSSFRow row = sheet.getRow(r);
					if (row == null) {
						continue;
					}

					boolean isEmptyRow = checkEmptyRow(row, callerId);

					if (isEmptyRow == true) {
						continue;
					}

					MsQuestiongroup msQuestiongroup = new MsQuestiongroup();
					MsQuestionofgroup msQuestionofgroup = new MsQuestionofgroup();

					for (int c = 0; c < 8; c++) { // kolom di excel
						XSSFCell cell = row
								.getCell(c, Row.RETURN_BLANK_AS_NULL);

						String value = "";
						int intValue = -1;

						if (cell != null) {
							switch (cell.getCellType()) {
							case XSSFCell.CELL_TYPE_NUMERIC:
								value = String.valueOf((int) cell
										.getNumericCellValue());
								intValue = Integer.valueOf(value).intValue();
								break;

							case XSSFCell.CELL_TYPE_STRING:
								value = cell.getStringCellValue();
								break;

							default:
							}
						}
						switch (c) {
						case 0:
							Object[][] params = { { Restrictions.eq(
									"questionGroupLabel", value) },
									{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
											amMsuser.getAmMssubsystem().getUuidMsSubsystem()) }};
							MsQuestiongroup msGroup = this.getManagerDAO()
									.selectOne(MsQuestiongroup.class, params);

							if (null == msGroup) {
								msQuestiongroup.setQuestionGroupLabel(value);
								msQuestionofgroup
										.setMsQuestiongroup(msQuestiongroup);
							} 
							else {
								msQuestionofgroup.setMsQuestiongroup(msGroup);
								msQuestiongroup.setUuidQuestionGroup(msGroup
										.getUuidQuestionGroup());
								msQuestiongroup.setQuestionGroupLabel(msGroup
										.getQuestionGroupLabel());
							}

							break;
						case 1:
							if ("".equals(value)) {
								value = "0";
							}
							msQuestiongroup.setIsActive(value);
							break;
						case 2:
							if (!value.equals(null) || !value.equals("")) {
								Object[][] param = { { Restrictions.eq("refId", value) },
										{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
												amMsuser.getAmMssubsystem().getUuidMsSubsystem()) }};
								MsQuestion msqQuestion = this.getManagerDAO()
										.selectOne(MsQuestion.class, param);

								if (null != msqQuestion) {
									msQuestionofgroup
											.setMsQuestion(msqQuestion);
								}

							}
							break;
						case 4:
							uploadBean.setValue(value);
							break;
						case 5:
							if (uploadBean.getValue().toString().equals("1")) {
								msQuestionofgroup.setSeqOrder(intValue);
							}
							break;
						case 6:
							if (uploadBean.getValue().toString().equals("1")) {
								msQuestionofgroup.setUuidQuestionOfGroup(Long.valueOf(value));
								MsQuestionofgroup msQOGroup = this
										.getManagerDAO()
										.selectOne(
												MsQuestionofgroup.class,
												msQuestionofgroup
														.getUuidQuestionOfGroup());
								MsQuestiongroup msQGroup = this
										.getManagerDAO()
										.selectOne(
												MsQuestiongroup.class,
												msQOGroup.getMsQuestiongroup()
														.getUuidQuestionGroup());
								msQuestionofgroup
										.getMsQuestiongroup()
										.setUuidQuestionGroup(
												msQGroup.getUuidQuestionGroup());
							}

							if (!uploadBean.getValue().toString().equals("1")
									&& !uploadBean.getValue().toString()
											.equals("0")) {
								msQuestiongroup.setDtmCrt(new Date());
								msQuestiongroup.setUsrCrt(callerId
										.getCallerId().toString());
								msQuestiongroup.setAmMssubsystem(amMsuser
										.getAmMssubsystem());
								msQuestionofgroup.setDtmCrt(new Date());
								msQuestionofgroup.setUsrCrt(callerId
										.getCallerId().toString());
							}
							break;
						}
						wb.close();
					}
					if (!uploadBean.getValue().toString().equals("1")
							&& !uploadBean.getValue().toString().equals("0")) {
						StringBuilder errorText = checkingUploadQOGroup(
								msQuestionofgroup, uploadBean.getValue()
										.toString(), callerId);
						if (errorText.length() == 0) {
							resultQOGroup.add(msQuestionofgroup);
							paramParse.put("resultQOGroup", resultQOGroup);
						} 
						else {
							resultErrorQOGroup.add(msQuestionofgroup);
							uploadBean.setErrorText(errorText.toString());
							paramParse.put("resultErrorQOGroup",
									resultErrorQOGroup);
							paramParse.put("resultErrorQGroup",
									resultErrorQGroup);
							inputStream.close();
							return paramParse;
						}
						resultQGroup.add(msQuestiongroup);
						paramParse.put("resultQGroup", resultQGroup);
					}

					if (uploadBean.getValue().toString().equals("1")) {
						StringBuilder errorText = checkingUploadQOGroup(
								msQuestionofgroup, uploadBean.getValue()
										.toString(), callerId);
						if (errorText.length() == 0) {
							resultEditQOGroup.add(msQuestionofgroup);
							paramParse.put("resultEditQOGroup",
									resultEditQOGroup);
						} 
						else {
							resultErrorEditQOGroup.add(msQuestionofgroup);
							uploadBean.setErrorText(errorText.toString());
							paramParse.put("resultErrorEditQOGroup",
									resultErrorEditQOGroup);
							paramParse.put("resultErrorQOGroup",
									resultErrorQOGroup);
							paramParse.put("resultErrorQGroup",
									resultErrorQGroup);
							inputStream.close();
							return paramParse;
						}
					}
				}

				paramParse.put("resultEditQOGroup", resultEditQOGroup);
				paramParse.put("resultErrorQOGroup", resultErrorQOGroup);
				paramParse.put("resultErrorQGroup", resultErrorQGroup);
				paramParse.put("resultErrorEditQOGroup", resultErrorEditQOGroup);
			}
		}
		return paramParse;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private Map parseSpreadsheetToUploadForm(File uploadedFile,
			AuditContext callerId) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsForm> resultMsForm = new ArrayList<>();
		List<MsForm> resultErrorMsForm = new ArrayList<>();
		List<MsForm> resultEditMsForm = new ArrayList<>();

		List<MsQuestiongroupofform> resultQGForm = new ArrayList<>();
		List<MsQuestiongroupofform> resultErrorQGForm = new ArrayList<>();
		List<MsQuestiongroupofform> resultEditQGForm = new ArrayList<>();
		List<MsQuestiongroupofform> resultErrorEditQGForm = new ArrayList<>();

		FileInputStream inputStream = new FileInputStream(uploadedFile);

		XSSFWorkbook wb = new XSSFWorkbook(inputStream);

		int numberOfSheets = wb.getNumberOfSheets();
		int sequence = 0;

		Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
				paramSubsystem);

		for (int sheetIdx = 0; sheetIdx < numberOfSheets; sheetIdx++) {
			if (sheetIdx == 2) {
				sequence = 0;
				XSSFSheet sheet = wb.getSheetAt(sheetIdx);
				int rows = sheet.getPhysicalNumberOfRows();
				for (int r = 1; r < rows; r++) {
					XSSFRow row = sheet.getRow(r);
					if (row == null) {
						continue;
					}

					boolean isEmptyRow = checkEmptyRow(row, callerId);

					if (isEmptyRow == true) {
						continue;
					}
					MsForm msForm = new MsForm();
					MsQuestiongroupofform msQuestiongroupofform = new MsQuestiongroupofform();
					msForm.setAmMssubsystem(amMsuser.getAmMssubsystem());
					sequence += 10;
					for (int c = 0; c < 8; c++) { // kolom di excel
						XSSFCell cell = row
								.getCell(c, Row.RETURN_BLANK_AS_NULL);

						String value = "";
						int intValue = -1;

						if (cell != null) {
							switch (cell.getCellType()) {
							case XSSFCell.CELL_TYPE_NUMERIC:
								value = String.valueOf((int) cell
										.getNumericCellValue());
								intValue = Integer.valueOf(value).intValue();
								break;

							case XSSFCell.CELL_TYPE_STRING:
								value = cell.getStringCellValue();
								break;

							default:
							}
						}
						switch (c) {
						case 0:
							Object[][] params = { { Restrictions.eq("formName",
									value) } };
							MsForm msf = this.getManagerDAO().selectOne(
									MsForm.class, params);
							if (null == msf) {
								msForm.setFormName(value);
								msQuestiongroupofform.setMsForm(msForm);

								if (amMsuser.getAmMssubsystem()
										.getSubsystemName()
										.equals(GlobalVal.SUBSYSTEM_MC)) {
									msForm.setIsPrintable("1");
								} 
								else {
									msForm.setIsPrintable("0");
								}

							} 
							else {
								msForm.setUuidForm(msf.getUuidForm());
								msForm.setFormName(value);
								msForm.setIsPrintable(msf.getIsPrintable());
								msQuestiongroupofform.setMsForm(msf);
							}

							break;
						case 1:
							if (!value.equals(null) || !value.equals("")) {
								Object[][] paramsCategory = { { Restrictions.eq("categoryDesc",
										value) } };
								MsFormcategory msFormcategory = this
										.getManagerDAO().selectOne(
												MsFormcategory.class,
												paramsCategory);

								if (null != msFormcategory) {
									msForm.setMsFormcategory(msFormcategory);
								}
							}
							break;
						case 2:
							msForm.setIsActive(value);
							break;
						case 3:
							Object[][] paramGLabel = { { Restrictions.eq(
									"questionGroupLabel", value) } };
							MsQuestiongroup msQgroup = this.getManagerDAO()
									.selectOne(MsQuestiongroup.class,
											paramGLabel);

							if (null != msQgroup) {
								msQuestiongroupofform
										.setMsQuestiongroup(msQgroup);
							}
							break;
						case 4:
							uploadBean.setValue(value);
							break;
						case 5:
							if (uploadBean.getValue().toString().equals("1")) {
								msQuestiongroupofform.setLineSeqOrder(intValue);
							}
							break;
						case 6:
							if (uploadBean.getValue().toString().equals("1")) {
								msQuestiongroupofform
										.setUuidQuestionGroupOfForm(Long.valueOf(value));
								Object[][] paramQGroup = { {
										Restrictions.eq("uuidQuestionGroupOfForm", Long.valueOf(value)) } };
								MsQuestiongroupofform msQOForm = this
										.getManagerDAO().selectOne(
												MsQuestiongroupofform.class,
												paramQGroup);
								msQuestiongroupofform.setMsForm(msQOForm
										.getMsForm());

								Object[][] paramForm = { { Restrictions.eq("uuidForm",
										msQOForm.getMsForm().getUuidForm()) } };
								MsForm msF = this.getManagerDAO().selectOne(
										MsForm.class, paramForm);
								msForm.setUuidForm(msF.getUuidForm());
							}
							if (!uploadBean.getValue().toString().equals("1")
									&& !uploadBean.getValue().toString()
											.equals("0")) {
								msForm.setDtmCrt(new Date());
								msForm.setUsrCrt(callerId.getCallerId()
										.toString());
								msQuestiongroupofform.setDtmCrt(new Date());
								msQuestiongroupofform.setUsrCrt(callerId
										.getCallerId().toString());
								msQuestiongroupofform.setLineSeqOrder(sequence);
								msQuestiongroupofform.setMsForm(msForm);
							}
							break;
						}
						wb.close();
					}
					if (!uploadBean.getValue().toString().equals("1")
							&& !uploadBean.getValue().toString().equals("0")) {
						
						StringBuilder errorText = checkingUploadQOForm(
								msQuestiongroupofform, uploadBean
										.getValue().toString(), callerId);
						if (errorText.length() == 0) {
							resultQGForm.add(msQuestiongroupofform);
							paramParse.put("resultQGForm", resultQGForm);
						} 
						else {
							resultErrorQGForm.add(msQuestiongroupofform);
							uploadBean.setErrorText(errorText.toString());
							paramParse.put("resultErrorQGForm",
									resultErrorQGForm);
							inputStream.close();
							return paramParse;
						}

						paramParse.put("resultForm", resultMsForm);
					}

					if (uploadBean.getValue().toString().equals("1")) {
						StringBuilder errorText = checkingUploadQOForm(
								msQuestiongroupofform, uploadBean
										.getValue().toString(), callerId);
						if (errorText.length() == 0) {
							resultEditQGForm.add(msQuestiongroupofform);
							resultEditMsForm.add(msForm);
							paramParse.put("resultEditQGForm",
									resultEditQGForm);
						} 
						else {
							uploadBean.setErrorText(errorText.toString());
							paramParse.put("resultErrorEditQGForm",
									resultErrorEditQGForm);
							paramParse.put("resultErrorQGForm",
									resultErrorQGForm);
							paramParse.put("resultErrorForm",
									resultErrorMsForm);
							inputStream.close();
							return paramParse;
						}
					}
					resultMsForm.add(msForm);
				}
				paramParse.put("resultEditQGForm", resultEditQGForm);
				paramParse.put("resultEditForm", resultEditMsForm);
				paramParse.put("resultErrorQGForm", resultErrorQGForm);
				paramParse.put("resultErrorForm", resultErrorMsForm);
				paramParse.put("resultErrorEditQGForm", resultErrorEditQGForm);
			}
		}
		return paramParse;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private String parseSpreadSheeToGetUuidForm(File uploadedFile,
			AuditContext callerId) throws IOException {
		String paramParse = "";

		FileInputStream inputStream = new FileInputStream(uploadedFile);

		XSSFWorkbook wb = new XSSFWorkbook(inputStream);

		int numberOfSheets = wb.getNumberOfSheets();
		int sequence = 0;

		Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
				paramSubsystem);

		for (int sheetIdx = 0; sheetIdx < numberOfSheets; sheetIdx++) {
			if (sheetIdx == 2) {
				sequence = 0;
				XSSFSheet sheet = wb.getSheetAt(sheetIdx);
				int rows = sheet.getPhysicalNumberOfRows();
				for (int r = 1; r < rows; r++) {
					XSSFRow row = sheet.getRow(r);
					if (row == null) {
						continue;
					}

					boolean isEmptyRow = checkEmptyRow(row, callerId);

					if (isEmptyRow == true) {
						continue;
					}
					MsForm msForm = new MsForm();
					MsQuestiongroupofform msQuestiongroupofform = new MsQuestiongroupofform();
					msForm.setAmMssubsystem(amMsuser.getAmMssubsystem());
					sequence += 10;
					for (int c = 0; c < 8; c++) { // kolom di excel
						XSSFCell cell = row
								.getCell(c, Row.RETURN_BLANK_AS_NULL);

						String value = "";
						int intValue = -1;

						if (cell != null) {
							switch (cell.getCellType()) {
							case XSSFCell.CELL_TYPE_NUMERIC:
								value = String.valueOf((int) cell
										.getNumericCellValue());
								intValue = Integer.valueOf(value).intValue();
								break;

							case XSSFCell.CELL_TYPE_STRING:
								value = cell.getStringCellValue();
								break;

							default:
							}
						}
						switch (c) {
						case 0:
							Object[][] params = { { Restrictions.eq("formName",
									value) } };
							MsForm msf = this.getManagerDAO().selectOne(
									MsForm.class, params);
							if (null == msf) {
								msForm.setFormName(value);
								msQuestiongroupofform.setMsForm(msForm);

								if (amMsuser.getAmMssubsystem()
										.getSubsystemName()
										.equals(GlobalVal.SUBSYSTEM_MC)) {
									msForm.setIsPrintable("1");
								}
								else {
									msForm.setIsPrintable("0");
								}

							} 
							else {
								msForm.setUuidForm(msf.getUuidForm());
								msForm.setFormName(value);
								msQuestiongroupofform.setMsForm(msf);
							}

							break;
						case 1:
							if (!value.equals(null) || !value.equals("")) {
								Object[][] paramsCategory = { { Restrictions.eq("categoryDesc",
										value) } };
								MsFormcategory msFormcategory = this
										.getManagerDAO().selectOne(
												MsFormcategory.class,
												paramsCategory);

								if (null != msFormcategory) {
									msForm.setMsFormcategory(msFormcategory);
								}
							}
							break;
						case 2:

							if (value.equals("")) {
								value = "0";
							}
							msForm.setIsActive(value);
							break;
						case 3:
							Object[][] paramGLabel = { { Restrictions.eq(
									"questionGroupLabel", value) } };
							MsQuestiongroup msQgroup = this.getManagerDAO()
									.selectOne(MsQuestiongroup.class,
											paramGLabel);

							if (null != msQgroup) {
								msQuestiongroupofform
										.setMsQuestiongroup(msQgroup);
							}
							break;
						case 4:
							uploadBean.setValue(value);
							break;
						case 5:
							if (uploadBean.getValue().toString().equals("1")) {
								msQuestiongroupofform.setLineSeqOrder(intValue);
							}
							break;
						case 6:
							if (uploadBean.getValue().toString().equals("1")) {
								msQuestiongroupofform
										.setUuidQuestionGroupOfForm(Long.valueOf(value));
								Object[][] paramQGroup = { {
										Restrictions.eq("uuidQuestionGroupOfForm", Long.valueOf(value)) } };
								MsQuestiongroupofform msQOForm = this
										.getManagerDAO().selectOne(
												MsQuestiongroupofform.class,
												paramQGroup);
								msQuestiongroupofform.setMsForm(msQOForm
										.getMsForm());

								Object[][] paramForm = { { Restrictions.eq("uuidForm",
										Long.valueOf(msQOForm.getMsForm().getUuidForm())) } };
								MsForm msF = this.getManagerDAO().selectOne(
										MsForm.class, paramForm);
								msForm.setUuidForm(msF.getUuidForm());
							}
							if (!uploadBean.getValue().toString().equals("1")
									&& !uploadBean.getValue().toString()
											.equals("0")) {
								msForm.setDtmCrt(new Date());
								msForm.setUsrCrt(callerId.getCallerId()
										.toString());
								msQuestiongroupofform.setDtmCrt(new Date());
								msQuestiongroupofform.setUsrCrt(callerId
										.getCallerId().toString());
								msQuestiongroupofform.setLineSeqOrder(sequence);
							}
							break;
						}
						wb.close();
					}
					paramParse = String.valueOf(msForm.getUuidForm());

				}

			}
		}
		return paramParse;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private Map parseSpreadsheetToUploadRelevant(File uploadedFile,
			AuditContext callerId) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsQuestionrelevant> resultRelevant = new ArrayList<>();
		List<MsQuestionrelevant> resultErrorRelevant = new ArrayList<>();
		List<MsQuestionrelevant> resultEditRelevant = new ArrayList<>();
		List<MsQuestionrelevant> resultErrorEditRelevant = new ArrayList<>();

		FileInputStream inputStream = new FileInputStream(uploadedFile);

		XSSFWorkbook wb = new XSSFWorkbook(inputStream);

		int numberOfSheets = wb.getNumberOfSheets();
		String teksError = "";

		for (int sheetIdx = 0; sheetIdx < numberOfSheets; sheetIdx++) {
			if (sheetIdx == 3) {
				XSSFSheet sheet = wb.getSheetAt(sheetIdx);
				int rows = sheet.getPhysicalNumberOfRows();
				for (int r = 1; r < rows; r++) {
					XSSFRow row = sheet.getRow(r);
					if (row == null) {
						continue;
					}

					boolean isEmptyRow = checkEmptyRow(row, callerId);

					if (isEmptyRow == true) {
						continue;
					}
					MsForm msForm = new MsForm();
					MsQuestionrelevant msQuestionrelevant = new MsQuestionrelevant();

					for (int c = 0; c < 12; c++) { // kolom di excel
						XSSFCell cell = row
								.getCell(c, Row.RETURN_BLANK_AS_NULL);

						String value = "";

						if (cell != null) {
							switch (cell.getCellType()) {
							case XSSFCell.CELL_TYPE_NUMERIC:
								value = String.valueOf((int) cell
										.getNumericCellValue());
								break;

							case XSSFCell.CELL_TYPE_STRING:
								value = cell.getStringCellValue();
								break;

							default:
							}
						}

						switch (c) {
						case 0:
							Object[][] formRel = { { (Restrictions.eq(
									"formName", value)) } };
							msForm = this.getManagerDAO().selectOne(
									MsForm.class, formRel);

							if (null != msForm) {
								msQuestionrelevant.setMsForm(msForm);
							}
							break;
						case 1:
							Object[][] groupRel = { { Restrictions.eq(
									"questionGroupLabel", value) } };
							MsQuestiongroup msQuestiongroup = this
									.getManagerDAO().selectOne(
											MsQuestiongroup.class, groupRel);

							if (null != msQuestiongroup) {
								msQuestionrelevant
										.setMsQuestiongroup(msQuestiongroup);
							}
							break;
						case 2:
							Object[][] questRel = { { Restrictions.eq("refId",
									value) } };
							MsQuestion msqQuestion = this.getManagerDAO()
									.selectOne(MsQuestion.class, questRel);

							if (null != msqQuestion) {
								msQuestionrelevant.setMsQuestion(msqQuestion);
							}
							break;
						case 4:
							msQuestionrelevant.setRelevant(value);
							break;
						case 5:
							msQuestionrelevant.setChoiceFilter(value);
							break;
						case 6:
							msQuestionrelevant.setCalculate(value);
							break;
						case 7:
							msQuestionrelevant.setQuestionValidation(value);
							break;
						case 8:
							msQuestionrelevant.setQuestionValue(value);
							break;
						case 9:
							msQuestionrelevant.setQuestionErrorMessage(value);
							break;
						case 10:
							msQuestionrelevant.setRelevantMandatory(value);
							break;
						case 11:
							uploadBean.setValue(value);
							break;
						case 12:
							if (uploadBean.getValue().toString().equals("1")) {
								msQuestionrelevant
										.setUuidQuestionRelevant(Long.valueOf(value));
							}
							break;
						}
						wb.close();
					}

					if (r == 1
							&& msQuestionrelevant.getMsForm() == null
							&& msQuestionrelevant.getMsQuestion() == null
							&& msQuestionrelevant.getMsQuestiongroup() == null
							&& "".equals(msQuestionrelevant.getCalculate())
							&& "".equals(msQuestionrelevant.getChoiceFilter())
							&& "".equals(msQuestionrelevant.getRelevant())
							&& "".equals(msQuestionrelevant.getQuestionErrorMessage())
							&& "".equals(msQuestionrelevant.getQuestionValidation())
							&& "".equals(msQuestionrelevant.getQuestionValue())
							&& "".equals(msQuestionrelevant.getRelevantMandatory())) {
//						break;
					} 
					else {
						if (!uploadBean.getValue().toString().equals("1")
								&& !uploadBean.getValue().toString()
										.equals("0")) {
							
							StringBuilder errorText = checkingUploadRelevant(msQuestionrelevant, callerId);

							if (teksError.length() != 0) {
								resultErrorRelevant.add(msQuestionrelevant);
								uploadBean.setErrorText(teksError);
								paramParse.put("resultErrorRelevant",
										resultErrorRelevant);
								paramParse.put("resultErrorEditRelevant",
										resultErrorEditRelevant);
								inputStream.close();
								return paramParse;
							} 
							else if (errorText.length() == 0) {
								resultRelevant.add(msQuestionrelevant);

								this.insertQRelevant(msQuestionrelevant,
										String.valueOf(msQuestionrelevant.getMsForm()
												.getUuidForm()),
												String.valueOf(msQuestionrelevant.getMsQuestion()
												.getUuidQuestion()),
												String.valueOf(msQuestionrelevant
												.getMsQuestiongroup()
												.getUuidQuestionGroup()),
										callerId);
								paramParse.put("resultRelevant",
										resultRelevant);

							} 
							else {
								resultErrorRelevant.add(msQuestionrelevant);
								uploadBean.setErrorText(errorText
										.toString());
								paramParse.put("resultErrorRelevant",
										resultErrorRelevant);
								paramParse.put("resultErrorEditRelevant",
										resultErrorEditRelevant);
								inputStream.close();
								return paramParse;
							}
						}
						if (uploadBean.getValue().toString().equals("1")) {
							
							StringBuilder errorText = checkingUploadRelevant(msQuestionrelevant, callerId);
							if (errorText.length() == 0) {
								resultEditRelevant.add(msQuestionrelevant);
								this.updateQRelevant(msQuestionrelevant,
										String.valueOf(msQuestionrelevant.getMsForm()
												.getUuidForm()),
										String.valueOf(msQuestionrelevant.getMsQuestion()
												.getUuidQuestion()),
										String.valueOf(msQuestionrelevant
												.getMsQuestiongroup()
												.getUuidQuestionGroup()),
										callerId);
							} 
							else if (teksError.length() != 0) {
								resultErrorRelevant.add(msQuestionrelevant);
								uploadBean.setErrorText(teksError);
								paramParse.put("resultErrorRelevant",
										resultErrorRelevant);
								paramParse.put("resultErrorEditRelevant",
										resultErrorEditRelevant);
								inputStream.close();
								return paramParse;
							} 
							else {
								resultErrorEditRelevant
										.add(msQuestionrelevant);
								uploadBean.setErrorText(errorText
										.toString());
								paramParse.put("resultErrorEditRelevant",
										resultErrorEditRelevant);
								paramParse.put("resultErrorRelevant",
										resultErrorRelevant);
								inputStream.close();
								return paramParse;
							}
						}
					}
				}
				paramParse.put("resultEditRelevant", resultEditRelevant);
				paramParse.put("resultErrorRelevant", resultErrorRelevant);
				paramParse.put("resultErrorEditRelevant",
						resultErrorEditRelevant);
			}
		}
		return paramParse;
	}

	private byte[] errorUpload(AuditContext callerId) {
		byte[] tmp = new byte[1];
		tmp[0] = 1;// for condition in action
		return tmp;
	}

	private boolean checkEmptyRow(XSSFRow row, AuditContext callerId) {
		String[] isEmptyCell = new String[10];
		Arrays.fill(isEmptyCell, "");
		for (int c = 0; c < 10; c++) {
			XSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
			if (cell == null) {
				isEmptyCell[c] = "empty";
			}
		}

		if ("empty".equals(isEmptyCell[0]) && "empty".equals(isEmptyCell[1])
				&& "empty".equals(isEmptyCell[2])
				&& "empty".equals(isEmptyCell[3])
				&& "empty".equals(isEmptyCell[4])
				&& "empty".equals(isEmptyCell[5])
				&& "empty".equals(isEmptyCell[6])
				&& "empty".equals(isEmptyCell[7])
				&& "empty".equals(isEmptyCell[8])
				&& "empty".equals(isEmptyCell[9])) {
			return true;
		} 
		else {
			return false;
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public StringBuilder checkingUploadQuestion(MsQuestion msQuestion,
			String edit, AuditContext callerId, String uuidForm, ArrayList listTag) {
		StringBuilder errorUpload = new StringBuilder();

		if (msQuestion.getQuestionLabel() == null
				|| ("").equals(msQuestion.getQuestionLabel())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
					this.messageSource.getMessage("businesslogic.global.mandatory", 
							new Object[]{"Question Label"}, this.retrieveLocaleAudit(callerId)));
		}

		Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser userSystem = this.getManagerDAO().selectOne(AmMsuser.class,
				paramSubsystem);

		if (GlobalVal.SUBSYSTEM_MO.equals(userSystem.getAmMssubsystem()
				.getSubsystemName())) {

			if (null != msQuestion.getMsOrdertag()) {

				Object[][] paramsTag = { { "uuidForm", uuidForm },
						{ "tagName", msQuestion.getMsOrdertag().getTagName() } };
				List checkTag = this.getManagerDAO().selectAllNative(
						"eform.form.checkTagging", paramsTag, null);

				if (listTag.contains(msQuestion.getMsOrdertag().getTagName())) {
					errorUpload.append(
						this.messageSource.getMessage("businesslogic.form.taggingcannotsame", 
								null, this.retrieveLocaleAudit(callerId)));
				} 
				else if (checkTag != null && !checkTag.isEmpty()) {
					errorUpload.append(this.messageSource.getMessage("businesslogic.form.taggingalreadyused", 
							null, this.retrieveLocaleAudit(callerId)));
				} 
				else {
					listTag.add(msQuestion.getMsOrdertag().getTagName());
				}
			}
		} 
		else if (GlobalVal.SUBSYSTEM_MC.equals(userSystem.getAmMssubsystem()
				.getSubsystemName())) {

			if (null != msQuestion.getMsCollectiontag()) {
				Object[][] paramsTag = {
						{ "uuidForm", uuidForm },
						{ "tagName",
								msQuestion.getMsCollectiontag().getTagName() } };
				List checkTag = this.getManagerDAO().selectAllNative(
						"eform.form.checkTagging", paramsTag, null);

				if (listTag.contains(msQuestion.getMsCollectiontag()
						.getTagName())) {
					errorUpload.append(
						this.messageSource.getMessage("businesslogic.form.taggingcannotsame", 
							null, this.retrieveLocaleAudit(callerId)));
				} 
				else if (checkTag != null && !checkTag.isEmpty()) {
					errorUpload.append(
						this.messageSource.getMessage("businesslogic.form.taggingalreadyused", 
								null, this.retrieveLocaleAudit(callerId)));
				} 
				else {
					listTag.add(msQuestion.getMsCollectiontag().getTagName());
				}
			}
		} 
		else {
			if ((null != msQuestion.getMsAssettag())) {

				Object[][] paramsTag = {
						{ "uuidForm", uuidForm },
						{ "tagName",
								msQuestion.getMsAssettag().getAssetTagName() } };
				List checkTag = this.getManagerDAO().selectAllNative(
						"eform.form.checkTagging", paramsTag, null);

				if (listTag.contains(msQuestion.getMsAssettag()
						.getAssetTagName())) {
					errorUpload.append(
						this.messageSource.getMessage("businesslogic.form.taggingcannotsame", 
								null, this.retrieveLocaleAudit(callerId)));
				} 
				else if (checkTag != null && !checkTag.isEmpty()) {
					errorUpload.append(
						this.messageSource.getMessage("businesslogic.form.taggingalreadyused", 
								null, this.retrieveLocaleAudit(callerId)));
				} 
				else {
					listTag.add(msQuestion.getMsAssettag().getAssetTagName());
				}
			}
		}

		if (msQuestion.getRefId() == null || ("").equals(msQuestion.getRefId())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
				this.messageSource.getMessage("businesslogic.global.mandatory", 
							new Object[]{"Identifier"}, this.retrieveLocaleAudit(callerId)));
		} 
		else {
			if (!edit.equals("1")) {
				if (20 <= msQuestion.getRefId().length()) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
							this.messageSource.getMessage("businesslogic.form.maxlength", 
									new Object[]{"Identifier","20"}, this.retrieveLocaleAudit(callerId)));
				} 
				else {
					Object[][] params = { { Restrictions.eq("refId",
							msQuestion.getRefId()) } };
					MsQuestion msqQuestion = this.getManagerDAO().selectOne(
							MsQuestion.class, params);
					if (msqQuestion != null) {
						if (!("").equals(errorUpload.toString())) {
							errorUpload.append(" | ");
						}
						errorUpload.append(
							this.messageSource.getMessage("businesslogic.global.mustunique", 
									new Object[]{"Identifier"}, this.retrieveLocaleAudit(callerId)));
					}
				}
			}
		}

		if (msQuestion.getMsAnswertype() == null) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
				this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
						new Object[]{"Answer Type"}, this.retrieveLocaleAudit(callerId)));
		} 
		else {

			if (GlobalVal.ANSWER_TYPE_IMAGE.equals(msQuestion.getMsAnswertype()
					.getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA
							.equals(msQuestion.getMsAnswertype()
									.getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS.equals(msQuestion
							.getMsAnswertype().getCodeAnswerType())) {
				if (msQuestion.getImgQlt() == null
						|| ("").equals(msQuestion.getImgQlt())) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
						this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
								new Object[]{"Image Quality"}, this.retrieveLocaleAudit(callerId)));
				}
			}

			if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(msQuestion
					.getMsAnswertype().getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION
							.equals(msQuestion.getMsAnswertype()
									.getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION
							.equals(msQuestion.getMsAnswertype()
									.getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_DROPDOWN.equals(msQuestion
							.getMsAnswertype().getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION
							.equals(msQuestion.getMsAnswertype()
									.getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_RADIO.equals(msQuestion
							.getMsAnswertype().getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION
							.equals(msQuestion.getMsAnswertype()
									.getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION
							.equals(msQuestion.getMsAnswertype()
									.getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_LU_ONLINE
							.equals(msQuestion.getMsAnswertype()
									.getCodeAnswerType())) {
				if (msQuestion.getLovGroup() == null
						|| ("").equals(msQuestion.getLovGroup())) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
						this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
								new Object[]{"Lov Group"}, this.retrieveLocaleAudit(callerId)));
				}
			}

			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(msQuestion
					.getMsAnswertype().getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_DECIMAL.equals(msQuestion
							.getMsAnswertype().getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_NUMERIC.equals(msQuestion
							.getMsAnswertype().getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION
							.equals(msQuestion.getMsAnswertype()
									.getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_TEXT.equals(msQuestion
							.getMsAnswertype().getCodeAnswerType())
					|| GlobalVal.ANSWER_TYPE_TEXT_MULTILINE.equals(msQuestion
							.getMsAnswertype().getCodeAnswerType())) {
				if (msQuestion.getMaxLength() == null
						|| ("").equals(msQuestion.getMaxLength())) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
						this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
								new Object[]{"Max Length"}, this.retrieveLocaleAudit(callerId)));
				}
			}
		}
		return errorUpload;
	}

	public StringBuilder checkingUploadRelevant(
			MsQuestionrelevant msQuestionrelevant, AuditContext callerId) {
		
		StringBuilder errorUpload = new StringBuilder();
		String[] listRefIdFromCalc = StringUtils.substringsBetween(
				msQuestionrelevant.getCalculate().toString(), "$", "$");

		if (null == msQuestionrelevant.getMsForm()) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
				this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
						new Object[]{"Form"}, this.retrieveLocaleAudit(callerId)));
		}

		if (null == msQuestionrelevant.getMsQuestiongroup()) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
				this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
						new Object[]{"Question Group"}, this.retrieveLocaleAudit(callerId)));
		}

		if (null == msQuestionrelevant.getMsQuestion()) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
				this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"Question"}, this.retrieveLocaleAudit(callerId)));
		}

		if (listRefIdFromCalc.length > 0) {
			for (int x = 0; x < listRefIdFromCalc.length; x++) {
				Object[][] paramRefIdCalc = { { Restrictions.eq("refId",
						listRefIdFromCalc[x]) } };
				MsQuestion refIdCalc = this.getManagerDAO().selectOne(
						MsQuestion.class, paramRefIdCalc);

				if (null == refIdCalc) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
							this.messageSource.getMessage("businesslogic.form.fornotexist", 
									new Object[]{"Ref ID","Calculate"}, this.retrieveLocaleAudit(callerId)));
				}
			}
		}
		
		//cek relevant
		String relevant = msQuestionrelevant.getRelevant();
		if (relevant != null && !relevant.isEmpty()) {
			List index1 = new ArrayList<>();
			List index2 = new ArrayList<>();
			for (int idx1 = relevant.indexOf("{"); idx1 >= 0; idx1 = relevant
					.indexOf("{", idx1 + 1)) {
				index1.add(idx1);
			}
			for (int idx2 = relevant.indexOf("}"); idx2 >= 0; idx2 = relevant
					.indexOf("}", idx2 + 1)) {
				index2.add(idx2);
			}
			
			if (index1.size() != index2.size()){
				if (relevant != null && !"".equals(relevant)) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
							this.messageSource.getMessage("businesslogic.global.error", 
									new Object[]{"Format Ref Id Relevant"}, this.retrieveLocaleAudit(callerId)));
				}
			}
			else{		
				if(!index1.isEmpty() && !index2.isEmpty()){
					for (int x = 0; x < index1.size(); x++) {
						Integer startIdx = (Integer) index1.get(x);
						Integer endIdx = (Integer) index2.get(x);
						if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
							if (relevant != null && !"".equals(relevant)) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append(
										this.messageSource.getMessage("businesslogic.global.error", 
												new Object[]{"Format Ref Id Relevant"}, this.retrieveLocaleAudit(callerId)));
							}
						}
						String refIdRel = relevant.substring(startIdx + 1,
								endIdx);
						String logic = relevant.substring(endIdx + 1,
								endIdx + 3);
						if (!"==".equals(logic) && !"!=".equals(logic)) {
							if (relevant != null && !"".equals(relevant)) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append(
										this.messageSource.getMessage("businesslogic.global.error", 
												new Object[]{"Format Ref Id Relevant"}, this.retrieveLocaleAudit(callerId)));
							}
						}
		
						if (!refIdRel.contains("$")) {
							Object[][] paramsRel = {
									{ Restrictions.eq("refId", refIdRel) },
									{ Restrictions.eq("isActive", "1") },
									{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
											msQuestionrelevant.getMsForm().getAmMssubsystem().getUuidMsSubsystem()) } };
							MsQuestion checkRefIdRel = this.getManagerDAO()
									.selectOne(MsQuestion.class, paramsRel);
		
							if (checkRefIdRel == null) {
								if (relevant != null && !"".equals(relevant)) {
									if (!("").equals(errorUpload.toString())) {
										errorUpload.append(" | ");
									}
									errorUpload.append(
											this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Ref ID","Relevant"}, this.retrieveLocaleAudit(callerId)));
								}
							}
						}
					}
				}
				else {
					if (relevant != null && !"".equals(relevant)) {
						if (!("").equals(errorUpload.toString())) {
							errorUpload.append(" | ");
						}
						errorUpload.append(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Relevant"}, this.retrieveLocaleAudit(callerId)));
					}
				}
			}
		}
		//end check relevant
		
		//cek choice filter
		//format
		String choiceFilter2 = msQuestionrelevant.getChoiceFilter();
		if (choiceFilter2 != null && !choiceFilter2.isEmpty()) {
			List index1 = new ArrayList<>();
			List index2 = new ArrayList<>();
			for (int idx1 = choiceFilter2.indexOf("{"); idx1 >= 0; idx1 = choiceFilter2
					.indexOf("{", idx1 + 1)) {
				index1.add(idx1);
			}
			for (int idx2 = choiceFilter2.indexOf("}"); idx2 >= 0; idx2 = choiceFilter2
					.indexOf("}", idx2 + 1)) {
				index2.add(idx2);
			}
			if (index1.size() != index2.size()) {
				if (choiceFilter2 != null && !"".equals(choiceFilter2)) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
							this.messageSource.getMessage("businesslogic.global.error", 
									new Object[]{"Format Ref Id Choice Filter"}, this.retrieveLocaleAudit(callerId)));
				}
			}
			else{
				if(!index1.isEmpty() && !index2.isEmpty()){
					for (int x = 0; x < index1.size(); x++) {
						Integer startIdx = (Integer) index1.get(x);
						Integer endIdx = (Integer) index2.get(x);
						if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
							if (choiceFilter2 != null && !"".equals(choiceFilter2)) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append(
										this.messageSource.getMessage("businesslogic.global.error", 
												new Object[]{"Format Ref Id Choice Filter"}, this.retrieveLocaleAudit(callerId)));
							}
						}
						String refIdRel = choiceFilter2.substring(startIdx + 1,
								endIdx);
		
						if (!refIdRel.contains("$")) {
							Object[][] paramsRel = {
									{ Restrictions.eq("refId", refIdRel) },
									{ Restrictions.eq("isActive", "1") },
									{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
											msQuestionrelevant.getMsForm().getAmMssubsystem().getUuidMsSubsystem()) } };
							MsQuestion checkRefIdRel = this.getManagerDAO()
									.selectOne(MsQuestion.class, paramsRel);
		
							if (checkRefIdRel == null) {
								if (choiceFilter2 != null && !"".equals(choiceFilter2)) {
									if (!("").equals(errorUpload.toString())) {
										errorUpload.append(" | ");
									}
									errorUpload.append(
											this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Ref ID","Choice Filter"}, this.retrieveLocaleAudit(callerId)));
								}
							}
						}
					}
				}
			}
		}
		//duplicat ref id
		String[] choiceFilter = msQuestionrelevant.getChoiceFilter()
				.replace(" ", StringUtils.EMPTY)
				.replace("(", StringUtils.EMPTY)
				.replace(")", StringUtils.EMPTY)
				.replaceAll("[{}]+", StringUtils.EMPTY).split(",");
		if (choiceFilter != null && choiceFilter.length > 0) {
			String current = choiceFilter[0];
			boolean found = false;
			for (int j = 1; j < choiceFilter.length; j++) {
				if (current.equals(choiceFilter[j]) && !found) {
					found = true;
					if (choiceFilter != null && choiceFilter.length > 0) {
						if (!("").equals(errorUpload.toString())) {
							errorUpload.append(" | ");
						}
						errorUpload.append(
								this.messageSource.getMessage("businesslogic.global.errorduplicate", 
										new Object[]{"Ref Id Choice Filter"}, this.retrieveLocaleAudit(callerId)));
					}
				}
			}
		}
		//end cek choice filter
		
		//cek question validation
		String questionValidation = msQuestionrelevant
				.getQuestionValidation();
		if (questionValidation != null && !questionValidation.isEmpty()) {
			if (questionValidation.indexOf("{") >= 0
					&& questionValidation.indexOf("}") >= 0) {
				List index1 = new ArrayList<>();
				List index2 = new ArrayList<>();
				List index3 = new ArrayList<>();
				List index4 = new ArrayList<>();
				List index5 = new ArrayList<>();
				for (int idx1 = questionValidation.indexOf("{"); idx1 >= 0; idx1 = questionValidation
						.indexOf("{", idx1 + 1)) {
					index1.add(idx1);
				}
				for (int idx2 = questionValidation.indexOf("}"); idx2 >= 0; idx2 = questionValidation
						.indexOf("}", idx2 + 1)) {
					index2.add(idx2);
				}

				// index of ( dan )
				for (int idx3 = questionValidation.indexOf("("); idx3 >= 0; idx3 = questionValidation
						.indexOf("(", idx3 + 1)) {
					index3.add(idx3);
				}
				for (int idx4 = questionValidation.indexOf(")"); idx4 >= 0; idx4 = questionValidation
						.indexOf(")", idx4 + 1)) {
					index4.add(idx4);
				}

				// index of |
				for (int idx5 = questionValidation.indexOf("|"); idx5 >= 0; idx5 = questionValidation
						.indexOf("|", idx5 + 1)) {
					index5.add(idx5);
				}

				if (index1.size() == index2.size()
						&& index3.size() == index4.size()
						&& index5.size() % 2 == 0) {
					for (int x = 0; x < index3.size(); x++) {
						Integer startIdx2 = (Integer) index3.get(x);
						Integer endIdx2 = (Integer) index4.get(x);
						if (startIdx2 < 0 || endIdx2 < 0
								|| endIdx2 < startIdx2) {
							if (questionValidation != null && !"".equals(questionValidation)) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append(
										this.messageSource.getMessage("businesslogic.global.error", 
												new Object[]{"Format Ref Id Question Validation"}, this.retrieveLocaleAudit(callerId)));
							}
						}
					}

					for (int x = 0; x < index1.size(); x++) {
						Integer startIdx = (Integer) index1.get(x);
						Integer endIdx = (Integer) index2.get(x);
						if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
							if (questionValidation != null && !"".equals(questionValidation)) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append(
										this.messageSource.getMessage("businesslogic.global.error", 
												new Object[]{"Format Ref Id Question Validation"}, this.retrieveLocaleAudit(callerId)));
							}
						}

						String refIdValidation = questionValidation
								.substring(startIdx + 1, endIdx);
						if (!refIdValidation.contains("$")) {
							Object[][] paramsVal = {
									{ Restrictions.eq("refId", refIdValidation) },
									{ Restrictions.eq("isActive", "1") },
									{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
											msQuestionrelevant.getMsForm().getAmMssubsystem().getUuidMsSubsystem()) } };
							MsQuestion checkRefIdVal = this.getManagerDAO()
									.selectOne(MsQuestion.class, paramsVal);

							if (checkRefIdVal == null) {
								if (questionValidation != null && !"".equals(questionValidation)) {
									if (!("").equals(errorUpload.toString())) {
										errorUpload.append(" | ");
									}
									errorUpload.append(
											this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Ref ID","question validation"}, this.retrieveLocaleAudit(callerId)));
								}
							}
						}
					}
				} 
				else {
					if (questionValidation != null && !"".equals(questionValidation)) {
						if (!("").equals(errorUpload.toString())) {
							errorUpload.append(" | ");
						}
						errorUpload.append(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Question Validation"}, this.retrieveLocaleAudit(callerId)));
					}
				}
			} 
			else {
				if (questionValidation != null && !"".equals(questionValidation)) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
							this.messageSource.getMessage("businesslogic.global.error", 
									new Object[]{"Format Question Validation"}, this.retrieveLocaleAudit(callerId)));
				}
			}
		}
		//end check question validation
		
		//cek question value
		String questionValue = msQuestionrelevant.getQuestionValue()
				.replace(" ", StringUtils.EMPTY)
				.replaceAll("[{}]+", StringUtils.EMPTY);
		int kurungBukaValue = countCharacter(msQuestionrelevant.getQuestionValue(),
				'(', callerId);
		int kurungTutupValue = countCharacter(msQuestionrelevant.getQuestionValue(),
				')', callerId);
		int kurawalBukaValue = countCharacter(msQuestionrelevant.getQuestionValue(),
				'{', callerId);
		int kurawalTutupValue = countCharacter(
				msQuestionrelevant.getQuestionValue(), '}', callerId);
		if (kurungBukaValue != kurungTutupValue || kurawalBukaValue != kurawalTutupValue) {
			if (questionValue != null && !"".equals(questionValue)) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(
						this.messageSource.getMessage("businesslogic.global.error", 
								new Object[]{"Format Question Value"}, this.retrieveLocaleAudit(callerId)));
			}
		}
		if (!StringUtils.isEmpty(questionValue)) {
			msQuestionrelevant.setQuestionValue(msQuestionrelevant.getQuestionValue().replaceAll("(?i)COPY\\(", "copy\\("));
			boolean isCopy = false;
			if (msQuestionrelevant.getQuestionValue().toUpperCase()
					.indexOf("COPY(") != 0) {
				if (msQuestionrelevant.getQuestionValue().indexOf("{") != 0
						&& msQuestionrelevant.getQuestionValue().indexOf(
								"}") != (msQuestionrelevant
								.getQuestionValue().length() - 1)) {
					if (questionValue != null && !"".equals(questionValue)) {
						if (!("").equals(errorUpload.toString())) {
							errorUpload.append(" | ");
						}
						errorUpload.append(
								this.messageSource.getMessage("businesslogic.global.error", 
										new Object[]{"Format Question Value"}, this.retrieveLocaleAudit(callerId)));
					}
				}
			} 
			else {
				isCopy = true;
			}

			if (!isCopy) {
				if (!questionValue.isEmpty()
						&& !questionValue.contains("$")) {
					Object[][] paramsQv = {
							{ Restrictions.eq("refId", questionValue) },
							{ Restrictions.eq("isActive", "1") },
							{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
									msQuestionrelevant.getMsForm().getAmMssubsystem().getUuidMsSubsystem()) } };
					MsQuestion checkRefIdQv = this.getManagerDAO()
							.selectOne(MsQuestion.class, paramsQv);

					if (checkRefIdQv == null) {
						if (questionValue != null && !"".equals(questionValue)) {
							if (!("").equals(errorUpload.toString())) {
								errorUpload.append(" | ");
							}
							errorUpload.append(
									this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Ref ID","Question Value"}, this.retrieveLocaleAudit(callerId)));
						}
					}
					else{
						//cek apakah answer type yang dicopy sama atau tidak
						if (!String.valueOf(checkRefIdQv.getMsAnswertype().getUuidAnswerType()).equals(String.valueOf(msQuestionrelevant.getMsQuestion().getMsAnswertype().getUuidAnswerType()))){
							if (questionValue != null && !"".equals(questionValue)) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append(
										this.messageSource.getMessage("businesslogic.form.answertypenotmath", 
												new Object[]{"Ref Id Question Value"}, this.retrieveLocaleAudit(callerId)));
							}
						}
					}
				}
			}
			else{
				//cek apakah argument2 dan argument3 memiliki answer type yang sama
				String[] splitQValue = msQuestionrelevant.getQuestionValue().split(",");
				if (!StringUtils.isEmpty(splitQValue[1])){
					String argument2 = splitQValue[1].replace("{", "").replace("}", "");
					Object[][] paramsArg2 = {
							{ Restrictions.eq("refId", argument2) },
							{ Restrictions.eq("isActive", "1") },
							{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
									msQuestionrelevant.getMsForm().getAmMssubsystem().getUuidMsSubsystem()) } };
					MsQuestion checkArg2 = this.getManagerDAO()
							.selectOne(MsQuestion.class, paramsArg2);
					if (checkArg2 == null) {
						if (questionValue != null && !"".equals(questionValue)) {
							if (!("").equals(errorUpload.toString())) {
								errorUpload.append(" | ");
							}
							errorUpload.append(
									this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Ref ID","Question Value"}, this.retrieveLocaleAudit(callerId)));
						}
					}
					else{
						//cek apakah answer type yang dicopy sama atau tidak
						if (!String.valueOf(checkArg2.getMsAnswertype().getUuidAnswerType()).equals(String.valueOf(msQuestionrelevant.getMsQuestion().getMsAnswertype().getUuidAnswerType()))){
							if (questionValue != null && !"".equals(questionValue)) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append(
										this.messageSource.getMessage("businesslogic.form.answertypenotmath", 
												new Object[]{"Ref Id Question Value"}, this.retrieveLocaleAudit(callerId)));
							}
						}
					}
				}
				if (!StringUtils.isEmpty(splitQValue[2])){
					String argument3 = splitQValue[2].replace("{", "").replace("}", "").replace(")", "");
					if (!StringUtils.isEmpty(argument3)){
						Object[][] paramsArg3 = {
								{ Restrictions.eq("refId", argument3) },
								{ Restrictions.eq("isActive", "1") },
								{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
										msQuestionrelevant.getMsForm().getAmMssubsystem().getUuidMsSubsystem()) } };
						MsQuestion checkArg3 = this.getManagerDAO()
								.selectOne(MsQuestion.class, paramsArg3);
						if (checkArg3 == null) {
							if (questionValue != null && !"".equals(questionValue)) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append(
										this.messageSource.getMessage("businesslogic.form.fornotexist", 
													new Object[]{"Ref ID","Question Value"}, this.retrieveLocaleAudit(callerId)));
							}
						}
						else{
							//cek apakah answer type yang dicopy sama atau tidak
							if (!String.valueOf(checkArg3.getMsAnswertype().getUuidAnswerType()).equals(String.valueOf(msQuestionrelevant.getMsQuestion().getMsAnswertype().getUuidAnswerType()))){
								if (questionValue != null && !"".equals(questionValue)) {
									if (!("").equals(errorUpload.toString())) {
										errorUpload.append(" | ");
									}
									errorUpload.append(
											this.messageSource.getMessage("businesslogic.form.answertypenotmath", 
													new Object[]{"Ref Id Question Value"}, this.retrieveLocaleAudit(callerId)));
								}
							}
						}
					}
				}
			}
		}
		//end cek question value
		
		//cek dynamic mandatory
		String relevantMandatory = msQuestionrelevant.getRelevantMandatory();
		if (relevantMandatory != null && !relevantMandatory.isEmpty()) {
			List index1 = new ArrayList<>();
			List index2 = new ArrayList<>();
			for (int idx1 = relevantMandatory.indexOf("{"); idx1 >= 0; idx1 = relevantMandatory
					.indexOf("{", idx1 + 1)) {
				index1.add(idx1);
			}
			for (int idx2 = relevantMandatory.indexOf("}"); idx2 >= 0; idx2 = relevantMandatory
					.indexOf("}", idx2 + 1)) {
				index2.add(idx2);
			}

			if (index1.size() == index2.size() && !index1.isEmpty()) {
				for (int x = 0; x < index1.size(); x++) {
					Integer startIdx = (Integer) index1.get(x);
					Integer endIdx = (Integer) index2.get(x);
					if (startIdx < 0 || endIdx < 0 || endIdx < startIdx) {
						if (relevantMandatory != null && !"".equals(relevantMandatory)) {
							if (!("").equals(errorUpload.toString())) {
								errorUpload.append(" | ");
							}
							errorUpload.append(
									this.messageSource.getMessage("businesslogic.global.error", 
											new Object[]{"Format Ref Id Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)));
						}
					}
					String refIdRel = relevantMandatory.substring(startIdx + 1,
							endIdx);
					String logic = relevantMandatory.substring(endIdx + 1,
							endIdx + 3);
					if (!"==".equals(logic) && !"!=".equals(logic)) {
						if (relevantMandatory != null && !"".equals(relevantMandatory)) {
							if (!("").equals(errorUpload.toString())) {
								errorUpload.append(" | ");
							}
							errorUpload.append(
									this.messageSource.getMessage("businesslogic.global.error", 
											new Object[]{"Format Ref Id Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)));
						}
					}

					if (!refIdRel.contains("$")) {
						Object[][] paramsRel = {
								{ Restrictions.eq("refId", refIdRel) },
								{ Restrictions.eq("isActive", "1") },
								{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
										msQuestionrelevant.getMsForm().getAmMssubsystem().getUuidMsSubsystem()) } };
						MsQuestion checkRefIdRel = this.getManagerDAO()
								.selectOne(MsQuestion.class, paramsRel);

						if (checkRefIdRel == null) {
							if (relevantMandatory != null && !"".equals(relevantMandatory)) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append(
										this.messageSource.getMessage("businesslogic.form.fornotexist", 
												new Object[]{"Ref ID","Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)));
							}
						}
					}
				}
			} 
			else {
				if (relevantMandatory != null && !"".equals(relevantMandatory)) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
							this.messageSource.getMessage("businesslogic.global.error", 
									new Object[]{"Format Dynamic Mandatory"}, this.retrieveLocaleAudit(callerId)));
				}
			}
		}
		//end cek dynamic mandatory
		

		if (msQuestionrelevant.getMsQuestion() != null
				&& msQuestionrelevant.getMsQuestiongroup() != null
				&& msQuestionrelevant.getMsForm() != null) {
			if (null == String.valueOf(msQuestionrelevant.getUuidQuestionRelevant())) {

				// if uuidRelevan null then check uuidForm,and uuidQGroup
				Object[][] rel = {
						{ Restrictions.eq("msQuestion.uuidQuestion",
								msQuestionrelevant.getMsQuestion().getUuidQuestion()) },
						{ Restrictions.eq("msQuestiongroup.uuidQuestionGroup",
								msQuestionrelevant.getMsQuestiongroup().getUuidQuestionGroup()) },
						{ Restrictions.eq("msForm.uuidForm",
								msQuestionrelevant.getMsForm().getUuidForm()) } };
				MsQuestionrelevant msqQuestionrelevant = this.getManagerDAO()
						.selectOne(MsQuestionrelevant.class, rel);
				if (null != msqQuestionrelevant) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
							this.messageSource.getMessage("service.global.existed", 
									new Object[]{"Question Relevant"}, this.retrieveLocaleAudit(callerId)));
				}
			}

		}
		return errorUpload;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public StringBuilder checkingUploadQOGroup(
			MsQuestionofgroup msQuestionofgroup, String edit, AuditContext callerId) {
		StringBuilder errorUpload = new StringBuilder();
		if (msQuestionofgroup.getMsQuestion() == null) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
					this.messageSource.getMessage("businesslogic.form.refidqueslabelnot", 
							null, this.retrieveLocaleAudit(callerId)));
		}

		if (msQuestionofgroup.getMsQuestiongroup().getQuestionGroupLabel() == null
				|| ("").equals(msQuestionofgroup.getMsQuestiongroup()
						.getQuestionGroupLabel())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
					this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
							new Object[]{"Question Label"}, this.retrieveLocaleAudit(callerId)));
		}

		if (!edit.equals("1")) {
			if (null != msQuestionofgroup.getMsQuestion()
					&& null != msQuestionofgroup.getMsQuestiongroup()) {

				Object[][] params = {
						{ Restrictions.eq("msQuestion.uuidQuestion",
								msQuestionofgroup.getMsQuestion()
										.getUuidQuestion()) },
						{ Restrictions.eq("msQuestiongroup.uuidQuestionGroup",
								msQuestionofgroup.getMsQuestiongroup()
										.getUuidQuestionGroup()) } };
				MsQuestionofgroup msqOGRoup = this.getManagerDAO().selectOne(
						MsQuestionofgroup.class, params);
				if (msqOGRoup != null) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
							this.messageSource.getMessage("service.global.existed", 
									new Object[]{"Question of Group "}, this.retrieveLocaleAudit(callerId)));
				}
			}
		}

		return errorUpload;
	}

	public StringBuilder checkingUploadQOForm(
			MsQuestiongroupofform msQuestiongroupofform, String edit, AuditContext callerId) {
		StringBuilder errorUpload = new StringBuilder();
		if (msQuestiongroupofform.getMsForm().getFormName() == null
				|| ("").equals(msQuestiongroupofform.getMsForm().getFormName())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
					this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
							new Object[]{"Form"}, this.retrieveLocaleAudit(callerId)));
		}

		if (msQuestiongroupofform.getMsQuestiongroup() == null) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
					this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
							new Object[]{"Question Group"}, this.retrieveLocaleAudit(callerId)));
		}

		if (msQuestiongroupofform.getMsForm().getMsFormcategory() == null) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(
					this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
							new Object[]{"Category"}, this.retrieveLocaleAudit(callerId)));
		}

		if (!edit.equals("1")) {
			if (msQuestiongroupofform.getMsQuestiongroup() != null
					&& msQuestiongroupofform.getMsForm() != null) {
				Object[][] params = {
						{ Restrictions.eq("msForm.uuidForm",
								msQuestiongroupofform.getMsForm().getUuidForm()) },
						{ Restrictions.eq("msQuestiongroup.uuidQuestionGroup",
								msQuestiongroupofform.getMsQuestiongroup().getUuidQuestionGroup()) } };
				MsQuestiongroupofform msqOForm = this.getManagerDAO()
						.selectOne(MsQuestiongroupofform.class, params);
				if (msqOForm != null) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(
							this.messageSource.getMessage("service.global.existed", 
									new Object[]{"Question Group of Form"}, this.retrieveLocaleAudit(callerId)));
				}
			}
		}
		return errorUpload;
	}

	private byte[] exportErrorUploadQuestion(List listQuestion,
			AuditContext callerId) throws SQLException {

		XSSFWorkbook workbook = this.createXlsTemplateErrorQuestion(
				listQuestion, callerId);

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private byte[] exportErrorUploadQOGroup(List listQOGroup, AuditContext callerId)
			throws SQLException {

		XSSFWorkbook workbook = this.createXlsTemplateErrorQOGroup(listQOGroup, callerId);

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private byte[] exportErrorUploadForm(List listForm, AuditContext callerId) throws SQLException {

		XSSFWorkbook workbook = this.createXlsTemplateErrorForm(listForm, callerId);

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private byte[] exportErrorUploadRelevant(List listRelevant, AuditContext callerId)
			throws SQLException {

		XSSFWorkbook workbook = this
				.createXlsTemplateErrorRelevant(listRelevant, callerId);

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private void setDataErrorUploadQuestion(XSSFSheet sheet1, List listQusetion, AuditContext callerId) {
		List questionList = listQusetion;

		int j = 1;
		Iterator iterator = questionList.iterator();

		Object[][] paramSubsystem = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser userSystem = this.getManagerDAO().selectOne(AmMsuser.class,
				paramSubsystem);

		while (iterator.hasNext()) {
			XSSFRow row = sheet1.createRow(j);
			MsQuestion msQuestion = (MsQuestion) iterator.next();

			XSSFCell cell = row.createCell(0);
			cell.setCellValue(msQuestion.getRefId());

			XSSFCell cell1 = row.createCell(1);
			cell1.setCellValue(msQuestion.getQuestionLabel());

			if (msQuestion.getMsAnswertype() != null) {
				XSSFCell cell2 = row.createCell(2);
				cell2.setCellValue(msQuestion.getMsAnswertype()
						.getAnswerTypeName());
			}

			XSSFCell cell3 = row.createCell(3);
			cell3.setCellValue(msQuestion.getImgQlt());

			XSSFCell cell4 = row.createCell(4);
			cell4.setCellValue(msQuestion.getLovGroup());

			if (null == msQuestion.getMaxLength()) {
				XSSFCell cell5 = row.createCell(5);
				cell5.setCellValue("");
			} 
			else {
				XSSFCell cell5 = row.createCell(5);
				cell5.setCellValue(msQuestion.getMaxLength());
			}

			if (GlobalVal.SUBSYSTEM_MS.equals(userSystem.getAmMssubsystem()
					.getSubsystemName())) {
				if (null == msQuestion.getMsAssettag()) {
					XSSFCell cell6 = row.createCell(6);
					cell6.setCellValue("");
				} 
				else {
					XSSFCell cell6 = row.createCell(6);
					cell6.setCellValue(msQuestion.getMsAssettag()
							.getAssetTagName());
				}
			} 
			else if (GlobalVal.SUBSYSTEM_MO.equals(userSystem
					.getAmMssubsystem().getSubsystemName())) {
				if (null == msQuestion.getMsOrdertag()) {
					XSSFCell cell6 = row.createCell(6);
					cell6.setCellValue("");
				} 
				else {
					XSSFCell cell6 = row.createCell(6);
					cell6.setCellValue(msQuestion.getMsOrdertag().getTagName());
				}
			}
			else {
				if (null == msQuestion.getMsCollectiontag()) {
					XSSFCell cell6 = row.createCell(6);
					cell6.setCellValue("");
				} 
				else {
					XSSFCell cell6 = row.createCell(6);
					cell6.setCellValue(msQuestion.getMsCollectiontag()
							.getTagName());
				}
			}

			XSSFCell cell7 = row.createCell(7);
			cell7.setCellValue(msQuestion.getIsMandatory());

			XSSFCell cell8 = row.createCell(8);
			cell8.setCellValue(msQuestion.getIsReadonly());

			XSSFCell cell9 = row.createCell(9);
			cell9.setCellValue(msQuestion.getIsVisible());

			XSSFCell cell10 = row.createCell(10);
			cell10.setCellValue(msQuestion.getIsActive());

			XSSFCell cell11 = row.createCell(11);
			cell11.setCellValue(msQuestion.getRegexPattern());

			XSSFCell cell12 = row.createCell(12);
			cell12.setCellValue(msQuestion.getIsHolidayAllowed());

			XSSFCell cell13 = row.createCell(13);
			cell13.setCellValue(uploadBean.getErrorText());

			j++;
		}
	}

	private XSSFWorkbook createXlsTemplateErrorQuestion(List listQuestion,
			AuditContext callerId) {

		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Question List");
			this.createHeaderErrorUploadQuestion(workbook, sheet, callerId);
			this.setDataErrorUploadQuestion(sheet, listQuestion, callerId);
		} 
		catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}

	private XSSFWorkbook createXlsTemplateErrorQOGroup(List listQOGroup, AuditContext callerId) {

		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Question Group");
			this.createHeaderErrorUploadQGroup(workbook, sheet, callerId);
			this.setDataErrorUploadQOGroup(sheet, listQOGroup, callerId);
		} 
		catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}

	private XSSFWorkbook createXlsTemplateErrorForm(List lisForm, AuditContext callerId) {

		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Form");
			this.createHeaderErrorUploadForm(workbook, sheet, callerId);
			this.setDataErrorUploadForm(sheet, lisForm, callerId);
		} 
		catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}

	private XSSFWorkbook createXlsTemplateErrorRelevant(List listRelevant, AuditContext callerId) {

		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Relevant");
			this.createHeaderErrorUploadRelevant(workbook, sheet, callerId);
			this.setDataErrorUploadRelevant(sheet, listRelevant, callerId);
		} 
		catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}

	private void setDataErrorUploadQOGroup(XSSFSheet sheet, List listQOGroup, AuditContext callerId) {

		int j = 1;
		Iterator iteratorQGroup = listQOGroup.iterator();

		while (iteratorQGroup.hasNext()) {
			XSSFRow row = sheet.createRow(j);
			MsQuestionofgroup msQOGroup = (MsQuestionofgroup) iteratorQGroup
					.next();

			if (null == msQOGroup.getMsQuestiongroup()) {
				XSSFCell cell = row.createCell(0);
				cell.setCellValue("");

				XSSFCell cell1 = row.createCell(1);
				cell1.setCellValue("");
			} 
			else {
				XSSFCell cell = row.createCell(0);
				cell.setCellValue(msQOGroup.getMsQuestiongroup()
						.getQuestionGroupLabel());

				XSSFCell cell1 = row.createCell(1);
				cell1.setCellValue(msQOGroup.getMsQuestiongroup().getIsActive());
			}

			if (null == msQOGroup.getMsQuestion()) {
				XSSFCell cell2 = row.createCell(2);
				cell2.setCellValue("");

				XSSFCell cell3 = row.createCell(3);
				cell3.setCellValue("");
			}
			else {
				XSSFCell cell2 = row.createCell(2);
				cell2.setCellValue(msQOGroup.getMsQuestion().getRefId());

				XSSFCell cell3 = row.createCell(3);
				cell3.setCellValue(msQOGroup.getMsQuestion().getQuestionLabel());
			}
			XSSFCell cell4 = row.createCell(4);
			cell4.setCellValue(uploadBean.getErrorText());
			j++;
		}
	}

	private void setDataErrorUploadForm(XSSFSheet sheet, List listForm, AuditContext callerId) {

		int j = 1;
		Iterator iterator = listForm.iterator();

		while (iterator.hasNext()) {
			XSSFRow row = sheet.createRow(j);
			MsQuestiongroupofform msOForm = (MsQuestiongroupofform) iterator
					.next();

			if (msOForm.getMsForm() != null) {
				XSSFCell cell = row.createCell(0);
				cell.setCellValue(msOForm.getMsForm().getFormName());

				if (msOForm.getMsForm().getMsFormcategory() != null) {
					XSSFCell cell1 = row.createCell(1);
					cell1.setCellValue(msOForm.getMsForm().getMsFormcategory()
							.getCategoryDesc());
				}
				XSSFCell cell2 = row.createCell(2);
				cell2.setCellValue(msOForm.getMsForm().getIsActive());
			}

			if (msOForm.getMsQuestiongroup() != null) {
				XSSFCell cell3 = row.createCell(3);
				cell3.setCellValue(msOForm.getMsQuestiongroup()
						.getQuestionGroupLabel());
			}

			XSSFCell cell4 = row.createCell(4);
			cell4.setCellValue(uploadBean.getErrorText());
			j++;
		}
	}

	private void setDataErrorUploadRelevant(XSSFSheet sheet4, List listRelevant, AuditContext callerId) {

		int j = 1;
		Iterator iterator = listRelevant.iterator();

		while (iterator.hasNext()) {
			XSSFRow row = sheet4.createRow(j);
			MsQuestionrelevant msQuestionrelevant = (MsQuestionrelevant) iterator
					.next();
			if (msQuestionrelevant.getMsForm() != null) {
				if (msQuestionrelevant.getMsForm().getFormName() != null) {
					XSSFCell cell = row.createCell(0);
					cell.setCellValue(msQuestionrelevant.getMsForm()
							.getFormName());

				}
			}
			if (msQuestionrelevant.getMsQuestiongroup() != null) {
				if (msQuestionrelevant.getMsQuestiongroup()
						.getQuestionGroupLabel() != null) {
					XSSFCell cell1 = row.createCell(1);
					cell1.setCellValue(msQuestionrelevant.getMsQuestiongroup()
							.getQuestionGroupLabel());
				}
			}
			if (msQuestionrelevant.getMsQuestion() != null) {
				if (msQuestionrelevant.getMsQuestion().getQuestionLabel() != null
						|| msQuestionrelevant.getMsQuestion().getRefId() != null) {
					XSSFCell cell2 = row.createCell(2);
					cell2.setCellValue(msQuestionrelevant.getMsQuestion()
							.getRefId());

					XSSFCell cell3 = row.createCell(3);
					cell3.setCellValue(msQuestionrelevant.getMsQuestion()
							.getQuestionLabel());
				}
			}

			XSSFCell cell4 = row.createCell(4);
			cell4.setCellValue(msQuestionrelevant.getRelevant());

			XSSFCell cell5 = row.createCell(5);
			cell5.setCellValue(msQuestionrelevant.getChoiceFilter());

			XSSFCell cell6 = row.createCell(6);
			cell6.setCellValue(msQuestionrelevant.getCalculate());

			XSSFCell cell7 = row.createCell(7);
			cell7.setCellValue(msQuestionrelevant.getQuestionValidation());

			XSSFCell cell8 = row.createCell(8);
			cell8.setCellValue(msQuestionrelevant.getQuestionValue());

			XSSFCell cell9 = row.createCell(9);
			cell9.setCellValue(msQuestionrelevant.getQuestionErrorMessage());
			
			XSSFCell cell10 = row.createCell(10);
			cell10.setCellValue(msQuestionrelevant.getRelevantMandatory());

			XSSFCell cell11 = row.createCell(11);
			cell11.setCellValue(uploadBean.getErrorText());
			j++;
		}
	}

	public String subStringRelevant(String relevant, AuditContext callerId) {
		String result = null;
		List index1 = new ArrayList<>();
		List index2 = new ArrayList<>();
		for (int idx1 = relevant.indexOf("{"); idx1 >= 0; idx1 = relevant
				.indexOf("{", idx1 + 1)) {
			index1.add(idx1);
		}
		for (int idx2 = relevant.indexOf("}"); idx2 >= 0; idx2 = relevant
				.indexOf("}", idx2 + 1)) {
			index2.add(idx2);
		}

		for (int x = 0; x < index1.size(); x++) {
			Integer startIdx = (Integer) index1.get(x);
			Integer endIdx = (Integer) index2.get(x);
			result = relevant.substring(startIdx + 1, endIdx);
		}
		return result;
	}

	public String subStringEquals(String relevant, AuditContext callerId) {
		String result = null;
		List index1 = new ArrayList<>();

		if (relevant.contains("==")) {
			for (int idx1 = relevant.indexOf("=="); idx1 >= 0; idx1 = relevant
					.indexOf("==", idx1 + 1)) {
				index1.add(idx1);
			}
		} 
		else {
			for (int idx1 = relevant.indexOf("!="); idx1 >= 0; idx1 = relevant
					.indexOf("!=", idx1 + 1)) {
				index1.add(idx1);
			}
		}

		for (int x = 0; x < index1.size(); x++) {
			Integer startIdx = (Integer) index1.get(x);
			Integer endIdx = (Integer) relevant.length();
			result = relevant.substring(startIdx, endIdx);
		}
		return result;
	}

	public List subStringOrRelevant(String relevant, AuditContext callerId) {
		String result = null;
		String rel = null;

		Integer index1 = 0;
		Integer index2 = 0;

		List relevantOr = new ArrayList<>();
		rel = relevant.replace("(", "").replace(")", "");
		List indexOR = new ArrayList<>();
		String replaceRel = null;

		if (rel.contains("OR")) {
			for (int idx1 = rel.indexOf("OR"); idx1 >= 0; idx1 = rel.indexOf(
					"OR", idx1 + 1)) {
				indexOR.add(idx1);
			}
		} 
		else {
			for (int idx1 = rel.indexOf("||"); idx1 >= 0; idx1 = rel.indexOf(
					"||", idx1 + 1)) {
				indexOR.add(idx1);
			}
		}

		for (int x = 0; x < indexOR.size(); x++) {
			if (x == 0) {
				index1 = 0;
				index2 = (Integer) indexOR.get(x);
			} 
			else {
				index1 = index2 + 1;
				index2 = (Integer) indexOR.get(x);
			}

			Integer startIdx = index1;
			Integer endIdx = index2;
			result = rel.substring(startIdx, endIdx);
			replaceRel = result.replace("OR", "").replace("||", "")
					.replace("'", "''").replace("|", "");

			if (replaceRel.contains("{") && replaceRel.contains("}")) {
				relevantOr.add(" relevant like ('%'+'" + replaceRel + "'+'%')");
			}

			if (x == (indexOR.size() - 1)) {
				index1 = (Integer) indexOR.get(x);
				index2 = rel.length();

				Integer startIndex = index1;
				Integer endIndex = index2;
				result = rel.substring(startIndex, endIndex);
				replaceRel = result.replace("OR", "").replace("||", "")
						.replace("'", "''");

				if (replaceRel.contains("{") && replaceRel.contains("}")) {
					relevantOr.add(" relevant like ('%'+'" + replaceRel
							+ "'+'%')");
				}
			}
		}
		return relevantOr;
	}

	public List subStringAndRelevant(String relevant, AuditContext callerId) {
		String result = null;
		String rel = null;

		Integer index1 = 0;
		Integer index2 = 0;

		List relevantAnd = new ArrayList<>();
		rel = relevant.replace("(", "").replace(")", "");
		List indexOR = new ArrayList<>();
		String replaceRel = null;

		if (rel.contains("AND")) {
			for (int idx1 = rel.indexOf("AND"); idx1 >= 0; idx1 = rel.indexOf(
					"AND", idx1 + 1)) {
				indexOR.add(idx1);
			}
		} 
		else {
			for (int idx1 = rel.indexOf("&&"); idx1 >= 0; idx1 = rel.indexOf(
					"&&", idx1 + 1)) {
				indexOR.add(idx1);
			}
		}

		for (int x = 0; x < indexOR.size(); x++) {
			if (x == 0) {
				index1 = 0;
				index2 = (Integer) indexOR.get(x);
			} 
			else {
				index1 = index2 + 1;
				index2 = (Integer) indexOR.get(x);
			}

			Integer startIdx = index1;
			Integer endIdx = index2;
			result = rel.substring(startIdx, endIdx);
			replaceRel = result.replace("AND", "").replace("&&", "")
					.replace("'", "''").replace("&", "");

			if (replaceRel.contains("{") && replaceRel.contains("}")) {
				relevantAnd
						.add(" relevant like ('%'+'" + replaceRel + "'+'%')");
			}

			if (x == (indexOR.size() - 1)) {
				index1 = (Integer) indexOR.get(x);
				index2 = rel.length();

				Integer startIndex = index1;
				Integer endIndex = index2;
				result = rel.substring(startIndex, endIndex);
				replaceRel = result.replace("AND", "").replace("&&", "")
						.replace("'", "''");

				if (replaceRel.contains("{") && replaceRel.contains("}")) {
					relevantAnd.add(" relevant like ('%'+'" + replaceRel
							+ "'+'%')");
				}
			}
		}
		return relevantAnd;
	}

	public String refIdToQuestionLabel(String relevant, String uuidForm, AuditContext callerId) {
		String tempLabel = null;
		String label = null;
		String lovDesc = null;

		String refIdRel = this.subStringRelevant(relevant, callerId);

		List orRel = this.subStringOrRelevant(relevant, callerId);
		List andRel = this.subStringAndRelevant(relevant, callerId);

		// ref id relevant to question label
		if (relevant.contains("||") || relevant.contains("OR")) {
			for (int x = 0; x < orRel.size(); x++) {
				String refId = this.subStringRelevant(orRel.get(x).toString(), callerId);
				Object[][] prmRefId = { { "refId", refId },
						{ "uuidForm", uuidForm } };

				List questionLabel = this.getManagerDAO().selectAllNative(
						"eform.form.checkRelevant", prmRefId, null);

				for (int j = 0; j < questionLabel.size(); j++) {
					Map labelMap = (Map) questionLabel.get(j);

					String equals = this.subStringEquals(orRel.get(x)
							.toString(), callerId);
					String lov = equals.replace(")", "").replace("'", "")
							.replace("+", "").replace("%", "")
							.replace("==", "").replace("!=", "");

					Object[][] prmLov = {
							{ "lovGroup", labelMap.get("d6").toString() },
							{ "code", lov } };
					List listLov = this.getManagerDAO().selectAllNative(
							"eform.form.lovDescription", prmLov, null);

					for (int i = 0; i < listLov.size(); i++) {
						Map lovMap = (Map) listLov.get(i);
						lovDesc = lovMap.get("d0").toString();
					}

					if (equals.contains("==")) {
						label = labelMap.get("d1").toString() + " == "
								+ lovDesc;
					}
					else {
						label = labelMap.get("d1").toString() + " != "
								+ lovDesc;
					}

					if (x == 0) {
						tempLabel = label;
					} 
					else {
						if (relevant.contains("||")) {
							tempLabel = tempLabel + " || " + label;
						} 
						else {
							tempLabel = tempLabel + " OR " + label;
						}
					}

					label = tempLabel;
				}
			}

		} 
		else if (relevant.contains("&&") || relevant.contains("AND")) {
			for (int x = 0; x < andRel.size(); x++) {
				String refId = this.subStringRelevant(andRel.get(x).toString(), callerId);
				Object[][] prmRefId = { { "refId", refId },
						{ "uuidForm", uuidForm } };

				List questionLabel = this.getManagerDAO().selectAllNative(
						"eform.form.checkRelevant", prmRefId, null);

				for (int j = 0; j < questionLabel.size(); j++) {
					Map labelMap = (Map) questionLabel.get(j);

					String equals = this.subStringEquals(andRel.get(x)
							.toString(), callerId);
					String lov = equals.replace(")", "").replace("'", "")
							.replace("+", "").replace("%", "")
							.replace("==", "").replace("!=", "");

					Object[][] prmLov = {
							{ "lovGroup", labelMap.get("d6").toString() },
							{ "code", lov } };
					List listLov = this.getManagerDAO().selectAllNative(
							"eform.form.lovDescription", prmLov, null);

					for (int i = 0; i < listLov.size(); i++) {
						Map lovMap = (Map) listLov.get(i);
						lovDesc = lovMap.get("d0").toString();
					}

					if (equals.contains("==")) {
						label = labelMap.get("d1").toString() + " == "
								+ lovDesc;
					} 
					else {
						label = labelMap.get("d1").toString() + " != "
								+ lovDesc;
					}

					if (x == 0) {
						tempLabel = label;
					} 
					else {
						if (relevant.contains("&&")) {
							tempLabel = tempLabel + " && " + label;
						} 
						else {
							tempLabel = tempLabel + " AND " + label;
						}
					}
					label = tempLabel;
				}
			}

		} 
		else {
			Object[][] prmRefId = { { "refId", refIdRel },
					{ "uuidForm", uuidForm } };

			List questionLabel = this.getManagerDAO().selectAllNative(
					"eform.form.checkRelevant", prmRefId, null);

			for (int j = 0; j < questionLabel.size(); j++) {
				Map labelMap = (Map) questionLabel.get(j);

				String equals = this.subStringEquals(relevant, callerId);
				String lov = equals.replace(")", "").replace("'", "")
						.replace("+", "").replace("%", "").replace("==", "")
						.replace("!=", "");

				Object[][] prmLov = {
						{ "lovGroup", labelMap.get("d6").toString() },
						{ "code", lov } };
				List listLov = this.getManagerDAO().selectAllNative(
						"eform.form.lovDescription", prmLov, null);

				for (int i = 0; i < listLov.size(); i++) {
					Map lovMap = (Map) listLov.get(i);
					lovDesc = lovMap.get("d0").toString();
				}

				if (equals.contains("==")) {
					label = labelMap.get("d1").toString() + " == " + lovDesc;
				} 
				else {
					label = labelMap.get("d1").toString() + " != " + lovDesc;
				}
			}
		}
		return label;
	}

	public XSSFWorkbook createXlsxTemplate(List questionList, MsForm msForm,
			String[] template, AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Form Template");
			this.createHeader(questionList, workbook, sheet, msForm, template, callerId);
			this.setTextDataFormat(workbook, sheet, questionList, template, callerId);
		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createHeader(List questionList, XSSFWorkbook workbook,
			XSSFSheet sheet, MsForm msForm, String[] template, AuditContext callerId) {
		XSSFCellStyle style = workbook.createCellStyle();
		XSSFFont font = workbook.createFont();

		XSSFRow rowForm = sheet.createRow(0);
		XSSFCell cellFormLabel = rowForm.createCell(0);
		cellFormLabel.setCellValue(GlobalVal.HEADER_FORM_NAME);
		cellFormLabel.setCellStyle(style);

		XSSFCell cellFormName = rowForm.createCell(1);
		cellFormName.setCellValue(msForm.getFormName());

		XSSFRow rowIdentifier = sheet.createRow(2);
		XSSFRow rowLabel = sheet.createRow(3);
		int idxIdentifier = 0;
		int idx = template.length;
		DataFormat format = workbook.createDataFormat();

		for (int i = 0; i < template.length + questionList.size(); i++) {

			if (i < template.length) {
				XSSFCell cell = rowLabel.createCell(i);
				cell.setCellValue(template[i]);
				XSSFCellStyle styleHeader = workbook.createCellStyle();

				style.setDataFormat(format.getFormat("@"));
				sheet.setDefaultColumnStyle(i, styleHeader);

				font.setBoldweight((short) 1000);
				style.setFont(font);

				cell.setCellStyle(style);
				sheet.setColumnWidth(i, 20 * 256);
			} 
			else {
				MsQuestion bean = (MsQuestion) questionList.get(idxIdentifier);

				if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(bean
						.getMsAnswertype().getCodeAnswerType())
						|| GlobalVal.ANSWER_TYPE_DECIMAL.equals(bean
								.getMsAnswertype().getCodeAnswerType())
						|| GlobalVal.ANSWER_TYPE_NUMERIC.equals(bean
								.getMsAnswertype().getCodeAnswerType())) {
					Object[][] paramsRelevant = { { Restrictions.eq(
							"msQuestion.uuidQuestion", bean.getUuidQuestion()) } };

					MsQuestionrelevant msQuestionrelevant = this
							.getManagerDAO().selectOne(
									MsQuestionrelevant.class, paramsRelevant);
					if (null != msQuestionrelevant) {
						if (null != msQuestionrelevant.getCalculate()
								&& !StringUtils.EMPTY.equals(msQuestionrelevant
										.getCalculate())) {
							idxIdentifier++;
							continue;
						}
					}
				}

				XSSFCell cell = rowLabel.createCell(idx);

				if ("1".equals(bean.getIsReadonly())) {
					XSSFCellStyle greyStyle = workbook.createCellStyle();
					greyStyle
							.setFillForegroundColor(IndexedColors.GREY_25_PERCENT
									.getIndex());
					greyStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
					sheet.setDefaultColumnStyle(idx, greyStyle);
				}

				XSSFCell cell2 = rowIdentifier.createCell(idx);
				cell2.setCellValue(bean.getRefId().toString());
				if ("1".equals(bean.getIsMandatory())) {
					cell.setCellValue(bean.getQuestionLabel().toString() + " *");
				}
				else {
					cell.setCellValue(bean.getQuestionLabel().toString());
				}

				font.setBoldweight((short) 1000);
				style.setDataFormat(format.getFormat("@"));
				style.setFont(font);

				cell.setCellStyle(style);
				sheet.setColumnWidth(idx, 20 * 256);
				idxIdentifier++;
				idx++;
			}
		}
	}

	private void setTextDataFormat(XSSFWorkbook workbook, XSSFSheet sheet,
			List questionList, String[] template, AuditContext callerId) {
		DataFormat format = workbook.createDataFormat();
		XSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("@"));

		int idx = 0;

		for (int i = 0; i < template.length + questionList.size(); i++) {
			if (i < template.length) {
				sheet.setDefaultColumnStyle(i, style);
			} 
			else {
				MsQuestion bean = (MsQuestion) questionList.get(idx);
				if ("1".equals(bean.getIsReadonly())) {
					XSSFCellStyle greyStyle = workbook.createCellStyle();
					greyStyle
							.setFillForegroundColor(IndexedColors.GREY_25_PERCENT
									.getIndex());
					greyStyle.setFillPattern(CellStyle.SOLID_FOREGROUND);
					sheet.setDefaultColumnStyle(i, greyStyle);
				}
				idx++;
			}
		}
	}

	// end tambahan 3+1

	@Override
	public String getIsUploadFormOn(String gsCode, AuditContext callerId) {
		String isUploadFormOn = "0";
		Object[][] params = { { Restrictions.eq("gsCode", gsCode) } };
		AmGeneralsetting result = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, params);
		if (result != null) {
			isUploadFormOn = result.getGsValue();
		}
		return isUploadFormOn;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertFormVersioning(String uuidForm, String changeLog, AuditContext callerId) {
		Object[][] versionParams = { { Restrictions.eq("msForm.uuidForm", Long.valueOf(uuidForm)) } };
		int newVersion = (int)(long) this.getManagerDAO().count(MsFormhistory.class, versionParams).get(GlobalKey.MAP_RESULT_SIZE) + 1;
		MsFormhistory formHistory = new MsFormhistory();
		formHistory.setMsForm(this.getManagerDAO().selectOne(MsForm.class, Long.valueOf(uuidForm)));
		formHistory.setFormVersion(newVersion);
		formHistory.setChangeLog(changeLog);
		formHistory.setDtmCrt(new Date());
		formHistory.setUsrCrt(callerId.getCallerId());
		this.getManagerDAO().insert(formHistory);

		String[][] formParams = { {"uuidForm", uuidForm} };
		List<Map<String,Object>> questionSetData = this.getManagerDAO().selectAllNative("eform.form.getQuestionSet", formParams, null);
		MsFormhistory formHistoryBean = this.getManagerDAO().selectOne(MsFormhistory.class, formHistory.getUuidFormHistory());
		String uuidUser = callerId.getCallerId();
		Date currentDate = new Date();
		for(int i = 0; i < questionSetData.size(); i++){
			Map qsDataMap = (Map) questionSetData.get(i);
			MsFormquestionset formQuestionSet = new MsFormquestionset();
			formQuestionSet.setMsFormhistory(formHistoryBean);
			String uuidQuestion = String.valueOf(qsDataMap.get("d0"));
			MsQuestion mq = this.getManagerDAO().selectOne(MsQuestion.class, Long.valueOf(uuidQuestion));
			formQuestionSet.setMsQuestion(mq);
			formQuestionSet.setQuestionIsActive(String.valueOf(qsDataMap.get("d1")));
			formQuestionSet.setRefId(String.valueOf(qsDataMap.get("d2")));
			formQuestionSet.setQuestionLabel(String.valueOf(qsDataMap.get("d3")));
			
			MsAnswertype answerTypeBean = this.getManagerDAO().selectOne(MsAnswertype.class, Long.valueOf(String.valueOf(qsDataMap.get("d4"))));
			formQuestionSet.setMsAnswertype(answerTypeBean);
			
			formQuestionSet.setLovGroup(String.valueOf(qsDataMap.get("d5")));
			formQuestionSet.setIsVisible(String.valueOf(qsDataMap.get("d6")));
			formQuestionSet.setIsMandatory(String.valueOf(qsDataMap.get("d7")));
			formQuestionSet.setIsReadonly(String.valueOf(qsDataMap.get("d8")));
			
			String isHollidayAllowed = String.valueOf(qsDataMap.get("d9"));
			if (!isHollidayAllowed.equals("null")){
				formQuestionSet.setIsHolidayAllowed(isHollidayAllowed);
			}
			
			String maxLength = String.valueOf(qsDataMap.get("d10"));
			if (!maxLength.equals("null")){
				formQuestionSet.setMaxLength(Integer.valueOf(maxLength));
			}
			
			formQuestionSet.setRegexPattern(String.valueOf(qsDataMap.get("d11")));
			
			String uuidAssetTag = String.valueOf(qsDataMap.get("d12"));
			if (!uuidAssetTag.equals("null")){
				MsAssettag msAssetTag = this.getManagerDAO().selectOne(MsAssettag.class, Long.valueOf(uuidAssetTag));
				formQuestionSet.setMsAssettag(msAssetTag);
			}
			
			String uuidCollectionTag = String.valueOf(qsDataMap.get("d13"));
			if (!uuidCollectionTag.equals("null")){
				MsCollectiontag msCollectionTag = this.getManagerDAO().selectOne(MsCollectiontag.class, Long.valueOf(uuidCollectionTag));
				formQuestionSet.setMsCollectiontag(msCollectionTag);
			}
			
			formQuestionSet.setAmMssubsystem(this.getManagerDAO().selectOne(AmMssubsystem.class, Long.valueOf(String.valueOf(qsDataMap.get("d14")))));
			
			String uuidOrderTag = String.valueOf(qsDataMap.get("d15"));
			if (!uuidOrderTag.equals("null")){
				MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, Long.valueOf(uuidOrderTag));
				formQuestionSet.setMsOrdertag(msOrderTag);
			}
			
			String imgQlt = String.valueOf(qsDataMap.get("d16"));
			if (!imgQlt.equals("null")){
				formQuestionSet.setImgQlt(imgQlt);
			}
			
			String uuidQuestionGroup = String.valueOf(qsDataMap.get("d17"));
			MsQuestiongroup mqg = this.getManagerDAO().selectOne(MsQuestiongroup.class, Long.valueOf(uuidQuestionGroup));
			formQuestionSet.setMsQuestiongroup(mqg);
			formQuestionSet.setQuestionGroupIsActive(String.valueOf(qsDataMap.get("d18")));
			formQuestionSet.setQuestionGroupLabel(String.valueOf(qsDataMap.get("d19")));
			formQuestionSet.setQuestionOfGroupSeq(Integer.valueOf(String.valueOf(qsDataMap.get("d20"))));
			String form = String.valueOf(qsDataMap.get("d21"));
			MsForm mf = this.getManagerDAO().selectOne(MsForm.class, Long.valueOf(form));
			formQuestionSet.setMsForm(mf);
			formQuestionSet.setFormIsActive(String.valueOf(qsDataMap.get("d22")));
			formQuestionSet.setFormName(String.valueOf(qsDataMap.get("d23")));
			String uuidFormCategory = String.valueOf(qsDataMap.get("d24"));
			
			MsFormcategory msFormCategory = this.getManagerDAO().selectOne(MsFormcategory.class, Long.valueOf(uuidFormCategory));
			formQuestionSet.setMsFormcategory(msFormCategory);
			
			formQuestionSet.setIsPrintable(String.valueOf(qsDataMap.get("d25")));
			formQuestionSet.setQuestionGroupOfFormSeq(Integer.valueOf(String.valueOf(qsDataMap.get("d26"))));
			formQuestionSet.setRelevant(String.valueOf(qsDataMap.get("d27")));
			formQuestionSet.setCalculate(String.valueOf(qsDataMap.get("d28")));
			formQuestionSet.setChoiceFilter(String.valueOf(qsDataMap.get("d29")));
			formQuestionSet.setQuestionValidation(String.valueOf(qsDataMap.get("d30")));
			formQuestionSet.setQuestionErrorMessage(String.valueOf(qsDataMap.get("d31")));
			formQuestionSet.setQuestionValue(String.valueOf(qsDataMap.get("d32")));
			formQuestionSet.setUsrCrt(uuidUser);
			formQuestionSet.setDtmCrt(currentDate);
			formQuestionSet.setRelevantMandatory(String.valueOf(qsDataMap.get("d33")));
			
			this.getManagerDAO().insert(formQuestionSet);
		}
		if(newVersion == NumberUtils.INTEGER_ONE){
//			intFormLogic.synSchema(formHistory.getMsForm().getFormName(), "ins", callerId.getCallerId());
		}else{
//			intFormLogic.synSchema(formHistory.getMsForm().getFormName(), "upd", callerId.getCallerId());
		}
	}
}
