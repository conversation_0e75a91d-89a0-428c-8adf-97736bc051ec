package com.adins.mss.businesslogic.impl.common;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.GeocodingConversionLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.AddressCoord;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.GeocodingResult;

public class GenericGeocodingConversionLogic extends BaseLogic implements GeocodingConversionLogic{
	private static final Logger LOG = LoggerFactory.getLogger(GenericGeocodingConversionLogic.class);
	
	private GeoApiContext context;
	
	 public void setContext(GeoApiContext context) {
		this.context = context;
	}
	
	@Override
	@Transactional
	public List startGeocodingConversion(AuditContext callerId) {
//		Object params[][] = {{Restrictions.isNull("latitude")}, {Restrictions.isNull("longitude")}, {Restrictions.eq("flagSource", GlobalVal.SUBSYSTEM_MC)}};
		String[][] prm = {{"gsPrm", GlobalKey.GENERALSETTING_GEO_CODE}};
		String gsValue = (String) this.getManagerDAO().selectOneNativeString("SELECT GS_VALUE FROM AM_GENERALSETTING with (nolock) WHERE GS_CODE = :gsPrm", prm);
		
		String[][] params = {{"startTime", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 00:00:00.000"},
				{"endTime", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 23:59:59.997"}};
		
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT TOP "+ (Integer.valueOf(gsValue)) +" UUID_TASK_H, CUSTOMER_ADDRESS, ISNULL(FAIL_COUNT_GEOCODING,0) FROM TR_TASK_H WITH (NOLOCK) ")
		.append("WHERE (ISNULL(FAIL_COUNT_GEOCODING, 0) < 4) AND LATITUDE IS NULL AND LONGITUDE IS NULL ")
		.append("AND SUBMIT_DATE BETWEEN :startTime AND :endTime");
		
		List result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString() , params);
//		List<TrTaskH> taskHList = (List)result.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}
	
	@SuppressWarnings("null")
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public void geocodeConversion(String uuidTrTaskH, String custAddress, int geoFail, AuditContext callerId) {
//		try { 
//			LOG.info("Start get geocode location for task : "+uuidTrTaskH);
//			GeocodingResult[] results =  GeocodingApi.geocode(context, custAddress).await();
//			BigDecimal lat = new BigDecimal(results[0].geometry.location.lat);
//			BigDecimal lng = new BigDecimal(results[0].geometry.location.lng);
//			if(lat != null && lng != null){
//				DecimalFormat format = new DecimalFormat("0.00");
//				format.setMaximumFractionDigits(7);
//				
//				Object[][] updateTaskH = {{"latitude", lat}, {"longitude", lng}, {"usrUpd", callerId.getCallerId()},{"uuidtaskH", uuidTrTaskH}};
//				this.getManagerDAO().updateNativeString("UPDATE TR_TASK_H SET LATITUDE = :latitude, LONGITUDE = :longitude, USR_UPD = :usrUpd, DTM_UPD = GETDATE() WHERE UUID_TASK_H = :uuidtaskH ", updateTaskH);
//				
//				String[][] param = { {"customerAddress", custAddress} };
//				Integer address = (Integer) this.getManagerDAO().selectOneNativeString(
//						"SELECT COUNT(1) FROM ADDRESS_COORD WITH(NOLOCK) WHERE ADDRESS = :customerAddress", param);
//									
//				if(address == 0){
//					AddressCoord addressCoord = new AddressCoord();
//					addressCoord.setAddress(custAddress);
//					addressCoord.setLatitude(lat);
//					addressCoord.setLongitude(lng);
//					addressCoord.setDtmCrt(new Date());
//					this.getManagerDAO().insert(addressCoord);
//				}
//				LOG.info("Success to convert location for task : {}, lat : [{}], lng : [{}]",uuidTrTaskH,
//						results[0].geometry.location.lat, results[0].geometry.location.lng);
//			}
//		} catch (Exception e) {
//			Integer geocode = 1;
//			if(0!=geoFail){
//				geocode = geoFail + 1;
//			}
//			//--
//			Object[][] updateTaskH = {{"geoFail", geocode}, {"usrUpd", callerId.getCallerId()},{"uuidtaskH", uuidTrTaskH}};
//			this.getManagerDAO().updateNativeString("UPDATE TR_TASK_H SET FAIL_COUNT_GEOCODING = :geoFail, USR_UPD = :usrUpd, DTM_UPD = GETDATE() WHERE UUID_TASK_H = :uuidtaskH ", updateTaskH);
//			
//			e.printStackTrace();
//			LOG.error("Failed to convert location for task "+uuidTrTaskH);
//		}
		return;
	}
	
	@SuppressWarnings("null")
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Map getLatLongGeocodeConversion(String custAddress, AuditContext callerId) {
		Map mapResult = new HashMap();
		mapResult.put("LATITUDE", null);
		mapResult.put("LONGITUDE", null);
		try { 
//				LOG.info("Start get geocode location for address : "+custAddress);
//				GeocodingResult[] results =  GeocodingApi.geocode(context, custAddress).await();
//				BigDecimal lat = new BigDecimal(results[0].geometry.location.lat);
//				BigDecimal lng = new BigDecimal(results[0].geometry.location.lng);
//				if(lat != null && lng != null){
//				mapResult.put("LATITUDE", lat);
//				mapResult.put("LONGITUDE", lng);
//				
//				String[][] param = { {"customerAddress", custAddress} };
//				Integer address = (Integer) this.getManagerDAO().selectOneNativeString(
//						"SELECT COUNT(1) FROM ADDRESS_COORD WITH(NOLOCK) WHERE ADDRESS = :customerAddress", param);
//									
//				if(address == 0){
//					AddressCoord addressCoord = new AddressCoord();
//					addressCoord.setAddress(custAddress);
//					addressCoord.setLatitude(lat);
//					addressCoord.setLongitude(lng);
//					addressCoord.setDtmCrt(new Date());
//					this.getManagerDAO().insert(addressCoord);
//				}
//				LOG.info("Success to convert location for address : {}, lat : [{}], lng : [{}]",custAddress,
//						results[0].geometry.location.lat, results[0].geometry.location.lng);
//			}
		} catch (Exception e) {
			LOG.error("Failed to convert location for address : {}, throw message : {}",custAddress,e.getMessage());
		}
		return mapResult;
	}
}
