package com.adins.mss.businesslogic.impl.common;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.AdInsException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.CellIdGeocoder;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.util.PropertiesHelper;

public class GenericGeolocationLogic extends BaseLogic implements GeolocationLogic {
    
    private static final Logger LOG = LoggerFactory.getLogger(GenericGeolocationLogic.class);
    private static final int DEFAULT_ACCURACY = 3_000;
    
    private CellIdGeocoder geocoder;
    
    public void setGeocoder(CellIdGeocoder geocoder) {
        this.geocoder = geocoder;
    }

    @Transactional(isolation=Isolation.READ_UNCOMMITTED)
    @Override
    public void geocodeCellId(List<LocationBean> locations, AuditContext auditContext)
            throws AdInsException {
//        this.geocodeCellId(locations, false, auditContext);
    }

    @Transactional(isolation=Isolation.READ_UNCOMMITTED)
    @Override
    public void geocodeCellId(List<LocationBean> locations, boolean updateTable, AuditContext auditContext)
            throws AdInsException{
        if (locations == null || locations.isEmpty())
            return;
        
        try {
            int accuracyTolerance = this.retrieveAccuracyTolerance();        
            Map<String, LocationBean> result = this.processGeocode(locations, accuracyTolerance);
            
            if (updateTable) {
                this.updateConversionFlag(result, accuracyTolerance);
            }
        }
        catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
        }
    }
    
    private boolean hasLacCellId(LocationBean location) {
        if (location == null)
            return false;
        
        if (location.getLac() == null || location.getCellid() == null)
            return false;
        
        return true;
    }
    
    private boolean hasCoordinate(LocationBean location) {
        if (location == null){
            return false;
        }
        if (location.getCoordinate() == null){
            return false;
        }
        return true;
    }
    
    private void mapLocationData(LocationBean source, LocationBean target) {
        if (source == null || target == null){
            throw new IllegalArgumentException("Location can not be null!");
        }
        
        target.setCoordinate(source.getCoordinate());
        target.setAccuracy(source.getAccuracy());
        target.setGeolocationProvider(source.getGeolocationProvider());
    }
    
    private String asMapKey(LocationBean location) {
        if (location == null){
            throw new IllegalArgumentException("Location can not be null!");
        }
        
        final char separator = ':';
        Integer[] arr =  { location.getMcc(), location.getMnc(), location.getLac(), location.getCellid() };
        return StringUtils.join(arr, separator);
    }
    
    private int retrieveAccuracyTolerance() {
        Object[][] queryParams = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_ACCURACY)} };
        AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, queryParams);
        if (result == null){
            return DEFAULT_ACCURACY;
        }
        return NumberUtils.toInt(result.getGsValue(), DEFAULT_ACCURACY);
    }
    
    private Map<String, LocationBean> processGeocode(List<LocationBean> locations, int accuracyTolerance) {
        if (locations == null){
            throw new IllegalArgumentException("Locations can not be null!");
        }
        
        Map<String, LocationBean> convertedLocations = new HashMap<>();
        for (LocationBean location : locations) {
            if (!hasLacCellId(location) || hasCoordinate(location)){
                continue;
            }
            String key = this.asMapKey(location);
            if (convertedLocations.containsKey(key)) {
                LOG.trace("{} already converted", key);
                LocationBean savedLocation = convertedLocations.get(key);
                if (hasCoordinate(savedLocation)
                        && (savedLocation.getAccuracy() <= accuracyTolerance)) {
                    this.mapLocationData(savedLocation, location);
                }
                continue;
            }
            
            int mcc = location.getMcc().intValue();
            int mnc = location.getMnc().intValue();
            int lac = location.getLac().intValue();
            int cellId = location.getCellid().intValue();
            LocationBean geocodeResult = this.geocoder.geocode(mcc, mnc, lac, cellId);
            convertedLocations.put(this.asMapKey(geocodeResult), geocodeResult);
            
            if (hasCoordinate(geocodeResult)) {
                if (geocodeResult.getAccuracy() > accuracyTolerance) {
                    LOG.info("{} geocode result:{}, acc={} > {}",
                            key, geocodeResult.getCoordinate(),
                            geocodeResult.getAccuracy(), accuracyTolerance);
                    continue;
                }
                
                this.mapLocationData(geocodeResult, location);
            }            
        }
        
        return convertedLocations;
    }
    
    private void updateConversionFlag(Map<String, LocationBean> convertedLocations, int accuracyTolerance) {
        if (convertedLocations == null || convertedLocations.isEmpty()){
            return;
        }
        for (String key : convertedLocations.keySet()) {
            LocationBean convertedLocation = convertedLocations.get(key);
            
            this.updateTableLocationHistory(convertedLocation, accuracyTolerance);
            if (!PropertiesHelper.isTaskDJson()) {
            	this.updateTableResultDetail(convertedLocation, accuracyTolerance);
            }
            this.updateTableRejectedResultDetail(convertedLocation, accuracyTolerance);
        }
    }
    
    private void updateTableLocationHistory(LocationBean convertedLocation, int accuracyTolerance) {
        if (!hasCoordinate(convertedLocation) ||
                (convertedLocation.getAccuracy() > accuracyTolerance)) {
            Object[][] params = {
                    {"mcc", convertedLocation.getMcc()}, {"mnc", convertedLocation.getMnc()},
                    {"lac", convertedLocation.getLac()}, {"cellId", convertedLocation.getCellid()},
            };
            this.getManagerDAO().updateNative(
                    "common.geolocation.updateTrackingNotFound", params);
        }
        else {
            Object[][] params = {
                    {"latitude", convertedLocation.getCoordinate().getLatitude()},
                    {"longitude", convertedLocation.getCoordinate().getLongitude()},
                    {"accuracy", convertedLocation.getAccuracy()},
                    {"provider", convertedLocation.getGeolocationProvider()},
                    {"mcc", convertedLocation.getMcc()}, {"mnc", convertedLocation.getMnc()},
                    {"lac", convertedLocation.getLac()}, {"cellId", convertedLocation.getCellid()},                        
            };
            this.getManagerDAO().updateNative(
                    "common.geolocation.updateTracking", params);
        }
    }
    
    private void updateTableResultDetail(LocationBean convertedLocation, int accuracyTolerance) {
        if (!hasCoordinate(convertedLocation) ||
                (convertedLocation.getAccuracy() > accuracyTolerance)) {
            Object[][] params = {
                    {"mcc", convertedLocation.getMcc()}, {"mnc", convertedLocation.getMnc()},
                    {"lac", convertedLocation.getLac()}, {"cellId", convertedLocation.getCellid()},
            };
            this.getManagerDAO().updateNative(
                    "common.geolocation.updateTaskDNotFound", params);
            this.getManagerDAO().updateNative(
                    "common.geolocation.updateTaskDLobNotFound", params);
        }
        else {
            Object[][] params = {
                    {"latitude", convertedLocation.getCoordinate().getLatitude()},
                    {"longitude", convertedLocation.getCoordinate().getLongitude()},
                    {"accuracy", convertedLocation.getAccuracy()},
                    {"provider", convertedLocation.getGeolocationProvider()},
                    {"mcc", convertedLocation.getMcc()}, {"mnc", convertedLocation.getMnc()},
                    {"lac", convertedLocation.getLac()}, {"cellId", convertedLocation.getCellid()},                        
            };
            this.getManagerDAO().updateNative(
                    "common.geolocation.updateTaskD", params);
            this.getManagerDAO().updateNative(
                    "common.geolocation.updateTaskDLob", params);
        }
    }
    
    private void updateTableRejectedResultDetail(LocationBean convertedLocation, int accuracyTolerance) {
        Object[][] params = {
                {"latitude", convertedLocation.getCoordinate().getLatitude()},
                {"longitude", convertedLocation.getCoordinate().getLongitude()},
                {"accuracy", convertedLocation.getAccuracy()},
                {"provider", convertedLocation.getGeolocationProvider()},
                {"mcc", convertedLocation.getMcc()}, {"mnc", convertedLocation.getMnc()},
                {"lac", convertedLocation.getLac()}, {"cellId", convertedLocation.getCellid()},                        
        };
        this.getManagerDAO().updateNative(
                "common.geolocation.updateTaskRejected", params);
    }
    
    
    @Transactional(isolation=Isolation.READ_UNCOMMITTED)
    @Override
    public void geocodeCellIdTaskLob(List<LocationBean> locations, boolean updateTable, AuditContext auditContext)
            throws AdInsException{
        if (locations == null || locations.isEmpty()){
            return;
        }
        
        try {
            int accuracyTolerance = this.retrieveAccuracyTolerance();        
            Map<String, LocationBean> result = this.processGeocode(locations, accuracyTolerance);
            
            if (updateTable) {
                this.updateConversionTaskLobFlag(result, accuracyTolerance);
            }
        }
        catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
        }
    }
    
    private void updateConversionTaskLobFlag(Map<String, LocationBean> convertedLocations, int accuracyTolerance) {
        if (convertedLocations == null || convertedLocations.isEmpty()){
            return;
        }
        for (String key : convertedLocations.keySet()) {
            LocationBean convertedLocation = convertedLocations.get(key);
            this.updateTrTaskDetailLob(convertedLocation, accuracyTolerance);
        }
    }
    
    private void updateTrTaskDetailLob(LocationBean convertedLocation, int accuracyTolerance) {
        if (!hasCoordinate(convertedLocation) ||
                (convertedLocation.getAccuracy() > accuracyTolerance)) {
            Object[][] params = {
                    {"mcc", convertedLocation.getMcc()}, {"mnc", convertedLocation.getMnc()},
                    {"lac", convertedLocation.getLac()}, {"cellId", convertedLocation.getCellid()},
            };
            this.getManagerDAO().updateNative(
                    "common.geolocation.updateTaskDetailLobNotFound", params);
        }
        else {
            Object[][] params = {
                    {"latitude", convertedLocation.getCoordinate().getLatitude()},
                    {"longitude", convertedLocation.getCoordinate().getLongitude()},
                    {"accuracy", convertedLocation.getAccuracy()},
                    {"provider", convertedLocation.getGeolocationProvider()},
                    {"mcc", convertedLocation.getMcc()}, {"mnc", convertedLocation.getMnc()},
                    {"lac", convertedLocation.getLac()}, {"cellId", convertedLocation.getCellid()},                        
            };
            this.getManagerDAO().updateNative(
                    "common.geolocation.updateTaskDetailLob", params);
            
            //penambahan pengecekan apakah subsystem MS aktif. Bila aktif, maka update data di TR_TASKSURVEYDATA
            Object[][] paramsCekMs = {{Restrictions.eq("subsystemName", GlobalVal.SUBSYSTEM_MS)}, {Restrictions.eq("isActive", "1")}};
    		AmMssubsystem cekMsSubsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, paramsCekMs);
    		if (cekMsSubsystem != null) {
    			this.getManagerDAO().updateNative(
                        "common.geolocation.updateLatLongTaskSurveyData", null);
    		}
            //end
        }
    }
}
