package com.adins.mss.businesslogic.impl.common;

import java.io.File;
import java.sql.Blob;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.GetImageLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.LazyLoadLogic;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.services.model.common.GetImageBean;
import com.google.common.io.BaseEncoding;

public class GenericGetImageLogic extends BaseLogic implements GetImageLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericGetImageLogic.class);
	
	private ImageStorageLogic imageStorageLogic; 
	
	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }
	
	private LazyLoadLogic lazyLoadLogic;
	
	public void setLazyLoadLogic(LazyLoadLogic lazyLoadLogic) {
		this.lazyLoadLogic = lazyLoadLogic;
	}

    @SuppressWarnings("rawtypes")
	@Transactional(readOnly=true)
	@Override
	public List<GetImageBean> getImg(long uuidTaskH, long uuidQuestion, AuditContext callerId){
		List<GetImageBean> image = new ArrayList<GetImageBean>();
		Object[][] params = {{"uuidTaskH", uuidTaskH}, {"uuidQuestion", uuidQuestion}};
		List x = this.getManagerDAO().selectAllNative("services.common.form.image", params, null);
		
		ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);
		
		if (x != null && !x.isEmpty()) {
			Iterator itr = x.iterator();
			while (itr.hasNext()) {
				GetImageBean bean = new GetImageBean();
				Map mp = (Map) itr.next();			
				if (ImageStorageLocation.DATABASE == saveImgLoc){
					try {
						Blob blob = (Blob) mp.get("d0");
						if (blob != null) {
							byte[] imageData = blob.getBytes(1, (int) blob.length());
		                    bean.setImage(BaseEncoding.base64().encode(imageData));
						}
					} catch (SQLException e) {
						LOG.error("Error message : {}", e.getMessage());
	                    throw new DatabaseException(DatabaseException.Reason.OTHER_DATA_ACCESS_EXCEPTION, e);
					}
				}
				else if (ImageStorageLocation.DMS == saveImgLoc) {
					boolean isFinal = false;
					
					Object uuidTaskLob = this.getManagerDAO().selectOneNativeString("select top 1 UUID_TASK_DETAIL_LOB from TR_TASKDETAILLOB with (nolock) where UUID_TASK_H = :uuidTaskH and QUESTION_ID = :uuidQuestion", params);
					if (null == uuidTaskLob) {
						uuidTaskLob = this.getManagerDAO().selectOneNativeString("select top 1 UUID_TASK_DETAIL_LOB from FINAL_TR_TASKDETAILLOB with (nolock) where UUID_TASK_H = :uuidTaskH and QUESTION_ID = :uuidQuestion", params);
						isFinal = true;
					}
					
					if (null != uuidTaskLob) {
						byte[] imageData = (isFinal) ? this.lazyLoadLogic.getImageLobFinal(uuidTaskLob+"", callerId)
								: this.lazyLoadLogic.getImageLob(uuidTaskLob+"", callerId);
						if (null != imageData) {
							bean.setImage(new String(BaseEncoding.base64().encode(imageData)));
						}
					}
					       
				}
				else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
					if (null != mp.get("d1")) {
						String path = (String) mp.get("d1");
						File file = new File(path);							
						byte[] imageData = imageStorageLogic.retrieveImageFileSystemByFile(file);
						bean.setImage(BaseEncoding.base64().encode(imageData));
					}
				}
				image.add(bean);
			}
		}
					
		return image;	
	}
}
