package com.adins.mss.businesslogic.impl.common;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.GetVerificationTaskListLogic;
import com.adins.mss.businesslogic.api.common.TaskVerificationLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.ActionBean;
import com.adins.mss.services.model.common.GetVerificationHeadBean;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.gson.Gson;

@SuppressWarnings({"unchecked","rawtypes"})
public class GenericGetVerificationTaskListLogic extends BaseLogic implements GetVerificationTaskListLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericGetVerificationTaskListLogic.class);
	
	private GeolocationLogic geocoder;
	
	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}
	
	@Autowired
	private TaskVerificationLogic taskVerificationLogic;

	public void setTaskVerificationLogic(TaskVerificationLogic taskVerificationLogic) {
		this.taskVerificationLogic = taskVerificationLogic;
	}
	
	@Transactional(readOnly=true,isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<GetVerificationHeadBean> verificationHeader(String mode, AuditContext callerId){		
		Object[][] head = { { "uuidUser", callerId.getCallerId() } };
		if ("branch".equals(mode)) {
			AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(callerId.getCallerId()));
			head = new Object[][]{ { "branchIds", user.getMsBranch().getUuidBranch() }, { "subsystemId", user.getAmMssubsystem().getUuidMsSubsystem() } };
		}
		List header = this.getManagerDAO().selectAllNative("branch".equals(mode) ? "task.getVerification.headerByBranch"
				: "task.getVerification.header", head, null);
		if (header == null || header.isEmpty()) {
			return Collections.emptyList();
		}
		
		List<GetVerificationHeadBean> listTaskList = new ArrayList<GetVerificationHeadBean>();
		Iterator itr = header.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			GetVerificationHeadBean hb = new GetVerificationHeadBean();
			hb.setUuidTaskH(mp.get("d0").toString());
			hb.setCustomerName((String)mp.get("d1"));
			hb.setCustomerPhone((String)mp.get("d2"));
			hb.setCustomerAddress((String)mp.get("d3"));
			hb.setNotes((String)mp.get("d4"));
			hb.setLatitude((String)mp.get("d5"));
			hb.setLongitude((String)mp.get("d6"));
			hb.setUuidForm(mp.get("d7").toString());
			hb.setFormLastUpdate((String)mp.get("d8"));
			hb.setApplNo((String)mp.get("d9"));
			hb.setIsVerification((String)mp.get("d10"));
			hb.setStatus((String)mp.get("d15"));
			hb.setPreprocessingSp((String)mp.get("d11"));
			hb.setDtmCrt((String)mp.get("d12"));
			hb.setIsPrintable((String)mp.get("d13"));
			hb.setTaskId((mp.get("d14") == null) ? null : mp.get("d14").toString());
			hb.setZip_code((String)mp.get("d16"));
			hb.setAssignDate((String) mp.get("d17"));
			hb.setFormVersion((String) mp.get("d18"));
			listTaskList.add(hb);
		}
		
		return listTaskList;
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<GetVerificationHeadBean> verificationDetail(long uuidTaskH, AuditContext callerId){	
		if (PropertiesHelper.isTaskDJson()) {
			return this.detailFromJson(uuidTaskH, callerId);
		}
		else {
			return this.detailFromRow(uuidTaskH, callerId);
		}
	}
	
	private List<GetVerificationHeadBean> detailFromJson(long uuidTaskH, AuditContext callerId) {
		Assert.isTrue(uuidTaskH > 0L);
		
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", uuidTaskH}});
		
		if (docDb == null || StringUtils.isBlank(docDb.getDocument())){
			return Collections.emptyList();
		}
		
		Gson gson = new Gson();
		TaskDocumentBean tdb = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
		if (tdb.getAnswers() == null || tdb.getAnswers().isEmpty()){
			return Collections.emptyList();
		}

		List<GetVerificationHeadBean> resultDetails = new ArrayList<GetVerificationHeadBean>();
		List<AnswerBean> answers = tdb.getAnswers();
		for (AnswerBean answer : answers) {
			GetVerificationHeadBean vb = new GetVerificationHeadBean();
			vb.setKey(answer.getQuestion().getLabel());
			
			String answerType = answer.getQuestion().getAnswerTypeCode();
			if (MssTool.isChoiceQuestion(answerType) && answer.getOptAnswers() != null) {
				vb.setFlag("0");
				
				for (int i = 0; i < answer.getOptAnswers().size(); i++) {
					if (i > 0) {
						try {
							GetVerificationHeadBean cloneBean = (GetVerificationHeadBean) BeanUtils.cloneBean(vb);
							cloneBean.setValue(answer.getOptAnswers().get(i).getDesc());
							resultDetails.add(cloneBean);
						} catch (IllegalAccessException | InstantiationException | InvocationTargetException
								| NoSuchMethodException e) {
							LOG.error("Error on cloning multipleAnswer", e);
						}
					}
					else {
						vb.setValue(answer.getOptAnswers().get(i).getDesc());
						resultDetails.add(vb);
					}
				}
				
				continue;
			}
			else if (MssTool.isImageQuestion(answerType) ||
					GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType)) {				
				if (MssTool.isImageQuestion(answerType)) {
					vb.setFlag("3");
				}
				else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType)) {
					vb.setFlag("2");
				}
				
				if (answer.getLobAnswer() != null && answer.getLobAnswer().isHasImage()) {
					vb.setValue(String.valueOf(answer.getLobAnswer().getId()));
				}
				
				if (GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType)) {				
					com.adins.mss.model.taskdjson.LocationBean location = answer.getLocation();
					if (null != location && null != location.getLat() && null != location.getLng()) {
						vb.setValue(StringUtils.join(location.getLat(), ",", location.getLng()));
					}
					else if (location != null) {
						LocationBean bean = new LocationBean();
						bean.setMcc(location.getMcc());
						bean.setMnc(location.getMnc());
						bean.setLac(location.getLac());
						bean.setCellid(location.getCid());
						bean = this.processLocation(bean, callerId);
						if (bean.getCoordinate() != null) {
							vb.setValue(bean.getCoordinate().getLatitude() + "," + bean.getCoordinate().getLongitude());
						}
						else {
							vb.setValue("");
						}
					}
					else {
						vb.setValue("");
					}
				}
			}
			else {
				vb.setFlag("0");
				vb.setValue(StringUtils.stripToEmpty(answer.getTxtAnswer()));
			}
			
			resultDetails.add(vb);
		}
		
		return resultDetails;
	}
	
	private List<GetVerificationHeadBean> detailFromRow(long uuidTaskH, AuditContext callerId) {
		Object[][] id = {{"uuidTaskH", uuidTaskH}};
		List details = this.getManagerDAO().selectAllNative("task.getVerification.detail", id, null);
		
		if (details == null || details.isEmpty()) {
			return Collections.emptyList();
		}
		List<GetVerificationHeadBean> verificationDetail = new ArrayList<GetVerificationHeadBean>();		
		Iterator itr = details.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			GetVerificationHeadBean vb = new GetVerificationHeadBean();
			vb.setKey((String) mp.get("d0"));
			vb.setValue((String) mp.get("d1"));
			vb.setFlag((String) mp.get("d2"));
			if ("2".equals(vb.getFlag())) { //location answerType
				String[] val = vb.getValue().split(",");
				if (val.length > 2) {
					LocationBean bean = new LocationBean();
					bean.setMcc(Integer.parseInt(val[0]));
					bean.setMnc(Integer.parseInt(val[1]));
					bean.setLac(Integer.parseInt(val[2]));
					bean.setCellid(Integer.parseInt(val[3]));
					bean = this.processLocation(bean, callerId);
					if (bean.getCoordinate() != null) {
						vb.setValue(bean.getCoordinate().getLatitude() + "," + bean.getCoordinate().getLongitude());
					}
					else {
						vb.setValue("");
					}
				}
			}
			else if ("3".equals(vb.getFlag())) {
				TrTaskdetaillob trTaskdetaillob = this.getManagerDAO().selectOne(
						"from TrTaskdetaillob lob join fetch lob.msQuestion mq join fetch mq.msAnswertype where lob.uuidTaskDetailLob = :uuidTaskDetailLob", 
						new Object[][] {{"uuidTaskDetailLob", Long.valueOf(vb.getValue())}});
				if (GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA.equals(trTaskdetaillob.getMsQuestion().getMsAnswertype().getUuidAnswerType())
						|| GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS.equals(trTaskdetaillob.getMsQuestion().getMsAnswertype().getUuidAnswerType())) {
					if ("1".equals(trTaskdetaillob.getIsGps()) &&
							null != trTaskdetaillob.getLatitude() &&
							null != trTaskdetaillob.getLongitude()) {
						vb.setValue(trTaskdetaillob.getLatitude() + "," + trTaskdetaillob.getLongitude());
					}
					else {
						LocationBean bean = new LocationBean();
						bean.setMcc(trTaskdetaillob.getMcc());
						bean.setMnc(trTaskdetaillob.getMnc());
						bean.setLac(trTaskdetaillob.getLac());
						bean.setCellid(trTaskdetaillob.getCellId());
						bean = this.processLocation(bean, callerId);
						if (bean.getCoordinate() != null) {
							vb.setValue(bean.getCoordinate().getLatitude() + "," + bean.getCoordinate().getLongitude());
						} 
						else {
							vb.setValue("");
						}
					}
				}
			}
			verificationDetail.add(vb);
		}
		
		return verificationDetail;
	}

	private LocationBean processLocation(LocationBean bean, AuditContext auditContext) {		
		List<LocationBean> listLocations = new ArrayList<LocationBean>();		
		listLocations.add(bean);
		this.geocoder.geocodeCellId(listLocations, auditContext);		
		return bean;
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<ActionBean> getListAction(long uuidTaskH, AuditContext callerId) {
		List<ActionBean> result = new ArrayList<ActionBean>();
		
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTaskH);
		AmMsuser amMsuser = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", NumberUtils.toLong(callerId.getCallerId())}});
		
		List comboWfNext = taskVerificationLogic.getWfEngineNextStep(trTaskH, amMsuser.getAmMssubsystem(), callerId);
		
		for (int i = 0; i < comboWfNext.size(); i++) {
			Map temp = (Map) comboWfNext.get(i);
			ActionBean bean = new ActionBean();
			bean.setUuidStatusTask(temp.get("uuidNextTask").toString());
			bean.setStatusCode(temp.get("statusNextTask").toString());
			bean.setStatusDesc(temp.get("descNextTask").toString());
			result.add(bean);
		}
		
		return result;
	}
}
