package com.adins.mss.businesslogic.impl.common;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.HolidayLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsHolidayD;
import com.adins.mss.model.MsHolidayH;

@SuppressWarnings("rawtypes")
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericHolidayLogic extends BaseLogic implements HolidayLogic, MessageSourceAware {
	private AuditInfo auditInfoHH;
	private AuditInfo auditInfoHD;
	private AuditInfo auditInfoB;
	
	@Autowired
	private MessageSource messageSource;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public GenericHolidayLogic() {
		String[] pkColsHH = { "uuidHolidayH" };
		String[] pkDbColsHH = { "UUID_HOLIDAY_H" };
		String[] colsHH = { "uuidHolidayH", "holidayCode", "holidayName", 
			"holidayDesc" };
		String[] dbColsHH = { "UUID_HOLIDAY_H", "HOLIDAY_CODE", "HOLIDAY_NAME", 
			"HOLIDAY_DESC" };
		this.auditInfoHH = new AuditInfo("MS_HOLIDAY_H", pkColsHH, pkDbColsHH,
			colsHH, dbColsHH);

		String[] pkColsHD = { "uuidHolidayD" };
		String[] pkDbColsHD = { "UUID_HOLIDAY_D" };
		String[] colsHD = { "uuidHolidayD", "msHolidayH.uuidHolidayH", "HDate", 
			"HDesc", "flagHoliday" };
		String[] dbColsHD = { "UUID_HOLIDAY_D", "UUID_HOLIDAY_H", "H_DATE", "H_DESC",
			"FLAG_HOLIDAY" };
		this.auditInfoHD = new AuditInfo("MS_HOLIDAY_D", pkColsHD, pkDbColsHD,
			colsHD, dbColsHD);

		String[] pkColsB = { "uuidBranch" };
		String[] pkDbColsB = { "UUID_BRANCH" };
		String[] colsB = { "uuidBranch", "msHolidayH.uuidHolidayH" };
		String[] dbColsB = { "UUID_BRANCH", "UUID_HOLIDAY_H" };
		this.auditInfoB = new AuditInfo("MS_BRANCH", pkColsB, pkDbColsB, colsB,
			dbColsB);
	}
    
	@Override
	public Map<String, Object> listHolidayScheme(Object params, 
			Object orders, int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;
		result = this.getManagerDAO().selectAll(MsHolidayH.class, params, 
			orders, pageNumber, pageSize);
		return result;
	}
	
	private boolean isRelatedToBranch(String uuidHoliday) {
		Object[][] params = { { Restrictions.eq("msHolidayH.uuidHolidayH", 
			Long.valueOf(uuidHoliday)) } };
		Map<String, Object> result = this.getManagerDAO().count(MsBranch.class, params);
		if ((Long) result.get(GlobalKey.MAP_RESULT_SIZE) > 0) {
			return true;
		}	
		else {
			return false;
		}	
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteHolidayScheme(String uuid, AuditContext callerId) {
		if ( !this.isRelatedToBranch(uuid)) {
			MsHolidayH obj = new MsHolidayH();
			obj.setUuidHolidayH(Long.valueOf(uuid));
			String[][] params = {{"uuidHolidayH", uuid}};
			this.getManagerDAO().deleteNative("setting.holiday.deleteHolidayDetail", 
				params);
			this.auditManager.auditDelete(obj, auditInfoHH, callerId.getCallerId(), "");
			this.getManagerDAO().delete(obj);
		}
		else {
			throw new DatabaseException(DatabaseException.Reason.CONSTRAINT_VIOLATION, 
				(this.messageSource.getMessage("businesslogic.error.haschild", null,
				this.retrieveLocaleAudit(callerId))), new Exception());
		}
	}

	@Override
	public MsHolidayH getHolidayScheme(String uuid, AuditContext callerId) {
		MsHolidayH result = null;
		result = this.getManagerDAO().selectOne(MsHolidayH.class, 
			Long.valueOf(uuid));
		return result;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateHolidayScheme(MsHolidayH obj, AuditContext callerId) {
		MsHolidayH dbModel = this.getManagerDAO().selectOne(MsHolidayH.class,
			obj.getUuidHolidayH());
		dbModel.setHolidayCode(obj.getHolidayCode());
		dbModel.setHolidayDesc(obj.getHolidayDesc());
		dbModel.setHolidayName(obj.getHolidayName());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());
		this.auditManager.auditEdit(dbModel, auditInfoHH, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
	}
	
	private boolean newHolidayValidity(String code) {
		boolean validity = true;
		String[][] params = { { "holidayCode", code } };
		Integer result = (Integer)this.getManagerDAO()
			.selectOneNative("setting.holiday.holidayCodeValidity", params);	
		if (result != 0) {
			validity = false;
		}	
		return validity;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void insertHolidayScheme(MsHolidayH obj, AuditContext callerId) {
		if( this.newHolidayValidity( obj.getHolidayCode() ) ) {
			obj.setDtmCrt(new Date());
			obj.setUsrCrt(callerId.getCallerId());
	
			this.getManagerDAO().insert(obj);
			this.auditManager.auditAdd(obj, auditInfoHH, callerId.getCallerId(), "");
		}
		else {
			Object[] object = { obj.getHolidayCode() };
			throw new EntityNotUniqueException((this.messageSource.getMessage(
					"businesslogic.holiday.holidaycodeexist", object, this.retrieveLocaleAudit(callerId))),
					obj.getHolidayCode());
		}
	}
	
	@Override
	public Map<String, Object> listHolidayBranch(Object params, 
			Object orders, int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;
		result = this.getManagerDAO().selectAll(MsBranch.class, params, 
			orders, pageNumber, pageSize);
		return result;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteHolidayBranch(String uuidBranch, String uuidHoliday, 
		AuditContext callerId) {
		
		MsHolidayH objH = this.getManagerDAO().selectOne(MsHolidayH.class, 
			Long.valueOf(uuidHoliday));
		objH.setDtmUpd(new Date());
		objH.setUsrUpd(callerId.getCallerId());
		
		MsBranch obj = this.getManagerDAO().selectOne(MsBranch.class, 
				Long.valueOf(uuidBranch));
		MsHolidayH mhh = null;
		obj.setMsHolidayH(mhh);
			
		this.auditManager.auditEdit(objH, auditInfoHH, callerId.getCallerId(), "");
		this.getManagerDAO().update(objH);
			
		this.auditManager.auditEdit(obj, auditInfoB, callerId.getCallerId(), "");
		this.getManagerDAO().update(obj);
	}

	@Override
	public List listHolidayBranchNull(Object params, Object orders, 
			AuditContext callerId) {
		List result = null;
		result = this.getManagerDAO()
				.selectAllNative("setting.holiday.listHolidaySelect", params, orders);
		return result;
	}
	
	@Override
	public Integer countListBranchNull(Object params, AuditContext callerId) {
		Integer result;
		result = (Integer)this.getManagerDAO().selectOneNative(
				"setting.holiday.cntListBranchNull", params);
		return result;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void saveBranchHoliday(String uuidHolidayScheme, String branchCodeSelect, 
			AuditContext callerId) {
		String kata[] = branchCodeSelect.split("\\^");
		if (!"".equals(kata[0]) && kata[0] != null) {
			MsHolidayH mhh = this.getManagerDAO().selectOne(MsHolidayH.class, 
					Long.valueOf(uuidHolidayScheme));
			mhh.setDtmUpd(new Date());
			mhh.setUsrUpd(callerId.getCallerId());
			this.getManagerDAO().update(mhh);
			for (int i = 0; i < kata.length; i++) {
				MsBranch obj = this.getManagerDAO().selectOne(MsBranch.class, 
						Long.valueOf(kata[i]));					
				obj.setMsHolidayH(mhh);
				this.auditManager.auditEdit(obj, auditInfoB, callerId.getCallerId(), "");
				this.getManagerDAO().update(obj);
			}
		}
	}
	
	@Override
	public List getTahunDb(Object params, Object orders, AuditContext callerId) {
		List result = null;
		result = this.getManagerDAO().selectAllNative("setting.holiday.listTahunDb", 
				params, orders);
		return result;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void saveOrUpdateHoliday(String uuidHolidayScheme, String dateAdd, 
			String descAdd, AuditContext callerId){
		//cek ketersediaan tanggalnya di DB
		MsHolidayD mhd = null;
		String[][] params = { {"uuidHolidayH", uuidHolidayScheme}, 
				{"dateAdd", dateAdd} };
		String uuidHolidayD = this.getManagerDAO().selectOneNative(
				"setting.holiday.holidayDateExist", params).toString();
		if (uuidHolidayD != null && !"".equals(uuidHolidayD)) {
			mhd = this.getManagerDAO().selectOne(MsHolidayD.class, 
					Long.valueOf(uuidHolidayD));
		}
		//kondisi bila tidak ada
		if (mhd == null) {
			MsHolidayD mhd2 = new MsHolidayD();
			mhd2.setDtmCrt(new Date());
			mhd2.setFlagHoliday("2");
			DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
			SimpleDateFormat formatterDay = new SimpleDateFormat("EEEE");
			Date date = null;
			try {
				date = format.parse(dateAdd);
				mhd2.setHDate(date);
			} 
			catch (ParseException e) {
				e.printStackTrace();
			}
			mhd2.setHDesc(descAdd);
			mhd2.setFlagDay(getFlagDay(formatterDay.format(date), callerId));
			MsHolidayH mhh = this.getManagerDAO().selectOne(MsHolidayH.class, Long.valueOf(uuidHolidayScheme));
			mhd2.setMsHolidayH(mhh);
			mhd2.setUsrCrt(callerId.getCallerId());
			this.getManagerDAO().insert(mhd2);
			this.auditManager.auditAdd(mhd2, auditInfoHD, callerId.getCallerId(), "");
		}
		else {
			//kondisi bila ada
			mhd.setDtmUpd(new Date());
			mhd.setFlagHoliday("2");
			mhd.setHDesc(descAdd);
			mhd.setUsrUpd(callerId.getCallerId());
			this.auditManager.auditEdit(mhd, auditInfoHD, callerId.getCallerId(), "");
			this.getManagerDAO().update(mhd);
		}
	}
	
	public String getFlagDay(String namaHari, AuditContext callerId){
		String flagDay = "";
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("SUNDAY", "0");
		map.put("MONDAY", "1");
		map.put("TUESDAY", "2");
		map.put("WEDNESDAY", "3");
		map.put("THURSDAY", "4");
		map.put("FRIDAY", "5");
		map.put("SATURDAY", "6");
		flagDay = (String) map.get(namaHari.toUpperCase());
		return flagDay;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void generateCalendar(String uuidHolidayScheme, String startYear, 
			String endYear, AuditContext callerId) {
		//cek ketersediaan tahunnya di DB
		for (int i=Integer.parseInt(startYear); i<=Integer.parseInt(endYear); i++) {
			String[][] params = { {"uuidHolidayH", uuidHolidayScheme}, 
					{"startYear", String.valueOf(i)}, {"endYear", String.valueOf(i)} };
			Integer result = (Integer) this.getManagerDAO().selectOneNative(
					"setting.holiday.yearExist", params);
			//kondisi bila tidak ada
			if (result.intValue() == 0) {
				//start generate calendar
				Calendar calStart = Calendar.getInstance();
				Calendar calEnd = Calendar.getInstance();
				calStart.setTimeInMillis(0);
				calEnd.setTimeInMillis(0);
				calStart.set(i, 0, 1, 0, 0, 0);
				calEnd.set(i, 11, 31, 00, 0, 0);
				java.util.Date curDate = calStart.getTime();
				java.util.Date endDate = calEnd.getTime();
					
				MsHolidayH mhh = this.getManagerDAO().selectOne(MsHolidayH.class, 
						Long.valueOf(uuidHolidayScheme));
				SimpleDateFormat formatterDay = new SimpleDateFormat("EEEE");
				while (!curDate.after(endDate)) {
					//insert
					MsHolidayD mhd = new MsHolidayD();
					mhd.setDtmCrt(new Date());
					mhd.setFlagDay(getFlagDay(formatterDay.format(curDate), callerId));
					mhd.setFlagHoliday("0");
					mhd.setHDate(curDate);
					mhd.setHDesc("Weekday");						
					mhd.setMsHolidayH(mhh);
					mhd.setUsrCrt(callerId.getCallerId());
					this.getManagerDAO().insert(mhd);
					this.auditManager.auditAdd(mhd, auditInfoHD, callerId.getCallerId(), "");
					calStart.add(Calendar.DAY_OF_YEAR, 1);
					curDate = calStart.getTime();
				}
				//end generate calendar				
			}
		}
	}
	
	@Override
	public String getHolidayByYear(Object params, AuditContext callerId){
		List<Map<String, Object>> result = null;
		String result2 = "";
		result = this.getManagerDAO().selectAllNative(
				"setting.holiday.getHolidayByYear", params, null);
		Iterator itr = result.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			result2 = result2 + (String) mp.get("d0") + ",";				
		}
		return result2;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateSetWeekdays(Object params, String daySelect, AuditContext callerId){
		List result = null;
		result = this.getManagerDAO().selectAllNative(
				"setting.holiday.getAllDateByYear", params, null);
		Iterator itr = result.iterator();
		String[] hari = daySelect.split(",");
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			int ada = 0;
			MsHolidayD mhd = this.getManagerDAO().selectOne(MsHolidayD.class, 
					Long.valueOf(mp.get("d0").toString()));
			for (int i = 0; i < hari.length; i++) {
				if (null != mhd.getFlagDay()) {
					if (mhd.getFlagDay().equals(hari[i])) {
						ada = 1;
					}
				}
			}
			if (ada == 1) {
				mhd.setFlagHoliday("0");
				mhd.setHDesc("Weekday");
			}
			else {
				mhd.setFlagHoliday("1");
				mhd.setHDesc("Weekend");
			}
			mhd.setDtmUpd(new Date());
			mhd.setUsrUpd(callerId.getCallerId());
			this.auditManager.auditEdit(mhd, auditInfoHD, callerId.getCallerId(), "");
			this.getManagerDAO().update(mhd);
		}
	}
	
	@Override
	public Map<String, String> getListTahunCombo(Object params, Object orders, 
			AuditContext callerId) {
		List listTahun = null;
		Map<String, String> listCombo =  new HashMap<String, String>();
		listTahun = this.getManagerDAO().selectAllNative(
				"setting.holiday.listTahunDb", params, orders);
		Iterator itr = listTahun.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			listCombo .put((String)mp.get("d0"), (String) mp.get("d0"));				
		}
		return listCombo;
	}
	
	@Override
	public List listHolidayDetail(String flagTahun, Object params, 
			AuditContext callerId) {
		List result = null;
		if ("0".equals(flagTahun)) {
			result = this.getManagerDAO().selectAllNativeString(
				"select * from ("+
					"SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (" +
						"Select UUID_HOLIDAY_D, Convert(varchar(30),h_date, 106) as h_date, " +
							"H_DESC, FLAG_HOLIDAY, FLAG_DAY , " +
							"ROW_NUMBER() OVER (ORDER BY H_DATE) AS rownum " +
						"from MS_HOLIDAY_D with (nolock) " +
						"where uuid_holiday_h = :uuidHolidayH " +
						"and (flag_holiday in (:publicHoliday) " +
						"or flag_holiday in (:isWeekend)) " +
						"and H_DESC like :searchDesc " +
					") a WHERE a.ROWNUM <= :end" +
				") b WHERE b.recnum >= :start", params);
		}
		else {
			result = this.getManagerDAO().selectAllNativeString(
				"select * from ("+
					"SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (" +
						"Select UUID_HOLIDAY_D, Convert(varchar(30),h_date, 106) as h_date, " +
							"H_DESC, FLAG_HOLIDAY, FLAG_DAY, " +
							"ROW_NUMBER() OVER (ORDER BY H_DATE) AS rownum " +
						"from MS_HOLIDAY_D with (nolock) " +
						"where uuid_holiday_h = :uuidHolidayH " +
						"and (flag_holiday in (:publicHoliday) " +
						"or flag_holiday in (:isWeekend)) " +
						"and H_DESC like :searchDesc and year(h_date) in (:yearList)" +
					") a WHERE a.ROWNUM <= :end"+
				") b WHERE b.recnum >= :start", params);
		}
		return result;
	}
	
	@Override
	public Integer countListAddCalendar(String flagTahun, Object params, 
			AuditContext callerId) {
		Integer jumlah;
		List result;
		if ("0".equals(flagTahun)) {
			result = this.getManagerDAO().selectAllNativeString(
				"Select UUID_HOLIDAY_D, Convert(varchar(30),h_date, 106) as h_date, " +
					"H_DESC, FLAG_HOLIDAY, FLAG_DAY "+
				"from MS_HOLIDAY_D with (nolock) " +
				"where uuid_holiday_h = :uuidHolidayH " +
				"and (flag_holiday in (:publicHoliday) " +
				"or flag_holiday in (:isWeekend)) and H_DESC like :searchDesc", params);
		}
		else {
			result = this.getManagerDAO().selectAllNativeString(
				"Select UUID_HOLIDAY_D, Convert(varchar(30),h_date, 106) as h_date, " +
					"H_DESC, FLAG_HOLIDAY, FLAG_DAY "+
				"from MS_HOLIDAY_D with (nolock) " +
				"where uuid_holiday_h = :uuidHolidayH and (flag_holiday in (:publicHoliday) " +
				"or flag_holiday in (:isWeekend)) and H_DESC like :searchDesc " +
				"and year(h_date) in (:yearList)", params);
		}
		jumlah = result.size();
		return jumlah;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteHolidayDetail(String uuidHolidayDetail, AuditContext callerId) {
		MsHolidayD obj = new MsHolidayD();
		obj.setUuidHolidayD(Long.valueOf(uuidHolidayDetail));
		this.auditManager.auditDelete(obj, auditInfoHD, callerId.getCallerId(), "");
		this.getManagerDAO().delete(obj);
	}
}
