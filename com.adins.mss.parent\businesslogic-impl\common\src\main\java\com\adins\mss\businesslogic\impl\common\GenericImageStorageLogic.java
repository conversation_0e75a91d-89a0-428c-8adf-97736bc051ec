package com.adins.mss.businesslogic.impl.common;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.FinalTrTaskdetaillob;
import com.adins.mss.model.FinalTrTaskrejecteddetail;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskrejecteddetail;
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericImageStorageLogic extends BaseLogic implements ImageStorageLogic {

    private static final Logger LOG = LoggerFactory.getLogger(GenericImageStorageLogic.class);
    
    @Override
    public ImageStorageLocation retrieveGsImageLocation(AuditContext auditContext) {
        Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_IMAGE_DB)} };
        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
        String gsValue = gs.getGsValue();
        return ImageStorageLocation.fromString(gsValue);
    }

    @Override
    public Path retrieveGsImageFileSystemPath(AuditContext auditContext) {
        Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_IMAGE_LOCATION)} };
        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
        String gsValue = gs.getGsValue();
        return Paths.get(gsValue);
    }

    @Override
    public byte[] retrieveRejectedImageBlob(long uuidTaskD, boolean isFinal) {
        if (isFinal) {
            FinalTrTaskrejecteddetail detailLob = this.getManagerDAO().selectOne(FinalTrTaskrejecteddetail.class, uuidTaskD);
            if (detailLob == null){
                return null;
            }
            return detailLob.getImageBlob();
        }
        else {
            TrTaskrejecteddetail detailLob = this.getManagerDAO().selectOne(TrTaskrejecteddetail.class, uuidTaskD);
            if (detailLob == null){
                return null;
            }
            return detailLob.getImageBlob();
        }
    }

    @Override
    public byte[] retrieveRejectedImageFileSystemByTaskD(long uuidTaskD, boolean isFinal) {
        String filename = null;
        
        if (isFinal) {
            FinalTrTaskrejecteddetail detailLob = this.getManagerDAO().selectOne(FinalTrTaskrejecteddetail.class, uuidTaskD);
            if (detailLob == null){
                return null;
            }
            filename = detailLob.getImagePath();
        }
        else {
            TrTaskrejecteddetail detailLob = this.getManagerDAO().selectOne(TrTaskrejecteddetail.class, uuidTaskD);
            if (detailLob == null){
                return null;
            }
            filename = detailLob.getImagePath();
        }
        
        return this.retrieveImageFileSystemByFile(new File(filename));
    }

    @Transactional(readOnly=true)
    @Override
    public byte[] retrieveImageBlob(long uuidTaskD, boolean isFinal) {
        if (isFinal) {
            FinalTrTaskdetaillob detailLob = this.getManagerDAO().selectOne(FinalTrTaskdetaillob.class, uuidTaskD);
            if (detailLob == null){
                return null;
            }
            return detailLob.getLobFile();
        }
        else {
            TrTaskdetaillob detailLob = this.getManagerDAO().selectOne(TrTaskdetaillob.class, uuidTaskD);
            if (detailLob == null){
                return null;
            }
            return detailLob.getLobFile();
        }
    }

    @Transactional(readOnly=true)
    @Override
    public byte[] retrieveImageFileSystemByTaskD(long uuidTaskD, boolean isFinal) {
        String filename = null;
        
        if (isFinal) {
            FinalTrTaskdetaillob detailLob = this.getManagerDAO().selectOne(FinalTrTaskdetaillob.class, uuidTaskD);
            if (detailLob == null){
                return null;
            }
            filename = detailLob.getImagePath();
        }
        else {
            TrTaskdetaillob detailLob = this.getManagerDAO().selectOne(TrTaskdetaillob.class, uuidTaskD);
            if (detailLob == null){
                return null;
            }
            filename = detailLob.getImagePath();
        }
        
        return this.retrieveImageFileSystemByFile(new File(filename));
    }

    @Override
    public byte[] retrieveImageFileSystemByFile(File file) {
        if (!file.exists()) {
            LOG.warn("Cannot find image file: {}", file.toString());
            return null;
        }
        
        try (FileInputStream fis = new FileInputStream(file)) { //close automatically (try with resources)
            byte[] imageBinary = IOUtils.toByteArray(fis);
            return imageBinary;
        }
        catch (FileNotFoundException e) {
            LOG.warn("Cannot find image file: {}", file.toString());
        }
        catch (IOException e) {
            LOG.error("Error on attempt reading file: {}", file.toString());
            throw new RuntimeException("Error on attempt reading file", e);
        }
        
        return null;
    }

    @Override
    public byte[] retrieveImageFileSystemByPath(Path path) {
        return this.retrieveImageFileSystemByFile(path.toFile());
    }

    @Override
    public byte[] retrieveImageDms(Map<String, Object> dmsParameters) {
        return new byte[0]; //2017-08-25 Belum ada implementasinya
    }

    @Override
    public String storeImageFileSystem(byte[] image, Path path, String fileName) {
        String absoluteFilename = Paths.get(path.toString(), fileName).toString();
        
        File saveDir = path.toFile();
        if (!saveDir.exists()) {
            saveDir.mkdirs();
        }        
        
        try (FileOutputStream fos = new FileOutputStream(absoluteFilename);
                BufferedOutputStream bos = new BufferedOutputStream(fos)) { //close automatically (try with resources)
            IOUtils.write(image, bos);
            return absoluteFilename;
        }
        catch (IOException ioe) {
            LOG.error("Error on attempt writing file: {}", absoluteFilename);
            throw new RuntimeException("Error on attempt writing file", ioe);
        }
    }

    @Override
    public Map<String, Object> storeImageDms(byte[] image, Map<String, Object> dmsParameters) {
        return Collections.emptyMap(); //2017-08-25 Belum ada implementasinya
    }

}
