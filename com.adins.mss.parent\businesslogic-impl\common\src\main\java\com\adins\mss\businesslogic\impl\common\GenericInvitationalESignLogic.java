package com.adins.mss.businesslogic.impl.common;

import java.math.BigInteger;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.AuditDataType;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.InvitationalESignLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.TblCaeData;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.services.model.common.CheckESignRegistrationResponse;
import com.adins.mss.services.model.common.CheckEsignRegistrationRequest;
import com.adins.mss.services.model.common.EsignRegistrationDataBean;
import com.adins.mss.services.model.common.GenerateInvitationRequest;
import com.adins.mss.services.model.common.InvitationalESignResponse;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericInvitationalESignLogic extends BaseLogic implements InvitationalESignLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericInvitationalESignLogic.class);
	
	private IntFormLogic intFormLogic;
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public static final String DATE_FORMAT = "yyyy-MM-dd";

	private Gson gson = new Gson();

	@Override
	public InvitationalESignResponse sentInvitationalESign(String formName, Map<String, String> request, AuditContext callerId) {
		request = this.buildMap(request);
		
		InvitationalESignResponse response = new InvitationalESignResponse();
		Status status = new Status(); 
		String jsonRequest=null;
		boolean needRegisPemohon = true;
		boolean needRegisPasangan = true;
		boolean needRegisPenjamin = true;
		
		boolean flagRegisKIPemohon = false;
		boolean flagRegisKIPasangan = false;
		boolean flagRegisKIPenjamin = false;
		
		boolean foundInTaskD = false;
		
        String taskId = request.get("$TASK_ID");
        TrTaskH trTaskH = null;
		if(null != taskId) {
			trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] {{"taskId", taskId}});
		}
		
		if(null==trTaskH) {
			status.setMessage("Task Tidak Ditemukan!");
			status.setCode(1);
			response.setStatus(status);
			return response;
		}

		BigInteger uuidTaskPreSurvey = null;
		MsGrouptask groupTask = trTaskH.getMsGrouptasks().iterator().next();
		this.getManagerDAO().fetch(trTaskH.getMsGrouptasks());
		
		if(!GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(formName)) {
			String [][] param = { {"groupTaskId", String.valueOf(groupTask.getGroupTaskId())}, 
					{"formName", GlobalVal.FORM_PRE_SURVEY}, {"statusCode", GlobalVal.SURVEY_STATUS_TASK_RELEASED} };
			StringBuilder query = new StringBuilder();
			query.append("SELECT tth.UUID_TASK_H ");
			query.append("FROM   MS_GROUPTASK msg WITH (NOLOCK) ");
			query.append("JOIN   TR_TASK_H tth WITH (NOLOCK) ON tth.UUID_TASK_H = msg.UUID_TASK_H ");
			query.append("JOIN   MS_FORM msf WITH (NOLOCK) ON tth.UUID_FORM = msf.UUID_FORM ");
			query.append("JOIN   MS_STATUSTASK mss WITH (NOLOCK) ON tth.UUID_STATUS_TASK = mss.UUID_STATUS_TASK ");
			query.append("WHERE  msg.GROUP_TASK_ID = :groupTaskId ");
			query.append("AND	 FORM_NAME = :formName ");
			query.append("AND	 STATUS_CODE = :statusCode ");
			
			uuidTaskPreSurvey = (BigInteger) this.getManagerDAO().selectOneNativeString(query.toString(), param);
		}
		
		if(null!=uuidTaskPreSurvey) {
			//ambil dari taskD;
			Object [][] param = { {"uuidTaskPreSurvey", uuidTaskPreSurvey}, {"refId", GlobalVal.REF_PRESURVEY_REG_ESIGN} };
			StringBuilder query = new StringBuilder();
			query.append("SELECT TEXT_ANSWER ");
			query.append("FROM   TR_TASK_D trtd WITH (NOLOCK) ");
			query.append("JOIN    TR_TASK_H TTH WITH (NOLOCK) ON TRTD.UUID_TASK_H = TTH.UUID_TASK_H  ");
			query.append("JOIN   MS_QUESTION msq WITH (NOLOCK) ON trtd.UUID_QUESTION = msq.UUID_QUESTION ");
			query.append("WHERE  trtd.UUID_TASK_H = :uuidTaskPreSurvey ");
			query.append("AND	 REF_ID = :refId ");
			
			
			String textAnswer = (String) this.getManagerDAO().selectOneNativeString(query.toString(), param);
			
			if(!StringUtils.isBlank(textAnswer)) {	

				String[] userStatus = textAnswer.toUpperCase().split(",");
				for(int i=0;i<userStatus.length;i++) {
					if(userStatus[i].toUpperCase().contains(GlobalVal.USER_TYPE_PEMOHON.toUpperCase())) {
						if(userStatus[i].toUpperCase().contains(GlobalVal.STATUS_ESIGN_GENERATED.toUpperCase()) || userStatus[i].toUpperCase().contains("SUDAH")) {
							needRegisPemohon = false;
							flagRegisKIPemohon = userStatus[i].toUpperCase().contains("KAWAN INTERNAL");
						}
					}
					if(userStatus[i].toUpperCase().contains(GlobalVal.USER_TYPE_PASANGAN.toUpperCase())) {
						if(userStatus[i].toUpperCase().contains(GlobalVal.STATUS_ESIGN_GENERATED.toUpperCase()) || userStatus[i].toUpperCase().contains("SUDAH")) {
							needRegisPasangan = false;
							flagRegisKIPasangan = userStatus[i].toUpperCase().contains("KAWAN INTERNAL");
						}
					}
					if(userStatus[i].toUpperCase().contains(GlobalVal.USER_TYPE_GUARANTOR.toUpperCase())) {
						if(userStatus[i].toUpperCase().contains(GlobalVal.STATUS_ESIGN_GENERATED.toUpperCase()) || userStatus[i].toUpperCase().contains("SUDAH")) {
							needRegisPenjamin = false;
							flagRegisKIPenjamin = userStatus[i].toUpperCase().contains("KAWAN INTERNAL");
						}
					}
				}
				foundInTaskD = true;
			}			
		}
		
		if((!GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(formName)&&!foundInTaskD ) || GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(formName)) {
			if (StringUtils.isNotBlank(trTaskH.getOrderNoCae())/*if found in task d take flag from table cae data*/) {
				Object[][] prmCaeData = { {Restrictions.eq("orderNoCae", trTaskH.getOrderNoCae())},
						{Restrictions.eq("isSuccess", "1")}, {Restrictions.eq("groupTaskId", Long.valueOf(groupTask.getGroupTaskId()))} };
				TblCaeData data = this.getManagerDAO().selectOne(TblCaeData.class, prmCaeData);
				jsonRequest = data.getJsonRequest();
			}
			Map<String, Object> mappingJsonRequest = gson.fromJson(jsonRequest, new TypeToken<Map<String, Object>>() {}.getType());	
			
			needRegisPemohon = !"T".equalsIgnoreCase(null == mappingJsonRequest?"F":(String) mappingJsonRequest.get(GlobalVal.SEND_LINK_PEMOHON));
			needRegisPasangan = !"T".equalsIgnoreCase(null == mappingJsonRequest?"F":(String) mappingJsonRequest.get(GlobalVal.SEND_LINK_PASANGAN));
			needRegisPenjamin = !"T".equalsIgnoreCase(null == mappingJsonRequest?"F":(String) mappingJsonRequest.get(GlobalVal.SEND_LINK_GUARANTOR));	
			
			flagRegisKIPemohon = !needRegisPemohon;
			flagRegisKIPasangan = !needRegisPasangan;
			flagRegisKIPenjamin = !needRegisPenjamin;
		}
		String statusMarriage = StringUtils.EMPTY;
		String isNeedGuarantor = StringUtils.EMPTY;
		String isSpouseSign = StringUtils.EMPTY;
		
		if (GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(formName)) {
			statusMarriage = request.get(GlobalVal.REF_PRESURVEY_STATUS_PERNIKAHAN);
			isNeedGuarantor = request.get(GlobalVal.REF_PRESURVEY_NEED_GRNTR);
			isSpouseSign = request.get(GlobalVal.REF_PRESURVEY_MELIBATKAN_PASANGAN);
		} else {
			statusMarriage = request.get(GlobalVal.STATUS_PERNIKAHAN); //perlu cek refid di tiap form
			isNeedGuarantor = (StringUtils.isBlank(request.get(GlobalVal.GUA_IS_NEED))) ? request.get(GlobalVal.REF_PRESURVEY_NEED_GRNTR) : request.get(GlobalVal.GUA_IS_NEED); //perlu cek refid di tiap form
			if("1".equalsIgnoreCase(request.get(GlobalVal.REF_PRESURVEY_MELIBATKAN_PASANGAN))) {
				isSpouseSign = request.get(GlobalVal.PSGN_TND_TGN_BAK);
			}else{
				isSpouseSign = request.get(GlobalVal.PSGN_TND_TGN);
			}
		}
		
		//penambahan paramater yang akan dilemparkan ke esign 19 
        String businessLineCode = StringUtils.EMPTY;
        
        try {
            if ((null != request.get(GlobalVal.REF_PRESURVEY_PRE_PROD_CAT)) 
                    && (!GlobalVal.FORM_COMPLETED_JASA.equals(formName) && !GlobalVal.FORM_COMPLETED_BARANG.equals(formName))) {
                businessLineCode = request.get(GlobalVal.REF_PRESURVEY_PRE_PROD_CAT);
            } else {
                businessLineCode = request.get(GlobalVal.PRODUCT_CATEGORY);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        

 
		Map <String, Map<String, String>> mapResult = new HashMap<>();
		Object[][] paramMapJson = { {"formName", formName}, {"type", GlobalVal.MAPPING_JSON_INVITATIONAL_ESIGN_TYPE} };
		List<Map<String, Object>> mapJson = this.getManagerDAO().selectAllNativeString(
				"SELECT KEY_SOURCE, KEY_DESTINATION " +
				"FROM   MS_MAPPINGJSON WITH (NOLOCK) " +
				"WHERE  FORM_NAME = :formName " +
				"       AND TYPE = :type ", paramMapJson);
		
		for (int i = 0; i < mapJson.size(); i++) {
			Map<String, Object> map = mapJson.get(i);
			
			String type = String.valueOf(map.get("d0"));
			String[] splitType = type.split("@");
			
			if ((GlobalVal.USER_TYPE_PEMOHON.equalsIgnoreCase(splitType[0]) && !needRegisPemohon)	
					|| (GlobalVal.USER_TYPE_PASANGAN.equalsIgnoreCase(splitType[0]) && (!needRegisPasangan || !"MAR".equals(statusMarriage) || ("MAR".equals(statusMarriage) && "0".equals(isSpouseSign))))				
					|| (GlobalVal.USER_TYPE_GUARANTOR.equalsIgnoreCase(splitType[0]) && (!needRegisPenjamin || !"1".equals(isNeedGuarantor)))) {
				continue;
			}
			
			String[] splitValue = StringUtils.split(splitType[1], "|");
			String resultValue = request.get(splitValue[0]);
			if (StringUtils.isBlank(resultValue)) {
				resultValue = StringUtils.EMPTY;
			}
			for (int j = 1; j < splitValue.length; j++) {
				if (StringUtils.isNotBlank(request.get(splitValue[j]))) {
					resultValue = resultValue + request.get(splitValue[j]);
				}
			}
			if (null != mapResult.get(splitType[0])) {
				Map<String, String> tempMapJson = mapResult.get(splitType[0]);
				
				tempMapJson.put(String.valueOf(map.get("d1")), resultValue);
			} else {
				Map<String, String> tempMapJson = new HashMap<>();
				tempMapJson.put(String.valueOf(map.get("d1")), resultValue);
				
				mapResult.put(splitType[0], tempMapJson);
			}
		}
		String messageResponse = StringUtils.EMPTY;
		boolean allGenerated = true;
		String SuksesMessage = GlobalVal.STATUS_ESIGN_GENERATED;
		
		if(!mapResult.isEmpty()) {
			List<Map<String, String>> result = new ArrayList<>();
			for (Map.Entry<String, Map<String, String>> entry : mapResult.entrySet()) {
				Map<String, String> mapUser = entry.getValue();
				
				String tlp = StringUtils.EMPTY;
				String email = StringUtils.EMPTY;
				for (Map.Entry<String, String> entryUser : mapUser.entrySet()) {
					if (entryUser.getKey().contains("email")) {
						email = entryUser.getValue();
					} else if (entryUser.getKey().contains("tlp")) {
						tlp = entryUser.getValue();
					}
				}
				
				if (StringUtils.isBlank(tlp) && StringUtils.isBlank(email)) {
					status.setCode(25);
					status.setMessage("Telepon atau Email dari " + entry.getKey() + " harus diisi");
					response.setStatus(status);
					return response;
				}
			}
			
			GenerateInvitationRequest invitationalRequest = new GenerateInvitationRequest();
			AuditDataType audit = new AuditDataType();
			audit.setCallerId("MOBILE");
			
			invitationalRequest.setAudit(audit);
			invitationalRequest.setBusinessLineName("");
			invitationalRequest.setBusinessLineCode("");
			
			if(StringUtils.isNotBlank(businessLineCode)) {
				//ambil BusinessLineCode
				Object[][] paramAnswerType = { {"prodCat", businessLineCode} };
				String businessLineName = String.valueOf(this.getManagerDAO().selectOneNativeString(
						"SELECT PRODUCT_CATEGORY_NAME " +
								"FROM TBL_PRODUCT_CATEGORY WITH(NOLOCK) " +
								"WHERE PRODUCT_CATEGORY_CODE = :prodCat " , paramAnswerType));
				invitationalRequest.setBusinessLineName(businessLineName);
				invitationalRequest.setBusinessLineCode(businessLineCode);
			}       
			
			if(null != taskId) {
				//ambil officeCode,OfficeName,applno,regionCode,REgionName
				Object[][] paramAnswerType2 = { {"uuidTaskH", taskId} };
				List<Map<String, Object>> esign_additional = this.getManagerDAO().selectAllNativeString(
						"SELECT TTH.APPL_NO,MB.BRANCH_NAME,MB.BRANCH_CODE,MR.REGION_NAME,MR.REGION_CODE " +
								"FROM TR_TASK_H TTH WITH(NOLOCK) " +
								"JOIN MS_BRANCH MB WITH(NOLOCK) ON TTH.UUID_BRANCH = MB.UUID_BRANCH " +
								"JOIN MS_REGION MR WITH(NOLOCK) ON MB.UUID_REGION = MR.UUID_REGION " +
								"WHERE TTH.UUID_TASK_H = :uuidTaskH " , paramAnswerType2);
				
				Map<String,Object> list1 = esign_additional.get(0);
				invitationalRequest.setReferenceNo(String.valueOf(list1.get("d0")));
				invitationalRequest.setOfficeName(String.valueOf(list1.get("d1")));
				invitationalRequest.setOfficeCode(String.valueOf(list1.get("d2")));
				invitationalRequest.setRegionName(String.valueOf(list1.get("d3")));
				invitationalRequest.setRegionCode(String.valueOf(list1.get("d4")));
			}
			
			for (Map.Entry<String, Map<String, String>> entry : mapResult.entrySet()) {
				Map<String, String> mapUser = entry.getValue();
				List<Map<String, String>> user = new ArrayList<>();
				String message = StringUtils.EMPTY;
				String userType = entry.getKey();
				user.add(mapUser);
				invitationalRequest.setUsers(user);
				try {
					MssResponseType responseFromESign = intFormLogic.GenerateInvitationLink(invitationalRequest);
					if (0 != responseFromESign.getStatus().getCode()) {
						status.setCode(responseFromESign.getStatus().getCode());
						String resMessage = StringUtils.isBlank(responseFromESign.getStatus().getMessage())
								? StringUtils.EMPTY
								: responseFromESign.getStatus().getMessage();
						
						resMessage = resMessage.replace('*', 'X');
						allGenerated = false;
						message = userType + " Failed: " + resMessage + ", ";
					} else {
						if("Success".equalsIgnoreCase(responseFromESign.getStatus().getMessage())) {
							message = userType + " " + GlobalVal.STATUS_ESIGN_GENERATED + " Generate Link, ";														
						}else {
							message = userType +  " " + GlobalVal.STATUS_ESIGN_GENERATED + " " +responseFromESign.getStatus().getMessage()+", ";	
						}
					}			
				} catch (Exception e) {
					allGenerated = false;
					message = userType + " Failed: " + e.getMessage()  + ", ";
				}
				messageResponse+=message;
			}
		}

		if(!needRegisPemohon) {
			if(flagRegisKIPemohon) {
				messageResponse = messageResponse + GlobalVal.USER_TYPE_PEMOHON + " sudah generate link di Kawan internal, ";
			}else {
				messageResponse = messageResponse + GlobalVal.USER_TYPE_PEMOHON + " " + GlobalVal.STATUS_ESIGN_GENERATED + " generate link, ";					
			}
		}

		if(!needRegisPasangan && "MAR".equals(statusMarriage) && "1".equals(isSpouseSign)) {
		    if(flagRegisKIPasangan) {
				messageResponse = messageResponse + GlobalVal.USER_TYPE_PASANGAN + " sudah generate link di Kawan internal, ";
			}else {
				messageResponse = messageResponse + GlobalVal.USER_TYPE_PASANGAN + " " + GlobalVal.STATUS_ESIGN_GENERATED + " generate link, ";					
			}
		}

		if(!needRegisPenjamin && "1".equals(isNeedGuarantor)) {
			if(flagRegisKIPenjamin) {
				messageResponse = messageResponse + GlobalVal.USER_TYPE_GUARANTOR + " sudah generate link di Kawan internal, ";
			}else {
				messageResponse = messageResponse + GlobalVal.USER_TYPE_GUARANTOR + " " + GlobalVal.STATUS_ESIGN_GENERATED + " generate link, ";					
			}
		}

		messageResponse = messageResponse.replaceAll(",\\s*$", "");
		status.setMessage(messageResponse);
		status.setCode(allGenerated ? 0 : 1);
		response.setStatus(status);
		
		return response;
	}
	@Override
	public InvitationalESignResponse sentInvitationalESignAutoPreSurvey(String formName, String taskId, AuditContext callerId) {
		
		Map <String, String> mappingAsnwer = new HashMap<>();
		
		InvitationalESignResponse response = new InvitationalESignResponse();
		Status status = new Status();

		String jsonRequest=null;
		boolean needRegisPemohon = true;
		boolean needRegisPasangan = true;
		boolean needRegisPenjamin = true;
		
		boolean flagRegisKIPemohon = false;
		boolean flagRegisKIPasangan = false;
		boolean flagRegisKIPenjamin = false;
		
		
		Object[][] params = { {"taskId", taskId} };
		
		List<Map<String, Object>> mapQuestion = this.getManagerDAO().selectAllNativeString(
				"SELECT \r\n" + 
				"    mq.REF_ID, \r\n" + 
				"    ttd.QUESTION_TEXT, \r\n" + 
				"    CASE\r\n" + 
				"        WHEN ans.code_answer_type = '011'\r\n" + 
				"        THEN ttd.INT_OPTION_TEXT\r\n" + 
				"        ELSE ttd.INT_TEXT_ANSWER  \r\n" + 
				"    END AS ANSWER,\r\n" + 
				"	ans.code_answer_type\r\n" + 
				"FROM TR_TASK_H tth WITH(NOLOCK)\r\n" + 
				"JOIN TR_TASK_D ttd WITH(NOLOCK) ON tth.UUID_TASK_H = ttd.UUID_TASK_H\r\n" + 
				"JOIN MS_QUESTION mq WITH(NOLOCK) ON ttd.UUID_QUESTION = mq.UUID_QUESTION\r\n" + 
				"JOIN MS_FORM mf WITH(NOLOCK) ON tth.UUID_FORM = mf.UUID_FORM\r\n" + 
				"JOIN MS_ANSWERTYPE ans WITH(NOLOCK) ON mq.UUID_ANSWER_TYPE = ans.UUID_ANSWER_TYPE\r\n" + 
				"LEFT JOIN MS_LOV msl WITH(NOLOCK) ON ttd.INT_LOV_ID = msl.UUID_LOV\r\n" + 
				"WHERE \r\n" + 
				"    mq.REF_ID IN (\r\n" + 
				"        'PRE_NAMA_KTP', 'PRE_NO_HP', 'PRE_EMAIL', 'PRE_NO_KTP', \r\n" + 
				"        'PRE_TGL_LHR', 'PRE_TMPT_LHR', 'PRE_GENDER', 'PRE_LGL_KCMTN', \r\n" + 
				"        'PRE_LGL_KLRHN', 'PRE_LGL_ZIPCODE', 'PRE_LGL_ADDR', \r\n" + 
				"        'PRE_LGL_CITY', 'PRE_LGL_PROVINSI', 'PRE_NAMA_PSGN', \r\n" + 
				"        'PRE_NO_HP_PSGN', 'PRE_EMAIL_PSGN', 'PRE_NO_KTP_PSGN', \r\n" + 
				"        'PRE_TGL_LHR_PSGN', 'PRE_TMPT_LHR_PSGN', 'PRE_INC_PSGN', \r\n" + 
				"        'PRE_STTS_PERNIKAHAN', 'PRE_PROD_CAT', 'SVY_ES_PERSETUJUAN', \r\n" + 
				"        'PRE_NEED_GRNTR', 'PRE_TGL_LHR_GRTR', 'PRE_TMPT_LHR_GRTR', 'PRE_NAMA_KTP_GRTR',\r\n" + 
				"		 'PRE_NO_KTP_GRTR', 'PRE_NO_HP_GRTR', 'PRE_EMAIL_GRTR' \r\n" + 
				"    )\r\n" + 
				"    AND tth.TASK_ID = :taskId", params);
		
		for (int i = 0; i < mapQuestion.size(); i++) {
			Map<String, Object> map = mapQuestion.get(i);
			
			String refId = String.valueOf(map.get("d0"));
			String answer = String.valueOf(map.get("d2"));
			String answerTypeCode = String.valueOf(map.get("d3"));
			
			if (GlobalVal.ANSWER_TYPE_DATE.equalsIgnoreCase(answerTypeCode)) {
				String date = dynamicDateFormatterByGenset(answer, DATE_FORMAT);
				answer = date;
			}
			
			mappingAsnwer.put(refId, answer);
		}
		
		
		
		String statusMarriage = mappingAsnwer.get(GlobalVal.REF_PRESURVEY_STATUS_PERNIKAHAN);
		String isSpouseSign = mappingAsnwer.get(GlobalVal.REF_PRESURVEY_MELIBATKAN_PASANGAN);
		String isNeedGuarantor = mappingAsnwer.get(GlobalVal.REF_PRESURVEY_NEED_GRNTR);
		String isDigitalSign = mappingAsnwer.get(GlobalVal.PERSETUJUAN);
		String businessLineCode = StringUtils.EMPTY;
		
		if ("1".equals(isDigitalSign)) {
		
	        TrTaskH trTaskH = null;
			if(null != taskId) {
				trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] {{"taskId", taskId}});
			}
			
			if(null==trTaskH) {
				status.setMessage("Task Tidak Ditemukan!");
				status.setCode(1);
				response.setStatus(status);
				return response;
			}

			MsGrouptask groupTask = trTaskH.getMsGrouptasks().iterator().next();
			this.getManagerDAO().fetch(trTaskH.getMsGrouptasks());
			
			if (StringUtils.isNotBlank(trTaskH.getOrderNoCae())/*if found in task d take flag from table cae data*/) {
				Object[][] prmCaeData = { {Restrictions.eq("orderNoCae", trTaskH.getOrderNoCae())},
						{Restrictions.eq("isSuccess", "1")}, {Restrictions.eq("groupTaskId", Long.valueOf(groupTask.getGroupTaskId()))} };
				TblCaeData data = this.getManagerDAO().selectOne(TblCaeData.class, prmCaeData);
				jsonRequest = data.getJsonRequest();
			}
			Map<String, Object> mappingJsonRequest = gson.fromJson(jsonRequest, new TypeToken<Map<String, Object>>() {}.getType());	
			
			needRegisPemohon = !"T".equalsIgnoreCase(null == mappingJsonRequest?"F":(String) mappingJsonRequest.get(GlobalVal.SEND_LINK_PEMOHON));
			needRegisPasangan = !"T".equalsIgnoreCase(null == mappingJsonRequest?"F":(String) mappingJsonRequest.get(GlobalVal.SEND_LINK_PASANGAN));
			needRegisPenjamin = !"T".equalsIgnoreCase(null == mappingJsonRequest?"F":(String) mappingJsonRequest.get(GlobalVal.SEND_LINK_GUARANTOR));	
			
			flagRegisKIPemohon = !needRegisPemohon;
			flagRegisKIPasangan = !needRegisPasangan;
			flagRegisKIPenjamin = !needRegisPenjamin;

	        try {
	            if ((null != mappingAsnwer.get(GlobalVal.REF_PRESURVEY_PRE_PROD_CAT)) 
	                    && (!GlobalVal.FORM_COMPLETED_JASA.equals(formName) && !GlobalVal.FORM_COMPLETED_BARANG.equals(formName))) {
	                businessLineCode = mappingAsnwer.get(GlobalVal.REF_PRESURVEY_PRE_PROD_CAT);
	            } else {
	                businessLineCode = mappingAsnwer.get(GlobalVal.PRODUCT_CATEGORY);
	            }
	        } catch (Exception e) {
	        	LOG.error(e.getMessage(), e);
	        }        
			
			Map <String, Map<String, String>> mapResult = new HashMap<>();
			Object[][] paramMapJson = { {"formName", formName}, {"type", GlobalVal.MAPPING_JSON_INVITATIONAL_ESIGN_TYPE_AUTO_PRESURVEY} };
			List<Map<String, Object>> mapJson = this.getManagerDAO().selectAllNativeString(
					"SELECT KEY_SOURCE, KEY_DESTINATION " +
					"FROM   MS_MAPPINGJSON WITH (NOLOCK) " +
					"WHERE  FORM_NAME = :formName " +
					"       AND TYPE = :type ", paramMapJson);
			
			for (int i = 0; i < mapJson.size(); i++) {
				Map<String, Object> map = mapJson.get(i);
				
				String type = String.valueOf(map.get("d0"));
				String[] splitType = type.split("@");
				
				if ((GlobalVal.USER_TYPE_PEMOHON.equalsIgnoreCase(splitType[0]) && !needRegisPemohon)	
						|| (GlobalVal.USER_TYPE_PASANGAN.equalsIgnoreCase(splitType[0]) && (!needRegisPasangan || !"MAR".equals(statusMarriage) || ("MAR".equals(statusMarriage) && "0".equals(isSpouseSign))))				
						|| (GlobalVal.USER_TYPE_GUARANTOR.equalsIgnoreCase(splitType[0]) && (!needRegisPenjamin || !"1".equals(isNeedGuarantor)))) {
					continue;
				}
				
				String[] splitValue = StringUtils.split(splitType[1], "|");
				String resultValue = mappingAsnwer.get(splitValue[0]);
				if (StringUtils.isBlank(resultValue)) {
					resultValue = StringUtils.EMPTY;
				}
				for (int j = 1; j < splitValue.length; j++) {
					if (StringUtils.isNotBlank(mappingAsnwer.get(splitValue[j]))) {
						resultValue = resultValue + mappingAsnwer.get(splitValue[j]);
					}
				}
				if (null != mapResult.get(splitType[0])) {
					Map<String, String> tempMapJson = mapResult.get(splitType[0]);
					
					tempMapJson.put(String.valueOf(map.get("d1")), resultValue);
				} else {
					Map<String, String> tempMapJson = new HashMap<>();
					tempMapJson.put(String.valueOf(map.get("d1")), resultValue);
					
					mapResult.put(splitType[0], tempMapJson);
				}
			}
			
			String messageResponse = StringUtils.EMPTY;
			boolean allGenerated = true;
			
			if(!mapResult.isEmpty()) {

				for (Map.Entry<String, Map<String, String>> entry : mapResult.entrySet()) {
					Map<String, String> mapUser = entry.getValue();
					
					String tlp = StringUtils.EMPTY;
					String email = StringUtils.EMPTY;
					for (Map.Entry<String, String> entryUser : mapUser.entrySet()) {
						if (entryUser.getKey().contains("email")) {
							email = entryUser.getValue();
						} else if (entryUser.getKey().contains("tlp")) {
							tlp = entryUser.getValue();
						}
					}
					
					if (StringUtils.isBlank(tlp) && StringUtils.isBlank(email)) {
						status.setCode(25);
						status.setMessage("Telepon atau Email dari " + entry.getKey() + " harus diisi");
						response.setStatus(status);
						return response;
					}
				}
				
				GenerateInvitationRequest invitationalRequest = new GenerateInvitationRequest();
				AuditDataType audit = new AuditDataType();
				audit.setCallerId("MOBILE");
				
				invitationalRequest.setAudit(audit);
				invitationalRequest.setBusinessLineName("");
		        invitationalRequest.setBusinessLineCode("");
		        
				if(StringUtils.isNotBlank(businessLineCode)) {
		            //ambil BusinessLineCode
		            Object[][] paramAnswerType = { {"prodCat", businessLineCode} };
		            String businessLineName = String.valueOf(this.getManagerDAO().selectOneNativeString(
		                "SELECT PRODUCT_CATEGORY_NAME " +
		                "FROM TBL_PRODUCT_CATEGORY WITH(NOLOCK) " +
		                "WHERE PRODUCT_CATEGORY_CODE = :prodCat " , paramAnswerType));
		            invitationalRequest.setBusinessLineName(businessLineName);
		            invitationalRequest.setBusinessLineCode(businessLineCode);
		        }       
		        
		        if(null != taskId) {
		            //ambil officeCode,OfficeName,applno,regionCode,REgionName
		            Object[][] paramAnswerType2 = { {"uuidTaskH", taskId} };
		            List<Map<String, Object>> esign_additional = this.getManagerDAO().selectAllNativeString(
		                    "SELECT TTH.APPL_NO,MB.BRANCH_NAME,MB.BRANCH_CODE,MR.REGION_NAME,MR.REGION_CODE " +
		                    "FROM TR_TASK_H TTH WITH(NOLOCK) " +
		                    "JOIN MS_BRANCH MB WITH(NOLOCK) ON TTH.UUID_BRANCH = MB.UUID_BRANCH " +
		                    "JOIN MS_REGION MR WITH(NOLOCK) ON MB.UUID_REGION = MR.UUID_REGION " +
		                    "WHERE TTH.UUID_TASK_H = :uuidTaskH " , paramAnswerType2);
		            
		            Map<String,Object> list1 = esign_additional.get(0);
		            invitationalRequest.setReferenceNo(String.valueOf(list1.get("d0")));
		            invitationalRequest.setOfficeName(String.valueOf(list1.get("d1")));
		            invitationalRequest.setOfficeCode(String.valueOf(list1.get("d2")));
		            invitationalRequest.setRegionName(String.valueOf(list1.get("d3")));
		            invitationalRequest.setRegionCode(String.valueOf(list1.get("d4")));
		        }
				
				for (Map.Entry<String, Map<String, String>> entry : mapResult.entrySet()) {
					Map<String, String> mapUser = entry.getValue();
					List<Map<String, String>> user = new ArrayList<>();
					String message = StringUtils.EMPTY;
					String userType = entry.getKey();
					user.add(mapUser);
					invitationalRequest.setUsers(user);
					try {
						MssResponseType responseFromESign = intFormLogic.GenerateInvitationLink(invitationalRequest);
						if (0 != responseFromESign.getStatus().getCode()) {
							status.setCode(responseFromESign.getStatus().getCode());
							String resMessage = StringUtils.isBlank(responseFromESign.getStatus().getMessage())
									? StringUtils.EMPTY
									: responseFromESign.getStatus().getMessage();
							
							resMessage = resMessage.replace('*', 'X');
							allGenerated = false;
							message = userType + " Failed: " + resMessage + ", ";
						} else {
							if("Success".equalsIgnoreCase(responseFromESign.getStatus().getMessage())) {
								message = userType + " " + GlobalVal.STATUS_ESIGN_GENERATED + " Generate Link, ";														
							}else {
								message = userType +  " " + GlobalVal.STATUS_ESIGN_GENERATED + " " +responseFromESign.getStatus().getMessage()+", ";	
							}
						}			
					} catch (Exception e) {
						allGenerated = false;
						message = userType + " Failed: " + e.getMessage()  + ", ";
					}

					messageResponse+=message;
				}
			}
			
			if(!needRegisPemohon) {
				if(flagRegisKIPemohon) {
					messageResponse = messageResponse + GlobalVal.USER_TYPE_PEMOHON + " sudah generate link di Kawan internal, ";
				}else {
					messageResponse = messageResponse + GlobalVal.USER_TYPE_PEMOHON + " " + GlobalVal.STATUS_ESIGN_GENERATED + " generate link, ";					
				}
			}

			if(!needRegisPasangan && "MAR".equals(statusMarriage) && "1".equals(isSpouseSign)) {
			    if(flagRegisKIPasangan) {
					messageResponse = messageResponse + GlobalVal.USER_TYPE_PASANGAN + " sudah generate link di Kawan internal, ";
				}else {
					messageResponse = messageResponse + GlobalVal.USER_TYPE_PASANGAN + " " + GlobalVal.STATUS_ESIGN_GENERATED + " generate link, ";					
				}
			}

			if(!needRegisPenjamin && "1".equals(isNeedGuarantor)) {
				if(flagRegisKIPenjamin) {
					messageResponse = messageResponse + GlobalVal.USER_TYPE_GUARANTOR + " sudah generate link di Kawan internal, ";
				}else {
					messageResponse = messageResponse + GlobalVal.USER_TYPE_GUARANTOR + " " + GlobalVal.STATUS_ESIGN_GENERATED + " generate link, ";					
				}
			}

			messageResponse = messageResponse.replaceAll(",\\s*$", "");
			status.setMessage(messageResponse);
			status.setCode(allGenerated ? 0 : 1);

		} else {
			status.setCode(0);
			status.setMessage("Pemohon Tidak Setuju Untuk Registrasi Esign");
		}
		
		response.setStatus(status);
		
		return response;
	}
	
	private Map<String, String> buildMap(Map<String, String> request) {
		Map<String, String> result = new HashMap<>();
		for (Map.Entry<String, String> entry : request.entrySet()) {
			String answer = entry.getValue();
			String refId = entry.getKey();
			Object[][] paramAnswerType = { {"refId", refId} };
			String answerType = String.valueOf(this.getManagerDAO().selectOneNativeString(
				"SELECT CODE_ANSWER_TYPE " +
				"FROM   MS_QUESTION MSQ WITH (NOLOCK) " +
				"JOIN   MS_ANSWERTYPE MSA WITH (NOLOCK) ON MSQ.UUID_ANSWER_TYPE = MSA.UUID_ANSWER_TYPE " +
				"WHERE  MSQ.REF_ID = :refId", paramAnswerType));
			
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerType) && null != answer) {
				DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
				try {
					Date dateParse = df.parse(answer);
					answer = (null != dateParse) ? new SimpleDateFormat("yyyy-MM-dd").format(dateParse) : null;
				} catch (ParseException e) {
					LOG.error(e.getMessage(), e);
					
					DateFormat df2 = new SimpleDateFormat("dd/MM/yyyy");
					try {
						Date dateParse = df2.parse(answer);
						answer = (null != dateParse) ? new SimpleDateFormat("yyyy-MM-dd").format(dateParse) : null;
					} catch (ParseException e1) {
						LOG.error(e.getMessage(), e);
					}
				} 
			}
			
			result.put(refId, answer);
		}
		
		return result;
	}

	@Override
	public InvitationalESignResponse checkRegistrationStatus(String formName, Map<String, String> request, AuditContext callerId) {
		request = this.buildMap(request);
		
		InvitationalESignResponse response = new InvitationalESignResponse();
		Status status = new Status();
		
		String statusMarriage = StringUtils.EMPTY;
		String isNeedGuarantor = StringUtils.EMPTY;
		String isSpouseSign = StringUtils.EMPTY;
		
		if (GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(formName)) {
			statusMarriage = request.get(GlobalVal.REF_PRESURVEY_STATUS_PERNIKAHAN);
			isNeedGuarantor = request.get(GlobalVal.REF_PRESURVEY_NEED_GRNTR);
			isSpouseSign = request.get(GlobalVal.REF_PRESURVEY_MELIBATKAN_PASANGAN);
		} else {
			statusMarriage = request.get(GlobalVal.STATUS_PERNIKAHAN); //perlu cek refid di tiap form
			isNeedGuarantor = (StringUtils.isBlank(request.get(GlobalVal.GUA_IS_NEED))) ? request.get(GlobalVal.REF_PRESURVEY_NEED_GRNTR) : request.get(GlobalVal.GUA_IS_NEED); //perlu cek refid di tiap form
			if("1".equalsIgnoreCase(request.get(GlobalVal.REF_PRESURVEY_MELIBATKAN_PASANGAN))) {
				isSpouseSign = request.get(GlobalVal.PSGN_TND_TGN_BAK);
			}else{
				isSpouseSign = request.get(GlobalVal.PSGN_TND_TGN);
			}
		}
		
		Map <String, Map<String, String>> mapResult = new HashMap<>();
		Object[][] paramMapJson = { {"formName", formName}, {"type", GlobalVal.MAPPING_JSON_CHECK_ESIGN_TYPE} };
		List<Map<String, Object>> mapJson = this.getManagerDAO().selectAllNativeString(
				"SELECT KEY_SOURCE, KEY_DESTINATION " +
				"FROM   MS_MAPPINGJSON WITH (NOLOCK) " +
				"WHERE  FORM_NAME = :formName " +
				"       AND TYPE = :type ", paramMapJson);
		
		for (int i = 0; i < mapJson.size(); i++) {
			Map<String, Object> map = mapJson.get(i);
			
			String type = String.valueOf(map.get("d0"));
			String[] splitType = type.split("@");
			
			if ((!"MAR".equals(statusMarriage) && GlobalVal.USER_TYPE_PASANGAN.equalsIgnoreCase(splitType[0]))
					|| (!"1".equals(isNeedGuarantor) && GlobalVal.USER_TYPE_GUARANTOR.equalsIgnoreCase(splitType[0]))
					|| ("MAR".equals(statusMarriage) && GlobalVal.USER_TYPE_PASANGAN.equalsIgnoreCase(splitType[0]) && "0".equals(isSpouseSign))) {
				
				continue;
			}
			
			String[] splitValue = StringUtils.split(splitType[1], "|");
			String resultValue = request.get(splitValue[0]);
			if (StringUtils.isBlank(resultValue)) {
				resultValue = StringUtils.EMPTY;
			}
			for (int j = 1; j < splitValue.length; j++) {
				if (StringUtils.isNotBlank(request.get(splitValue[j]))) {
					resultValue = resultValue + request.get(splitValue[j]);
				}
			}
			if (null != mapResult.get(splitType[0])) {
				Map<String, String> tempMapJson = mapResult.get(splitType[0]);
				
				tempMapJson.put(String.valueOf(map.get("d1")), resultValue);
			} else {
				Map<String, String> tempMapJson = new HashMap<>();
				tempMapJson.put(String.valueOf(map.get("d1")), resultValue);
				
				mapResult.put(splitType[0], tempMapJson);
			}
		}
		
		Map<String, String> resultMap = new HashMap<>();
		CheckEsignRegistrationRequest checkEsignRequest = new CheckEsignRegistrationRequest();
	
		String messageResponse = StringUtils.EMPTY;
		boolean allActivated = true;
		for (Map.Entry<String, Map<String, String>> entry : mapResult.entrySet()) {
			Map<String, String> mapUser = entry.getValue();
			String message = StringUtils.EMPTY;
			String userType = entry.getKey();
			
			String nik = StringUtils.EMPTY;
			for (Map.Entry<String, String> entryUser : mapUser.entrySet()) {
				if (entryUser.getKey().contains("Ktp")) {
					nik = entryUser.getValue();
				}
			}
			
			if (StringUtils.isBlank(nik)) {
				status.setCode(25);
				status.setMessage("nik dari " + userType + " harus diisi");
				response.setStatus(status);
				return response;
			}

			//
			AuditDataType audit = new AuditDataType();
			audit.setCallerId(callerId.getCallerId());
			
			checkEsignRequest.setAudit(audit);
			checkEsignRequest.setDataType("NIK");
			checkEsignRequest.setUserData(mapUser.get("idKtp"));
			
			try {
				CheckESignRegistrationResponse responseFromESign = intFormLogic.CheckEsignRegistrationStatus(checkEsignRequest);
				if (0 != responseFromESign.getStatus().getCode()) {
					if(8165 == responseFromESign.getStatus().getCode()) {
						message = userType + " Belum melakukan Registrasi, ";
					}else {
						message = "Message From E-Sign for "+userType+": " + responseFromESign.getStatus().getMessage()+", ";						
					}
					allActivated=false;
				} else {
					List<EsignRegistrationDataBean> registrationDataList = responseFromESign.getRegistrationData();
					
					for (EsignRegistrationDataBean esignRegistrationDataBean : registrationDataList) {
					    String registrationStatus = esignRegistrationDataBean.getRegistrationStatus();
						String vendor = esignRegistrationDataBean.getVendor();
						if("Vida".equals(vendor)) {
							if ("0".equals(registrationStatus)) {
								message = userType + " Belum melakukan Registrasi, ";
								allActivated = false;
								break;
							} else if ("1".equals(registrationStatus)) {
								message = userType + " Sudah melakukan Registrasi namun belum Aktivasi, ";
								allActivated = false;
							} else {
								if (!resultMap.containsKey(userType)) {
									message = userType + " Sudah melakukan Registrasi dan Aktivasi, ";
								}
							}
						}
					}
				}			
			} catch (Exception e) {
				status.setCode(1);
				status.setMessage( e.getMessage());
				response.setStatus(status);
				return response;
			}
			messageResponse+=message;
		}
		messageResponse = messageResponse.replaceAll(",\\s*$", "");
		messageResponse = messageResponse.replace('*', 'X');
		
		status.setCode( allActivated ? 0 : 1);
		status.setMessage(messageResponse);
		response.setStatus(status);
		return response;

	}
}
