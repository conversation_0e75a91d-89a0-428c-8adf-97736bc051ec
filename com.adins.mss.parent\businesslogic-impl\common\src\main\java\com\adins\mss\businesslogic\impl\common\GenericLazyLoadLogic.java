package com.adins.mss.businesslogic.impl.common;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.LazyLoadLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.FinalTrTaskdetaillob;
import com.adins.mss.model.FinalTrTaskrejecteddetail;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskrejecteddetail;
import com.adins.mss.model.custom.UploadImageBean;
import com.adins.mss.model.custom.ViewImageRequestBean;
import com.adins.mss.model.custom.ViewImageResponseBean;
import com.adins.mss.util.CipherTool;

@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericLazyLoadLogic extends BaseLogic implements LazyLoadLogic {

	private IntFormLogic intFormLogic;
	private CommonLogic commonLogic;
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
    private ImageStorageLogic imageStorageLogic; 
    
	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }

	@Override
	public byte[] getImageLob(String uuidTaskLob, AuditContext callerId) {
		ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);
		if("1".equals(this.link_encrypt)){
			String[] toBeDecrypt = {uuidTaskLob};
			uuidTaskLob = CipherTool.decryptData(toBeDecrypt).get(0).toString();	
		}
		
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			return imageStorageLogic.retrieveImageBlob(
					Long.valueOf(uuidTaskLob), false);
		}
		else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
			return imageStorageLogic.retrieveImageFileSystemByTaskD(
					Long.valueOf(uuidTaskLob), false);
		} 
		else if (ImageStorageLocation.DMS == saveImgLoc) {
			TrTaskdetaillob mapResult = this.getManagerDAO().selectOne(TrTaskdetaillob.class, Long.valueOf(uuidTaskLob));
			
			MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
					mapResult.getTrTaskH().getMsForm().getUuidForm(), mapResult.getTrTaskH().getFormVersion(), callerId);
			
			Object[][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHist.getUuidFormHistory())},
					{Restrictions.eq("msQuestion.uuidQuestion", mapResult.getMsQuestion().getUuidQuestion())}};
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
			
			ViewImageRequestBean request = new ViewImageRequestBean();
			UploadImageBean imageBean = new UploadImageBean();
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != mapResult.getTrTaskH().getSubmitDate()) {
	        	taskDate = mapResult.getTrTaskH().getSubmitDate();
	        } else {
	        	taskDate = mapResult.getTrTaskH().getDtmCrt();
	        }
	        Object [][] params = {{"uuidTaskH", mapResult.getTrTaskH().getTaskId()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT	GROUP_TASK_ID " + 
					"FROM	MS_GROUPTASK with(nolock) " + 
					"WHERE	UUID_TASK_H = :uuidTaskH ", params);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(mapResult.getTrTaskH().getMsForm().getUuidForm()));

			try {
				cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
			} catch (ParseException e1) {
				e1.printStackTrace();
			}

			if(taskDate.before(cutoffDate)) {
				imageBean.setTaskId(mapResult.getTrTaskH().getTaskId());
				imageBean.setId(mapResult.getTrTaskH().getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				imageBean.setTaskId(groupTaskIdForm);
				imageBean.setId(groupTaskIdForm);
			}
			String dmsType = "survey";
			
			imageBean.setJenisDoc(qset.getQuestionLabel());
			if(mapResult.getTrTaskH().getAmMsuser() != null){
				imageBean.setUsername(mapResult.getTrTaskH().getAmMsuser().getUniqueId());
			} else {
				imageBean.setUsername("admin");
			}
			imageBean.setType(dmsType);
			imageBean.setRefId(qset.getRefId());
			request.setUploadImageBean(imageBean);
			ViewImageResponseBean response = this.intFormLogic.viewImageDMS(callerId, request);
			return response.getByteImage();
		}
		
		return null;
	}

	@Override
	public byte[] getImageLobFinal(String uuidTaskLob, AuditContext callerId) {
        ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);
        if("1".equals(this.link_encrypt)){
        	String[] toBeDecrypt = {uuidTaskLob};
    		uuidTaskLob = CipherTool.decryptData(toBeDecrypt).get(0).toString();	
        }
        
        if (ImageStorageLocation.DATABASE == saveImgLoc) {
            return imageStorageLogic.retrieveImageBlob(
            		Long.valueOf(uuidTaskLob), true);
        }
        else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
            return imageStorageLogic.retrieveImageFileSystemByTaskD(
            		Long.valueOf(uuidTaskLob), true);
        }
        else if (ImageStorageLocation.DMS == saveImgLoc) {
			FinalTrTaskdetaillob mapResult = this.getManagerDAO().selectOne(FinalTrTaskdetaillob.class, Long.valueOf(uuidTaskLob));
			
			MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
					mapResult.getFinalTrTaskH().getMsForm().getUuidForm(), mapResult.getFinalTrTaskH().getFormVersion(), callerId);
			
			Object[][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHist.getUuidFormHistory())},
					{Restrictions.eq("msQuestion.uuidQuestion", mapResult.getMsQuestion().getUuidQuestion())}};
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
			
			ViewImageRequestBean request = new ViewImageRequestBean();
			UploadImageBean imageBean = new UploadImageBean();
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != mapResult.getFinalTrTaskH().getSubmitDate()) {
	        	taskDate = mapResult.getFinalTrTaskH().getSubmitDate();
	        } else {
	        	taskDate = mapResult.getFinalTrTaskH().getDtmCrt();
	        }
	        Object [][] params = {{"uuidTaskH", mapResult.getFinalTrTaskH().getTaskId()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT	GROUP_TASK_ID " + 
					"FROM	FINAL_MS_GROUPTASK with(nolock) " + 
					"WHERE	UUID_TASK_H = :uuidTaskH ", params);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(mapResult.getFinalTrTaskH().getMsForm().getUuidForm()));

			try {
				cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
			} catch (ParseException e1) {
				e1.printStackTrace();
			}

			if(taskDate.before(cutoffDate)) {
				imageBean.setTaskId(mapResult.getFinalTrTaskH().getTaskId());
				imageBean.setId(mapResult.getFinalTrTaskH().getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				imageBean.setTaskId(groupTaskIdForm);
				imageBean.setId(groupTaskIdForm);
			}
			String dmsType = "survey";
			
			imageBean.setJenisDoc(qset.getQuestionLabel());
			imageBean.setUsername(mapResult.getFinalTrTaskH().getAmMsuser().getUniqueId());
			imageBean.setType(dmsType);
			imageBean.setRefId(qset.getRefId());
			request.setUploadImageBean(imageBean);
			ViewImageResponseBean response = this.intFormLogic.viewImageDMS(callerId, request);
			return response.getByteImage();
		}
        
        return null;
	}
	
	@Override
	public byte[] getImageLobReject(String uuidTaskLob, AuditContext callerId) {
        ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);
        
        if (ImageStorageLocation.DATABASE == saveImgLoc) {
            return imageStorageLogic.retrieveRejectedImageBlob(
            		Long.valueOf(uuidTaskLob), false);
        }
        else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
            return imageStorageLogic.retrieveRejectedImageFileSystemByTaskD(
            		Long.valueOf(uuidTaskLob), false);
        }
        else if (ImageStorageLocation.DMS == saveImgLoc) {
        	TrTaskrejecteddetail mapResult = this.getManagerDAO().selectOne(TrTaskrejecteddetail.class, Long.valueOf(uuidTaskLob));
        	
        	MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
					mapResult.getTrTaskH().getMsForm().getUuidForm(), mapResult.getTrTaskH().getFormVersion(), callerId);
			
			Object[][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHist.getUuidFormHistory())},
					{Restrictions.eq("msQuestion.uuidQuestion", mapResult.getQuestionId())}};
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
        	
			ViewImageRequestBean request = new ViewImageRequestBean();
			UploadImageBean imageBean = new UploadImageBean();
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != mapResult.getTrTaskH().getSubmitDate()) {
	        	taskDate = mapResult.getTrTaskH().getSubmitDate();
	        } else {
	        	taskDate = mapResult.getTrTaskH().getDtmCrt();
	        }
	        Object [][] params = {{"uuidTaskH", mapResult.getTrTaskH().getTaskId()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT	GROUP_TASK_ID " + 
					"FROM	MS_GROUPTASK with(nolock) " + 
					"WHERE	UUID_TASK_H = :uuidTaskH ", params);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(mapResult.getTrTaskH().getMsForm().getUuidForm()));

			try {
				cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
			} catch (ParseException e1) {
				e1.printStackTrace();
			}

			if(taskDate.before(cutoffDate)) {
				imageBean.setTaskId(mapResult.getTrTaskH().getTaskId());
				imageBean.setId(mapResult.getTrTaskH().getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				imageBean.setTaskId(groupTaskIdForm);
				imageBean.setId(groupTaskIdForm);
			}
			String dmsType = "survey";
			
			
			imageBean.setJenisDoc(qset.getQuestionLabel());
			imageBean.setUsername(mapResult.getTrTaskH().getAmMsuser().getUniqueId());
			imageBean.setType(dmsType);
			imageBean.setRefId(qset.getRefId());
			request.setUploadImageBean(imageBean);
			ViewImageResponseBean response = this.intFormLogic.viewImageDMS(callerId, request);
			return response.getByteImage();
		}
        
        return null;
	}
	
	@Override
	public byte[] getImageLobRejectFinal(String uuidTaskLob, AuditContext callerId) {
        ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);
        
        if (ImageStorageLocation.DATABASE == saveImgLoc) {
            return imageStorageLogic.retrieveRejectedImageBlob(
            		Long.valueOf(uuidTaskLob), true);
        }
        else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
            return imageStorageLogic.retrieveRejectedImageFileSystemByTaskD(
            		Long.valueOf(uuidTaskLob), true);
        }
        else if (ImageStorageLocation.DMS == saveImgLoc) {
        	FinalTrTaskrejecteddetail mapResult = this.getManagerDAO().selectOne(FinalTrTaskrejecteddetail.class, Long.valueOf(uuidTaskLob));

        	MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
					mapResult.getFinalTrTaskH().getMsForm().getUuidForm(), mapResult.getFinalTrTaskH().getFormVersion(), callerId);
			
			Object[][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHist.getUuidFormHistory())},
					{Restrictions.eq("msQuestion.uuidQuestion", mapResult.getQuestionId())}};
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
        	
			ViewImageRequestBean request = new ViewImageRequestBean();
			UploadImageBean imageBean = new UploadImageBean();
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != mapResult.getFinalTrTaskH().getSubmitDate()) {
	        	taskDate = mapResult.getFinalTrTaskH().getSubmitDate();
	        } else {
	        	taskDate = mapResult.getFinalTrTaskH().getDtmCrt();
	        }
	        Object [][] params = {{"uuidTaskH", mapResult.getFinalTrTaskH().getTaskId()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT	GROUP_TASK_ID " + 
					"FROM	MS_GROUPTASK with(nolock) " + 
					"WHERE	UUID_TASK_H = :uuidTaskH ", params);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(mapResult.getFinalTrTaskH().getMsForm().getUuidForm()));

			try {
				cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
			} catch (ParseException e1) {
				e1.printStackTrace();
			}

			if(taskDate.before(cutoffDate)) {
				imageBean.setTaskId(mapResult.getFinalTrTaskH().getTaskId());
				imageBean.setId(mapResult.getFinalTrTaskH().getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				imageBean.setTaskId(groupTaskIdForm);
				imageBean.setId(groupTaskIdForm);
			}
			
			String dmsType = "survey";
			
			
			imageBean.setJenisDoc(qset.getQuestionLabel());
			imageBean.setUsername(mapResult.getFinalTrTaskH().getAmMsuser().getUniqueId());
			imageBean.setType(dmsType);
			imageBean.setRefId(qset.getRefId());
			request.setUploadImageBean(imageBean);
			ViewImageResponseBean response = this.intFormLogic.viewImageDMS(callerId, request);
			return response.getByteImage();
		}
        
        return null;
	}
}
