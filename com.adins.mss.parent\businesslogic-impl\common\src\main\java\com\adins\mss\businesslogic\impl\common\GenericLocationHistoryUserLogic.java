package com.adins.mss.businesslogic.impl.common;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.LocationHistoryUserLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrDatausagelog;
import com.adins.mss.model.TrLocationhistory;
import com.adins.mss.model.custom.MessageEventBean;
import com.adins.mss.services.model.common.LocationHistoryUserBean;
import com.google.gson.Gson;

public class GenericLocationHistoryUserLogic extends BaseLogic implements LocationHistoryUserLogic  {
	private static final Logger LOG = LoggerFactory.getLogger(GenericLocationHistoryUserLogic.class);
	
	private CommonLogic commonLogic;
	
	public void setCommonLogic(CommonLogic commonLogic) {
        this.commonLogic = commonLogic;
    }

    private String saveHistory(String[] locationHistArray, String precentageBattery, String dataUsage,
    		AuditContext callerId) throws ParseException{
		String surveyorId = null;
		boolean newer = true;
		LocationHistoryUserBean locBean = null;
		AmMsuser am = null;
		if (locationHistArray.length != 0) {
			locBean = new LocationHistoryUserBean(locationHistArray[0]);
			am = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(locBean.getSurveyor()));
		}
		
		String[][] params = {{"surveyor", String.valueOf(am.getUuidMsUser())}};
		Date lastLocationTimeStamp = (Date) this.getManagerDAO().selectOneNative(
				"services.common.user.getLastTimeStamp", params);
		
		LOG.debug(callerId.getCallerId() + " : lastLocationTimeStamp = " + lastLocationTimeStamp);
		
		Map<String, Object> latestData = new HashMap<>();
		Date sysdate = new Date();
		for (int i=0; i < locationHistArray.length; i++) {
			newer = true;
			if (locationHistArray[i].length() < 1){
				continue;
			}
			
			LocationHistoryUserBean trackingBean = new LocationHistoryUserBean(locationHistArray[i]);
				
			if (lastLocationTimeStamp != null) {
				long trackTime = (trackingBean.getTimestampDate().getTime()/1000)*1000;
				long lastTrackTime = lastLocationTimeStamp.getTime();
				if(trackTime <= lastTrackTime){
					newer = false;
				} 
				else {
					lastLocationTimeStamp = trackingBean.getTimestampDate();
				}
			}
			else {
				lastLocationTimeStamp = trackingBean.getTimestampDate();
			}
			if(!newer)continue;

			this.processLocation(trackingBean);
			
			if (null == surveyorId){
				surveyorId = trackingBean.getSurveyor();
			}
			
			TrLocationhistory bean = new TrLocationhistory();				
			bean.setAmMsuser(am);
			
			if(trackingBean.getLatitude() != null){
			    BigDecimal lat = new BigDecimal(trackingBean.getLatitude());
				bean.setLatitude(lat);
                latestData.put("lat", lat);
			}
			if(trackingBean.getLongitude() != null){
			    BigDecimal lng = new BigDecimal(trackingBean.getLongitude());
				bean.setLongitude(lng);
				latestData.put("lng", lng);
				latestData.put("timestamp", trackingBean.getTimestampDate());
			}
			bean.setMcc(Integer.parseInt(trackingBean.getMcc()));
			bean.setMnc(Integer.parseInt(trackingBean.getMnc()));
			bean.setLac(Integer.parseInt(trackingBean.getLac()));
			bean.setCellId(Integer.parseInt(trackingBean.getCellId()));
			bean.setDatetime(trackingBean.getTimestampDate());
			bean.setIsGps(trackingBean.getIsGps());
			bean.setAccuracy(trackingBean.getAccuracy());
			bean.setGeolocationProvider(trackingBean.getProvider());
			bean.setDtmCrt(sysdate);
			bean.setUsrCrt(callerId.getCallerId());
			this.getManagerDAO().insert(bean);
		}
		locBean = new LocationHistoryUserBean(locationHistArray[locationHistArray.length-1]);
		this.saveDataUsage(am, dataUsage, locBean.getTimestampDate(), callerId); //Gunakan jam tracking instead sysdate, (kemungkinan dispute waktu jika WITA disave di WIB)
		this.saveLatestAttribute(am, latestData, precentageBattery, callerId);

		return surveyorId;
	}
    
    private void saveDataUsage(AmMsuser user, String dataUsage, Date dateUsage, AuditContext callerId) {
    	//if data usage dan date isNotBlank
        if (StringUtils.isNotBlank(dataUsage) && dateUsage != null) {
        	//cek data trdatausage by date date dan user
        	Object[][] params = {{"surveyor", user.getUuidMsUser()}};
			Date lastTimestamp = (Date) this.getManagerDAO().selectOneNative(
					"services.common.user.getLastTimeStampDataUsage", params);
			if (lastTimestamp!=null) {
				//do update
				//cek lasttime
				long trackTime = (dateUsage.getTime()/1000)*1000;
				long lastTrackTime = lastTimestamp.getTime();
				if(trackTime > lastTrackTime){
					String[][] paramsLog = {{"surveyor", String.valueOf(user.getUuidMsUser())}};
					Object seqLog = this.getManagerDAO().selectOneNative(
							"services.common.user.getDataUsageLog", paramsLog);
					long sequenceLog = Long.parseLong(seqLog.toString());
					TrDatausagelog dbModel = this.getManagerDAO().selectOne(TrDatausagelog.class, sequenceLog);
					dbModel.setAmMsuser(user);
					dbModel.setDtmUpd(dateUsage);
					dbModel.setDataUsage(Integer.parseInt(dataUsage));
					this.getManagerDAO().update(dbModel);					
				}
			} 
			else {
				//insert data
				TrDatausagelog dbModel = new TrDatausagelog();
				dbModel.setAmMsuser(user);
				dbModel.setDtmCrt(dateUsage);
				dbModel.setDataUsage(Integer.parseInt(dataUsage));
				this.getManagerDAO().insert(dbModel);
			}
        	
        }
    }
    
    private void saveLatestAttribute(AmMsuser user, Map<String, Object> latestData,String percentageBattery, AuditContext callerId) {
        if (user == null || latestData == null || latestData.isEmpty()){
        	return;
        }
        
        this.commonLogic.insertLastLocation(user.getUuidMsUser(),
        		user.getAmMssubsystem().getUuidMsSubsystem(),
                (BigDecimal) latestData.get("lat"), (BigDecimal) latestData.get("lng"), callerId);
        
        this.commonLogic.insertLastLocationTimestamp(user.getUuidMsUser(),
        		user.getAmMssubsystem().getUuidMsSubsystem(),
                (Date) latestData.get("timestamp"), callerId);
        
        if (StringUtils.isNotBlank(percentageBattery)) { // for supporting older apps which not sending batteryPct information            
            this.commonLogic.insertPercentageBattery(user.getUuidMsUser(),
            		user.getAmMssubsystem().getUuidMsSubsystem(), percentageBattery, callerId);
        }
        
    }
	
	private void processLocation(LocationHistoryUserBean bean) {
		boolean isGps = !(Double.valueOf(bean.getLatitude()).doubleValue() == 0d
				&& Double.valueOf(bean.getLongitude()).doubleValue() == 0d) ? true : false;
		if(!isGps) {
			bean.setLatitude(null); 
			bean.setLongitude(null);
			bean.setIsGps("0");
		}
		else {
			bean.setIsGps("1");
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String saveLocationHistory(List<LocationHistoryUserBean> listLocationInfo, String precentageBattery, 
			String dataUsage, AuditContext callerId) 
			throws ParseException{
		String[] locationHistArray = new String[listLocationInfo.size()];
		for(int i=0; i<listLocationInfo.size(); i++){
			LocationHistoryUserBean bean = (LocationHistoryUserBean) listLocationInfo.get(i);
			StringBuilder loc = new StringBuilder();
			loc.append(bean.getSurveyor()).append(";")
				.append(bean.getLatitude()).append(";")
				.append(bean.getLongitude()).append(";")
				.append(bean.getCellId()).append(";")
				.append(bean.getMcc()).append(";")
				.append(bean.getMnc()).append(";")
				.append(bean.getLac()).append(";")
				.append(bean.getTimestamp()).append(";")
				.append(bean.getAccuracy().toString()).append(";");
			
			locationHistArray[i] = loc.toString();
		}
		String surveyor = this.saveHistory(locationHistArray, precentageBattery, dataUsage, callerId);
		return surveyor;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
    @Override
    public List<LocationBean> listEmptyCoordinate(AuditContext callerId) {
       List<Map<String, Object>> queryResult = this.getManagerDAO().selectAllNative("common.locationhistory.listEmptyCoordinate", null, null);
       if (queryResult == null || queryResult.isEmpty()) {
           return Collections.emptyList();
       }
       
       List<LocationBean> locations = new ArrayList<>();
       for (Map<String, Object> record : queryResult) {
           LocationBean locationBean = new LocationBean();
           locationBean.setMcc((Integer) record.get("d0"));
           locationBean.setMnc((Integer) record.get("d1"));
           locationBean.setLac((Integer) record.get("d2"));
           locationBean.setCellid((Integer) record.get("d3"));
           locations.add(locationBean);
       }
       
       return locations;
    }
	
	@Override
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Async
	public void sendMessageToJms(List<LocationHistoryUserBean> listLocationInfo, AuditContext callerId) {
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		//SSE
		MessageEventBean msgBean = new MessageEventBean();
		msgBean.setEvent(GlobalVal.EVENT_LOC);
		msgBean.setUuidUser(user.getImei()+user.getUuidMsUser());
		msgBean.setLatitude(listLocationInfo.get(listLocationInfo.size()-1).getLatitude());
		msgBean.setLongitude(listLocationInfo.get(listLocationInfo.size()-1).getLongitude());
		msgBean.setIsDetected("1");
		msgBean.setInitialName(user.getInitialName());
		msgBean.setFullName(user.getFullName());
		msgBean.setLastTimeDetected(DateFormatUtils.format(new Date(),"HH:mm:ss"));
		msgBean.setLoginId(user.getLoginId());
		msgBean.setUuidMessageBean(this.getManagerDAO().getUUID());
		Gson gson = new Gson();
		try{
			sendMessage(gson.toJson(msgBean));
			LOG.info("Send message to JMS Success");
		}
		catch(Exception e){
			LOG.error("Send message to JMS Failed", e);
		}
		
	}
}
