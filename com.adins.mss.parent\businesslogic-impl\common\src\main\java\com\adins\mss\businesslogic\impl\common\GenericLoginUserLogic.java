package com.adins.mss.businesslogic.impl.common;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.LdapException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.framework.service.base.model.AuditDataType;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.ldap.LdapLogic;
import com.adins.framework.tool.password.PasswordHash;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.LoginUserLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.LoginException;
import com.adins.mss.exceptions.LoginException.Reason;
import com.adins.mss.exceptions.ServicesUserException;
import com.adins.mss.model.AmMemberofgroup;
import com.adins.mss.model.AmMobilemenuofgroup;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.custom.MessageEventBean;
import com.adins.mss.services.model.common.LoginMultiUserBean;
import com.adins.mss.services.model.common.LoginUserBean;
import com.adins.mss.services.model.common.MenuUserBean;
import com.adins.mss.util.PropertiesHelper;
import com.google.gson.Gson;

public class GenericLoginUserLogic extends BaseLogic implements LoginUserLogic, MessageSourceAware  {
	private static final Logger LOG = LoggerFactory.getLogger(GenericLoginUserLogic.class);

	private AuditInfo auditInfo;
	
	@Autowired
	private GlobalLogic  globalLogic;
	private IntFormLogic intFormLogic;
    private LdapLogic ldapLogic;
    private MessageSource messageSource;
    private CommonLogic commonLogic;
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}

	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

    public void setLdapLogic(LdapLogic ldapLogic) {
        this.ldapLogic = ldapLogic;
    }
    
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setCommonLogic(CommonLogic commonLogic) {
        this.commonLogic = commonLogic;
    }

	public GenericLoginUserLogic() {
		String[] pkCols = { "uuidMsUser" };
		String[] pkDbCols = { "UUID_MS_USER" };
		String[] cols = { "uuidMsUser", "imei", "androidId", "deviceInfo" };
		String[] dbCols = { "UUID_MS_USER", "IMEI", "ANDROID_ID", "DEVICE_INFO" };
		this.auditInfo = new AuditInfo("AM_MSUSER", pkCols, pkDbCols, cols, dbCols);
	}
	
	private String loginCode = "PRM07_VERS";
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	@SuppressWarnings("rawtypes")
	public List<LoginUserBean> listGeneralSetting(String application, AuditContext callerId){
		List<LoginUserBean> beanGs = new ArrayList<LoginUserBean>();
		String applicationType = null;
		if (GlobalVal.SUBSYSTEM_MS.equals(application.toUpperCase())) {
			applicationType = GlobalVal.SUBSYSTEM_MS;
		} 
		else if (GlobalVal.SUBSYSTEM_MC.equals(application.toUpperCase())) {
			applicationType = GlobalVal.SUBSYSTEM_MC;
		} 
		else if (GlobalVal.SUBSYSTEM_MO.equals(application.toUpperCase())) {
			applicationType = GlobalVal.SUBSYSTEM_MO;
		}
		else if (GlobalVal.SUBSYSTEM_MT.equals(application.toUpperCase())) {
			applicationType = GlobalVal.SUBSYSTEM_MT;
		}
		String[][] params = { {"applicationType", applicationType} };
		List listResult = this.getManagerDAO().selectAllNative("services.common.user.getListGs", params, null);
		Iterator itr = listResult.iterator();
		
		AmMsuser loginUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		this.getManagerDAO().fetch(loginUser.getMsBranch());
		String isPiloting = loginUser.getMsBranch().getIsPiloting();
		String isPilotingCae = loginUser.getMsBranch().getIsPilotingCAE();
		
		if("1".equals(isPiloting) || "1".equals(isPilotingCae)) {
			while (itr.hasNext()) {
				Map mp = (Map) itr.next();
				LoginUserBean ag = new LoginUserBean();
				String code = (String)mp.get("d0");
				String value = (String)mp.get("d1");
				if(loginCode.equals(code) || "PRM09_LINK".equals(code)) {
					continue;
				}
				if("PRM07_VERS_P".equals(code)) {
					if("1".equals(isPilotingCae)) {
						continue;
					}
					code = loginCode;
				}else if("PRM09_LINK_P".equals(code)) {
					code = "PRM09_LINK";
				}
				if("PRM07_VERS_PC".equals(code)) {
					if("0".equals(isPilotingCae)) {
						continue;
					}
					code = loginCode;
				}
				
				ag.setGsCode(code); // utk gs code
				ag.setGsValue(value); // utk gs value
				ag.setGsUuid(mp.get("d2").toString()); // utk gs uuid
				beanGs.add(ag);
			}
		}else {
			while (itr.hasNext()) {
				Map mp = (Map) itr.next();
				LoginUserBean ag = new LoginUserBean();
				ag.setGsCode((String)mp.get("d0")); // utk gs code
				ag.setGsValue((String)mp.get("d1")); // utk gs value
				ag.setGsUuid(mp.get("d2").toString()); // utk gs uuid
				beanGs.add(ag);
			}
		}
		
		return beanGs;
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@SuppressWarnings("rawtypes")
	@Override
	public Map<String, String> listLoginUser(String loginId, AuditContext callerId){
		List listUser = Collections.emptyList();
		Map<String, String> mapUser = new HashMap<String, String>();
		String[][] params = { {"loginId", loginId} };
		listUser = (List) this.getManagerDAO().selectAllNative("services.common.user.getListLoginUser", params, null);
		Iterator itr = listUser.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			mapUser.put((String)mp.get("d0"), (String)mp.get("d1"));
		}
		return mapUser;
	}
    
    @Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
    @SuppressWarnings("rawtypes")
    @Override
    public Map<String, String> listLoginUserByUuid(long uuid, AuditContext callerId) {
        List listUser = Collections.emptyList();
    Map<String, String> mapUser = new HashMap<String, String>();
        Object[][] params = { {"uuid", uuid} };
        listUser = (List) this.getManagerDAO().selectAllNative("services.common.user.getListLoginUserByUuid", params, null);
        Iterator itr = listUser.iterator();
        while (itr.hasNext()) {
            Map mp = (Map) itr.next();
            mapUser.put((String) mp.get("d0"), (String) mp.get("d1"));
        }
        /*flag_job, full_name, branch_id, branch_name, login_id, branch_addr*/
        return mapUser;
    }
	
	@Transactional(noRollbackFor={LoginException.class, ServicesUserException.class},
	        isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public AmMsuser doMobileLogin(String loginId, String password, String imei, String androidId, 
			String flagFreshInstall, AuditDataType deviceInformation, AuditContext callerId){
		//FIXME : digunakan apabila ID sales di order = loginId + O
//			if (GlobalVal.SUBSYSTEM_MO.equals(deviceInformation.getApplication())){
//				loginId = loginId+"O";
//			}
		
		Object[][] params = {{Restrictions.eq("loginId", StringUtils.upperCase(loginId))}};
		
		AmMsuser bean = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.amMssubsystem join fetch u.msJob join fetch u.msBranch where u.loginId = :loginId", new Object[][] {{"loginId", StringUtils.upperCase(loginId)}});
		
        //cek Login ID
        if (bean == null) {
        	throw new ServicesUserException(this.messageSource.getMessage("businesslogic.login.failed", null, this.retrieveLocaleAudit(callerId)));
        }
		
		String uuid = String.valueOf(bean.getUuidMsUser());
		String canLogin = "0";
		String gsValue = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MAX_FAIL_COUNT, callerId);
		Integer maxFailCount =  Integer.parseInt(gsValue);
		
		Map<String, Object> param = callerId.getParameters();
		
		//cek Subsystem Name
		if (!(param.get(AuditContext.KEY_PARAMS_APPLICATIONNAME)).equals(bean.getAmMssubsystem().getSubsystemName())) {
			throw new ServicesUserException(this.messageSource.getMessage("businesslogic.login.failed", null, this.retrieveLocaleAudit(callerId)));
		}
		
		//cek Active Subsystem 
		if (!("1").equals(bean.getAmMssubsystem().getIsActive())) {
			throw new ServicesUserException(
					this.messageSource.getMessage("businesslogic.login.inactivesubsystem", new Object[]{loginId}, this.retrieveLocaleAudit(callerId)));
		}			
			               
		Integer failCount = bean.getFailCount();
		String isLocked = bean.getIsLocked();
		String isLoggedIn = bean.getIsLoggedIn();
		if (failCount == null) { failCount = 0; }
		if (StringUtils.isBlank(isLocked)) { isLocked = "0"; }
		if (StringUtils.isBlank(isLoggedIn)) { isLoggedIn = "0"; }
		
		if (PropertiesHelper.isBypassLogin()) {
			canLogin = "1";
		}
		else {
			if (GlobalVal.FLAG_LOGIN_PROVIDER_NC.equals(bean.getLoginProvider())){
				boolean result = intFormLogic.authenticateUser(bean, password);
				canLogin = (result) ? "1" : "0";
			}
			else if (GlobalVal.FLAG_LOGIN_PROVIDER_AD.equals(bean.getLoginProvider())) {
			    boolean authenticated = this.ldapAuth(loginId, password);
			    canLogin = (authenticated) ? "1" : "0";
			}
			else {
				//cek username, password, dan imei, jika sama maka bisa login
				if (PasswordHash.validatePassword(password, bean.getPassword()) && loginId.toUpperCase().equals(bean.getLoginId().toUpperCase())){
					canLogin = "1";
				}
			}
		}
		
		//check freshInstall from mobile or not
		String generalFreshInstall = globalLogic.getGsValue(bean.getAmMssubsystem().getSubsystemName()+GlobalKey.GENERALSETTING_CHECK_FRESHINSTALL_MOBILE, callerId);
		flagFreshInstall = generalFreshInstall.equals("1")? flagFreshInstall : "0";
		
		//freshInstall=1 -> cek mode login
		if (!"1".equals(flagFreshInstall) && "1".equals(canLogin)) {
			String modeLogin = globalLogic.getGsValue(bean.getAmMssubsystem().getSubsystemName()+GlobalKey.GENERALSETTING_MODE_LOGIN_SUBMIT, callerId);
			if(modeLogin == null || "".equals(modeLogin)){
				modeLogin = GlobalVal.MODE_IMEI;
			}
			//if mode login == imei
			if(GlobalVal.MODE_IMEI.equalsIgnoreCase(modeLogin)){
				if(StringUtils.equalsIgnoreCase(imei, bean.getImei()) || StringUtils.equalsIgnoreCase(imei, bean.getImei2())){
					canLogin = "1";
				}
				else {
					throw new ServicesUserException(this.messageSource.getMessage("businesslogic.global.imeinotvalid", new Object[]{imei}, this.retrieveLocaleAudit(callerId)));
				}
			}
			else if(GlobalVal.MODE_ANDROID_ID.equalsIgnoreCase(modeLogin)){
				if(StringUtils.equalsIgnoreCase(androidId, bean.getAndroidId())) {
					canLogin = "1";
				}
				else {
					throw new ServicesUserException(this.messageSource.getMessage("businesslogic.global.androididnotvalid", null, this.retrieveLocaleAudit(callerId)));
				}
			}
		}
		
		if ("1".equals(canLogin)) {
		    //cek is field person
            if(!"1".equals(bean.getMsJob().getIsFieldPerson())){
                throw new ServicesUserException(
                		this.messageSource.getMessage("businesslogic.login.notfieldperson", new Object[]{loginId}, this.retrieveLocaleAudit(callerId)));
            }
            
            //user is locked or not
            if ("1".equals(isLocked)) {
                throw new ServicesUserException(
                		this.messageSource.getMessage("businesslogic.login.locked", new Object[]{loginId}, this.retrieveLocaleAudit(callerId)));
            }
                    
            //cek user aktif atau ga
            if (!"1".equals(bean.getIsActive())) {
                throw new LoginException(
                		this.messageSource.getMessage("businesslogic.login.inactive", new Object[]{loginId}, this.retrieveLocaleAudit(callerId)),
                		Reason.LOGIN_INACTIVE);
            }
            
//                if ("1".equals(isLoggedIn)) {
//                    throw new ServicesUserException("Login failed. User "+ loginId + " already logged-in");
//                }
            
            if(GlobalVal.FLAG_LOGIN_PROVIDER_DB.equals(bean.getLoginProvider())){
				if (this.isPwdExpired(bean.getUuidMsUser(), callerId)){
					bean.setIsPasswordExpired("1");
					bean.setChangePwdLogin("1");
					bean.setLastExpired(new Date());
					bean.setDtmUpd(new Date());
					bean.setUsrUpd(uuid);
				}
            }
            
			//updateLoginUser(bean, deviceInformation, callerId);
            Date currentDate = new Date();
			bean.setDtmUpd(currentDate);
			bean.setUsrUpd(uuid);
			bean.setDeviceInfo(StringUtils.join(deviceInformation.getOs(), " ", deviceInformation.getOsVersion(),
					" ", deviceInformation.getDeviceId(), " ", deviceInformation.getDeviceModel(),
					" ", deviceInformation.getApplication(), " ", deviceInformation.getApplicationVersion()));
				
			//sukses login - update status login
			bean.setLastLoggedIn(currentDate);
			bean.setFailCount(0);
			bean.setIsLoggedIn("1");
			
			//update imei dan android ID jika flagFreshInstall = 1
			if ("1".equals(flagFreshInstall)) {
				bean.setImei(imei);
				bean.setAndroidId(androidId);
			}
			
			this.auditManager.auditEdit(bean, auditInfo, callerId.getCallerId(), "");
			this.getManagerDAO().update(bean);
			
			
			Object[][] paramUpdateMulti = { {"uuidUser", bean.getUuidMsUser()}, 
					{"imei", imei},
					{"androidId", androidId},
					{"uniqueId", bean.getUniqueId()} };
			String queryUpdateMulti = "UPDATE AM_MSUSER set IMEI = :imei, ANDROID_ID = :androidId"
										+ "	where UNIQUE_ID = :uniqueId"
										+ " and IS_ACTIVE = 1"
										+ " and IS_DELETED = 0"
										+ " and UUID_MS_USER != :uuidUser";
			this.getManagerDAO().updateNativeString(queryUpdateMulti, paramUpdateMulti);
			
			this.commonLogic.insertLastLocationTimestamp(bean.getUuidMsUser(), bean.getAmMssubsystem().getUuidMsSubsystem(), currentDate, callerId);
		} 
		else {
			if ("0".equals(isLocked)) {
				failCount += 1;
				bean.setFailCount(failCount);
				bean.setLastLoggedFail(new Date());
				if (failCount >= maxFailCount) {
					bean.setIsLocked("1");
					bean.setLastLocked(new Date());
				}
				//update failCount
				bean.setDtmUpd(new Date());
				bean.setUsrUpd(uuid);
				this.getManagerDAO().update(bean);
			}
			throw new ServicesUserException(this.messageSource.getMessage("businesslogic.login.idpassword", null, this.retrieveLocaleAudit(callerId)));
		}
		return bean;
		
	}
	
	@Async
	@Override
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	public void sendMessageToJms(AmMsuser user, AuditContext auditContext){
		//SSE message		
		Gson gson = new Gson();
		MessageEventBean msgBean = new MessageEventBean();
		msgBean.setEvent(GlobalVal.EVENT_NEWS);
		msgBean.setUuidUser(user.getImei()+user.getUuidMsUser());
		msgBean.setUuidMessageBean(this.getManagerDAO().getUUID());
		Object[] msgArgs = {user.getLoginId(), user.getFullName()};
		msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.login", msgArgs, this.retrieveLocaleAudit(auditContext)));
		
		msgBean.setDate(DateFormatUtils.format(new Date(), "HH:mm:ss"));
		msgBean.setIsDetected("1");
		
		try{
			sendMessage(gson.toJson(msgBean));
			LOG.info("Send message to JMS Success");
		}catch(Exception e){
			LOG.error("Send message to JMS Failed", e);
		}
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	public List<MenuUserBean> listMenu(AmMsuser loginBean, AuditContext callerId){
		List<MenuUserBean> listMenu = new ArrayList<MenuUserBean>();		
		Long uuid = loginBean.getUuidMsUser();
		if (uuid != null) {
			Object[][] paramsMemberofGroup = {{Restrictions.eq("amMsuser.uuidMsUser", uuid)}};
			Map amMemberofGroup = this.getManagerDAO().selectAll(AmMemberofgroup.class, paramsMemberofGroup, null);
			Long[] uuidMsGroupArr = new Long[((Long) amMemberofGroup.get(GlobalKey.MAP_RESULT_SIZE)).intValue()];
			List<AmMemberofgroup> msGroupofUser = (List<AmMemberofgroup>) amMemberofGroup.get(GlobalKey.MAP_RESULT_LIST);
			for (int i = 0; i < msGroupofUser.size(); i++) {
				uuidMsGroupArr[i] = new Long(msGroupofUser.get(i).getAmMsgroup().getUuidMsGroup());
			}
			if ( msGroupofUser.isEmpty() ) {
				uuidMsGroupArr = new Long[1];
				uuidMsGroupArr[0] = 0L;
			}

            Object[][] params = {
            	{"uuidMsGroup", uuidMsGroupArr},
            	{"isActive", "1"}
            };
            Map map = this.getManagerDAO().list(
            		"from AmMobilemenuofgroup mm join fetch mm.amMsmobilemenu join fetch mm.amMsgroup " +
            		"where mm.amMsgroup.uuidMsGroup in :uuidMsGroup AND " +
            		"mm.amMsmobilemenu.isActive = :isActive AND " +
            		"mm.amMsgroup.isActive = :isActive", params);
            List listResult = (List) map.get("resultList");
            Iterator itr = listResult.iterator();
            while (itr.hasNext()) {
            	AmMobilemenuofgroup mp = (AmMobilemenuofgroup) itr.next();
                MenuUserBean ag = new MenuUserBean();
                ag.setUuidMenu(mp.getAmMsmobilemenu().getMenuPrompt());
                ag.setUuidUser(String.valueOf(uuid));
                listMenu.add(ag);
            }
		}
		return listMenu;
	}
	
	private boolean isPwdExpired (long uuidMsUser, AuditContext auditContext) {
		Object[][] params = { {"uuidMsUser", uuidMsUser} };
		Integer days = (Integer) this.getManagerDAO().selectOneNativeString(
			"SELECT DATEDIFF(DAY, MAX(DTM_CRT), CURRENT_TIMESTAMP) " +
			"from AM_USERPWDHISTORY " +
			"WHERE UUID_MS_USER = :uuidMsUser", params);
		
		days = (days == null) ? new Integer(0) : days;
		
		String generalSettingPwdExpired = globalLogic.getGsValue(
				GlobalKey.GENERALSETTING_PASSWORD_EXPIRED, auditContext);
		if (Integer.parseInt(generalSettingPwdExpired) < days){
			return true;
		}
		else{
			return false;		
		}
	}
	
    public boolean ldapAuth(String user, String password) throws LdapException {
        boolean ldapEnabled = BooleanUtils.toBoolean(SpringPropertiesUtils.getProperty(GlobalKey.LDAP_ENABLED));
        if (!ldapEnabled) {
            LOG.debug("LDAP Authentication disabled! Return true");
            return true;
        }
        
        return this.ldapLogic.authenticate(
        		SpringPropertiesUtils.getProperty(GlobalKey.LDAP_DOMAIN) + "\\" + user, password, false);
    }
    
    @Override
	public Status doResetPassword(String loginId, Date dob, String email, String ktpNo, AuditContext auditContext) {
		Status status = new Status();
		status.setCode(0);
		status.setMessage("Success Reset Password");
		intFormLogic.resetPassword(loginId, dob, email, ktpNo, auditContext);
		return status;
	}
    
    @Transactional(noRollbackFor={LoginException.class, ServicesUserException.class},
	        isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<LoginMultiUserBean> doMobileMultiLogin(String uniqueId, String password, AuditContext callerId) {
	
		if (StringUtils.isBlank(uniqueId) || StringUtils.isBlank(password)) {
			throw new LoginException("User ID or Password is required", Reason.LOGIN_REQUIRED);
		}
		
    	String[][] paramMultiLogin = { {"uniqueId", uniqueId} };
    	List<Map<String, Object>> listLoginId = this.getManagerDAO().selectAllNativeString("select LOGIN_ID from AM_MSUSER with (nolock) where UNIQUE_ID = :uniqueId and IS_ACTIVE = 1 and IS_DELETED = 0", paramMultiLogin);
    	if (listLoginId.size() == 1) {
    		String loginId = (String) this.getManagerDAO().selectOneNativeString("select LOGIN_ID from AM_MSUSER with (nolock) where UNIQUE_ID = '"+uniqueId+"' and IS_ACTIVE = 1 and IS_DELETED = 0", null);
    		List listUser = new ArrayList();
    		LoginMultiUserBean bean = new LoginMultiUserBean();
    		bean.setLoginId(loginId);
    		listUser.add(bean);
    		return listUser;
    	} else if (listLoginId.isEmpty()) {
    		throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
    	}
    	
    	Integer ttlIsActive = (Integer) this.getManagerDAO().selectOneNativeString("select COUNT(1) from AM_MSUSER with (nolock) where UNIQUE_ID = '"+uniqueId+"' and IS_ACTIVE = 1", null);
    	if (ttlIsActive == 0) {
    		throw new ServicesUserException(
            		this.messageSource.getMessage("businesslogic.login.inactive", new Object[]{uniqueId}, this.retrieveLocaleAudit(callerId)));
    	}
    	
    	Integer ttlIsNotLocked = (Integer) this.getManagerDAO().selectOneNativeString("select COUNT(1) from AM_MSUSER with (nolock) where UNIQUE_ID = '"+uniqueId+"' and IS_ACTIVE = 1 and IS_DELETED = 0 and IS_LOCKED != 1", null);
    	if (ttlIsNotLocked == 0) {
    		throw new ServicesUserException(
            		this.messageSource.getMessage("businesslogic.login.locked", new Object[]{uniqueId}, this.retrieveLocaleAudit(callerId)));
    	}
    	
    	if (listLoginId.isEmpty()) {
    		throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
    	}
    	
//    	String loginId = (String) this.getManagerDAO().selectOneNativeString("select top 1 LOGIN_ID from MS_CONFIGUSERLOGIN with (nolock) where NIK = :nik and ACTIVE = 1", paramMultiLogin);
    	String loginId = (String) listLoginId.get(0).get("d0");
    	if (null == loginId) {
    		throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
    	}
    	
		Object[][] params = {{Restrictions.eq("loginId", StringUtils.upperCase(loginId))}};
		AmMsuser bean = this.getManagerDAO().selectOne(AmMsuser.class, params);
		            
        //cek Login ID
        if (bean == null) {
        	throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
        }
        
        this.getManagerDAO().fetch(bean.getAmMssubsystem());
        
        if (!("1").equals(bean.getAmMssubsystem().getIsActive())) {
			throw new LoginException("ID " + bean.getLoginId()
					+ " is registered in inactive subsystem. Please contact your Administrator.", 
					Reason.LOGIN_INACTIVE);
		}
		
		String canLogin = "0";
		String gsValue = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MAX_FAIL_COUNT, callerId);
		Integer maxFailCount =  Integer.parseInt(gsValue);
		
		this.getManagerDAO().fetch(bean.getAmMssubsystem());
        this.getManagerDAO().fetch(bean.getMsJob());
        
        Integer failCount = bean.getFailCount();
		if (failCount == null) { failCount = 0; }
               
		if (PropertiesHelper.isBypassLogin()) {
			canLogin = "1";
		}
		else {
			if (GlobalVal.FLAG_LOGIN_PROVIDER_NC.equals(bean.getLoginProvider())){
				boolean result = intFormLogic.authenticateUser(bean, password);
				canLogin = (result) ? "1" : "0";
			}
			else if (GlobalVal.FLAG_LOGIN_PROVIDER_AD.equals(bean.getLoginProvider())) {
			    boolean authenticated = this.ldapAuth(uniqueId, password);
			    canLogin = (authenticated) ? "1" : "0";
			}
			else {
				//cek username, password, dan imei, jika sama maka bisa login
				if (PasswordHash.validatePassword(password, bean.getPassword()) && loginId.toUpperCase().equals(bean.getLoginId().toUpperCase())){
					canLogin = "1";
				}
			}
		}
		
		String[] arrId = new String[listLoginId.size()];
		for (int i=0; i<listLoginId.size(); i++ ) {
			Map mapId = listLoginId.get(i);
			arrId[i] = (String) mapId.get("d0");
		}
		
		Object[][] paramsUser = { {Restrictions.in("loginId", arrId)} };
		Map mapUserLogin = this.getManagerDAO().selectAll(AmMsuser.class, paramsUser, null);
		List<AmMsuser> listUserLogin = (List) mapUserLogin.get(GlobalKey.MAP_RESULT_LIST);
		
		boolean isPwdExp = false;
		if (this.isPwdExpired(bean.getUuidMsUser(), callerId) && !bean.getLoginProvider().equals(GlobalVal.NC)) {				
			isPwdExp = true;
		}
		
		List listUser = new ArrayList();
		if ("1".equals(canLogin)) {
			List<Map<String, Object>> listMapUserLogin = this.getManagerDAO().selectAllNative("am.login.getMultiLogin", paramMultiLogin, null);
			if (!listMapUserLogin.isEmpty()) {
				for (Map<String, Object> mp : listMapUserLogin) {
					String name = StringUtils.EMPTY;
					String branchCode = StringUtils.trim((String) mp.get("d1"));
					String branchName = StringUtils.trim((String) mp.get("d2"));
					name = branchName + " (" + branchCode + ") " + " - " + StringUtils.trim((String) mp.get("d3"));
					LoginMultiUserBean userBean = new LoginMultiUserBean();
					userBean.setLoginId((String) mp.get("d0"));
					userBean.setBranchId(name);
					listUser.add(userBean);
				}
			}
			
			for (AmMsuser usr : listUserLogin) {
				usr.setDtmUpd(new Date());
				usr.setUsrUpd(String.valueOf(usr.getUuidMsUser()));
				usr.setIsLocked("0");
				usr.setFailCount(0);
				
				if (isPwdExp) {
					usr.setIsPasswordExpired("1");
					usr.setChangePwdLogin("1");
				}
				this.getManagerDAO().update(usr);
			}
		} else {
			Date dateFailed = new Date();
			boolean isUserLocked = false;
			for (AmMsuser usr : listUserLogin) {
				if (isPwdExp) {
					usr.setIsPasswordExpired("1");
					usr.setChangePwdLogin("1");
				}
				
				if (Integer.compare(maxFailCount-1, failCount) > 0) {
					usr.setFailCount((failCount == null ? 1 : failCount + 1));
					usr.setPrevLoggedFail(usr.getLastLoggedFail());
					usr.setLastLoggedFail(dateFailed);
				} else {
					isUserLocked = true;
					usr.setIsLocked("1");
					usr.setFailCount((failCount == null ? 1 : failCount + 1));
					usr.setPrevLoggedFail(usr.getLastLoggedFail());
					usr.setLastLoggedFail(dateFailed);
				}
				//update failCount
				usr.setDtmUpd(new Date());
				usr.setUsrUpd(String.valueOf(usr.getUuidMsUser()));
				this.getManagerDAO().update(usr);
			}
			
			if (isUserLocked) {
				throw new ServicesUserException(
	            		this.messageSource.getMessage("businesslogic.login.locked", new Object[]{uniqueId}, this.retrieveLocaleAudit(callerId)));
			}

			throw new LoginException("Invalid ID or Password", Reason.LOGIN_INVALID);
		}
		return listUser;
	}
}
