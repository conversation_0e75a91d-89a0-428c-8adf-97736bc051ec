package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Scanner;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Row;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LovLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.exceptions.LovException;
import com.adins.mss.exceptions.LovException.Reason;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsBranchoflov;
import com.adins.mss.model.MsCollectionresult;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.custom.LovBean;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true,isolation=Isolation.READ_UNCOMMITTED)
public class GenericLovLogic extends BaseLogic implements LovLogic, MessageSourceAware {
	private AuditInfo auditInfo;
	private AuditInfo auditInfoDoL;
	
	@Autowired
	private MessageSource messageSource;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public GenericLovLogic() {
		String[] pkCols = { "uuidLov" };
		String[] pkDbCols = { "UUID_LOV" };
		String[] cols = { "uuidLov", "isActive", "isDeleted", "lovGroup", "code",
				"description", "sequence", "constraint1", "constraint2",
				"constraint3", "constraint4", "constraint5" };
		String[] dbCols = { "UUID_LOV", "IS_ACTIVE", "IS_DELETED", "LOV_GROUP", "CODE",
				"DESCRIPTION", "SEQUENCE", "CONSTRAINT_1", "CONSTRAINT_2",
				"CONSTRAINT_3", "CONSTRAINT_4", "CONSTRAINT_5" };
		this.auditInfo = new AuditInfo("MS_LOV", pkCols, pkDbCols, cols, dbCols);
		
		String[] pkColsDoL = { "seqno" };
		String[] pkDbColsDoL = { "SEQNO" };
		String[] colsDoL = { "seqno", "msLov.uuidLov", "msBranch.uuidBranch" };
		String[] dbColsDoL = { "SEQNO", "UUID_LOV", "UUID_BRANCH" };
		this.auditInfoDoL = new AuditInfo("MS_BRANCHOFLOV", pkColsDoL, pkDbColsDoL, colsDoL, dbColsDoL);
	}

    private static final Logger LOG = LoggerFactory.getLogger(GenericLovLogic.class);
	private static final String[] TEMPLATE_HEADER = { "LOV Group*", "Key*",
			"Description*", "Sequence*", "Active*", "Constraint1", "Constraint2",
			"Constraint3", "Constraint4", "Constraint5", "Branch Code(Ex:CODE1, CODE2, etc)" };
	private static final int[] HEADER_COLUMN_WIDTH = { 30 * 256, 20 * 256,
			20 * 256, 30 * 256, 20 * 256, 30 * 256, 20 * 256, 20 * 256,
			30 * 256, 20 * 256, 20 * 256 };

	private static final String[] TEMPLATE_HEADER_ERROR = { "LOV Group*", "Key*",
			"Description*", "Sequence*", "Active*", "Constraint1", "Constraint2",
			"Constraint3", "Constraint4", "Constraint5", "Branch Code(Ex:CODE1, CODE2, etc)", "Error" };
	private static final int[] HEADER_COLUMN_WIDTH_ERROR = { 30 * 256,
			20 * 256, 20 * 256, 30 * 256, 20 * 256, 30 * 256, 20 * 256,
			20 * 256, 30 * 256, 20 * 256, 20 * 256, 80 * 256 };

	@Override
	public MsLov getLov(long params, AuditContext callerId) {
		MsLov result = this.getManagerDAO().selectOne(MsLov.class, params);
		return result;
	}

	@Override
	public Map<String, Object> listLov(Object params, Object orders,
			int pageNumber, int pageSize, AuditContext callerId) {
		int indexParam = 0;
		String[][] tmpParams = (String[][]) params;
		Object[][] queryParams = new Object[tmpParams.length][1];
		if(StringUtils.isNotBlank(tmpParams[0][1]) && !"%".equals(tmpParams[0][1])){
			queryParams[indexParam++][0] = Restrictions.like(tmpParams[0][0], "%"+tmpParams[0][1]+"%");
		}
		else 
			queryParams[indexParam++][0] = Restrictions.like(tmpParams[0][0], "%");
		
		if(StringUtils.isNotBlank(tmpParams[1][1])){
			queryParams[indexParam++][0] = Restrictions.like(tmpParams[1][0], "%"+tmpParams[1][1]+"%");
		}
		
		if(StringUtils.isNotBlank(tmpParams[2][1])){
			queryParams[indexParam++][0] = Restrictions.like(tmpParams[2][0], "%"+tmpParams[2][1]+"%");
		}
		queryParams[indexParam++][0] = Restrictions.eq(tmpParams[3][0], tmpParams[3][1]);
		
		Map<String, Object>  result = this.getManagerDAO().selectAll(MsLov.class, queryParams,
				orders, pageNumber, pageSize);
		return result;
	}

	@Override
	public List listLovByNative(String[][] params, AuditContext callerId) {
		List listLov = this.getManagerDAO().selectAllNative("eform.lov.getListLovByLovGroupSync", params, null);
		return listLov;
	}

	@Override
	public Integer countLovByNative(String[][] params, AuditContext callerId) {
		Integer total = (Integer) this.getManagerDAO().selectOneNative("eform.lov.getListLovByLovGroupSyncCount", params);
		return total;
	}
	
	@Override
	public List<Map<String, Object>> listGroup(AuditContext callerId) {
		List group = new ArrayList();
		List listGroup = this.getManagerDAO().selectAllNative(
				"eform.lov.getGroupLov", null, null);

		if (!listGroup.isEmpty()) {
			for (int x = 0; x < listGroup.size(); x++) {
				Map map = (Map) listGroup.get(x);
				group.add(map.get("d0"));
			}
		}
		return group;
	}
	
	@Override
	public List<Map<String, Object>> listGroupSync(AuditContext callerId) {
		List group = new ArrayList();
		List listGroup = this.getManagerDAO().selectAllNative(
				"eform.lov.getGroupLovSync", null, null);

		if (!listGroup.isEmpty()) {
			for (int x = 0; x < listGroup.size(); x++) {
				Map map = (Map) listGroup.get(x);
				group.add(map.get("d0"));
			}
		}
		return group;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateLov(MsLov msl, AuditContext callerId) {
		String uuidLov = this.getLovExist(msl);
		if(StringUtils.isNotEmpty(uuidLov) && !uuidLov.equals(String.valueOf(msl.getUuidLov()))){
			throw new EntityNotUniqueException("Key "+msl.getCode()+" already exist", msl.getCode());
		}
		else{
			MsLov dbModel = this.getManagerDAO().selectOne(MsLov.class, msl.getUuidLov());
			
			dbModel.setDescription(msl.getDescription());
			dbModel.setSequence(msl.getSequence());
			dbModel.setIsActive(msl.getIsActive());
			dbModel.setIsDeleted("0");
			dbModel.setConstraint1(StringUtils.trimToNull(msl.getConstraint1()));
			dbModel.setConstraint2(StringUtils.trimToNull(msl.getConstraint2()));
			dbModel.setConstraint3(StringUtils.trimToNull(msl.getConstraint3()));
			dbModel.setConstraint4(StringUtils.trimToNull(msl.getConstraint4()));
			dbModel.setConstraint5(StringUtils.trimToNull(msl.getConstraint5()));
			dbModel.setUsrUpd(callerId.getCallerId());
			dbModel.setDtmUpd(new Date());

			this.auditManager.auditEdit(dbModel, auditInfo, callerId.getCallerId(), "");
			this.getManagerDAO().update(dbModel);
			
			if((GlobalVal.LOV_TAG_COLLECTION_RESULT).equalsIgnoreCase(msl.getLovGroup())) {
				Object[][] paramCode = { {Restrictions.eq("resultCode", msl.getCode())} };
				MsCollectionresult dbColl = this.getManagerDAO().selectOne(MsCollectionresult.class, paramCode);
				dbColl.setResultDesc(msl.getDescription());
				dbColl.setIsActive(msl.getIsActive());
				
				this.getManagerDAO().update(dbColl);
			}
		}
		
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertLov(MsLov msl, AuditContext callerId) {
		String uuidLov = this.getLovExist(msl);
		if ( StringUtils.isBlank(uuidLov) ) {
			msl.setLovGroup(msl.getLovGroup().toUpperCase());
			msl.setIsActive(msl.getIsActive());
			msl.setIsDeleted("0");
			msl.setDtmCrt(new Date());
			msl.setUsrCrt(callerId.getCallerId().toString());

			// set null if empty
			if ("".equals(msl.getConstraint1())) {
				msl.setConstraint1(null);
			}
			if ("".equals(msl.getConstraint2())) {
				msl.setConstraint2(null);
			}
			if ("".equals(msl.getConstraint3())) {
				msl.setConstraint3(null);
			}
			if ("".equals(msl.getConstraint4())) {
				msl.setConstraint4(null);
			}
			if ("".equals(msl.getConstraint5())) {
				msl.setConstraint5(null);
			}

			this.getManagerDAO().insert(msl);
			this.auditManager.auditAdd(msl, auditInfo, callerId.getCallerId(), "");
		}
		else {
			MsLov lov = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(uuidLov));
			if("1".equals(lov.getIsDeleted())){
				lov.setIsActive(msl.getIsActive());
				lov.setIsDeleted("0");
				lov.setDtmUpd(new Date());
				lov.setUsrUpd(callerId.getCallerId().toString());
				lov.setLovGroup(msl.getLovGroup());
				lov.setDescription(msl.getDescription());
				lov.setSequence(msl.getSequence());
				// set null if empty
				if ("".equals(msl.getConstraint1())) {
					lov.setConstraint1(null);
				}
				else{
					lov.setConstraint1(msl.getConstraint1());
				}
				if ("".equals(msl.getConstraint2())) {
					lov.setConstraint2(null);
				}
				else{
					lov.setConstraint2(msl.getConstraint2());
				}
				if ("".equals(msl.getConstraint3())) {
					lov.setConstraint3(null);
				}
				else{
					lov.setConstraint3(msl.getConstraint3());
				}
				if ("".equals(msl.getConstraint4())) {
					lov.setConstraint4(null);
				}
				else{
					lov.setConstraint4(msl.getConstraint4());
				}
				if ("".equals(msl.getConstraint5())) {
					lov.setConstraint5(null);
				}
				else{
					lov.setConstraint5(msl.getConstraint5());
				}
				this.auditManager.auditEdit(lov, auditInfo, callerId.getCallerId(), "");
				this.getManagerDAO().update(lov);
			}
			else{
				//ubah jadi update lov
				lov.setSequence(msl.getSequence());
				lov.setDescription(msl.getDescription());
				lov.setIsActive(msl.getIsActive());
				lov.setIsDeleted("0");
				lov.setDtmUpd(new Date());
				lov.setUsrUpd(callerId.getCallerId().toString());
				this.auditManager.auditEdit(lov, auditInfo, callerId.getCallerId(), "");
				this.getManagerDAO().update(lov);
			}
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteLov(long uuid, AuditContext callerId) {
		MsLov msl = this.getManagerDAO().selectOne(MsLov.class, uuid);
		msl.setIsActive("0");
		msl.setIsDeleted("1");
		msl.setUsrUpd(callerId.getCallerId());
		msl.setDtmUpd(new Date());
		
		this.auditManager.auditEdit(msl, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(msl);
	}
	
	@Override
	public Map<String, Object> listMemberOfLov(long uuidLov, String[][] orders, 
			int pageNumber, int pageSize,  AuditContext callerId) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and mbol.msLov.uuidLov=:uuidLov");
		condition.append(" and mbol.isDeleted=:isDeleted");
		paramMap.put("uuidLov", uuidLov);
		paramMap.put("isDeleted", "0");
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mbol.").append(orders[0][0]).append(" ").append(orders[0][1]);
		orderQuery.append(" ,mbol.").append(orders[1][0]).append(" ").append(orders[1][1]);
		
		Map<String, Object> result = this.getManagerDAO().selectAll(
				"from MsBranchoflov mbol join fetch mbol.msLov join fetch mbol.msBranch where 1=1"
						+ condition.toString() + orderQuery,
				paramMap);
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteMemberOfLov(long seqNo, AuditContext callerId) {
		
		Object[][] seqno = { { Restrictions.eq("seqno", seqNo) } };
		MsBranchoflov msBranchoflov = this.getManagerDAO().selectOne(
				MsBranchoflov.class, seqno);
		
		if (msBranchoflov != null) {
			msBranchoflov.setIsDeleted("1");
			msBranchoflov.setUsrUpd(callerId.getCallerId());
			msBranchoflov.setDtmUpd(new Date());
			this.getManagerDAO().update(msBranchoflov);	
		}
	}

	@Override
	public List listBranch(String[][] params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("eform.lov.listBranch", params, null);
		return result;
	}

	@Override
	public Integer countListBranch(Object params, AuditContext callerId) {
		Integer result = (Integer)this.getManagerDAO().selectOneNative("eform.lov.cntBranch", params);	
		return result;
	}
	

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertBranchOfLov(long uuidLov, String[] uuidBranches, AuditContext callerId) {
		MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, uuidLov);
					
		for(int i = 0; i < uuidBranches.length; i++) {
			if(String.valueOf(uuidBranches[i]).isEmpty()) continue;
			long uuidBranch = Long.parseLong(uuidBranches[i]);
			
			MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, uuidBranch);
			 
			Object[][] params = {{Restrictions.eq("msLov.uuidLov", msLov.getUuidLov())},
								{Restrictions.eq("msBranch.uuidBranch", msBranch.getUuidBranch())}};
			MsBranchoflov cekMsBoL = this.getManagerDAO().selectOne(MsBranchoflov.class, params);
							
			if(cekMsBoL != null) {
				Object[][] seqno = {{Restrictions.eq("seqno", new Long(cekMsBoL.getSeqno()))}};
				MsBranchoflov msBOL = this.getManagerDAO().selectOne(MsBranchoflov.class, seqno);
				msBOL.setUsrCrt(callerId.getCallerId());
				msBOL.setDtmCrt(new Date());
				msBOL.setUsrUpd(null);
				msBOL.setDtmUpd(null);
				msBOL.setIsDeleted("0");
				this.getManagerDAO().update(msBOL);
			} 
			else {
				MsBranchoflov branchOfLov = new MsBranchoflov();
				branchOfLov.setMsLov(msLov);
				branchOfLov.setMsBranch(msBranch);
				branchOfLov.setUsrCrt(callerId.getCallerId());
				branchOfLov.setDtmCrt(new Date());
				branchOfLov.setIsDeleted("0");
				this.getManagerDAO().insert(branchOfLov);
				this.auditManager.auditAdd(branchOfLov, auditInfoDoL, callerId.getCallerId(), "");
			}
		}
	}
	
	public int getSeqMsLovOfBranch(String uuidLov) {			
		Object[][] param = {{Restrictions.eq("lovGroup", GlobalVal.LOV_TAG_BRANCH_OF_LOV)},
				{Restrictions.eq("constraint1", uuidLov)}};
		Map<String, Object> map = this.getManagerDAO().count(MsLov.class, param);
		Long result = (Long) map.get(GlobalKey.MAP_RESULT_SIZE);
		return result.intValue()+1;
	}

	@Override
	public byte[] exportExcel(AuditContext callerId) {

		HSSFWorkbook workbook = this.createXlsTemplate();
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	public HSSFWorkbook createXlsTemplate() {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Lov List");
			this.createHeader(workbook, sheet);
			this.setTextDataFormat(workbook, sheet);
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createHeader(HSSFWorkbook workbook, HSSFSheet sheet) {
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);

		HSSFRow row = sheet.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			HSSFCell cell = row.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER[i]);
			cell.setCellStyle(style);
			sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH[i]);
		}
	}

	private void setTextDataFormat(HSSFWorkbook workbook, HSSFSheet sheet) {
		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("General"));

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			sheet.setDefaultColumnStyle(i, style);
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public byte[] processSpreadSheetFile(File uploadedFile, AuditContext callerId) {
		byte[] errorUploadByte = null;

		Map parseMap;
		try {
			parseMap = this.parseSpreadsheetToLovBeans(uploadedFile,callerId);
		} catch (IOException e) {
			errorUploadByte = this.errorUpload();
			return errorUploadByte;
		}
		
		List listofLov = (List) parseMap.get("result");
		Map mapBranchofLov = (Map) parseMap.get("resultBranchoflov");
		List listofErrorLov = (List) parseMap.get("resultError");
		List branchCode = (List) parseMap.get("branchCode");
		List <LovBean> lovBean = (List) parseMap.get("lovBean");

		for (Iterator iterator = listofLov.iterator(); iterator.hasNext(); ) {
			MsLov msl = (MsLov) iterator.next();				
			String uuidExist = this.getLovExist(msl);
			// check existing identical data
			if (StringUtils.isNotBlank(uuidExist)) {
				// update to active
				MsLov dbModel = this.getManagerDAO().selectOne( MsLov.class, Long.valueOf(uuidExist) );
				
				dbModel.setSequence(msl.getSequence());
				dbModel.setDescription(msl.getDescription());
				dbModel.setIsActive(msl.getIsActive());
				dbModel.setIsDeleted("0");
				dbModel.setUsrUpd(callerId.getCallerId());
				dbModel.setDtmUpd(new Date());

				this.auditManager.auditEdit(dbModel, auditInfo, callerId.getCallerId(), "");
				this.getManagerDAO().update(dbModel);
				
				Object[][] params = { { Restrictions.eq("msLov.uuidLov", Long.valueOf(uuidExist)) } };
				Map<String, Object> MapMsbol = this.getManagerDAO().selectAll(MsBranchoflov.class, params, null);
				List tempMsbol = (List) MapMsbol.get(GlobalKey.MAP_RESULT_LIST);
				for(int i=0; i<tempMsbol.size(); i++){
					MsBranchoflov msbol = (MsBranchoflov) tempMsbol.get(i);	
					msbol.setDtmCrt(new Date());
					msbol.setUsrCrt(callerId.getCallerId());	
					msbol.setIsDeleted("1");
					this.getManagerDAO().update(msbol);
				}
				
				List listBranchofLov = (List) mapBranchofLov.get(msl.getCode().toLowerCase()); 
				for(int i=0; i<listBranchofLov.size(); i++){
					MsBranchoflov msbol = (MsBranchoflov) listBranchofLov.get(i);
					Object[][] paramCode = { {Restrictions.eq("msBranch.uuidBranch", msbol.getMsBranch().getUuidBranch())},
											{Restrictions.eq("msLov.uuidLov", Long.valueOf(uuidExist))} };
					MsBranchoflov cekMsbol = this.getManagerDAO().selectOne( MsBranchoflov.class, paramCode );
					if(cekMsbol == null){
						msbol.setMsLov(dbModel);
						msbol.setDtmCrt(new Date());
						msbol.setUsrCrt(callerId.getCallerId());								
						msbol.setIsDeleted("0");
						this.getManagerDAO().insert(msbol);
					}
					else{
						cekMsbol.setIsDeleted("0");
						cekMsbol.setDtmUpd(new Date());
						cekMsbol.setUsrUpd(callerId.getCallerId());
						this.getManagerDAO().update(cekMsbol);
					}
				}
				
			} 
			else if (StringUtils.isBlank(uuidExist)) {
				// insert new
				if ( 80 >= msl.getCode().length() ){
					insertLov(msl, callerId);
					List listBranchofLov = (List) mapBranchofLov.get(msl.getCode().toLowerCase()); 
					for(int i=0; i<listBranchofLov.size(); i++){
						MsBranchoflov msbol = (MsBranchoflov) listBranchofLov.get(i);
						msbol.setMsLov(msl);
						msbol.setDtmCrt(new Date());
						msbol.setUsrCrt(callerId.getCallerId());								
						msbol.setIsDeleted("0");
						this.getManagerDAO().insert(msbol);
					}
				}
				else {
					listofErrorLov.add(msl);
				}
			}
		}
		if (!listofErrorLov.isEmpty()) {
			try {
				errorUploadByte = exportErrorLov(listofErrorLov, branchCode,
						listofLov.size(), listofErrorLov.size(), lovBean, callerId);
			} catch (SQLException e) {
				throw new LovException("Error generating XLS", Reason.ERROR_GENERATE);
			}
		}
		return errorUploadByte;
	}
	
	//error condition untuk di action
	private byte[] errorUpload(){
		byte[] tmp = new byte[1];
		tmp[0]=1;//for condition in action
		return tmp;
	}

	public String getLovExist(MsLov msl) {
		String lovGroup = StringUtils.trimToNull(msl.getLovGroup());
		String code = StringUtils.trimToNull(msl.getCode());
		String constraint1 = StringUtils.trimToNull(msl.getConstraint1());
		String constraint2 = StringUtils.trimToNull(msl.getConstraint2());
		String constraint3 = StringUtils.trimToNull(msl.getConstraint3());
		String constraint4 = StringUtils.trimToNull(msl.getConstraint4());
		String constraint5 = StringUtils.trimToNull(msl.getConstraint5());

		Object[] cons1 = (constraint1 == null) ? new Object[] { Restrictions.isNull("constraint1") }
		        : new Object[] { Restrictions.eq("constraint1", constraint1) };
		
		Object[] cons2 = (constraint2 == null) ? new Object[] { Restrictions.isNull("constraint2") }
		        : new Object[] { Restrictions.eq("constraint2", constraint2) };
		
		Object[] cons3 = (constraint3 == null) ? new Object[] { Restrictions.isNull("constraint3") }
		        : new Object[] { Restrictions.eq("constraint3", constraint3) };
		
		Object[] cons4 = (constraint4 == null) ? new Object[] { Restrictions.isNull("constraint4") }
		        : new Object[] { Restrictions.eq("constraint4", constraint4) };
		
		Object[] cons5 = (constraint5 == null) ? new Object[] { Restrictions.isNull("constraint5") }
		        : new Object[] { Restrictions.eq("constraint5", constraint5) };		

		Object[][] queryParams = { { Restrictions.eq("lovGroup", lovGroup) },
				{ Restrictions.eq("code", code) },
				cons1, cons2, cons3, cons4, cons5 };

		MsLov result = this.getManagerDAO().selectOne(MsLov.class, queryParams);

		String uuid = "";
		if (result != null) {
			uuid = String.valueOf(result.getUuidLov());
		}
		
		return uuid;
	}

	private Map parseSpreadsheetToLovBeans( File uploadedFile, AuditContext callerId ) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsLov> result = new ArrayList<>();
		List<MsLov> resultError = new ArrayList<>();
		List<String> branchCode = new ArrayList<>();
		List <LovBean> lovBean = new ArrayList<>();
		Map<String, Object> resultBranchoflov = new HashMap<>();

		FileInputStream inputStream = new FileInputStream(uploadedFile);
		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		HSSFSheet sheet = wb.getSheetAt(0);
		try{
			//cek header
			HSSFRow rowHeader = sheet.getRow(0);
			for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
				HSSFCell cell = rowHeader.getCell(i);
				if(StringUtils.isEmpty(cell.getStringCellValue())){
					wb.close();
					throw new IOException();
				}else if(!TEMPLATE_HEADER[i].equalsIgnoreCase(cell.getStringCellValue())){
					wb.close();
					throw new IOException();
				}
			}
			//end cek header
		
			int rows = sheet.getPhysicalNumberOfRows();
			for (int r = 1; r < rows; r++) {
				LovBean lovErrorBean = new LovBean();
				String branch = "";
				HSSFRow row = sheet.getRow(r);
				if (row == null) {
					continue;
				}
				
	    		boolean isEmptyRow = checkEmptyRow(row); 
	    				
	    		if (isEmptyRow == true){
	    			continue;
	    		}
	    		
				MsLov msl = new MsLov();
				List<MsBranchoflov> listmsbol = new ArrayList<>();
				for (int c = 0; c < 11; c++) {
					HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
									
					String value = "";
					long numberValue = -1;
					
					// if intValue -1, then sequence is posted with string
					msl.setDtmCrt(new Date());
					msl.setUsrCrt(callerId.getCallerId().toString());
	
					if (cell != null) {
						switch (cell.getCellType()) {
	    					case HSSFCell.CELL_TYPE_NUMERIC:
	    						value = String.valueOf((long) cell.getNumericCellValue());
	    						numberValue = Long.valueOf(value).longValue();
	    						break;
	    
	    					case HSSFCell.CELL_TYPE_STRING:
	    						value = cell.getStringCellValue();
	    						break;
	    
	    					default:
						}
					}
					else{
						numberValue = -2;
					}
	
					switch (c) {
	    				case 0:
	    					msl.setLovGroup(value);
	    					break;
	    				case 1:
	    					msl.setCode(value);
	    					break;
	    				case 2:
	    					msl.setDescription(value);
	    					break;
	    				case 3:
	    					msl.setSequence((int) numberValue);
	    					break;
	    				case 4:
	    					msl.setIsActive(value);
	    					break;
	    				case 5:
	    					msl.setConstraint1(value);
	    					break;
	    				case 6:
	    					msl.setConstraint2(value);
	    					break;
	    				case 7:
	    					msl.setConstraint3(value);
	    					break;
	    				case 8:
	    					msl.setConstraint4(value);
	    					break;
	    				case 9:
	    					msl.setConstraint5(value);
	    					break;
	    				case 10:
	    					branch = value;
	    					String input = value;
	    					Scanner scan = new Scanner(input).useDelimiter(",");
	    					while(scan.hasNext()) {
	    						MsBranchoflov msbol = new MsBranchoflov();
	    						String next = scan.next().replace(" ", "");
	    						Object[][] paramCode = { {Restrictions.eq("branchCode", next)} };
	    						MsBranch msb = this.getManagerDAO().selectOne(MsBranch.class, paramCode);
	    						if(msb != null){
	    							msbol.setMsBranch(msb);
	    							listmsbol.add(msbol);
	    						}
	    					}
	    					break;
					}
					
				}
	
				StringBuilder errorText = checkingLovUpload(msl, callerId);
				StringBuilder errorBranchText = checkingBranch(branch, callerId);
				if (errorText.length() == 0 && errorBranchText.length()== 0) {
					result.add(msl);
					resultBranchoflov.put(msl.getCode().toLowerCase(),listmsbol);
				}
				else {
					if(errorText.length() > 0){
						resultError.add(msl);
						branchCode.add(branch);
					} 
					if (errorBranchText.length() > 0) {
						if(errorText.length() > 0 && errorBranchText.length() > 0){
							if (!("").equals(errorText.toString())) {
								errorText.append(" | ");
							}
							resultError.add(msl);
							errorText.append(errorBranchText);
							branchCode.add(branch);
						} else {
							resultError.add(msl);
							resultBranchoflov.put(msl.getCode().toLowerCase(),listmsbol);
							errorText.append(errorBranchText);
							branchCode.add(branch);
						}
					}
					lovErrorBean.setErrorText(errorText.toString());
					lovBean.add(lovErrorBean);
				}
			}
		
			paramParse.put("result", result);
			paramParse.put("resultError", resultError);
			paramParse.put("resultBranchoflov", resultBranchoflov);
			paramParse.put("branchCode", branchCode);
			paramParse.put("lovBean", lovBean);
		}finally{
			wb.close();
		}
		return paramParse;
	}

	private boolean checkEmptyRow(HSSFRow row) {
		String[] isEmptyCell = new String [10]  ;
		Arrays.fill(isEmptyCell, "");

		for (int c = 0; c < 10; c++) {
			HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
			if (cell == null){
				isEmptyCell[c]="empty";
			}
		}
		
		if ("empty".equals(isEmptyCell[0]) && "empty".equals(isEmptyCell[1]) 
				&& "empty".equals(isEmptyCell[2])&& "empty".equals(isEmptyCell[3])
				&& "empty".equals(isEmptyCell[4])&& "empty".equals(isEmptyCell[5])
				&& "empty".equals(isEmptyCell[6])&& "empty".equals(isEmptyCell[7])
				&& "empty".equals(isEmptyCell[8])&& "empty".equals(isEmptyCell[9])){
			return true;
		}
		else{
			return false;
		}
	}

	public StringBuilder checkingLovUpload(MsLov msl, AuditContext callerId) {
	    StringBuilder errorUpload = new StringBuilder();
		if (msl.getLovGroup() == null || ("").equals(msl.getLovGroup())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"Lov Group"}, this.retrieveLocaleAudit(callerId)));
		}
		else if (80 < msl.getLovGroup().length()) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", 
					new Object[]{"Lov Group","80"}, this.retrieveLocaleAudit(callerId)));
		}
		if (msl.getCode() == null || ("").equals(msl.getCode())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"Code"}, this.retrieveLocaleAudit(callerId)));
		}
		else if (80 < msl.getCode().length()) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", 
					new Object[]{"Code","80"}, this.retrieveLocaleAudit(callerId)));			
		}	
		if (msl.getDescription() == null || ("").equals(msl.getDescription())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"Deskripsi"}, this.retrieveLocaleAudit(callerId)));
		}
		if (!("0").equals(msl.getIsActive())) {
			if (!("1").equals(msl.getIsActive())) {
				if (!("").equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
						new Object[]{"Active"}, this.retrieveLocaleAudit(callerId)));
			}
		}
		if (-2 == msl.getSequence()) {

			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.mandatory", 
					new Object[]{"Sequence"}, this.retrieveLocaleAudit(callerId)));
		}
		
		if (-1 == msl.getSequence()) {

			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.numeric", 
					new Object[]{"Sequence"}, this.retrieveLocaleAudit(callerId)));
		}
		return errorUpload;
	}
	
	public StringBuilder checkingBranch (String branch, AuditContext callerId){
		StringBuilder errorUpload = new StringBuilder();
		if(branch.length()>0){
			String [] branchCode = branch.split(",");
			for (int i =0; i<branchCode.length; i++){
				Object[][] paramCode = { {Restrictions.eq("branchCode", branchCode[i].replace(" ", ""))} };
				MsBranch msb = this.getManagerDAO().selectOne(MsBranch.class, paramCode);
				if(msb == null){
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(this.messageSource.getMessage("businesslogic.uploadtask.notvalid", 
							new Object[]{"Branch code",branchCode[i].replace(" ", "")}, this.retrieveLocaleAudit(callerId)));
				}
			}
		}
		return errorUpload;
	}

	private byte[] exportErrorLov(List listLov, List branchCode, int lovSuccess, int lovError, List lovBean, AuditContext callerId)
			throws SQLException {

		HSSFWorkbook workbook = this.createXlsTemplateErrorLov(listLov, branchCode,
				lovSuccess, lovError, lovBean, callerId);

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} catch (IOException e) {
            LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private HSSFWorkbook createXlsTemplateErrorLov(List listLov, List branchCode, int lovSuccess, int lovError, List lovBean, AuditContext callerId) {

		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("LOV List");
			this.createHeaderErrorUpload(workbook, sheet, lovSuccess,
					lovError, callerId);
			this.setTextDataFormatErrorUpload(workbook, sheet);
			this.setDataErrorUpload(workbook, sheet, listLov, branchCode, lovBean);
		} catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}

	private void setDataErrorUpload(HSSFWorkbook workbook, HSSFSheet sheet, List listLov, List branchCode, List<LovBean> lovBean) {
		List lovList = listLov;

		int j = 5;
		int idx = 0;
		Iterator iterator = lovList.iterator();

		while (iterator.hasNext()) {
			HSSFRow row = sheet.createRow(j);
			MsLov msl = (MsLov) iterator.next();

			HSSFCell cell = row.createCell(0);
			cell.setCellValue(msl.getLovGroup());
			sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH_ERROR[0]);

			HSSFCell cell1 = row.createCell(1);
			cell1.setCellValue(msl.getCode());
			sheet.setColumnWidth(1, HEADER_COLUMN_WIDTH_ERROR[1]);

			HSSFCell cell2 = row.createCell(2);
			cell2.setCellValue(msl.getDescription());
			sheet.setColumnWidth(2, HEADER_COLUMN_WIDTH_ERROR[2]);
			
			if (-1 != msl.getSequence() && -2 != msl.getSequence()) {
				HSSFCell cell3 = row.createCell(3);
				cell3.setCellValue(msl.getSequence());
				sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH_ERROR[3]);
			}

			HSSFCell cell4 = row.createCell(4);
			cell4.setCellValue(msl.getIsActive());
			sheet.setColumnWidth(4, HEADER_COLUMN_WIDTH_ERROR[4]);

			HSSFCell cell5 = row.createCell(5);
			cell5.setCellValue(msl.getConstraint1());
			sheet.setColumnWidth(5, HEADER_COLUMN_WIDTH_ERROR[5]);

			HSSFCell cell6 = row.createCell(6);
			cell6.setCellValue(msl.getConstraint2());
			sheet.setColumnWidth(6, HEADER_COLUMN_WIDTH_ERROR[6]);

			HSSFCell cell7 = row.createCell(7);
			cell7.setCellValue(msl.getConstraint3());
			sheet.setColumnWidth(7, HEADER_COLUMN_WIDTH_ERROR[7]);

			HSSFCell cell8 = row.createCell(8);
			cell8.setCellValue(msl.getConstraint4());
			sheet.setColumnWidth(8, HEADER_COLUMN_WIDTH_ERROR[8]);

			HSSFCell cell9 = row.createCell(9);
			cell9.setCellValue(msl.getConstraint5());
			sheet.setColumnWidth(9, HEADER_COLUMN_WIDTH_ERROR[9]);

			HSSFCell cell10 = row.createCell(10);
			if(!branchCode.isEmpty()){
					cell10.setCellValue(branchCode.get(idx).toString());
			}
			sheet.setColumnWidth(10, HEADER_COLUMN_WIDTH_ERROR[10]);
			
			HSSFCell cell11 = row.createCell(11);
			cell11.setCellValue(lovBean.get(idx).getErrorText().toString());
			sheet.setColumnWidth(11, HEADER_COLUMN_WIDTH_ERROR[11]);

			j++;
			idx++;
		}
	}

	private void setTextDataFormatErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet) {

		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("@"));

		for (int i = 0; i < TEMPLATE_HEADER_ERROR.length; i++) {
			sheet.setDefaultColumnStyle(i, style);
		}

	}

	private void createHeaderErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet, int lovSuccess, int lovError, AuditContext callerId) {

		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);

		HSSFRow row0 = sheet.createRow(0);
		HSSFRow row1 = sheet.createRow(1);
		HSSFRow row2 = sheet.createRow(2);

		HSSFCell cell0 = row0.createCell(0);
		cell0.setCellValue(this.messageSource.getMessage(
				"businesslogic.uploadtask.tottaskupload", new Object[]{"LOV", lovSuccess}, this.retrieveLocaleAudit(callerId)));
		cell0.setCellStyle(style);

		HSSFCell cell1 = row1.createCell(0);
		cell1.setCellValue(this.messageSource.getMessage(
				"businesslogic.uploadtask.tottaskerr", new Object[]{"LOV", lovError}, this.retrieveLocaleAudit(callerId)));
		cell1.setCellStyle(style);

		HSSFCell cell2 = row2.createCell(0);
		cell2.setCellValue(this.messageSource.getMessage(
				"businesslogic.uploadtask.errupload", new Object[]{"LOV"}, this.retrieveLocaleAudit(callerId)));
		cell2.setCellStyle(style);

		HSSFRow row4 = sheet.createRow(4);

		for (int i = 0; i < TEMPLATE_HEADER_ERROR.length; i++) {
			HSSFCell cell = row4.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_ERROR[i]);
			cell.setCellStyle(style);
			sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_ERROR[i]);
		}
	}

	
	@Override
	public String getIsSystem(String lovGroup, AuditContext callerId) {
		String isSystem = (String) this.getManagerDAO().selectOneNativeString("select COALESCE(IS_SYSTEM, '0') from MS_LOV_GROUP_SYNC where LOV_GROUP = '"+lovGroup+"'", null);
		return isSystem;
	}

}
