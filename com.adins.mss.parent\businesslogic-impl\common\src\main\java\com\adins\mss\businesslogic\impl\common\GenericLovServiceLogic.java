package com.adins.mss.businesslogic.impl.common;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LovServiceLogic;
import com.adins.mss.model.MsLov;

@SuppressWarnings("deprecation")
public class GenericLovServiceLogic extends BaseLogic implements LovServiceLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericLovServiceLogic.class);
	private AuditInfo auditInfo;
	
	public GenericLovServiceLogic() {
		String[] pkCols = { "uuidLov" };
		String[] pkDbCols = { "UUID_LOV" };
		String[] cols = { "uuidLov", "isActive", "isDeleted", "lovGroup", "code",
				"description", "sequence", "constraint1", "constraint2",
				"constraint3", "constraint4", "constraint5" };
		String[] dbCols = { "UUID_LOV", "IS_ACTIVE", "IS_DELETED", "LOV_GROUP", "CODE",
				"DESCRIPTION", "SEQUENCE", "CONSTRAINT_1", "CONSTRAINT_2",
				"CONSTRAINT_3", "CONSTRAINT_4", "CONSTRAINT_5" };
		this.auditInfo = new AuditInfo("MS_LOV", pkCols, pkDbCols, cols, dbCols);
	}
	
	@Transactional(readOnly=true,isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String syncLov(AuditContext callerId, String lovGroup, String code,
			String description, String sequence, String isActive,
			String constraint1, String constraint2, String constraint3,
			String constraint4, String constraint5) {

		if (StringUtils.stripToEmpty(constraint1) == ""){
			constraint1 = null;
		}
		if (StringUtils.stripToEmpty(constraint2) == ""){
			constraint2 = null;
		}
		if (StringUtils.stripToEmpty(constraint3) == ""){
			constraint3 = null;
		}
		if (StringUtils.stripToEmpty(constraint4) == ""){
			constraint4 = null;
		}
		if (StringUtils.stripToEmpty(constraint5) == "") {
			constraint5 = null;
		}
		
		String result = null;
		Integer seq = Integer.valueOf(sequence);
		
		Object checks[][] = { {"lovGroup", lovGroup}, {"code", code}, {"constraint1", constraint1}, {"constraint2", constraint2}, {"constraint3", constraint3}, {"constraint4", constraint4}, {"constraint5", constraint5} };
		Object id[][] = { {"uuidUser", callerId.getCallerId()} };
		

		Integer i = (Integer) this.getManagerDAO()
				.selectOneNativeString("SELECT COUNT(*) FROM MS_LOV WHERE LOV_GROUP = :lovGroup"
										+ " AND CODE = :code"
										+ " AND CONSTRAINT_1 "+checkNull(constraint1,"constraint1")
										+ " AND CONSTRAINT_2 "+checkNull(constraint2,"constraint2")
										+ " AND CONSTRAINT_3 "+checkNull(constraint3,"constraint3")
										+ " AND CONSTRAINT_4 "+checkNull(constraint4,"constraint4")
										+ " AND CONSTRAINT_5 "+checkNull(constraint5,"constraint5"), checks);

		String login = (String) this.getManagerDAO().selectOneNativeString("SELECT LOGIN_ID FROM AM_MSUSER WHERE UUID_MS_USER = :uuidUser", id);

		if (i == 0) {
			this.insertLOV(callerId, lovGroup, code, description, seq, isActive, constraint1, constraint2, constraint3, constraint4, constraint5, login);
			result = "LovGroup "+lovGroup+", Code "+code+", berhasil di insert";
			LOG.info("LovGroup {}, Code {}, berhasil di insert.",lovGroup, code);
		}
		else {
			Object[] x = (Object[]) this.getManagerDAO().selectOneNativeString("SELECT CAST(UUID_LOV as VARCHAR), IS_ACTIVE, DESCRIPTION, SEQUENCE FROM MS_LOV WHERE LOV_GROUP = :lovGroup"
					+ " AND CODE = :code"
					+ " AND CONSTRAINT_1 "+checkNull(constraint1,"constraint1")
					+ " AND CONSTRAINT_2 "+checkNull(constraint2,"constraint2")
					+ " AND CONSTRAINT_3 "+checkNull(constraint3,"constraint3")
					+ " AND CONSTRAINT_4 "+checkNull(constraint4,"constraint4")
					+ " AND CONSTRAINT_5 "+checkNull(constraint5,"constraint5"), checks);
			
			String uuidLov = (String) x[0];
			String isActiveOld = (String) x[1];
			String descriptionOld = (String) x[2];
			Integer sequenceOld = (Integer) x[3];
			LOG.info("isActiveOld : {}, descriptionOld : {}, sequenceOld : {}",isActiveOld,descriptionOld,sequenceOld);
			LOG.info("isActive : {}, description : {}, sequence : {}",isActive,description,seq);
			if (isActive.equals(isActiveOld) && description.equals(descriptionOld) && (seq.equals(sequenceOld))) {
				result = "LovGroup "+lovGroup+", Code "+code+", sudah ada di database dan tidak terjadi perubahan.";
				LOG.info("LovGroup {}, Code {}, sudah ada di database dan tidak terjadi perubahan.",lovGroup, code);
			} 
			else {
				this.updateLOV(callerId, uuidLov, description, seq, isActive, login);
				result = "LovGroup "+lovGroup+", Code "+code+", berhasil di update.";
				LOG.info("LovGroup {}, Code {}, berhasil di update.",lovGroup, code);
			}
		}
		
		return result;
	}
	
	private void insertLOV(AuditContext callerId, String lovGroup,
			String code, String description, Integer sequence, String isActive,
			String constraint1, String constraint2, String constraint3,
			String constraint4, String constraint5, String loginId) {
	
		MsLov msLov = new MsLov(loginId, new Date(), lovGroup, description, sequence);
		msLov.setCode(code);
		msLov.setIsActive(isActive);
		msLov.setConstraint1(constraint1);
		msLov.setConstraint2(constraint2);
		msLov.setConstraint3(constraint3);
		msLov.setConstraint4(constraint4);
		msLov.setConstraint5(constraint5);
		
		this.getManagerDAO().insert(msLov);
		this.auditManager.auditAdd(msLov, auditInfo, callerId.getCallerId(), "");
	}
	
	private void updateLOV(AuditContext callerId, String uuidLov, String description, Integer sequence, String isActive, String loginId) {
		Object params[][] = { {"isActive", isActive}, {"loginId", loginId}, {"dtmUpd", new Date()}, {"description", description}, {"sequence", sequence}, {"uuidLov", uuidLov} };
		this.getManagerDAO().updateNativeString("UPDATE MS_LOV SET IS_ACTIVE = :isActive, USR_UPD = :loginId, DTM_UPD = :dtmUpd, DESCRIPTION = :description, SEQUENCE = :sequence WHERE UUID_LOV = :uuidLov", params);

	}
	
	public String checkNull(String in, String param){
		return in == null ? "is null" : "= :"+param;
	}
	

}
