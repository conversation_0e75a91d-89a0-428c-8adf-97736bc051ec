package com.adins.mss.businesslogic.impl.common;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LuAreaLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsArea;
import com.adins.mss.model.MsAreaofbranch;
import com.adins.mss.model.MsAreaofuser;
import com.adins.mss.model.MsAreapath;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.custom.LuAreaBean;

@SuppressWarnings({"rawtypes", "unchecked"})
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericLuAreaLogic extends BaseLogic implements LuAreaLogic, MessageSourceAware {
	private AuditInfo auditInfo;
	private AuditInfo auditInfoAreaPath;
	
	@Autowired
	private MessageSource messageSource;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
					
	public GenericLuAreaLogic() {
		String[] pkCols = {"uuidArea"};
		String[] pkDbCols = {"UUID_AREA"};
		String[] cols = {"uuidArea", "areaName", "areaTypeCode", 
				"latitude", "longitude", "radius", "msArea.uuidArea", 
				"usrCrt", "dtmCrt"};
		String[] dbCols = {"UUID_AREA", "AREA_NAME", "AREA_TYPE_CODE", 
				"LATITUDE", "LONGITUDE", "RADIUS", "PARENT_AREA_ID",
				"USR_CRT", "DTM_CRT"};
		this.auditInfo = new AuditInfo("MS_AREA", pkCols, pkDbCols, cols, 
				dbCols);
		
		String[] pkColsAp = {"uuidAreaPath"};
		String[] pkDbColsAp = {"UUID_AREA_PATH"};
		String[] colsAp = {"uuidAreaPath", "msArea.uuidArea", "sequence", 
				"latitude", "longitude"};
		String[] dbColsAp = {"UUID_AREA_PATH", "UUID_AREA", "SEQUENCE", 
				"LATITUDE", "LONGITUDE"};
		this.auditInfoAreaPath = new AuditInfo("MS_AREAPATH", pkColsAp, 
				pkDbColsAp, colsAp, dbColsAp);
	}

	@Override
	public Map<String, Object> listAreasByBranchArea(Object params,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<>();
		List<LuAreaBean> listResult = new ArrayList<LuAreaBean>();
		List listCriteria = new ArrayList();
		Long countCriteria = (long) 0;
		String[][] tmpParams = (String[][]) params;
		if ("".equals(tmpParams[0][1]) || tmpParams[0][1] == null) {
				tmpParams[0][1] = "%";
		}
			
		MsAreaofbranch msAreaofbranch = (MsAreaofbranch) this.getManagerDAO().
				selectOne("from MsAreaofbranch maob "
						+ "join fetch maob.msArea ma "
						+ "join fetch maob.msBranch mb "
						+ "where mb.uuidBranch = :uuidBranch", 
						new Object[][] {{"uuidBranch", 
						Long.valueOf(tmpParams[1][1])}});
		//Get latlng branch
		MsBranch msBranch = (MsBranch) this.getManagerDAO()
				.selectOne(MsBranch.class, Long.valueOf(tmpParams[1][1]));
		//User Branch Area
		String areaUserBranch = "\"-\"";
		Object[][] param2 = {{"uuidBranch",Long.valueOf(tmpParams[1][1]) }};
		List area = this.getManagerDAO().selectAllNative(
				"lookup.area.areabranchuser", param2, null);
		Object[] areaBranch = (Object[]) this.getManagerDAO()
				.selectOneNative("lookup.area.areabranch", param2);
		if (areaBranch[8].toString().equalsIgnoreCase("poly")) {
			areaUserBranch = areaBranch[8].toString()+",";
			for (int i = 0; i < area.size(); i++) {
				Map temp2 = (Map) area.get(i);
				areaUserBranch = areaUserBranch+temp2.get("d0") + "~" + 
						temp2.get("d2") + "~" + temp2.get("d1") + ",";
			}
		}
		else {
			areaUserBranch = areaBranch[8].toString() + "," + 
					areaBranch[9].toString() + "," + areaBranch[10].toString() + 
					"," + areaBranch[11].toString();
		}
			
		if (msAreaofbranch != null) {
			Object[][] queryParamList = {{"areaName", tmpParams[0][1] },
					{"uuidArea", msAreaofbranch.getMsArea().getUuidArea()},
					{ "start", String.valueOf((pageNumber-1)*pageSize+1)},
					{ "end", String.valueOf((pageNumber-1)*pageSize+pageSize)}};
			Object[][] queryParamCount = {{"areaName", tmpParams[0][1] },
					{"uuidArea", msAreaofbranch.getMsArea().getUuidArea()}};
				
			listCriteria = this.getManagerDAO().selectAllNative(
					"lookup.area.listHirarkiArea", queryParamList, null);
			countCriteria = Long.valueOf((Integer) this.getManagerDAO()
					.selectOneNative("lookup.area.countHirarkiArea", 
					queryParamCount));
				
			if (!listCriteria.isEmpty()) {
				for (int i = 0; i < listCriteria.size(); i++) {
					LuAreaBean bean = new LuAreaBean();
					Map map = (Map) listCriteria.get(i);
					bean.setBeanArea(setBeanAreaFromMap(map));
						
					if (GlobalVal.AREA_POLY.equals(map.get("d2"))) {
						//get area path by uuidArea
						Object[][] queryParams = {{Restrictions.eq(
								"msArea.uuidArea", 
								Long.valueOf(map.get("d0").toString()))}};
						Map mapAreaPath = this.getManagerDAO()
								.list(MsAreapath.class, queryParams, null);
						List<MsAreapath> listOfAreaPath = (List) mapAreaPath
								.get(GlobalKey.MAP_RESULT_LIST);
							String areapath = getAreaPathString(listOfAreaPath);
							bean.setAreaPath(areapath);
					} 
					else {
						bean.setAreaPath("");
					}
						
					//get subsystem user login
					Object[][] paramsUserLogin = {{Restrictions.eq(
							"uuidMsUser",Long.valueOf(callerId.getCallerId())) }};
					AmMsuser userLogin = this.getManagerDAO()
							.selectOne(AmMsuser.class, paramsUserLogin);
						
					//get user by uuidArea
					Map<String, Object> mapAreaUser = this.getManagerDAO().list(
							"from MsAreaofuser maou "
							+ "join fetch maou.msArea ma "
							+ "join fetch maou.amMsuser mu "
							+ "join fetch mu.amMssubsystem ms "
							+ "where ma.uuidArea = :uuidArea "
							+ "and mu.isActive = :isActive "
							+ "and ms.uuidMsSubsystem = :uuidMsSubsystem", 
							new Object[][] {{"uuidArea", 
							Long.valueOf(map.get("d0").toString())}, 
							{"isActive", "1"}, {"uuidMsSubsystem", 
								userLogin.getAmMssubsystem().getUuidMsSubsystem()}});
					List<MsAreaofuser> listOfAreaUser = (List<MsAreaofuser>) 
							mapAreaUser.get(GlobalKey.MAP_RESULT_LIST);
					String areaUser = getAreaUserString(listOfAreaUser);
					bean.setAreaUser(areaUser);
						
					bean.setBeanAreaBranchUser(areaUserBranch);
					bean.setBeanBranch(msBranch);
					//Don't know how to get bigdecimal in struts2
					bean.setUserLat(bean.getBeanArea().getLatitude().toString());
					bean.setUserLng(bean.getBeanArea().getLongitude().toString());
					bean.setBranchLat(msBranch.getLatitude().toString());
					bean.setBranchLng(msBranch.getLongitude().toString());
					listResult.add(bean);
				}
			}
		}
		result.put(GlobalKey.MAP_RESULT_LIST, listResult);
		result.put(GlobalKey.MAP_RESULT_SIZE, countCriteria);

		return result;
	}

	public MsArea setBeanAreaFromMap(Map map) {
		MsArea bean = new MsArea();
		
		bean.setUuidArea(Long.valueOf(map.get("d0").toString()));
		bean.setAreaName((String)map.get("d1"));
		bean.setAreaTypeCode((String)map.get("d2"));
		bean.setLongitude((BigDecimal)map.get("d3"));
		bean.setLatitude((BigDecimal)map.get("d4"));
		bean.setRadius((Integer)map.get("d5"));
		
		return bean;
	}
	
	public String getAreaPathString(List<MsAreapath> listOfAreaPath) {
		String result = new String();
		
		for (int i = 0; i < listOfAreaPath.size(); i++) {
			MsAreapath bean = listOfAreaPath.get(i);
			result += bean.getSequence() + "~" + bean.getLongitude() +
					"~" + bean.getLatitude() + ",";
		}
		
		return  result.substring(0, result.length() - 1);
	}
	
	public String getAreaUserString(List listOfAreaUser) {
		String result = new String();
		
		if (!listOfAreaUser.isEmpty()) {
			for (int i = 0; i < listOfAreaUser.size(); i++) {
				MsAreaofuser bean = (MsAreaofuser) listOfAreaUser.get(i);
				result += bean.getAmMsuser().getUuidMsUser() + "~" + 
						bean.getAmMsuser().getFullName()  +",";
			}
			result = result.substring(0, result.length() - 1);
		}
		else {
			result = "";
		}
		
		return result;
	}
	
	@Override
	public LuAreaBean getAreaBranch(long uuidBranch, AuditContext callerId) {
		LuAreaBean result = null;
		Map<String, Object> map = null;

		MsAreaofbranch msAreaofbranch = (MsAreaofbranch) this.getManagerDAO().
				selectOne("from MsAreaofbranch maob "
						+ "join fetch maob.msArea ma "
						+ "join fetch maob.msBranch mb "
						+ "where mb.uuidBranch = :uuidBranch", 
				new Object[][] {{"uuidBranch", uuidBranch}});
						
		if (msAreaofbranch != null) {
			result = new LuAreaBean();
			if (GlobalVal.AREA_POLY.equals(msAreaofbranch.getMsArea()
					.getAreaTypeCode())) {
				//get area path by uuidArea
				Object[][] queryParams = {{ Restrictions.eq("msArea.uuidArea",
						msAreaofbranch.getMsArea().getUuidArea()) }};
				map = this.getManagerDAO().list(MsAreapath.class, 
						queryParams, null);
				List<MsAreapath> listOfAreaPath = (List) map.get(
						GlobalKey.MAP_RESULT_LIST);
				result.setListOfAreaPath(listOfAreaPath);
			}
			result.setBeanArea(msAreaofbranch.getMsArea());
			result.setBeanBranch(msAreaofbranch.getMsBranch());
		}
		return result;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public MsArea insertArea(MsArea bean, String areapath, AuditContext callerId) {
		MsArea msArea = bean;
			
		Object[][] paramsExist = {{Restrictions.eq("areaName", 
				msArea.getAreaName().toUpperCase())}};
			
		MsArea isExist = this.getManagerDAO().selectOne(MsArea.class, paramsExist);
		if (null != isExist) {
			throw new EntityNotUniqueException((this.messageSource
					.getMessage("businesslogic.lookuparea.areanameexist", null,
					this.retrieveLocaleAudit(callerId))), msArea.getAreaName());
		}
			
		msArea.setUsrCrt(callerId.getCallerId());
		msArea.setDtmCrt(new Date());
		msArea.setAreaName(msArea.getAreaName().toUpperCase());
		msArea.setIsActive("1");
			
		this.getManagerDAO().insert(msArea);
		this.auditManager.auditAdd(msArea, auditInfo, callerId.getCallerId(), "");
		
		//insert area of path if areaTypeCode == POLY
		if (GlobalVal.AREA_POLY.equals(msArea.getAreaTypeCode())) {
			String[] paths= areapath.split(",");
			MsArea beanArea = this.getManagerDAO().selectOne(MsArea.class, 
					msArea.getUuidArea());
			for (int i = 0; i < paths.length; i++) {
				insertAreaOfPath(paths[i], beanArea, callerId);
			}
		}
		
		return msArea;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	public void insertAreaOfPath(String paths, MsArea msArea, 
			AuditContext callerId) {
		String[] path = paths.split("~");
		
		MsAreapath dbModel = new MsAreapath();
		dbModel.setUsrCrt(callerId.getCallerId());
		dbModel.setDtmCrt(new Date());
		dbModel.setMsArea(msArea);
		dbModel.setSequence(NumberUtils.toShort(path[0]));
		dbModel.setLatitude(BigDecimal.valueOf(NumberUtils.toDouble(path[1])));
		dbModel.setLongitude(BigDecimal.valueOf(NumberUtils.toDouble(path[2])));
		this.getManagerDAO().insert(dbModel);
		this.auditManager.auditAdd(dbModel, auditInfoAreaPath, 
				callerId.getCallerId(), "");
	}

	@Override
	public LuAreaBean getAreaUser(long uuidUser, AuditContext callerId) {
		LuAreaBean result = new LuAreaBean();
		Map<String, Object> map = null;
		
		MsAreaofuser msAreaofuser = (MsAreaofuser) this.getManagerDAO().
				selectOne("from MsAreaofuser maou "
						+ "join fetch maou.msArea ma "
						+ "join fetch maou.amMsuser am "
						+ "where am.uuidMsUser = :uuidMsUser", 
					new Object[][] {{"uuidMsUser", uuidUser}});
						
		if (msAreaofuser != null) {
			if (GlobalVal.AREA_POLY.equals(msAreaofuser
					.getMsArea().getAreaTypeCode())) {
				//get area path by uuidArea
				Object[][] queryParams = {{ Restrictions.eq(
						"msArea.uuidArea",
						msAreaofuser.getMsArea().getUuidArea()) }};
				map = this.getManagerDAO().list(MsAreapath.class, 
						queryParams, null);
				List<MsAreapath> listOfAreaPath = (List) map.get(
						GlobalKey.MAP_RESULT_LIST);
				result.setAreaPath(getAreaPathString(listOfAreaPath));
			}
			result.setBeanArea(msAreaofuser.getMsArea());
			result.setAreaUser("1");
			result.setBeanBranch(getBranch(uuidUser));
		} 
		else {
			result.setAreaUser("0");
		}
		return result;
	}

	@Override
	public List getOtherBranch(long id, AuditContext auditContext) {
		List listBranches = new ArrayList();
			
    	Object[][] queryParams = {{"uuidBranch",id}};
    	List branches = this.getManagerDAO().selectAllNative(
    			"lookup.area.listOtherBranches", queryParams, null);
    	for (int i = 0; i < branches.size(); i++) {
    		Map temp = (Map) branches.get(i);
    			
    		String d0;
    		if (null == temp.get("d0")) {
    			d0 = "\"\"";
    		} 
    		else {
    			d0 = "\"" + temp.get("d0").toString() + "\"";
    		}
    			
    		String d1;
    		if (null == temp.get("d1")) {
    			d1 = "\"\"";
    		} 
    		else {
    			d1 = "\"" + temp.get("d1").toString() + "\"";
    		}
    			
    		String d2;
    		if (null == temp.get("d2")) {
    			d2 = "\"\"";
    		} 
    		else {
    			d2 = "\"" + temp.get("d2").toString() + "\"";
    		}
    			
    		String d3;
    		if (null == temp.get("d3")) {
    			d3 = "0";
    		} 
    		else {
    			d3 = "\"" + temp.get("d3").toString() + "\"";
    		}
    			
    		String d4;
    		if (null == temp.get("d4")) {
    			d4 = "0";
    		} 
    		else {
    			d4 = "\"" + temp.get("d4").toString() + "\"";
    		}
    			
    		String d5 = "\"-\"";
    		if (null != temp.get("d5")) {
    			MsArea area = this.getManagerDAO()
    					.selectOne(MsArea.class, 
    					Long.valueOf(temp.get("d5").toString()));				
    			if (area.getAreaTypeCode().equalsIgnoreCase("poly")) {
    				d5 = "\"" + area.getAreaTypeCode() + ",";
    				Map<String,Object> temp1 = this.getManagerDAO()
    						.list(MsAreapath.class, new Object[][] {{ 
    						Restrictions.eq("msArea.uuidArea", 
    				        Long.valueOf(temp.get("d5").toString())) }},
    				        new String[][] {{"sequence",GlobalVal.ROW_ORDER_ASC}});
    				List<MsAreapath> list = (List)temp1.get(
    						GlobalKey.MAP_RESULT_LIST);
    				for (MsAreapath path : list) {
    					d5 = d5 + path.getSequence() + "~" + path.getLongitude() + 
    							"~" + path.getLatitude() + ",";						
    				}
    				d5 += "\"";
    			}
    			else {
    				d5 = "\"" + area.getAreaTypeCode() + "," + 
    						area.getRadius().toString() + "\"";		
    			}
    		}
    		
    		listBranches.add(d0);
    		listBranches.add(d1);
    		listBranches.add(d2);
    		listBranches.add(d3);
    		listBranches.add(d4);
    		listBranches.add(d5);
    	}
		return listBranches;
	}
	
	public MsBranch getBranch(long uuidUser) {
		MsBranch branchUser;
			
		Object[][] paramUser = {{Restrictions.eq("uuidMsUser",uuidUser) }};
		AmMsuser user = (AmMsuser) this.getManagerDAO().selectOne(
				AmMsuser.class, paramUser);
			
		Object[][] paramBranch = {{Restrictions.eq("uuidBranch",
				user.getMsBranch().getUuidBranch())}};
		branchUser = (MsBranch) this.getManagerDAO().selectOne(MsBranch.class,
					paramBranch);
		return branchUser;
	}

	@Override
	public String getAreaBranchUser(long uuidBranch){
		String areaUserBranch;
		areaUserBranch = "\"-\"";
		Object[][] param = {{"uuidBranch",uuidBranch}};
		List area = this.getManagerDAO().selectAllNative(
				"lookup.area.areabranchuser", param, null);
		Object[] areaBranch = (Object[]) this.getManagerDAO()
				.selectOneNative("lookup.area.areabranch", param);
		if (areaBranch[8].toString().equalsIgnoreCase("poly")) {
			areaUserBranch = areaBranch[8].toString()+",";
			for (int i = 0; i < area.size(); i++) {
				Map temp2 = (Map) area.get(i);
				areaUserBranch = areaUserBranch+temp2.get("d0") + 
						"~" + temp2.get("d2") + "~" + temp2.get("d1") + ",";
			}
		}
		else {
			areaUserBranch=areaBranch[8].toString() + "," + 
					areaBranch[9].toString() + "," + areaBranch[10].toString() +
					"," + areaBranch[11].toString();
		}
		return areaUserBranch;
	}
	
	@Override
	public MsArea validateAreaName(String areaName, AuditContext auditContext) {
		MsArea msArea;
			
		Object[][] paramArea = {{Restrictions.eq("areaName", areaName) }};
		msArea = (MsArea) this.getManagerDAO().selectOne(MsArea.class, paramArea);
		
		return msArea;
	}
}
