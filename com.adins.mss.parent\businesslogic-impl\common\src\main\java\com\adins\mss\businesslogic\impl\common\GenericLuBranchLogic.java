package com.adins.mss.businesslogic.impl.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LuBranchLogic;
import com.adins.mss.constants.GlobalKey;

@SuppressWarnings("rawtypes")
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericLuBranchLogic extends BaseLogic implements LuBranchLogic {

	@Override
	public Map<String, Object> listBranchExc(Object params, 
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String,Object>();
		String[][] tempParam = (String[][])params;
		List listCriteria = ListUtils.EMPTY_LIST;
		Integer countCriteria = 0;
		//branchCode
		if (StringUtils.isBlank(tempParam[0][1])) {
			tempParam[0][1] = "%";
		}
		//branchName
		if (StringUtils.isBlank(tempParam[1][1])) {
			tempParam[1][1] = "%";
		}
		//uuidBranch	
		if (StringUtils.isNotBlank(tempParam[4][1]) &&
				!("%").equals(tempParam[4][1])) {
			tempParam[3][1] = tempParam[4][1];
		}			
		
		String[][] queryParamList = {{tempParam[0][0], tempParam[0][1]},	//branchCode
				{tempParam[1][0], tempParam[1][1]}, 						//branchName
				{"start", String.valueOf((pageNumber-1)*pageSize+1)},
				{"end", String.valueOf((pageNumber-1)*pageSize+pageSize)}};
		String[][] queryParamCount = {{tempParam[0][0], tempParam[0][1]}, 	//branchCode
				{tempParam[1][0], tempParam[1][1]}}; 						//branchName
			
		String queryListName = "lookup.branch.listExcCriteria";
		String queryCountName  = "lookup.branch.listExcCriteriaCnt";
			
		listCriteria = this.getManagerDAO().selectAllNative(queryListName, 
				queryParamList, null);
		countCriteria = (Integer)this.getManagerDAO().selectOneNative(
				queryCountName, queryParamCount);
			
		result.put(GlobalKey.MAP_RESULT_LIST, listCriteria);
		result.put(GlobalKey.MAP_RESULT_SIZE, countCriteria);
			
		return result;
	}

	@Override
	public Map<String, Object> listBranchDob(Object params, 
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String,Object>();
		String[][] tempParam = (String[][])params;
		List listCriteria = new ArrayList();
		Integer countCriteria = 0;
		//branchCode
		if (StringUtils.isBlank(tempParam[0][1])) {
			tempParam[0][1] = "%";
		}
		//branchName
		if (StringUtils.isBlank(tempParam[1][1])) {
			tempParam[1][1] = "%";
		}
		//uuidDealer
		if (StringUtils.isNotBlank(tempParam[4][1])) {
			tempParam[5][1] = tempParam[4][1];
		}	
			
		String[][] queryParamList = {{ tempParam[0][0], tempParam[0][1]},			//branchCode
					{ tempParam[1][0], tempParam[1][1]},							//branchName
					{ tempParam[5][0], tempParam[5][1]},							//uuidDealer
					{ "start",  String.valueOf((pageNumber-1)*pageSize+1)},
					{ "end",   String.valueOf((pageNumber-1)*pageSize+pageSize)}};	
		String[][] queryParamCount = {{ tempParam[0][0], tempParam[0][1]},			//branchCode
					{ tempParam[1][0], tempParam[1][1]},							//branchName
					{ tempParam[5][0], tempParam[5][1]}};							//uuidDealer
			
		String queryListName = "lookup.branch.listDobCriteria";
		String queryCountName = "lookup.branch.listDobCriteriaCnt";
			
		listCriteria = this.getManagerDAO().selectAllNative(queryListName, 
				queryParamList, null);
		countCriteria = (Integer)this.getManagerDAO().selectOneNative(
				queryCountName, queryParamCount);
			
		result.put(GlobalKey.MAP_RESULT_LIST, listCriteria);
		result.put(GlobalKey.MAP_RESULT_SIZE, countCriteria);
			
		return result;
	}
}
