package com.adins.mss.businesslogic.impl.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LuJobLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.MsJob;

@SuppressWarnings("rawtypes")
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericLuJobLogic extends BaseLogic implements LuJobLogic {

	@Override
	public Map<String, Object> listJob(Object params, int pageNumber, 
			int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String,Object>();
		String queryListName = new String();
		String queryCountName = new String();
		Object[][] tempParam = (Object[][])params;
		List listCriteria = new ArrayList();
		Integer countCriteria = 0;
		//jobCode
		if ("".equals(tempParam[0][1]) || tempParam[0][1] == null) {
			tempParam[0][1] = "%";
		}
		//description
		if ("".equals(tempParam[1][1]) || tempParam[1][1] == null){
			tempParam[1][1] = "%";
		}
		
		queryListName = "lookup.job.listExcCriteriaLogin";
		queryCountName  = "lookup.job.listExcCriteriaLoginCnt";
		
		//cek job admin
		long uuidJob = Long.valueOf(String.valueOf(tempParam[3][1]));
		MsJob msJob = this.getManagerDAO().selectOne(MsJob.class, 
				uuidJob);
		
		Object[][] paramsAdmin = { 
				{Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MS+GlobalKey.GENERALSETTING_JOBADMIN)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsAdmin);
		if (ArrayUtils.contains(StringUtils.split(amGeneralsetting.getGsValue(), ";"), msJob.getJobCode())) {
			queryListName = "lookup.job.listExcCriteriaAdmin";
			queryCountName = "lookup.job.listExcCriteriaAdminCnt";
		}
		
		//uuidJob	
		if (!"".equals(tempParam[4][1]) && tempParam[4][1] != null) {
			tempParam[3][1] = tempParam[4][1];
		}			
			
		Object[][] queryParamList = {{ tempParam[0][0], tempParam[0][1]},		//jobCode
				{ tempParam[1][0], tempParam[1][1]},							//description
				{ tempParam[3][0], tempParam[3][1]},							//uuidJob
				{ "start",  String.valueOf((pageNumber-1)*pageSize+1)},
				{ "end",   String.valueOf((pageNumber-1)*pageSize+pageSize)}};
		Object[][] queryParamCount = {{ tempParam[0][0], tempParam[0][1]},		//jobCode
				{ tempParam[1][0], tempParam[1][1]},							//description
				{ tempParam[3][0], tempParam[3][1]}};							//uuidJob
		
		listCriteria = this.getManagerDAO().selectAllNative(queryListName, 
				queryParamList, null);
		countCriteria = (Integer)this.getManagerDAO().selectOneNative(
				queryCountName, queryParamCount);
	
		result.put(GlobalKey.MAP_RESULT_LIST, listCriteria);
		result.put(GlobalKey.MAP_RESULT_SIZE, countCriteria);
		 
		return result;
	}
}
