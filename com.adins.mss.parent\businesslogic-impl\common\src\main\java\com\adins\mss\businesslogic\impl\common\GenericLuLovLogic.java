package com.adins.mss.businesslogic.impl.common;

import java.util.HashMap;
import java.util.Map;

import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LuLovLogic;
import com.adins.mss.model.MsLov;

@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericLuLovLogic extends BaseLogic implements LuLovLogic {

	@Override
	public Map<String, Object> listLov(Object params, Object orders,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String,Object>();
		result = this.getManagerDAO().selectAll(MsLov.class,
					params, orders, pageNumber, pageSize); 
		return result;
	}
}
