package com.adins.mss.businesslogic.impl.common;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.sql.Clob;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LuOnlineLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.impl.interfacing.IntNcFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.MsMappingcolumn;
import com.adins.mss.services.model.common.AnswerItemBean;
import com.adins.mss.services.model.common.GetMaskapaiAsuransiRequest;
import com.adins.mss.services.model.common.OnlineLuResponse;
import com.google.gson.Gson;

@Transactional
public class GenericLuOnlineLogic extends BaseLogic implements LuOnlineLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericLuOnlineLogic.class);
	
	private IntFormLogic intFormLogic;
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	@Transactional(readOnly=true)
	@Override
	public OnlineLuResponse luOnline(String refId, String lovGroup, String searchVal, String choiceFilterVal,
			AuditContext callerId) {
		
//		choice filter :  searchValue@@@kolomGA@@@kolomGB
		
/*		select  stuff(( SELECT ', '+login_id +'-'+FULL_NAME FROM am_msuser 
				WHERE  spv_id = '274427CC-6E91-11E6-8622-2D6633B4A512'FOR XML PATH('')  )
					, 1, 2, '')  as result*/
		List<AnswerItemBean> resultToMobile = new ArrayList<>();
		String[] paramChoiceFilter = choiceFilterVal.split("@@@");
		OnlineLuResponse response = new OnlineLuResponse();
		
		Object[][] paramSetting = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_MAX_LIST_DATA_LUONLINE)} };
		AmGeneralsetting amSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramSetting);
				
		Object[][] params = {{Restrictions.eq("lovGroup", lovGroup)}};
		MsMappingcolumn mapping = this.getManagerDAO().selectOne(MsMappingcolumn.class, params);
		
		if (null == mapping) {
			throw new EntityNotFoundException("LuOnline Group : "+lovGroup+" has not been mapped", "MsMappingcolumn");
		}
		
		String[] paramColumnName = mapping.getParamColumnName().split("@@@");
		
		//fix: 2018-01-11 validasi max list data saat select, agar irit memori jika hasil select > 1000 data
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT  STUFF(( SELECT DISTINCT ';; '+CONVERT(VARCHAR(50), tbl.id)+' - '+ UPPER(descr)")
				.append(" FROM (")
				.append("SELECT TOP ").append(NumberUtils.toInt(amSetting.getGsValue(), LuOnlineLogic.DEFAULT_MAX_LIST_DATA_LUONLINE))
				.append(mapping.getResultIdColumn()).append(" as id,")
				.append(mapping.getResultDescColumn()).append(" as descr")
				.append(" FROM ").append(mapping.getTableName()).append(" with (nolock) WHERE ")
				.append(paramColumnName[0])
				.append(" LIKE ")
				.append("'%"+searchVal+"%' ");
		
		/*'select  stuff(( SELECT '', ''+' +@kolom_hasil+' FROM '+@tabel+' WHERE  '+@kolom_filter+
		' = '''+@nilai_filter+'''FOR XML PATH('''')  ), 1, 2, '''')  as result'*/
		if (paramColumnName.length >1) {
			for (int i=1; i<paramColumnName.length; i++ ){
				String[] filterValue = StringUtils.split(paramChoiceFilter[i-1], "|");
				sb.append(" AND ");
				sb.append(paramColumnName[i]);
				if (filterValue.length > 0) {
					sb.append(" = ");
					sb.append("'"+filterValue[0].replaceAll("\\s+","")+"'");
				}
				else {
					sb.append(" LIKE ");
					sb.append("'%'");
				}
			}
		}
		sb.append(") tbl FOR XML PATH('')  ), 1, 2, '')  as result");
		sb.toString();
		
		Clob hasilLovDelimited = (Clob) this.getManagerDAO().selectOneNativeString(sb.toString(), null);
		
		if (null != hasilLovDelimited) {
			String resultQuery = clobToString(hasilLovDelimited);
			String[] resultDelimited = resultQuery.split(";;");
			for (int k = 0; k < resultDelimited.length; k++){	
				AnswerItemBean ansItem = new AnswerItemBean();
				ansItem.setKey(StringUtils.trim(resultDelimited[k].split(" - ")[0]));
				ansItem.setValue(StringUtils.trim(resultDelimited[k].split(" - ")[1]));
				resultToMobile.add(ansItem);
			}
		} 
		else {
			AnswerItemBean ansItem = new AnswerItemBean();
			ansItem.setKey("");
			ansItem.setValue("No Data Found");
			resultToMobile.add(ansItem);
		}
		
		response.setListField(resultToMobile);
		
		return response;
	
	}

	@Transactional(readOnly=true)
	@Override
	public boolean validateLuOnlineCode(String id, String lovGroup, AuditContext callerId) {
		Object[][] params = {{Restrictions.eq("lovGroup", lovGroup)}};
		MsMappingcolumn mapping = this.getManagerDAO().selectOne(MsMappingcolumn.class, params);
		
		if (null == mapping) {
			throw new EntityNotFoundException("LuOnline Group : "+lovGroup+" has not been mapped", "MsMappingcolumn");
		}
		
		String idColumn = mapping.getResultIdColumn();
		String tableName = mapping.getTableName();
		StringBuilder sb = new StringBuilder("SELECT COUNT(*) FROM ").append(tableName)
				.append(" WHERE ").append(idColumn).append(" = ").append(":id");
		
		Integer count = (Integer) this.getManagerDAO().selectOneNativeString(sb.toString(),
				new Object[][]{{"id", id}});
		
		if (count.intValue() > 0) {
			return true;
		}
		else {
			return false;
		}
	}
	
	private String clobToString(Clob data) {
	    StringBuilder sb = new StringBuilder();
	    try {
	        Reader reader = data.getCharacterStream();
	        BufferedReader br = new BufferedReader(reader);

	        String line;
	        while(null != (line = br.readLine())) {
	            sb.append(line);
	        }
	        br.close();
	    } 
	    catch (SQLException e) {
	        LOG.error("Error on converting CLOB to string", e);
	    } 
	    catch (IOException e) {
	        LOG.error("Error on converting CLOB to string", e);
	    }
	    return sb.toString();
	}

	@Transactional(readOnly=true)
	@Override
	public OnlineLuResponse generalLuOnline(String refId, String lovGroup, String searchVal, String choiceFilterVal,
			AuditContext callerId) {
		OnlineLuResponse response = new OnlineLuResponse();
					
		if (GlobalVal.REF_PRESURVEY_MASKAPAI_ASURANSI.equalsIgnoreCase(refId)) {
			response = intFormLogic.luOnlineMaskapaiAsuransi(refId, lovGroup, searchVal, choiceFilterVal, callerId);
		} else {
			response = luOnline(refId, lovGroup, searchVal, choiceFilterVal, callerId);
		}
		
		return response;
	}

}
