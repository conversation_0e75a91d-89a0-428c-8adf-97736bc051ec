package com.adins.mss.businesslogic.impl.common;

import java.util.List;

import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LuQuestionLogic;

@SuppressWarnings("rawtypes")
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericLuQuestionLogic extends BaseLogic implements 
		LuQuestionLogic {

	@Override
	public List listQuestion(String[][] params, 
			AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"lookup.question.listQuestionForm", params, null);
		return result;
	}

	@Override
	public Integer countListQuestion(Object params, 
			AuditContext callerId) {
		Integer result = (Integer)this.getManagerDAO().selectOneNative(
				"lookup.question.cntListQuestionForm", params);
		return result;
	}
}
