package com.adins.mss.businesslogic.impl.common;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LuReportLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings("rawtypes")
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericLuReportLogic extends BaseLogic implements LuReportLogic{

	@Override
	public Map<String, Object> luReportBranch(Object[][] paramList, Object[][] paramCount,  boolean isWithRegion, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String,Object>();
		List listResult = new ArrayList();
		Integer countResult = 0;
		
		String queryListName = "lookup.report.getListHierarkiBranch";
		String queryCountName = "lookup.report.getListHierarkiBranchCount";
		
		if (isWithRegion) {
			queryListName = "lookup.report.getListHierarkiBranchByRegion";
			queryCountName = "lookup.report.getListHierarkiBranchCountByRegion";
		}
		
		listResult = this.getManagerDAO().selectAllNative(queryListName, paramList, null);
		if (null != paramCount) {
			countResult = (Integer)this.getManagerDAO().selectOneNative(queryCountName, paramCount);
		}
	
		result.put(GlobalKey.MAP_RESULT_LIST, listResult);
		result.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(countResult));
			
		return result;
	}

	@Override
	public Map<String, Object> luReportUserSpv(Object[][] paramList, Object[][] paramCount, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String,Object>();
		List listResult = new ArrayList();
		Integer countResult = 0;
		
		String queryListName = "lookup.report.getUserSpvByBranch";
		String queryCountName = "lookup.report.getUserSpvByBranchCount";
		
		listResult = this.getManagerDAO().selectAllNative(queryListName, paramList, null);
		if (null != paramCount) {
			countResult = (Integer)this.getManagerDAO().selectOneNative(queryCountName, paramCount);
		}
	
		result.put(GlobalKey.MAP_RESULT_LIST, listResult);
		result.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(countResult));
			
		return result;
	}

	@Override
	public Map<String, Object> luReportUser(String uuidBranch, Object[][] paramList, Object[][] paramCount, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String,Object>();
		List listResult = new ArrayList();
		Integer countResult = 0;
		
		if ("%".equals(uuidBranch)) {
			String queryListName = "lookup.report.getUserBySpvAllBranch";
			String queryCountName = "lookup.report.getUserBySpvAllBranchCount";
			
			if (null != paramCount) {
				countResult = (Integer)this.getManagerDAO().selectOneNative(queryCountName, paramCount);
			}
			
			listResult = this.getManagerDAO().selectAllNative(queryListName, paramList, null);
		} else {
			String queryListName = "lookup.report.getUserBySpvBranch";
			String queryCountName = "lookup.report.getUserBySpvBranchCount";
			
			if (null != paramCount) {
				countResult = (Integer)this.getManagerDAO().selectOneNative(queryCountName, paramCount);
			}
			
			listResult = this.getManagerDAO().selectAllNative(queryListName, paramList, null);
		}
		
		result.put(GlobalKey.MAP_RESULT_LIST, listResult);
		result.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(countResult));
			
		return result;
	}
	
	@Override
	public String getJobSpvList(AmMsuser loginBean, AuditContext callerId) {
		String gsSpv = StringUtils.EMPTY;
		if (GlobalVal.SUBSYSTEM_MC.equals(loginBean.getAmMssubsystem().getSubsystemName())) {
			gsSpv = GlobalKey.GENERALSETTING_MC_JOBSPV;
		} 
		else if (GlobalVal.SUBSYSTEM_MT.equals(loginBean.getAmMssubsystem().getSubsystemName())) {
			gsSpv = GlobalKey.GENERALSETTING_MT_JOBSPV;
		} 
		else {
			gsSpv = GlobalKey.GENERALSETTING_MS_JOBSPV;
		}
		Object[][] paramsGs = {{Restrictions.eq("gsCode", gsSpv)}};
		AmGeneralsetting generalsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsGs);
		return generalsetting.getGsValue();
	}
}
