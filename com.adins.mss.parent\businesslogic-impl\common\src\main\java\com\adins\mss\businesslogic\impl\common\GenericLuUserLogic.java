package com.adins.mss.businesslogic.impl.common;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LuUserLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.AmMsuser;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericLuUserLogic extends BaseLogic implements LuUserLogic {

	@Override
	public Map<String, Object> listUserBrh(String loginId, String fullName, 
			String uuidJob, String uuidBranch, String uuidDealer, long uuidSubsystem,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String, Object>();
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		if (StringUtils.isNotBlank(loginId)) {
			condition.append(" and amu.loginId like :loginId");
			paramMap.put("loginId", "%" + loginId + "%");
		}
			
		if (StringUtils.isNotBlank(fullName)) {
			condition.append(" and amu.fullName like :fullName");
			paramMap.put("fullName", "%" + fullName + "%");
		}
		if (StringUtils.isNotBlank(uuidJob)) {
			condition.append(" and amu.msJob.uuidJob=:uuidJob");
			paramMap.put("uuidJob", Long.valueOf(uuidJob));
		}
		if (StringUtils.isNotBlank(uuidBranch)) {
			condition.append(" and amu.msBranch.uuidBranch=:uuidBranch");
			paramMap.put("uuidBranch", Long.valueOf(uuidBranch));
		}
		if (StringUtils.isNotBlank(uuidDealer)) {
			condition.append(" and amu.msDealer.uuidDealer=:uuidDealer");
			paramMap.put("uuidDealer", Long.valueOf(uuidDealer));
		}
		condition.append(" and amu.amMssubsystem.uuidMsSubsystem=:uuidMsSubsystem");
		condition.append(" and amu.isActive=:isActive");
		paramMap.put("uuidMsSubsystem", uuidSubsystem);
		paramMap.put("isActive", "1");
			
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by amu.loginId asc");
			
		result = this.getManagerDAO().selectAll(
				"from AmMsuser amu "
				+ "join fetch amu.msJob "
				+ "join fetch amu.msBranch "
				+ "join fetch amu.msDealer "
				+ "join fetch amu.amMssubsystem "
				+ "where 1=1"
					+ condition.toString() + orderQuery.toString(),
				"select count(*) "
				+ "from AmMsuser amu "
				+ "join amu.msJob "
				+ "join amu.msBranch "
				+ "join amu.msDealer "
				+ "join amu.amMssubsystem "
				+ "where 1=1"
					+ condition.toString(), paramMap, pageNumber, pageSize);
		List<AmMsuser> listUser = (List) result.get(
				GlobalKey.MAP_RESULT_LIST);
		result.put(GlobalKey.MAP_RESULT_LIST, listUser);
		return result;
	}
	
	@Override
	public Map<String, Object> listUserDlr(String loginId, String fullName, 
			String uuidJob, String uuidBranch, String uuidDealer, long uuidSubsystem,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String, Object>();
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		if (StringUtils.isNotBlank(loginId)) {
			condition.append(" and amu.loginId like :loginId");
			paramMap.put("loginId", "%" + loginId + "%");
		}
			
		if (StringUtils.isNotBlank(fullName)) {
			condition.append(" and amu.fullName like :fullName");
			paramMap.put("fullName", "%" + fullName + "%");
		}
		if (StringUtils.isNotBlank(uuidJob)) {
			condition.append(" and amu.msJob.uuidJob=:uuidJob");
			paramMap.put("uuidJob", Long.valueOf(uuidJob));
		}
		if (StringUtils.isNotBlank(uuidBranch)) {
			condition.append(" and amu.msBranch.uuidBranch=:uuidBranch");
			paramMap.put("uuidBranch", Long.valueOf(uuidBranch));
		}
		if (StringUtils.isNotBlank(uuidDealer)) {
			condition.append(" and amu.msDealer.uuidDealer=:uuidDealer");
			paramMap.put("uuidDealer", Long.valueOf(uuidDealer));
		}
		condition.append(" and amu.amMssubsystem.uuidMsSubsystem=:uuidMsSubsystem");
		condition.append(" and amu.isActive=:isActive");
		paramMap.put("uuidMsSubsystem", uuidSubsystem);
		paramMap.put("isActive", "1");
		
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by amu.loginId asc");
			
		result = this.getManagerDAO().selectAll(
				"from AmMsuser amu "
				+ "join fetch amu.msJob "
				+ "join fetch amu.msBranch "
				+ "join fetch amu.msDealer "
				+ "join fetch amu.amMssubsystem "
				+ "where 1=1"
					+ condition.toString() + orderQuery.toString(),
				"select count(*) "
				+ "from AmMsuser amu "
				+ "join amu.msJob "
				+ "join amu.msBranch "
				+ "join amu.msDealer "
				+ "join amu.amMssubsystem "
				+ "where 1=1"
					+ condition.toString(),	paramMap, pageNumber, pageSize);
			
		List<AmMsuser> listUser = (List) result
				.get(GlobalKey.MAP_RESULT_LIST);
		result.put(GlobalKey.MAP_RESULT_LIST, listUser);
		return result;
	}

	public boolean cekParam(String[][] params, int x) {
		return (params[x].length == 2) && (params[x][0] != null)
				&& (!"".equals(params[x][0])) && (params[x][1] != null)
				&& (!"".equals(params[x][1]));
	}

	public String[] getBranchByLogin(long uuid) {

		Object[][] params = { { "uuidBranch", uuid } };

		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getBranchByLogin", params, null);
		if (!list.isEmpty()) {
			String[] stringResult = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = map.get("d0").toString();
			}
			return stringResult;
		} 
		else {
			return null;
		}
	}

	public Long[] getDealerByLogin(long uuid) {

		Object[][] params = { { "uuidDealer", uuid } };

		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getDealerByLogin", params, null);
		if (!list.isEmpty()) {
			Long[] stringResult = new Long[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = Long.valueOf(map.get("d0").toString());
			}
			return stringResult;
		} 
		else {
			return null;
		}

	}

	public String getParentJobByLoginJob(long uuid) {

		Object[][] params = { { "uuidJob", uuid } };

		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"lookup.user.getParentJobByjobLogin", params, null);
		if (!list.isEmpty()) {
			Map map = list.get(0);
			if (map.get("d1") != null) {
				return map.get("d1").toString();
			}
			else {
				return null;
			}
		} 
		else {
			return null;
		}
	}
}
