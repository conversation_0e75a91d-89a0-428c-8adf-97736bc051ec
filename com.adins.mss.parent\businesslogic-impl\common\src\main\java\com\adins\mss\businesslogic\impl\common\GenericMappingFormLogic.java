package com.adins.mss.businesslogic.impl.common;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.MappingFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsMappingformD;
import com.adins.mss.model.MsMappingformH;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.custom.MappingFormBean;
import com.google.gson.Gson;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericMappingFormLogic extends BaseLogic implements MappingFormLogic, MessageSourceAware {
	private AuditInfo auditInfo;
	
	@Autowired
	private MessageSource messageSource;

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public GenericMappingFormLogic() {
		String[] pkCols = { "uuidMappingFormH" };
		String[] pkDbCols = { "UUID_MAPPING_FORM_H" };
		String[] cols = { "uuidMappingFormH", "mappingName", "mappingDesc", "msForm.uuidForm",
				"numberTaskAssignment", "isActive" };
		String[] dbCols = { "UUID_MAPPING_FORM_H", "MAPPING_NAME", "MAPPING_DESC", "UUID_FORM",
				"NUMBER_TASK_ASSIGNMENT", "IS_ACTIVE" };
		this.auditInfo = new AuditInfo("MS_MAPPINGFORM_H", pkCols, pkDbCols,
				cols, dbCols);
	}
	
	@Override
	public Map<String, Object> listMappingForm(int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and mfh.isActive=:isActive");
		paramMap.put("isActive", "1");

		result = this.getManagerDAO().selectAll(
				"from MsMappingformH mfh join fetch mfh.msForm where 1=1"
						+ condition.toString(),
				"select count(*) from MsMappingformH mfh join mfh.msForm where 1=1"
						+ condition.toString(),
				paramMap, pageNumber, pageSize);

		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteMappingForm(long uuid, AuditContext callerId) {
		MsMappingformH obj = this.getManagerDAO().selectOne(MsMappingformH.class, uuid);
		obj.setDtmUpd(new Date());
		obj.setUsrUpd(callerId.getCallerId());
		obj.setIsActive("0");
		this.auditManager.auditEdit(obj, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(obj);
	}
	
	@Override
	public MsMappingformH getMappingForm(long uuid, AuditContext callerId){
		MsMappingformH result = this.getManagerDAO().selectOne(
				"from MsMappingformH mmfh join fetch mmfh.msForm where mmfh.uuidMappingFormH = :uuidMappingFormH", 
				new Object[][] {{"uuidMappingFormH", uuid}});

		return result;
	}
	
	@Override
	public List getFormOrderList(Object params, Object orders, AuditContext callerId, String flag){
		List result = null;
		if("1".equals(flag)){
			result = this.getManagerDAO().selectAllNative(
					"common.mappingform.getFormOrderListVersioning2", params, orders);
		}
		else{
			result = this.getManagerDAO().selectAllNative(
					"common.mappingform.getFormOrderListVersioning", params, orders);
		}
		
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveMappingFormH(String task, String uuidQuestionSurvey, String uuidQuestionOrder, MsMappingformH formH,
			String paramUuidFormSurvey, String paramFormSurveyVersion, AuditContext callerId) {
		if ("Add".equals(task)){
			// table header form
			MsMappingformH obj = new MsMappingformH();
			obj.setMappingName(formH.getMappingName());
			obj.setMappingDesc(formH.getMappingDesc());
			obj.setFormVersion(formH.getFormVersion());
			MsForm mf = this.getManagerDAO().selectOne(MsForm.class, formH.getMsForm().getUuidForm());
			obj.setMsForm(mf);
			obj.setNumberTaskAssignment(formH.getNumberTaskAssignment());
			obj.setIsActive("1");
			obj.setDtmCrt(new Date());
			obj.setUsrCrt(callerId.getCallerId());
			this.getManagerDAO().insert(obj);
			this.auditManager.auditAdd(obj, auditInfo, callerId.getCallerId(), "");
			// end table header form
			
			// table detail form
			MsMappingformD mmfd = new MsMappingformD();
			mmfd.setDtmCrt(new Date());
			mmfd.setUuidMappingFormD(obj.getUuidMappingFormH());
			mmfd.setUsrCrt(callerId.getCallerId());
			//question mapping
			
			//begin set json
			List<MappingFormBean> listFormQuestion = new ArrayList<MappingFormBean>();
			String[] splitUuidSurvey = paramUuidFormSurvey.split("\\^");
			String[] splitVersionSurvey = paramFormSurveyVersion.split("\\^");
			for(int i=0; i<splitUuidSurvey.length; i++){
				MappingFormBean bean = new MappingFormBean();
				bean.setFormId(splitUuidSurvey[i]);
				bean.setFormVersion(Integer.parseInt(splitVersionSurvey[i]));
				List<MappingFormBean> listQuestionMapping = new ArrayList<MappingFormBean>();
				String[] questionSurvey = uuidQuestionSurvey.split("\\^");
				String[] questionOrder = uuidQuestionOrder.split("\\^");
				for(int j=0;j<questionOrder.length; j++){
					String[] questionSurveySplit = questionSurvey[j].split("\\|");
					if(questionOrder[j] != null && !"".equals(questionOrder[j])){
						if(questionSurveySplit[1].equals(String.valueOf(i))){
							MappingFormBean bean2 = new MappingFormBean();
							bean2.setUuidQuestion1(questionSurveySplit[0]);
							bean2.setUuidQuestion2(questionOrder[j]);
							listQuestionMapping.add(bean2);
						}
					}
				}
				bean.setListQuestionMapping(listQuestionMapping);
				listFormQuestion.add(bean);
			}
			String qm =  new Gson().toJson(listFormQuestion);
			mmfd.setQuestionMapping(qm);
			//end set json
			
			MsMappingformH mmfh = this.getManagerDAO().selectOne(MsMappingformH.class, obj.getUuidMappingFormH());
			mmfd.setMsMappingformH(mmfh);
			this.getManagerDAO().insert(mmfd);
		}
	}
	
	@Override
	public List getQuestionOrder(Object params, Object orders, AuditContext callerId) {
		List<Map<String, Object>> result = this.getManagerDAO().selectAllNative(
				"common.mappingform.getQuestionQSet", params,orders);
		return result;
	}
	
	private List getQuestionByAnswerType(Object params, Object orders, AuditContext callerId) {
		List<Map<String, Object>> result = this.getManagerDAO().selectAllNative(
				"common.mappingform.getQuestionByAnswer", params,orders);
		return result;
	}
	
	@Override
	public List getQuestionSurvey(Object paramsSurvey, Object paramsOrder, AuditContext callerId) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		List<Map<String, Object>> listQuestionSurvey = this.getQuestionOrder(paramsSurvey, null, callerId);
		for (Map map : listQuestionSurvey) {
			String[][] param = { { "paramUuidForm", ((String[][])paramsOrder)[0][1] }, 
					{ "answerType", map.get("d2").toString() } };
			List tmp = this.getQuestionByAnswerType(param, null, callerId);
			map.put("list", tmp);
			result.add(map);
		}
		return result;
	}
	
	@Override
	public String getQuestionMapping(Object params, AuditContext callerId){
		String questionMapping = null;
		MsMappingformD bean = this.getManagerDAO().selectOne(MsMappingformD.class, params);
		if(bean != null){
			questionMapping = bean.getQuestionMapping();
		}
		
		return questionMapping;
	}
	
	@Override
	public String getFormSurveyName(Object params, AuditContext callerId){
		MsForm bean = this.getManagerDAO().selectOne(MsForm.class, params);
		String formSurveyName = bean.getFormName();

		return formSurveyName;
	}
	
	@Override
	public List getQuestionLabel(Object params, AuditContext callerId){
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		String[][] tmpParams = (String[][])params;
		String[] question = tmpParams[0][1].split(",");
		for(int i=0; i<question.length; i++){
			String[] questionSurveyOrder = question[i].split(":");
			MsQuestion bean = this.getManagerDAO().selectOne(MsQuestion.class, Long.valueOf(questionSurveyOrder[0].trim()));
			MsQuestion bean2 = this.getManagerDAO().selectOne(MsQuestion.class, Long.valueOf(questionSurveyOrder[1].trim()));
			Map mapQuestion = new HashMap();
			mapQuestion.put("pertanyaanSurvey", bean.getQuestionLabel());
			mapQuestion.put("pertanyaanOrder", bean2.getQuestionLabel());
			result.add(mapQuestion);
		}

		return result;
	}
	
	@Override
	public void checkMappingName(String mappingName, AuditContext callerId) {
		Object [][] params = { { Restrictions.eq("mappingName", mappingName)} };
		Map<String, Object> mappingForm = this.getManagerDAO().list(MsMappingformH.class, params, null);
		List<MsMappingformH> listMapping = (List<MsMappingformH>) mappingForm.get(GlobalKey.MAP_RESULT_LIST);
		
		if (!listMapping.isEmpty()) {
			throw new EntityNotUniqueException(
					this.messageSource.getMessage("service.global.existed", 
							new Object[]{"Mapping Name "+ mappingName}, this.retrieveLocaleAudit(callerId))
							, mappingName);
		}
	}
}
