package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.businesslogic.api.common.MappingUserDealerLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.MappingUserDealerException;
import com.adins.mss.exceptions.MappingUserDealerException.Reason;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsMappingUserDealer;
import com.adins.mss.model.TrUploadtasklog;
import com.adins.mss.model.custom.MappingUserDealerBean;
import com.google.common.base.Stopwatch;

@SuppressWarnings({"unchecked","rawtypes"})
public class GenericMappingUserDealerLogic extends BaseLogic implements
		MappingUserDealerLogic {
    private static final Logger LOG = LoggerFactory.getLogger(GenericMappingUserDealerLogic.class);
    private static final String[] TEMPLATE_HEADER = { "Username", "Branch", "Dealer Code", 
			"Is Active" };
    private static final int[] HEADER_COLUMN_WIDTH = { 20 * 256, 40 * 256,
			20 * 256, 10 * 256 };
    MappingUserDealerBean mappingUserDealerBean = new MappingUserDealerBean();
	
	@Autowired
	private MessageSource messageSource;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	@Transactional(readOnly = true)
	@Override
	public List listMappingUserDealer(Object[][] params, String type, AuditContext callerId) {
		List result = null;
		
		for (int i = 0;  i < params.length; i++) {
			params[i][1] = "".equalsIgnoreCase(params[i][1].toString()) ? "%" : params[i][1];
		}		
		
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
		StringBuilder ordersQueryString = this.sqlPagingOrderBuilder((String) ((Object[][]) params)[6][1], callerId);		
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT * FROM ( ")
			.append("			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("				select ")
			.append("					mud.USERNAME, ")
			.append("					am.FULL_NAME, ")
			.append("					DEALER_CODE, ")
			.append("					mud.IS_ACTIVE, ")
			.append("					ID, ")
			.append("			    	ROW_NUMBER() OVER (")
			.append(ordersQueryString)
			.append(					") AS rownum, ")
			.append("			    	msb.BRANCH_NAME ")
			.append("				from MS_MAPPING_USER_DEALER mud with (nolock) ")
			.append("					JOIN AM_MSUSER am with (nolock) ON mud.USERNAME = am.LOGIN_ID ")
			.append("					LEFT JOIN MS_BRANCH msb with (nolock) ON mud.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("				where 1=1 ")
			.append("				and DEALER_TYPE = :type ")
			.append(paramsQueryString)
			.append("			) a  WHERE a.ROWNUM <= :end ")
			.append("		) b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[]{"type", type});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[5][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
		result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		return result;
	}
	
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null){
			return new StringBuilder();
		}
		
		StringBuilder sb = new StringBuilder();	
		
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append(" and mud.USERNAME LIKE '%'+ :userName +'%' ");
			paramStack.push(new Object[]{"userName", (String) params[0][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append(" and am.FULL_NAME LIKE '%'+ :fullName +'%' ");
			paramStack.push(new Object[]{"fullName", (String) params[1][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append(" and DEALER_CODE LIKE '%'+ :dealerCode +'%' ");
			paramStack.push(new Object[]{"dealerCode", (String) params[2][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append(" and mud.IS_ACTIVE = :isActive ");
			paramStack.push(new Object[]{"isActive", (String) params[3][1]});
		}
		
		return sb;
	}

	@Transactional(readOnly = true)
	@Override
	public Integer countListMappingUserDealer(Object[][] params, String type, AuditContext callerId) {
		Integer result = 0;
		
		for (int i = 0;  i < params.length; i++) {
			params[i][1] = params[i][1].toString().isEmpty() ? "%" : params[i][1];
		}
		
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("select count(1) ")
			.append("from MS_MAPPING_USER_DEALER mud with (nolock) ")
			.append("JOIN AM_MSUSER am with (nolock) ON mud.USERNAME = am.LOGIN_ID ")
			.append("where 1=1 ")
			.append("AND DEALER_TYPE = :type ")
			.append(paramsQueryString);
		
		paramsStack.push(new Object[]{"type", type});
				
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}
	
	@Transactional(readOnly=true)
	@Override
	public byte[] exportExcel(AuditContext callerId) {

		HSSFWorkbook workbook = this.createXlsTemplate();
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	public HSSFWorkbook createXlsTemplate() {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Mapping User Dealer List");
			this.createHeader(workbook, sheet);
			this.setTextDataFormat(workbook, sheet);
			this.setListDataValidation(workbook,sheet);
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private void createHeader(HSSFWorkbook workbook, HSSFSheet sheet) {
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);
		style.setBorderBottom(style.BORDER_DOUBLE);
		style.setFillPattern(style.SOLID_FOREGROUND); 
		style.setFillBackgroundColor(HSSFColor.GREY_25_PERCENT.index);
		style.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index);
		style.setAlignment(style.ALIGN_CENTER);
		
		HSSFRow row = sheet.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			HSSFCell cell = row.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER[i]);
			cell.setCellStyle(style);
			sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH[i]);
		}
		
		HSSFCellStyle style1 = workbook.createCellStyle();
		sheet.protectSheet("header");
		style1.setLocked(false);	
		style1.setDataFormat((short)BuiltinFormats.getBuiltinFormat("text")); 

		for (int i = 1; i < 2000; i++) {
			HSSFRow allRow = sheet.createRow(i);
			for (int j = 0; j < 4; j++) {
				HSSFCell cell = allRow.createCell(j);
				cell.setCellStyle(style1);
			}
		}
	}
	
	private void setTextDataFormat(HSSFWorkbook workbook, HSSFSheet sheet) {
		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("@"));

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			sheet.setDefaultColumnStyle(i, style);
		}
	}
	
	private void setListDataValidation(HSSFWorkbook workbook, HSSFSheet sheet) {
		//Data Validation untuk Is Active
		CellRangeAddressList cellIsActive = new CellRangeAddressList(
			    1, 2000, 3, 3);
		
		DVConstraint listIsActive = DVConstraint.createExplicitListConstraint(
			    new String[]{"0", "1"});
		DataValidation validationIsActive = new HSSFDataValidation
			    (cellIsActive, listIsActive);
		validationIsActive.setSuppressDropDownArrow(false);
		sheet.addValidationData(validationIsActive);
		
		
		List listBranch = this.getManagerDAO().selectAllNativeString("SELECT a.NAMA FROM ( " + 
				"select RTRIM(LTRIM(REPLACE(branch_name, ' SYARIAH', ''))) AS NAMA " + 
				"from ms_branch msb with (NOLOCK)) a " + 
				"GROUP BY a.NAMA", null);
		// buat Data Master untuk menyimpan isi dropdown
		HSSFSheet sheetDM = workbook.createSheet("Data Master");
		this.createDataMaster(workbook, sheetDM, listBranch);
		
		//Data Validation untuk Branch
		CellRangeAddressList cellBranch = new CellRangeAddressList(
				1, 2000, 1, 1);
		DVConstraint dvConstraintBranch = DVConstraint
				.createFormulaListConstraint("'Data Master'!$A$3:$A$"
						+ (listBranch.size() + 2));
		HSSFDataValidation dataValidBranch = new HSSFDataValidation(
				cellBranch, dvConstraintBranch);
		dataValidBranch.setSuppressDropDownArrow(false);
		dataValidBranch.setEmptyCellAllowed(false);
		sheet.addValidationData(dataValidBranch);
	}
	
	private void createDataMaster(HSSFWorkbook workbook, HSSFSheet sheet, List listBranch) {

		// title
		HSSFCellStyle titleStyle = workbook.createCellStyle();
		titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
		titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
		titleStyle.setFillBackgroundColor(HSSFColor.BLACK.index);
		titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
		HSSFFont titleFont = workbook.createFont();
		titleFont.setBoldweight((short) 1000);
		titleFont.setColor(HSSFColor.WHITE.index);
		HSSFRow rowLabel = sheet.createRow(1);
		DataFormat format = workbook.createDataFormat();
		titleStyle.setDataFormat(format.getFormat("@"));

		//header
		HSSFCell cell = rowLabel.createCell(0);
		cell.setCellValue(GlobalVal.HEADER_BRANCH_DESCRIPTION);
		titleStyle.setFont(titleFont);
		cell.setCellStyle(titleStyle);

		// value
		HSSFCellStyle valStyle = workbook.createCellStyle();
		valStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
		valStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		valStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		valStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);

		int rowSize = listBranch.size();

		Map valBranch = null;

		for (int i = 0; i < rowSize; i++) {
			HSSFRow rowLabelVal = sheet.createRow(i + 2);
			if (i < listBranch.size()) {
				valBranch = (Map) listBranch.get(i);
				HSSFCell cellb = rowLabelVal.createCell(0);
				cellb.setCellValue(valBranch.get("d0").toString());
				cellb.setCellStyle(valStyle);
				sheet.autoSizeColumn(i);
			}
		}
		
		sheet.protectSheet("Data Master");
	}
		
	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public Map<String, Object> processSpreadSheetSaveToFile(File uploadedFile,
			String fileName, AmMsuser loginBean, String mode, AuditContext callerId)
			throws IOException {
		Map<String, Object> ret = new HashMap<String, Object>();
		Date date = new Date();
		Stopwatch totalSw = Stopwatch.createStarted();
		Stopwatch sw = Stopwatch.createUnstarted();
		Map parseMap;
		
		fileName = fileName.split("\\.")[0];
		if(mode.equalsIgnoreCase(GlobalVal.MAPPING_DEALER_TYPE_FOCUS)) {
			if(!fileName.contains(GlobalVal.TEMPLATE_MAPPING_USER_DEALER_FOKUS)) {
				throw new MappingUserDealerException(this.messageSource.getMessage(
						"businesslogic.uploadtask.wrongtemplate", null, this.retrieveLocaleAudit(callerId)),
						Reason.ERROR_TEMPLATE);
			}
		}
		else {
			if(!fileName.contains(GlobalVal.TEMPLATE_MAPPING_USER_DEALER_NON_FOKUS)) {
				throw new MappingUserDealerException(this.messageSource.getMessage(
						"businesslogic.uploadtask.wrongtemplate", null, this.retrieveLocaleAudit(callerId)),
						Reason.ERROR_TEMPLATE);
			}
		}
		
		try {
			sw.start();
			parseMap = this.parseSpreadsheetToMappingBeans(uploadedFile,mode,callerId);
			sw.stop();
			LOG.debug("Excel parsing time: {}(ms).",
					sw.elapsed(TimeUnit.MILLISECONDS));
		} 
		catch (Exception e) {
			ret = this.errorUpload(e);
			return ret;
		}
		
		try {
			saveFile(uploadedFile, loginBean, fileName, date, mode,
					callerId);
		} 
		catch (Exception e) {
			throw new MappingUserDealerException(this.messageSource.getMessage(
					"businesslogic.global.errorgenxld", null, this.retrieveLocaleAudit(callerId)),
					e, Reason.ERROR_GENERATE);
		}

		totalSw.stop();
		LOG.debug("===Finish processing excel time: {}(ms).",
				totalSw.elapsed(TimeUnit.MILLISECONDS));
		return ret;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private void saveFile(File uploadedFile, AmMsuser loginBean,
			String fileName, Date date, String mode, AuditContext callerId)
			throws IOException {
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		FileInputStream inputStream = new FileInputStream(uploadedFile);
		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		String path = StringUtils.EMPTY;
		String storeFileName = StringUtils.EMPTY;

		LOG.info("File name from uploaded file : {}", fileName);
		String[] splitFile = StringUtils.split(fileName,
				SystemUtils.FILE_SEPARATOR);
		String splitFileName = splitFile[splitFile.length - 1];
		LOG.info("File name after remove prefix file path : {}", splitFileName);

		try {
			storeFileName = StringUtils.remove(splitFileName, ".xls") + "_"
					+ loginBean.getLoginId() + "_"
					+ DateFormatUtils.format(date, "yyyyMMdd HHmmss") + ".xls";
			LOG.info("Store file name : {}", storeFileName);

			Object[][] param = { {"gsCode",
					GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH } };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(
					AmGeneralsetting.class, param);

			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.uploadtask.locationnotdefined", null,
						this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}

			path = amGeneralSetting.getGsValue() + storeFileName;
			LOG.info("Stored file location : {}", path);
			wb.write(stream);
		} 
		finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (wb != null) {
					wb.close();
				}
			} 
			catch (IOException e) {
				LOG.error(
						"SCHEDULER error while close workbook. params[form={}]",
						fileName);
			}
		}

		byte[] exp = stream.toByteArray();

		TrUploadtasklog trUploadTaskLog = new TrUploadtasklog();
		trUploadTaskLog.setAmMsuser(loginBean);
		trUploadTaskLog.setDtmUpload(date);
		trUploadTaskLog.setInputFilename(storeFileName);
		trUploadTaskLog.setIsFinish("0");
		if(mode.equalsIgnoreCase(GlobalVal.MAPPING_DEALER_TYPE_FOCUS)) {
			trUploadTaskLog.setType(GlobalVal.MAPPING_DEALER_TYPE_FOCUS);
		}else {
			trUploadTaskLog.setType(GlobalVal.MAPPING_DEALER_TYPE_NON_FOCUS);
		}		
		
		this.getManagerDAO().insert(trUploadTaskLog);

		FileOutputStream fileOut = new FileOutputStream(path);
		try {
			fileOut.write(exp);
			fileOut.flush();
		} 
		finally {
			fileOut.close();
		}
	}
	
	// error condition untuk di action
	private Map<String, Object> errorUpload(Exception e) {
		Map<String, Object> ret = new HashMap<String, Object>();
		byte[] tmp = new byte[1];
		String msg = StringUtils.EMPTY;
		if (e instanceof MappingUserDealerException) {
			if (((MappingUserDealerException) e).getReason().equals(
					Reason.ERROR_MAX_EXCEEDED)) {
				tmp[0] = 2;
			} 
			else if (((MappingUserDealerException) e).getReason().equals(
					Reason.ERROR_TEMPLATE)) {
				tmp[0] = 1;
			}
			msg = e.getMessage();
			ret.put("exp", tmp);
			ret.put("msg", msg);
		} 
		else {
			tmp[0] = 3;
			ret.put("exp", tmp);
		}
		return ret;
	}
	
	private Map parseSpreadsheetToMappingBeans( File uploadedFile, String mode, AuditContext callerId ) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsMappingUserDealer> result = new ArrayList<>();
		List<MsMappingUserDealer> resultError = new ArrayList<>();

		FileInputStream inputStream = null;

		try {
			inputStream = new FileInputStream(uploadedFile);
			HSSFWorkbook wb = new HSSFWorkbook(inputStream);
			HSSFSheet sheet = wb.getSheetAt(0);
			
			//cek header
			HSSFRow rowHeader = sheet.getRow(0);
	
			for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
				HSSFCell cell = rowHeader.getCell(i);
				if(StringUtils.isEmpty(cell.getStringCellValue())){
					wb.close();
					throw new IOException();
				}else if(!TEMPLATE_HEADER[i].equalsIgnoreCase(cell.getStringCellValue())){
					wb.close();
					throw new IOException();
				}
			}
			//end cek header
			int rowsEmpty = 0;
			int rows = sheet.getPhysicalNumberOfRows();
			for (int r = 1; r < rows; r++) {
				HSSFRow row = sheet.getRow(r);
				if (row == null) {
					continue;
				}
				
	    		boolean isEmptyRow = checkEmptyRow(row); 
	    				
	    		if (isEmptyRow == true){
	    			rowsEmpty++;
	    			continue;
	    		}
	    		MsMappingUserDealer mud = new MsMappingUserDealer();
	    		MsBranch msBranch = new MsBranch();
				for (int c = 0; c < 4; c++) {
					HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
									
					String value = "";
					int intValue = -1;
					
					// if intValue -1, then sequence is posted with string
					mud.setDtmCrt(new Date());
					mud.setUsrCrt(callerId.getCallerId().toString());
					if(mode.equalsIgnoreCase(GlobalVal.MAPPING_DEALER_TYPE_FOCUS)) {
						mud.setDealerType(GlobalVal.MAPPING_DEALER_TYPE_FOCUS);
					}else {
						mud.setDealerType(GlobalVal.MAPPING_DEALER_TYPE_NON_FOCUS);
					}
					mud.setDtmUpd(null);
					mud.setUsrUpd(null);
					mud.setMsBranch(msBranch);
					
					if (cell != null) {
						switch (cell.getCellType()) {
	    					case HSSFCell.CELL_TYPE_NUMERIC:
	    						value = String.valueOf((int) cell.getNumericCellValue());
	    						intValue = Integer.valueOf(value).intValue();
	    						break;
	    
	    					case HSSFCell.CELL_TYPE_STRING:
	    						value = cell.getStringCellValue();
	    						break;
	    
	    					default:
						}
					}
					
					switch (c) {
					case 0:
						mud.setUsername(value);
						break;
					case 1:
						mud.getMsBranch().setBranchName(value);
						break;
					case 2:
						mud.setDealerCode(value);
						break;
					case 3:
						mud.setIsActive(value);
						break;
					}
					wb.close();
				}
			}
			
			if(rowsEmpty == rows-1) {
    			throw new MappingUserDealerException(this.messageSource.getMessage(
						"businesslogic.global.errortemplateempty", null, this.retrieveLocaleAudit(callerId)),
						Reason.ERROR_TEMPLATE);
			}
			
			paramParse.put("result", result);
			paramParse.put("resultError", resultError);
			return paramParse;
		}
		finally {
			if (inputStream != null) {
				inputStream.close();
			}
		}
	}
	
	private boolean checkEmptyRow(HSSFRow row) {
		String[] isEmptyCell = new String [10]  ;
		Arrays.fill(isEmptyCell, "");

		for (int c = 0; c < 10; c++) {
			HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
			if (cell == null){
				isEmptyCell[c]="empty";
			}
		}
		
		if ("empty".equals(isEmptyCell[0]) && "empty".equals(isEmptyCell[1]) 
				&& "empty".equals(isEmptyCell[2]) && "empty".equals(isEmptyCell[3])){
			return true;
		}
		else{
			return false;
		}
	}
	
	public Map checkingUserDealerUpload(MsMappingUserDealer mud) {
		Map<String, Object> resultCheck = new HashMap<>();
		List<MsMappingUserDealer> listMappingByUnique = new ArrayList<>();
	    StringBuilder errorUpload = new StringBuilder();
	    //Jika username tidak diisi
		if (("").equalsIgnoreCase(mud.getUsername())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append("Username harus diisi ");
		}
		else {
			if (!("").equalsIgnoreCase(mud.getMsBranch().getBranchName())) {
				//Pengecekan ada tidaknya username di AmMsuser
				Integer uuidUser = (Integer) this.getManagerDAO().selectOneNativeString("SELECT count(1) FROM AM_MSUSER with (nolock) WHERE UNIQUE_ID = :uniqueId ",
						new Object[][] {{"uniqueId", mud.getUsername()}});
				if (0 == uuidUser) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append("Username tidak terdaftar");
				}
				else {
					//Jika user terdaftar
					AmGeneralsetting jobCMO = this.getManagerDAO().selectOne(AmGeneralsetting.class, 
							new Object [][] { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_JOBSVY)} });
					String branchName = mud.getMsBranch().getBranchName();
					boolean strip = branchName.contains("-");
					StringBuilder joinBranch = new StringBuilder();
					StringBuilder whereBranch = new StringBuilder();
					Stack<Object[]> paramsStack = new Stack<>();
					
					if (strip) { //Jika Branchname ada tanda -
						String first = branchName.substring( 0, branchName.indexOf("-"));
						String last = branchName.substring(branchName.indexOf("-") + 1, branchName.length());
												
						joinBranch.append("JOIN (SELECT BRANCH_NAME FROM MS_BRANCH WITH (NOLOCK) ")
									.append("WHERE CHARINDEX(:first , BRANCH_NAME) != 0 ")
									.append("AND CHARINDEX(:last, BRANCH_NAME) != 0) bn ")
									.append("ON mb.BRANCH_NAME = bn.BRANCH_NAME ");
						
						paramsStack.push(new Object[]{"first", first});
						paramsStack.push(new Object[]{"last", last});
					} else {	
						whereBranch.append("and mb.BRANCH_NAME like '%'+ :branchName +'%' ");
						paramsStack.push(new Object[]{"branchName", branchName});
					}
					
					StringBuilder sb = new StringBuilder()
							.append("SELECT LOGIN_ID FROM AM_MSUSER amu with (nolock) ")
							.append("JOIN MS_JOB mj with (nolock) ON amu.UUID_JOB = mj.UUID_JOB ")
							.append("JOIN MS_BRANCH mb with (nolock) ON amu.UUID_BRANCH = mb.UUID_BRANCH ")
							.append(joinBranch)
							.append("WHERE amu.UNIQUE_ID = :uniqueId ")
							.append("and mj.JOB_CODE= :jobCode ")
							.append(whereBranch);
					
					paramsStack.push(new Object[]{"uniqueId", mud.getUsername()});
					paramsStack.push(new Object[]{"jobCode", jobCMO.getGsValue()});
							
					Object[][] sqlParams = new Object[paramsStack.size()][2];
				    for (int i = 0; i < paramsStack.size(); i++) {
						Object[] objects = paramsStack.get(i);
						sqlParams[i] = objects;
					}
					
					List userSvy =  (List) this.getManagerDAO().selectAllNativeString(sb.toString(), sqlParams);
					boolean flagIsActive = false; //Untuk penjagaan agar pesan "username tidak aktif"nya tidak dobel
					boolean flagIsMapped = false;
					if (userSvy.isEmpty()) { //Jika tidak ada user CMO dengan branch yng di pilih
						if (!("").equals(errorUpload.toString())) {
							errorUpload.append(" | ");
						}
						errorUpload.append("User CMO dengan Branch " + mud.getMsBranch().getBranchName() + " tidak ditemukan");
					}
					else {
						List <MsMappingUserDealer> listTemp = new ArrayList<>();
						for (int i = 0; i < userSvy.size(); i++) {
							Map loginId = (Map) userSvy.get(i);					
							AmMsuser userCMO = this.getManagerDAO().selectOne(AmMsuser.class,
									new Object[][] { { Restrictions.eq("loginId", loginId.get("d0").toString()) } });
							this.getManagerDAO().fetch(userCMO.getMsBranch());
							int isActive = Integer.valueOf(userCMO.getIsActive());
							MsMappingUserDealer mapping = new MsMappingUserDealer();
							mapping.setDealerCode(mud.getDealerCode());
							mapping.setDealerType(mud.getDealerType());
							mapping.setIsActive(mud.getIsActive());
							mapping.setUsername(loginId.get("d0").toString());
							mapping.setMsBranch(userCMO.getMsBranch());
							listTemp.add(mapping);
							if (1 == isActive && !flagIsActive) { //Jika user tidak aktif
								flagIsActive = true;
							}
							if ("0".equals(mud.getIsActive())) {
								Object[][] params = { { Restrictions.eq("username", userCMO.getLoginId()) }, 
										{ Restrictions.eq("msBranch.uuidBranch", userCMO.getMsBranch().getUuidBranch()) },
										{ Restrictions.eq("dealerType", mud.getDealerType()) } };
								Map<String, Object> result = this.getManagerDAO().selectAll(MsMappingUserDealer.class, params, null);
								Integer countResult = Integer.parseInt(String.valueOf(result.get(GlobalKey.MAP_RESULT_SIZE)));
								if (0 < countResult && !flagIsMapped) {
									flagIsMapped = true;
								}
								if (flagIsMapped && 0 == isActive) {
									flagIsActive = true;
								}
							} else {
								flagIsMapped = true;
							}
						}
						if (flagIsActive && flagIsMapped) {
							for (int i = 0; i < listTemp.size(); i++) {
								MsMappingUserDealer msMappingUserDealer = listTemp.get(i);
								listMappingByUnique.add(msMappingUserDealer);
							}
						} else {
							if (!flagIsActive) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append("Username tidak aktif");
							}
							if (!flagIsMapped) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append("Nilai is Active tidak boleh 0 pada User yang belum memiliki mapping");
							}
						}
					}
				}
			}
		}
		
		if (("").equalsIgnoreCase(mud.getMsBranch().getBranchName())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append("Branch harus diisi ");
		}
		
		if (("").equals(mud.getDealerCode())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append("Dealer code harus diisi ");
		}
		
		if (("").equals(mud.getIsActive())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append("Is Active harus diisi ");
		}
		
		resultCheck.put("mappingUser", listMappingByUnique);
		resultCheck.put("errorUpload", errorUpload);		
		return resultCheck;
	}
	
	//error condition untuk di action
	private byte[] errorUpload(){
		byte[] tmp = new byte[1];
		tmp[0]=1;//for condition in action
		return tmp;
	}
	
	public String getMsMappingUserDealerExist(MsMappingUserDealer mud) {
		String userName = StringUtils.trimToNull(String.valueOf(mud.getUsername()));
		Object [][] params = { {"userName", userName}, {"type", mud.getDealerType()},
				{"dealerCode", mud.getDealerCode()}, {"uuidBranch", mud.getMsBranch().getUuidBranch()} };
		
		BigInteger cekUserName = (BigInteger) this.getManagerDAO().selectOneNativeString(
				"SELECT ID "
				+ " FROM MS_MAPPING_USER_DEALER with (nolock) "
				+ " WHERE USERNAME = :userName "
				+ " AND DEALER_TYPE = :type "
				+ " AND DEALER_CODE = :dealerCode "
				+ " AND UUID_BRANCH = :uuidBranch ", params);

		String uuid = "";
		if (cekUserName != null) {
			uuid = String.valueOf(cekUserName);
		}
		
		return uuid;
	}
	
	@Transactional
	@Override
	public void updateMappingUserDealer(MsMappingUserDealer mud, int uuidExist, AuditContext callerId) {
		MsMappingUserDealer dbModel = this.getManagerDAO().selectOne(MsMappingUserDealer.class,
				Long.valueOf(uuidExist));

		dbModel.setIsActive(mud.getIsActive());
		dbModel.setDealerCode(mud.getDealerCode());
		dbModel.setMsBranch(mud.getMsBranch());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId().toString());

		this.getManagerDAO().update(dbModel);
	}
	
	@Transactional
	@Override
	public void insertMappingUserDealer(MsMappingUserDealer mud, AuditContext callerId) {
		mud.setDtmCrt(new Date());
		mud.setUsrCrt(callerId.getCallerId().toString());
		mud.setDtmUpd(null);
		mud.setUsrUpd(null);

		this.getManagerDAO().insert(mud);
	}
	
	private byte[] exportErrorMapping(List listMapping, int mappingSuccess, List mappingBeanList, String type)
			throws SQLException {

		HSSFWorkbook workbook = this.createXlsTemplateErrorMapping(listMapping,
				mappingSuccess, mappingBeanList, type);

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} catch (IOException e) {
            LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	private HSSFWorkbook createXlsTemplateErrorMapping(List listMapping, int mappingSuccess, List mappingBeanList, String type) {

		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = type.equalsIgnoreCase(GlobalVal.MAPPING_DEALER_TYPE_FOCUS) ?  
					workbook.createSheet("Mapping User Dealer List") : workbook.createSheet("Mapping User Dealer Non Fokus List");
			this.createHeaderErrorUpload(workbook, sheet, mappingSuccess,
					listMapping.size());
			this.setTextDataFormatErrorUpload(workbook, sheet);
			this.setDataErrorUpload(workbook, sheet, listMapping, mappingBeanList);
		} catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}
	
	private void createHeaderErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet, int mappingSuccess, int mappingError) {

		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);

		HSSFRow row0 = sheet.createRow(0);
		HSSFRow row1 = sheet.createRow(1);
		HSSFRow row2 = sheet.createRow(2);

		HSSFCell cell0 = row0.createCell(0);
		cell0.setCellValue("Total Sukses diupload : " + mappingSuccess);
		cell0.setCellStyle(style);

		HSSFCell cell1 = row1.createCell(0);
		cell1.setCellValue("Total Gagal diupload : " + mappingError);
		cell1.setCellStyle(style);

		HSSFCell cell2 = row2.createCell(0);
		cell2.setCellValue("Berikut data yang gagal di upload");
		cell2.setCellStyle(style);

		HSSFCellStyle styleheader = workbook.createCellStyle();
		HSSFFont fontheader = workbook.createFont();
		fontheader.setBoldweight((short) 1000);
		styleheader.setFont(fontheader);
		styleheader.setBorderBottom(styleheader.BORDER_DOUBLE);
		styleheader.setFillPattern(styleheader.SOLID_FOREGROUND); 
		styleheader.setFillBackgroundColor(HSSFColor.GREY_25_PERCENT.index);
		styleheader.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index);
		styleheader.setAlignment(styleheader.ALIGN_CENTER);
		
		HSSFRow row4 = sheet.createRow(5);

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			HSSFCell cell = row4.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER[i]);
			cell.setCellStyle(styleheader);
			sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH[i]);
		}
		sheet.protectSheet("header");
		
		
	}
	
	private void setTextDataFormatErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet) {

		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("@"));

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			sheet.setDefaultColumnStyle(i, style);
		}
	}

	private void setDataErrorUpload(HSSFWorkbook workbook, HSSFSheet sheet, List listMapping, List mappingBeanList) {
		List mappingList = listMapping;

		int j = 6;

		for (int k = 0; k < listMapping.size(); k++) {
			HSSFRow row = sheet.createRow(j);
			MsMappingUserDealer mud = (MsMappingUserDealer) mappingList.get(k);
			MappingUserDealerBean mappingBean = (MappingUserDealerBean) mappingBeanList.get(k);

			HSSFCell cell = row.createCell(0);
			cell.setCellValue(mud.getUsername());
			sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH[0]);

			HSSFCell cell1 = row.createCell(1);
			cell1.setCellValue(mud.getMsBranch().getBranchName());
			sheet.setColumnWidth(1, HEADER_COLUMN_WIDTH[1]);

			HSSFCell cell2 = row.createCell(2);
			cell2.setCellValue(mud.getDealerCode());
			sheet.setColumnWidth(2, HEADER_COLUMN_WIDTH[2]);
			
			HSSFCell cell3 = row.createCell(3);
			cell3.setCellValue(mud.getIsActive());
			sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH[3]);
			
			HSSFCell cell4 = row.createCell(4);
			cell4.setCellValue(mappingBean.getErrorText().toString());
			sheet.setColumnWidth(4, 80 * 256);

			j++;
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void doUploadMappingProcess(TrUploadtasklog trUploadTaskLog, String type,
			AuditContext callerId) {
		LOG.info("Start Job Parse Excel to Database. Filename={}.",
				trUploadTaskLog.getInputFilename());

		Object[][] paramPath = { { Restrictions.eq("gsCode",
				GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH) } };
		AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, paramPath);

		if (amGeneralSetting == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage(
					"businesslogic.uploadtask.locationnotdefined", null,
					this.retrieveLocaleAudit(callerId)),
					GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH);
		}

		String path = amGeneralSetting.getGsValue();
		File processedFile = null;
		FileInputStream inputStream = null;
		HSSFWorkbook wb = null;
		Date dateStart = null;

		// create folder date success/error
		File doneFolder = new File(path	+ DateFormatUtils.format(trUploadTaskLog.getDtmUpload(),"yyyy-MM-dd"));
		if (!doneFolder.exists()) {
			doneFolder.mkdirs();
		}

		try {
			Stopwatch swLoop = Stopwatch.createStarted();
			dateStart = new Date();
			Map<String, Object> parseMap = new HashMap<String, Object>();
			processedFile = new File(path + trUploadTaskLog.getInputFilename());
			inputStream = new FileInputStream(processedFile);
			wb = new HSSFWorkbook(inputStream);
			HSSFSheet sheet = wb.getSheetAt(0);

			parseMap = parseExcelIntoCollection(wb, sheet, type, callerId);

			Map mapReturn = loopDataToInsert(parseMap, callerId, type);
			
			byte[] errorUploadByte = (byte[]) mapReturn.get("errorUploadByte");
			String errorFileLocation = StringUtils.EMPTY;

			if (null != errorUploadByte) {
				// failed parse excel to database
				errorFileLocation = this.writeErrorFile(doneFolder,
						trUploadTaskLog.getInputFilename(), errorUploadByte, type);
			}

			// move uploaded file to done folder + date
			processedFile.renameTo(new File(doneFolder.getPath()
					+ SystemUtils.FILE_SEPARATOR + processedFile.getName()));

			Date dateFinish = new Date();
			trUploadTaskLog.setProcessStartTime(dateStart);
			trUploadTaskLog.setProcessFinishTime(dateFinish);
			trUploadTaskLog.setProcessDurationSeconds((int)Math
					.abs(dateFinish.getTime() - dateStart.getTime()) / 1000);
			trUploadTaskLog
					.setInputCount((Integer) mapReturn.get("inputCount"));
			trUploadTaskLog.setCountSuccess((Integer) mapReturn
					.get("countSuccess"));
			trUploadTaskLog
					.setCountError((Integer) mapReturn.get("countError"));
			if (StringUtils.isNotBlank(errorFileLocation)) {
				trUploadTaskLog.setErrorFileLocation(errorFileLocation);
			}
			trUploadTaskLog.setIsFinish("1");
			this.getManagerDAO().update(trUploadTaskLog);
			swLoop.stop();
			LOG.info(
					"Job Parse Excel to Database. Filename={}. Elapsed time=<{}>ms",
					trUploadTaskLog.getInputFilename(),
					swLoop.elapsed(TimeUnit.MILLISECONDS));
		} 
		catch (Exception ex) {
			Date dateFinish = new Date();
			String message = "Parsing excel error.";
			Workbook xlsError = this.createXlsError(message);

			String errorFileLocation = null;
			ByteArrayOutputStream stream = null;
			try {
				stream = new ByteArrayOutputStream();
				xlsError.write(stream);
				errorFileLocation = this.writeErrorFile(doneFolder,
						trUploadTaskLog.getInputFilename(),
						stream.toByteArray(), type);

//				 move uploaded file to done folder + date
				processedFile
						.renameTo(new File(doneFolder.getPath()
								+ SystemUtils.FILE_SEPARATOR
								+ processedFile.getName()));
			} 
			catch (IOException e) {
				LOG.error("Error writing warning excel", e);
			} 
			finally {
				try {
					if (stream != null) {
						stream.flush();
						stream.close();
					}
					if (xlsError != null)
						xlsError.close();
				} 
				catch (IOException e) {
					LOG.error("Error closing workbook", e);
				}
			}

			trUploadTaskLog.setProcessStartTime(dateStart);
			trUploadTaskLog.setProcessFinishTime(dateFinish);
			trUploadTaskLog.setProcessDurationSeconds((int)Math
					.abs(dateFinish.getTime() - dateStart.getTime()) / 1000);
			trUploadTaskLog.setErrorFileLocation(errorFileLocation);
			trUploadTaskLog.setIsFinish("1");
			this.getManagerDAO().update(trUploadTaskLog);
			LOG.warn("Error on Job Parse Excel to Database. Filename={}",
					trUploadTaskLog.getInputFilename(), ex);
		} 
		finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (wb != null) {
					wb.close();
				}
			} 
			catch (IOException e) {
				LOG.error(
						"SCHEDULER error while close workbook. params[filename={}]",
						trUploadTaskLog.getInputFilename());
			}
		}
	}
	
	private String writeErrorFile(File doneFolder, String inputFileName,
			byte[] errorUploadByte, String type) throws IOException {
		String errorFilename = StringUtils.EMPTY;
		
		if(type.equalsIgnoreCase(GlobalVal.MAPPING_DEALER_TYPE_FOCUS)) {
			errorFilename = "ErrorMappingDealerUpload-";
		}else {
			errorFilename = "ErrorMappingDealerNonFocusUpload-";
		}
		
		// failed parse excel to database
		String errorFileLocation = doneFolder.getPath()
				+ SystemUtils.FILE_SEPARATOR + errorFilename
				+ inputFileName;
		FileOutputStream fileOut = new FileOutputStream(errorFileLocation);
		try {
			fileOut.write(errorUploadByte);
			fileOut.flush();
		} 
		finally {
			fileOut.close();
		}

		return errorFileLocation;
	}
	
	private Workbook createXlsError(String errorMessage) {
		// ByteArrayOutputStream stream = new ByteArrayOutputStream();
		HSSFWorkbook workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet("Mapping Dealer List");

		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1000);
		styleHeader.setFont(fontHeader);

		HSSFRow row = sheet.createRow(1);
		HSSFCell cell = row.createCell(0);
		cell.setCellValue(errorMessage);
		cell.setCellStyle(styleHeader);
		sheet.setColumnWidth(0, 30 * 256);

		return workbook;
	}
	
	private Map parseExcelIntoCollection(HSSFWorkbook wb, HSSFSheet sheet, String type, AuditContext callerId) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsMappingUserDealer> result = new ArrayList<>();
		List<MsMappingUserDealer> resultInputSuccess = new ArrayList<>();
		List<MsMappingUserDealer> resultError = new ArrayList<>();
		List<MappingUserDealerBean> mappingBeanList = new ArrayList<MappingUserDealerBean>();
		
		//cek header
		HSSFRow rowHeader = sheet.getRow(0);

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			HSSFCell cell = rowHeader.getCell(i);
			if (StringUtils.isEmpty(cell.getStringCellValue())) {
				wb.close();
				throw new IOException();
			} else if(!TEMPLATE_HEADER[i].equalsIgnoreCase(cell.getStringCellValue())) {
				wb.close();
				throw new IOException();
			}
		}
		//end cek header
		
		int rows = sheet.getPhysicalNumberOfRows();
		for (int r = 1; r < rows; r++) {
			HSSFRow row = sheet.getRow(r);
			if (row == null) {
				continue;
			}
			
    		boolean isEmptyRow = checkEmptyRow(row); 
    				
    		if (isEmptyRow == true){
    			continue;
    		}
    		MsMappingUserDealer mud = new MsMappingUserDealer();
    		MsBranch msBranch = new MsBranch();
			for (int c = 0; c < 4; c++) {
				HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
								
				String value = "";
				int intValue = -1;
				
				// if intValue -1, then sequence is posted with string
				if(type.equalsIgnoreCase(GlobalVal.MAPPING_DEALER_TYPE_FOCUS)) {
					mud.setDealerType(GlobalVal.MAPPING_DEALER_TYPE_FOCUS);
				}else {
					mud.setDealerType(GlobalVal.MAPPING_DEALER_TYPE_NON_FOCUS);
				}
				mud.setMsBranch(msBranch);
				
				if (cell != null) {
					switch (cell.getCellType()) {
    					case HSSFCell.CELL_TYPE_NUMERIC:
    						value = String.valueOf((int) cell.getNumericCellValue());
    						intValue = Integer.valueOf(value).intValue();
    						break;
    
    					case HSSFCell.CELL_TYPE_STRING:
    						value = cell.getStringCellValue();
    						break;
    
    					default:
					}
				}
				
				switch (c) {
				case 0:
					mud.setUsername(value);
					break;
				case 1:
					mud.getMsBranch().setBranchName(value);
					break;
				case 2:
					mud.setDealerCode(value);
					break;
				case 3:
					mud.setIsActive(value);
					break;
				}
				wb.close();
			}

			Map<String, Object> checking = checkingUserDealerUpload(mud);
			List listofMapping = (List) checking.get("mappingUser");
			StringBuilder errorText = (StringBuilder) checking.get("errorUpload");	
			
			if (errorText.length() == 0) {
				for (Iterator iterator = listofMapping.iterator(); iterator.hasNext(); ) {
					MsMappingUserDealer mmud = (MsMappingUserDealer) iterator.next();
					result.add(mmud);
				}
				resultInputSuccess.add(mud);
			} 
			else { 
				resultError.add(mud);
				MappingUserDealerBean mappingBean = new MappingUserDealerBean();
				mappingBean.setErrorText(errorText.toString());
				mappingBeanList.add(mappingBean);
			}
		}
		
		paramParse.put("mappingBeanList", mappingBeanList); // nyimpen tulisan error
		paramParse.put("result", result);
		paramParse.put("resultInputSuccess", resultInputSuccess);
		paramParse.put("resultError", resultError);
		
		return paramParse;
	}
	
	private Map loopDataToInsert(Map result, AuditContext callerId, String type) {
		Map<String, Object> mapReturn = new HashMap<String, Object>();
		Stopwatch sw = Stopwatch.createUnstarted();
		byte[] errorUploadByte = null;
		int mappingSuccess = 0;
		List<MappingUserDealerBean> mappingBeanList = (List<MappingUserDealerBean>) result.get("mappingBeanList");
		List listofMapping = (List) result.get("result");
		List<MappingUserDealerBean> listofErrorMapping = (List<MappingUserDealerBean>) result.get("resultError");
		List listofInputSuccess = (List) result.get("resultInputSuccess");
		mappingSuccess = listofInputSuccess.size();
		
		for (Iterator iterator = listofMapping.iterator(); iterator.hasNext(); ) {
			sw.start();
			MsMappingUserDealer mud = (MsMappingUserDealer) iterator.next();
			String uuidExist = this.getMsMappingUserDealerExist(mud);
			// check existing identical data
			if (StringUtils.isNotBlank(uuidExist)) {
				// update username, dealerCode & isActive
				updateMappingUserDealer(mud, Integer.valueOf(uuidExist), callerId);
			} else if (StringUtils.isBlank(uuidExist)) {
				// insert new
				insertMappingUserDealer(mud, callerId);
			}
			
			sw.stop();
			LOG.debug("Mapping [{}] processing time: {}(ms).", iterator,
					sw.elapsed(TimeUnit.MILLISECONDS));
		}
		if (listofErrorMapping.size() > 0) {				
			try {
				errorUploadByte = exportErrorMapping(listofErrorMapping, listofInputSuccess.size(), mappingBeanList, type);
			} 
			catch (SQLException e) {
				throw new MappingUserDealerException(this.messageSource.getMessage(
						"businesslogic.global.errorgenxld", null,
						this.retrieveLocaleAudit(callerId)), e, Reason.ERROR_GENERATE);
			}
		}
	
		int countMappingError = listofErrorMapping.size();
		
		mapReturn.put("errorUploadByte", errorUploadByte);
		mapReturn.put("inputCount", mappingSuccess + countMappingError);
		mapReturn.put("countSuccess", mappingSuccess);
		mapReturn.put("countError", countMappingError);
		return mapReturn;
	}
	
	/*
	 * 1 FULL_NAME 2 BRANCH_NAME 3 DEALER_CODE
	 */
	private StringBuilder sqlPagingOrderBuilder(String orders, AuditContext callerId) {
		if (StringUtils.isEmpty(orders) || "0".equals(orders)) {
			orders = "1A"; // set default order by fullname ASC
		}

		String[] orderCols = { "FULL_NAME", "BRANCH_NAME",
				"DEALER_CODE"};
		StringBuilder queryOrder = new StringBuilder(" ORDER BY ");

		int colIdx = NumberUtils.toInt(StringUtils.substring(orders, 0,
				StringUtils.length(orders) - 1));
		colIdx = (colIdx > 0) ? colIdx - 1 : colIdx;
		queryOrder.append(orderCols[colIdx]);
		if (StringUtils.endsWith(orders, "D")) {
			queryOrder.append(" DESC");
		}
		else {
			queryOrder.append(" ASC");
		}

		return queryOrder;
	}
}
