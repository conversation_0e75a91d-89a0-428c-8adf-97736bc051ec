package com.adins.mss.businesslogic.impl.common;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BuiltinFormats;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.MappingUserKatLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.MappingUserKatException;
import com.adins.mss.exceptions.MappingUserKatException.Reason;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsMappingUserKat;
import com.adins.mss.model.TblProductCategory;
import com.adins.mss.model.TrUploadtasklog;
import com.adins.mss.model.custom.MappingUserKatBean;
import com.adins.mss.model.custom.MappingUserKatErrorBean;
import com.adins.mss.model.custom.UploadTaskBean;
import com.google.common.base.Stopwatch;
import com.opencsv.CSVWriter;

@SuppressWarnings({"unchecked","rawtypes"})
public class GenericMappingUserKatLogic extends BaseLogic implements
	MappingUserKatLogic {
    private static final Logger LOG = LoggerFactory.getLogger(GenericMappingUserKatLogic.class);
    private static final String[] TEMPLATE_HEADER = { "Username", "Branch", "KAT Code",
			"Is Active", GlobalVal.HEADER_PRODUCT_CATEGORY};
    private static final String[] TEMPLATE_HEADER_DATA_MASTER = { GlobalVal.HEADER_BRANCH_DESCRIPTION, GlobalVal.HEADER_PRODUCT_CATEGORY};
    private static final int[] HEADER_COLUMN_WIDTH = { 20 * 256, 35 * 256,
			20 * 256, 10 * 256, 35 * 256};
    private static final String[] TEMPLATE_HEADER_ERROR = { "Username", "Branch", "KAT Code",
			"Is Active", GlobalVal.HEADER_PRODUCT_CATEGORY};
    private static final int[] HEADER_COLUMN_WIDTH_ERROR = { 20 * 256, 35 * 256,
			20 * 256, 10 * 256, 35 * 256 };
    MappingUserKatBean mappingUserKatBean = new MappingUserKatBean();
	
	@Autowired
	private MessageSource messageSource;
	private CommonLogic commonLogic;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}


	@Transactional(readOnly = true)
	@Override
	public List listMappingUserKat(Object[][] params, AuditContext callerId) {
		List result = null;
		
		for (int i = 0;  i < params.length; i++) {
			params[i][1] = "".equalsIgnoreCase(params[i][1].toString()) ? "%" : params[i][1];
		}
		
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
		StringBuilder ordersQueryString = this.sqlPagingOrderBuilder((String) ((Object[][]) params)[7][1], callerId);
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT * FROM ( ")
			.append("			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("				select ")
			.append("					muk.USERNAME, ")
			.append("					am.FULL_NAME, ")
			.append("					KAT_CODE, ")
			.append("					CASE ")
			.append("						WHEN muk.IS_ACTIVE = 1 AND am.IS_ACTIVE = 1 ")
			.append(" 						THEN  1 ")
			.append(" 						ELSE  0 ")
			.append(" 					END AS IS_ACTIVE, ")
			.append("					tbc.PRODUCT_CATEGORY_NAME, ")
			.append("					ID, ")
			.append("			    	ROW_NUMBER() OVER (")
			.append(ordersQueryString)
			.append(					") AS rownum, ")
			.append("			    	msb.BRANCH_NAME ")
			.append("				from MS_MAPPING_USER_KAT muk with (nolock) ")
			.append("					JOIN AM_MSUSER am with (nolock) ON muk.USERNAME = am.LOGIN_ID ")
			.append("					LEFT JOIN TBL_PRODUCT_CATEGORY tbc with (nolock) ON muk.TBL_PRODUCT_CATEGORY_ID = tbc.TBL_PRODUCT_CATEGORY_ID ")
			.append("					LEFT JOIN MS_BRANCH msb with (nolock) ON muk.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("				where 1=1 ")
			.append(paramsQueryString)
			.append("			) a  WHERE a.ROWNUM <= :end ")
			.append("		) b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[5][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[6][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
		result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		return result;
	}
	
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null){
			return new StringBuilder();
		}
		
		StringBuilder sb = new StringBuilder();	
		
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append(" and muk.USERNAME LIKE '%'+ :userName +'%' ");
			paramStack.push(new Object[]{"userName", (String) params[0][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append(" and am.FULL_NAME LIKE '%'+ :fullName +'%' ");
			paramStack.push(new Object[]{"fullName", (String) params[1][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append(" and KAT_CODE LIKE '%'+ :katCode +'%' ");
			paramStack.push(new Object[]{"katCode", (String) params[2][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append(" and muk.TBL_PRODUCT_CATEGORY_ID = :productCategory ");
			paramStack.push(new Object[]{"productCategory", (String) params[3][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[4][1])) {
			sb.append(" and muk.IS_ACTIVE = :isActive and am.IS_ACTIVE = :isActive");
			paramStack.push(new Object[]{"isActive", (String) params[4][1]});
		}
		
		return sb;
	}

	@Transactional(readOnly = true)
	@Override
	public Integer countListMappingUserKat(Object[][] params, AuditContext callerId) {
		Integer result = 0;
		
		for (int i = 0;  i < params.length; i++) {
			params[i][1] = params[i][1].toString().isEmpty() ? "%" : params[i][1];
		}
		
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("select count(1) ")
			.append("from MS_MAPPING_USER_KAT muk with (nolock) ")
			.append("JOIN AM_MSUSER am with (nolock) ON muk.USERNAME = am.LOGIN_ID ")
			.append("LEFT JOIN TBL_PRODUCT_CATEGORY tbc with (nolock) ON muk.TBL_PRODUCT_CATEGORY_ID = tbc.TBL_PRODUCT_CATEGORY_ID ")
			.append("where 1=1 ")
			.append(paramsQueryString);
				
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}
	
	@Transactional(readOnly=true)
	@Override
	public byte[] exportExcel(AuditContext callerId) {

		HSSFWorkbook workbook = this.createXlsTemplate();
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	public HSSFWorkbook createXlsTemplate() {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Mapping User Kat List");
			this.createHeader(workbook, sheet);
			this.setTextDataFormat(workbook, sheet);
			this.setListDataValidation(workbook,sheet);
			
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private void createHeader(HSSFWorkbook workbook, HSSFSheet sheet) {
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);
		style.setBorderBottom(style.BORDER_DOUBLE);
		style.setFillPattern(style.SOLID_FOREGROUND); 
		style.setFillBackgroundColor(HSSFColor.GREY_25_PERCENT.index);
		style.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index);
		style.setAlignment(style.ALIGN_CENTER);
		
		HSSFRow row = sheet.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			HSSFCell cell = row.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER[i]);
			cell.setCellStyle(style);
			sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH[i]);
		}
		
		HSSFCellStyle style1 = workbook.createCellStyle();
		sheet.protectSheet("header");
		style1.setLocked(false);
		style1.setDataFormat((short)BuiltinFormats.getBuiltinFormat("text"));
		
		for (int i = 1; i < 2000; i++) {
			HSSFRow allRow = sheet.createRow(i);
			for (int j = 0; j < 5; j++) {
				HSSFCell cell = allRow.createCell(j);
				cell.setCellStyle(style1);
			}
		}
	}
	
	private void setTextDataFormat(HSSFWorkbook workbook, HSSFSheet sheet) {
		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("@"));

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			sheet.setDefaultColumnStyle(i, style);
		}
	}
	
	private void setListDataValidation(HSSFWorkbook workbook, HSSFSheet sheet) {
		//Data Validation untuk Is Active
		CellRangeAddressList cellIsActive = new CellRangeAddressList(
			    1, 2000, 3, 3);
		
		DVConstraint listIsActive = DVConstraint.createExplicitListConstraint(
			    new String[]{"0", "1"});
		DataValidation validationIsActive = new HSSFDataValidation
			    (cellIsActive, listIsActive);
		validationIsActive.setSuppressDropDownArrow(false);
		sheet.addValidationData(validationIsActive);
		
		CellRangeAddressList cellIsActive2 = new CellRangeAddressList(
				1, 2000, 1, 1);
		CellRangeAddressList cellProduct = new CellRangeAddressList(
			    1, 2000, 4, 4);
		List listBranch = this.getManagerDAO().selectAllNativeString("SELECT BRANCH_NAME FROM MS_BRANCH with (NOLOCK) GROUP BY BRANCH_NAME", null);
		List listProductCategory = this.getManagerDAO().selectAllNativeString("SELECT PRODUCT_CATEGORY_NAME FROM TBL_PRODUCT_CATEGORY with (NOLOCK) GROUP BY PRODUCT_CATEGORY_NAME", null);
		
		HSSFSheet sheetDM = workbook.createSheet("Data Master");
		this.createDataMaster(workbook, sheetDM, listBranch, listProductCategory);
		
		DVConstraint dvConstraintBranch = DVConstraint.createFormulaListConstraint("'Data Master'!$A$3:$A$"+ (listBranch.size() + 2));
		HSSFDataValidation dataValidBranch = new HSSFDataValidation(cellIsActive2, dvConstraintBranch);
		dataValidBranch.setSuppressDropDownArrow(false);
		dataValidBranch.setEmptyCellAllowed(false);
		sheet.addValidationData(dataValidBranch);
		

		DVConstraint dvConstraintProduct = DVConstraint.createFormulaListConstraint("'Data Master'!$B$3:$B$"+ (listProductCategory.size() + 2));
		HSSFDataValidation dataValidProduct = new HSSFDataValidation(cellProduct, dvConstraintProduct);
		dataValidProduct.setSuppressDropDownArrow(false);
		dataValidProduct.setEmptyCellAllowed(false);
		sheet.addValidationData(dataValidProduct);
		
	}
	
	
	private void createDataMaster(HSSFWorkbook workbook, HSSFSheet sheet, List listBranch, List listProductCategory) {
		
		 // title
		 HSSFCellStyle titleStyle = workbook.createCellStyle();
		 titleStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
		 titleStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
		 titleStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
		 titleStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
		 titleStyle.setFillBackgroundColor(HSSFColor.BLACK.index);
		 titleStyle.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		 titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
		 HSSFFont titleFont = workbook.createFont();
		 titleFont.setBoldweight((short) 1000);
		 titleFont.setColor(HSSFColor.WHITE.index);
		 HSSFRow rowLabel = sheet.createRow(1);
		 DataFormat format = workbook.createDataFormat();
		 titleStyle.setDataFormat(format.getFormat("@"));
		 
		 for (int i = 0; i < TEMPLATE_HEADER_DATA_MASTER.length; i++) {
				HSSFCell cell = rowLabel.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_DATA_MASTER[i]);
				titleStyle.setFont(titleFont);
				cell.setCellStyle(titleStyle);

				  // value
				 HSSFCellStyle valStyle = workbook.createCellStyle();
				 valStyle.setBorderTop(HSSFCellStyle.BORDER_THIN);
				 valStyle.setBorderBottom(HSSFCellStyle.BORDER_THIN);
				 valStyle.setBorderLeft(HSSFCellStyle.BORDER_THIN);
				 valStyle.setBorderRight(HSSFCellStyle.BORDER_THIN);
		
				List listData = new ArrayList();
				int rowSize = 0;
				Map valData = null;
				
				if(GlobalVal.HEADER_BRANCH_DESCRIPTION.equals(TEMPLATE_HEADER_DATA_MASTER[i])) {
					rowSize = listBranch.size();
					listData = listBranch;
				}else {
					rowSize = listProductCategory.size();
					listData = listProductCategory;
				}
		
				for (int j = 0; j < rowSize; j++) {
					HSSFRow rowLabelVal = null;
					if(i!=0) {
						rowLabelVal = sheet.getRow(j+2);
					}else {
						rowLabelVal = sheet.createRow(j+2);
					}
								
					if (j < rowSize) {
						valData = (Map) listData.get(j);				
						cell = rowLabelVal.createCell(i);
						cell.setCellValue(valData.get("d0").toString());
						cell.setCellStyle(valStyle);
						sheet.autoSizeColumn(j);							
					} 					
				}
			}
		sheet.protectSheet("Data Master");
	}

	
	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public Map<String, Object> processSpreadSheetSaveToFile(File uploadedFile,
			String fileName, AmMsuser loginBean, AuditContext callerId)
			throws IOException {
		Map<String, Object> ret = new HashMap<String, Object>();
		Date date = new Date();
		Stopwatch totalSw = Stopwatch.createStarted();
		Stopwatch sw = Stopwatch.createUnstarted();
		
		AmGeneralsetting gensetFtpEnabled = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_IS_UPLOAD_SP_ENABLED, callerId);
		if(gensetFtpEnabled == null || !"1".equalsIgnoreCase(gensetFtpEnabled.getGsValue())) {
			sw.start();
			this.parseSpreadsheetToMappingBeans(uploadedFile,callerId);
			sw.stop();
			LOG.debug("Excel parsing time: {}(ms).", sw.elapsed(TimeUnit.MILLISECONDS));
			
		}
		
		try {
			saveFile(uploadedFile, loginBean, fileName, date,
					callerId);
		} 
		catch (Exception e) {
			if(gensetFtpEnabled == null || !"1".equalsIgnoreCase(gensetFtpEnabled.getGsValue())) {
				throw new MappingUserKatException(this.messageSource.getMessage(
						"businesslogic.global.errorgenxld", null, this.retrieveLocaleAudit(callerId)),
						e, Reason.ERROR_GENERATE);
			} else {
				throw new MappingUserKatException(this.messageSource.getMessage(
						"businesslogic.global.errorgencsv", null, this.retrieveLocaleAudit(callerId)),
						e, Reason.ERROR_GENERATE);
			}

		}

		totalSw.stop();
		LOG.debug("===Finish processing excel time: {}(ms).",
				totalSw.elapsed(TimeUnit.MILLISECONDS));
		return ret;
	}
	
	private Map parseSpreadsheetToMappingBeans( File uploadedFile, AuditContext callerId ) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsMappingUserKat> result = new ArrayList<>();
		List<MsMappingUserKat> resultError = new ArrayList<>();

		FileInputStream inputStream = null;

		try {
			inputStream = new FileInputStream(uploadedFile);
			HSSFWorkbook wb = new HSSFWorkbook(inputStream);
			HSSFSheet sheet = wb.getSheetAt(0);
			
			//cek header
			HSSFRow rowHeader = sheet.getRow(0);
	
			for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
				HSSFCell cell = rowHeader.getCell(i);
				if(StringUtils.isEmpty(cell.getStringCellValue())){
					wb.close();
					throw new IOException();
				}else if(!TEMPLATE_HEADER[i].equalsIgnoreCase(cell.getStringCellValue())){
					wb.close();
					throw new IOException();
				}
			}
			//end cek header
			
			
			
			int rows = sheet.getPhysicalNumberOfRows();
			int emptyRow = 1;
			for (int r = 1; r < rows; r++) {
				HSSFRow row = sheet.getRow(r);
				if (row == null) {
					continue;
				}
				
	    		boolean isEmptyRow = checkEmptyRow(row); 
	    				
	    		if(emptyRow==1999) {
	    			throw new MappingUserKatException("Empty Excel", Reason.ERROR_GENERATE);
	    		}
	    		
	    		if (isEmptyRow == true){
	    			emptyRow++;
	    			continue;
	    		}
	    		
	    		
	    		MsMappingUserKat muk = new MsMappingUserKat();
				for (int c = 0; c < 4; c++) {
					HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
									
					String value = "";
					int intValue = -1;
					
					// if intValue -1, then sequence is posted with string
					muk.setDtmCrt(new Date());
					muk.setUsrCrt(callerId.getCallerId().toString());
					muk.setDtmUpd(null);
					muk.setUsrUpd(null);
					
					if (cell != null) {
						switch (cell.getCellType()) {
	    					case HSSFCell.CELL_TYPE_NUMERIC:
	    						value = String.valueOf((int) cell.getNumericCellValue());
	    						intValue = Integer.valueOf(value).intValue();
	    						break;
	    
	    					case HSSFCell.CELL_TYPE_STRING:
	    						value = cell.getStringCellValue();
	    						break;
	    
	    					default:
						}
					}
					
					switch (c) {
					case 0:
						muk.setUsername(value);
						break;
					case 1:
						muk.setKatCode(value);
						break;
					case 2:
						muk.setIsActive(value);
						break;
					}
					wb.close();
				}
			}
			paramParse.put("result", result);
			paramParse.put("resultError", resultError);
			return paramParse;
		}
		finally {
			if (inputStream != null) {
				inputStream.close();
			}
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private void saveFile(File uploadedFile, AmMsuser loginBean,
			String fileName, Date date, AuditContext callerId)
			throws IOException {
		
		AmGeneralsetting gensetFtpEnabled = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_IS_UPLOAD_SP_ENABLED, callerId);
		
		if(gensetFtpEnabled!=null && "1".equalsIgnoreCase(gensetFtpEnabled.getGsValue())) {
			FileInputStream inputStream = new FileInputStream(uploadedFile);
			String path = StringUtils.EMPTY;
			String storeFileName = StringUtils.EMPTY;

			LOG.info("File name from uploaded file : {}", fileName);
			String[] splitFile = StringUtils.split(fileName,
					SystemUtils.FILE_SEPARATOR);
			String splitFileName = splitFile[splitFile.length - 1];
			if(!splitFileName.contains(".csv")) {
				throw new RemoteException("File is not a csv file");
			}
			LOG.info("File name after remove prefix file path : {}", splitFileName);
			try {
				
				storeFileName = StringUtils.remove(splitFileName, ".csv") + "_"
						+ loginBean.getLoginId() + "_"
						+ DateFormatUtils.format(date, "yyyyMMdd HHmmss") + ".csv";
				LOG.info("Store file name : {}", storeFileName);

				Object[][] param = { {"gsCode",
						GlobalKey.GENERALSETTING_FILE_KAT_LOCATION } };
				AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(
						AmGeneralsetting.class, param);

				if (amGeneralSetting == null) {
					throw new EntityNotFoundException(this.messageSource.getMessage(
							"businesslogic.uploadtask.locationnotdefined", null,
							this.retrieveLocaleAudit(callerId)),
							GlobalKey.GENERALSETTING_FILE_KAT_LOCATION);
				}

				path = amGeneralSetting.getGsValue() + storeFileName;
				
				LOG.info("Copy file to path : {}", path);
				
				File targetFile = new File(path);
				
				Files.copy(inputStream, targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
			} 
			finally {
				try {
					if (inputStream != null) {
						inputStream.close();
					}
				} 
				catch (IOException e) {
					LOG.error(
							"SCHEDULER error while close workbook. params[form={}]",
							fileName);
				}
			}
			
//			byte[] exp = stream.toByteArray();

			TrUploadtasklog trUploadTaskLog = new TrUploadtasklog();
			trUploadTaskLog.setAmMsuser(loginBean);
			trUploadTaskLog.setDtmUpload(date);
			trUploadTaskLog.setInputFilename(storeFileName);
			trUploadTaskLog.setIsFinish("0");
			trUploadTaskLog.setType(GlobalVal.MAPPING_KAT);
			trUploadTaskLog.setFileExtension("csv");
			
			this.getManagerDAO().insert(trUploadTaskLog);
		} else {
			ByteArrayOutputStream stream = new ByteArrayOutputStream();
			FileInputStream inputStream = new FileInputStream(uploadedFile);
			HSSFWorkbook wb = new HSSFWorkbook(inputStream);
			String path = StringUtils.EMPTY;
			String storeFileName = StringUtils.EMPTY;

			LOG.info("File name from uploaded file : {}", fileName);
			String[] splitFile = StringUtils.split(fileName,
					SystemUtils.FILE_SEPARATOR);
			String splitFileName = splitFile[splitFile.length - 1];
			LOG.info("File name after remove prefix file path : {}", splitFileName);

			try {
				storeFileName = StringUtils.remove(splitFileName, ".xls") + "_"
						+ loginBean.getLoginId() + "_"
						+ DateFormatUtils.format(date, "yyyyMMdd HHmmss") + ".xls";
				LOG.info("Store file name : {}", storeFileName);

				Object[][] param = { {"gsCode",
						GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH } };
				AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(
						AmGeneralsetting.class, param);

				if (amGeneralSetting == null) {
					throw new EntityNotFoundException(this.messageSource.getMessage(
							"businesslogic.uploadtask.locationnotdefined", null,
							this.retrieveLocaleAudit(callerId)),
							GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
				}

				path = amGeneralSetting.getGsValue() + storeFileName;
				LOG.info("Stored file location : {}", path);
				wb.write(stream);
			} 
			finally {
				try {
					if (inputStream != null) {
						inputStream.close();
					}
					if (wb != null) {
						wb.close();
					}
				} 
				catch (IOException e) {
					LOG.error(
							"SCHEDULER error while close workbook. params[form={}]",
							fileName);
				}
			}
			
			byte[] exp = stream.toByteArray();

			TrUploadtasklog trUploadTaskLog = new TrUploadtasklog();
			trUploadTaskLog.setAmMsuser(loginBean);
			trUploadTaskLog.setDtmUpload(date);
			trUploadTaskLog.setInputFilename(storeFileName);
			trUploadTaskLog.setIsFinish("0");
			trUploadTaskLog.setType(GlobalVal.MAPPING_KAT);
			trUploadTaskLog.setFileExtension("xls");
			
			this.getManagerDAO().insert(trUploadTaskLog);

			FileOutputStream fileOut = new FileOutputStream(path);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		}
		
	}
	
	// error condition untuk di action
	private Map<String, Object> errorUpload(Exception e) {
		Map<String, Object> ret = new HashMap<String, Object>();
		byte[] tmp = new byte[1];
		String msg = StringUtils.EMPTY;
		if (e instanceof MappingUserKatException) {
			if (((MappingUserKatException) e).getReason().equals(
					Reason.ERROR_MAX_EXCEEDED)) {
				tmp[0] = 2;
			} 
			else if (((MappingUserKatException) e).getReason().equals(
					Reason.ERROR_TEMPLATE)) {
				tmp[0] = 1;
			}
			msg = e.getMessage();
			ret.put("exp", tmp);
			ret.put("msg", msg);
		} 
		else {
			tmp[0] = 3;
			ret.put("exp", tmp);
		}
		return ret;
	}
	

	
	private boolean checkEmptyRow(HSSFRow row) {
		String[] isEmptyCell = new String [10]  ;
		Arrays.fill(isEmptyCell, "");

		for (int c = 0; c < 10; c++) {
			HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
			if (cell == null){
				isEmptyCell[c]="empty";
			}
		}
		
		if ("empty".equals(isEmptyCell[0]) && "empty".equals(isEmptyCell[1]) 
				&& "empty".equals(isEmptyCell[2]) && "empty".equals(isEmptyCell[3])){
			return true;
		}
		else{
			return false;
		}
	}
	
	public Map checkingUserKatUpload(MsMappingUserKat muk) {
		Map<String, Object> resultCheck = new HashMap<>();
		List<MsMappingUserKat> listMappingByUnique = new ArrayList<>();
	    StringBuilder errorUpload = new StringBuilder();
	    TblProductCategory tbCategory  = new TblProductCategory();
	    //Jika username tidak diisi
		if (("").equalsIgnoreCase(muk.getUsername())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append("Username harus diisi ");
		}
		else {
			if (!("").equalsIgnoreCase(muk.getMsBranch().getBranchName())) {
				if(!("").equalsIgnoreCase(muk.getTblProductCategory().getProductCategoryName())) {
					tbCategory = this.getManagerDAO().selectOne(TblProductCategory.class,
							new Object[][] { { Restrictions.eq("productCategoryName", muk.getTblProductCategory().getProductCategoryName()) } });
					MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class,
							new Object[][] { { Restrictions.eq("branchName", muk.getMsBranch().getBranchName()) } });
					if((GlobalVal.JENIS_PEMBIAYAAN_KONVENSIONAL_CODE.equalsIgnoreCase(tbCategory.getJenisPembiayaan()) 
							&& !GlobalVal.JENIS_PEMBIAYAAN_KONVENSIONAL.equalsIgnoreCase(msBranch.getKonvenSyariah())
							||(!GlobalVal.JENIS_PEMBIAYAAN_KONVENSIONAL_CODE.equalsIgnoreCase(tbCategory.getJenisPembiayaan()) 
								&& GlobalVal.JENIS_PEMBIAYAAN_KONVENSIONAL.equalsIgnoreCase(msBranch.getKonvenSyariah()))))
					{
						errorUpload.append("Cabang " + msBranch.getKonvenSyariah().toLowerCase() + 
								" hanya bisa dimappingkan product dengan jenis pembiayaan " + msBranch.getKonvenSyariah().toLowerCase());
					}
				}
				//Pengecekan ada tidaknya username di AmMsuser
				Integer uuidUser = (Integer) this.getManagerDAO().selectOneNativeString("SELECT count(1) FROM AM_MSUSER with (nolock) WHERE UNIQUE_ID = :uniqueId ",
						new Object[][] {{"uniqueId", muk.getUsername()}});
				if (0 == uuidUser) {
					if (!("").equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append("Username tidak terdaftar");
				}
				else {
					//Jika user terdaftar
					AmGeneralsetting jobCMO = this.getManagerDAO().selectOne(AmGeneralsetting.class, 
							new Object [][] { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_JOBSVY)} });
					String branchName = muk.getMsBranch().getBranchName();
					Stack<Object[]> paramsStack = new Stack<>();
					
					AmGeneralsetting jobCMOPOLO = this.getManagerDAO().selectOne(AmGeneralsetting.class, 
							new Object [][] { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_JOBSVYPOLO)} });
					
					StringBuilder sb = new StringBuilder()
							.append("SELECT LOGIN_ID FROM AM_MSUSER amu with (nolock) ")
							.append("JOIN MS_JOB mj with (nolock) ON amu.UUID_JOB = mj.UUID_JOB ")
							.append("JOIN MS_BRANCH mb with (nolock) ON amu.UUID_BRANCH = mb.UUID_BRANCH ")
							.append("WHERE amu.UNIQUE_ID = :uniqueId ")
							.append("and mj.JOB_CODE in (:jobCode) ")
							.append("and mb.BRANCH_NAME = :branchName");
					
					List listJobKAT = Arrays.asList(new String[] {jobCMO.getGsValue()});
					listJobKAT = ListUtils.union(listJobKAT, Arrays.asList(StringUtils.split(jobCMOPOLO.getGsValue(), ";")));
					
					paramsStack.push(new Object[]{"branchName", branchName});
					paramsStack.push(new Object[]{"uniqueId", muk.getUsername()});
					paramsStack.push(new Object[]{"jobCode", listJobKAT});
							
					Object[][] sqlParams = new Object[paramsStack.size()][2];
				    for (int i = 0; i < paramsStack.size(); i++) {
						Object[] objects = paramsStack.get(i);
						sqlParams[i] = objects;
					}
					
					List userSvy =  (List) this.getManagerDAO().selectAllNativeString(sb.toString(), sqlParams);
					boolean flagIsActive = false; //Untuk penjagaan agar pesan "username tidak aktif"nya tidak dobel
					boolean flagIsMapped = false;
					if (userSvy.isEmpty()) { //Jika tidak ada user CMO dengan branch yng di pilih
						if (!("").equals(errorUpload.toString())) {
							errorUpload.append(" | ");
						}
						errorUpload.append("User CMO dengan Branch " + muk.getMsBranch().getBranchName() + " tidak ditemukan");
					}
					else {
						List <MsMappingUserKat> listTemp = new ArrayList<>();
						for (int i = 0; i < userSvy.size(); i++) {
							Map loginId = (Map) userSvy.get(i);					
							AmMsuser userCMO = this.getManagerDAO().selectOne(AmMsuser.class,
									new Object[][] { { Restrictions.eq("loginId", loginId.get("d0").toString()) } });
							this.getManagerDAO().fetch(userCMO.getMsBranch());
							int isActive = Integer.valueOf(userCMO.getIsActive());
							MsMappingUserKat mapping = new MsMappingUserKat();
							mapping.setKatCode(muk.getKatCode());
							mapping.setIsActive(muk.getIsActive());
							mapping.setUsername(loginId.get("d0").toString());
							mapping.setMsBranch(userCMO.getMsBranch());
							mapping.setTblProductCategory(tbCategory);
							listTemp.add(mapping);
							
							if (1 == isActive && !flagIsActive) { //Jika user tidak aktif
								flagIsActive = true;
							}
							if ("0".equals(muk.getIsActive())) {
								Object[][] params = { { Restrictions.eq("username", userCMO.getLoginId()) }, 
										{ Restrictions.eq("msBranch.uuidBranch", userCMO.getMsBranch().getUuidBranch()) } };
								Map<String, Object> result = this.getManagerDAO().selectAll(MsMappingUserKat.class, params, null);
								Integer countResult = Integer.parseInt(String.valueOf(result.get(GlobalKey.MAP_RESULT_SIZE)));
								if (0 < countResult && !flagIsMapped) {
									flagIsMapped = true;
								}
								if (flagIsMapped && 0 == isActive) {
									flagIsActive = true;
								}
							} else {
								flagIsMapped = true;
							}
						}
						if (flagIsActive && flagIsMapped) {
							for (int i = 0; i < listTemp.size(); i++) {
								MsMappingUserKat msMappingUserKat = listTemp.get(i);
								listMappingByUnique.add(msMappingUserKat);
							}
						} else {
							if (!flagIsActive) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append("Username tidak aktif");
							}
							if (!flagIsMapped) {
								if (!("").equals(errorUpload.toString())) {
									errorUpload.append(" | ");
								}
								errorUpload.append("Nilai is Active tidak boleh 0 pada User yang belum memiliki mapping");
							}
						}
					}
				}
			}
		}
		
		if (("").equalsIgnoreCase(muk.getMsBranch().getBranchName())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append("Branch harus diisi ");
		}
		
		if (("").equals(muk.getKatCode())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append("KAT code harus diisi ");
		}
		
		if (("").equals(muk.getIsActive())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append("Is Active harus diisi ");
		}
		
		if (("").equals(muk.getTblProductCategory().getProductCategoryName())) {
			if (!("").equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			errorUpload.append("Product Category harus diisi ");
		}
		
		resultCheck.put("mappingUser", listMappingByUnique);
		resultCheck.put("errorUpload", errorUpload);		
		return resultCheck;
	}
	
	//error condition untuk di action
	private byte[] errorUpload(){
		byte[] tmp = new byte[1];
		tmp[0]=1;//for condition in action
		return tmp;
	}
	
	public String getMsMappingUserKatExist(MsMappingUserKat muk) {
		String username = StringUtils.trimToNull(muk.getUsername());
		Object [][] params = { {"userName", username}, {"katCode", muk.getKatCode()},
				{"uuidBranch", muk.getMsBranch().getUuidBranch()}, {"tblProductCategory", muk.getTblProductCategory().getTblProductCategoryId()} };
		
		BigInteger cekUserName = (BigInteger) this.getManagerDAO().selectOneNativeString("SELECT ID "
				+ " FROM MS_MAPPING_USER_KAT  KAT with (nolock)"				
				+ " WHERE USERNAME = :userName "
				+ " AND KAT_CODE = :katCode "
				+ " AND UUID_BRANCH = :uuidBranch "
				+ " AND KAT.TBL_PRODUCT_CATEGORY_ID = :tblProductCategory ", params);

		String uuid = "";
		if (cekUserName != null) {
			uuid = String.valueOf(cekUserName);
		}
		
		return uuid;
	}
	
	@Transactional
	@Override
	public void updateMappingUserKat(MsMappingUserKat muk, int uuidExist, AuditContext callerId) {
		MsMappingUserKat dbModel = this.getManagerDAO().selectOne(MsMappingUserKat.class, Long.valueOf(uuidExist));

		dbModel.setIsActive(muk.getIsActive());
		dbModel.setKatCode(muk.getKatCode());
		dbModel.setMsBranch(muk.getMsBranch());
		dbModel.setTblProductCategory(muk.getTblProductCategory());
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId().toString());

		this.getManagerDAO().update(dbModel);
	}
	
	@Transactional
	@Override
	public void insertMappingUserKat(MsMappingUserKat muk, AuditContext callerId) {
		muk.setDtmCrt(new Date());
		muk.setUsrCrt(callerId.getCallerId().toString());
		muk.setDtmUpd(null);
		muk.setUsrUpd(null);
				
		this.getManagerDAO().insert(muk);
	}
	
	private byte[] exportErrorRate(List listRate, int rateSuccess, List taskBeanList)
			throws SQLException {

		HSSFWorkbook workbook = this.createXlsTemplateErrorRate(listRate,
				rateSuccess, taskBeanList);

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} catch (IOException e) {
            LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	private HSSFWorkbook createXlsTemplateErrorRate(List listRate, int rateSuccess, List taskBeanList) {

		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Mapping User Kat List");
			this.createHeaderErrorUpload(workbook, sheet, rateSuccess,
					listRate.size());
			this.setTextDataFormatErrorUpload(workbook, sheet);
			this.setDataErrorUpload(workbook, sheet, listRate, taskBeanList);
		} catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}
	
	private void createHeaderErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet, int rateSuccess, int rateError) {

		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);

		HSSFRow row0 = sheet.createRow(0);
		HSSFRow row1 = sheet.createRow(1);
		HSSFRow row2 = sheet.createRow(2);

		HSSFCell cell0 = row0.createCell(0);
		cell0.setCellValue("Total Sukses diupload : " + rateSuccess);
		cell0.setCellStyle(style);

		HSSFCell cell1 = row1.createCell(0);
		cell1.setCellValue("Total Gagal diupload : " + rateError);
		cell1.setCellStyle(style);

		HSSFCell cell2 = row2.createCell(0);
		cell2.setCellValue("Berikut data yang gagal di upload");
		cell2.setCellStyle(style);

		HSSFRow row4 = sheet.createRow(4);

		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1000);
		styleHeader.setFont(font);
		styleHeader.setBorderBottom(styleHeader.BORDER_DOUBLE);
		styleHeader.setFillPattern(styleHeader.SOLID_FOREGROUND); 
		styleHeader.setFillBackgroundColor(HSSFColor.GREY_25_PERCENT.index);
		styleHeader.setFillForegroundColor(HSSFColor.GREY_25_PERCENT.index);
		styleHeader.setAlignment(styleHeader.ALIGN_CENTER);	

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			HSSFCell cell = row4.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER[i]);
			cell.setCellStyle(styleHeader);
			sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH[i]);
		}			
		sheet.protectSheet("header");
	}
	
	private void setTextDataFormatErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet) {

		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("@"));

		for (int i = 0; i < TEMPLATE_HEADER_ERROR.length; i++) {
			sheet.setDefaultColumnStyle(i, style);
		}
	}
	
	private void setDataErrorUpload(HSSFWorkbook workbook, HSSFSheet sheet, List listRate, List taskBeanList) {
		List rateList = listRate;

		int j = 5;
		Iterator iterator = rateList.iterator();
		Iterator iteratorBean = taskBeanList.iterator();

		while (iterator.hasNext()) {
			HSSFRow row = sheet.createRow(j);
			MsMappingUserKat mud = (MsMappingUserKat) iterator.next();
			MappingUserKatBean mukb = (MappingUserKatBean)iteratorBean.next();

			HSSFCell cell = row.createCell(0);
			cell.setCellValue(mud.getUsername());
			sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH_ERROR[0]);
			
			HSSFCell cell2 = row.createCell(1);
			cell2.setCellValue(mud.getMsBranch().getBranchName());
			sheet.setColumnWidth(2, HEADER_COLUMN_WIDTH_ERROR[2]);

			HSSFCell cell3 = row.createCell(2);
			cell3.setCellValue(mud.getKatCode());
			sheet.setColumnWidth(3, HEADER_COLUMN_WIDTH_ERROR[1]);

			HSSFCell cell4 = row.createCell(3);
			cell4.setCellValue(mud.getIsActive());
			sheet.setColumnWidth(4, HEADER_COLUMN_WIDTH_ERROR[4]);
			
			HSSFCell cell5 = row.createCell(4);
			cell5.setCellValue(mud.getTblProductCategory().getProductCategoryName());
			sheet.setColumnWidth(5, HEADER_COLUMN_WIDTH_ERROR[2]);
			
			HSSFCell cell6 = row.createCell(5);
			cell6.setCellValue(mukb.getErrorText().toString());
			sheet.setColumnWidth(6, 80 * 256);
			
			j++;
		}
	}
	
	private Map processSpreadSheetFile ( File uploadedFile, AuditContext callerId ) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsMappingUserKat> result = new ArrayList<>();
		List<MsMappingUserKat> resultError = new ArrayList<>();
		List<MsMappingUserKat> resultInputSuccess = new ArrayList<>();
		List<MappingUserKatBean> mappingBeanList = new ArrayList<MappingUserKatBean>();

		FileInputStream inputStream = new FileInputStream(uploadedFile);

		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		HSSFSheet sheet = wb.getSheetAt(0);
		
		//cek header
		HSSFRow rowHeader = sheet.getRow(0);

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			HSSFCell cell = rowHeader.getCell(i);
			if(StringUtils.isEmpty(cell.getStringCellValue())){
				wb.close();
				throw new IOException();
			}else if(!TEMPLATE_HEADER[i].equalsIgnoreCase(cell.getStringCellValue())){
				wb.close();
				throw new IOException();
			}
		}
		//end cek header
		
		int rows = sheet.getPhysicalNumberOfRows();
		for (int r = 1; r < rows; r++) {
			HSSFRow row = sheet.getRow(r);
			if (row == null) {
				continue;
			}
			
    		boolean isEmptyRow = checkEmptyRow(row); 
    				
    		if (isEmptyRow == true){
    			continue;
    		}
    		MsMappingUserKat muk = new MsMappingUserKat();
			for (int c = 0; c < 5; c++) {
				HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
								
				String value = "";
				int intValue = -1;
				
				// if intValue -1, then sequence is posted with string
				muk.setDtmCrt(new Date());
				muk.setUsrCrt(callerId.getCallerId().toString());
				muk.setDtmUpd(null);
				muk.setUsrUpd(null);
				MsBranch msbranch = new MsBranch();
				TblProductCategory tblProductCategory = new TblProductCategory();
				
				if (cell != null) {
					switch (cell.getCellType()) {
    					case HSSFCell.CELL_TYPE_NUMERIC:
    						value = String.valueOf((int) cell.getNumericCellValue());
    						intValue = Integer.valueOf(value).intValue();
    						break;
    
    					case HSSFCell.CELL_TYPE_STRING:
    						value = cell.getStringCellValue();
    						break;
    
    					default:
					}
				}
				
				switch (c) {
				case 0:
					muk.setUsername(value);
					break;
				case 1:			
					msbranch.setBranchName(value);
					muk.setMsBranch(msbranch);
					break;
				case 2:
					muk.setKatCode(value);
					break;
				case 3:
					muk.setIsActive(value);
					break;
				case 4:			
					tblProductCategory.setProductCategoryName(value);
					muk.setTblProductCategory(tblProductCategory);
					break;
				}
				wb.close();
			}

			try {
				Map<String, Object> checking = checkingUserKatUpload(muk);				
				List listofMapping = (List) checking.get("mappingUser");
				StringBuilder errorText = (StringBuilder) checking.get("errorUpload");	
				
				if (errorText.length() == 0) {
					for(Iterator iterator = listofMapping.iterator(); iterator.hasNext();) {
						MsMappingUserKat mmuk = (MsMappingUserKat) iterator.next();						
						result.add(mmuk);
					}	
					resultInputSuccess.add(muk);
				} 
				else {
					resultError.add(muk);
					MappingUserKatBean mappingTaskBean = new MappingUserKatBean();
					mappingTaskBean.setErrorText(errorText.toString());
					mappingBeanList.add(mappingTaskBean);
				}
			} 
			finally {
				
			}
		}
		paramParse.put("mappingTaskBeanList", mappingBeanList);
		paramParse.put("result", result);
		paramParse.put("resultInputSuccess", resultInputSuccess);
		paramParse.put("resultError", resultError);
		
		return paramParse;
	}
	
	@Transactional
	@Override
	public Map loopDataInToUserKAT(Map result, AuditContext callerId) {
				
		Map<String, Object> mapReturn = new HashMap<String, Object>();
		byte[] errorUploadByte = null;
		int taskSuccess = 0;
		int counttaskError = 0;
			
		List<MappingUserKatBean> taskBeanList = (List<MappingUserKatBean>) result.get("mappingTaskBeanList");		
		List listofTask = (List) result.get("result");
		List<UploadTaskBean> listofErrorTask = (List<UploadTaskBean>) result.get("resultError");
		List listofInputSuccess = (List) result.get("resultInputSuccess");
		taskSuccess =  listofInputSuccess.size();
		
		for (Iterator iterator = listofTask.iterator(); iterator.hasNext(); ) {
			MsMappingUserKat muk = (MsMappingUserKat) iterator.next();
			String uuidExist = this.getMsMappingUserKatExist(muk);					
					
//					// check existing identical data
			if (StringUtils.isNotBlank(uuidExist)) {
				// update username, katCode & isActive								
				updateMappingUserKat(muk, Integer.valueOf(uuidExist), callerId);
			} else if (StringUtils.isBlank(uuidExist)) {
				// insert new								
				insertMappingUserKat(muk, callerId);
			}					
		}
		if (!listofErrorTask.isEmpty()) {
			try {
				errorUploadByte = exportErrorRate(listofErrorTask,
						listofTask.size(), taskBeanList);
			} catch (SQLException e) {
				throw new MappingUserKatException("Error generating XLS", Reason.ERROR_GENERATE);
			}
			counttaskError = listofErrorTask.size();
		}
		
		mapReturn.put("errorUploadByte", errorUploadByte);
		mapReturn.put("inputCount", taskSuccess + counttaskError);
		mapReturn.put("countSuccess", taskSuccess);
		mapReturn.put("countError", counttaskError);
		return mapReturn;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void doUploadTaskProcess(TrUploadtasklog trUploadTaskLog,
			AuditContext callerId) {
		LOG.info("Start Job Parse Excel to Database. Filename={}.",
				trUploadTaskLog.getInputFilename());

		Object[][] paramPath = { { Restrictions.eq("gsCode",
				GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH) } };
		AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, paramPath);

		if (amGeneralSetting == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage(
					"businesslogic.uploadtask.locationnotdefined", null,
					this.retrieveLocaleAudit(callerId)),
					GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH);
		}

		String path = amGeneralSetting.getGsValue();
		File processedFile = null;
		FileInputStream inputStream = null;
		HSSFWorkbook wb = null;
		Date dateStart = null;

		// create folder date success/error
		File doneFolder = new File(path
				+ DateFormatUtils.format(trUploadTaskLog.getDtmUpload(),
						"yyyy-MM-dd"));
		if (!doneFolder.exists()) {
			doneFolder.mkdirs();
		}

		try {
			Stopwatch swLoop = Stopwatch.createStarted();
			dateStart = new Date();
			
			Map<String, Object> parseMap = new HashMap<String, Object>();
			processedFile = new File(path + trUploadTaskLog.getInputFilename());
	
			parseMap = processSpreadSheetFile(processedFile, callerId);
					
			Map mapReturn = loopDataInToUserKAT(parseMap, callerId);
			byte[] errorUploadByte = (byte[]) mapReturn.get("errorUploadByte");
			String errorFileLocation = StringUtils.EMPTY;

			if (null != errorUploadByte) {
				// failed parse excel to database
				errorFileLocation = this.writeErrorFile(doneFolder,
						trUploadTaskLog.getInputFilename(), errorUploadByte);
			}

			// move uploaded file to done folder + date
			processedFile.renameTo(new File(doneFolder.getPath()
					+ SystemUtils.FILE_SEPARATOR + processedFile.getName()));

			Date dateFinish = new Date();
			trUploadTaskLog.setProcessStartTime(dateStart);
			trUploadTaskLog.setProcessFinishTime(dateFinish);
			trUploadTaskLog.setProcessDurationSeconds((int)Math
					.abs(dateFinish.getTime() - dateStart.getTime()) / 1000);
			trUploadTaskLog
					.setInputCount((Integer) mapReturn.get("inputCount"));
			trUploadTaskLog.setCountSuccess((Integer) mapReturn
					.get("countSuccess"));
			trUploadTaskLog
					.setCountError((Integer) mapReturn.get("countError"));
			if (StringUtils.isNotBlank(errorFileLocation)) {
				trUploadTaskLog.setErrorFileLocation(errorFileLocation);
			}
			trUploadTaskLog.setIsFinish("1");
			this.getManagerDAO().update(trUploadTaskLog);
			swLoop.stop();
			LOG.info(
					"Job Parse Excel to Database. Filename={}. Elapsed time=<{}>ms",
					trUploadTaskLog.getInputFilename(),
					swLoop.elapsed(TimeUnit.MILLISECONDS));
		} 
		catch (Exception ex) {
			Date dateFinish = new Date();
			String message = "Parsing excel error.";
			Workbook xlsError = this.createXlsError(message);

			String errorFileLocation = null;
			ByteArrayOutputStream stream = null;
			try {
				stream = new ByteArrayOutputStream();
				xlsError.write(stream);
				errorFileLocation = this.writeErrorFile(doneFolder,
						trUploadTaskLog.getInputFilename(),
						stream.toByteArray());

//				 move uploaded file to done folder + date
				processedFile
						.renameTo(new File(doneFolder.getPath()
								+ SystemUtils.FILE_SEPARATOR
								+ processedFile.getName()));
			} 
			catch (IOException e) {
				LOG.error("Error writing warning excel", e);
			} 
			finally {
				try {
					if (stream != null) {
						stream.flush();
						stream.close();
					}
					if (xlsError != null)
						xlsError.close();
				} 
				catch (IOException e) {
					LOG.error("Error closing workbook", e);
				}
			}

			trUploadTaskLog.setProcessStartTime(dateStart);
			trUploadTaskLog.setProcessFinishTime(dateFinish);
			trUploadTaskLog.setProcessDurationSeconds((int)Math
					.abs(dateFinish.getTime() - dateStart.getTime()) / 1000);
			trUploadTaskLog.setErrorFileLocation(errorFileLocation);
			trUploadTaskLog.setIsFinish("1");
			this.getManagerDAO().update(trUploadTaskLog);
			LOG.warn("Error on Job Parse Excel to Database. Filename={}",
					trUploadTaskLog.getInputFilename(), ex);
		} 
		finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (wb != null) {
					wb.close();
				}
			} 
			catch (IOException e) {
				LOG.error(
						"SCHEDULER error while close workbook. params[filename={}]",
						trUploadTaskLog.getInputFilename());
			}
		}
	}
	
	private Workbook createXlsError(String errorMessage) {
		// ByteArrayOutputStream stream = new ByteArrayOutputStream();
		HSSFWorkbook workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet("Task List");

		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1000);
		styleHeader.setFont(fontHeader);

		HSSFRow row = sheet.createRow(1);
		HSSFCell cell = row.createCell(0);
		cell.setCellValue(errorMessage);
		cell.setCellStyle(styleHeader);
		sheet.setColumnWidth(0, 30 * 256);

		return workbook;
	}
	
	private String writeErrorFile(File doneFolder, String inputFileName,
			byte[] errorUploadByte) throws IOException {
		// failed parse excel to database
		String errorFileLocation = doneFolder.getPath()
				+ SystemUtils.FILE_SEPARATOR + "ErrorTaskUpload-"
				+ inputFileName;
		FileOutputStream fileOut = new FileOutputStream(errorFileLocation);
		try {
			fileOut.write(errorUploadByte);
			fileOut.flush();
		} 
		finally {
			fileOut.close();
		}

		return errorFileLocation;
	}

	@Transactional(readOnly=true)
	@Override
	public List getProductCategoryList(AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNativeString("SELECT TBL_PRODUCT_CATEGORY_ID, PRODUCT_CATEGORY_NAME FROM TBL_PRODUCT_CATEGORY WITH(NOLOCK)",null);		
		return result;
	}
	
	/*
	 * 1 FULL_NAME 2 BRANCH_NAME 3 KAT_CODE 4 PRODUCT_CATEGORY_NAME
	 */
	private StringBuilder sqlPagingOrderBuilder(String orders, AuditContext callerId) {
		if (StringUtils.isEmpty(orders) || "0".equals(orders)) {
			orders = "1A"; // set default order by fullname ASC
		}

		String[] orderCols = { "FULL_NAME", "BRANCH_NAME",
				"KAT_CODE", "PRODUCT_CATEGORY_NAME"};
		StringBuilder queryOrder = new StringBuilder(" ORDER BY ");

		int colIdx = NumberUtils.toInt(StringUtils.substring(orders, 0,
				StringUtils.length(orders) - 1));
		colIdx = (colIdx > 0) ? colIdx - 1 : colIdx;
		queryOrder.append(orderCols[colIdx]);
		if (StringUtils.endsWith(orders, "D")) {
			queryOrder.append(" DESC");
		}
		else {
			queryOrder.append(" ASC");
		}

		return queryOrder;
	}

	@Override
	@Transactional
	public void doUploadMappingUserKATBySP(TrUploadtasklog trUploadTaskLog, AuditContext callerId) {
		Date dateStart = new Date();
		//panggil sp upload
		Object paramsCallImportKAT[][] = {{1, trUploadTaskLog.getInputFilename()}};
		this.getManagerDAO().callProcedureNativeString("{ call SP_IMPORT_KAT(?) } ", paramsCallImportKAT, null);
		
		//generate file error
		StringBuilder queryGetAllData = new StringBuilder();
		queryGetAllData.append("SELECT count(1) as total")
		.append(" FROM USER_KAT ukat WITH(NOLOCK) ");
		
		Integer countAlllData = (Integer) this.getManagerDAO().selectOneNativeString(queryGetAllData.toString(), null);
		
		StringBuilder queryGetDataSuccess = new StringBuilder();
		queryGetDataSuccess.append("SELECT count(1) as total")
		.append(" FROM USER_KAT ukat WITH(NOLOCK) ")
		.append(" WHERE STATUS in ('1', '2', '3', '4')");
		
		Integer countSuccess = (Integer) this.getManagerDAO().selectOneNativeString(queryGetDataSuccess.toString(), null);
		
		int countError = 0;
		StringBuilder queryGetErrorData = new StringBuilder();
		queryGetErrorData.append("SELECT USER_NAME as username, BRANCH as branch, "
				+ "KAT_CODE as katCode, IS_ACTIVE as isActive, "
				+ "PRODUCT_CATEGORY as productCategoryName, ERROR as errorText ")
		.append(" FROM USER_KAT ukat WITH(NOLOCK) ")
		.append(" WHERE STATUS='999'")
		.append(" ORDER BY ID ASC");
		
		Object[][] params = null;
		List<MappingUserKatErrorBean> listError = this.getManagerDAO().selectForListString(MappingUserKatErrorBean.class, queryGetErrorData.toString(), params, null);
		String errorFileLocation = StringUtils.EMPTY;
		if(listError!=null && listError.size()>0) {
			countError = listError.size();
			errorFileLocation = createCsvTemplateErrorRate(trUploadTaskLog, listError, countSuccess.intValue(), callerId);
		}
		
		Date dateFinish = new Date();
		
		trUploadTaskLog.setProcessStartTime(dateStart);
		trUploadTaskLog.setProcessFinishTime(dateFinish);
		trUploadTaskLog.setProcessDurationSeconds((int)Math.abs(dateFinish.getTime() - dateStart.getTime()) / 1000);
		trUploadTaskLog.setInputCount(countAlllData.intValue());
		trUploadTaskLog.setCountSuccess(countSuccess.intValue());
		trUploadTaskLog.setCountError(countError);
		if (StringUtils.isNotBlank(errorFileLocation)) {
			trUploadTaskLog.setErrorFileLocation(errorFileLocation);
		}
		trUploadTaskLog.setIsFinish("1");
		this.getManagerDAO().update(trUploadTaskLog);
	}
	
	private String createCsvTemplateErrorRate(TrUploadtasklog uploadTaskLog, List listRate, int rateSuccess, AuditContext callerId) {
		Object[][] paramPath = { { Restrictions.eq("gsCode",
				GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH) } };
		AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, paramPath);

		if (amGeneralSetting == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage(
					"businesslogic.uploadtask.locationnotdefined", null,
					this.retrieveLocaleAudit(callerId)),
					GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH);
		}
		String path = amGeneralSetting.getGsValue();
		
		// create folder date success/error
		File doneFolder = new File(path
				+ DateFormatUtils.format(uploadTaskLog.getDtmUpload(),
						"yyyy-MM-dd"));
		if (!doneFolder.exists()) {
			doneFolder.mkdirs();
		}
		
		String errorFileLocation = doneFolder.getPath()
				+ SystemUtils.FILE_SEPARATOR + "ErrorTaskUpload-"
				+ uploadTaskLog.getInputFilename();
		CSVWriter writer = null;
		BufferedWriter bufWriter = null;
		try {
			int bufSize = 64*1024;
			bufWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(errorFileLocation)),bufSize);
			writer = new CSVWriter(bufWriter, ',');
			writer.writeNext(new String[] {"sep=,"});
			createHeaderErrorUploadCsv(writer, rateSuccess, listRate.size());
			createRecordsErrorUploadCsv(writer, listRate);
		} catch (Exception ex) {
			try {
				bufWriter.close();
				writer.close();
			} 
			catch (IOException e) {
				LOG.info("Cannot Close Open CSV Writer");
				e.printStackTrace();
			}
			throw new RuntimeException(ex);
		} finally {
			try {
				bufWriter.close();
				writer.close();
			} 
			catch (IOException e) {
				LOG.info("Cannot Close Open CSV Writer");
				e.printStackTrace();
			}
		}
		
		return errorFileLocation;
	}
	
	private void createHeaderErrorUploadCsv(CSVWriter writer, int rateSuccess, int rateError) {
		writer.writeNext(new String[] {"Total Sukses diupload : " + rateSuccess});
		writer.writeNext(new String[] {"Total Gagal diupload : " + rateError});
		
		writer.writeNext(new String[] {""});
		writer.writeNext(new String[] {"Berikut data yang gagal di upload"});

		writer.writeNext(new String[] {"Username", "Branch", "KAT Code", "Is Active", "Product Category", "Error Message"});
	}
	
	private void createRecordsErrorUploadCsv(CSVWriter writer,List listRate) {
		List rateList = listRate;

		Iterator iterator = rateList.iterator();
		
		while (iterator.hasNext()) {
			List<String> tmpAnswerList = new ArrayList<>();
			MappingUserKatErrorBean bean = (MappingUserKatErrorBean) iterator.next();

			tmpAnswerList.add(bean.getUsername());
			tmpAnswerList.add(bean.getBranch());
			tmpAnswerList.add(bean.getKatCode());
			tmpAnswerList.add(bean.getIsActive());
			tmpAnswerList.add(bean.getProductCategoryName());
			tmpAnswerList.add(bean.getErrorText());
			
			Object[] tmpArray = tmpAnswerList.toArray();
			writer.writeNext(Arrays.copyOf(tmpArray, tmpArray.length, String[].class));
		}
	}
	
}
