package com.adins.mss.businesslogic.impl.common;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.NewConfinsUserManagementLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.LoginProvider;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.UserLoginIdExistsException;
import com.adins.mss.model.AmMemberofgroup;
import com.adins.mss.model.AmMsgroup;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsGroupofjob;
import com.adins.mss.model.MsJob;

@SuppressWarnings("deprecation")
public class GenericNewConfinsUserManagementLogic extends BaseLogic implements
		NewConfinsUserManagementLogic, MessageSourceAware {
	private AuditInfo auditInfo;
	
	@Autowired
	private MessageSource messageSource;

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public GenericNewConfinsUserManagementLogic() {
		String[] pkCols = {"uuidMsUser"};
		String[] pkDbCols = {"UUID_MS_USER"};
		String[] cols = {"uuidMsUser", "loginId", "fullName", "isActive", "msBranch.uuidBranch", "msJob.uuidJob"};
		String[] dbCols = {"UUID_MS_USER", "LOGIN_ID", "FULL_NAME", "IS_ACTIVE", "UUID_BRANCH", "UUID_JOB"};
		this.auditInfo = new AuditInfo("AM_MSUSER", pkCols, pkDbCols, cols, dbCols);
	}
    
    private static final String FLAG_FALSE = "0";
    private static final String FLAG_TRUE = "1";
	
    @Transactional
	@Override
	public String insertUser(String loginId, String name, String activeStatus,
			String branchCode, String jobCode, AuditContext auditContext) {
		String uuidNewUser = null;
		loginId = StringUtils.upperCase(loginId);
		this.checkLoginId(loginId, auditContext); 
		MsBranch msBranch = this.getMsBranch(branchCode);
        if (msBranch == null) {
        	throw new EntityNotFoundException(this.messageSource.getMessage("businesslogic.usermanagement.branchnotfound", 
					new Object[]{branchCode}, this.retrieveLocaleAudit(auditContext)), branchCode);
        }
        
		MsJob msJob = this.getMsJob(jobCode);
        if (msJob == null) {
        	throw new EntityNotFoundException(this.messageSource.getMessage("businesslogic.usermanagement.jobnotfound", 
					new Object[]{jobCode}, this.retrieveLocaleAudit(auditContext)), jobCode);
        }
		
		AmMsuser amMsuser = new AmMsuser(auditContext.getCallerId(), new Date(), name);
		amMsuser.setLoginId(loginId);
		amMsuser.setIsActive(activeStatus);
		amMsuser.setMsBranch(msBranch);
		amMsuser.setMsJob(msJob);
		amMsuser.setFailCount(new Integer(0));
		amMsuser.setIsLoggedIn(FLAG_FALSE);
		amMsuser.setIsLocked(FLAG_FALSE);
		amMsuser.setIsPasswordExpired(FLAG_FALSE);
		amMsuser.setIsDormant(FLAG_FALSE);
		amMsuser.setChangePwdLogin(FLAG_FALSE);
		amMsuser.setAmMssubsystem(msJob.getAmMssubsystem());
		amMsuser.setLoginProvider(LoginProvider.NEW_CONFINS.toString());
		amMsuser.setMsDealer(null);
		amMsuser.setAmMsuser(null);
		amMsuser.setFacebookId(null);
		amMsuser.setGoogleId(null);
		amMsuser.setPassword(null);
		amMsuser.setLastLoggedIn(null);
		amMsuser.setLastLocked(null);
		amMsuser.setLastExpired(null);
		amMsuser.setLastDormant(null);
		amMsuser.setLastLoggedFail(null);
		amMsuser.setLastRequestOut(null);
		amMsuser.setPrevLoggedIn(null);
		amMsuser.setPrevLoggedFail(null);
		amMsuser.setUserLevel(null);
		amMsuser.setDeviceInfo(null);
		amMsuser.setImei(null);
		amMsuser.setLastSynchronize(null);
		amMsuser.setEmail(null);
		amMsuser.setPhone(null);
		amMsuser.setIsDeleted(FLAG_FALSE);
		
		this.getManagerDAO().insert(amMsuser);
		this.auditManager.auditAdd(amMsuser, auditInfo, auditContext.getCallerId(), "");
		List<AmMsgroup> listGroup = this.retrieveGroup(String.valueOf(msJob.getUuidJob()));
		for (AmMsgroup group : listGroup) {
			this.insertGroupOfUser(amMsuser, group, auditContext.getCallerId());
		}
		
		uuidNewUser = String.valueOf(amMsuser.getUuidMsUser());
		
		return uuidNewUser;
	}

    @Transactional
	@Override
	public void updateUser(String loginId, String name, String activeStatus,
			String branchCode, String jobCode, AuditContext auditContext) {

		loginId = StringUtils.upperCase(loginId);
		AmMsuser amMsuser = this.getAmMsuser(loginId);
		if (amMsuser == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage("businesslogic.usermanagement.usernotfound", 
					new Object[]{loginId}, this.retrieveLocaleAudit(auditContext)), loginId);
		}
		
		MsBranch msBranch = this.getMsBranch(branchCode);
		if (msBranch == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage("businesslogic.usermanagement.branchnotfound", 
					new Object[]{branchCode}, this.retrieveLocaleAudit(auditContext)), branchCode);
		}
		
		MsJob msJob = this.getMsJob(jobCode);
        if (msJob == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage("businesslogic.usermanagement.jobnotfound", 
					new Object[]{jobCode}, this.retrieveLocaleAudit(auditContext)), jobCode);
        }

		amMsuser.setFullName(name);
		amMsuser.setIsActive(activeStatus);
		amMsuser.setMsBranch(msBranch);
		amMsuser.setMsJob(msJob);
		amMsuser.setAmMssubsystem(msJob.getAmMssubsystem());
		amMsuser.setUsrUpd(auditContext.getCallerId());
		amMsuser.setDtmUpd(new Date());
		
		this.auditManager.auditEdit(amMsuser, auditInfo, auditContext.getCallerId(), "");
		this.getManagerDAO().update(amMsuser);
	}

    @Transactional
	@Override
	public void deleteUser(String loginId, AuditContext auditContext) {

		loginId = StringUtils.upperCase(loginId);
		AmMsuser amMsuser = this.getAmMsuser(loginId);
		if (amMsuser == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage("businesslogic.usermanagement.usernotfound", 
					new Object[]{loginId}, this.retrieveLocaleAudit(auditContext)), loginId);
		}
		
		amMsuser.setIsDeleted(FLAG_TRUE);
		amMsuser.setUsrUpd(auditContext.getCallerId());
		amMsuser.setDtmUpd(new Date());
		this.auditManager.auditEdit(amMsuser, auditInfo, auditContext.getCallerId(), "");
		this.getManagerDAO().update(amMsuser);
			
	}
	
	private void checkLoginId(String loginId, AuditContext auditContext) throws UserLoginIdExistsException {
		Object[][] queryParams = { {Restrictions.eq("loginId", loginId)}, {Restrictions.eq("isDeleted", FLAG_FALSE)} };
		Map<String, Object> queryResults = this.getManagerDAO().list(AmMsuser.class, queryParams, null);
		List<?> listData = (List<?>) queryResults.get(GlobalKey.MAP_RESULT_LIST);
		if (listData == null || !listData.isEmpty()) {
			throw new UserLoginIdExistsException(this.messageSource.getMessage("businesslogic.usermanagement.loginidexist", 
					new Object[]{loginId}, this.retrieveLocaleAudit(auditContext)));
		}
	}
	
	@SuppressWarnings("unchecked")
	private List<AmMsgroup> retrieveGroup(String uuidJob) {
		Map<String, Object> queryResults = this.getManagerDAO().list(
			"from MsGroupofjob mgoj join fetch mgoj.msJob mj join fetch mgoj.amMsgroup mg where mj.uuidJob = :uuidJob", 
			new Object[][] {{"uuidJob", uuidJob}});
		List<?> listData = (List<?>) queryResults.get(GlobalKey.MAP_RESULT_LIST);
		
		if (listData == null || listData.isEmpty()) {
			return Collections.<AmMsgroup> emptyList();
		}
		
		List<AmMsgroup> resultList = new ArrayList<>();
		for (MsGroupofjob groupOfJob : (List<MsGroupofjob>) listData) {
			resultList.add(groupOfJob.getAmMsgroup());
		}
		return resultList;
	}
	
	private void insertGroupOfUser(AmMsuser user, AmMsgroup group, String callerId) {
		AmMemberofgroup memberOfGroup = new AmMemberofgroup();
		memberOfGroup.setAmMsuser(user);
		memberOfGroup.setAmMsgroup(group);
		memberOfGroup.setUsrCrt(callerId);
		memberOfGroup.setDtmCrt(new Date());
		
		this.getManagerDAO().insert(memberOfGroup);
	}

	private AmMsuser getAmMsuser(String loginId) {
		if (StringUtils.isBlank(loginId))
			return null;
				
		Object[][] queryParams = { {Restrictions.eq("loginId", loginId)}, {Restrictions.eq("isDeleted", FLAG_FALSE)} };
		AmMsuser result = this.getManagerDAO().selectOne(AmMsuser.class, queryParams);
		
		return result;
	}

	private MsBranch getMsBranch(String branchCode) {
		if (StringUtils.isBlank(branchCode))
			return null;
		
		Object[][] queryParams = { {Restrictions.eq("branchCode", branchCode)} };
		MsBranch result = this.getManagerDAO().selectOne(MsBranch.class, queryParams);
		
		return result;
	}

	private MsJob getMsJob(String jobCode) {
		if (StringUtils.isBlank(jobCode))
			return null;
		MsJob result = this.getManagerDAO().selectOne(
			"from MsJob mj join fetch mj.amMssubsystem where mj.jobCode = :jobCode", new Object[][] {{"jobCode", jobCode}});		
		return result;
	}
	
	/*
	 * List table per 2015-01-21 :
	 
	  	AITMSS.dbo.AM_ATTRIBUTEOFMEMBER AmAttributeofmember
		AITMSS.dbo.AM_MEMBEROFGROUP AmMemberofgroup
		AITMSS.dbo.AM_MOBILEMENUOFUSER AmMobilemenuofuser
		AITMSS.dbo.AM_TODOLISTOFUSER AmTodolistofuser
		AITMSS.dbo.AM_USERPWDHISTORY AmUserpwdhistory
		AITMSS.dbo.MS_AREAOFUSER MsAreaofuser
		AITMSS.dbo.MS_CUSTOMGROUP MsCustomgroup
		AITMSS.dbo.MS_CUSTOMGROUPMEMBER MsCustomgroupmember
		AITMSS.dbo.MS_FORMOFUSER MsFormofuser
		AITMSS.dbo.MS_ROUNDROBINTASK MsRoundrobintask
		AITMSS.dbo.MS_ZIPCODEOFUSER MsZipcodeofuser
		
		AITMSS.dbo.AM_USEREVENTLOG AmUsereventlog --not deleted, for audit
		***Transaction data not deleted
		AITMSS.dbo.TR_ATTENDANCE
		AITMSS.dbo.TR_ATTENDANCEFAILED
		AITMSS.dbo.TR_COLLDAILYSUMMARY
		AITMSS.dbo.TR_LOCATIONHISTORY
		AITMSS.dbo.TR_MESSAGE
		AITMSS.dbo.TR_MOBILETIMELINE
		AITMSS.dbo.TR_MOBILETIMELINECOMMENT
		AITMSS.dbo.TR_SURVEYDAILYSUMMARY
		AITMSS.dbo.TR_TASK_H
		AITMSS.dbo.TR_TASKREJECTEDHISTORY
	 */
	@SuppressWarnings("unused")
	private void deleteChildOfUser(String uuidMsuser) {
		Object[][] queryParams = { {"uuidMsUser", uuidMsuser} };
		
		this.getManagerDAO().deleteHqlString("delete from AmAttributeofmember where amMsuser.uuidMsUser = :uuidMsUser", queryParams);
		this.getManagerDAO().deleteHqlString("delete from AmMemberofgroup where amMsuser.uuidMsUser = :uuidMsUser", queryParams);
		this.getManagerDAO().deleteHqlString("delete from AmMobilemenuofuser where amMsuser.uuidMsUser = :uuidMsUser", queryParams);
		this.getManagerDAO().deleteHqlString("delete from AmTodolistofuser where amMsuser.uuidMsUser = :uuidMsUser", queryParams);
		this.getManagerDAO().deleteHqlString("delete from AmUserpwdhistory where amMsuser.uuidMsUser = :uuidMsUser", queryParams);
		this.getManagerDAO().deleteHqlString("delete from MsAreaofuser where amMsuser.uuidMsUser = :uuidMsUser", queryParams);
		this.getManagerDAO().deleteHqlString("delete from MsCustomgroupmember where amMsuser.uuidMsUser = :uuidMsUser", queryParams);		
		this.getManagerDAO().deleteHqlString("delete from MsCustomgroup where amMsuser.uuidMsUser = :uuidMsUser", queryParams);
		this.getManagerDAO().deleteHqlString("delete from MsFormofuser where amMsuser.uuidMsUser = :uuidMsUser", queryParams);
		this.getManagerDAO().deleteHqlString("delete from MsRoundrobintask where amMsuser.uuidMsUser = :uuidMsUser", queryParams); 
		this.getManagerDAO().deleteHqlString("delete from MsZipcodeofuser where amMsuser.uuidMsUser = :uuidMsUser", queryParams);
		
	}
}
