package com.adins.mss.businesslogic.impl.common;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.PrintTemplateLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsPrintitemofform;
import com.adins.mss.model.MsPrintitemtype;
import com.adins.mss.model.MsQuestion;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericPrintTemplateLogic extends BaseLogic implements PrintTemplateLogic, MessageSourceAware {
    private static final Logger LOG = LoggerFactory.getLogger(GenericPrintTemplateLogic.class);
    private AuditInfo auditInfo;
	
    @Autowired
	private MessageSource messageSource;

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    
	public GenericPrintTemplateLogic() {
		String[] pkCols = { "uuidPrintItemOfForm" };
		String[] pkDbCols = { "UUID_PRINT_ITEM_OF_FORM" };
		String[] cols = { "uuidPrintItemOfForm", "msForm.uuidForm", "msPrintitemtype.uuidPrintItemType", 
				"printItemLabel", "lineSeqOrder", "msQuestion" };
		String[] dbCols = { "UUID_PRINT_ITEM_OF_FORM", "UUID_FORM", "UUID_PRINT_ITEM_TYPE", 
				"PRINT_ITEM_LABEL", "LINE_SEQ_ORDER", "UUID_QUESTION" };
		this.auditInfo = new AuditInfo("MS_PRINTITEMOFFORM", pkCols, pkDbCols,
				cols, dbCols);
	}
    
	@Override
	public Map<String, Object> listPrintTemplateForm(Object params, Object orders,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO().selectAll(MsForm.class, params,
					orders, pageNumber, pageSize);
		return result;
	}

	@Override
	public MsForm getForm(long uuid, AuditContext callerId) {
		MsForm result = this.getManagerDAO().selectOne(MsForm.class, uuid);
		return result;
	}
	
	@Override
	public Map<String, Object> listPrintConfiguration(long uuidForm, int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and mpiof.msForm.uuidForm=:uuidForm");
		paramMap.put("uuidForm", uuidForm);

		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mpiof.lineSeqOrder asc");
					
		Map<String, Object> result = this.getManagerDAO().selectAll(
				"from MsPrintitemofform mpiof join fetch mpiof.msForm join fetch mpiof.msPrintitemtype where 1=1"
						+ condition.toString() + orderQuery.toString(),
				"select count(*) from MsPrintitemofform mpiof join mpiof.msForm join mpiof.msPrintitemtype where 1=1"
						+ condition.toString(),
				paramMap, pageNumber, pageSize);

		return result;
	}
	
	@Override
	public MsPrintitemofform getPrintItemOfForm(long uuidPrintItemOfForm, AuditContext callerId) {
		MsPrintitemofform result = this.getManagerDAO().selectOne(
			"from MsPrintitemofform mp join fetch mp.msForm join fetch mp.msPrintitemtype left join fetch mp.msQuestion "
			+ "where mp.uuidPrintItemOfForm = :uuidPrintItemOfForm", 
			new Object[][] {{"uuidPrintItemOfForm", uuidPrintItemOfForm}});
		
		return result;
	}
	
	
	public Map<String, Object> getListPrintItem(Object params, AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO().selectAll(MsPrintitemtype.class, params, null);

		return result;
	}
	
	@Override
	public Map<String, String> getListPrintItemType(Object params, AuditContext callerId) {
		String[][] orders = { {"printItemTypeName", "ASC"} };
		Map<String, Object> listPrintItemType = this.getManagerDAO().list(MsPrintitemtype.class, params, orders);
				
		List<MsPrintitemtype> listPrintItem = (List) listPrintItemType.get(GlobalKey.MAP_RESULT_LIST);
		
		Map<String, String> listComboPrintItem = new LinkedHashMap<String, String>();
		if (listPrintItemType.size() > 0) {
			for (MsPrintitemtype bean : listPrintItem){
				listComboPrintItem.put(String.valueOf(bean.getUuidPrintItemType()), bean.getPrintItemTypeName());
			}
		}	
		return listComboPrintItem;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertPrintConfig (MsPrintitemofform obj, AuditContext callerId){
		Object[][] params = { {Restrictions.eq("lineSeqOrder", obj.getLineSeqOrder())},
				{Restrictions.eq("msForm.uuidForm", obj.getMsForm().getUuidForm())}};
		if (this.validateCode(params)) {
			obj.setDtmCrt(new Date());
			obj.setUsrCrt(callerId.getCallerId());
			//foreignkey
			MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, obj.getMsForm().getUuidForm());
			//foreignkey
			MsPrintitemtype msPrintitemtype = new MsPrintitemtype();
			msPrintitemtype.setUuidPrintItemType(obj.getMsPrintitemtype().getUuidPrintItemType());
			
			obj.setMsForm(msForm);
			obj.setMsPrintitemtype(msPrintitemtype);	
			if(obj.getMsQuestion() != null){
				if (obj.getMsQuestion().getUuidQuestion() != 0) {
					MsQuestion msQuestion = new MsQuestion();
					msQuestion.setUuidQuestion(obj.getMsQuestion().getUuidQuestion());
					obj.setMsQuestion(msQuestion);
				}
				else {
					obj.setMsQuestion(null);
				}
			}
			this.getManagerDAO().insert(obj);
			this.auditManager.auditAdd(obj, auditInfo, callerId.getCallerId(), "");
		}
		else {
			throw new EntityNotUniqueException(
				this.messageSource.getMessage("service.global.existed", 
						new Object[]{"Print Item Sequence "+obj.getLineSeqOrder()}, this.retrieveLocaleAudit(callerId))
						, String.valueOf(obj.getLineSeqOrder()));
		}

	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updatePrintConfig(MsPrintitemofform obj,  AuditContext callerId) {   
		Object[][] params = { {Restrictions.eq("lineSeqOrder", obj.getLineSeqOrder())},
				{Restrictions.eq("msForm.uuidForm", obj.getMsForm().getUuidForm())},
				{Restrictions.ne("uuidPrintItemOfForm", obj.getUuidPrintItemOfForm())}};
		if (this.validateCode(params)) {
			MsPrintitemofform dbModel = this.getManagerDAO().selectOne(MsPrintitemofform.class,
					obj.getUuidPrintItemOfForm());
			
			dbModel.setLineSeqOrder(obj.getLineSeqOrder());
			dbModel.setPrintItemLabel(obj.getPrintItemLabel());
			dbModel.setMsPrintitemtype(obj.getMsPrintitemtype());
			LOG.trace("QUESTION = {}", obj.getMsQuestion().getUuidQuestion());
			
			if (obj.getMsQuestion().getUuidQuestion()!=0) {
			
				MsQuestion msQuestion = new MsQuestion();
				msQuestion.setUuidQuestion(obj.getMsQuestion().getUuidQuestion());
				dbModel.setMsQuestion(msQuestion);
			}
			else {
				dbModel.setMsQuestion(null);
			}
			dbModel.setDtmUpd(new Date());
			dbModel.setUsrUpd(callerId.getCallerId());

			this.auditManager.auditEdit(dbModel, auditInfo, callerId.getCallerId(), "");
			this.getManagerDAO().update(dbModel);
		} 
		else {
			throw new EntityNotUniqueException(
				this.messageSource.getMessage("service.global.existed", 
						new Object[]{"Print Item Sequence "+obj.getLineSeqOrder()}, this.retrieveLocaleAudit(callerId))
						, String.valueOf(obj.getLineSeqOrder()));
		}

	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	 public void deletePrintConfig(long uuid, AuditContext callerId){
		MsPrintitemofform obj = new MsPrintitemofform();
		obj.setUuidPrintItemOfForm(uuid);

		this.auditManager.auditDelete(obj, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().delete(obj);

	 }
	
	private boolean validateCode(Object params) {
		Map<String, Object> result = this.getManagerDAO().count(MsPrintitemofform.class, params);
		if ((Long) result.get(GlobalKey.MAP_RESULT_SIZE) > 0){
			return false;
		}
		else{
			return true;
		}
	}
}
