package com.adins.mss.businesslogic.impl.common;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.math.NumberUtils;
import org.codehaus.jackson.map.ObjectMapper;
import org.hibernate.criterion.Restrictions;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.PushNotificationLogic;
import com.adins.mss.businesslogic.api.common.UserLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsDealer;
import com.adins.mss.model.TrNotification;
import com.adins.mss.model.custom.Content;
import com.adins.mss.model.custom.ParamNotifNotificationBean;
import com.google.gson.Gson;
@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericPushNotificationLogic extends BaseLogic implements
		PushNotificationLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericPushNotificationLogic.class);	
	private AuditInfo auditInfo;
	private Gson gson = new Gson();
	@Autowired
	private UserLogic userLogic;

	public void setUserLogic(UserLogic userLogic) {
		this.userLogic = userLogic;
	}

	public GenericPushNotificationLogic() {
		String[] pkCols = { "uuidMsUser" };
		String[] pkDbCols = { "UUID_MS_USER" };
		String[] cols = { "uuidMsUser", "fcmToken" };
		String[] dbCols = { "UUID_MS_USER", "FCM_TOKEN" };
		this.auditInfo = new AuditInfo("AM_MSUSER", pkCols, pkDbCols, cols,
				dbCols);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveTokenId(long uuidMsUser, String tokenId,
			AuditContext auditContext) {
		Object[][] params = { { Restrictions.eq("uuidMsUser", uuidMsUser) } };
		AmMsuser bean = this.getManagerDAO().selectOne(AmMsuser.class, params);
		bean.setFcmToken(tokenId);
		this.auditManager.auditEdit(bean, auditInfo,
				auditContext.getCallerId(), "");
		this.getManagerDAO().update(bean);
	}

	@Override
	public void sendNotification(String apiKey, Content message) {
		URL url;
		try {
			url = new URL("https://fcm.googleapis.com/fcm/send");

			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-Type", "application/json");
			conn.setRequestProperty("Authorization", "key=" + apiKey);
			conn.setDoOutput(true);
			ObjectMapper mapper = new ObjectMapper();
			DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
			mapper.writeValue(wr, message);
			wr.flush();
			wr.close();
			int responseCode = conn.getResponseCode();
			LOG.trace("\nSending 'POST' request to URL : {}", url);
			LOG.trace("Response Code : {}", responseCode);

			BufferedReader in = new BufferedReader(new InputStreamReader(
					conn.getInputStream()));
			String inputLine;
			StringBuffer response = new StringBuffer();

			while ((inputLine = in.readLine()) != null) {
				response.append(inputLine);
			}
			in.close();
			LOG.trace(response.toString());
		} catch (Exception e) {
			LOG.error("Error sending push notifications", e);
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void sendNotification(AuditContext auditContext) {
		String URI = SpringPropertiesUtils
				.getProperty(GlobalKey.GOOGLE_API_FIREBASE);
		Calendar endDate = Calendar.getInstance();
		endDate.setTime(new Date());
		Calendar startDate = Calendar.getInstance();
		startDate.setTime(endDate.getTime());
		startDate.add(Calendar.MINUTE, -2);

		Object[][] params3 = {
				{ Restrictions.between("sendDate", startDate.getTime(),
						endDate.getTime()) },
				{ Restrictions.eq("isSend", "0") } };
		Map list = this.getManagerDAO().selectAll(TrNotification.class,
				params3, null);
		List<TrNotification> notifList = (List) list
				.get(GlobalKey.MAP_RESULT_LIST);

		for (int i = 0; i < notifList.size(); i++) {
			TrNotification notif = notifList.get(i);
			String request = notif.getParamNotif();
			ParamNotifNotificationBean bean = gson.fromJson(request,
					ParamNotifNotificationBean.class);
			if ("INDIVIDU".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidUser().length; j++) {
					AmMsuser amMsuser = userLogic.fetchUserByUuid(
							NumberUtils.toLong(bean.getUuidUser()[i]), auditContext);
					List<String> messagePush = new ArrayList<>();
					messagePush.add(bean.getMessage());
					Content content = new Content();
					content.createData("Timeline", bean.getMessage());
					content.addRegId(amMsuser.getFcmToken());
					sendNotificationToDevice(URI, content);
				}
			} 
			else if ("BRANCH".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidBranch().length; j++) {
					Object[][] params2 = { { "uuidBranch",
							bean.getUuidBranch()[j] } };
					List userList = this.getManagerDAO().selectAllNative(
							"fcm.notification.getUserByBranch", params2, null);
					for (int k = 0; k < userList.size(); k++) {
						Map map2 = (Map) userList.get(k);
						List<String> messagePush = new ArrayList<>();
						messagePush.add(bean.getMessage());
						Content content = new Content();
						content.createData("Timeline", bean.getMessage());
						content.addRegId((String) map2.get("d0"));
						sendNotificationToDevice(URI, content);
					}
				}
			} 
			else if ("SALES".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidUser().length; j++) {
					AmMsuser sales = userLogic.fetchUserByUuid(NumberUtils.toLong(bean.getUuidUser()[j]),
							auditContext);
					List<String> messagePush = new ArrayList<>();
					messagePush.add(bean.getMessage());
					Content content = new Content();
					content.createData("Timeline", bean.getMessage());
					content.addRegId(sales.getFcmToken());
					sendNotificationToDevice(URI, content);
				}
			} 
			else if ("DEALER".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidDealer().length; j++) {
					Object[][] params2 = { { "uuidDealer",
							bean.getUuidDealer()[j] } };
					List userList = this.getManagerDAO().selectAllNative(
							"fcm.notification.getUserByDealer", params2, null);
					for (int k = 0; k < userList.size(); k++) {
						Map map2 = (Map) userList.get(k);
						List<String> messagePush = new ArrayList<>();
						messagePush.add(bean.getMessage());
						Content content = new Content();
						content.createData("Timeline", bean.getMessage());
						content.addRegId((String) map2.get("d0"));
						sendNotificationToDevice(URI, content);
					}
				}
			} 
			else if ("GROUP".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidGroup().length; j++) {
					Object[][] params2 = { { "uuidGroup",
							bean.getUuidGroup()[j] } };
					List userList = this.getManagerDAO().selectAllNative(
							"fcm.notification.getUserByGroup", params2, null);
					for (int k = 0; k < userList.size(); k++) {
						Map map2 = (Map) userList.get(k);
						List<String> messagePush = new ArrayList<>();
						messagePush.add(bean.getMessage());
						Content content = new Content();
						content.createData("Timeline", bean.getMessage());
						content.addRegId((String) map2.get("d0"));
						sendNotificationToDevice(URI, content);
					}
				}
			} 
			else if ("ALL".equalsIgnoreCase(bean.getOption())) {
				List userList = this.getManagerDAO().selectAllNative(
						"fcm.notification.getAllUser", null, null);
				for (int k = 0; k < userList.size(); k++) {
					Map map2 = (Map) userList.get(k);
					List<String> messagePush = new ArrayList<>();
					messagePush.add(bean.getMessage());
					Content content = new Content();
					content.createData("Timeline", bean.getMessage());
					content.addRegId((String) map2.get("d0"));
					sendNotificationToDevice(URI, content);
				}
			}
			notif.setIsSend("1");
			notif.setDtmUpd(new Date());
			notif.setUsrUpd(auditContext.getCallerId());
			this.getManagerDAO().update(notif);
		}
	}

	public int sendNotificationToDevice(String apiKey, Content message) {
		URL url;
		int result = 0;
		try {
			url = new URL("https://fcm.googleapis.com/fcm/send");

			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			conn.setRequestMethod("POST");
			conn.setRequestProperty("Content-Type", "application/json");
			conn.setRequestProperty("Authorization", "key=" + apiKey);
			conn.setDoOutput(true);
			ObjectMapper mapper = new ObjectMapper();
			DataOutputStream wr = new DataOutputStream(conn.getOutputStream());
			mapper.writeValue(wr, message);
			wr.flush();
			wr.close();
			int responseCode = conn.getResponseCode();
			LOG.trace("\nSending 'POST' request to URL : {}", url);
			LOG.trace("Response Code : {}", responseCode);

			BufferedReader in = new BufferedReader(new InputStreamReader(
					conn.getInputStream()));
			String inputLine;
			StringBuffer response = new StringBuffer();

			while ((inputLine = in.readLine()) != null) {
				response.append(inputLine);
			}
			in.close();
			LOG.trace(response.toString());
			JSONObject jsonObject = new JSONObject(response.toString());
			LOG.trace(jsonObject.toString());
			
			try {
				result = (int) jsonObject.get("failure");
			} catch (Exception e) {
				result = 0;
			}
		} catch (Exception e) {
			LOG.error("Error sending push notifications", e);
		}
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void sendNotificationByTopic(AuditContext auditContext) {
		String URI = SpringPropertiesUtils
				.getProperty(GlobalKey.GOOGLE_API_FIREBASE);

		Calendar endDate = Calendar.getInstance();
		endDate.setTime(new Date());
		Calendar startDate = Calendar.getInstance();
		startDate.setTime(endDate.getTime());
		startDate.add(Calendar.MINUTE, -2);

		Object[][] params3 = {
				{ Restrictions.le("sendDate", endDate.getTime()) },
				{ Restrictions.eq("isSend", "0") } };
		Map list = this.getManagerDAO().selectAll(TrNotification.class,
				params3, null);
		List<TrNotification> notifList = (List) list
				.get(GlobalKey.MAP_RESULT_LIST);

		for (int i = 0; i < notifList.size(); i++) {
			TrNotification notif = notifList.get(i);
			String request = notif.getParamNotif();
			ParamNotifNotificationBean bean = gson.fromJson(request,
					ParamNotifNotificationBean.class);
			int result = -1;
			if ("INDIVIDU".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidUser().length; j++) {
					AmMsuser amMsuser = userLogic.fetchUserByUuid(
							NumberUtils.toLong(bean.getUuidUser()[j]), auditContext);
					List<String> messagePush = new ArrayList<>();
					messagePush.add(bean.getMessage());
					Content content = new Content();
					content.createData("Timeline", bean.getMessage());
					content.addRegId(amMsuser.getFcmToken());
					result = sendNotificationToDevice(URI, content);
				}
			} 
			else if ("BRANCH".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidBranch().length; j++) {
					Content content = new Content();
					content.createData("Timeline", bean.getMessage());
					String topic = "BRANCH-"+bean.getUuidBranch()[j] + "-" + bean.getOptionType().toUpperCase();
					content.addTopic("/topics/" + topic);
					result = sendNotificationToDevice(URI, content);
				}
			} 
			else if ("SALES".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidUser().length; j++) {
					AmMsuser sales = userLogic.fetchUserByUuid(
							NumberUtils.toLong(bean.getUuidUser()[j]), auditContext);
					List<String> messagePush = new ArrayList<>();
					messagePush.add(bean.getMessage());
					Content content = new Content();
					content.createData("Timeline", bean.getMessage());
					content.addRegId(sales.getFcmToken());
					result = sendNotificationToDevice(URI, content);
				}
			} 
			else if ("DEALER".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidDealer().length; j++) {
					Content content = new Content();
					content.createData("Timeline", bean.getMessage());
					String topic = "DEALER-"+bean.getUuidDealer()[j] + "-" + bean.getOptionType().toUpperCase();
					content.addTopic("/topics/" + topic);
					result = sendNotificationToDevice(URI, content);
				}
			} 
			else if ("GROUP".equalsIgnoreCase(bean.getOption())) {
				for (int j = 0; j < bean.getUuidGroup().length; j++) {
					Content content = new Content();
					content.createData("Timeline", bean.getMessage());
					String topic = "GROUP-"+bean.getUuidGroup()[j] + "-" + bean.getOptionType().toUpperCase();
					content.addTopic("/topics/" + topic);
					result = sendNotificationToDevice(URI, content);
				}
			} 
			else if (bean.getOption().contains("ALL")) {
				Content content = new Content();
				content.createData("Timeline", bean.getMessage());
				String topic = bean.getOption() + "-" + bean.getOptionType().toUpperCase();
				content.addTopic("/topics/" + topic);
				result = sendNotificationToDevice(URI, content);
			}
			if(result == 0){
				notif.setIsSend("1");
				notif.setDtmUpd(new Date());
				notif.setUsrUpd(auditContext.getCallerId());
				this.getManagerDAO().update(notif);
			}
		}

	}

	@Override
	public Map<String, Object> getDealerList(Object params, Object order,
			int pageNumber, int pageSize, AuditContext auditContex) {
		Map<String, Object> result = this.getManagerDAO().selectAll(
				MsDealer.class, params, order, pageNumber, pageSize);
		return result;
	}

	@Override
	public Map<String, Object> getBranchList(Object param, Object order,
			int pageNumber, int pageSize, AuditContext auditContext) {
		Map<String, Object> branch = this.getManagerDAO().selectAll(
				MsBranch.class, param, order, pageNumber, pageSize);
		return branch;

	}

	@Override
	public List getGroupListBySubsystem(Object param,
			int pageNumber, int pageSize, AuditContext auditContext) {

		String tmpParam[][] = (String[][]) param;
		String[][] queryParamList = {
				{ tmpParam[0][0], tmpParam[0][1] }, //Group Name
				{ tmpParam[1][0], tmpParam[1][1] }, //Group Desc
				{ tmpParam[2][0], tmpParam[2][1] }, //Subsystem Id
				{ "start", String.valueOf((pageNumber - 1) * pageSize + 1) },
				{ "end", String.valueOf((pageNumber - 1) * pageSize + pageSize) } };

		List result = this.getManagerDAO().selectAllNative(
				"fcm.notification.getGroupListBySubsystem", queryParamList,
				null);
		return result;
	}

	@Override
	public Integer getCountGroupListBySubsystem(Object param,
			int pageNumber, int pageSize, AuditContext auditContext) {
		Integer result = (Integer) this.getManagerDAO().selectOneNative(
				"fcm.notification.getCountGroupListBySubsystem", param);
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveNotification(TrNotification notif, AuditContext auditContext) {
		notif.setUsrCrt(auditContext.getCallerId());
		notif.setDtmCrt(new Date());
		this.getManagerDAO().insert(notif);
	}

	@Override
	public List getIndividu(Object param, int pageNumber,
			int pageSize, AuditContext auditContext) {
		String tmpParam[][] = (String[][]) param;
		String[][] queryParamList = {
				{ tmpParam[0][0], tmpParam[0][1] }, //User Id
				{ tmpParam[1][0], tmpParam[1][1] }, //Full Name
				{ tmpParam[2][0], tmpParam[2][1] }, //Subsystem Id
				{ "start", String.valueOf((pageNumber - 1) * pageSize + 1) },
				{ "end", String.valueOf((pageNumber - 1) * pageSize + pageSize) } };

		List result = this.getManagerDAO().selectAllNative(
				"fcm.notification.getIndividu", queryParamList, null);

		return result;
	}

	@Override
	public Integer getCountIndividu(Object params, AuditContext callerId) {
		Integer result = (Integer) this.getManagerDAO().selectOneNative(
				"fcm.notification.getCountIndividu", params);
		return result;
	}

	@Override
	public List getSales(Object param, int pageNumber,
			int pageSize, AuditContext auditContext) {
		String tmpParam[][] = (String[][]) param;
		String[][] queryParamList = {
				{ tmpParam[0][0], tmpParam[0][1] }, //User Id
				{ tmpParam[1][0], tmpParam[1][1] }, //Full Name
				{ tmpParam[2][0], tmpParam[2][1] }, //Subsystem Id
				{ "start", String.valueOf((pageNumber - 1) * pageSize + 1) },
				{ "end", String.valueOf((pageNumber - 1) * pageSize + pageSize) } };

		List result = this.getManagerDAO().selectAllNative(
				"fcm.notification.getSales", queryParamList, null);

		return result;
	}

	@Override
	public Integer getCountSales(Object params, AuditContext callerId) {
		Integer result = (Integer) this.getManagerDAO().selectOneNative(
				"fcm.notification.getCountSales", params);
		return result;
	}

}
