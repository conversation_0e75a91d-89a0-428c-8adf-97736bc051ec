package com.adins.mss.businesslogic.impl.common;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.PushSyncLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.foundation.http.MssResponseType.Status;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsMobilePushsyncFiles;
import com.adins.mss.model.TrPushsync;
import com.adins.mss.model.custom.MsLovBean;
import com.adins.mss.services.model.common.BranchBean;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericPushSyncLogic extends BaseLogic implements PushSyncLogic{	
	@Autowired
	CommonLogic commonLogic;
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	@Override
	public Map lovGroupList(Object params, AuditContext callerId) {
		Map<String, Object> result = new HashMap();
		
		StringBuilder query = new StringBuilder()
			.append("SELECT LOV_GROUP ")
			.append("FROM ( ")
			.append("		SELECT a.LOV_GROUP, ROW_NUMBER() OVER(ORDER BY rownum) AS recnum ")
			.append("		FROM ( ")
			.append("			SELECT lov.LOV_GROUPNAME AS LOV_GROUP, ")
			.append("				   ROW_NUMBER() OVER (ORDER BY lov.LOV_GROUPNAME ASC) AS rownum ")
			.append("			FROM ( ")
			.append("				SELECT DISTINCT(LOV_GROUP) AS LOV_GROUPNAME ")
			.append("				FROM MS_LOV WITH(NOLOCK) ")
			.append("				WHERE IS_ACTIVE = :isActive ")
			.append("				AND IS_DELETED = :isDeleted ")
			.append("				AND LOV_GROUP LIKE :lovGroupName ")
			.append("				AND LOV_GROUP NOT IN (:exception) ")
			.append("			) lov ")
			.append("		) a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		
	    List<Map<String, Object>> list = 
	    		this.getManagerDAO().selectAllNativeString(query.toString(), params);
	    List<MsLovBean> listLOV = new ArrayList<MsLovBean>();
	    
	    for (Map object : list) {
	    	MsLovBean lovBean = new MsLovBean();
	    	lovBean.setLovGroupName(object.get("d0").toString());
	    	listLOV.add(lovBean);
		}
		
		result.put(GlobalKey.MAP_RESULT_LIST, listLOV);
		
		return result;
	}
	
	@Override
	public Integer countLovGroupList(Object params, AuditContext callerId) {
		StringBuilder query = new StringBuilder()
									.append("SELECT COUNT(1) ")
									.append("		FROM ( ")
									.append("			SELECT lov.LOV_GROUPNAME AS LOV_GROUP, ")
									.append("				   ROW_NUMBER() OVER (ORDER BY lov.LOV_GROUPNAME ASC) AS rownum ")
									.append("			FROM ( ")
									.append("				SELECT DISTINCT(LOV_GROUP) AS LOV_GROUPNAME ")
									.append("				FROM MS_LOV WITH(NOLOCK) ")
									.append("				WHERE IS_ACTIVE = :isActive ")
									.append("				AND IS_DELETED = :isDeleted ")
									.append("				AND LOV_GROUP LIKE :lovGroupName ")
									.append("				AND LOV_GROUP NOT IN (:exception) ")
									.append("			) lov ")
									.append("		) a ");
		
	    Integer result = (Integer) this.getManagerDAO().selectOneNativeString(query.toString(), params);
		
		return result;
	}

	@Override
	public List<MsLov> getListLov(String[] selectedLovArr, AuditContext callerId) {
		List<MsLov> result = new ArrayList<MsLov>();
		if (selectedLovArr != null && !ArrayUtils.isEmpty(selectedLovArr)) {
			for (String object : selectedLovArr) {
				MsLov msLov = new MsLov();
				msLov.setLovGroup(object);
				result.add(msLov);
			}
		}
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Map insertPushSync(String pushSyncBy, String[] leftDataTable, String[] rightDataTable, AuditContext callerId) {
		Map<String, Object> mapResult = new HashMap<>();
		String query = StringUtils.EMPTY;
		Object[][] params = new Object[2][2];
		
		if ("0".equals(pushSyncBy)) { //BY USER
			query = "SELECT UUID_MS_USER FROM AM_MSUSER WITH (NOLOCK) WHERE UUID_MS_USER IN (:listUuidMsUser) AND IS_ACTIVE = :isActive";
			params[0][0] = "listUuidMsUser";
			params[0][1] = leftDataTable;
			
			params[1][0] = "isActive";
			params[1][1] = "1";
		} else if ("1".equals(pushSyncBy)) { //BY JOB
			query = "SELECT UUID_MS_USER FROM AM_MSUSER WITH (NOLOCK) WHERE UUID_JOB IN (:listUuidJob) AND IS_ACTIVE = :isActive";
			params[0][0] = "listUuidJob";
			params[0][1] = leftDataTable;
			
			params[1][0] = "isActive";
			params[1][1] = "1";
		} else if ("2".equals(pushSyncBy)) { //BY BRANCH
			query = "SELECT UUID_MS_USER FROM AM_MSUSER am WITH (NOLOCK) JOIN MS_JOB ms WITH(NOLOCK) on am.UUID_JOB = ms.UUID_JOB WHERE am.UUID_BRANCH IN (:listUuidBranch) AND am.IS_ACTIVE = :isActive AND IS_FIELD_PERSON = '1'";
			params[0][0] = "listUuidBranch";
			params[0][1] = leftDataTable;
			
			params[1][0] = "isActive";
			params[1][1] = "1";			
		}
		
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNativeString(query, params);
		
		List<String> listUuidUser = new ArrayList<String>();
		Iterator itr = list.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			String uuidMsUser = String.valueOf(mp.get("d0").toString());
			listUuidUser.add(uuidMsUser);
		}
		
		for (int i = 0; i < listUuidUser.size(); i++) {
			for (int j = 0; j < rightDataTable.length; j++) {
				AmMsuser user = new AmMsuser();
				user.setUuidMsUser(Long.valueOf(listUuidUser.get(i)));
                Object[][] paramPushSync = {{Restrictions.eq("amMsuser", user)}, {Restrictions.eq("msMobilePushsyncFiles.idPushsyncFile", Long.parseLong(rightDataTable[j]))}};
                TrPushsync trPushSync = this.getManagerDAO().selectOne(TrPushsync.class, paramPushSync);
				
				if (trPushSync != null) {
					trPushSync.setFlag("0");
					trPushSync.setDtmUpd(new Date());
					trPushSync.setUsrUpd(String.valueOf(callerId.getCallerId()));
					this.getManagerDAO().update(trPushSync);
				} else {
					TrPushsync newTrPushSync = new TrPushsync();
					newTrPushSync.setAmMsuser(user);
					newTrPushSync.setDtmCrt(new Date());
					newTrPushSync.setUsrCrt(String.valueOf(callerId.getCallerId()));
					newTrPushSync.setFlag("0");
					MsMobilePushsyncFiles file = this.getManagerDAO().selectOne(MsMobilePushsyncFiles.class, Long.parseLong(rightDataTable[j]));
					newTrPushSync.setMsMobilePushsyncFiles(file);
					newTrPushSync.setLovGroup(file.getLovGroup());
					this.getManagerDAO().insert(newTrPushSync);
				}
			}
		}
		
		Status status = new Status();
		status.setCode(1);
		status.setMessage("OK");
		mapResult.put("status", status);
		return mapResult;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Map deletePushSync(Long seqNo, AuditContext callerId) {
		Map<String, Object> mapResult = new HashMap<>();
		Object[][] paramPushSync = {{Restrictions.eq("seqno", seqNo)}};
		TrPushsync trPushSync = this.getManagerDAO().selectOne(TrPushsync.class, paramPushSync);
		
		trPushSync.setFlag("2");
		trPushSync.setDtmUpd(new Date());
		trPushSync.setUsrUpd(String.valueOf(callerId.getCallerId()));
		this.getManagerDAO().update(trPushSync);
		
		Status status = new Status();
		status.setCode(1);
		status.setMessage("OK");
		mapResult.put("status", status);
		return mapResult;
	}
	
	public boolean isExistSyncData(String uuidMsUser, String lovGroup) {
		Object[][] params = {{Restrictions.eq("uuidMsUser", Long.valueOf(uuidMsUser))}, {"lovGroup", lovGroup}};
		TrPushsync trPushsync = this.getManagerDAO().selectOne(TrPushsync.class, params);
		if (trPushsync != null) {
			return true;
		}
		return false;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List listPushSync(Object params, int startPage, int endPage, AuditContext callerId) {
		Stack<Object[]> paramStack = new Stack<>();
		
		Object[][] tmpParams = (String[][]) params;
		StringBuilder condition = new StringBuilder();
		
		// fullName
		if (cekParam(tmpParams, 0, callerId)) {
			condition.append(" AND amu.FULL_NAME like :fullName");
			paramStack.push(new Object[]{"fullName", "%" + tmpParams[0][1] + "%"});
		}
		
		// loginId
		if (cekParam(tmpParams, 1, callerId)) {
			condition.append(" AND amu.LOGIN_ID like :loginId");
			paramStack.push(new Object[]{"loginId", "%" + tmpParams[1][1] + "%"});
		}
		
		// job
		if (cekParam(tmpParams, 2, callerId)) {
			condition.append(" AND amu.UUID_JOB = :uuidJob");
			paramStack.push(new Object[]{"uuidJob", Long.valueOf((String)tmpParams[2][1])});
		}
		
		if (GlobalVal.SUBSYSTEM_MO.equals(tmpParams[6][1])) {
			if (cekParam(tmpParams, 3, callerId)) {
				// branch
				String branchSearch = String.valueOf(tmpParams[3][1]);
				if(!"%".equals(tmpParams[3][1])){
					condition.append(" AND amu.UUID_BRANCH = :uuidBranch");
					paramStack.push(new Object[]{"uuidBranch", branchSearch});
				}
			} else if (cekParam(tmpParams, 4, callerId)) {
				// dealer
				String dealerSearch = String.valueOf(tmpParams[4][1]);
				if(!"%".equals(tmpParams[4][1])){
					condition.append(" AND amu.UUID_DEALER = :uuidDealer");
					paramStack.push(new Object[]{"uuidDealer", dealerSearch});
				}
			}
		} else {
			// branch
			if (cekParam(tmpParams, 3, callerId)) {
				if(!"%".equals(tmpParams[3][1])){
					condition.append(" AND amu.UUID_BRANCH = :uuidBranch");
					paramStack.push(new Object[]{"uuidBranch", (String)tmpParams[3][1]});
				}
			} 
		}
		
		// flag
		if (cekParam(tmpParams, 5, callerId)) {
			condition.append(" AND hs.FLAG = :flag");
			paramStack.push(new Object[]{"flag", Long.valueOf((String)tmpParams[5][1])});
		}
		
		// subsystem
		String amMsSubsystem = String.valueOf(this.getManagerDAO()
				.selectOneNativeString("select uuid_ms_subsystem from am_mssubsystem with (nolock) where subsystem_name = :subsystemName", new Object[][] {{"subsystemName", tmpParams[6][1]}}));
		condition.append(" AND amu.UUID_MS_SUBSYSTEM = :uuidMsSubsystem");
		paramStack.push(new Object[]{"uuidMsSubsystem", Long.valueOf(amMsSubsystem)});
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT UUID_MS_USER, LOGIN_ID, FULL_NAME, ")
				.append("		UUID_JOB, JOB_CODE, DESCRIPTION, IS_BRANCH, UUID_BRANCH, BRANCH_CODE, BRANCH_NAME, ")
				.append("		UUID_DEALER, DEALER_CODE, DEALER_NAME, SEQNO, LOV_GROUP, FLAG ")
				.append("FROM ( ")
				.append("	  SELECT a.UUID_MS_USER, a.LOGIN_ID, a.FULL_NAME, ")
				.append("			 a.UUID_JOB, a.JOB_CODE, a.DESCRIPTION, a.IS_BRANCH, ")
				.append("			 a.UUID_BRANCH, a.BRANCH_CODE, a.BRANCH_NAME, ")
				.append("			 a.UUID_DEALER, a.DEALER_CODE, a.DEALER_NAME, ")
				.append("			 a.SEQNO, a.LOV_GROUP, a.FLAG, ROW_NUMBER() OVER(ORDER BY rownum) AS recnum ")
				.append("	  FROM ( ")
				.append("			SELECT hs.UUID_MS_USER, amu.LOGIN_ID, amu.FULL_NAME, ")
				.append("				   amu.UUID_JOB, mj.JOB_CODE, mj.DESCRIPTION, mj.IS_BRANCH, ")
				.append("				   amu.UUID_BRANCH, msb.BRANCH_CODE, mb.BRANCH_NAME, ")
				.append("				   amu.UUID_DEALER, md.DEALER_CODE, md.DEALER_NAME, ")
				.append("				   hs.SEQNO, mmpf.LOV_GROUP, hs.FLAG, ")
				.append("				   ROW_NUMBER() OVER (ORDER BY hs.DTM_CRT ASC, hs.FLAG DESC) as rownum ")
				.append("			FROM TR_PUSHSYNC hs with (nolock) ")
				.append("           JOIN MS_MOBILE_PUSHSYNC_FILES mmpf with(nolock) on mmpf.ID_PUSHSYNC_FILE = hs.ID_PUSHSYNC_FILE ")
				.append("			JOIN AM_MSUSER amu with (nolock) on hs.UUID_MS_USER = amu.UUID_MS_USER ")
				.append("			JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin( :uuidBranchLogin )) mb on amu.UUID_BRANCH = mb.UUID_BRANCH ")
				.append("			JOIN MS_BRANCH msb with (nolock) on mb.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("			JOIN (SELECT keyValue as UUID_JOB FROM dbo.getJobByLogin( :uuidJobLogin )) msj on amu.UUID_JOB = msj.UUID_JOB ")
				.append("			JOIN MS_JOB mj with (nolock) on msj.UUID_JOB = mj.UUID_JOB ")
				.append("			JOIN (SELECT keyValue as UUID_DEALER FROM dbo.getDealerByLogin( :uuidDealerLogin )) msd on amu.UUID_DEALER = msd.UUID_DEALER ")
				.append("			JOIN MS_DEALER md with (nolock) on msd.UUID_DEALER = md.UUID_DEALER ")
				.append("			WHERE 1=1 AND hs.FLAG != '2' ")
				.append(condition)
				.append("	 ) a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start");
		
		paramStack.push(new Object[]{"uuidJobLogin", Long.valueOf((String)tmpParams[8][1])});
		paramStack.push(new Object[]{"uuidBranchLogin", Long.valueOf((String)tmpParams[9][1])});
		paramStack.push(new Object[]{"uuidDealerLogin", Long.valueOf((String)tmpParams[10][1])});
		paramStack.push(new Object[]{"start", startPage});
		paramStack.push(new Object[]{"end", endPage});
		
		Object[][] sqlParams = new Object[paramStack.size()][2];
	    for (int i = 0; i < paramStack.size(); i++) {
			Object[] objects = paramStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    List result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
	    
		return result;
	}

	@Override
	public Integer countListPushSync(Object params, AuditContext callerId) {
		Integer result = 0;
		Stack<Object[]> paramStack = new Stack<>();
		
		String[][] tmpParams = (String[][]) params;
		StringBuilder condition = new StringBuilder();
		
		// fullName
		if (cekParam(tmpParams, 0, callerId)) {
			condition.append(" AND amu.FULL_NAME like :fullName");
			paramStack.push(new Object[]{"fullName", "%" + tmpParams[0][1] + "%"});
		}
		
		// loginId
		if (cekParam(tmpParams, 1, callerId)) {
			condition.append(" AND amu.LOGIN_ID like :loginId");
			paramStack.push(new Object[]{"loginId", "%" + tmpParams[1][1] + "%"});
		}
		
		// job
		if (cekParam(tmpParams, 2, callerId)) {
			condition.append(" AND amu.UUID_JOB = :uuidJob");
			paramStack.push(new Object[]{"uuidJob", Long.valueOf(tmpParams[2][1])});
		}
		
		if (GlobalVal.SUBSYSTEM_MO.equals(tmpParams[6][1])) {
			if (cekParam(tmpParams, 3, callerId)) {
				// branch
				String branchSearch = tmpParams[3][1];
				if(!"%".equals(tmpParams[3][1])){
					condition.append(" AND amu.UUID_BRANCH = :uuidBranch");
					paramStack.push(new Object[]{"uuidBranch", branchSearch});
				}
			} else if (cekParam(tmpParams, 4, callerId)) {
				// dealer
				String dealerSearch = tmpParams[4][1];
				if(!"%".equals(tmpParams[4][1])){
					condition.append(" AND amu.UUID_DEALER = :uuidDealer");
					paramStack.push(new Object[]{"uuidDealer", dealerSearch});
				}
			}
		} else {
			// branch
			if (cekParam(tmpParams, 3, callerId)) {
				if(!"%".equals(tmpParams[3][1])){
					condition.append(" AND amu.UUID_BRANCH = :uuidBranch");
					paramStack.push(new Object[]{"uuidBranch", tmpParams[3][1]});
				}
			} 
		}
		
		// flag
		if (cekParam(tmpParams, 5, callerId)) {
			condition.append(" AND hs.FLAG = :flag");
			paramStack.push(new Object[]{"flag", String.valueOf(tmpParams[5][1])});
		}
		
		// subsystem
		String amMsSubsystem = String.valueOf(this.getManagerDAO()
				.selectOneNativeString("select uuid_ms_subsystem from am_mssubsystem with (nolock) where subsystem_name = :subsystemName", 
						new Object[][] {{"subsystemName", tmpParams[6][1]}}));
		condition.append(" AND amu.UUID_MS_SUBSYSTEM = :uuidMsSubsystem");
		paramStack.push(new Object[]{"uuidMsSubsystem", Long.valueOf(amMsSubsystem)});
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("	  SELECT COUNT(1) ")
				.append("	  FROM ( ")
				.append("			SELECT hs.UUID_MS_USER, amu.LOGIN_ID, amu.FULL_NAME, ")
				.append("				   amu.UUID_JOB, mj.JOB_CODE, mj.DESCRIPTION, mj.IS_BRANCH, ")
				.append("				   amu.UUID_BRANCH, msb.BRANCH_CODE, mb.BRANCH_NAME, ")
				.append("				   amu.UUID_DEALER, md.DEALER_CODE, md.DEALER_NAME, ")
				.append("				   hs.SEQNO, hs.LOV_GROUP, hs.FLAG, ")
				.append("				   ROW_NUMBER() OVER (ORDER BY hs.DTM_CRT ASC, hs.FLAG ASC) as rownum ")
				.append("			FROM TR_PUSHSYNC hs with (nolock) ")
				.append("			JOIN AM_MSUSER amu with (nolock) on hs.UUID_MS_USER = amu.UUID_MS_USER ")
				.append("			JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin( :uuidBranchLogin )) mb on amu.UUID_BRANCH = mb.UUID_BRANCH ")
				.append("			JOIN MS_BRANCH msb with (nolock) on mb.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("			JOIN (SELECT keyValue as UUID_JOB FROM dbo.getJobByLogin( :uuidJobLogin )) msj on amu.UUID_JOB = msj.UUID_JOB ")
				.append("			JOIN MS_JOB mj with (nolock) on msj.UUID_JOB = mj.UUID_JOB ")
				.append("			JOIN (SELECT keyValue as UUID_DEALER FROM dbo.getDealerByLogin( :uuidDealerLogin )) msd on amu.UUID_DEALER = msd.UUID_DEALER ")
				.append("			JOIN MS_DEALER md with (nolock) on msd.UUID_DEALER = md.UUID_DEALER ")
				.append("			WHERE 1=1 AND hs.FLAG != '2' ")
				.append(condition)
				.append("	 ) a ");
		
		paramStack.push(new Object[]{"uuidJobLogin", Long.valueOf(tmpParams[8][1])});
		paramStack.push(new Object[]{"uuidBranchLogin", Long.valueOf(tmpParams[9][1])});
		paramStack.push(new Object[]{"uuidDealerLogin", Long.valueOf(tmpParams[10][1])});
		
		Object[][] sqlParams = new Object[paramStack.size()][2];
	    for (int i = 0; i < paramStack.size(); i++) {
			Object[] objects = paramStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
	    
		return result;
	}

	@Override
	public List getJobListCombo(AuditContext callerId) {
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
		String[][] paramsJob = { { "isActive", "1" }, {"isFieldPerson", "1"}, { "subsystemName", subsysName } };
		
		StringBuilder query = new StringBuilder()
				.append("SELECT job.UUID_JOB, job.DESCRIPTION ")
				.append("FROM MS_JOB job WITH (NOLOCK) ")
				.append("JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) ")
				.append("ON job.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM ")
				.append("WHERE job.IS_ACTIVE = :isActive ")
				.append("AND job.IS_FIELD_PERSON = :isFieldPerson ")
				.append("AND subsystem.SUBSYSTEM_NAME = :subsystemName");
		
		List result = this.getManagerDAO().selectAllNativeString(query.toString(), paramsJob);

		return result;
	}
	
	public boolean cekParam(Object[][] params, int x, AuditContext callerId) {
		return (params[x].length == 2) && (params[x][0] != null)
				&& (!"".equals(params[x][0])) && (params[x][1] != null)
				&& (!"".equals(params[x][1]) && (!"%".equals(params[x][1])));
	}
	
	@Override
	public Map luPushByUserList(Object params, AuditContext callerId) {
		Map<String, Object> result = new HashMap();
		List<Map<String, Object>> list = Collections.EMPTY_LIST;
		
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		String userJob = user.getMsJob().getJobCode();
		
		String getAllUserByLogin = StringUtils.EMPTY;
		
		if(!GlobalVal.JOB_ADMIN.equals(userJob)) {
			getAllUserByLogin = "               JOIN getAllUserByLogin( :callerId ) uu on amu.UUID_MS_USER = uu.keyValue";
		}
		
		StringBuilder query = new StringBuilder()
				.append("SELECT UUID_MS_USER, LOGIN_ID, FULL_NAME ")
				.append("FROM ( ")
				.append("		SELECT a.UUID_MS_USER, a.LOGIN_ID, a.FULL_NAME, ROW_NUMBER() OVER(ORDER BY rownum) AS recnum ")
				.append("		FROM ( ")
				.append("				SELECT amu.UUID_MS_USER, amu.LOGIN_ID, amu.FULL_NAME, ")
				.append("						 ROW_NUMBER() OVER (ORDER BY amu.FULL_NAME ASC) AS rownum ")
				.append("				FROM AM_MSUSER amu WITH(NOLOCK) ")
				.append("				JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) mb on amu.UUID_BRANCH = mb.UUID_BRANCH ")
				.append("				JOIN MS_BRANCH msb with (nolock) on mb.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("				JOIN (SELECT keyValue as UUID_JOB FROM dbo.getJobByLogin(:uuidJob)) msj on amu.UUID_JOB = msj.UUID_JOB ") 
				.append("				JOIN MS_JOB mj with (nolock) on msj.UUID_JOB = mj.UUID_JOB ")
				.append("				JOIN (SELECT keyValue as UUID_DEALER FROM dbo.getDealerByLogin(:uuidDealer)) msd on amu.UUID_DEALER = msd.UUID_DEALER ")
				.append("				JOIN MS_DEALER md with (nolock) on msd.UUID_DEALER = md.UUID_DEALER ")
				.append(getAllUserByLogin)
				.append("				WHERE 1=1 ")
				.append("				AND amu.UUID_MS_USER NOT IN (:exception) ")
				.append("				AND amu.FULL_NAME like :fullName ")
				.append("				AND amu.LOGIN_ID like :loginId ")
				.append("				AND amu.UUID_MS_SUBSYSTEM = :uuidMsSubsystem ")
				.append("				AND amu.IS_ACTIVE = :isActive ")
				.append("				AND mj.IS_FIELD_PERSON = :isFieldPerson ")
				.append("		) a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start ");
		
	    list = this.getManagerDAO().selectAllNativeString(query.toString(), params);
	    List<AmMsuser> listUser = new ArrayList<AmMsuser>();
	    
	    for (Map object : list) {
	    	AmMsuser msUser = new AmMsuser();
			msUser.setUuidMsUser(Long.valueOf(object.get("d0").toString()));
			msUser.setLoginId(object.get("d1").toString());
			msUser.setFullName(object.get("d2").toString());
			
			listUser.add(msUser);
		}
		
		result.put(GlobalKey.MAP_RESULT_LIST, listUser);
		
		return result;
	}

	@Override
	public Integer countLuPushByUserList(Object params, AuditContext callerId) {
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		String userJob = user.getMsJob().getJobCode();
		
		String getAllUserByLogin = StringUtils.EMPTY;
		
		if(!GlobalVal.JOB_ADMIN.equals(userJob)) {
			getAllUserByLogin = "               JOIN getAllUserByLogin( :callerId ) uu on amu.UUID_MS_USER = uu.keyValue";
		}
		
		StringBuilder query = new StringBuilder()
				.append("SELECT COUNT(1) ") 
				.append("		FROM ( ")
				.append("				SELECT amu.UUID_MS_USER, amu.LOGIN_ID, amu.FULL_NAME, ")
				.append("						 ROW_NUMBER() OVER (ORDER BY amu.FULL_NAME ASC) AS rownum ")
				.append("				FROM AM_MSUSER amu WITH(NOLOCK) ")
				.append("				JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) mb on amu.UUID_BRANCH = mb.UUID_BRANCH ")
				.append("				JOIN MS_BRANCH msb with (nolock) on mb.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("				JOIN (SELECT keyValue as UUID_JOB FROM dbo.getJobByLogin(:uuidJob)) msj on amu.UUID_JOB = msj.UUID_JOB ")
				.append("				JOIN MS_JOB mj with (nolock) on msj.UUID_JOB = mj.UUID_JOB ")
				.append("				JOIN (SELECT keyValue as UUID_DEALER FROM dbo.getDealerByLogin(:uuidDealer)) msd on amu.UUID_DEALER = msd.UUID_DEALER ")
				.append("				JOIN MS_DEALER md with (nolock) on msd.UUID_DEALER = md.UUID_DEALER ")
				.append(getAllUserByLogin)
				.append("				WHERE 1=1 ")
				.append("				AND amu.UUID_MS_USER NOT IN (:exception) ")
				.append("				AND amu.FULL_NAME like :fullName ")
				.append("				AND amu.LOGIN_ID like :loginId ")
				.append("				AND amu.UUID_MS_SUBSYSTEM = :uuidMsSubsystem ")
				.append("				AND amu.IS_ACTIVE = :isActive ")
				.append("				AND mj.IS_FIELD_PERSON = :isFieldPerson ") 
				.append("		) a ");
		
	    Integer result = (Integer) this.getManagerDAO().selectOneNativeString(query.toString(), params);
		
		return result;
	}
	
	@Override
	public List<AmMsuser> getListUser(String[] selectedUserArr, AuditContext callerId) {
		List<AmMsuser> result = new ArrayList<AmMsuser>();
		if (selectedUserArr != null && !ArrayUtils.isEmpty(selectedUserArr)) {
			for (String amMsuser : selectedUserArr) {
				AmMsuser msUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(amMsuser));
				result.add(msUser);
			}
		}
		return result;
	}

	@Override
	public Map luPushByJobList(Object params, AuditContext callerId) {
		StringBuilder query = new StringBuilder()
				.append("SELECT UUID_JOB, JOB_CODE, DESCRIPTION ")
				.append("FROM ( ")
				.append("		SELECT a.UUID_JOB, a.JOB_CODE, a.DESCRIPTION, ROW_NUMBER() OVER(ORDER BY rownum) AS recnum ")
				.append("		FROM ( ")
				.append("				SELECT msj.UUID_JOB, msj.JOB_CODE, msj.DESCRIPTION, ")
				.append("						 ROW_NUMBER() OVER (ORDER BY msj.DESCRIPTION ASC) AS rownum ")
				.append("				FROM MS_JOB msj WITH (NOLOCK) ")
				.append("				JOIN (SELECT keyValue as UUID_JOB FROM dbo.getJobByLogin(:uuidJob)) mj on msj.UUID_JOB = mj.UUID_JOB ")
				.append("				WHERE 1=1 ")
				.append("				AND msj.UUID_JOB NOT IN (:exception) ")
				.append("				AND msj.DESCRIPTION like :description ")
				.append("				AND msj.JOB_CODE like :jobCode ")
				.append("				AND msj.UUID_MS_SUBSYSTEM = :uuidMsSubsystem ")
				.append("				AND msj.IS_ACTIVE = :isActive ")
				.append("				AND msj.IS_FIELD_PERSON = :isFieldPerson ")
				.append("		) a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start ");
		
	    List<Map<String, Object>> list = this.getManagerDAO().selectAllNativeString(query.toString(), params);
	    
	    List<MsJob> listJob = new ArrayList<MsJob>();
		for (Map object : list) {
			MsJob msJob = new MsJob();
			msJob.setUuidJob(Long.valueOf(object.get("d0").toString()));
			msJob.setJobCode(object.get("d1").toString());
			msJob.setDescription(object.get("d2").toString());
			
			listJob.add(msJob);
		}
		
		Map<String, Object> result = new HashMap();
		result.put(GlobalKey.MAP_RESULT_LIST, listJob);
		
		return result;
	}

	@Override
	public Integer countLuPushByJobList(Object params, AuditContext callerId) {
		StringBuilder query = new StringBuilder()
				.append("SELECT COUNT(1) ")
				.append("		FROM ( ")
				.append("				SELECT msj.UUID_JOB, msj.JOB_CODE, msj.DESCRIPTION, ")
				.append("						 ROW_NUMBER() OVER (ORDER BY msj.DESCRIPTION ASC) AS rownum ") 
				.append("				FROM MS_JOB msj WITH (NOLOCK) ")
				.append("				JOIN (SELECT keyValue as UUID_JOB FROM dbo.getJobByLogin(:uuidJob)) mj on msj.UUID_JOB = mj.UUID_JOB ")
				.append("				WHERE 1=1 ")
				.append("				AND msj.UUID_JOB NOT IN (:exception) ")
				.append("				AND msj.DESCRIPTION like :description ")
				.append("				AND msj.JOB_CODE like :jobCode ")
				.append("				AND msj.UUID_MS_SUBSYSTEM = :uuidMsSubsystem ")
				.append("				AND msj.IS_ACTIVE = :isActive ")
				.append("				AND msj.IS_FIELD_PERSON = :isFieldPerson ")
				.append("		) a ");
		
	    Integer result = (Integer) this.getManagerDAO().selectOneNativeString(query.toString(), params);
		
		return result;
	}

	@Override
	public List<MsJob> getListJob(String[] selectedJobArr, AuditContext callerId) {
		List<MsJob> result = new ArrayList<MsJob>();
		if (selectedJobArr != null && !ArrayUtils.isEmpty(selectedJobArr)) {
			for (int i = 0; i < selectedJobArr.length; i++) {
				MsJob msJob = this.getManagerDAO().selectOne(MsJob.class, Long.valueOf(selectedJobArr[i]));
				result.add(msJob);
			}
		}
		return result;
	}

	@Override
	public Map luPushByBranchList(Object params, AuditContext callerId) {
		StringBuilder query = new StringBuilder()
				.append("SELECT UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
				.append("FROM ( ")
				.append("		SELECT a.UUID_BRANCH, a.BRANCH_CODE, a.BRANCH_NAME, ROW_NUMBER() OVER(ORDER BY rownum) AS recnum ")
				.append("		FROM ( ")
				.append("				SELECT msb.UUID_BRANCH, msb.BRANCH_CODE, msb.BRANCH_NAME, ")
				.append("						 ROW_NUMBER() OVER (ORDER BY msb.BRANCH_NAME ASC) AS rownum ")
				.append("				FROM MS_BRANCH msb WITH (NOLOCK) ")
				.append("				JOIN (SELECT keyValue as UUID_BRANCH FROM dbo.getCabangByLogin(:uuidBranch)) mb on msb.UUID_BRANCH = mb.UUID_BRANCH ")
				.append("				WHERE 1=1 ")
				.append("				AND msb.UUID_BRANCH NOT IN (:exception) ")
				.append("				AND msb.BRANCH_NAME like :branchName ")
				.append("				AND msb.BRANCH_CODE like :branchCode ")
				.append("				AND msb.IS_ACTIVE = :isActive ")
				.append("		) a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start ");
		
	    List<Map<String, Object>> list = this.getManagerDAO().selectAllNativeString(query.toString(), params);
	    List<MsBranch> listBranch = new ArrayList<MsBranch>();

		for (Map object : list) {
			MsBranch msBranch = new MsBranch();
			msBranch.setUuidBranch(Long.valueOf(object.get("d0").toString()));
			msBranch.setBranchCode(object.get("d1").toString());
			msBranch.setBranchName(object.get("d2").toString());
			
			listBranch.add(msBranch);
		}
		
		Map<String, Object> result = new HashMap();
		result.put(GlobalKey.MAP_RESULT_LIST, listBranch);
		
		return result;
	}

	@Override
	public Integer countLuPushByBranchList(Object params, AuditContext callerId) {
		StringBuilder query = new StringBuilder()
				.append("SELECT COUNT(1) ")
				.append("		FROM ( ")
				.append("				SELECT msb.UUID_BRANCH, msb.BRANCH_CODE, msb.BRANCH_NAME, ")
				.append("						 ROW_NUMBER() OVER (ORDER BY msb.BRANCH_NAME ASC) AS rownum ")
				.append("				FROM MS_BRANCH msb WITH (NOLOCK) ")
				.append("				JOIN (SELECT keyValue as UUID_BRANCH FROM dbo.getCabangByLogin(:uuidBranch)) mb on msb.UUID_BRANCH = mb.UUID_BRANCH ")
				.append("				WHERE 1=1 ")
				.append("				AND msb.UUID_BRANCH NOT IN (:exception) ")
				.append("				AND msb.BRANCH_NAME like :branchName ")
				.append("				AND msb.BRANCH_CODE like :branchCode ")
				.append("				AND msb.IS_ACTIVE = :isActive ") 
				.append("		) a ");
		
	    Integer result = (Integer) this.getManagerDAO().selectOneNativeString(query.toString(), params);
		
		return result;
	}

	@Override
	public List<MsBranch> getListBranch(String[] selectedBranchArr, AuditContext callerId) {
		List<MsBranch> result = new ArrayList<MsBranch>();
		if (selectedBranchArr != null && !ArrayUtils.isEmpty(selectedBranchArr)) {
			for (int i = 0; i < selectedBranchArr.length; i++) {
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(selectedBranchArr[i]));
				result.add(msBranch);
			}
		}
		return result;
	}

	@Transactional(readOnly = true)
	@Override
	public List<MsBranch> getListBranchForSettingTime(Object[][] params, AuditContext callerId) {
		List result = new ArrayList();
		List list = this.getManagerDAO().selectAllNative("setting.branch.listBranch", params, null);
		Iterator itr = list.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			BranchBean bean = new BranchBean();
			bean.setUuidBranch(Long.valueOf(mp.get("d0").toString()));
			bean.setBranchCode((String) mp.get("d1"));
			bean.setBranchName((String) mp.get("d2"));
			if(mp.get("d3") != null){
				bean.setParentId(Long.valueOf(mp.get("d3").toString()));
			}
			bean.setParentName((String) mp.get("d4"));
			bean.setBranchAddress((String) mp.get("d5"));
			bean.setColor((String) mp.get("d6"));
			bean.setLevel((Integer) mp.get("d7"));
			bean.setRownum((BigInteger) mp.get("d8"));
			bean.setIsActive((String)mp.get("d10"));
			bean.setIsPiloting(mp.get("d11")==null?"0": (String)mp.get("d11"));
			
			
			Object timePushSync = mp.get("d12");
			if (timePushSync == null) {
				AmGeneralsetting gsValue = commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_DEFAULT_TIME_PUSHSYNC, callerId);
				bean.setTimePushSync(gsValue.getGsValue());
			} else {
				bean.setTimePushSync((String)mp.get("d12"));
			}
			
			result.add(bean);
		}
		return result;
	}

	@Transactional(readOnly = true)
	@Override
	public Integer getListCountBranchForSettingTime(Object[][] params, AuditContext callerId) {
		Integer result = (Integer)this.getManagerDAO().selectOneNative("setting.branch.listBranchCount", params);
		return result;
	}

	@Transactional
	@Override
	public void saveTimeForBranch(long uuidBranch, String time, AuditContext callerId) {
		Object paramsUpdateBranch[][] = {{"uuidBranch", uuidBranch}, {"time", time}, {"usrUpd", callerId.getCallerId()}};
		StringBuilder queryUpdateTImeBranch = new StringBuilder(" UPDATE MS_BRANCH SET")
				.append(" PUSH_SYNC_TIME = :time, ")
				.append(" USR_UPD = :usrUpd ,")
				.append(" DTM_UPD = GETDATE() ")
				.append(" WHERE UUID_BRANCH = :uuidBranch ");
		
		this.getManagerDAO().updateNativeString(queryUpdateTImeBranch.toString(), paramsUpdateBranch);
	}

	@Transactional(readOnly=true)
	@Override
	public List generatedSyncList(String paramsTable, String paramsOrder, String[] selectedId, int start, int end, AuditContext callerId) {
		Object[][] params = null;
		StringBuilder queryGeneratedSync = new StringBuilder("SELECT * from ( ")
				.append(" SELECT a.*, ROW_NUMBER() OVER ( ")
				.append(" ORDER BY rownum) AS recnum FROM ( ")
				.append(" SELECT ID_PUSHSYNC_FILE as id, LOV_GROUP as lovGroupName, isnull(LEFT(CONVERT(VARCHAR, MAX_TIMESTAMPS, 113), 17),'-') as maxTimestamps, ");
		if (GlobalVal.ROW_ORDER_ASC.equals(paramsOrder)) {
			queryGeneratedSync.append(" ROW_NUMBER() OVER ( ORDER BY MAX_TIMESTAMPS ASC )  AS rownum ");
		} else if (GlobalVal.ROW_ORDER_DESC.equals(paramsOrder)) {
			queryGeneratedSync.append(" ROW_NUMBER() OVER ( ORDER BY MAX_TIMESTAMPS DESC )  AS rownum ");
		}
		queryGeneratedSync.append(" FROM MS_MOBILE_PUSHSYNC_FILES WITH(NOLOCK) ")
				.append(" WHERE IS_ACTIVE=1 ");
		if (StringUtils.isNotBlank(paramsTable)) {
			queryGeneratedSync.append(" AND LOV_GROUP = :lovGroup ");
			if(selectedId!=null && selectedId.length>0) {
				queryGeneratedSync.append(" AND ID_PUSHSYNC_FILE not in ( :selectedId ) ");
				params = new Object[][] {{"lovGroup", paramsTable}, {"start", start}, {"end", end}, {"selectedId", selectedId}};
			} else {
				params = new Object[][] {{"lovGroup", paramsTable}, {"start", start}, {"end", end}};
			}
		} else {
			if(selectedId!=null && selectedId.length>0) {
				queryGeneratedSync.append(" AND ID_PUSHSYNC_FILE not in ( :selectedId ) ");
				params = new Object[][] {{"start", start}, {"end", end}, {"selectedId", selectedId}};
			} else {
				params = new Object[][] {{"start", start}, {"end", end}};
			}
		}
		
		
		queryGeneratedSync.append(" ) a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start ");
		List resultList = this.getManagerDAO().selectForListOfMapString(queryGeneratedSync.toString(), params, null);
		return resultList;
	}

	@Transactional(readOnly=true)
	@Override
	public Integer countGeneratedSyncList(String paramsTable,  String[] selectedId, AuditContext callerId) {
		Object[][] params = null;
		StringBuilder queryGeneratedSync = new StringBuilder(" SELECT count(1) ")
				.append(" FROM MS_MOBILE_PUSHSYNC_FILES WITH(NOLOCK) ")
				.append(" WHERE IS_ACTIVE=1 ");
		if (StringUtils.isNotBlank(paramsTable)) {
			queryGeneratedSync.append(" AND LOV_GROUP = :lovGroup ");
			if(selectedId!=null && selectedId.length>0) {
				queryGeneratedSync.append(" AND ID_PUSHSYNC_FILE not in ( :selectedId ) ");
				params = new Object[][] {{"lovGroup", paramsTable}, {"selectedId", selectedId}};
			} else {
				params = new Object[][] {{"lovGroup", paramsTable}};
			}
		} else {
			if(selectedId!=null && selectedId.length>0) {
				queryGeneratedSync.append(" AND ID_PUSHSYNC_FILE not in ( :selectedId ) ");
				params = new Object[][] {{"selectedId", selectedId}};
			} 
		}
		Integer count = (Integer) this.getManagerDAO().selectOneNativeString(queryGeneratedSync.toString(), params);
		return count;
	}

	@Transactional(readOnly=true)
	@Override
	public List tableList(AuditContext callerId) {
		StringBuilder queryTableList = new StringBuilder(" SELECT DISTINCT LOV_GROUP ")
				.append(" FROM MS_MOBILE_PUSHSYNC_FILES WITH(NOLOCK) ")
				.append(" WHERE IS_ACTIVE=1 ");
		List resultList = this.getManagerDAO().selectAllNativeString(queryTableList.toString(), null);
		return resultList;
	}
}

