package com.adins.mss.businesslogic.impl.common;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.QuestionGroupLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsQuestiongroup;
import com.adins.mss.model.MsQuestiongroupofform;
import com.adins.mss.model.MsQuestionofgroup;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericQuestionGroupLogic extends BaseLogic implements QuestionGroupLogic, MessageSourceAware{
	private static final Logger LOG = LoggerFactory.getLogger(GenericFormLogic.class);
	private AuditInfo auditInfoQG;
	private AuditInfo auditInfoQoG;
	private AuditInfo auditInfoF;
	
	@Autowired
	private MessageSource messageSource;

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public GenericQuestionGroupLogic() {
		String[] pkColsQG = { "uuidQuestionGroup" };
		String[] pkDbColsQG = { "UUID_QUESTION_GROUP" };
		String[] colsQG = { "uuidQuestionGroup", "isActive", "questionGroupLabel" };
		String[] dbColsQG = { "UUID_QUESTION_GROUP", "IS_ACTIVE", "QUESTION_GROUP_LABEL" };
		this.auditInfoQG = new AuditInfo("MS_QUESTIONGROUP", pkColsQG, pkDbColsQG, colsQG, dbColsQG);

		String[] pkColsF = { "uuidForm" };
		String[] pkDbColsF = { "UUID_FORM" };
		String[] colsF = { "uuidForm", "formLastUpdate" };
		String[] dbColsF = { "UUID_FORM", "FORM_LAST_UPDATE" };
		this.auditInfoF = new AuditInfo("MS_FORM", pkColsF, pkDbColsF, colsF, dbColsF);
		
		String[] pkColsQoG = { "uuidQuestionOfGroup" };
		String[] pkDbColsQoG = { "UUID_QUESTION_OF_GROUP" };
		String[] colsQoG = { "uuidQuestionOfGroup", "msQuestiongroup.uuidQuestionGroup", "msQuestion.uuidQuestion", "seqOrder" };
		String[] dbColsQoG = { "UUID_QUESTION_OF_GROUP", "UUID_QUESTION_GROUP", "UUID_QUESTION", "SEQ_ORDER" };
		this.auditInfoQoG = new AuditInfo("MS_QUESTIONOFGROUP", pkColsQoG, pkDbColsQoG, colsQoG, dbColsQoG);
	}
	
	@Override
	public Map getQuestionGroupList(Object params, Object orders,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO().selectAll(MsQuestiongroup.class, params, orders, pageNumber, pageSize);
		return result;
	}

	@Override
	public MsQuestiongroup selectOneQuestionGroup(long uuid, AuditContext callerId) {
		MsQuestiongroup result = (MsQuestiongroup)this.getManagerDAO().selectOne(MsQuestiongroup.class, uuid);			
		return result;
	}

	@Override
	public MsQuestionofgroup getQuestionDetail(long uuidQuestion, AuditContext callerId) {	
		MsQuestionofgroup result = new MsQuestionofgroup();
		MsQuestion msQ = (MsQuestion) this.getManagerDAO().selectOne(
			"from MsQuestion mq left join fetch mq.msAnswertype left join fetch mq.msAssettag "
			+ " left join fetch mq.msCollectiontag join fetch mq.amMssubsystem "
			+ "where mq.uuidQuestion = :uuidQuestion", new Object[][] {{"uuidQuestion", uuidQuestion}});			
		result.setMsQuestion(msQ);			
		LOG.trace("result = {}", result);
		
		return result;
	}

	@Override
	public Map getQuestionList(long uuidQuestiongroup, AuditContext callerId) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();			
		condition.append(" and mqog.msQuestiongroup.uuidQuestionGroup=:uuidQuestionGroup");
		paramMap.put("uuidQuestionGroup", uuidQuestiongroup);
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mqog.seqOrder asc");
		Map result = this.getManagerDAO().selectAll(
				"from MsQuestionofgroup mqog join fetch mqog.msQuestiongroup mg join fetch mqog.msQuestion where 1=1"
						+ condition.toString() + orderQuery.toString(),
				paramMap);

		return result;
	}
	
	@Override
	public Map questionList(Object params, Object orders, int pageNumber, int pageSize,
			AuditContext callerId) {
		Map<String, Object>  result = this.getManagerDAO().selectAll(MsQuestion.class,
				params, orders, pageNumber, pageSize);
		return result;
	}

	@Override
	public List<MsQuestion> getListQuestion(long[] selectedQuestionArr,
			AuditContext callerId) {
		List<MsQuestion> result = new ArrayList<MsQuestion>();
		if (selectedQuestionArr != null && !ArrayUtils.isEmpty(selectedQuestionArr)) {
			for (int i = 0; i < selectedQuestionArr.length; i++) {
				MsQuestion msQuestion = this.getManagerDAO().selectOne(MsQuestion.class, selectedQuestionArr[i]);
				result.add(msQuestion);
			}
		}
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertQuestionGroup (MsQuestiongroup msQuestionGroup, String question, String order, long subsystem, AuditContext callerId){
        boolean exist = this.checkQuestionGroupName(msQuestionGroup.getQuestionGroupLabel(), subsystem, callerId);
		
		if (exist) {
			throw new EntityNotUniqueException(
				this.messageSource.getMessage("service.global.existed", 
					new Object[]{"Question Group " + msQuestionGroup.getQuestionGroupLabel()}, this.retrieveLocaleAudit(callerId))
					, msQuestionGroup.getQuestionGroupLabel());
		}

		msQuestionGroup.setDtmCrt(new Date());
		msQuestionGroup.setUsrCrt(callerId.getCallerId());
		msQuestionGroup.setAmMssubsystem(msQuestionGroup.getAmMssubsystem());
		
		this.getManagerDAO().insert(msQuestionGroup);
		this.auditManager.auditAdd(msQuestionGroup, auditInfoQG, callerId.getCallerId(), "");
		this.insertQuestionOfGroup(question, order, msQuestionGroup.getUuidQuestionGroup(), callerId);
		LOG.info("Finish Insert Question Group");
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertQuestionOfGroup(String question, String order, long uuidQuestionGroup, AuditContext callerId) {
		Object[][] param = {{ "uuidQustionGroup", uuidQuestionGroup }};
		this.getManagerDAO().deleteNativeString("delete from MS_QUESTIONOFGROUP where UUID_QUESTION_GROUP = :uuidQustionGroup", param);
		
		if (question != null && !question.isEmpty() ) {
			String[] selectedQuestion = question.split(",");
			String[] orderQuestion = order.split(",");
			for (int i = 0; i < selectedQuestion.length; i++) {
				LOG.info("Start Insert Question Of Group");
				if (selectedQuestion[i] != null && !selectedQuestion[i].isEmpty()) {
					MsQuestionofgroup bean = new MsQuestionofgroup();
					bean.setUsrCrt(callerId.getCallerId());
					bean.setDtmCrt(new Date());
					
					MsQuestiongroup msQuestiongroup= this.getManagerDAO().selectOne(MsQuestiongroup.class, uuidQuestionGroup);
					bean.setMsQuestiongroup(msQuestiongroup);
					
					MsQuestion msQuestion = this.getManagerDAO().selectOne(MsQuestion.class, Long.parseLong(selectedQuestion[i]));
					bean.setMsQuestion(msQuestion);
					
					bean.setSeqOrder(Integer.parseInt(orderQuestion[i]));
					this.getManagerDAO().insert(bean);
					this.auditManager.auditAdd(bean, auditInfoQoG, callerId.getCallerId(), "");					
				}
				LOG.info("Finish Insert Question Of Group");
			}
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateQuestionGroup(long uuidQuestionGroup, MsQuestiongroup msQuestionGroup, 
			String question, String order, long uuidSubsystem, AuditContext callerId) {
        MsQuestiongroup obj= this.getManagerDAO().selectOne(MsQuestiongroup.class, uuidQuestionGroup);
		
		boolean exist = this.checkQuestionGroupName(msQuestionGroup.getQuestionGroupLabel(), uuidSubsystem, callerId);
		
		if (exist) {
			if (!StringUtils.equalsIgnoreCase(obj.getQuestionGroupLabel(), msQuestionGroup.getQuestionGroupLabel())) {
				throw new EntityNotUniqueException(
					this.messageSource.getMessage("service.global.existed", 
						new Object[]{"Question Group " + msQuestionGroup.getQuestionGroupLabel()}, this.retrieveLocaleAudit(callerId))
						, msQuestionGroup.getQuestionGroupLabel());
			}
		}
		
		obj.setQuestionGroupLabel(msQuestionGroup.getQuestionGroupLabel());
		obj.setIsActive(msQuestionGroup.getIsActive());
		
		Object[][] paramQOF = { { "uuidQuestionGroup", uuidQuestionGroup } };
		
		this.getManagerDAO().updateNative("eform.questiongroup.updateForm", paramQOF);
		this.auditManager.auditEdit(obj, auditInfoQG, callerId.getCallerId(), "");
		this.getManagerDAO().update(obj);
		this.insertQuestionOfGroup(question, order, uuidQuestionGroup, callerId);
		
		LOG.info("Finish Update Question Group");		
	}

	@Override
	public List getAnswerTypeList(AuditContext callerId) {
		Object[][] params = {{Restrictions.eq("isActive", "1")}};
		String[][] orders = {{"answerTypeName", GlobalVal.ROW_ORDER_ASC}};
		Map<String, Object> listOfAnswerType = this.getManagerDAO().list(MsAnswertype.class, params, orders);
		List<MsAnswertype> result = (List) listOfAnswerType.get(GlobalKey.MAP_RESULT_LIST);
		return result;	
	}
	
	public boolean checkQuestionGroupName(String questionGroupName, long subsystem, AuditContext callerId) {
		boolean exist = false;
		Object[][] param = { {"questionGroupLabel", questionGroupName}, {"uuidSubsystem", subsystem} };
		Integer flag = (Integer) this.getManagerDAO().selectOneNative("eform.questiongroup.questionGroupValidity", param);
		if (flag != 0){
			exist = true;
		}
		return exist;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateForm(long uuidQuestionGroup, AuditContext callerId) {
		Object[][] paramsForm = {{Restrictions.eq("msQuestiongroup.uuidQuestionGroup", uuidQuestionGroup)}};
		Map formMap = this.getManagerDAO().list(MsQuestiongroupofform.class, paramsForm, null);
		List<MsQuestiongroupofform> formList = (List<MsQuestiongroupofform>) formMap.get(GlobalKey.MAP_RESULT_LIST);
		
		if (null != formList && !formList.isEmpty()) {
			for (MsQuestiongroupofform bean2 : formList) {
				MsForm form = this.getManagerDAO().selectOne(MsForm.class, bean2.getMsForm().getUuidForm());
				form.setFormLastUpdate(new Date());
				this.auditManager.auditEdit(form, auditInfoF, callerId.getCallerId(), "");
				this.getManagerDAO().update(form);
			}
		}
	}
	
}

