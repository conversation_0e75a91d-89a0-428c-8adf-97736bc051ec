package com.adins.mss.businesslogic.impl.common;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.QuestionListLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsAssettag;
import com.adins.mss.model.MsCollectiontag;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsOrdertag;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsQuestiongroupofform;
import com.adins.mss.model.MsQuestionofgroup;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericQuestionListLogic extends BaseLogic implements QuestionListLogic, MessageSourceAware  {
	private AuditInfo auditInfoQ;
	private AuditInfo auditInfoF;

	@Autowired
	private MessageSource messageSource;

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public GenericQuestionListLogic() {
		String[] pkColsQ = { "uuidQuestion" };
		String[] pkDbColsQ = { "UUID_QUESTION" };
		String[] colsQ = { "uuidQuestion", "msCollectiontag.uuidCollectionTag", 
				"msAssettag.uuidAssetTag", "msAnswertype.uuidAnswerType",
				"lovGroup", "isActive", "refId", "questionLabel", "isVisible",
				"isMandatory", "isReadonly", "isHolidayAllowed", "maxLength",
				"regexPattern", "imgQlt", "msOrderTag.uuidOrderTag" };
		String[] dbColsQ = { "UUID_QUESTION", "UUID_COLLECTION_TAG", "UUID_ASSET_TAG",
				"UUID_ANSWER_TYPE", "LOV_GROUP", "IS_ACTIVE", "REF_ID",
				"QUESTION_LABEL", "IS_VISIBLE", "IS_MANDATORY", "IS_READONLY",
				"IS_HOLIDAY_ALLOWED", "MAX_LENGTH", "REGEX_PATTERN",
				"IMG_QLT", "UUID_ORDER_TAG" };
		this.auditInfoQ = new AuditInfo("MS_QUESTION", pkColsQ, pkDbColsQ, colsQ, dbColsQ);

		String[] pkColsF = { "uuidForm" };
		String[] pkDbColsF = { "UUID_FORM" };
		String[] colsF = { "uuidForm", "formLastUpdate" };
		String[] dbColsF = { "UUID_FORM", "FORM_LAST_UPDATE" };
		this.auditInfoF = new AuditInfo("MS_FORM", pkColsF, pkDbColsF, colsF, dbColsF);
	}

	@Override
	public Map getQuestionList(long uuidMsSubsystem, String questionLabel, int searchBy, 
			String searchValue, String isActive, int pageNumber, int pageSize, AuditContext callerId) {
		
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and mq.amMssubsystem.uuidMsSubsystem=:uuidMsSubsystem");
		paramMap.put("uuidMsSubsystem", uuidMsSubsystem);
		
		if (StringUtils.isNotBlank(questionLabel)) { //quick-search
			condition.append(" and mq.questionLabel like :questionLabel");
			paramMap.put("questionLabel", "%" + questionLabel + "%");
		}
		else {
			if (StringUtils.isNotBlank(searchValue)) {
				if (0 == searchBy) {// answerType
					condition.append(" and mq.msAnswertype.uuidAnswerType = :uuidAnswerType");
					paramMap.put("uuidAnswerType", Long.parseLong(searchValue));
				}
				else if (1 == searchBy) {//refId
					condition.append(" and mq.refId like :refId");
					paramMap.put("refId", "%" + searchValue + "%");
				}
			}
			if (StringUtils.isNotBlank(isActive)) {
				condition.append(" and mq.isActive = :isActive");
				paramMap.put("isActive", isActive);
			}
		}
		
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mq.questionLabel asc");
		Map<String, Object> result = this.getManagerDAO().selectAll(
				"from MsQuestion mq join fetch mq.msAnswertype join fetch mq.amMssubsystem where 1=1"
						+ condition.toString() + orderQuery.toString(),
				"select count(*) from MsQuestion mq join mq.msAnswertype join mq.amMssubsystem where 1=1"
						+ condition.toString(),
				paramMap, pageNumber, pageSize);
		List < MsQuestion > resultList = (List) result.get(GlobalKey.MAP_RESULT_LIST);
		result.put(GlobalKey.MAP_RESULT_LIST, resultList);

		return result;
	}

	@Override
	public MsQuestion selectOneQuestion(long uuidQuestion,
			AuditContext callerId) {
		MsQuestion result = (MsQuestion) this.getManagerDAO().selectOne(
			"from MsQuestion mq join fetch mq.msAnswertype "
			+ "left join fetch mq.msAssettag "
			+ "left join fetch mq.msCollectiontag "
			+ "left join fetch mq.msOrdertag "
			+ "where mq.uuidQuestion = :uuidQuestion", 
			new Object[][] {{"uuidQuestion", uuidQuestion}});

		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertQuestion(MsQuestion obj, AmMssubsystem subsystem, AuditContext callerId) {
			
        if (checkValidity(obj.getRefId(), subsystem.getUuidMsSubsystem(),callerId)) {
			obj.setAmMssubsystem(subsystem);
			obj.setRefId(obj.getRefId().toUpperCase());
			
			Object[][] params = {{Restrictions.eq("codeAnswerType", obj.getMsAnswertype().getCodeAnswerType())}};
			obj.setMsAnswertype(this.getManagerDAO().selectOne(MsAnswertype.class, params));
			if("1".equals(obj.getIsActive())){
				if (GlobalVal.SUBSYSTEM_MS.equalsIgnoreCase(subsystem.getSubsystemName())) {
					MsAssettag tag = this.getManagerDAO().selectOne(MsAssettag.class, 
							obj.getMsAssettag().getUuidAssetTag());
					if(checkValidityTagging(obj.getMsAssettag().getUuidAssetTag(), 
							GlobalVal.SUBSYSTEM_MS, obj.getUuidQuestion(), callerId)){
						obj.setMsAssettag(tag);
						obj.setMsCollectiontag(null);
						obj.setMsOrdertag(null);
					}
					else {
						throw new EntityNotUniqueException(
							this.messageSource.getMessage("businesslogic.questionlist.questionexist", 
								new Object[]{"Asset Tag",tag.getAssetTagName()}, this.retrieveLocaleAudit(callerId))
								, obj.getRefId());
					}
				}
				else if (GlobalVal.SUBSYSTEM_MC.equalsIgnoreCase(subsystem.getSubsystemName())) {
					MsCollectiontag tag = this.getManagerDAO().selectOne(MsCollectiontag.class, 
							obj.getMsCollectiontag().getUuidCollectionTag());
					if(checkValidityTagging(obj.getMsCollectiontag().getUuidCollectionTag(), 
							GlobalVal.SUBSYSTEM_MC, obj.getUuidQuestion(), callerId)){
						obj.setMsCollectiontag(tag);
						obj.setMsAssettag(null);
						obj.setMsOrdertag(null);
					}
					else {
						throw new EntityNotUniqueException(
							this.messageSource.getMessage("businesslogic.questionlist.questionexist", 
								new Object[]{"Collection Tag",tag.getTagName()}, this.retrieveLocaleAudit(callerId))
								, obj.getRefId());
					}
				}
				else if (GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(subsystem.getSubsystemName())) {
					MsOrdertag tag = this.getManagerDAO().selectOne(MsOrdertag.class, 
							obj.getMsOrdertag().getUuidOrderTag());
					if(checkValidityTagging(obj.getMsOrdertag().getUuidOrderTag(), 
							GlobalVal.SUBSYSTEM_MO, obj.getUuidQuestion(), callerId)){
						obj.setMsOrdertag(tag);
						obj.setMsAssettag(null);
						obj.setMsCollectiontag(null);
					}
					else {
						throw new EntityNotUniqueException(
							this.messageSource.getMessage("businesslogic.questionlist.questionexist", 
								new Object[]{"Order Tag",tag.getTagName()}, this.retrieveLocaleAudit(callerId))
								, obj.getRefId());
					}
				}
				else {
					obj.setMsCollectiontag(null);
					obj.setMsAssettag(null);
					obj.setMsOrdertag(null);
				}
			}
			
			obj.setUsrCrt(callerId.getCallerId());
			obj.setDtmCrt(new Date());
			
			this.getManagerDAO().insert(obj);
			this.auditManager.auditAdd(obj, auditInfoQ, callerId.getCallerId(), "");
		}
		else {
			throw new EntityNotUniqueException(
				this.messageSource.getMessage("service.global.existed", 
					new Object[]{"Identifier "+obj.getRefId()}, this.retrieveLocaleAudit(callerId))
					, obj.getRefId());
		}	
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateQuestion(long uuidQuestion, AmMssubsystem subsystem, MsQuestion obj,
			AuditContext callerId) {
			
		MsQuestion question = selectOneQuestion(uuidQuestion, callerId);
		question.setQuestionLabel(obj.getQuestionLabel());
		
		question.setLovGroup(obj.getLovGroup());
		question.setIsActive(obj.getIsActive());
		question.setIsVisible(obj.getIsVisible());
		question.setIsMandatory(obj.getIsMandatory());
		question.setIsReadonly(obj.getIsReadonly());
		question.setIsHolidayAllowed(obj.getIsHolidayAllowed());
		question.setMaxLength(obj.getMaxLength());
		question.setRegexPattern(obj.getRegexPattern());
		question.setUsrUpd(callerId.getCallerId());
		question.setImgQlt(obj.getImgQlt());
		question.setDtmUpd(new Date());
		
		this.auditManager.auditEdit(question, auditInfoQ, callerId.getCallerId(), "");
		this.getManagerDAO().update(question);
	}

	@Override
	public List getLovList(AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("eform.question.getListLov", null, null);
		return result;	
	}

	@Override
	public List getAssetList(AuditContext callerId) {
		Object[][] params = {{Restrictions.eq("isActive", "1")}};
		String[][] orders = {{"assetTagName", GlobalVal.ROW_ORDER_ASC}};
		Map<String, Object> listOfAssetTags = this.getManagerDAO().list(MsAssettag.class, params, orders);
		List<MsAssettag> result = (List) listOfAssetTags.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}

	@Override
	public List getCollectionList( Object[][] params, AuditContext callerId) {
		String[][] orders = {{"tagName", GlobalVal.ROW_ORDER_ASC}};
		Map<String, Object> listOfCollectionTag = this.getManagerDAO().list( MsCollectiontag.class, params, orders);
		List<MsCollectiontag> result = (List) listOfCollectionTag.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}
	
	@Override
	public List getOrderList(AuditContext callerId) {	
		Object[][] params = {{Restrictions.eq("isActive", "1")}};
		String[][] orders = {{"tagName", GlobalVal.ROW_ORDER_ASC}};
		Map<String, Object> listOfOrderTag = this.getManagerDAO().list(MsOrdertag.class, params, orders);
		List<MsOrdertag> result = (List) listOfOrderTag.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}

	@Override
	public List getAnswerTypeList(AuditContext callerId) {	
		Object[][] params = {{Restrictions.eq("isActive", "1")}};
		String[][] orders = {{"answerTypeName", GlobalVal.ROW_ORDER_ASC}};
		Map<String, Object> listOfAnswerType = this.getManagerDAO().list(MsAnswertype.class, params, orders);
		List<MsAnswertype> result = (List) listOfAnswerType.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}
	
	private boolean checkValidity(String refId, long subsystem, AuditContext callerId) {
		boolean valid = true;
		Object[][] param = { {"refId", refId}, {"uuidSubsystem", subsystem} };
		Integer flag = (Integer) this.getManagerDAO().selectOneNative("eform.question.questionValidity", param);
		if( flag != 0){
			valid = false;
		}
		return valid;
	}
	
	private boolean checkValidityTagging(long tagging, String subsystem, long uuidQuestion, AuditContext callerId) {
		boolean valid = true;
		Object[][] param = {{"uuidTag", tagging}, {"uuidQuestion", uuidQuestion}};
		String query;
		if(GlobalVal.SUBSYSTEM_MS.equals(subsystem)){
			query = "eform.question.questionAssetTaggingValidity";
		}
		else if (GlobalVal.SUBSYSTEM_MC.equals(subsystem)){
			query = "eform.question.questionCollTaggingValidity";
		}
		else {
			query = "eform.question.questionOrderTaggingValidity";
		}
			
		Integer flag = (Integer) this.getManagerDAO().selectOneNative(query, param);
		if( flag > 0){
			valid = false;
		}
		return valid;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateForm(long uuidQuestion, AuditContext callerId) {
		Object[][] paramsQG = {{Restrictions.eq("msQuestion.uuidQuestion", uuidQuestion)}};
		Map questionGroupMap = this.getManagerDAO().list(MsQuestionofgroup.class, paramsQG, null);
		List<MsQuestionofgroup> questionGroupList = (List<MsQuestionofgroup>) questionGroupMap.get(GlobalKey.MAP_RESULT_LIST);
		
		if (null != questionGroupList && !questionGroupList.isEmpty()) {
			for (MsQuestionofgroup bean1 : questionGroupList) {
				Object[][] paramsForm = {{Restrictions.eq("msQuestiongroup.uuidQuestionGroup", 
						bean1.getMsQuestiongroup().getUuidQuestionGroup())}};
				Map formMap = this.getManagerDAO().list(MsQuestiongroupofform.class, paramsForm, null);
				List<MsQuestiongroupofform> formList = (List<MsQuestiongroupofform>) formMap.get(GlobalKey.MAP_RESULT_LIST);
				
				if (null != formList && !formList.isEmpty()) {
					for (MsQuestiongroupofform bean2 : formList) {
						MsForm form = this.getManagerDAO().selectOne(MsForm.class, bean2.getMsForm().getUuidForm());
						form.setFormLastUpdate(new Date());
						this.auditManager.auditEdit(form, auditInfoF, callerId.getCallerId(), "");
						this.getManagerDAO().update(form);
					}
				}
			}
		}	
	}
	
	@Override
	public List getLuOnlineList(AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("eform.question.getListLuOnline", null, null);
		return result;
	}
	
}