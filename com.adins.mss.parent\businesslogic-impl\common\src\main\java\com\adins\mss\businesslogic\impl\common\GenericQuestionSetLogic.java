package com.adins.mss.businesslogic.impl.common;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.QuestionSetLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.services.model.common.QuestionSetBean;


public class GenericQuestionSetLogic extends BaseLogic implements QuestionSetLogic  {
//	private static final Logger LOG = LoggerFactory.getLogger(GenericQuestionSetLogic.class);
	@SuppressWarnings("rawtypes")
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<QuestionSetBean> getQuestionSetByFormId(long formId, int version, AuditContext callerId){
		List<QuestionSetBean> listQuestionSet = new ArrayList<QuestionSetBean>();
		List listResult = null;
		AmMsuser amUser = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", NumberUtils.toLong(callerId.getCallerId())}});
		String subsystem = amUser.getAmMssubsystem().getSubsystemName();
		Object[][] params = { {"formId", formId}, {"version", version} };
		if(subsystem.equals(GlobalVal.SUBSYSTEM_MS)){
			listResult = this.getManagerDAO().selectAllNative("services.common.form.getQuestionSetByFormIdMs2", params, null);
		}else if(subsystem.equals(GlobalVal.SUBSYSTEM_MC)){
			listResult = this.getManagerDAO().selectAllNative("services.common.form.getQuestionSetByFormIdMc2", params, null);
		}else if(subsystem.equals(GlobalVal.SUBSYSTEM_MO)){
			listResult = this.getManagerDAO().selectAllNative("services.common.form.getQuestionSetByFormIdMo2", params, null);
		}else if(subsystem.equals(GlobalVal.SUBSYSTEM_MT)){ //sama seperti survey 
			listResult = this.getManagerDAO().selectAllNative("services.common.form.getQuestionSetByFormIdMs2", params, null);
		}
		if (listResult != null) {
			Iterator itr = listResult.iterator();
			while (itr.hasNext()) {
				Map mp = (Map) itr.next();
				QuestionSetBean qs = new QuestionSetBean();
				qs.setQuestionGroupId(mp.get("d0").toString());
				qs.setQuestionGroupName((String)mp.get("d1"));
				qs.setQuestionGroupOrder((Integer)mp.get("d2"));
				qs.setQuestionId(mp.get("d3").toString());
				qs.setQuestionLabel((String)mp.get("d4"));
				qs.setQuestionOrder((Integer)mp.get("d5"));
				qs.setAnswerType((String)mp.get("d6"));
				qs.setChoiceFilter((String)mp.get("d7"));
				qs.setIsMandatory((String)mp.get("d8"));
				qs.setMaxLength((Integer)mp.get("d9"));
				qs.setIsVisible((String)mp.get("d10"));
				qs.setIsReadonly((String)mp.get("d11"));
				qs.setRegexPattern((String)mp.get("d12"));
				qs.setRelQuestions((String)mp.get("d13"));
				qs.setCalculate((String)mp.get("d14"));
				qs.setContraintMessage((String)mp.get("d15"));
				qs.setLovGroup((String)mp.get("d16"));
				qs.setIdentifierName((String)mp.get("d17"));
				qs.setTagName((String)mp.get("d18"));
				qs.setHolidayAllowed((String)mp.get("d19"));
				qs.setImgQlt((String)mp.get("d20"));
				qs.setQuestionValidation((String)mp.get("d21"));
				qs.setQuestionErrorMessage((String)mp.get("d22"));
				qs.setQuestionValue((String)mp.get("d23"));
				qs.setFormVersion(mp.get("d24").toString());
				qs.setRelevantMandatory((String)mp.get("d25"));
				listQuestionSet.add(qs);
			}
		}
		return listQuestionSet;
	}
}
