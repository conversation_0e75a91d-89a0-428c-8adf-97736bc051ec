package com.adins.mss.businesslogic.impl.common;

import java.util.Date;

import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.RefreshTaskLogic;

public class GenericRefreshTaskLogic extends BaseLogic implements RefreshTaskLogic {	
//	private static final Logger LOG = LoggerFactory.getLogger(GenericRefreshTaskLogic.class);
	
	@Transactional(readOnly=true,isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Integer refreshTask(Date dtmCrt, AuditContext callerId){
		Object[][] params = { {"dtmCrt", dtmCrt}, {"uuidMsUser", callerId.getCallerId()} };
		Integer x = 0;
		Integer n= (Integer) this.getManagerDAO().selectOneNative("services.common.task.refreshTask", params);
		if(n!=null){
			x = n;
		}
		return x;	
	}
}
