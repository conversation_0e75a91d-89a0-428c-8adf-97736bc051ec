package com.adins.mss.businesslogic.impl.common;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.RegionLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsRegion;
import com.adins.mss.model.MsRegionofbranch;

@SuppressWarnings("rawtypes")
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericRegionLogic extends BaseLogic implements RegionLogic, MessageSourceAware {
	private AuditInfo auditInfoR;
	private AuditInfo auditInfoRoB;
	
	@Autowired
	private MessageSource messageSource;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	public GenericRegionLogic() {
		String[] pkColsR = { "uuidRegion" };
		String[] pkDbColsR = { "UUID_REGION" };
		String[] colsR = { "uuidRegion", "isActive", "regionCode", 
				"regionName", "city" };
		String[] dbColsR = { "UUID_REGION", "IS_ACTIVE", 
				"REGION_CODE", "REGION_NAME", "CITY" };
		this.auditInfoR = new AuditInfo("MS_REGION", pkColsR, 
				pkDbColsR, colsR, dbColsR);

		String[] pkColsRoB = { "uuidRegionOfBranch" };
		String[] pkDbColsRoB = { "UUID_REGION_OF_BRANCH" };
		String[] colsRoB = { "uuidRegionOfBranch", "msRegion.uuidRegion", 
				"msBranch.uuidBranch" };
		String[] dbColsRoB = { "UUID_REGION_OF_BRANCH", "UUID_REGION", 
				"UUID_BRANCH" };
		this.auditInfoRoB = new AuditInfo("MS_REGIONOFBRANCH", pkColsRoB,
				pkDbColsRoB, colsRoB, dbColsRoB);
	}

	@Override
	public Map getRegionList(Object params, Object orders, int pageNumber,
			int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;
		result = this.getManagerDAO().selectAll(MsRegion.class, 
				params, orders, pageNumber, pageSize);
		return result;
	}
	
	@Override
	public MsRegion selectOneRegion(String uuidRegion, AuditContext callerId) {
		MsRegion result = (MsRegion)this.getManagerDAO().selectOne(MsRegion.class, 
				Long.valueOf(uuidRegion));	
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteRegion(String uuidRegion, AuditContext callerId) {
		Object param[][] = { {Restrictions.eq("msRegion.uuidRegion", 
				uuidRegion)} };
		Map regionBranch = this.getManagerDAO().selectAll(
				MsRegionofbranch.class, param, null);
		
		if ((Integer) regionBranch.get(
				GlobalKey.MAP_RESULT_SIZE) > 0) {
			throw new DatabaseException(
					DatabaseException.Reason.CONSTRAINT_VIOLATION, 
					(this.messageSource.getMessage("businesslogic.error.haschild", null,
					this.retrieveLocaleAudit(callerId))), new Exception());
		}
			
		MsRegion obj = new MsRegion();
		obj.setUuidRegion(Long.valueOf(uuidRegion));
		this.auditManager.auditDelete(obj, auditInfoR, 
		callerId.getCallerId(), "");
		this.getManagerDAO().delete(obj);		
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void insertRegion(MsRegion region, AuditContext callerId) {
		String [][] params = { {"regionId",region.getRegionCode()} };
		if (insertRegionValidity(params, callerId)) { 
			region.setRegionCode(region.getRegionCode().toUpperCase());
			region.setIsActive("1");
			region.setUsrCrt(callerId.getCallerId());
			region.setDtmCrt(new Date());
			this.getManagerDAO().insert(region);
			this.auditManager.auditAdd(region, auditInfoR, 
					callerId.getCallerId(), "");	
		} 
		else {
			Object[] object = { region.getRegionCode() };
			throw new EntityNotUniqueException((this.messageSource.getMessage(
					"businesslogic.region.regionidexist", object, this.retrieveLocaleAudit(callerId))),
					region.getRegionCode());
		}
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateRegion(String uuidRegion, MsRegion region,
			AuditContext callerId) {
		MsRegion obj = selectOneRegion(uuidRegion, callerId);
		obj.setRegionCode(region.getRegionCode().toUpperCase());
		obj.setRegionName(region.getRegionName());
		obj.setCity(region.getCity());
		obj.setUsrUpd(callerId.getCallerId());
		obj.setDtmUpd(new Date());
		this.auditManager.auditEdit(obj, auditInfoR, 
				callerId.getCallerId(), "");
		this.getManagerDAO().update(obj);
	}

	@Override
	public Map getBranchListOfRegion(long uuidRegion,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		
		condition.append(" and mrofb.msRegion.uuidRegion=:uuidRegion");
		paramMap.put("uuidRegion", uuidRegion);
			
		result = this.getManagerDAO().selectAll(
				"from MsRegionofbranch mrofb "
				+ "join fetch mrofb.msRegion join fetch mrofb.msBranch "
				+ "where 1=1"
				+ condition.toString(),
				"select count(*) "
				+ "from MsRegionofbranch mrofb "
				+ "join mrofb.msRegion join mrofb.msBranch "
				+ "where 1=1"
				+ condition.toString(),
				paramMap, pageNumber, pageSize);
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteBranch(String uuidRegionOfBranch, 
			AuditContext callerId) {
		MsRegionofbranch obj = new MsRegionofbranch();
		obj.setUuidRegionOfBranch(Long.valueOf(uuidRegionOfBranch));
		this.auditManager.auditDelete(obj, auditInfoRoB, 
				callerId.getCallerId(), "");
		this.getManagerDAO().delete(obj);
	}

	@Override
	public List listBranch(Object params, AuditContext callerId) {
		List result=null;
		result = this.getManagerDAO().selectAllNative(
				"setting.region.listBranch", params, null);
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void insertRegionOfBranches(String uuidRegion, 
			String[] uuidBranches, AuditContext callerId) {
		MsRegion region = new MsRegion();
		region.setUuidRegion(Long.valueOf(uuidRegion));
			
		for (int i = 0; i < uuidBranches.length; i++) {
			if (uuidBranches[i].isEmpty()) {
				continue;
			}
			MsBranch msBranch = new MsBranch();
			msBranch.setUuidBranch(Long.valueOf(uuidBranches[i]));
			
			MsRegionofbranch regionOfBranch = new MsRegionofbranch();
				
			regionOfBranch.setUsrCrt(callerId.getCallerId());
			regionOfBranch.setDtmCrt(new Date());
			regionOfBranch.setMsBranch(msBranch);
			regionOfBranch.setMsRegion(region);
				
			this.getManagerDAO().insert(regionOfBranch);
			this.auditManager.auditAdd(regionOfBranch, auditInfoRoB, 
					callerId.getCallerId(), "");
		}
	}

	public boolean insertRegionValidity(Object params, 
			AuditContext callerId) {
		boolean validity = true;
		Integer result = (Integer)this.getManagerDAO().selectOneNative(
				"setting.region.regionValidity", params);	
		if ( result != 0) {
			validity = false;
		}	
		return validity;
	}

	@Override
	public Integer countListBranch(Object params, AuditContext callerId) {
		Integer result;
		result = (Integer)this.getManagerDAO().selectOneNative(
				"setting.region.cntBranch", params);	
		return result;
	}
}
