package com.adins.mss.businesslogic.impl.common;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFPicture;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtilities;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.plot.PiePlot;
import org.jfree.chart.title.LegendTitle;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.ui.RectangleEdge;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.common.ReportAbsensiLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportAbsensiLogic extends BaseLogic implements
		ReportAbsensiLogic, MessageSourceAware {
	private static final String[] HEADER_SUMMARY = { "No", "Branch", "<08:00",
			"08:00-08:30", "08:30-09:00", ">09:00", "Absent", "Grand Total" };
	private static final int[] SUMMARY_COLUMN_WIDTH = { 10 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256 };
	private static final String[] HEADER_DETAIL = { "Date",
			"Attendance In Time", "Attendance Address", "Location" };
	private static final int[] DETAIL_COLUMN_WIDTH = { 20 * 256, 20 * 256,
			60 * 256, 60 * 256 };
	private static final Logger LOG = LoggerFactory
			.getLogger(GenericReportAbsensiLogic.class);
	
	@Autowired
    private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    
	@Autowired
	private GlobalLogic  globalLogic;
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}
	
	@Override
	public List getComboBranch(String branchId, AuditContext callerId) {
		List result = null;
		String[][] params = { { "branchId", branchId } };
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}

	@Override
	public List getReportAttendance(String subsystemId, String branchId,
			String startDate, String endDate, AuditContext callerId) {
		List result = null;
		String[][] params = { { "branchId", branchId }, { "subsystemId", subsystemId }, 
				{ "startDate", startDate }, { "endDate", endDate } };
		result = this.getManagerDAO().selectAllNative("report.absensi.getAttendanceSummary", params, null);
		return result;
	}

	@Override
	public List getUser(String uuidUser, int pageNo, int pageSize, AuditContext callerId) {
		List result = null;
		Object[][] params = { { "uuidSpv", uuidUser }, { "start", (pageNo - 1) * pageSize + 1 },
				{ "end", (pageNo - 1) * pageSize + pageSize } };
		result = this.getManagerDAO().selectAllNative("report.absensi.getUsersAll", params, null);
		return result;
	}

	@Override
	public List getUserByBranch(AmMsuser user, String uuidBranch, int pageNo, int pageSize,
			AuditContext callerId) {
		List result = null;
		String subsystem = String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem());
			
		if ("%".equals(uuidBranch)) {
			Object[][] params = { { "uuidBranch", user.getMsBranch().getUuidBranch() }, 
					{ "subsystem", subsystem }, { "start", (pageNo - 1) * pageSize + 1 },
					{ "end", (pageNo - 1) * pageSize + pageSize } };
				
			result = this.getManagerDAO().selectAllNative("report.absensi.getUsersAllByBranch", params, null);
		}
		else {
			Object[][] params = { { "uuidBranch", uuidBranch }, { "subsystem", subsystem },
					{ "start", (pageNo - 1) * pageSize + 1 }, { "end", (pageNo - 1) * pageSize + pageSize } };
				
			result = this.getManagerDAO().selectAllNative("report.absensi.getUsersAllByBranchSpv", params, null);
		}
		return result;
	}

	@Override
	public Integer countUserByBranch(AmMsuser user, String uuidBranch, AuditContext callerId) {
		Integer result;
		String subsystem = String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem());
			
		if ("%".equals(uuidBranch)) {
			Object[][] params = { { "uuidBranch", user.getMsBranch().getUuidBranch() }, 
					{ "subsystem", subsystem } };
			result = (Integer) this.getManagerDAO().selectOneNative(
					"report.absensi.getUsersAllByBranchCount", params);
		} 
		else {
			Object[][] params = { { "uuidBranch", uuidBranch }, { "subsystem", subsystem }};
			result = (Integer) this.getManagerDAO().selectOneNative(
					"report.absensi.getUsersAllByBranchCountSpv", params);
		}
		return result;
	}

	@Override
	public Map getReportAttendanceDetail(AmMsuser user, String type, String branchId, 
			String userId, String startDate, String endDate, AuditContext callerId) {
		Map result = new HashMap<>();
		String[][] params = {{"branchId", "0".equals(branchId) ? String.valueOf(user.getMsBranch()
									.getUuidBranch()) : branchId },
				{ "subsystemId", String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem()) },
				{ "userId", userId.isEmpty() ? "%" : userId }, { "startDate", startDate }, { "endDate", endDate } };

		String branch = "";
		String name = "";
		String hbm = !"0".equals(branchId) ? "report.absensi.getAttendanceDetailByBranch"
				: "report.absensi.getAttendanceDetail";
		List tempList = new ArrayList<>();
		Map tempMap = new HashMap<>();

		if (!"0".equals(branchId)) {
			MsBranch brc = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(branchId));
			branch = brc.getBranchCode() + " - " + brc.getBranchName();
		} 
		else if ("0".equals(type)) {
			MsBranch brc = this.getManagerDAO().selectOne(MsBranch.class, user.getMsBranch().getUuidBranch());
			branch = brc.getBranchCode() + " - " + brc.getBranchName();
		} 
		else {
			branch = "All Branches";
		}
		result.put("branch", branch);

		List list = this.getManagerDAO().selectAllNative(hbm, params, null);
		List resultList = new ArrayList<>();
		for (int i = 0; i < list.size(); i++) {
			Map map = (Map) list.get(i);
			Map temp = new HashMap();

			temp.put("d0", map.get("d1").toString());
			temp.put("d1", map.get("d2").toString());
			temp.put("d2", map.get("d3").toString());
			temp.put("d3", map.get("d4").toString());
			if (map.get("d5") != null) {
				temp.put("d4", map.get("d5").toString());
			}
			tempList.add(temp);

			name = map.get("d0").toString();
			if ((i + 1) == list.size() || !name.equals(((Map) list.get(i + 1)).get("d0").toString())) {
				tempMap.put("user", name);
				tempMap.put("list", tempList);
				if (!name.isEmpty()) {
					resultList.add(tempMap);
					tempList = new ArrayList<>();
					tempMap = new HashMap<>();
				}
			}
		}
		result.put("resultList", resultList);
		return result;
	}

	@Override
	public byte[] exportExcel(AmMsuser user, String branchId, String userId,
			String startDate, String endDate, String type, AuditContext callerId) {
		XSSFWorkbook workbook = this.createXlsTemplate(user, branchId, userId,
				startDate, endDate, type, callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(AmMsuser user, String branchId,
			String userId, String startDate, String endDate, String type,
			AuditContext callerId) {
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setSubsystemCode(user.getAmMssubsystem().getSubsystemName());
		reportBean.setUuidLoginId(String.valueOf(user.getUuidMsUser()));
		reportBean.setUuidBranch(branchId);
		reportBean.setUuidUser(userId);
		reportBean.setStartDate(startDate);
		reportBean.setEndDate(endDate);
		reportBean.setType(type);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(user);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("Attendance Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_ABSENSI.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}

	private XSSFWorkbook createXlsTemplate(AmMsuser user, String branchId,
			String userId, String startDate, String endDate, String type,
			AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Attendance Report");
			if (type.equalsIgnoreCase("0")) {
				List result = this.getReportAttendance(String.valueOf(user.getAmMssubsystem()
						.getUuidMsSubsystem()), String.valueOf(user.getMsBranch()
						.getUuidBranch()), startDate, endDate, callerId);
				DefaultPieDataset pieChart = this.createDataSummary(workbook,
						sheet, result, startDate, endDate);
				// creating chart
				JFreeChart myPieChart = ChartFactory.createPieChart(
						"Attendance Report", pieChart, true, true, false);
				PiePlot plot = (PiePlot) myPieChart.getPlot();
				plot.setLabelGenerator(null);

				LegendTitle legend = myPieChart.getLegend();
				legend.setPosition(RectangleEdge.RIGHT);

				int width = 640; /* Width of the chart */
				int height = 480; /* Height of the chart */
				float quality = 1;
				ByteArrayOutputStream chart_out = new ByteArrayOutputStream();
				ChartUtilities.writeChartAsJPEG(chart_out, quality, myPieChart, width, height);
				int my_picture_id = workbook.addPicture(chart_out.toByteArray(), Workbook.PICTURE_TYPE_JPEG);
				chart_out.close();
				XSSFDrawing drawing = sheet.createDrawingPatriarch();
				ClientAnchor my_anchor = new XSSFClientAnchor();
				/*
				 * Define top left corner, and we can resize picture suitable
				 * from there
				 */
				my_anchor.setCol1(1);
				my_anchor.setRow1(result.size() + 4);
				/* Invoke createPicture and pass the anchor point and ID */
				XSSFPicture my_picture = drawing.createPicture(my_anchor,
						my_picture_id);
				/* Call resize method, which resizes the image */
				my_picture.resize();
				// end creating chart
			} 
			else {
				List result = (List) this.getReportAttendanceDetail(user, type,
						branchId, userId, startDate, endDate, callerId).get("resultList");
				this.createDataDetail(workbook, sheet, result, startDate, endDate);
			}
		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private DefaultPieDataset createDataSummary(XSSFWorkbook workbook,
			XSSFSheet sheet, List result, String startDate, String endDate) {
		DefaultPieDataset chart = new DefaultPieDataset();
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell = 0;
		XSSFRow rowHead = sheet.createRow(rowcell++);

		XSSFRow rowHeader = sheet.createRow(rowcell++);
		for (int i = 0; i < HEADER_SUMMARY.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT SUMMARY PERIOD " + startDate.substring(0, 10) + " - "
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(HEADER_SUMMARY[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, SUMMARY_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0,
				HEADER_SUMMARY.length - 1));
		// Data row
		int[] tempGrand = {0, 0, 0, 0, 0, 0};

		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map) result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			// Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i + 1);
			cellNo.setCellStyle(styles.get("cell"));
			// data cell
			for (int j = 1; j < HEADER_SUMMARY.length; j++) {
				XSSFCell cell = rowData.createCell(j);
				if (j == 1) { // Set data to string
					cell.setCellValue(temp.get("d" + (j - 1)).toString()
							+ " - " + temp.get("d" + (j)).toString());
					cell.setCellStyle(styles.get("cell"));
				} 
				else if (j == HEADER_SUMMARY.length - 1) {
					cell.setCellValue(Integer.parseInt(temp.get("d" + (j)).toString()));
					cell.setCellStyle(styles.get("cell"));
				} 
				else {
					cell.setCellValue(Integer.parseInt(temp.get("d" + (j)).toString()));
					cell.setCellStyle(styles.get("cell"));
					tempGrand[j - 2] = tempGrand[j - 2] + Integer.parseInt(temp.get("d" + (j)).toString());
				}
			}
		}

		float grandAll = tempGrand[0]+tempGrand[1]+tempGrand[2]+tempGrand[3]+tempGrand[4];

		// Total Data
		XSSFRow rowData = sheet.createRow(rowcell++);
		XSSFCell cellNo = rowData.createCell(0);
		cellNo.setCellValue("Total");
		cellNo.setCellStyle(styles.get("header"));
		XSSFCell cell = rowData.createCell(1);
		String ref = (char) ('B') + "3:" + (char) ('B') + Integer.toString(result.size() + 2);
		cell.setCellFormula("COUNTA(" + ref + ")");
		cell.setCellStyle(styles.get("header"));

		NumberFormat formatter = new DecimalFormat("###");
		for (int i = 0; i < HEADER_SUMMARY.length-2;i++) {    // -2  karena cell A dan B sudah diisi
        	cell = rowData.createCell(i+2);
        	ref = (char)('C'+i) + "3:" + (char)('C'+i) + Integer.toString(result.size()+2);
            cell.setCellFormula("SUM(" + ref + ")");
            cell.setCellStyle(styles.get("header"));
            
            if (i < HEADER_SUMMARY.length - 3) {
            	chart.setValue(HEADER_SUMMARY[i+2] + " = " + tempGrand[i] + " (" 
            			+ formatter.format((Double.valueOf(tempGrand[i])/grandAll)*100) + "%)", tempGrand[i]);
            }
        }
        
        ///End Total Data   
		XSSFFormulaEvaluator.evaluateAllFormulaCells(workbook);
        return chart;
	}

	private void createDataDetail(XSSFWorkbook workbook, XSSFSheet sheet,
			List result, String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell = 0;
		XSSFRow rowHead = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_DETAIL.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT DETAIL PERIOD " + startDate.substring(0, 10) + " - "
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, DETAIL_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_DETAIL.length - 1));

		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map) result.get(i);
			XSSFRow rowHeader1 = sheet.createRow(rowcell++);

			XSSFCell cellUser1 = rowHeader1.createCell(0);
			cellUser1.setCellValue("User Name");
			cellUser1.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(0, DETAIL_COLUMN_WIDTH[0]);

			XSSFCell cellUser2 = rowHeader1.createCell(1);
			cellUser2.setCellValue(temp.get("user").toString());
			cellUser2.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(1, DETAIL_COLUMN_WIDTH[1]);
			sheet.setColumnWidth(2, DETAIL_COLUMN_WIDTH[2]);

			XSSFRow rowHeader2 = sheet.createRow(rowcell++);
			for (int j = 0; j < HEADER_DETAIL.length; j++) {
				XSSFCell cell = rowHeader2.createCell(j);
				cell.setCellValue(HEADER_DETAIL[j]);
				cell.setCellStyle(styles.get("header"));
				sheet.setColumnWidth(j, DETAIL_COLUMN_WIDTH[j]);
			}
			// Data row
			List list = (List) temp.get("list");
			for (int j = 0; j < list.size(); j++) {
				Map tempList = (Map) list.get(j);
				XSSFRow rowData = sheet.createRow(rowcell++);
				// data cell
				for (int k = 0; k < HEADER_DETAIL.length; k++) {
					XSSFCell cell = rowData.createCell(k);
					if (k == HEADER_DETAIL.length - 1) {
						cell.setCellValue("( " + tempList.get("d" + (k - 1)).toString() + ", "
								+ tempList.get("d" + (k)).toString() + " )");
						cell.setCellStyle(styles.get("cell"));
					} 
					else if (k == HEADER_DETAIL.length - 2) {
						cell.setCellValue(tempList.get("d" + (k + 2)).toString());
						cell.setCellStyle(styles.get("cell"));
					} 
					else {
						cell.setCellValue(tempList.get("d" + k).toString());
						cell.setCellStyle(styles.get("cell"));
					}
				}
			}
		}
	}

	private static Map<String, CellStyle> createStyles(Workbook wb) {
		Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
		CellStyle style;

		style = wb.createCellStyle();
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		styles.put("cell", style);

		style = wb.createCellStyle();
		Font font = wb.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setFillPattern(CellStyle.SOLID_FOREGROUND);
		styles.put("header", style);
		return styles;
	}

	@Override
	public Integer countUser(String uuidUser, AuditContext callerId) {
		Integer result;
		Object[][] params = { { "uuidSpv", uuidUser } };
		result = (Integer) this.getManagerDAO().selectOneNative("report.absensi.countUsersAll", params);
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog,
			AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(trReportResultLog.getAmMsuser(), 
				reportBean.getUuidBranch(), reportBean.getUuidUser(),
				reportBean.getStartDate(), reportBean.getEndDate(), reportBean.getType(), callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("ReportAbsensi_");
			
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
				reportBean.setUuidBranch(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
						Long.valueOf(reportBean.getUuidBranch()));
				sb.append(msBranch.getBranchCode());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if ("0".equals(reportBean.getUuidUser()) || "%".equals(reportBean.getUuidUser())) {
				reportBean.setUuidUser(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
				AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
						Long.valueOf(reportBean.getUuidUser()));
				sb.append(user.getLoginId());
			} 
			else {
				sb.append("ALL");
			}
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(trReportResultLog.getProcessStartTime()
				.getTime() - trReportResultLog.getProcessFinishTime().getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}

	@Override
	public List getUserByBranch(String uuidBranch, int pageNo, int pageSize,
			AmMsuser user, String job, AuditContext callerId) {
		List result = null;
		String query = null;
		Object[][] params = null;
		String jobCode = user.getMsJob().getJobCode();
		if (null == job || StringUtils.EMPTY.equals(job)) {
			job = "%";
		}
		String spvMs = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_JOBSPV, callerId);
		String spvMc = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MC_JOBSPV, callerId);
		if (jobCode.equalsIgnoreCase(spvMs) || jobCode.equalsIgnoreCase(spvMc) ) {
			query = "report.absensi.getAllUsersBySpv";
			Object[][] temp = {{"uuidUser",user.getUuidMsUser()}, {"start", ( pageNo-1 ) * pageSize + 1 }, 
					{ "end", ( pageNo-1 ) * pageSize + pageSize }};
			params = temp;
		}
		else {
			query = "report.absensi.getAllUsersByBranch";
			Object[][] temp = { { "uuidBranch", uuidBranch }, 
					{"start", ( pageNo-1 ) * pageSize + 1 }, {"jobcode", job},
					{ "end", ( pageNo-1 ) * pageSize + pageSize },
					{"uuidMsSubsystem",user.getAmMssubsystem().getUuidMsSubsystem()},
					{"uuidUser",user.getUuidMsUser()}};
			params = temp;
		}
		result = this.getManagerDAO().selectAllNative(query, params, null);
		return result;
	}

	@Override
	public Integer countUserByBranch(String uuidBranch, AmMsuser user, String job, AuditContext callerId) {
		Integer result;
		Object[][] params = null;
		String query = null;
		String jobCode = user.getMsJob().getJobCode();
		if (null == job || StringUtils.EMPTY.equals(job)) {
			job = "%";
		}
		String spvMs = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MS_JOBSPV, callerId);
		String spvMc = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MC_JOBSPV, callerId);
		if (jobCode.equalsIgnoreCase(spvMs) || jobCode.equalsIgnoreCase(spvMc) ) {
			query = "report.absensi.getAllUsersBySpvCount";
			Object[][] temp = {{"uuidUser",user.getUuidMsUser()}};
			params = temp;
		}
		else {
			query = "report.absensi.getAllUsersByBranchCount";
			Object[][] temp = { { "uuidBranch", uuidBranch },
					{"uuidMsSubsystem",user.getAmMssubsystem().getUuidMsSubsystem()},
					{"uuidUser",user.getUuidMsUser()}, {"jobcode", job}};
			params = temp;
		}
		result = (Integer)this.getManagerDAO().selectOneNative(query, params);
		return result;
	}
	
	@Override
	public List getUserSurveyorByBranch(AmMsuser user, String uuidBranch, int pageNo, int pageSize,
			AuditContext callerId) {
		List result = null;
		String subsystem = String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem());
		String job = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, callerId);
			
		if ("%".equals(uuidBranch)) {
			Object[][] params = { { "uuidBranch", user.getMsBranch().getUuidBranch() }, 
					{ "subsystem", subsystem },
					{ "jobCode", job },
					{ "start", (pageNo - 1) * pageSize + 1 },
					{ "end", (pageNo - 1) * pageSize + pageSize } };
				
			result = this.getManagerDAO().selectAllNative(
					"report.absensi.getUsersSurveyorAllByBranch", params, null);
		}
		else {
			Object[][] params = { { "uuidBranch", uuidBranch }, { "subsystem", subsystem },
					{ "jobCode", job },
					{ "start", (pageNo - 1) * pageSize + 1 },
					{ "end", (pageNo - 1) * pageSize + pageSize } };
				
			result = this.getManagerDAO().selectAllNative(
					"report.absensi.getUsersSurveyorAllByBranchSpv", params, null);
		}
		return result;
	}

	@Override
	public Integer countUserSurveyorByBranch(AmMsuser user, String uuidBranch, AuditContext callerId) {
		Integer result;
		String job = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, callerId);
		String subsystem = String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem());
			
		if ("%".equals(uuidBranch)) {
			Object[][] params = { { "uuidBranch", user.getMsBranch().getUuidBranch() }, 
					{ "subsystem", subsystem }, { "jobCode", job } };
			result = (Integer) this.getManagerDAO().selectOneNative(
					"report.absensi.getUsersSurveyorAllByBranchCount", params);
		}
		else {
			Object[][] params = { { "uuidBranch", uuidBranch }, { "subsystem", subsystem }, { "jobCode", job }};
			result = (Integer) this.getManagerDAO().selectOneNative(
					"report.absensi.getUsersSurveyorAllByBranchCountSpv", params);
		}
		return result;
	}
}
