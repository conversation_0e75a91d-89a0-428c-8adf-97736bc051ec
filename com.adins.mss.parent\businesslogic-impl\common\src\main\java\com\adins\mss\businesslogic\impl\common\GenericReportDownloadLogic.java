package com.adins.mss.businesslogic.impl.common;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.Stack;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.ReportDownloadLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.opencsv.CSVWriter;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportDownloadLogic extends BaseLogic implements ReportDownloadLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericReportDownloadLogic.class);
	
	private static final String[] TEMPLATE_HEADER_MS = { "APPL NO", "Form ID", "Status Task",
		"Prioritas", "Supervisor", "Mobile User ID", "Mobile User Name", "Branch Name", "Assign Date", "Download Date", "Read Date", "Start Date", "Send Date", "Submitted Date", "Promised Date", "Approval Date", "Wise Status", "Start Date Current Status", "Start Date Data Preparation", "Submit Date Data Preparation", "Nama Customer",
		"No Telp", "Alamat", "Notes", "Latitude", "Longitude" };
	private static final int[] HEADER_COLUMN_WIDTH_MS = { 30 * 256, 20 * 256, 20 * 256,
		20 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 20 * 256, 20 * 256,
		30 * 256, 20 * 256, 30 * 256, 30 * 256 };
	
	private static final String[] TEMPLATE_HEADER_MC = { "Agreement No", "Form ID", "Status Task",
		"Prioritas", "Supervisor", "ARO ID", "Send Date", "Submitted Date", "Nama Customer",
		"No Telp", "Alamat", "Notes", "SurveyID", "Latitude", "Longitude" };
	private static final int[] HEADER_COLUMN_WIDTH_MC = { 30 * 256, 20 * 256, 20 * 256,
		20 * 256, 30 * 256, 30 * 256, 20 * 256, 30 * 256, 20 * 256, 20 * 256,
		30 * 256, 20 * 256, 30 * 256, 30 * 256, 30 * 256 };
	
	private static final String[] TEMPLATE_HEADER_MT = { "Form ID", "Status Task",
		"Prioritas", "Supervisor", "ID Supir", "Cabang", "Send Date", "Submitted Date", "Nama Lokasi",
		"Alamat", "No Telp", "Notes", "Latitude", "Longitude" };
	
	private String apiViewImagePath = "/services/p/task/view/image/";
	private CommonLogic commonLogic;
	private String paramSubsystemName = "subsystemName";		
	private String paramIsActive = "isActive";
	private String paramFileName = "fileName";
	
	public void setApiViewImagePath(String apiViewImagePath) {
		this.apiViewImagePath = apiViewImagePath;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	@Override
	public Map<String, Object> getCombo(AmMsuser amMsuser, AuditContext callerId) {
		Map<String, Object> resultMap = new HashMap();
			
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
			
			//Combo List Branch
		String[][] paramBranch = { {"uuidBranch", String.valueOf(amMsuser.getMsBranch().getUuidBranch())} };
		List listBranchCombo = this.getManagerDAO().selectAllNative(
						"report.download.getlistbranch", paramBranch, null);
			
		Map<String, String> branchCombo = new HashMap<String, String>();
		if (!listBranchCombo.isEmpty()) {
			for (int i = 0; i < listBranchCombo.size(); i++) {
				Map temp = (Map) listBranchCombo.get(i);
				branchCombo.put(String.valueOf(temp.get("d0")),
						(String) temp.get("d2")+" - "+(String) temp.get("d1"));
			}
		}
			
		Map<String, String> userCombo = new HashMap<String, String>();
			
			//Combo List Form
		String[][] paramsForm = { { paramIsActive, "1" }, {paramSubsystemName, subsysName}};
		List<Map<String, Object>> listFormCombo = this.getManagerDAO().selectAllNativeString(
				"SELECT form.UUID_FORM, form.FORM_NAME "
				+ "FROM MS_FORM form WITH (NOLOCK) "
					+ "JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
					+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
				+ "WHERE form.IS_ACTIVE = :isActive "
					+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName "
				+ "ORDER BY form.FORM_NAME", paramsForm);
		
			
		Map<String, String> formCombo = new HashMap<String, String>();
		if (!listFormCombo.isEmpty()) {
			for (int i = 0; i < listFormCombo.size(); i++) {
				Map<String, Object> mp = listFormCombo.get(i);
				formCombo.put( String.valueOf(mp.get("d0")), String.valueOf(mp.get("d1")) );
			}
		}
		
		Map<String, String> formVersion = new HashMap<String, String>();
		formVersion.put("", "-- Choose One --");
		resultMap.put("branchCombo", branchCombo);
		resultMap.put("userCombo", userCombo);
		resultMap.put("formCombo", listFormCombo);
		resultMap.put("formVersion", formVersion);
		return resultMap;
	}
	
	@Override
	public Map<String, Object> getVersion(String uuidForm, AuditContext callerId) {
		Map<String, Object> resultMap = new HashMap();							
			
		Object[][] paramFormhistory = { {Restrictions.eq("msForm.uuidForm", Long.valueOf(uuidForm))} };
		String[][] orderFormHistory ={{"formVersion", "DESC"}};
		Map<String, Object> MsFormhistory = this.getManagerDAO().list(MsFormhistory.class, 
					paramFormhistory, orderFormHistory);
		List<MsFormhistory> listFormVersion = (List<MsFormhistory>) MsFormhistory.get(
				GlobalKey.MAP_RESULT_LIST);
		List<Map<String, String>> formVersion = new ArrayList<>();
		for (int j = 0; j < listFormVersion.size(); j++) {
			Map<String, String> tempVersion = new HashMap<String, String>();
			tempVersion.put("value", String.valueOf(listFormVersion.get(j).getUuidFormHistory()));
			tempVersion.put("text", String.valueOf(listFormVersion.get(j).getFormVersion()));
			formVersion.add(tempVersion);
		}
		resultMap.put("formVersion", formVersion);
		return resultMap;
	}
	
	@Override
	public Map<String, Object> getComboUser(String uuidBranch, AuditContext callerId) {
		
		List listUserCombo = new ArrayList();
		//Combo List User
		Map<String, Object> userCombo = new HashMap<String, Object>();
		if (!StringUtils.isBlank(uuidBranch)) {
			AmMsuser amMsUser = this.getManagerDAO().selectOne(
				"from AmMsuser u "
					+ "join fetch u.amMssubsystem "
				+ "where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
			String subsystemName = amMsUser.getAmMssubsystem().getSubsystemName();
			
			String[][] paramUser = { {"uuidBranch", uuidBranch}, {paramSubsystemName, subsystemName} };
			listUserCombo = this.getManagerDAO().selectAllNative(
						"report.download.getlistuser", paramUser, null);
			
			String key[] = new String[listUserCombo.size()];
			String value[] = new String[listUserCombo.size()];
			
			if (!listUserCombo.isEmpty()) {
				for (int i = 0; i < listUserCombo.size(); i++) {
					Map temp = (Map) listUserCombo.get(i);
					
					key[i] = (String) temp.get("d0");
					value[i] = (String) temp.get("d1");
				}
				
				userCombo.put("key",key);
				userCombo.put("value",value);
			}
		}
		return userCombo;
	}
	
	@Override
	public byte[] exportTaskReport(AuditContext callerId, String uuidUser, 
			String uuidSpv, String uuidBranch, String uuidForm, String startDate, String endDate) {
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		HSSFWorkbook workbook = this.createXls(callerId, uuidUser, uuidSpv, uuidBranch, 
				uuidForm, startDate, endDate);
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		return stream.toByteArray();
	}
	
	public HSSFWorkbook createXls(AuditContext callerId, String uuidUser, String uuidSpv, 
			String uuidBranch, String uuidForm, String startDate, String endDate) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			Map callerIdMap = callerId.getParameters();
			String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
			
			String sheetName = (GlobalVal.SUBSYSTEM_MC.equals(subsysName)) 
					? "Collection Result List" : "Survey Result List";
			HSSFSheet sheet = workbook.createSheet(sheetName);
			
			this.exportData(subsysName, workbook, sheet, uuidUser, uuidSpv, uuidBranch, 
					uuidForm, startDate, endDate);
			this.setTextDataFormat(subsysName, workbook, sheet);
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private void exportData(String subsysName, HSSFWorkbook workbook, HSSFSheet sheet, String uuidUser, 
			String uuidSpv, String uuidBranch, String uuidForm, String startDate, String endDate) {
		
		String queryMinDate = StringUtils.EMPTY;
		String queryMaxDate = StringUtils.EMPTY;
		if (StringUtils.isBlank(startDate)) {
			//Month
			queryMinDate = changeDateFormat(endDate, "yyyy-MM", DATE_FORMAT_DT_DB, true, true);
			queryMaxDate = changeDateFormat(endDate, "yyyy-MM", DATE_FORMAT_DT_DB, false, true);
		} 
		else if (StringUtils.isBlank(endDate)) {
			//Daily
			queryMinDate = changeDateFormat(startDate, "yyyy-MM-dd", DATE_FORMAT_DT_DB, true, false);
			queryMaxDate = changeDateFormat(startDate, "yyyy-MM-dd", DATE_FORMAT_DT_DB, false, false);
		} 
		else {
			//Range Date
			queryMinDate = changeDateFormat(startDate, "yyyy-MM-dd", DATE_FORMAT_DT_DB, true, false);
			queryMaxDate = changeDateFormat(endDate, "yyyy-MM-dd", DATE_FORMAT_DT_DB, false, false);
		}
		
		Object[][] paramHeader = { {"uuidUser", uuidUser}, {"uuidSpv", uuidSpv}, 
				{"uuidBranch", uuidBranch}, {"uuidForm", uuidForm},
				{"startDate", queryMinDate}, {"endDate", queryMaxDate} };
		
		List listHeader = new ArrayList();
		if (GlobalVal.SUBSYSTEM_MS.equals(subsysName)) {
			listHeader = this.getManagerDAO().selectAllNative(
					"report.download.downloadtaskms", paramHeader, null);
		}
		else {
			listHeader = this.getManagerDAO().selectAllNative(
					"report.download.downloadtaskmc", paramHeader, null);
		}
		if (!listHeader.isEmpty()) {
			this.getDataHeader(workbook, sheet, listHeader, subsysName);
			this.getDataDetail(workbook, sheet, listHeader, uuidForm, subsysName);
		} 
		else {
			this.createErrorMsg(workbook, sheet);
		}
	}
	
	private void createErrorMsg(HSSFWorkbook workbook, HSSFSheet sheet) {
		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1000);
		styleHeader.setFont(fontHeader);
		
		HSSFRow row = sheet.createRow(1);
		HSSFCell cell = row.createCell(0);
		cell.setCellValue("No Data Found");
		cell.setCellStyle(styleHeader);
		sheet.setColumnWidth(0, 30 * 256);
	}
	
	private void getDataHeader(HSSFWorkbook workbook, HSSFSheet sheet, List listHeader, String subsysName) {
		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1000);
		styleHeader.setFont(fontHeader);
		
		HSSFRow row = sheet.createRow(1);
		int r = 2;
		if (GlobalVal.SUBSYSTEM_MS.equals(subsysName)){
			for (int i = 0; i < TEMPLATE_HEADER_MS.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MS[i]);
				cell.setCellStyle(styleHeader);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MS[i]);
			}
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER_MC.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MC[i]);
				cell.setCellStyle(styleHeader);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MC[i]);
			}
		}
		
		for (int j = 0; j < listHeader.size(); j++) {
			HSSFRow rowLoop = sheet.createRow(r);
			Map map = (HashMap) listHeader.get(j);
			
			for (int k = 0; k < map.size()-1; k++) {
				HSSFCell cell = rowLoop.createCell(k);
				String getMap = "d"+k;
				String data = (map.get(getMap) == null) ? null : map.get(getMap).toString();
				
				if ("d6".equals(getMap) || "d13".equals(getMap)) {
					data = changeDateFormat(data, "dd MMM yyyy HH:mm", DATE_FORMAT_DTM);
				}
				cell.setCellValue(StringUtils.upperCase(data));
			}
			r++;
		}
	}
	
	private void getDataDetail(HSSFWorkbook workbook, HSSFSheet sheet, List listHeader, 
			String uuidForm, String subsysName) {
		int startLen = (GlobalVal.SUBSYSTEM_MS.equals(subsysName)) 
				? TEMPLATE_HEADER_MS.length : TEMPLATE_HEADER_MC.length;
		
		Object[][] paramQuestion = { {"uuidForm", uuidForm} };
		List listQuestion = this.getManagerDAO().selectAllNative("report.download.getquestionlist", 
				paramQuestion, null);
		
		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1000);
		styleHeader.setFont(fontHeader);
		
		HSSFCellStyle styleDetail = workbook.createCellStyle();
		styleDetail.setAlignment(CellStyle.ALIGN_RIGHT);
		
		for (int i = 0; i < listQuestion.size(); i++) {
			Map mapQuestion = (HashMap) listQuestion.get(i);
			
			boolean isHeader = true;
			for (int j = 0; j < listHeader.size(); j++) {
				Map mapHeader = (HashMap) listHeader.get(j);
				if (isHeader) {
					HSSFRow row = sheet.getRow(1);
					HSSFCell cell = row.createCell(i+startLen);
					cell.setCellValue(StringUtils.upperCase((String) mapQuestion.get("d1")));
					cell.setCellStyle(styleHeader);
					sheet.setColumnWidth(i+startLen, 30 * 256);
					isHeader = false;
				}
				
				String flag = StringUtils.EMPTY;				
				List listDetail = null;
				
				if (GlobalVal.SUBSYSTEM_MS.equals(subsysName)) {
					flag = mapHeader.get("d15").toString();									
				}
				else {
					flag = mapHeader.get("d16").toString();
				}
					
				String getUUIDMap = "d" + startLen;
				Object[][] paramDetail = {{"uuidTaskH", mapHeader.get(getUUIDMap).toString()}, 
						{"uuidQuestion", mapQuestion.get("d0").toString()} };

				if ("2".equals(flag)) { //no need create function from json as it is deprecated
					listDetail = this.getManagerDAO().selectAllNative(
							"report.download.getdetailanswerfinal", paramDetail, null);
				}
				else {
					listDetail = this.getManagerDAO().selectAllNative(
							"report.download.getdetailanswer", paramDetail, null);
				}
				
				String value = StringUtils.EMPTY;
				for (int k = 0; k < listDetail.size(); k++) {
					Map mapDetail = (HashMap) listDetail.get(k);
					
					HSSFRow row = sheet.getRow(j+2);
					HSSFCell cell = row.createCell(i+startLen);
					
					if ("1".equals(mapDetail.get("d3").toString())) {
						value = "(image)";
					} 
					else if ("0".equals(mapDetail.get("d3").toString())) {
						value = null;
					} 
					else if ("01".equals(mapDetail.get("d3").toString())) {
						if (null != mapDetail.get("d5") && 
								null != mapDetail.get("d6") &&
								null != mapDetail.get("d7")) {
							String latitude = mapDetail.get("d5").toString();
							String longitude = mapDetail.get("d6").toString();
							String acc = mapDetail.get("d7").toString();
							value = latitude + ", " + longitude + " accuracy:" + acc + "m";
						}
					} 
					else {
						String answer = StringUtils.EMPTY;
						String code = StringUtils.EMPTY;
						String option = StringUtils.EMPTY;
						
						//check if get INT
						if ("0".equals(mapDetail.get("d8").toString())) {
							answer = mapDetail.get("d0").toString();
							code = mapDetail.get("d1").toString();
							option = mapDetail.get("d2").toString();
						} 
						else {
							answer = mapDetail.get("d9").toString();
							code = mapDetail.get("d10").toString();
							option = mapDetail.get("d11").toString();
						}
						
						if(!StringUtils.isBlank(code)){
							MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(code));
							code = msLov.getCode();
						}
						
						if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(mapDetail.get("d4").toString()) || 
								GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION
									.equals(mapDetail.get("d4").toString()) ||
								GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION
									.equals(mapDetail.get("d4").toString())) {
							if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
								if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(mapDetail.get("d4").toString())) {
									value += code + " | " + option;
								} 
								else if (GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION
										.equals(mapDetail.get("d4").toString())) {
									value += code + " | " + option;
									if (k == listDetail.size()-1) {
										value += " - " + answer;
									}
								} 
								else if (GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION
										.equals(mapDetail.get("d4").toString())) {
									value += code + " | " + option + " - " + answer;
								}
								if (k < listDetail.size()-1) {
									value += ", ";
								}
							}
						} 
						else if (GlobalVal.ANSWER_TYPE_DROPDOWN.equals(mapDetail.get("d4").toString()) || 
								GlobalVal.ANSWER_TYPE_RADIO.equals(mapDetail.get("d4").toString())) {
							if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
								value = code + " | " + option;
							}
							else if (GlobalVal.COL_TAG_RVNUMBER.equals(mapDetail.get("d12"))) {
								 value = (String) mapDetail.get("d13");
							}
						} 
						else if (GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION
								.equals(mapDetail.get("d4").toString()) ||
								GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION
								.equals(mapDetail.get("d4").toString())) {
							if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
								value = code + " | " + option + " - " + answer;
							}
						} 
						else if (GlobalVal.ANSWER_TYPE_TIME.equals(mapDetail.get("d4").toString())) {
							value = changeDateFormat(answer, "HH:mm:ss", DATE_FORMAT_TM);
						} 
						else if (GlobalVal.ANSWER_TYPE_DATETIME.equals(mapDetail.get("d4").toString())) {
							value = changeDateFormat(answer, "dd/MM/yyyy HH:mm:ss", DATE_FORMAT_DTM);
						} 
						else if (GlobalVal.ANSWER_TYPE_DATE.equals(mapDetail.get("d4").toString())) {
							value = changeDateFormat(answer, "dd/MM/yyyy", DATE_FORMAT_DT);
						} 
						else if (GlobalVal.ANSWER_TYPE_TEXT_MULTILINE.equals(mapDetail.get("d4").toString())) {
							String[] temp = StringUtils.split(answer,"\n");
							value = StringUtils.join(temp, " ");
						}
						else {
							value = answer;
						}
					}
					
					cell.setCellValue(StringUtils.upperCase(value));
					if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(mapDetail.get("d4").toString()) ||
							GlobalVal.ANSWER_TYPE_DECIMAL.equals(mapDetail.get("d4").toString()) ||
							GlobalVal.ANSWER_TYPE_NUMERIC.equals(mapDetail.get("d4").toString())) {
						cell.setCellStyle(styleDetail);
					}
				}
				if (j == listHeader.size()-1) {
					isHeader = true;
				}
			}
		}
	
	}
	
	private void setTextDataFormat(String subsysName, HSSFWorkbook workbook, HSSFSheet sheet) {
		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("General"));
		if (GlobalVal.SUBSYSTEM_MS.equals(subsysName)) {
			for (int i = 0; i < TEMPLATE_HEADER_MS.length; i++) {
				sheet.setDefaultColumnStyle(i, style);
			}
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER_MC.length; i++) {
				sheet.setDefaultColumnStyle(i, style);
			}
		}
	}
	
	private String changeDateFormat(String date, String fromDateFormat, String toDateformat) {
		if (StringUtils.isBlank(date)) {
			return null;
		}
		String newdate = StringUtils.EMPTY;
		DateFormat df = new SimpleDateFormat(fromDateFormat);
		
		try {
			Date result = df.parse(date);
			newdate = (null != result) ? new SimpleDateFormat(toDateformat).format(result) : "-";
		} 
		catch (ParseException e) {
			e.printStackTrace();
		} 
		return newdate;
	}
	
	private String changeDateFormat(String date, String fromDateFormat, String toDateformat, 
			boolean isMin, boolean isMonthYear) {
		Date dt = stringToDate(date, fromDateFormat);
		
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(dt);
		if (isMin) {
			if (isMonthYear) {
				calendar.set(Calendar.DATE, calendar.getActualMinimum(Calendar.DATE));
			}
			calendar.set(Calendar.HOUR,0);
			calendar.set(Calendar.MINUTE,0);
			calendar.set(Calendar.SECOND,0);
			calendar.set(Calendar.MILLISECOND,0);
		} 
		else {
			if (isMonthYear) {
				calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
			}
			calendar.set(Calendar.HOUR,23);
			calendar.set(Calendar.MINUTE,59);
			calendar.set(Calendar.SECOND,59);
			calendar.set(Calendar.MILLISECOND,997);
		}
	    Date dateResult = calendar.getTime();
	    DateFormat dateFormat = new SimpleDateFormat(toDateformat);
	    return dateFormat.format(dateResult);
	}
	
	private Date stringToDate(String date, String dateformat){
		DateFormat format = new SimpleDateFormat(dateformat);
		Date d = null;
		try {
			d = format.parse(date);
		} 
		catch (Exception e){
			e.printStackTrace();
		}
		return d;
	}

	@Override
	public List getUserByBranch(String uuidBranch, int pageNo, int pageSize, AmMsuser loginBean,
			AuditContext callerId) {
		List result = null;
		String gsSpv = StringUtils.EMPTY;
		if (GlobalVal.SUBSYSTEM_MC.equals(loginBean.getAmMssubsystem().getSubsystemName())) {
			gsSpv = GlobalKey.GENERALSETTING_MC_JOBSPV;
		} 
		else if (GlobalVal.SUBSYSTEM_MT.equals(loginBean.getAmMssubsystem().getSubsystemName())) {
			gsSpv = GlobalKey.GENERALSETTING_MT_JOBSPV;
		} 
		else {
			gsSpv = GlobalKey.GENERALSETTING_MS_JOBSPV;
		}
		Object[][] paramsGs = {{Restrictions.eq("gsCode", gsSpv)}};
		AmGeneralsetting generalsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsGs);
		Object[][] params = { 
				{ "uuidBranchLogin", loginBean.getMsBranch().getUuidBranch()},
				{ "uuidBranch", uuidBranch },
				{ "jobCode" , generalsetting.getGsValue()},
				{ "start", ( pageNo-1 ) * pageSize + 1 }, 
				{ "end", ( pageNo-1 ) * pageSize + pageSize }};
		result = this.getManagerDAO().selectAllNative("report.download.getUsersAllByBranch", params, null);
		return result;
	}

	@Override
	public Integer countUserByBranch(String uuidBranch, AmMsuser loginBean, AuditContext callerId) {
		Integer result;
		String gsSpv = StringUtils.EMPTY;
		if (GlobalVal.SUBSYSTEM_MC.equals(loginBean.getAmMssubsystem().getSubsystemName())) {
			gsSpv = GlobalKey.GENERALSETTING_MC_JOBSPV;
		} 
		else if (GlobalVal.SUBSYSTEM_MT.equals(loginBean.getAmMssubsystem().getSubsystemName())) {
			gsSpv = GlobalKey.GENERALSETTING_MT_JOBSPV;
		} 
		else {
			gsSpv = GlobalKey.GENERALSETTING_MS_JOBSPV;
		}
		Object[][] paramsGs = {{Restrictions.eq("gsCode", gsSpv)}};
		AmGeneralsetting generalsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsGs);
		Object[][] params = { { "uuidBranchLogin", loginBean.getMsBranch().getUuidBranch()}, 
				{ "uuidBranch", uuidBranch }, { "jobCode" , generalsetting.getGsValue()}};
		result = (Integer)this.getManagerDAO().selectOneNative(
				"report.download.getUsersAllByBranchCount", params);	
		return result;
	}
	
	@Override
	public List getUserBySpv(String uuidSpv, String uuidBranch, int pageNo, int pageSize, AmMsuser loginBean,
			AuditContext callerId) {
		List result = null;
		if ("%".equals(uuidBranch)) {
			Object[][] params = { { "uuidSpv", uuidSpv }, 
					{ "uuidBranch", loginBean.getMsBranch().getUuidBranch() }, 
					{ "subsystemCode", loginBean.getAmMssubsystem().getSubsystemName() }, 
					{"start", ( pageNo-1 ) * pageSize + 1 }, 
					{ "end", ( pageNo-1 ) * pageSize + pageSize }};
			result = this.getManagerDAO().selectAllNative(
					"report.download.getUsersAllByBranchHierarchy", params, null);
		}
		else {
			Object[][] params = { { "uuidSpv", uuidSpv }, { "uuidBranch", uuidBranch }, 
					{ "subsystemCode", loginBean.getAmMssubsystem().getSubsystemName() }, 
					{"start", ( pageNo-1 ) * pageSize + 1 }, 
					{ "end", ( pageNo-1 ) * pageSize + pageSize }};
			result = this.getManagerDAO().selectAllNative("report.download.getUsersAllBySpv", params, null);
		}
		return result;
	}

	@Override
	public Integer countUserBySpv(String uuidSpv, String uuidBranch, AmMsuser loginBean, AuditContext callerId) {
		Integer result;
		if ("%".equals(uuidBranch)) {
			Object[][] params = { { "uuidSpv", uuidSpv }, 
					{ "uuidBranch", loginBean.getMsBranch().getUuidBranch() }, 
					{ "subsystemCode", loginBean.getAmMssubsystem().getSubsystemName() } };
			result = (Integer)this.getManagerDAO().selectOneNative(
					"report.download.getUsersAllByBranchHierarchyCount", params);
		}
		else {
			Object[][] params = { { "uuidSpv", uuidSpv }, { "uuidBranch", uuidBranch }, 
					{ "subsystemCode", loginBean.getAmMssubsystem().getSubsystemName() } };
			result = (Integer)this.getManagerDAO().selectOneNative(
					"report.download.getUsersAllBySpvCount", params);	
		} 
		return result;
	}

	@Override
	public String getExcelPath(AuditContext callerId) {
		Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
		AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
		
		if (amGeneralSetting == null) {
			throw new EntityNotFoundException("Excel result location has not been defined.",
					GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
		}
		
		return amGeneralSetting.getGsValue();
	}

	@Override
	public List getListForm(AuditContext callerId) {
		Map<String, Object> mapResult = this.getManagerDAO().list(
			"from MsForm mf "
				+ "join fetch mf.amMssubsystem "
			+ "where mf.isActive = :isActive", new Object[][] {{paramIsActive, "1"}});
		List<MsForm> result = (List) mapResult.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}
	
	@Override
	public Map<String, Object> getListFormTask(Date minDate, Date maxDate, AuditContext callerId) {
		List resultList = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		
		StringBuilder paramsQueryString = new StringBuilder();
		paramsQueryString.append(" AND tth.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd");
		paramsStack.push(new Object[]{ "assignDateStart", minDate });
		paramsStack.push(new Object[]{ "assignDateEnd", maxDate });
		
		StringBuilder queryBuilder = new StringBuilder()
		.append("select tth.uuid_form, mf.form_name, tth.form_version, count(uuid_task_h) jmlh from TR_TASK_H tth")
		.append(" join ms_form mf on mf.uuid_form = tth.uuid_form")
		.append(" where 1=1 and uuid_ms_subsystem = 5 ").append(paramsQueryString);
		queryBuilder.append(" group by tth.uuid_form, mf.form_name,tth.form_version");
		queryBuilder.append(" order by tth.uuid_form, tth.form_version");
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    resultList = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
	    Map<String, Object> result = new HashMap<String, Object>();
	    if(!resultList.isEmpty()){
	   	    AmMssubsystem subsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, 5L);
	    	for (int i = 0; i < resultList.size(); i++) {
				Map<String, Object> map = (Map) resultList.get(i);
				long uuidForm = (long) Long.valueOf(map.get("d0").toString());
				String formName = (String) map.get("d1");
				int version = (int) map.get("d2");
				MsForm bean = new MsForm();
				bean.setUuidForm(uuidForm);
				bean.setFormName(formName);
				bean.setAmMssubsystem(subsystem);
				result.put("bean"+i, bean);
				result.put("version"+i, version);	
			
			}
	    	result.put("size", resultList.size());	
	    } 
	    else{
	    	result.put("size", 0);	
	    } 
	    
		return result;
	}
	
	
	/**
	 * <p>Load from local file for <b>monthly</b> and <b>daily report</b> for backdated date if <u>no filter branch,spv,user</u> 
	 * 
	 * @param callerId
	 * @param uuidUser
	 * @param uuidSpv
	 * @param uuidBranch
	 * @param uuidForm
	 * @param startDate
	 * @param endDate
	 * @param isMonth		if true, will read local file instead of fetch from database for monthly report 
	 * @return
	 */
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public Map<String, Object> exportTaskReport(AmMsuser user, AuditContext callerId, 
			String uuidUser, String uuidSpv, String uuidBranch, String uuidForm, String uuidVersion, 
			Date startDate, Date endDate, String subsystem, String filterBy) {
		Map<String, Object> map = null;
		byte[] result = null;
		String filename = StringUtils.EMPTY;
		try {
			Map<String, Object> callerIdMap = callerId.getParameters();
			String subsystemCode = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
			
			if (FilterType.FILTER_BY_MONTHLY.toString().equals(filterBy)) {
				Object[][] paramForm = { {Restrictions.eq("uuidForm", Long.valueOf(uuidForm))} };
				MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, paramForm);
				Object[][] paramFormHistory = {{Restrictions.eq("uuidFormHistory", Long.valueOf(uuidVersion))}};
				MsFormhistory msFormHistory = this.getManagerDAO().selectOne(MsFormhistory.class, paramFormHistory);
				String path = getExcelPath(callerId);
				filename = DateFormatUtils.format(startDate, "yyyy_MM_") + msForm.getFormName() + "_v"+ msFormHistory.getFormVersion()+ ".csv";
				String pathFile = path + filename;
				
				File file = new File(pathFile);
				if (file.exists()) {
					Path pathsolute = Paths.get(pathFile);
					result = Files.readAllBytes(pathsolute);
				} 
				else {
					this.exportTaskReportCSV(user, callerId, uuidUser, uuidSpv,  uuidBranch, uuidForm, 
							uuidVersion, startDate, endDate, filterBy);
					return null;
				}
			}
			else if (this.isReadFromDailyFile(uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate)) {
				String path = getExcelPath(callerId);
				String dirName = DateFormatUtils.format(startDate, "yyyy");
				String filePrefixName = DateFormatUtils.format(startDate, "yyyy_MM_dd_");
				filename = filePrefixName + uuidForm + ".csv";
				String absoluteFile = path + dirName + SystemUtils.FILE_SEPARATOR + filename;
				
				File file = new File(absoluteFile);
				if (file.exists()) {
					Path pathsolute = Paths.get(absoluteFile);
					result = Files.readAllBytes(pathsolute);
				} 
				else {
					this.exportTaskReportCSV(user, callerId, uuidUser, uuidSpv, uuidBranch, uuidForm, 
							uuidVersion, startDate, endDate, filterBy);
					return null;
				}
			}
			else {
				ReportDownloadBean reportBean = new ReportDownloadBean();
				reportBean.setSubsystemCode(subsystemCode);
				reportBean.setUuidLoginId(String.valueOf(user.getUuidMsUser()));
				reportBean.setUuidForm(uuidForm);
				reportBean.setUuidVersion(uuidVersion);
				reportBean.setUuidBranch(uuidBranch);
				reportBean.setUuidSpv(uuidSpv);
				reportBean.setUuidUser(uuidUser);
				reportBean.setStartDate(DateFormatUtils.format(startDate, "yyyy-MM-dd HH:mm:ss.S"));
				reportBean.setEndDate(DateFormatUtils.format(endDate, "yyyy-MM-dd HH:mm:ss.S"));
				
				Gson gson = new GsonBuilder().serializeNulls().create();
				String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);
				
				TrReportresultlog trReportResultLog = new TrReportresultlog();
				trReportResultLog.setAmMsuser(user);
				trReportResultLog.setDtmRequest(new Date());
				if (GlobalVal.SUBSYSTEM_MC.equals(subsystemCode)) {
					trReportResultLog.setRptName("Collection Result");
				} 
				else {
					trReportResultLog.setRptName("Survey Result");
				}
				trReportResultLog.setRptType(filterBy);
				trReportResultLog.setRptParams(jsonParams);
				trReportResultLog.setProcessStatus("0");
				this.getManagerDAO().insert(trReportResultLog);
				
				return null;
			}
			map = new HashMap<String, Object>();
			map.put(paramFileName, filename);
			map.put("result", result);
			return map;
			
		} 
		catch (IOException e) {
			LOG.error("Error while create excel file. params[user={}, spv={}, "
					+ "branch={}, form={}, start={}, end={}]",
	    		uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate, e);
		}
		
		return map;
	}
	
	@Override
	public void generateCollectionReportSchedulerDaily(AuditContext callerId, MsForm msForm, 
			Date startDate, Date endDate) {
		int formVersion = 0; //FIXME no form with version=0
		List<Map<String, Object>> taskList = this.retrieveTaskListLimit(
				msForm.getAmMssubsystem().getSubsystemName(), null, null, null, 
				String.valueOf(msForm.getUuidForm()), startDate, endDate,"0","20000", null, null, formVersion);
		Map<String, QuestionSetBean> questionSet = this.retrieveQuestionSet(
				String.valueOf(msForm.getUuidForm()), msForm.getAmMssubsystem().getSubsystemName());
		this.writeDailyCSVFile(taskList, questionSet, startDate, msForm, callerId);
	}
	
	@Override
	public byte[] generateCollectionReportScheduler(AuditContext callerId, MsForm msForm, 
			String path, Date startDate, Date endDate) {
		String subsysCode = msForm.getAmMssubsystem().getSubsystemName();
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		HSSFWorkbook workbook = null;
		FileInputStream file = null;
		try {
			boolean isNew = false;
			
			HSSFSheet sheet = null;
			
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startDate);
			if (calendar.getActualMinimum(Calendar.DAY_OF_MONTH) == calendar.get(Calendar.DAY_OF_MONTH)) {
				isNew = true;
			}
			
			try {
				file = new FileInputStream(new File(path));
			} 
			catch(FileNotFoundException ef) {
				LOG.warn("File path : {}, not existed !", path);
				isNew = true;
			}
			
			if (isNew) {
				workbook = new HSSFWorkbook();
				String sheetName = (GlobalVal.SUBSYSTEM_MC.equals(subsysCode)) 
						? "Collection Result List" : "Survey Result List";
				sheet = workbook.createSheet(sheetName);
			} 
			else {
				workbook = new HSSFWorkbook(file);
				sheet = workbook.getSheetAt(0);
			}
			
			int row = (isNew) ? 2 : sheet.getLastRowNum()+1; //starting row = 2
			
			List<Map<String, Object>> taskList = this.retrieveTaskList(subsysCode, null, null, null, 
					String.valueOf(msForm.getUuidForm()), startDate, endDate);
			if (taskList.isEmpty()) {
				this.writeHeader(workbook, sheet, subsysCode);							
			}
			else {
				if (isNew) {
					this.writeHeader(workbook, sheet, subsysCode);
				}
				Map<String, QuestionSetBean> questionSet = this.retrieveQuestionSet(
						String.valueOf(msForm.getUuidForm()), subsysCode);
				
				//2016-02-21
				this.writeDailyExcelFile(taskList, questionSet, startDate, msForm);
				
				int lastCol = this.writeQuestionSetHeader(workbook, sheet, questionSet);
				this.writeRecords(workbook, sheet, taskList, questionSet, row);
				
				final int statusTaskColIndex = 2; 
				final int startRecordRowIndex = 2;

				List<LinkedHashMap<Integer, String>> sortedRow = this.sortSheet(sheet, statusTaskColIndex, 
						startRecordRowIndex);
				this.writeToSheet(sheet, sortedRow, startRecordRowIndex, lastCol);
			}						
			
			workbook.write(stream);
			return stream.toByteArray();
			
		} 
		catch (IOException e) {
			LOG.error("SCHEDULER error while create excel file. params[form={}, start={}, end={}]",
					msForm.getUuidForm(), startDate, endDate, e);
		} 
		finally {
			try {
				if (file != null) {
					file.close();
				}
				if (workbook != null) {
					workbook.close();
				}
			} 
			catch (IOException e) {
				LOG.error("SCHEDULER error while close workbook. params[form={}]", msForm.getUuidForm());
			}
		}
		
		return ArrayUtils.EMPTY_BYTE_ARRAY;
	}
	
	private List<Map<String, Object>> retrieveTaskList(String subsystemCode, String uuidUser,
			String uuidSpv, String uuidBranch, String uuidForm, Date startDate, Date endDate) {
		if (GlobalVal.SUBSYSTEM_MS.equals(subsystemCode)) {
			return this.retrieveTaskListMs(uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
		}
		else if (GlobalVal.SUBSYSTEM_MC.equals(subsystemCode)) {
			return this.retrieveTaskListMc(uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
		}
		else {
			//ILLEGAL subsystem			
			return Collections.emptyList(); 
		}
	}
	
	private List<Map<String, Object>> retrieveTaskListMs(String uuidUser,
			String uuidSpv, String uuidBranch, String uuidForm, Date startDate, Date endDate) {
		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[]{ "uuidForm", uuidForm});
		paramStack.push(new Object[]{ "startDate", startDate});
		paramStack.push(new Object[]{ "endDate", endDate});
		
		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder
			.append("SELECT trth.APPL_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ")
			.append("ammsuspv.FULL_NAME, ammsu.LOGIN_ID, send_date, submit_date, ") 
			.append("trth.CUSTOMER_NAME, trth.CUSTOMER_PHONE, trth.CUSTOMER_ADDRESS, trth.NOTES, ") 
			.append("trth.LATITUDE, trth.LONGITUDE, trth.UUID_TASK_H, msst.STATUS_CODE ")
			.append("FROM TR_TASK_H trth with (nolock) ")
			.append("left join TR_TASKORDERDATA trtod with (nolock) on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
			.append("left join MS_BRANCH msb with (nolock) on trth.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM ")
			.append("left join MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY ")
			.append("left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
			.append("left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER ")
			.append("WHERE trth.UUID_FORM = :uuidForm ");
		
		if (StringUtils.isNotEmpty(uuidBranch)) {
			sqlBuilder.append("AND trth.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{ "uuidBranch", uuidBranch});
		}
		
		sqlBuilder.append("AND assign_date between :startDate and :endDate "); 
		if (StringUtils.isNotEmpty(uuidUser)) {
			sqlBuilder.append("AND trth.UUID_MS_USER = :uuidUser ");
			paramStack.push(new Object[]{ "uuidUser", uuidUser});
		}
		sqlBuilder.append("AND msst.STATUS_CODE not in ('A') ");
	    if (StringUtils.isNotEmpty(uuidSpv)) {
	    	sqlBuilder.append("AND ammsu.SPV_ID = :uuidSpv ");
			paramStack.push(new Object[]{ "uuidSpv", uuidSpv});
	    }	
	    sqlBuilder.append("ORDER BY trth.DTM_CRT");
		
	    LOG.trace("retrieveTaskListMs SQL: {}, params[user={}, spv={}, branch={}, form={}, start={}, end={}]",
	    		sqlBuilder.toString(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
	    
	    Object[][] sqlParams = new Object[paramStack.size()][2];
	    for (int i = 0; i < paramStack.size(); i++) {
			Object[] objects = paramStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNativeString(
	    		sqlBuilder.toString(), sqlParams);
	    LOG.info("retrieveTaskListMs.size: {}, params[user={}, spv={}, branch={}, form={}, start={}, end={}]",
	    		resultList.size(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
		return resultList;
	}
	
	private List<Map<String, Object>> retrieveTaskListMc(String uuidUser,
			String uuidSpv, String uuidBranch, String uuidForm, Date startDate, Date endDate) {
		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[]{ "uuidForm", uuidForm});
		paramStack.push(new Object[]{ "startDate", startDate});
		paramStack.push(new Object[]{ "endDate", endDate});
		
		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder
			.append("SELECT trth.AGREEMENT_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ")
			.append("ammsuspv.FULL_NAME, ammsu.LOGIN_ID, send_date, submit_date, ") 
			.append("trth.CUSTOMER_NAME, trth.CUSTOMER_PHONE, trth.CUSTOMER_ADDRESS, trth.NOTES, ")
			.append("trlink.UUID_TASK_H_SURVEY, ") 
			.append("trth.LATITUDE, trth.LONGITUDE, trth.UUID_TASK_H, msst.STATUS_CODE ")
			.append("FROM TR_TASK_H trth with (nolock) ")
			.append("left join TR_TASKORDERDATA trtod with (nolock) on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
			.append("left join MS_BRANCH msb with (nolock) on trth.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM ")
			.append("left join MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY ")
			.append("left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
			.append("left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER ")
			.append("left join TR_TASKLINK trlink with (nolock) on trth.UUID_TASK_H = trlink.UUID_TASK_H_COLLECT ")
			.append("WHERE trth.UUID_FORM = :uuidForm ");
		
		if (StringUtils.isNotEmpty(uuidBranch)) {
			sqlBuilder.append("AND trth.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{ "uuidBranch", uuidBranch});
		}
		sqlBuilder.append("AND assign_date between :startDate and :endDate ");
		if (StringUtils.isNotEmpty(uuidUser)) {
			sqlBuilder.append("AND trth.UUID_MS_USER = :uuidUser ");
			paramStack.push(new Object[]{ "uuidUser", uuidUser});
		}
		sqlBuilder.append("AND msst.STATUS_CODE not in ('A') ");
	    if (StringUtils.isNotEmpty(uuidSpv)) {
	    	sqlBuilder.append("AND ammsu.SPV_ID = :uuidSpv ");
			paramStack.push(new Object[]{ "uuidSpv", uuidSpv});
	    }	
	    sqlBuilder.append("ORDER BY msst.STATUS_TASK_DESC DESC, trth.DTM_CRT");
		
	    LOG.trace("retrieveTaskListMc SQL: {}, params[user={}, spv={}, branch={}, form={}, start={}, end={}]",
	    		sqlBuilder.toString(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
	    	    
	    Object[][] sqlParams = new Object[paramStack.size()][2];
	    for (int i = 0; i < paramStack.size(); i++) {
			Object[] objects = paramStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNativeString(
	    		sqlBuilder.toString(), sqlParams);
	    LOG.info("retrieveTaskListMc.size: {}, params[user={}, spv={}, branch={}, form={}, start={}, end={}]",
	    		resultList.size(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
		return resultList;
	}
	
	@Deprecated
	private Map<String, QuestionSetBean> retrieveQuestionSet(String uuidForm, String subsysName) {
		Object [][] paramQuestion = {{"uuidForm", Long.valueOf(uuidForm)}};
		List<Map<String, Object>> listQuestion = this.getManagerDAO().selectAllNative(
				"report.download.getquestionlist", paramQuestion, null);
		
		if (listQuestion.isEmpty()) {
			return Collections.emptyMap();
		}
		int offset = (GlobalVal.SUBSYSTEM_MS.equals(subsysName)) 
				? TEMPLATE_HEADER_MS.length : TEMPLATE_HEADER_MC.length;
		Map<String, QuestionSetBean> resultList = new LinkedHashMap<>();
		
		for (Iterator<Map<String, Object>> iterator = listQuestion.iterator(); iterator.hasNext();) {
			Map<String, Object> map = iterator.next();
			
			QuestionSetBean qsb = new QuestionSetBean((String) map.get("d1"), offset++, (String) map.get("d2")); 
			resultList.put(String.valueOf(map.get("d0")), qsb);
			
			map = null;
		}
		return resultList;
	}
	
	private Map<String, QuestionSetBean> retrieveQuestionSetByFormVersion( 
			String uuidVersion, String subsysName) {
		if (uuidVersion == null) {
			return Collections.emptyMap();
		}
		Object [][] paramQuestion = {{"uuidVersion", uuidVersion}};
		
		List<Map<String, Object>> listQuestion = this.getManagerDAO().selectAllNative(
				"report.download.getquestionlistByFormVersion", paramQuestion, null);
		
		if (listQuestion.isEmpty()) {
			return Collections.emptyMap();
		}
		int offset = (GlobalVal.SUBSYSTEM_MS.equals(subsysName)) 
				? TEMPLATE_HEADER_MS.length : TEMPLATE_HEADER_MC.length;
		Map<String, QuestionSetBean> resultList = new LinkedHashMap<>();
		
		for (Iterator<Map<String, Object>> iterator = listQuestion.iterator(); iterator.hasNext();) {
			Map<String, Object> map = iterator.next();
			
			QuestionSetBean qsb = new QuestionSetBean( 
					(String) map.get("d1"), offset++, (String) map.get("d2")); 
			resultList.put(String.valueOf(map.get("d0")), qsb);
			
			map = null;
		}
		
		return resultList;
	}
	
	private void writeHeader(HSSFWorkbook workbook, HSSFSheet sheet,
			String subsysName) {
		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1_000);
		styleHeader.setFont(fontHeader);
		
		HSSFRow row = sheet.getRow(1);
		if (row == null) {
			row = sheet.createRow(1);
		}
		if (GlobalVal.SUBSYSTEM_MS.equals(subsysName)){
			for (int i = 0; i < TEMPLATE_HEADER_MS.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MS[i]);
				cell.setCellStyle(styleHeader);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MS[i]);
			}
		} 
		else {
			for (int i = 0; i < TEMPLATE_HEADER_MC.length; i++) {
				HSSFCell cell = row.createCell(i);
				cell.setCellValue(TEMPLATE_HEADER_MC[i]);
				cell.setCellStyle(styleHeader);
				sheet.setColumnWidth(i, HEADER_COLUMN_WIDTH_MC[i]);
			}
		}
	}
	
	private int writeQuestionSetHeader(HSSFWorkbook workbook, HSSFSheet sheet,
			Map<String, QuestionSetBean> questionSet) {
		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1000);
		styleHeader.setFont(fontHeader);
		
		HSSFRow row = sheet.getRow(1);
		for (Iterator<String> iterator = questionSet.keySet().iterator(); iterator.hasNext();) {
			String uuid = (String) iterator.next();
			QuestionSetBean qsb = questionSet.get(uuid);
			HSSFCell cell = row.createCell(qsb.getCellOffset());
			cell.setCellValue(StringUtils.upperCase((String) qsb.getLabel()));
			cell.setCellStyle(styleHeader);
			sheet.setColumnWidth(qsb.getCellOffset(), 30 * 256);
		}
		
		return row.getLastCellNum();
	}
	
	private void writeRecords(HSSFWorkbook workbook, HSSFSheet sheet,
			List<Map<String, Object>> listHeader, Map<String, QuestionSetBean> questionSet, int r) {
		final String[] fieldN = { "d0", "d1", "d2", "d3", "d4", "d5", "d6", "d7", "d8",
				"d9", "d10", "d11", "d12", "d13", "d14", "d15" };
		final String datetimeFormat = "yyyy-MM-dd HH:mm";
		final String hasImageValue = "(image)";
		
		HSSFCellStyle styleRightAlignment = workbook.createCellStyle();
		styleRightAlignment.setAlignment(CellStyle.ALIGN_RIGHT);
		
		for (int i = 0; i < listHeader.size(); i++) {
			Map<String, Object> header = listHeader.get(i);
			HSSFRow row = sheet.createRow(r++);
			//writing header's data || minus 2 because not writing uuid_task_h and status_code
			for (int j = 0; j < header.size()-2; j++) { 
				HSSFCell cell = row.createCell(j);
				
				String cellValue = null;
				if ("d6".equals(fieldN[j]) || "d7".equals(fieldN[j])) {
					if (header.get(fieldN[j]) != null) {
						cellValue = DateFormatUtils.format((Date) header.get(fieldN[j]), datetimeFormat);
					}
				}
				else {
					cellValue = header.get(fieldN[j]) == null ? null : header.get(fieldN[j]).toString();
				}
				
				cell.setCellValue(StringUtils.upperCase(cellValue));								
			} //end of writing header's data
			
			String statusCode = (String) header.get("d" + (header.size()-1));
			boolean isLoadIntVal = (StringUtils.equals(statusCode, GlobalVal.COLLECTION_STATUS_TASK_CANCELED) ||
					StringUtils.equals(statusCode, GlobalVal.COLLECTION_STATUS_TASK_DELETED));
			
			String[][] paramAnswerSet = { {"uuidTaskH", (String) header.get("d" + (header.size()-2))} }; 
			List<Map<String, Object>> answerSet = this.getManagerDAO().selectAllNative(
					"report.download.getTaskAnswers", paramAnswerSet, null); //no need create function from json as it is deprecated
			
			for (int j = 0; j < answerSet.size(); j++) {
				Map<String, Object> answerMap = answerSet.get(j);
								
				String uuidQuestion = (String) answerMap.get("d0");
				QuestionSetBean qsb = questionSet.get(uuidQuestion);
				int offset = qsb.getCellOffset();
				
				HSSFCell cell = row.getCell(offset);
				if (cell == null) {
					cell = row.createCell(offset);
				}
				
				String cellValue = cell.getStringCellValue();
				String answerType = (String) answerMap.get("d12");

				if ("1".equals((String) answerMap.get("d8"))) { //hasImage
					cellValue = hasImageValue;
				} 
				else if ("0".equals((String) answerMap.get("d8"))) {
					cellValue = null;
				} 
				else if ("01".equals((String) answerMap.get("d8"))) { //answertype = LOCATION
					if (null != answerMap.get("d5") && null != answerMap.get("d6") &&
							null != answerMap.get("d7")) {
						String latitude = answerMap.get("d5").toString();
						String longitude = answerMap.get("d6").toString();
						String acc = answerMap.get("d7").toString();
						cellValue = StringUtils.join(latitude, ", ", longitude, " accuracy: ", acc, "m");
					}
				} 
				else {
					String txtAnswer = null;
					String code = null;
					String option = null;
					
					//check if get INT
					if (isLoadIntVal) {
						txtAnswer = (String) answerMap.get("d9");
						code = (String) answerMap.get("d11");
						option = (String) answerMap.get("d10");
					} 
					else {
						txtAnswer = (String) answerMap.get("d2");
						code = (String) answerMap.get("d3");
						option = (String) answerMap.get("d4");
					}
					
					if (StringUtils.isNotBlank(code)) {
						MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(code));
						code = msLov.getCode();
						msLov = null;
					}
															
					if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType) || 
							GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType) ||
							GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
						if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
							if (StringUtils.isNotEmpty(cellValue)) {
								cellValue += ", ";
							}
							if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType)) {
								cellValue += code + " | " + option;
							} 
							else if (GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType)) {
								cellValue += code + " | " + option + " - " + txtAnswer;
							} 
							else if(GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
								cellValue += code + " | " + option + " - " + txtAnswer;
							}
						}
					} 
					else if (GlobalVal.ANSWER_TYPE_DROPDOWN.equals(answerType) ||
							GlobalVal.ANSWER_TYPE_RADIO.equals(answerType)) {
						if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))){
							cellValue = code + " | " + option;
						}
					} 
					else if (GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(answerType) ||
							GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(answerType)){
						if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
							cellValue = code + " | " + option + " - " + txtAnswer;
						}
					} 
					else if (GlobalVal.ANSWER_TYPE_TIME.equals(answerType)) {
						cellValue = changeDateFormat(txtAnswer, "HH:mm:ss", DATE_FORMAT_TM);
					} 
					else if (GlobalVal.ANSWER_TYPE_DATETIME.equals(answerType)) {
						cellValue = changeDateFormat(txtAnswer, "dd/MM/yyyy HH:mm:ss", DATE_FORMAT_DTM);
					} 
					else if (GlobalVal.ANSWER_TYPE_DATE.equals(answerType)) {
						cellValue = changeDateFormat(txtAnswer, "dd/MM/yyyy", DATE_FORMAT_DT);
					} 
					else if (GlobalVal.ANSWER_TYPE_TEXT_MULTILINE.equals(answerType)) {
						String[] temp = StringUtils.split(txtAnswer,"\n");
						cellValue = StringUtils.join(temp, " ");
					} 
					else {
						cellValue = txtAnswer;
					}
				}
				
				cell.setCellValue(StringUtils.upperCase(cellValue));
				if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType) ||
						GlobalVal.ANSWER_TYPE_DECIMAL.equals(answerType) ||
						GlobalVal.ANSWER_TYPE_NUMERIC.equals(answerType)) { 
					cell.setCellStyle(styleRightAlignment);
				}
				answerMap = null;
			}
			header = null;
		}
	}
	
	private void writeToSheet(Sheet sheet, List<LinkedHashMap<Integer, String>> records, 
			int startRow, int lastCol) {
		if (records == null || records.isEmpty() || sheet == null) {
			LOG.info("Nothing to write into sheet.");
			return;
		}

		Stopwatch sw = Stopwatch.createStarted();
		LOG.info("Start writing into sheet={} data.", records.size());
		int rownum = startRow;
		for (Iterator<LinkedHashMap<Integer, String>> iterator = records.iterator(); iterator.hasNext();) {
			LinkedHashMap<Integer, String> linkedHashMap = iterator.next();
			
			Row row = sheet.getRow(rownum);
			if (row == null) {
				row = sheet.createRow(rownum);
			}
						
			for (int i = 0; i <= lastCol; i++) {				
				Cell cell = row.getCell(i);				
				if (cell == null) {
					cell = row.createCell(i);
				}
				
				cell.setCellValue(linkedHashMap.get(i));
			}
			
			rownum++;
		}
		sw.stop();
		LOG.info("Finish writing into sheet={} data.Exec time=<{}>ms", 
				records.size(), sw.elapsed(TimeUnit.MILLISECONDS));
	}
	
	/**
	 * <p>Created this method because excel result created/appended by batch, is not sorted
	 * like excel created via web application.
	 * 
	 * <p>2016-02-15 The algorithm of this method if grouping list by their status, and then
	 * append all list into 1 big result.
	 * 
	 * @param sheet
	 * @param column	col index for Status Task (zero-based index)
	 * @param rowNumber row index for starting data (zero-based index)
	 * @return
	 */
	private List<LinkedHashMap<Integer, String>> sortSheet(Sheet sheet, int column, int rowNumber) {
		Stopwatch sw = Stopwatch.createStarted();
		LOG.info("Start sorting sheet");
		
		Integer statusCol = new Integer(column);
		
		DataFormatter dataFormatter = new DataFormatter(Locale.US);
		SortedMap<String, List<LinkedHashMap<Integer, String>>> holderPerStatus = new TreeMap<>();
		
		Iterator<Row> rows = sheet.rowIterator();
		String value = null;
		
		while (rows.hasNext()) {
			HSSFRow row = (HSSFRow) rows.next();
			if (row.getRowNum() < rowNumber) {
				continue;
			}
			
			Iterator<Cell> cells = row.cellIterator();
			
			LinkedHashMap<Integer, String> record = new LinkedHashMap<>();
			while (cells.hasNext()) {
				HSSFCell cell = (HSSFCell) cells.next();
				if (cell != null) {
					switch (cell.getCellType()) {
    					case HSSFCell.CELL_TYPE_NUMERIC:    						
    				        value = dataFormatter.formatCellValue(cell);
    						break;
    					case HSSFCell.CELL_TYPE_STRING:
    						value = cell.getStringCellValue();
    						break;
    					default:
					}
				}
				record.put(Integer.valueOf(cell.getColumnIndex()), value);
				value = null;
			}
			
			//add record into status group
			String status = record.get(statusCol);
			List<LinkedHashMap<Integer, String>> listPerStatus = holderPerStatus.get(status);
			if (listPerStatus == null) {
				listPerStatus = new ArrayList<>();
				holderPerStatus.put(status, listPerStatus);
			}
			listPerStatus.add(record);
		}
		
		List<LinkedHashMap<Integer, String>> result = new ArrayList<>();		
		
		Set<String> keys = holderPerStatus.keySet();
		List<String> keyList = new ArrayList<>(keys);
		Collections.reverse(keyList);
		
		Iterator<String> keyIterator = keyList.iterator();
		while (keyIterator.hasNext()) {
			String key = keyIterator.next();
			List<LinkedHashMap<Integer, String>> list = holderPerStatus.get(key);
			result.addAll(list);
		}
		
		sw.stop();
		LOG.info("Finish sorting sheet.Exec time=<{}>ms", sw.elapsed(TimeUnit.MILLISECONDS));
		return result;
	}
	
	/**
	 * Write excel result file type=DAILY into GS PATH + "/{yyyy}"
	 * Output filename => "{yyyy_MM_dd}_{uuidForm}.xls"
	 * Output file will be read when web request xls result for backdated date 
	 * 
	 * @param taskList
	 * @param questionSet
	 * @param startDate
	 * @param uuidForm
	 */
	private void writeDailyExcelFile(List<Map<String, Object>> taskList,
			Map<String, QuestionSetBean> questionSet, Date startDate, MsForm msForm) {		
		LOG.info("Start writing daily result file. uuidform={}; date={}", msForm.getUuidForm(), startDate);		
		String subsysCode = msForm.getAmMssubsystem().getSubsystemName();
		
		String path = getExcelPath(null);
		String folderFormat = DateFormatUtils.format(startDate, "yyyy");
		String fileFormat = DateFormatUtils.format(startDate, "yyyy_MM_dd");
		String absoluteFile = path + folderFormat + SystemUtils.FILE_SEPARATOR
				+ fileFormat + "_" + msForm.getUuidForm() + ".xls";
		File file = new File(absoluteFile);
		
		if (file.exists()) {
			return;
		}
		HSSFWorkbook workbook = new HSSFWorkbook();
		String sheetName = (GlobalVal.SUBSYSTEM_MC.equals(subsysCode)) 
				? "Collection Result List" : "Survey Result List";
		HSSFSheet sheet = workbook.createSheet(sheetName);
		
		int row = 2; //starting data row 2 (zero-index)
		this.writeHeader(workbook, sheet, subsysCode);
		this.writeQuestionSetHeader(workbook, sheet, questionSet);
		this.writeRecords(workbook, sheet, taskList, questionSet, row);
		
		FileOutputStream fos = null;
		try {
			File directory = new File(path + folderFormat);
			
			if (!directory.exists()) {
				directory.mkdirs();
			}
			fos = new FileOutputStream(file);
			workbook.write(fos);
		}
		catch (IOException ioe) {
			LOG.error("SCHEDULER error while create daily excel file. params[date={}, uuidForm={}, file={}]",
					startDate, msForm.getUuidForm(), absoluteFile, ioe);
		}
		finally {
			try {
				if (fos != null) {
					fos.close();
				}
				if (workbook != null) {
					workbook.close();
				}
			} 
			catch (IOException e) {
				e.printStackTrace();
			}
		}
		
		LOG.info("Finished writing daily result file={}. uuidform={}; date={}", 
				absoluteFile, msForm.getUuidForm(), startDate);
	}
	
	boolean isReadFromDailyFile(String uuidUser, String uuidSpv, String uuidBranch,
			String uuidForm, Date startDate, Date endDate) {
		if (StringUtils.isNotBlank(uuidUser)) {
			return false;
		}
		if (StringUtils.isNotBlank(uuidSpv)) {
			return false;
		}
		if (StringUtils.isNotBlank(uuidBranch)) {
			return false;
		}
		if (!isDailyBackdated(startDate, endDate)) {
			return false;
		}
		return true;
	}
	
	boolean isDailyBackdated(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("The date must not be null");
        }
        
        boolean sameDay = DateUtils.isSameDay(startDate, endDate);
        if (!sameDay) {
        	return false;
        }
        DateTime currentTime = new DateTime();
	    return startDate.before(currentTime.toDate());
	}
	
	HSSFWorkbook loadFromFile(String absoluteFile, String subsystem) throws IOException {
		FileInputStream file = null;
		HSSFWorkbook workbook = null;		
		try {
			LOG.info("Load result from file: {}", absoluteFile);
			file = new FileInputStream(new File(absoluteFile));
			workbook = new HSSFWorkbook(file);
		} 
		catch(FileNotFoundException ef) {
			LOG.warn("File: {}, not existed !", absoluteFile);
			workbook = new HSSFWorkbook();
			HSSFSheet sheet = null;
			if (subsystem.equalsIgnoreCase(GlobalVal.SUBSYSTEM_MC)) {
				sheet = workbook.createSheet("Collection Result List");
			}
			else {
				sheet = workbook.createSheet("Survey Result List");
			}
			this.createErrorMsg(workbook, sheet);
		}
		finally {
			if (file != null) {
				file.close();
			}
		}
		
		return workbook;
	}
	
	private static class QuestionSetBean {
		private String label;
		private String refId;
		private int cellOffset;
		
		public QuestionSetBean(String label, int cellOffset, String refId) {
			super();
			this.label = label;
			this.cellOffset = cellOffset;
			this.refId = refId;
		}
		
		public String getLabel() {
			return label;
		}
		
		public int getCellOffset() {
			return cellOffset;
		}
		
		public String getRefId() {
			return refId;
		}
		
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public TrReportresultlog getReportResult(String rptName, AuditContext callerId) {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and rl.processStatus=:processStatus");
		
		if (GlobalVal.RPTNAME_SURVEY_RESULT.equals(rptName)) {
			condition.append(" and rl.rptName = 'Survey Result'");
		} else if (GlobalVal.RPTNAME_ALL.equals(rptName)) {
			condition.append(" and rl.rptName != 'Survey Result'");
		}
		
		paramMap.put("processStatus", "0");
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by rl.seqno asc");
		Map<String, Object> mapResult = this.getManagerDAO().selectAll(
				"from TrReportresultlog rl "
					+ "join fetch rl.amMsuser mu "
					+ "join fetch mu.amMssubsystem ms "
					+ "join fetch mu.msBranch "
					+ "join fetch mu.msJob "
				+ "where 1=1"
				+ condition.toString() + orderQuery.toString(),
				paramMap);
		List<TrReportresultlog> listResult = (List<TrReportresultlog>) mapResult.get(GlobalKey.MAP_RESULT_LIST);
		TrReportresultlog trReportresultlog = null;
		TrReportresultlog nextReport = null;
		boolean errHasNextNew = true;
		
		if (!listResult.isEmpty()) {
			for (int i=0; i < listResult.size(); i++) {
				trReportresultlog = listResult.get(i);
				int next = i+1;
				if (next < listResult.size() && null != trReportresultlog.getProcessStartTime()) {
					for (int j=next; j < listResult.size(); j++) {
						nextReport = listResult.get(j);
						errHasNextNew = (null == nextReport.getProcessStartTime());
						if (errHasNextNew) {
							break;
						}
					}
				}
				if (null == trReportresultlog.getProcessStartTime() || next == listResult.size() || !errHasNextNew) {
					trReportresultlog.setProcessStartTime(new Date());
					this.getManagerDAO().update(trReportresultlog);
					break;
				}
			}
		}
		
		return trReportresultlog;
	}

	@Transactional
	@Override
	public void updateStartTime(TrReportresultlog trReportResultLog, AuditContext callerId) {
		if (trReportResultLog == null)
			return;
		
		trReportResultLog.setProcessStartTime(new Date());
		this.getManagerDAO().update(trReportResultLog);
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public List<TrReportresultlog> getReportResultList(AuditContext callerId) {
		Map<String, Object> param = new HashMap<>();
		param.put("processStatus", "0");
		
		Map<String, Object> mapResult = this.getManagerDAO().list(
				"from TrReportresultlog rl "
				+ "join fetch rl.amMsuser mu "
				+ "join fetch mu.amMssubsystem "
				+ "join fetch mu.msBranch "
				+ "join fetch mu.msJob "
				+ "where rl.processStatus=:processStatus " 
				+ "and rl.rptName != 'Survey Result' " 
				+ "order by rl.seqno asc", param);
		
		return (List<TrReportresultlog>) mapResult.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void processRequestReport(AuditContext callerId, TrReportresultlog trReprtResultLog) {
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		HSSFWorkbook workbook = new HSSFWorkbook();
		Gson gson = new Gson();
		
		ReportDownloadBean bean = gson.fromJson(trReprtResultLog.getRptParams(), ReportDownloadBean.class);
		
		String sheetName = (GlobalVal.SUBSYSTEM_MC.equals(bean.getSubsystemCode())) 
				? "Collection Result List" : "Survey Result List";
		HSSFSheet sheet = workbook.createSheet(sheetName);
		
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");
		Date start = new Date();
		Date end = new Date();
		try {
			start = sdf.parse(bean.getStartDate());
			end = sdf.parse(bean.getEndDate());
		} 
		catch (ParseException e) {
			LOG.error("Error while parse date. Start={}. End={}. with sdf=yyyy-MM-dd HH:mm:ss.S", 
					bean.getStartDate(), bean.getEndDate());
		}
		
		List<Map<String, Object>> taskList = this.retrieveTaskList(bean.getSubsystemCode(), 
																	bean.getUuidUser(), 
																	bean.getUuidSpv(), 
																	bean.getUuidBranch(), 
																	bean.getUuidForm(), 
																	start, end);
		if (taskList.isEmpty()) {
			this.createErrorMsg(workbook, sheet);								
		}
		else {
			this.writeHeader(workbook, sheet, bean.getSubsystemCode());
			Map<String, QuestionSetBean> questionSet = this.retrieveQuestionSet(
					bean.getUuidForm(), bean.getSubsystemCode());
			this.writeQuestionSetHeader(workbook, sheet, questionSet);
			this.writeRecords(workbook, sheet, taskList, questionSet, 2);
		}
		
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
			byte[] excelStream = stream.toByteArray();
			
			StringBuilder sb = new StringBuilder();
			
			if (GlobalVal.SUBSYSTEM_MC.equals(bean.getSubsystemCode())) {
				sb.append("CollectionResult_");
			} 
			else {
				sb.append("SurveyResult_");
			}
			
			sb.append(DateFormatUtils.format(start, "yyyyMMdd"));
			sb.append("-");
			sb.append(DateFormatUtils.format(end, "yyyyMMdd"));
			
			sb.append("_FORM-");
			if (StringUtils.isNotBlank(bean.getUuidForm())) {
				MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, 
						Long.valueOf(bean.getUuidForm()));
				sb.append(msForm.getFormName());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_BRANCH-");
			if (StringUtils.isNotBlank(bean.getUuidBranch())) {
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
						Long.valueOf(bean.getUuidBranch()));
				sb.append(msBranch.getBranchCode());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_SPV-");
			if (StringUtils.isNotBlank(bean.getUuidSpv())) {
				AmMsuser spv = this.getManagerDAO().selectOne(AmMsuser.class, 
						Long.valueOf(bean.getUuidSpv()));
				sb.append(spv.getLoginId());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if (StringUtils.isNotBlank(bean.getUuidUser())) {
				AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
						Long.valueOf(bean.getUuidUser()));
				sb.append(user.getLoginId());
			} 
			else {
				sb.append("ALL");
			}
			sb.append("_");
			sb.append(DateFormatUtils.format(trReprtResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xls");
			
			File doneFolder = new File(getExcelPath(callerId) + DateFormatUtils.format(
					trReprtResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report collection result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(excelStream);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error("Error while create excel file. params[user={}, spv={}, "
					+ "branch={}, form={}, start={}, end={}]",
					bean.getUuidUser(), bean.getUuidSpv(), bean.getUuidBranch(), bean.getUuidForm(), 
					bean.getStartDate(), bean.getEndDate(), e);
		}
		
		trReprtResultLog.setProcessFinishTime(new Date());
		trReprtResultLog.setProcessDurationSeconds((int)Math.abs(
				trReprtResultLog.getProcessStartTime().getTime() 
				- trReprtResultLog.getProcessFinishTime().getTime())/1000);
		trReprtResultLog.setReportFileLocation(filePath);
		trReprtResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReprtResultLog);
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	//tambahan untuk csv
	@Override
	public void processRequestReportCSV(AuditContext callerId, TrReportresultlog trReprtResultLog) {
		Gson gson = new Gson();
		ReportDownloadBean bean = gson.fromJson(trReprtResultLog.getRptParams(), ReportDownloadBean.class);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");
		Date start = new Date();
		Date end = new Date();
		
		try {
			start = sdf.parse(bean.getStartDate());
			end = sdf.parse(bean.getEndDate());
		} 
		catch (ParseException e) {
			LOG.error("Error while parse date. Start={}. End={}. with sdf=yyyy-MM-dd HH:mm:ss.S", 
					bean.getStartDate(), bean.getEndDate());
		}
		
		String filePath = StringUtils.EMPTY;
		String fileName = generateFileName(bean, trReprtResultLog, start, end);
		
		File doneFolder = new File(getExcelPath(callerId) + DateFormatUtils.format(
				trReprtResultLog.getProcessStartTime(), "yyyy"));
		if (!doneFolder.exists()) {
			doneFolder.mkdirs();
		}
		
		if (FilterType.FILTER_BY_MONTHLY.toString().equals(trReprtResultLog.getRptType())) {
			File monthlyDoneFolder = new File(getExcelPath(callerId));
			filePath = monthlyDoneFolder.getPath() + SystemUtils.FILE_SEPARATOR + fileName;
		} 
		else {
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + fileName;
		}
		
		CSVWriter writer = null;
		BufferedWriter bufWriter = null;
		AmMsuser userLogin = this.getManagerDAO().selectOne(AmMsuser.class, 
				Long.valueOf(bean.getUuidLoginId()));
		try {
			int bufSize = 64*1024;
			bufWriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath)),bufSize);
			writer = new CSVWriter(bufWriter, ',');
			long startTime = start.getTime();
			long endTime = end.getTime();
			long diffTime = endTime - startTime;
//			int diffDays = (int)diffTime / (1000 * 60 * 60 * 24);
			long diffDays = TimeUnit.DAYS.convert(diffTime, TimeUnit.MILLISECONDS);
			Calendar c = Calendar.getInstance();
			c.setTime(start);
			c.add(Calendar.DATE,-1);
			Map<String, QuestionSetBean> questionSet =  new HashMap();
			if (GlobalVal.SUBSYSTEM_MS.equalsIgnoreCase(bean.getSubsystemCode())
					|| GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(bean.getSubsystemCode())) {
				questionSet = this.retrieveQuestionSetByFormVersion(
						bean.getUuidVersion(), bean.getSubsystemCode());
				this.writeHeaderCSV(writer,bean.getSubsystemCode(),questionSet);
			}
			else {
				questionSet = this.retrieveQuestionSetByFormVersion(
						bean.getUuidVersion(), bean.getSubsystemCode());
				this.writeHeaderCSV(writer, bean.getSubsystemCode(), questionSet);
			}
			for (int i = 0; i <= diffDays; i++) {
				c.add(Calendar.DATE, 1);
				c.set(Calendar.HOUR_OF_DAY, 0);
				c.set(Calendar.MINUTE, 0);
				c.set(Calendar.SECOND, 0);
				c.set(Calendar.MILLISECOND, 0);
				Date startDate = c.getTime();
				c.set(Calendar.HOUR_OF_DAY, 23);
				c.set(Calendar.MINUTE, 59);
				c.set(Calendar.SECOND, 59);
				c.set(Calendar.MILLISECOND, 997);
				Date endDate = c.getTime();
				int rowLimit = Integer.valueOf(SpringPropertiesUtils.getProperty("row.query"));
				int startLimit =0;
				int endLimit = rowLimit;
				int z=1;
				while (true) {
					MsFormhistory version = this.getManagerDAO().selectOne(MsFormhistory.class,
							StringUtils.isBlank(bean.getUuidVersion()) ? null : 
								Long.valueOf(bean.getUuidVersion()));
					int versi = version == null?0:version.getFormVersion();
					List<Map<String, Object>> taskList = this.retrieveTaskListLimit(bean.getSubsystemCode(), 
							bean.getUuidUser(), 
							bean.getUuidSpv(), 
							bean.getUuidBranch(), 
							bean.getUuidForm(), 
							startDate, endDate,
							String.valueOf(startLimit),
							String.valueOf(endLimit),
							bean.getUuidLoginId(),
							String.valueOf(userLogin.getMsBranch().getUuidBranch()), 
							versi);
					
					if (!taskList.isEmpty()) {
						if (GlobalVal.SUBSYSTEM_MC.equalsIgnoreCase(bean.getSubsystemCode())) {
							List<String> idOfQuestionSet = new ArrayList<String>(); 
							idOfQuestionSet.addAll(questionSet.keySet());
							this.writeRecordsCSVMc(writer, taskList, callerId, idOfQuestionSet);
						}
						else if (GlobalVal.SUBSYSTEM_MT.equalsIgnoreCase(bean.getSubsystemCode())) {
							this.writeRecordsCSVMt(writer, taskList);
						}
						else {
							List<String> idOfQuestionSet = new ArrayList<String>(); 
							idOfQuestionSet.addAll(questionSet.keySet());					
							this.writeRecordsCSV(writer, taskList, callerId, idOfQuestionSet);
						}
					} 
					else {
						break;
					}
					startLimit = endLimit + 1;
					z++;
					endLimit = z*rowLimit;
				}
			}
		} 
		catch (IOException e) {
			LOG.info("Path Not Found");
			e.printStackTrace();
		} 
		finally {
			try {
				bufWriter.close();
				writer.close();
			} 
			catch (IOException e) {
				LOG.info("Cannot Close Open CSV Writer");
				e.printStackTrace();
			}
		}
		
		trReprtResultLog.setProcessFinishTime(new Date());
		trReprtResultLog.setProcessDurationSeconds((int)Math.abs(
				trReprtResultLog.getProcessStartTime().getTime() 
				- trReprtResultLog.getProcessFinishTime().getTime())/1000);
		trReprtResultLog.setReportFileLocation(filePath);
		trReprtResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReprtResultLog);
	}
	
	private String generateFileName(ReportDownloadBean bean, TrReportresultlog trReprtResultLog, 
			Date start, Date end) {
		StringBuilder sb = new StringBuilder();
		
		if (FilterType.FILTER_BY_MONTHLY.toString().equals(trReprtResultLog.getRptType())) {
			Object[][] paramForm = { {Restrictions.eq("uuidForm", Long.valueOf(bean.getUuidForm()))} };
			MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, paramForm);
			Object[][] paramFormHistory = {{Restrictions.eq("uuidFormHistory", Long.valueOf(bean.getUuidVersion()))}};
			MsFormhistory msFormHistory = this.getManagerDAO().selectOne(MsFormhistory.class, paramFormHistory);
			String filename = DateFormatUtils.format(start, "yyyy_MM_");
			filename = filename + msForm.getFormName() + "_v" + msFormHistory.getFormVersion() + ".csv";
			return filename;
		} 
		else if (this.isReadFromDailyFile(bean.getUuidUser(), bean.getUuidSpv(), 
				bean.getUuidBranch(), bean.getUuidForm(), start, end)) {
			String filePrefixName = DateFormatUtils.format(start, "yyyy_MM_dd_");
			String filename = filePrefixName + bean.getUuidForm() + ".csv";
			return filename;
		} 
		else {
			if (GlobalVal.SUBSYSTEM_MC.equals(bean.getSubsystemCode())) {
				sb.append(trReprtResultLog.getRptName() + "_");
			} 
			else if (GlobalVal.SUBSYSTEM_MT.equals(bean.getSubsystemCode())) {
				sb.append("TrackingResult_");
			} 
			else {
				sb.append("SurveyResult_");
			}
			
			sb.append(DateFormatUtils.format(start, "yyyyMMdd"));
			sb.append("-");
			sb.append(DateFormatUtils.format(end, "yyyyMMdd"));
			
			sb.append("_FORM-");
			if (StringUtils.isNotBlank(bean.getUuidForm())) {
				MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, 
						Long.valueOf(bean.getUuidForm()));
				sb.append(msForm.getFormName());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_BRANCH-");
			if (StringUtils.isNotBlank(bean.getUuidBranch())) {
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
						Long.valueOf(bean.getUuidBranch()));
				sb.append(msBranch.getBranchCode());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_SPV-");
			if (StringUtils.isNotBlank(bean.getUuidSpv())) {
				AmMsuser spv = this.getManagerDAO().selectOne(AmMsuser.class, 
						Long.valueOf(bean.getUuidSpv()));
				sb.append(spv.getLoginId());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if (StringUtils.isNotBlank(bean.getUuidUser())) {
				AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
						Long.valueOf(bean.getUuidUser()));
				sb.append(user.getLoginId());
			} 
			else {
				sb.append("ALL");
			}
			sb.append("_");
			sb.append(DateFormatUtils.format(trReprtResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".csv");
		}
		
		return sb.toString();
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void exportTaskReportCSV(AmMsuser user, AuditContext callerId, 
			String uuidUser, String uuidSpv, String uuidBranch, String uuidForm, String uuidFormVersion,
			Date startDate, Date endDate, String filterBy) {
		
		try {
			Map<String, Object> callerIdMap = callerId.getParameters();
			String subsystemCode = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
			if (FilterType.FILTER_BY_MONTHLY.toString().equals(filterBy)) {
				Calendar calendar = Calendar.getInstance();
				calendar.setTime(startDate);
				int days = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
				calendar.set(Calendar.DATE,1);
				startDate = calendar.getTime();
				calendar.set(Calendar.DATE, days);
				calendar.set(Calendar.HOUR, 23);
				calendar.set(Calendar.MINUTE, 59);
				calendar.set(Calendar.SECOND,59);
				endDate = calendar.getTime();
			} 
			ReportDownloadBean reportBean = new ReportDownloadBean();
			
			reportBean.setSubsystemCode(subsystemCode);
			reportBean.setUuidLoginId(String.valueOf(user.getUuidMsUser()));
			reportBean.setUuidForm(uuidForm);
			reportBean.setUuidVersion(uuidFormVersion);
			reportBean.setUuidBranch(uuidBranch);
			reportBean.setUuidSpv(uuidSpv);
			reportBean.setUuidUser(uuidUser);
			reportBean.setStartDate(DateFormatUtils.format(startDate, "yyyy-MM-dd HH:mm:ss.S"));
			reportBean.setEndDate(DateFormatUtils.format(endDate, "yyyy-MM-dd HH:mm:ss.S"));
			
			Gson gson = new GsonBuilder().serializeNulls().create();
			String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);
			
			TrReportresultlog trReportResultLog = new TrReportresultlog();
			
			trReportResultLog.setAmMsuser(user);
			trReportResultLog.setDtmRequest(new Date());
			if (GlobalVal.SUBSYSTEM_MC.equals(subsystemCode)) {
				trReportResultLog.setRptName("Collection Result");
			} 
			else if (GlobalVal.SUBSYSTEM_MT.equals(subsystemCode)) {
				trReportResultLog.setRptName("Tracking Result");
			} 
			else {
				trReportResultLog.setRptName("Survey Result");
			}
			trReportResultLog.setRptType(filterBy);
			trReportResultLog.setRptParams(jsonParams);
			trReportResultLog.setProcessStatus("0");
			this.getManagerDAO().insert(trReportResultLog);
			
		} 
		catch (Exception e) {
			LOG.error("Error while create excel file. params[user={}, spv={}, "
					+ "branch={}, form={}, start={}, end={}]",
	    		uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate, e);
		}
	}
	
	private void writeHeaderCSV(CSVWriter writer,String subsysName, Map<String, QuestionSetBean> questionSet) {
		List<String> tmpHeaderMs = new ArrayList<String>(Arrays.asList(TEMPLATE_HEADER_MS));
		List<String> tmpHeaderMc = new ArrayList<String>(Arrays.asList(TEMPLATE_HEADER_MC));
		List<String> tmpHeaderMt = new ArrayList<String>(Arrays.asList(TEMPLATE_HEADER_MT));
		if (subsysName.equalsIgnoreCase(GlobalVal.SUBSYSTEM_MS) || 
				subsysName.equalsIgnoreCase(GlobalVal.SUBSYSTEM_MC) || 
				subsysName.equalsIgnoreCase(GlobalVal.SUBSYSTEM_MT)) {
			for (Iterator<String> iterator = questionSet.keySet().iterator(); iterator.hasNext();) {
				String uuid = (String) iterator.next();
				QuestionSetBean qsb = questionSet.get(uuid);
				if (GlobalVal.SUBSYSTEM_MS.equals(subsysName)){
					if(StringUtils.upperCase(qsb.getLabel()).contains("KOLEKTIBILITAS / JUMLAH  KONTRAK / BAKI DEBET")) {
						for(int i=1;i<=5;i++) {
							String label = (String) qsb.getLabel().replace("Kolektibilitas ","Kolektibilitas "+i);
							tmpHeaderMs.add(StringUtils.upperCase(label));
						}
					}else{
						String label = StringUtils.EMPTY;
						
						if(qsb.getRefId().equalsIgnoreCase("SVY_TOT_KTR_ATF_PMHN")){
							label = (String) qsb.getLabel() + " Pemohon";
						}else if(qsb.getRefId().equalsIgnoreCase("SVY_TOT_KTR_ATF_PSGN")){
							label = (String) qsb.getLabel() + " Pasangan";
						}else if(qsb.getRefId().equalsIgnoreCase("SVY_TOT_KTR_ATF_GRTR")){
							label = (String) qsb.getLabel()+ " Guarantor";
						}else {
							label = (String) qsb.getLabel();
						}
						tmpHeaderMs.add(StringUtils.upperCase(label));						
					}
				} 
				else if (GlobalVal.SUBSYSTEM_MT.equals(subsysName)){
					tmpHeaderMt.add(StringUtils.upperCase((String) qsb.getLabel()));
				}
				else {
					tmpHeaderMc.add(StringUtils.upperCase((String) qsb.getLabel()));
				}
			}
		}
		if (GlobalVal.SUBSYSTEM_MS.equals(subsysName)){
			Object[] tmpArray = tmpHeaderMs.toArray();
			writer.writeNext(Arrays.copyOf(tmpArray, tmpArray.length, String[].class));
		} 
		else if (GlobalVal.SUBSYSTEM_MT.equals(subsysName)) {
			Object[] tmpArray = tmpHeaderMt.toArray();
			writer.writeNext(Arrays.copyOf(tmpArray, tmpArray.length, String[].class));
		} 
		else {
			Object[] tmpArray = tmpHeaderMc.toArray();
			writer.writeNext(Arrays.copyOf(tmpArray, tmpArray.length, String[].class));
		}
	}
	
	private int writeRecordsCSV(CSVWriter writer, List<Map<String, Object>> listHeader, AuditContext callerId, 
			List idOfQuestionSet) {
		final String[] fieldN = { "d0", "d1", "d2", "d3", "d4", "d5", "d24", "d25", "d6", "d7", "d8",
				"d9", "d10", "d11", "d12", "d13", "d14", "d15", "d16", "d17", "d18", "d19", "d20", "d21", "d22", "d23", "d24", "d25" };		
		final String datetimeFormat = "yyyy-MM-dd HH:mm";
		final String baseUrlImage = SpringPropertiesUtils.getProperty(GlobalKey.WS_BASE_PUBLIC) 
				+ apiViewImagePath;		
		for (int i = 0; i < listHeader.size(); i++) {
			Map<String, Object> header = listHeader.get(i);
			List<String> tmpAnswerList = new ArrayList<>();
			for (int j = 0; j < header.size()-5; j++) {
				//writing header's data || minus 5 because not writing uuid_task_h and status_code and dtm_crt
				String cellValue = StringUtils.EMPTY;
				if ("d6".equals(fieldN[j]) || "d7".equals(fieldN[j])|| "d8".equals(fieldN[j])|| "d9".equals(fieldN[j])||
					"d10".equals(fieldN[j])|| "d11".equals(fieldN[j])|| "d12".equals(fieldN[j])|| "d13".equals(fieldN[j])) {
					if (header.get(fieldN[j]) != null) {
						cellValue = DateFormatUtils.format((Date) header.get(fieldN[j]), datetimeFormat);
					}
				}
				else {
					cellValue = header.get(fieldN[j]) == null ? StringUtils.EMPTY 
							: header.get(fieldN[j]).toString();
				}
				cellValue = cellValue.replace("\n", " ");
				cellValue = "=\""+cellValue+"\"";
				tmpAnswerList.add(StringUtils.upperCase(cellValue));								
			}
			
			String statusCode = (String) header.get("d"+(header.size()-4));
			boolean isLoadIntVal = (StringUtils.equals(statusCode, 
					GlobalVal.COLLECTION_STATUS_TASK_CANCELED));
			if (PropertiesHelper.isTaskDJson()) {
				this.retrieveRecordsJson(NumberUtils.toLong(header.get("d"+(header.size()-5)).toString()),
						baseUrlImage, isLoadIntVal, tmpAnswerList, callerId);
			}
			else {
				try{
					this.retrieveRecordsRow(Long.valueOf(header.get("d"+(header.size()-5)).toString()),
							baseUrlImage, isLoadIntVal, tmpAnswerList, false, idOfQuestionSet);
				}catch(Exception e){
					continue;
				}
			}
			
			Object[] tmpArray = tmpAnswerList.toArray();
			writer.writeNext(Arrays.copyOf(tmpArray, tmpArray.length, String[].class));
		}
		return 1;
	}
	
	private void retrieveRecordsJson(long uuidTaskH, String baseUrlImage,
			boolean isLoadIntVal, List<String> tmpAnswerList, AuditContext auditContext) {
		List<String> answerList = this.commonLogic.listAnswersFromJsonForDownloadResult(
				uuidTaskH, baseUrlImage, isLoadIntVal, auditContext);
		tmpAnswerList.addAll(answerList);
	}
	
	private void retrieveRecordsRow(long uuidTaskH, String baseUrlImage,
			boolean isLoadIntVal, List<String> tmpAnswerList, boolean isMc, List idOfQuestionSet) {
		Object[][] paramAnswerSet = { { "uuidTaskH", uuidTaskH },  {"refid", GlobalVal.MASKING_DOWNLOAD_RESULT_REFID_LIST} };
		List<Map<String, Object>> answerSet = this.getManagerDAO().selectAllNative("report.download.getTaskAnswersCSV",
				paramAnswerSet, null);
		List<Map<String, Object>> newAnswerSet = new ArrayList();
		Map<String, Object> baseMap =  new HashMap();
		int answerSetSize = answerSet.size();
		int ctr = 0;
		int sizeForD = 0;
		String uuidQuestionBefore = StringUtils.EMPTY;
		
		try{
			baseMap = answerSet.get(0);
			sizeForD = baseMap.size();
		}catch(Exception e){
			//task_h exists but task_d empty
			LOG.error("No records found in database");
		}
		
		//insert dummy data to newAnswerSet
		for(int i = 0; i < idOfQuestionSet.size(); i++){
			Map<String, Object> answerMap = new HashMap(); 
			String uuidAnswerSet = StringUtils.EMPTY;
			Map<String, Object> temp = new HashMap();
			String uuidQuestionSet = idOfQuestionSet.get(i).toString();
			
			MsQuestion msQuestion = this.commonLogic.retrieveQuestionByUuid(Long.parseLong(uuidQuestionSet), null);
			String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
			String questionLabel = msQuestion.getQuestionLabel();
			
			if(ctr < answerSetSize){
				answerMap = answerSet.get(ctr);
				uuidAnswerSet = answerMap.get("d0").toString();
			}		
			
			if(!uuidQuestionSet.equalsIgnoreCase(uuidAnswerSet) || null == answerMap){
				for(int h = 0; h < sizeForD; h++){				
					temp.put("d"+h, StringUtils.EMPTY);
				}
				temp.replace("d0", uuidQuestionSet);
				temp.replace("d1", questionLabel);
				temp.replace("d12", answerType);
				newAnswerSet.add(temp);
				temp = null;
			}else{
				newAnswerSet.add(answerMap);
				answerMap = null;
				ctr++;
			}
		}		
		
		for (int j = 0; j < newAnswerSet.size(); j++) {
			Map<String, Object> answerMap = newAnswerSet.get(j);
			String cellValue = StringUtils.EMPTY;
			String answerType = (String) answerMap.get("d12");
			String uuidQuestionCurrent =  (answerMap.get("d0") == null) ? null : answerMap.get("d0").toString();
			String questionLabel = (String) answerMap.get("d1");

			if ("1".equals((String) answerMap.get("d8"))) { // hasImage
				String queryParam = (FLAG_FINAL_TABLE.equals((String) answerMap.get("d15"))) ? QUERY_PARAM_FINAL
						: StringUtils.EMPTY;
				String uuidLob = (answerMap.get("d16") == null) ? null : answerMap.get("d16").toString();
				cellValue = baseUrlImage + uuidLob + queryParam;
			}
			else if ("0".equals((String) answerMap.get("d8"))) {
				cellValue = StringUtils.EMPTY;
			}
			else if ("01".equals((String) answerMap.get("d8"))) { // answertype = LOCATION
				if (null != answerMap.get("d5") && null != answerMap.get("d6") && null != answerMap.get("d7")) {
					String latitude = answerMap.get("d5").toString();
					String longitude = answerMap.get("d6").toString();
					String acc = answerMap.get("d7").toString();
					cellValue = StringUtils.join(latitude, ", ", longitude, " accuracy: ", acc, "m");
				}
				
			}
			else {
				String txtAnswer = StringUtils.EMPTY;
				String code = StringUtils.EMPTY;
				String option = StringUtils.EMPTY;

				// check if get INT
				if (isLoadIntVal) {
					txtAnswer = StringUtils.upperCase((String) answerMap.get("d9"));
					code = (null == answerMap.get("d11")) ? null : answerMap.get("d11").toString();
					option = StringUtils.upperCase((String) answerMap.get("d10"));
				}
				else {
					txtAnswer = StringUtils.upperCase((String) answerMap.get("d2"));
					code = (null == answerMap.get("d3")) ? null : answerMap.get("d3").toString();
					option = StringUtils.upperCase((String) answerMap.get("d4"));
				}

				if (StringUtils.isNotBlank(code) && !code.matches("(\\*)+")) {
					MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(code));
					code = msLov.getCode();
					msLov = null;
					
				}

				if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
					if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
						if (StringUtils.isNotEmpty(cellValue)) {
							cellValue += ", ";
						}
						if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType)) {
							cellValue += code + " | " + option;
						}
						else if (GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType)) {
							cellValue += code + " | " + option + " - " + txtAnswer;
						}
						else if (GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
							cellValue += code + " | " + option + " - " + txtAnswer;
						}
					}
				}
				else if (GlobalVal.ANSWER_TYPE_DROPDOWN.equals(answerType) || GlobalVal.ANSWER_TYPE_RADIO.equals(answerType)) {
					if (isMc && GlobalVal.COL_TAG_RVNUMBER.equals(answerMap.get("d13"))) {
						cellValue = (String) answerMap.get("d14");
					}
					else if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
						cellValue = code + " | " + option;
					} 
				}
				else if (GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION.equals(answerType)) {
					if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
						cellValue = code + " | " + option + " - " + txtAnswer;
					}
				}
				else if (GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(answerType)) {
					if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
						cellValue = code + " | " + option + " - " + txtAnswer;
					}
				}
				else if (GlobalVal.ANSWER_TYPE_TIME.equals(answerType) && !txtAnswer.matches("(\\*)+")) {
					cellValue = changeDateFormat(txtAnswer, "HH:mm:ss", DATE_FORMAT_TM);
				}
				else if (GlobalVal.ANSWER_TYPE_DATETIME.equals(answerType) && !txtAnswer.matches("(\\*)+")) {
					cellValue = changeDateFormat(txtAnswer, "dd/MM/yyyy HH:mm:ss", DATE_FORMAT_DTM);
				}
				else if (GlobalVal.ANSWER_TYPE_DATE.equals(answerType) && !txtAnswer.matches("(\\*)+")) {
					cellValue = changeDateFormat(txtAnswer, "dd/MM/yyyy", DATE_FORMAT_DT);
				}
				else if (GlobalVal.ANSWER_TYPE_TEXT_MULTILINE.equals(answerType)) {
					String[] temp = StringUtils.split(txtAnswer, "\n");
					cellValue = StringUtils.join(temp, " ");
				}
				else if (GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(answerType)) {
					if (null != answerMap.get("d5") && null != answerMap.get("d6") && null != answerMap.get("d7")) {
						String latitude = answerMap.get("d5").toString();
						String longitude = answerMap.get("d6").toString();
						String acc = answerMap.get("d7").toString();
						cellValue = StringUtils.join("Koordinat: ", latitude, ", ", longitude, ", Akurasi: ", acc, " m, Alamat: ", txtAnswer);
					} else {
						cellValue = txtAnswer;
					}
				}
				else {
					cellValue = txtAnswer;
				}
			}
			
			if (null == cellValue) {
				cellValue = StringUtils.EMPTY;
			}
			
			if(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE_SEPERATE.equals(answerType)) {
				if(questionLabel.contains("Kolektibilitas / Jumlah  Kontrak / Baki Debet")) {
						String[] values = StringUtils.split(cellValue, "\n");
						int len = values.length;
						if (uuidQuestionCurrent.equalsIgnoreCase(uuidQuestionBefore)) {
							for (int i = 0; i < 5; i++) {
								String temp = tmpAnswerList.get(tmpAnswerList.size() - (len-i));
								if (len > i) {
									temp += ";" + values[i];
									tmpAnswerList.set(tmpAnswerList.size() - (len-i), temp);
								} else {
									tmpAnswerList.set(tmpAnswerList.size() - (len-i), StringUtils.EMPTY);
								}
							}
						}else {
				            for (int i = 0; i < 5; i++) {
				                tmpAnswerList.add(i < len ? values[i] : StringUtils.EMPTY);
				            }
						}
					}
			}else {
				cellValue = cellValue.replace("\n", " ");
				if (uuidQuestionCurrent.equalsIgnoreCase(uuidQuestionBefore)) {
					String temp = tmpAnswerList.get(tmpAnswerList.size() - 1);
					temp += ";" + cellValue;
					tmpAnswerList.set(tmpAnswerList.size() - 1, temp);
				}else {
					tmpAnswerList.add(cellValue);
				}
			}
			answerMap = null;
			uuidQuestionBefore = uuidQuestionCurrent;	
		}
	}
	
	@Override
	public byte[] generateReportSchedulerCSV(AuditContext callerId, MsForm msForm, String path, 
			Date startDate, Date endDate, int formVersion) {
		String subsysCode = msForm.getAmMssubsystem().getSubsystemName();
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		FileInputStream file = null;
		CSVWriter writer = null;
		try {
			boolean isNew = false;
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(startDate);
			if (calendar.getActualMinimum(Calendar.DAY_OF_MONTH) == calendar.get(Calendar.DAY_OF_MONTH)) {
				isNew = true;
			}
			
			try {
				file = new FileInputStream(new File(path));
			} 
			catch(FileNotFoundException ef) {
				LOG.warn("File path : {}, not existed !", path);
				isNew = true;
			}
			if (isNew) {
				writer = new CSVWriter(new FileWriter(path), ',');
			} 
			else {
				FileWriter fileWriter = new FileWriter(path, true);
				writer = new CSVWriter(fileWriter, ',');
			}
			
			Map<String, QuestionSetBean> questionSet = this.retrieveQuestionSet(
					String.valueOf(msForm.getUuidForm()), subsysCode);
			List<Map<String, Object>> taskList = this.retrieveTaskListLimit(subsysCode, null, 
					null, null, String.valueOf(msForm.getUuidForm()), startDate, endDate,"0","20000", 
					null, null, formVersion);
			if (!taskList.isEmpty()) {	
				if (isNew) {
					this.writeHeaderCSV(writer,subsysCode,questionSet);
				}
				
				this.writeDailyCSVFile(taskList, questionSet, startDate, msForm, callerId);
				if (GlobalVal.SUBSYSTEM_MC.equals(subsysCode)) {
					List<String> idOfQuestionSet = new ArrayList<String>(); 
					idOfQuestionSet.addAll(questionSet.keySet());
					this.writeRecordsCSVMc(writer, taskList, callerId, idOfQuestionSet);
				}
				else {
					List<String> idOfQuestionSet = new ArrayList<String>(); 
					idOfQuestionSet.addAll(questionSet.keySet());			
					this.writeRecordsCSV(writer, taskList, callerId, idOfQuestionSet);
				}
			}
			else {
				if (isNew) {
					this.writeHeaderCSV(writer,subsysCode,questionSet);
				}
			}
			writer.close();
			return stream.toByteArray();
			
		} 
		catch (Exception e) {
			LOG.error("SCHEDULER error while create excel file. params[form={}, start={}, end={}]",
					msForm.getUuidForm(), startDate, endDate, e);
		} 
		finally {
			try {
				if (file != null) {
					file.close();
				}
				writer.close();
			} 
			catch (IOException e) {
				LOG.error("SCHEDULER error while close workbook. params[form={}]", msForm.getUuidForm());
			}
		}
		return ArrayUtils.EMPTY_BYTE_ARRAY;
	}
	
	private void writeDailyCSVFile(List<Map<String, Object>> taskList,
			Map<String, QuestionSetBean> questionSet, Date startDate, MsForm msForm, AuditContext callerId) {		
		LOG.info("Start writing daily result file. uuidform={}; date={}", msForm.getUuidForm(), startDate);		
		String subsysCode = msForm.getAmMssubsystem().getSubsystemName();
		
		String path = getExcelPath(null);
		String folderFormat = DateFormatUtils.format(startDate, "yyyy");
		String fileFormat = DateFormatUtils.format(startDate, "yyyy_MM_dd");
		String absoluteFile = path + folderFormat + SystemUtils.FILE_SEPARATOR
				+ fileFormat + "_" + msForm.getUuidForm() + ".csv";
		File file = new File(absoluteFile);
		
		if (file.exists()) {
			return;
		}
		CSVWriter writer = null;
		try {
			writer = new CSVWriter(new FileWriter(absoluteFile), ',');
			this.writeHeaderCSV(writer, subsysCode, questionSet);
			if (GlobalVal.SUBSYSTEM_MC.equals(subsysCode)) {
				List<String> idOfQuestionSet = new ArrayList<String>(); 
				idOfQuestionSet.addAll(questionSet.keySet());
				this.writeRecordsCSVMc(writer, taskList, callerId, idOfQuestionSet);
			}
			else {
				List<String> idOfQuestionSet = new ArrayList<String>(); 
				idOfQuestionSet.addAll(questionSet.keySet());					
				this.writeRecordsCSV(writer, taskList, callerId, idOfQuestionSet);
			}
			writer.close();
		} 
		catch (IOException e) {
			LOG.error("Error write daily CSV file");;
			e.printStackTrace();
		}
		LOG.info("Finished writing daily result file={}. uuidform={}; date={}", absoluteFile, 
				msForm.getUuidForm(), startDate);
	}
	
	byte[] loadCSVFromFile(String absoluteFile, String subsystem) throws IOException {
		byte[] result = null;
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		File file = null;
		CSVWriter writer = new CSVWriter(new OutputStreamWriter(stream), ',', CSVWriter.NO_QUOTE_CHARACTER);
		try {
			LOG.info("Load result from file: {}", absoluteFile);
			file = new File(absoluteFile);
			if (file.exists()) {
				Path path = Paths.get(absoluteFile);
				result = Files.readAllBytes(path);
			}
			else {
				writer.writeNext(new String[] {"Data Not Found"});
				writer.close();
				result = stream.toByteArray();
			}
		} 
		catch(FileNotFoundException ef) {
			LOG.warn("File: {}, not existed !", absoluteFile);
		}
		if (result == null) {
			result = new byte[1];
		}
		return result;
	}
	
	private List<Map<String, Object>> retrieveTaskListMsCSV(String uuidUser, String uuidSpv, 
			String uuidBranch, String uuidForm, Date startDate, Date endDate, String start, 
			String end, String uuidUserLogin, String uuidBranchLogin, int formVersion) {
		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[]{ "uuidForm", uuidForm});
		paramStack.push(new Object[]{ "startDate", startDate});
		paramStack.push(new Object[]{ "endDate", endDate});
		if (formVersion != 0){
			paramStack.push(new Object[]{ "formVersion", formVersion});
		}
		String retriction = getRestrictionStringMsCSV(paramStack, uuidBranch, uuidUser, uuidSpv, formVersion);
		String retrictionBranch = getRestrictionBranchMsCSV(paramStack, uuidUserLogin, uuidBranchLogin);
		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder.append("SELECT b.* from ( SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum ")
			.append("FROM (select c.*, ROW_NUMBER() OVER (order by DTM_CRT) AS rownum from (");
		sqlBuilder.append("SELECT trth.APPL_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ")
			.append("ammsuspv.FULL_NAME, ammsu.UNIQUE_ID,assign_date,download_date,read_date,start_dtm, send_date, submit_date, ") 
			.append("promise_date,approval_date, cpx.STATUS, cpx.STATS_START_DT, cpx.PREP_START_DT, cpx.PREP_END_DT, trth.CUSTOMER_NAME, ISNULL(REPLICATE('*', LEN(trth.CUSTOMER_PHONE)), '*') CUSTOMER_PHONE, ISNULL(REPLICATE('*', LEN(trth.CUSTOMER_ADDRESS)), '*') CUSTOMER_ADDRESS, trth.NOTES, ")
			.append("trth.LATITUDE, trth.LONGITUDE, ammsu.FULL_NAME as SVY_FULL_NAME, msb.BRANCH_NAME, trth.UUID_TASK_H, msst.STATUS_CODE, trth.DTM_CRT ")
			.append("FROM TR_TASK_H trth with (nolock) ")
			.append("left join TR_TASKORDERDATA trtod with (nolock) on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
			.append("left join MS_BRANCH msb with (nolock) on trth.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM ")
			.append("left join MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY ")
			.append("left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
			.append("left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER ")
			.append("left outer JOIN STAGING_CREDIT_PROCESS_X cpx with (nolock) on (trth.APPL_NO=cpx.MOBILE_TASK_CODE ")
			.append("or trth.APPL_NO = cpx.APP_NO ")
			.append("and SEQUENCE_PROCESS =(SELECT MAX (SEQUENCE_PROCESS) ")
			.append("FROM STAGING_CREDIT_PROCESS_X where APP_NO = trth.APPL_NO) ) ")
			.append(retrictionBranch);

		sqlBuilder.append(retriction);
	    sqlBuilder.append("UNION ALL ");
		sqlBuilder.append("SELECT trth.APPL_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ")
		.append("ammsuspv.FULL_NAME, ammsu.UNIQUE_ID,assign_date,download_date,read_date,start_dtm, send_date, submit_date, ") 
		.append("promise_date,approval_date, cpx.STATUS, cpx.STATS_START_DT, cpx.PREP_START_DT, cpx.PREP_END_DT,trth.CUSTOMER_NAME, ISNULL(REPLICATE('*', LEN(trth.CUSTOMER_PHONE)), '*') CUSTOMER_PHONE, ISNULL(REPLICATE('*', LEN(trth.CUSTOMER_ADDRESS)), '*') CUSTOMER_ADDRESS, trth.NOTES, ") 
		.append("trth.LATITUDE, trth.LONGITUDE, ammsu.FULL_NAME as SVY_FULL_NAME, msb.BRANCH_NAME, trth.UUID_TASK_H, msst.STATUS_CODE, trth.DTM_CRT ")
		.append("FROM FINAL_TR_TASK_H trth with (nolock) left join TR_TASKORDERDATA trtod with (nolock) ")
		.append("on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
		.append("left join MS_BRANCH msb with (nolock) on trth.UUID_BRANCH = msb.UUID_BRANCH ")
		.append("left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM ")
		.append("left join MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY ")
		.append("left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
		.append("left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
		.append("left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER ")
		.append("left outer JOIN STAGING_CREDIT_PROCESS_X cpx with (nolock) on (trth.APPL_NO=cpx.MOBILE_TASK_CODE ")
		.append("or trth.APPL_NO = cpx.APP_NO ")
		.append("and SEQUENCE_PROCESS =(SELECT MAX (SEQUENCE_PROCESS) ")
		.append("FROM STAGING_CREDIT_PROCESS_X where APP_NO = trth.APPL_NO) ) ")
		.append(retrictionBranch);
		sqlBuilder.append(retriction);	
			
	    sqlBuilder.append(")c) a WHERE a.rownum <= :end) b WHERE b.recnum >= :start");
	    paramStack.push(new Object[]{ "start", start});
	    paramStack.push(new Object[]{ "end", end});
	    
	    LOG.trace("retrieveTaskListMs SQL: {}, params[user={}, spv={}, "
	    		+ "branch={}, form={}, start={}, end={}]",
	    		sqlBuilder.toString(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
	    
	    Object[][] sqlParams = new Object[paramStack.size()][2];
	    for (int i = 0; i < paramStack.size(); i++) {
			Object[] objects = paramStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNativeString(
	    		sqlBuilder.toString(), sqlParams);
	    LOG.info("retrieveTaskListMs.size: {}, params[user={}, spv={}, "
	    		+ "branch={}, form={}, start={}, end={}]",
	    		resultList.size(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
		return resultList;
	}
	
	private String getRestrictionStringMsCSV(Stack<Object[]> paramStack, String uuidBranch, 
			String uuidUser, String uuidSpv, int formVersion ) {
		StringBuilder sqlBuilder = new StringBuilder();
		
		sqlBuilder.append("WHERE trth.UUID_FORM = :uuidForm ");
		if (formVersion != 0) {
			sqlBuilder.append("AND trth.FORM_VERSION = :formVersion ");
		}
		if (StringUtils.isNotEmpty(uuidBranch)) {
			sqlBuilder.append("AND trth.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{ "uuidBranch", uuidBranch});
		}
		sqlBuilder.append("AND ((assign_date between :startDate and :endDate) ")
			.append("OR (flag_source = 'MS' and submit_date between :startDate and :endDate)) ");
		if (StringUtils.isNotEmpty(uuidUser)) {
			sqlBuilder.append("AND trth.UUID_MS_USER = :uuidUser ");
			paramStack.push(new Object[]{ "uuidUser", uuidUser});
		}
		sqlBuilder.append("AND msst.STATUS_CODE not in ('A') ");
	    if (StringUtils.isNotEmpty(uuidSpv)) {
	    	sqlBuilder.append("AND ammsu.SPV_ID = :uuidSpv ");
			paramStack.push(new Object[]{ "uuidSpv", uuidSpv});
	    }	
	    
	    return sqlBuilder.toString();
	}
	
	private String getRestrictionBranchMsCSV(Stack<Object[]> paramStack, String uuidUserLogin, String uuidBranchLogin) {
		StringBuilder sqlBuilder = new StringBuilder();
		if (StringUtils.isNotEmpty(uuidUserLogin)) {
			  sqlBuilder.append("JOIN (SELECT keyValue as UUID_BRANCH ")
				.append("FROM dbo.getCabangByLogin(:uuidBranchLogin)) cbl  ")
				.append("on cbl.UUID_BRANCH = trth.UUID_BRANCH ");
			  paramStack.push(new Object[]{ "uuidBranchLogin", uuidBranchLogin});
		}
	    return sqlBuilder.toString();
	}
	
	private List<Map<String, Object>> retrieveTaskListMcCSV(String uuidUser, String uuidSpv, 
			String uuidBranch, String uuidForm, Date startDate, Date endDate, String start, String end,
			String uuidUserLogin, String uuidBranchLogin, int formVersion) {
		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[]{ "uuidForm", uuidForm});
		paramStack.push(new Object[]{ "startDate", startDate});
		paramStack.push(new Object[]{ "endDate", endDate});
		if (formVersion != 0) {
			paramStack.push(new Object[]{ "formVersion", formVersion});
		}
		String retriction = getRetrictionStringMcCSV(paramStack, uuidBranch, uuidUserLogin, 
				uuidBranchLogin, uuidUser, uuidSpv, formVersion);
		String retrictionBranch = getRetrictionBranchMcCSV(paramStack, uuidUserLogin, uuidBranchLogin);
		StringBuilder sqlBuilder = new StringBuilder();
		sqlBuilder.append("SELECT b.* from (SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum ")
				.append("FROM (select c.*, ROW_NUMBER() OVER (order by STATUS_TASK_DESC DESC, DTM_CRT) AS rownum from (");
		sqlBuilder.append("SELECT trth.AGREEMENT_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ")
			.append("ammsuspv.FULL_NAME, ammsu.LOGIN_ID, trth.send_date, trth.submit_date, ") 
			.append("trth.CUSTOMER_NAME, trth.CUSTOMER_PHONE, trth.CUSTOMER_ADDRESS, trth.NOTES, ")
			.append("trlink.UUID_TASK_H_SURVEY, ") 
			.append("trth.LATITUDE, trth.LONGITUDE, trth.UUID_TASK_H, msst.STATUS_CODE, trth.DTM_CRT ")
			.append("FROM TR_TASK_H trth with (nolock) ")
			.append("left join MS_BRANCH msb with (nolock) on trth.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM ")
			.append("left join MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY ")
			.append("left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
			.append("left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER ")
			.append("left join TR_TASKLINK trlink with (nolock) on trth.UUID_TASK_H = trlink.UUID_TASK_H_COLLECT ")
			.append(retrictionBranch);
		
		sqlBuilder.append(retriction);
	    sqlBuilder.append("UNION ALL ");
	    sqlBuilder.append("SELECT trth.AGREEMENT_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ")
			.append("ammsuspv.FULL_NAME, ammsu.LOGIN_ID, trth.send_date, trth.submit_date, ") 
			.append("trth.CUSTOMER_NAME, trth.CUSTOMER_PHONE, trth.CUSTOMER_ADDRESS, trth.NOTES, ")
			.append("trlink.UUID_TASK_H_SURVEY, ") 
			.append("trth.LATITUDE, trth.LONGITUDE, trth.UUID_TASK_H, msst.STATUS_CODE, trth.DTM_CRT ")
			.append("FROM FINAL_TR_TASK_H trth with (nolock) ")
			.append("left join MS_BRANCH msb with (nolock) on trth.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM ")
			.append("left join MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY ")
			.append("left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
			.append("left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER ")
			.append("left join TR_TASKLINK trlink with (nolock) on trth.UUID_TASK_H = trlink.UUID_TASK_H_COLLECT ")
			.append(retrictionBranch);
		sqlBuilder.append(retriction);	
			
			
	    sqlBuilder.append(")c) a WHERE a.rownum <= :end) b WHERE b.recnum >= :start");
	    paramStack.push(new Object[]{ "start", start});
	    paramStack.push(new Object[]{ "end", end});
		
	    LOG.trace("retrieveTaskListMc SQL: {}, params[user={}, spv={}, "
	    		+ "branch={}, form={}, start={}, end={}]",
	    		sqlBuilder.toString(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
	    	    
	    Object[][] sqlParams = new Object[paramStack.size()][2];
	    for (int i = 0; i < paramStack.size(); i++) {
			Object[] objects = paramStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNativeString(
	    		sqlBuilder.toString(), sqlParams);
	    LOG.info("retrieveTaskListMc.size: {}, params[user={}, spv={}, "
	    		+ "branch={}, form={}, start={}, end={}]",
	    		resultList.size(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
		return resultList;
	}
	
	private String getRetrictionStringMcCSV(Stack<Object[]> paramStack, String uuidBranch, 
			String uuidUserLogin,  String uuidBranchLogin, String uuidUser, String uuidSpv, int formVersion) {
		StringBuilder sqlBuilder = new StringBuilder();
		
		sqlBuilder.append("WHERE trth.UUID_FORM = :uuidForm ");
		if(formVersion != 0){
			sqlBuilder.append("AND trth.FORM_VERSION = :formVersion ");
		}
		if (StringUtils.isNotEmpty(uuidBranch)) {
			sqlBuilder.append("AND trth.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{ "uuidBranch", uuidBranch});
		}
		sqlBuilder.append("AND assign_date between :startDate and :endDate ");
		if (StringUtils.isNotEmpty(uuidUser)) {
			sqlBuilder.append("AND trth.UUID_MS_USER = :uuidUser ");
			paramStack.push(new Object[]{ "uuidUser", uuidUser});
		}
		sqlBuilder.append("AND msst.STATUS_CODE not in ('A') ");
	    if (StringUtils.isNotEmpty(uuidSpv)) {
	    	sqlBuilder.append("AND ammsu.SPV_ID = :uuidSpv ");
			paramStack.push(new Object[]{ "uuidSpv", uuidSpv});
	    }
	    String result = sqlBuilder.toString();
	    return result;
	}
	
	private String getRetrictionBranchMcCSV(Stack<Object[]> paramStack, String uuidUserLogin,  String uuidBranchLogin) {
		StringBuilder sqlBuilder = new StringBuilder();
		if (StringUtils.isNotEmpty(uuidUserLogin)) {
			   sqlBuilder.append("JOIN (SELECT keyValue as UUID_BRANCH ")
					.append("FROM dbo.getCabangByLogin(:uuidBranchLogin)) cbl  ")
					.append("on cbl.UUID_BRANCH = trth.UUID_BRANCH ");
			   paramStack.push(new Object[]{ "uuidBranchLogin", uuidBranchLogin});
		}
	    String result = sqlBuilder.toString();
	    return result;
	}
	
	private List<Map<String, Object>> retrieveTaskListLimit(String subsystemCode, String uuidUser,
			String uuidSpv, String uuidBranch, String uuidForm, Date startDate, Date endDate, 
			String start, String end, String uuidUserLogin, String uuidBranchLogin, int formVersion) {
		if (GlobalVal.SUBSYSTEM_MS.equals(subsystemCode)) {
			return this.retrieveTaskListMsCSV(uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, 
					endDate, start, end, uuidUserLogin, uuidBranchLogin, formVersion);
		}
		else if (GlobalVal.SUBSYSTEM_MC.equals(subsystemCode)) {
			return this.retrieveTaskListMcCSV(uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, 
					endDate, start, end, uuidUserLogin, uuidBranchLogin, formVersion);
		}
		else if (GlobalVal.SUBSYSTEM_MT.equals(subsystemCode)) {
			return this.retrieveTaskListMtCSV(uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, 
					endDate, start, end, uuidUserLogin, uuidBranchLogin);
		}
		else {
			//ILLEGAL subsystem			
			return Collections.emptyList(); 
		}
	}
	
	private void writeRecordsCSVMc(CSVWriter writer,List<Map<String, Object>> listData, AuditContext callerId,
			List idOfQuestionSet) {
		final String baseUrlImage = SpringPropertiesUtils.getProperty(GlobalKey.WS_BASE_PUBLIC) 
				+ apiViewImagePath;
		final String datetimeFormat = "yyyy-MM-dd HH:mm";
		
		for (int i = 0; i < listData.size(); i++) {
			Map<String,Object> tmpData = listData.get(i);
			List<String> listValue = new ArrayList<>();
			for (int j = 0; j < tmpData.size() - 5; j++) {
				String value = StringUtils.EMPTY;
				if (j == 6 || j == 7) {
					if (tmpData.get("d"+j) != null) {
						value = DateFormatUtils.format((Date) tmpData.get("d"+j), datetimeFormat);
					}
				}
				else {
					value = (tmpData.get("d"+j) == null) ? StringUtils.EMPTY 
							: tmpData.get("d"+j).toString();
				}
				
				value = value.replace("\n", " ");
				value = "=\""+value+"\"";
				listValue.add(StringUtils.upperCase(value));
			}
			
			String statusCode = (String) tmpData.get("d"+(tmpData.size()-4));
			boolean isLoadIntVal = (StringUtils.equals(statusCode, GlobalVal.COLLECTION_STATUS_TASK_CANCELED) ||
					StringUtils.equals(statusCode, GlobalVal.COLLECTION_STATUS_TASK_DELETED));
			
			if (PropertiesHelper.isTaskDJson()) {
				 this.retrieveRecordsJson(Long.valueOf(tmpData.get("d"+(tmpData.size()-5)).toString()),
						 baseUrlImage, isLoadIntVal, listValue, callerId);
			}
			else {
				this.retrieveRecordsRow(Long.valueOf(tmpData.get("d"+(tmpData.size()-5)).toString()),
						baseUrlImage, isLoadIntVal, listValue, true, idOfQuestionSet);
			}
			
			Object[] tmpArray = listValue.toArray();
			writer.writeNext(Arrays.copyOf(tmpArray, tmpArray.length, String[].class));
		}
	}
	
	private List<Map<String, Object>> retrieveTaskListMtCSV(String uuidUser, String uuidSpv, 
			String uuidBranch, String uuidForm, Date startDate, Date endDate, String start, String end,
			String uuidUserLogin, String uuidBranchLogin) {
		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[]{ "uuidForm", uuidForm});
		paramStack.push(new Object[]{ "startDate", startDate});
		paramStack.push(new Object[]{ "endDate", endDate});
		
		StringBuilder sqlBuilder = new StringBuilder();
		String retriction = getRetrictionStringMtCSV(paramStack, uuidBranch, uuidUserLogin, 
				uuidBranchLogin, uuidUser, uuidSpv);
		String retrictionBranch = getRetrictionBranchMtCSV(paramStack, uuidUserLogin, uuidBranchLogin);
		
		sqlBuilder.append("SELECT b.* from (SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum ")
				.append("FROM (select c.*, ROW_NUMBER() OVER (order by STATUS_TASK_DESC DESC, DTM_CRT) AS rownum from (");
		sqlBuilder.append("SELECT msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ")
			.append("ammsuspv.FULL_NAME, ammsu.LOGIN_ID, msb.branch_name, trth.send_date, trth.submit_date, ") 
			.append("ml.location_name, trth.CUSTOMER_PHONE, ml.location_address, trth.NOTES, ") 
			.append("trth.LATITUDE, trth.LONGITUDE, trth.UUID_TASK_H, msst.STATUS_CODE, trth.DTM_CRT ")
			.append("FROM TR_TASK_H trth with (nolock) ")
			.append("left join MS_BRANCH msb with (nolock) on trth.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM ")
			.append("left join MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY ")
			.append("left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
			.append("left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER ")
			.append("left join ms_location ml with (nolock) on ml.uuid_location = trth.uuid_location ")
			.append(retrictionBranch);
		
		sqlBuilder.append(retriction);
	    sqlBuilder.append("UNION ALL ");
		sqlBuilder.append("SELECT msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ")
			.append("ammsuspv.FULL_NAME, ammsu.LOGIN_ID, msb.branch_name, trth.send_date, trth.submit_date, ") 
			.append("ml.location_name, trth.CUSTOMER_PHONE, ml.location_address, trth.NOTES, ") 
			.append("trth.LATITUDE, trth.LONGITUDE, trth.UUID_TASK_H, msst.STATUS_CODE, trth.DTM_CRT ")
			.append("FROM FINAL_TR_TASK_H trth with (nolock) ")
			.append("left join MS_BRANCH msb with (nolock) on trth.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM ")
			.append("left join MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY ")
			.append("left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
			.append("left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER ")
			.append("left join ms_location ml with (nolock) on ml.uuid_location = trth.uuid_location ")
			.append(retrictionBranch);
		sqlBuilder.append(retriction);
		
	    sqlBuilder.append(")c) a WHERE a.rownum <= :end) b WHERE b.recnum >= :start");
	    paramStack.push(new Object[]{ "start", start});
	    paramStack.push(new Object[]{ "end", end});
		
	    LOG.trace("retrieveTaskListMt SQL: {}, params[user={}, spv={}, "
	    		+ "branch={}, form={}, start={}, end={}]",
	    		sqlBuilder.toString(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
	    	    
	    Object[][] sqlParams = new Object[paramStack.size()][2];
	    for (int i = 0; i < paramStack.size(); i++) {
			Object[] objects = paramStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNativeString(
	    		sqlBuilder.toString(), sqlParams);
	    LOG.info("retrieveTaskListMt.size: {}, params[user={}, spv={}, "
	    		+ "branch={}, form={}, start={}, end={}]",
	    		resultList.size(), uuidUser, uuidSpv, uuidBranch, uuidForm, startDate, endDate);
		return resultList;
	}
	
	private String getRetrictionStringMtCSV(Stack<Object[]> paramStack, String uuidBranch, 
			String uuidUserLogin,  String uuidBranchLogin, String uuidUser, String uuidSpv ) {
		StringBuilder sqlBuilder = new StringBuilder();
		
		sqlBuilder.append("WHERE trth.UUID_FORM = :uuidForm ");
		if (StringUtils.isNotEmpty(uuidBranch)) {
			sqlBuilder.append("AND trth.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{ "uuidBranch", uuidBranch});
		}
		sqlBuilder.append("AND assign_date between :startDate and :endDate ");
		if (StringUtils.isNotEmpty(uuidUser)) {
			sqlBuilder.append("AND trth.UUID_MS_USER = :uuidUser ");
			paramStack.push(new Object[]{ "uuidUser", uuidUser});
		}
		sqlBuilder.append("AND msst.STATUS_CODE not in ('A') ");
	    if (StringUtils.isNotEmpty(uuidSpv)) {
	    	sqlBuilder.append("AND ammsu.SPV_ID = :uuidSpv ");
			paramStack.push(new Object[]{ "uuidSpv", uuidSpv});
	    }	
	    String result = sqlBuilder.toString();
	    return result;
	}
	
	private String getRetrictionBranchMtCSV(Stack<Object[]> paramStack,  
			String uuidUserLogin, String uuidBranchLogin) {
		StringBuilder sqlBuilder = new StringBuilder();
		if (StringUtils.isNotEmpty(uuidUserLogin)) {
			sqlBuilder.append("JOIN (SELECT keyValue as UUID_BRANCH ")
				.append("FROM dbo.getCabangByLogin(:uuidBranchLogin)) cbl  ")
				.append("on cbl.UUID_BRANCH = trth.UUID_BRANCH ");
			paramStack.push(new Object[]{ "uuidBranchLogin", uuidBranchLogin});
		}
	    String result = sqlBuilder.toString();
	    return result;
	}
	
	private void writeRecordsCSVMt(CSVWriter writer, List<Map<String, Object>> listData) {
		final String baseUrlImage = SpringPropertiesUtils.getProperty(GlobalKey.WS_BASE_PUBLIC) + apiViewImagePath;
		
		for (int i = 0; i < listData.size();i++) {
			Map<String,Object> tmpData = listData.get(i);
			List<String> listValue = new ArrayList<>();
			for (int j = 0; j < tmpData.size() - 5; j++) {
				String value = String.valueOf(tmpData.get("d"+j));
				value = value.replace("\n", " ");
				value = "=\""+value+"\"";
				listValue.add(StringUtils.upperCase(value));
			}
			
			String statusCode = (String) tmpData.get("d"+(tmpData.size()-4));
			boolean isLoadIntVal = (StringUtils.equals(statusCode, GlobalVal.COLLECTION_STATUS_TASK_CANCELED) ||
					StringUtils.equals(statusCode, GlobalVal.COLLECTION_STATUS_TASK_DELETED));
			
			Object[][] paramAnswerSet = { {"uuidTaskH", tmpData.get("d"+(tmpData.size()-5))} }; 
			List<Map<String, Object>> answerSet = this.getManagerDAO().selectAllNative(
					"report.download.getTaskAnswersCSV", paramAnswerSet, null);
			
			String uuidQuestionBefore = StringUtils.EMPTY;
			for (int j = 0; j < answerSet.size(); j++) {
				Map<String, Object> answerMap = answerSet.get(j);
				String answerType = (String) answerMap.get("d12");
				String cellValue=StringUtils.EMPTY;
				String uuidQuestionCurrent =  (String) answerMap.get("d0");
				
				if ("1".equals((String) answerMap.get("d8"))) { //hasImage
//					cellValue = hasImageValue; //change to url image
					String queryParam = (FLAG_FINAL_TABLE.equals((String) answerMap.get("d15")))
							? QUERY_PARAM_FINAL : StringUtils.EMPTY;
					cellValue = baseUrlImage + (String) answerMap.get("d16") + queryParam;
				} 
				else if ("0".equals((String) answerMap.get("d8"))) {
					cellValue = StringUtils.EMPTY;
				} 
				else if ("01".equals((String) answerMap.get("d8"))) { //answertype = LOCATION
					if (null != answerMap.get("d5") && null != answerMap.get("d6") &&
							null != answerMap.get("d7")) {
						String latitude = answerMap.get("d5").toString();
						String longitude = answerMap.get("d6").toString();
						String acc = answerMap.get("d7").toString();
						cellValue = StringUtils.join(latitude, ", ", longitude, " accuracy: ", acc, "m");
					}
				} 
				else {
					String txtAnswer = StringUtils.EMPTY;
					String code = StringUtils.EMPTY;
					String option = StringUtils.EMPTY;
					
					//check if get INT
					if (isLoadIntVal) {
						txtAnswer = StringUtils.upperCase((String) answerMap.get("d9"));
						code = (String) answerMap.get("d11");
						option = StringUtils.upperCase((String) answerMap.get("d10"));
					} 
					else {
						txtAnswer = StringUtils.upperCase((String) answerMap.get("d2"));
						code = (String) answerMap.get("d3");
						option = StringUtils.upperCase((String) answerMap.get("d4"));
					}
					
					if (StringUtils.isNotBlank(code)) {
						MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(code));
						code = msLov.getCode();
						msLov = null;
					}
															
					if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType) || 
							GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType) ||
							GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
						if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
							if (StringUtils.isNotEmpty(cellValue)) {
								cellValue += ", ";
							}
							if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType)) {
								cellValue += code + " | " + option;
							} 
							else if (GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType)) {
								cellValue += code + " | " + option + " - " + txtAnswer;
							} 
							else if(GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
								cellValue += code + " | " + option + " - " + txtAnswer;
							}
						}
					} 
					else if (GlobalVal.ANSWER_TYPE_DROPDOWN.equals(answerType) ||
							GlobalVal.ANSWER_TYPE_RADIO.equals(answerType)) {
						if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
							cellValue = code + " | " + option;
						}
					} 
					else if (GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(answerType) ||
							GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(answerType)) {
						if (!(StringUtils.isBlank(code) || StringUtils.isBlank(option))) {
							cellValue = code + " | " + option + " - " + txtAnswer;
						}
					} 
					else if (GlobalVal.ANSWER_TYPE_TIME.equals(answerType)) {
						cellValue = changeDateFormat(txtAnswer, "HH:mm:ss", DATE_FORMAT_TM);
					} 
					else if (GlobalVal.ANSWER_TYPE_DATETIME.equals(answerType)) {
						cellValue = changeDateFormat(txtAnswer, "dd/MM/yyyy HH:mm:ss", DATE_FORMAT_DTM);
					} 
					else if (GlobalVal.ANSWER_TYPE_DATE.equals(answerType)) {
						cellValue = changeDateFormat(txtAnswer, "dd/MM/yyyy", DATE_FORMAT_DT);
					} 
					else if (GlobalVal.ANSWER_TYPE_TEXT_MULTILINE.equals(answerType)) {
						String[] temp = StringUtils.split(txtAnswer,"\n");
						cellValue = StringUtils.join(temp, " ");
					} 
					else {
						cellValue = txtAnswer;
					}
				}
				if (null == cellValue) {
					cellValue=StringUtils.EMPTY;
				}
				cellValue = cellValue.replace("\n", " ");
				
				if (uuidQuestionCurrent.equalsIgnoreCase(uuidQuestionBefore)) {
					String temp = listValue.get(listValue.size()-1);
					temp += ";"+cellValue;
					listValue.set(listValue.size()-1, temp);
				} 
				else {
					listValue.add(cellValue);
				}
				uuidQuestionBefore = uuidQuestionCurrent;
				answerMap = null;
			}
			
			Object[] tmpArray = listValue.toArray();
			writer.writeNext(Arrays.copyOf(tmpArray, tmpArray.length, String[].class));
		}
	}

	@Override
	public Map<String, Object> getComboPerForm(AmMsuser amMsuser, String formName, AuditContext callerId) {
		Map<String, Object> resultMap = new HashMap();
		
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
			
			//Combo List Branch
		String[][] paramBranch = { {"uuidBranch", String.valueOf(amMsuser.getMsBranch().getUuidBranch())} };
		List listBranchCombo = this.getManagerDAO().selectAllNative(
						"report.download.getlistbranch", paramBranch, null);
			
		Map<String, String> branchCombo = new HashMap<>();
		if (!listBranchCombo.isEmpty()) {
			for (int i = 0; i < listBranchCombo.size(); i++) {
				Map temp = (Map) listBranchCombo.get(i);
				branchCombo.put(String.valueOf(temp.get("d0")),
						(String) temp.get("d2")+" - "+(String) temp.get("d1"));
			}
		}
			
		Map<String, String> userCombo = new HashMap<>();
			
			//Combo List Form
		String[][] paramsForm = { { paramIsActive, "1" }, {paramSubsystemName, subsysName}, {"formName", formName}};
		List<Map<String, Object>> listFormCombo = this.getManagerDAO().selectAllNativeString(
				"SELECT form.UUID_FORM, form.FORM_NAME "
				+ "FROM MS_FORM form WITH (NOLOCK) "
					+ "JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
					+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
				+ "WHERE form.IS_ACTIVE = :isActive "
					+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName "
					+ "AND form.FORM_NAME = :formName ", paramsForm);
		
			
		Map<String, String> formCombo = new HashMap<>();
		if (!listFormCombo.isEmpty()) {
			for (int i = 0; i < listFormCombo.size(); i++) {
				Map<String, Object> mp = listFormCombo.get(i);
				formCombo.put( String.valueOf(mp.get("d0")), String.valueOf(mp.get("d1")) );
			}
		}
		
		Map<String, String> formVersion = new HashMap<String, String>();
		formVersion.put("", "-- Choose One --");
		resultMap.put("branchCombo", branchCombo);
		resultMap.put("userCombo", userCombo);
		resultMap.put("formCombo", listFormCombo);
		resultMap.put("formVersion", formVersion);
		return resultMap;
	}

	@Override
	public Map<String, Object> exportTaskPreSurvey(Map<String, String> searchParams) {
		Map<String, Object> result = new HashMap<>();
		byte[] data = null;
		Gson gson = new Gson(); 
		String rptParams = gson.toJson(searchParams);
		
		try {
			String excelPath = getExcelPath(null) + SystemUtils.FILE_SEPARATOR;
			String filePath = searchParams.get("folder") + SystemUtils.FILE_SEPARATOR
								+ searchParams.get(paramFileName) + ".csv";
			
			String fullPath = excelPath + filePath;
			File file = new File(fullPath);
			
			if (file.exists()) {//Check if file already generated
				Path pathsolute = Paths.get(fullPath);
				data = Files.readAllBytes(pathsolute);
			} 
			else {
				Object[][] reportParams = {{Restrictions.eq("rptType", GlobalVal.RPTTYPE_PRESURVEY)}, {Restrictions.eq("rptName", searchParams.get(paramFileName))}};
				TrReportresultlog reportLog = this.getManagerDAO().selectOne(TrReportresultlog.class, reportParams);
				
				if(reportLog != null) {
					//If has ReportLog but no file then it will re-generated
					reportLog.setDtmRequest(new Date());
					reportLog.setRptParams(rptParams);
					reportLog.setProcessStartTime(null);
					reportLog.setProcessFinishTime(null);
					reportLog.setProcessDurationSeconds(null);
					reportLog.setReportFileLocation(null);
					reportLog.setProcessStatus("0");
				} else {
					//If has no ReportLog then will make one
					AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(searchParams.get("uuidLoginId")));
					TrReportresultlog newReportLog = new TrReportresultlog();
					
					newReportLog.setAmMsuser(user);
					newReportLog.setDtmRequest(new Date());
					newReportLog.setRptName(searchParams.get(paramFileName));
					newReportLog.setRptType(GlobalVal.RPTTYPE_PRESURVEY);
					newReportLog.setRptParams(rptParams);
					newReportLog.setProcessStatus("0");
					this.getManagerDAO().insert(newReportLog);
				}
				return null;
			}
		} catch (IOException e) {
			LOG.error("Error while create excel file. SearchParams=[{}], ErrorMessage=[{}]", rptParams, e);
		}
		
		result.put("filename", searchParams.get(paramFileName));
		result.put("result", data);
		return result;
	}
}
