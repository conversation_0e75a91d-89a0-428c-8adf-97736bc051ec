package com.adins.mss.businesslogic.impl.common;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ReportResultLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.exceptions.UploadTaskException;
import com.adins.mss.exceptions.UploadTaskException.Reason;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.util.CipherTool;

@SuppressWarnings("rawtypes")
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportResultLogic extends BaseLogic implements 
		ReportResultLogic, MessageSourceAware {
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	@Autowired
	private MessageSource messageSource;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List getListReportResult(AuditContext auditContext, Object params, Object orders) {
		List list = this.getManagerDAO().selectAllNative("report.result.getList", params, orders);
		if("1".equals(this.link_encrypt)){
			//d0 = seqNo
			List newList = new ArrayList();
			for(int i=0; i<list.size(); i++){
				Map map = (Map) list.get(i);
				String[] toBeEncrypt = {map.get("d0").toString()};
				map.put("d0", CipherTool.encryptData(toBeEncrypt).get(0).toString());
				newList.add(map);
			}
			return newList;
		}
		return list;
	}

	@Override
	public Integer getListReportResultCount(AuditContext auditContext, Object params) {
		Integer count = (Integer) this.getManagerDAO().selectOneNative("report.result.getListCount", params);
		return count;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void cancelRequest(AuditContext auditContext, String seqNo) {
		Object[][] param = { {Restrictions.eq("seqno", Long.valueOf(seqNo))} };
		TrReportresultlog trReportresultlog = this.getManagerDAO().selectOne(TrReportresultlog.class, param);
		if ("0".equals(trReportresultlog.getProcessStatus()) 
				&& null == trReportresultlog.getProcessStartTime()) {
			trReportresultlog.setProcessStatus("2");
			this.getManagerDAO().update(trReportresultlog);
		} 
		else {
			throw new ChangeException(this.messageSource.getMessage(
					"businesslogic.report.taskprocess", null, this.retrieveLocaleAudit(auditContext)));
		}
	}

	@Override
	public Map<String, Object> downloadReportResult(AuditContext auditContext, String seqNo) {
		Map<String, Object> mapResult = new HashMap<String, Object>();
		Object[][] param = { {Restrictions.eq("seqno", Long.valueOf(seqNo))} };
		TrReportresultlog trReportresultlog = this.getManagerDAO().selectOne(TrReportresultlog.class, param);
		byte[] exp = getReportResultCSV(trReportresultlog, auditContext);
		String[] splitFilename = StringUtils.split(trReportresultlog.getReportFileLocation(), 
				SystemUtils.FILE_SEPARATOR);
		String filename = splitFilename[splitFilename.length-1];
		
		mapResult.put("errorResult", exp);
		mapResult.put("filename", filename);
		return mapResult;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteRequest(AuditContext auditContext, String seqNo) {
		Object[][] param = { {Restrictions.eq("seqno", Long.valueOf(seqNo))} };
		TrReportresultlog trReportresultlog = this.getManagerDAO().selectOne(TrReportresultlog.class, param);
		this.getManagerDAO().delete(trReportresultlog);
	}
	
	private byte[] getReportResultCSV(TrReportresultlog trReportresultlog, AuditContext callerId) {
		byte[] result = null;
		if (trReportresultlog != null) {
			try {
				File file = new File(trReportresultlog.getReportFileLocation());
				if (file.exists()) {
					Path path = Paths.get(trReportresultlog.getReportFileLocation());
					result = Files.readAllBytes(path);
				} 
				else {
					throw new UploadTaskException(
							this.messageSource.getMessage(
							"businesslogic.report.nocsv", null, this.retrieveLocaleAudit(callerId)), 
							Reason.ERROR_NOT_EXIST);
				}
			} 
			catch (IOException ex) {
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.report.errordata", null, this.retrieveLocaleAudit(callerId)), Reason.ERROR_PARSING);
			}
		}
		return result;
	}
}
