package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ReportSlaLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportSlaLogic extends BaseLogic 
		implements ReportSlaLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericReportUserMemberLogic.class);
	private static final String[] HEADER_SUMMARY = { 
		"Branch",
		"Total Task Submit", 
		"New-Download (HH:mm)", 
		"Download-Read (HH:mm)", 
		"Read-On Progress (HH:mm)", 
		"On Progress-Submit (HH:mm)", 
		"Average Time Download-Submit (HH:mm)",
		"Total Task Under SLA",
		"Average Time Under SLA (HH:mm)",
		"Total Task Over SLA",
		"Average Time Over SLA (HH:mm)"
	};
	private static final int[] SUMMARY_COLUMN_WIDTH = { 
		30 * 256, 
		20 * 256,
		20 * 256,
		20 * 256, 
		20 * 256, 
		20 * 256,
		20 * 256, 
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256
	};
	private static final String[] HEADER_DETAIL = { 
		"No", 
		"Branch", 
		"Surveyor",
		"Application No",
		"Assignment Date",
		"Promised Date",
		"Start Date",
		"Send Date",
		"Submitted Date",
		"PTS-Survey Start (HH:mm)", 
		"Survey Start-Submit (HH:mm)", 
		"Total SLA (HH:mm)",
		"Form Name"
	};
	private static final int[] DETAIL_COLUMN_WIDTH = { 
		10 * 256, 
		30 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256,
		20 * 256
	};
	
	@Autowired
    private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	@Override
	public List getComboBranch(String branchId, AuditContext callerId) {
		List result = null;
		String[][] params = {{ "branchId", branchId }};
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}

	@Override
	public List getReportSla(String subsystemId, String branchId,
			String startDate, String endDate, AuditContext callerId, String uuidForm) {
		List result = new ArrayList<>();
		Stack<Object[]> paramsStack = new Stack<>();
		
		StringBuilder paramsQueryString = this.sqlPagingBuilder(uuidForm, paramsStack);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("WITH hBranch as (")
				.append("SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)")
				.append("),")
				.append("N as (")
				.append("select DATEDIFF(minute, assign_date, download_date) as total_minutes_ad,")
				.append("DATEDIFF(minute, download_date, read_date) as total_minutes_dr,")
				.append("DATEDIFF(minute, read_date, start_dtm) as total_minutes_rp,")
				.append("DATEDIFF(minute, start_dtm, submit_date) as total_minutes_ps,")
				.append("DATEDIFF(minute, download_date, submit_date) as total_minutes_s,")
				.append("DATEDIFF(minute, assign_date, submit_date) as total_minutes_sla,")
				.append("cbl.uuid_branch, cbl.branch_code, cbl.branch_name")
				.append(" FROM TR_TASK_H ttrh with (nolock)")
				.append(" JOIN ms_statustask mst with (nolock) on ttrh.uuid_status_task = mst.uuid_status_task")
				.append(" JOIN MS_FORM form with (nolock) ON ttrh.UUID_FORM = form.UUID_FORM")
				.append(" JOIN hBranch cbl on cbl.UUID_BRANCH = ttrh.UUID_BRANCH")
				.append(" WHERE ttrh.ASSIGN_DATE between :startDate and :endDate")
				.append(" and mst.status_code != 'U'")
				.append(" and mst.uuid_ms_subsystem = :subsystemId")
				.append(" and submit_date is not null")
				.append(paramsQueryString)
				.append(" UNION ALL ")
				.append("select DATEDIFF(minute, assign_date, download_date) as total_minutes_ad,")
				.append("DATEDIFF(minute, download_date, read_date) as total_minutes_dr,")
				.append("DATEDIFF(minute, read_date, start_dtm) as total_minutes_rs,")
				.append("DATEDIFF(minute, start_dtm, submit_date) as total_minutes_ps,")
				.append("DATEDIFF(minute, download_date, submit_date) as total_minutes_s,")
				.append("DATEDIFF(minute, assign_date, submit_date) as total_minutes_sla,")
				.append("cbl.uuid_branch, cbl.branch_code, cbl.branch_name")
				.append(" FROM FINAL_TR_TASK_H fttrh with (nolock)")
				.append(" JOIN ms_statustask mst with (nolock) on fttrh.uuid_status_task = mst.uuid_status_task")
				.append(" JOIN MS_FORM form with (nolock) ON fttrh.UUID_FORM = form.UUID_FORM")
				.append(" JOIN hBranch cbl on cbl.UUID_BRANCH = fttrh.UUID_BRANCH")
				.append(" WHERE fttrh.ASSIGN_DATE between :startDate and :endDate")
				.append(" and mst.status_code != 'U'")
				.append(" and mst.uuid_ms_subsystem = :subsystemId")
				.append(" and submit_date is not null")
				.append(paramsQueryString)
				.append(") ")
				.append("select a.branch_code, a.branch_name, isnull(a.totalSubmitTask,0) totalSubmitTask,") 
				.append("isnull(a.hour_dr,0) hour_dr, isnull(a.min_dr,0) min_dr, isnull(a.hour_rp,0) hour_rp,")
				.append("isnull(a.min_rp,0) min_rp, isnull(a.hour_ps,0) hour_ps, isnull(a.min_ps,0) min_ps,") 
				.append("isnull(a.hour_s,0) hour_s, isnull(a.min_s,0) min_s, isnull(b.task_bf_sla,0) task_bf_sla,") 
				.append("isnull(b.hour_bf_sla,0) hour_bf_sla, isnull(b.min_bf_sla,0) min_bf_sla,")
				.append("isnull(c.task_ov_sla,0) task_ov_sla, isnull(c.hour_ov_sla,0) hour_ov_sla,") 
				.append("isnull(c.min_ov_sla,0) min_ov_sla, a.uuid_branch, isnull(a.hour_ad,0) hour_ad,") 
				.append("isnull(a.min_ad,0) min_ad")
				.append(" FROM (")
				.append("select uuid_branch, branch_code, branch_name, count(*) totalSubmitTask,")
				.append("COALESCE(AVG(N.total_minutes_ad)/60, 0) as hour_ad,")
				.append("COALESCE(AVG(N.total_minutes_ad)%60, 0) as min_ad,")
				.append("COALESCE(AVG(N.total_minutes_dr)/60, 0) as hour_dr,")
				.append("COALESCE(AVG(N.total_minutes_dr)%60, 0) as min_dr,")
				.append("COALESCE(AVG(N.total_minutes_rp)/60, 0) as hour_rp,")
				.append("COALESCE(AVG(N.total_minutes_rp)%60, 0) as min_rp,")
				.append("COALESCE(AVG(N.total_minutes_ps)/60, 0) as hour_ps,")
				.append("COALESCE(AVG(N.total_minutes_ps)%60, 0) as min_ps,")
				.append("COALESCE(AVG(N.total_minutes_s)/60, 0) as hour_s,")
				.append("COALESCE(AVG(N.total_minutes_s)%60, 0) as min_s")
				.append(" FROM N with (nolock)")
				.append(" GROUP BY branch_code, branch_name, uuid_branch")
				.append(") a")
				.append(" left outer join (")
				.append("select branch_code, branch_name, count(*) task_bf_sla,")
				.append("AVG(total_minutes_sla)/60 as hour_bf_sla,")
				.append("AVG(total_minutes_sla)%60 as min_bf_sla")
				.append(" FROM N with (nolock)")
				.append(" WHERE total_minutes_sla/60.0 between 0 and (")
				.append("select gs_value from am_generalsetting where gs_code = 'SLA_TIME'")
				.append(")")
				.append(" GROUP BY branch_code, branch_name")
				.append(") b on a.branch_code = b.branch_code")
				.append(" left outer join (")
				.append("select branch_code, branch_name, count(*) task_ov_sla,")
				.append("AVG(total_minutes_sla)/60 as hour_ov_sla,")
				.append("AVG(total_minutes_sla)%60 as min_ov_sla")
				.append(" FROM N with (nolock)")
				.append(" WHERE total_minutes_sla/60.0 > (")
				.append("select gs_value from am_generalsetting where gs_code = 'SLA_TIME'")
				.append(")")
				.append(" group by branch_code, branch_name")
				.append(") c on a.branch_code = c.branch_code")
				.append(" order by BRANCH_CODE");
		
		paramsStack.push(new Object[]{"branchId", branchId});	
		paramsStack.push(new Object[]{"subsystemId", Long.valueOf(subsystemId)});
		paramsStack.push(new Object[]{"startDate", startDate});
		paramsStack.push(new Object[]{"endDate", endDate});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    List resultReportSla = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
			
		for (int i = 0; i < resultReportSla.size(); i++) {
			Map temp = (Map) resultReportSla.get(i);
			Map tempResult = new HashMap();
				
			tempResult.put("d0", temp.get("d0").toString() + " - " + temp.get("d1").toString());
			tempResult.put("d1", temp.get("d2").toString());
			tempResult.put("d2", String.format("%02d", Integer.parseInt(temp.get("d18").toString())) + " : " 
					+ String.format("%02d", Integer.parseInt(temp.get("d19").toString())));
			tempResult.put("d3", String.format("%02d", Integer.parseInt(temp.get("d3").toString())) + " : " 
					+ String.format("%02d", Integer.parseInt(temp.get("d4").toString())));
			tempResult.put("d4", String.format("%02d", Integer.parseInt(temp.get("d5").toString())) + " : " 
					+ String.format("%02d", Integer.parseInt(temp.get("d6").toString())));
			tempResult.put("d5", String.format("%02d", Integer.parseInt(temp.get("d7").toString())) + " : " 
					+ String.format("%02d", Integer.parseInt(temp.get("d8").toString())));
			tempResult.put("d6", String.format("%02d", Integer.parseInt(temp.get("d9").toString())) + " : " 
					+ String.format("%02d", Integer.parseInt(temp.get("d10").toString())));
			tempResult.put("d7", temp.get("d11").toString());
			tempResult.put("d8", String.format("%02d", Integer.parseInt(temp.get("d12").toString())) + " : " 
					+ String.format("%02d", Integer.parseInt(temp.get("d13").toString())));
			tempResult.put("d9", temp.get("d14").toString());
			tempResult.put("d10", String.format("%02d", Integer.parseInt(temp.get("d15").toString())) + " : " 
					+ String.format("%02d", Integer.parseInt(temp.get("d16").toString())));
			tempResult.put("d11", temp.get("d17").toString());
				
			result.add(i, tempResult);
		}
		return result;
	}
	
	@Override
	public Map getReportSlaDetail(AmMsuser user, String type, String branchId,
			String userId, String startDate, String endDate, AuditContext callerId, String uuidForm) {
		Map result = new HashMap<>();
		
		String branch = "";
		if (!"0".equals(branchId)) {
			MsBranch brc = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(branchId));
			branch = brc.getBranchCode() + " - " + brc.getBranchName();
		}
		else if ("0".equals(type)) {
			MsBranch brc = this.getManagerDAO().selectOne(MsBranch.class, user.getMsBranch().getUuidBranch());
			branch = brc.getBranchCode() + " - " + brc.getBranchName();
		}
		else {
			branch = "All Branches";
		}
		result.put("branch", branch);

		Stack<Object[]> paramsStack = new Stack<>();
			paramsStack.push(new Object[]{"branchId", "0".equals(branchId) ? String.valueOf(user.getMsBranch().getUuidBranch()) : branchId});	
			paramsStack.push(new Object[]{"subsystemId", String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem())});
			paramsStack.push(new Object[]{"startDate", startDate});
			paramsStack.push(new Object[]{"endDate", endDate});
			
		List resultReportSla;
		if ("0".equals(branchId)) {
			 resultReportSla = this.getSlaDetail(userId, uuidForm, paramsStack);
		}
		else {
			 resultReportSla = this.getSlaDetailByBranch(userId, uuidForm, paramsStack);
		}
			
		List resultList = new ArrayList<>();
		for (int i = 0; i < resultReportSla.size(); i++) {
			Map temp = (Map) resultReportSla.get(i);
			Map tempResult = new HashMap();
			tempResult.put("d0", temp.get("d0").toString());
			tempResult.put("d1", temp.get("d1").toString() + " - " + temp.get("d2").toString());
			tempResult.put("d2", temp.get("d3").toString());
			tempResult.put("d3", temp.get("d4").toString());
			tempResult.put("d4", temp.get("d5").toString());
			tempResult.put("d5", temp.get("d6").toString());
			tempResult.put("d6", temp.get("d7").toString());
			tempResult.put("d7", temp.get("d8").toString());
			tempResult.put("d8", temp.get("d9").toString());
			tempResult.put("d9", String.format("%02d", Math.abs(Integer.parseInt(temp.get("d10").toString()))) + " : " + String.format("%02d", Math.abs(Integer.parseInt(temp.get("d11").toString()))));
			tempResult.put("d10", String.format("%02d", Math.abs(Integer.parseInt(temp.get("d12").toString()))) + " : " + String.format("%02d", Math.abs(Integer.parseInt(temp.get("d13").toString()))));
			tempResult.put("d11", String.format("%02d", Math.abs(Integer.parseInt(temp.get("d14").toString()))) + " : " + String.format("%02d", Math.abs(Integer.parseInt(temp.get("d15").toString()))));
			tempResult.put("d12", temp.get("d16").toString());
			tempResult.put("d13", temp.get("d17").toString());
			tempResult.put("d14", temp.get("d18").toString());
			resultList.add(i, tempResult);
		}
		result.put("resultList", resultList);
		
		return result;
	}

	private StringBuilder sqlPagingBuilder(String uuidForm, Stack<Object[]> paramStack) {
		StringBuilder sb = new StringBuilder();

		if (!StringUtils.equals("%", uuidForm)) {
			sb.append(" and form.UUID_FORM = :uuidForm ");
			paramStack.push(new Object[]{"uuidForm", uuidForm });	
		}		
		return sb;
	}
	
	private StringBuilder sqlPagingBuilderDetail(String userId, String uuidForm, Stack<Object[]> paramStack) {
		StringBuilder sb = new StringBuilder();
		if ((!StringUtils.equals("", userId)) || (!userId.isEmpty())) {
			sb.append(" and N.uuid_ms_user = :userId ");
			paramStack.push(new Object[]{"userId", userId });	
		}
		if (!StringUtils.equals("%", uuidForm)) {
			sb.append(" and form.UUID_FORM = :uuidForm ");
			paramStack.push(new Object[]{"uuidForm", uuidForm });	
		}		
		return sb;
	}

	private List getSlaDetail(String userId, String uuidForm, Stack<Object[]> paramStack) {
		Stack<Object[]> paramsStack1 = paramStack;
		StringBuilder paramsQueryString = this.sqlPagingBuilderDetail(userId, uuidForm, paramsStack1);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append(" WITH N as ( ")
				.append("select *  ")
				.append("from TR_TASK_H ttrh with(nolock) ")
				.append("where ttrh.ASSIGN_DATE between :startDate and :endDate ")
				.append("UNION ALL ")
				.append("select *  ")
				.append("from FINAL_TR_TASK_H fttrh with(nolock) ")
				.append("where fttrh.ASSIGN_DATE between :startDate and :endDate ")
				.append(" ) ")
				.append("select ROW_NUMBER() OVER(ORDER BY msb.branch_code ASC) rownum, branch_code, branch_name, full_name, ")
				.append("isnull(appl_no,''), format(assign_date,'yyyy-MM-dd hh:mm:ss') as assign_date, ")
				.append("COALESCE(format(PROMISE_DATE,'yyyy-MM-dd hh:mm:ss'),'-') as promise_date, ")
				.append("format(start_dtm,'yyyy-MM-dd hh:mm:ss') as strt_dtm, ")
				.append("format(SEND_DATE,'yyyy-MM-dd hh:mm:ss') as send_date, ")
				.append("format(submit_date,'yyyy-MM-dd hh:mm:ss') as submit_date, ")
				.append("COALESCE((DATEDIFF(minute, promise_date, start_dtm))/60,0) as hour_ptson, ")
				.append("COALESCE((DATEDIFF(minute, promise_date, start_dtm))%60,0) as min_ptson, ")
				.append("COALESCE((DATEDIFF(minute, start_dtm, submit_date))/60,0) as hour_ps, ")
				.append("COALESCE((DATEDIFF(minute, start_dtm, submit_date))%60,0) as min_ps, ")
				.append("COALESCE((DATEDIFF(minute, promise_date, submit_date))/60,0) as hour_ptssubmit, ")
				.append("COALESCE((DATEDIFF(minute, promise_date, submit_date))%60,0) as min_ptssubmit, form.FORM_NAME, ")
				.append("CASE  ")
				.append("WHEN COALESCE(DATEDIFF(minute, assign_date, submit_date)/60.0,0.0) >  ")
				.append("(select gs_value from am_generalsetting where gs_code = 'SLA_TIME') THEN '1'  ")
				.append("ELSE '0'  ")
				.append("END AS COLOR, N.uuid_branch ")
				.append("from N with (nolock) ")
				.append("join ms_branch msb with (nolock) on N.uuid_branch = msb.uuid_branch ")
				.append("join am_msuser msu with (nolock) on N.uuid_ms_user = msu.uuid_ms_user ")
				.append("join ms_statustask mst with (nolock) on N.uuid_status_task = mst.uuid_status_task ")
				.append("JOIN MS_FORM form with (nolock) ON N.UUID_FORM = form.UUID_FORM ")
				.append(" JOIN (SELECT keyValue as UUID_BRANCH FROM dbo.getCabangByLogin(:branchId)) cbl on cbl.UUID_BRANCH = N.UUID_BRANCH ")
				.append("where submit_date is not null ")
				.append("and mst.status_code != 'U' ")
				.append("and mst.uuid_ms_subsystem = :subsystemId  ")
				.append("and N.assign_date between :startDate and :endDate ")
				.append(paramsQueryString);
		
		Object[][] sqlParams = new Object[paramsStack1.size()][2];
	    for (int i = 0; i < paramsStack1.size(); i++) {
			Object[] objects = paramsStack1.get(i);
			sqlParams[i] = objects;
		}

		List resultReportSla = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		return resultReportSla;
	}
	
	private List getSlaDetailByBranch(String userId, String uuidForm, Stack<Object[]> paramStack) {
		Stack<Object[]> paramsStack1 = paramStack;
		StringBuilder paramsQueryString = this.sqlPagingBuilderDetail(userId, uuidForm, paramsStack1);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append(" WITH N as ( ")
				.append("select * ")
				.append("from TR_TASK_H ttrh with(nolock) ")
				.append("where ttrh.ASSIGN_DATE between :startDate and :endDate ")
				.append("UNION ALL ")
				.append("select *  ")
				.append("from FINAL_TR_TASK_H fttrh with(nolock) ")
				.append("where fttrh.ASSIGN_DATE between :startDate and :endDate ")
				.append(" ) ")
				.append("select ROW_NUMBER() OVER(ORDER BY msb.branch_code ASC) rownum, branch_code, branch_name, full_name, ")
				.append("isnull(appl_no,''), format(assign_date,'yyyy-MM-dd HH:mm:ss') as assign_date, ")
				.append("COALESCE(format(PROMISE_DATE,'yyyy-MM-dd HH:mm:ss'),'-') as promise_date,  ")
				.append("format(start_dtm,'yyyy-MM-dd HH:mm:ss') as strt_dtm, ")
				.append("format(SEND_DATE,'yyyy-MM-dd HH:mm:ss') as send_date, ")
				.append("format(submit_date,'yyyy-MM-dd HH:mm:ss') as submit_date, ")
				.append("CASE WHEN COALESCE((DATEDIFF(minute, promise_date, start_dtm))/60,0) < 0 THEN '0' ")
				.append("ELSE COALESCE((DATEDIFF(minute, promise_date, start_dtm))/60,0) END as hour_ptson, ")
				.append("CASE WHEN COALESCE((DATEDIFF(minute, promise_date, start_dtm))%60,0) < 0 THEN '0' ")
				.append("ELSE COALESCE((DATEDIFF(minute, promise_date, start_dtm))%60,0) END as min_ptson, ")
				.append("CASE WHEN COALESCE((DATEDIFF(minute, start_dtm, submit_date))/60,0) < 0 THEN '0' ")
				.append("ELSE COALESCE((DATEDIFF(minute, start_dtm, submit_date))/60,0) END as hour_ps, ")
				.append("CASE WHEN COALESCE((DATEDIFF(minute, start_dtm, submit_date))%60,0) < 0 THEN '0' ")
				.append("ELSE COALESCE((DATEDIFF(minute, start_dtm, submit_date))%60,0) END as min_ps, ")
				.append("CASE WHEN COALESCE((DATEDIFF(minute, promise_date, submit_date))/60,0) < 0 THEN '0' ")
				.append("ELSE COALESCE((DATEDIFF(minute, promise_date, submit_date))/60,0) END as hour_ptssubmit, ")
				.append("CASE WHEN COALESCE((DATEDIFF(minute, promise_date, submit_date))%60,0) < 0 THEN '0' ")
				.append("ELSE COALESCE((DATEDIFF(minute, promise_date, submit_date))%60,0) END as min_ptssubmit, form.FORM_NAME, ")
				.append("CASE WHEN COALESCE(DATEDIFF(minute, assign_date, submit_date)/60.0,0.0) > ")
				.append("(select gs_value from am_generalsetting where gs_code = 'SLA_TIME') THEN '1'  ")
				.append("ELSE '0'  ")
				.append("END AS COLOR, N.uuid_branch ")
				.append("from N with (nolock) ")
				.append("join ms_branch msb with (nolock) on N.uuid_branch = msb.uuid_branch ")
				.append("join am_msuser msu with (nolock) on N.uuid_ms_user = msu.uuid_ms_user ")
				.append("join ms_statustask mst with (nolock) on N.uuid_status_task = mst.uuid_status_task ")
				.append("JOIN MS_FORM form with (nolock) ON N.UUID_FORM = form.UUID_FORM ")
				.append("where ")
				.append("N.uuid_branch = :branchId ")
				.append("and submit_date is not null ")
				.append("and mst.status_code != 'U' ")
				.append("and mst.uuid_ms_subsystem = :subsystemId ")
				.append("and N.assign_date between :startDate and :endDate ")
				.append(paramsQueryString);
		
		Object[][] sqlParams = new Object[paramsStack1.size()][2];
	    for (int i = 0; i < paramsStack1.size(); i++) {
			Object[] objects = paramsStack1.get(i);
			sqlParams[i] = objects;
		}

		List resultReportSla = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		return resultReportSla;
	}
	
	@Override
	public List getUser(String uuidUser, int pageNo, int pageSize, AuditContext callerId) {
		List result = null;
		Object[][] params = { { "uuidSpv", uuidUser }, { "start", ( pageNo-1 ) * pageSize + 1 }, 
			{ "end", ( pageNo-1 ) * pageSize + pageSize } };
		result = this.getManagerDAO().selectAllNative("report.absensi.getUsersAll", params, null);
		return result;
	}

	@Override
	public Integer countUser(String uuidUser, AuditContext callerId) {
		Integer result;
		Object[][] params = { { "uuidSpv", uuidUser } };
		result = (Integer)this.getManagerDAO().selectOneNative("report.absensi.countUsersAll", params);	
		return result;
	}

	@Override
	public byte[] exportExcel(AmMsuser user, String branchId, String userId,
			String startDate, String endDate, String type, AuditContext callerId, String uuidForm) {
		XSSFWorkbook workbook = this.createXlsTemplate(user, branchId, userId, 
				startDate, endDate, type, callerId, uuidForm);
		
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private XSSFWorkbook createXlsTemplate(AmMsuser user, String branchId, String userId, String startDate,
			String endDate, String type, AuditContext callerId, String uuidForm) {
		
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Sla Report");
			List resultReportSla = new ArrayList<>();
			
			if (type.equalsIgnoreCase("0")) {
				resultReportSla = this.getReportSla(String.valueOf(user.getAmMssubsystem()
						.getUuidMsSubsystem()), String.valueOf(user.getMsBranch().getUuidBranch()), 
						startDate, endDate, callerId, uuidForm);
			}
			else {
				resultReportSla = (List) this.getReportSlaDetail(user, type, branchId, userId, 
						startDate, endDate, callerId, uuidForm).get("resultList");
			}
				
			this.createData(workbook, sheet, resultReportSla, type.equalsIgnoreCase("0") 
					? HEADER_SUMMARY : HEADER_DETAIL, 
					type.equalsIgnoreCase("0") ? SUMMARY_COLUMN_WIDTH : DETAIL_COLUMN_WIDTH, 
					type.equals("0")?"SUMMARY":"DETAIL", startDate, endDate);
		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createData(XSSFWorkbook workbook, XSSFSheet sheet, List result, 
			String[] header, int[] columnSetting, String type, String startDate, String endDate) {
		
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);
		for (int i = 0; i < header.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT SLA " + type + " PERIOD "
					+ startDate.substring(0, 10) + " - " + endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(header[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, columnSetting[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, header.length-1));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//data cell
			for (int j = 0; j < header.length; j++) {
				XSSFCell cell = rowData.createCell(j);
				cell.setCellValue(temp.get("d"+(j)).toString());
				cell.setCellStyle(styles.get("cell"));
			}
		}
	}

	private static Map<String, CellStyle> createStyles(Workbook wb) {
		
		Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
		CellStyle style;

		style = wb.createCellStyle();
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		styles.put("cell", style);

		style = wb.createCellStyle();
		Font font = wb.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setFillPattern(CellStyle.SOLID_FOREGROUND);
		styles.put("header",style );
		return styles;
	}
	
	@Override
	public List getFormListCombo(AuditContext callerId) {
		List result = null;
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
		String[][] paramsForm = { { "isActive", "1" }, {"subsystemName", subsysName}};
		result = this.getManagerDAO().selectAllNativeString(
				"SELECT form.UUID_FORM, form.FORM_NAME "
				+ "FROM MS_FORM form WITH (NOLOCK) "
					+ "JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
					+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
				+ "WHERE form.IS_ACTIVE = :isActive "
					+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName "
					+ "ORDER BY form.FORM_NAME ASC", paramsForm);
		return result;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(AmMsuser user, String branchId,
			String userId, String startDate, String endDate, String type,
			AuditContext callerId, String uuidForm) {
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setSubsystemCode(user.getAmMssubsystem().getSubsystemName());
		reportBean.setUuidLoginId(String.valueOf(user.getUuidMsUser()));
		reportBean.setUuidBranch(branchId);
		reportBean.setUuidUser(userId);
		reportBean.setStartDate(startDate);
		reportBean.setEndDate(endDate);
		reportBean.setType(type);
		reportBean.setUuidForm(uuidForm);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(user);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("SLA Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_SLA_SURVEY.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(trReportResultLog.getAmMsuser(), 
				reportBean.getUuidBranch(), reportBean.getUuidUser(), 
				reportBean.getStartDate(), reportBean.getEndDate(), reportBean.getType(), 
				callerId, reportBean.getUuidForm());
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("SurveySLAReport_");
			
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
				reportBean.setUuidBranch(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
						Long.valueOf(reportBean.getUuidBranch()));
				sb.append(msBranch.getBranchCode());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if ("0".equals(reportBean.getUuidUser()) || "%".equals(reportBean.getUuidUser())) {
				reportBean.setUuidUser(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
				AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
						Long.valueOf(reportBean.getUuidUser()));
				sb.append(user.getLoginId());
			} else {
				sb.append("ALL");
			}
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(
				trReportResultLog.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime()
				.getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}
}
