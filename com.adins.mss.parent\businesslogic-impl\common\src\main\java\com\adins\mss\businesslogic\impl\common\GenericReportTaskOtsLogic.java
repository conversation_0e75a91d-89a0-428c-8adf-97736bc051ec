package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.ReportTaskOtsLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.adins.mss.util.CipherTool;
import com.adins.mss.util.MssTool;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings("unchecked")
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportTaskOtsLogic extends BaseLogic implements ReportTaskOtsLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericReportTaskOtsLogic.class);
	private Gson gson = new GsonBuilder().serializeNulls().create();
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	final String baseUrlImage = SpringPropertiesUtils.getProperty(GlobalKey.WS_BASE_PUBLIC) + "/services/p/task/view/image/";
	
	private static final String[] TEMPLATE_LABEL_HEADER_MS = {"Application No", "Status Task", "Field Person", "Branch Name", "Customer Name", "Date"};
	private static final String[] TEMPLATE_KEY_HEADER_MS = {"APPL_NO", "STATUS_TASK", "FULL_NAME", "BRANCH_NAME", "CUSTOMER_NAME", "DTM_CRT"};
	private static final int[] TEMPLATE_HEADER_MS_WIDTH = {30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 15 * 256};
	
	private String header = "header";
	
	@Autowired
    private MessageSource messageSource;
	
	private CommonLogic commonLogic;
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
	@Override
	public List<Map<String, Object>> getTaskOtsList(String [][] params, AuditContext callerId) {
		Stack<Object[]> paramsStack = new Stack<>();
		paramsStack.push(new Object[] {"uuidFormHistory", params[5][1]});
		StringBuilder paramsQueryString = this.sqlPagingBuilder(params, paramsStack);
		
		List<Map<String, Object>> listRefIdOts = this.getManagerDAO().selectAllNativeString(
				"SELECT REF_ID FROM MS_FORMQUESTIONSET WITH (NOLOCK) WHERE UUID_FORM_HISTORY = :uuidFormHistory ORDER BY QUESTION_GROUP_OF_FORM_SEQ, QUESTION_OF_GROUP_SEQ",
				new Object [][] { {"uuidFormHistory", params[5][1]} });
		String refIdOts = StringUtils.EMPTY;
		for (int i = 0; i < listRefIdOts.size(); i++) {
			Map<String, Object> mapRefId = listRefIdOts.get(i);
			if (StringUtils.isNotBlank(refIdOts)) {
				refIdOts = refIdOts + ", " + String.valueOf(mapRefId.get("d0"));
			} else {
				refIdOts = String.valueOf(mapRefId.get("d0"));
			}
		}
		
		StringBuilder queryBuilder = new StringBuilder();
		queryBuilder.append("WITH    tempForm AS ( ");
		queryBuilder.append("        SELECT    MFQS.UUID_QUESTION, MFQS.REF_ID ");
		queryBuilder.append("        FROM      MS_FORMQUESTIONSET MFQS WITH (NOLOCK) ");
		queryBuilder.append("        WHERE     MFQS.UUID_FORM_HISTORY = :uuidFormHistory "); 
		queryBuilder.append(") ");
		queryBuilder.append("SELECT  b.* "); 
		queryBuilder.append("FROM    ( "); 
		queryBuilder.append("           SELECT  * "); 
		queryBuilder.append("           FROM    ( "); 
		queryBuilder.append("                   SELECT  form.REF_ID as REF_ID, ");
		queryBuilder.append("                           APPL_NO AS APPL_NO, ");
		queryBuilder.append("                           MST.STATUS_TASK_DESC AS STATUS_TASK, ");
		queryBuilder.append("                           AMM.FULL_NAME AS FULL_NAME, ");
		queryBuilder.append("                           MSB.BRANCH_NAME AS BRANCH_NAME, ");
		queryBuilder.append("                           CUSTOMER_NAME AS CUSTOMER_NAME, ");
		queryBuilder.append("                           CONVERT(VARCHAR(10), TRTH.DTM_CRT, 120) AS DTM_CRT, ");
		queryBuilder.append("                           ISNULL(TEXT_ANSWER, OPTION_TEXT) AS ANSWER ");
		queryBuilder.append("                   FROM    TR_TASK_D detail WITH (NOLOCK) ");
		queryBuilder.append("                   JOIN    tempForm form on form.UUID_QUESTION = detail.UUID_QUESTION ");
		queryBuilder.append("                   JOIN    TR_TASK_H TRTH WITH (NOLOCK) ON TRTH.UUID_TASK_H = detail.UUID_TASK_H ");
		queryBuilder.append("                   JOIN    MS_STATUSTASK MST WITH (NOLOCK) ON MST.UUID_STATUS_TASK = TRTH.UUID_STATUS_TASK ");
		queryBuilder.append("                   JOIN    AM_MSUSER AMM WITH (NOLOCK) ON AMM.UUID_MS_USER = TRTH.UUID_MS_USER ");
		queryBuilder.append("                   JOIN    MS_BRANCH MSB WITH (NOLOCK) ON MSB.UUID_BRANCH = TRTH.UUID_BRANCH ");
		queryBuilder.append("                   WHERE   1=1 ");
		queryBuilder.append(paramsQueryString);
		queryBuilder.append("                   UNION   ALL ");
		queryBuilder.append("                   SELECT  form.REF_ID as REF_ID, ");
		queryBuilder.append("                           APPL_NO AS APPL_NO, ");
		queryBuilder.append("                           MST.STATUS_TASK_DESC AS STATUS_TASK, ");
		queryBuilder.append("                           AMM.FULL_NAME AS FULL_NAME, ");
		queryBuilder.append("                           MSB.BRANCH_NAME AS BRANCH_NAME, ");
		queryBuilder.append("                           CUSTOMER_NAME AS CUSTOMER_NAME, ");
		queryBuilder.append("                           CONVERT(VARCHAR(10), TRTH.DTM_CRT, 120) AS DTM_CRT, ");
		queryBuilder.append("                           CONVERT(VARCHAR(36), detail.UUID_TASK_DETAIL_LOB) AS ANSWER ");
		queryBuilder.append("                   FROM    TR_TASKDETAILLOB detail WITH (NOLOCK) ");
		queryBuilder.append("                   JOIN    tempForm form on form.UUID_QUESTION = detail.QUESTION_ID ");
		queryBuilder.append("                   JOIN    TR_TASK_H TRTH WITH (NOLOCK) ON TRTH.UUID_TASK_H = detail.UUID_TASK_H ");
		queryBuilder.append("                   JOIN    MS_STATUSTASK MST WITH (NOLOCK) ON MST.UUID_STATUS_TASK = TRTH.UUID_STATUS_TASK ");
		queryBuilder.append("                   JOIN    AM_MSUSER AMM WITH (NOLOCK) ON AMM.UUID_MS_USER = TRTH.UUID_MS_USER ");
		queryBuilder.append("                   JOIN    MS_BRANCH MSB WITH (NOLOCK) ON MSB.UUID_BRANCH = TRTH.UUID_BRANCH ");
		queryBuilder.append("                   WHERE   (LOB_FILE IS NOT NULL OR IMAGE_PATH IS NOT NULL) ");
		queryBuilder.append(paramsQueryString);
		queryBuilder.append("            ) a "); 
		queryBuilder.append("            PIVOT (MAX(ANSWER) FOR REF_ID IN (" + refIdOts + ")) piv ");
		queryBuilder.append("        ) b ");
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    List<Map<String, Object>> result = this.getManagerDAO().selectForListOfMapString(queryBuilder.toString(), sqlParams, null);
	    List<Map<String, Object>> listData = new ArrayList<>();
	    if (null != result && !result.isEmpty()) {
	    	for (int i = 0; i < result.size(); i++) {
	    		Map<String, Object> mapResult = result.get(i);
	    		String json = gson.toJson(mapResult);
	    		
	    		Map<String, Object> mapData = new HashMap<>();
	    		mapData.put("data", json);
	    		listData.add(mapData);
	    	}
	    }
	    
		return listData; 
	}
	
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramsStack) {
		if (params == null) {
			return new StringBuilder();
		}
		
		StringBuilder sb = new StringBuilder();
		if (StringUtils.isNotBlank((String) params[0][1])) {
			sb.append(" AND MSB.UUID_BRANCH = :uuidBranch ");
			paramsStack.push(new Object[] {"uuidBranch", Long.valueOf((String) params[0][1])});
		}
		
		if (StringUtils.isNotBlank(params[3][1].toString()) && StringUtils.isNotBlank(params[4][1].toString()) ) {
			sb.append(" AND TRTH.DTM_CRT BETWEEN :startDate AND :endDate ");
			paramsStack.push(new Object[] {"startDate", (String) params[3][1]});
			paramsStack.push(new Object[] {"endDate", (String) params[4][1]});
		}
		
		if (StringUtils.isNotBlank(params[1][1].toString())) {
			sb.append(" AND TRTH.UUID_MS_USER = :uuidMsUser ");
			paramsStack.push(new Object[] {"uuidMsUser", Long.valueOf((String) params[1][1])});
		}
		
		if (StringUtils.isNotBlank(params[2][1].toString())) {
			sb.append(" AND TRTH.APPL_NO LIKE '%'+ :applNo +'%' ");
			paramsStack.push(new Object[] {"applNo", (String) params[2][1]});
		}
		
		return sb;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(reportBean.getParamsAction(), callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (null == amGeneralSetting) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("ReportTaskOts_");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getEndDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
					sb.append("ALL");
				} else {
					MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
							Long.valueOf(reportBean.getUuidBranch()));
					sb.append(msBranch.getBranchCode());
				}
			} else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
				if ("0".equals(reportBean.getUuidUser()) || "%".equals(reportBean.getUuidUser())) {
					sb.append("ALL");
				} else {
					AmMsuser amMuser = this.getManagerDAO().selectOne(AmMsuser.class, 
							Long.valueOf(reportBean.getUuidUser()));
					sb.append(amMuser.getFullName());
				}
			} else {
				sb.append("ALL");
			}
			
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} finally {
				fileOut.close();
			}
		} catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(trReportResultLog
				.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime().getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}

	private XSSFWorkbook createXlsTemplate(String[][] params, AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Report Task Ots");
			List<Map<String, Object>> result = this.getTaskOtsList(params, callerId);
			this.createData(workbook, sheet, result, params[3][1], params[4][1], params[5][1], callerId);
		} catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createData(XSSFWorkbook workbook, XSSFSheet sheet, 
			List<Map<String, Object>> result, String startDate, String endDate, String uuidVersion, AuditContext callerId) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell = 0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		XSSFRow rowHeader = sheet.createRow(rowcell++);
		List<Map<String, Object>> headerByQset = this.retrieveHeaderByQuestionSet(uuidVersion, callerId);
		for (int i = 0; i < headerByQset.size(); i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT TASK OTS PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			
			XSSFCell cell = rowHeader.createCell(i);
			Map<String, Object> mapHeader = headerByQset.get(i);
			cell.setCellValue(mapHeader.get("d1").toString());
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, (int) mapHeader.get("d2"));
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, headerByQset.size() - 1));

		for (int i = 0; i < result.size(); i++) {
			Map<String, Object> temp = result.get(i);
			String json = temp.get("data").toString();
			temp = gson.fromJson(json, Map.class);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//data cell			
			for (int j = 0; j < headerByQset.size(); j++) {
				XSSFCell cell = rowData.createCell(j);
				Map<String, Object> mapHeader = headerByQset.get(j);
				if ("1".equals(mapHeader.get("d3").toString())) {
					if (null != temp.get(mapHeader.get("d0").toString())) {
						String image = this.retrieveEncryptedImage(temp.get(mapHeader.get("d0").toString()).toString(), callerId);
						try {
							image = URLEncoder.encode(image, StandardCharsets.UTF_8.toString());
							cell.setCellValue(baseUrlImage + image);
						} catch (UnsupportedEncodingException e) {
							e.printStackTrace();
						}
					} else {
						cell.setCellValue("");
					}
				} else {
					if (null != temp.get(mapHeader.get("d0").toString())) {
						cell.setCellValue(temp.get(mapHeader.get("d0").toString()).toString());
					} else {
						cell.setCellValue("");
					}
				}
				cell.setCellStyle(styles.get("cell"));
			}
		}
	}

	private Map<String, CellStyle> createStyles(XSSFWorkbook wb) {
		Map<String, CellStyle> styles = new HashMap<>();
		CellStyle style;

		style = wb.createCellStyle();
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		styles.put("cell", style);

		style = wb.createCellStyle();
		Font font = wb.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setFillPattern(CellStyle.SOLID_FOREGROUND);
		styles.put(header, style);
		return styles;
	}

	@Override
	public String saveExportScheduler(String [][] params, AuditContext callerId) {
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setUuidLoginId(callerId.getCallerId());
		reportBean.setUuidBranch(params[0][1]);
		reportBean.setStartDate(params[3][1]);
		reportBean.setEndDate(params[4][1]);
		reportBean.setUuidUser(params[1][1]);
		reportBean.setUuidVersion(params[5][1]);
		reportBean.setParamsAction(params);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);
		
		AmMsuser amMsUser = new AmMsuser();
		amMsUser.setUuidMsUser(Long.valueOf(callerId.getCallerId()));
		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("Task Ots Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_REPORT_OTS.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}
	
	@Override
	public List<Map<String, Object>> retrieveHeaderByQuestionSet(String uuidVersion, AuditContext callerId) {
		Object [][] paramQuestion = { {"uuidVersion", uuidVersion} };
		List<Map<String, Object>> listQuestion = this.getManagerDAO().selectAllNative(
				"report.download.getquestionlistByFormVersionOts", paramQuestion, null);
		
		List<Map<String, Object>> listHeader = new ArrayList<>();
		for (int i = 0; i < TEMPLATE_LABEL_HEADER_MS.length; i++) {
			Map<String, Object> tempHeader = new HashMap<>();
			tempHeader.put("d0", TEMPLATE_KEY_HEADER_MS[i]);
			tempHeader.put("d1", TEMPLATE_LABEL_HEADER_MS[i]);
			tempHeader.put("d2", TEMPLATE_HEADER_MS_WIDTH[i]);
			tempHeader.put("d3", "0");
			
			listHeader.add(tempHeader);
		}
		for (int j = 0; j < listQuestion.size(); j++) {
			Map<String, Object> map = listQuestion.get(j);
			
			Map<String, Object> tempHeader = new HashMap<>();
			tempHeader.put("d0", String.valueOf(map.get("d2")));
			tempHeader.put("d1", String.valueOf(map.get("d1")));
			tempHeader.put("d2", 30 * 256);
			String codeAnswerType = String.valueOf(map.get("d3"));
			if (MssTool.isImageQuestion(codeAnswerType)) {
				tempHeader.put("d3", "1");
			} else {
				tempHeader.put("d3", "0");
			}
			
			listHeader.add(tempHeader);
		}
		
		return listHeader;
	}

	@Override
	public List<Map<String, Object>> retrieveComboVersion(AuditContext callerId) {		
		AmMssubsystem amMssubsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		MsForm msFormOts = this.commonLogic.retrieveMsFormByName(GlobalVal.FORM_OTS, true, amMssubsystem.getUuidMsSubsystem(), callerId);
		String [][] params = { {"uuidForm", String.valueOf(msFormOts.getUuidForm())} };
		
		return this.getManagerDAO().selectAllNativeString(
				"SELECT   UUID_FORM_HISTORY, FORM_VERSION " +
				"FROM     MS_FORMHISTORY WITH (NOLOCK) " +
				"WHERE    UUID_FORM = :uuidForm " +
				"ORDER BY FORM_VERSION DESC", params);
	}

	@Override
	public String retrieveEncryptedImage(String uuidLob, AuditContext callerId) {
		String result = uuidLob;
		if ("1".equals(this.link_encrypt)) {
			String[] temp = {uuidLob};
			result = CipherTool.encryptData(temp).get(0).toString();
		}
		
		return result;
	}
	
}
