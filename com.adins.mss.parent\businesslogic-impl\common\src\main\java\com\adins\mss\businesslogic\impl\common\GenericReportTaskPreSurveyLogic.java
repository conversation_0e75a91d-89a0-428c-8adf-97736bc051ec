package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ReportTaskPreSurveyLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrReportresultlog;
import com.google.gson.Gson;

import android.preference.PreferenceActivity.Header;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportTaskPreSurveyLogic extends BaseLogic implements ReportTaskPreSurveyLogic {

	private static final Logger LOG = LoggerFactory.getLogger(GenericReportTaskPreSurveyLogic.class);
	private boolean isLogged = false;
	
	private static final String[] HEADER = {
			/*1 */"APPL NO",
			/*2 */"Form ID",
			/*3 */"Status Task",
			/*4 */"Prioritas",
			/*5 */"Supervisor",
			/*6 */"Mobile User ID",
			/*7 */"Mobile User Name",
			/*8 */"Branch Name",
			/*9 */"Approval Date",
			/*10*/"Nama Customer",
			/*11*/"No Telp",
			/*12*/"Alamat",
			/*13*/"Notes",
			/*14*/"Tanggal Survey",
			/*15*/"Jam Survey"
	};
	private static final String[][] HEADER_REFID = {
			/*1*/{"No Identitas Pemohon", "PRE_NO_KTP"},
			/*2*/{"Nama KTP", "PRE_NAMA_KTP"},
			/*3*/{"Tempat Lahir Pemohon", "PRE_TMPT_LHR"},
			/*4*/{"Tanggal Lahir Pemohon", "PRE_TGL_LHR"},
			/*5*/{"Alamat Legal (KTP)", "PRE_LGL_ADDR"},
			/*6*/{"RT Legal (KTP)", "PRE_LGL_RT"},
			/*7*/{"RW Legal (KTP)", "PRE_LGL_RW"},
			/*8*/{"Nama Provinsi Legal (KTP)", "PRE_LGL_PROVINSI"},
			/*9*/{"Nama Kabupaten/Kota Legal (KTP)", "PRE_LGL_CITY"},
			/*10*/{"Nama kecamatan Legal (KTP)", "PRE_LGL_KCMTN"},
			/*11*/{"Nama kelurahan Legal (KTP)", "PRE_LGL_KLRHN"},
			/*12*/{"Kode Pos Legal (KTP)", "PRE_LGL_ZIPCODE"},
			/*13*/{"Status Pernikahan", "PRE_STTS_PERNIKAHAN"},
			/*14*/{"Nama Ibu Kandung", "PRE_IBU_KANDUNG"},
			/*15*/{"Jenis Kelamin", "PRE_GENDER"},
			/*16*/{"Agama", "PRE_AGAMA"},
			/*17*/{"Email", "PRE_EMAIL"},
			/*18*/{"No Handphone", "PRE_NO_HP"},
			/*19*/{"Surat Pernyataan Pasangan tidak ikut tanda tangan Kredit", "PRE_STATE_PSGN"},
			/*20*/{"No Identitas Pasangan", "PRE_NO_KTP_PSGN"},
			/*21*/{"Nama KTP Pasangan", "PRE_NAMA_PSGN"},
			/*22*/{"Tempat Lahir Pasangan", "PRE_TMPT_LHR_PSGN"},
			/*23*/{"Tanggal Lahir Pasangan", "PRE_TGL_LHR_PSGN"},
			/*24*/{"Alamat Legal (KTP) Pasangan", "PRE_LGL_ADDR_PSGN"},
			/*25*/{"RT Legal (KTP) Pasangan", "PRE_LGL_RT_PSGN"},
			/*26*/{"RW Legal (KTP) Pasangan", "PRE_LGL_RW_PSGN"},
			/*27*/{"Nama Provinsi Legal (KTP) Pasangan", "PRE_LGL_PRVNS_PSGN"},
			/*28*/{"Nama Kabupaten/Kota Legal (KTP) Pasangan", "PRE_LGL_CITY_PSGN"},
			/*29*/{"Nama kecamatan Legal (KTP) Pasangan", "PRE_LGL_KCMTN_PSGN"},
			/*30*/{"Nama kelurahan Legal (KTP) Pasangan", "PRE_LGL_KLRHN_PSGN"},
			/*31*/{"Kode Pos Legal (KTP) Pasangan", "PRE_LGL_ZIPCODE_PSGN"},
			/*32*/{"Nama Ibu Kandung Pasangan", "PRE_IBU_KANDUNG_PSGN"},
			/*33*/{"Email Pasangan", "PRE_EMAIL_PSGN"},
			/*34*/{"No Handphone Pasangan", "PRE_NO_HP_PSGN"},
			/*35*/{"No Identitas Guarantor", "PRE_NO_KTP_GRTR"},
			/*36*/{"Nama KTP Guarantor", "PRE_NAMA_KTP_GRTR"},
			/*37*/{"Tempat Lahir Guarantor", "PRE_TMPT_LHR_GRTR"},
			/*38*/{"Tanggal Lahir Guarantor", "PRE_TGL_LHR_GRTR"},
			/*39*/{"Alamat Legal (KTP) Guarantor", "PRE_LGL_ADDR_GRTR"},
			/*40*/{"RT Legal (KTP) Guarantor", "PRE_LGL_RT_GRTR"},
			/*41*/{"RW Legal (KTP) Guarantor", "PRE_LGL_RW_GRTR"},
			/*42*/{"Nama Provinsi Legal (KTP) Guarantor", "PRE_LGL_PRVNS_GRTR"},
			/*43*/{"Nama Kabupaten/Kota Legal (KTP) Guarantor", "PRE_LGL_CITY_GRTR"},
			/*44*/{"Nama kecamatan Legal (KTP) Guarantor", "PRE_LGL_KCMTN_GRTR"},
			/*45*/{"Nama kelurahan Legal (KTP) Guarantor", "PRE_LGL_KLRHN_GRTR"},
			/*46*/{"Kode Pos Legal (KTP) Guarantor", "PRE_LGL_ZIPCODE_GRTR"},
			/*47*/{"Nama Ibu Kandung Guarantor", "PRE_IBU_KANDUNG_GRTR"},
			/*48*/{"Email Guarantor", "PRE_EMAIL_GRTR"},
			/*49*/{"No Handphone Guarantor", "PRE_NO_HP_GRTR"},
			/*50*/{"Status Kepemilikan Rumah", "PRE_STATUS_RUMAH"},
			/*51*/{"NPWP", "PRE_NPWP"},
			/*52*/{"Alamat Tinggal (Survey)", "PRE_SVY_ADDR"},
			/*53*/{"RT Tinggal (Survey", "PRE_SVY_RT"},
			/*54*/{"RW Tinggal (survey)", "PRE_SVY_RW"},
			/*55*/{"Nama Provinsi Tinggal (Survey)", "PRE_SVY_PROVINSI"},
			/*56*/{"Nama Kota/Kabupaten Tinggal (Survey)", "PRE_SVY_CITY"},
			/*57*/{"Nama Kecamatan Tinggal (Survey)", "PRE_SVY_KCMTN"},
			/*58*/{"Nama Kelurahan Tinggal (Survey)", "PRE_SVY_KLRHN"},
			/*59*/{"Kode Pos Tinggal (Survey)", "PRE_SVY_ZIPCODE"},
			/*60*/{"Sub Zip Code", "PRE_SUBZIPCODE"},
			/*61*/{"Product Type", "PRE_PROD_TYPE"},
			/*62*/{"Product Offering Type (POT)", "PRE_PROD_OFF_TYPE"},
			/*63*/{"Brand", "PRE_BRAND"},
			/*64*/{"Assets", "PRE_ASSETS"},
			/*65*/{"Group Type", "PRE_GROUP_TYPE"},
			/*66*/{"Type", "PRE_TYPE"},
			/*67*/{"Kategori Assets", "PRE_ASSETS_CAT"},
			/*68*/{"Tahun Kendaraan", "PRE_THN_KNDRN"},
			/*69*/{"SOA", "PRE_SOA"},
			/*70*/{"Kode-Nama Dealer (Mandatory untuk NB)", "PRE_DEALER"},
			/*71*/{"Pos Dealer", "PRE_POS_DEALER"},
			/*72*/{"Kode-Nama Sales Dealer (Mandatory untuk NB)", "PRE_SALES"},
			/*73*/{"Notes", "PRE_NOTES"},
			/*74*/{"Prospect/Interest?", "PRE_PRSPCT_INTRST"},
			/*75*/{"OTR", "PRE_OTR"},
			/*76*/{"DP/NTF (Amount)", "PRE_DP_NTF"},
			/*77*/{"DP Murni", "PRE_DP_MURNI"},
			/*78*/{"DP/LTV (%)", "PRE_DP_LTV"},
			/*79*/{"Tenor", "PRE_TENOR"},
			/*80*/{"Angsuran", "PRE_ANGSURAN"},
			/*81*/{"Penghasilan", "PRE_PENGHASILAN"},
			/*82*/{"Education", "PRE_EDUCATION"},
			/*83*/{"Profession Name", "PRE_PROFESSION"},
			/*84*/{"Length Of Work (Months)", "PRE_LENGTH_WORK"},
			/*85*/{"DSR (%)", "PRE_DSR"},
			/*86*/{"Stay length (Months)", "PRE_STAY_LENGTH"},
			/*87*/{"BPKB Ownership", "PRE_BPKB_OWNERSHIP"},
			/*88*/{"Owner Relationship", "PRE_OWNER_RLTNSHP"},
			/*89*/{"Dukcapil Result (Pemohon)", "PRE_RESULT_DKCP"},
			/*90*/{"Phone Status Result (Pemohon)", "PRE_RESULT_PHONE"},
			
			/*KBIJ*/
			/**/{"Total Kontrak Aktif Pemohon", "SVY_TOT_KTR_ATF_PMHN"},
			/**/{"Plafond Awal", "SVY_PLAF_AWL_PMHN"},
			/**/{"Total Baki Debet", "SVY_TOT_BAK_DBT_PMHN"},
			/**/{"Kolektibilitas 1/Jumlah kontrak /Baki Debet", "COL_JMLKTR_BDEB_PMHN"},
			/**/{"Kolektibilitas 2/Jumlah kontrak /Baki Debet", "COL_JMLKTR_BDEB_PMHN_2"},
			/**/{"Kolektibilitas 3/Jumlah kontrak /Baki Debet", "COL_JMLKTR_BDEB_PMHN_3"},
			/**/{"Kolektibilitas 4/Jumlah kontrak /Baki Debet", "COL_JMLKTR_BDEB_PMHN_4"},
			/**/{"Kolektibilitas 5/Jumlah kontrak /Baki Debet", "COL_JMLKTR_BDEB_PMHN_5"},
			/**/{"Maks DPD / Baki Debet / Plafond", "MAX_DPD_BDE_PLA_PMHN"},
			/**/{"Baki Debet Terbesar/ Plafond Awal/ Latest DPD", "MAX_BD_PLA_LDPD_PMHN"},
			/**/{"Total Angsuran", "SVY_TOT_ANG_PMHN"},
			/**/{"Last Update", "SVY_LAST_UPD_PMHN"},
			/**/{"Total Kontrak / Plafon Awal", "TOT_KTRK_PLA_PMHN"},
			/**/{"Max DPD / Plafon Awal ", "MAX_DPD_PLA_PMHN"},
			/**/{"Plafon Terbesar / DPD", "MAX_PLA_DPD_PMHN"},
			/**/{"Total Kontrak Maks DPD > 7 hari ", "SVY_TOT_MAX_DPD_PMHN"},
			/**/{"Last Update", "SVY_LAST_UPD_PMHN2"},
			/**/{"Rekomendasi Biro Kredit Pemohon(result)", "SVY_RSLT_REBIKR_PMHN"},
			/**/{"Total Kontrak Aktif Pasangan ", "SVY_TOT_KTR_ATF_PSGN"},
			/**/{"Plafond Awal", "SVY_PLAF_AWL_PSGN"},
			/**/{"Total Baki Debet", "SVY_TOT_BAK_DBT_PSGN"},
			/**/{"Kolektibilitas 1/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_PSGN"},
			/**/{"Kolektibilitas 2/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_PSGN_2"},
			/**/{"Kolektibilitas 3/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_PSGN_3"},
			/**/{"Kolektibilitas 4/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_PSGN_4"},
			/**/{"Kolektibilitas 5/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_PSGN_5"},
			/**/{"Maks DPD / Baki Debet / Plafond", "MAX_DPD_BDE_PLA_PSGN"},
			/**/{"Baki Debet Terbesar/ Plafond Awal/ Latest DPD", "MAX_BD_PLA_LDPD_PSGN"},
			/**/{"Total Angsuran", "SVY_TOT_ANG_PSGN"},
			/**/{"Last Update", "SVY_LAST_UPD_PSGN2"},
			/**/{"Total Kontrak / Plafon Awal ", "TOT_KTRK_PLA_PSGN"},
			/**/{"Max DPD / Plafon Awal ", "MAX_DPD_PLA_PSGN"},
			/**/{"Plafon Terbesar / DPD", "MAX_PLA_DPD_PSGN"},
			/**/{"Total Kontrak Maks DPD > 7 hari", "SVY_TOT_MAX_DPD_PSGN"},
			/**/{"Last Update", "SVY_LAST_UPD_PSGN3"},
			/**/{"Rekomendasi Biro Kredit Pasangan(result)", "SVY_RSLT_REBIKR_PSGN"},
			/**/{"Total Kontrak Aktif Guarantor", "SVY_TOT_KTR_ATF_GRTR"},
			/**/{"Plafond Awal", "SVY_PLAF_AWL_GRTR"},
			/**/{"Total Baki Debet", "SVY_TOT_BAK_DBT_GRTR"},
			/**/{"Kolektibilitas 1/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_GRTR"},
			/**/{"Kolektibilitas 2/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_GRTR_2"},
			/**/{"Kolektibilitas 3/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_GRTR_3"},
			/**/{"Kolektibilitas 4/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_GRTR_4"},
			/**/{"Kolektibilitas 5/Jumlah kontrak /Baki Debet ", "COL_JMLKTR_BDEB_GRTR_5"},
			/**/{"Maks DPD / Baki Debet / Plafond", "MAX_DPD_BDE_PLA_GRTR"},
			/**/{"Baki Debet Terbesar/ Plafond Awal/ Latest DPD", "MAX_BD_PLA_LDPD_GRTR"},
			/**/{"Total Angsuran", "SVY_TOT_ANG_GRTR"},
			/**/{"Last Update", "SVY_LAST_UPD_GRTR2"},
			/**/{"Total Kontrak / Plafon Awal ", "TOT_KTRK_PLA_GRTR"},
			/**/{"Max DPD / Plafon Awal ", "MAX_DPD_PLA_GRTR"},
			/**/{"Plafon Terbesar / DPD", "MAX_PLA_DPD_GRTR"},
			/**/{"Total Kontrak Maks DPD > 7 hari", "SVY_TOT_MAX_DPD_GRTR"},
			/**/{"Last Update", "SVY_LAST_UPD_GRTR3"},
			/**/{"Rekomendasi Biro Kredit Guarantor(result)", "SVY_RSLT_REBIKR_GRTR"},
			/**/{"Rekomendasi Final Biro Kredit", "REC_FINAL_BK"},
			/**/{"Reason Pemohon", "SVY_REASON_PMHN"},
			/**/{"Reason Pasangan", "SVY_REASON_PSGN"},
			/**/{"Reason Penjamin", "SVY_REASON_GRTR"},
			/**/{"Maskapai Asuransi", "PRE_ASURANSI"},
			/**/{"Persetujuan penerimaan pemberitahuan dari WOM Finance tentang produk, program, dan kegiatan WOM Finance melalui berbagai saluran komunikasi, termasuk email, telepon, SMS, dan media lainnya", "PRE_PEMBERITAHUAN"},
			/**/{"Persetujuan penggunaan data pribadi pemohon dalam pemasaran produk WOMF Finance dan produk mitra WOM Finance sesuai peraturan dan hukum yang berlaku", "PRE_PENGGUNAAN_DATA"},
			/**/{"Lembaga Penyelesaian Sengketa", "LMBG_PNYLSN_SNGKT"},
			/*100*/{"Is Cancel App?", "SVY_IS_CNCL_APP"},
			/*101*/{"Reasons Is Cancel APP", "SVY_R_CNCL_APP"},
			/*102*/{"Notes Is Cancel APP", "SVY_NOTES_C_APP"},
			/*103*/{"CMO Recommendation", "SVY_CMO_RCMND"},
			/*104*/{"Reasons CMO Recommendation", "SVY_R_CMO_RCMD"},
			/*105*/{"Notes CMO Recommendation", "SVY_NOTES_CMO_RCMD"}
	};
	
	private Gson gson = new Gson(); 
	private String paramFileName = "fileName";
	private String paramUuidBranch = "uuidBranch";
	
	private void log(String logMessage) {
		if(isLogged) {
			LOG.info(logMessage);
		}
	}

	@Transactional
	@Override
	public void exportExcelTask(TrReportresultlog trReportResultLog, AuditContext callerId) {
		TrReportresultlog reportLog = this.getManagerDAO().selectOne(TrReportresultlog.class, trReportResultLog.getSeqno());
		reportLog.setProcessStartTime(new Date());
		
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		HSSFWorkbook workbook = this.createXls(GlobalVal.RPTTYPE_PRESURVEY, getReportData(trReportResultLog.getRptParams()));
		try {
			Map<String, String> rptParams = new HashMap();
			rptParams = gson.fromJson(trReportResultLog.getRptParams(), rptParams.getClass());
			
			//Get Directory Path
			String filePath = rptParams.get("folder");
			String fileName = rptParams.get(paramFileName);
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting genSet = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (genSet == null) {
				throw new EntityNotFoundException("Error Excel Result Location not found.", GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			//String dirPath = "/Users/<USER>/Work/Eclipse/Wildfly/excel/" + filePath + "/";
			String dirPath = genSet.getGsValue() + filePath + "/";
			String fullPath = dirPath + fileName + ".xls";
			
			//Check Directory
			File folder = new File(dirPath);
			if(!folder.exists()) {
				folder.mkdirs();
			}
			
			//Save file
			workbook.write(stream);
			byte[] xls = stream.toByteArray();
			FileOutputStream fileOut = new FileOutputStream(fullPath);
			
			try {
				fileOut.write(xls);
				fileOut.flush();
			} finally {
				fileOut.close();
			}
			
			//Update Report Log
			reportLog.setProcessFinishTime(new Date());
			Date startTime = reportLog.getProcessStartTime();
			Date finishTime = reportLog.getProcessFinishTime();
			long duration = (finishTime.getTime() - startTime.getTime()) / 1000;
			
			reportLog.setProcessDurationSeconds(Math.toIntExact(duration));
			reportLog.setReportFileLocation(fullPath);
			reportLog.setProcessStatus("1");
			
		} catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		this.getManagerDAO().update(reportLog);
	}

	@Override
	public Map<String, Object> getCombo(AmMsuser amMsuser, String formName, AuditContext callerId) {
		Map<String, Object> resultMap = new HashMap();
		
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
			
			//Combo List Branch
		String[][] paramBranch = { {paramUuidBranch, String.valueOf(amMsuser.getMsBranch().getUuidBranch())} };
		List listBranchCombo = this.getManagerDAO().selectAllNative(
						"report.download.getlistbranch", paramBranch, null);
			
		Map<String, String> branchCombo = new HashMap<>();
		if (!listBranchCombo.isEmpty()) {
			for (int i = 0; i < listBranchCombo.size(); i++) {
				Map temp = (Map) listBranchCombo.get(i);
				branchCombo.put(String.valueOf(temp.get("d0")),
						(String) temp.get("d2")+" - "+(String) temp.get("d1"));
			}
		}
			
		Map<String, String> userCombo = new HashMap<>();
			
			//Combo List Form
		String[][] paramsForm = { { "isActive", "1" }, {"subsystemName", subsysName}, {"formName", formName}};
		List<Map<String, Object>> listFormCombo = this.getManagerDAO().selectAllNativeString(
				"SELECT form.UUID_FORM, form.FORM_NAME "
				+ "FROM MS_FORM form WITH (NOLOCK) "
					+ "JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
					+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
				+ "WHERE form.IS_ACTIVE = :isActive "
					+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName "
					+ "AND form.FORM_NAME = :formName ", paramsForm);
		
			
		Map<String, String> formCombo = new HashMap<>();
		if (!listFormCombo.isEmpty()) {
			for (int i = 0; i < listFormCombo.size(); i++) {
				Map<String, Object> mp = listFormCombo.get(i);
				formCombo.put( String.valueOf(mp.get("d0")), String.valueOf(mp.get("d1")) );
			}
		}
		
		Map<String, String> formVersion = new HashMap<>();
		formVersion.put("", "-- Choose One --");
		resultMap.put("branchCombo", branchCombo);
		resultMap.put("userCombo", userCombo);
		resultMap.put("formCombo", listFormCombo);
		resultMap.put("formVersion", formVersion);
		return resultMap;
	}

	@Transactional
	@Override
	public Map<String, Object> export(Map<String, String> searchParams) {
		byte[] data = null;
		
		String rptParams = gson.toJson(searchParams);
		
		try {
			String excelPath = getExcelPath() + SystemUtils.FILE_SEPARATOR;
			String filePath = searchParams.get("folder") + SystemUtils.FILE_SEPARATOR
								+ searchParams.get(paramFileName) + ".xls";
			
//			excelPath="C:\\Excel\\Result\\";
			String fullPath = excelPath + filePath;
			File file = new File(fullPath);
			
			if (file.exists()) {//Check if file already generated
				Path pathsolute = Paths.get(fullPath);
				data = Files.readAllBytes(pathsolute);
			} 
			else {
				Object[][] reportParams = {{Restrictions.eq("rptType", GlobalVal.RPTTYPE_PRESURVEY)}, {Restrictions.eq("rptName", searchParams.get(paramFileName))}};
				TrReportresultlog reportLog = this.getManagerDAO().selectOne(TrReportresultlog.class, reportParams);
				
				if(reportLog != null) {
					//If has ReportLog but no file then it will re-generated
					reportLog.setDtmRequest(new Date());
					reportLog.setRptParams(rptParams);
					reportLog.setProcessStartTime(null);
					reportLog.setProcessFinishTime(null);
					reportLog.setProcessDurationSeconds(null);
					reportLog.setReportFileLocation(null);
					reportLog.setProcessStatus("0");
					this.getManagerDAO().update(reportLog);
				} else {
					//If has no ReportLog then will make one
					AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(searchParams.get("uuidLoginId")));
					TrReportresultlog newReportLog = new TrReportresultlog();
					
					newReportLog.setAmMsuser(user);
					newReportLog.setDtmRequest(new Date());
					newReportLog.setRptName(searchParams.get(paramFileName));
					newReportLog.setRptType(GlobalVal.RPTTYPE_PRESURVEY);
					newReportLog.setRptParams(rptParams);
					newReportLog.setProcessStatus("0");
					this.getManagerDAO().insert(newReportLog);
				}
				return null;
			}
		} catch (IOException e) {
			LOG.error("Error while create excel file. SearchParams=[{}], ErrorMessage=[{}]", rptParams, e);
		}
		
		Map<String, Object> result = new HashMap<>();
		result.put("filename", searchParams.get(paramFileName));
		result.put("result", data);
		return result;
	}
	
	private String getExcelPath() {
		Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
		AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
		
		if (amGeneralSetting == null) {
			throw new EntityNotFoundException("Excel result location has not been defined.",
					GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
		}
		
		return amGeneralSetting.getGsValue();
	}

	private String[][] getReportData(String rptParams) {
		
		Map<String, Object> search = getSearch(rptParams);
		String queryConditions = (String) search.get("searchConditions");
		Object[][] params = (Object[][]) search.get("params");
		
		String queryListTask = "SELECT CONVERT(VARCHAR(20), taskH.UUID_TASK_H), '0' [FLAG_FINAL], taskH.APPL_NO, form.FORM_NAME, sts.STATUS_TASK_DESC, prt.PRIORITY_DESC,\n"
				+"	spv.UNIQUE_ID [SPV], usr.UNIQUE_ID, usr.FULL_NAME, branch.BRANCH_NAME, CONVERT(VARCHAR(20), taskH.APPROVAL_DATE, 120) AS TANGGAL_APPROVAL, taskH.CUSTOMER_NAME, \n"
				+"	ISNULL(REPLICATE('*', LEN(taskH.CUSTOMER_PHONE)), '*') CUSTOMER_PHONE,\n"
				+"	ISNULL(REPLICATE('*', LEN(taskH.CUSTOMER_ADDRESS)), '*') CUSTOMER_ADDRESS, \n"
				+"	taskH.NOTES, CONVERT(VARCHAR(10), taskH.SUBMIT_DATE, 120) AS TANGGAL,\n"
				+"	CONVERT (VARCHAR(10), taskH.SUBMIT_DATE, 108) AS JAM\n"
				+"FROM TR_TASK_H taskH with(nolock)\n"
				+"JOIN MS_FORM form with(nolock) ON taskH.UUID_FORM = form.UUID_FORM\n"
				+"JOIN MS_FORMHISTORY formHistory with(nolock) ON form.UUID_FORM = formHistory.UUID_FORM AND taskH.FORM_VERSION = formHistory.FORM_VERSION\n"
				+"JOIN MS_STATUSTASK sts with(nolock) ON taskH.UUID_STATUS_TASK = sts.UUID_STATUS_TASK\n"
				+"JOIN MS_PRIORITY prt with(nolock) ON taskH.UUID_PRIORITY = prt.UUID_PRIORITY\n"
				+"LEFT JOIN AM_MSUSER usr with(nolock) ON taskH.UUID_MS_USER = usr.UUID_MS_USER\n"
				+"LEFT JOIN MS_BRANCH branch with(nolock) ON usr.UUID_BRANCH = branch.UUID_BRANCH\n"
				+"LEFT JOIN AM_MSUSER spv with(nolock) ON usr.SPV_ID = usr.UUID_MS_USER\n"
				+"WHERE 1=1\n"
				+ queryConditions
				+"UNION\n"
				+"SELECT CONVERT(VARCHAR(20), taskH.UUID_TASK_H), '0' [FLAG_FINAL], taskH.APPL_NO, form.FORM_NAME, sts.STATUS_TASK_DESC, prt.PRIORITY_DESC,\n"
				+"	spv.UNIQUE_ID [SPV], usr.UNIQUE_ID, usr.FULL_NAME, branch.BRANCH_NAME, CONVERT(VARCHAR(20), taskH.APPROVAL_DATE, 120) AS TANGGAL_APPROVAL, taskH.CUSTOMER_NAME,\n"
				+"	ISNULL(REPLICATE('*', LEN(taskH.CUSTOMER_PHONE)), '*') CUSTOMER_PHONE,\n"
				+"	ISNULL(REPLICATE('*', LEN(taskH.CUSTOMER_ADDRESS)), '*') CUSTOMER_ADDRESS, \n"
				+"	taskH.NOTES, CONVERT(VARCHAR(10), taskH.SUBMIT_DATE, 120) AS TANGGAL,\n"
				+"	CONVERT (VARCHAR(10), taskH.SUBMIT_DATE, 108) AS JAM\n"
				+"FROM FINAL_TR_TASK_H taskH with(nolock)\n"
				+"JOIN MS_FORM form with(nolock) ON taskH.UUID_FORM = form.UUID_FORM\n"
				+"JOIN MS_FORMHISTORY formHistory with(nolock) ON form.UUID_FORM = formHistory.UUID_FORM AND taskH.FORM_VERSION = formHistory.FORM_VERSION\n"
				+"JOIN MS_STATUSTASK sts with(nolock) ON taskH.UUID_STATUS_TASK = sts.UUID_STATUS_TASK\n"
				+"JOIN MS_PRIORITY prt with(nolock) ON taskH.UUID_PRIORITY = prt.UUID_PRIORITY\n"
				+"LEFT JOIN AM_MSUSER usr with(nolock) ON taskH.UUID_MS_USER = usr.UUID_MS_USER\n"
				+"LEFT JOIN MS_BRANCH branch with(nolock) ON usr.UUID_BRANCH = branch.UUID_BRANCH\n"
				+"LEFT JOIN AM_MSUSER spv with(nolock) ON usr.SPV_ID = usr.UUID_MS_USER\n"
				+"WHERE 1=1\n"
				+ queryConditions;
		
		List listTask = this.getManagerDAO().selectAllNativeString(queryListTask, params);
		String result[][] = new String[listTask.size() + 1][HEADER.length + HEADER_REFID.length];
		
		//Row Header
		for(int i = 0; i < HEADER.length; i++) {
			int idx = i;
			String value = HEADER[i];
			result[0][idx] = value; 
			log("rowHeader=" + idx + ", " + value);
		}
		for(int i = 0; i < HEADER_REFID.length; i++) {
			int idx = HEADER.length + i;
			String value = HEADER_REFID[i][0];
			result[0][idx] = value;
			log("rowHeader=" + idx + ", " + value);
		}
		
		//Row Data
		if(listTask != null) {
			Iterator list = listTask.iterator();
			int rowCount = 1;
			while(list.hasNext()) {
				Map<String, String> tempTask = new HashMap();
				tempTask = (Map<String, String>) list.next();
				
				for(int i = 0; i < HEADER.length; i++) {
					int idx = i;
					String value = tempTask.get("d" + (i + 2));
					if(value == null) {
						value = "";
					}
					result[rowCount][idx] = value;
					log("rowData=" + idx + ", " + value);
					
				}
				Map<String, String> tempDetail = getTaskD(tempTask.get("d0").toString(), tempTask.get("d1"));
				for(int i = 0; i < HEADER_REFID.length; i++) {
					int idx = HEADER.length + i;
					String value = tempDetail.get("d" + i);
					if(value == null) {
						value = "";
					}
					result[rowCount][idx] = value;
					log("rowData=" + idx + ", " + value);
				}
				rowCount++;
			}
		}
		
		return result;
	}
	
	private Map<String, Object> getSearch(String rptParams) {
		
		Map<String, Object> result = new HashMap();
		Map<String, String> searchParams = new HashMap();
		searchParams = gson.fromJson(rptParams, searchParams.getClass());
		
		String searchConditions = "";
		Stack<Object[]> stackParams = new Stack<>();
		
		String uuidForm = searchParams.get("uuidForm");
		String uuidVersion = searchParams.get("uuidVersion");
		String uuidBranch = searchParams.get(paramUuidBranch);
		String uuidSpv = searchParams.get("uuidSpv");
		String uuidUser = searchParams.get("uuidUser");
		String startDate = searchParams.get("startDate");
		String endDate = searchParams.get("endDate");
		
		if(!uuidForm.isEmpty()) {
			searchConditions += "AND form.UUID_FORM = :uuidForm\n";
			stackParams.push(new Object[] {"uuidForm", uuidForm});
		}
		if(!uuidVersion.isEmpty() ) {
			searchConditions += "AND formHistory.UUID_FORM_HISTORY = :uuidVersion\n";
			stackParams.push(new Object[] {"uuidVersion", uuidVersion});
		}
		if(!uuidBranch.isEmpty()) {
			searchConditions += "AND usr.UUID_BRANCH = :uuidBranch\n";
			stackParams.push(new Object[] {paramUuidBranch, uuidBranch});
		}
		if(!uuidSpv.isEmpty()) {
			searchConditions += "AND usr.SPV_ID = :uuidSpv\n";
			stackParams.push(new Object[] {"uuidSpv", uuidSpv});
		}
		if(!uuidUser.isEmpty()) {
			searchConditions += "AND usr.UUID_MS_USER = :uuidUser\n";
			stackParams.push(new Object[] {"uuidUser", uuidUser});
		}
		if(!startDate.isEmpty() && !endDate.isEmpty()) {
			searchConditions += "AND taskH.ASSIGN_DATE BETWEEN :startDate AND :endDate\n";
			stackParams.push(new Object[] {"startDate", startDate});
			stackParams.push(new Object[] {"endDate", endDate});
		}
		
		Object[][] params = new Object[stackParams.size()][];
		for (int i = 0; i < stackParams.size(); i++) {
			Object[] object = stackParams.get(i);
			params[i] = object;
		}
		
		result.put("searchConditions", searchConditions);
		result.put("params", params);
		
		return result;
	}
	private Map<String, String> getTaskD(String uuidTaskH, String isFinal) {//[index][{0 = RefId, 1 = Value}]
		
		String tableTaskD = "1".equalsIgnoreCase(isFinal)?"FINAL_TR_TASK_D":"TR_TASK_D";
		String query = "SELECT A.REF_ID,\n"
				+"		CASE \n"
				+"			WHEN A.REF_ID IN(:refid)\n"
				+"			THEN ISNULL(REPLICATE('*', LEN(A.VALUE)), '*')\n"
				+"		ELSE A.VALUE\n"
				+"		END [VALUE]\n"
				+"FROM (\n"
				+"		SELECT qst.REF_ID,\n"
				+"			CASE\n"
				+"				WHEN atype.ANSWER_TYPE_NAME IN (\n"
				+"					'LuOffline'\n"
				+"					,'LuOffline with Suggestion'\n"
				+"				) THEN ISNULL(ISNULL(taskD.OPTION_TEXT, taskD.INT_OPTION_TEXT), '')\n"
				+"				WHEN atype.ANSWER_TYPE_NAME IN ( \n"
				+"					'Radio'\n"
				+"					,'Dropdown'\n"
				+"				) THEN ISNULL((Select description from ms_lov where uuid_lov = (ISNULL(taskD.LOV_ID, taskD.INT_LOV_ID))), '')\n"
				+"			ELSE ISNULL(ISNULL(taskD.TEXT_ANSWER, taskD.INT_TEXT_ANSWER), '')\n"
				+"			END [VALUE]\n"
				+"		FROM " + tableTaskD + " taskD with(nolock)\n"
				+"		JOIN MS_QUESTION qst with(nolock) ON taskD.UUID_QUESTION = qst.UUID_QUESTION\n"
				+"		JOIN MS_ANSWERTYPE atype with(nolock) ON qst.UUID_ANSWER_TYPE = atype.UUID_ANSWER_TYPE\n"
				+"		WHERE UUID_TASK_H = :uuidTaskH\n"
				+") A\n";
		Object[][] detailParams = {{"uuidTaskH", uuidTaskH}, {"refid", GlobalVal.MASKING_PRE_SURVEY_REFID_LIST}};
		
		List listDetail = this.getManagerDAO().selectAllNativeString(query, detailParams);
		Map<String, String> result = new HashMap();
		
		if(listDetail != null) {
			Iterator list = listDetail.iterator();
			Map<String, String> tempList = new HashMap();
			while(list.hasNext()) {
				Map detail = (Map) list.next();
				String refId = (String) detail.get("d0");
				String value = (String) detail.get("d1");
				
				//CR_KBIJ result history
				if(refId.contains("COL_JMLKTR_BDEB")) {
					String[] values = StringUtils.split(value, "\n");
					int len = values.length;
					tempList.put(refId, len > 0 ? values[0] : "");
					for (int i = 1; i < 5; i++) {
						 int idx = i + 1;
						 if (len > i) {
							 tempList.put(refId + "_" + idx, values[i]);
						 } else {
							 tempList.put(refId + "_" + idx, "");
						 }
					}
				}else {
					tempList.put(refId, value);					
				}
			}
			for(int i = 0; i < HEADER_REFID.length; i++) {
				int idx = i;
				String value = tempList.get(HEADER_REFID[i][1]);
				if(value == null) {
					value = "";
				} else {
					value = formatValue(HEADER_REFID[i][0], value);
				}
				result.put("d" + idx, value);
			}
		}
		return result;
	}
	
	private HSSFWorkbook createXls(String sheetName, String[][] reportData) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet(sheetName);
		
		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1000);
		styleHeader.setFont(fontHeader);
		
		//int columnWidth = 30 * 256;
		
		if(reportData != null) {
			for(int i = 0; i < reportData.length; i++) {
				HSSFRow row = sheet.createRow(i);
				//sheet.setColumnWidth(i, columnWidth);
				for(int j = 0; j < reportData[i].length; j++) {
					HSSFCell cell = row.createCell(j);
					if(i == 0) {
						cell.setCellStyle(styleHeader);
					}
					cell.setCellValue(reportData[i][j]);
				}
			}
		}
		return workbook;
	}
	
	private String formatValue(String key, String value) {//Key from HEADER_REFID[][0]
		String result = value;
		
		log("formatValue[key=" + key + ", " + value + " > " + result + "]");
		return result;
	}
}
