package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFFormulaEvaluator;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ReportUserMemberLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsgroup;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportUserMemberLogic extends BaseLogic implements 
		ReportUserMemberLogic, MessageSourceAware {
	private static final String[] HEADER_SUMMARY = { "No", "Branch Name",
		"User Web", "User Mobile", "User Active","User In-Active" };
	private static final int[] SUMMARY_COLUMN_WIDTH = { 10 * 256, 20 * 256, 20 * 256,
		20 * 256, 20 * 256, 20 * 256, 20 * 256};
	private static final String[] HEADER_DETAIL = { "No", "User Name", "Login ID", "Branch Name",
		"Job", "Group", "Date Create", "Status", "Locked", "Last Locked", "Last Logged In", "Device ID" };
	private static final int[] DETAIL_COLUMN_WIDTH = { 10 * 256, 20 * 256, 20 * 256, 20 * 256,
		20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256};
	private static final Logger LOG = LoggerFactory.getLogger(GenericReportUserMemberLogic.class);
	
	@Autowired
    private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	@Override
	public List<Map<String,Object>> getBranchListCombo(String branchId,
			AuditContext callerId) {
		List<Map<String,Object>> result;
		String[][] params={{"branchId",branchId}};
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}
	
	@Override
	public List<AmMsgroup> getGroupListCombo(String uuidMsSub, AuditContext callerId) {
		Map<String,Object> result;	
		List<AmMsgroup> listGroup;
		Object[][] params = {{Restrictions.eq("amMssubsystem.uuidMsSubsystem",
				Long.valueOf(uuidMsSub))},{Restrictions.eq("isActive","1")}};
		result = this.getManagerDAO().list(AmMsgroup.class,params,null);
		listGroup = (List<AmMsgroup>) result.get(GlobalKey.MAP_RESULT_LIST);
		return listGroup;
	}
	
	@Override
	public List getReportUserMember(String[][] params, AuditContext callerId) {
		List result = null;	
		AmMsuser user = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", 
			new Object[][] {{"uuidMsUser", Long.valueOf(params[1][1])}});
		String[][] paramsQuery = {
				{ "branchId", String.valueOf(user.getMsBranch().getUuidBranch()) },
				{ "startDate", params[2][1] }, { "endDate", params[3][1] },
				{ "uuidMsSubSystem", params[4][1] } };
		result = this.getManagerDAO().selectAllNative(
				"report.usermember.getListReportAll", paramsQuery, null);
		return result;
	}
	
	@Override
	public Map<String, Object> getReportUserMemberDetail(String[][] params, AuditContext callerId){
		Map<String, Object> result = new HashMap<>();
		if ("%".equalsIgnoreCase(params[0][1])) {
			AmMsuser user = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", 
					new Object[][] {{"uuidMsUser", Long.valueOf(params[1][1])}});
			String[][] paramsQuery = {{"branchId",String.valueOf(user.getMsBranch().getUuidBranch())},
					{"startDate",params[2][1]},
					{"endDate",params[3][1]},
					{"uuidMsSubSystem",params[4][1]},
					{"groupId",params[5][1]},
					{"searchName",params[6][1]},
					{"start",params[7][1]},
					{"end",params[8][1]}};
			List resultList = this.getManagerDAO().selectAllNative(
					"report.usermember.getListReportDetailAll", paramsQuery,null);
			result.put("resultList", resultList);
				
				//clear :start, :end params, not needed for count query

			String[][] paramQueryCount = {{"branchId",String.valueOf(user.getMsBranch().getUuidBranch())},
					{"startDate",params[2][1]},
					{"endDate",params[3][1]},
					{"uuidMsSubSystem",params[4][1]},
					{"groupId",params[5][1]},
					{"searchName",params[6][1]}};
			Integer resultCount = (Integer) this.getManagerDAO().selectOneNative(
					"report.usermember.countListReportDetailAll", paramQueryCount);
			result.put("resultSize", resultCount);
		}
		else {
			String[][] paramsQuery = {{"branchId",params[0][1]},
					{"startDate",params[2][1]},
					{"endDate",params[3][1]},
					{"uuidMsSubSystem",params[4][1]},
					{"groupId",params[5][1]},
					{"searchName",params[6][1]},
					{"start",params[7][1]},
					{"end",params[8][1]}};
			List resultList = this.getManagerDAO().selectAllNative(
					"report.usermember.getListReportDetail", paramsQuery,null);
			result.put("resultList", resultList);
				
			//clear :start, :end params, not needed for count query

			String[][] paramsQueryCount = {{"branchId",params[0][1]},
					{"startDate",params[2][1]},
					{"endDate",params[3][1]},
					{"uuidMsSubSystem",params[4][1]},
					{"groupId",params[5][1]},
					{"searchName",params[6][1]}};
			Integer resultCount = (Integer) this.getManagerDAO().selectOneNative(
					"report.usermember.countListReportDetail", paramsQueryCount);
			result.put("resultSize", resultCount);
		}
		return result;
	}
	
	@Override
	public int getReportUserMemberDetailCount(String[][] params, AuditContext callerId) {
		int size = 0;
		if ("%".equalsIgnoreCase(params[0][1])) {
			AmMsuser user = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(params[1][1])}});
			String[][] paramsQuery = {{"branchId",String.valueOf(user.getMsBranch().getUuidBranch())},
					{"startDate",params[2][1]},
					{"endDate",params[3][1]},
					{"uuidMsSubSystem",params[4][1]},
					{"groupId",params[5][1]},
					{"searchName",params[6][1]}};
			size = (Integer)this.getManagerDAO().selectOneNative(
					"report.usermember.getListReportDetailAllCount", paramsQuery);
		}
		else {
			String[][] paramsQuery = {{"branchId",params[0][1]},
					{"startDate",params[2][1]},
					{"endDate",params[3][1]},
					{"uuidMsSubSystem",params[4][1]},
					{"groupId",params[5][1]},
					{"searchName",params[6][1]}};
			size = (Integer)this.getManagerDAO().selectOneNative(
					"report.usermember.getListReportDetailCount", paramsQuery);
		}
		return size;
	}
	
	@Override
	public byte[] exportExcel(String[][] params, String type, AuditContext callerId) {	
		XSSFWorkbook workbook = this.createXlsTemplate(params,type,callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(String[][] params, String type,
			AuditContext callerId) {
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setUuidBranch(params[0][1]);
		reportBean.setUuidLoginId(params[1][1]);
		reportBean.setStartDate(params[2][1]);
		reportBean.setEndDate(params[3][1]);
		reportBean.setSubsystemCode(params[4][1]);
		reportBean.setGroupId(params[5][1]);
		reportBean.setSearchName(params[6][1]);
		reportBean.setParamsAction(params);
		reportBean.setType(type);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

		AmMsuser amMsUser = new AmMsuser();
		amMsUser.setUuidMsUser(Long.valueOf(callerId.getCallerId()));
		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("User Member");
		trReportResultLog.setRptType(FilterType.FILTER_BY_USER_MEMBER.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}
	
	public XSSFWorkbook createXlsTemplate(String[][] params, String type, AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Report User Member");
			if (type.equalsIgnoreCase("0")) {
				List result= this.getReportUserMember(params, callerId);
				this.createDataSummary(workbook, sheet, result, params[2][1], params[3][1]);
			}
			else {
				List result= this.getReportUserMemberDetailReport(params, callerId);
				this.createDataDetail(workbook,sheet,result, params[2][1], params[3][1]);
			}
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private void createDataSummary(XSSFWorkbook workbook, XSSFSheet sheet,
			List result, String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell = 0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_SUMMARY.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT SUMMARY PERIOD " + 
					startDate.substring(0, 10) + " - " + endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(HEADER_SUMMARY[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, SUMMARY_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_SUMMARY.length-1));
		//Data row
		for (int i = 0; i < result.size(); i++){
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			//data cell
			for(int j = 1; j < HEADER_SUMMARY.length; j++){
				XSSFCell cell = rowData.createCell(j);
				if (j == 1) { //Set data to string
					cell.setCellValue(temp.get("d"+(j-1)).toString());
					cell.setCellStyle(styles.get("cell"));
				}
				else {
					cell.setCellValue(Integer.parseInt(temp.get("d"+(j-1)).toString()));
					cell.setCellStyle(styles.get("cell"));
				}
			}
		} 
		//Total Data
		if (!result.isEmpty()) {
			XSSFRow rowData = sheet.createRow(rowcell++);
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue("Total");
			cellNo.setCellStyle(styles.get("header"));
			XSSFCell cell = rowData.createCell(1);
			String ref = (char)('B') + "3:" + (char)('B') + Integer.toString(result.size()+2);
	        cell.setCellFormula("COUNTA(" + ref + ")");	
	        cell.setCellStyle(styles.get("header"));
	        
	        for (int i = 0; i < HEADER_SUMMARY.length-2; i++) {    // -2  karena sudah cell A dan B sudah diisi
	        	cell = rowData.createCell(i+2);
	        	ref = (char)('C'+i) + "3:" + (char)('C'+i) + Integer.toString(result.size()+2);
	            cell.setCellFormula("SUM(" + ref + ")");
	            cell.setCellStyle(styles.get("header"));
	        }
		}
        XSSFFormulaEvaluator.evaluateAllFormulaCells(workbook);
        //End Total Data       	
	}
	
	private void createDataDetail(XSSFWorkbook workbook, XSSFSheet sheet,
			List result, String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_DETAIL.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT DETAIL PERIOD "+ 
					startDate.substring(0, 10) + " - " + endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(HEADER_DETAIL[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, DETAIL_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_DETAIL.length-1));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			//data cell
			for(int j = 1; j < HEADER_DETAIL.length; j++){
				XSSFCell cell = rowData.createCell(j);
				if (HEADER_DETAIL[j].equalsIgnoreCase("status")) {
					cell.setCellValue(temp.get("d"+(j-1)).toString().equalsIgnoreCase("1") ? "Active":"Inactive");
					cell.setCellStyle(styles.get("cell"));
				}
				else {
					cell.setCellValue(temp.get("d"+(j-1)) == null || temp.get("d"+(j-1)).toString()
							.equalsIgnoreCase("") ? "-":temp.get("d"+(j-1)).toString());
					cell.setCellStyle(styles.get("cell"));	
				}
			}
		}       	
	}	
	
	private static Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style;

        style = wb.createCellStyle();
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styles.put("cell", style);

        style = wb.createCellStyle();
        Font font = wb.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(CellStyle.SOLID_FOREGROUND);
        styles.put("header",style );
        return styles;
    }

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog,
			AuditContext callerId) {	
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(reportBean.getParamsAction(),
				reportBean.getType(),callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("UserMember_");
			
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
				reportBean.setUuidBranch(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				MsBranch msBranch = this.getManagerDAO().selectOne(
						MsBranch.class, Long.valueOf(reportBean.getUuidBranch()));
				sb.append(msBranch.getBranchCode());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_GROUP-");
			if ("0".equals(reportBean.getGroupId()) || "%".equals(reportBean.getGroupId())) {
				reportBean.setGroupId(null);
			}
			if (StringUtils.isNotBlank(reportBean.getGroupId())) {
				AmMsgroup msGroup = this.getManagerDAO().selectOne(AmMsgroup.class, 
						Long.valueOf(reportBean.getGroupId()));
				sb.append(msGroup.getGroupname());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_NAME-");
			if ("0".equals(reportBean.getSearchName()) || "%".equals(reportBean.getSearchName())) {
				reportBean.setSearchName(null);
			}
			if (StringUtils.isNotBlank(reportBean.getSearchName())) {
				sb.append(reportBean.getSearchName());
			} 
			else {
				sb.append("ALL");
			}
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(
				trReportResultLog.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime()
				.getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}
	
	@Override
	public List getReportUserMemberDetailReport(String[][] params, AuditContext callerId){
		List result = null;	
		if (params[0][1].equalsIgnoreCase("%")) {
			AmMsuser user = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", 
					new Object[][] {{"uuidMsUser", Long.valueOf(params[1][1])}});
			String[][] paramsQuery = {{"branchId",String.valueOf(user.getMsBranch().getUuidBranch())},
					{"startDate",params[2][1]},
					{"endDate",params[3][1]},
					{"uuidMsSubSystem",params[4][1]},
					{"groupId",params[5][1]},
					{"searchName",params[6][1]}}; 
			result = this.getManagerDAO().selectAllNative(
					"report.usermember.getListReportDetailAllReport", paramsQuery,null);
		}
		else {
			String[][] paramsQuery = {{"branchId",params[0][1]},
					{"startDate",params[2][1]},
					{"endDate",params[3][1]},
					{"uuidMsSubSystem",params[4][1]},
					{"groupId",params[5][1]},
					{"searchName",params[6][1]}};
			result = this.getManagerDAO().selectAllNative("report.usermember.getListReportDetailReport", 
					paramsQuery,null);
		}
		return result;
	}
}