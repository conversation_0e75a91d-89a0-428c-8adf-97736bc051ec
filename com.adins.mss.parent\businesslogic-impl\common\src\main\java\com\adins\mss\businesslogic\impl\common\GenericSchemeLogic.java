package com.adins.mss.businesslogic.impl.common;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.SchemeLogic;
import com.adins.mss.services.model.common.SchemePrintBean;
@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericSchemeLogic extends BaseLogic implements SchemeLogic {	
//	private static final Logger LOG = LoggerFactory.getLogger(GenericSchemeLogic.class);

	/* =================== LIST SCHEME ===================== */
	@Override
	public List listScheme(AuditContext callerId){
		List listScheme = new ArrayList();
		Object[][] user = { {"uuidMsUser", NumberUtils.toLong(callerId.getCallerId())} };
		
		List form = this.getManagerDAO().selectAllNative("services.common.form.getFormFieldUser", user, null);
		List[] xx = new List[form.size()];
		int i = 0;
		if (form != null) {
			Iterator lp = form.iterator();
			while (lp.hasNext()) {
				Map map = (Map) lp.next();
				Object[][] scheme = {{"uuidForm", map.get("d0")}};
				List formList = this.getManagerDAO().selectAllNative("services.common.form.getList", scheme, null);
				if (!formList.isEmpty()) {
					xx[i] = formList;
					i++;
				}
			}
			
			for (int j=0; j < i; j++) {
				Map<String, Object> maps = new HashMap<String, Object>();
				Iterator itr = xx[j].iterator();
				while (itr.hasNext()) {
					Map mp = (Map) itr.next();
					if ((Object) mp.get("d1") != null && !"".equals((Object) mp.get("d1"))) {
						maps.put((String) mp.get("d0"), (Object) mp.get("d1"));
					}
				}
				if (null != maps || !maps.isEmpty()) {
					listScheme.add(maps);
				}
			}				
		}
		return listScheme;
	}
	
	/* ========================== SCHEME ONE ========================= */
	@Override
	public List schemeOne(long uuidForm, AuditContext callerId) {
		List listScheme = new ArrayList();
		Object[][] params = {{"uuidForm", uuidForm}};
		List getOne = (List) this.getManagerDAO().selectAllNative("services.common.form.getOne", params, null);
		Map<String, Object> mapSO = new HashMap<String, Object>();
		Iterator itr = getOne.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			if ((Object) mp.get("d1") != null && !"".equals((Object) mp.get("d1"))) {
				mapSO.put((String) mp.get("d0"), (Object) mp.get("d1"));
			}
		}
		listScheme.add(mapSO);
		return listScheme;	
	}
	
	/* ========================== Print Items ======================== */
	@Override
	public List listPrintItems(AuditContext callerId){
		List item = new ArrayList();
		Object[][] id = { {"uuidMsUser", NumberUtils.toLong(callerId.getCallerId())} };
		List uuid = this.getManagerDAO().selectAllNative("services.common.form.getPrintForm", id, null);
		Iterator itr = uuid.iterator();
		while (itr.hasNext()) {
			Map map = (Map) itr.next();				
			Object[][] form = {{"uuidForm", map.get("d0")}};
			List items = this.getManagerDAO().selectAllNative("services.common.form.getPrintItem1", form, null);
			Iterator tr = items.iterator();
			while (tr.hasNext()) {
				Map imap = (Map) tr.next();
				SchemePrintBean bean = new SchemePrintBean();
				bean.setLineSeqOrder((Integer) imap.get("d0"));
				bean.setUuidForm(imap.get("d1").toString());
				bean.setUuidPrintItemOfForm(imap.get("d2").toString());
				bean.setCode((String) imap.get("d3"));
				bean.setPrintItemLabel((String) imap.get("d4"));
				bean.setUuidQuestion((imap.get("d5") != null ? imap.get("d5").toString() : null));
				bean.setUuidQuestionGroup((imap.get("d6") != null ? imap.get("d6").toString() : null));
				item.add(bean);
			}							
		}
		return item;
	}

    @Override
    public List listPrintItems(List listScheme, AuditContext callerId) {
		if (listScheme == null || listScheme.isEmpty()) {
			return Collections.emptyList();
		}
		
        List item = new ArrayList();
        Iterator itr = listScheme.iterator();
        while (itr.hasNext()) {
            Map map = (Map) itr.next();
            if (!("1".equals((String) map.get("is_printable")) &&
                    "1".equals((String) map.get("is_active")))) {
                continue;
            }
            
            String[][] form = {{"uuidForm", (String) map.get("uuid_scheme")}};
            List items = this.getManagerDAO().selectAllNative("services.common.form.getPrintItem1", form, null);
            Iterator tr = items.iterator();
            while (tr.hasNext()) {
                Map imap = (Map) tr.next();
                SchemePrintBean bean = new SchemePrintBean();
                bean.setLineSeqOrder((Integer) imap.get("d0"));
                bean.setUuidForm(imap.get("d1").toString());
                bean.setUuidPrintItemOfForm(imap.get("d2").toString());
                bean.setCode((String) imap.get("d3"));
                bean.setPrintItemLabel((String) imap.get("d4"));
                bean.setUuidQuestion((imap.get("d5") == null) ? null : imap.get("d5").toString());
                bean.setUuidQuestionGroup((imap.get("d6") == null) ? null : imap.get("d6").toString());
                item.add(bean);
            }                           
        }
        
        return item;
    }
	
	@Override
	public List listPrint(long uuidForm, AuditContext callerId){
		List item = new ArrayList();
		Object[][] id = { {"uuidForm", uuidForm} };
		List uuid = this.getManagerDAO().selectAllNative("services.common.form.getUuidPrint", id, null);
		List[] xx = new List[uuid.size()];
		int j = 0;
		if(uuid != null){
			Iterator lp = uuid.iterator();
			while (lp.hasNext()){
				Map map = (Map) lp.next();
				if(map.get("d0") != null){
					Object[][] form = {{"uuidPrint", (BigInteger) map.get("d0")}, {"uuidForm", uuidForm}};
					xx[j] = this.getManagerDAO().selectAllNative("services.common.form.getPrintItem", form, null);
				}
				j++;
			}
			int n=0;
			if (xx[n] != null) {
				for (int k=n; k<xx.length; k++){
					Map<String, Object> print = new HashMap<String, Object>();
					Iterator itr = xx[k].iterator();
					while (itr.hasNext()) {
						Map mp = (Map) itr.next();
						if((Object) mp.get("d1") != null && !"".equals((Object) mp.get("d1"))){
							print.put((String) mp.get("d0"), (Object) mp.get("d1"));
						}
					}
					item.add(print);
				}
			}
		}
		return item;
	}
}
