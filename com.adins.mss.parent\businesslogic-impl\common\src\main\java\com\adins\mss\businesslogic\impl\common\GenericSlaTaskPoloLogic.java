package com.adins.mss.businesslogic.impl.common;

import java.math.BigInteger;
import java.rmi.RemoteException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.SlaTaskPoloLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;

public class GenericSlaTaskPoloLogic extends BaseLogic implements SlaTaskPoloLogic, MessageSourceAware {
	
	@Autowired
	private MessageSource messageSource;
	private CommonLogic commonLogic;
	private IntFormLogic intFormLogic;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericSlaTaskPoloLogic.class);

	@Transactional
	@Override
	public void processSlaTaskVisitIn(Map<String, Object> mapBean, AuditContext callerId) {
		AmGeneralsetting slaTaskIn = this.commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_SLA_IN_HANDLING, callerId);
		BigInteger uuidTask = (BigInteger) mapBean.get("d0");
		TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTask.longValue());
		boolean isInSla = false;
		Date createDate = taskH.getDtmCrt();
		Date currentTimestamp = new Date();
		
		Object[][] paramGroupTask = {{"uuidTaskH", taskH.getUuidTaskH()}};
		StringBuilder queryGroupTask = new StringBuilder(" SELECT GROUP_TASK_ID ")
				.append(" FROM MS_GROUPTASK WITH(NOLOCK) ")
				.append(" WHERE UUID_TASK_H = :uuidTaskH ");
		BigInteger groupTaskId = (BigInteger) this.getManagerDAO().selectOneNativeString(queryGroupTask.toString(), paramGroupTask);
		if (null != groupTaskId && groupTaskId.longValue()!=taskH.getUuidTaskH()) {
			TrTaskH firstTask = this.getManagerDAO().selectOne(TrTaskH.class, groupTaskId.longValue());
			createDate = firstTask.getPromiseDate();
		}
		
		long diffInMillies = currentTimestamp.getTime() - createDate.getTime();
		long diffDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
		String gsValue = slaTaskIn.getGsValue();
		
		long sla = StringUtils.isNotBlank(gsValue) ? Long.valueOf(gsValue) : 0;
		if (diffDays < sla) {
			isInSla = true;
		}

		if (!isInSla) {
			AmMssubsystem subs = this.commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
			MsStatustask stts = this.commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_DELETED, subs.getUuidMsSubsystem(), callerId);
			MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_DELETED, callerId);

			String actor = "SCHEDULER";
			
			//UPDATE STATUS TASK DELETED
			taskH.setMsStatustask(stts);
			taskH.setMsStatusmobile(msm);
			taskH.setDtmUpd(new Date());
			taskH.setUsrUpd(actor);
			this.getManagerDAO().update(taskH);
			
			// INSERT INTO TR TASK HISTORY
			String notes = this.messageSource.getMessage(
		               "businesslogic.notes.changestatus.in", null, this.retrieveLocaleAudit(callerId));
			
			TrTaskhistory trTaskHistory = new TrTaskhistory(
					taskH.getMsStatustask(), taskH,
					actor, new Date(),
					notes, null,
					actor,
					GlobalVal.CODE_PROCESS_DELETED);
			this.getManagerDAO().insert(trTaskHistory);
									
			intFormLogic.updateDataPolo(null, taskH, "DATA UNHANDLED", "Deleted", "T", null, null, null, null, callerId);
		}
	}

	@Transactional
	@Override
	public void processSlaTaskVisitOut(Map<String, Object> mapBean, AuditContext callerId) {
		AmMssubsystem subs = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		MsStatustask stts = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_DELETED, subs.getUuidMsSubsystem(), callerId);
		MsStatusmobile msm = commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_DELETED, callerId);
		
		BigInteger uuidTask = (BigInteger) mapBean.get("d0");
		TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTask.longValue());
		
		String actor = "SCHEDULER";
		
		//UPDATE STATUS TASK DELETED
		taskH.setMsStatustask(stts);
		taskH.setMsStatusmobile(msm);
		taskH.setDtmUpd(new Date());
		taskH.setUsrUpd(actor);
		this.getManagerDAO().update(taskH);
		
		// INSERT INTO TR TASK HISTORY
		String notes = this.messageSource.getMessage(
	               "businesslogic.notes.changestatus.in", null, this.retrieveLocaleAudit(callerId));
		
		TrTaskhistory trTaskHistory = new TrTaskhistory(
				taskH.getMsStatustask(), taskH,
				actor, new Date(),
				notes, null,
				actor,
				GlobalVal.CODE_PROCESS_DELETED);
		this.getManagerDAO().insert(trTaskHistory);
		
		intFormLogic.updateDataPolo(null, taskH, "DATA UNHANDLED", "Deleted", "T", null, null, null, null, callerId);
	}

	@Transactional(readOnly=true)
	@Override
	public List<Map<String, Object>> getSlaTaskVisitIn(AuditContext callerId) {
		List<Map<String, Object>> resultList = null;
		Object[][] params = {{"formPolo", GlobalVal.FORM_VISIT_POLO}, {"statusPending", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION}};
		resultList = this.getManagerDAO().selectAllNativeString("SELECT UUID_TASK_H FROM TR_TASK_H TTH WITH(NOLOCK) "
				+ "JOIN MS_FORM MSF WITH(NOLOCK) ON MSF.UUID_FORM = TTH.UUID_FORM "
				+ "JOIN MS_STATUSTASK MST WITH(NOLOCK) ON MST.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK "
				+ "WHERE FORM_NAME = :formPolo "
				+ "AND STATUS_CODE = :statusPending ", params);
		
		LOG.info("SLA Task Visit In Handling, found : {} tasks", resultList == null ? 0 : resultList.size());
		return resultList;
	}

	@Transactional(readOnly=true)
	@Override
	public List<Map<String, Object>> getslaTaskVisitOut(AuditContext callerId) {
		List<Map<String, Object>> resultList = null;
		AmGeneralsetting slaTaskOut = this.commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_SLA_OUT_HANDLING, callerId);
		if (null != slaTaskOut) {
			Object[][] params = {{"gsCode", slaTaskOut.getGsValue()}, {"formPolo", GlobalVal.FORM_VISIT_POLO}, {"statusUnassign", GlobalVal.SURVEY_STATUS_TASK_UNASSIGN}};
			resultList = this.getManagerDAO().selectAllNativeString("SELECT UUID_TASK_H FROM TR_TASK_H TTH WITH(NOLOCK) "
					+ "JOIN MS_FORM MSF WITH(NOLOCK) ON MSF.UUID_FORM = TTH.UUID_FORM "
					+ "JOIN MS_STATUSTASK MST WITH(NOLOCK) ON MST.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK "
					+ "WHERE DATEDIFF(DAY, TTH.DTM_CRT, GETDATE()) >= :gsCode "
					+ "AND FORM_NAME = :formPolo "
					+ "AND STATUS_CODE = :statusUnassign ", params);
		}
		LOG.info("SLA Task Visit Out Handling, found : {} tasks", resultList == null ? 0 : resultList.size());
		return resultList;
	}
	
}