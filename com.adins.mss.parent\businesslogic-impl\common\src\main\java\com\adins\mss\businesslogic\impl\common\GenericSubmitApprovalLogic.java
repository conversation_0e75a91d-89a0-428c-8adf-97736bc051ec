package com.adins.mss.businesslogic.impl.common;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.SubmitApprovalLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.helper.WorkflowHelper;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;

@Deprecated
public class GenericSubmitApprovalLogic extends BaseLogic implements SubmitApprovalLogic {

	@Autowired
    private WorkflowHelper workflowHelper;

	public void setWorkflowHelper(WorkflowHelper workflowHelper) {
		this.workflowHelper = workflowHelper;
	}

	public String submitApproval(String uuidTaskH, String flag, AuditContext auditContext){
		String result = null;

			
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH tth join fetch tth.amMsuser am join fetch tth.msStatustask mst join fetch mst.amMssubsystem where tth.uuidTaskH = :uuidTaskH", 
				new Object[][] {{"uuidTaskH", Long.valueOf(uuidTaskH)}});
		trTaskH.setApprovalDate(new Date());
		this.getManagerDAO().update(trTaskH);
		
		long processId = getUuidProcess(trTaskH, trTaskH.getMsStatustask().getAmMssubsystem());
		String codeProcess = StringUtils.EMPTY;
		MsStatustask msStatustask;
		if (flag.equals("1")) {
			flag = "0";
			msStatustask = commitWfAndUpdateTaskH(trTaskH, processId, trTaskH.getMsStatustask().getAmMssubsystem(), Integer.parseInt(flag));
			insertTaskHistory(auditContext, msStatustask, trTaskH, "Task from " + trTaskH.getFlagSource() + " has been approved.", GlobalVal.CODE_PROCESS_APPROVED, auditContext.getCallerId());
			result = "Approve.";
			codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
		} 
		else {
			flag = "1";
			msStatustask = commitWfAndUpdateTaskH(trTaskH, processId, trTaskH.getMsStatustask().getAmMssubsystem(), Integer.parseInt(flag));
			insertTaskRejected(msStatustask, trTaskH, auditContext);
			result = "Rejected";
			codeProcess = GlobalVal.CODE_PROCESS_REJECTED_APPROVAL;
		}
		
		trTaskH.setApprovalDate(new Date());
		this.getManagerDAO().update(trTaskH);
		
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(auditContext.getCallerId()));
		
		if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
			commitOrder(usr, trTaskH.getNotes(), trTaskH, trTaskH.getMsStatustask().getAmMssubsystem(), Integer.parseInt(flag), codeProcess, auditContext);
		}
		return result;
	}
	
	private void insertTaskRejected(MsStatustask msStatusTask, TrTaskH trTaskH, AuditContext auditContext) {
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(auditContext.getCallerId()));
		TrTaskhistory trTaskHistory = new TrTaskhistory(msStatusTask, trTaskH, auditContext.getCallerId(),
				new Date(), trTaskH.getNotes(), trTaskH.getAmMsuser().getFullName(),
				amMsuser.getFullName(), GlobalVal.CODE_PROCESS_REJECTED_APPROVAL);
		
		this.getManagerDAO().insert(trTaskHistory);
	}
}
