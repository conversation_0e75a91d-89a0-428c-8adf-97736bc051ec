package com.adins.mss.businesslogic.impl.common;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.UnknownHostException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.codehaus.jackson.annotate.JsonProperty;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.SubmitLayerLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.CreateTaskLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.function.KbijDataLogic;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsQuestiongroup;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.TrTasksurveydata;
import com.adins.mss.model.custom.UploadImageBean;
import com.adins.mss.model.custom.UploadImageRequestBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.ImageBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.QuestionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.BiometricRequest;
import com.adins.mss.services.model.common.BiometricResponse;
import com.adins.mss.services.model.common.CheckDataEkycRequest;
import com.adins.mss.services.model.common.GetDsrFromPoloResponse;
import com.adins.mss.services.model.common.GetDsrRequest;
import com.adins.mss.services.model.common.GetDsrResponse;
import com.adins.mss.services.model.common.HistoryCreditKBIJRequest;
import com.adins.mss.services.model.common.HistoryCreditKBIJResponse;
import com.adins.mss.services.model.common.KbijData;
import com.adins.mss.services.model.common.QuestionGroupBean;
import com.adins.mss.services.model.common.SubmitLayerQuestionGroupBean;
import com.adins.mss.services.model.common.SubmitLayerRequest;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.SubmitTaskHBean;
import com.adins.mss.services.model.common.UpdateDataPoloRequest;
import com.adins.mss.services.model.common.UpdateDataPoloResponse;
import com.adins.mss.services.model.newconfins.CIFBean;
import com.adins.mss.services.model.newconfins.CheckDukcapilResponse;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericSubmitLayerLogic extends BaseLogic implements SubmitLayerLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericSubmitLayerLogic.class);
	private Gson gson = new GsonBuilder().serializeNulls().create();
	
	private ImageStorageLogic imageStorageLogic;
	private CommonLogic commonLogic;
	private GeolocationLogic geocoder;
	private CreateTaskLogic createTaskLogic;
	private IntFormLogic intFormLogic;
	private MessageSource messageSource;
	private KbijDataLogic kbijDataLogic;
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }
	
	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}
	
	public void setCreateTaskLogic(CreateTaskLogic createTaskLogic) {
		this.createTaskLogic = createTaskLogic;
	}
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	public KbijDataLogic getKbijDataLogic() {
		return kbijDataLogic;
	}

	public void setKbijDataLogic(KbijDataLogic kbijDataLogic) {
		this.kbijDataLogic = kbijDataLogic;
	}

	private String paramTaskH = "uuidTaskH";
	private String paramUuidQuestion = "msQuestion.uuidQuestion";
	private String paramUuidTaskH = "trTaskH.uuidTaskH";
	private String dateFormat = "dd/MM/yyyy";
	
	@Transactional(readOnly = false, isolation=Isolation.READ_UNCOMMITTED, propagation=Propagation.REQUIRES_NEW)
	@Override
	public Map<String, Object> doSubmitLayer(SubmitLayerRequest request, boolean isFromSubmitTask, String sendTaskPreSurvey, AuditContext auditContext) {
		Map<String, Object> resultLayer = new HashMap<>();
		Map<String, Object> packageMap = new HashMap<>();
		boolean isSubmitLayer1 = false;

		// Create Task H First
		TrTaskH trTaskH = this.getManagerDAO().selectOne("from TrTaskH h left join fetch h.msStatustask left join fetch h.msForm where h.uuidTaskH = :uuidTaskH",
				new Object[][] {{paramTaskH, Long.valueOf(request.getTaskH().getUuid_task_h())}});
		
		String query = "SELECT ISNULL(ttd.TEXT_ANSWER, ttd.INT_TEXT_ANSWER) PRODUCT_OFFERING_NAME \r\n" +
				"FROM TR_TASK_H tth WITH(NOLOCK)\r\n" + 
				"JOIN TR_TASK_D ttd ON tth.UUID_TASK_H = ttd.UUID_TASK_H\r\n" + 
				"WHERE 1=1\r\n" + 
				"	AND tth.UUID_TASK_H = :uuidTaskH\r\n" + 
				"	AND (ttd.QUESTION_TEXT = 'Product Offering Type (POT)' OR  ttd.QUESTION_TEXT = 'Produk') ";
		List<Map<String, Object>> map = this.getManagerDAO().selectAllNativeString(query, 
				new Object[][] {{"uuidTaskH", request.getTaskH().getUuid_task_h()}});
		
		String queryBranch = "SELECT	mb.BRANCH_CODE, mr.REGION_CODE, \r\n" + 
				"		ISNULL(tth.AGREEMENT_NO, '') CONTRACT_NO\r\n" + 
				"FROM TR_TASK_H tth WITH(NOLOCK)\r\n" + 
				"JOIN MS_BRANCH mb ON tth.UUID_BRANCH = mb.UUID_BRANCH\r\n" + 
				"JOIN MS_REGION mr ON mb.UUID_REGION = mr.UUID_REGION\r\n" + 
				"WHERE 1=1\r\n" + 
				"	AND tth.UUID_TASK_H = :uuidTaskH\r\n"; 
		List<Map<String, Object>> mapBranch = this.getManagerDAO().selectAllNativeString(queryBranch, 
				new Object[][] {{"uuidTaskH", request.getTaskH().getUuid_task_h()}});
		
		String queryProduct = "SELECT TOP 1 spo.PRODUCT_OFFERING_CODE\r\n" + 
				"FROM STAGING_PRODUCT_OFFERING spo WITH(NOLOCK)\r\n" + 
				"WHERE spo.PRODUCT_OFFERING_NAME = :productOfferingName";
		
		String productOfferingName = StringUtils.EMPTY;
		
		if (map != null && !map.isEmpty()) {
		    productOfferingName = String.valueOf(map.get(0).get("d0"));
		}
		
		//ambil POT dari request jika di TaskD tidak ada
		if(StringUtils.isBlank(productOfferingName)) {
			for (int i = 0; i < request.getQuestionGroup().size(); i++) {
				SubmitLayerQuestionGroupBean bean = request.getQuestionGroup().get(i);
				for (int x = 0; x < bean.getTaskD().size(); x++) {
					SubmitTaskDBean taskDBean = bean.getTaskD().get(x);
					if(taskDBean.getQuestion_label().equalsIgnoreCase("Product Offering Type (POT)")){       
						productOfferingName = taskDBean.getText_answer();
					}
				}
			}				
		}
		
		String productOfferingCode = (String) this.getManagerDAO().selectOneNativeString(queryProduct,
				new Object[][] { {"productOfferingName", productOfferingName} });
		
		if (StringUtils.isBlank(productOfferingCode)) {
			 productOfferingCode = StringUtils.isEmpty(productOfferingName)? null : productOfferingName;
		}
		
		String uuidQuestionGroupProcess = request.getUuidQuestionGroupProcess();
		String result = StringUtils.EMPTY;
		String process = StringUtils.EMPTY;
		String negativeCust = StringUtils.EMPTY;
		String errorMessage = StringUtils.EMPTY;
		String taskIdPolo = StringUtils.isNotBlank(trTaskH.getOrderNoCae())?trTaskH.getOrderNoCae():trTaskH.getTaskIdPolo();
		Integer isPreApproval = null;
		String officeCode = String.valueOf(mapBranch.get(0).get("d0"));
		String officeRegionCode = String.valueOf(mapBranch.get(0).get("d1"));
		String contractNo = String.valueOf(mapBranch.get(0).get("d2"));
		String sourceData = trTaskH.getSourceData();
		Map<String, String> mapApi = new HashMap<>();
		
		if(trTaskH.getIsPreApproval() != null) {
			isPreApproval = trTaskH.getIsPreApproval();
		}
		
		int maxRetryDukcapil = 0;
		int maxRetryBiometrik = 0; 
		
		AmGeneralsetting gensetMaxRetry = this.commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_MAX_RETRY_PRE_SURVEY, auditContext);
		if (gensetMaxRetry != null) {
		    String[] listSourceMaxRetry = gensetMaxRetry.getGsValue().split(";");
		    for (String sourceMaxRetry : listSourceMaxRetry) {
		        String[] parts = sourceMaxRetry.split(":");
		        if (parts.length < 2) {
		            continue;
		        }
		        String gensetSourceData = parts[0].trim();
		        String[] listMaxRetry = parts[1].split(",");
		        if (gensetSourceData.equalsIgnoreCase(sourceData) && listMaxRetry.length >= 3) {
		            try {
		            	if("1".equals(listMaxRetry[0])){
		            		maxRetryDukcapil = Integer.parseInt(listMaxRetry[1].trim());
		            		maxRetryBiometrik = Integer.parseInt(listMaxRetry[2].trim());		            		
		            	}
		            } catch (NumberFormatException e) {
		                LOG.error("Invalid number format: " + e.getMessage());
		            }
		            break;
		        }
		    }
		}
		
		int counterLayer = 0;
		for (int i = 0; i < request.getQuestionGroup().size(); i++) {
			SubmitLayerQuestionGroupBean bean = request.getQuestionGroup().get(i);
			if (StringUtils.isNotBlank(uuidQuestionGroupProcess) &&
					!StringUtils.equals(uuidQuestionGroupProcess, bean.getUuidQuestionGroup())) {
				continue;
			}
			
			if (StringUtils.isBlank(result) || !result.contains("Failed") || StringUtils.isNotBlank(sendTaskPreSurvey)) {
				List <Map<String, Object>> listProcessOfLayer = this.getManagerDAO().selectAllNativeString(
						"SELECT    PROCESS, UUID_QUESTION_RESULT, PARAM_API, QUESTION_PROCESS " + 
						"FROM      MS_MAPPINGSUBMITLAYER WITH (NOLOCK) " + 
						"WHERE     UUID_FORM = :uuidForm " +
						"          AND UUID_QUESTION_GROUP = :uuidQuestionGroup " + 
						"ORDER BY  PROCESS_SEQ",
						new Object[][] { {"uuidForm", request.getTaskH().getUuid_scheme()}, {"uuidQuestionGroup", bean.getUuidQuestionGroup()} });
				
				if (null != listProcessOfLayer && !listProcessOfLayer.isEmpty()) {
					String questionResult = null;
					String layerResult = null;
					Map<String, String> filter = request.getFilter().get(bean.getUuidQuestionGroup());
					if (null == filter) {
						filter = new HashMap<>();
					}
					
					Object[][] params = { { Restrictions.eq("uuidQuestionGroup", new Long(bean.getUuidQuestionGroup())) } };
					MsQuestiongroup msGroup = this.getManagerDAO().selectOne(MsQuestiongroup.class, params);
						
					SubmitTaskDBean taskDBeanResult = null;
					if (null != listProcessOfLayer.get(0).get("d1")) {
						questionResult = listProcessOfLayer.get(0).get("d1").toString();
						for (int x = 0; x < bean.getTaskD().size(); x++) {
							SubmitTaskDBean taskDBean = bean.getTaskD().get(x);
							if (StringUtils.equals(questionResult, taskDBean.getQuestion_id())) {
								layerResult = taskDBean.getText_answer();
								taskDBeanResult = taskDBean;
								break;
							}
						}
					}
					
					int resultCode = 1;
					if (StringUtils.isBlank(layerResult) || !"Success".equals(layerResult)) {
						if (!isFromSubmitTask) {
							this.processingTaskDetail(trTaskH, request.getTaskH(), bean.getTaskD(), auditContext);
						}
						
						for (int y = 0; y < listProcessOfLayer.size(); y++) {
							int resultProcess = 0;
							
							Map<String, Object> mapProcessOfLayer = listProcessOfLayer.get(y);
							String processOfLayer = String.valueOf(mapProcessOfLayer.get("d0"));
							String questionProcess = null == mapProcessOfLayer.get("d3") ? null : String.valueOf(mapProcessOfLayer.get("d3"));
							String ansOfQuestionProcess = StringUtils.isBlank(questionProcess) ? null : filter.get(questionProcess);
							
							if (null != mapProcessOfLayer.get("d2")) {
								mapApi = generateMapForParamApi(filter, String.valueOf(mapProcessOfLayer.get("d2")));
							}
							
							if (null != mapApi && !mapApi.isEmpty()) {
								mapApi.put("taskIdPolo", taskIdPolo);
								mapApi.put("officeCode", officeCode);
								mapApi.put("officeRegionCode", officeRegionCode);
								mapApi.put("productOfferingCode", productOfferingCode);
								mapApi.put("contractNo", contractNo);
								mapApi.put("IsPreApproval", String.valueOf(isPreApproval));
								
								if ("Pre Survey Layer 1".equalsIgnoreCase(msGroup.getQuestionGroupLabel())) {
									mapApi.put("callMode", "0");
									isSubmitLayer1 = true;
								} else {
									mapApi.put("callMode", "1");
								}
								
								if(StringUtils.isNotBlank(ansOfQuestionProcess)) {
									resultProcess = this.convertResultAnswerToCode(ansOfQuestionProcess, processOfLayer);
								}
								
								if (processOfLayer.equalsIgnoreCase(GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC)) {
									
									boolean isNeedHitApi = this.biometricCheckAnswer(request, bean, filter, isFromSubmitTask, trTaskH, auditContext);

									if (isNeedHitApi) {
										if (!isFromSubmitTask && (Integer.parseInt(request.getCountRetry()) > maxRetryBiometrik && maxRetryBiometrik != 0)) { 
											resultCode = 2;
											resultProcess = 1;
											process = processOfLayer;
										}else {
											resultProcess = 0;											
										}
									} else {
										resultProcess = 1;
									}
								}
								
								if (isFromSubmitTask && GlobalVal.PRE_SURVEY_PROCESS_SLIK.equals(processOfLayer)) {
									resultProcess = 1;
								}
								
								if ((!isFromSubmitTask && Integer.parseInt(request.getCountRetry()) > 1) && GlobalVal.PRE_SURVEY_PROCESS_SLIK.equals(processOfLayer)) {
									resultProcess = 1;
								}

								if (!isFromSubmitTask && (Integer.parseInt(request.getCountRetry()) > maxRetryDukcapil && maxRetryDukcapil != 0) && GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL.equals(processOfLayer)) {
									if(1 != resultProcess) { 
										resultCode = 2;
										resultProcess = 1;
										process = processOfLayer;
									}
								}
																
								if(resultProcess != 1) {
									
									if (GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL.equals(processOfLayer)) {
										resultProcess = doValidateDukcapil(mapApi,isPreApproval, auditContext);
									} else if (GlobalVal.PRE_SURVEY_PROCESS_NEGATIVE_CUST.equals(processOfLayer)) {
										resultProcess = doCheckNegativeCust(mapApi,isPreApproval, auditContext);
									} else if (GlobalVal.PRE_SURVEY_PROCESS_PHONE.equals(processOfLayer)) {
										resultProcess = doTeleStatus(mapApi,isPreApproval, auditContext);
									} else if (GlobalVal.PRE_SURVEY_PROCESS_SLIK.equals(processOfLayer)) {
										packageMap = submitLayerGetKBIJ(request, mapApi);
										resultProcess = 1;
										resultLayer.putAll(packageMap);
									} else if (GlobalVal.PRE_SURVEY_PROCESS_INCOME.equals(processOfLayer)) {
										resultProcess = doCheckIncome(mapApi, auditContext);
									} else if (GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC.equals(processOfLayer)) {
										packageMap = this.doBiometricProcess(request, mapApi, isPreApproval, bean, filter, isFromSubmitTask, trTaskH, mapProcessOfLayer, 
												productOfferingCode, sendTaskPreSurvey, auditContext);
										
										String errorBiometric = getStringFromMap(packageMap, "errorBiometric");
										int resultBiometric = getIntFromMap(packageMap, "resultBiometric");
										if (StringUtils.isBlank(errorBiometric)) {
											
											resultProcess = resultBiometric;
											packageMap.remove("resultBiometric");
											resultLayer.putAll(packageMap);
										} else {
											
											errorMessage = errorBiometric;
											resultProcess = resultBiometric;
											packageMap.remove("resultBiometric");
											packageMap.remove("errorBiometric");
											resultLayer.putAll(packageMap);												
										}
										
									}
									
									ansOfQuestionProcess = this.convertCodeToResultAnswer(resultProcess, processOfLayer);
								
									if ((StringUtils.isNotBlank(ansOfQuestionProcess) && StringUtils.isNotBlank(questionProcess)) 
											&& !GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC.equals(processOfLayer)) {
										MsQuestion msQuestion = this.commonLogic.retrieveQuestionByRefId(questionProcess, auditContext);
										this.inserTaskDResult(trTaskH, ansOfQuestionProcess, msQuestion, null, auditContext);
									}
								}

							} else if(null == mapApi || mapApi.isEmpty()) {
								resultProcess = 1;
							}
							
							if ((StringUtils.isNotBlank(questionProcess) && StringUtils.isNotBlank(ansOfQuestionProcess)) && !GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC.equals(processOfLayer)) {
								resultLayer.put(questionProcess, ansOfQuestionProcess);
								LOG.info("REF_ID: {} : {}", questionProcess, ansOfQuestionProcess);
								if ("PRE_RESULT_NEGATIVE".equalsIgnoreCase(questionProcess)) {
									negativeCust = ansOfQuestionProcess;
								}
							}
							
							if (1 == resultCode) {
								resultCode = resultProcess;
								process = processOfLayer;
							}
							
							LOG.info("result {} : {}", processOfLayer, resultProcess);
						}	
					} else if ("Success".equals(layerResult)) {
						resultCode = 1;
					}
					if (0 == resultCode) {
						if (StringUtils.isNotBlank(errorMessage)) {
							result = errorMessage;
						} else {
							result = "Error ketika proses cek " + process; 
						}
					} else if (1 == resultCode) {
						result = GlobalVal.PRE_SURVEY_RESULT_SUCCESS;
					} else if (3 == resultCode) {
						//not found dianggap sukses
						result = GlobalVal.PRE_SURVEY_RESULT_SUCCESS;
					} else {
						if (GlobalVal.FORM_PRE_SURVEY.equals(trTaskH.getMsForm().getFormName())) {
							if (GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC.equals(process) && StringUtils.isNotBlank(errorMessage)) {
								result = GlobalVal.PRE_SURVEY_RESULT_FAILED + " " + errorMessage;
							} else {
								result = GlobalVal.PRE_SURVEY_RESULT_FAILED + " on process " + process;
							}
						} else if (GlobalVal.FORM_GUARANTOR.equals(trTaskH.getMsForm().getFormName())) {
							if(GlobalVal.PRE_SURVEY_PROCESS_INCOME.equals(process)) {
								result = GlobalVal.PRE_SURVEY_RESULT_SUCCESS;
							} else {
								result = GlobalVal.GUARANTOR_RESULT_FAILED + " on process " + process;
							}
							
						}
					}
					if (!isFromSubmitTask || (StringUtils.isNotBlank(sendTaskPreSurvey) && StringUtils.isNotBlank(questionResult))) {
						MsQuestion msQuestion = commonLogic.retrieveQuestionByUuid(Long.valueOf(questionResult), auditContext);
						String refIdResult = this.inserTaskDResult(trTaskH, result, msQuestion, taskDBeanResult, auditContext);
						resultLayer.put(refIdResult, result);
					}
				
					counterLayer++;
				}
			}
		}
		
		if (isSubmitLayer1 && !isFromSubmitTask){
			this.updateResultEkycPolo(resultLayer, trTaskH, auditContext);
		}
		
		if (!isFromSubmitTask && result.contains(GlobalVal.PRE_SURVEY_RESULT_FAILED)) {
			int maxRetry = 0;
			boolean isDropTask = true;
			AmGeneralsetting amGeneralSetting = this.commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_LAYER_MAX_RETRY, auditContext);
			if (amGeneralSetting!=null) {
				maxRetry = Integer.parseInt(amGeneralSetting.getGsValue());
			}
			
			if (StringUtils.isNotBlank(request.getCountRetry())) {
				int countRetry = Integer.parseInt(request.getCountRetry());
				if (countRetry < maxRetry) {
					isDropTask = false;
				}
			} 
			
			if (isDropTask) {
				this.doDeleteTask(trTaskH, trTaskH.getAmMsuser(), counterLayer, process, negativeCust, auditContext);
			}
		}
		

		return resultLayer;
	}
	
	private Map<String, Object> doBiometricProcess(SubmitLayerRequest request, Map<String, String> mapApi, Integer isPreapproval, SubmitLayerQuestionGroupBean bean,
	        Map<String, String> filter, boolean isFromSubmitTask, TrTaskH trTaskH, Map<String, Object> mapProcessOfLayer,
	        String productOfferingCode, String sendTaskPreSurvey, AuditContext auditContext) {
		
		StringBuilder errorStack = new StringBuilder();
	    Map<String, Object> resultBiometric = new HashMap<>();
	    Map <String, Object> updateMapReadonly =  new HashMap<>();
	    String questionProcess = StringUtils.EMPTY;
	    String errorMessage = StringUtils.EMPTY;
	    String errorProcess = StringUtils.EMPTY;
	    int resultProcess = 0;
	    String processOfLayer = String.valueOf(mapProcessOfLayer.get("d0"));
	    String ansOfQuestionProcess = StringUtils.EMPTY;
	    String ansOfQuestionScore = StringUtils.EMPTY;
	    Status status = new Status();
	    MsQuestion msQuestion = null;
	    
	    String listquestionProcess = String.valueOf(mapProcessOfLayer.get("d3"));
	    String[] questionSpliter = listquestionProcess.split(";");
	        	
        Map<String, String> mapApiBiometric = generateMapForParamApi(filter, String.valueOf(mapProcessOfLayer.get("d2")));
        mapApi.putAll(mapApiBiometric);
        BiometricResponse resp = new BiometricResponse();
        
        String includeSpouse = mapApi.get("includePsgn");
		String includeGuarantor = mapApi.get("includeGrtr");
		
		LOG.info("include pasangan : {}", includeSpouse);
		LOG.info("include guarantor : {}", includeGuarantor);
        
        Map<String, Object> mapBiometric = intFormLogic.checkBiometricCae(mapApi.get("officeCode"), mapApi.get("officeRegionCode"), productOfferingCode, 
        		mapApi.get("nik"), mapApi.get("selfiePhoto"), mapApi.get("nikPsgn"), mapApi.get("selfiePhotoPsgn"), mapApi.get("nikGrtr"), 
        		mapApi.get("selfiePhotoGrtr"), mapApi.get("taskIdPolo"), mapApi.get("contractNo"), isPreapproval,includeSpouse, includeGuarantor, auditContext);

        int codeBiometricCust = getIntFromMap(mapBiometric, "code");
        int codeBiometricPsgn = getIntFromMap(mapBiometric, "codePsgn");
        int codeBiometricGrtr = getIntFromMap(mapBiometric, "codeGrtr");
        String msg = getStringFromMap(mapBiometric, "msg");
        String msgPsgn = getStringFromMap(mapBiometric, "msgPsgn");
        String msgGrtr = getStringFromMap(mapBiometric, "msgGrtr");
        String rslt = getStringFromMap(mapBiometric, "rslt");
        String rsltPsgn = getStringFromMap(mapBiometric, "rsltPsgn");
        String rsltGrtr = getStringFromMap(mapBiometric, "rsltGrtr");
        String score = getStringFromMap(mapBiometric, "score");
        String scorePsgn = getStringFromMap(mapBiometric, "scorePsgn");
        String scoreGrtr = getStringFromMap(mapBiometric, "scoreGrtr");
        
        for (String s : questionSpliter) {
            questionProcess = s;
            if (questionProcess.equalsIgnoreCase("PRE_RESULT_PASANGAN") && StringUtils.isNotBlank(rsltPsgn)) {
                if (200 == codeBiometricPsgn || 999 == codeBiometricPsgn) {
                    codeBiometricPsgn = 0;
                    resultBiometric.put(questionProcess, rsltPsgn);
                }
                ansOfQuestionScore = scorePsgn;
                ansOfQuestionProcess = rsltPsgn;
                status.setCode(codeBiometricPsgn);
                status.setMessage(msgPsgn);
            } else if (questionProcess.equalsIgnoreCase("PRE_RESULT_GUARANTOR") && StringUtils.isNotBlank(rsltGrtr)) {
                if (200 == codeBiometricGrtr || 999 == codeBiometricGrtr) {
                    codeBiometricGrtr = 0;
                    resultBiometric.put(questionProcess, rsltGrtr);
                }
                ansOfQuestionProcess = rsltGrtr;
                ansOfQuestionScore = scoreGrtr;
                status.setCode(codeBiometricGrtr);
                status.setMessage(msgGrtr);
            } else if (questionProcess.equalsIgnoreCase("PRE_RESULT_PEMOHON") && StringUtils.isNotBlank(rslt)) {
                if (200 == codeBiometricCust || 999 == codeBiometricCust) {
                    codeBiometricCust = 0;
                    resultBiometric.put(questionProcess, rslt);
                }
                ansOfQuestionProcess = rslt;
                ansOfQuestionScore = score;
                status.setCode(codeBiometricCust);
                status.setMessage(msg);
            } else {
				continue;
			}
            resp.setMapResult(resultBiometric);
            resp.setStatus(status);
            
            if (resp.getStatus().getCode() == 100 && StringUtils.isNotBlank(resp.getStatus().getMessage())) {
                errorMessage = resp.getStatus().getMessage();
                
                resultBiometric.put("errorBiometric", errorMessage);
                resultBiometric.put("resultBiometric", resultProcess);
                return resultBiometric;
            }
            
            resultProcess = this.convertResultAnswerToCode(ansOfQuestionScore, processOfLayer);
     
            LOG.info("REF_ID: {} , RESULT: {} ", questionProcess, resultBiometric.get(questionProcess));
            
            if (resultProcess != 1) {
            	
            	String refId = questionProcess;
            	String[] parts = refId.split("_");
            	String typeCustomer = parts[parts.length - 1];
            	typeCustomer = convertLetter(typeCustomer);
            	errorProcess = typeCustomer;
            	resultProcess = 2;
            	
            	if (GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC_FATAL.equalsIgnoreCase(ansOfQuestionScore)) {
            		updateMapReadonly.put(getRefIdFotoSelfie(questionProcess), "0");
				}
            	
            	msQuestion = this.commonLogic.retrieveQuestionByRefId(questionProcess, auditContext);
				String refIdResult = this.inserTaskDResult(trTaskH, ansOfQuestionProcess, msQuestion, null, auditContext);
					
				resultBiometric.put(refIdResult, ansOfQuestionProcess);
            	resultBiometric.put(questionProcess, ansOfQuestionProcess);
				errorStack.append(errorProcess).append(";");
            	
			} else {
				
				msQuestion = this.commonLogic.retrieveQuestionByRefId(questionProcess, auditContext);
                this.inserTaskDResult(trTaskH, ansOfQuestionProcess, msQuestion, null, auditContext);
                resultBiometric.put(questionProcess, ansOfQuestionProcess);
				
			}
        }
        
        if (StringUtils.isNotBlank(errorStack)) {
        	
        	errorProcess = errorStack.toString().replaceAll(";", ", "); // Replace semicolons with commas and add space
            errorProcess = errorProcess.replaceAll("\\s*,\\s*$", ""); // Remove trailing comma with potential whitespace
			
        	resultBiometric.put("errorBiometric", "Foto " + errorProcess + " tidak sesuai dengan KTP");
		}
        
        if (updateMapReadonly != null) {
			resultBiometric.put("update_readonly", updateMapReadonly);
		}
			
        LOG.info("result {} : {}", processOfLayer , resultProcess);
        
        resultBiometric.put("resultBiometric", resultProcess);
	        
	    return resultBiometric;
	}
	
	public static String convertLetter(String inputString) {
	    StringBuilder result = new StringBuilder();
	    
	    for (int i = 0; i < inputString.length(); i++) {
	        char word = inputString.charAt(i);
	        if (i == 0) {
	            // Capitalize the first word
	            result.append(Character.toUpperCase(word));
	        } else {
	            // convert to lower case
	            result.append(Character.toLowerCase(word));
	        }
	    }
	    return result.toString();
	}
	
	private String getRefIdFotoSelfie (String refid) {
		
		String result = StringUtils.EMPTY;
		
		if (refid.equalsIgnoreCase(GlobalVal.REF_PRESURVEY_BIO_PEMOHON)) {
			result = GlobalVal.REF_PRESURVEY_FOTO_PEMOHON;
		} else if (refid.equalsIgnoreCase(GlobalVal.REF_PRESURVEY_BIO_PASANGAN)) {
			result = GlobalVal.REF_PRESURVEY_FOTO_PASANGAN;
		} else {
			result = GlobalVal.REF_PRESURVEY_FOTO_PENJAMIN;
		}
		
		return result;
	}
	
	private String getStringFromMap(Map<String, Object> map, String key) {
	    Object value = map.get(key);
	    return value != null ? value.toString() : null;
	}
	
	private String getStringForPreApproval(Map<String, String> mapApi, String key) {
	    Object value = mapApi.get(key);
	    return value != null ? value.toString() : "0";
	}
 
	private int getIntFromMap(Map<String, Object> map, String key) {
	    Object value = map.get(key);
	 // Check if the value is not null and is an instance of String
	    if (value != null && value instanceof String) {
	        try {
	            // Parse the String value to an integer
	            return Integer.parseInt((String) value);
	        } catch (NumberFormatException e) {
	            // Handle the case where the value cannot be parsed to an integer
	            e.printStackTrace(); // Or log the error
	        }
	    } else if (value instanceof Integer) {
	    	return value instanceof Integer ? (Integer) value : 0; // Corrected casting to Integer
		}

	    // Return a default value if the key is not found or the value cannot be parsed
	    return 0;
	}
	
	private boolean biometricCheckAnswer (SubmitLayerRequest request, SubmitLayerQuestionGroupBean bean,
	        Map<String, String> filter, boolean isFromSubmitTask, TrTaskH trTaskH, AuditContext auditContext) {
		
		 List<Map<String, Object>> listProcessOfLayer = this.getManagerDAO().selectAllNativeString(
		            "SELECT    MMSL.PROCESS, MMSL.UUID_QUESTION_RESULT, MMSL.PARAM_API, MMSL.QUESTION_PROCESS " +
		                    "FROM      MS_MAPPINGSUBMITLAYER MMSL WITH (NOLOCK) " +
		                    "JOIN      MS_FORM MSF WITH (NOLOCK) ON MSF.UUID_FORM = MMSL.UUID_FORM " +
		                    "WHERE     MMSL.PROCESS = 'BIOMETRIC'" +
		                    "          AND MSF.FORM_NAME = :formName", new Object[][]{{"formName", trTaskH.getMsForm().getFormName()}});
		 
		String questionProcess = StringUtils.EMPTY;
	    int resultProcess = 0;
	    Map<String, String> mapApi = new HashMap<>();
	    String processOfLayer = StringUtils.EMPTY;
	    String ansOfQuestionProcess = StringUtils.EMPTY;
	    boolean isNeedHitApi = false;
		    
	    String listquestionProcess = String.valueOf(listProcessOfLayer.get(0).get("d3"));
	    String[] questionSpliter = listquestionProcess.split(";");
	        
        mapApi = this.getBiometricParamFromSubmitLayer(String.valueOf(listProcessOfLayer.get(0).get("d2")), filter);
        
        for (String s : questionSpliter) {
            questionProcess = s;
            processOfLayer = String.valueOf(listProcessOfLayer.get(0).get("d0"));
            ansOfQuestionProcess = filter.get(questionProcess);
            if (mapApi != null && !mapApi.isEmpty()) {
                if (questionProcess.equalsIgnoreCase("PRE_RESULT_PEMOHON") && StringUtils.isNotBlank(mapApi.get("PRE_NO_KTP"))) {
                    resultProcess = this.convertResultAnswerToCode(ansOfQuestionProcess, processOfLayer);
                    if (GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC_NOT_MATCH.equalsIgnoreCase(ansOfQuestionProcess)) {
						resultProcess = 0;
					}
                } else if (questionProcess.equalsIgnoreCase("PRE_RESULT_PASANGAN") && StringUtils.isNotBlank(mapApi.get("PRE_NO_KTP_PSGN"))) {
                    resultProcess = this.convertResultAnswerToCode(ansOfQuestionProcess, processOfLayer);
                    if (GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC_NOT_MATCH.equalsIgnoreCase(ansOfQuestionProcess)) {
						resultProcess = 0;
					}
                } else if (questionProcess.equalsIgnoreCase("PRE_RESULT_GUARANTOR") && StringUtils.isNotBlank(mapApi.get("PRE_NO_KTP_GRTR"))) {
                    resultProcess = this.convertResultAnswerToCode(ansOfQuestionProcess, processOfLayer);
                    if (GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC_NOT_MATCH.equalsIgnoreCase(ansOfQuestionProcess)) {
						resultProcess = 0;
					}
                }
                LOG.info("Answer Qustion: {} ", ansOfQuestionProcess);
                LOG.info("Process Layer: {} ", processOfLayer);
                LOG.info("REF_ID: {} , ANSWER: {} ", questionProcess, ansOfQuestionProcess);
                LOG.info("Result Process: {}", resultProcess);
                if (resultProcess != 1) {
                    isNeedHitApi = true;
                }
                LOG.info("Is Need Hit API : {} ", isNeedHitApi);
            }
        }
		
		return isNeedHitApi;
	}
	
	private String convertCodeToResultAnswer(int resultCode, String process) {
		String result = StringUtils.EMPTY;
		if (GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL.equals(process)) {
			if (3 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL_NOTFOUND;
			} else if (1 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL_MATCH;
			} else if (2 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL_NOTMATCH;
			}
		} else if (GlobalVal.PRE_SURVEY_PROCESS_NEGATIVE_CUST.equals(process)) {
			if (2 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_NEGATIVE_CUST_MATCH;
			} else if (1 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_NEGATIVE_CUST_NOTMATCH;
			}
		} else if (GlobalVal.PRE_SURVEY_PROCESS_PHONE.equals(process)) {
			if (3 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_PHONE_NOTSUCCESS;
			} else if (1 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_PHONE_SUCCESS;
			}
		} else if (GlobalVal.PRE_SURVEY_PROCESS_INCOME.equals(process)) {
			if (0 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_INCOME_NULL;
			} else if (1 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_INCOME_AMIDST;
			} else if (2 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_INCOME_ABOVE;
			} else if (3 == resultCode) {
				result = GlobalVal.PRE_SURVEY_PROCESS_INCOME_BELOW;
			}
		}
		
		return result;
	}
	
	private int convertResultAnswerToCode(String resultAnswer, String process) {
		int result = 0;
		if (GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL.equals(process)) {
			if (GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL_NOTFOUND.equalsIgnoreCase(resultAnswer)) {
				result = 3;
			} else if (GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL_MATCH.equalsIgnoreCase(resultAnswer)) {
				result = 1;
			} else if (GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL_NOTMATCH.equalsIgnoreCase(resultAnswer)) {
				result = 2;
			}
		} else if (GlobalVal.PRE_SURVEY_PROCESS_NEGATIVE_CUST.equals(process)) {
			if(GlobalVal.PRE_SURVEY_PROCESS_NEGATIVE_CUST_NOTMATCH.equalsIgnoreCase(resultAnswer)) {
				result = 1;
			} else if(GlobalVal.PRE_SURVEY_PROCESS_NEGATIVE_CUST_MATCH.equalsIgnoreCase(resultAnswer)) {
				result = 2;
			}
		} else if (GlobalVal.PRE_SURVEY_PROCESS_PHONE.equals(process)) {
			if(GlobalVal.PRE_SURVEY_PROCESS_PHONE_SUCCESS.equalsIgnoreCase(resultAnswer)) {
				result = 1;
			} else if(GlobalVal.PRE_SURVEY_PROCESS_PHONE_NOTSUCCESS.equalsIgnoreCase(resultAnswer)) {
				result = 2;
			}
		} else if (GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC.equals(process)) {
			if(GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC_FATAL.equalsIgnoreCase(resultAnswer)) {
				result = 2;
			} else if (StringUtils.isBlank(resultAnswer)) {
				result = 0;
			} else {
				result = 1;
			}
		} else if (GlobalVal.PRE_SURVEY_PROCESS_INCOME.equals(process)) {
			result = 1;
		}
		
		return result;
	}
	
	private Map<String, String> generateMapForParamApi(Map<String, String> mapJsonRequest, String param) {
		Map<String, String> result = new HashMap<>();
		if (null != mapJsonRequest) {			
			String[] listParam = param.split(";");
			for (int i = 0; i < listParam.length; i++) {
				String[] paramKeyVal = listParam[i].split("@@@");
				if (StringUtils.isNotBlank(mapJsonRequest.get(paramKeyVal[1]))) {
					result.put(paramKeyVal[0], mapJsonRequest.get(paramKeyVal[1]));
					
					LOG.info("REF_ID: {}, {}", paramKeyVal[0], mapJsonRequest.get(paramKeyVal[1]));
				}
			}
		}
		
		return result;
	}
	
	@Override
	public int doValidateDukcapil(Map<String, String> paramApi,Integer isPreApproval, AuditContext auditContext) {
		int result = 0;
		
		Object[][] params = { { Restrictions.eq("uuidMsUser", new Long(auditContext.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, params);
		 
		AmGeneralsetting amGeneralSetting = this.commonLogic.retrieveGs(GlobalKey.GENERALSETTING_CHECK_DUKCAPIL, auditContext);
		if ("1".equals(amGeneralSetting.getGsValue())) {
			String appSource = SpringPropertiesUtils.getProperty(GlobalKey.NC_CHECK_DUKCAPIL_APP_SOURCE);
			
			String nik = StringUtils.isBlank(paramApi.get("nik"))?StringUtils.EMPTY:paramApi.get("nik");
			String name = StringUtils.isBlank(paramApi.get("name"))?StringUtils.EMPTY:paramApi.get("name");
			String birthPlace = StringUtils.isBlank(paramApi.get("birthPlace"))?StringUtils.EMPTY:paramApi.get("birthPlace");
			String birthDate = StringUtils.isBlank(paramApi.get("birthDate"))?StringUtils.EMPTY:paramApi.get("birthDate");
			String taskIdPolo = StringUtils.isBlank(paramApi.get("taskIdPolo"))?StringUtils.EMPTY:paramApi.get("taskIdPolo");
			
			CheckDukcapilResponse dukcapil = createTaskLogic.dukcapil(auditContext, "1", nik, name, birthPlace,
					birthDate, amMsuser.getLoginId(), appSource, taskIdPolo, isPreApproval);
			
			if ("Match".equalsIgnoreCase(dukcapil.getFinalResult()) && "00".equals(dukcapil.getResponseCode())) {
				result = 1;
			} else if ("Not Match".equalsIgnoreCase(dukcapil.getFinalResult())) {
				result = 2;
			} else if ("Not Found".equalsIgnoreCase(dukcapil.getFinalResult())) {
				result = 3;
			}
		} else {
			result = 1;
		}
		
		return result;
		// Hardcode return fail
		// return 2;
	}
	
	@SuppressWarnings("unused")
	private int doTeleCheck(Map<String, String> paramApi, AuditContext auditContext) {
		int result = 0;
		boolean isTelecheckId = intFormLogic.requestTeleowner(paramApi.get("nik"), paramApi.get("mobilePhone"), true, auditContext);
		if (isTelecheckId) {
			result = 1;
		}
		
		return result;
	}
	
	@Override
	public int doTeleStatus(Map<String, String> paramApi, Integer isPreApproval, AuditContext auditContext) {
		int result = 0;
		String teleStatusResult = "result";
		Map checkTeleStatus =  intFormLogic.checkTeleStatus(auditContext, paramApi.get("mobilePhone"), 0, true, paramApi.get("taskIdPolo"), isPreApproval);
		if(checkTeleStatus.get(teleStatusResult) != null) {
			if ("ACTIVE".equalsIgnoreCase(checkTeleStatus.get(teleStatusResult).toString())) {
				result = 1;
			} else if ("INACTIVE".equalsIgnoreCase(checkTeleStatus.get(teleStatusResult).toString())) {
				//Tele Status tidak aktif dianggak not found, karena tidak termasuk fatal score
				result = 3;
			}
		}
		return result;
	}
	
	@Override
	public int doCheckNegativeCust(Map<String, String> paramApi,Integer isPreApproval, AuditContext auditContext) {
		int result = 0;
		boolean isNegativeCust = intFormLogic.checkNegativeCust(paramApi.get("nik"), paramApi.get("taskIdPolo"), paramApi.get("name"), paramApi.get("birthPlace"), paramApi.get("birthDate"), paramApi.get("mothername"), isPreApproval, paramApi.get("contractNo"), auditContext);
		if (!isNegativeCust) {
			result = 1;
		} else {
			result = 2;
		}
		
		return result;
	}
	
	private int doCheckSlik(Map<String, String> paramApi, AuditContext auditContext) {
		int result = 0;

		AmGeneralsetting amGeneralSetting = this.commonLogic.retrieveGs(GlobalKey.GENERALSETTING_IS_BYPASS_CHECKSLIK, auditContext);
		
		if (!"1".equalsIgnoreCase(amGeneralSetting.getGsValue())) {
			Map checkSlik =  intFormLogic.checkSlikCAE(paramApi.get("nik"), paramApi.get("name"), paramApi.get("birthdate"), paramApi.get("birthplace"), paramApi.get("mothername"),
						paramApi.get("nikPsgn"), paramApi.get("namePsgn"),  paramApi.get("birthdatePsgn"), paramApi.get("birthplacePsgn"), paramApi.get("mothernamePsgn"),
						paramApi.get("nikGrntr"), paramApi.get("nameGrntr"),  paramApi.get("birthdateGrntr"), paramApi.get("birthplaceGrntr"), paramApi.get("mothernameGrntr"), paramApi.get("ntf"), 
						paramApi.get("officeCode"), paramApi.get("officeRegionCode"), paramApi.get("productOfferingCode"), paramApi.get("callMode"), paramApi.get("contractNo"), auditContext);
			if (checkSlik.get("code").toString().equals("200")){
				result = 1;
			} else  {
				result = 0;
			}
		} else {
			result = 1;
		}

		return result;

	}
	
	private int doCheckIncome(Map<String, String> paramApi, AuditContext auditContext) {
		int result = 0;

		Map checkIncome =  intFormLogic.checkIncome(paramApi.get("nik"), paramApi.get("income"), auditContext);
		if(checkIncome.get("rslt") != null) {
			if ("AMIDST".equalsIgnoreCase(checkIncome.get("rslt").toString())) {
				result = 1;
			} else if ("ABOVE".equalsIgnoreCase(checkIncome.get("rslt").toString())) {
				result = 2;
			} else if ("BELOW".equalsIgnoreCase(checkIncome.get("rslt").toString())) {
				result = 3;
			}
		}

		return result;

	}

	private void doDeleteTask(TrTaskH trTaskH, AmMsuser usr, int counterLayer, String process, String negativeCustomer, AuditContext auditContext) {
		Date now = new Date();
		
		AmMssubsystem subsystemMs = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, auditContext);
		MsStatustask msStatustask = this.commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_DELETED,
		subsystemMs.getUuidMsSubsystem(), auditContext);
		MsStatusmobile statusMobile = commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_DELETED, auditContext);
		MsStatustask currStatusTask = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_UPLOADING,
				subsystemMs.getUuidMsSubsystem(), auditContext);
		trTaskH.setMsStatustask(msStatustask);
		trTaskH.setMsStatusmobile(statusMobile);
		trTaskH.setDtmUpd(now);
		trTaskH.setUsrUpd(usr.getFullName());
		
		this.getManagerDAO().update(trTaskH);
		
		String message = this.messageSource.getMessage("businesslogic.notes.changestatus.submitlayerfailure",
				new Object[] {counterLayer, process}, this.retrieveLocaleAudit(auditContext));
		if (counterLayer == 0) {
			message = this.messageSource.getMessage("businesslogic.notes.changestatus.submittaskvisitfailure",
					null, this.retrieveLocaleAudit(auditContext));
		}
		// INSERT INTO TR TASK HISTORY
		TrTaskhistory trTaskHistory = new TrTaskhistory(
				trTaskH.getMsStatustask(), trTaskH,
				usr.getFullName(), now,
				message, usr.getFullName(),
				usr.getFullName(),
				GlobalVal.CODE_PROCESS_DELETED);
		this.getManagerDAO().insert(trTaskHistory);
		
		Object [][] prmUpdNextTask = { {"applNo", trTaskH.getApplNo()}, {"dtmUpd", now},
				{"uuidStatusTask", msStatustask.getUuidStatusTask()}, {"statusMobileSeqNo", statusMobile.getStatusMobileSeqNo()},
				{"usrUpd", String.valueOf(trTaskH.getAmMsuser().getUuidMsUser())},
				{"currStatusTask", currStatusTask.getUuidStatusTask()} };
		StringBuilder queryUpdNextTask = new StringBuilder();
		queryUpdNextTask.append("UPDATE TTH ");
		queryUpdNextTask.append("SET    TTH.UUID_STATUS_TASK = :uuidStatusTask, ");
		queryUpdNextTask.append("       TTH.STATUS_MOBILE_SEQ_NO = :statusMobileSeqNo, ");
		queryUpdNextTask.append("       TTH.DTM_UPD = :dtmUpd, ");
		queryUpdNextTask.append("       TTH.USR_UPD = :usrUpd ");
		queryUpdNextTask.append("FROM   TR_TASK_H TTH ");
		queryUpdNextTask.append("WHERE  TTH.APPL_NO = :applNo ");
		queryUpdNextTask.append("       AND TTH.UUID_STATUS_TASK != :currStatusTask ");

		
		this.getManagerDAO().updateNativeString(queryUpdNextTask.toString(), prmUpdNextTask);
		
		Object [][] prmInsHistoryNextTask = { {"applNo", trTaskH.getApplNo()}, {"dtmCrt", now},
				{"uuidStatusTask", msStatustask.getUuidStatusTask()}, {"usrCrt", trTaskH.getAmMsuser().getLoginId()},
				{"currStatusTask", currStatusTask.getUuidStatusTask()}, {"notes", ""},
				{"codeProcess", GlobalVal.CODE_PROCESS_DELETED} };
		StringBuilder insTrHistory = new StringBuilder();
		insTrHistory.append("INSERT INTO TR_TASKHISTORY (USR_CRT, DTM_CRT, UUID_TASK_H, UUID_STATUS_TASK, NOTES, ACTOR, CODE_PROCESS) ");
		insTrHistory.append("SELECT :usrCrt, :dtmCrt, TTH.UUID_TASK_H, :uuidStatusTask, :notes, :usrCrt, :codeProcess ");
		insTrHistory.append("FROM   TR_TASK_H TTH ");
		insTrHistory.append("WHERE  TTH.APPL_NO = :applNo ");
		insTrHistory.append("       AND TTH.UUID_STATUS_TASK != :currStatusTask ");

		this.getManagerDAO().insertNativeString(insTrHistory.toString(), prmInsHistoryNextTask);
		
		intFormLogic.updateDataPolo(null, trTaskH, null, "Deleted", "F", negativeCustomer, null, null, "Fatal Score", auditContext);
	}
	
	private String inserTaskDResult(TrTaskH trTaskH, String resultAnswer, MsQuestion msQuestion, SubmitTaskDBean beanResult, AuditContext auditContext) {		
		String latitude = null;
		String longitude = null;
		String mcc = null;
		String mnc = null;
		String lac = null;
		String cellId = null;
		String accuracy = null;
		if (null != beanResult) {
			latitude = beanResult.getLatitude();
			longitude = beanResult.getLongitude();
			mcc = beanResult.getMcc();
			mnc = beanResult.getMnc();
			lac = beanResult.getLac();
			cellId = beanResult.getCid();
			accuracy = beanResult.getAccuracy();
		}
		
		insertTaskD(auditContext, trTaskH, msQuestion, null, msQuestion.getQuestionLabel(), resultAnswer, 
				latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, null);
		
		return msQuestion.getRefId();
	}
	
	private void processingTaskDetail(TrTaskH taskH, SubmitTaskHBean taskHBean, List<SubmitTaskDBean> listTaskDBean, AuditContext auditContext) {
		MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
				Long.parseLong(taskHBean.getUuid_scheme()), taskHBean.getFormVersion(), auditContext);
		Map<Integer, MsQuestion> msQuestions = this.getAllMsQuestion(listTaskDBean, formHist);
		
		ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(auditContext);
        Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
                ? this.imageStorageLogic.retrieveGsImageFileSystemPath(auditContext) : null;
		
        boolean saveAsJson = PropertiesHelper.isTaskDJson();
		if (saveAsJson) {
			this.saveTaskDIntoJson(listTaskDBean, taskH, msQuestions, isl, imagePath, auditContext);
		} else {
			Map<Long, TrTaskD> listTaskD = this.listTaskDAsMap(String.valueOf(taskH.getUuidTaskH()));
		    this.saveTaskDIntoRow(listTaskDBean, taskH, msQuestions, listTaskD, isl, imagePath, auditContext);
		}
	}
	
	private Map<Integer, MsQuestion> getAllMsQuestion(List<SubmitTaskDBean> listTaskDBean, MsFormhistory formHist){
		Map<Integer, MsQuestion> result = new HashMap<>();
		 
		for (int i = 0; i < listTaskDBean.size(); i++) {
			Object[][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHist.getUuidFormHistory())},
								{Restrictions.eq(paramUuidQuestion, Long.valueOf(listTaskDBean.get(i).getQuestion_id()))}};
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
			
			MsQuestion msQuestion = new MsQuestion();
			msQuestion.setUuidQuestion(Long.parseLong(listTaskDBean.get(i).getQuestion_id()));
			msQuestion.setRefId(qset.getRefId());
			msQuestion.setQuestionLabel(qset.getQuestionLabel());
			msQuestion.setMsAnswertype(qset.getMsAnswertype());
			msQuestion.setMsAssettag(qset.getMsAssettag());
			msQuestion.setMsCollectiontag(qset.getMsCollectiontag());
			msQuestion.setMsOrdertag(qset.getMsOrdertag());
			msQuestion.setLovGroup(qset.getLovGroup());
			msQuestion.setIsVisible(qset.getIsVisible());
			msQuestion.setIsMandatory(qset.getIsMandatory());
			msQuestion.setIsReadonly(qset.getIsReadonly());
			msQuestion.setIsHolidayAllowed(qset.getIsHolidayAllowed());
			msQuestion.setMaxLength(qset.getMaxLength());
			msQuestion.setRegexPattern(qset.getRegexPattern());
			msQuestion.setAmMssubsystem(qset.getAmMssubsystem());
			msQuestion.setImgQlt(qset.getImgQlt());
			result.put(i, msQuestion);
		}
		return result;
	}
	
	private Map<Long, TrTaskD> listTaskDAsMap(String uuidTaskH) {
		List<TrTaskD> result = this.listTaskD(uuidTaskH);
		
		if (null == result || result.isEmpty()){
			return Collections.emptyMap();
		}
		
		Map<Long, TrTaskD> resultMap = new HashMap<>(result.size());
		for (Iterator iterator = result.iterator(); iterator.hasNext();) {
			TrTaskD trTaskD = (TrTaskD) iterator.next();
			resultMap.put(trTaskD.getMsQuestion().getUuidQuestion(), trTaskD);
		}
		
		return resultMap;
	}
	
	private List<TrTaskD> listTaskD(String uuidTaskH) {
		if (StringUtils.isBlank(uuidTaskH)) {
			return Collections.emptyList();
		}
		
		Object[][] params = { {Restrictions.eq(paramUuidTaskH, Long.valueOf(uuidTaskH))} };
		Map<String, Object> result = this.getManagerDAO().list(TrTaskD.class, params, null);
		
		return (List<TrTaskD>) result.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	private void saveTaskDIntoJson(List<SubmitTaskDBean> listTaskDBean, TrTaskH trTaskH, Map<Integer, MsQuestion> msQuestions,
			ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		TaskDocumentBean document = null;
		boolean newDoc = false;
		if (docDb == null) {
			docDb = new TrTaskdocument(trTaskH, new Date(), auditContext.getCallerId());
			document = new TaskDocumentBean();
			newDoc = true;
		} else {
			document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
			docDb.setDtmUpd(new Date());
			docDb.setUsrUpd(auditContext.getCallerId());
			newDoc = false;
		}
		
		List<AnswerBean> answers = (newDoc) ? new ArrayList<>(listTaskDBean.size()) : document.getAnswers();		
		if (newDoc) {
			document.setAnswers(answers);
		}
		
		for (int i = 0; i < listTaskDBean.size(); i++) {
			MsQuestion msQuestion = msQuestions.get(i);
			String answerTypeCode = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String optionAnswerId = StringUtils.stripToEmpty(listTaskDBean.get(i).getOption_answer_id());
			String questionText = StringUtils.stripToEmpty(listTaskDBean.get(i).getQuestion_label());
			String textAnswer = StringUtils.stripToEmpty(listTaskDBean.get(i).getText_answer());
			String latitude = StringUtils.stripToNull(listTaskDBean.get(i).getLatitude());						
			String longitude = StringUtils.stripToNull(listTaskDBean.get(i).getLongitude());
			String mcc = StringUtils.stripToNull(listTaskDBean.get(i).getMcc());
			String mnc = StringUtils.stripToNull(listTaskDBean.get(i).getMnc());
			String lac = StringUtils.stripToNull(listTaskDBean.get(i).getLac());				
			String cellId = StringUtils.stripToNull(listTaskDBean.get(i).getCid());				
			String accuracy = StringUtils.stripToNull(listTaskDBean.get(i).getAccuracy());	
			String image = StringUtils.stripToNull(listTaskDBean.get(i).getImage());
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerTypeCode) &&
					StringUtils.isNotEmpty(textAnswer) && StringUtils.containsAny(textAnswer, ',', '.')) {
				textAnswer = textAnswer.substring(0, textAnswer.length()-2).replace(",", "").replace(".", "");				
			}
			
			int idxAnswer = document.findAnswerIndex(msQuestion.getUuidQuestion());
			
			AnswerBean answer = this.constructAnswer(
					document, idxAnswer, msQuestion, answerTypeCode, questionText, textAnswer, optionAnswerId,
					latitude, longitude, accuracy, mcc, mnc, lac, cellId, trTaskH, image, isl, imagePath, auditContext);
			
			if (idxAnswer == -1) {
				answers.add(answer);
			}
		}
		docDb.setDocument(gson.toJson(document, TaskDocumentBean.class));
		
		if (newDoc) {
			this.getManagerDAO().insert(docDb);
		} else {
			this.getManagerDAO().update(docDb);
		}
	}
	
	private void saveTaskDIntoRow(List<SubmitTaskDBean> listTaskDBean, TrTaskH trTaskH, Map<Integer, MsQuestion> msQuestions,
			Map<Long, TrTaskD> listTaskD, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {				
		
		boolean hasBrand = false;
		boolean hasModel = false;
		boolean hasGroup = false;
		boolean hasType = false;
		String idAsset = StringUtils.EMPTY;
		
		Map<String, Object> mapAssetProduct = new HashMap<>();
		
		if (!listTaskDBean.isEmpty()) {
			//delete multiple detail
			String[] answerTypeArr = {GlobalVal.ANSWER_TYPE_MULTIPLE, GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION,
					GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION};
			Object[][] paramsMulti = {
					{paramTaskH, trTaskH.getUuidTaskH()},
					{"answerType", Arrays.asList(answerTypeArr)} };
			this.getManagerDAO().deleteNativeString(
					"DELETE     d " +
					"FROM       TR_TASK_D d " +
					"LEFT JOIN  MS_QUESTION q ON d.uuid_question = q.uuid_question " +
					"LEFT JOIN  MS_ANSWERTYPE at ON q.uuid_answer_type = at.uuid_answer_type " +
					"WHERE      d.uuid_task_h = :uuidTaskH " +
					"           AND at.code_answer_type IN (:answerType)", paramsMulti);
		}
		
		for (int i = 0; i < listTaskDBean.size(); i++) {
			MsQuestion msQuestion = msQuestions.get(i);
			
			String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String uuidTaskD = listTaskDBean.get(i).getUuid_task_d();
			String optionAnswerId = listTaskDBean.get(i).getOption_answer_id();
			String lov = listTaskDBean.get(i).getLov();
			String textAnswer = listTaskDBean.get(i).getText_answer();
			String questionText = listTaskDBean.get(i).getQuestion_label();
			String latitude = listTaskDBean.get(i).getLatitude();
			String longitude = listTaskDBean.get(i).getLongitude();
			String mcc = listTaskDBean.get(i).getMcc();
			String mnc = listTaskDBean.get(i).getMnc();
			String lac = listTaskDBean.get(i).getLac();
			String cellId = listTaskDBean.get(i).getCid();
			String accuracy = listTaskDBean.get(i).getAccuracy();
			String image = listTaskDBean.get(i).getImage();
			String uuidLookup = listTaskDBean.get(i).getUuid_lookup();
			
			String assetType = StringUtils.EMPTY;
			if (null != msQuestion.getMsAssettag()) {
				assetType = msQuestion.getMsAssettag().getAssetTagName();
			}
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType) && (textAnswer.contains(",") || textAnswer.contains("."))) {
				textAnswer = textAnswer.substring(0,textAnswer.length()-2).replace(",", "").replace(".", "");
			}
			
			if (GlobalVal.ANSWER_TYPE_DSR.equals(answerType)) {
				String[] temp = textAnswer.split("@");
				textAnswer = temp[0];
			}
			
			if (GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(answerType) && textAnswer.contains("|")) {
				String[] temp = textAnswer.split("[|]");
				textAnswer = temp[temp.length-1];
			}
			
			if (MssTool.isImageQuestion(answerType)) {
				this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
							latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, isl, imagePath);
			} else if (GlobalVal.ANSWER_TYPE_LU_OFFLINE.equals(answerType) || 
					GlobalVal.ANSWER_TYPE_LU_OFFLINE_WITH_SUGGESTION.equals(answerType)) {
				// Check product answer
				if (GlobalVal.ASSET_TAG_BRAND.equals(assetType) ||
						GlobalVal.ASSET_TAG_MODEL.equals(assetType) ||
						GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType) ||
						GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
					if (GlobalVal.ASSET_TAG_BRAND.equals(assetType)) {
						hasBrand = true;
					} else if (GlobalVal.ASSET_TAG_MODEL.equals(assetType)) {
						hasModel = true;
					} else if (GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType)) {
						hasGroup = true;
					} else if (GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
						hasType = true;
						idAsset = listTaskDBean.get(i).getUuid_lookup();
					}
					mapAssetProduct.put(assetType, i);
				} else {
					Map<String, Object> mapAns = new HashMap<>();
					if (null != uuidLookup) {
						Long idStagingAsset = Long.valueOf(uuidLookup);
						String optionText = null;
						if (null != idStagingAsset) {
							if (GlobalVal.ASSET_TAG_PRODUCT.equals(assetType)) {
								Object[][] paramLu = { {"idLu", idStagingAsset} };
								List<Map<String, Object>> luAnswer = this.getManagerDAO().selectAllNativeString(
										"SELECT PRODUCT_OFFERING_ID, PRODUCT_OFFERING_NAME, ID " +
										"FROM   STAGING_PRODUCT_OFFERING WITH (NOLOCK) " +
										"WHERE  ID = :idLu", paramLu);
								if (!luAnswer.isEmpty()) {
									mapAns = luAnswer.get(0);
									BigInteger prodId = (BigInteger) mapAns.get("d0");
									mapAns.put("d0", String.valueOf(prodId));
								}
								optionText = (String) mapAns.get("d0");
								textAnswer = (String) mapAns.get("d1");
								idStagingAsset = ((BigInteger) mapAns.get("d2")).longValue();
								optionAnswerId = null;
							} else {
								optionText = lov;
								optionAnswerId = null;
							}
							
							insertTaskD(auditContext, trTaskH, msQuestion, optionAnswerId, questionText, textAnswer, 
									latitude, longitude, mcc, mnc, lac, cellId, accuracy, idStagingAsset, optionText, listTaskD);
						}
					}
				}
			} else if (GlobalVal.ANSWER_TYPE_SCORING.equals(answerType) ) {
				String optionText = lov;
				optionAnswerId = null;
				insertTaskD(auditContext, trTaskH, msQuestion, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, optionText, listTaskD);
			} else {
				//insert into Table TR_TASK_D
				this.insertTaskD(auditContext, trTaskH, msQuestion, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, listTaskD);
			}
		}
		
		//Insert into Task D for Product 
		if (hasBrand && hasModel && hasGroup && hasType) {
			Long longIdAsset = new Long(idAsset);
			
			Object[][] paramPo = { {"idPo", longIdAsset} };
			List<Map<String, Object>> listAssetAns = this.getManagerDAO().selectAllNativeString(
					"SELECT HIERARCHY_L1_CODE, ASSET_HIERARCHY_L1_NAME, " +
					"       HIERARCHY_L2_CODE, ASSET_HIERARCHY_L2_NAME, " +
					"       GROUP_TYPE, MASTER_CODE, MASTER_NAME " +
					"FROM   STAGING_PO_ASSET WITH (NOLOCK) " +
					"WHERE  id = :idPo", paramPo);
			
			Map<String, Object> mapAsset = new HashMap<>();
			if (!listAssetAns.isEmpty()) {
				mapAsset = listAssetAns.get(0);
			}
			
			for (Map.Entry<String, Object> mp : mapAssetProduct.entrySet()) {
				int i = (int) mp.getValue();
				MsQuestion msQuestion = msQuestions.get(i);
				String assetType = msQuestion.getMsAssettag().getAssetTagName();
				String optionAnswerId = null;
				String textAnswer = listTaskDBean.get(i).getText_answer();
				String questionText = listTaskDBean.get(i).getQuestion_label();
				String latitude = listTaskDBean.get(i).getLatitude();
				String longitude = listTaskDBean.get(i).getLongitude();
				String mcc = listTaskDBean.get(i).getMcc();
				String mnc = listTaskDBean.get(i).getMnc();
				String lac = listTaskDBean.get(i).getLac();
				String cellId = listTaskDBean.get(i).getCid();
				String accuracy = listTaskDBean.get(i).getAccuracy();
				String optionText = null;
				
				if (GlobalVal.ASSET_TAG_BRAND.equals(assetType)) {
					optionText = (String) mapAsset.get("d0");
					textAnswer = (String) mapAsset.get("d1");
				} else if (GlobalVal.ASSET_TAG_MODEL.equals(assetType)) {
					optionText = (String) mapAsset.get("d2");
					textAnswer = (String) mapAsset.get("d3");
				} else if (GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType)) {
					optionText = (String) mapAsset.get("d4");
					textAnswer = (String) mapAsset.get("d4");
				} else if (GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
					optionText = (String) mapAsset.get("d5");
					textAnswer = (String) mapAsset.get("d6");
				}
				insertTaskD(auditContext, trTaskH, msQuestion, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, longIdAsset, optionText, listTaskD);
			}
		}
	}
	
	private void insertTaskD(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String optionAnswerId,
			String questionText, String textAnswer, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Long idStagingAsset, String optionText, Map<Long, TrTaskD> listTaskD){
		DateTime startDateTime = new DateTime();
		LOG.info("Inserting task detail...");
		MsLov msLovByLovId =  null;
		TrTaskD taskD = null;
		if (null != listTaskD) {
			if (!GlobalVal.ANSWER_TYPE_MULTIPLE.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
					!GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
					!GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
				taskD = listTaskD.get(Long.valueOf(msQuestion.getUuidQuestion()));
			}
		} else {
			Object [][] prmTaskD = { {Restrictions.eq(paramUuidTaskH, trTaskH.getUuidTaskH())},
					{Restrictions.eq(paramUuidQuestion, msQuestion.getUuidQuestion())} };
			taskD = this.getManagerDAO().selectOne(TrTaskD.class, prmTaskD);
		}
		if (StringUtils.isNotBlank(optionAnswerId)){
			msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
		}
		if ((GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())
				|| GlobalVal.ANSWER_TYPE_DATETIME.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) && null != textAnswer) {
				DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
			try {
				Date result = df.parse(textAnswer);
				if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
					textAnswer = (null != result) 
							? new SimpleDateFormat(dateFormat).format(result)
							: null;
				} else {
					textAnswer = (null != result) 
							? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result)
							: null;
				}
			} catch (ParseException e) {
				LOG.error(e.getMessage(), e);
			}
		}
		if (null != taskD) {
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setDtmUpd(new Date());
			if (null != msLovByLovId) {
				taskD.setMsLovByLovId(msLovByLovId);
				taskD.setOptionText(msLovByLovId.getDescription());
			} else {
				taskD.setOptionText(optionText);
			}
			taskD.setQuestionText(questionText);
			taskD.setTextAnswer(textAnswer);
			taskD.setLatitude(checkEmptyBigdecimal(latitude));
			taskD.setLongitude(checkEmptyBigdecimal(longitude));
			if (null != taskD.getLatitude() && null != taskD.getLongitude() &&
					taskD.getLatitude().intValue() != 0 && taskD.getLongitude().intValue() != 0) {
				taskD.setIsGps("1");
			}
			taskD.setMcc(checkEmptyInteger(mcc));
			taskD.setMnc(checkEmptyInteger(mnc));
			taskD.setLac(checkEmptyInteger(lac));
			taskD.setCellId(checkEmptyInteger(cellId));
			taskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(taskD.getIsGps()) && null != taskD.getMcc() && null != taskD.getMnc()
					&& null != taskD.getLac() && null != taskD.getCellId()) {
				this.getLocationByCellId(taskD, auditContext);
			}
			taskD.setIdStagingAsset(idStagingAsset);
			this.getManagerDAO().update(taskD);
		} else {				
			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			trTaskD.setTrTaskH(trTaskH);
			trTaskD.setMsQuestion(msQuestion);
			if (null != msLovByLovId) {
				trTaskD.setMsLovByLovId(msLovByLovId);
				trTaskD.setOptionText(msLovByLovId.getDescription());
			} else {
				trTaskD.setOptionText(optionText);
			}
			trTaskD.setQuestionText(questionText);
			trTaskD.setTextAnswer(textAnswer);
			trTaskD.setLatitude(checkEmptyBigdecimal(latitude));
			trTaskD.setLongitude(checkEmptyBigdecimal(longitude));
			if (null != trTaskD.getLatitude() && null != trTaskD.getLongitude() &&
					trTaskD.getLatitude().intValue() != 0 && trTaskD.getLongitude().intValue() != 0) {
				trTaskD.setIsGps("1");
			}
			trTaskD.setMcc(checkEmptyInteger(mcc));
			trTaskD.setMnc(checkEmptyInteger(mnc));
			trTaskD.setLac(checkEmptyInteger(lac));
			trTaskD.setCellId(checkEmptyInteger(cellId));
			trTaskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(trTaskD.getIsGps()) && null != trTaskD.getMcc() && null != trTaskD.getMnc()
					&& null != trTaskD.getLac() && null != trTaskD.getCellId()) {
				this.getLocationByCellId(trTaskD, auditContext);
			}
			trTaskD.setIdStagingAsset(idStagingAsset);
			this.getManagerDAO().insert(trTaskD);
			
		}
		LOG.info("End of insert task detail : {}", new DateTime().minusMillis(startDateTime.getMillisOfDay()).getMillisOfDay());
	}
	
	private BigDecimal checkEmptyBigdecimal(String in){
		if (StringUtils.isBlank(in)) {
			return null;
		} else {
			return new BigDecimal(in);		
		}
	}
	
	private Integer checkEmptyInteger(String in){		
		if (StringUtils.isBlank(in)){
			return null;
		} else {
			return Integer.valueOf((int) Math.round(new Double(in)));		
		}
	}
	
	private void getLocationByCellId(TrTaskD taskD, AuditContext auditContext){
		List<LocationBean> listLocations = new ArrayList<>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(taskD.getCellId());
		locationBean.setLac(taskD.getLac());
		locationBean.setMcc(taskD.getMcc());
		locationBean.setMnc(taskD.getMnc());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			taskD.setLatitude(BigDecimal.valueOf(locationBean.getCoordinate().getLatitude()));
			taskD.setLongitude(BigDecimal.valueOf(locationBean.getCoordinate().getLongitude()));
			taskD.setAccuracy(locationBean.getAccuracy());
			
			taskD.setTextAnswer("Coord : "+taskD.getLatitude().setScale(6, RoundingMode.HALF_UP) + ", " +
					taskD.getLongitude().setScale(6, RoundingMode.HALF_UP) + " Accuracy : " + taskD.getAccuracy() + " m");
			taskD.setIsConverted("1");
		}
	}
	
	@SuppressWarnings("deprecation")
	private long insertDetailLob(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD,
			String questionText, String base64Image, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Boolean isGps, Boolean isConverted, ImageStorageLocation saveImgLoc, Path imgLoc){
		Object[][] params = { {Restrictions.eq(paramUuidTaskH, trTaskH.getUuidTaskH())},
				{Restrictions.eq(paramUuidQuestion, msQuestion.getUuidQuestion())} };
		TrTaskdetaillob trTaskdetaillob = this.getManagerDAO().selectOne(TrTaskdetaillob.class, params);

		if (null == trTaskdetaillob) {
			trTaskdetaillob = new TrTaskdetaillob();
			trTaskdetaillob.setUsrCrt(auditContext.getCallerId());
			trTaskdetaillob.setDtmCrt(new Date());
		} else {
			trTaskdetaillob.setUsrUpd(auditContext.getCallerId());
			trTaskdetaillob.setDtmUpd(new Date());
		}
		
		trTaskdetaillob.setTrTaskH(trTaskH);
		trTaskdetaillob.setMsQuestion(msQuestion);
		
		if (GlobalVal.ANSWER_TYPE_OCR.equalsIgnoreCase(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			String[] ocr = base64Image.split("@@@");
			base64Image = ocr[0];
			String result = URLDecoder.decode(ocr[1]);
			
			trTaskdetaillob.setTextAnswer(result);
		}
		
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			if (null != base64Image && !base64Image.isEmpty()) {
				trTaskdetaillob.setLobFile(BaseEncoding.base64().decode(base64Image));
			}
		} else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc && (null != base64Image && !base64Image.isEmpty())) {
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			Date date = new Date();
			Path outputPath = Paths.get(imgLoc.toString(), dateFormat.format(date));

			String absoluteFilename = trTaskH.getTaskId() + "_" + msQuestion.getRefId() + "_"
			        + uuidTaskD + ".jpg";
				
			String outputFile = this.imageStorageLogic.storeImageFileSystem(
			        BaseEncoding.base64().decode(base64Image), outputPath, absoluteFilename);
			trTaskdetaillob.setImagePath(outputFile);
		}
		trTaskdetaillob.setQuestionText(questionText);
		trTaskdetaillob.setLatitude(checkEmptyBigdecimal(latitude));
		trTaskdetaillob.setLongitude(checkEmptyBigdecimal(longitude));
		
		if (Boolean.TRUE.equals(isGps) || (null != trTaskdetaillob.getLatitude() && null != trTaskdetaillob.getLongitude() &&
				trTaskdetaillob.getLatitude().intValue() !=0  && trTaskdetaillob.getLongitude().intValue() !=0 ) &&
				!Boolean.TRUE.equals(isConverted)) {
			trTaskdetaillob.setIsGps("1");
		} else if (Boolean.FALSE.equals(isGps)) {
			trTaskdetaillob.setIsGps("0");
		}
		
		trTaskdetaillob.setMcc(checkEmptyInteger(mcc));
		trTaskdetaillob.setMnc(checkEmptyInteger(mnc));
		trTaskdetaillob.setLac(checkEmptyInteger(lac));
		trTaskdetaillob.setCellId(checkEmptyInteger(cellId));
		trTaskdetaillob.setAccuracy(checkEmptyInteger(accuracy));
		if (!Boolean.TRUE.equals(isConverted) && !"1".equals(trTaskdetaillob.getIsGps()) 
				&& null != trTaskdetaillob.getMcc() && null != trTaskdetaillob.getMnc()
				&& null != trTaskdetaillob.getLac() && null != trTaskdetaillob.getCellId()) {
			this.getLocationByCellId(trTaskdetaillob, auditContext);
		} else if (Boolean.TRUE.equals(isConverted)) {
			trTaskdetaillob.setIsConverted("1");
		}
		
		if (null != msQuestion.getMsAssettag()) {
			Object paramsSurveyData[][] = { {Restrictions.eq("uuidTaskId", trTaskH.getUuidTaskH())} };
			TrTasksurveydata trTasksurveydata = this.getManagerDAO().selectOne(TrTasksurveydata.class, paramsSurveyData);
			TrTasksurveydata taskSurveyDataInsert = new TrTasksurveydata();
			if (null == trTasksurveydata) {
				taskSurveyDataInsert.setUsrCrt(auditContext.getCallerId());
				taskSurveyDataInsert.setDtmCrt(new Date());
				taskSurveyDataInsert.setTrTaskH(trTaskH);
				taskSurveyDataInsert.setUuidTaskId(trTaskH.getUuidTaskH());
			} else {
				taskSurveyDataInsert = trTasksurveydata;
				taskSurveyDataInsert.setUsrUpd(auditContext.getCallerId());
				taskSurveyDataInsert.setDtmUpd(new Date());
			}
			
			if (GlobalVal.ASSET_TAG_HOME.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setHomeLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setHomeLongitude(trTaskdetaillob.getLongitude());
			} else if (GlobalVal.ASSET_TAG_IDENTITY.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setLegalAddrLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setLegalAddrLongitude(trTaskdetaillob.getLongitude());
			} else if (GlobalVal.ASSET_TAG_OFFICE.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setOfficeLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setOfficeLongitude(trTaskdetaillob.getLongitude());
			} else if (GlobalVal.ASSET_TAG_STREET.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setDrivewayLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setDrivewayLongitude(trTaskdetaillob.getLongitude());
			} else if (GlobalVal.ASSET_TAG_VEHICLE.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setVehicleLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setVehicleLongitude(trTaskdetaillob.getLongitude());
			}
			
			if (null == trTasksurveydata) {
				this.getManagerDAO().insert(taskSurveyDataInsert);
			} else {
				this.getManagerDAO().update(taskSurveyDataInsert);
			}
		}	

		this.getManagerDAO().update(trTaskdetaillob);
		
		if (ImageStorageLocation.DMS == saveImgLoc) {
			UploadImageRequestBean request = new UploadImageRequestBean();
			UploadImageBean uploadBean  = new UploadImageBean();
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Object [][] param = {{paramTaskH, trTaskH.getTaskId()}};
			Object groupTaskId = this.getManagerDAO().selectOneNativeString(
					"SELECT GROUP_TASK_ID " + 
					"FROM   MS_GROUPTASK with(nolock) " + 
					"WHERE  UUID_TASK_H = :uuidTaskH ", param);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(trTaskH.getMsForm().getUuidForm()));
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if (null != trTaskH.getSubmitDate()) {
	        	taskDate = trTaskH.getSubmitDate();
	        } else {
	        	taskDate = new Date();
	        }
			try {
				cutoffDate = new SimpleDateFormat(dateFormat).parse(gs.getGsValue());
			} catch (ParseException e1) {
				LOG.error(e1.getMessage(), e1);
				e1.printStackTrace();
			}
			if (taskDate.before(cutoffDate)) {
				uploadBean.setId(trTaskH.getTaskId());
				uploadBean.setTaskId(trTaskH.getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				uploadBean.setId(groupTaskIdForm);
				uploadBean.setTaskId(groupTaskIdForm);
			}
	
	
			uploadBean.setType("survey");
			uploadBean.setJenisDoc(msQuestion.getQuestionLabel());
			uploadBean.setRefId(msQuestion.getRefId());
			uploadBean.setUsername(trTaskH.getAmMsuser().getUniqueId());
			if (StringUtils.isNotEmpty(base64Image)){
				trTaskdetaillob.setImagePath(String.valueOf(trTaskdetaillob.getUuidTaskDetailLob()));
				this.getManagerDAO().update(trTaskdetaillob);
				request.setByteImage(BaseEncoding.base64().decode(base64Image));
			}
			request.setFileName(trTaskdetaillob.getUuidTaskDetailLob()+".jpg");
			try {
				request.setIpAddress(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e) {
				LOG.error("Logging error DMS Host : {}",e.getMessage());
			}
			request.setUploadImageBean(uploadBean); 
			if(StringUtils.isNotEmpty(base64Image)){
				
				//PANGGIL KE INTNCFORMLOGIC
				intFormLogic.uploadImageResponse(auditContext,request);
			}
		}
		
		return trTaskdetaillob.getUuidTaskDetailLob();
	}
	
	private void getLocationByCellId(TrTaskdetaillob detailLob, AuditContext auditContext){
		List<LocationBean> listLocations = new ArrayList<>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(detailLob.getCellId());
		locationBean.setLac(detailLob.getLac());
		locationBean.setMcc(detailLob.getMcc());
		locationBean.setMnc(detailLob.getMnc());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			detailLob.setLatitude(BigDecimal.valueOf(locationBean.getCoordinate().getLatitude()));
			detailLob.setLongitude(BigDecimal.valueOf(locationBean.getCoordinate().getLongitude()));
			detailLob.setAccuracy(locationBean.getAccuracy());
			
			detailLob.setTextAnswer("Coord : " + detailLob.getLatitude().setScale(6, RoundingMode.HALF_UP)+", " +
					detailLob.getLongitude().setScale(6, RoundingMode.HALF_UP) + " Accuracy : " + detailLob.getAccuracy()+" m");
			detailLob.setIsConverted("1");
		}
	}
	
	private AnswerBean constructAnswer(TaskDocumentBean document, int idxAnswer, MsQuestion msQuestion,
			String answerTypeCode, String questionText, String textAnswer, String optionAnswerId,
			String latitude, String longitude, String accuracy, String mcc, String mnc, String lac, String cellId,
			TrTaskH trTaskH, String image, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		AnswerBean answer = (idxAnswer == -1) ? new AnswerBean() : document.getAnswers().get(idxAnswer);
		QuestionBean questionBean = new QuestionBean(
				msQuestion.getUuidQuestion(), questionText, msQuestion.getRefId(), answerTypeCode,
				(null == msQuestion.getMsOrdertag() ? null : msQuestion.getMsOrdertag().getTagName()),
				(null == msQuestion.getMsAssettag() ? null : msQuestion.getMsAssettag().getAssetTagName()),
				(null == msQuestion.getMsCollectiontag() ? null : msQuestion.getMsCollectiontag().getTagName()));
		answer.setQuestion(questionBean);
		
		if (MssTool.isChoiceQuestion(answerTypeCode)) {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			List<OptionBean> options = (null == answer.getOptAnswers()) ? new ArrayList<>() : answer.getOptAnswers();
			
			MsLov msLovByLovId = null; 
			if (StringUtils.isNotBlank(optionAnswerId)) {
				msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
			}
			if (null != msLovByLovId) {
				OptionBean option = new OptionBean();
				option.setUuid(msLovByLovId.getUuidLov());
				option.setCode(msLovByLovId.getCode());
				option.setDesc(msLovByLovId.getDescription());
				option.setFreeText(StringUtils.trimToNull(textAnswer));
				if (!options.contains(option)) {
					options.add(option);
				}
				
				answer.setOptAnswers(options);
			}				
		} else if (MssTool.isImageQuestion(answerTypeCode) || GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
			answer.setOptAnswers(null);
			Boolean isGps = null;
			Boolean isConverted = null;
			
			if (null != mcc) {
				com.adins.mss.model.taskdjson.LocationBean locationBean = (null == answer.getLocation()) ?
						new com.adins.mss.model.taskdjson.LocationBean() : answer.getLocation();
				locationBean.setMcc(checkEmptyInteger(mcc));
				locationBean.setMnc(checkEmptyInteger(mnc));
				locationBean.setLac(checkEmptyInteger(lac));
				locationBean.setCid(checkEmptyInteger(cellId));
				locationBean.setAccuracy(checkEmptyInteger(accuracy));
				
				if (null != latitude && null != longitude
						&& new BigDecimal(latitude).intValue() != 0 && new BigDecimal(longitude).intValue() != 0) {
					BigDecimal lat = checkEmptyBigdecimal(latitude);
					BigDecimal lng = checkEmptyBigdecimal(longitude);
					locationBean.setLat(null != lat ? lat.doubleValue() : null);
					locationBean.setLng(null != lng ? lng.doubleValue() : null);
					locationBean.setIsGps(1);
				} else {
					LocationBean convertedLocation = this.getLocationByCellId(locationBean.getMcc(), locationBean.getMnc(),
							locationBean.getLac(), locationBean.getCid(), auditContext);
					if (null != convertedLocation.getCoordinate()) {
						locationBean.setAccuracy(convertedLocation.getAccuracy());
						locationBean.setLat(convertedLocation.getCoordinate().getLatitude());
						locationBean.setLng(convertedLocation.getCoordinate().getLongitude());
						locationBean.setIsGps(0);
						locationBean.setIsConverted(1);
						
						latitude = Double.toString(convertedLocation.getCoordinate().getLatitude());
						longitude = Double.toString(convertedLocation.getCoordinate().getLongitude());
						accuracy = Integer.toString(convertedLocation.getAccuracy());
					}
					isGps = Boolean.FALSE;
					isConverted = Boolean.TRUE;
				}
				
				answer.setLocation(locationBean);
			}

			if (MssTool.isImageQuestion(answerTypeCode)) {
				ImageBean imageBean = (null == answer.getLobAnswer()) ? new ImageBean() : answer.getLobAnswer();
				if (null != image && !image.isEmpty()) {
					imageBean.setHasImage(true);
					answer.setLobAnswer(imageBean);
				}
				
				//insert into Table TR_TASKDETAILLOB
				String uuidTaskD = this.getManagerDAO().getUUID();
				long idLob = this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, isGps, isConverted, isl, imagePath);
				if (idLob > 0L){
					imageBean.setId(idLob);
				}
			} else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
				answer.setTxtAnswer(textAnswer);
			}
		} else {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)
					|| GlobalVal.ANSWER_TYPE_DATETIME.equals(answerTypeCode)) {
				textAnswer = this.dateAnswerToText(textAnswer, answerTypeCode);
			}
			answer.setOptAnswers(null);
			answer.setTxtAnswer(textAnswer);
		}
		
		return answer;
	}
	
	private String dateAnswerToText(String dateAnswer, String answerTypeCode) {
		DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
		try {
			Date result = df.parse(dateAnswer);
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)) {
				return (null != result) ? new SimpleDateFormat(dateFormat).format(result) : null;
			} else {
				return (null != result) ? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result) : null;
			}
		} catch (ParseException e) {
			LOG.warn("Cannot parse '{}' to answer type {}", dateAnswer, answerTypeCode, e);
			return null;
		}
	}
	
	private LocationBean getLocationByCellId(int mcc, int mnc, int lac, int cellId, AuditContext auditContext) {
		List<LocationBean> listLocations = new ArrayList<>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(cellId);
		locationBean.setLac(lac);
		locationBean.setMnc(mnc);
		locationBean.setMcc(mcc);
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);
		return locationBean;
	}

	@Transactional
	@Override
	public BiometricResponse doBiometric(BiometricRequest request, String formName, AuditContext auditContext) {
		Map<String, Object> result = new HashMap<>();
		Status status = new Status();
		String taskIdPolo = (String) this.getManagerDAO().selectOneNativeString("SELECT ISNULL(ORDER_NO_CAE, TASK_ID_POLO) FROM TR_TASK_H WITH(NOLOCK) WHERE UUID_TASK_H = :uuidTaskH ", new Object[][] {{"uuidTaskH", request.getUuidTaskH()}});
		Integer isPreApproval = (Integer) this.getManagerDAO().selectOneNativeString("SELECT IS_PRE_APPROVAL FROM TR_TASK_H WITH(NOLOCK) WHERE UUID_TASK_H = :uuidTaskH ", new Object[][] {{"uuidTaskH", request.getUuidTaskH()}});
		
		String query = "SELECT ISNULL(ttd.TEXT_ANSWER, ttd.INT_TEXT_ANSWER) PRODUCT_OFFERING_NAME \r\n" +
				"FROM TR_TASK_H tth WITH(NOLOCK)\r\n" + 
				"JOIN TR_TASK_D ttd ON tth.UUID_TASK_H = ttd.UUID_TASK_H\r\n" + 
				"WHERE 1=1\r\n" + 
				"	AND tth.UUID_TASK_H = :uuidTaskH\r\n" + 
				"	AND ttd.QUESTION_TEXT = 'Product Offering Type (POT)'";
		List<Map<String, Object>> map = this.getManagerDAO().selectAllNativeString(query, 
				new Object[][] {{"uuidTaskH", request.getUuidTaskH()}});
		
		String queryProduct = "SELECT TOP 1 spo.PRODUCT_OFFERING_CODE\r\n" + 
				"FROM STAGING_PRODUCT_OFFERING spo WITH(NOLOCK)\r\n" + 
				"WHERE spo.PRODUCT_OFFERING_NAME = :productOfferingName";
		
		String productOfferingName = StringUtils.EMPTY;
		
		if (map != null && !map.isEmpty()) {
		    productOfferingName = String.valueOf(map.get(0).get("d0"));
		}
		
		String productOfferingCode = (String) this.getManagerDAO().selectOneNativeString(queryProduct,
				new Object[][] { {"productOfferingName", productOfferingName} });	
		
		
		String queryBranch = "SELECT	mb.BRANCH_CODE, mr.REGION_CODE, \r\n" + 
				"		ISNULL(tth.AGREEMENT_NO, '') CONTRACT_NO \r\n" + 
				"FROM TR_TASK_H tth WITH(NOLOCK)\r\n" + 
				"JOIN MS_BRANCH mb ON tth.UUID_BRANCH = mb.UUID_BRANCH\r\n" + 
				"JOIN MS_REGION mr ON mb.UUID_REGION = mr.UUID_REGION\r\n" + 
				"WHERE 1=1\r\n" + 
				"	AND tth.UUID_TASK_H = :uuidTaskH\r\n"; 
		List<Map<String, Object>> mapBranch = this.getManagerDAO().selectAllNativeString(queryBranch, 
				new Object[][] {{"uuidTaskH", request.getUuidTaskH()}});
		
		String officeCode = String.valueOf(mapBranch.get(0).get("d0"));
		String officeRegionCode = String.valueOf(mapBranch.get(0).get("d1"));
		String contractNo = String.valueOf(mapBranch.get(0).get("d2"));
		
		Map<String, String> filter = request.getFilter();
		if (null != filter) {
			List <Map<String, Object>> listProcessOfLayer = this.getManagerDAO().selectAllNativeString(
					"SELECT    MMSL.PROCESS, MMSL.PARAM_API, MMSL.QUESTION_PROCESS " + 
					"FROM      MS_MAPPINGSUBMITLAYER MMSL WITH (NOLOCK) " + 
					"JOIN      MS_FORM MSF WITH (NOLOCK) ON MSF.UUID_FORM = MMSL.UUID_FORM " +
					"WHERE     MMSL.PROCESS = 'BIOMETRIC'" +
					"          AND MSF.FORM_NAME = :formName", new Object[][] { {"formName", formName} });
			
			for (int y = 0; y < listProcessOfLayer.size(); y++) {
				Map<String, Object> mapProcessOfLayer = listProcessOfLayer.get(y);
				String questionProcess = null != mapProcessOfLayer.get("d2") ? String.valueOf(mapProcessOfLayer.get("d2")) : null;									
				String ansOfQuestionProcess = StringUtils.isBlank(questionProcess) ? null : filter.get(questionProcess);
				String processOfLayer = String.valueOf(mapProcessOfLayer.get("d0"));
					
				try {
					int resultProcess = 0;
					if(StringUtils.isNotBlank(ansOfQuestionProcess)) {
						resultProcess = this.convertResultAnswerToCode(ansOfQuestionProcess, processOfLayer);
					}
					
					if(resultProcess != 1) {

						Map<String, String> mapApi = generateMapForParamApi(filter, mapProcessOfLayer.get("d1").toString());
						
						if (null == mapApi || mapApi.isEmpty()) {
							continue;
						}
						String image = request.getImage();
						if (StringUtils.isBlank(image)) {
							image = mapApi.get("fotoBiometric");
						}
						
						Map mapBiometric = intFormLogic.checkBiometricCae(officeCode, officeRegionCode, productOfferingCode, mapApi.get("nik"), mapApi.get("selfiePhoto"), 
								mapApi.get("nikPgn"), mapApi.get("selfiePhotoPsgn"), mapApi.get("nikGrtr"), mapApi.get("selfiePhotoGrtr"), taskIdPolo, contractNo, isPreApproval, null, null, auditContext);
						int codeBiometric = (int) mapBiometric.get("code");
						String msg = String.valueOf(mapBiometric.get("msg"));
						String rslt = null != mapBiometric.get("rslt") ? String.valueOf(mapBiometric.get("rslt")) : null;
						if (200 == codeBiometric || 999 == codeBiometric) {
							codeBiometric = 0;
							result.put(questionProcess, rslt);
						}
							
						status.setCode(codeBiometric);
						status.setMessage(msg);
						break;
					}
				} catch (Exception e) {
					LOG.error(e.getMessage());
					status.setCode(100);
					status.setMessage(e.getMessage());
				}
			}
		}
		
		BiometricResponse resp = new BiometricResponse();
		resp.setMapResult(result);
		resp.setStatus(status);
		return resp;
	}
	
	private Map<String, String> getBiometricParamFromSubmitLayer(String param, Map<String, String> filter) {
		Map<String, String> result = new HashMap<>();			
		String[] listParam = param.split(";");
		
		for (int i = 0; i < listParam.length; i++) {
			String[] paramKeyVal = listParam[i].split("@@@");
			if (null != filter.get(paramKeyVal[1])) {
				result.put(paramKeyVal[1], filter.get(paramKeyVal[1]));
				
				LOG.info("Param: {} , Value: {} ", paramKeyVal[1], filter.get(paramKeyVal[1]));
			}
		}
		
		return result;
	}

	@Transactional
	@Override
	public GetDsrResponse getDsr(GetDsrRequest request, String formName, AuditContext auditContext) {
		GetDsrResponse resp = new GetDsrResponse();
		Status status = new Status();
		int code = 1;
		String message = "Empty Parameter";
		
		Map<String, String> filter = request.getFilter();
		if (null != filter) {
			String dsrProcess = String.valueOf(this.getManagerDAO().selectOneNativeString(
					"SELECT    MMSL.PARAM_API " + 
					"FROM      MS_MAPPINGSUBMITLAYER MMSL WITH (NOLOCK) " + 
					"JOIN      MS_FORM MSF WITH (NOLOCK) ON MSF.UUID_FORM = MMSL.UUID_FORM " +
					"WHERE     MMSL.PROCESS = 'DSR' " +
					"          AND MSF.FORM_NAME = :formName", new Object[][] { {"formName", formName} }));

			Map<String, String> mapApi = generateMapForParamApi(filter, dsrProcess);
			StringBuilder stringQuery = new StringBuilder();
			stringQuery.append("SELECT ISNULL(TASK_ID_POLO, ORDER_NO_CAE) AS TASK_ID ");
			stringQuery.append("FROM   TR_TASK_H WITH (NOLOCK) ");
			stringQuery.append("WHERE  UUID_TASK_H = :uuidTaskH ");
			Object[][] prmTaskId = { {paramTaskH, request.getUuidTaskH()} };
			Object taskIdPolo = this.getManagerDAO().selectOneNativeString(stringQuery.toString(), prmTaskId);
			mapApi.put("taskId", String.valueOf(taskIdPolo));
			
			GetDsrFromPoloResponse response = intFormLogic.getDsrFromPolo(mapApi, auditContext);
			message = "Empty Response From Polo";
			if (null != response) {
				message = response.getResponseMessage();
				if ("00".equals(response.getResponseCode()) && null != response.getData()) {
					resp.setValue(String.valueOf(response.getData()));
					
					try {
						resp.setMapReadonly(this.doCheckIncomeFromDsr(formName, filter, auditContext));
						code = 0;
					} catch (Exception e) {
						code = 1;
						message = "Error Income : " + e.getMessage();
					}
				}
			}
		}
		
		status.setCode(code);
		status.setMessage(message);
		resp.setStatus(status);
		
		return resp;
	}
	
	private Map<String, String> doCheckIncomeFromDsr(String formName, Map<String, String> filter, AuditContext auditContext) {
		Map<String, String> result = null;
		String incomeProcess = String.valueOf(this.getManagerDAO().selectOneNativeString(
				"SELECT    MMSL.PARAM_API " + 
				"FROM      MS_MAPPINGSUBMITLAYER MMSL WITH (NOLOCK) " + 
				"JOIN      MS_FORM MSF WITH (NOLOCK) ON MSF.UUID_FORM = MMSL.UUID_FORM " +
				"WHERE     MMSL.PROCESS = 'INCOME' " +
				"          AND MSF.FORM_NAME = :formName", new Object[][] { {"formName", formName} }));
		Map<String, String> mapApiIncome = generateMapForParamApi(filter, incomeProcess);
		if (this.isValidCheckIncome(filter, incomeProcess, auditContext)) {
			Map checkIncome =  intFormLogic.checkIncome(mapApiIncome.get("nik"), mapApiIncome.get("income"), auditContext);
			if (null != checkIncome.get("rslt")) {
				
				if(!"NULL".equalsIgnoreCase(checkIncome.get("rslt").toString())) {
					String isReadonly = this.isResultIncomeRegisteredOnGeneralSetting(checkIncome.get("rslt").toString(), auditContext);
					result = this.buildFlagEditableIncome(incomeProcess, isReadonly, filter, auditContext);						
				}else {
					result = new HashMap<>();
				}
				
			} else {
				throw new RemoteException(checkIncome.get("msg").toString());
			}
		}
		
		return result;
	}
	
	private boolean isValidCheckIncome(Map<String, String> mapJsonRequest, String param, AuditContext auditContext) {
		boolean result = true;
		String[] listParam = param.split(";");
		for (int i = 0; i < listParam.length; i++) {
			String isReadonly = "1";
			String[] paramKeyVal = listParam[i].split("@@@");
			if (StringUtils.isNotBlank(mapJsonRequest.get(paramKeyVal[1]))) {
				MsQuestion msQuestion = this.commonLogic.retrieveQuestionByRefId(paramKeyVal[1], auditContext);
				if (null != msQuestion.getMsAssettag()) {
					if (GlobalVal.ASSET_TAG_NIK.equalsIgnoreCase(msQuestion.getMsAssettag().getAssetTagName())) {
						isReadonly = "0";
					}
				} else {
					isReadonly = (mapJsonRequest.get(paramKeyVal[1]).split("@"))[1];
				}
				
				if ("1".equals(isReadonly)) {
					result = false;
					break;
				}
			}
		}
		
		return result;
	}
	
	private String isResultIncomeRegisteredOnGeneralSetting(String incomeResult, AuditContext auditContext) {
		boolean result = true;
		if (!"NULL".equals(incomeResult)) {
			AmGeneralsetting amGeneralSetting = this.commonLogic.retrieveGs(GlobalKey.GENERALSETTING_MS_INCOME_RESULT, auditContext);
			String listFromGenset = amGeneralSetting.getGsValue();
			String [] arrListFromGenset = listFromGenset.split(";");
			for (int i = 0; i < arrListFromGenset.length; i++) {
				result = incomeResult.equals(arrListFromGenset[i]);
				if (result) {
					break;
				}
			}
		}
		
		return result ? "0" : "1";
	}
	
	private Map<String, String> buildFlagEditableIncome(String param, String isReadonly, Map<String, String> mapJsonRequest, AuditContext auditContext) {
		Map<String, String> result = new HashMap<>();
		String[] listParam = param.split(";");
		for (int i = 0; i < listParam.length; i++) {
			String[] paramKeyVal = listParam[i].split("@@@");
			boolean isValid = true;
			MsQuestion msQuestion = this.commonLogic.retrieveQuestionByRefId(paramKeyVal[1], auditContext);
			if (null != msQuestion.getMsAssettag()) {
				if (GlobalVal.ASSET_TAG_NIK.equalsIgnoreCase(msQuestion.getMsAssettag().getAssetTagName())) {
					isValid = false;
				}
			}
			
			if (isValid) {
				result.put(paramKeyVal[1], isReadonly);
			}
		}
		
		return result;
	}

	@SuppressWarnings("unused")
	@Transactional
	@Override
	public Map<String, Object> submitLayerGetKBIJ(SubmitLayerRequest request, Map<String, String> mapApi) {
		
		Map<String, Object> result = new HashMap<String, Object>();
		String uuidTaskH = request.getTaskH().getUuid_task_h();
		String uuidQuestionGroup = null;
		String[] listRefID_KBIJ = {
				 "SVY_TOT_KTR_ATF_PMHN"
				,"SVY_PLAF_AWL_PMHN"
				,"SVY_TOT_BAK_DBT_PMHN"
				,"COL_JMLKTR_BDEB_PMHN"
				,"MAX_DPD_BDE_PLA_PMHN"
				,"MAX_BD_PLA_LDPD_PMHN"
				,"SVY_TOT_ANG_PMHN"
				,"SVY_LAST_UPD_PMHN"
				,"TOT_KTRK_PLA_PMHN"
				,"MAX_DPD_PLA_PMHN"
				,"MAX_PLA_DPD_PMHN"
				,"SVY_TOT_MAX_DPD_PMHN"
				,"SVY_LAST_UPD_PMHN2"
		};
		
		Map<String, String> checkResultCode = kbijDataLogic.checkResultCode(uuidTaskH);
		String isHitAPI = checkResultCode.get("isHitAPI");
		String resultCodeCust = checkResultCode.get("BiroKreditRecomendationCode");
		String resultCodePsgn = checkResultCode.get("BiroKreditRecomendationCodePsgn");
		String resultCodeGrtr = checkResultCode.get("BiroKreditRecomendationCodeGrtr");
		
		if("1".equalsIgnoreCase(isHitAPI)) {
			HistoryCreditKBIJRequest reqClass = getHistoryCreditKBIJRequest(uuidTaskH, resultCodeCust, resultCodePsgn, resultCodeGrtr, mapApi);
			String reqJson = gson.toJson(reqClass, HistoryCreditKBIJRequest.class);
			LOG.info("Json : {}", reqJson);
			String resJson = intFormLogic.getHistoryCreditKBIJ(reqJson);
			if(resJson == null || resJson.isEmpty()) {//Return if Empty
				return result;
			}
			
			//Convert JSON Property Name to Java Attribute Name
			try {
				Field summaryMSS = HistoryCreditKBIJResponse.class.getDeclaredField("summary_mss");
				Field[] listField = summaryMSS.getType().getDeclaredFields();
				for(int i = 0; i < listField.length; i++) {
					String jsonName = listField[i].getAnnotation(JsonProperty.class).value();
					String paramName = listField[i].getName();
					if(jsonName == null) {
						jsonName = paramName;
					}
					resJson = resJson.replace("\"" + jsonName + "\"", "\"" + paramName + "\"");
				}
			} catch (NoSuchFieldException e1) {
				e1.printStackTrace();
			} catch (SecurityException e1) {
				e1.printStackTrace();
			}
			
			//Get List Mapping Task D
			Object[][] paramsMapTaskD = {
					{"formName", GlobalVal.FORM_PRE_SURVEY}
			};
			String queryMapTaskD =
					  "SELECT [COL_NAME], [REF_ID]\r\n"
					+ "FROM TBL_MAP_TASK_D A with(nolock)\r\n"
					+ "JOIN MS_FORM B with(nolock) ON A.UUID_FORM = B.UUID_FORM\r\n"
					+ "WHERE B.FORM_NAME = :formName\r\n"
					+ "AND ANSWER_VALUE IS NULL\r\n "
					+ "AND SOURCE_DATA IS NULL\r\n ";
			List<Map<String, Object>> listMapTaskD = this.getManagerDAO().selectAllNativeString(queryMapTaskD, paramsMapTaskD);
			Map<String, Object> listMapRefID = new HashMap<>();
			for(Map<String, Object> mapTaskD : listMapTaskD) {
				String col_name = (String) mapTaskD.get("d0");
				String ref_id = (String) mapTaskD.get("d1");
				listMapRefID.put(col_name, ref_id);
			}
			
			//Get Json Summary MSS
			Map<String, Object> resJsonMap = gson.fromJson(resJson, Map.class);
			Map<String, Object> summaryMssMap = (Map<String, Object>) resJsonMap.get("summary_mss");
			String summaryMssJson = gson.toJson(summaryMssMap, Map.class);
			
			//Get Map Result
			if(summaryMssJson != null && !summaryMssJson.equals("null")) {
				result = getMapData(summaryMssJson, listMapRefID);
			}
		}
		return result;
	}
	private HistoryCreditKBIJRequest getHistoryCreditKBIJRequest(String uuidTaskH, String resultCodeCust, String resultCodePsgn, String resultCodeGrtr, Map<String, String> mapApi) {
		HistoryCreditKBIJRequest reqClass = new HistoryCreditKBIJRequest();
		
		Map<String, Object> mapParamAnswer = new HashMap<String, Object>();
		String[][] listRefID_RequestKBIJ = {
				  {"PRE_NO_KTP", "nik"}
				 ,{"PRE_NAMA_KTP", "fullname"}
				 ,{"PRE_TMPT_LHR", "birthplace"}
				 ,{"PRE_TGL_LHR", "birthdate"}
				 ,{"PRE_IBU_KANDUNG", "mothername"}

				 ,{"PRE_NO_KTP_PSGN", "nik_spouse"}
				 ,{"PRE_NAMA_PSGN", "fullname_spouse"}
				 ,{"PRE_TMPT_LHR_PSGN", "birthplace_spouse"}
				 ,{"PRE_TGL_LHR_PSGN", "birthdate_spouse"}
				 ,{"PRE_IBU_KANDUNG_PSGN", "mothername_spouse"}

				 ,{"PRE_NO_KTP_GRTR", "nik_guarantor"}
				 ,{"PRE_NAMA_KTP_GRTR", "fullname_guarantor"}
				 ,{"PRE_TMPT_LHR_GRTR", "birthplace_guarantor"}
				 ,{"PRE_TGL_LHR_GRTR", "birthdate_guarantor"}
				 ,{"PRE_IBU_KANDUNG_GRTR", "mothername_guarantor"}
				 
				 ,{"PRE_DP_NTF", "ntf"}
				 
		};
		List<String> listRefID = new ArrayList<String>();
		for(String[] refID_RequestKBIJ : listRefID_RequestKBIJ) {
			listRefID.add(refID_RequestKBIJ[0]);
		}
		
		Object[][] paramsListDataKBIJ = {
				{"uuidTaskH", uuidTaskH},
				{"listRefID", listRefID}
		};
		String queryListDataKBIJ = 
				  "SELECT\r\n" + 
				  "	A.APPL_NO,\r\n" + 
				  "	E.BRANCH_CODE, \r\n" + 
				  "	C.REF_ID, F.ANSWER_TYPE_NAME,\r\n" + 
				  "	CASE\r\n" + 
				  "		WHEN F.ANSWER_TYPE_NAME IN ('Text', 'Numeric', 'Currency') THEN ISNULL(B.TEXT_ANSWER, B.INT_TEXT_ANSWER)\r\n" + 
				  "		WHEN F.ANSWER_TYPE_NAME IN ('Dropdown') THEN B.INT_OPTION_TEXT\r\n" + 
				  "		WHEN F.ANSWER_TYPE_NAME IN ('Date') THEN CONVERT(VARCHAR(50), ISNULL(CONVERT(DATE, B.TEXT_ANSWER, 105), CONVERT(DATE, B.INT_TEXT_ANSWER, 105)), 105)\r\n" +
				  "		ELSE ''\r\n" + 
				  "END ANSWER\r\n" + 
				  "FROM TR_TASK_H A with(nolock)\r\n" + 
				  "LEFT JOIN TR_TASK_D B with(nolock) ON A.UUID_TASK_H = B.UUID_TASK_H\r\n" + 
				  "LEFT JOIN MS_QUESTION C with(nolock) ON B.UUID_QUESTION = C.UUID_QUESTION\r\n" + 
				  "LEFT JOIN AM_MSUSER D with(nolock) ON A.UUID_MS_USER = D.UUID_MS_USER\r\n" + 
				  "LEFT JOIN MS_BRANCH E with(nolock) ON D.UUID_BRANCH = E.UUID_BRANCH\r\n" + 
				  "LEFT JOIN MS_ANSWERTYPE F with(nolock) ON C.UUID_ANSWER_TYPE = F.UUID_ANSWER_TYPE\r\n" + 
				  "WHERE A.UUID_TASK_H = :uuidTaskH\r\n" + 
				  "AND C.REF_ID IN ( :listRefID )";
		List<Map<String, Object>> listDataKBIJ = this.getManagerDAO().selectAllNativeString(queryListDataKBIJ, paramsListDataKBIJ);
		
		if(listDataKBIJ.size() > 0) {//listDataKBIJ > mapParamAnswer
			for(Map<String, Object> dataKBIJ : listDataKBIJ) {
				String refID = (String) dataKBIJ.get("d2");//REF_ID
				String answer = (String) dataKBIJ.get("d4");//ANSWER
				String param_name = "";
				for(int i = 0; i < listRefID_RequestKBIJ.length; i++) {
					String refID_Map = listRefID_RequestKBIJ[i][0];
					if(refID.equalsIgnoreCase(refID_Map)) {
						param_name = listRefID_RequestKBIJ[i][1];
						mapParamAnswer.put(param_name, answer);
						
						LOG.info("ANSWER : {} : {}", param_name, answer);
					}
				}
				
			}
		}

		
		reqClass.setCheck_type("slik");
		reqClass.setSource("MSS");
		reqClass.setApp_no(mapApi.get("taskIdPolo"));
		
		if("1".equalsIgnoreCase(resultCodeCust) || "1".equalsIgnoreCase(resultCodePsgn)
				|| "1".equalsIgnoreCase(resultCodeGrtr)) {
			//Pemohon
			reqClass.setNik(getStringFromMap(mapParamAnswer, "nik"));
			reqClass.setFullname(getStringFromMap(mapParamAnswer, "fullname"));
			reqClass.setBirthdate(getStringFromMap(mapParamAnswer, "birthdate"));
			reqClass.setBirthplace(getStringFromMap(mapParamAnswer, "birthplace"));
			reqClass.setMothername(getStringFromMap(mapParamAnswer, "mothername"));
			//pasangan
			reqClass.setNik_spouse(getStringFromMap(mapParamAnswer, "nik_spouse"));
			reqClass.setFullname_spouse(getStringFromMap(mapParamAnswer, "fullname_spouse"));
			reqClass.setBirthdate_spouse(getStringFromMap(mapParamAnswer, "birthdate_spouse"));
			reqClass.setBirthplace_spouse(getStringFromMap(mapParamAnswer, "birthplace_spouse"));
			reqClass.setMothername_spouse(getStringFromMap(mapParamAnswer, "mothername_spouse"));
			//penjamin
			reqClass.setNik_guarantor(getStringFromMap(mapParamAnswer, "nik_guarantor"));
			reqClass.setFullname_guarantor(getStringFromMap(mapParamAnswer, "fullname_guarantor"));
			reqClass.setBirthdate_guarantor(getStringFromMap(mapParamAnswer, "birthdate_guarantor"));
			reqClass.setBirthplace_guarantor(getStringFromMap(mapParamAnswer, "birthplace_guarantor"));
			reqClass.setMothername_guarantor(getStringFromMap(mapParamAnswer, "mothername_guarantor"));
		}
		 
		reqClass.setOffice_code(mapApi.get("officeCode"));
		reqClass.setOffice_region_code(mapApi.get("officeRegionCode"));
		reqClass.setProduct_offering_code(mapApi.get("productOfferingCode"));
		reqClass.setNtf(getIntFromMap(mapParamAnswer, "ntf"));
		reqClass.setCall_mode(mapApi.get("callMode"));
		reqClass.setIsPreApproval(getStringForPreApproval(mapApi, "IsPreApproval"));
		reqClass.setContract_no(mapApi.get("contractNo"));
		reqClass.setReport(true);
		
		return reqClass;
	}
	
	private Map<String, Object> getMapData(String json, Map<String, Object> listMapRefID) {
		Map<String, Object> result = new HashMap<String, Object>();
		
		KbijData dataKbij = gson.fromJson(json, KbijData.class);
		
		//Format Data KBIJ
		dataKbij = kbijDataLogic.formatDataKBIJ(dataKbij);
		
		json = gson.toJson(dataKbij, KbijData.class);
		Map<String, Object> jsonMap = gson.fromJson(json, Map.class);
		
		//Insert into Result
		for(Map.Entry<String, Object> data : jsonMap.entrySet() ) {
			String param_name = data.getKey();
			String ref_id = getStringFromMap(listMapRefID, param_name);
			String param_value = null;
			if(data.getValue() != null) {
				param_value = String.valueOf(data.getValue());
			}
			if((GlobalVal.REC_FINAL_BK.equals(ref_id) || GlobalVal.SVY_RSLT_REBIKR_GRTR.equals(ref_id) || GlobalVal.SVY_RSLT_REBIKR_PMHN.equals(ref_id) || GlobalVal.SVY_RSLT_REBIKR_PSGN.equals(ref_id)) && StringUtils.isBlank(param_value)) {
				param_value = "Data tidak ditemukan";
			}
			if(param_value != null && ref_id != null) {
				result.put(ref_id, param_value);
				LOG.info("RESULT: {} : {}", ref_id, param_value);
			}
		}
		
		String COL_JMLKTR_BDEB_PMHN = kbijDataLogic.getDataKBIJ(dataKbij, GlobalVal.KBIJ_DATATYPE.COLLECT_CUST.code());
		String COL_JMLKTR_BDEB_PSGN = kbijDataLogic.getDataKBIJ(dataKbij, GlobalVal.KBIJ_DATATYPE.COLLECT_SPOUSE.code());
		String COL_JMLKTR_BDEB_GRTR = kbijDataLogic.getDataKBIJ(dataKbij, GlobalVal.KBIJ_DATATYPE.COLLECT_GRTR.code());
		
		String SVY_REASON_PMHN = kbijDataLogic.getDataKBIJ(dataKbij, GlobalVal.KBIJ_DATATYPE.REASON_CUST.code());
		String SVY_REASON_PSGN = kbijDataLogic.getDataKBIJ(dataKbij, GlobalVal.KBIJ_DATATYPE.REASON_SPOUSE.code());
		String SVY_REASON_GRTR = kbijDataLogic.getDataKBIJ(dataKbij, GlobalVal.KBIJ_DATATYPE.REASON_GRTR.code());
		
		result.put("COL_JMLKTR_BDEB_PMHN", COL_JMLKTR_BDEB_PMHN);
		result.put("COL_JMLKTR_BDEB_PSGN", COL_JMLKTR_BDEB_PSGN);
		result.put("COL_JMLKTR_BDEB_GRTR", COL_JMLKTR_BDEB_GRTR);

		result.put("SVY_REASON_PMHN", SVY_REASON_PMHN);
		result.put("SVY_REASON_PSGN", SVY_REASON_PSGN);
		result.put("SVY_REASON_GRTR", SVY_REASON_GRTR);
		
		return result;
	}
	
	@Async
	@Transactional(readOnly = false, isolation=Isolation.READ_UNCOMMITTED)
	private void updateResultEkycPolo(Map<String, Object> resultLayer, TrTaskH trTaskH , AuditContext auditContext) {
		
		String formName = trTaskH.getMsForm().getFormName();
		Map <String, String> mapResult = new HashMap<>();
		
		Object[][] paramGroupTask = { { "uuidTaskH", trTaskH.getUuidTaskH() } };
		BigInteger grpTaskId = (BigInteger) this.getManagerDAO().selectOneNativeString(
				"select top 1 (GROUP_TASK_ID) from MS_GROUPTASK with (nolock) where UUID_TASK_H = :uuidTaskH",
				paramGroupTask);

		mapResult.put("TaskIdMss", String.valueOf(grpTaskId));
		mapResult.put("OrderNo", trTaskH.getApplNo());
		mapResult.put("TaskId", trTaskH.getOrderNoCae());
		mapResult.put("IsContinueTask", "0");
		mapResult.put("formData", formName);
			
		Object[][] paramMapJson = { {"formName", formName}, {"type", GlobalVal.MAPPING_JSON_SUBMIT_EKYC_POLO} };
		List<Map<String, Object>> mapJson = this.getManagerDAO().selectAllNativeString(
				"SELECT KEY_SOURCE, KEY_DESTINATION " +
				"FROM   MS_MAPPINGJSON WITH (NOLOCK) " +
				"WHERE  FORM_NAME = :formName " +
				"       AND TYPE = :type ", paramMapJson);
		for (int j = 0; j < mapJson.size(); j++) {
			Map<String, Object> map = mapJson.get(j);
			String colName = String.valueOf(map.get("d0"));
			String[] splitType = colName.split("@");
			String refId = splitType[0];
			String type = splitType[1];
			String alias = String.valueOf(map.get("d1"));
			
			mapResult.put(alias,(String)resultLayer.get(refId));
		}
		
		String json = gson.toJson(mapResult);
		
		LOG.info("JSON UPDATE DATA POLO: {}", json);
		
		this.intFormLogic.updateDataPoloLayer(json, trTaskH, auditContext);
		
	}
	
	
	@Transactional(readOnly = false, isolation=Isolation.READ_UNCOMMITTED, propagation=Propagation.REQUIRES_NEW)
	@Override
	public Map<String, Object> checkDataEKYC(CheckDataEkycRequest request, AuditContext auditContext) {
		Map<String, Object> resultLayer = new HashMap<>(); 

		// Create Task H First
		TrTaskH trTaskH = this.getManagerDAO().selectOne("from TrTaskH h left join fetch h.msStatustask left join fetch h.msForm where h.uuidTaskH = :uuidTaskH",
				new Object[][] {{paramTaskH, Long.valueOf(request.getTaskH().getUuid_task_h())}});
	
		String queryBranch = "SELECT	mb.BRANCH_CODE, mr.REGION_CODE, \r\n" + 
				"		ISNULL(tth.AGREEMENT_NO, '') CONTRACT_NO\r\n" + 
				"FROM TR_TASK_H tth WITH(NOLOCK)\r\n" + 
				"JOIN MS_BRANCH mb ON tth.UUID_BRANCH = mb.UUID_BRANCH\r\n" + 
				"JOIN MS_REGION mr ON mb.UUID_REGION = mr.UUID_REGION\r\n" + 
				"WHERE 1=1\r\n" + 
				"	AND tth.UUID_TASK_H = :uuidTaskH\r\n"; 
		List<Map<String, Object>> mapBranch = this.getManagerDAO().selectAllNativeString(queryBranch, 
				new Object[][] {{"uuidTaskH", request.getTaskH().getUuid_task_h()}});
		 
		String result = StringUtils.EMPTY;
		String process = StringUtils.EMPTY; 
		String errorMessage = StringUtils.EMPTY;
		String taskIdPolo = trTaskH.getTaskIdPolo();
		String officeCode = String.valueOf(mapBranch.get(0).get("d0"));
		String officeRegionCode = String.valueOf(mapBranch.get(0).get("d1"));
		String contractNo = String.valueOf(mapBranch.get(0).get("d2")); 
		Map<String, String> mapApi = new HashMap<>();
		String negativeCust = StringUtils.EMPTY;
		 
		for (int i = 0; i < request.getQuestionGroup().size(); i++) {
			QuestionGroupBean bean = request.getQuestionGroup().get(i);
			
			if (StringUtils.isBlank(result) || !result.contains(GlobalVal.TASK_VISIT_RESULT_FAILED)) {
				List <Map<String, Object>> listProcessOfLayer = this.getManagerDAO().selectAllNativeString(
						"SELECT    PROCESS, UUID_QUESTION_RESULT, PARAM_API, QUESTION_PROCESS " + 
						"FROM      MS_MAPPINGSUBMITLAYER WITH (NOLOCK) " + 
						"WHERE     UUID_FORM = :uuidForm " +
						"          AND UUID_QUESTION_GROUP = :uuidQuestionGroup " + 
						"ORDER BY  PROCESS_SEQ",
						new Object[][] { {"uuidForm", request.getTaskH().getUuid_scheme()}, {"uuidQuestionGroup", bean.getUuidQuestionGroup()} });
				
				if (null != listProcessOfLayer && !listProcessOfLayer.isEmpty()) {
					String questionResult = null;
					String layerResult = null;
					Map<String, String> filter = request.getFilter().get(bean.getUuidQuestionGroup());
					if (null == filter) {
						filter = new HashMap<>();
					}
					Map<String, String> mapMandatory = new HashMap<>();
					String sourceData = filter.getOrDefault("$SOURCE_DATA", StringUtils.EMPTY);
					String isProspect = filter.getOrDefault(GlobalVal.REF_STV_PROSPECT, StringUtils.EMPTY);
					
					if(GlobalVal.SOURCE_DATA_THIRDPARTY.equalsIgnoreCase(sourceData) && "1".equalsIgnoreCase(isProspect)) {
						String message = StringUtils.EMPTY;
						for (Map.Entry<String, String> entryParam : filter.entrySet()) {
					        String key = entryParam.getKey();
					        String value = entryParam.getValue();
					       
							if (key.contains("TMPT_LHR") || key.contains("TGL_LHR") || key.contains("IBU_KANDUNG")) {
								if (StringUtils.isBlank(value)){
									mapMandatory.put(key, "1");
									String questionLabel = this.commonLogic.retrieveQuestionByRefId(key, auditContext).getQuestionLabel();
									message = message +"field "+questionLabel+" is required, ";
								}
							}
						}
						message = message.replaceAll(",\\s*$", "");
						if (!mapMandatory.isEmpty()) {
							resultLayer.put(GlobalKey.STATUS_RESULT_MESSAGES, message);
							resultLayer.put("is_mandatory", mapMandatory);
							return resultLayer;
						}
					}
						 
					if (null != listProcessOfLayer.get(0).get("d1")) {
						questionResult = listProcessOfLayer.get(0).get("d1").toString();
						for (int x = 0; x < bean.getTaskD().size(); x++) {
							SubmitTaskDBean taskDBean = bean.getTaskD().get(x);
							if (StringUtils.equals(questionResult, taskDBean.getQuestion_id())) {
								layerResult = taskDBean.getText_answer(); 
								break;
							}
						}
					}
					
					int resultCode = 1;
					if (StringUtils.isBlank(layerResult) || !"Success".equals(layerResult)) { 
						
						for (int y = 0; y < listProcessOfLayer.size(); y++) {
							int resultProcess = 0;
							
							Map<String, Object> mapProcessOfLayer = listProcessOfLayer.get(y);
							String processOfLayer = String.valueOf(mapProcessOfLayer.get("d0"));
							String questionProcess = null == mapProcessOfLayer.get("d3") ? null : String.valueOf(mapProcessOfLayer.get("d3"));
							String ansOfQuestionProcess = StringUtils.isBlank(questionProcess) ? null : filter.get(questionProcess);
							
							if (null != mapProcessOfLayer.get("d2")) {
								mapApi = generateMapForParamApi(filter, String.valueOf(mapProcessOfLayer.get("d2")));
							}
							
							if (null != mapApi && !mapApi.isEmpty()) {
								mapApi.put("taskIdPolo", taskIdPolo);
								mapApi.put("officeCode", officeCode);
								mapApi.put("officeRegionCode", officeRegionCode);
								mapApi.put("contractNo", contractNo); 
 								
								if(StringUtils.isNotBlank(ansOfQuestionProcess)) {
									resultProcess = this.convertResultAnswerToCodeTaskVisit(ansOfQuestionProcess, processOfLayer);
								}
																
								if(resultProcess != 1) {
									try {
										if (GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL.equals(processOfLayer)) {
											resultProcess = doValidateDukcapil(mapApi, null, auditContext);
										} else if (GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST.equals(processOfLayer)) {
											resultProcess = doCheckNegativeCust(mapApi, null, auditContext);
										}										
									} catch (Exception e) {
										LOG.error("Error ketika proses cek {}", process, e);
									}
									
									ansOfQuestionProcess = this.convertCodeToResultAnswerTaskVisit(resultProcess, processOfLayer);
							
								}

							} else if(null == mapApi || mapApi.isEmpty()) {
								resultProcess = 1;
							}
							
							if ((StringUtils.isNotBlank(questionProcess) && StringUtils.isNotBlank(ansOfQuestionProcess))) {
								resultLayer.put(questionProcess, ansOfQuestionProcess);
								LOG.info("REF_ID: {} : {}", questionProcess, ansOfQuestionProcess);
							}
							
							if (1 == resultCode) {
								resultCode = resultProcess;
								process = processOfLayer;
							}
							
							if (GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST.equals(processOfLayer)) {
								negativeCust = ansOfQuestionProcess;
							}
							LOG.info("result {} : {}", processOfLayer, resultProcess);
						}	
					} else if ("Success".equals(layerResult)) {
						resultCode = 1;
					}
					
					if (0 == resultCode) {
						if (StringUtils.isNotBlank(errorMessage)) {
							result = errorMessage;
						} else {
							result = "Error ketika proses cek " + process; 
						}
					} else if (1 == resultCode) {
						result = GlobalVal.TASK_VISIT_RESULT_SUCCESS;
					} else if (3 == resultCode) { 
						result = GlobalVal.TASK_VISIT_RESULT_SUCCESS;
					} else {
						result = GlobalVal.TASK_VISIT_RESULT_FAILED + " on process " + process;
					}
					
					if (StringUtils.isNotBlank(questionResult)) {
						MsQuestion msQuestion = commonLogic.retrieveQuestionByUuid(Long.valueOf(questionResult), auditContext); 
						resultLayer.put(msQuestion.getRefId(), result);
					}
				 
				}
			}
		} 
		if (result.contains(GlobalVal.TASK_VISIT_RESULT_FAILED)) {
			int maxRetry = 0;
			boolean isDropTask = true;
			AmGeneralsetting amGeneralSetting = this.commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_LAYER_MAX_RETRY, auditContext);
			if (amGeneralSetting!=null) {
				maxRetry = Integer.parseInt(amGeneralSetting.getGsValue());
			}
			
			if (StringUtils.isNotBlank(request.getCountRetry())) {
				int countRetry = Integer.parseInt(request.getCountRetry());
				if (countRetry < maxRetry) {
					isDropTask = false;
				}
			}
			if (isDropTask) {
				this.doDeleteTask(trTaskH, trTaskH.getAmMsuser(), 0, process, negativeCust, auditContext);
			}
		}
		
 		return resultLayer;
	}
	
	private int convertResultAnswerToCodeTaskVisit(String resultAnswer, String process) {
		int result = 0;
		if (GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL.equals(process)) {
			if (GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL_NOTFOUND.equalsIgnoreCase(resultAnswer)) {
				result = 3;
			} else if (GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL_MATCH.equalsIgnoreCase(resultAnswer)) {
				result = 1;
			} else if (GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL_NOTMATCH.equalsIgnoreCase(resultAnswer)) {
				result = 2;
			}
		} else if (GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST.equals(process)) {
			if(GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST_NOTMATCH.equalsIgnoreCase(resultAnswer)) {
				result = 1;
			} else if(GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST_MATCH.equalsIgnoreCase(resultAnswer)) {
				result = 2;
			}
		} 		
		return result;
	}
	
	private String convertCodeToResultAnswerTaskVisit(int resultCode, String process) {
		String result = StringUtils.EMPTY;
		if (GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL.equals(process)) {
			if (3 == resultCode) {
				result = GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL_NOTFOUND;
			} else if (1 == resultCode) {
				result = GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL_MATCH;
			} else if (2 == resultCode) {
				result = GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL_NOTMATCH;
			}
		} else if (GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST.equals(process)) {
			if (2 == resultCode) {
				result = GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST_MATCH;
			} else if (1 == resultCode) {
				result = GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST_NOTMATCH;
			}
		}
		return result;
	}
}