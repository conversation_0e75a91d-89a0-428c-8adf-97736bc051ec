package com.adins.mss.businesslogic.impl.common;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.DistanceUtils;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeocodingConversionLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.LazyLoadLogic;
import com.adins.mss.businesslogic.api.common.SubmitLayerLogic;
import com.adins.mss.businesslogic.api.common.SubmitTaskLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.common.TaskServiceLogic;
import com.adins.mss.businesslogic.api.common.UserLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constant.Global;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.constants.enums.MobileDefAnswer;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.LoginException;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.exceptions.SubmitTaskException;
import com.adins.mss.exceptions.SubmitTaskException.Reason;
import com.adins.mss.exceptions.UserLoginIdNotExistsException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsAgreementcollectlocation;
import com.adins.mss.model.MsAssettag;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsMappingformD;
import com.adins.mss.model.MsMappingformH;
import com.adins.mss.model.MsOrdertag;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TblAgreementHistory;
import com.adins.mss.model.TblCaeData;
import com.adins.mss.model.TblPoloData;
import com.adins.mss.model.TrRvnumber;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskcolldata;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.TrTasklink;
import com.adins.mss.model.TrTaskrejecteddetail;
import com.adins.mss.model.TrTaskrejectedhistory;
import com.adins.mss.model.TrTasksurveydata;
import com.adins.mss.model.TrTaskupdate;
import com.adins.mss.model.custom.MappingFormBean;
import com.adins.mss.model.custom.MessageEventBean;
import com.adins.mss.model.custom.SubmitTaskResultBean;
import com.adins.mss.model.custom.SubmitTaskUpdateResultBean;
import com.adins.mss.model.custom.UploadImageBean;
import com.adins.mss.model.custom.UploadImageRequestBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.ImageBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.QuestionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.AddTaskCAERequest;
import com.adins.mss.services.model.common.AddTaskPoloRequest;

import com.adins.mss.services.model.common.AddTaskScoringCAERequest;
import com.adins.mss.services.model.common.AddTaskScoringCAEResponse;
import com.adins.mss.services.model.common.BiometricRequest;
import com.adins.mss.services.model.common.BiometricResponse;
import com.adins.mss.services.model.common.CheckReferantorResponse;
import com.adins.mss.services.model.common.GetReferantorResponse;
import com.adins.mss.services.model.common.SubmitLayerQuestionGroupBean;
import com.adins.mss.services.model.common.SubmitLayerRequest;
import com.adins.mss.services.model.common.SubmitNegativeCustPoloRequest;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.SubmitTaskHBean;

import com.adins.mss.services.model.common.UpdateDataPoloRequest;
import com.adins.mss.services.model.newconfins.AddTaskDetailBean;
import com.adins.mss.util.CipherTool;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

@SuppressWarnings({"unchecked","rawtypes"})
public class GenericSubmitTaskLogic extends BaseLogic implements SubmitTaskLogic, MessageSourceAware {

	private static final Logger LOG = LoggerFactory.getLogger(GenericSubmitTaskLogic.class);
	private static final String GEOCODE_FORMAT_ADDR = SpringPropertiesUtils.getProperty(GlobalKey.FORMAT_GEOCODING_ADDRESS);
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	
	private Gson gson = new Gson();

	private IntFormLogic intFormLogic;
	private MessageSource messageSource;
	private GeolocationLogic geocoder;
	private UserLogic userLogic;
	private GlobalLogic globalLogic;
	private ImageStorageLogic imageStorageLogic;
	private CommonLogic commonLogic;
	private GeocodingConversionLogic geocodingConversionLogic;
	private TaskServiceLogic taskServiceLogic;
	private SubmitLayerLogic submitLayerLogic;
	private LazyLoadLogic lazyLoadLogic;

	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}
	
	public void setUserLogic(UserLogic userLogic) {
		this.userLogic = userLogic;
	}

	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}
	
	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }
	
	public void setGeocodingConversionLogic(GeocodingConversionLogic geocodingConversionLogic) {
		this.geocodingConversionLogic = geocodingConversionLogic;
	}
	
	public void setTaskServiceLogic(TaskServiceLogic taskServiceLogic) {
		this.taskServiceLogic = taskServiceLogic;
	}

	public void setSubmitLayerLogic(SubmitLayerLogic submitLayerLogic) {
		this.submitLayerLogic = submitLayerLogic;
	}
	
	public void setTaskDistributionLogic(TaskDistributionLogic taskDistributionLogic) {
	}
	
	public void setLazyLoadLogic(LazyLoadLogic lazyLoadLogic) {
		this.lazyLoadLogic = lazyLoadLogic;
	}

	private final String paramApplNo = "applNo";
	private final String deleted = "DELETED";
	private final String released = "RELEASED";
	private final String notesSubmit = "Task has been submitted.";
	private final String paramIsSuccess = "isSuccess";
	private final String paramGroupTaskId = "groupTaskId";
	private final String paramGsCode = "gsCode";
	private final String dateFormat = "dd/MM/yyyy";
	private final String paramTaskId = "taskId";
	
    @Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public SubmitTaskResultBean submitTask(AuditContext auditContext, String application, SubmitTaskHBean taskHBean,
			SubmitTaskDBean taskDBean[], String imei, String androidId, Map<String, Map<String, String>> filter) {
    	Map parameters = (Map) auditContext.getParameters();
		String buildVersion = ((String)parameters.get("applicationVersion")).split("-")[0];
		AmMsuser loginUser = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.amMssubsystem join fetch u.msJob join fetch u.msBranch where u.uuidMsUser = :uuidMsUser",
				new Object[][]{ {"uuidMsUser", Long.valueOf(auditContext.getCallerId())}});
		if("1".equals(loginUser.getMsBranch().getIsPiloting())) {
			String gsValPiloting = (String)this.getManagerDAO().selectOneNativeString("SELECT GS_VALUE FROM AM_GENERALSETTING WITH(NOLOCK) WHERE GS_CODE = 'MS_PRM07_VERS_P' ", null);
			if(StringUtils.isNotBlank(gsValPiloting)) {
				List buildVersionPilotingList = Arrays.asList(gsValPiloting.split(";"));
				if(!buildVersionPilotingList.contains(buildVersion)) {
					String gsValNonPiloting = (String)this.getManagerDAO().selectOneNativeString("SELECT GS_VALUE FROM AM_GENERALSETTING WITH(NOLOCK) WHERE GS_CODE = 'MS_PRM07_VERS' ", null);
					List buildVersionList = Arrays.asList(gsValNonPiloting.split(";"));
					if(buildVersionList.contains(buildVersion)) {
						throw new LoginException("Need Update APK Using Piloting Version", com.adins.mss.exceptions.LoginException.Reason.LOGIN_REQUIRED);
					}
				}
			}else {
				throw new LoginException("Need Update APK Using Piloting Version", com.adins.mss.exceptions.LoginException.Reason.LOGIN_REQUIRED);
			}
		}
		
		DateTime startDateTime = new DateTime();
		LOG.info("Submitting task...{}", taskHBean.getUuid_task_h());
		
		String taskId = StringUtils.EMPTY;
		String uuid = null;
		boolean firstSend = true;
		long groupTaskId = 0;
			
		AmMsuser usr = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.amMssubsystem join fetch u.msJob join fetch u.msBranch where u.uuidMsUser = :uuidMsUser",
				new Object[][]{ {"uuidMsUser", Long.valueOf(taskHBean.getUuid_user())}});
			
		//cek mode submit
		this.validateDeviceId(usr, imei, androidId, auditContext);

		TrTaskH trTaskH = null;
		boolean saveNewTaskArchive = false;
		if (NumberUtils.isDigits(taskHBean.getUuid_task_h())) { //Data text dari NEW SURVEY mobile pertama kali uuidTaskH isinya GUID
			trTaskH = this.getManagerDAO().selectOne("from TrTaskH h left join fetch h.msStatustask where h.uuidTaskH = :uuidTaskH",
					new Object[][] {{"uuidTaskH", Long.valueOf(taskHBean.getUuid_task_h())}});
		}
		else { //NEW_TASK 2018-02-06 penjagaan duplikat submit
			String uniqueTaskH = taskHBean.getUuid_task_h();
			Map<String, Object> resultMap = this.getManagerDAO().selectForMap("services.common.task.checkDuplicateTask",
					new Object[][]{{"uuidTaskHMobile", uniqueTaskH}});
			if (null != resultMap) {
				BigInteger existingTaskId = (BigInteger) resultMap.get("uuid");
				LOG.warn("Duplicate new task submit!!");
				uuid = existingTaskId.toString();
				taskId = uuid;
				return new SubmitTaskResultBean(taskId, uuid);
			}
			
			saveNewTaskArchive = true;
		}
		
		TrTaskrejectedhistory taskRejected = null;
		boolean rejected = false;
		if (null != trTaskH) {
			taskRejected = this.validateRejected(trTaskH, usr, auditContext);
			rejected = (taskRejected != null) ? true : false;
		}
						
		AmMssubsystem subsystem = usr.getAmMssubsystem();
		
		//START Insert into Table TR_TASK_H with return TaskId
		MsStatustask curMsStatus = (trTaskH == null) ? null : trTaskH.getMsStatustask();
        String curStatusCode = (curMsStatus == null) ? null : curMsStatus.getStatusCode();		
		String curTaskId = (trTaskH == null) ? null : String.valueOf(trTaskH.getTaskId());
				
		MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
				Long.parseLong(taskHBean.getUuid_scheme()), taskHBean.getFormVersion(), auditContext);		
		
		boolean flag = false; 
		if (curStatusCode == null && !rejected) {
			MsForm msForm  = this.getManagerDAO().selectOne(MsForm.class, Long.valueOf(taskHBean.getUuid_scheme()));
			
			trTaskH = new TrTaskH();
			trTaskH.setMsForm(msForm);
			trTaskH.setUsrCrt(auditContext.getCallerId());
			trTaskH.setDtmCrt(new Date());
			trTaskH.setFormVersion(taskHBean.getFormVersion());			
			//Priority
			MsPriority normalPriority = this.commonLogic.retrieveMsPriority(GlobalVal.TAKS_PRIORTY_NORMAL, true, auditContext);
			trTaskH.setMsPriority(normalPriority);
			
			flag = true;
		} 
		else {
			if (!rejected && trTaskH != null && usr.getUuidMsUser() == trTaskH.getAmMsuser().getUuidMsUser().longValue()) {
				trTaskH.setUsrUpd(auditContext.getCallerId());
				trTaskH.setDtmUpd(new Date());
				flag = true;
			} 
			else {
				taskId = trTaskH.getTaskId();
			}
		}
		
		boolean flagRevisit = false;
		if (flag) {
			if (!rejected) {
				if (null != taskHBean.getStartDtm()) {
					Date startDtm = taskHBean.getStartDtm();
					trTaskH.setStartDtm(startDtm);
				}
				if (null != taskHBean.getReadDtm()) {
					Date readDtm = taskHBean.getReadDtm();
					trTaskH.setReadDate(readDtm);
				}
				if (null != taskHBean.getPromisedDate()) {
					Date promisedDate = taskHBean.getPromisedDate();
					if (null == trTaskH.getPromiseDate()) {
						trTaskH.setPromiseDate(promisedDate);
					}
				}
				trTaskH.setSendDate(taskHBean.getSubmitDate());
				trTaskH.setSubmitDate(new Date());
				trTaskH.setSubmitLatitude(checkEmptyBigdecimal(taskHBean.getLatitude()));
				trTaskH.setSubmitLongitude(checkEmptyBigdecimal(taskHBean.getLongitude()));
			}
			
			if (curTaskId != null) {
				trTaskH.setTaskId(curTaskId);
				taskId = curTaskId;
			}
			
			if (curStatusCode == null) { //newtask
				trTaskH.setCustomerName(taskHBean.getCustomer_name());
				trTaskH.setCustomerPhone(taskHBean.getCustomer_phone());
				trTaskH.setCustomerAddress(taskHBean.getCustomer_address());
				trTaskH.setNotes(taskHBean.getNotes());
				trTaskH.setAmMsuser(usr);
				trTaskH.setMsBranch(usr.getMsBranch());
				trTaskH.setIsDraft("0");
				trTaskH.setReadDate(null); //newtask no readdate
				trTaskH.setApplNo(this.commonLogic.retrieveNewTaskId(GlobalVal.SEQUENCE_ORDER_NO_CODE));

				if (StringUtils.isBlank(trTaskH.getFlagSource()) && Global.APPLICATION_COLLECTION.equals(subsystem.getSubsystemName())) {
					trTaskH.setFlagSource(GlobalVal.SUBSYSTEM_MC);
				}
				else if (StringUtils.isBlank(trTaskH.getFlagSource()) && Global.APPLICATION_SURVEY.equals(subsystem.getSubsystemName())) {
					trTaskH.setFlagSource(GlobalVal.SUBSYSTEM_MS);
				}							
				trTaskH.setIsAppNotified("0");
				
				this.getManagerDAO().insert(trTaskH);
				trTaskH.setTaskId(String.valueOf(trTaskH.getUuidTaskH()));
				uuid = String.valueOf(trTaskH.getUuidTaskH());
				taskId = uuid;
				
				if (GlobalVal.SUBSYSTEM_MS.equals(subsystem.getSubsystemName())) {		
					this.saveGroupTaskSurvey(trTaskH, auditContext); //insert ms_grouptask
					groupTaskId = trTaskH.getUuidTaskH();
				}
				else if (GlobalVal.SUBSYSTEM_MC.equals(subsystem.getSubsystemName())) { //revisit collection
					flagRevisit = this.saveRevisit(taskHBean, trTaskH, auditContext);
				}

				firstSend = true;
				
				if (saveNewTaskArchive) {
					Map<String, Object> paramNewTaskArchive = new HashMap<>();
					paramNewTaskArchive.put("uuidTaskHMobile", taskHBean.getUuid_task_h());
					paramNewTaskArchive.put("uuidTaskHWeb", uuid);
					paramNewTaskArchive.put("callerId", auditContext.getCallerId());
					this.getManagerDAO().insertNative("services.common.task.insertNewTaskArchive", paramNewTaskArchive);
				}
			}
			else if (curStatusCode != null && "N".equals(curStatusCode)){
				trTaskH.setTaskId(String.valueOf(trTaskH.getUuidTaskH()));
				this.getManagerDAO().update(trTaskH);
				firstSend = true;
			}
			else {
				trTaskH.setTaskId(String.valueOf(trTaskH.getUuidTaskH()));
				this.getManagerDAO().update(trTaskH);
				firstSend = false;
			}
			//End insert into Table TR_TASK_H
		}				
					
		Map<Integer, MsQuestion> msQuestions = this.getAllMsQuestion(taskDBean, formHist);
		//LOOPING FOR TR_TASK_D
		ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(auditContext);
		Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl) ? this.imageStorageLogic.retrieveGsImageFileSystemPath(auditContext) : null;

		SaveTaskDResult saveResult = null;
        boolean saveAsJson = PropertiesHelper.isTaskDJson();
		if (saveAsJson && flag) {
			saveResult = this.saveTaskDIntoJson(taskDBean, trTaskH, usr, flagRevisit, taskRejected, flag, msQuestions, isl, imagePath, groupTaskId, auditContext);
		}
		else if (saveAsJson && !flag) {
			saveResult = this.saveTaskDRejected(taskDBean, trTaskH, taskRejected, msQuestions, isl, auditContext);
		}
		else {			
			//2016-09-16 SUM - select all taskD first instead of select one for all answers
			Map<Long, TrTaskD> listTaskD = this.listTaskDAsMap(String.valueOf(trTaskH.getUuidTaskH()));
		    saveResult = this.saveTaskDIntoRow(taskDBean, trTaskH, usr, flagRevisit, taskRejected, flag, msQuestions, listTaskD, isl, imagePath, auditContext);
		}
		String isFinal = saveResult.getIsFinal();
		String totalBayar = saveResult.getTotalBayar();
		
		String productCategoryCode = saveResult.getProdCat();
		
		//return taskId untuk reAssign task / status task D/V/P/R
		if (!firstSend) {
			MsStatustask tempStatus = trTaskH.getMsStatustask();
			if (null != tempStatus) {
				if (GlobalVal.SURVEY_STATUS_TASK_DELETED.equals(tempStatus.getStatusCode()) ||
						GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equals(tempStatus.getStatusCode()) ||
						GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(tempStatus.getStatusCode()) ||
						GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(tempStatus.getStatusCode()) ||
						GlobalVal.SURVEY_STATUS_TASK_APPROVALDATAENTRY.equals(tempStatus.getStatusCode()) ||
						GlobalVal.SURVEY_STATUS_TASK_APPROVALONWOMF.equals(tempStatus.getStatusCode())) {
					return new SubmitTaskResultBean(trTaskH.getTaskId(), null);
				}
			}
		}
		//finish insert task
		
		boolean isValidateCmoRecommendation = true;
		String submitLayerResult = StringUtils.EMPTY;
		String notesDel = StringUtils.EMPTY;
		/*12-04-2022 Validate IS_CANCEL_APP & CMO Recommendation CR CAE FASE 1*/
		if ("1".equalsIgnoreCase(loginUser.getMsBranch().getIsPilotingCAE())) {
			if ("1".equals(isFinal)) {
				if (GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
					if (StringUtils.isBlank(taskHBean.getSendTaskPreSurvey())) {
						trTaskH.setIsOffline("0");
					} else {
						trTaskH.setIsOffline("1");
					}
					this.getManagerDAO().update(trTaskH);
				} else if (GlobalVal.MSCOREF.equals(trTaskH.getFlagSource()) || GlobalVal.MSCOREP.equals(trTaskH.getFlagSource())
						|| GlobalVal.MSIAFOTO.equals(trTaskH.getFlagSource())) {
					String flagOffline = isHavePreSurvey(trTaskH.getApplNo());
					if (StringUtils.isNotBlank(flagOffline)) {
						trTaskH.setIsOffline(flagOffline);
					} else {
						trTaskH.setIsOffline("0");
					}
					this.getManagerDAO().update(trTaskH);
				}
			}
			AmMssubsystem subs = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, auditContext);
			MsStatustask statusTask = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_DELETED, subs.getUuidMsSubsystem(), auditContext);
			MsStatusmobile statusMobile = commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_DELETED, auditContext);
			if (StringUtils.isNotBlank(saveResult.getIsCancelApp()) && "1".equalsIgnoreCase(saveResult.getIsCancelApp())) {
				isValidateCmoRecommendation = false;
				trTaskH.setMsStatustask(statusTask);
				trTaskH.setMsStatusmobile(statusMobile);
				trTaskH.setDtmUpd(new Date());
				trTaskH.setUsrUpd(usr.getFullName());
				this.getManagerDAO().update(trTaskH);
				notesDel = this.messageSource.getMessage("businesslogic.notes.changestatus.cancellapp", null, 
									this.retrieveLocaleAudit(auditContext));
			} 
			
			if("0".equalsIgnoreCase(saveResult.getIsCmoRecommendation())) {
				trTaskH.setCmoRecommendation(saveResult.getIsCmoRecommendation());
				isValidateCmoRecommendation = false;
				intFormLogic.submitNegativeList(auditContext, curTaskId, "1", "1");
				if (Objects.equals(trTaskH.getMsStatustask().getUuidStatusTask(), statusTask.getUuidStatusTask())) {
					notesDel = this.messageSource.getMessage("businesslogic.notes.changestatus.cancel.not.cmo.recommendation", null, 
							this.retrieveLocaleAudit(auditContext));
				} else {
					trTaskH.setMsStatustask(statusTask);
					trTaskH.setMsStatusmobile(statusMobile);
					String tagCmoRecommendation = "CMO Recommendation";
					if (GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
						tagCmoRecommendation = "CRO Recommendation";
					}
					notesDel = this.messageSource.getMessage("businesslogic.notes.changestatus.not.cmo.recommendation", new Object[] {tagCmoRecommendation}, 
							this.retrieveLocaleAudit(auditContext));
				}
				trTaskH.setDtmUpd(new Date());
				trTaskH.setUsrUpd(usr.getFullName());
				this.getManagerDAO().update(trTaskH);
			}
			
			if ("1".equals(isFinal) && GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName()) && isValidateCmoRecommendation) {
				SubmitLayerRequest request = this.buildRequestForSubmitLayer(taskHBean, filter);
				Map<String, Object> submitLayer = submitLayerLogic.doSubmitLayer(request, true, taskHBean.getSendTaskPreSurvey(), auditContext);
				LOG.info("Result Submit Layer: {}", submitLayer);
				int counterLayer = 1;
				for (Map.Entry<String, Object> entry : submitLayer.entrySet()) {
					submitLayerResult = null != entry.getValue() ? entry.getValue().toString() : null;
					if (GlobalVal.PRE_SURVEY_RESULT_FAILED.equals(submitLayerResult) && StringUtils.isBlank(taskHBean.getSendTaskPreSurvey())) {
						notesDel = this.messageSource.getMessage("businesslogic.notes.changestatus.submitlayerfailure",
								new Object[] {counterLayer}, this.retrieveLocaleAudit(auditContext));
						
						trTaskH.setMsStatustask(statusTask);
						trTaskH.setMsStatusmobile(statusMobile);
						trTaskH.setDtmUpd(new Date());
						trTaskH.setUsrUpd(usr.getFullName());
						
						this.getManagerDAO().update(trTaskH);
						break;
					}
					counterLayer++;
				}
			}
			
			if (StringUtils.isNotBlank(notesDel)) {
				// INSERT INTO TR TASK HISTORY
				TrTaskhistory trTaskHistory = new TrTaskhistory(
						trTaskH.getMsStatustask(), trTaskH,
						usr.getFullName(), new Date(),
						notesDel, null,
						usr.getFullName(),
						GlobalVal.CODE_PROCESS_DELETED);
				this.getManagerDAO().insert(trTaskHistory);
				SubmitTaskResultBean resultBean = new SubmitTaskResultBean(taskId, uuid);
				if(!isValidateCmoRecommendation ||
						(GlobalVal.PRE_SURVEY_RESULT_FAILED.equals(submitLayerResult) && StringUtils.isBlank(taskHBean.getSendTaskPreSurvey()))) {
					if (GlobalVal.PROCESS_CODE_MSCOREP.equals(trTaskH.getFlagSource())
							|| GlobalVal.PROCESS_CODE_MSCOREF.equals(trTaskH.getFlagSource())) {
						String flagSources = GlobalVal.PROCESS_CODE_MSCOREP;
						if (GlobalVal.PROCESS_CODE_MSCOREP.equals(trTaskH.getFlagSource())) {
							flagSources = GlobalVal.PROCESS_CODE_MSCOREF;
						}
						
						MsStatustask statusDelete = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_DELETED, subsystem.getUuidMsSubsystem(), auditContext);
						MsStatustask statusRejectWithResurvey = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_REJECTED_WITH_RESURVEY, subsystem.getUuidMsSubsystem(), auditContext);
						
						Object[][] paramsTask = { {Restrictions.eq(paramApplNo, trTaskH.getApplNo()) },
								{Restrictions.eq("flagSource", flagSources)}, 
								{Restrictions.ne("msStatustask.uuidStatusTask", statusDelete.getUuidStatusTask())},
								{Restrictions.ne("msStatustask.uuidStatusTask", statusRejectWithResurvey.getUuidStatusTask())}
								};
						TrTaskH taskHPair = this.getManagerDAO().selectOne(TrTaskH.class, paramsTask);
						
						taskHPair.setMsStatustask(statusTask);
						taskHPair.setMsStatusmobile(statusMobile);
						taskHPair.setDtmUpd(new Date());
						taskHPair.setUsrUpd(usr.getFullName());
						
						this.getManagerDAO().update(taskHPair);
						
						TrTaskhistory trTaskHistorypair = new TrTaskhistory(
								taskHPair.getMsStatustask(), taskHPair,
								usr.getFullName(), new Date(),
								notesDel, null,
								usr.getFullName(),
								GlobalVal.CODE_PROCESS_DELETED);
						this.getManagerDAO().insert(trTaskHistorypair);
						
						this.intFormLogic.updateDataPolo(null, trTaskH, null, deleted, "F", this.getNegativeCustomer(trTaskH.getApplNo(), trTaskH.getCmoRecommendation()), null, null, null, auditContext);
						
						AmMsuser submitterBean = this.getManagerDAO().selectOne(AmMsuser.class, new Object[][] {{Restrictions.eq("uuidMsUser", trTaskH.getAmMsuser().getUuidMsUser())}});
						
						this.getManagerDAO().fetch(trTaskH.getMsGrouptasks());
						MsGrouptask groupTask = trTaskH.getMsGrouptasks().iterator().next();
						AddTaskCAERequest tblRequestBean = new AddTaskCAERequest();
						AddTaskPoloRequest tblPoloRequestBean = new AddTaskPoloRequest();
						String isIA = StringUtils.EMPTY;
						Integer isPreApproval = 0;
 
						if (StringUtils.isNotBlank(trTaskH.getTaskIdPolo())) {
							TblPoloData poloBean = this.getManagerDAO().selectOne(TblPoloData.class, 
									new Object[][] {
								{Restrictions.eq("taskIdPolo", trTaskH.getTaskIdPolo())}, 
								{Restrictions.eq("groupTaskId", groupTask.getGroupTaskId())},
								{Restrictions.eq("isSuccess", "1")}
							});
							tblPoloRequestBean = gson.fromJson(poloBean.getJsonRequest(), AddTaskPoloRequest.class);
							isPreApproval = tblPoloRequestBean.getIsPreApproval();
						} else {
							TblCaeData tblBean = this.getManagerDAO().selectOne(TblCaeData.class, 
									new Object[][] {
										{Restrictions.eq("orderNoCae", trTaskH.getOrderNoCae())}, 
										{Restrictions.eq("groupTaskId", groupTask.getGroupTaskId())},
										{Restrictions.eq("isSuccess", "1")}
									});
							tblRequestBean = gson.fromJson(tblBean.getJsonRequest(), AddTaskCAERequest.class);
							isIA = tblBean.getIsIa();
							isPreApproval = tblRequestBean.getIsPreApproval();
						}
						
						String mssStat = StringUtils.EMPTY;
						
						MsStatustask rejectResurveyStat = this.getManagerDAO().selectOne(MsStatustask.class, 
								new Object[][] {{Restrictions.eq("statusCode",GlobalVal.SURVEY_STATUS_TASK_REJECTED_WITH_RESURVEY)}});
						TrTaskH previousTask = this.getManagerDAO().selectOne(TrTaskH.class, 
								new Object[][] {
													{Restrictions.eq("flagSource", trTaskH.getFlagSource())},
													{Restrictions.eq("applNo", trTaskH.getApplNo())},
													{Restrictions.eq("msStatustask.uuidStatusTask",rejectResurveyStat.getUuidStatusTask())}
												});

						if(null == previousTask) {
							if("1".equals(isIA) || 1 == isPreApproval) {
								if(GlobalVal.USR_CRT_ADD_TASK_SCORING_CAE.equalsIgnoreCase(trTaskH.getUsrCrt()) 
										|| GlobalVal.USR_CRT_AUTO_CANCEL_TASK.equalsIgnoreCase(trTaskH.getUsrCrt())) {
									mssStat = GlobalVal.UPDATE_STATUS_IDE_CANCEL_RESURVEY;
								} else {

									mssStat = GlobalVal.UPDATE_STATUS_IDE_DELETE;
								}
							}else {
								mssStat = GlobalVal.UPDATE_STATUS_IDE_DELETE;
							}
						} else {
							mssStat = GlobalVal.UPDATE_STATUS_IDE_CANCEL_RESURVEY;
						}
						this.intFormLogic.updateStatusIDE(trTaskH, mssStat, mssStat, submitterBean, null, auditContext);
					} else {
						this.doUpdateAndInsertHistoryOfflineTask(trTaskH, subsystem, true, notesDel, taskHBean.getSendTaskPreSurvey(), auditContext);
					}
					
					resultBean.setDropTask(true);
					resultBean.setMessage(notesDel);
				}
				
				return resultBean;
			}
		}
		/*end*/
		
		if (flag) {						
			long uuidProcess = this.getUuidProcess(trTaskH, subsystem);
			String codeProcess = StringUtils.EMPTY;
			
			//Check is final & update status task
			if (firstSend && "0".equals(isFinal)) {
				MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_UPLOADING, auditContext);
				trTaskH.setMsStatusmobile(msm);
				commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
				
				if (GlobalVal.SUBSYSTEM_MC.equals(subsystem.getSubsystemName()) && !"0".equals(totalBayar)){
					//update cash on hand HANYA ketika first send
					userLogic.updateCashOnHand(NumberUtils.toLong(taskHBean.getUuid_task_h()), totalBayar, auditContext);
				}				
			}
			else if (!firstSend && "0".equals(isFinal) && GlobalVal.SUBSYSTEM_MS.equals(subsystem.getSubsystemName())){
				if (null == trTaskH.getMsStatustask()) {
					Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_UPLOADING) } };
					MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
					trTaskH.setMsStatusmobile(msm);
					commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
				}
				else {
					if (!GlobalVal.SURVEY_STATUS_TASK_UPLOADING.equals(trTaskH.getMsStatustask().getStatusCode())) {
						Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_UPLOADING) } };
						MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
						trTaskH.setMsStatusmobile(msm);
						commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
					}
				}
			} 
			else if ((firstSend && "1".equals(isFinal))) {
				if (GlobalVal.SUBSYSTEM_MC.equals(subsystem.getSubsystemName()) && !"0".equals(totalBayar)) {
					//update cash on hand HANYA ketika first send
					userLogic.updateCashOnHand(NumberUtils.toLong(taskHBean.getUuid_task_h()), totalBayar, auditContext);
				}	
				//get status from workflow
				MsStatustask msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
				
				//update status mobile jadi released
				Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_SUBMITTED) } };
				MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
				trTaskH.setMsStatusmobile(msm);
				
				// LOGIC UNTUK MENGUBAH STATUS TASK MENJADI APPROVAL BERDASARKAN SETTINGAN APPROVAL
				MsStatustask statusTaskBySettingApprovalOts = null;
				String flagSourceOts = null;
				if (GlobalVal.FORM_OTS.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
					flagSourceOts = (String) this.getManagerDAO().selectOneNativeString("SELECT FLAG_SOURCE_OTS FROM TBL_OTS_DATA WITH(NOLOCK) WHERE GROUP_TASK_ID = :uuidTaskH ", new Object[][] {{"uuidTaskH", trTaskH.getUuidTaskH()}});
				}
				int count = this.getStatusBySettingApprovalOts(trTaskH.getMsForm().getUuidForm(), "IS_APPROVAL_ON_DE", trTaskH.getAmMsuser().getMsJob().getUuidJob(), flagSourceOts);
				if (count > 0) {
					statusTaskBySettingApprovalOts = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_APPROVALDATAENTRY, subsystem.getUuidMsSubsystem(), auditContext);
					codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
				} else {
					count = this.getStatusBySettingApprovalOts(trTaskH.getMsForm().getUuidForm(), "IS_APPROVAL_ON_WOM", trTaskH.getAmMsuser().getMsJob().getUuidJob(), flagSourceOts);
					if (count > 0) {
						statusTaskBySettingApprovalOts = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_APPROVALONWOMF, subsystem.getUuidMsSubsystem(), auditContext);
						codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
					}
					else {
						count = this.getStatusBySettingApprovalOts(trTaskH.getMsForm().getUuidForm(), null, trTaskH.getAmMsuser().getMsJob().getUuidJob(), flagSourceOts);
						if (count > 0) {
							statusTaskBySettingApprovalOts = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_RELEASED, subsystem.getUuidMsSubsystem(), auditContext);
							codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
						}
					}
				}
				
				if (statusTaskBySettingApprovalOts == null) {
					//get status from workflow
					msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
					
					if (GlobalVal.COLLECTION_STATUS_TASK_UPLOADING.equals(msStatusTask.getStatusCode())) {
						trTaskH.setMsStatusmobile(msm);
						msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
					}
					
					if (GlobalVal.SURVEY_STATUS_TASK_RELEASED_ON_PENDING.equals(msStatusTask.getStatusCode())) {
						codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
					} else if (GlobalVal.SURVEY_STATUS_TASK_APPROVALDATAENTRY.equals(msStatusTask.getStatusCode())) {
						codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
					} else {
						codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
					}
				} else {
					msStatusTask = statusTaskBySettingApprovalOts;
					trTaskH.setMsStatustask(msStatusTask);
					trTaskH.setDtmUpd(new Date());
					this.getManagerDAO().update(trTaskH);
				}
				
				String notes = notesSubmit;
				
				//INSERT INTO CHECK HISTORY
				insertTaskHistory(auditContext, msStatusTask, trTaskH, notes, 
						codeProcess, usr.getFullName(), usr, null);
				
				if (GlobalVal.PROCESS_CODE_MSCOREP.equalsIgnoreCase(trTaskH.getFlagSource()) || GlobalVal.MSIAFOTO.equalsIgnoreCase(trTaskH.getFlagSource())) {
					String email = Arrays.stream(taskDBean)
						    .filter(d -> GlobalVal.PMHN_EMAIL.equalsIgnoreCase(d.getRefId()))
						    .map(SubmitTaskDBean::getText_answer)
						    .findFirst()
						    .orElse(null);
					insertTblAgreementHistory(trTaskH, saveResult, email);
				}
				
				if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
					commitOrder(usr, trTaskH.getNotes(), trTaskH, subsystem, 0, codeProcess, auditContext);
				}
				
				this.getManagerDAO().fetch(trTaskH.getMsBranch());
				String flagIsPiloting = trTaskH.getMsBranch().getIsPiloting();
				if(GlobalVal.SUBSYSTEM_MS.equals(trTaskH.getFlagSource())) {
					if ("1".equals(flagIsPiloting)) {
						Object paramMap[][] = { {Restrictions.eq("msForm.uuidForm", Long.valueOf(taskHBean.getUuid_scheme()))},
								{Restrictions.eq("isActive", "1")}, {Restrictions.eq("isPiloting", "1")} };
						MsMappingformH msMapFormH = this.getManagerDAO().selectOne(MsMappingformH.class, paramMap);
						if (null != msMapFormH) {
							createTaskSurveyPiloting(auditContext, usr, trTaskH, subsystem, msMapFormH, groupTaskId);
						} else {
							Object paramForm[][] = { {Restrictions.eq("uuidForm", Long.valueOf(taskHBean.getUuid_scheme()))} };
							MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, paramForm);
							throw new SubmitTaskException(this.messageSource.getMessage(
									"businesslogic.submit.surveynomapping", new Object[]{msForm.getFormName()}, this.retrieveLocaleAudit(auditContext)),
									Reason.NO_ORDER_SURVEY_MAPPING);
						}
					} else {
						Object paramMap[][] = { {Restrictions.eq("msForm.uuidForm", Long.valueOf(taskHBean.getUuid_scheme()))},
								{Restrictions.eq("isActive", "1")}, {Restrictions.ne("isPiloting", "1")} };
						MsMappingformH msMapFormH = this.getManagerDAO().selectOne(MsMappingformH.class, paramMap);
						if (null != msMapFormH) {
							createTaskSurvey(auditContext, usr, trTaskH, subsystem, msMapFormH, groupTaskId);
						} else {
							Object paramForm[][] = { {Restrictions.eq("uuidForm", Long.valueOf(taskHBean.getUuid_scheme()))} };
							MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, paramForm);
							throw new SubmitTaskException(this.messageSource.getMessage(
									"businesslogic.submit.surveynomapping", new Object[]{msForm.getFormName()}, this.retrieveLocaleAudit(auditContext)),
									Reason.NO_ORDER_SURVEY_MAPPING);
						}
					}
				}
			} 
			else if (!firstSend && "1".equals(isFinal)) {				
				//get status from workflow
				Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_SUBMITTED) } };
				MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
				trTaskH.setMsStatusmobile(msm);
				
				MsStatustask msStatusTask = trTaskH.getMsStatustask();
				// LOGIC UNTUK MENGUBAH STATUS TASK MENJADI APPROVAL BERDASARKAN SETTINGAN APPROVAL
				MsStatustask statusTaskBySettingApprovalOts = null;
				String flagSourceOts = null;
				if (GlobalVal.FORM_OTS.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
					flagSourceOts = (String) this.getManagerDAO().selectOneNativeString("SELECT FLAG_SOURCE_OTS FROM TBL_OTS_DATA WITH(NOLOCK) WHERE GROUP_TASK_ID = :uuidTaskH ", new Object[][] {{"uuidTaskH", trTaskH.getUuidTaskH()}});
				}
				int count = this.getStatusBySettingApprovalOts(trTaskH.getMsForm().getUuidForm(), "IS_APPROVAL_ON_DE", trTaskH.getAmMsuser().getMsJob().getUuidJob(), flagSourceOts);
				if (count > 0) {
					statusTaskBySettingApprovalOts = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_APPROVALDATAENTRY, subsystem.getUuidMsSubsystem(), auditContext);
					codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
				} else {
					count = this.getStatusBySettingApprovalOts(trTaskH.getMsForm().getUuidForm(), "IS_APPROVAL_ON_WOM", trTaskH.getAmMsuser().getMsJob().getUuidJob(), flagSourceOts);
					if (count > 0) {
						statusTaskBySettingApprovalOts = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_APPROVALONWOMF, subsystem.getUuidMsSubsystem(), auditContext);
						codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
					}
					else {
						count = this.getStatusBySettingApprovalOts(trTaskH.getMsForm().getUuidForm(), null, trTaskH.getAmMsuser().getMsJob().getUuidJob(), flagSourceOts);
						if (count > 0) {
							statusTaskBySettingApprovalOts = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_RELEASED, subsystem.getUuidMsSubsystem(), auditContext);
							codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
						}
					}
				}
				if (statusTaskBySettingApprovalOts == null) {
					//get status from workflow
					msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
					
					if (GlobalVal.COLLECTION_STATUS_TASK_UPLOADING.equals(msStatusTask.getStatusCode())) {
						trTaskH.setMsStatusmobile(msm);
						msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
					}
					
					if (GlobalVal.SURVEY_STATUS_TASK_RELEASED_ON_PENDING.equals(msStatusTask.getStatusCode())) {
						codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
					} else if (GlobalVal.SURVEY_STATUS_TASK_APPROVALDATAENTRY.equals(msStatusTask.getStatusCode())) {
						codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
					} else {
						codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
					}
				} else {
					msStatusTask = statusTaskBySettingApprovalOts;
					trTaskH.setMsStatustask(msStatusTask);
					trTaskH.setDtmUpd(new Date());
					this.getManagerDAO().update(trTaskH);
					AmMsuser submitterBean = this.getManagerDAO().selectOne(AmMsuser.class, new Object[][] {{Restrictions.eq("uuidMsUser", trTaskH.getAmMsuser().getUuidMsUser())}});
					this.intFormLogic.updateStatusIDE(trTaskH, GlobalVal.UPDATE_STATUS_IDE_APPROVAL_ON_WOM, GlobalVal.UPDATE_STATUS_IDE_APPROVAL_ON_WOM,
							submitterBean, null, auditContext);
				}
				
				String notes = notesSubmit;
				
				//INSERT INTO CHECK HISTORY
				insertTaskHistory(auditContext, msStatusTask, trTaskH, notes, 
						codeProcess, usr.getFullName(), usr, null);
				
				if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
					commitOrder(usr, trTaskH.getNotes(), trTaskH, subsystem, 0, codeProcess, auditContext);
				}
				
				this.getManagerDAO().fetch(trTaskH.getMsBranch());
				String flagIsPiloting = trTaskH.getMsBranch().getIsPiloting();
				if(GlobalVal.SUBSYSTEM_MS.equals(trTaskH.getFlagSource())) {
					if ("1".equals(flagIsPiloting)) {
						Object paramMap[][] = { {Restrictions.eq("msForm.uuidForm", Long.valueOf(taskHBean.getUuid_scheme()))},
								{Restrictions.eq("isActive", "1")}, {Restrictions.eq("isPiloting", "1")} };
						MsMappingformH msMapFormH = this.getManagerDAO().selectOne(MsMappingformH.class, paramMap);
						if (null != msMapFormH) {
							createTaskSurveyPiloting(auditContext, usr, trTaskH, subsystem, msMapFormH, groupTaskId);
						} else {
							Object paramForm[][] = { {Restrictions.eq("uuidForm", Long.valueOf(taskHBean.getUuid_scheme()))} };
							MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, paramForm);
							throw new SubmitTaskException(this.messageSource.getMessage(
									"businesslogic.submit.surveynomapping", new Object[]{msForm.getFormName()}, this.retrieveLocaleAudit(auditContext)),
									Reason.NO_ORDER_SURVEY_MAPPING);
						}
					} else {
						Object paramMap[][] = { {Restrictions.eq("msForm.uuidForm", Long.valueOf(taskHBean.getUuid_scheme()))},
								{Restrictions.eq("isActive", "1")}, {Restrictions.ne("isPiloting", "1")} };
						MsMappingformH msMapFormH = this.getManagerDAO().selectOne(MsMappingformH.class, paramMap);
						if (null != msMapFormH) {
							createTaskSurvey(auditContext, usr, trTaskH, subsystem, msMapFormH, groupTaskId);
						} else {
							Object paramForm[][] = { {Restrictions.eq("uuidForm", Long.valueOf(taskHBean.getUuid_scheme()))} };
							MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, paramForm);
							throw new SubmitTaskException(this.messageSource.getMessage(
									"businesslogic.submit.surveynomapping", new Object[]{msForm.getFormName()}, this.retrieveLocaleAudit(auditContext)),
									Reason.NO_ORDER_SURVEY_MAPPING);
						}
					}
				}
				
				this.getManagerDAO().fetch(trTaskH.getMsForm());
			}
		}
		
		if (firstSend && GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
			
			Object[][] paramProspect = { {Restrictions.eq("assetTagName", GlobalVal.ASSET_TAG_PROSPECT)} };
			MsAssettag assetPros = this.getManagerDAO().selectOne(MsAssettag.class, paramProspect);

			Object[][] paramTaskDPros = { {"uuidTaskH", trTaskH.getUuidTaskH()}, {"uuidAssetTag", assetPros.getUuidAssetTag()} };
			String answPros = (String) this.getManagerDAO().selectOneNative("services.common.task.getAnswerCodeByAssetTag", paramTaskDPros);
			if (StringUtils.isBlank(answPros) && StringUtils.isNotBlank(saveResult.getPoloProspect())) {
				answPros = saveResult.getPoloProspect();
			}
			
			Object[][] paramInter = { {Restrictions.eq("assetTagName", GlobalVal.ASSET_TAG_INTEREST)} };
			MsAssettag assetInter = this.getManagerDAO().selectOne(MsAssettag.class, paramInter);

			Object[][] paramTaskDProd = { {"uuidTaskH", trTaskH.getUuidTaskH()}, {"uuidAssetTag", assetInter.getUuidAssetTag()} };
			String answInter = (String) this.getManagerDAO().selectOneNative("services.common.task.getAnswerCodeByAssetTag", paramTaskDProd);
			if (StringUtils.isBlank(answInter) && StringUtils.isNotBlank(saveResult.getPoloInterest())) {
				answInter = saveResult.getPoloInterest();
			}
			
			Object[][] paramGroupTask = { { "uuidTaskH", trTaskH.getUuidTaskH() } };
			BigInteger grpTaskId = (BigInteger) this.getManagerDAO().selectOneNativeString(
					"select top 1 (GROUP_TASK_ID) from MS_GROUPTASK with (nolock) where UUID_TASK_H = :uuidTaskH",
					paramGroupTask);
			
			Object[][] paramPoloData = {{Restrictions.eq("taskIdPolo",trTaskH.getTaskIdPolo())}, {Restrictions.eq(paramIsSuccess,"1")},
					{Restrictions.eq(paramGroupTaskId, grpTaskId.longValue())}};
			TblPoloData poloData = this.getManagerDAO().selectOne(TblPoloData.class, paramPoloData);
			AddTaskPoloRequest requestTaskPolo = gson.fromJson(poloData.getJsonRequest(), AddTaskPoloRequest.class);
			
			UpdateDataPoloRequest poloRequest = new UpdateDataPoloRequest();
			poloRequest.setIdName(GlobalVal.IDNAME_MSS_POLO);
			poloRequest.setTaskId(trTaskH.getTaskIdPolo());
//			poloRequest.setApplicationId(trTaskH.getApplNo());
			poloRequest.setFieldPersonName(trTaskH.getAmMsuser().getFullName());
			poloRequest.setNotesNewLead(StringUtils.substring(trTaskH.getNotes(), 0, 100));
			poloRequest.setOrderNo(trTaskH.getApplNo());
			poloRequest.setAppNo("");
			
			poloRequest.setSubdistrictKatCode(requestTaskPolo.getSubzipcode_survey());
			poloRequest.setContractNo(requestTaskPolo.getNoKontrak());
			/*poloRequest.setDataSource(requestTaskPolo.getSumberData());*/
			poloRequest.setNoteTelesales(requestTaskPolo.getNoteTelesales());
			
			poloRequest.setStatusMss(GlobalVal.LOV_CODE_TIDAK.equalsIgnoreCase(answInter) ? "Not Interest" : "Interest");
		
			poloRequest.setSupervisor(saveResult.getPoloSpv());
			poloRequest.setVisitDate(saveResult.getPoloVisitDate());
			poloRequest.setInputDate(saveResult.getPoloInputDate());
			poloRequest.setReferantorCode(saveResult.getPoloRefCode());
			poloRequest.setReferantorName(saveResult.getPoloRefName());
			
			Map<String, String> surveyorDetail = this.getSurveyorDetailForUpdatePolo(trTaskH.getAmMsuser());
			poloRequest.setSurveyorName(surveyorDetail.get("svyName"));
			poloRequest.setSurveyorCode(surveyorDetail.get("svyCode"));
			poloRequest.setSurveyorEmpPosition(surveyorDetail.get("svyEmpPosition"));
			poloRequest.setSurveyorSpv(surveyorDetail.get("svySpv"));
			poloRequest.setSurveyorSpvCode(surveyorDetail.get("svySpvCode"));
			poloRequest.setSurveyorSpvEmpPosition(surveyorDetail.get("svySpvEmpPosition"));
			String prospectStat = this.retrieveResultTypeGlobal(String.valueOf(trTaskH.getUuidTaskH()), trTaskH.getMsForm().getFormName(), answPros, answInter, "0");
			poloRequest.setProspectStat(prospectStat);
			
			/*Tambahan kirim notes task visit*/
			String notesTaskVisit = StringUtils.EMPTY;
			MsQuestion questionNotesVisit = commonLogic.retrieveQuestionByRefId(GlobalVal.REF_STV_NTS_VST, auditContext);
			
			if (questionNotesVisit!=null) {
				Object [][] prmNtsVst = { {"uuidTaskH", trTaskH.getUuidTaskH()}, {"questionId", questionNotesVisit.getUuidQuestion()} };
				StringBuilder strBuilderNtsVst = new StringBuilder();
				strBuilderNtsVst.append("SELECT TTD.TEXT_ANSWER ");
				strBuilderNtsVst.append("FROM   TR_TASK_D TTD WITH (NOLOCK) ");
				strBuilderNtsVst.append("WHERE  TTD.UUID_QUESTION = :questionId AND TTD.UUID_TASK_H = :uuidTaskH");
				notesTaskVisit = (String) this.getManagerDAO().selectOneNativeString(strBuilderNtsVst.toString(), prmNtsVst);
				poloRequest.setNotesTaskVisit(notesTaskVisit);
			}
			
			/*Penambahan Third Party*/
			
			String poloRefCode = saveResult.getPoloRefCode();
			String poloRefName = saveResult.getPoloRefName();
			String poloRef2Code = saveResult.getPoloRef2Code();
			String poloRef2Name = saveResult.getPoloRef2Name();
			String poloRef3Code = saveResult.getPoloRef3Code();
			String poloRef3Name = saveResult.getPoloRef3Name();
			boolean isCanRevisit = true;
			
			if(GlobalVal.SOURCE_DATA_THIRDPARTY.equalsIgnoreCase(trTaskH.getSourceData()) && GlobalVal.LOV_CODE_YA.equalsIgnoreCase(answPros)) {
				if (StringUtils.isNotBlank(trTaskH.getAmMsuser().getUniqueId())) {
					GetReferantorResponse referantorDetail = intFormLogic.getReferantorCodeFromNc(trTaskH.getAmMsuser().getUniqueId(), auditContext);
					if (referantorDetail != null) {
						poloRefCode = StringUtils.isNotBlank(referantorDetail.getReferantorNo()) ? referantorDetail.getReferantorNo() : StringUtils.EMPTY;
						poloRefName = StringUtils.isNotBlank(referantorDetail.getReferantorName()) ? referantorDetail.getReferantorName() : StringUtils.EMPTY;
					}
				} 
				poloRef2Code = saveResult.getPoloRefCode();
				poloRef2Name = saveResult.getPoloRefName();
				poloRef3Code = saveResult.getPoloRef2Code();
				poloRef3Name = saveResult.getPoloRef2Name();				
			}
			
			
			poloRequest.setNegativeCustomer(saveResult.getPoloNegListCust());
			poloRequest.setNegativeSpouse(saveResult.getPoloNegListPsgn());
			poloRequest.setNegativeGuarantor(saveResult.getPoloNegListGrtr());
			poloRequest.setDukcapilResult(saveResult.getPoloDkcpCust());
			poloRequest.setSpouseDukcapilResult(saveResult.getPoloDkcpPsgn());
			poloRequest.setDukcapilGuarantorResult(saveResult.getPoloDkcpGrtr());
			poloRequest.setReferantorCode(poloRefCode);
			poloRequest.setReferantorName(poloRefName);
			poloRequest.setReferantorCode1(poloRef2Code);
			poloRequest.setReferantorName1(poloRef2Name);
			poloRequest.setReferantorCode2(poloRef3Code);
			poloRequest.setReferantorName2(poloRef3Name);
			poloRequest.setNIK(saveResult.getNik());
			poloRequest.setCustName(saveResult.getCustName());
			poloRequest.setPhone1(saveResult.getPhone());
			poloRequest.setBirthplace(saveResult.getBirthPlace());
			poloRequest.setBirthDt(saveResult.getBirthDate());
			poloRequest.setMotherName(saveResult.getMotherName());
			poloRequest.setResidenceAddr(saveResult.getAddress());
			poloRequest.setResidenceRt(saveResult.getRt());
			poloRequest.setResidenceRw(saveResult.getRw());
			poloRequest.setResidenceProvinsi(saveResult.getProvince());
			poloRequest.setResidenceCity(saveResult.getCity());
			poloRequest.setResidenceKecamatan(saveResult.getKecamatan());
			poloRequest.setResidenceKelurahan(saveResult.getKelurahan());
			poloRequest.setResidenceZipcode(saveResult.getZipCode());
			poloRequest.setResidenceSubZipcode(saveResult.getSubZipCode());
			poloRequest.setProductCategory(saveResult.getProdCat());
			poloRequest.setProdOfferingCode(saveResult.getProdOff());
			poloRequest.setOfficeRegionName(saveResult.getRegionName());
			poloRequest.setOfficeCode(saveResult.getOfficeCode());
			poloRequest.setCustomerId(saveResult.getCustId());
			poloRequest.setContractNo(saveResult.getContractNo());
			poloRequest.setSOA(saveResult.getSoa());
			
			poloRequest.setLob(productCategoryCode);
			
			if((GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST_NOTMATCH.equalsIgnoreCase(saveResult.getPoloNegListCust()) 
					&& GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL_MATCH.equalsIgnoreCase(saveResult.getPoloDkcpCust())) 
					|| (StringUtils.isBlank(saveResult.getPoloNegListCust()) || StringUtils.isBlank(saveResult.getPoloDkcpCust())) && !rejected) {
				if (GlobalVal.LOV_CODE_YA.equalsIgnoreCase(answPros)) {
					// Prospek YA
					taskServiceLogic.createTaskAutoAssignSurvey(trTaskH,  
							poloRefCode,
							poloRefName,
							poloRef2Code,
							poloRef2Name,
							saveResult.getPoloVisitDate(),
							prospectStat,
							saveResult.getPoloNotesTaskVisit(),
							saveResult.getPoloNotesCrm(),
							auditContext);
					poloRequest.setFlagVoidSla("F");
					poloRequest.setStatusMss("Prospek");
					poloRequest.setStatusTaskMss(released);
					poloRequest.setIsContinueTask("0");
					intFormLogic.updateDataPolo(poloRequest, null, null, null, null, null, null, null, null, auditContext);
					
					if(GlobalVal.SOURCE_DATA_THIRDPARTY.equalsIgnoreCase(trTaskH.getSourceData())) {
						//tambah logic update referantor
						try {
							this.updateReferantorTaskPolo(trTaskH, auditContext);
						} catch (Exception e) {
							LOG.error("Logging error Updating Referantor saat Submit task: ", e);
						} 
						
					}
					
				} else {
					// Prospek TIDAK
					if (GlobalVal.LOV_CODE_YA.equalsIgnoreCase(answInter)) {
						// Interest YA
						if (isInSlaHandling(trTaskH, auditContext)) {
							// IN SLA AND INTEREST -> create task visit
							
							if (canRecreateTaskVisit(trTaskH.getUuidTaskH(), auditContext)) {
								createTaskVisitForInterest (trTaskH, taskDBean, msQuestions, saveResult, auditContext);
								poloRequest.setFlagVoidSla("F");
								poloRequest.setStatusTaskMss(released);
								poloRequest.setIsContinueTask("0");
								poloRequest.setFlagRevisit("T");
								intFormLogic.updateDataPolo(poloRequest, null, null, null, null, null, null, null, null, auditContext);
							} else {
								isCanRevisit = false;
								poloRequest.setFlagVoidSla("F");
								poloRequest.setStatusTaskMss(deleted);
								poloRequest.setIsContinueTask("1");
								poloRequest.setFlagRevisit("F");
								poloRequest.setReasonDelete("GAGAL PROSPECT VISIT");				
								intFormLogic.updateDataPolo(poloRequest, null, null, null, null, null, null, null, null, auditContext);
							}
						} else {
							// HIT POLO UPDATE DATA TO PRIORITY VOID SLA
							poloRequest.setFlagVoidSla("T");
							poloRequest.setReferantorCode("");
							poloRequest.setReferantorName("");
							poloRequest.setStatusMss("DATA UNHANDLED");
							poloRequest.setStatusTaskMss(deleted);
							poloRequest.setIsContinueTask("1");
							poloRequest.setReasonDelete("VOID SLA HANDLING");
							intFormLogic.updateDataPolo(poloRequest, null, null, null, null, null, null, null, null, auditContext);
						}
					} else {
							// Interest TIDAK -> HIT POLO UPDATE DATA TO PRIORITY NOT INTEREST, delete task
							isCanRevisit = false;
							poloRequest.setFlagVoidSla("F");
							
							if(!GlobalVal.SOURCE_DATA_THIRDPARTY.equalsIgnoreCase(trTaskH.getSourceData())) {
								poloRequest.setReferantorCode("");
								poloRequest.setReferantorName("");
							}
							poloRequest.setStatusTaskMss(deleted);
							poloRequest.setReasonDelete("GAGAL PROSPECT VISIT");
							poloRequest.setIsContinueTask("1");
							intFormLogic.updateDataPolo(poloRequest, null, null, null, null, null, null, null, null, auditContext);
					}
				}
			}  else if(GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST_MATCH.equalsIgnoreCase(saveResult.getPoloNegListCust()) 
					|| GlobalVal.TASK_VISIT_PROCESS_DUKCAPIL_NOTMATCH.equalsIgnoreCase(saveResult.getPoloDkcpCust()) && !rejected) {
				
				if(GlobalVal.TASK_VISIT_PROCESS_NEGATIVE_CUST_MATCH.equalsIgnoreCase(saveResult.getPoloNegListCust())) {
					// Negative cust submit to wise
					SubmitNegativeCustPoloRequest negPoloReq = new SubmitNegativeCustPoloRequest();
					negPoloReq.setCustNo(trTaskH.getCustomerIdPolo());
					negPoloReq.setNotes("");
					intFormLogic.submitNegativeCustomerPoloToWise(negPoloReq);					
				}
				// update to POLO
				if(isInSlaHandling(trTaskH, auditContext)) {
					poloRequest.setFlagVoidSla("F");	
				} else {
					poloRequest.setFlagVoidSla("T");
				}
				
				if (GlobalVal.LOV_CODE_YA.equalsIgnoreCase(answPros)) {
					poloRequest.setStatusMss("Prospek");
				}
				
				poloRequest.setReferantorCode("");
				poloRequest.setReferantorName("");
				poloRequest.setStatusTaskMss(deleted);
				poloRequest.setReasonDelete("FATAL SCORE");
				poloRequest.setIsContinueTask("1");
				intFormLogic.updateDataPolo(poloRequest, null, null, null, null, null, null, null, null, auditContext);
			} 
				
			if ("T".equals(poloRequest.getFlagVoidSla()) 
				|| "T".equals(poloRequest.getNegativeCustomer())
				|| "Not Interest".equals(poloRequest.getStatusMss())
				|| !isCanRevisit) {
				// update deleted if void sla OR negative cust OR not interest
				Object[][] paramsSub = {{ Restrictions.eq("subsystemName", GlobalVal.SUBSYSTEM_MS) }};
				AmMssubsystem subs = this.getManagerDAO().selectOne(AmMssubsystem.class, paramsSub);

				Object[][] paramsST = {
						{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DELETED) },
						{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", subs.getUuidMsSubsystem()) } };
				MsStatustask stts = this.getManagerDAO().selectOne(MsStatustask.class, paramsST);
				
				Object[][] prmMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_DELETED) } };
				MsStatusmobile stsMsm = this.getManagerDAO().selectOne(MsStatusmobile.class, prmMsm);

				//UPDATE STATUS TASK DELETED
				trTaskH.setMsStatustask(stts);
				trTaskH.setMsStatusmobile(stsMsm);
				trTaskH.setDtmUpd(new Date());
				trTaskH.setUsrUpd(usr.getFullName());
				this.getManagerDAO().update(trTaskH);
				
				// INSERT INTO TR TASK HISTORY
				String notesDelete = this.messageSource.getMessage("businesslogic.notes.changestatus.del", null, 
									this.retrieveLocaleAudit(auditContext));
				
				TrTaskhistory trTaskHistory = new TrTaskhistory(
						trTaskH.getMsStatustask(), trTaskH,
						usr.getFullName(), new Date(),
						notesDelete, null,
						usr.getFullName(),
						GlobalVal.CODE_PROCESS_DELETED);
				this.getManagerDAO().insert(trTaskHistory);							
			}
		}
		
		String flagSource = trTaskH.getFlagSource();
		
		
		/*2022-03-15 Penambahan Flow Promise To Survey*/

		boolean isTaskCaePiloting = (GlobalVal.FORM_PROMISE_TO_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName())
				|| GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName())
				|| GlobalVal.FORM_GUARANTOR.equalsIgnoreCase(trTaskH.getMsForm().getFormName()));
		if (!rejected && isTaskCaePiloting && "1".equals(isFinal)) {
			processTaskPilotingCAE(trTaskH, subsystem, null,
					this.getFlagSendOfflineByTask(taskHBean, trTaskH.getMsForm().getFormName()),  auditContext);
		}
		
		
		/*2025-02-11 Penambahan Flow Promise To Visit*/
		if (!rejected && GlobalVal.FORM_PROMISE_TO_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName()) && "1".equals(isFinal)) {
			String promiseToVisitDate = saveResult.getPtvDate();
			this.doUpdateTaskPoloVisit(trTaskH, subsystem, promiseToVisitDate, auditContext);
		}
		
		
		
		//06-10-2015  SubmitTask To Interface NC
		if (!GlobalVal.SUBSYSTEM_MC.equals(subsystem.getSubsystemName()) && isValidateCmoRecommendation
				&& !isTaskCaePiloting 
				&& (GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(trTaskH.getMsStatustask().getStatusCode()) || GlobalVal.SURVEY_STATUS_TASK_RELEASED_ON_PENDING.equals(trTaskH.getMsStatustask().getStatusCode()) )
				&& !rejected) {
			this.getManagerDAO().fetch(trTaskH.getMsBranch());
			if ("1".equals(isFinal) && GlobalVal.PROCESS_CODE_MSCORE.equals(flagSource)) {
				if (GlobalVal.FORM_OTS.equals(trTaskH.getMsForm().getFormName())) {
					String flagSourceOts = getOtsFlagSource(String.valueOf(trTaskH.getUuidTaskH()), null != trTaskH.getTrTaskH());
					intFormLogic.submitTaskOTS(auditContext, flagSourceOts, trTaskH);
				} else {
					Object[][] paramStatSvy = { {Restrictions.eq("assetTagName", GlobalVal.ASSET_TAG_STATUS_SURVEY)} };
					MsAssettag assetStatSvy = this.getManagerDAO().selectOne(MsAssettag.class, paramStatSvy);
	
					Object[][] paramTaskDProd = { {"uuidTaskH", trTaskH.getUuidTaskH()}, {"uuidAssetTag", assetStatSvy.getUuidAssetTag()} };
					String answ = (String) this.getManagerDAO().selectOneNative("services.common.task.getAnswerCodeByAssetTag", paramTaskDProd);
					
					if ("LVL2".equalsIgnoreCase(answ)) {
						intFormLogic.insertLossDeal(auditContext, curTaskId, isFinal);
					} else if ("LVL3".equalsIgnoreCase(answ)) {
						intFormLogic.submitNegativeList(auditContext, curTaskId, isFinal, "0");
					} else {
						if (!GlobalVal.FORM_VISIT_POLO.equals(trTaskH.getMsForm().getFormName()) && !GlobalVal.FORM_PROMISE_TO_VISIT_POLO.equals(trTaskH.getMsForm().getFormName())) {
							intFormLogic.submitNap(auditContext, taskId, isFinal);
						}
					}
				}
			} else if ("1".equals(isFinal) && GlobalVal.PROCESS_CODE_MSCOREP.equals(flagSource)) {
				Object[][] paramStatus = {{"statusCode", GlobalVal.SURVEY_STATUS_TASK_RELEASED}, {"subsystem", GlobalVal.SUBSYSTEM_MS}};
				BigInteger uuidStatusReleased = (BigInteger)this.getManagerDAO().selectOneNativeString("SELECT UUID_STATUS_TASK FROM MS_STATUSTASK MST WITH(NOLOCK) "
						+ "JOIN AM_MSSUBSYSTEM AMS WITH(NOLOCK) ON AMS.UUID_MS_SUBSYSTEM = MST.UUID_MS_SUBSYSTEM "
						+ "WHERE MST.STATUS_CODE = :statusCode AND AMS.SUBSYSTEM_NAME = :subsystem", paramStatus);
				
				Object[][] paramsTaskFoto = { {Restrictions.eq(paramApplNo, trTaskH.getApplNo())},
						{Restrictions.eq("flagSource", GlobalVal.PROCESS_CODE_MSCOREF)}, 
						{Restrictions.eq("msStatustask.uuidStatusTask", uuidStatusReleased.longValueExact())} };
				TrTaskH trTaskHFoto = this.getManagerDAO().selectOne(TrTaskH.class, paramsTaskFoto);
				if (null != trTaskHFoto) {
					//get status from workflow
					long uuidProcess = this.getUuidProcess(trTaskH, subsystem);
					MsStatustask msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
					//INSERT INTO CHECK HISTORY SURVEY
					String codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
					String notes = notesSubmit;
					insertTaskHistory(auditContext, msStatusTask, trTaskH, notes, codeProcess, usr.getFullName(), usr, null);
					if (!GlobalVal.FORM_VISIT_POLO.equals(trTaskH.getMsForm().getFormName()) && !GlobalVal.FORM_PROMISE_TO_VISIT_POLO.equals(trTaskH.getMsForm().getFormName())) {
						if ("1".equals(trTaskH.getIsPilotingCae())) {
							intFormLogic.submitPolo(taskDBean, trTaskH, GlobalVal.FLAG_TASK_SURVEY_PILOTING, null, auditContext);
						} else {
							intFormLogic.submitNap(auditContext, taskId, isFinal);
						}
					}
				}
			} else if ("1".equals(isFinal) && GlobalVal.MSIAFOTO.equals(flagSource)) {
				if (!GlobalVal.FORM_VISIT_POLO.equals(trTaskH.getMsForm().getFormName()) && !GlobalVal.FORM_PROMISE_TO_VISIT_POLO.equals(trTaskH.getMsForm().getFormName())) {
					if ("1".equals(trTaskH.getIsPilotingCae())) {
						intFormLogic.submitPolo(taskDBean, trTaskH, GlobalVal.FLAG_TASK_COMPLETED, null, auditContext);
					} else {
						intFormLogic.submitNap(auditContext, taskId, isFinal);
					}
				}
			}
		}
		
		if (!GlobalVal.SUBSYSTEM_MC.equals(subsystem.getSubsystemName())){
//			intFormLogic.submitResult(auditContext, taskId, "1");
		}
		if (!GlobalVal.SUBSYSTEM_MO.equals(subsystem.getSubsystemName())) {
			if (GlobalVal.SUBSYSTEM_MS.equals(subsystem.getSubsystemName())) {
//				intFormLogic.saveResult(auditContext, taskId, flagSource, subsystem.getSubsystemName(), auditContext.getCallerId(), "1");
			}
			else if (GlobalVal.SUBSYSTEM_MC.equals(subsystem.getSubsystemName())){
				//cek apakah pakai RV di depan atau belakang (1=di depan, 0=di belakang), 
				String gsValue = globalLogic.getGsValue(GlobalKey.GENERALSETTING_FLAG_RV, auditContext);
				if ("1".equals(gsValue)) {
					//jika RV di depan, maka langsung submit ke core
//					intFormLogic.saveResult(auditContext, taskId, flagSource, subsystem.getSubsystemName(), auditContext.getCallerId(), "1");
				}
				else {
					//jika RV di belakang, maka perlu cek apakah ada jumlah uang yang berhasil ditagih
					//kalau tidak ada, maka submit ke core. kalau ada, setelah print atau input RV baru kirim ke core
					if ("0".equals(totalBayar)) {
//						intFormLogic.saveResult(auditContext, taskId, flagSource, subsystem.getSubsystemName(), auditContext.getCallerId(), "1");
					}
				}
			}
		}
		
		//TODO update dummy for prototopye
//		if( "Survey".equals(trTaskH.getMsForm().getMsFormcategory().getCategoryDesc()) &&
//				GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(trTaskH.getMsStatustask().getStatusCode())){
//			String dummyApplNo = "APP"+DateFormatUtils.format(new Date(), "yyMMdd")+StringUtils.right(trTaskH.getTaskId(), 3);
//			trTaskH.setApplNo(dummyApplNo);
//			trTaskH.setPromiseDate(trTaskH.getReadDate());
//			this.getManagerDAO().update(trTaskH);
//			
//			Object[][] paramGroupTask = { {"uuidTaskH", trTaskH.getUuidTaskH()} };
//			String grpTaskId = (String) this.getManagerDAO().selectOneNativeString("select top 1 (GROUP_TASK_ID) from MS_GROUPTASK where UUID_TASK_H = :uuidTaskH", paramGroupTask);
//			Object[][] objGrpTask = { {Restrictions.eq("groupTaskId", grpTaskId)} };
//			Map<String, Object> mpGrpTask = this.getManagerDAO().selectAll(MsGrouptask.class, objGrpTask, null);
//			List<MsGrouptask> listGrpTask = (List<MsGrouptask>) mpGrpTask.get(GlobalKey.MAP_RESULT_LIST);
//			for (MsGrouptask msg : listGrpTask) {
//				msg.setApplNo(dummyApplNo);
//				TrTaskH grpTaskH = msg.getTrTaskH();
//				if (trTaskH.getUuidTaskH()!=grpTaskH.getUuidTaskH()){
//					grpTaskH.setApplNo(dummyApplNo);
//					this.getManagerDAO().update(grpTaskH);
//				}
//			}
//		}
		LOG.info("End of submit task {}: {}", taskHBean.getUuid_task_h(), new DateTime().minusMillis(startDateTime.getMillisOfDay()).getMillisOfDay());
		
		SubmitTaskResultBean resultBean = new SubmitTaskResultBean(taskId, uuid);
		if(!isValidateCmoRecommendation) {
			resultBean.setDropTask(true);
			resultBean.setMessage(notesDel);
		}
		
		return new SubmitTaskResultBean(taskId, uuid);
	}
    
    private void insertTblAgreementHistory(TrTaskH trTaskH, SaveTaskDResult saveResult, String email) {
		TblAgreementHistory tblAgreementHistory = new TblAgreementHistory();
		tblAgreementHistory.setCustNo(trTaskH.getCustNo());
		tblAgreementHistory.setName(trTaskH.getCustomerName());
		tblAgreementHistory.setIdNo(trTaskH.getNik());
		tblAgreementHistory.setBirthPlace(saveResult.getBirthPlace());
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		Date birthDate;
		try {
			birthDate = formatter.parse(saveResult.getBirthDate());
			tblAgreementHistory.setBirthDt(birthDate);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		tblAgreementHistory.setEmailAddress(email);
		tblAgreementHistory.setPhoneNumber(trTaskH.getCustomerPhone());
		tblAgreementHistory.setType("Customer");
		tblAgreementHistory.setTaskIdPolo(trTaskH.getTaskIdPolo());
		tblAgreementHistory.setOrderNo(trTaskH.getOrderId());
		tblAgreementHistory.setAppNo(trTaskH.getApplNo());
		String source = StringUtils.EMPTY;
		if (trTaskH.getMsForm().getFormName().contains("Text")) {
			source = "Task Text";
		} else if (trTaskH.getMsForm().getFormName().contains("Completed")) {
			source = "Task Completed";
		}
		tblAgreementHistory.setSource(source);
		tblAgreementHistory.setDtmCrt(new Date());
		
        this.getManagerDAO().insert(tblAgreementHistory);
    }
    
    private void updateReferantorTaskPolo(TrTaskH trTaskH, AuditContext auditContext) {
		
    	CheckReferantorResponse response = new CheckReferantorResponse();
		int maxRetry = 0;
		int count = 0;
		String taskId = String.valueOf(trTaskH.getUuidTaskH());
		this.getManagerDAO().fetch(trTaskH.getMsGrouptasks());
		MsGrouptask groupTask = trTaskH.getMsGrouptasks().iterator().next();
		
    	AmGeneralsetting amGeneralSetting = this.commonLogic.retrieveGs(GlobalVal.GENERAL_SETTING_MAX_RETRY_VALIDATION_CHECK, auditContext);
		if (amGeneralSetting!=null) {
			maxRetry = Integer.parseInt(amGeneralSetting.getGsValue());
		}
		
		while(count < maxRetry){
			try {
				response = taskServiceLogic.checkReferantor(taskId, null, null, auditContext);            
				if(0 == response.getStatus().getCode()) {
					break;
				}
			} catch (Exception e) {
				LOG.error("Logging error Check Referantor saat Submit task: ", e);
			}
			
			count++;
		}
		
		if (response == null || response.getStatus().getCode() != 0) return;

		Map <String, String> mapResult = response.getMapResult();
	    if (mapResult == null || mapResult.isEmpty()) return;
	    
		Object[][] paramsTaskH = new Object[][] {{Restrictions.eq("groupTaskId", Long.valueOf(groupTask.getGroupTaskId()))}};
		List<MsGrouptask> listGroupTask = (List<MsGrouptask>) this.getManagerDAO().selectAll(MsGrouptask.class, paramsTaskH, null).get(GlobalKey.MAP_RESULT_LIST);
	    
	    for (Map.Entry<String, String> entry : mapResult.entrySet()) {
	    	String key = entry.getKey();
	    	String value = entry.getValue();
	    	
	    	if(StringUtils.isNotBlank(value)) { 
	    		MsQuestion msQuestion = this.commonLogic.retrieveQuestionByRefId(key, auditContext);
	    		
	    		for(MsGrouptask groupTaskBean : listGroupTask) {
	    			TrTaskH taskHBean = groupTaskBean.getTrTaskH();
	    			String statusTask = taskHBean.getMsStatustask().getStatusCode();
	    			if(GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION.equalsIgnoreCase(statusTask)
	    					|| GlobalVal.SURVEY_STATUS_TASK_WAITING_ON_PENDING.equalsIgnoreCase(statusTask)) {
	    				try {
	    					this.insertTaskDReferantor(taskHBean, msQuestion, value, null, auditContext);											
						} catch (Exception e) {
							LOG.error("error Updating Referantor, RefId: {} ", msQuestion.getRefId(), e);
						}
	    				
	    				if(GlobalVal.REFERANTOR3_CODE_CAE.equalsIgnoreCase(msQuestion.getRefId())) {
	    					MsQuestion msQuestionAdaRef = this.commonLogic.retrieveQuestionByRefId(GlobalVal.ADAREF3_CODE_CAE, auditContext);
	    					try {
	    						this.insertTaskDReferantor(taskHBean, msQuestionAdaRef, null, GlobalVal.LOV_CODE_YA, auditContext);
	    					} catch (Exception e) {
	    						LOG.error("error Updating Referantor, RefId: {} ", msQuestionAdaRef.getRefId(), e);
	    					}
	    				}
	    				
	    			}
	    		}
	    		
	    	}
	    }
	    
	}
    
    private void insertTaskDReferantor(TrTaskH taskHBean, MsQuestion msQuestion, String answer, String optionAnswerId, AuditContext auditContext) {
		
    	Object[][] params = { { Restrictions.eq("trTaskH.uuidTaskH", taskHBean.getUuidTaskH()) },
				{ Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion()) } };
		TrTaskD taskD = this.getManagerDAO().selectOne(TrTaskD.class, params);
		MsLov msLOV = null;
		String optionAnswerGroupId = msQuestion.getLovGroup();
		
		if (StringUtils.isNotBlank(optionAnswerGroupId) && StringUtils.isNotBlank(optionAnswerId)) {
			Object[][] paramsLov = { { Restrictions.eq("lovGroup", optionAnswerGroupId) },
					{ Restrictions.eq("code", optionAnswerId) }, { Restrictions.eq("isActive", "1") } };
			msLOV = this.getManagerDAO().selectOne(MsLov.class, paramsLov);
		}
		
		if(null != taskD) {
			taskD.setDtmUpd(new Date());
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setIntTextAnswer(answer); 
			if (null!=msLOV) { 
				taskD.setMsLovByIntLovId(msLOV);
				taskD.setIntOptionText(msLOV.getCode());
			} 
			this.getManagerDAO().update(taskD);
		}else {
			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			trTaskD.setTrTaskH(taskHBean);
			trTaskD.setMsQuestion(msQuestion);
			trTaskD.setQuestionText(msQuestion.getQuestionLabel());
			trTaskD.setIntTextAnswer(answer); 
			if (null!=msLOV) { 
				trTaskD.setMsLovByIntLovId(msLOV);
				trTaskD.setIntOptionText(msLOV.getCode());
			}
			trTaskD.setIsReadonly("1");
			this.getManagerDAO().insert(trTaskD);
		}	
    }

	private SubmitLayerRequest buildRequestForSubmitLayer(SubmitTaskHBean taskH,
    		Map<String, Map<String, String>> filter) {
    	SubmitLayerRequest request = new SubmitLayerRequest();
    	request.setTaskH(taskH);
    	request.setFilter(filter);
    	Object[][] prmMapSubmitLayer = { {"uuidForm", taskH.getUuid_scheme()} };
    	List <Map<String, Object>> listOfLayer = this.getManagerDAO().selectAllNativeString(
    			"SELECT    UUID_QUESTION_GROUP, UUID_QUESTION_RESULT " + 
    			"FROM      MS_MAPPINGSUBMITLAYER WITH (NOLOCK) " + 
    			"WHERE     UUID_FORM = :uuidForm AND UUID_QUESTION_GROUP IS NOT NULL " + 
    			"GROUP BY  UUID_QUESTION_GROUP, UUID_FORM, PROCESS_SEQ_OF_FORM, UUID_QUESTION_RESULT " + 
    			"ORDER BY  PROCESS_SEQ_OF_FORM", prmMapSubmitLayer);
    	
    	List<SubmitLayerQuestionGroupBean> listQGroupBean = new ArrayList<>();
    	for (int i = 0; i < listOfLayer.size(); i++) {
    		SubmitLayerQuestionGroupBean bean = new SubmitLayerQuestionGroupBean();
    		Map<String, Object> mapOfLayer = listOfLayer.get(i);
    		String uuidQGroup = mapOfLayer.get("d0").toString();
    		bean.setUuidQuestionGroup(uuidQGroup);
    		String uuidQuestionResult = null != mapOfLayer.get("d1") ? String.valueOf(mapOfLayer.get("d1")) : null;
    		List<SubmitTaskDBean> listOfTaskD = new ArrayList<>();
    		if (StringUtils.isNotBlank(uuidQuestionResult)) {
    			SubmitTaskDBean taskDBeanResult = new SubmitTaskDBean();
    			Object answerQuestionResult = this.getManagerDAO().selectOneNativeString(
    					"SELECT TEXT_ANSWER FROM TR_TASK_D WITH (NOLOCK) WHERE UUID_QUESTION = :uuidQuestionResult AND UUID_TASK_H = :uuidTaskH",
    					new Object[][] { {"uuidQuestionResult", uuidQuestionResult}, {"uuidTaskH", taskH.getUuid_task_h()} });
    			if (null != answerQuestionResult) {
    				taskDBeanResult.setUuid_task_h(taskH.getUuid_task_h());
    				taskDBeanResult.setQuestion_group_id(uuidQGroup);
    				taskDBeanResult.setQuestion_id(uuidQuestionResult);
    				taskDBeanResult.setQuestion_id(uuidQuestionResult);
    				taskDBeanResult.setText_answer(String.valueOf(answerQuestionResult));
    				
    				listOfTaskD.add(taskDBeanResult);
    			}
    		}
    		
    		bean.setTaskD(listOfTaskD);
    		listQGroupBean.add(bean);
    	}
    	request.setQuestionGroup(listQGroupBean);
    	
    	return request;
    }
    
    @Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public SubmitTaskUpdateResultBean submitTaskUpdate(AuditContext auditContext, String taskUpdateId, String feedbackNotes, String uploadDocument) {		
    	TrTaskupdate taskUpdate = null;
    	Object[][] prm = {{Restrictions.eq("uuidTaskUpdate", Long.valueOf(taskUpdateId))}};
    	taskUpdate = this.getManagerDAO().selectOne(TrTaskupdate.class, prm);
    	
    	this.getManagerDAO().fetch(taskUpdate.getTrTaskH());
    	TrTaskH taskH = taskUpdate.getTrTaskH();
    	
    	taskUpdate.setIsPending("0");
    	taskUpdate.setFeedbackNotes(feedbackNotes);
    	if (StringUtils.isNotBlank(uploadDocument)) {
    		
			ImageStorageLocation saveImgLoc = this.imageStorageLogic.retrieveGsImageLocation(auditContext);

			if (ImageStorageLocation.DMS == saveImgLoc) {
				UploadImageRequestBean request = new UploadImageRequestBean();
				UploadImageBean uploadBean = new UploadImageBean();

				// Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
				Object[][] paramImgLoc = {
						{ Restrictions.eq(paramGsCode, GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE) } };
				AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
				Object[][] param = { { "uuidTaskH", taskH.getTaskId() } };
				Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString("SELECT	GROUP_TASK_ID "
						+ "FROM	MS_GROUPTASK with(nolock) " + "WHERE	UUID_TASK_H = :uuidTaskH ", param);
				String groupTaskIdForm = String.valueOf(groupTaskId).concat("_")
						.concat(String.valueOf(taskH.getMsForm().getUuidForm()));
				Date cutoffDate = null;
				Date taskDate = null;
				if (null != taskUpdate.getDtmUpd()) {
					taskDate = taskUpdate.getDtmUpd();
				} else {
					taskDate = new Date();
				}
				try {
					cutoffDate = new SimpleDateFormat(dateFormat).parse(gs.getGsValue());
				} catch (ParseException e1) {
					LOG.error(e1.getMessage(), e1);
					e1.printStackTrace();
				}
				if (taskDate.before(cutoffDate)) {
					uploadBean.setId(taskH.getTaskId());
					uploadBean.setTaskId(taskH.getTaskId());
				} else if (taskDate.after(cutoffDate)) {
					uploadBean.setId(groupTaskIdForm);
					uploadBean.setTaskId(groupTaskIdForm);
				}

				uploadBean.setType("survey");
				uploadBean.setJenisDoc("Document Task Update");
				uploadBean.setRefId("DOC_TASK_UPDATE_" + taskUpdate.getUuidTaskUpdate());
				uploadBean.setUsername(taskH.getAmMsuser().getUniqueId());
				if (StringUtils.isNotEmpty(uploadDocument)) {
					taskUpdate.setDocumentPath(String.valueOf(taskUpdate.getUuidTaskUpdate()));
					this.getManagerDAO().update(taskUpdate);
					request.setByteImage(BaseEncoding.base64().decode(uploadDocument));
				}
				request.setFileName(taskUpdate.getUuidTaskUpdate() + ".jpg");
				try {
					request.setIpAddress(InetAddress.getLocalHost().getHostAddress());
				} catch (UnknownHostException e) {
					LOG.error("Logging error DMS Host : {}", e.getMessage());
				}
				request.setUploadImageBean(uploadBean);
				if (StringUtils.isNotEmpty(uploadDocument)) {

					// PANGGIL KE INTNCFORMLOGIC
					intFormLogic.uploadImageResponse(auditContext, request);
					
				}
			} else {
				taskUpdate.setLobDocument(BaseEncoding.base64().decode(uploadDocument));
			}
		}
		else {
			taskUpdate.setLobDocument(null);
		}
    	
		intFormLogic.submitTaskUpdate(auditContext, taskUpdateId);
		
		intFormLogic.updateStatusWiseIDE(taskH, "Task Update", taskH.getAmMsuser(), null, auditContext);
		
		return new SubmitTaskUpdateResultBean(taskUpdateId);
	}
    
    private String changeDateFormat(String date, String dateformat) {
  		if (StringUtils.isBlank(date))
  			return "-";

  		DateFormat df = new SimpleDateFormat(dateFormat);

  		try {
  			Date result = df.parse(date);
  			return (null != result) ? new SimpleDateFormat(dateformat).format(result) : "-";
  		} catch (ParseException e) {
  			LOG.error("Error formatting date: {} with format {}", date, dateformat, e);
  		}

  		return "-";
  	}
    
    private void insertTaskSurveyData(TrTaskH trTaskH, String optionAnswerId, String userUpdateName){
		TrTasksurveydata taskSurveyData = null;
		Object[][] prm = {{Restrictions.eq("uuidTaskId", trTaskH.getUuidTaskH())}};
		taskSurveyData = this.getManagerDAO().selectOne(TrTasksurveydata.class, prm);
		
		if (taskSurveyData == null){
			taskSurveyData = new TrTasksurveydata();
			taskSurveyData.setTrTaskH(trTaskH);
	    	taskSurveyData.setUsrCrt(userUpdateName);
	    	taskSurveyData.setDtmCrt(new Date());
	    	taskSurveyData.setProductLovId(Long.valueOf(optionAnswerId));
	    	this.getManagerDAO().insert(taskSurveyData);
		} 
		else {
			taskSurveyData.setProductLovId(Long.valueOf(optionAnswerId));
			taskSurveyData.setUsrUpd(userUpdateName);
			taskSurveyData.setDtmUpd(new Date());
			this.getManagerDAO().update(taskSurveyData);
		}
	}
	
	private void insertTaskD(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String optionAnswerId,
			String questionText, String textAnswer, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Long idStagingAsset, String optionText, Map<Long, TrTaskD> listTaskD){
		DateTime startDateTime = new DateTime();
		LOG.info("Inserting task detail...");
		MsLov msLovByLovId =  null;
		TrTaskD taskD = null;
		if (!GlobalVal.ANSWER_TYPE_MULTIPLE.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			taskD = listTaskD.get(msQuestion.getUuidQuestion());
		}
		if (StringUtils.isNotBlank(optionAnswerId)){
			msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
		}
		if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())
				|| GlobalVal.ANSWER_TYPE_DATETIME.equals(msQuestion.getMsAnswertype().getCodeAnswerType())){
			if (textAnswer != null){
				DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
				try {
					Date result = df.parse(textAnswer);
					if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
						textAnswer = (null != result) 
								? new SimpleDateFormat(dateFormat).format(result)
								: null;
					} 
					else {
						textAnswer = (null != result) 
								? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result)
								: null;
					}
				} 
				catch (ParseException e) {
					LOG.error(e.getMessage(), e);
				} 
			}
		}
		if (null != taskD) {
			
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setDtmUpd(new Date());
			if (msLovByLovId != null) {
				taskD.setMsLovByLovId(msLovByLovId);
				taskD.setOptionText(msLovByLovId.getDescription());
			} else {
				taskD.setOptionText(optionText);
			}
			taskD.setQuestionText(questionText);
			taskD.setTextAnswer(textAnswer);
			taskD.setLatitude(checkEmptyBigdecimal(latitude));
			taskD.setLongitude(checkEmptyBigdecimal(longitude));
			if (taskD.getLatitude()!=null && taskD.getLongitude()!=null &&
					taskD.getLatitude().intValue() != 0 && taskD.getLongitude().intValue() != 0) {
				taskD.setIsGps("1");
			}
			taskD.setMcc(checkEmptyInteger(mcc));
			taskD.setMnc(checkEmptyInteger(mnc));
			taskD.setLac(checkEmptyInteger(lac));
			taskD.setCellId(checkEmptyInteger(cellId));
			taskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(taskD.getIsGps()) && taskD.getMcc() != null && taskD.getMnc() != null
					&& taskD.getLac() != null && taskD.getCellId() != null) {
				this.getLocationByCellId(taskD, auditContext);
			}
			taskD.setIdStagingAsset(idStagingAsset);
			this.getManagerDAO().update(taskD);
		} 
		else {				
			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			trTaskD.setTrTaskH(trTaskH);
			trTaskD.setMsQuestion(msQuestion);
			if (msLovByLovId != null) {
				trTaskD.setMsLovByLovId(msLovByLovId);
				trTaskD.setOptionText(msLovByLovId.getDescription());
			} else {
				trTaskD.setOptionText(optionText);
			}
			trTaskD.setQuestionText(questionText);
			
			if (textAnswer != null) {
				textAnswer = textAnswer.replace("\n","\\n");
			}
			
			trTaskD.setTextAnswer(textAnswer);
			trTaskD.setLatitude(checkEmptyBigdecimal(latitude));
			trTaskD.setLongitude(checkEmptyBigdecimal(longitude));
			if (trTaskD.getLatitude()!=null && trTaskD.getLongitude()!=null &&
					trTaskD.getLatitude().intValue() != 0 && trTaskD.getLongitude().intValue() != 0) {
				trTaskD.setIsGps("1");
			}
			trTaskD.setMcc(checkEmptyInteger(mcc));
			trTaskD.setMnc(checkEmptyInteger(mnc));
			trTaskD.setLac(checkEmptyInteger(lac));
			trTaskD.setCellId(checkEmptyInteger(cellId));
			trTaskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(trTaskD.getIsGps()) && trTaskD.getMcc() != null && trTaskD.getMnc() != null
					&& trTaskD.getLac() != null && trTaskD.getCellId() != null) {
				this.getLocationByCellId(trTaskD, auditContext);
			}
			trTaskD.setIdStagingAsset(idStagingAsset);
			this.getManagerDAO().insert(trTaskD);
			
		}
		LOG.info("End of insert task detail : {}", new DateTime().minusMillis(startDateTime.getMillisOfDay()).getMillisOfDay());
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private long insertDetailLob(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD,
			String questionText, String base64Image, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Boolean isGps, Boolean isConverted, ImageStorageLocation saveImgLoc, Path imgLoc){
		
		Object[][] params = { 
				{Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
				{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())} 
		};
		TrTaskdetaillob trTaskdetaillob = this.getManagerDAO().selectOne(TrTaskdetaillob.class, params);

		if (trTaskdetaillob == null) {
			trTaskdetaillob = new TrTaskdetaillob();
			trTaskdetaillob.setUsrCrt(auditContext.getCallerId());
			trTaskdetaillob.setDtmCrt(new Date());
		} 
		else {
			trTaskdetaillob.setUsrUpd(auditContext.getCallerId());
			trTaskdetaillob.setDtmUpd(new Date());
		}
		
		trTaskdetaillob.setTrTaskH(trTaskH);
		trTaskdetaillob.setMsQuestion(msQuestion);
		
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				trTaskdetaillob.setLobFile(BaseEncoding.base64().decode(base64Image));
			}
		}
		else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
				Date date = new Date();
				Path outputPath = Paths.get(imgLoc.toString(), dateFormat.format(date));

				String absoluteFilename = trTaskH.getTaskId() + "_" + msQuestion.getRefId() + "_"
				        + uuidTaskD + ".jpg";
				
				String outputFile = this.imageStorageLogic.storeImageFileSystem(
				        BaseEncoding.base64().decode(base64Image), outputPath, absoluteFilename);
				trTaskdetaillob.setImagePath(outputFile);
			}
		}
		trTaskdetaillob.setQuestionText(questionText);
		trTaskdetaillob.setLatitude(checkEmptyBigdecimal(latitude));
		trTaskdetaillob.setLongitude(checkEmptyBigdecimal(longitude));
		
		if (Boolean.TRUE.equals(isGps) || (trTaskdetaillob.getLatitude()!=null && trTaskdetaillob.getLongitude()!=null &&
				trTaskdetaillob.getLatitude().intValue()!=0 && trTaskdetaillob.getLongitude().intValue()!=0) &&
				!Boolean.TRUE.equals(isConverted))
			trTaskdetaillob.setIsGps("1");
		else if (Boolean.FALSE.equals(isGps))
			trTaskdetaillob.setIsGps("0");
		
		trTaskdetaillob.setMcc(checkEmptyInteger(mcc));
		trTaskdetaillob.setMnc(checkEmptyInteger(mnc));
		trTaskdetaillob.setLac(checkEmptyInteger(lac));
		trTaskdetaillob.setCellId(checkEmptyInteger(cellId));
		trTaskdetaillob.setAccuracy(checkEmptyInteger(accuracy));
		if (!Boolean.TRUE.equals(isConverted) && !"1".equals(trTaskdetaillob.getIsGps()) && trTaskdetaillob.getMcc() != null && trTaskdetaillob.getMnc() != null
				&& trTaskdetaillob.getLac() != null && trTaskdetaillob.getCellId() != null) {
			this.getLocationByCellId(trTaskdetaillob, auditContext);
		}
		else if (Boolean.TRUE.equals(isConverted)) {
			trTaskdetaillob.setIsConverted("1");
		}
		
		//penambahan save ke table TR_TASKSURVEYDATA
		if (msQuestion.getMsAssettag() != null) {
			Object paramsSurveyData[][] = { 
					{Restrictions.eq("uuidTaskId", trTaskH.getUuidTaskH())} 
			};
			TrTasksurveydata trTasksurveydata = this.getManagerDAO().selectOne(TrTasksurveydata.class, paramsSurveyData);
			TrTasksurveydata taskSurveyDataInsert = new TrTasksurveydata();
			if (trTasksurveydata == null) {
				taskSurveyDataInsert.setUsrCrt(auditContext.getCallerId());
				taskSurveyDataInsert.setDtmCrt(new Date());
				taskSurveyDataInsert.setTrTaskH(trTaskH);
				taskSurveyDataInsert.setUuidTaskId(trTaskH.getUuidTaskH());
			} else {
				taskSurveyDataInsert = trTasksurveydata;
				taskSurveyDataInsert.setUsrUpd(auditContext.getCallerId());
				taskSurveyDataInsert.setDtmUpd(new Date());
			}
			
			if (GlobalVal.ASSET_TAG_HOME.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setHomeLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setHomeLongitude(trTaskdetaillob.getLongitude());
			}
			else if (GlobalVal.ASSET_TAG_IDENTITY.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setLegalAddrLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setLegalAddrLongitude(trTaskdetaillob.getLongitude());
			}
			else if (GlobalVal.ASSET_TAG_OFFICE.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setOfficeLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setOfficeLongitude(trTaskdetaillob.getLongitude());
			}
			else if (GlobalVal.ASSET_TAG_STREET.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setDrivewayLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setDrivewayLongitude(trTaskdetaillob.getLongitude());
			}
			else if (GlobalVal.ASSET_TAG_VEHICLE.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setVehicleLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setVehicleLongitude(trTaskdetaillob.getLongitude());
			}
			
			if (trTasksurveydata == null) {
				this.getManagerDAO().insert(taskSurveyDataInsert);
			}
			else {
				this.getManagerDAO().update(taskSurveyDataInsert); //DAO will do saveOrUpdate
			}
		}	

		//end penambahan save ke table TR_TASKSURVEYDATA
		this.getManagerDAO().update(trTaskdetaillob); //DAO will do saveOrUpdate
		
		if (ImageStorageLocation.DMS == saveImgLoc) {
			UploadImageRequestBean request = new UploadImageRequestBean();
			UploadImageBean uploadBean  = new UploadImageBean();
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq(paramGsCode, GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Object [][] param = {{"uuidTaskH", trTaskH.getTaskId()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT	GROUP_TASK_ID " + 
					"FROM	MS_GROUPTASK with(nolock) " + 
					"WHERE	UUID_TASK_H = :uuidTaskH ", param);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(trTaskH.getMsForm().getUuidForm()));
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != trTaskH.getSubmitDate()) {
	        	taskDate = trTaskH.getSubmitDate();
	        } else {
	        	taskDate = new Date();
	        }
			try {
				cutoffDate = new SimpleDateFormat(dateFormat).parse(gs.getGsValue());
			} catch (ParseException e1) {
				LOG.error(e1.getMessage(), e1);
				e1.printStackTrace();
			}
			if(taskDate.before(cutoffDate)) {
				uploadBean.setId(trTaskH.getTaskId());
				uploadBean.setTaskId(trTaskH.getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				uploadBean.setId(groupTaskIdForm);
				uploadBean.setTaskId(groupTaskIdForm);
			}
			
			uploadBean.setType("survey");
			uploadBean.setJenisDoc(msQuestion.getQuestionLabel());
			uploadBean.setRefId(msQuestion.getRefId());
			uploadBean.setUsername(trTaskH.getAmMsuser().getUniqueId());
			if(StringUtils.isNotEmpty(base64Image)){
				String uuidtrTaskdetaillob = String.valueOf(trTaskdetaillob.getUuidTaskDetailLob());
				this.getManagerDAO().updateNativeString("UPDATE tt SET IMAGE_PATH = :imagePath, DTM_UPD = GETDATE() FROM TR_TASKDETAILLOB tt WITH(NOLOCK) WHERE UUID_TASK_DETAIL_LOB = :uuidlob", 
						new Object[][] {{"imagePath", uuidtrTaskdetaillob}, 
					{"uuidlob", trTaskdetaillob.getUuidTaskDetailLob()}});
				request.setByteImage(BaseEncoding.base64().decode(base64Image));
			}
			request.setFileName(trTaskdetaillob.getUuidTaskDetailLob()+".jpg");
			try {
				request.setIpAddress(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e) {
				LOG.error("Logging error DMS Host : {}",e.getMessage());
			}
			request.setUploadImageBean(uploadBean); 
			if(StringUtils.isNotEmpty(base64Image)){
				
				//PANGGIL KE INTNCFORMLOGIC
				intFormLogic.uploadImageResponse(auditContext,request);
				
			}
		}
		
		return trTaskdetaillob.getUuidTaskDetailLob();
	}
	
	public void createTaskSurvey(AuditContext auditContext, AmMsuser userSubmit, TrTaskH trTaskHOrder,
			AmMssubsystem subsystemMs, MsMappingformH msMapFormH, long groupTaskId) {
		Object queryParams[][] = { {Restrictions.eq("msMappingformH.uuidMappingFormH", msMapFormH.getUuidMappingFormH())} };
		MsMappingformD bean = this.getManagerDAO().selectOne(MsMappingformD.class, queryParams);		
		if (bean == null) {
			throw new SubmitTaskException(this.messageSource.getMessage(
					"businesslogic.submit.mappingnotexists", new Object[]{msMapFormH.getMappingName()}, this.retrieveLocaleAudit(auditContext)),
					Reason.NO_ORDER_SURVEY_MAPPING);
		}
		
		Object paramHist[][] = { {Restrictions.eq("trTaskH.uuidTaskH", trTaskHOrder.getUuidTaskH())} };
		Map<String, Object> mapHistory = this.getManagerDAO().selectAll(TrTaskhistory.class, paramHist, null);
		
		List<TrTaskhistory> listHistory = (List) mapHistory.get(GlobalKey.MAP_RESULT_LIST); 
		
		Type collectionType = new TypeToken<Collection<MappingFormBean>>(){}.getType();
		List<MappingFormBean> listFormQuestion = gson.fromJson(bean.getQuestionMapping(), collectionType);
		if (listFormQuestion == null || listFormQuestion.isEmpty()) {
			return;
		}
		
		SubmitTaskDBean[] taskDBeanOrder = null;
		boolean saveAsJson = PropertiesHelper.isTaskDJson();
		if (saveAsJson) {
			taskDBeanOrder = this.getSubmitTaskDBeanFromJson(trTaskHOrder);
		}
		else {
			taskDBeanOrder = taskServiceLogic.getSubmitTaskDBean(trTaskHOrder.getUuidTaskH()); //not use passed in argument.. (AITMSSPRD-239)
		}
			
		MsPriority normalPriority = this.commonLogic.retrieveMsPriority(GlobalVal.TAKS_PRIORTY_NORMAL, true, auditContext);
		MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
				trTaskHOrder.getMsForm().getUuidForm(), trTaskHOrder.getFormVersion(), auditContext);
		
		Map<Integer, MsQuestion> msQuestions = this.getAllMsQuestion(taskDBeanOrder, formHist, auditContext);
//		MsBranch msBranchMfSelected = this.getBranch(msQuestions, taskDBeanOrder);
//		String mhId = this.getMhId(msQuestions, taskDBeanOrder);
//		String ptsDate = this.getPtsDate(msQuestions, taskDBeanOrder);
//		String locationSurvey = this.getLocationSurvey(msQuestions, taskDBeanOrder);
		
//		BigDecimal latHeader = null;
//		BigDecimal longiHeader = null;
//		if (StringUtils.isNotEmpty(locationSurvey)){
//			latHeader = BigDecimal.valueOf(Double.valueOf(locationSurvey.split(";")[0]));
//			longiHeader = BigDecimal.valueOf(Double.valueOf(locationSurvey.split(";")[1]));
//		}
		
//		AmMsuser userMh = null;
//		if (mhId != null && !"".equals(mhId)) {
//			userMh = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(mhId));
//		}
		
		Map mapLatLong = getLatLongSubmitData(trTaskHOrder, auditContext);
		BigDecimal latOrder = (BigDecimal) mapLatLong.get("LATITUDE");
		BigDecimal longOrder = (BigDecimal) mapLatLong.get("LONGITUDE");
		
		List<TrTaskH> listTaskDist = new ArrayList<TrTaskH>();
		for (int j = 0; j < listFormQuestion.size(); j++) {
			MappingFormBean formQuestion = listFormQuestion.get(j);
			
			TrTaskH taskHSurvey = this.saveTaskHSurvey(userSubmit, trTaskHOrder, latOrder, longOrder,
					normalPriority, null, null, null, null, formQuestion, false, auditContext);
			
//			this.saveTaskLink(trTaskHOrder.getUuidTaskH(), taskHSurvey.getUuidTaskH(), auditContext);
			
			this.saveGroupTaskSurveyByExistingTaskid(taskHSurvey, groupTaskId, auditContext);

            ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(auditContext);
            Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
                    ? this.imageStorageLogic.retrieveGsImageFileSystemPath(auditContext) : null;
			
			//insert into tr_taskd and tr_taskdetaillob base on mapping to data default
			List<MappingFormBean> listQuestion = formQuestion.getListQuestionMapping();
			if (saveAsJson) {
				this.saveTaskDSurveyIntoJson(taskHSurvey, listQuestion, msQuestions, taskDBeanOrder, isl, imagePath, auditContext);
			}
			else {
				this.saveTaskDSurveyIntoRow(taskHSurvey, listQuestion, msQuestions, taskDBeanOrder, isl, imagePath, auditContext);
			}
			listTaskDist.add(taskHSurvey);

			long uuidProcess = this.getUuidProcess(taskHSurvey, subsystemMs);
			MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(taskHSurvey, uuidProcess, subsystemMs, 0);
			msStatustaskSurvey = commitWfAndUpdateTaskH(taskHSurvey, uuidProcess, subsystemMs, 0);
			
			if (!listHistory.isEmpty()) {
				for (TrTaskhistory prevHistBean : listHistory) {
					AmMsuser actor = new AmMsuser();
					actor.setFullName(prevHistBean.getActor());
					
					String prevNotes = prevHistBean.getNotes();
					if (GlobalVal.CODE_PROCESS_SUBMITTED.equals(prevHistBean.getCodeProcess())) {
						prevNotes = GlobalVal.NOTES_TASK_SUBMITTED_HIST + prevHistBean.getTrTaskH().getTaskId();
					}
					
					insertTaskHistory(auditContext, prevHistBean.getMsStatustask(), taskHSurvey, prevNotes, 
							prevHistBean.getCodeProcess(), prevHistBean.getFieldPerson(), actor, null);
				}
			}
			
			String notes = GlobalVal.NOTES_TASK_CREATED_HIST+trTaskHOrder.getTaskId();
			//INSERT INTO CHECK HISTORY
			insertTaskHistory(auditContext, msStatustaskSurvey, taskHSurvey, notes, 
					GlobalVal.CODE_PROCESS_ASSIGNMENT, userSubmit.getFullName(), userSubmit, null);
			
			//insert into tr_taskHistory
//			AmMsuser mos = new AmMsuser();
//			mos.setFullName("MOS");
//			this.insertTaskHistory(auditContext, msStatustaskSurvey, taskHSurvey, "MOS", GlobalVal.CODE_PROCESS_UNASSIGNED, null, mos, null);
		}
		
//		if (userSubmit == null || StringUtils.isNotBlank(ptsDate)) {
//			return;
//		}
				
		//cek task distribution berdasarkan subsystem
		//distribution berdasarkan type round robin, low task, dan zipcode
		
//		if ("1".equals(userSubmit.getMsJob().getIsSelfAssignment())) {
//			Object[][] prmUser={ {Restrictions.eq("loginId", userSubmit.getLoginId().substring(0, userSubmit.getLoginId().length()-1))} };
//			AmMsuser userCMS = this.getManagerDAO().selectOne(AmMsuser.class, prmUser);
//			Map<String, Object> result = taskDistributionLogic.distributeToCMS(listTaskDist, subsystemMs, userCMS, auditContext);
//			if (null != result) {
//				if (null != userCMS) {
//					TrTaskH taskbean = (TrTaskH) result.get("taskH");
//					commitOrder(userCMS, taskbean.getNotes(), taskbean, subsystemMs, 0, GlobalVal.CODE_PROCESS_ASSIGNMENT, auditContext);
//				}
//			}
//		}
//		else if ("1".equals(userSubmit.getMsJob().getIsAutomaticDistribution())) {
//			Object[][] prm={ {"uuidMsSubsystem", subsystemMs.getUuidMsSubsystem()} };
//			MsTskdistributionofmodule msTaskDistModul =  this.getManagerDAO().selectOne(
//					"from MsTskdistributionofmodule tdm join fetch tdm.msTaskdistribution where tdm.amMssubsystem.uuidMsSubsystem=:uuidMsSubsystem", prm);
//			if (msTaskDistModul == null ||
//					GlobalVal.MODULE_TASK_DISTRIBUTION_OFF.equals(msTaskDistModul.getMsTaskdistribution().getCode())) {
//				return;
//			}
//						
//			TrTaskorderdata trTaskOrderData = this.getManagerDAO().selectOne(TrTaskorderdata.class, trTaskHOrder.getUuidTaskH());
//			Long uuidMh = trTaskOrderData.getMhId();
//			if (uuidMh == null){
//				return;
//			}
//			
//			AmMsuser beanMh = this.getManagerDAO().selectOne(AmMsuser.class, uuidMh);			
//			String taskDistCode = msTaskDistModul.getMsTaskdistribution().getCode();
//					
//			Map<String, Object> result = null;
//			if (GlobalVal.MODULE_TASK_DISTRIBUTION_ROUND_ROBIN.equals(taskDistCode)){
//				result = taskDistributionLogic.distributeRoundRobin(listTaskDist, subsystemMs, String.valueOf(uuidMh), null, null, auditContext);
//			} 
//			else if (GlobalVal.MODULE_TASK_DISTRIBUTION_LOW_TASK.equals(taskDistCode)){
//				result = taskDistributionLogic.distributeLowTask(listTaskDist, subsystemMs, String.valueOf(uuidMh), null, null, auditContext);
//			} 
//			else if (GlobalVal.MODULE_TASK_DISTRIBUTION_ZIP_CODE.equals(taskDistCode)){
//				result = taskDistributionLogic.distributeZipCode(listTaskDist, subsystemMs, trTaskHOrder.getZipCode(), String.valueOf(uuidMh), null, null, auditContext);
//			} 
//			else if (GlobalVal.MODULE_TASK_DISTRIBUTION_GEOFENCING.equals(taskDistCode)){
//				result = taskDistributionLogic.distributeGeofencing(listTaskDist, subsystemMs, locationSurvey, String.valueOf(uuidMh), null, null, auditContext);
//			}
//			if (null != result && !result.isEmpty()) {
//				AmMsuser amMsuser = (AmMsuser) result.get("userAssign");
//				if (null != amMsuser) {
//					TrTaskH taskbean = (TrTaskH) result.get("taskH");
//					commitOrder(beanMh, taskbean.getNotes(), taskbean, subsystemMs, 0, GlobalVal.CODE_PROCESS_ASSIGNMENT, auditContext);
//				}
//			}			
//		}
	}
	
	public void createTaskSurveyPiloting(AuditContext auditContext, AmMsuser userSubmit, TrTaskH trTaskHOrder,
			AmMssubsystem subsystemMs, MsMappingformH msMapFormH, long groupTaskId) {
		Object[][] mapFormDParam = { {Restrictions.eq("msMappingformH.uuidMappingFormH", msMapFormH.getUuidMappingFormH())} };
		List<MsMappingformD> msMappingFormD = (List<MsMappingformD>) this.getManagerDAO().selectAll(
				MsMappingformD.class, mapFormDParam, null).get(GlobalKey.MAP_RESULT_LIST);		
		if (msMappingFormD == null) {
			throw new SubmitTaskException(this.messageSource.getMessage(
					"businesslogic.submit.mappingnotexists", new Object[]{msMapFormH.getMappingName()}, this.retrieveLocaleAudit(auditContext)),
					Reason.NO_ORDER_SURVEY_MAPPING);
		}
		
		Object paramHist[][] = { {Restrictions.eq("trTaskH.uuidTaskH", trTaskHOrder.getUuidTaskH())} };
		Map<String, Object> mapHistory = this.getManagerDAO().selectAll(TrTaskhistory.class, paramHist, null);
		
		List<TrTaskhistory> listHistory = (List) mapHistory.get(GlobalKey.MAP_RESULT_LIST);
		
		for (int x = 0; x < msMappingFormD.size(); x++) {
			MsMappingformD bean = msMappingFormD.get(x);
			Type collectionType = new TypeToken<Collection<MappingFormBean>>(){}.getType();
			List<MappingFormBean> listFormQuestion = gson.fromJson(bean.getQuestionMapping(), collectionType);
			if (listFormQuestion == null || listFormQuestion.isEmpty()) {
				return;
			}
			
			SubmitTaskDBean[] taskDBeanOrder = null;
			boolean saveAsJson = PropertiesHelper.isTaskDJson();
			if (saveAsJson) {
				taskDBeanOrder = this.getSubmitTaskDBeanFromJson(trTaskHOrder);
			}
			else {
				taskDBeanOrder = taskServiceLogic.getSubmitTaskDBean(trTaskHOrder.getUuidTaskH()); //not use passed in argument.. (AITMSSPRD-239)
			}
				
			MsPriority normalPriority = this.commonLogic.retrieveMsPriority(GlobalVal.TAKS_PRIORTY_NORMAL, true, auditContext);
			MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
					trTaskHOrder.getMsForm().getUuidForm(), trTaskHOrder.getFormVersion(), auditContext);
			
			Map<Integer, MsQuestion> msQuestions = this.getAllMsQuestion(taskDBeanOrder, formHist, auditContext);
//			MsBranch msBranchMfSelected = this.getBranch(msQuestions, taskDBeanOrder);
//			String mhId = this.getMhId(msQuestions, taskDBeanOrder);
//			String ptsDate = this.getPtsDate(msQuestions, taskDBeanOrder);
//			String locationSurvey = this.getLocationSurvey(msQuestions, taskDBeanOrder);
			
//			BigDecimal latHeader = null;
//			BigDecimal longiHeader = null;
//			if (StringUtils.isNotEmpty(locationSurvey)){
//				latHeader = BigDecimal.valueOf(Double.valueOf(locationSurvey.split(";")[0]));
//				longiHeader = BigDecimal.valueOf(Double.valueOf(locationSurvey.split(";")[1]));
//			}
			
//			AmMsuser userMh = null;
//			if (mhId != null && !"".equals(mhId)) {
//				userMh = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(mhId));
//			}
			
			Map mapLatLong = getLatLongSubmitData(trTaskHOrder, auditContext);
			BigDecimal latOrder = (BigDecimal) mapLatLong.get("LATITUDE");
			BigDecimal longOrder = (BigDecimal) mapLatLong.get("LONGITUDE");
			
			List<TrTaskH> listTaskDist = new ArrayList<TrTaskH>();
			for (int j = 0; j < listFormQuestion.size(); j++) {
				MappingFormBean formQuestion = listFormQuestion.get(j);
				
				TrTaskH taskHSurvey = this.saveTaskHSurvey(userSubmit, trTaskHOrder, latOrder, longOrder,
						normalPriority, null, null, null, null, formQuestion, false, auditContext);
				
//				this.saveTaskLink(trTaskHOrder.getUuidTaskH(), taskHSurvey.getUuidTaskH(), auditContext);
				
				this.saveGroupTaskSurveyByExistingTaskid(taskHSurvey, groupTaskId, auditContext);

	            ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(auditContext);
	            Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
	                    ? this.imageStorageLogic.retrieveGsImageFileSystemPath(auditContext) : null;
				
				//insert into tr_taskd and tr_taskdetaillob base on mapping to data default
				List<MappingFormBean> listQuestion = formQuestion.getListQuestionMapping();
				if (saveAsJson) {
					this.saveTaskDSurveyIntoJson(taskHSurvey, listQuestion, msQuestions, taskDBeanOrder, isl, imagePath, auditContext);
				}
				else {
					this.saveTaskDSurveyIntoRow(taskHSurvey, listQuestion, msQuestions, taskDBeanOrder, isl, imagePath, auditContext);
				}
				listTaskDist.add(taskHSurvey);

				long uuidProcess = this.getUuidProcess(taskHSurvey, subsystemMs);
				MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(taskHSurvey, uuidProcess, subsystemMs, 0);
				msStatustaskSurvey = commitWfAndUpdateTaskH(taskHSurvey, uuidProcess, subsystemMs, 0);
				
				if (!listHistory.isEmpty()) {
					for (TrTaskhistory prevHistBean : listHistory) {
						AmMsuser actor = new AmMsuser();
						actor.setFullName(prevHistBean.getActor());
						
						String prevNotes = prevHistBean.getNotes();
						if (GlobalVal.CODE_PROCESS_SUBMITTED.equals(prevHistBean.getCodeProcess())) {
							prevNotes = GlobalVal.NOTES_TASK_SUBMITTED_HIST + prevHistBean.getTrTaskH().getTaskId();
						}
						
						insertTaskHistory(auditContext, prevHistBean.getMsStatustask(), taskHSurvey, prevNotes, 
								prevHistBean.getCodeProcess(), prevHistBean.getFieldPerson(), actor, null);
					}
				}
				
				String notes = GlobalVal.NOTES_TASK_CREATED_HIST+trTaskHOrder.getTaskId();
				//INSERT INTO CHECK HISTORY
				insertTaskHistory(auditContext, msStatustaskSurvey, taskHSurvey, notes, 
						GlobalVal.CODE_PROCESS_ASSIGNMENT, userSubmit.getFullName(), userSubmit, null);
				
				//insert into tr_taskHistory
//				AmMsuser mos = new AmMsuser();
//				mos.setFullName("MOS");
//				this.insertTaskHistory(auditContext, msStatustaskSurvey, taskHSurvey, "MOS", GlobalVal.CODE_PROCESS_UNASSIGNED, null, mos, null);
			}
			
//			if (userSubmit == null || StringUtils.isNotBlank(ptsDate)) {
//				return;
//			}
					
			//cek task distribution berdasarkan subsystem
			//distribution berdasarkan type round robin, low task, dan zipcode
			
//			if ("1".equals(userSubmit.getMsJob().getIsSelfAssignment())) {
//				Object[][] prmUser={ {Restrictions.eq("loginId", userSubmit.getLoginId().substring(0, userSubmit.getLoginId().length()-1))} };
//				AmMsuser userCMS = this.getManagerDAO().selectOne(AmMsuser.class, prmUser);
//				Map<String, Object> result = taskDistributionLogic.distributeToCMS(listTaskDist, subsystemMs, userCMS, auditContext);
//				if (null != result) {
//					if (null != userCMS) {
//						TrTaskH taskbean = (TrTaskH) result.get("taskH");
//						commitOrder(userCMS, taskbean.getNotes(), taskbean, subsystemMs, 0, GlobalVal.CODE_PROCESS_ASSIGNMENT, auditContext);
//					}
//				}
//			}
//			else if ("1".equals(userSubmit.getMsJob().getIsAutomaticDistribution())) {
//				Object[][] prm={ {"uuidMsSubsystem", subsystemMs.getUuidMsSubsystem()} };
//				MsTskdistributionofmodule msTaskDistModul =  this.getManagerDAO().selectOne(
//						"from MsTskdistributionofmodule tdm join fetch tdm.msTaskdistribution where tdm.amMssubsystem.uuidMsSubsystem=:uuidMsSubsystem", prm);
//				if (msTaskDistModul == null ||
//						GlobalVal.MODULE_TASK_DISTRIBUTION_OFF.equals(msTaskDistModul.getMsTaskdistribution().getCode())) {
//					return;
//				}
//							
//				TrTaskorderdata trTaskOrderData = this.getManagerDAO().selectOne(TrTaskorderdata.class, trTaskHOrder.getUuidTaskH());
//				Long uuidMh = trTaskOrderData.getMhId();
//				if (uuidMh == null){
//					return;
//				}
//				
//				AmMsuser beanMh = this.getManagerDAO().selectOne(AmMsuser.class, uuidMh);			
//				String taskDistCode = msTaskDistModul.getMsTaskdistribution().getCode();
//						
//				Map<String, Object> result = null;
//				if (GlobalVal.MODULE_TASK_DISTRIBUTION_ROUND_ROBIN.equals(taskDistCode)){
//					result = taskDistributionLogic.distributeRoundRobin(listTaskDist, subsystemMs, String.valueOf(uuidMh), null, null, auditContext);
//				} 
//				else if (GlobalVal.MODULE_TASK_DISTRIBUTION_LOW_TASK.equals(taskDistCode)){
//					result = taskDistributionLogic.distributeLowTask(listTaskDist, subsystemMs, String.valueOf(uuidMh), null, null, auditContext);
//				} 
//				else if (GlobalVal.MODULE_TASK_DISTRIBUTION_ZIP_CODE.equals(taskDistCode)){
//					result = taskDistributionLogic.distributeZipCode(listTaskDist, subsystemMs, trTaskHOrder.getZipCode(), String.valueOf(uuidMh), null, null, auditContext);
//				} 
//				else if (GlobalVal.MODULE_TASK_DISTRIBUTION_GEOFENCING.equals(taskDistCode)){
//					result = taskDistributionLogic.distributeGeofencing(listTaskDist, subsystemMs, locationSurvey, String.valueOf(uuidMh), null, null, auditContext);
//				}
//				if (null != result && !result.isEmpty()) {
//					AmMsuser amMsuser = (AmMsuser) result.get("userAssign");
//					if (null != amMsuser) {
//						TrTaskH taskbean = (TrTaskH) result.get("taskH");
//						commitOrder(beanMh, taskbean.getNotes(), taskbean, subsystemMs, 0, GlobalVal.CODE_PROCESS_ASSIGNMENT, auditContext);
//					}
//				}			
//			}
		}
	}
	
	private SubmitTaskDBean[] getSubmitTaskDBeanFromJson(TrTaskH taskHOrder) {
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", taskHOrder.getUuidTaskH()}});
		if (docDb == null){
			return new SubmitTaskDBean[0];
		}
		
		String jsonDocument = docDb.getDocument();
		TaskDocumentBean tdb = gson.fromJson(jsonDocument, TaskDocumentBean.class);
		List<AnswerBean> answers = tdb.getAnswers();
		
		List<SubmitTaskDBean> taskDs = new ArrayList<SubmitTaskDBean>(answers.size());
		
		for (Iterator iterator = answers.iterator(); iterator.hasNext();) {
			AnswerBean answerBean = (AnswerBean) iterator.next();
			if (answerBean.getLobAnswer() != null) {
				continue;
			}
			
			SubmitTaskDBean taskD = new SubmitTaskDBean();
			taskD.setQuestion_id(String.valueOf(answerBean.getQuestion().getUuidQuestion()));
			taskD.setQuestion_label(answerBean.getQuestion().getLabel());
			taskD.setText_answer(StringUtils.stripToEmpty(answerBean.getTxtAnswer()));
			
			if (answerBean.getLocation() != null) {
				com.adins.mss.model.taskdjson.LocationBean location = answerBean.getLocation();
				if (location.getLat() != null) {
					taskD.setLatitude(location.getLat().toString());
				}
				if (location.getLng() != null) {
					taskD.setLongitude(location.getLng().toString());
				}
				if (location.getAccuracy() != null) {
					taskD.setAccuracy(location.getAccuracy().toString());
				}
				taskD.setMcc(String.valueOf(location.getMcc()));
				taskD.setMnc(String.valueOf(location.getMnc()));
				taskD.setLac(String.valueOf(location.getLac()));
				taskD.setCid(String.valueOf(location.getCid()));
			}
			
			if (answerBean.getOptAnswers() != null) {
				List<OptionBean> options = answerBean.getOptAnswers();
				
				int ctr = 0;
				for (Iterator iterator2 = options.iterator(); iterator2.hasNext();) {
					OptionBean optionBean = (OptionBean) iterator2.next();
					
					if (ctr > 0) {						
						try {
							SubmitTaskDBean clone = (SubmitTaskDBean) BeanUtils.cloneBean(taskD);
							clone.setOption_answer_id(String.valueOf(optionBean.getUuid()));
							taskDs.add(clone);
						} 
						catch (IllegalAccessException | InstantiationException | InvocationTargetException | NoSuchMethodException e) {
							LOG.error("Error on cloning taskD", e);
						}
					} 
					else {
						taskD.setOption_answer_id(String.valueOf(optionBean.getUuid()));
						taskDs.add(taskD);
					}
					
					ctr++;
				}
			}
			else {
				taskDs.add(taskD);
			}			
		}
		
		Object[][] paramsTask = {{Restrictions.eq("trTaskH.uuidTaskH", taskHOrder.getUuidTaskH())}};
		Map resultTaskDL = this.getManagerDAO().list(TrTaskdetaillob.class, paramsTask, null);
		List <TrTaskdetaillob> listTaskDL = (List) resultTaskDL.get(GlobalKey.MAP_RESULT_LIST);
		for (TrTaskdetaillob bean : listTaskDL) {
			SubmitTaskDBean temp = new SubmitTaskDBean();
			temp.setQuestion_id(String.valueOf(bean.getMsQuestion().getUuidQuestion()));
			temp.setQuestion_label(bean.getQuestionText());
			
			if (bean.getLatitude() != null) {
				temp.setLatitude(bean.getLatitude().toString());
			}
			
			if (bean.getLongitude() != null) {
				temp.setLongitude(bean.getLongitude().toString());
			}
			
			if (bean.getCellId() != null) {
				temp.setCid(bean.getCellId().toString());
			}
			
			if (bean.getMcc() != null) {
				temp.setMcc(bean.getMcc().toString());
			}
			
			if (bean.getMnc() != null) {
				temp.setMnc(bean.getMnc().toString());
			}
			
			if (bean.getLac() != null) {
				temp.setLac(bean.getLac().toString());
			}
			
			if (bean.getAccuracy()!= null) {
				temp.setAccuracy(bean.getAccuracy().toString());
			}
			if (bean.getLobFile()!=null) {
				temp.setImage(BaseEncoding.base64().encode(bean.getLobFile()));
			}
			taskDs.add(temp);
		}
		SubmitTaskDBean[] result = new SubmitTaskDBean[taskDs.size()];
		return taskDs.toArray(result);
	}
	
	@Override
	public Map<Integer, MsQuestion> getAllMsQuestion(SubmitTaskDBean[] taskDBean, MsFormhistory formHist, AuditContext auditContext){
		Map<Integer, MsQuestion> result = new HashMap<>();
		 
		for (int i=0; i<taskDBean.length; i++) {
			Object [][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", Long.valueOf(formHist.getUuidFormHistory()))},
								{Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(taskDBean[i].getQuestion_id()))}};
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
			
			MsQuestion msQuestion = new MsQuestion();
			msQuestion.setUuidQuestion(Long.parseLong(taskDBean[i].getQuestion_id()));
			msQuestion.setRefId(qset.getRefId());
			msQuestion.setQuestionLabel(qset.getQuestionLabel());
			msQuestion.setMsAnswertype(qset.getMsAnswertype());
			msQuestion.setMsAssettag(qset.getMsAssettag());
			msQuestion.setMsCollectiontag(qset.getMsCollectiontag());
			msQuestion.setMsOrdertag(qset.getMsOrdertag());
			msQuestion.setLovGroup(qset.getLovGroup());
			msQuestion.setIsVisible(qset.getIsVisible());
			msQuestion.setIsMandatory(qset.getIsMandatory());
			msQuestion.setIsReadonly(qset.getIsReadonly());
			msQuestion.setIsHolidayAllowed(qset.getIsHolidayAllowed());
			msQuestion.setMaxLength(qset.getMaxLength());
			msQuestion.setRegexPattern(qset.getRegexPattern());
			msQuestion.setAmMssubsystem(qset.getAmMssubsystem());
			msQuestion.setImgQlt(qset.getImgQlt());
			result.put(i, msQuestion);
		}
		return result;
	}
	
	private MsBranch getBranch(Map mapQuestion, SubmitTaskDBean[] taskDBean){
		Object[][] queryParams = new Object[1][1];
		queryParams[0][0] = Restrictions.eq("tagName", GlobalVal.ORDER_TAG_MF_BRANCH);
		MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, queryParams);
		for (int i=0; i<taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) mapQuestion.get(i);
			
			if (msQuestion.getMsOrdertag() != null &&
					msOrderTag.getUuidOrderTag() == msQuestion.getMsOrdertag().getUuidOrderTag()) {
				
				MsLov lov =  this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(taskDBean[i].getOption_answer_id()));
				if (lov == null) {
					return null;
				}
				MsBranch result = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(lov.getCode()));
				return result;
				
			}
		}
		
		return null;
	}
	
	private String getMhId(Map mapQuestion, SubmitTaskDBean[] taskDBean){
		Object[][] queryParams = new Object[1][1];
		queryParams[0][0] = Restrictions.eq("tagName", GlobalVal.ORDER_TAG_JOB_MH);
		MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, queryParams);
		
		if (msOrderTag == null ){
			return null;
		}
		
		for (int i=0; i<taskDBean.length; i++) {
			for (int j=0; j<mapQuestion.size(); j++) {
				MsQuestion msQuestion = (MsQuestion) mapQuestion.get(j);
				if (String.valueOf(msQuestion.getUuidQuestion()).equals(taskDBean[i].getQuestion_id())) {
					if (msQuestion.getMsOrdertag() != null &&
							msOrderTag.getUuidOrderTag() == msQuestion.getMsOrdertag().getUuidOrderTag()) {
						String[][] params = {
								{"code", taskDBean[i].getLov()},
								{"lovGroup", msQuestion.getLovGroup()} };
						if (null != taskDBean[i].getLov()) {
							Object[] msLov = (Object[])this.getManagerDAO().selectOneNative("services.submitorder.getMsLov", params);
							if (msLov != null) {
								return taskDBean[i].getLov();
							}
						} 
						else {
							MsLov lovBean = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(taskDBean[i].getOption_answer_id()));
							if (lovBean != null) {
								return lovBean.getCode();
							}
						}
					}
				}
			}
		}
		
		return StringUtils.EMPTY;
	}
	
	private String getPtsDate(Map mapQuestion, SubmitTaskDBean[] taskDBean){
		Object[][] queryParams = { {Restrictions.eq("tagName", GlobalVal.ORDER_TAG_PTS_DATE)} } ;
		MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, queryParams);
		if (msOrderTag == null) {
			return null;
		}
		
		for (int i=0; i<taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) mapQuestion.get(i);
			
			if (msQuestion.getMsOrdertag() != null &&
					msOrderTag.getUuidOrderTag() == msQuestion.getMsOrdertag().getUuidOrderTag()) {
				return taskDBean[i].getText_answer();					
			}
		}
		
		return null;
	}
	
	private String getLocationSurvey(Map mapQuestion, SubmitTaskDBean[] taskDBean){
		Object[][] queryParams = { {Restrictions.eq("tagName", GlobalVal.ORDER_TAG_SURVEY_LOCATION)} } ;
		MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, queryParams);
		if (msOrderTag == null) {
			return null;
		}
		for (int i=0; i<taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) mapQuestion.get(i);
			
			if (msQuestion.getMsOrdertag() != null &&
					msOrderTag.getUuidOrderTag() == msQuestion.getMsOrdertag().getUuidOrderTag()) {
				return taskDBean[i].getLatitude() + ";" + taskDBean[i].getLongitude();
			}
		}
		
		return null;
	}
	
	@Override
	public TrTaskH saveTaskHSurvey(AmMsuser userSubmit, TrTaskH trTaskHOrder,
			BigDecimal latHeader, BigDecimal longiHeader, MsPriority surveyPriority,
			MsBranch msBranchHO, AmMsuser userMh, MsBranch msBranchMfSelected,
			String ptsDate, MappingFormBean formQuestion, boolean isNewLead, AuditContext auditContext) {
		
		String dtc = StringUtils.EMPTY;
		String notesOrder = trTaskHOrder.getNotes();
		
		if (null != latHeader && null != longiHeader) {
			String lat = trTaskHOrder.getMsBranch().getLatitude()==null?"0":String.valueOf(trTaskHOrder.getMsBranch().getLatitude());
			String lng = trTaskHOrder.getMsBranch().getLongitude()==null?"0":String.valueOf(trTaskHOrder.getMsBranch().getLongitude());
			
			double distance = DistanceUtils.getDistanceInKm(
					Double.parseDouble(String.valueOf(latHeader)), 
					Double.parseDouble(String.valueOf(longiHeader)), 
					Double.parseDouble(lat), 
					Double.parseDouble(lng));
			NumberFormat nf = new DecimalFormat("#.##");
			dtc = "Jarak alamat customer dengan cabang WOM : "+nf.format(distance)+" Km\n";
			notesOrder = dtc + notesOrder;
		}
		
		//insert into tr_taskh
		TrTaskH taskHSurvey = new TrTaskH();
		taskHSurvey.setUsrCrt(auditContext.getCallerId());
		taskHSurvey.setDtmCrt(new Date());
		
		taskHSurvey.setMsBranch(trTaskHOrder.getMsBranch());
		taskHSurvey.setAmMsuser(userSubmit);
		
		//getMsForm from questionMap formid
		MsForm msForm = this.commonLogic.retrieveMsFormByUuid(Long.parseLong(formQuestion.getFormId()), auditContext);
		taskHSurvey.setMsForm(msForm);
		taskHSurvey.setApplNo(trTaskHOrder.getApplNo());
		taskHSurvey.setMsPriority(surveyPriority);
		taskHSurvey.setCustomerName(trTaskHOrder.getCustomerName());
		taskHSurvey.setCustomerPhone(trTaskHOrder.getCustomerPhone());
		taskHSurvey.setCustomerAddress(trTaskHOrder.getCustomerAddress());
		taskHSurvey.setZipCode(trTaskHOrder.getZipCode());
		taskHSurvey.setNotes(notesOrder);
		//trTaskHMap.setTaskId(this.commonLogic.retrieveNewTaskId(GlobalVal.SEQUENCE_TASK_ID_MOBILE_SURVEY));
		taskHSurvey.setIsDraft("0");
		if (StringUtils.contains(msForm.getFormName().toUpperCase(), "FOTO")) {
			taskHSurvey.setFlagSource(GlobalVal.MSCOREF);
		} else if (StringUtils.contains(msForm.getFormName().toUpperCase(), "TEXT")) {
			taskHSurvey.setFlagSource(GlobalVal.MSCOREP);
		} else if (StringUtils.contains(msForm.getFormName().toUpperCase(), GlobalVal.FORM_INSTANT_APPROVAL) ||
				StringUtils.contains(msForm.getFormName().toUpperCase(), "NEW LEAD") ||
				StringUtils.contains(msForm.getFormName().toUpperCase(), "ORD")) {
			taskHSurvey.setFlagSource(GlobalVal.SUBSYSTEM_MS);
		} else {
			taskHSurvey.setFlagSource("MSCORE");
		}
		taskHSurvey.setIsAppNotified("0");
		taskHSurvey.setLatitude(latHeader);
		taskHSurvey.setLongitude(longiHeader);
		taskHSurvey.setIsMatchDukcapil("0");
		taskHSurvey.setBranchKmDistance(0);
		
		Object[][] paramForm = { {"uuidForm", msForm.getUuidForm()} };
		Integer newVersion = (Integer) this.getManagerDAO().selectOneNativeString("SELECT max(FORM_VERSION) from MS_FORMHISTORY where UUID_FORM = :uuidForm", paramForm);
		taskHSurvey.setFormVersion(newVersion);
//		taskHSurvey.setFormVersion(formQuestion.getFormVersion());
		taskHSurvey.setAssignDate(new Date());
		
		if (StringUtils.isNotBlank(ptsDate)){
			taskHSurvey.setPtsDate(stringToDate(ptsDate, "dd/MM/yyyy HH:mm:ss"));
		}
		
		if (true == isNewLead) {
			taskHSurvey.setIsAlternate1("0");
			taskHSurvey.setIsAlternate2("0");
			taskHSurvey.setIsUnassign("0");
		}
		this.getManagerDAO().insert(taskHSurvey);
		
		taskHSurvey.setTaskId(String.valueOf(taskHSurvey.getUuidTaskH()));
		
		return taskHSurvey;
	}
	
	@Override
	public Map getLatLongSubmitData(TrTaskH taskH, AuditContext auditContext){
		String formatAddress = GEOCODE_FORMAT_ADDR;
		
		String[] listAssetTag = {
				GlobalVal.ASSET_TAG_NAMA_JALAN, GlobalVal.ASSET_TAG_PROVINSI, 
				GlobalVal.ASSET_TAG_KOTA, GlobalVal.ASSET_TAG_KECAMATAN,
				GlobalVal.ASSET_TAG_KELURAHAN, GlobalVal.ASSET_TAG_ZIPCODE
		};
		
		Object[][] paramTaskAsset = { {"assetTags", listAssetTag}, {"uuidTaskH", taskH.getUuidTaskH()} };
		
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT ast.ASSET_TAG_NAME, ");
		sb.append(" CASE WHEN td.TEXT_ANSWER is null THEN td.OPTION_TEXT ELSE td.TEXT_ANSWER END Answer ");
		sb.append(" from TR_TASK_D td ");
		sb.append(" join MS_QUESTION q on td.UUID_QUESTION = q.UUID_QUESTION ");
		sb.append(" join MS_ASSETTAG ast on q.UUID_ASSET_TAG = ast.UUID_ASSET_TAG ");
		sb.append(" where ast.ASSET_TAG_NAME in :assetTags ");
		sb.append(" and td.UUID_TASK_H = :uuidTaskH ");
		
		List<Map<String, Object>> listMap = this.getManagerDAO().selectAllNativeString(sb.toString(), paramTaskAsset);
		
		if (!listMap.isEmpty()) {
			for (Map<String, Object> mp : listMap) {
				String assetName = (String) mp.get("d0");
				String answer = (String) mp.get("d1");
				formatAddress = StringUtils.replace(formatAddress, "["+assetName+"]", answer);
			}
		}
		Map mapLatLong = geocodingConversionLogic.getLatLongGeocodeConversion(formatAddress, auditContext);
		return mapLatLong;
	}
	
	private Date stringToDate(String date, String dateformat){
		DateFormat format = new SimpleDateFormat(dateformat);
		Date d = null;
		try{
			d = format.parse(date);
		} 
		catch (Exception e){
			LOG.error(e.getMessage(),e);
			e.printStackTrace();
		}
		return d;
	}
	
	private void saveTaskDSurveyIntoJson(TrTaskH taskHSurvey, List<MappingFormBean> listQuestion, Map<Integer, MsQuestion> questions,
			SubmitTaskDBean[] taskDBeanOrder, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		if (taskDBeanOrder == null){
			return;
		}
		
		if (listQuestion == null || listQuestion.isEmpty()){
			return;
		}
		
		TrTaskdocument docDb = new TrTaskdocument(taskHSurvey, new Date(), auditContext.getCallerId());
		TaskDocumentBean document = new TaskDocumentBean();
		List<AnswerBean> answers = new ArrayList<AnswerBean>();		
		document.setAnswers(answers);
		
		MsFormhistory formHistory = this.commonLogic.retrieveMsFormhistory(taskHSurvey.getMsForm().getUuidForm(),
				taskHSurvey.getFormVersion(), auditContext);
		
		for (int i = 0; i < listQuestion.size(); i++) {
			MappingFormBean mappingQuestion = listQuestion.get(i); //uuidQ1=Svy(target) uuidQ2=Odr(source)
			
			for (int k = 0; k < taskDBeanOrder.length; k++) {
				String uuidQuestionD = taskDBeanOrder[k].getQuestion_id();
				if (mappingQuestion.getUuidQuestion2().equals(uuidQuestionD)) { //kalau jawaban Order ada di Mapping -> insert
					String optionAnswerId = taskDBeanOrder[k].getOption_answer_id();
					String textAnswer = taskDBeanOrder[k].getText_answer();
					String latitude = taskDBeanOrder[k].getLatitude();
					String longitude = taskDBeanOrder[k].getLongitude();
					String mcc = taskDBeanOrder[k].getMcc();
					String mnc = taskDBeanOrder[k].getMnc();
					String lac = taskDBeanOrder[k].getLac();
					String cellId = taskDBeanOrder[k].getCid();
					String accuracy = taskDBeanOrder[k].getAccuracy();
					String image = taskDBeanOrder[k].getImage();
					
					Object [][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHistory.getUuidFormHistory())},
							{Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(mappingQuestion.getUuidQuestion1()))}};
					MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
					MsQuestion msQuestionSvy = 	qset.getMsQuestion();
					msQuestionSvy.setQuestionLabel(qset.getQuestionLabel());
					
					int idxAnswer = document.findAnswerIndex(msQuestionSvy.getUuidQuestion());
					
					AnswerBean answer = this.constructAnswer(document, idxAnswer, msQuestionSvy,
							msQuestionSvy.getMsAnswertype().getCodeAnswerType(), msQuestionSvy.getQuestionLabel(),
							textAnswer, optionAnswerId, latitude, longitude,
							accuracy, mcc, mnc, lac, cellId, taskHSurvey, image, isl, imagePath, true, false, auditContext);
					
					if (idxAnswer == -1) {
						answers.add(answer);
					}
					
					break;
				}
			}
		}
		
		docDb.setDocument(gson.toJson(document, TaskDocumentBean.class));
		this.getManagerDAO().insert(docDb);		
	}
	
	private AnswerBean constructAnswer(TaskDocumentBean document, int idxAnswer, MsQuestion msQuestion,
			String answerTypeCode, String questionText, String textAnswer, String optionAnswerId,
			String latitude, String longitude, String accuracy, String mcc, String mnc, String lac, String cellId,
			TrTaskH trTaskH, String image, ImageStorageLocation isl, Path imagePath,
			boolean saveToInt, boolean checkExisting, AuditContext auditContext) {
		AnswerBean answer = (idxAnswer == -1) ? new AnswerBean() : document.getAnswers().get(idxAnswer);
		QuestionBean questionBean = new QuestionBean(
				msQuestion.getUuidQuestion(), questionText, msQuestion.getRefId(), answerTypeCode,
				(msQuestion.getMsOrdertag() == null ? null : msQuestion.getMsOrdertag().getTagName()),
				(msQuestion.getMsAssettag() == null ? null : msQuestion.getMsAssettag().getAssetTagName()),
				(msQuestion.getMsCollectiontag() == null ? null : msQuestion.getMsCollectiontag().getTagName()));
		answer.setQuestion(questionBean);
		
		if (MssTool.isChoiceQuestion(answerTypeCode)) {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			List<OptionBean> options = null;
			if (saveToInt) {
				options = (answer.getIntOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getIntOptAnswers();
			}
			else {
				options = (answer.getOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getOptAnswers();
			}
			MsLov msLovByLovId = null; 
			if (StringUtils.isNotBlank(optionAnswerId)) {
				if (null != msQuestion.getMsOrdertag() && GlobalVal.ORDER_TAG_JOB_MH.equals(msQuestion.getMsOrdertag().getTagName())) {						
					Object[][] paramsLov = {{Restrictions.eq("code", optionAnswerId)}, {Restrictions.eq("lovGroup", "LOOKUPMH")}};
					msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, paramsLov);						
				}
				else {
					msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
				}
			}
			if (msLovByLovId != null) {
				OptionBean option = new OptionBean();
				option.setUuid(msLovByLovId.getUuidLov());
				option.setCode(msLovByLovId.getCode());
				option.setDesc(msLovByLovId.getDescription());
				option.setFreeText(StringUtils.trimToNull(textAnswer));
				if (!options.contains(option)) {
					options.add(option);
				}
				
				if (saveToInt) {
					answer.setIntOptAnswers(options);
				}
				else {
					answer.setOptAnswers(options);
				}
			}				
		}
		else if (MssTool.isImageQuestion(answerTypeCode) || GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
			answer.setOptAnswers(null);
			Boolean isGps = null;
			Boolean isConverted = null;
			
			if (mcc != null) {
				com.adins.mss.model.taskdjson.LocationBean locationBean = (answer.getLocation() == null) ? new com.adins.mss.model.taskdjson.LocationBean() : answer.getLocation();
				locationBean.setMcc(checkEmptyInteger(mcc).intValue());
				locationBean.setMnc(checkEmptyInteger(mnc).intValue());
				locationBean.setLac(checkEmptyInteger(lac).intValue());
				locationBean.setCid(checkEmptyInteger(cellId).intValue());
				locationBean.setAccuracy(checkEmptyInteger(accuracy));
				
				if (latitude != null && longitude != null
						&& new BigDecimal(latitude).intValue() != 0 && new BigDecimal(longitude).intValue() != 0) {
					locationBean.setLat(checkEmptyBigdecimal(latitude).doubleValue());
					locationBean.setLng(checkEmptyBigdecimal(longitude).doubleValue());
					locationBean.setIsGps(1);
				}
				else {
					com.adins.framework.tool.geolocation.model.LocationBean convertedLocation =
							this.getLocationByCellId(locationBean.getMcc(), locationBean.getMnc(),
							locationBean.getLac(), locationBean.getCid(), auditContext);
					if (convertedLocation.getCoordinate() != null) {
						locationBean.setAccuracy(convertedLocation.getAccuracy());
						locationBean.setLat(convertedLocation.getCoordinate().getLatitude());
						locationBean.setLng(convertedLocation.getCoordinate().getLongitude());
						locationBean.setIsGps(0);
						locationBean.setIsConverted(1);
						
						latitude = Double.toString(convertedLocation.getCoordinate().getLatitude());
						longitude = Double.toString(convertedLocation.getCoordinate().getLongitude());
						accuracy = Integer.toString(convertedLocation.getAccuracy());
					}
					isGps = Boolean.FALSE;
					isConverted = Boolean.TRUE;
				}
				
				answer.setLocation(locationBean);
			}

			if (MssTool.isImageQuestion(answerTypeCode)) {
				ImageBean imageBean = (answer.getLobAnswer() == null) ? new ImageBean() : answer.getLobAnswer();
				if (image != null && !image.isEmpty()) {
					imageBean.setHasImage(true);
					answer.setLobAnswer(imageBean);
				}
				
				//insert into Table TR_TASKDETAILLOB
				String uuidTaskD = this.getManagerDAO().getUUID();
				long idLob = this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, isGps, isConverted, isl, imagePath, checkExisting);
				if (idLob > 0L)
					imageBean.setId(idLob);
			}
			else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
				if (saveToInt) {
					answer.setIntTxtAnswer(textAnswer);
				}
				else {
					answer.setTxtAnswer(textAnswer);
				}
			}
		}
		else {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)
					|| GlobalVal.ANSWER_TYPE_DATETIME.equals(answerTypeCode)) {
				textAnswer = this.dateAnswerToText(textAnswer, answerTypeCode);
			}
			if (saveToInt) {
				answer.setIntOptAnswers(null);
				answer.setIntTxtAnswer(textAnswer);
			}
			else {
				answer.setOptAnswers(null);
				answer.setTxtAnswer(textAnswer);
			}			
		}
		
		return answer;
	}
	
	private long insertDetailLob(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String questionText,
			String base64Image, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Boolean isGps, Boolean isConverted, ImageStorageLocation saveImgLoc, Path imgLoc, boolean checkExisting){
		
		if (checkExisting) {
			Object[][] paramTaskD = {
				{Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
				{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())}
			};
			TrTaskdetaillob bean = (TrTaskdetaillob) this.getManagerDAO().selectOne(TrTaskdetaillob.class, paramTaskD);
	
			if (bean != null) {
				return 0L;
			}
		}
		
		TrTaskdetaillob trTaskDetailLob = new TrTaskdetaillob();
		trTaskDetailLob.setUsrCrt(auditContext.getCallerId());
		trTaskDetailLob.setDtmCrt(new Date());	
		trTaskDetailLob.setTrTaskH(trTaskH);
		trTaskDetailLob.setMsQuestion(msQuestion);
		
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				trTaskDetailLob.setLobFile(BaseEncoding.base64().decode(base64Image));
			}
		}
        else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
            if (base64Image != null && !base64Image.isEmpty()) {
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date date = new Date();
                Path outputPath = Paths.get(imgLoc.toString(), dateFormat.format(date));

                String absoluteFilename = trTaskH.getTaskId() + "_" + msQuestion.getRefId() + "_"
                        + uuidTaskD + ".jpg";
                
                String outputFile = this.imageStorageLogic.storeImageFileSystem(
                        BaseEncoding.base64().decode(base64Image), outputPath, absoluteFilename);
                trTaskDetailLob.setImagePath(outputFile);
            }
        }
		trTaskDetailLob.setQuestionText(questionText);
		trTaskDetailLob.setLatitude(checkEmptyBigdecimal(latitude));
		trTaskDetailLob.setLongitude(checkEmptyBigdecimal(longitude));
		
		if (Boolean.TRUE.equals(isGps) || (trTaskDetailLob.getLatitude()!=null && trTaskDetailLob.getLongitude()!=null &&
				trTaskDetailLob.getLatitude().intValue()!=0 && trTaskDetailLob.getLongitude().intValue()!=0) &&
				!Boolean.TRUE.equals(isConverted)) {
			trTaskDetailLob.setIsGps("1");
		}
		else if (Boolean.FALSE.equals(isGps)) {
			trTaskDetailLob.setIsGps("0");
		}
		trTaskDetailLob.setMcc(checkEmptyInteger(mcc));
		trTaskDetailLob.setMnc(checkEmptyInteger(mnc));
		trTaskDetailLob.setLac(checkEmptyInteger(lac));
		trTaskDetailLob.setCellId(checkEmptyInteger(cellId));
		trTaskDetailLob.setAccuracy(checkEmptyInteger(accuracy));
		if (!Boolean.TRUE.equals(isConverted) && !"1".equals(trTaskDetailLob.getIsGps()) && trTaskDetailLob.getMcc() != null && trTaskDetailLob.getMnc() != null
				&& trTaskDetailLob.getLac() != null && trTaskDetailLob.getCellId() != null) {
			this.getLocationByCellId(trTaskDetailLob, auditContext);
		}
		else if (Boolean.TRUE.equals(isConverted)) {
			trTaskDetailLob.setIsConverted("1");
		}
		this.getManagerDAO().insert(trTaskDetailLob);
		
		if (ImageStorageLocation.DMS == saveImgLoc) {
			UploadImageRequestBean request = new UploadImageRequestBean();
			UploadImageBean uploadBean  = new UploadImageBean();
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq(paramGsCode, GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Object [][] param = {{"uuidTaskH", trTaskH.getUuidTaskH()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT	GROUP_TASK_ID " + 
					"FROM	MS_GROUPTASK with(nolock) " + 
					"WHERE	UUID_TASK_H = :uuidTaskH ", param);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(trTaskH.getMsForm().getUuidForm()));
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != trTaskH.getSubmitDate()) {
	        	taskDate = trTaskH.getSubmitDate();
	        } else {
	        	taskDate = new Date();
	        }
			try {
				cutoffDate = new SimpleDateFormat(dateFormat).parse(gs.getGsValue());
			} catch (ParseException e1) {
				LOG.error(e1.getMessage(), e1);
				e1.printStackTrace();
			}
			if(taskDate.before(cutoffDate)) {
				uploadBean.setId(trTaskH.getTaskId());
				uploadBean.setTaskId(trTaskH.getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				uploadBean.setId(groupTaskIdForm);
				uploadBean.setTaskId(groupTaskIdForm);
			}
	
			uploadBean.setType("survey");
			uploadBean.setJenisDoc(msQuestion.getQuestionLabel());
			uploadBean.setRefId(msQuestion.getRefId());
			uploadBean.setUsername(trTaskH.getAmMsuser().getUniqueId());
			if(StringUtils.isNotEmpty(base64Image)){
				trTaskDetailLob.setImagePath(String.valueOf(trTaskDetailLob.getUuidTaskDetailLob()));
				this.getManagerDAO().update(trTaskDetailLob);
				request.setByteImage(BaseEncoding.base64().decode(base64Image));
			}
			request.setFileName(trTaskDetailLob.getUuidTaskDetailLob()+".jpg");
			try {
				request.setIpAddress(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e) {
				LOG.error("Logging error DMS Host : {}",e.getMessage());
			}
			request.setUploadImageBean(uploadBean); 
			if(StringUtils.isNotEmpty(base64Image)){
				
				//PANGGIL KE INTNCFORMLOGIC
				intFormLogic.uploadImageResponse(auditContext,request);
			}
		}
		
		return trTaskDetailLob.getUuidTaskDetailLob();
	}
	
	@Override
	public void saveTaskDSurveyIntoRow(TrTaskH taskHSurvey, List<MappingFormBean> listQuestion, Map<Integer, MsQuestion> questions,
			SubmitTaskDBean[] taskDBeanOrder, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		Object [][] prmFormHist = {{Restrictions.eq("msForm.uuidForm", taskHSurvey.getMsForm().getUuidForm())},
				{Restrictions.eq("formVersion", taskHSurvey.getFormVersion())}};
		MsFormhistory formHistory = this.getManagerDAO().selectOne(MsFormhistory.class, prmFormHist);
		
		for (int i = 0; i < listQuestion.size(); i++) {
			MappingFormBean mappingQuestion = listQuestion.get(i); //uuidQ1=Svy(target) uuidQ2=Odr(source)
			
			for (int k = 0; k < taskDBeanOrder.length; k++) {
				String uuidQuestionD = taskDBeanOrder[k].getQuestion_id();
				if (mappingQuestion.getUuidQuestion2().equals(uuidQuestionD)) { //kalau jawaban Order ada di Mapping -> insert
					MsQuestion msQuestion2 = questions.get(k);
					String answerType = msQuestion2.getMsAnswertype().getCodeAnswerType();
					
					String optionAnswerId = taskDBeanOrder[k].getOption_answer_id();
					String textAnswer = taskDBeanOrder[k].getText_answer();
					String latitude = taskDBeanOrder[k].getLatitude();
					String longitude = taskDBeanOrder[k].getLongitude();
					String mcc = taskDBeanOrder[k].getMcc();
					String mnc = taskDBeanOrder[k].getMnc();
					String lac = taskDBeanOrder[k].getLac();
					String cellId = taskDBeanOrder[k].getCid();
					String accuracy = taskDBeanOrder[k].getAccuracy();
					String image = taskDBeanOrder[k].getImage();
					String uuidTaskD = this.getManagerDAO().getUUID().toUpperCase();
					String optionText = null;
					Long uuidLookup = null;
					
					Object [][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHistory.getUuidFormHistory())},
							{Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(mappingQuestion.getUuidQuestion1()))}};
					MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
					MsQuestion msQuestionSvy = 	qset.getMsQuestion();
					msQuestionSvy.setQuestionLabel(qset.getQuestionLabel());
					
					if (GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(answerType)) {
						if (textAnswer.contains("|")) {
							String temp[] = textAnswer.split("[|]");
							textAnswer = temp[temp.length-1];
						}
					}
					
					if (msQuestionSvy.getMsAssettag() != null){
						if (GlobalVal.ASSET_TAG_PRODUCT.equalsIgnoreCase(msQuestionSvy.getMsAssettag().getAssetTagName()) && 
								(StringUtils.isNotBlank(optionAnswerId))){
							this.insertTaskSurveyData(taskHSurvey, optionAnswerId, auditContext.getCallerId());
						}
					}
					
					if (MssTool.isImageQuestion(msQuestion2.getMsAnswertype().getCodeAnswerType())){
						//insert into Table TR_TASKDETAILLOB
						insertDetailLobSurvey(auditContext, taskHSurvey, msQuestionSvy, uuidTaskD, image, 
								latitude, longitude, mcc, mnc, lac, cellId, accuracy, isl, imagePath);
					} 
					else {
						if (GlobalVal.ANSWER_TYPE_LU_OFFLINE.equals(msQuestionSvy.getMsAnswertype().getCodeAnswerType()) || 
								GlobalVal.ANSWER_TYPE_LU_OFFLINE_WITH_SUGGESTION.equals(msQuestionSvy.getMsAnswertype().getCodeAnswerType())) {
							if (StringUtils.isNotBlank(taskDBeanOrder[k].getUuid_lookup())) {
								uuidLookup = Long.valueOf(taskDBeanOrder[k].getUuid_lookup());
							}
							optionText = taskDBeanOrder[k].getLov();
							optionAnswerId = null;
						} else if (GlobalVal.ANSWER_TYPE_SCORING.equals(msQuestionSvy.getMsAnswertype().getCodeAnswerType()) ) {
							optionText = taskDBeanOrder[k].getLov();
							optionAnswerId = null;
						}
						//insert into Table TR_TASK_D
						insertTaskDSurvey(auditContext, taskHSurvey, msQuestionSvy, uuidTaskD, optionAnswerId, 
								textAnswer, latitude, longitude, mcc, mnc, lac, cellId, accuracy, uuidLookup, optionText);
					}
					
					break;
				}
			}
		}
		
		if (GlobalVal.MSCOREP.equals(taskHSurvey.getFlagSource())) {
			taskServiceLogic.insertTaskDForReferantor (formHistory,  taskHSurvey, auditContext);
		}
	}
	
	private void insertDetailLobSurvey(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD,
			String base64Image, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, ImageStorageLocation saveImgLoc, Path imgLoc) {

		TrTaskdetaillob trTaskDetailLob = new TrTaskdetaillob();
		trTaskDetailLob.setUsrCrt(auditContext.getCallerId());
		trTaskDetailLob.setDtmCrt(new Date());
		
		trTaskDetailLob.setTrTaskH(trTaskH);
		trTaskDetailLob.setMsQuestion(msQuestion);
		trTaskDetailLob.setQuestionText(msQuestion.getQuestionLabel());
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				trTaskDetailLob.setLobFile(BaseEncoding.base64().decode(base64Image));
			}
		}
        else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
            if (base64Image != null && !base64Image.isEmpty()) {
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date date = new Date();
                Path outputPath = Paths.get(imgLoc.toString(), dateFormat.format(date));

                String absoluteFilename = trTaskH.getTaskId() + "_" + msQuestion.getRefId() + "_"
                        + uuidTaskD + ".jpg";
                
                String outputFile = this.imageStorageLogic.storeImageFileSystem(
                        BaseEncoding.base64().decode(base64Image), outputPath, absoluteFilename);
                trTaskDetailLob.setImagePath(outputFile);
            }
        }
		trTaskDetailLob.setLatitude(checkEmptyBigdecimal(latitude) );
		trTaskDetailLob.setLongitude(checkEmptyBigdecimal(longitude) );
		
		if (trTaskDetailLob.getLatitude()!=null && trTaskDetailLob.getLongitude()!=null)
			trTaskDetailLob.setIsGps("1");
		
		trTaskDetailLob.setMcc(checkEmptyInteger(mcc) );
		trTaskDetailLob.setMnc(checkEmptyInteger(mnc) );
		trTaskDetailLob.setLac(checkEmptyInteger(lac) );
		trTaskDetailLob.setCellId(checkEmptyInteger(cellId));
		trTaskDetailLob.setAccuracy(checkEmptyInteger(accuracy) );
		
		this.getManagerDAO().insert(trTaskDetailLob);
		
		if (ImageStorageLocation.DMS == saveImgLoc) {
			UploadImageRequestBean request = new UploadImageRequestBean();
			UploadImageBean uploadBean  = new UploadImageBean();
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq(paramGsCode, GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Object [][] param = {{"uuidTaskH", trTaskH.getTaskId()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT	GROUP_TASK_ID " + 
					"FROM	MS_GROUPTASK with(nolock) " + 
					"WHERE	UUID_TASK_H = :uuidTaskH ", param);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(trTaskH.getMsForm().getUuidForm()));
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != trTaskH.getSubmitDate()) {
	        	taskDate = trTaskH.getSubmitDate();
	        } else {
	        	taskDate = new Date();
	        }
			try {
				cutoffDate = new SimpleDateFormat(dateFormat).parse(gs.getGsValue());
			} catch (ParseException e1) {
				LOG.error(e1.getMessage(), e1);
				e1.printStackTrace();
			}
			if(taskDate.before(cutoffDate)) {
				uploadBean.setId(trTaskH.getTaskId());
				uploadBean.setTaskId(trTaskH.getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				uploadBean.setId(groupTaskIdForm);
				uploadBean.setTaskId(groupTaskIdForm);
			}
	
			uploadBean.setType("survey");
			uploadBean.setJenisDoc(msQuestion.getQuestionLabel());
			uploadBean.setRefId(msQuestion.getRefId());
			uploadBean.setUsername(trTaskH.getAmMsuser().getUniqueId());
			if(StringUtils.isNotEmpty(base64Image)){
				trTaskDetailLob.setImagePath(String.valueOf(trTaskDetailLob.getUuidTaskDetailLob()));
				this.getManagerDAO().update(trTaskDetailLob);
				request.setByteImage(BaseEncoding.base64().decode(base64Image));
			}
			request.setFileName(trTaskDetailLob.getUuidTaskDetailLob()+".jpg");
			try {
				request.setIpAddress(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e) {
				LOG.error("Logging error DMS Host : {}",e.getMessage());
			}
			request.setUploadImageBean(uploadBean); 
			if(StringUtils.isNotEmpty(base64Image)){
				
				//PANGGIL KE INTNCFORMLOGIC
				intFormLogic.uploadImageResponse(auditContext,request);
			}
		}
	}
	
	private void insertTaskDSurvey(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, 
			String optionAnswerId, String textAnswer, String latitude, String longitude,
			String mcc, String mnc, String lac, String cellId, String accuracy, Long idStagingAsset, String optionText) {
		MsLov msLovByLovId =  null;
		TrTaskD taskD = null;
		if (!GlobalVal.ANSWER_TYPE_MULTIPLE.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) ||
				!GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) ||
				!GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			Object[][] params = {
					{Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
					{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())}
				};
			taskD = this.getManagerDAO().selectOne(TrTaskD.class, params);
		} 
		else {
			String[][] paramsDel = {
					{"uuidTaskH", String.valueOf(trTaskH.getUuidTaskH())},
					{"uuidQuestion", String.valueOf(msQuestion.getUuidQuestion())},
					{"uuidForm", String.valueOf(trTaskH.getMsForm().getUuidForm())}
				};
			this.getManagerDAO().deleteNativeString("delete from tr_task_d where uuid_task_h = :uuidTaskH and uuid_question = :uuidQuestion and uuid_form = :uuidForm", paramsDel);
		}
		if (null != taskD) {
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setDtmUpd(new Date());
			taskD.setIntTextAnswer(textAnswer);
			taskD.setLatitude(checkEmptyBigdecimal(latitude));
			taskD.setLongitude(checkEmptyBigdecimal(longitude));
			taskD.setMcc(checkEmptyInteger(mcc));
			taskD.setMnc(checkEmptyInteger(mnc));
			taskD.setLac(checkEmptyInteger(lac));
			taskD.setCellId(checkEmptyInteger(cellId));
			taskD.setAccuracy(checkEmptyInteger(accuracy));
			taskD.setIntIdStagingAsset(idStagingAsset);
			taskD.setIntOptionText(optionText);
			
			this.getManagerDAO().update(taskD);
		} 
		else {
			
			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			
			if (StringUtils.isNotBlank(optionAnswerId)) {
				msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
				trTaskD.setMsLovByIntLovId(msLovByLovId);
				trTaskD.setIntOptionText(msLovByLovId.getDescription());
			} else {
				trTaskD.setIntOptionText(optionText);
			}
						
			trTaskD.setTrTaskH(trTaskH);
			trTaskD.setMsQuestion(msQuestion);		
			trTaskD.setQuestionText(msQuestion.getQuestionLabel());
			trTaskD.setIntTextAnswer(textAnswer);
			trTaskD.setLatitude(checkEmptyBigdecimal(latitude));
			trTaskD.setLongitude(checkEmptyBigdecimal(longitude));
			trTaskD.setMcc(checkEmptyInteger(mcc));
			trTaskD.setMnc(checkEmptyInteger(mnc));
			trTaskD.setLac(checkEmptyInteger(lac));
			trTaskD.setCellId(checkEmptyInteger(cellId));
			trTaskD.setAccuracy(checkEmptyInteger(accuracy));
			trTaskD.setIntIdStagingAsset(idStagingAsset);
			
			this.getManagerDAO().insert(trTaskD);
		}
	}
	
	
	private BigDecimal checkEmptyBigdecimal(String in){
		if (StringUtils.isBlank(in)){
			return null;
		}
		else if ("null".equals(in)){
			return null;
		}
		else{
			return new BigDecimal(in);
		}
	}
	
	private Integer checkEmptyInteger(String in){
		if (StringUtils.isBlank(in)){
			return null;
		}
		else if ("null".equals(in)){
			return null;
		}
		else{
			return Integer.valueOf(in);		
		}
	}
	
	private Map<Integer, MsQuestion> getAllMsQuestion(SubmitTaskDBean[] taskDBean, MsFormhistory formHist){
		Map<Integer, MsQuestion> result = new HashMap<>();
		
		LOG.info("UUID Form History: {}, Form Version: {}", formHist.getUuidFormHistory(), formHist.getFormVersion()); 
		
		for (int i=0; i<taskDBean.length; i++) {
			
			LOG.info("Question Label: {}, uuid Question: {}", taskDBean[i].getQuestion_label(), taskDBean[i].getQuestion_id());
			
			Object[][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHist.getUuidFormHistory())},
								{Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(taskDBean[i].getQuestion_id()))}};
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
			
			MsQuestion msQuestion = new MsQuestion();
			msQuestion.setUuidQuestion(Long.parseLong(taskDBean[i].getQuestion_id()));
			msQuestion.setRefId(qset.getRefId());
			msQuestion.setQuestionLabel(qset.getQuestionLabel());
			msQuestion.setMsAnswertype(qset.getMsAnswertype());
			msQuestion.setMsAssettag(qset.getMsAssettag());
			msQuestion.setMsCollectiontag(qset.getMsCollectiontag());
			msQuestion.setMsOrdertag(qset.getMsOrdertag());
			msQuestion.setLovGroup(qset.getLovGroup());
			msQuestion.setIsVisible(qset.getIsVisible());
			msQuestion.setIsMandatory(qset.getIsMandatory());
			msQuestion.setIsReadonly(qset.getIsReadonly());
			msQuestion.setIsHolidayAllowed(qset.getIsHolidayAllowed());
			msQuestion.setMaxLength(qset.getMaxLength());
			msQuestion.setRegexPattern(qset.getRegexPattern());
			msQuestion.setAmMssubsystem(qset.getAmMssubsystem());
			msQuestion.setImgQlt(qset.getImgQlt());
			result.put(i, msQuestion);
		}
		return result;
	}
    
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String submitAssign(long uuidTaskH, String application, String notes, long uuidUserAssign, AuditContext callerId) {
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH h join fetch h.msStatustask join fetch h.msBranch where h.uuidTaskH = :uuidTaskH",
				new Object[][] {{"uuidTaskH", uuidTaskH}});
							
		AmMsuser usrAssign = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch join fetch u.msJob where u.uuidMsUser = :uuidMsUser",
				new Object[][] {{"uuidMsUser", uuidUserAssign}});
		
		if (!GlobalVal.SURVEY_STATUS_TASK_UNASSIGN.equals(trTaskH.getMsStatustask().getStatusCode())) {
			return this.messageSource.getMessage("businesslogic.global.datachanged", null, this.retrieveLocaleAudit(callerId));
		}			
		if ("0".equals(usrAssign.getIsActive())) {
			return this.messageSource.getMessage("businesslogic.assign.inactive", null, this.retrieveLocaleAudit(callerId));
		}
		if ("1".equals(usrAssign.getIsDeleted())) {
			return this.messageSource.getMessage("businesslogic.assign.deleted", null, this.retrieveLocaleAudit(callerId));
		}
		if ("0".equals(usrAssign.getMsJob().getIsFieldPerson())) {
			return this.messageSource.getMessage("businesslogic.assign.notfieldperson", null, this.retrieveLocaleAudit(callerId));
		}
		if (trTaskH.getMsBranch().getUuidBranch() != usrAssign.getMsBranch().getUuidBranch()) {
			return this.messageSource.getMessage("businesslogic.assign.diffbranch", null, this.retrieveLocaleAudit(callerId));
		}
			
		trTaskH.setUsrUpd(callerId.getCallerId());
		trTaskH.setDtmUpd(new Date());
		trTaskH.setAssignDate(new Date());
		trTaskH.setAmMsuser(usrAssign);
		
		//get status from workflow
		long uuidProcess = getUuidProcess(trTaskH, usrAssign.getAmMssubsystem());
		MsStatustask msStatustask = commitWf(trTaskH, uuidProcess, usrAssign.getAmMssubsystem(), 0);
		trTaskH.setMsStatustask(msStatustask);
		
		this.getManagerDAO().update(trTaskH);
		
		AmMsuser loginBean = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		
		//INSERT INTO HISTORY
		insertTaskHistory(callerId, trTaskH.getMsStatustask(), trTaskH, "Assigned from handset", 
				GlobalVal.CODE_PROCESS_ASSIGNMENT, usrAssign.getFullName());
		
		if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
			commitOrder(loginBean, notes, trTaskH, usrAssign.getAmMssubsystem(), 0, GlobalVal.CODE_PROCESS_ASSIGNMENT, callerId);
		}		

		return GlobalVal.SERVICES_RESULT_SUCCESS;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String submitReAssign(long uuidTaskH, String application, String notes, long uuidUserAssign, AuditContext callerId) {		
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH h join fetch h.msStatustask join fetch h.amMsuser where h.uuidTaskH = :uuidTaskH",
				new Object[][] {{"uuidTaskH", uuidTaskH}});		
		
		AmMsuser usrAssign = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch join fetch u.msJob where u.uuidMsUser = :uuidMsUser",
				new Object[][] {{"uuidMsUser", uuidUserAssign}});		
		
		if (!GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION.equals(trTaskH.getMsStatustask().getStatusCode())) {
			return this.messageSource.getMessage("businesslogic.global.datachanged", null, this.retrieveLocaleAudit(callerId));
		}
		if (uuidUserAssign == trTaskH.getAmMsuser().getUuidMsUser()) {
			return this.messageSource.getMessage("businesslogic.assign.sameuser", null, this.retrieveLocaleAudit(callerId));
		}
		if ("0".equals(usrAssign.getIsActive())) {
			return this.messageSource.getMessage("businesslogic.assign.inactive", null, this.retrieveLocaleAudit(callerId));
		}
		if ("1".equals(usrAssign.getIsDeleted())) {
			return this.messageSource.getMessage("businesslogic.assign.deleted", null, this.retrieveLocaleAudit(callerId));
		}
		if ("0".equals(usrAssign.getMsJob().getIsFieldPerson())) {
			return this.messageSource.getMessage("businesslogic.assign.notfieldperson", null, this.retrieveLocaleAudit(callerId));
		}
		if (!(trTaskH.getMsBranch().getUuidBranch() == usrAssign.getMsBranch().getUuidBranch())) {
			return this.messageSource.getMessage("businesslogic.assign.diffbranch", null, this.retrieveLocaleAudit(callerId));
		}		
		
		trTaskH.setUsrUpd(callerId.getCallerId());
		trTaskH.setDtmUpd(new Date());
		trTaskH.setAssignDate(new Date());
		trTaskH.setDownloadDate(null);
		trTaskH.setReadDate(null);
		trTaskH.setStartDtm(null);
		trTaskH.setAmMsuser(usrAssign);
		
		this.getManagerDAO().update(trTaskH);
		
		//INSERT INTO HISTORY
		insertTaskHistory(callerId, trTaskH.getMsStatustask(), trTaskH, "Reassigned from handset", 
				GlobalVal.CODE_PROCESS_REASSIGNMENT, usrAssign.getFullName());				

		return GlobalVal.SERVICES_RESULT_SUCCESS;
	}
	
	private void insertTaskRejectedDetail(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String optionAnswerId,
			String questionText, String textAnswer, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, String image, TrTaskrejectedhistory taskRejH, ImageStorageLocation isl) {		
		TrTaskrejecteddetail taskRejectDetail = new TrTaskrejecteddetail();

		taskRejectDetail.setTrTaskH(trTaskH);
		
		if (image != null) {
			taskRejectDetail.setImageBlob(BaseEncoding.base64().decode(image));
		}
		
		taskRejectDetail.setDtmCrt(new Date());
		taskRejectDetail.setUsrCrt(auditContext.getCallerId());
		taskRejectDetail.setQuestionText(questionText);
		if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())
				|| GlobalVal.ANSWER_TYPE_DATETIME.equals(msQuestion.getMsAnswertype().getCodeAnswerType())){
			if (textAnswer != null){
				DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
				try {
					Date result = df.parse(textAnswer);
					if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
						textAnswer = (null != result) 
								? new SimpleDateFormat(dateFormat).format(result)
								: null;
					} 
					else {
						textAnswer = (null != result) 
								? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result)
								: null;
					}
				} 
				catch (ParseException e) {
					LOG.error(e.getMessage(), e);
					e.printStackTrace();
				} 
			}
		}
		taskRejectDetail.setTextAnswer(textAnswer);
		taskRejectDetail.setLatitude(checkEmptyBigdecimal(latitude) );
		taskRejectDetail.setLongitude(checkEmptyBigdecimal(longitude) );
		
		if (taskRejectDetail.getLatitude()!=null && taskRejectDetail.getLongitude()!=null &&
				taskRejectDetail.getLatitude().intValue()!=0 && taskRejectDetail.getLongitude().intValue()!=0) {
			taskRejectDetail.setIsGps("1");
		}
		
		taskRejectDetail.setMcc(checkEmptyInteger(mcc));
		taskRejectDetail.setMnc(checkEmptyInteger(mnc));
		taskRejectDetail.setLac(checkEmptyInteger(lac));
		taskRejectDetail.setCellId(checkEmptyInteger(cellId));
		taskRejectDetail.setAccuracy(checkEmptyInteger(accuracy));
		taskRejectDetail.setTrTaskrejectedhistory(taskRejH);
		taskRejectDetail.setTimestampRejected(new Date());
		taskRejectDetail.setQuestionId(msQuestion.getUuidQuestion());
		
		MsLov msLovByLovId = null;
		if (StringUtils.isNotBlank(optionAnswerId)) {
			msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
		}
		if (msLovByLovId != null) {
			taskRejectDetail.setLovId(msLovByLovId.getUuidLov());
			taskRejectDetail.setOptionText(msLovByLovId.getDescription());
		}
		this.getManagerDAO().insert(taskRejectDetail);
		if (image != null) {
			taskRejectDetail.setImageBlob(BaseEncoding.base64().decode(image));
			
			if (ImageStorageLocation.DMS == isl) {
				UploadImageRequestBean request = new UploadImageRequestBean();
				UploadImageBean uploadBean  = new UploadImageBean();
				
				//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
				Object[][] paramImgLoc = { {Restrictions.eq(paramGsCode, GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
		        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
		        Object [][] param = {{"uuidTaskH", trTaskH.getTaskId()}};
				Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
						"SELECT	GROUP_TASK_ID " + 
						"FROM	MS_GROUPTASK with(nolock) " + 
						"WHERE	UUID_TASK_H = :uuidTaskH ", param);
				String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(trTaskH.getMsForm().getUuidForm()));
		        Date cutoffDate = null;
		        Date taskDate = null;
		        if(null != trTaskH.getSubmitDate()) {
		        	taskDate = trTaskH.getSubmitDate();
		        } else {
		        	taskDate = new Date();
		        }
				try {
					cutoffDate = new SimpleDateFormat(dateFormat).parse(gs.getGsValue());
				} catch (ParseException e1) {
					LOG.error(e1.getMessage(), e1);
					e1.printStackTrace();
				}
				if(taskDate.before(cutoffDate)) {
					uploadBean.setId(trTaskH.getTaskId());
					uploadBean.setTaskId(trTaskH.getTaskId());
				} else if (taskDate.after(cutoffDate)) {
					uploadBean.setId(groupTaskIdForm);
					uploadBean.setTaskId(groupTaskIdForm);
				}
		
				uploadBean.setType("survey");
				uploadBean.setJenisDoc(msQuestion.getQuestionLabel());
				uploadBean.setRefId(msQuestion.getRefId());
				uploadBean.setUsername(trTaskH.getAmMsuser().getUniqueId());
				if(StringUtils.isNotEmpty(image)){
					request.setByteImage(BaseEncoding.base64().decode(image));
				}
				request.setFileName(taskRejectDetail.getUuidTaskRejectedDetail()+".jpg");
				try {
					request.setIpAddress(InetAddress.getLocalHost().getHostAddress());
				} catch (UnknownHostException e) {
					LOG.error("Logging error DMS Host : {}",e.getMessage());
				}
				request.setUploadImageBean(uploadBean); 
				if(StringUtils.isNotEmpty(image)){
					intFormLogic.uploadImageResponse(auditContext,request);
				}
			}
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String submitPts(AuditContext auditContext, String application,
			SubmitTaskHBean taskHBean) {
		DateTime startDateTime = new DateTime();
		LOG.info("Reschedule(pts) task...");
		
		AmMsuser usr = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.amMssubsystem join fetch u.amMsuser join fetch u.msBranch where u.uuidMsUser = :uuidMsUser",
				new Object[][] {{"uuidMsUser", Long.valueOf(taskHBean.getUuid_user())}});
		
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH h left join fetch h.msStatustask join fetch h.amMsuser where h.uuidTaskH = :uuidTaskH",
				new Object[][] {{"uuidTaskH", Long.valueOf(taskHBean.getUuid_task_h())}});
		
		if (trTaskH == null) {
			throw new EntityNotFoundException(
					this.messageSource.getMessage("businesslogic.global.entitynotfound",
							new Object[]{"Task " + taskHBean.getUuid_task_h()}, this.retrieveLocaleAudit(auditContext)), taskHBean.getUuid_task_h());
		}
		
		TrTaskrejectedhistory taskRejected = this.validateRejected(trTaskH, usr, auditContext);
		boolean rejected = (taskRejected != null) ? true : false;
		
		if (rejected) {
			LOG.info("End of Reschedule(pts) task [REJECTED]: " + new DateTime().minusMillis(startDateTime.getMillisOfDay()).getMillisOfDay());
			return trTaskH.getTaskId();
		}		
		
		//START Update Table TR_TASK_H
		trTaskH.setUsrUpd(auditContext.getCallerId());
		trTaskH.setDtmUpd(new Date());
		trTaskH.setSubmitDate(new Date());
		trTaskH.setSendDate(taskHBean.getSubmitDate());
		if (null != taskHBean.getStartDtm()) {
			Date startDtm = taskHBean.getStartDtm();
			trTaskH.setStartDtm(startDtm);
		}
		if (null != taskHBean.getReadDtm()) {
			Date readDtm = taskHBean.getReadDtm();
			trTaskH.setReadDate(readDtm);
		}
		
		if (null != taskHBean.getPromisedDate()) {
			Date promisedDate = taskHBean.getPromisedDate();
			if (null == trTaskH.getPromiseDate()) {
				trTaskH.setPromiseDate(promisedDate);
			}
		}
		
		if (StringUtils.isBlank(trTaskH.getFlagSource())) {
			trTaskH.setFlagSource(GlobalVal.SUBSYSTEM_MS);
		}
		
		//get PTS Status
		MsStatustask msStatusPts = this.commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_PROMISE_TO_SURVEY,
				usr.getAmMssubsystem().getUuidMsSubsystem(), auditContext);
		trTaskH.setMsStatustask(msStatusPts);
		MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_SUBMITTED, auditContext);
		trTaskH.setMsStatusmobile(msm);
		this.getManagerDAO().update(trTaskH);
		
		TrTaskhistory trTaskHistory = new TrTaskhistory(trTaskH.getMsStatustask()
				, trTaskH, auditContext.getCallerId(), new Date()
				, trTaskH.getNotes(), usr.getFullName(), usr.getFullName(), GlobalVal.CODE_PROCESS_PROMISE_TO_SURVEY);
		this.getManagerDAO().insert(trTaskHistory);
		//Finish update task
		
		TrTaskH trTaskPts = this.saveTaskPts(trTaskH, usr, taskHBean.getPtsDate(), auditContext);
		
		long uuidProcess = getUuidProcess(trTaskH, usr.getAmMssubsystem());
		String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess, trTaskPts.getUuidTaskH());
		
		MsStatustask msStatustask = this.commonLogic.retrieveStatusTask(statusCode, usr.getAmMssubsystem().getUuidMsSubsystem(), auditContext);
		trTaskPts.setMsStatustask(msStatustask);
		trTaskPts.setTaskId(String.valueOf(trTaskPts.getUuidTaskH()));
		this.getManagerDAO().update(trTaskPts);
		
		//transfer taskHistory from previous to new pts task
		this.copyTaskhistoryNative(trTaskPts, trTaskH, auditContext);

		//Copy Detail
		boolean saveAsJson = PropertiesHelper.isTaskDJson();
		this.copyTaskDAsInitAnswerNative(trTaskPts, trTaskH, saveAsJson, auditContext);		
		
		//Copy Image
		this.copyTaskdetailLobAsInitAnswerNative(trTaskPts, trTaskH, auditContext);
		
		//check if Order
		if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
			Object[][] paramLink = { {Restrictions.eq("uuidTaskHSurvey", trTaskH.getUuidTaskH())} };
			TrTasklink trTaskLinkOrder = this.getManagerDAO().selectOne(TrTasklink.class, paramLink);
			
			TrTasklink trTaskLink = new TrTasklink();							
			trTaskLink.setUsrCrt(auditContext.getCallerId());
			trTaskLink.setDtmCrt(new Date());
			trTaskLink.setUuidTaskHOrder(trTaskLinkOrder.getUuidTaskHOrder());
			trTaskLink.setUuidTaskHSurvey(trTaskPts.getUuidTaskH());
			this.getManagerDAO().insert(trTaskLink);
		}
		
		//insert ms_grouptask
		Object[][] prevGroupParam = { {Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())} };
		MsGrouptask prevGroup = this.getManagerDAO().selectOne(MsGrouptask.class, prevGroupParam);
		
		MsGrouptask msGrouptask = new MsGrouptask();
		msGrouptask.setGroupTaskId(prevGroup.getGroupTaskId());
		msGrouptask.setTrTaskH(trTaskPts);
		msGrouptask.setMsBranch(trTaskPts.getMsBranch());
		msGrouptask.setUsrCrt(auditContext.getCallerId());
		msGrouptask.setDtmCrt(new Date());
		msGrouptask.setCustomerName(trTaskPts.getCustomerName());
		msGrouptask.setApplNo(trTaskPts.getApplNo());
		this.getManagerDAO().insert(msGrouptask);
		
		//insert PTS Hist
		TrTaskhistory trHistPts = new TrTaskhistory(trTaskPts.getMsStatustask()
				, trTaskPts, auditContext.getCallerId(), new Date()
				, trTaskPts.getNotes(), null, usr.getFullName(), GlobalVal.CODE_PROCESS_ASSIGNMENT);
		this.getManagerDAO().insert(trHistPts);
		//End Create PTS Task
		
		LOG.info("End of Reschedule(pts) task : " + new DateTime().minusMillis(startDateTime.getMillisOfDay()).getMillisOfDay());
		return trTaskH.getTaskId();
	}
	
	public void insertTaskDPts(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion,
			MsLov msLov, String textAnswer, String latitude, String longitude, String mcc,
			String mnc, String lac, String cellId, String accuracy){
		
		String questionText = msQuestion.getQuestionLabel();
		
		TrTaskD trTaskD = new TrTaskD();
		trTaskD.setUsrCrt(auditContext.getCallerId());
		trTaskD.setDtmCrt(new Date());
		trTaskD.setTrTaskH(trTaskH);
		trTaskD.setMsQuestion(msQuestion);
		trTaskD.setMsLovByIntLovId(msLov);
		if (msLov != null) {
			if (msLov.getDescription() != null){
				trTaskD.setIntOptionText(msLov.getDescription());
			}
		}
		trTaskD.setQuestionText(questionText);
		trTaskD.setIntTextAnswer(textAnswer);
		trTaskD.setLatitude(checkEmptyBigdecimal(latitude));
		trTaskD.setLongitude(checkEmptyBigdecimal(longitude));
		trTaskD.setMcc(checkEmptyInteger(mcc));
		trTaskD.setMnc(checkEmptyInteger(mnc));
		trTaskD.setLac(checkEmptyInteger(lac));
		cellId = (StringUtils.isBlank(cellId)) ? null : cellId;
		trTaskD.setCellId(checkEmptyInteger(cellId));
		trTaskD.setAccuracy(checkEmptyInteger(accuracy) );

		this.getManagerDAO().insert(trTaskD);
	}
	
	public void insertLobPts(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion,
			MsLov msLov, String textAnswer, String latitude, String longitude, String mcc,
			String mnc, String lac, String cellId, String accuracy, String imagePath, String isGps, String isGsm, 
			String geoProvider, String isVisibleQa, String lob){
		
		String questionText = msQuestion.getQuestionLabel();
		
		TrTaskdetaillob trTaskDetailLob = new TrTaskdetaillob();
		trTaskDetailLob.setUsrCrt(auditContext.getCallerId());
		trTaskDetailLob.setDtmCrt(new Date());
		trTaskDetailLob.setTrTaskH(trTaskH);
		trTaskDetailLob.setMsQuestion(msQuestion);
		trTaskDetailLob.setQuestionText(questionText);
		
		trTaskDetailLob.setMsLovByIntLovId(msLov);
		if (msLov != null) {
			if (msLov.getDescription() != null){
				trTaskDetailLob.setIntOptionText(msLov.getDescription());
			}
		}
		
		trTaskDetailLob.setIntTextAnswer(textAnswer);
		trTaskDetailLob.setLatitude(checkEmptyBigdecimal(latitude));
		trTaskDetailLob.setLongitude(checkEmptyBigdecimal(longitude));
		trTaskDetailLob.setMcc(checkEmptyInteger(mcc));
		trTaskDetailLob.setMnc(checkEmptyInteger(mnc));
		trTaskDetailLob.setLac(checkEmptyInteger(lac));
		cellId = (StringUtils.isBlank(cellId)) ? null : cellId;
		trTaskDetailLob.setCellId(checkEmptyInteger(cellId));
		trTaskDetailLob.setAccuracy(checkEmptyInteger(accuracy) );
		trTaskDetailLob.setImagePath(imagePath);
		trTaskDetailLob.setIsGps(isGps);
		trTaskDetailLob.setIsGsm(isGsm);
		trTaskDetailLob.setGeolocationProvider(geoProvider);
		trTaskDetailLob.setIsVisibleQa(isVisibleQa);
		if (lob != null && !lob.isEmpty()){
			trTaskDetailLob.setLobFile(BaseEncoding.base64().decode(lob));
		}
		this.getManagerDAO().insert(trTaskDetailLob);
	}
	
	@Transactional
    @Override
    public List<LocationBean> listEmptyCoordinate(AuditContext callerId) {
       List<Map<String, Object>> queryResult = this.getManagerDAO().selectAllNative("common.locationhistory.listTaskLobEmptyCoordinate", null, null);
       if (queryResult == null || queryResult.isEmpty()) {
           return Collections.emptyList();
       }
       
       List<LocationBean> locations = new ArrayList<>();
       for (Map<String, Object> record : queryResult) {
           LocationBean locationBean = new LocationBean();
           locationBean.setMcc((Integer) record.get("d0"));
           locationBean.setMnc((Integer) record.get("d1"));
           locationBean.setLac((Integer) record.get("d2"));
           locationBean.setCellid(Integer.parseInt((String)record.get("d3")));
           locations.add(locationBean);
       }
       
       return locations;
    }
	
	private LocationBean getLocationByCellId(int mcc, int mnc, int lac, int cellId,
			AuditContext auditContext) {
		List<LocationBean> listLocations = new ArrayList<LocationBean>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(cellId);
		locationBean.setLac(lac);
		locationBean.setMnc(mnc);
		locationBean.setMcc(mcc);
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);
		return locationBean;
	}
	
	private void getLocationByCellId(TrTaskD taskD, AuditContext auditContext){
		List<LocationBean> listLocations = new ArrayList<LocationBean>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(taskD.getCellId());
		locationBean.setLac(taskD.getLac().intValue());
		locationBean.setMcc(taskD.getMcc().intValue());
		locationBean.setMnc(taskD.getMnc().intValue());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			taskD.setLatitude(new BigDecimal(locationBean.getCoordinate()
					.getLatitude()));
			taskD.setLongitude(new BigDecimal(locationBean.getCoordinate()
					.getLongitude()));
			taskD.setAccuracy(new Integer(locationBean.getAccuracy()));
			
			taskD.setTextAnswer("Coord : "+taskD.getLatitude().setScale(6, RoundingMode.HALF_UP)+", "+taskD.getLongitude().setScale(6, RoundingMode.HALF_UP)+" Accuracy : "+taskD.getAccuracy()+" m");
			taskD.setIsConverted("1");
		}
	}
	
	private void getLocationByCellId(TrTaskdetaillob detailLob, AuditContext auditContext){
		List<LocationBean> listLocations = new ArrayList<LocationBean>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(detailLob.getCellId());
		locationBean.setLac(detailLob.getLac().intValue());
		locationBean.setMcc(detailLob.getMcc().intValue());
		locationBean.setMnc(detailLob.getMnc().intValue());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			detailLob.setLatitude(new BigDecimal(locationBean.getCoordinate()
					.getLatitude()));
			detailLob.setLongitude(new BigDecimal(locationBean.getCoordinate()
					.getLongitude()));
			detailLob.setAccuracy(new Integer(locationBean.getAccuracy()));
			
			detailLob.setTextAnswer("Coord : "+detailLob.getLatitude().setScale(6, RoundingMode.HALF_UP)+", "+detailLob.getLongitude().setScale(6, RoundingMode.HALF_UP)+" Accuracy : "+detailLob.getAccuracy()+" m");
			detailLob.setIsConverted("1");
		}
	}
	
	private List<TrTaskD> listTaskD(String uuidTaskH) {
		if (StringUtils.isBlank(uuidTaskH)) {
			return Collections.emptyList();
		}
		
		Object[][] params = { {Restrictions.eq("trTaskH.uuidTaskH", Long.valueOf(uuidTaskH))} };
		Map<String, Object> result = this.getManagerDAO().list(TrTaskD.class, params, null);
		
		return (List<TrTaskD>) result.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	private Map<Long, TrTaskD> listTaskDAsMap(String uuidTaskH) {
		List<TrTaskD> result = this.listTaskD(uuidTaskH);
		
		if (result == null || result.isEmpty()){
			return Collections.emptyMap();
		}
		
		Map<Long, TrTaskD> resultMap = new HashMap<>(result.size());
		for (Iterator iterator = result.iterator(); iterator.hasNext();) {
			TrTaskD trTaskD = (TrTaskD) iterator.next();
			resultMap.put(trTaskD.getMsQuestion().getUuidQuestion(), trTaskD);
		}
		
		return resultMap;
	}
	
	@Transactional(readOnly=true)
	@Override
	@Async
	public void sendMessageToJms(AuditContext auditContext, String application,
			 SubmitTaskDBean taskDBean[], String taskId) {
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(auditContext.getCallerId()));
		Object[][] params = {{Restrictions.eq(paramTaskId, taskId)}};
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, params);
		boolean sendLob = false;
		boolean sendDetail = false;
		String isFinal = StringUtils.EMPTY;
		
		for (int i=0; i < taskDBean.length; i++) {
			isFinal = taskDBean[i].getIs_final();
			if (StringUtils.isNotBlank(taskDBean[i].getImage())) {										
				sendLob = true;
			} 
			else {
				sendDetail = true;
			}
		}
		
		//SSE
		MessageEventBean msgBean = new MessageEventBean();
		msgBean.setEvent(GlobalVal.EVENT_NEWS);
		msgBean.setUuidUser(usr.getImei()+usr.getUuidMsUser());
		if (GlobalVal.SUBSYSTEM_MC.equals(application)) {
			Object[] msgArgs = {usr.getLoginId(), usr.getFullName(), (trTaskH.getAgreementNo()!=null?trTaskH.getAgreementNo():trTaskH.getTaskId()), trTaskH.getCustomerName()};
			if (sendLob && !sendDetail){
				msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.uploadimage", msgArgs, this.retrieveLocaleAudit(auditContext)));
			} 
			else {
				msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.submittask", msgArgs, this.retrieveLocaleAudit(auditContext)));				
			}
		} 
		else {
			Object[] msgArgs = {usr.getLoginId(), usr.getFullName(), (trTaskH.getApplNo()!=null?trTaskH.getApplNo():trTaskH.getTaskId()), trTaskH.getCustomerName()};
			if (sendLob && !sendDetail){
				msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.uploadimage", msgArgs, this.retrieveLocaleAudit(auditContext)));
			} 
			else {
				msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.submittask", msgArgs, this.retrieveLocaleAudit(auditContext)));
			}
		}
		
		if (isFinal != null && "1".equals(isFinal)){
			msgBean.setTask("SUBMIT");
			msgBean.setFullName(usr.getFullName());
			msgBean.setCustomerName(trTaskH.getCustomerName());
			msgBean.setTaskId(trTaskH.getTaskId());
			msgBean.setLatitude(trTaskH.getSubmitLatitude()+"");
			msgBean.setLongitude(trTaskH.getSubmitLongitude()+"");
		}
		
		msgBean.setDate(DateFormatUtils.format(new Date(), "HH:mm:ss"));
		msgBean.setUuidMessageBean(this.getManagerDAO().getUUID());

	
		Gson gson = new Gson();

		try{
			sendMessage(gson.toJson(msgBean));
			LOG.info("Send message to JMS Success");
		}
		catch(Exception e){
			LOG.error("Send message to JMS Failed", e);
		}
	}

	private class SaveTaskDResult {
		private String isFinal;
		private String totalBayar;
		private String poloNegCust;
		private String poloInterest;
		private String poloVisitDate;
		private String poloInputDate;
		private String poloSpv;
		private String poloRef2Code;
		private String poloRef2Name;
		private String poloRefCode;
		private String poloRefName;
		private String isCancelApp;
		private String isCmoRecommendation;
		private String poloNotesTaskVisit;
		private String poloNotesCrm;
		private String poloProspect;
		private String poloDkcpCust;
		private String poloNegListCust;
		private String poloRef3Code;
		private String poloRef3Name;

		private String poloDkcpPsgn;
		private String poloNegListPsgn;
		private String poloDkcpGrtr;
		private String poloNegListGrtr;
		public String nik;
		public String custName;
		public String phone;
		public String birthPlace;
		public String birthDate;
		public String motherName;
		public String address;
		public String rt;
		public String rw;
		public String province;
		public String city;
		public String kecamatan;
		public String kelurahan;
		public String zipCode;
		public String subZipCode;
		public String prodCat;
		public String prodOff;
		public String regionName;
		public String officeCode;
		public String custId;
		public String contractNo;
		public String soa;
		public String ptvDate;

		public SaveTaskDResult(String isFinal, String totalBayar, String poloNegCust, String poloInterest, String poloInputDate, String poloVisitDate,
				String poloSpv, String poloRefCode, String poloRefName, String poloRef2Code, String poloRef2Name, String isCancelApp, String isCmoRecommendation,
				String poloNotesTaskVisit, String poloNotesCrm, String poloProspect, String poloDkcp, String poloNegListCust, String poloRef3Code, String poloRef3Name,
				String poloDkcpPsgn, String poloNegListPsgn, String poloDkcpGrtr, String poloNegListGrtr, String nik, String custName, String phone, String birthPlace, 
				String birthDate, String motherName, String address, String rt, String rw, String province, String city, String kecamatan, String kelurahan, String zipCode, 
				String subZipCode, String prodCat, String prodOff, String regionName, String officeCode, String custId, String contractNo, String soa, String ptvDate) {
			super();
			this.isFinal = isFinal;
			this.totalBayar = totalBayar;
			this.poloNegCust = poloNegCust;
			this.poloInterest = poloInterest;
			this.poloInputDate = poloInputDate;
			this.poloVisitDate = poloVisitDate;
			this.poloSpv = poloSpv;
			this.poloRefCode = poloRefCode;
			this.poloRefName = poloRefName;
			this.poloRef2Code = poloRef2Code;
			this.poloRef2Name = poloRef2Name;
			this.poloRef3Code = poloRef3Code;
			this.poloRef3Name = poloRef3Name;
			this.isCancelApp = isCancelApp;
			this.isCmoRecommendation = isCmoRecommendation;
			this.poloNotesTaskVisit = poloNotesTaskVisit;
			this.poloNotesCrm = poloNotesCrm;
			this.poloProspect = poloProspect;
			this.poloDkcpCust = poloDkcp;
			this.poloNegListCust = poloNegListCust;
			this.poloDkcpPsgn = poloDkcpPsgn;
			this.poloNegListPsgn = poloNegListPsgn;
			this.poloDkcpGrtr = poloDkcpGrtr;
			this.poloNegListGrtr = poloNegListGrtr;
			this.nik = nik;
			this.custName = custName;
			this.phone = phone;
			this.birthPlace = birthPlace;
			this.birthDate = birthDate;
			this.motherName = motherName;
			this.address = address;
			this.rt = rt;
			this.rw = rw;
			this.province = province;
			this.city = city;
			this.kecamatan = kecamatan;
			this.kelurahan = kelurahan;
			this.zipCode = zipCode;
			this.subZipCode = subZipCode;
			this.prodCat = prodCat;
			this.prodOff = prodOff;
			this.regionName = regionName;
			this.officeCode = officeCode;
			this.custId = custId;
			this.contractNo = contractNo;
			this.soa = soa;
			this.ptvDate = ptvDate;
			
		}
		
		public String getIsFinal() {
			return isFinal;
		}
		public String getTotalBayar() {
			return totalBayar;
		}

		public String getPoloNegCust() {
			return poloNegCust;
		}

		public String getPoloInterest() {
			return poloInterest;
		}
		
		public String getPoloVisitDate() {
			return poloVisitDate;
		}
		
		public String getPoloInputDate() {
			return poloInputDate;
		}
		
		public String getPoloSpv() {
			return poloSpv;
		}

		public String getPoloRef2Code() {
			return poloRef2Code;
		}

		public String getPoloRef2Name() {
			return poloRef2Name;
		}

		public String getPoloRefCode() {
			return poloRefCode;
		}

		public String getPoloRefName() {
			return poloRefName;
		}

		public String getIsCancelApp() {
			return isCancelApp;
		}

		public void setIsCancelApp(String isCancelApp) {
			this.isCancelApp = isCancelApp;
		}

		public String getIsCmoRecommendation() {
			return isCmoRecommendation;
		}

		public void setIsCmoRecommendation(String isCmoRecommendation) {
			this.isCmoRecommendation = isCmoRecommendation;
		}
		
		public String getPoloNotesTaskVisit() {
			return poloNotesTaskVisit;
		}

		public void setPoloNotesTaskVisit(String poloNotesTaskVisit) {
			this.poloNotesTaskVisit = poloNotesTaskVisit;
		}
		
		public String getPoloNotesCrm() {
			return poloNotesCrm;
		}

		public void setPoloNotesCrm(String poloNotesCrm) {
			this.poloNotesCrm = poloNotesCrm;
		}

		public String getPoloProspect() {
			return poloProspect;
		}

		public void setPoloProspect(String poloProspect) {
			this.poloProspect = poloProspect;
		}
		
		public String getPoloDkcpCust() {
			return poloDkcpCust;
		} 
		public void setPoloDkcpCust(String poloDkcpCust) {
			this.poloDkcpCust = poloDkcpCust;
		} 
		public String getPoloNegListCust() {
			return poloNegListCust;
		} 
		public void setPoloNegListCust(String poloNegListCust) {
			this.poloNegListCust = poloNegListCust;
		}

		public String getPoloDkcpPsgn() {
			return poloDkcpPsgn;
		} 
		public void setPoloDkcpPsgn(String poloDkcpPsgn) {
			this.poloDkcpPsgn = poloDkcpPsgn;
		} 
		public String getPoloNegListPsgn() {
			return poloNegListPsgn;
		} 
		public void setPoloNegListPsgn(String poloNegListPsgn) {
			this.poloNegListPsgn = poloNegListPsgn;
		}
		
		public String getPoloDkcpGrtr() {
			return poloDkcpGrtr;
		} 
		public void setPoloDkcpGrtr(String poloDkcpGrtr) {
			this.poloDkcpGrtr = poloDkcpGrtr;
		} 
		public String getPoloNegListGrtr() {
			return poloNegListGrtr;
		} 
		public void setPoloNegListGrtr(String poloNegListGrtr) {
			this.poloNegListGrtr = poloNegListGrtr;
		}

		public String getPoloRef3Code() {
			return poloRef3Code;
		}
		
		public String getPoloRef3Name() {
			return poloRef3Name;
		}

		public String getNik() {
			return nik;
		}

		public void setNik(String nik) {
			this.nik = nik;
		}

		public String getCustName() {
			return custName;
		}

		public void setCustName(String custName) {
			this.custName = custName;
		}

		public String getPhone() {
			return phone;
		}

		public void setPhone(String phone) {
			this.phone = phone;
		}

		public String getBirthPlace() {
			return birthPlace;
		}

		public void setBirthPlace(String birthPlace) {
			this.birthPlace = birthPlace;
		}

		public String getBirthDate() {
			return birthDate;
		}

		public void setBirthDate(String birthDate) {
			this.birthDate = birthDate;
		}

		public String getMotherName() {
			return motherName;
		}

		public void setMotherName(String motherName) {
			this.motherName = motherName;
		}

		public String getAddress() {
			return address;
		}

		public void setAddress(String address) {
			this.address = address;
		}

		public String getRt() {
			return rt;
		}

		public void setRt(String rt) {
			this.rt = rt;
		}

		public String getRw() {
			return rw;
		}

		public void setRw(String rw) {
			this.rw = rw;
		}

		public String getProvince() {
			return province;
		}

		public void setProvince(String province) {
			this.province = province;
		}

		public String getCity() {
			return city;
		}

		public void setCity(String city) {
			this.city = city;
		}

		public String getKecamatan() {
			return kecamatan;
		}

		public void setKecamatan(String kecamatan) {
			this.kecamatan = kecamatan;
		}

		public String getKelurahan() {
			return kelurahan;
		}

		public void setKelurahan(String kelurahan) {
			this.kelurahan = kelurahan;
		}

		public String getZipCode() {
			return zipCode;
		}

		public void setZipCode(String zipCode) {
			this.zipCode = zipCode;
		}

		public String getSubZipCode() {
			return subZipCode;
		}

		public void setSubZipCode(String subZipCode) {
			this.subZipCode = subZipCode;
		}

		public String getProdCat() {
			return prodCat;
		}

		public void setProdCat(String prodCat) {
			this.prodCat = prodCat;
		}

		public String getProdOff() {
			return prodOff;
		}

		public void setProdOff(String prodOff) {
			this.prodOff = prodOff;
		}

		public String getRegionName() {
			return regionName;
		}

		public void setRegionName(String regionName) {
			this.regionName = regionName;
		}

		public String getOfficeCode() {
			return officeCode;
		}

		public void setOfficeCode(String officeCode) {
			this.officeCode = officeCode;
		}

		public String getCustId() {
			return custId;
		}

		public void setCustId(String custId) {
			this.custId = custId;
		}

		public String getContractNo() {
			return contractNo;
		}

		public void setContractNo(String contractNo) {
			this.contractNo = contractNo;
		}

		public String getSoa() {
			return soa;
		}

		public void setSoa(String soa) {
			this.soa = soa;
		}

		public void setIsFinal(String isFinal) {
			this.isFinal = isFinal;
		}

		public void setTotalBayar(String totalBayar) {
			this.totalBayar = totalBayar;
		}

		public void setPoloNegCust(String poloNegCust) {
			this.poloNegCust = poloNegCust;
		}

		public void setPoloInterest(String poloInterest) {
			this.poloInterest = poloInterest;
		}

		public void setPoloVisitDate(String poloVisitDate) {
			this.poloVisitDate = poloVisitDate;
		}

		public void setPoloInputDate(String poloInputDate) {
			this.poloInputDate = poloInputDate;
		}

		public void setPoloSpv(String poloSpv) {
			this.poloSpv = poloSpv;
		}

		public void setPoloRef2Code(String poloRef2Code) {
			this.poloRef2Code = poloRef2Code;
		}

		public void setPoloRef2Name(String poloRef2Name) {
			this.poloRef2Name = poloRef2Name;
		}

		public void setPoloRefCode(String poloRefCode) {
			this.poloRefCode = poloRefCode;
		}

		public void setPoloRefName(String poloRefName) {
			this.poloRefName = poloRefName;
		}

		public void setPoloRef3Code(String poloRef3Code) {
			this.poloRef3Code = poloRef3Code;
		}

		public void setPoloRef3Name(String poloRef3Name) {
			this.poloRef3Name = poloRef3Name;
		}
		public void setPtvDate(String ptvDate) {
			this.ptvDate = ptvDate;
		}

		public String getPtvDate() {
			return ptvDate;
		}
		
		

	}
	
	private void validateDeviceId(AmMsuser usr, String imei, String androidId, AuditContext auditContext) {
		AmGeneralsetting beanGeneral = this.getManagerDAO().selectOne(AmGeneralsetting.class,
				new Object[][] { { Restrictions.eq(paramGsCode, usr.getAmMssubsystem().getSubsystemName() + GlobalKey.GENERALSETTING_MODE_LOGIN_SUBMIT) } });
		if (beanGeneral.getGsValue() == null || "".equals(beanGeneral.getGsValue())) {
			beanGeneral.setGsValue(GlobalVal.MODE_IMEI);
		}
		
		if (GlobalVal.MODE_IMEI.equalsIgnoreCase(beanGeneral.getGsValue())) { // jika MODE_SUBMIT == IMEI
			if (imei != null && !imei.equals(usr.getImei()) && !imei.equals(usr.getImei2())) {
				throw new UserLoginIdNotExistsException(this.messageSource.getMessage(
						"businesslogic.global.imeinotvalid", new Object[] { imei }, this.retrieveLocaleAudit(auditContext)));				
			}
		} 
		else if (GlobalVal.MODE_ANDROID_ID.equalsIgnoreCase(beanGeneral.getGsValue())) { // jika MODE_SUBMIT = ANDROID_ID			
			if (androidId != null &&
					(usr.getAndroidId() == null || !usr.getAndroidId().equals(androidId))) {
				throw new UserLoginIdNotExistsException(this.messageSource.getMessage(
						"businesslogic.global.androididnotvalid", null, this.retrieveLocaleAudit(auditContext)));				
			}
		}
	}
	
	private void saveGroupTaskSurvey(TrTaskH taskHSurvey, AuditContext auditContext) {
		MsGrouptask msGrouptask = new MsGrouptask();
		msGrouptask.setGroupTaskId(taskHSurvey.getUuidTaskH());
		msGrouptask.setTrTaskH(taskHSurvey);
		msGrouptask.setMsBranch(taskHSurvey.getMsBranch());
		msGrouptask.setUsrCrt(auditContext.getCallerId());
		msGrouptask.setDtmCrt(new Date());
		msGrouptask.setCustomerName(taskHSurvey.getCustomerName());
		msGrouptask.setApplNo(taskHSurvey.getApplNo());
		this.getManagerDAO().insert(msGrouptask);
	}
	
	@Override
	public void saveGroupTaskSurveyByExistingTaskid(TrTaskH taskHSurvey, long groupTaskId, AuditContext auditContext) {
		MsGrouptask msGrouptask = new MsGrouptask();
		msGrouptask.setGroupTaskId(groupTaskId);
		msGrouptask.setTrTaskH(taskHSurvey);
		msGrouptask.setMsBranch(taskHSurvey.getMsBranch());
		msGrouptask.setUsrCrt(auditContext.getCallerId());
		msGrouptask.setDtmCrt(new Date());
		msGrouptask.setCustomerName(taskHSurvey.getCustomerName());
		msGrouptask.setApplNo(taskHSurvey.getApplNo());
		this.getManagerDAO().insert(msGrouptask);
	}
	
	private TrTaskrejectedhistory validateRejected(TrTaskH trTaskH, AmMsuser userSubmit, AuditContext auditContext) {
		if (!String.valueOf(userSubmit.getUuidMsUser()).equals(String.valueOf(trTaskH.getAmMsuser().getUuidMsUser())) || //beda user
				(GlobalVal.SURVEY_STATUS_TASK_DELETED.equals(trTaskH.getMsStatustask().getStatusCode()))) { //sudah deleted
			 
			Object[][] paramTaskRejectedHist = { {Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
					{Restrictions.eq("amMsuser.uuidMsUser", userSubmit.getUuidMsUser())} };
			//FIX: jadi Tidak ada select existing rejected by user, karena user yg telah direject, masih bisa diassign
			String[][] orders = {{"uuidTaskRejectedHistory", GlobalVal.ROW_ORDER_DESC}};
			
			Map<String, Object> resultMap = this.getManagerDAO().list(TrTaskrejectedhistory.class, paramTaskRejectedHist, orders);
			List<TrTaskrejectedhistory> listRejected = (List<TrTaskrejectedhistory>) resultMap.get(GlobalKey.MAP_RESULT_LIST); 
						
			if (listRejected.isEmpty() || listRejected.get(0).getAmMsuser().getUuidMsUser() != userSubmit.getUuidMsUser()) {
				TrTaskrejectedhistory taskRejected = new TrTaskrejectedhistory();
				taskRejected.setAmMsuser(userSubmit);
				taskRejected.setDtmCrt(new Date());
				taskRejected.setTrTaskH(trTaskH);
				taskRejected.setReasonCode(GlobalVal.CODE_PROCESS_REJECTED);
				
				this.getManagerDAO().insert(taskRejected);
				insertTaskHistory(auditContext, trTaskH.getMsStatustask(), trTaskH, "Task from " + trTaskH.getFlagSource() + " has been rejected.", 
						GlobalVal.CODE_PROCESS_REJECTED, userSubmit.getFullName(), userSubmit, taskRejected);
				
				return taskRejected;
			}
			else {
				return listRejected.get(0);
			}			
		}
		
		return null;
	}
	
	private void saveOrUpdateAgreementCollectLocation(TrTaskH trTaskH, AmMsuser userSubmit, String latitude, String longitude) {
		Object[][] paramAgrNo = { { Restrictions.eq("agreementNo", trTaskH.getAgreementNo()) } };
		MsAgreementcollectlocation macl = this.getManagerDAO().selectOne(MsAgreementcollectlocation.class, paramAgrNo);
		if (macl != null) {
			//update data
			macl.setLatitude(BigDecimal.valueOf(Double.valueOf(latitude)));
			macl.setLongitude(BigDecimal.valueOf(Double.valueOf(longitude)));
			macl.setDtmUpd(new Date());
			macl.setUsrUpd(String.valueOf(userSubmit.getUuidMsUser()));
			this.getManagerDAO().update(macl);
		} 
		else {
			MsAgreementcollectlocation addLoc = new MsAgreementcollectlocation();
			addLoc.setAgreementNo(trTaskH.getAgreementNo());
			addLoc.setCustomerName(trTaskH.getCustomerName());
			addLoc.setUsrCrt(String.valueOf(userSubmit.getUuidMsUser()));
			addLoc.setDtmCrt(new Date());
			addLoc.setLatitude(BigDecimal.valueOf(Double.valueOf(latitude)));
			addLoc.setLongitude(BigDecimal.valueOf(Double.valueOf(longitude)));
			this.getManagerDAO().insert(addLoc);
		}
	}
	
	private void updateUsedRvNumber(TrTaskH trTaskH, SubmitTaskDBean taskDBean, AuditContext auditContext) {
		String rvNumber = taskDBean.getOption_answer_id();
		Object params[][] = {{Restrictions.eq("amMsuser.uuidMsUser", Long.valueOf(auditContext.getCallerId()))}, {Restrictions.eq("uuidRvNumber", rvNumber)}};
		TrRvnumber trRv = this.getManagerDAO().selectOne(TrRvnumber.class, params);
		//KNOWN ISSUES - ON NEXT DAY, ASSIGNED RV (DELETE AND ADD NEW), RV WILL NOT BE FOUND, ON MOBILE-AUTOSUBMIT NEXT DAY 
		if (null == trRv) {
			throw new EntityNotFoundException(this.messageSource.getMessage(
					"businesslogic.global.rvnotfound", null, this.retrieveLocaleAudit(auditContext)), rvNumber);
		}
		else if (GlobalVal.STATUS_RV_NUMBER_USED.equals(trRv.getStatusRv())) {
			throw new SubmitTaskException(this.messageSource.getMessage(
            		"businesslogic.global.rvalreadyused", null, this.retrieveLocaleAudit(auditContext)), Reason.RV_USED);
		}
		
		trTaskH.setRvNumber(trRv.getRvNumber());
		trTaskH.setUsrUpd(auditContext.getCallerId());
		trTaskH.setDtmUpd(new Date());
		this.getManagerDAO().update(trTaskH);
		
		trRv.setDtmUse(new Date());
		trRv.setStatusRv(GlobalVal.STATUS_RV_NUMBER_USED);
		this.getManagerDAO().update(trRv);
	}
	
	private boolean saveRevisit(SubmitTaskHBean taskHBean, TrTaskH trTaskH, AuditContext auditContext) {
	Map<String, Object> resultMap = this.getManagerDAO().selectForMapString(
				"select AGREEMENT_NO as agreementNo, ASSIGN_DATE as assignDate from TR_TASK_H where TASK_ID = :taskId",
				new Object[][]{ {paramTaskId, taskHBean.getTask_id()} } );
							
		if (resultMap != null) {
			String agreementNo = (String) resultMap.get("agreementNo");
			trTaskH.setAgreementNo(agreementNo);
			this.getManagerDAO().update(trTaskH);
			//insert ms_grouptask
			Date date = (Date) resultMap.get("assignDate");
			
			String startDate = DateFormatUtils.format(date, "yyyy-MM-dd") + " 00:00:00.000";
			String endDate = DateFormatUtils.format(date, "yyyy-MM-dd") + " 23:59:59.997";
									
			String[][] params = { {"agreeNo", agreementNo}, 
								  {"start", startDate},
								  {"end", endDate} };
			
			BigInteger groupTaskId = (BigInteger) this.getManagerDAO()
					.selectOneNativeString("select TOP 1 msg.GROUP_TASK_ID from MS_GROUPTASK msg"
							+ " inner join TR_TASK_H tth on tth.UUID_TASK_H = msg.UUID_TASK_H"
							+ " where tth.AGREEMENT_NO = :agreeNo"
							+ " and tth.ASSIGN_DATE between :start and :end"
							+ " UNION "
							+ " select TOP 1 msg.GROUP_TASK_ID from MS_GROUPTASK msg"
							+ " inner join FINAL_TR_TASK_H tth on tth.UUID_TASK_H = msg.UUID_TASK_H"
							+ " where tth.AGREEMENT_NO = :agreeNo"
							+ " and tth.ASSIGN_DATE between :start and :end", params);
			
			MsGrouptask msGrouptask = new MsGrouptask();
			if (groupTaskId == null) {
				msGrouptask.setGroupTaskId(trTaskH.getUuidTaskH());		
			} 
			else {
				msGrouptask.setGroupTaskId(groupTaskId.longValue());
			}
			msGrouptask.setTrTaskH(trTaskH);
			msGrouptask.setMsBranch(trTaskH.getMsBranch());
			msGrouptask.setUsrCrt(auditContext.getCallerId());
			msGrouptask.setDtmCrt(new Date());
			msGrouptask.setCustomerName(trTaskH.getCustomerName());
			msGrouptask.setApplNo(trTaskH.getApplNo());
			this.getManagerDAO().insert(msGrouptask);							
			
			Object[][] groupId = { {paramGroupTaskId, groupTaskId} };
			BigInteger uuidH = (BigInteger) this.getManagerDAO().selectOneNativeString(
					"select msg.UUID_TASK_H from MS_GROUPTASK msg"
							+ " inner join TR_TASK_H tth on tth.UUID_TASK_H = msg.UUID_TASK_H"
							+ " where msg.GROUP_TASK_ID = :groupTaskId and tth.ASSIGN_DATE IS NOT NULL", groupId);
			
			TrTaskH taskHpertama = this.getManagerDAO().selectOne(
					"from TrTaskH h join fetch h.amMsuser where h.uuidTaskH = :uuidTaskH", new Object[][]{{"uuidTaskH", uuidH.longValue()}});						
			AmMsuser msUser = this.getManagerDAO().selectOne(AmMsuser.class, taskHpertama.getAmMsuser().getUuidMsUser());	
			
			//INSERT INTO HISTORY
			insertTaskHistory(auditContext, taskHpertama.getMsStatustask(), taskHpertama, "Revisit from handset", 
					GlobalVal.CODE_PROCESS_REVISIT, msUser.getFullName());
			
			return true;
		}
		
		return false;
	}
	
	private SaveTaskDResult saveTaskDRejected(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, 
			TrTaskrejectedhistory rejectHistory, Map<Integer, MsQuestion> msQuestions, ImageStorageLocation isl, AuditContext auditContext) {
		String isFinal = "0";
		
		for (int i=0; i < taskDBean.length; i++) {
			isFinal = taskDBean[i].getIs_final();
			
			MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
			String answerTypeCode = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String uuidTaskD = taskDBean[i].getUuid_task_d();
			String optionAnswerId = StringUtils.stripToEmpty(taskDBean[i].getOption_answer_id());
			String questionText = StringUtils.stripToEmpty(taskDBean[i].getQuestion_label());
			String textAnswer = StringUtils.stripToEmpty(taskDBean[i].getText_answer());
			String latitude = StringUtils.stripToNull(taskDBean[i].getLatitude());						
			String longitude = StringUtils.stripToNull(taskDBean[i].getLongitude());
			String mcc = StringUtils.stripToNull(taskDBean[i].getMcc());
			String mnc = StringUtils.stripToNull(taskDBean[i].getMnc());
			String lac = StringUtils.stripToNull(taskDBean[i].getLac());				
			String cellId = StringUtils.stripToNull(taskDBean[i].getCid());				
			String accuracy = StringUtils.stripToNull(taskDBean[i].getAccuracy());	
			String image = StringUtils.stripToNull(taskDBean[i].getImage());
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerTypeCode) &&
					StringUtils.isNotEmpty(textAnswer) && StringUtils.containsAny(textAnswer, ',', '.')) {
				textAnswer = textAnswer.substring(0, textAnswer.length()-2).replace(",", "").replace(".", "");				
			}
			
			this.insertTaskRejectedDetail(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, 
					textAnswer, latitude, longitude, mcc, mnc, lac, cellId, accuracy, image, rejectHistory, isl);			
		}
		
		return new SaveTaskDResult(isFinal, "0", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "","", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "");
	}
	
	private SaveTaskDResult saveTaskDIntoJson(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, AmMsuser userSubmit, boolean flagRevisit,
			TrTaskrejectedhistory rejectHistory, boolean flag,
			Map<Integer, MsQuestion> msQuestions, ImageStorageLocation isl, Path imagePath, long groupTaskId, AuditContext auditContext) {
		String isFinal = "0";
		String totalBayar = "0";
		
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		TaskDocumentBean document = null;
		boolean newDoc = false;
		if (docDb == null) {
			docDb = new TrTaskdocument(trTaskH, new Date(), auditContext.getCallerId());
			document = new TaskDocumentBean();
			newDoc = true;
		}
		else {
			document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
			docDb.setDtmUpd(new Date());
			docDb.setUsrUpd(auditContext.getCallerId());
			newDoc = false;
		}
		
		List<AnswerBean> answers = (newDoc) ? new ArrayList<AnswerBean>(taskDBean.length) : document.getAnswers();		
		if (newDoc) {
			document.setAnswers(answers);
		}
		
		TrTaskcolldata taskCollData = this.getManagerDAO().selectOne(TrTaskcolldata.class, trTaskH.getUuidTaskH());
		if (taskCollData == null){
			taskCollData = new TrTaskcolldata();
			taskCollData.setTrTaskH(trTaskH);
		}
		for (int i=0; i < taskDBean.length; i++) {
			isFinal = taskDBean[i].getIs_final();
			
			MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
			String answerTypeCode = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String optionAnswerId = StringUtils.stripToEmpty(taskDBean[i].getOption_answer_id());
			String questionText = StringUtils.stripToEmpty(taskDBean[i].getQuestion_label());
			String textAnswer = StringUtils.stripToEmpty(taskDBean[i].getText_answer());
			String latitude = StringUtils.stripToNull(taskDBean[i].getLatitude());						
			String longitude = StringUtils.stripToNull(taskDBean[i].getLongitude());
			String mcc = StringUtils.stripToNull(taskDBean[i].getMcc());
			String mnc = StringUtils.stripToNull(taskDBean[i].getMnc());
			String lac = StringUtils.stripToNull(taskDBean[i].getLac());				
			String cellId = StringUtils.stripToNull(taskDBean[i].getCid());				
			String accuracy = StringUtils.stripToNull(taskDBean[i].getAccuracy());	
			String image = StringUtils.stripToNull(taskDBean[i].getImage());
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerTypeCode) &&
					StringUtils.isNotEmpty(textAnswer) && StringUtils.containsAny(textAnswer, ',', '.')) {
				textAnswer = textAnswer.substring(0, textAnswer.length()-2).replace(",", "").replace(".", "");				
			}
			
			int idxAnswer = document.findAnswerIndex(msQuestion.getUuidQuestion());
			
			AnswerBean answer = this.constructAnswer(
					document, idxAnswer, msQuestion, answerTypeCode, questionText, textAnswer, optionAnswerId,
					latitude, longitude, accuracy, mcc, mnc, lac, cellId, trTaskH, image, isl, imagePath, groupTaskId, auditContext);
			
			if (idxAnswer == -1) {
				answers.add(answer);
			}
			
			//Additional logic
			if (!GlobalVal.SUBSYSTEM_MC.equals(userSubmit.getAmMssubsystem().getSubsystemName()) ||
					msQuestion.getMsCollectiontag() == null)
				continue;
			
			if (GlobalVal.COL_TAG_RVNUMBER.equals(msQuestion.getMsCollectiontag().getTagName())) {
				this.updateUsedRvNumber(trTaskH, taskDBean[i], auditContext);
			}
			else if (GlobalVal.COL_TAG_LOCATION.equals(msQuestion.getMsCollectiontag().getTagName())) {
				if (latitude != null && longitude != null) {
					this.saveOrUpdateAgreementCollectLocation(trTaskH, userSubmit, latitude, longitude);
				}
			}
			else if (GlobalVal.COL_TAG_COLL_RESULT.equals(msQuestion.getMsCollectiontag().getTagName())) {
				String codeResult = taskDBean[i].getLov();
				taskCollData.setResultCode(codeResult);
			}
			else if (GlobalVal.COL_TAG_BAYAR.equals(msQuestion.getMsCollectiontag().getTagName())) {
				String ans = taskDBean[i].getText_answer();
				if (StringUtils.isNotBlank(ans)) {
					taskCollData.setPaymentReceived(new BigDecimal(ans));
					totalBayar = ans;
				}
			}
			else if (GlobalVal.COL_TAG_PTP_DATE.equals(msQuestion.getMsCollectiontag().getTagName())) {
				String ans = taskDBean[i].getText_answer();
				SimpleDateFormat sdf2 = new SimpleDateFormat("ddMMyyyyHHmmss");
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				try {
					Date date = sdf2.parse(ans);
					ans = sdf.format(date);
					taskCollData.setPtpDate(sdf.parse(ans));
				} 
				catch (ParseException e) {
					LOG.warn("Error parsing ptp date", e);
				}
			}
			if (flagRevisit) {
				if (GlobalVal.COL_TAG_OVERDUE_DAYS.equals(msQuestion.getMsCollectiontag().getTagName()) && taskCollData != null) {
					String txtAnswer = taskDBean[i].getText_answer();
					if (StringUtils.isBlank(txtAnswer)) txtAnswer = "0";
					taskCollData.setOd(Integer.parseInt(txtAnswer));
				}
				else if (GlobalVal.COL_TAG_INSTALLMENT_NO.equals(msQuestion.getMsCollectiontag().getTagName()) && taskCollData != null) {
					String txtAnswer = taskDBean[i].getText_answer();
					if (StringUtils.isBlank(txtAnswer)) txtAnswer = "0";
					taskCollData.setInstNo(Integer.parseInt(txtAnswer));
				}
				else if (GlobalVal.COL_TAG_TENOR.equals(msQuestion.getMsCollectiontag().getTagName()) && taskCollData != null) {
					String txtAnswer = taskDBean[i].getText_answer();
					if (StringUtils.isBlank(txtAnswer)) txtAnswer = "0";
					taskCollData.setTenor(Integer.parseInt(txtAnswer));
				}
				else if (GlobalVal.COL_TAG_TAGIHAN.equals(msQuestion.getMsCollectiontag().getTagName()) && taskCollData != null) {
					String txtAnswer = taskDBean[i].getText_answer();
					if (StringUtils.isBlank(txtAnswer)) txtAnswer = "0";
					taskCollData.setAmountDue(new BigDecimal(txtAnswer));
				}
			}
		}
		
		docDb.setDocument(gson.toJson(document, TaskDocumentBean.class));
		
		if (newDoc) {
			this.getManagerDAO().insert(docDb);
		}
		else {
			this.getManagerDAO().update(docDb);
		}
		
		if (GlobalVal.SUBSYSTEM_MC.equals(userSubmit.getAmMssubsystem().getSubsystemName())) {
			if (flagRevisit) {
				taskCollData.setTrTaskH(trTaskH);
				taskCollData.setDtmCrt(new Date());
				taskCollData.setUsrCrt(auditContext.getCallerId());
				this.getManagerDAO().insert(taskCollData);
			}
			//update ke trtaskcollData
			else if (!flagRevisit && taskCollData != null) {
				taskCollData.setDtmUpd(new Date());
				taskCollData.setUsrUpd(auditContext.getCallerId());
				this.getManagerDAO().update(taskCollData);
			}
		}
		
		return new SaveTaskDResult(isFinal, totalBayar, "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "","","", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "");
	}
	
	private AnswerBean constructAnswer(TaskDocumentBean document, int idxAnswer, MsQuestion msQuestion,
			String answerTypeCode, String questionText, String textAnswer, String optionAnswerId,
			String latitude, String longitude, String accuracy, String mcc, String mnc, String lac, String cellId,
			TrTaskH trTaskH, String image, ImageStorageLocation isl, Path imagePath, long groupTaskId, AuditContext auditContext) {
		AnswerBean answer = (idxAnswer == -1) ? new AnswerBean() : document.getAnswers().get(idxAnswer);
		QuestionBean questionBean = new QuestionBean(
				msQuestion.getUuidQuestion(), questionText, msQuestion.getRefId(), answerTypeCode,
				(msQuestion.getMsOrdertag() == null ? null : msQuestion.getMsOrdertag().getTagName()),
				(msQuestion.getMsAssettag() == null ? null : msQuestion.getMsAssettag().getAssetTagName()),
				(msQuestion.getMsCollectiontag() == null ? null : msQuestion.getMsCollectiontag().getTagName()));
		answer.setQuestion(questionBean);
		
		if (MssTool.isChoiceQuestion(answerTypeCode)) {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			List<OptionBean> options = (answer.getOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getOptAnswers();
			
			MsLov msLovByLovId = null; 
			if (StringUtils.isNotBlank(optionAnswerId)) {
				msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
			}
			if (msLovByLovId != null) {
				OptionBean option = new OptionBean();
				option.setUuid(msLovByLovId.getUuidLov());
				option.setCode(msLovByLovId.getCode());
				option.setDesc(msLovByLovId.getDescription());
				option.setFreeText(StringUtils.trimToNull(textAnswer));
				if (!options.contains(option)) {
					options.add(option);
				}
				
				answer.setOptAnswers(options);
			}				
		}
		else if (MssTool.isImageQuestion(answerTypeCode) || GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
			answer.setOptAnswers(null);
			Boolean isGps = null;
			Boolean isConverted = null;
			
			if (mcc != null) {
				com.adins.mss.model.taskdjson.LocationBean locationBean = (answer.getLocation() == null) ?
						new com.adins.mss.model.taskdjson.LocationBean() : answer.getLocation();
				locationBean.setMcc(checkEmptyInteger(mcc).intValue());
				locationBean.setMnc(checkEmptyInteger(mnc).intValue());
				locationBean.setLac(checkEmptyInteger(lac).intValue());
				locationBean.setCid(checkEmptyInteger(cellId).intValue());
				locationBean.setAccuracy(checkEmptyInteger(accuracy));
				
				if (latitude != null && longitude != null
						&& new BigDecimal(latitude).intValue() != 0 && new BigDecimal(longitude).intValue() != 0) {
					locationBean.setLat(checkEmptyBigdecimal(latitude).doubleValue());
					locationBean.setLng(checkEmptyBigdecimal(longitude).doubleValue());
					locationBean.setIsGps(1);
				}
				else {
					LocationBean convertedLocation = this.getLocationByCellId(locationBean.getMcc(), locationBean.getMnc(),
							locationBean.getLac(), locationBean.getCid(), auditContext);
					if (convertedLocation.getCoordinate() != null) {
						locationBean.setAccuracy(convertedLocation.getAccuracy());
						locationBean.setLat(convertedLocation.getCoordinate().getLatitude());
						locationBean.setLng(convertedLocation.getCoordinate().getLongitude());
						locationBean.setIsGps(0);
						locationBean.setIsConverted(1);
						
						latitude = Double.toString(convertedLocation.getCoordinate().getLatitude());
						longitude = Double.toString(convertedLocation.getCoordinate().getLongitude());
						accuracy = Integer.toString(convertedLocation.getAccuracy());
					}
					isGps = Boolean.FALSE;
					isConverted = Boolean.TRUE;
				}
				
				answer.setLocation(locationBean);
			}

			if (MssTool.isImageQuestion(answerTypeCode)) {
				ImageBean imageBean = (answer.getLobAnswer() == null) ? new ImageBean() : answer.getLobAnswer();
				if (image != null && !image.isEmpty()) {
					imageBean.setHasImage(true);
					answer.setLobAnswer(imageBean);
				}
				
				//insert into Table TR_TASKDETAILLOB
				String uuidTaskD = this.getManagerDAO().getUUID();
				long idLob = this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, isGps, isConverted, isl, imagePath);
				if (idLob > 0L){
					imageBean.setId(idLob);
				}
			}
			else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
				answer.setTxtAnswer(textAnswer);
			}
		}
		else {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)
					|| GlobalVal.ANSWER_TYPE_DATETIME.equals(answerTypeCode)) {
				textAnswer = this.dateAnswerToText(textAnswer, answerTypeCode);
			}
			answer.setOptAnswers(null);
			answer.setTxtAnswer(textAnswer);
		}
		
		return answer;
	}
	
	private String dateAnswerToText(String dateAnswer, String answerTypeCode) {
		DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
		try {
			Date result = df.parse(dateAnswer);
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)) {
				return (null != result) ? new SimpleDateFormat(dateFormat).format(result) : null;
			}
			else {
				return (null != result) ? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result) : null;
			}
		}
		catch (ParseException e) {
			LOG.warn("Cannot parse '{}' to answer type {}", dateAnswer, answerTypeCode, e);
			return null;
		}
	}
	
	private SaveTaskDResult saveTaskDIntoRow(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, AmMsuser userSubmit, boolean flagRevisit,
			TrTaskrejectedhistory rejectHistory, boolean flag,
			Map<Integer, MsQuestion> msQuestions, Map<Long, TrTaskD> listTaskD, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {				
		
		boolean hasBrand = false;
		boolean hasModel = false;
		boolean hasGroup = false;
		boolean hasType = false;
		String idAsset = StringUtils.EMPTY;
		
		Map<String, Object> mapAssetProduct = new HashMap<String, Object>();
		
		if (taskDBean.length > 1) {
			//delete multiple detail
			String[] answerTypeArr = {GlobalVal.ANSWER_TYPE_MULTIPLE, GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION,
					GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION};
			Object[][] paramsMulti = {
					{"uuidTaskH", trTaskH.getUuidTaskH()},
					{"answerType", Arrays.asList(answerTypeArr)} };
			this.getManagerDAO().deleteNativeString("delete d from tr_task_d d "
					+"left join ms_question q on d.uuid_question = q.uuid_question "
					+"left join ms_answertype at on q.uuid_answer_type = at.uuid_answer_type "
					+"where d.uuid_task_h = :uuidTaskH and "
					+"at.code_answer_type in (:answerType)", paramsMulti);
		}
		
		String isFinal = "0";
		String totalBayar = "0";
		String poloInterest = "";
		String poloProspect = "";
		String poloNegCust = "";
		String poloInputDate = "";
		String poloVisitDate = "";
		String poloSpv = "";
		String poloRef2Code = "";
		String poloRef2Name = "";
		String poloRefCode = "";
		String poloRefName = "";
		String poloDkcp = "";
		String poloNegListCust = "";
		String poloDkcpPsgn = "";
		String poloNegListPsgn = "";
		String poloDkcpGrtr = "";
		String poloNegListGrtr = "";
		String isCancelApp = null;
		String isCmoRecommendation = null;
		String poloNotesTaskVisit = null;
		String poloNotesCrm = null;
		String poloRef3Code = "";
		String poloRef3Name = "";
		String nik = StringUtils.EMPTY;
		String custName = StringUtils.EMPTY;
		String phone = StringUtils.EMPTY;
		String birthPlace = StringUtils.EMPTY;
		String birthDate = StringUtils.EMPTY;
		String motherName = StringUtils.EMPTY;
		String address = StringUtils.EMPTY;
		String rt = StringUtils.EMPTY;
		String rw = StringUtils.EMPTY;
		String province = StringUtils.EMPTY;
		String city = StringUtils.EMPTY;
		String kecamatan = StringUtils.EMPTY;
		String kelurahan = StringUtils.EMPTY;
		String zipCode = StringUtils.EMPTY;
		String subZipCode = StringUtils.EMPTY;
		String prodCat = StringUtils.EMPTY;
		String prodOff = StringUtils.EMPTY;
		String regionName = StringUtils.EMPTY;
		String officeCode = StringUtils.EMPTY;
		String custId = StringUtils.EMPTY;
		String contractNo = StringUtils.EMPTY;
		String soa = StringUtils.EMPTY;
		String ptvDate = StringUtils.EMPTY;
		String emailCust = StringUtils.EMPTY;
		
		
		TrTaskcolldata taskCollData = this.getManagerDAO().selectOne(TrTaskcolldata.class, trTaskH.getUuidTaskH());
		if (taskCollData == null){
			taskCollData = new TrTaskcolldata();
		}
		
		boolean isSetRes = false;
		for (int i=0; i < taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
			
			String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String uuidTaskD = taskDBean[i].getUuid_task_d();
			String optionAnswerId = taskDBean[i].getOption_answer_id();
			String lov = taskDBean[i].getLov();
			String textAnswer = taskDBean[i].getText_answer();
			String questionText = taskDBean[i].getQuestion_label();
			String latitude = taskDBean[i].getLatitude();
			String longitude = taskDBean[i].getLongitude();
			String mcc = taskDBean[i].getMcc();
			String mnc = taskDBean[i].getMnc();
			String lac = taskDBean[i].getLac();
			String cellId = taskDBean[i].getCid();
			String accuracy = taskDBean[i].getAccuracy();
			String image = taskDBean[i].getImage();
			String uuidLookup = taskDBean[i].getUuid_lookup();
			isFinal = taskDBean[i].getIs_final();
			
			String assetType = StringUtils.EMPTY;
			if (null != msQuestion.getMsAssettag()) {
				assetType = msQuestion.getMsAssettag().getAssetTagName();
			}
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType)) {
				if (StringUtils.isNotBlank(textAnswer) && (textAnswer.contains(",")||textAnswer.contains("."))) {
					textAnswer = textAnswer.substring(0,textAnswer.length()-2).replace(",", "").replace(".", "");
				}
			}
			
			if (GlobalVal.ANSWER_TYPE_DSR.equals(answerType)) {
				String[] temp = textAnswer.split("@");
				textAnswer = temp[0];
			}
			
			if (GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(answerType)) {				
				if (null != textAnswer && textAnswer.contains("|")) {
					String[] temp = textAnswer.split("[|]");
					textAnswer = temp[temp.length-1];
				}
				if ((GlobalVal.TGL_LOC_ADDR.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.PMHN_LOC_ADDR.equalsIgnoreCase(msQuestion.getRefId()))
						&& !isSetRes) {
					this.getManagerDAO().fetch(trTaskH.getMsBranch());
					MsBranch msBranch = trTaskH.getMsBranch();
					Integer branchDistance = 0;
					if (StringUtils.isNotBlank(latitude) && StringUtils.isNotBlank(longitude) &&
							null != msBranch.getLatitude() && null != msBranch.getLongitude()) {
						branchDistance = this.getBranchDistance(latitude, longitude, msBranch.getLatitude(), msBranch.getLongitude());
						trTaskH.setBranchKmDistance(branchDistance);
						if (GlobalVal.TGL_LOC_ADDR.equalsIgnoreCase(msQuestion.getRefId())) {
							isSetRes = true;
						}
					} else {
						trTaskH.setBranchKmDistance(branchDistance);
					}
					
				}
			}
			
			if (flag) {
				if (MssTool.isImageQuestion(answerType)){										
						//insert into Table TR_TASKDETAILLOB
					this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
								latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, isl, imagePath);						
				} else if (GlobalVal.ANSWER_TYPE_LU_OFFLINE.equals(answerType) || 
						GlobalVal.ANSWER_TYPE_LU_OFFLINE_WITH_SUGGESTION.equals(answerType)) {
					// Check product answer
					if (GlobalVal.ASSET_TAG_BRAND.equals(assetType) ||
							GlobalVal.ASSET_TAG_MODEL.equals(assetType) ||
							GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType) ||
							GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
						if (GlobalVal.ASSET_TAG_BRAND.equals(assetType)) {
							hasBrand = true;
						} else if (GlobalVal.ASSET_TAG_MODEL.equals(assetType)) {
							hasModel = true;
						} else if (GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType)) {
							hasGroup = true;
						} else if (GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
							hasType = true;
							idAsset = taskDBean[i].getUuid_lookup();
						}
						mapAssetProduct.put(assetType, i);
					} else {
						Map<String, Object> mapAns = new HashMap<String, Object>();
						if (StringUtils.isNotBlank(uuidLookup)) {
							Long idStagingAsset = Long.valueOf(uuidLookup);
							String optionText = null;
							if (null != idStagingAsset) {
								if (GlobalVal.ASSET_TAG_PRODUCT.equals(assetType)) {
									Object[][] paramLu = { {"idLu", idStagingAsset} };
									List<Map<String, Object>> luAnswer = this.getManagerDAO().selectAllNativeString("select PRODUCT_OFFERING_ID, PRODUCT_OFFERING_NAME from STAGING_PRODUCT_OFFERING where id = :idLu", paramLu);
									if (!luAnswer.isEmpty()) {
										mapAns = luAnswer.get(0);
										BigInteger prodId = (BigInteger) mapAns.get("d0");
										mapAns.put("d0", String.valueOf(prodId));
									}
									optionText = (String) mapAns.get("d0");
									textAnswer = (String) mapAns.get("d1");
									optionAnswerId = null;
								} else {
									optionText = lov;
									optionAnswerId = null;
								}
								
								insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
										latitude, longitude, mcc, mnc, lac, cellId, accuracy, idStagingAsset, optionText, listTaskD);
							}
						}
					}
				} else if (GlobalVal.ANSWER_TYPE_SCORING.equals(answerType) ) {
					String optionText = lov;
					optionAnswerId = null;
					insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
							latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, optionText, listTaskD);
				}
				else {
					//tambahan RV
					if (GlobalVal.SUBSYSTEM_MC.equals(userSubmit.getAmMssubsystem().getSubsystemName()) &&
							msQuestion.getMsCollectiontag() != null) {												 
						if (GlobalVal.COL_TAG_RVNUMBER.equals(msQuestion.getMsCollectiontag().getTagName())) {
							this.updateUsedRvNumber(trTaskH, taskDBean[i], auditContext);
						}
						else if (GlobalVal.COL_TAG_LOCATION.equals(msQuestion.getMsCollectiontag().getTagName())) {
							if (latitude != null && longitude != null) {
								this.saveOrUpdateAgreementCollectLocation(trTaskH, userSubmit, latitude, longitude);
							}
						}
						else if (GlobalVal.COL_TAG_COLL_RESULT.equals(msQuestion.getMsCollectiontag().getTagName())) {
							String codeResult = taskDBean[i].getLov();
							taskCollData.setResultCode(codeResult);
						}
						else if (GlobalVal.COL_TAG_BAYAR.equals(msQuestion.getMsCollectiontag().getTagName())) {
							String ans = taskDBean[i].getText_answer();
							if (StringUtils.isNotBlank(ans)) {
								taskCollData.setPaymentReceived(new BigDecimal(ans));
								totalBayar = ans;
							}
						}
						else if (GlobalVal.COL_TAG_PTP_DATE.equals(msQuestion.getMsCollectiontag().getTagName())) {
							String ans = taskDBean[i].getText_answer();
							SimpleDateFormat sdf2 = new SimpleDateFormat("ddMMyyyyHHmmss");
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
							try {
								Date date = sdf2.parse(ans);
								ans = sdf.format(date);
								taskCollData.setPtpDate(sdf.parse(ans));
							} 
							catch (ParseException e) {
								LOG.warn("Error parsing ptp date", e);
							}
						}
						if (flagRevisit) {
							if (GlobalVal.COL_TAG_OVERDUE_DAYS.equals(msQuestion.getMsCollectiontag().getTagName()) && taskCollData != null) {
								String answer = taskDBean[i].getText_answer();
								if (StringUtils.isBlank(answer)) answer = "0";
								taskCollData.setOd(Integer.parseInt(answer));
							}
							else if (GlobalVal.COL_TAG_INSTALLMENT_NO.equals(msQuestion.getMsCollectiontag().getTagName()) && taskCollData != null) {
								String answer = taskDBean[i].getText_answer();
								if (StringUtils.isBlank(answer)) answer = "0";
								taskCollData.setInstNo(Integer.parseInt(answer));
							}
							else if (GlobalVal.COL_TAG_TENOR.equals(msQuestion.getMsCollectiontag().getTagName()) && taskCollData != null) {
								String answer = taskDBean[i].getText_answer();
								if (StringUtils.isBlank(answer)) answer = "0";
								taskCollData.setTenor(Integer.parseInt(answer));
							}
							else if (GlobalVal.COL_TAG_TAGIHAN.equals(msQuestion.getMsCollectiontag().getTagName()) && taskCollData != null) {
								String answer = taskDBean[i].getText_answer();
								if (StringUtils.isBlank(answer)) answer = "0";
								taskCollData.setAmountDue(new BigDecimal(answer));
							}
						}						
					}
					if (msQuestion.getMsAssettag() != null){
			            if ((GlobalVal.ASSET_TAG_PRODUCT).equalsIgnoreCase(msQuestion.getMsAssettag().getAssetTagName()) && 
			            		(StringUtils.isNotBlank(optionAnswerId))){
							this.insertTaskSurveyData(trTaskH, optionAnswerId, auditContext.getCallerId());
			            }
		            }
					//end tambahan RV
					//insert into Table TR_TASK_D
					this.insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
							latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, listTaskD);
				}
			}
			else {					
				this.insertTaskRejectedDetail(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, 
						textAnswer, latitude, longitude, mcc, mnc, lac, cellId, accuracy, image, rejectHistory, isl);
			}
			
			if (msQuestion.getMsAssettag()!=null && ((GlobalVal.ASSET_TAG_CANCEL_APP).equalsIgnoreCase(msQuestion.getMsAssettag().getAssetTagName()) 
					 || (GlobalVal.ASSET_TAG_CMO_RECOMMENDATION).equalsIgnoreCase(msQuestion.getMsAssettag().getAssetTagName())) && 
           		(StringUtils.isNotBlank(optionAnswerId))){
				String code = (String) this.getManagerDAO().selectOneNativeString("SELECT CODE FROM MS_LOV WITH(NOLOCK) WHERE UUID_LOV = :uuidLov", new Object[][] {{"uuidLov", optionAnswerId}}); 
				if(StringUtils.isNotBlank(code)) {
					if(GlobalVal.ASSET_TAG_CANCEL_APP.equalsIgnoreCase(msQuestion.getMsAssettag().getAssetTagName())) {
						isCancelApp = code;
					} else {
						isCmoRecommendation = code;
					}
				}
				
           }
			
			if (msQuestion.getMsAssettag() != null){
	            if (GlobalVal.ASSET_TAG_PRODUCT_CATEGORY.equalsIgnoreCase(msQuestion.getMsAssettag().getAssetTagName()) && 
	            		(StringUtils.isNotBlank(lov))){
					prodCat = lov;
	            }
            }
			
			//tambahan POLO
			if(GlobalVal.FORM_VISIT_POLO.equals(trTaskH.getMsForm().getFormName())) {
				if(GlobalVal.REF_STV_INTEREST.equals(msQuestion.getRefId())) {
					poloInterest = lov;
				}
				if(GlobalVal.REF_STV_NEG_CUST.equals(msQuestion.getRefId())) {
					poloNegCust = lov;
				}
				if(GlobalVal.REF_STV_SPV.equals(msQuestion.getRefId())) {
					poloSpv = textAnswer;
				}
				if(GlobalVal.REF_STV_REFCODE3.equals(msQuestion.getRefId())) {
					poloRef3Code = textAnswer;
				}
				if(GlobalVal.REF_STV_REFNAME3.equals(msQuestion.getRefId())) {
					poloRef3Name = textAnswer;
				}
				if(GlobalVal.REF_STV_REFCODE2.equals(msQuestion.getRefId())) {
					poloRef2Code = textAnswer;
				}
				if(GlobalVal.REF_STV_REFNAME2.equals(msQuestion.getRefId())) {
					poloRef2Name = textAnswer;
				}
				if(GlobalVal.REF_STV_REFCODE.equals(msQuestion.getRefId())) {
					poloRefCode = textAnswer;
				}
				if(GlobalVal.REF_STV_REFNAME.equals(msQuestion.getRefId())) {
					poloRefName = textAnswer;
				}
				if(GlobalVal.REF_STV_VISIT_DATE.equals(msQuestion.getRefId())) {
					poloVisitDate = this.dynamicDateFormatterByGenset(textAnswer, "yyyy-MM-dd HH:mm:ss");
				}
				if(GlobalVal.REF_STV_INPUT_DATE.equals(msQuestion.getRefId())) {
					poloInputDate = this.dynamicDateFormatterByGenset(textAnswer, "yyyy-MM-dd HH:mm:ss");
				}
				if (GlobalVal.REF_STV_NTS_CRM.equals(msQuestion.getRefId())) {
					poloNotesCrm = textAnswer;
				}
				if (GlobalVal.REF_STV_NTS_VST.equals(msQuestion.getRefId())) {
					poloNotesTaskVisit = textAnswer;
				}
				if(GlobalVal.REF_STV_PROSPECT.equals(msQuestion.getRefId())) {
					poloProspect = lov;
				}				
				if(GlobalVal.REF_STV_RSLT_DKCP.equals(msQuestion.getRefId())) {
					poloDkcp = textAnswer;
				}
				if(GlobalVal.REF_STV_RSLT_NGTV.equals(msQuestion.getRefId())) {
					poloNegListCust = textAnswer;
				}
				if(GlobalVal.REF_STV_RSLT_DKCP_PSGN.equals(msQuestion.getRefId())) {
					poloDkcpPsgn = textAnswer;
				}
				if(GlobalVal.REF_STV_RSLT_NGTV_PSGN.equals(msQuestion.getRefId())) {
					poloNegListPsgn = textAnswer;
				}
				if(GlobalVal.REF_STV_RSLT_DKCP_GRTR.equals(msQuestion.getRefId())) {
					poloDkcpGrtr = textAnswer;
				}
				if(GlobalVal.REF_STV_RSLT_NGTV_GRTR.equals(msQuestion.getRefId())) {
					poloNegListGrtr = textAnswer;
				}
				if (GlobalVal.REF_STV_NO_KTP.equals(msQuestion.getRefId())) {
					nik = textAnswer;
				}
				if (GlobalVal.REF_STV_CUSTNAME.equals(msQuestion.getRefId())) {
					custName = textAnswer;
				}
				if (GlobalVal.REF_STV_PHONE.equals(msQuestion.getRefId())) {
					phone = textAnswer;
				}
				if (GlobalVal.REF_STV_TMPT_LHR.equals(msQuestion.getRefId())) {
					birthPlace = textAnswer;
				}
				if (GlobalVal.REF_STV_TGL_LHR.equals(msQuestion.getRefId())) {
					birthDate = this.dynamicDateFormatterByGenset(textAnswer, "yyyy-MM-dd");
				}
				if (GlobalVal.REF_STV_IBU_KANDUNG.equals(msQuestion.getRefId())) {
					motherName = textAnswer;
				}
				if (GlobalVal.REF_STV_ADDRESS.equals(msQuestion.getRefId())) {
					address = textAnswer;
				}
				if (GlobalVal.REF_STV_RT.equals(msQuestion.getRefId())) {
					rt = textAnswer;
				}
				if (GlobalVal.REF_STV_RW.equals(msQuestion.getRefId())) {
					rw = textAnswer;
				}
				if (GlobalVal.REF_STV_PROVINSI.equals(msQuestion.getRefId())) {
					province = lov;
				}
				if (GlobalVal.REF_STV_CITY.equals(msQuestion.getRefId())) {
					city = lov;
				}
				if (GlobalVal.REF_STV_KCMTN.equals(msQuestion.getRefId())) {
					kecamatan = lov;
				}
				if (GlobalVal.REF_STV_KELURAHAN.equals(msQuestion.getRefId())) {
					kelurahan = lov;
				}
				if (GlobalVal.REF_STV_ZIPCODE.equals(msQuestion.getRefId())) {
					zipCode = lov;
				}
				if (GlobalVal.REF_STV_KAT.equals(msQuestion.getRefId())) {
					subZipCode = lov;
				}
				if (GlobalVal.REF_STV_PROD_CAT.equals(msQuestion.getRefId())) {
					prodCat = lov;
				}
				if (GlobalVal.REF_STV_PROD_OFF_CODE.equals(msQuestion.getRefId())) {
					prodOff = lov;
				}
				if (GlobalVal.REF_STV_REGION.equals(msQuestion.getRefId())) {
					regionName = textAnswer;
				}
				if (GlobalVal.REF_STV_BRANCH.equals(msQuestion.getRefId())) {
					officeCode = null != trTaskH.getMsBranch() ? trTaskH.getMsBranch().getBranchCode() : StringUtils.EMPTY;
				}
				if (GlobalVal.REF_STV_CUSTID.equals(msQuestion.getRefId())) {
					custId = textAnswer;
				}
				if (GlobalVal.REF_STV_NOKONTRAK.equals(msQuestion.getRefId())) {
					contractNo = textAnswer;
				}
				if (GlobalVal.REF_STV_SOA.equals(msQuestion.getRefId())) {
					soa = lov;
				}				
			}
			if(GlobalVal.FORM_PROMISE_TO_VISIT_POLO.equals(trTaskH.getMsForm().getFormName())) {
				if(GlobalVal.REF_SVY_DTM_VISIT.equals(msQuestion.getRefId())) {
					ptvDate = this.dynamicDateFormatterByGenset(textAnswer, "dd/MM/yyyy HH:mm:ss");
				}
			}
		}
		
		if (GlobalVal.SUBSYSTEM_MC.equals(userSubmit.getAmMssubsystem().getSubsystemName())) {
			if (flagRevisit) {
				taskCollData.setTrTaskH(trTaskH);
				taskCollData.setDtmCrt(new Date());
				taskCollData.setUsrCrt(auditContext.getCallerId());
				this.getManagerDAO().insert(taskCollData);
			}
			//update ke trtaskcollData
			else if (!flagRevisit && taskCollData != null) {
				taskCollData.setDtmUpd(new Date());
				taskCollData.setUsrUpd(auditContext.getCallerId());
				this.getManagerDAO().update(taskCollData);
			}
		}
		
		//Insert into Task D for Product 
		if (hasBrand && hasModel && hasGroup && hasType) {
			Long longIdAsset = new Long(idAsset);
			
			Object[][] paramPo = { {"idPo", longIdAsset} };
			List<Map<String, Object>> listAssetAns = this.getManagerDAO().selectAllNativeString(
					"select HIERARCHY_L1_CODE, ASSET_HIERARCHY_L1_NAME, "
					+ " HIERARCHY_L2_CODE, ASSET_HIERARCHY_L2_NAME, "
					+ " GROUP_TYPE, MASTER_CODE, MASTER_NAME "
					+ " from STAGING_PO_ASSET where id = :idPo", paramPo);
			
			Map<String, Object> mapAsset = new HashMap<String, Object>();
			if (!listAssetAns.isEmpty()) {
				mapAsset = listAssetAns.get(0);
			}
			
			for (Map.Entry<String, Object> mp : mapAssetProduct.entrySet()) {
				int i = (int) mp.getValue();
				MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
				String assetType = msQuestion.getMsAssettag().getAssetTagName();
				String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
				String uuidTaskD = taskDBean[i].getUuid_task_d();
				String optionAnswerId = null;
				String textAnswer = taskDBean[i].getText_answer();
				String questionText = taskDBean[i].getQuestion_label();
				String latitude = taskDBean[i].getLatitude();
				String longitude = taskDBean[i].getLongitude();
				String mcc = taskDBean[i].getMcc();
				String mnc = taskDBean[i].getMnc();
				String lac = taskDBean[i].getLac();
				String cellId = taskDBean[i].getCid();
				String gpsTime = taskDBean[i].getGps_time();
				String accuracy = taskDBean[i].getAccuracy();
				String image = taskDBean[i].getImage();
				String uuidLookup = taskDBean[i].getUuid_lookup();
				String optionText = null;
				
				if (GlobalVal.ASSET_TAG_BRAND.equals(assetType)) {
					optionText = (String) mapAsset.get("d0");
					textAnswer = (String) mapAsset.get("d1");
				} else if (GlobalVal.ASSET_TAG_MODEL.equals(assetType)) {
					optionText = (String) mapAsset.get("d2");
					textAnswer = (String) mapAsset.get("d3");
				} else if (GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType)) {
					optionText = (String) mapAsset.get("d4");
					textAnswer = (String) mapAsset.get("d4");
				} else if (GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
					optionText = (String) mapAsset.get("d5");
					textAnswer = (String) mapAsset.get("d6");
				}
				insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, longIdAsset, optionText, listTaskD);
			}
		}
		
		return new SaveTaskDResult(isFinal, totalBayar, poloNegCust, poloInterest, poloInputDate, poloVisitDate, poloSpv, poloRefCode,
				poloRefName, poloRef2Code, poloRef2Name, isCancelApp, isCmoRecommendation, poloNotesTaskVisit, poloNotesCrm, poloProspect, poloDkcp, poloNegListCust,
				poloRef3Code, poloRef3Name, poloDkcpPsgn, poloNegListPsgn, poloDkcpGrtr, poloNegListGrtr, nik, custName, phone, birthPlace, birthDate, motherName, address, 
				rt, rw, province, city, kecamatan, kelurahan, zipCode, subZipCode, prodCat, prodOff, regionName, officeCode, custId, contractNo, soa, ptvDate);
	}
	
	private TrTaskH saveTaskPts(TrTaskH originalTask, AmMsuser userSubmit, Date ptsDate, AuditContext auditContext) {
		//Start create new pts task from previous Task
		TrTaskH trTaskPts = new TrTaskH();
		trTaskPts.setUsrCrt(auditContext.getCallerId());
		trTaskPts.setDtmCrt(new Date());
		trTaskPts.setApplNo(originalTask.getApplNo());
		trTaskPts.setMsBranch(originalTask.getMsBranch());
		trTaskPts.setMsForm(originalTask.getMsForm());
		trTaskPts.setMsPriority(originalTask.getMsPriority());
		trTaskPts.setCustomerName(originalTask.getCustomerName());
		trTaskPts.setCustomerPhone(originalTask.getCustomerPhone());
		trTaskPts.setCustomerAddress(originalTask.getCustomerAddress());
		trTaskPts.setNotes(originalTask.getNotes());
		trTaskPts.setLatitude(originalTask.getLatitude());
		trTaskPts.setLongitude(originalTask.getLongitude());
		trTaskPts.setZipCode(originalTask.getZipCode());
		trTaskPts.setIsDraft("0");
		trTaskPts.setIsAppNotified("0");
		trTaskPts.setFlagSource(originalTask.getFlagSource());
		trTaskPts.setAmMsuser(userSubmit.getAmMsuser());
		trTaskPts.setMsStatustask(null);
		trTaskPts.setTrTaskH(originalTask);
		if (null != ptsDate) {
			trTaskPts.setPtsDate(ptsDate);
		}
		trTaskPts.setFormVersion(originalTask.getFormVersion());
		this.getManagerDAO().insert(trTaskPts);
		
		return trTaskPts;
	}
	
	private void copyTaskhistoryNative(TrTaskH trTaskHnew, TrTaskH trTaskHSource, AuditContext auditContext) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("uuidTaskHSource", trTaskHSource.getUuidTaskH());
		paramMap.put("taskIdSource", trTaskHSource.getTaskId());
		paramMap.put("uuidTaskH", trTaskHnew.getUuidTaskH());
		paramMap.put("usrCrt", auditContext.getCallerId());
		this.getManagerDAO().insertNative("services.common.task.copyTaskhistoryForPts", paramMap);
	}
	
	/**
	 * @deprecated see {@link #copyTaskhistoryNative(TrTaskH, TrTaskH, AuditContext)}
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private void copyTaskhistory(TrTaskH trTaskHnew, TrTaskH trTaskHSource, AuditContext auditContext) {
		Object[][] paramHist = { {Restrictions.eq("trTaskH.uuidTaskH", trTaskHSource.getUuidTaskH())} };
		Object[][] orderHist = { {"dtmCrt", "ASC"} };
		Map<String, Object> mapHistory = this.getManagerDAO().list(TrTaskhistory.class, paramHist, orderHist);
		List<TrTaskhistory> listHist = (List<TrTaskhistory>) mapHistory.get(GlobalKey.MAP_RESULT_LIST);
		
		if (!listHist.isEmpty()) {
			for(TrTaskhistory trHist : listHist) {
				TrTaskhistory trHistTransf = new TrTaskhistory(trHist.getMsStatustask()
						, trTaskHnew, auditContext.getCallerId(), new Date()
						, StringUtils.left(trHist.getNotes() + " | PTS dari Task ID: " + trTaskHSource.getTaskId(), 2048)
						, trHist.getFieldPerson(), trHist.getActor(), trHist.getCodeProcess());
				this.getManagerDAO().insert(trHistTransf);
			}
		}
	}
	
	private void copyTaskDAsInitAnswerNative(TrTaskH trTaskHnew, TrTaskH trTaskHSource, boolean isJson, AuditContext auditContext) {		
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("uuidTaskHSource", trTaskHSource.getUuidTaskH());
		paramMap.put("uuidTaskH", trTaskHnew.getUuidTaskH());
		paramMap.put("usrCrt", auditContext.getCallerId());
		
		final String namedQuery = (isJson) ? "services.common.task.copyTaskDJsonAsInitAnswer" :
				"services.common.task.copyTaskDInitAsInitAnswer";
		
		this.getManagerDAO().insertNative(namedQuery, paramMap);
	}
	
	/**
	 * @deprecated see {@link #copyTaskDAsInitAnswerNative(TrTaskH, TrTaskH, AuditContext)}
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private void copyTaskDAsInitAnswer(TrTaskH trTaskHnew, TrTaskH trTaskHSource, AuditContext auditContext) {
		Object[][] prevDetailParam = { {Restrictions.eq("trTaskH.uuidTaskH", trTaskHSource.getUuidTaskH())} };
		Map<String, Object> mapResultPrevDetail = this.getManagerDAO().list(TrTaskD.class, prevDetailParam, null);
		List<TrTaskD> listResult = (List) mapResultPrevDetail.get(GlobalKey.MAP_RESULT_LIST);
		if (!listResult.isEmpty()) {
			for (TrTaskD prevTrTaskD : listResult) {
				this.getManagerDAO().fetch(prevTrTaskD.getMsLovByIntLovId());
				this.getManagerDAO().fetch(prevTrTaskD.getMsQuestion().getMsAnswertype());
				
				String textAnswer = (StringUtils.isBlank(prevTrTaskD.getIntTextAnswer())) ? null : prevTrTaskD.getIntTextAnswer();
				String lat = (null == prevTrTaskD.getLatitude()) ? null : String.valueOf(prevTrTaskD.getLatitude());
				String longi = (null == prevTrTaskD.getLongitude()) ? null : String.valueOf(prevTrTaskD.getLongitude());
				String mcc = (null == prevTrTaskD.getMcc()) ? null : String.valueOf(prevTrTaskD.getMcc());
				String mnc = (null == prevTrTaskD.getMnc()) ? null : String.valueOf(prevTrTaskD.getMnc());
				String lac = (null == prevTrTaskD.getLac()) ? null : String.valueOf(prevTrTaskD.getLac());
				String cellId = (null == prevTrTaskD.getCellId()) ? null : String.valueOf(prevTrTaskD.getCellId());
				String accuracy = (null == prevTrTaskD.getAccuracy()) ? null : String.valueOf(prevTrTaskD.getAccuracy());
				
				insertTaskDPts(auditContext, trTaskHnew, prevTrTaskD.getMsQuestion(), prevTrTaskD.getMsLovByIntLovId(),
						textAnswer, lat, longi, mcc, mnc, lac, cellId, accuracy);
			}
		}
	}
	
	private void copyTaskdetailLobAsInitAnswerNative(TrTaskH trTaskHnew, TrTaskH trTaskHSource, AuditContext auditContext) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("uuidTaskHSource", trTaskHSource.getUuidTaskH());
		paramMap.put("uuidTaskH", trTaskHnew.getUuidTaskH());
		paramMap.put("usrCrt", auditContext.getCallerId());
		this.getManagerDAO().insertNative("services.common.task.copyTaskdetaillobInitAsInitAnswer", paramMap);
	}
	
	/**
	 * @deprecated see {@link #copyTaskdetailLobAsInitAnswerNative(TrTaskH, TrTaskH, AuditContext)}
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private void copyTaskdetailLobAsInitAnswer(TrTaskH trTaskHnew, TrTaskH trTaskHSource, AuditContext auditContext) {
		Object[][] prevDetailParam = { {Restrictions.eq("trTaskH.uuidTaskH", trTaskHSource.getUuidTaskH())} };
		Map<String, Object> mapResultPrevImage = this.getManagerDAO().list(TrTaskdetaillob.class, prevDetailParam, null);
		List<TrTaskdetaillob> listImage = (List) mapResultPrevImage.get(GlobalKey.MAP_RESULT_LIST);
		if (!listImage.isEmpty()) {
			for (TrTaskdetaillob prevLob : listImage) {
				this.getManagerDAO().fetch(prevLob.getMsLovByIntLovId());
				this.getManagerDAO().fetch(prevLob.getMsQuestion().getMsAnswertype());
				
				String textAnswer = (StringUtils.isBlank(prevLob.getIntTextAnswer())) ? null : prevLob.getIntTextAnswer();
				String lat = (null == prevLob.getLatitude()) ? null : String.valueOf(prevLob.getLatitude());
				String longi = (null == prevLob.getLongitude()) ? null : String.valueOf(prevLob.getLongitude());
				String mcc = (null == prevLob.getMcc()) ? null : String.valueOf(prevLob.getMcc());
				String mnc = (null == prevLob.getMnc()) ? null : String.valueOf(prevLob.getMnc());
				String lac = (null == prevLob.getLac()) ? null : String.valueOf(prevLob.getLac());
				String cellId = (null == prevLob.getCellId()) ? null : String.valueOf(prevLob.getCellId());
				String accuracy = (null == prevLob.getAccuracy()) ? null : String.valueOf(prevLob.getAccuracy());
				String imagePath = (null == prevLob.getImagePath()) ? null : String.valueOf(prevLob.getImagePath());
				String isGps = (null == prevLob.getIsGps()) ? null : String.valueOf(prevLob.getIsGps());
				String isGsm = (null == prevLob.getIsGsm()) ? null : String.valueOf(prevLob.getIsGsm());
				String geoProvider = (null == prevLob.getGeolocationProvider()) ? null : String.valueOf(prevLob.getGeolocationProvider());
				String isVisibleQa = (null == prevLob.getIsVisibleQa()) ? null : String.valueOf(prevLob.getIsVisibleQa());
				String lob = (null == prevLob.getLobFile()) ? null : BaseEncoding.base64().encode(prevLob.getLobFile());
				
				insertLobPts(auditContext, trTaskHnew, prevLob.getMsQuestion(), prevLob.getMsLovByIntLovId(),
						textAnswer, lat, longi, mcc, mnc, lac, cellId, accuracy, imagePath, isGps, isGsm, geoProvider, isVisibleQa, lob);
			}
		}
	}
	
	@Transactional
	@Override
	public String updatePromisedDate(long uuidTaskH, Date promisedDate, AuditContext callerId) {
		TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTaskH);
		if (null == taskH) {
			throw new EntityNotFoundException("Task not found !", String.valueOf(uuidTaskH));
		}
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, new Long(callerId.getCallerId()));
		if(user != null) {
			this.getManagerDAO().fetch(user.getMsBranch());
			if("1".equalsIgnoreCase(user.getMsBranch().getIsPiloting())) {
				if (null == taskH.getPromiseDate() && StringUtils.isNotBlank(taskH.getApplNo())) {
					AmMssubsystem subsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
					MsStatustask statusPending = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION, subsystem.getUuidMsSubsystem(), callerId);
					MsStatustask statusWaitingOnPending = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_WAITING_ON_PENDING, subsystem.getUuidMsSubsystem(), callerId);
					
					// 19 Mar 2020 Perlu dicek dulu tasknya ada dan sesuai gak, kalo gak return failed
					Object [][] paramListTask = { {paramApplNo, taskH.getApplNo()}, {"uuidStatusTaskPending", statusPending.getUuidStatusTask()},
							{"uuidMsUser", callerId.getCallerId()}, {"uuidStatusTaskWp", statusWaitingOnPending.getUuidStatusTask()} };
					List<Map<String, Object>> listTask = this.getManagerDAO().selectAllNativeString(
							"SELECT UUID_TASK_H " +
							"FROM   TR_TASK_H WITH (NOLOCK) " +
							"WHERE  PROMISE_DATE IS NULL " +
							"       AND APPL_NO = :applNo " +
							"       AND (UUID_STATUS_TASK = :uuidStatusTaskPending OR UUID_STATUS_TASK = :uuidStatusTaskWp) " +
							"       AND UUID_MS_USER = :uuidMsUser", paramListTask);
					if (null != listTask && !listTask.isEmpty()) {
						Object [][] param = { {paramApplNo, taskH.getApplNo()},
								{"uuidStatusTaskPending", statusPending.getUuidStatusTask()},
								{"promisedDate", promisedDate}, {"usrUpd", callerId.getCallerId()},
								{"uuidStatusTaskWp", statusWaitingOnPending.getUuidStatusTask()} };
						this.getManagerDAO().updateNativeString(
								"UPDATE TR_TASK_H " +
								"SET    DTM_UPD = CURRENT_TIMESTAMP, USR_UPD = :usrUpd, PROMISE_DATE = :promisedDate " +
								"WHERE  PROMISE_DATE IS NULL " +
								"       AND APPL_NO = :applNo " +
								"       AND (UUID_STATUS_TASK = :uuidStatusTaskPending OR UUID_STATUS_TASK = :uuidStatusTaskWp)", param);
					} else {
						LOG.info("uuid Task H : {}, cannot be promised", uuidTaskH);
						return GlobalVal.SERVICES_RESULT_FAILED;
					}
				} else {
					LOG.info("uuid Task H : {}, already has promised date : {}", uuidTaskH, taskH.getPromiseDate());
				}
				return GlobalVal.SERVICES_RESULT_SUCCESS;
			}
		}
		if (null == taskH.getPromiseDate()) {
			taskH.setUsrUpd(callerId.getCallerId());
			taskH.setDtmUpd(new Date());
			taskH.setPromiseDate(promisedDate);
			this.getManagerDAO().update(taskH);
		} else {
			LOG.info("uuid Task H : {}, already has promised date : {}", uuidTaskH, taskH.getPromiseDate());
		}
		return GlobalVal.SERVICES_RESULT_SUCCESS;
	}
	
	private Integer getBranchDistance(String resLatitude, String resLongitude, BigDecimal branchLatitude, BigDecimal branchLongitude) {
		double doubResLatitude = Double.valueOf(resLatitude);
		double doubResLongitude = Double.valueOf(resLongitude);
		double doubBranchLatitude = branchLatitude.doubleValue();
		double doubBranchLongitude = branchLongitude.doubleValue();
		if ((doubResLatitude == doubBranchLatitude) && (doubResLongitude == doubBranchLongitude)) {
			return 0;
		}
		else {
			double theta = doubResLongitude - doubBranchLongitude;
			double dist = Math.sin(Math.toRadians(doubResLatitude)) * Math.sin(Math.toRadians(doubBranchLatitude))
					+ Math.cos(Math.toRadians(doubResLatitude)) * Math.cos(Math.toRadians(doubBranchLatitude)) * Math.cos(Math.toRadians(theta));
			dist = Math.acos(dist);
			dist = Math.toDegrees(dist);
			dist = dist * 60 * 1.1515;
			dist = dist * 1.609344;
			return (int) dist;
		}
	}
	
	
	private boolean isInSlaHandling(TrTaskH taskH, AuditContext auditContext) {
		boolean isInSla = false;
		Date createDate = taskH.getDtmCrt();
		Date currentTimestamp = new Date();
		
		Object[][] paramGroupTask = {{"uuidTaskH", taskH.getUuidTaskH()}};
		StringBuilder queryGroupTask = new StringBuilder(" SELECT GROUP_TASK_ID ")
				.append(" FROM MS_GROUPTASK WITH(NOLOCK) ")
				.append(" WHERE UUID_TASK_H = :uuidTaskH ");
		BigInteger groupTaskId = (BigInteger) this.getManagerDAO().selectOneNativeString(queryGroupTask.toString(), paramGroupTask);
		if (null != groupTaskId && groupTaskId.longValue()!=taskH.getUuidTaskH()) {
			TrTaskH firstTask = this.getManagerDAO().selectOne(TrTaskH.class, groupTaskId.longValue());
			createDate = firstTask.getPromiseDate();
		}
		
		long diffInMillies = currentTimestamp.getTime() - createDate.getTime();
		long diffDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
		String gsValue = globalLogic.getGsValue(GlobalVal.GENERAL_SETTING_SLA_IN_HANDLING, auditContext);
		
		long sla = Long.valueOf(StringUtils.isNotBlank(gsValue)?Long.valueOf(gsValue):0);
		
		if (diffDays <= sla) {
			isInSla = true;
		}
		return isInSla;
	}
	
	private void createTaskVisitForInterest (TrTaskH trTaskH, SubmitTaskDBean[] taskDBeanList, Map<Integer, MsQuestion> msQuestions, SaveTaskDResult saveResult, AuditContext auditContext) {
		AmMsuser amMsuser = this.getManagerDAO().selectOne(
				"from AmMsuser u " 
				+ "join fetch u.amMssubsystem "
				+ "where u.uuidMsUser = :uuidMsUser",
				new Object[][] { { "uuidMsUser", Long.valueOf(auditContext.getCallerId()) } });

		String prevTaskId = trTaskH.getTaskId();
		String formVersion = String.valueOf(trTaskH.getFormVersion());
		
		Map result = taskServiceLogic.addTask(auditContext, trTaskH.getFlagSource(),
				GlobalVal.SUBSYSTEM_MS, trTaskH.getMsForm().getFormName(),
				formVersion, trTaskH.getMsPriority().getPriorityDesc(), trTaskH.getCustomerName(), 
				trTaskH.getCustomerAddress(), trTaskH.getCustomerPhone(), trTaskH.getZipCode(),
				trTaskH.getNotes(),
				trTaskH.getMsBranch().getBranchCode(), trTaskH.getApplNo(),
				amMsuser.getLoginId(), MobileDefAnswer.DB.toString(), null,
				trTaskH.getLatitude() == null ? StringUtils.EMPTY : trTaskH.getLatitude().toString(),
				trTaskH.getLongitude() == null ? StringUtils.EMPTY : trTaskH.getLongitude().toString(), 
				null, amMsuser.getFullName(), null, prevTaskId, null, trTaskH.getIsPilotingCae());
		
		TrTaskH taskVisitRecreated = (TrTaskH) result.get("task");
		taskVisitRecreated.setIsRevisit("1");
		taskVisitRecreated.setVisitType(trTaskH.getVisitType());
		taskVisitRecreated.setStatusFollowUp(trTaskH.getStatusFollowUp());

		this.getManagerDAO().update(trTaskH);
		
		// Insert Question Is Revisit
		MsQuestion questionIsRevisit = commonLogic.retrieveQuestionByRefId(GlobalVal.REF_STV_ISREVISIT, auditContext);
		this.insertTaskDSurvey(auditContext, taskVisitRecreated, questionIsRevisit, null, null, "YA",
				null, null, null, null, null, null, null, null, null);
		
		// Get Question Notes Visit
		MsQuestion questionNotesVisit = commonLogic.retrieveQuestionByRefId(GlobalVal.REF_STV_NTS_VST, auditContext);
		
		Object [][] prmNtsVst = { {"uuidTaskH", trTaskH.getUuidTaskH()}, {"questionId", questionNotesVisit.getUuidQuestion()} };
		StringBuilder strBuilderNtsVst = new StringBuilder();
		strBuilderNtsVst.append("SELECT TTD.TEXT_ANSWER ");
		strBuilderNtsVst.append("FROM   TR_TASK_D TTD WITH (NOLOCK) ");
		strBuilderNtsVst.append("WHERE  TTD.UUID_QUESTION = :questionId AND TTD.UUID_TASK_H = :uuidTaskH");
		String ntsVst = (String) this.getManagerDAO().selectOneNativeString(strBuilderNtsVst.toString(), prmNtsVst);
		
		MsQuestion questionNotesVisitSebelum = commonLogic.retrieveQuestionByRefId(GlobalVal.REF_STV_NTS_VST_SEBELUM, auditContext);
		this.insertTaskDSurvey(auditContext, taskVisitRecreated, questionNotesVisitSebelum, null, null, ntsVst,
				null, null, null, null, null, null, null, null, null);

		String notesH = "Task with ID: " + trTaskH.getTaskId() + " has been created another task because of interest";

		this.insertTaskHistory(auditContext, trTaskH.getMsStatustask(), trTaskH, notesH, 
				GlobalVal.CODE_PROCESS_RELEASED, amMsuser.getLoginId(), amMsuser, null);
	}
	
	private boolean guarantorRecreateValid(String uuidTaskH, boolean isFatal) {
		// menghitung task sudah berapa kali dibuat ulang
		StringBuilder query = new StringBuilder();
		query.append("WITH N (UUID_TASK_H, RESURVEY_ID) AS ( ");
		query.append("select ");
		query.append("    UUID_TASK_H, ");
		query.append("    RESURVEY_ID ");
		query.append("from ");
		query.append("    TR_TASK_H ");
		query.append("where ");
		query.append("    UUID_TASK_H = :uuidTaskH ");
		query.append("union all ");
		query.append("select ");
		query.append("    TR_TASK_H.UUID_TASK_H, ");
		query.append("    TR_TASK_H.RESURVEY_ID ");
		query.append("from ");
		query.append("    N ");
		query.append("    JOIN TR_TASK_H ON N.RESURVEY_ID = TR_TASK_H.UUID_TASK_H ");
		query.append(") ");
		query.append("select ");
		query.append("  count(1) - 1 ");
		query.append("from ");
		query.append("  N ");
		int recreateCount = (int) this.getManagerDAO().selectOneNativeString(query.toString(),
				new Object[][] { { "uuidTaskH", Long.valueOf(uuidTaskH) } });

		AmGeneralsetting guarantorRecreateCountGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class,
				new Object[][] { { paramGsCode, "GUARANTOR_MAX_RECREATE_COUNT_TRY" } });
		
		int maxRecreateCount = 0;
		if (guarantorRecreateCountGeneralSetting != null) {
			maxRecreateCount = Integer.parseInt(guarantorRecreateCountGeneralSetting.getGsValue());
		}
		return (recreateCount < maxRecreateCount && isFatal);
	}

	private void recreateGuarantor(AuditContext auditContext, TrTaskH trTaskH) {
		String flagSource = trTaskH.getFlagSource();
		String subSystemCode = GlobalVal.SUBSYSTEM_MS;
		String schemaID = GlobalVal.FORM_GUARANTOR;
		String formVersion = Integer.toString(trTaskH.getFormVersion());
		String customerName = trTaskH.getCustomerName();
		String priority = trTaskH.getMsPriority().getPriorityDesc();
		String customerAddress = trTaskH.getCustomerAddress();
		String customerPhone = trTaskH.getCustomerPhone();
		String zipCode = trTaskH.getZipCode();
		String notes = trTaskH.getNotes();
		String branchID = trTaskH.getMsBranch().getBranchCode();
		String refNumber = trTaskH.getApplNo();
		String fieldPersonID = trTaskH.getAmMsuser().getLoginId();
		String isNeedDefAnswer = MobileDefAnswer.DB.toString();
		String surveyAssignmentID = null;
		String latitude = trTaskH.getLatitude() == null ? StringUtils.EMPTY : trTaskH.getLatitude().toString();
		String longitude = trTaskH.getLongitude() == null ? StringUtils.EMPTY : trTaskH.getLongitude().toString();
		String taskID = null;
		String userUpdateName = trTaskH.getUsrCrt();
		AddTaskDetailBean[] listTaskDetail = null;
		String prevTaskID = Long.toString(trTaskH.getUuidTaskH());
		String mode = "";

		this.taskServiceLogic.addTask(auditContext, flagSource, subSystemCode, schemaID, formVersion, priority,
				customerName, customerAddress, customerPhone, zipCode, notes, branchID, refNumber, fieldPersonID,
				isNeedDefAnswer, surveyAssignmentID, latitude, longitude, taskID, userUpdateName, listTaskDetail,
				prevTaskID, mode, trTaskH.getIsPilotingCae());
	}
	
	private void processTaskPilotingCAE(TrTaskH trTaskH, AmMssubsystem amMssubsytem, String notes,
			String flagSendOffline, AuditContext auditContext) {
		if (GlobalVal.FORM_GUARANTOR.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
//			boolean isFatal = 1 != this.taskGuarantorValidation(trTaskH, auditContext);
//			if (this.guarantorRecreateValid(Long.toString(trTaskH.getUuidTaskH()), isFatal)) {
//				this.recreateGuarantor(auditContext, trTaskH);
//			} else {
				intFormLogic.submitTaskGuarantor(auditContext, trTaskH);
//			}
		} else {
			if (GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
				intFormLogic.submitPolo(null, trTaskH, GlobalVal.FLAG_TASK_PRE_SURVEY, flagSendOffline, auditContext);
			}
			
			this.doUpdateAndInsertHistoryOfflineTask(trTaskH, amMssubsytem, false, notes, flagSendOffline, auditContext);
		}
	}
	
	private int taskGuarantorValidation(TrTaskH trTaskH, AuditContext auditContext) {
		int result = 1;
		Integer isPreApproval = null;
		if(trTaskH.getIsPreApproval() != null) {
			isPreApproval = trTaskH.getIsPreApproval();
		}
		List <Map<String, Object>> listProcessGuarantor = this.getManagerDAO().selectAllNativeString(
				"SELECT    PROCESS, PARAM_API " + 
				"FROM      MS_MAPPINGSUBMITLAYER WITH (NOLOCK) " + 
				"WHERE     UUID_FORM = :uuidForm " +
				"ORDER BY  PROCESS_SEQ",
				new Object[][] { {"uuidForm", trTaskH.getMsForm().getUuidForm()} });
		for (int i = 0; i < listProcessGuarantor.size(); i++) {
			if (result == 1) {
				Map<String, Object> mapProcess = listProcessGuarantor.get(i);
				Map<String, String> paramApi = this.buildGuarantorValidationParam(trTaskH, mapProcess.get("d1").toString(), auditContext);
				String process = mapProcess.get("d0").toString();
				if (GlobalVal.PRE_SURVEY_PROCESS_BIOMETRIC.equals(process)) {
					BiometricRequest requestBiometric = new BiometricRequest();
					requestBiometric.setFilter(paramApi);
					BiometricResponse response = submitLayerLogic.doBiometric(requestBiometric, trTaskH.getMsForm().getFormName(), auditContext);
					result = response.getStatus().getCode();
				} else {
					if (GlobalVal.PRE_SURVEY_PROCESS_DUKCAPIL.equals(process)) {
						result = submitLayerLogic.doValidateDukcapil(paramApi, isPreApproval, auditContext);
					} else if (GlobalVal.PRE_SURVEY_PROCESS_NEGATIVE_CUST.equals(process)) {
						result = submitLayerLogic.doCheckNegativeCust(paramApi, isPreApproval, auditContext);
					} else if (GlobalVal.PRE_SURVEY_PROCESS_PHONE.equals(process)) {
						result = submitLayerLogic.doTeleStatus(paramApi, isPreApproval, auditContext);
					}
				}
			} else {
				break;
			}			
		}
		
		return result;
	}
	
	private Map<String, String> buildGuarantorValidationParam(TrTaskH trTaskH, String param, AuditContext auditContext) {
		Map<String, String> filter = new HashMap<>();			
		String[] listParam = param.split(";");
		
		for (int i = 0; i < listParam.length; i++) {
			String[] paramKeyVal = listParam[i].split("@@@");
			MsQuestion msQuestion = commonLogic.retrieveQuestionByRefId(paramKeyVal[1], auditContext);
			this.getManagerDAO().fetch(msQuestion.getMsAnswertype());
			String answer = null;
			Object [][] prmGetAnswer = { {"uuidTaskH", trTaskH.getUuidTaskH()}, {"questionId", msQuestion.getUuidQuestion()} };
			if (MssTool.isImageQuestion(msQuestion.getMsAnswertype().getCodeAnswerType())) {
				StringBuilder strBuilderLob = new StringBuilder();
				strBuilderLob.append("SELECT TTDL.UUID_TASK_DETAIL_LOB ");
				strBuilderLob.append("FROM   TR_TASKDETAILLOB TTDL WITH (NOLOCK) ");
				strBuilderLob.append("WHERE  TTDL.QUESTION_ID = :questionId AND TTDL.UUID_TASK_H = :uuidTaskH");
				Object objLob = this.getManagerDAO().selectOneNativeString(strBuilderLob.toString(), prmGetAnswer);
				if (null != objLob) {
					String uuidLob = String.valueOf(objLob);
					if ("1".equals(this.link_encrypt)) {
						String[] temp = { uuidLob };
						uuidLob = CipherTool.encryptData(temp).get(0).toString();
					}
					byte[] byteImage = lazyLoadLogic.getImageLob(uuidLob, auditContext);
					if (null != byteImage) {
						answer = java.util.Base64.getEncoder().encodeToString(byteImage);
					}
				}
			} else {
				StringBuilder strBuilderTaskD = new StringBuilder();
				strBuilderTaskD.append("SELECT TTD.TEXT_ANSWER ");
				strBuilderTaskD.append("FROM   TR_TASK_D TTD WITH (NOLOCK) ");
				strBuilderTaskD.append("WHERE  TTD.UUID_QUESTION = :questionId AND TTD.UUID_TASK_H = :uuidTaskH");
				Object objTaskD = (this.getManagerDAO().selectOneNativeString(strBuilderTaskD.toString(), prmGetAnswer));
				if (null != objTaskD) {
					answer = objTaskD.toString();
				}
			}
			if (StringUtils.isNotBlank(answer)) {
				filter.put(paramKeyVal[0], answer);
			}
		}
		
		return filter;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public AddTaskScoringCAEResponse addTaskScoringCAE(AddTaskScoringCAERequest request) {
		LOG.info("Add Task Scoring CAE with group task id: {}", request.getMobilePreSurveyTaskId());
		LOG.info("Request Add Task Scoring : {}", gson.toJson(request));
		TrTaskH trTaskH = this.validateAddTaskScoring(request);
		
		AuditContext auditContext = new AuditContext();
		auditContext.setCallerId("Add Task Scoring CAE");
		
		String jsonRequest = null;
		boolean isFromPoloVisit  = false;
		if (StringUtils.isNotBlank(trTaskH.getOrderNoCae())) {
			Object[][] prmCaeData = { {Restrictions.eq("orderNoCae", trTaskH.getOrderNoCae())},
					{Restrictions.eq(paramIsSuccess, "1")}, {Restrictions.eq(paramGroupTaskId, Long.valueOf(request.getMobilePreSurveyTaskId()))} };
			TblCaeData data = this.getManagerDAO().selectOne(TblCaeData.class, prmCaeData);
			jsonRequest = data.getJsonRequest();
		} else {
			Object[][] prmPoloData = { {Restrictions.eq("taskIdPolo", trTaskH.getTaskIdPolo())},
					{Restrictions.eq(paramIsSuccess, "1")}, {Restrictions.eq(paramGroupTaskId, Long.valueOf(request.getMobilePreSurveyTaskId()))} };
			TblPoloData poloData = this.getManagerDAO().selectOne(TblPoloData.class, prmPoloData);
			jsonRequest = poloData.getJsonRequest();
			isFromPoloVisit = true;
		}
		
		String taskIdCreated = null;
		if ("1".equals(request.getResultScoring())) {
			taskIdCreated = taskServiceLogic.createTaskCAEFromSubmitTask(trTaskH, jsonRequest, GlobalVal.FLAG_TASK_COMPLETED,
					isFromPoloVisit, false, false, request, false, auditContext);
		} else {
			taskIdCreated = taskServiceLogic.createTaskCAEFromSubmitTask(trTaskH, jsonRequest, GlobalVal.FLAG_TASK_SURVEY_PILOTING,
					isFromPoloVisit, false, false, request, false, auditContext);
		}
		
		AddTaskScoringCAEResponse response = new AddTaskScoringCAEResponse();
		response.setCode(1);
		response.setMessage(GlobalVal.SERVICES_RESULT_SUCCESS);
		response.setMobileSurveyTaskId(taskIdCreated);
		
		return response;
	}
	
	private TrTaskH validateAddTaskScoring(AddTaskScoringCAERequest request) {
		if (StringUtils.isBlank(request.getMobilePreSurveyTaskId())) {
			throw new RemoteException("Empty mobilePreSurveyTaskId");
		}
		if (StringUtils.isBlank(request.getResultScoring())) {
			throw new RemoteException("Empty resultScoring");
		}
		        
	//Karena ketika submit pre survey task id yang dikirim ke polo adalah group task id, 
		//maka ketika add task scoring perlu cari lagi task pre surveynya menggunakan group task id yang sebelumnya dikirim
		Object[][] param = { {paramGroupTaskId, request.getMobilePreSurveyTaskId()},  {"formName", GlobalVal.FORM_PRE_SURVEY}  };
		BigInteger uuidTaskH = (BigInteger) this.getManagerDAO().selectOneNativeString(
				"select msg.UUID_TASK_H from MS_GROUPTASK msg with(nolock)"
						+ " join TR_TASK_H tth with(nolock) on tth.UUID_TASK_H = msg.UUID_TASK_H"
						+ " join MS_FORM msf with(nolock) on tth.UUID_FORM = msf.UUID_FORM"
						+ " where msg.GROUP_TASK_ID = :groupTaskId and msf.FORM_NAME = :formName ", param);
		
		if (null == uuidTaskH) {
			throw new EntityNotFoundException("Task Pre Survey with mobilePreSurveyTaskId " + uuidTaskH + " is not found", request.getMobilePreSurveyTaskId());
		}
		
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTaskH.longValue());
		
		this.getManagerDAO().fetch(trTaskH.getMsStatustask());
		if (!GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(trTaskH.getMsStatustask().getStatusCode())) {
			throw new EntityNotFoundException("Task Pre Survey not released yet", request.getMobilePreSurveyTaskId());
		}
		
		String[][] prmH = { {paramApplNo, trTaskH.getApplNo()} };
		Integer resultCheck = (Integer) this.getManagerDAO().selectOneNativeString(
				"SELECT COUNT(1) " +
				"FROM   TR_TASK_H TTH WITH (NOLOCK) " +
				"JOIN   MS_STATUSTASK MSS WITH (NOLOCK) ON MSS.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK " +
				"WHERE  MSS.STATUS_CODE != 'D' AND TTH.APPL_NO = :applNo " +
				"       AND TTH.FLAG_SOURCE IN ('MSCOREF', 'MSCOREP', 'MSIAFOTO')", prmH);
		if (0 != resultCheck) {
			throw new RemoteException("Failed add task scoring caused by: task created from mobilePreSurveyTaskId " + request.getMobilePreSurveyTaskId() + " already exists");
		}	

		return trTaskH;
	}

	@Transactional(readOnly=true)
	@Override
	public Map<String, String> submitPolo(AddTaskScoringCAERequest request) {
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class,
				new Object[][] { {Restrictions.eq(paramTaskId, request.getMobilePreSurveyTaskId())} });
		this.getManagerDAO().fetch(trTaskH.getMsForm());
		AuditContext auditContext = new AuditContext();
		auditContext.setCallerId("Add Task Scoring CAE");
		String response = this.intFormLogic.submitPolo(null, trTaskH, GlobalVal.FLAG_TASK_COMPLETED, null, auditContext);
		Map<String, String> result = new HashMap<>();
		result.put("result", response);
		
		return result;
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public Map<String, String> autoSubmitPolo(String taskId, AuditContext auditContext) {
		Map<String, String> result = new HashMap<>();
		
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, Long.parseLong(taskId));
		
		updateSubmitAnswer(trTaskH.getUuidTaskH());
		
		String response = this.intFormLogic.submitPolo(null, trTaskH, GlobalVal.FLAG_TASK_AUTO_SUBMIT_PREAPPROVAL, null, auditContext);
		
		if (GlobalVal.SERVICES_RESULT_SUCCESS.equalsIgnoreCase(response)) {
			trTaskH.setUsrUpd(auditContext.getCallerId());
			trTaskH.setDtmUpd(new Date());
			trTaskH.setAutoSubmitPolo(0);
			this.getManagerDAO().update(trTaskH);
		
			result.put("result", response);
		}
		return result;
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public String setMaxRetry(String taskId, AuditContext callerId) {
		
		Integer maxRetry = null;
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, Long.parseLong(taskId));
		
		maxRetry = (trTaskH.getMaxRetry()) + 1;
		
		trTaskH.setUsrUpd(callerId.getCallerId());
		trTaskH.setDtmUpd(new Date());
		trTaskH.setMaxRetry(maxRetry);
		this.getManagerDAO().update(trTaskH);
		
		return taskId;
	} 
	
	private void doUpdateAndInsertHistoryOfflineTask(TrTaskH trTaskH, AmMssubsystem amMssubsytem,
			boolean flagDropTask, String notes, String flagSendOffline, AuditContext auditContext) {
		Date now = new Date();
		StringBuilder queryJoin = new StringBuilder();
		StringBuilder queryWhere = new StringBuilder();
		String statusCode = GlobalVal.SURVEY_STATUS_TASK_DELETED;
		String statusCodeMobile = GlobalVal.STATUS_MOBILE_DELETED;
		
		// Ketika flagSendOffline tidak ada isinya, maka proses submit yang dilakukan adalah submit online
		if (GlobalVal.FORM_PROMISE_TO_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName()) && !flagDropTask) {
			// Kondisi yang perlu dilakukan adalah ketika form task submit adalah PTS dan tidak drop task,
			// maka melakukan update status task Pre Survey menjadi pending
			statusCode = GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION;
			statusCodeMobile = GlobalVal.STATUS_MOBILE_NEW;
			
			if (StringUtils.isNotBlank(isHavePreSurvey(trTaskH.getApplNo()))) {
				queryJoin.append("JOIN MS_FORM MSF ON MSF.UUID_FORM = TTH.UUID_FORM ");
				queryWhere.append("AND MSF.FORM_NAME = '" + GlobalVal.FORM_PRE_SURVEY + "' ");
			}
			
		} else if (GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
			// Ketika form task submit adalah Pre Survey,
			// jika online maka melakukan submit polo dan update status task survey menjadi deleted, karena task survey akan dibentuk melalui addTaskScoring
			// jika drop task dan task berasal dari addTaskPolo, makan task promise to visit dan task visit nya akan di delete
			if (flagDropTask && StringUtils.isNotBlank(trTaskH.getTaskIdPolo())) {
				queryJoin.append("JOIN MS_FORM MSF ON MSF.UUID_FORM = TTH.UUID_FORM ");
				queryWhere.append("AND MSF.FORM_NAME != '" + GlobalVal.FORM_PRE_SURVEY + "' ");
				
			} else {
				queryWhere.append("AND TTH.FLAG_SOURCE <> 'MSCORE' ");
			}			
			// jika offline maka update next task jadi pending
			if (StringUtils.isNotBlank(flagSendOffline) && !flagDropTask) {
				statusCode = GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION;
				statusCodeMobile = GlobalVal.STATUS_MOBILE_NEW;
			}
		}
		
		MsStatustask currStatusTask = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_WAITING_ON_PENDING,
				amMssubsytem.getUuidMsSubsystem(), auditContext);
		MsStatustask updStatusTask = commonLogic.retrieveStatusTask(statusCode, amMssubsytem.getUuidMsSubsystem(), auditContext);
		if (flagDropTask) {
			currStatusTask = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_UPLOADING,
					amMssubsytem.getUuidMsSubsystem(), auditContext);
		}
		
		Object [][] prmUpdNextTask = { {paramApplNo, trTaskH.getApplNo()}, {"dtmUpd", now},
				{"uuidStatusTask", updStatusTask.getUuidStatusTask()}, {"codeStatusMobile", statusCodeMobile},
				{"usrUpd", String.valueOf(trTaskH.getAmMsuser().getUuidMsUser())},
				{"currStatusTask", currStatusTask.getUuidStatusTask()} };
		StringBuilder queryUpdNextTask = new StringBuilder();
		queryUpdNextTask.append("UPDATE TTH ");
		queryUpdNextTask.append("SET    TTH.UUID_STATUS_TASK = :uuidStatusTask, ");
		queryUpdNextTask.append("       TTH.STATUS_MOBILE_SEQ_NO = (SELECT STATUS_MOBILE_SEQ_NO FROM MS_STATUSMOBILE WHERE STATUS_MOBILE_CODE = :codeStatusMobile), ");
		queryUpdNextTask.append("       TTH.DTM_UPD = :dtmUpd, ");
		queryUpdNextTask.append("       TTH.USR_UPD = :usrUpd ");
		queryUpdNextTask.append("FROM   TR_TASK_H TTH ");
		queryUpdNextTask.append(queryJoin);
		queryUpdNextTask.append("WHERE  TTH.APPL_NO = :applNo ");
		if (flagDropTask) {
			queryUpdNextTask.append("       AND TTH.UUID_STATUS_TASK != :currStatusTask ");
		} else {
			queryUpdNextTask.append("       AND TTH.UUID_STATUS_TASK = :currStatusTask ");
		}
		queryUpdNextTask.append(queryWhere);
		
		this.getManagerDAO().updateNativeString(queryUpdNextTask.toString(), prmUpdNextTask);
		
		if (GlobalVal.SURVEY_STATUS_TASK_DELETED.equals(statusCode)) {
			
			if (StringUtils.isBlank(notes) && GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
				notes = "Deleted Survey for Update Task.";
			}else {
				notes = StringUtils.EMPTY;
			}
			
			Object [][] prmInsHistoryNextTask = { {paramApplNo, trTaskH.getApplNo()}, {"dtmCrt", now},
					{"uuidStatusTask", updStatusTask.getUuidStatusTask()}, {"usrCrt", trTaskH.getAmMsuser().getLoginId()},
					{"currStatusTask", currStatusTask.getUuidStatusTask()}, {"notes", notes},
					{"codeProcess", GlobalVal.CODE_PROCESS_DELETED} };
			StringBuilder insTrHistory = new StringBuilder();
			insTrHistory.append("INSERT INTO TR_TASKHISTORY (USR_CRT, DTM_CRT, UUID_TASK_H, UUID_STATUS_TASK, NOTES, ACTOR, CODE_PROCESS) ");
			insTrHistory.append("SELECT :usrCrt, :dtmCrt, TTH.UUID_TASK_H, :uuidStatusTask, :notes, :usrCrt, :codeProcess ");
			insTrHistory.append("FROM   TR_TASK_H TTH ");
			insTrHistory.append(queryJoin);
			insTrHistory.append("WHERE  TTH.APPL_NO = :applNo ");
			insTrHistory.append("       AND TTH.UUID_STATUS_TASK = :currStatusTask ");
			insTrHistory.append(queryWhere);
			
			this.getManagerDAO().insertNativeString(insTrHistory.toString(), prmInsHistoryNextTask);
			
			if (StringUtils.isBlank(notes) || !GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
				
				AddTaskCAERequest tblRequestBean = new AddTaskCAERequest();
				AddTaskPoloRequest tblPoloRequestBean = new AddTaskPoloRequest();
				String isIA = StringUtils.EMPTY;
				Integer isPreApproval = 0;
				AmMsuser submitterBean = this.getManagerDAO().selectOne(AmMsuser.class, new Object[][] {{Restrictions.eq("uuidMsUser", trTaskH.getAmMsuser().getUuidMsUser())}});
 
				if(!(GlobalVal.FORM_PROMISE_TO_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName()) || GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName()))) {
					this.getManagerDAO().fetch(trTaskH.getMsGrouptasks());
					MsGrouptask groupTask = trTaskH.getMsGrouptasks().iterator().next();
					
					if (StringUtils.isNotBlank(trTaskH.getTaskIdPolo())) {
						TblPoloData poloBean = this.getManagerDAO().selectOne(TblPoloData.class, 
								new Object[][] {
							{Restrictions.eq("taskIdPolo", trTaskH.getTaskIdPolo())}, 
							{Restrictions.eq("groupTaskId", groupTask.getGroupTaskId())},
							{Restrictions.eq("isSuccess", "1")}
						});
						tblPoloRequestBean = gson.fromJson(poloBean.getJsonRequest(), AddTaskPoloRequest.class);
						isPreApproval = tblPoloRequestBean.getIsPreApproval();
					} else {
						TblCaeData tblBean = this.getManagerDAO().selectOne(TblCaeData.class, 
								new Object[][] {
									{Restrictions.eq("orderNoCae", trTaskH.getOrderNoCae())}, 
									{Restrictions.eq("groupTaskId", groupTask.getGroupTaskId())},
									{Restrictions.eq("isSuccess", "1")}
								});
						tblRequestBean = gson.fromJson(tblBean.getJsonRequest(), AddTaskCAERequest.class);
						isIA = tblBean.getIsIa();
						isPreApproval = tblRequestBean.getIsPreApproval();
					}
					
					String mssStat = StringUtils.EMPTY;
					
					MsStatustask rejectResurveyStat = this.getManagerDAO().selectOne(MsStatustask.class, 
							new Object[][] {{Restrictions.eq("statusCode",GlobalVal.SURVEY_STATUS_TASK_REJECTED_WITH_RESURVEY)}});
					TrTaskH previousTask = this.getManagerDAO().selectOne(TrTaskH.class, 
							new Object[][] {
						{Restrictions.eq("flagSource", trTaskH.getFlagSource())},
						{Restrictions.eq("applNo", trTaskH.getApplNo())},
						{Restrictions.eq("msStatustask.uuidStatusTask",rejectResurveyStat.getUuidStatusTask())}
					});
					if(null == previousTask) {
						if("1".equals(isIA) || 1 == isPreApproval) {
							if(GlobalVal.USR_CRT_ADD_TASK_SCORING_CAE.equalsIgnoreCase(trTaskH.getUsrCrt()) 
									|| GlobalVal.USR_CRT_AUTO_CANCEL_TASK.equalsIgnoreCase(trTaskH.getUsrCrt())) {
								mssStat = GlobalVal.UPDATE_STATUS_IDE_CANCEL_RESURVEY;
							} else {
								
								mssStat = GlobalVal.UPDATE_STATUS_IDE_DELETE;
							}
						}else {
							mssStat = GlobalVal.UPDATE_STATUS_IDE_DELETE;
						}
					} else {
						mssStat = GlobalVal.UPDATE_STATUS_IDE_CANCEL_RESURVEY;
					}
					this.intFormLogic.updateStatusIDE(trTaskH, mssStat, mssStat, submitterBean, null, auditContext);
				}
				
				intFormLogic.updateDataPolo(null, trTaskH, null, deleted, "F", this.getNegativeCustomer(trTaskH.getApplNo(), trTaskH.getCmoRecommendation()), null, null, null, auditContext);				
			}
		}
	}
	
	private String getNegativeCustomer(String applNo, String isCmoRecommendation) {
		if (StringUtils.isNotBlank(isCmoRecommendation) && "0".equalsIgnoreCase(isCmoRecommendation)) {
			return "MATCH";
		} else {
			StringBuilder queryGetNegativeCustomer = new StringBuilder();
			queryGetNegativeCustomer.append(" SELECT     TOP 1 ISNULL(TEXT_ANSWER, CASE WHEN msl.CODE = '1' THEN 'MATCH' ELSE 'NOT MATCH' END) AS ANSWER ");
			queryGetNegativeCustomer.append(" FROM       TR_TASK_D ttd with(nolock) ");
			queryGetNegativeCustomer.append(" JOIN       TR_TASK_H tth with(nolock) on tth.UUID_TASK_H = ttd.UUID_TASK_H ");
			queryGetNegativeCustomer.append(" JOIN       MS_QUESTION msq with(nolock) on msq.UUID_QUESTION = ttd.UUID_QUESTION ");
			queryGetNegativeCustomer.append(" LEFT JOIN  MS_LOV msl with (nolock) on msq.LOV_GROUP = msl.LOV_GROUP AND ttd.LOV_ID = msl.UUID_LOV ");
			queryGetNegativeCustomer.append(" WHERE      (REF_ID = 'PRE_RESULT_NEGATIVE' OR REF_ID = 'STV_NEGATIVE_CUST') AND tth.APPL_NO = :orderNo ");
			queryGetNegativeCustomer.append(" ORDER BY   ttd.UUID_TASK_D DESC ");
			
			return (String) this.getManagerDAO().selectOneNativeString(queryGetNegativeCustomer.toString(),
					new Object[][] { {"orderNo", applNo} });
		}
	}
	
	private String getFlagSendOfflineByTask(SubmitTaskHBean taskHBean, String formName) {
		String result = null;
		if (GlobalVal.FORM_PROMISE_TO_SURVEY.equals(formName)) {
			result = taskHBean.getSendTaskPromiseToSurvey();
		} else if (GlobalVal.FORM_PRE_SURVEY.equals(formName)) {
			result = taskHBean.getSendTaskPreSurvey();
		} else {
			result = taskHBean.getSendTaskSurvey();
		}
		
		return result;
	}
	
	private String isHavePreSurvey(String applNo) {
		Object taskPreSurvey = this.getManagerDAO().selectOneNativeString(
				"SELECT ISNULL(TTH.IS_OFFLINE, '0') AS IS_OFFLINE " +
				"FROM   TR_TASK_H TTH WITH (NOLOCK) " +
				"JOIN   MS_FORM MSF WITH (NOLOCK) ON MSF.UUID_FORM = TTH.UUID_FORM " +
				"WHERE  APPL_NO = :applNo " +
				"       AND MSF.FORM_NAME = :formName",
				new Object [][] { {paramApplNo, applNo}, {"formName", GlobalVal.FORM_PRE_SURVEY} });
		return null != taskPreSurvey ? String.valueOf(taskPreSurvey) : null;
	}
	
	public boolean canRecreateTaskVisit(long uuidTaskH, AuditContext auditContext) {
		StringBuilder query = new StringBuilder();
		query.append(" WITH N (UUID_TASK_H, RESURVEY_ID) AS ( ");
		
		query.append(" SELECT UUID_TASK_H, RESURVEY_ID ");
		query.append(" FROM TR_TASK_H WITH (NOLOCK) ");
		query.append(" WHERE UUID_TASK_H = :uuidTaskH ");
		
		query.append(" UNION ALL ");
		
		query.append(" SELECT TR_TASK_H.UUID_TASK_H, TR_TASK_H.RESURVEY_ID ");
		query.append(" FROM N ");
		query.append(" JOIN TR_TASK_H WITH (NOLOCK) ON N.RESURVEY_ID = TR_TASK_H.UUID_TASK_H ");
		
		query.append(") ");
		query.append(" SELECT count(1) ");
		query.append(" FROM N ");
		int recreateCount = (int) this.getManagerDAO().selectOneNativeString(query.toString(),
				new Object[][] { {"uuidTaskH", uuidTaskH} });
		
		AmGeneralsetting gsMaxTaskVisit = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_MAX_TASKVISIT_INTEREST, auditContext);
		if (gsMaxTaskVisit != null && StringUtils.isNumeric(gsMaxTaskVisit.getGsValue())) {
			return (recreateCount < Integer.parseInt(gsMaxTaskVisit.getGsValue()));
		}
		return true;
	}

	public void updateSubmitAnswer(long taskID) {
		//Pre Approval Auto Submit > Update all Submitted Answer from Default Answer
		
		//Get Group Task ID
		Object[][] params = {
				{Restrictions.eq("trTaskH.uuidTaskH", taskID)}
		};
		MsGrouptask groupTask = this.getManagerDAO().selectOne(MsGrouptask.class, params);
		long groupTaskID = groupTask.getGroupTaskId();
		
		LOG.info("updateSubmitAnswer: {}", groupTaskID);
		
		String queryUpdate = "UPDATE C\r\n"
				+ "SET	TEXT_ANSWER = C.INT_TEXT_ANSWER\r\n"
				+ "	,LOV_ID = C.INT_LOV_ID\r\n"
				+ "	,OPTION_TEXT = (SELECT [DESCRIPTION] FROM MS_LOV with(nolock) WHERE UUID_LOV = C.INT_LOV_ID)\r\n"
				+ "	,ID_STAGING_ASSET = C.INT_ID_STAGING_ASSET\r\n"
				+ "FROM MS_GROUPTASK A with(nolock)\r\n"
				+ "JOIN TR_TASK_D C with(nolock) ON A.UUID_TASK_H = C.UUID_TASK_H\r\n"
				+ "WHERE A.GROUP_TASK_ID = :groupTaskID\r\n";
		Object[][] paramsUpdate = {
				{"groupTaskID", groupTaskID}
		};
		
		this.getManagerDAO().updateNativeString(queryUpdate, paramsUpdate);
	}
	
	private void doUpdateTaskPoloVisit(TrTaskH trTaskH, AmMssubsystem amMssubsytem, String ptvDate, AuditContext auditContext) {
		Date now = new Date();
		StringBuilder queryJoin = new StringBuilder();
		StringBuilder queryWhere = new StringBuilder();
		String statusCode = GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION;
		String statusCodeMobile = GlobalVal.STATUS_MOBILE_NEW;

		MsStatustask currStatusTask = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_WAITING_ON_PENDING,
				amMssubsytem.getUuidMsSubsystem(), auditContext);
		MsStatustask updStatusTask = commonLogic.retrieveStatusTask(statusCode, amMssubsytem.getUuidMsSubsystem(), auditContext);
		
		if(StringUtils.isNotBlank(ptvDate)) {
			try {
				this.insertDefaultTanggalVisit(trTaskH, ptvDate, amMssubsytem, auditContext);			
			} catch (Exception e) {
				LOG.error("Failed Update tanggal Visit: ", e);
			}			
		}
 
		queryJoin.append("JOIN MS_FORM MSF ON MSF.UUID_FORM = TTH.UUID_FORM ");
		queryWhere.append("AND MSF.FORM_NAME = '" + GlobalVal.FORM_VISIT_POLO + "' ");
		 
		
		Object [][] prmUpdNextTask = { {paramApplNo, trTaskH.getApplNo()}, {"dtmUpd", now},
				{"uuidStatusTask", updStatusTask.getUuidStatusTask()}, {"codeStatusMobile", statusCodeMobile},
				{"usrUpd", String.valueOf(trTaskH.getAmMsuser().getUuidMsUser())},
				{"currStatusTask", currStatusTask.getUuidStatusTask()} };
		StringBuilder queryUpdNextTask = new StringBuilder();
		queryUpdNextTask.append("UPDATE TTH ");
		queryUpdNextTask.append("SET    TTH.UUID_STATUS_TASK = :uuidStatusTask, ");
		queryUpdNextTask.append("       TTH.STATUS_MOBILE_SEQ_NO = (SELECT STATUS_MOBILE_SEQ_NO FROM MS_STATUSMOBILE WHERE STATUS_MOBILE_CODE = :codeStatusMobile), ");
		queryUpdNextTask.append("       TTH.DTM_UPD = :dtmUpd, ");
		queryUpdNextTask.append("       TTH.USR_UPD = :usrUpd ");
		queryUpdNextTask.append("FROM   TR_TASK_H TTH ");
		queryUpdNextTask.append(queryJoin);
		queryUpdNextTask.append("WHERE  TTH.APPL_NO = :applNo ");
		queryUpdNextTask.append("       AND TTH.UUID_STATUS_TASK = :currStatusTask ");
		queryUpdNextTask.append(queryWhere);
		
		this.getManagerDAO().updateNativeString(queryUpdNextTask.toString(), prmUpdNextTask);
		
	}

	private void insertDefaultTanggalVisit(TrTaskH trTaskH, String ptvDate, AmMssubsystem amMssubsytem, AuditContext auditContext) {
		
		MsStatustask currStatusTask = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_WAITING_ON_PENDING,
				amMssubsytem.getUuidMsSubsystem(), auditContext);
		MsForm msForm = commonLogic.retrieveMsFormByName(GlobalVal.FORM_VISIT_POLO, true, amMssubsytem.getUuidMsSubsystem(), auditContext);
		Object[][] paramsTaskVisit = { { Restrictions.eq("applNo", trTaskH.getApplNo()) },
				{ Restrictions.eq("msForm.uuidForm", msForm.getUuidForm()) },
				{ Restrictions.eq("msStatustask.uuidStatusTask", currStatusTask.getUuidStatusTask()) } };
		
		TrTaskH taskHVisit = this.getManagerDAO().selectOne(TrTaskH.class, paramsTaskVisit);
		
		if(null==taskHVisit) {
			return;
		}
		
		TrTaskD taskD = null;
		
		MsQuestion questionBean = commonLogic.retrieveQuestionByRefId(GlobalVal.REF_STV_VISIT_DATE, auditContext);
		
		Object[][] params = { { Restrictions.eq("trTaskH.uuidTaskH", taskHVisit.getUuidTaskH()) },
				{ Restrictions.eq("msQuestion.uuidQuestion", questionBean.getUuidQuestion()) } };
		taskD = this.getManagerDAO().selectOne(TrTaskD.class, params);
		
		if (null != taskD) {
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setDtmUpd(new Date());
			taskD.setIntTextAnswer(ptvDate);

			this.getManagerDAO().update(taskD);
		} else {

			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());

			trTaskD.setTrTaskH(taskHVisit);
			trTaskD.setMsQuestion(questionBean);
			trTaskD.setQuestionText(questionBean.getQuestionLabel());
			trTaskD.setIntTextAnswer(ptvDate); 
			trTaskD.setIsReadonly("1");
			this.getManagerDAO().insert(trTaskD);
		}
	}
	
}
