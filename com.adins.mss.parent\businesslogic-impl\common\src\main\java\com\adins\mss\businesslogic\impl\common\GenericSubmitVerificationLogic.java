package com.adins.mss.businesslogic.impl.common;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.SubmitVerificationLogic;
import com.adins.mss.businesslogic.api.common.TaskVerificationLogic;
import com.adins.mss.constant.Global;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.SubmitTaskException;
import com.adins.mss.exceptions.SubmitTaskException.Reason;
import com.adins.mss.exceptions.UserLoginIdNotExistsException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.QuestionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.SubmitVerificationDBean;
import com.adins.mss.services.model.common.SubmitVerificationHBean;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.gson.Gson;

public class GenericSubmitVerificationLogic extends BaseLogic implements
		SubmitVerificationLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericSubmitVerificationLogic.class);

	private TaskVerificationLogic taskVerificationLogic;
	private CommonLogic commonLogic;
	
	private MessageSource messageSource;

	public void setTaskVerificationLogic(TaskVerificationLogic taskVerificationLogic) {
		this.taskVerificationLogic = taskVerificationLogic;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void reject(long uuidTaskH, String notes, AuditContext auditContext, String imei, String androidId) {		
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH tth join fetch tth.amMsuser mu join fetch mu.amMssubsystem where tth.uuidTaskH = :uuidTaskH", 
				new Object[][] {{"uuidTaskH", uuidTaskH}});

		long uuidProcess = getUuidProcess(trTaskH, trTaskH.getAmMsuser().getAmMssubsystem());
		MsStatustask msStatustask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, trTaskH
				.getAmMsuser().getAmMssubsystem(), 1);
		insertTaskHistory(auditContext, msStatustask, trTaskH, notes,
				GlobalVal.CODE_PROCESS_REJECTED_VERIFICATION, trTaskH.getAmMsuser().getFullName());

		AmMsuser usr = this.getManagerDAO().selectOne( 
				"from AmMsuser u join fetch u.msBranch join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(auditContext.getCallerId())}});
		
		//pengecekan mode submit
		this.validateDeviceId(usr, imei, androidId, auditContext);
		//end pengecekan mode submit

		if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
			commitOrder(usr, notes, trTaskH, trTaskH.getAmMsuser().getAmMssubsystem(), 1,
					GlobalVal.CODE_PROCESS_REJECTED_VERIFICATION, auditContext);
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String verify(AuditContext auditContext, SubmitVerificationHBean taskHBean,
			SubmitVerificationDBean[] taskDBean, String imei, String notes, String androidId) {
		String taskId = StringUtils.EMPTY;

		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH tth join fetch tth.msStatustask where tth.uuidTaskH = :uuidTaskH", 
				new Object[][] {{"uuidTaskH", Long.valueOf(taskHBean.getUuidTaskH())}});
		
		if (GlobalVal.SURVEY_STATUS_TASK_DELETED.equals(trTaskH.getMsStatustask().getStatusCode())) {
			throw new SubmitTaskException(this.messageSource.getMessage(
					"businesslogic.submit.deleted", new Object[]{trTaskH.getTaskId()}, this.retrieveLocaleAudit(auditContext)), Reason.DELETED);
		}
		AmMsuser usr = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(taskHBean.getUuidMsUser())}});
		
		//pengecekan mode submit
		this.validateDeviceId(usr, imei, androidId, auditContext);
		//end pengecekan mode submit

		AmMssubsystem subsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, usr.getAmMssubsystem().getUuidMsSubsystem());
		
		trTaskH.setUsrUpd(auditContext.getCallerId());
		trTaskH.setDtmUpd(new Date());
		trTaskH.setVerificationNotes(notes);
		trTaskH.setVerificationDate(new Date());
		trTaskH.setIsDraft("0");

		if (null == trTaskH.getFlagSource() && StringUtils.EMPTY.equals(trTaskH.getFlagSource())) {
			if (Global.APPLICATION_COLLECTION.equals(subsystem.getSubsystemName())) {
				trTaskH.setFlagSource(GlobalVal.SUBSYSTEM_MC);
			}
			else if (Global.APPLICATION_SURVEY.equals(subsystem.getSubsystemName())) {
				trTaskH.setFlagSource(GlobalVal.SUBSYSTEM_MS);
			}
		}

		trTaskH.setIsAppNotified("0");
		if (trTaskH.getTaskId() != null) {
			taskId = trTaskH.getTaskId();
		} else {
			trTaskH.setTaskId(String.valueOf(trTaskH.getUuidTaskH()));
		}

//		this.getManagerDAO().update(trTaskH); //sudah ada commitWfAndUpdateTaskH di bawah

		if (PropertiesHelper.isTaskDJson()) {
			this.saveVerificationJson(trTaskH, taskDBean, auditContext);
		}
		else {
			this.saveVerificationRow(taskHBean, taskDBean, auditContext);
		}
		
		long uuidProcess = getUuidProcess(trTaskH, subsystem);

		// get status from workflow
		MsStatustask msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);

		// INSERT INTO CHECK HISTORY
		insertTaskHistory(auditContext, msStatusTask, trTaskH,
				notes, GlobalVal.CODE_PROCESS_VERIFIED, usr.getFullName());
		if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
			commitOrder(usr, notes, trTaskH, subsystem, 0, GlobalVal.CODE_PROCESS_VERIFIED, auditContext);
		}
		
		return taskId;		
	}
	
	private void saveVerificationJson(TrTaskH trTaskH,
			SubmitVerificationDBean[] taskDBean, AuditContext auditContext) {
		Gson gson = new Gson();
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});				
		TaskDocumentBean tdb = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
		
		MsFormhistory msFormhistory = this.commonLogic.retrieveMsFormhistory(trTaskH.getMsForm().getUuidForm(), 
				trTaskH.getFormVersion(), auditContext);
		long uuidFormHistory = msFormhistory.getUuidFormHistory();
		
		List<AnswerBean> savedAnswers = tdb.getAnswers();
		for (int i = 0; i < taskDBean.length; i++) {
			long uuidQuestion = Long.parseLong(taskDBean[i].getUuidQuestion());

			MsQuestion msQuestion = this.commonLogic.retrieveQuestionByuuidQuestionQset(uuidQuestion, 
					trTaskH.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem(), 
					uuidFormHistory, auditContext);
			String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
			if (MssTool.isImageQuestion(answerType)) {
				continue;
			}
			
			String textAnswer = taskDBean[i].getTextAnswer();
			String uuidLov = taskDBean[i].getUuidOptionAnswer();
			String latitude = taskDBean[i].getLatitude();
			String longitude = taskDBean[i].getLongitude();
			
			int idxAnswer = tdb.findAnswerIndex(msQuestion.getUuidQuestion());
			
			AnswerBean answer = this.transformToJsonByAnswerType(trTaskH,
					uuidFormHistory, msQuestion, textAnswer, uuidLov, latitude,
					longitude, tdb, idxAnswer, auditContext);
			
			if (idxAnswer == -1) {
				savedAnswers.add(answer);
			}
		}
		
		docDb.setDocument(gson.toJson(tdb, TaskDocumentBean.class));
		docDb.setDtmUpd(new Date());
		docDb.setUsrUpd(auditContext.getCallerId());

		this.getManagerDAO().update(docDb);
	}

	private AnswerBean transformToJsonByAnswerType(TrTaskH taskH,
			long uuidFormHistory, MsQuestion quest, String textAnswer, String uuidLov,
			String latitude, String longitude, TaskDocumentBean document,
			int idxAnswer, AuditContext auditContext) {
		Assert.notNull(document);

		AnswerBean answer = (idxAnswer == -1) ? new AnswerBean() : document.getAnswers().get(idxAnswer);
		QuestionBean questionBean = new QuestionBean(quest.getUuidQuestion(), quest.getQuestionLabel(), 
				quest.getRefId(), quest.getMsAnswertype().getCodeAnswerType(),
				(quest.getMsOrdertag() == null ? null : quest.getMsOrdertag().getTagName()), 
				(quest.getMsAssettag() == null ? null : quest.getMsAssettag().getAssetTagName()),
				(quest.getMsCollectiontag() == null ? null : quest.getMsCollectiontag().getTagName()));
		answer.setQuestion(questionBean);

		String answerType = quest.getMsAnswertype().getCodeAnswerType();

		boolean flagOption = false;

		if (MssTool.isChoiceQuestion(answerType)) {
			flagOption = true;
			
			MsLov lov = this.getManagerDAO().selectOne(MsLov.class, NumberUtils.toLong(uuidLov));
			if (lov == null) {
				return answer;
			}
						
			// update
			List<OptionBean> options = (answer.getFinOptAnswers() == null) ? new ArrayList<OptionBean>()
					: answer.getFinOptAnswers();

			OptionBean option = new OptionBean();
			option.setUuid(lov.getUuidLov());
			option.setCode(lov.getCode());
			option.setDesc(lov.getDescription());
			option.setFreeText(StringUtils.stripToNull(textAnswer));
			
			if (!options.contains(option)) {
				options.add(option);
			}
			answer.setFinOptAnswers(options);			
		}
		else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType) && !flagOption) {
			BigDecimal decLatitude = (NumberUtils.isNumber(latitude)) ? new BigDecimal(latitude) : null;
			BigDecimal decLongitude = (NumberUtils.isNumber(longitude)) ? new BigDecimal(longitude) : null;

			if (decLatitude != null) {
				com.adins.mss.model.taskdjson.LocationBean locationBean = (answer.getLocation() == null) 
						? new com.adins.mss.model.taskdjson.LocationBean() : answer.getLocation();
				locationBean.setLat(decLatitude.doubleValue());
				locationBean.setLng(decLongitude.doubleValue());
				answer.setLocation(locationBean);
				answer.setFinTxtAnswer(answer.getTxtAnswer());
			}
		} 
		else if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType) && StringUtils.isNotBlank(textAnswer)) {
			answer.setFinTxtAnswer(textAnswer.replace(".00", "").replace(",", ""));
		} 
		else if (!flagOption) {
			// text,textmultiline,numeric,decimal,date,datetime,text with
			// suggestion,luonline
			answer.setFinTxtAnswer(StringUtils.stripToNull(textAnswer));
		}

		return answer;
	}
	
	private void saveVerificationRow(SubmitVerificationHBean taskHBean,
			SubmitVerificationDBean[] taskDBean, AuditContext auditContext) {
		Map<Integer, MsQuestion> msQuestions = this.getAllMsQuestion(taskDBean, auditContext);
		// LOOPING FOR TR_TASK_D
		DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
		for (int i = 0; i < taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
			String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
			if (MssTool.isImageQuestion(answerType)) {
				continue;
			}
			
			String optionAnswerId = taskDBean[i].getUuidOptionAnswer();
			String textAnswer = taskDBean[i].getTextAnswer();
			String latitude = taskDBean[i].getLatitude();
			String longitude = taskDBean[i].getLongitude();
			String uuidQuestion = taskDBean[i].getUuidQuestion();
			String uuidTaskH = taskHBean.getUuidTaskH();
			String uuidForm = taskHBean.getUuidForm();
			
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerType)
					|| GlobalVal.ANSWER_TYPE_DATETIME.equals(answerType)) {
				if (textAnswer != null) {
					try {
						Date result = df.parse(textAnswer);
						if (GlobalVal.ANSWER_TYPE_DATE.equals(answerType)) {
							textAnswer = (null != result) 
									? new SimpleDateFormat("dd/MM/yyyy").format(result)
									: null;
						}
						else {
							textAnswer = (null != result) 
									? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result)
									: null;
						}
					} catch (ParseException e) {
						LOG.error("Error on parsing date", e);
					} 
				}
			}
			
			taskVerificationLogic.updateDetail(textAnswer, optionAnswerId, latitude, longitude, 
					uuidForm, uuidTaskH, uuidQuestion, answerType, auditContext);			
		}
	}

	public String getNewTaskId(String seq) {
		BigInteger result = (BigInteger) this.getManagerDAO().selectOneNativeString("Select NEXT VALUE FOR " + seq, null);
		return result.toString();
	}

	private Map<Integer, MsQuestion> getAllMsQuestion(SubmitVerificationDBean[] taskDBean, AuditContext callerId) {
		Map<Integer, MsQuestion> result = new HashMap<Integer, MsQuestion>();
		for (int i = 0; i < taskDBean.length; i++) {
			MsQuestion msQuestion = this.commonLogic.retrieveQuestionByUuid(
					Long.valueOf(taskDBean[i].getUuidQuestion()), callerId);
			result.put(i, msQuestion);
		}
		return result;
	}

	public void checkImei(String imei, String uuidMsUser, AuditContext auditContext)
			throws UserLoginIdNotExistsException {
		String[][] params = { { "imei", imei }, { "uuidMsUser", uuidMsUser } };
		String check = (String) this.getManagerDAO().selectOneNativeString(
				"SELECT UUID_MS_User FROM AM_MSUSER WHERE IMEI = :imei and uuid_ms_user = :uuidMsUser", params);
		if (check == null)
			throw new UserLoginIdNotExistsException(this.messageSource.getMessage(
					"businesslogic.global.imeinotvalid", new Object[]{imei}, this.retrieveLocaleAudit(auditContext)));
	}

	@Transactional
	@Override
	public String rejectWithResurvey(long uuidTaskH, long uuidMsUser,
			String isSuggested, String notes, AuditContext auditContext, String imei, String androidId) {
		AmMsuser loginBean = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(auditContext.getCallerId())}});
		
		//pengecekan mode submit
		this.validateDeviceId(loginBean, imei, androidId, auditContext);
		//end pengecekan mode submit
		
		String mode = StringUtils.EMPTY;
		if ("1".equals(isSuggested)) {
			mode = "suggested";
		} else {
			mode = null;
		}
		taskVerificationLogic.rejectWithResurvey(
				uuidTaskH, 
				uuidMsUser, 
				notes, 
				mode, 
				loginBean, 
				auditContext
			);
		String result = "Rejected with Re-Survey.";
		return result;
	}
	
	private void validateDeviceId(AmMsuser usr, String imei, String androidId, AuditContext auditContext) {
		AmGeneralsetting beanGeneral = this.getManagerDAO().selectOne(AmGeneralsetting.class,
				new Object[][] { { Restrictions.eq("gsCode", usr.getAmMssubsystem().getSubsystemName() + GlobalKey.GENERALSETTING_MODE_LOGIN_SUBMIT) } });
		if (beanGeneral.getGsValue() == null || "".equals(beanGeneral.getGsValue())) {
			beanGeneral.setGsValue(GlobalVal.MODE_IMEI);
		}
		
		if (GlobalVal.MODE_IMEI.equalsIgnoreCase(beanGeneral.getGsValue())) { // jika MODE_SUBMIT == IMEI
			if (imei != null && !imei.equals(usr.getImei()) && !imei.equals(usr.getImei2())) {
				throw new UserLoginIdNotExistsException(this.messageSource.getMessage(
						"businesslogic.global.imeinotvalid", new Object[] { imei }, this.retrieveLocaleAudit(auditContext)));				
			}
		} else if (GlobalVal.MODE_ANDROID_ID.equalsIgnoreCase(beanGeneral.getGsValue())) { // jika MODE_SUBMIT = ANDROID_ID			
			if (androidId != null &&
					(usr.getAndroidId() == null || !usr.getAndroidId().equals(androidId))) {
				throw new UserLoginIdNotExistsException(this.messageSource.getMessage(
						"businesslogic.global.androididnotvalid", null, this.retrieveLocaleAudit(auditContext)));				
			}
		}
	}
}
