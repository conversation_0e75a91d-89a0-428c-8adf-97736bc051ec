		package com.adins.mss.businesslogic.impl.common;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Blob;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.SyncServiceLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.exceptions.SyncException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsDealer;
import com.adins.mss.model.MsDealerofbranch;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsMobiledatafiles;
import com.adins.mss.model.custom.UserManagementBean;
import com.adins.mss.services.model.common.CashUserBean;
import com.adins.mss.services.model.common.SyncHolidayBean;
import com.adins.mss.services.model.common.SyncLocationBean;
import com.adins.mss.services.model.common.SyncLovBean;
import com.adins.mss.services.model.common.SyncPushTableBean;
import com.adins.mss.services.model.common.SyncTableAssetSchemeBean;
import com.adins.mss.services.model.common.SyncTableBlacklistBean;
import com.adins.mss.services.model.common.SyncTableIndustryMarginRequest;
import com.adins.mss.services.model.common.SyncTableOtrBean;
import com.adins.mss.services.model.common.SyncTablePOAssetBean;
import com.adins.mss.services.model.common.SyncTablePODealerBean;
import com.adins.mss.services.model.common.SyncTablePOOfficeBean;
import com.adins.mss.services.model.common.SyncTableProductOfferingBean;
import com.adins.mss.services.model.common.SyncTableRuleFileBean;
import com.adins.mss.services.model.common.SyncZoneBean;
import com.google.common.io.BaseEncoding;

@SuppressWarnings({ "unchecked", "rawtypes" })
public class GenericSyncServiceLogic extends BaseLogic implements SyncServiceLogic{
	private static final Logger LOG = LoggerFactory.getLogger(GenericSyncServiceLogic.class);
	private AuditInfo auditInfo;
	
	private static final String URI_RULE_FILE = SpringPropertiesUtils.getProperty(GlobalKey.RULE_FILE_URI);
	
	public GenericSyncServiceLogic() {
		String[] pkCols = { "uuidLov" };
		String[] pkDbCols = { "UUID_LOV" };
		String[] cols = { "uuidLov", "isActive", "isDeleted", "lovGroup", "code",
				"description", "sequence", "constraint1", "constraint2",
				"constraint3", "constraint4", "constraint5" };
		String[] dbCols = { "UUID_LOV", "IS_ACTIVE", "IS_DELETED", "LOV_GROUP", "CODE",
				"DESCRIPTION", "SEQUENCE", "CONSTRAINT_1", "CONSTRAINT_2",
				"CONSTRAINT_3", "CONSTRAINT_4", "CONSTRAINT_5" };
		this.auditInfo = new AuditInfo("MS_LOV", pkCols, pkDbCols, cols, dbCols);
	}
	
	//-------------------------
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List syncHoliday(AuditContext auditContext, String dtm_upd) {
		List<SyncHolidayBean> listResult = new ArrayList<SyncHolidayBean>();
		String uuidUser = auditContext.getCallerId();
		Object[][] params;
		String condition;
		if (dtm_upd == null) {
			params = new Object[][] { {"uuidUser", uuidUser} };
			condition = "";
		} 
		else {
			SimpleDateFormat formatter = new SimpleDateFormat("ddMMyyyyHHmmss");
			Date dtmUpd = null;
			try {
				dtmUpd = formatter.parse(dtm_upd);
			} catch (ParseException e) {
				e.printStackTrace();
			}
			params = new Object[][] { {"uuidUser", uuidUser}, {"dtmUpd", dtmUpd} };
			condition = " AND mhd.dtm_upd >= :dtmUpd";
		}
			
		List result = this.getManagerDAO().selectAllNativeString("SELECT h_date, H_DESC, FLAG_HOLIDAY, FLAG_DAY, mhd.DTM_UPD, mb.branch_code, mhd.uuid_holiday_d"
				+ " FROM AM_MSUSER usr with (nolock)"
				+ " JOIN MS_BRANCH mb with (nolock) ON usr.uuid_branch = mb.uuid_branch"
				+ " JOIN MS_HOLIDAY_H mhh with (nolock) ON mb.uuid_holiday_h = mhh.uuid_holiday_h"
				+ " JOIN MS_HOLIDAY_D mhd with (nolock) ON mhh.UUID_HOLIDAY_H = mhd.UUID_HOLIDAY_H"
				+ " WHERE FLAG_HOLIDAY IN ('1','2')"
				+ " AND usr.uuid_ms_user = :uuidUser"
				+ condition, params);
		
		if (result != null) {
			Iterator itr = result.iterator();
			while(itr.hasNext()) {
				Map mp = (Map) itr.next();
				
				SyncHolidayBean bean = new SyncHolidayBean();
				if (mp.get("d0") != null) {
					bean.setH_date(toDate(mp.get("d0")));
				}
				bean.setH_desc((String)mp.get("d1"));
				bean.setFlag_holiday((String)mp.get("d2"));
				bean.setFlag_day((String)mp.get("d3"));
				if (mp.get("d4") != null) {
					bean.setDtm_upd(toDate(mp.get("d4")));
				}
				bean.setBranch_code((String)mp.get("d5"));
				bean.setUuid_holiday(mp.get("d6").toString());
				listResult.add(bean);
			}
		}
		return listResult;
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List syncLov(AuditContext auditContext, SyncLovBean[] syncLovBean, String init) {
		List<SyncLovBean> listSyncLov = new ArrayList<SyncLovBean>();

		//Sync LOV Phase 1 (Mobile Send Form ID)
		if (syncLovBean[0].getLov_group() == null && syncLovBean[0].getFormId() != null) {
			//get Form from user if SPV
			Object[][] paramSPV = { {"uuidSpvUser",auditContext.getCallerId()} };
			
			List listform = this.getManagerDAO().selectAllNative("services.common.task.getFormFromSPV", paramSPV, null);
			
			int totalForm = syncLovBean.length + listform.size();
			String[] formName= new String[totalForm];
			
			int i=0;
			for (i=0; i<syncLovBean.length; i++) {
				formName[i] = syncLovBean[i].getFormId();
			}
			
			if(listform != null){
				Iterator itr = listform.iterator();
					while (itr.hasNext()) {
					Map mp = (Map) itr.next();
					formName[i] = (String) mp.get("d0");
					i++;
				}
			}
			
			//IF INIT [MIN(lov.dtm_crt)]
			String dateCondition = " MAX(ISNULL(lov.DTM_UPD, lov.DTM_CRT)) ";
			
			if (formName[0] != null) {
				Object[][] param = { {"formName",formName} };
				List result = this.getManagerDAO().selectAllNativeString("SELECT DISTINCT lov.lov_group,"+dateCondition+"FROM MS_LOV lov with (nolock)"
										+ " JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON lov.LOV_GROUP = mfqs.LOV_GROUP"
										+ " WHERE lov.lov_group IS NOT NULL AND lov.lov_group != ''"
										+ " AND mfqs.FORM_NAME in (:formName)"
										+ " GROUP BY lov.lov_group", param);
				
				if (result != null) {
					Iterator itr = result.iterator();
					while (itr.hasNext()) {
						Map mp = (Map) itr.next();
						SyncLovBean bean = new SyncLovBean();
						bean.setLov_group( (String) mp.get("d0"));
						if (mp.get("d1") != null) {
							bean.setDtm_upd(toDate(mp.get("d1")));
						}
						listSyncLov.add(bean);
					}
				}
			}
		} 
		else { //Sync LOV Phase 2 (Mobile Send LOV GROUP)
			
			if("DEALER_NAME".equalsIgnoreCase(syncLovBean[0].getLov_group()) ||
					"REF_SUBZIPCODE".equalsIgnoreCase(syncLovBean[0].getLov_group())	) {
				return listSyncLov;
			}
			
			Stack<Object[]> paramStack = new Stack<>();
			StringBuilder condition = new StringBuilder("");
			StringBuilder conditionBranch= new StringBuilder("");

			Date dtmUpd = null;
			AmMsuser user =  this.getManagerDAO().selectOne( 
					"from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", 
					new Object[][] {{"uuidMsUser", Long.valueOf(auditContext.getCallerId())}});
			if (syncLovBean[0].getDtm_upd() == null) {
				paramStack.push(new Object []{"lovGroup",syncLovBean[0].getLov_group()});
				if("1".equals(user.getMsJob().getIsBranch())){
					conditionBranch.append(" and mbl.UUID_BRANCH = :uuidBranch");
					paramStack.push(new Object []{"uuidBranch",user.getMsBranch().getUuidBranch()});
				}
				else{
					String[] branchList = getBranchFromDealer(String.valueOf(user.getMsDealer().getUuidDealer()));
					if(branchList != null){
						conditionBranch.append(" and mbl.UUID_BRANCH in (:uuidBranch)");
						paramStack.push(new Object []{"uuidBranch",branchList});
					}
				}
			}
			else{
				SimpleDateFormat formatter = new SimpleDateFormat("ddMMyyyyHHmmss");
				
				try {
					dtmUpd = formatter.parse(syncLovBean[0].getDtm_upd());
				} 
				catch (ParseException e) {
					LOG.error(e.getMessage(),e);
					e.printStackTrace();
				}
				condition.append(" AND (SELECT ISNULL(t2.DTM_UPD, t2.DTM_CRT) from MS_LOV t2 with (nolock) WHERE t1.UUID_LOV = t2.UUID_LOV) > :dtmUpd");
				paramStack.push(new Object[]{"lovGroup", syncLovBean[0].getLov_group()});
				paramStack.push(new Object[]{"dtmUpd", dtmUpd});

				if("1".equals(user.getMsJob().getIsBranch())){
					conditionBranch.append(" and mbl.UUID_BRANCH = :uuidBranch");
					paramStack.push(new Object []{"uuidBranch",user.getMsBranch().getUuidBranch()});
				}
				else{
					String[] branchList = getBranchFromDealer(String.valueOf(user.getMsDealer().getUuidDealer()));
					if(branchList != null){
						conditionBranch.append(" and mbl.UUID_BRANCH in (:uuidBranch)");
						paramStack.push(new Object []{"uuidBranch",branchList});
					}
				}
			}
			
			if (GlobalVal.LOV_TAG_JOB_MH.equals(syncLovBean[0].getLov_group())) {
				Object[][] param = { { Restrictions.eq("msDealer.uuidDealer", user.getMsDealer().getUuidDealer()) } };
				Map<String, Object>  result = this.getManagerDAO().list(MsDealerofbranch.class, param, null);
				List<MsDealerofbranch> listResult = (List) result.get(GlobalKey.MAP_RESULT_LIST);
				if (listResult != null) {
					String[] stringResult = new String[listResult.size()];
					int i = 0;
					for (MsDealerofbranch dealerOfBranch : listResult) {
						stringResult[i] = String.valueOf(dealerOfBranch.getMsBranch().getUuidBranch());
						i++;
					}
					condition.append(" AND CONSTRAINT_1 in (:constraint1)");
					paramStack.push(new Object[]{"constraint1", stringResult});
				}
			} 
			else if (GlobalVal.LOV_TAG_DEALER_OF_BRANCH.equals(syncLovBean[0].getLov_group())) {
				if (String.valueOf(user.getMsDealer().getUuidDealer()) != null) {
					condition.append(" AND CONSTRAINT_1 = :constraint1");
					paramStack.push(new Object[]{"constraint1", user.getMsDealer().getUuidDealer()});

				}
			}
			
			List listResult = null;
			StringBuilder query = new StringBuilder();
			if("1".equals(user.getMsJob().getIsBranch())){
				query.append(" SELECT LOV_GROUP, CODE, [DESCRIPTION], SEQUENCE, CONSTRAINT_1, CONSTRAINT_2,")
					.append(" CONSTRAINT_3, CONSTRAINT_4, CONSTRAINT_5, IS_ACTIVE, t1.IS_DELETED, ISNULL(t1.DTM_UPD, t1.DTM_CRT), t1.UUID_LOV")
					.append(" FROM MS_LOV t1 with (nolock)")
					.append(" left join MS_BRANCHOFLOV mbl with (nolock)")
					.append(" on t1.UUID_LOV = mbl.UUID_LOV")
					.append(" WHERE LOV_GROUP = :lovGroup")
					.append(" and mbl.UUID_LOV is null")
					.append(condition) 
					.append(" union")
					.append(" SELECT LOV_GROUP, CODE, [DESCRIPTION], SEQUENCE, CONSTRAINT_1, CONSTRAINT_2,")
					.append(" CONSTRAINT_3, CONSTRAINT_4, CONSTRAINT_5, IS_ACTIVE, mbl.IS_DELETED, ISNULL(t1.DTM_UPD, t1.DTM_CRT), t1.UUID_LOV")
					.append(" FROM MS_LOV t1 with (nolock)")
					.append(" join MS_BRANCHOFLOV mbl with (nolock)")
					.append(" on t1.UUID_LOV = mbl.UUID_LOV")
					.append(" WHERE LOV_GROUP = :lovGroup")
					.append(conditionBranch)
					.append(condition);
			}
			else{
			 query.append(" SELECT LOV_GROUP, CODE, [DESCRIPTION], SEQUENCE, CONSTRAINT_1, CONSTRAINT_2,")
						.append(" CONSTRAINT_3, CONSTRAINT_4, CONSTRAINT_5, IS_ACTIVE, t1.IS_DELETED, ISNULL(t1.DTM_UPD, t1.DTM_CRT), t1.UUID_LOV")
						.append(" FROM MS_LOV t1 with (nolock)")
						.append(" left join MS_BRANCHOFLOV mbl with (nolock)")
						.append(" on t1.UUID_LOV = mbl.UUID_LOV")
						.append(" WHERE LOV_GROUP = :lovGroup")
						.append(" and mbl.UUID_LOV is null")
						.append(condition) 
						.append(" union")
						.append(" SELECT DISTINCT LOV_GROUP, CODE, [DESCRIPTION], SEQUENCE, CONSTRAINT_1, CONSTRAINT_2,")
						.append(" CONSTRAINT_3, CONSTRAINT_4, CONSTRAINT_5, IS_ACTIVE, mbl.IS_DELETED, ISNULL(t1.DTM_UPD, t1.DTM_CRT), t1.UUID_LOV")
						.append(" FROM MS_LOV t1 with (nolock)")
						.append(" join MS_BRANCHOFLOV mbl with (nolock)")
						.append(" on t1.UUID_LOV = mbl.UUID_LOV")
						.append(" WHERE LOV_GROUP = :lovGroup")
						.append(conditionBranch)
						.append(condition);
			}
			Object[][] params = new Object[paramStack.size()][2];
		    for (int i = 0; i < paramStack.size(); i++) {
				Object[] objects = paramStack.get(i);
				params[i] = objects;
			}
		    listResult = this.getManagerDAO().selectAllNativeString(query.toString(), params);
			if (listResult != null) {
				Iterator itr = listResult.iterator();
				while(itr.hasNext()) {
					Map mp = (Map) itr.next();
					SyncLovBean bean = new SyncLovBean();
					bean.setLov_group( (String) mp.get("d0"));
					bean.setCode( (String) mp.get("d1"));
					bean.setValue( (String) mp.get("d2"));
					bean.setSequence(Integer.toString((Integer) mp.get("d3")));
					if (mp.get("d4") != null) {
						bean.setFilter1( (String) mp.get("d4"));
					}
					if (mp.get("d5") != null) {
						bean.setFilter2( (String) mp.get("d5"));
					}
					if (mp.get("d6") != null) {
						bean.setFilter3( (String) mp.get("d6"));
					}
					if (mp.get("d7") != null) {
						bean.setFilter4( (String) mp.get("d7"));
					}
					if (mp.get("d8") != null) {
						bean.setFilter5( (String) mp.get("d8"));
					}
					bean.setIs_active( (String) mp.get("d9"));
					if (mp.get("d10") != null) {
						bean.setIs_deleted( (String) mp.get("d10"));
					}
					if (mp.get("d11") != null) {
						bean.setDtm_upd(toDate(mp.get("d11")));
					}
					bean.setUuid_lookup(mp.get("d12").toString());
					listSyncLov.add(bean);
				}
			}
		}
		return listSyncLov;
	}
	
	public String[] getBranchFromDealer(String uuidDealer) {

		String[][] params = { { "uuidDealer", uuidDealer } };

		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"services.common.task.getBranchFromDealer", params, null);
		if (!list.isEmpty()) {
			String[] stringResult = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = map.get("d0").toString();
			}
			return stringResult;
		} else {
			return null;
		}

	}
	
	
	//Sync User To User Bean
	@Transactional(readOnly=true)
	@Override
	public UserManagementBean toUserSynBean(String subSys, String action,
			String userId, String userName, String isActive, String officeId,
			String userParentId, String jobTitle, String isDealer,
			String userUpdateName, String cashLimit, AuditContext auditContext) {
		UserManagementBean resultBean = new UserManagementBean();
		MsDealer msDealer = null;
		MsBranch msBranch = null;
		MsJob msJob = null;
		AmMsuser amMsUser = null;
		Object[][] subSysParam = { {Restrictions.eq("subsystemName", subSys)} };
		AmMssubsystem amMssubsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, subSysParam);
		isActive = (StringUtils.isBlank(isActive)) ? "1" : isActive;
		
		//Get Max Task Load
		Object[][] paramSetting = { {Restrictions.eq("gsCode", "AM_DEFAULT_MAX_TASK_LOAD")} };
		AmGeneralsetting amSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramSetting);
		
		if("ins".equalsIgnoreCase(action) ){
			if(isLoginIdValid(userId)){
				amMsUser = new AmMsuser();
			}
			else{
				throw new EntityNotUniqueException("Login ID "+userId+" already exist", userId);
			}
		}
		else if("upd".equalsIgnoreCase(action) || "del".equalsIgnoreCase(action)){
			Object[][] params = { {Restrictions.eq("loginId", userId)}, {Restrictions.eq("isDeleted","0")} };
			amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, params);
			
			if (amMsUser != null){
				resultBean.setBean(amMsUser);
			}
			else{
				throw new EntityNotFoundException("Login ID "+userId+" not exist", userId);
			}
			
			msDealer = amMsUser.getMsDealer();
			msBranch = amMsUser.getMsBranch();
			
			if("del".equalsIgnoreCase(action)){
				return resultBean;
			}
		}
		amMsUser.setLoginId(userId);
		amMsUser.setFullName(userName);
		amMsUser.setIsActive(isActive);
		amMsUser.setAmMssubsystem(amMssubsystem);
		amMsUser.setInitialName("");
		amMsUser.setMaxTaskLoad(Integer.valueOf(amSetting.getGsValue()));
		//check Job
		msJob = this.getMsJob(jobTitle,String.valueOf(amMssubsystem.getUuidMsSubsystem()));
	    if (msJob == null){
	    	throw new EntityNotFoundException("No job found with code '" + jobTitle + "'", jobTitle);
	    }
	                
		
	    //check branch / dealer
		if(subSys.equalsIgnoreCase(GlobalVal.SUBSYSTEM_MO) && "0".equals(msJob.getIsBranch())){
			msDealer = this.getMsDealer(officeId);
        	if (msDealer == null){        		
        		throw new EntityNotFoundException("No dealer found with code '" + officeId + "'", officeId);
        	}
        	if(amMsUser.getMsBranch() == null){
	        	Object[][] param ={{Restrictions.eq("branchName", GlobalVal.BRANCH_HO)}};
	        	msBranch = this.getManagerDAO().selectOne(MsBranch.class, param);
        	}
        }else{
			msBranch = getMsBranch(officeId);
			if (msBranch == null){
				throw new EntityNotFoundException("No branch found with code '" + officeId + "'", officeId);
			}
                       
			if(amMsUser.getMsDealer() == null){
				Object[][] param ={{Restrictions.eq("dealerName", GlobalVal.DEALER_HO)}};
				msDealer = this.getManagerDAO().selectOne(MsDealer.class, param);
			}
		}
		
	    AmMsuser userParent =  this.parentUser(userParentId);
	    if(null == userParent){
	    	userParent = new AmMsuser();
	    }
	    amMsUser.setMsBranch(msBranch);
	    amMsUser.setMsDealer(msDealer);
	    amMsUser.setMsJob(msJob);
	    amMsUser.setAmMsuser(userParent);
	    amMsUser.setCashLimit(new BigDecimal(cashLimit));
        
        resultBean.setBean(amMsUser);
		return resultBean;
	}
	
	//Sync Dealer To Dealer Bean
	@Transactional(readOnly=true)
	@Override
	public MsDealer toDealerSynBean(AuditContext auditContext, String action,
			String dealerID, String dealerName, String dealerAddress,
			String dealerParent, String isActive, String userUpdateName) {
		MsDealer msDealer = null;
		MsDealer msDealerParent = null;
		Object[][] params = { {Restrictions.eq("dealerCode", dealerID)} };
		isActive = (StringUtils.isBlank(isActive)) ? "1" : isActive;
		if("ins".equalsIgnoreCase(action) ){
			if(validateDealer(params)){
				msDealer = new MsDealer();
			}
			else{
				throw new EntityNotUniqueException("Dealer Code "+dealerID+" already exist", dealerID);
			}
		}
		else if("upd".equalsIgnoreCase(action) || "del".equalsIgnoreCase(action)){
			msDealer = this.getManagerDAO().selectOne(MsDealer.class, params);
			
			if (msDealer == null){
				throw new EntityNotFoundException("Dealer Code "+dealerID+" not exist", dealerID);
			}
			
			if("del".equalsIgnoreCase(action))
				return msDealer;
		}
		
		if(!StringUtils.isBlank(dealerParent) && !"HO".equalsIgnoreCase(dealerParent)){
			Object[][] paramsParanet = { {Restrictions.eq("dealerCode", dealerParent)} };
			msDealerParent = this.getManagerDAO().selectOne(MsDealer.class, paramsParanet);
			if (msDealerParent == null)
				throw new EntityNotFoundException("Dealer Parent Code "+dealerParent+" not exist", dealerParent);
		}
		msDealer.setDealerCode(dealerID);
		msDealer.setDealerName(dealerName);
		msDealer.setIsActive(isActive);
		msDealer.setDealerAddress(dealerAddress);
		msDealer.setMsDealer(msDealerParent);
		msDealer.setOrderTarget(null);
		return msDealer;
	}

	//Sync Branch To Branch Bean
	@Transactional(readOnly=true)
	@Override
	public Map toBranchSynBean(String action, String branchId,
			String branchName, String branchAddress, String branchParentId,
			String isActive, String userUpdate, String longitude,
			String latitude, String userUpdateName, String cashLimitDefault, AuditContext auditContext) {
		Map result = new HashMap();
		MsBranch msBranch = null;
		MsBranch msParentBranch = null;
		BigDecimal lat = checkEmptyBigdecimal(latitude);
		BigDecimal longi = checkEmptyBigdecimal(longitude);
		String uuidParent = "-";
		isActive = (StringUtils.isBlank(isActive)) ? "1" : isActive;
		Object[][] params = { {Restrictions.eq("branchCode", branchId)} };
		if("ins".equalsIgnoreCase(action) ){
			if(!isExistBranch(branchId)){
				msBranch = new MsBranch();
			}
			else{
				throw new EntityNotUniqueException("Branch Code "+branchId+" already exist", branchId);
			}
		}
		else if("upd".equalsIgnoreCase(action) || "del".equalsIgnoreCase(action)){
			msBranch = this.getManagerDAO().selectOne(MsBranch.class, params);
			
			if (msBranch == null){
				throw new EntityNotFoundException("Branch Code "+branchId+" not exist", branchId);
			}
			
			if("del".equalsIgnoreCase(action)){
				result.put("msBranch", msBranch);
				return result;
			}
		}
		
		if(!StringUtils.isBlank(branchParentId)){
			Object[][] paramsParanet = { {Restrictions.eq("branchCode", branchParentId)} };
			msParentBranch = this.getManagerDAO().selectOne(MsBranch.class, paramsParanet);
			if (msParentBranch == null){
				throw new EntityNotFoundException("Branch Parent Code "+branchParentId+" not exist", branchParentId);
			}
		}
		msBranch.setBranchCode(branchId);
		msBranch.setBranchName(branchName);
		msBranch.setBranchAddress(branchAddress);
		msBranch.setMsBranch(msParentBranch);
		msBranch.setIsActive(isActive);
		msBranch.setLatitude(lat);
		msBranch.setLongitude(longi);
		msBranch.setCashLimitDefault(new BigDecimal(cashLimitDefault));
		
		if(msParentBranch != null)
			uuidParent = String.valueOf(msParentBranch.getUuidBranch());
		
		result.put("msBranch", msBranch);
		result.put("uuidParent", uuidParent);
		return result;
	}
	
	//Sync Param Master
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public Map syncParamMaster(AuditContext auditContext, String action,
			String lovGroupName, String code, String description,
			String constraint1, String constraint2, String constraint3,
			String constraint4, String constraint5, String isActive,
			String isDeleted, String sequence, String userUpdateName) {
		LOG.info("Sync Param ||action : {}|| lovGroupName : {}, code : {}||"
				+ " description : {}|| constraint1 : {}|| constraint2 : {}||"
				+ " constraint3 : {}|| constraint4 : {}|| constraint5 : {}|| isActive : {}||"
				+ " User Update : {}"
				, action, lovGroupName, code, description, constraint1
				, constraint2, constraint3, constraint4, constraint5, isActive
				, isDeleted, sequence, userUpdateName);
		Map mapResult = null;
		
		isActive = (isActive == null) ? "1" : isActive;
		isDeleted = (isDeleted == null) ? "0" : isDeleted;
		sequence = (StringUtils.isBlank(sequence)) ? getSequence(lovGroupName, code): sequence;
		lovGroupName = lovGroupName.toUpperCase();
		constraint1 = (StringUtils.isBlank(constraint1)) ? null : StringUtils.trimToNull(constraint1);
		constraint2 = (StringUtils.isBlank(constraint2)) ? null : StringUtils.trimToNull(constraint2);
		constraint3 = (StringUtils.isBlank(constraint3)) ? null : StringUtils.trimToNull(constraint3);
		constraint4 = (StringUtils.isBlank(constraint4)) ? null : StringUtils.trimToNull(constraint4);
		constraint5 = (StringUtils.isBlank(constraint5)) ? null : StringUtils.trimToNull(constraint5);
		
		if("Ins".equalsIgnoreCase(action)){
			mapResult = insertParamMaster(lovGroupName, code, description, constraint1, constraint2
					, constraint3, constraint4, constraint5, isActive, isDeleted, sequence, userUpdateName);
		}
		else if("Upd".equalsIgnoreCase(action)){
			mapResult = updateParamMaster(lovGroupName, code, description, constraint1, constraint2
					, constraint3, constraint4, constraint5, isActive, isDeleted, sequence, userUpdateName);
		}
		else if("Del".equalsIgnoreCase(action)){
			mapResult = deleteParamMaster(lovGroupName, code, constraint1, constraint2
					, constraint3, constraint4, constraint5, userUpdateName);
		}
		else{
			Status status =new Status();
			status.setCode(0);
			status.setMessage("Action diisi dengan Ins / Upd / Del");
			mapResult = new HashMap();
			mapResult.put("status", status);
		}
		return mapResult;
	}
	
	public Map insertParamMaster(String lovGroupName, String code, String description,
			String constraint1, String constraint2, String constraint3,
			String constraint4, String constraint5, String isActive,
			String isDeleted, String sequence, String userUpdateName){
		Map mapResult = new HashMap();
		Status status = new Status();
		Boolean isUpdate = false;
		status.setCode(0);
		
		MsLov msLov = new MsLov();
		
		msLov.setDtmCrt(new Date());
		msLov.setUsrCrt(userUpdateName);
		msLov.setLovGroup(lovGroupName);
		msLov.setCode(code);
		msLov.setDescription(description);
		msLov.setIsActive(isActive);
		msLov.setIsDeleted(isDeleted);
		msLov.setConstraint1(constraint1);
		msLov.setConstraint2(constraint2);
		msLov.setConstraint3(constraint3);
		msLov.setConstraint4(constraint4);
		msLov.setConstraint5(constraint5);
		msLov.setSequence(checkEmptyInteger(sequence));
		
		MsLov checkLov = isExist(msLov);
		if(null != checkLov){
			if("1".equals(checkLov.getIsActive()) && "0".equals(checkLov.getIsDeleted())){
				status.setMessage("parameter telah terdaftar");
				mapResult.put("status", status);
				return mapResult;
			}
			else{
				checkLov.setDtmUpd(new Date());
				checkLov.setUsrUpd(userUpdateName);
				checkLov.setIsActive(isActive);
				checkLov.setIsDeleted(isDeleted);
				checkLov.setDescription(description);
				checkLov.setSequence(checkEmptyInteger(sequence));
				isUpdate = true;
			}
		}
		
		if(isUpdate){
			this.auditManager.auditEdit(checkLov, auditInfo, userUpdateName, "");
			this.getManagerDAO().update(checkLov);
		}
		else{
			this.getManagerDAO().insert(msLov);
			this.auditManager.auditAdd(msLov, auditInfo, userUpdateName, "");
		}
		
		status.setCode(1);
		status.setMessage("OK");
		mapResult.put("status", status);
		
		LOG.info("Insert Parameter, uuidLOV : {}, lovGroupName : {}, code : {}, description : {}"
				+ ",isActive : {} ,isDeleted : {} ,DtmCrt : {} ,UsrCrt : {}, constraint1: {}"
				+ ",constraint2 : {} ,constraint3 : {} ,constraint4 : {} ,constraint5 : {}"
				, msLov.getUuidLov(), msLov.getLovGroup(), msLov.getCode(), msLov.getDescription()
				, msLov.getIsActive(), msLov.getIsDeleted(), msLov.getDtmCrt(), msLov.getUsrCrt(), msLov.getConstraint1()
				, msLov.getConstraint2(), msLov.getConstraint3(), msLov.getConstraint4(), msLov.getConstraint5());

		return mapResult;
	}
	
	public Map updateParamMaster(String lovGroupName, String code, String description,
			String constraint1, String constraint2, String constraint3,
			String constraint4, String constraint5, String isActive,
			String isDeleted, String sequence, String userUpdateName){
		Map mapResult = new HashMap();
		Status status = new Status();
		status.setCode(0);

		MsLov msLov = new MsLov();
		msLov.setLovGroup(lovGroupName);
		msLov.setCode(code);
		msLov.setConstraint1(constraint1);
		msLov.setConstraint2(constraint2);
		msLov.setConstraint3(constraint3);
		msLov.setConstraint4(constraint4);
		msLov.setConstraint5(constraint5);
		
		MsLov checkedExistLov = isExist(msLov);
		if(null == checkedExistLov){
			status.setMessage("parameter tidak terdaftar");
			mapResult.put("status", status);
			return mapResult;
		}
		
		checkedExistLov.setDescription(description);
		checkedExistLov.setUsrUpd(userUpdateName);
		checkedExistLov.setDtmUpd(new Date());
		checkedExistLov.setSequence(checkEmptyInteger(sequence));
		checkedExistLov.setIsActive(isActive);
		checkedExistLov.setIsDeleted(isDeleted);
		
		this.auditManager.auditEdit(checkedExistLov, auditInfo, userUpdateName, "");
		this.getManagerDAO().update(checkedExistLov);
		status.setCode(1);
		status.setMessage("OK");
		mapResult.put("status", status);
		
		return mapResult;
	}
	
	public Map deleteParamMaster(String lovGroupName, String code, String constraint1, String constraint2,
			String constraint3,	String constraint4, String constraint5, String userUpdateName){
		Map mapResult = new HashMap();
		Status status = new Status();
		status.setCode(0);
			
		MsLov msLov = new MsLov();
		msLov.setLovGroup(lovGroupName);
		msLov.setCode(code);
		msLov.setConstraint1(constraint1);
		msLov.setConstraint2(constraint2);
		msLov.setConstraint3(constraint3);
		msLov.setConstraint4(constraint4);
		msLov.setConstraint5(constraint5);
		
		MsLov checkedExistLov = isExist(msLov);
		if(null == checkedExistLov){
			status.setMessage("parameter tidak terdaftar");
			mapResult.put("status", status);
			return mapResult;
		}
		checkedExistLov.setUsrUpd(userUpdateName);
		checkedExistLov.setDtmUpd(new Date());
		checkedExistLov.setIsDeleted("1");
		checkedExistLov.setIsActive("0");
		this.auditManager.auditEdit(checkedExistLov, auditInfo, userUpdateName, "");
		this.getManagerDAO().update(checkedExistLov);
		status.setCode(1);
		status.setMessage("OK");
		mapResult.put("status", status);
		return mapResult;
	}
	
	private MsLov isExist(MsLov lov){
		Object[][] params = new Object[7][1];
		params[0][0] = Restrictions.eq("lovGroup", lov.getLovGroup());
		params[1][0] = Restrictions.eq("code", lov.getCode());
		
		if( StringUtils.isBlank(lov.getConstraint1())){
			params[2][0] = Restrictions.isNull("constraint1");
		}
		else {
			params[2][0] = Restrictions.eq("constraint1", lov.getConstraint1());
		}
		
		if( StringUtils.isBlank(lov.getConstraint2())){
			params[3][0] = Restrictions.isNull("constraint2");
		}
		else {
			params[3][0] = Restrictions.eq("constraint2", lov.getConstraint2());
		}
		
		if( StringUtils.isBlank(lov.getConstraint3())){
			params[4][0] = Restrictions.isNull("constraint3");
		}
		else {
			params[4][0] = Restrictions.eq("constraint3", lov.getConstraint3());
		}
		
		if( StringUtils.isBlank(lov.getConstraint4())){
			params[5][0] = Restrictions.isNull("constraint4");
		}
		else {
			params[5][0] = Restrictions.eq("constraint4", lov.getConstraint4());
		}
		
		if( StringUtils.isBlank(lov.getConstraint5())){
			params[6][0] = Restrictions.isNull("constraint5");
		}
		else {
			params[6][0] = Restrictions.eq("constraint5", lov.getConstraint5());
		}
		
		MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, params);
		return msLov;
	}
	
	private String getSequence(String lovGroup, String code) {
		Object[][] params2 = { {"lovGroup", lovGroup} };
		Integer sequence = (Integer) this.getManagerDAO().selectOneNativeString("select max(sequence) from ms_lov where LOV_GROUP = :lovGroup", params2);
		sequence = (null == sequence) ? 10 : (sequence+=1);
		return String.valueOf(sequence);
	}
	
	public String toDate(Object obj) {
		String s = new SimpleDateFormat("ddMMyyyyHHmmss").format(obj);
		return s;
	}
	
	private BigDecimal checkEmptyBigdecimal(String in){
		BigDecimal bd;
		if (StringUtils.isBlank(in)){
			return null;
		}
		else{
			bd = new BigDecimal(in);
		}
		return bd;
	}
	
	private Integer checkEmptyInteger(String in){
		Integer i = 0;
		if (StringUtils.isBlank(in)){
			return null;
		}
		else {
			i = Integer.valueOf(in);
		}
		return i;
	}

	private boolean validateDealer(Object params) {
		Map<String, Object> result = this.getManagerDAO().count(MsDealer.class, params);
		if ((Integer) result.get(GlobalKey.MAP_RESULT_SIZE) > 0){
			return false;
		}
		else {
			return true;
		}
	}
	
	public boolean isExistBranch(String branchCode) {
		Object[][] params = { {Restrictions.eq("branchCode", branchCode)} };
		Map<String, Object> mapBranch = this.getManagerDAO().list(MsBranch.class, params, null);
		List<MsBranch> listBranch = (List<MsBranch>) mapBranch.get(GlobalKey.MAP_RESULT_LIST);
		
		for (int i = 0; i < listBranch.size(); i++) {
			if (listBranch.get(i).getBranchCode().equals(branchCode)) {
				return true;
			}
		}
		return false;
	}
	
	public boolean isLoginIdValid(String loginId) {
		Map<String, Object> result = new HashMap<>();
		boolean x = true;
		
		Object[][] param = new Object[2][1];
	
		param[0][0] = Restrictions.eq("loginId",loginId.toUpperCase());
		param[1][0] = Restrictions.eq("isDeleted","0");
		result =  this.getManagerDAO().count(AmMsuser.class, param);
		
		Integer listSize = (Integer) result.get(GlobalKey.MAP_RESULT_SIZE);

		if (listSize > 0) {
			x = false;
		}
		
		return x;
	}
	
	private AmMsuser parentUser(String userParentId) {
		AmMsuser result ;
		if (StringUtils.isBlank(userParentId)){
			return null;
		}
		else{
			Object[][] queryParams = { {Restrictions.eq("loginId", userParentId)},
										{Restrictions.eq("isDeleted","0")}};
			result = this.getManagerDAO().selectOne(AmMsuser.class, queryParams);
			if (result == null) {
	            throw new EntityNotFoundException("No parent found with code '" + userParentId +
	                    "'", userParentId);
	        }	
		}
		return result;
	}
	private MsBranch getMsBranch(String officeId) {
		if (StringUtils.isBlank(officeId)){
			return null;
		}
		Object[][] queryParams = { {Restrictions.eq("branchCode", officeId)} };
		MsBranch result = this.getManagerDAO().selectOne(MsBranch.class, queryParams);
		return result;
	}
	private MsDealer getMsDealer(String officeId) {
		if (StringUtils.isBlank(officeId)){
			return null;
		}
		Object[][] queryParams = { {Restrictions.eq("dealerCode", officeId)} };
		MsDealer result = this.getManagerDAO().selectOne(MsDealer.class, queryParams);
		return result;
	}
	private MsJob getMsJob(String jobTitle,String subSys) {
		if (StringUtils.isBlank(jobTitle)){
			return null;		
		}
		MsJob result = this.getManagerDAO().selectOne(
				"from MsJob mj join fetch mj.amMssubsystem ms where mj.jobCode = :jobCode and ms.uuidMsSubsystem = :uuidMsSubsystem", 
				new Object[][] {{"uuidMsUser", jobTitle}, {"uuidMsSubsystem", Long.valueOf(subSys)}});
		return result;
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List getLov(AuditContext auditContext, SyncLovBean[] syncLovBean, String init) {
		List<SyncLovBean> listSyncLov = new ArrayList<SyncLovBean>();

		String constraint1 = syncLovBean[0].getFilter1();
		String constraint2 = syncLovBean[0].getFilter2();
		String constraint3 = syncLovBean[0].getFilter3();
		String constraint4 = syncLovBean[0].getFilter4();
		String constraint5 = syncLovBean[0].getFilter5();
		
		if (StringUtils.isBlank(constraint1)) constraint1 = null;
		if (StringUtils.isBlank(constraint2)) constraint2 = null;
		if (StringUtils.isBlank(constraint3)) constraint3 = null;
		if (StringUtils.isBlank(constraint4)) constraint4 = null;
		if (StringUtils.isBlank(constraint5)) constraint5 = null;
		
		Stack<Object[]> paramStack = new Stack<>();
		StringBuilder condition = new StringBuilder("");
		StringBuilder conditionBranch= new StringBuilder("");
		Date dtmUpd = null;
		AmMsuser user =  this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", Long.valueOf(auditContext.getCallerId())}});
		if (syncLovBean[0].getDtm_upd() == null) {
			paramStack.push(new Object[]{ "lovGroup", syncLovBean[0].getLov_group()});
			paramStack.push(new Object[]{ "uuidBranch", user.getMsBranch().getUuidBranch()});
			if("1".equals(user.getMsJob().getIsBranch())){
				conditionBranch.append(" and mbl.UUID_BRANCH = :uuidBranch");
				paramStack.push(new Object[]{ "uuidBranch", user.getMsBranch().getUuidBranch()});
			}
			else{
				String[] branchList = getBranchFromDealer(String.valueOf(user.getMsDealer().getUuidDealer()));
				if(branchList != null){
					conditionBranch.append(" and mbl.UUID_BRANCH in (:uuidBranch)");
					paramStack.push(new Object[]{ "uuidBranch", branchList});
				}
			}
		}
		else {
			SimpleDateFormat formatter = new SimpleDateFormat("ddMMyyyyHHmmss");
			
			try {
				dtmUpd = formatter.parse(syncLovBean[0].getDtm_upd());
			} 
			catch (ParseException e) {
				LOG.error(e.getMessage(),e);
				e.printStackTrace();
			}
			// NEW logic, if LOV GROUP is SOA then dtmUpd equals 1970-01-01
			// so the query will return all SOA data
			if("SOA".equals(syncLovBean[0].getLov_group())) {
				dtmUpd = new Date(0L);
			}
			
			condition.append(" AND (SELECT ISNULL(t2.DTM_UPD, t2.DTM_CRT) from MS_LOV t2 with (nolock) WHERE t1.UUID_LOV = t2.UUID_LOV) > :dtmUpd");
			paramStack.push(new Object[]{ "lovGroup", syncLovBean[0].getLov_group()});
			paramStack.push(new Object[]{ "dtmUpd", dtmUpd});	
			if("1".equals(user.getMsJob().getIsBranch())){
				conditionBranch.append(" and mbl.UUID_BRANCH = :uuidBranch");
				paramStack.push(new Object[]{ "uuidBranch", user.getMsBranch().getUuidBranch()});
			}
			else{
				String[] branchList = getBranchFromDealer(String.valueOf(user.getMsDealer().getUuidDealer()));
				if(branchList != null){
					conditionBranch.append(" and mbl.UUID_BRANCH in (:uuidBranch)");
					paramStack.push(new Object[]{ "uuidBranch", branchList});
				}
			}
		}
		
		if(syncLovBean[0].getConstraintAmount()==1){
			condition.append(" AND CONSTRAINT_1 "+checkParamNull(constraint1, "constraint1"));
			paramStack.push(new Object[]{ "constraint1", constraint1});
		}
		else if(syncLovBean[0].getConstraintAmount()==2){
			condition.append(" AND CONSTRAINT_1 "+checkParamNull(constraint1, "constraint1"));
			condition.append(" AND CONSTRAINT_2 "+checkParamNull(constraint1, "constraint2"));
			paramStack.push(new Object[]{ "constraint1", constraint1});
			paramStack.push(new Object[]{ "constraint2", constraint2});
		}
		else if(syncLovBean[0].getConstraintAmount()==3){
			condition.append(" AND CONSTRAINT_1 "+checkParamNull(constraint1, "constraint1"));
			condition.append(" AND CONSTRAINT_2 "+checkParamNull(constraint1, "constraint2"));
			condition.append(" AND CONSTRAINT_3 "+checkParamNull(constraint1, "constraint3"));
			paramStack.push(new Object[]{ "constraint1", constraint1});
			paramStack.push(new Object[]{ "constraint2", constraint2});
			paramStack.push(new Object[]{ "constraint3", constraint3});
		}
		else if(syncLovBean[0].getConstraintAmount()==4){
			condition.append(" AND CONSTRAINT_1 "+checkParamNull(constraint1, "constraint1"));
			condition.append(" AND CONSTRAINT_2 "+checkParamNull(constraint1, "constraint2"));
			condition.append(" AND CONSTRAINT_3 "+checkParamNull(constraint1, "constraint3"));
			condition.append(" AND CONSTRAINT_4 "+checkParamNull(constraint1, "constraint4"));
			paramStack.push(new Object[]{ "constraint1", constraint1});
			paramStack.push(new Object[]{ "constraint2", constraint2});
			paramStack.push(new Object[]{ "constraint3", constraint3});
			paramStack.push(new Object[]{ "constraint4", constraint4});
		}
		else if(syncLovBean[0].getConstraintAmount()==5){
			condition.append(" AND CONSTRAINT_1 "+checkParamNull(constraint1, "constraint1"));
			condition.append(" AND CONSTRAINT_2 "+checkParamNull(constraint1, "constraint2"));
			condition.append(" AND CONSTRAINT_3 "+checkParamNull(constraint1, "constraint3"));
			condition.append(" AND CONSTRAINT_4 "+checkParamNull(constraint1, "constraint4"));
			condition.append(" AND CONSTRAINT_5 "+checkParamNull(constraint1, "constraint5"));
			paramStack.push(new Object[]{ "constraint1", constraint1});
			paramStack.push(new Object[]{ "constraint2", constraint2});
			paramStack.push(new Object[]{ "constraint3", constraint3});
			paramStack.push(new Object[]{ "constraint4", constraint4});
			paramStack.push(new Object[]{ "constraint5", constraint5});
		}
		StringBuilder query = null;
		
		if("1".equals(user.getMsJob().getIsBranch())){
		 query = new StringBuilder("SELECT LOV_GROUP, CODE, [DESCRIPTION], SEQUENCE, CONSTRAINT_1, CONSTRAINT_2,")
			.append(" CONSTRAINT_3, CONSTRAINT_4, CONSTRAINT_5, IS_ACTIVE, t1.IS_DELETED, ISNULL(t1.DTM_UPD, t1.DTM_CRT), t1.UUID_LOV")
			.append(" FROM MS_LOV t1 with (nolock)")
			.append(" left join MS_BRANCHOFLOV mbl with (nolock)")
			.append(" on t1.UUID_LOV = mbl.UUID_LOV")
			.append(" WHERE LOV_GROUP = :lovGroup AND IS_ACTIVE = 1")
			.append(" and mbl.UUID_LOV is null")
			.append(condition)
			.append(" UNION")
			.append(" SELECT LOV_GROUP, CODE, [DESCRIPTION], SEQUENCE, CONSTRAINT_1, CONSTRAINT_2,")
			.append(" CONSTRAINT_3, CONSTRAINT_4, CONSTRAINT_5, IS_ACTIVE, t1.IS_DELETED, ISNULL(t1.DTM_UPD, t1.DTM_CRT), t1.UUID_LOV")
			.append(" FROM MS_LOV t1 with (nolock)")
			.append(" join MS_BRANCHOFLOV mbl with (nolock)")
			.append(" on t1.UUID_LOV = mbl.UUID_LOV")
			.append(" WHERE LOV_GROUP = :lovGroup")
			.append(conditionBranch)
			.append(condition);			
		}
		else{
			 query = new StringBuilder("SELECT LOV_GROUP, CODE, [DESCRIPTION], SEQUENCE, CONSTRAINT_1, CONSTRAINT_2,")
			.append(" CONSTRAINT_3, CONSTRAINT_4, CONSTRAINT_5, IS_ACTIVE, t1.IS_DELETED, ISNULL(t1.DTM_UPD, t1.DTM_CRT), t1.UUID_LOV")
			.append(" FROM MS_LOV t1 with (nolock)")
			.append(" left join MS_BRANCHOFLOV mbl with (nolock)")
			.append(" on t1.UUID_LOV = mbl.UUID_LOV")
			.append(" WHERE LOV_GROUP = :lovGroup AND IS_ACTIVE = 1")
			.append(" and mbl.UUID_LOV is null")
			.append(condition)
			.append(" UNION")
			.append(" SELECT LOV_GROUP, CODE, [DESCRIPTION], SEQUENCE, CONSTRAINT_1, CONSTRAINT_2,")
			.append(" CONSTRAINT_3, CONSTRAINT_4, CONSTRAINT_5, IS_ACTIVE, t1.IS_DELETED, ISNULL(t1.DTM_UPD, t1.DTM_CRT), t1.UUID_LOV")
			.append(" FROM MS_LOV t1 with (nolock)")
			.append(" join MS_BRANCHOFLOV mbl with (nolock)")
			.append(" on t1.UUID_LOV = mbl.UUID_LOV")
			.append(" WHERE LOV_GROUP = :lovGroup")
			.append(conditionBranch)
			.append(condition);	
		}
		
	    Object[][] sqlParams = new Object[paramStack.size()][2];
	    for (int i = 0; i < paramStack.size(); i++) {
			Object[] objects = paramStack.get(i);
			sqlParams[i] = objects;
		}
		
		List listResult = this.getManagerDAO().selectAllNativeString(new String(query), sqlParams);
		
		if (listResult != null) {
			Iterator itr = listResult.iterator();
			while(itr.hasNext()) {
				Map mp = (Map) itr.next();
				SyncLovBean bean = new SyncLovBean();
				bean.setLov_group( (String) mp.get("d0"));
				bean.setCode( (String) mp.get("d1"));
				bean.setValue( (String) mp.get("d2"));
				bean.setSequence(Integer.toString((Integer) mp.get("d3")));
				if (mp.get("d4") != null) {
					bean.setFilter1( (String) mp.get("d4"));
				}
				if (mp.get("d5") != null) {
					bean.setFilter2( (String) mp.get("d5"));
				}
				if (mp.get("d6") != null) {
					bean.setFilter3( (String) mp.get("d6"));
				}
				if (mp.get("d7") != null) {
					bean.setFilter4( (String) mp.get("d7"));
				}
				if (mp.get("d8") != null) {
					bean.setFilter5( (String) mp.get("d8"));
				}
				bean.setIs_active( (String) mp.get("d9"));
				if (mp.get("d10") != null) {
					bean.setIs_deleted( (String) mp.get("d10"));
				}
				if (mp.get("d11") != null) {
					bean.setDtm_upd(toDate(mp.get("d11")));
				}
				bean.setUuid_lookup(mp.get("d12").toString());
				listSyncLov.add(bean);
			}
		}
		return listSyncLov;
	}
	
	public String checkParamNull(String in, String param){
		return in == null ? "is null" : "= :"+param;
	}
	
	@Transactional(readOnly=true)		
	@Override
	public List syncCash(AuditContext auditContext,String loginId, String init) {		
		List<CashUserBean> listResult = new ArrayList<CashUserBean>();	
		
		AmMsuser user = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch where u.loginId = :loginId", new Object[][] {{"loginId", loginId}});	
		
		CashUserBean bean = new CashUserBean();	
			
		if(user.getCashLimit() == null || user.getCashLimit().compareTo(new BigDecimal(0)) == 0){
			bean.setCash_limit(user.getMsBranch().getCashLimitDefault());
		}	
		else {
			bean.setCash_limit(user.getCashLimit());
		}
		bean.setCash_on_hand(user.getCashOnHand());	
		bean.setIs_tracking(user.getIsTracking());
		bean.setStart_time(user.getTrackingStartTime());
		bean.setEnd_time(user.getTrackingEndTime());
		
		listResult.add(bean);	
			
	return listResult;		
			
	}

	@Override
	@Transactional(readOnly=true)
	public List syncLocation(AuditContext auditContext, String dtm_upd) {
		List<SyncLocationBean> locationBeanList= new ArrayList<>();
		Object params[][];
		Date datetime = null;
		SimpleDateFormat format = new SimpleDateFormat("ddMMyyyyHHmmss");
		if(StringUtils.isNotBlank(dtm_upd)){
			try {
				datetime = format.parse(dtm_upd);
			} 
			catch (ParseException e) {
				LOG.error(e.getMessage(),e);
				e.printStackTrace();
			}
		}
		
		String query = StringUtils.EMPTY;
		if(datetime==null){
			query = "select UUID_LOCATION, IS_ACTIVE, format(ISNULL(DTM_UPD, DTM_CRT),'ddMMyyyyHHmmss'), "+ 
					"LOCATION_CODE, LOCATION_NAME, LOCATION_ADDRESS, LATITUDE, LONGITUDE "+
					"from MS_LOCATION with (nolock) "+
					"where IS_ACTIVE = :isActive";
			Object tempParams[][] = {{"isActive", "1"}};
			params = tempParams;
		} 
		else {
			query = "select UUID_LOCATION, IS_ACTIVE, format(ISNULL(DTM_UPD, DTM_CRT),'ddMMyyyyHHmmss'), "+ 
					"LOCATION_CODE, LOCATION_NAME, LOCATION_ADDRESS, LATITUDE, LONGITUDE "+
					"from MS_LOCATION with (nolock) "+
					"where format(ISNULL(DTM_UPD, DTM_CRT),'yyyyMMddHHmmss') > format(:dtmUpd,'yyyyMMddHHmmss') and IS_ACTIVE = :isActive";
			Object tempParams[][] = {{"isActive", "1"}, {"dtmUpd", datetime }};
			params = tempParams;
		}
		List result = this.getManagerDAO().selectAllNativeString( query, params);
		if(result!=null){
			for(int i=0; i<result.size();i++){
				Map temp = (Map) result.get(i);
				SyncLocationBean bean = new SyncLocationBean();
				bean.setUuid_location(temp.get("d0").toString());
				bean.setIs_active((String)temp.get("d1"));
				bean.setDtm_upd((String)temp.get("d2"));
				bean.setLocation_code((String)temp.get("d3"));
				bean.setLocation_name((String)temp.get("d4"));
				bean.setLocation_address((String)temp.get("d5"));
				bean.setLatitude(temp.get("d6")!=null?temp.get("d6").toString():null);
				bean.setLongitude(temp.get("d7")!=null?temp.get("d7").toString():null);
				locationBeanList.add(bean);
			}
		}
		return locationBeanList;
	}

	@Override
	@Transactional(readOnly=true)
	public List syncZone(AuditContext auditContext, String dtm_upd) {
		List<SyncZoneBean> zoneList= new ArrayList<>();
		Object params[][];
		Date datetime = null;
		SimpleDateFormat format = new SimpleDateFormat("ddMMyyyyHHmmss");
		if(StringUtils.isNotBlank(dtm_upd)){
			try {
				datetime = format.parse(dtm_upd);
			} 
			catch (ParseException e) {
				LOG.error(e.getMessage(),e);
				e.printStackTrace();
			}
		}
		
		String query = StringUtils.EMPTY;
		if(datetime==null){
			query = "select UUID_ZONE, IS_ACTIVE as isActive, format(ISNULL(DTM_UPD, DTM_CRT),'ddMMyyyyHHmmss'),"+ 
					" ZONE_CODE, ZONE_NAME"+
					" from MS_ZONE with (nolock)"+
					"where IS_ACTIVE = :isActive";
			Object tempParams[][] = {{"isActive", "1"}};
			params = tempParams;
		} 
		else {
			query = "select UUID_ZONE, IS_ACTIVE as isActive, format(ISNULL(DTM_UPD, DTM_CRT),'ddMMyyyyHHmmss'),"+ 
					" ZONE_CODE, ZONE_NAME"+
					" from MS_ZONE with (nolock)"+
					"where format(ISNULL(DTM_UPD, DTM_CRT),'yyyyMMddHHmmss') > format(:dtmUpd,'yyyyMMddHHmmss') and IS_ACTIVE = :isActive";
			Object tempParams[][] = {{"isActive", "1"}, {"dtmUpd", datetime }};
			params = tempParams;
		}
		List result = this.getManagerDAO().selectAllNativeString( query, params);
		if(result!=null){
			for(int i=0; i<result.size();i++){
				Map temp = (Map) result.get(i);
				SyncZoneBean bean = new SyncZoneBean();
				bean.setUuid_zone(temp.get("d0").toString());
				bean.setIs_active((String)temp.get("d1"));
				bean.setDtm_upd((String)temp.get("d2"));
				bean.setZone_code((String)temp.get("d3"));
				bean.setZone_name((String)temp.get("d4"));
				zoneList.add(bean);
			}
		}
		return zoneList;
	}

	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<MsMobiledatafiles> listDataFiles(Date timestamp, AuditContext auditContext) {
		if (timestamp == null) {
			try {
				timestamp = DateUtils.parseDate("1990-01-01", "yyyy-MM-dd");
			} catch (ParseException e) {
				LOG.error("Error parsing default date 1990-01-01 with format yyyy-MM-dd", e);
			}
		}
		
		BigInteger uuidBranchUser = (BigInteger) this.getManagerDAO().selectOneNativeString(
				"SELECT UUID_BRANCH FROM AM_MSUSER with(nolock) WHERE UUID_MS_USER = :uuidMsUser",
				new String[][]{ {"uuidMsUser", auditContext.getCallerId()} });
		
		Object[][] sqlParams = {
				{"uuidBranchUser", uuidBranchUser},
				{"timestamp", timestamp} };
		List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNative("common.datafiles.list", sqlParams, null);
		List<MsMobiledatafiles> result = this.convertToMsMobiledatafiles(resultList);
						
		return result;
	}	
	
	private List<MsMobiledatafiles> convertToMsMobiledatafiles(List<Map<String, Object>> nativeResultList) {
		if (nativeResultList == null || nativeResultList.isEmpty()) {
			return Collections.emptyList();
		}
		
		List<MsMobiledatafiles> resultList = new ArrayList<>();
		for (Map<String, Object> record : nativeResultList) {
			MsMobiledatafiles mdf = new MsMobiledatafiles();			
			BigInteger bi = (BigInteger) record.get("d0");
			mdf.setIdDatafile(bi.longValue());			
			bi = null;
			mdf.setUsrCrt((String) record.get("d1")); 
			mdf.setDtmCrt((Date) record.get("d2"));
			mdf.setUsrUpd((String) record.get("d3"));
			mdf.setDtmUpd((Date) record.get("d4"));
			mdf.setIsActive((String) record.get("d5"));
			mdf.setMaxTimestamp((Date) record.get("d6"));
			mdf.setFileUrl((String) record.get("d7"));
			mdf.setAlternateFileUrl((String) record.get("d8"));
			mdf.setHashSha1((String) record.get("d9"));
			
			resultList.add(mdf);
		}
		
		nativeResultList.clear();
		nativeResultList = null;
		
		return resultList;
	}
	
	@Override
	@Transactional(readOnly=true)
	public List syncIndustryMargin(AuditContext auditContext, Date dtmUpd) {
		List<SyncTableIndustryMarginRequest> listSyncIndsMargin = new ArrayList<SyncTableIndustryMarginRequest>();
		
		String mobileDate = StringUtils.EMPTY;
		
		if (null != dtmUpd) {
			mobileDate = DateFormatUtils.format(dtmUpd, "yyyyMMddHHmmss");
		}
		
		List<Map<String, Object>> result = new ArrayList<>();
		
		String query = "select ID, IS_DELETED, INDUSTRY_TYPE_CODE, COALESCE(TOTAL_MARGIN, 0) as TOTAL_MARGIN,"
				+ " ISNULL(DTM_UPD, DTM_CRT) as DTM_UPD from STAGING_INDUSTRY_MARGIN with (nolock)";
		if (StringUtils.isNotBlank(mobileDate)) {
			query += " where format(ISNULL(DTM_UPD, DTM_CRT),'yyyyMMddHHmmss')  > :dtmUpd";
			Object params[][] = { {"dtmUpd", mobileDate } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		} else {
			result = this.getManagerDAO().selectAllNativeString(query, null);
		}
		
		if (!result.isEmpty()) {
			for (Map<String, Object> map : result) {
				SyncTableIndustryMarginRequest bean = new SyncTableIndustryMarginRequest();
				bean.setId((BigInteger) map.get("d0"));
				bean.setIsDeleted((Integer) map.get("d1"));
				bean.setIndsTypeCode((String) map.get("d2"));
				bean.setMargin((BigDecimal) map.get("d3"));
				bean.setDtmUpd(toDate(map.get("d4")));
				listSyncIndsMargin.add(bean);
			}
		}
		
		return listSyncIndsMargin;
	}
	
	@Override
	@Transactional(readOnly=true)
	public List syncBlacklistData(AuditContext auditContext, Date dtmUpd) {
		List<SyncTableBlacklistBean> listSyncBlacklist = new ArrayList<SyncTableBlacklistBean>();
		
		String mobileDate = StringUtils.EMPTY;
		
		if (null != dtmUpd) {
			mobileDate = DateFormatUtils.format(dtmUpd, "yyyyMMddHHmmss");
		}
		
		List<Map<String, Object>> result = new ArrayList<>();
		
		String query = "select ID, IS_DELETED, EXCLUDE_INFO_1, EXCLUDE_INFO_2, EXCLUDE_TYPE_CODE, EXCLUDE_TYPE_NAME,"
				+ " ISNULL(DTM_UPD, DTM_CRT) as DTM_UPD from STAGING_BLACKLIST_DATA with (nolock)";
		if (StringUtils.isNotBlank(mobileDate)) {
			query += " where format(ISNULL(DTM_UPD, DTM_CRT),'yyyyMMddHHmmss') > :dtmUpd";
			Object params[][] = { {"dtmUpd", mobileDate } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		} else {
			result = this.getManagerDAO().selectAllNativeString(query, null);
		}
		
		if (!result.isEmpty()) {
			for (Map<String, Object> map : result) {
				SyncTableBlacklistBean bean = new SyncTableBlacklistBean();
				bean.setId((BigInteger) map.get("d0"));
				bean.setIsDeleted((Integer) map.get("d1"));
				bean.setExcludeInfo1((String) map.get("d2"));
				bean.setExcludeInfo2((String) map.get("d3"));
				bean.setExcludeTypeCode((String) map.get("d4"));
				bean.setExcludeTypeName((String) map.get("d5"));
				bean.setDtmUpd(toDate(map.get("d6")));
				listSyncBlacklist.add(bean);
			}
		}
		
		return listSyncBlacklist;
	}
	
	@Override
	@Transactional(readOnly=true)
	public List syncPo(AuditContext auditContext, Date dtmUpd) {
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, new Long(auditContext.getCallerId()));
		
		List<SyncTableProductOfferingBean> listSyncPo = new ArrayList<SyncTableProductOfferingBean>();
		
		String mobileDate = StringUtils.EMPTY;
		
		if (null != dtmUpd) {
			mobileDate = DateFormatUtils.format(dtmUpd, "yyyyMMddHHmmss");
		}
		
		List<Map<String, Object>> result = new ArrayList<>();
		
		String query = "select ID, IS_DELETED, PRODUCT_OFFERING_CODE, PRODUCT_OFFERING_NAME, PO_OFFICE_CODE, PRODUCT_CATEGORY_CODE, PRODUCT_CATEGORY_NAME, "
				+ " JENIS_PEMBIAYAAN, MIN_TENOR, MAX_TENOR, SC_ID, RULE_SET_ID_RULE_DATA_ASSET_PRICE, RULE_SET_ID_RULE_DATA_APP_TC, "
				+ " RULE_SET_ID_RULE_DATA_MIN_DP, RULE_SET_ID_RULE_SCORING, RULE_SET_ID_SCORING_MATRIX, ISNULL(DTM_UPD, DTM_CRT) as DTM_UPD, "
				+ " COMPONENT, RULE_SET_ID_RULE_MANUFACTURING_YEAR, RULE_SET_ID_RULE_MINIMUM_TDP, ASSET_SCHM_H_ID, DEALER_SCHM_H_ID, SHOW_POT "
				+ " from STAGING_PRODUCT_OFFERING with (nolock) "
				+ " where PO_OFFICE_CODE in "
				+ " (select distinct BRANCH_CODE from AM_MSUSER usr with (nolock) "
				+ " join MS_BRANCH br with (nolock) on usr.UUID_BRANCH = br.UUID_BRANCH "
				+ " where UNIQUE_ID = '" + usr.getUniqueId() + "' and usr.IS_ACTIVE = '1' and br.IS_ACTIVE = '1') ";
		if (StringUtils.isNotBlank(mobileDate)) {
			query += " and format(ISNULL(DTM_UPD, DTM_CRT),'yyyyMMddHHmmss') > :dtmUpd";
			Object params[][] = { {"dtmUpd", mobileDate } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		} else {
			result = this.getManagerDAO().selectAllNativeString(query, null);
		}
		
		if (!result.isEmpty()) {
			for (Map<String, Object> map : result) {
				try {
					SyncTableProductOfferingBean bean = new SyncTableProductOfferingBean();
					bean.setId((BigInteger) map.get("d0"));
					bean.setIsDeleted((Integer) map.get("d1"));
					bean.setProdOffId((String) map.get("d2"));
					bean.setProdOffName((String) map.get("d3"));
					bean.setAssetSchemeId((BigInteger) map.get("d20"));
					bean.setDealerSchemeId((BigInteger) map.get("d21"));
					bean.setBranchId((String) map.get("d4"));
					bean.setProdCatCode((String) map.get("d5"));
					bean.setProdCatName((String) map.get("d6"));
					bean.setJnsPmbiayaan(map.get("d7")+"");
					
					String minTenor = StringUtils.EMPTY;
					if (null != map.get("d8")) {
						minTenor = map.get("d8")+"";
					} else {
						minTenor = "0";
					}
	                bean.setMinTenor(Double.valueOf(minTenor).intValue());
	                
	                String maxTenor = StringUtils.EMPTY;
					if (null != map.get("d9")) {
						maxTenor = map.get("d9")+"";
					} else {
						maxTenor = "999";
					}
					bean.setMaxTenor(Double.valueOf(maxTenor).intValue());
	                bean.setScId(String.valueOf(map.get("d10")));
					
	                bean.setRuleDataAstPrice((BigInteger) map.get("d11"));
                    bean.setRuleDataAppTc((BigInteger) map.get("d12"));
                    bean.setRuleDataMinDp((BigInteger) map.get("d13"));
                    bean.setRuleDataScoring((BigInteger) map.get("d14"));
                    bean.setRuleDataScoringMatrix((BigInteger) map.get("d15"));
                    
					bean.setDtmUpd(toDate(map.get("d16")));
					
					bean.setComponent((String) map.get("d17"));
					bean.setRuleDataManfYear((BigInteger) map.get("d18"));
					bean.setRuleDataMinTdp((BigInteger) map.get("d19"));
					bean.setIsShow((Integer) map.get("d22"));
					
					listSyncPo.add(bean);
				} catch (Exception e) {
					throw new SyncException(e.getMessage());
				}
			}
		}
		return listSyncPo;
	}
	
	@Override
	@Transactional(readOnly=true)
	public List syncPoAsset(AuditContext auditContext, Date dtmUpd) {
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, new Long(auditContext.getCallerId()));
		
		List<SyncTablePOAssetBean> listSyncPoAsset = new ArrayList<SyncTablePOAssetBean>();
		
		String mobileDate = StringUtils.EMPTY;
		
		if (null != dtmUpd) {
			mobileDate = DateFormatUtils.format(dtmUpd, "yyyyMMddHHmmss");
		}
		
		List<Map<String, Object>> result = new ArrayList<>();
		String query = "select distinct spa.ID, spa.IS_DELETED, spa.ASSET_SCHM_H_ID, HIERARCHY_L1_CODE, ASSET_HIERARCHY_L1_NAME, "
				+ " HIERARCHY_L2_CODE, ASSET_HIERARCHY_L2_NAME, GROUP_TYPE, MASTER_CODE, MASTER_NAME, "
				+ " ISNULL(spa.DTM_UPD, spa.DTM_CRT) as DTM_UPD from STAGING_PO_ASSET spa with (nolock) "
				+ " Join STAGING_ASSET_SCHEME spo with (nolock) on spa.ASSET_SCHM_H_ID = spo.ASSET_SCHM_H_ID "
				+ " Join STAGING_PRODUCT_OFFERING po with (nolock) on spo.ASSET_SCHM_H_ID = po.ASSET_SCHM_H_ID "
				+ " where PO_OFFICE_CODE in "
				+ " (select distinct BRANCH_CODE from AM_MSUSER usr with (nolock) "
				+ " join MS_BRANCH br with (nolock) on usr.UUID_BRANCH = br.UUID_BRANCH "
				+ " where UNIQUE_ID = '" + usr.getUniqueId() + "' and usr.IS_ACTIVE = '1' and br.IS_ACTIVE = '1') ";
		if (StringUtils.isNotBlank(mobileDate)) {
			query += " and format(ISNULL(spa.DTM_UPD, spa.DTM_CRT),'yyyyMMddHHmmss') > :dtmUpd";
			Object params[][] = { {"dtmUpd", mobileDate } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		} else {
			result = this.getManagerDAO().selectAllNativeString(query, null);
		}
		
		if (!result.isEmpty()) {
			for (Map<String, Object> map : result) {
				SyncTablePOAssetBean bean = new SyncTablePOAssetBean();
				bean.setId((BigInteger) map.get("d0"));
				bean.setIsDeleted((Integer) map.get("d1"));
				bean.setAssetSchemeId((BigInteger) map.get("d2"));
				bean.setBrandCode((String) map.get("d3"));
				bean.setBrandName((String) map.get("d4"));
				bean.setModelCode((String) map.get("d5"));
				bean.setModelName((String) map.get("d6"));
				bean.setGroupType((String) map.get("d7"));
				bean.setMasterCode((String) map.get("d8"));
				bean.setMasterName((String) map.get("d9"));
				bean.setDtmUpd(toDate(map.get("d10")));
				listSyncPoAsset.add(bean);
			}
		}
		return listSyncPoAsset;
	}
	
	@Override
	@Transactional(readOnly=true)
	public List syncPoAssetScheme(AuditContext auditContext, Date dtmUpd) {
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, new Long(auditContext.getCallerId()));
		
		List<SyncTableAssetSchemeBean> listSyncAssetScheme = new ArrayList<SyncTableAssetSchemeBean>();
		
		String mobileDate = StringUtils.EMPTY;
		
		if (null != dtmUpd) {
			mobileDate = DateFormatUtils.format(dtmUpd, "yyyyMMddHHmmss");
		}
		
		List<Map<String, Object>> result = new ArrayList<>();
		String query = "select distinct sas.ID, sas.IS_DELETED, sas.ASSET_SCHM_H_ID, sas.ASSET_TYPE_CODE, sas.ASSET_TYPE_NAME, "
				+ " ISNULL(sas.DTM_UPD, sas.DTM_CRT) as DTM_UPD From STAGING_ASSET_SCHEME sas with (nolock) "
				+ " Join STAGING_PRODUCT_OFFERING spo on sas.ASSET_SCHM_H_ID = spo.ASSET_SCHM_H_ID "
				+ " where PO_OFFICE_CODE in "
				+ " (select distinct BRANCH_CODE from AM_MSUSER usr with (nolock) "
				+ " join MS_BRANCH br with (nolock) on usr.UUID_BRANCH = br.UUID_BRANCH "
				+ " where UNIQUE_ID = '" + usr.getUniqueId() + "' and usr.IS_ACTIVE = '1' and br.IS_ACTIVE = '1') ";
		if (StringUtils.isNotBlank(mobileDate)) {
			query += " and format(ISNULL(sas.DTM_UPD, sas.DTM_CRT),'yyyyMMddHHmmss') > :dtmUpd";
			Object params[][] = { {"dtmUpd", mobileDate } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		} else {
			result = this.getManagerDAO().selectAllNativeString(query, null);
		}
		
		if (!result.isEmpty()) {
			for (Map<String, Object> map : result) {
				SyncTableAssetSchemeBean bean = new SyncTableAssetSchemeBean();
				bean.setId((BigInteger) map.get("d0"));
				bean.setIsDeleted((Integer) map.get("d1"));
				bean.setAssetSchemeId((BigInteger) map.get("d2"));
				bean.setAssetTypeCode((String) map.get("d3"));
				bean.setAssetTypeName((String) map.get("d4"));
				bean.setDtmUpd(toDate(map.get("d5")));
				listSyncAssetScheme.add(bean);
			}
		}
		return listSyncAssetScheme;
	}
	
	@Override
	@Transactional(readOnly=true)
	public List syncPoDealer(AuditContext auditContext, Date dtmUpd) {
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, new Long(auditContext.getCallerId()));
		
		List<SyncTablePODealerBean> listSyncPoDealer = new ArrayList<SyncTablePODealerBean>();
		
		String mobileDate = StringUtils.EMPTY;
		
		if (null != dtmUpd) {
			mobileDate = DateFormatUtils.format(dtmUpd, "yyyyMMddHHmmss");
		}
		
		List<Map<String, Object>> result = new ArrayList<>();
		
		String query = " select distinct pod.ID, pod.IS_DELETED, pod.DEALER_SCHM_H_ID, pod.DEALER_CODE, pod.DEALER_NAME, "
				+ "  ISNULL(pod.DTM_UPD, pod.DTM_CRT) as DTM_UPD from STAGING_PO_DEALER pod with (nolock) "
				+ " Join STAGING_PRODUCT_OFFERING po with (nolock) on pod.DEALER_SCHM_H_ID = po.DEALER_SCHM_H_ID "
				+ " where PO_OFFICE_CODE in "
				+ " (select distinct BRANCH_CODE from AM_MSUSER usr with (nolock) "
				+ " join MS_BRANCH br with (nolock) on usr.UUID_BRANCH = br.UUID_BRANCH "
				+ " where UNIQUE_ID = '" + usr.getUniqueId() + "' and usr.IS_ACTIVE = '1' and br.IS_ACTIVE = '1') ";
		
		if (StringUtils.isNotBlank(mobileDate)) {
			query += " and format(ISNULL(pod.DTM_UPD, pod.DTM_CRT),'yyyyMMddHHmmss') > :dtmUpd";
			Object params[][] = { {"dtmUpd", mobileDate } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		} else {
			result = this.getManagerDAO().selectAllNativeString(query, null);
		}
		
		if (!result.isEmpty()) {
			for (Map<String, Object> map : result) {
				SyncTablePODealerBean bean = new SyncTablePODealerBean();
				bean.setId((BigInteger) map.get("d0"));
				bean.setIsDeleted((Integer) map.get("d1"));
				bean.setDealerSchemeId((BigInteger) map.get("d2"));
				bean.setDealerId((String) map.get("d3"));
				bean.setDealerName((String) map.get("d4"));
				bean.setDtmUpd(toDate(map.get("d5")));
				listSyncPoDealer.add(bean);
			}
		}
		
		return listSyncPoDealer;
	}
	
	@Override
	@Transactional(readOnly=true)
	public List syncPoOffice(AuditContext auditContext, Date dtmUpd) {
		List<SyncTablePOOfficeBean> listSyncPoOffice = new ArrayList<SyncTablePOOfficeBean>();
		
		String mobileDate = StringUtils.EMPTY;
		
		if (null != dtmUpd) {
			mobileDate = DateFormatUtils.format(dtmUpd, "yyyyMMddHHmmss");
		}
		
		List<Map<String, Object>> result = new ArrayList<>();
		
		String query = "select soo.ID, soo.IS_DELETED, spo.PRODUCT_OFFERING_CODE, soo.OFFICE_CODE, ISNULL(soo.DTM_UPD, soo.DTM_CRT) as DTM_UPD from STAGING_PO_OFFICE soo with (nolock)"
				+ " join STAGING_PRODUCT_OFFERING spo with (nolock)  on soo.PRODUCT_OFFERING_ID = spo.PRODUCT_OFFERING_ID";
		
		if (StringUtils.isNotBlank(mobileDate)) {
			query += " where format(ISNULL(DTM_UPD, DTM_CRT),'yyyyMMddHHmmss') > :dtmUpd";
			Object params[][] = { {"dtmUpd", mobileDate } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		} else {
			result = this.getManagerDAO().selectAllNativeString(query, null);
		}
		
		if (!result.isEmpty()) {
			for (Map<String, Object> map : result) {
				SyncTablePOOfficeBean bean = new SyncTablePOOfficeBean();
				bean.setId((BigInteger) map.get("d0"));
				bean.setIsDeleted((Integer) map.get("d1"));
				bean.setProdOffId((String) map.get("d2"));
				bean.setOfficeId((String) map.get("d3"));
				bean.setDtmUpd(toDate(map.get("d4")));
				listSyncPoOffice.add(bean);
			}
		}
		
		return listSyncPoOffice;
	}
	
	@Override
	@Transactional(readOnly=true)
	public List syncRuleFile(AuditContext auditContext, Date dtmUpd) {
		List<SyncTableRuleFileBean> listSyncRule = new ArrayList<SyncTableRuleFileBean>();
		
		String mobileDate = StringUtils.EMPTY;
		
		if (null != dtmUpd) {
			mobileDate = DateFormatUtils.format(dtmUpd, "yyyyMMddHHmmss");
		}
		
		List<Map<String, Object>> result = new ArrayList<>();
		
		String query = "select ID, IS_DELETED, RULE_ID, RULE_NAME, ISNULL(DTM_UPD, DTM_CRT) as DTM_UPD from STAGING_RULE_FILE with (nolock)";
		
		if (StringUtils.isNotBlank(mobileDate)) {
			query += " where format(ISNULL(DTM_UPD, DTM_CRT),'yyyyMMddHHmmss') > :dtmUpd";
			Object params[][] = { {"dtmUpd", mobileDate } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		} else {
			result = this.getManagerDAO().selectAllNativeString(query, null);
		}
		
		if (!result.isEmpty()) {
			for (Map<String, Object> map : result) {
				try {
					BigInteger ruleId = (BigInteger) map.get("d2");
					String ruleName = (String) map.get("d3");
					String uri = URI_RULE_FILE + "/" + ruleId + "_" + ruleName + ".xls";
					
					SyncTableRuleFileBean bean = new SyncTableRuleFileBean();
					bean.setId((BigInteger) map.get("d0"));
					bean.setIsDeleted((Integer) map.get("d1"));
					bean.setRuleName(ruleName);
					bean.setUrl(uri);
					bean.setDtmUpd(toDate(map.get("d4")));
					listSyncRule.add(bean);
				} catch (Exception e) {
					throw new SyncException(e.getMessage());
				}
			}
		}
		
		return listSyncRule;
	}	
	
	@Override
	@Transactional(readOnly=true)
	public List syncOtr(AuditContext auditContext, Date dtmUpd) {
		List<SyncTableOtrBean> listSyncOtr = new ArrayList<SyncTableOtrBean>();
		
		String mobileDate = StringUtils.EMPTY;
		
		AmMsuser user =  this.getManagerDAO().selectOne( 
				"from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(auditContext.getCallerId())}});
		
		if (null != dtmUpd) {
			mobileDate = DateFormatUtils.format(dtmUpd, "yyyyMMddHHmmss");
		}
		
		List<Map<String, Object>> result = new ArrayList<>();
		
		String query = "select ID, IS_DELETED, ASSET_CODE, MANUFACTURING_YEAR, "
				+ " TOLERANCE_PRCTG, MARKET_PRICE, EFFECTIVE_DT, ISNULL(DTM_UPD, DTM_CRT) as DTM_UPD, OFFICE_CODE "
				+ " from STAGING_OTR_WITH_OFFICE with (nolock)";
		
		if (StringUtils.isNotBlank(mobileDate)) {
			query += " where OFFICE_CODE = :officeCode and format(ISNULL(DTM_UPD, DTM_CRT),'yyyyMMddHHmmss') > :dtmUpd";
			Object params[][] = { {"officeCode", user.getMsBranch().getBranchCode() }, {"dtmUpd", mobileDate } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		} else {
			query += " where OFFICE_CODE = :officeCode";
			Object params[][] = { {"officeCode", user.getMsBranch().getBranchCode() } };
			result = this.getManagerDAO().selectAllNativeString(query, params);
		}
		
		if (!result.isEmpty()) {
			for (Map<String, Object> map : result) {
				try {
					SyncTableOtrBean bean = new SyncTableOtrBean();
					bean.setId((BigInteger) map.get("d0"));
					bean.setIsDeleted((Integer) map.get("d1"));
					bean.setAssetCode((String) map.get("d2"));
					bean.setManfYear((String) map.get("d3"));
					bean.setTolerancePrctg(checkBigDecimal(map.get("d4")));
					bean.setMarketPrice(checkBigDecimal(map.get("d5")));
					bean.setEffDate(toDate(map.get("d6")));
					bean.setDtmUpd(toDate(map.get("d7")));
					bean.setOfficeCode((String) map.get("d8"));
					listSyncOtr.add(bean);
				} catch (Exception e) {
					throw new SyncException(e.getMessage());
				}
			}
		}
		
		return listSyncOtr;
	}	
	
	private BigDecimal checkBigDecimal(Object in){
		BigDecimal bd;
		if (in instanceof BigDecimal) {
			return (BigDecimal) in;
		} else if (in instanceof String) {
			String val = (String) in;
			if (StringUtils.isBlank(val)){
				bd = new BigDecimal("0");
			}
			else{
				bd = new BigDecimal(val);
			}
		} else {
			bd = new BigDecimal("0");
		}
		
		return bd;
	}

	@Transactional(readOnly = true)
	@Override
	public List syncPushTable(AuditContext auditContext) {
		// TODO Auto-generated method stub
		Object params[][] = {{"uuidUser", auditContext.getCallerId()}};
		StringBuilder queryGetListPushSyncFiles = new StringBuilder("");
		queryGetListPushSyncFiles.append("SELECT tps.SEQNO as idPushSync, mmpf.IS_ACTIVE as isActive, mmpf.MAX_TIMESTAMPS as maxTimestamps, ")
			.append(" mmpf.TABLE_NAME as tableName, mmpf.LOV_GROUP as lovGroup, mmpf.TOTAL_RECORD as totalRecord, mmpf.FILENAME as filename, mmpf.FILE_URL as fileUrl ")
			.append(" FROM TR_PUSHSYNC tps with(nolock) ")
			.append(" JOIN MS_MOBILE_PUSHSYNC_FILES mmpf with(nolock) ON mmpf.ID_PUSHSYNC_FILE = tps.ID_PUSHSYNC_FILE ")
			.append(" WHERE tps.UUID_MS_USER = :uuidUser ")
			.append(" AND tps.FLAG = '0' ");
		List<SyncPushTableBean> listPushSyncFiles = this.getManagerDAO().selectForListString(SyncPushTableBean.class, queryGetListPushSyncFiles.toString(), params, null);
		if (listPushSyncFiles.isEmpty()) {
			return new ArrayList<>();
		} else {
			return listPushSyncFiles;
		}
	}
	
	/**
	 * Logic used in sync endpoint {@code /syncTableConstraint} for ticket WOMFMSS-867
	 * 
	 * @return The requested product offering data.
	 * <AUTHOR>
	 * @version 1.0
	 * @since 2025-04-25
	 */
	@Override
	@Transactional(readOnly=true)
	public SyncTableProductOfferingBean syncTablePoConstraint(String productOfferingCode, String branchCode) {
		
		SyncTableProductOfferingBean beanPo = new SyncTableProductOfferingBean();
		
		
		String query = "select top 1 ID, IS_DELETED, PRODUCT_OFFERING_CODE, PRODUCT_OFFERING_NAME, PO_OFFICE_CODE, PRODUCT_CATEGORY_CODE, PRODUCT_CATEGORY_NAME, "
				+ " JENIS_PEMBIAYAAN, MIN_TENOR, MAX_TENOR, SC_ID, RULE_SET_ID_RULE_DATA_ASSET_PRICE, RULE_SET_ID_RULE_DATA_APP_TC, "
				+ " RULE_SET_ID_RULE_DATA_MIN_DP, RULE_SET_ID_RULE_SCORING, RULE_SET_ID_SCORING_MATRIX, ISNULL(DTM_UPD, DTM_CRT) as DTM_UPD, "
				+ " COMPONENT, RULE_SET_ID_RULE_MANUFACTURING_YEAR, RULE_SET_ID_RULE_MINIMUM_TDP, ASSET_SCHM_H_ID, DEALER_SCHM_H_ID, SHOW_POT "
				+ " from STAGING_PRODUCT_OFFERING with (nolock) "
				+ " where PO_OFFICE_CODE = (select BRANCH_CODE from MS_BRANCH with (nolock) where BRANCH_CODE = '" + branchCode + "')"
				+ " and PRODUCT_OFFERING_CODE = '" + productOfferingCode + "'";
		
		Object[] resultSet =  (Object[]) this.getManagerDAO().selectOneNativeString(query, null);
		
		if(null != resultSet && resultSet.length > 0) {
			try {
				
				beanPo.setId((BigInteger) resultSet[0]);
				beanPo.setIsDeleted((Integer) resultSet[1]);
				beanPo.setProdOffId((String) resultSet[2]);
				beanPo.setProdOffName((String) resultSet[3]);
				beanPo.setAssetSchemeId((BigInteger) resultSet[20]);
				beanPo.setDealerSchemeId((BigInteger) resultSet[21]);
				beanPo.setBranchId((String) resultSet[4]);
				beanPo.setProdCatCode((String) resultSet[5]);
				beanPo.setProdCatName((String) resultSet[6]);
				beanPo.setJnsPmbiayaan(resultSet[7]+"");
				
				String minTenor = StringUtils.EMPTY;
				if (null != resultSet[8]) {
					minTenor = resultSet[8]+"";
				} else {
					minTenor = "0";
				}
                beanPo.setMinTenor(Double.valueOf(minTenor).intValue());
                
                String maxTenor = StringUtils.EMPTY;
				if (null != resultSet[9]) {
					maxTenor = resultSet[9]+"";
				} else {
					maxTenor = "999";
				}
				beanPo.setMaxTenor(Double.valueOf(maxTenor).intValue());
                beanPo.setScId(String.valueOf(resultSet[10]));
				
                beanPo.setRuleDataAstPrice((BigInteger) resultSet[11]);
                beanPo.setRuleDataAppTc((BigInteger) resultSet[12]);
                beanPo.setRuleDataMinDp((BigInteger) resultSet[13]);
                beanPo.setRuleDataScoring((BigInteger) resultSet[14]);
                beanPo.setRuleDataScoringMatrix((BigInteger) resultSet[15]);
                
				beanPo.setDtmUpd(toDate(resultSet[16]));
				
				beanPo.setComponent((String) resultSet[17]);
				beanPo.setRuleDataManfYear((BigInteger) resultSet[18]);
				beanPo.setRuleDataMinTdp((BigInteger) resultSet[19]);
				beanPo.setIsShow((Integer) resultSet[22]);
				
			} catch (Exception e) {
				throw new SyncException(e.getMessage());
			}
		}
		return beanPo;
	}

	@Transactional
	@Override
	public void updateStatusSyncPushTable(AuditContext auditContext, long idPushSync) {
		Object[][] params = {{"idPushSync", idPushSync}};
		Integer isExist = (Integer) this.getManagerDAO().selectOneNativeString("SELECT COUNT(1) FROM TR_PUSHSYNC WITH(NOLOCK) WHERE SEQNO = :idPushSync ", params);
		if (isExist > 0) {
			Object[][] paramsUpdate = {{"idPushSync", idPushSync}, {"uuidUser", auditContext.getCallerId()}};
			this.getManagerDAO().updateNativeString("UPDATE TR_PUSHSYNC SET FLAG = '1', USR_UPD = :uuidUser, DTM_UPD = GETDATE() WHERE SEQNO = :idPushSync", paramsUpdate);
		} else {
			throw new RemoteException(" Data push sync for id "+idPushSync+" is not exist!"); 
		}
	}
	
}
