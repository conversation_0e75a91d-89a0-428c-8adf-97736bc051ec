package com.adins.mss.businesslogic.impl.common;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.TaskAssignmentLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.TblMapProdAssign;
import com.adins.mss.model.TblProductCategory;
import com.adins.mss.model.TblTaskAssignMode;
import com.adins.mss.model.custom.UploadFormBean;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericTaskAssignmentLogic extends BaseLogic implements TaskAssignmentLogic {
	
	private static final Logger LOG = LoggerFactory
			.getLogger(GenericTaskAssignmentLogic.class);
	private AuditInfo auditInfoP;
	private AuditInfo auditInfoMPA;
	
	public GenericTaskAssignmentLogic() {
		String[] pkColsP = { "tblProductCategoryId" };
		String[] pkDbColsP = { "TBL_PRODUCT_CATEGORY_ID" };
		String[] colsP = { "tblProductCategoryId", "isDeleted", "productCategoryCode",
				"productCategoryName", "jenisPembiayaan", "usrCrt",
				"dtmCrt", "usrUpd", "dtmUpd" };
		String[] dbColsP = { "TBL_PRODUCT_CATEGORY_ID", "IS_DELETED", "PRODUCT_CATEGORY_CODE",
				"PRODUCT_CATEGORY_NAME", "JENIS_PEMBIAYAAN", "USR_CRT",
				"DTM_CRT", "USR_UPD", "DTM_UPD" };
		this.auditInfoP = new AuditInfo("TBL_PRODUCT_CATEGORY", pkColsP, pkDbColsP, colsP,
				dbColsP);
		
		String[] pkColsMPA = { "tblMapProdAssignId" };
		String[] pkDbColsMPA = { "TBL_MAP_PROD_ASSIGN_ID" };
		String[] colsMPA = { "tblMapProdAssignId", "tblProductCategory.tblProductCategoryId",
				"tblTaskAssignMode.tblTaskAssignModeId", "seqOrder" };
		String[] dbColsMPA = { "TBL_MAP_PROD_ASSIGN_ID",
				"TBL_PRODUCT_CATEGORY_ID", "TBL_TASK_ASSIGN_MODE_ID", "SEQ_ORDER" };
		this.auditInfoMPA = new AuditInfo("TBL_MAP_PROD_ASSIGN",
				pkColsMPA, pkDbColsMPA, colsMPA, dbColsMPA);
	}
	
	UploadFormBean uploadBean = new UploadFormBean();

	@Transactional(readOnly = true)
	@Override
	public Map<String, Object> listTaskAssignment(Object params, Object orders,int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO().selectAll(TblProductCategory.class, params,
				orders, pageNumber, pageSize);
		List <TblProductCategory> list = (List<TblProductCategory>) result.get(GlobalKey.MAP_RESULT_LIST);
		result.put(GlobalKey.MAP_RESULT_LIST, list);
		return result;
	}

	@Override
	public TblProductCategory getTblProductCategory(String uuid, AuditContext callerId) {
		TblProductCategory result = this.getManagerDAO().selectOne(TblProductCategory.class, new Object[][] {{Restrictions.eq("tblProductCategoryId", Long.valueOf(uuid))}});
		return result;
	}
	
	@Override
	public TblTaskAssignMode getTblTaskAssignMode(String code, AuditContext callerId) {
		TblTaskAssignMode result = this.getManagerDAO().selectOne(TblTaskAssignMode.class, new Object[][] {{Restrictions.eq("assignCode", code)}});
		return result;
	}

	@Override
	public List getProductCategory(AuditContext callerId) {
		
		String[][] orders = { {"productCategoryName", "ASC"}};
		Object [][] param = {{Restrictions.eq("isMapped", 0)}, {Restrictions.eq("isDeleted", 0)}};
		
		Map<String, Object> result = this.getManagerDAO().selectAll(TblProductCategory.class, param,orders);
		List <TblProductCategory> list = (List<TblProductCategory>) result.get(GlobalKey.MAP_RESULT_LIST);
		
		return list;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String insertTaskAssignment(TblProductCategory tblProductCategory, String taskAssignMode, String order,AuditContext callerId) {
		TblProductCategory dbModel = this.getTblProductCategory(String.valueOf(tblProductCategory.getTblProductCategoryId()), callerId);
		
		dbModel.setIsMapped(1);
		dbModel.setDtmCrt(new Date());
		dbModel.setUsrCrt(callerId.getCallerId());
		this.getManagerDAO().update(dbModel);
		this.auditManager.auditAdd(dbModel, auditInfoP, callerId.getCallerId(), "");
		this.insertMapProductAssign(taskAssignMode, order, String.valueOf(tblProductCategory.getTblProductCategoryId()), callerId);
		String result = String.valueOf(dbModel.getTblProductCategoryId());
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String updateTaskAssignment(TblProductCategory tblProductCategory, String taskAssignMode, String order,
			AuditContext callerId) {
		TblProductCategory dbModel = this.getTblProductCategory(String.valueOf(tblProductCategory.getTblProductCategoryId()), callerId);
				
		dbModel.setDtmUpd(new Date());
		dbModel.setUsrUpd(callerId.getCallerId());

		this.auditManager.auditEdit(dbModel, auditInfoP,
				callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
		this.insertMapProductAssign(taskAssignMode, order, String.valueOf(tblProductCategory.getTblProductCategoryId()), callerId);
		String result = String.valueOf(tblProductCategory.getTblProductCategoryId());
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String checkSequence(String selectedTAModeArr, String order, AuditContext callerId) {
		int seqKDL = 0;
		int seqAlternate1 = 0;
		boolean isAlt1Exist = false;
		int seqAlternate2 = 0;
		boolean isAlt2Exist = false;
		String KDL=StringUtils.EMPTY;
		String result = StringUtils.EMPTY;
		
		if (selectedTAModeArr != null && !selectedTAModeArr.isEmpty()) {
			String[] selectedTAMode = selectedTAModeArr.split(",");
			String[] orderTAMode = order.split(",");
			for (int i = 0; i < selectedTAMode.length; i++) {
				TblTaskAssignMode tblTaskAssignMode = this.getManagerDAO().selectOne(TblTaskAssignMode.class,
						Long.valueOf(selectedTAMode[i]));
				if (GlobalVal.MAPPING_DEALER_TYPE_NON_FOCUS.equalsIgnoreCase(tblTaskAssignMode.getAssignCode())) {
					break;
				}
				int orderMode = Integer.valueOf(orderTAMode[i]);
				if (GlobalVal.MAPPING_KAT.equalsIgnoreCase(tblTaskAssignMode.getAssignCode())
						|| GlobalVal.MAPPING_DEALER_TYPE_FOCUS.equalsIgnoreCase(tblTaskAssignMode.getAssignCode())
						|| GlobalVal.MAPPING_LOAD.equalsIgnoreCase(tblTaskAssignMode.getAssignCode())) {
					if (orderMode > seqKDL) {
						seqKDL = orderMode;
						KDL = tblTaskAssignMode.getAssignDescription();
					}
				} else if (GlobalVal.MAPPING_ALTERNATE_1.equalsIgnoreCase(tblTaskAssignMode.getAssignCode())) {
					isAlt1Exist = true;
					seqAlternate1 = orderMode;
				} else if (GlobalVal.MAPPING_ALTERNATE_2.equalsIgnoreCase(tblTaskAssignMode.getAssignCode())) {
					isAlt2Exist = true;
					seqAlternate2 = orderMode;
				}
			}
		}
		
		if(isAlt1Exist && seqAlternate1 < seqKDL) {
			result = "Alternate 1 must be larger than "+KDL;
		}
		if(isAlt1Exist && isAlt2Exist && seqAlternate2 < seqAlternate1) {
			result = "Alternate 2 must be larger than Alternate 1";
		}
		
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void insertMapProductAssign(String selectedTAModeArr, String order,
			String tblProductCategoryId, AuditContext callerId) {
		
		String[][] param = { { "tblProductCategoryId", tblProductCategoryId } };
		this.getManagerDAO().deleteNativeString("delete from TBL_MAP_PROD_ASSIGN where TBL_PRODUCT_CATEGORY_ID = :tblProductCategoryId", param);
		
		if (selectedTAModeArr != null && !selectedTAModeArr.isEmpty()) {
			String[] selectedTAMode = selectedTAModeArr.split(",");
			String[] orderTAMode = order.split(",");
			for (int i = 0; i < selectedTAMode.length; i++) {
				TblProductCategory tblProductCategory = this.getManagerDAO().selectOne(TblProductCategory.class,
						Long.valueOf(tblProductCategoryId));
				TblTaskAssignMode tblTaskAssignMode = this.getManagerDAO()
						.selectOne(TblTaskAssignMode.class, Long.valueOf(selectedTAMode[i]));
				TblMapProdAssign bean = new TblMapProdAssign();
				bean.setTblProductCategory(tblProductCategory);;
				bean.setTblTaskAssignMode(tblTaskAssignMode);
				bean.setUsrCrt(callerId.getCallerId());
				bean.setDtmCrt(new Date());
				bean.setSeqOrder(Integer.parseInt(orderTAMode[i]));
				this.getManagerDAO().insert(bean);
				this.auditManager.auditAdd(bean, auditInfoMPA, callerId.getCallerId(), "");
			}
		}
	}

	@Override
	public List taskAssignModeList(Object params, AuditContext callerId) {
		
		StringBuilder sb = new StringBuilder()
			.append("select * from ( ")
			.append("			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("				select TBL_TASK_ASSIGN_MODE_ID, ASSIGN_DESCRIPTION, ASSIGN_CODE, ")
			.append("					ROW_NUMBER() OVER (order by TBL_TASK_ASSIGN_MODE_ID asc) AS rownum ")
			.append("				from TBL_TASK_ASSIGN_MODE with (nolock) ")
			.append("				where IS_DELETED=0 ")
			.append("					and not TBL_TASK_ASSIGN_MODE_ID in (:taskAssignModeId) ")
			.append("			) a WHERE a.ROWNUM <= :end ")
			.append("		) b WHERE b.recnum >= :start ");
	    
	    List list = this.getManagerDAO().selectAllNativeString(sb.toString(), params);
		return list;
	}

	@Override
	public Map<String, Object> getListMapProductAssign(long productCategoryId,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;		
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		
		condition.append(" and tmpa.tblProductCategory.tblProductCategoryId=:tblProductCategoryId");
		paramMap.put("tblProductCategoryId", productCategoryId);
		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by tmpa.seqOrder asc");
		
		if (pageNumber == 0 && pageSize == 0) {
			result = this.getManagerDAO().selectAll(
					"from TblMapProdAssign tmpa join fetch tmpa.tblProductCategory join fetch tmpa.tblTaskAssignMode where 1=1"
							+ condition.toString() + orderQuery.toString(),
					paramMap);
		} 
		else {
			result = this.getManagerDAO().selectAll(
					"from TblMapProdAssign tmpa join fetch tmpa.tblProductCategory join fetch tmpa.tblTaskAssignMode where 1=1"
							+ condition.toString() + orderQuery.toString(),
					"select count(*) from TblMapProdAssign tmpa join tmpa.TblProductCategory join tmpa.TblTaskAssignMode where 1=1"
							+ condition.toString(),
					paramMap, pageNumber, pageSize);
		}
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteTaskAssignment(String tblProductCategoryId, AuditContext callerId) {
		TblProductCategory dbModel = this.getTblProductCategory(tblProductCategoryId, callerId);
		
		dbModel.setIsMapped(0);
		dbModel.setDtmUpd(null);
		dbModel.setUsrUpd(null);
		
		this.auditManager.auditEdit(dbModel, auditInfoP,
				callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
		
		Object[][] params = { { "tblProductCategoryId", Long.valueOf(tblProductCategoryId) } };
		this.getManagerDAO().deleteNativeString("delete from TBL_MAP_PROD_ASSIGN where TBL_PRODUCT_CATEGORY_ID = :tblProductCategoryId", params);
		
	}
	
	@Override
	public List<TblTaskAssignMode> getListSelectedTAM(String[] selectedTAMode,
			AuditContext callerId) {
		List<TblTaskAssignMode> result = new ArrayList<TblTaskAssignMode>();
		if (selectedTAMode != null
				&& !ArrayUtils.isEmpty(selectedTAMode)) {
			for (int i = 0; i < selectedTAMode.length; i++) {
				LOG.info("Select Task Assign Mode UUID {} ",
						selectedTAMode[i]);
				TblTaskAssignMode tblTaskAssignMode = this.getManagerDAO()
						.selectOne(TblTaskAssignMode.class,
								Long.valueOf(selectedTAMode[i]));
				result.add(tblTaskAssignMode);
			}
		}
		return result;
	}

	@Override
	public String retrieveTaskAssignModeCode(String[] selectedTaskAssignMode) {
		if (null != selectedTaskAssignMode) {
			String taskAssignModeCode = StringUtils.EMPTY;
			for (int i = 0; i < selectedTaskAssignMode.length; i++) {
				TblTaskAssignMode tblTaskAssignMode = this.getManagerDAO().selectOne(
						TblTaskAssignMode.class, Long.valueOf(selectedTaskAssignMode[i]));
				taskAssignModeCode += tblTaskAssignMode.getAssignCode() + ";";
			}
			return taskAssignModeCode;
		}
		return StringUtils.EMPTY;
	}
}
