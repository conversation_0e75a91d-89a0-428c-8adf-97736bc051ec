package com.adins.mss.businesslogic.impl.common;

import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.expression.ParseException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsattribute;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsAreapath;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.MsTaskdistribution;
import com.adins.mss.model.MsTskdistributionofmodule;
import com.adins.mss.model.TblPoloData;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.services.model.common.AddTaskPoloRequest;
import com.adins.mss.services.model.common.GetReferantorResponse;
import com.adins.mss.services.model.newconfins.UpdateStatusMainDealerResponse;
import com.google.gson.Gson;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericTaskDistributionLogic extends BaseLogic implements TaskDistributionLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericTaskDistributionLogic.class);
	
	private Gson gson = new Gson();
	
	private IntFormLogic intFormLogic;

	private AuditInfo auditInfo;
	@Autowired
	private MessageSource messageSource;

	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	public GenericTaskDistributionLogic() {
		String[] pkCols = { "uuidTskDstrbtnOfModule" };
		String[] pkDbCols = { "UUID_TSK_DSTRBTN_OF_MODULE" };
		String[] cols = { "uuidTskDstrbtnOfModule", "amMssubsystem.uuidMsSubsystem",
				"msTaskdistribution.uuidTaskDistribution" };
		String[] dbCols = { "UUID_TSK_DSTRBTN_OF_MODULE", "UUID_MS_SUBSYSTEM", "UUID_TASK_DISTRIBUTION" };
		this.auditInfo = new AuditInfo("MS_TSKDISTRIBUTIONOFMODULE", pkCols, pkDbCols, cols, dbCols);
	}

	private GlobalLogic globalLogic;
	private CommonLogic commonLogic;

	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	@Override
	public Map<String, Object> listTaskDistribution(Object params, Object orders, AuditContext callerId) {
		Map<String, Object> result = this.getManagerDAO().selectAll(MsTaskdistribution.class, params, orders);
		return result;
	}

	@Override
	public String getUuidTaskDistributionFk(long subsystemName, AuditContext callerId) {
		String uuidTaskDistributionFk = "";
		Object[][] params = { { Restrictions.eq("amMssubsystem.uuidMsSubsystem", subsystemName) } };
		MsTskdistributionofmodule dbModel = this.getManagerDAO().selectOne(MsTskdistributionofmodule.class, params);
		if (dbModel != null) {
			uuidTaskDistributionFk = String.valueOf(dbModel.getMsTaskdistribution().getUuidTaskDistribution());
		}
		return uuidTaskDistributionFk;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateTaskDistribution(long uuidSubsystem, String uuidTaskDistributionFk, AuditContext callerId) {
		Object[][] params = { { Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidSubsystem) } };
		MsTskdistributionofmodule dbModel = this.getManagerDAO().selectOne(MsTskdistributionofmodule.class, params);
		MsTaskdistribution mt = new MsTaskdistribution();
		mt.setUuidTaskDistribution(Long.valueOf(uuidTaskDistributionFk));
		dbModel.setMsTaskdistribution(mt);
		this.auditManager.auditEdit(dbModel, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void insertTaskDistribution(long uuidSubsystem, String uuidTaskDistributionFk, AuditContext callerId) {
		MsTskdistributionofmodule obj = new MsTskdistributionofmodule();
		AmMssubsystem as = new AmMssubsystem();
		MsTaskdistribution mt = new MsTaskdistribution();
		as.setUuidMsSubsystem(uuidSubsystem);
		obj.setAmMssubsystem(as);
		mt.setUuidTaskDistribution(Long.valueOf(uuidTaskDistributionFk));
		obj.setMsTaskdistribution(mt);
		obj.setDtmCrt(new Date());
		obj.setUsrCrt(callerId.getCallerId());
		this.getManagerDAO().insert(obj);
		this.auditManager.auditAdd(obj, auditInfo, callerId.getCallerId(), "");
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public Map<String, Object> distributeRoundRobin(List<TrTaskH> listTaskDist, AmMssubsystem subsystem, String uuidMh,
			String type, String mode, AuditContext auditContext) {
		Map<String, Object> result = new HashMap<>();
		String svyJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, auditContext);
		MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_NEW, auditContext);
		for (int i = 0; i < listTaskDist.size(); i++) {
			TrTaskH bean = listTaskDist.get(i);
			boolean flagNextUser = false;
			AmMsuser userMh = null;

			String[][] params = { { "uuidManager", uuidMh } };
			if ("branch".equals(mode)) {
				userMh = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(uuidMh));
				params = new String[][] { { "uuidBranch", String.valueOf(userMh.getMsBranch().getUuidBranch()) },
						{ "jobCode", svyJobCode } };
			}
			Object[] nextUser = (Object[]) this.getManagerDAO()
					.selectOneNative("branch".equals(mode) ? "services.submitorder.getNextUserByBranch"
							: "services.submitorder.getNextUser", params);
			String userAssign = StringUtils.EMPTY;

			if (nextUser == null) {
				Object[] nextUserTop = (Object[]) this.getManagerDAO()
						.selectOneNative("branch".equals(mode) ? "services.submitorder.getNextUserTopByBranch"
								: "services.submitorder.getNextUserTop", params);
				if (nextUserTop == null) {
					flagNextUser = false;
				} else {
					flagNextUser = true;
					nextUser = nextUserTop;
				}
			} else {
				flagNextUser = true;

				AmMsuser next = this.getManagerDAO().selectOne(
						"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser",
						new Object[][] { { "uuidMsUser", Long.valueOf(String.valueOf(nextUser[0])) } });

				String[][] paramsTaskLoad = { { "uuidUser", String.valueOf(next.getUuidMsUser()) },
						{ "uuidSubsystem", String.valueOf(next.getAmMssubsystem().getUuidMsSubsystem()) },
						{ "statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION } };

				Integer totalTask = (Integer) this.getManagerDAO().selectOneNative("services.submitorder.getTotalTask",
						paramsTaskLoad);

				if (totalTask >= next.getMaxTaskLoad()) {
					String[][] paramsNext = { { "uuidUser", String.valueOf(next.getUuidMsUser()) },
							{ "uuidSubsystem", String.valueOf(next.getAmMssubsystem().getUuidMsSubsystem()) },
							{ "statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION },
							{ "uuidManager", String.valueOf(next.getAmMsuser().getUuidMsUser()) } };
					if ("branch".equals(mode)) {
						paramsNext = new String[][] { { "uuidUser", String.valueOf(next.getUuidMsUser()) },
								{ "uuidSubsystem", String.valueOf(next.getAmMssubsystem().getUuidMsSubsystem()) },
								{ "statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION },
								{ "uuidBranch", String.valueOf(next.getAmMsuser().getMsBranch().getUuidBranch()) },
								{ "jobCode", svyJobCode } };
					}
					nextUser = (Object[]) this.getManagerDAO().selectOneNative(
							"branch".equals(mode) ? "services.submitorder.getNextUserByTaskLoadByBranch"
									: "services.submitorder.getNextUserByTaskLoad",
							paramsNext);

					if (null != nextUser && null == type) {
						flagNextUser = true;

						Object[][] param = { { "userId", next.getUuidMsUser() } };
						String queryString = " UPDATE MS_ROUNDROBINTASK SET FLAG_TASK = '0' "
								+ " WHERE UUID_MS_USER = :userId ";
						this.getManagerDAO().updateNativeString(queryString, param);

					} else {
						flagNextUser = false;
					}
				}
			}

			if (flagNextUser) {
				List userLead = this.getManagerDAO()
						.selectAllNative("branch".equals(mode) ? "services.submitorder.getLeadUserByBranch"
								: "services.submitorder.getLeadUser", params, null);
				Long firstUser = null;
				if (!userLead.isEmpty()) {
					for (int j = 0; j < userLead.size(); j++) {
						Map user = (Map) userLead.get(j);
						Long userId = Long.valueOf(user.get("d0").toString());
						String nextUserId = StringUtils.EMPTY;
						if (user.get("d1") != null) {
							nextUserId = user.get("d1").toString();
						}
						if (j == 0) {
							firstUser = userId;
						}

						if (Long.valueOf(nextUser[0].toString()) == userId && null == type) {
							// update
							Object[][] param = { { "userId", nextUser[0] } };
							String queryString = " UPDATE MS_ROUNDROBINTASK SET FLAG_TASK = '0' "
									+ " WHERE UUID_MS_USER = :userId ";
							this.getManagerDAO().updateNativeString(queryString, param);

							if (StringUtils.isBlank(nextUserId)) {
								Object[][] prm = { { "userId", firstUser } };
								queryString = "UPDATE MS_ROUNDROBINTASK SET FLAG_TASK = '1' "
										+ " WHERE UUID_MS_USER = :userId ";
								this.getManagerDAO().updateNativeString(queryString, prm);
							} else if (StringUtils.isNotBlank(nextUserId)) {
								Object[][] prm = { { "userId", nextUserId } };
								queryString = "UPDATE MS_ROUNDROBINTASK SET FLAG_TASK = '1' "
										+ " WHERE UUID_MS_USER = :userId ";
								this.getManagerDAO().updateNativeString(queryString, prm);
							}
							break;
						}
					}
					userAssign = nextUser[0].toString();
				} else {
					flagNextUser = false;
				}
			}

			if (flagNextUser) {
				AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(userAssign));

				if (null == type) {
					long uuidProcess = getUuidProcess(bean, subsystem);
					MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(bean, uuidProcess, subsystem, 0);
					bean.setAmMsuser(amMsuser);
					bean.setAssignDate(new Date());
					bean.setMsStatustask(msStatustaskSurvey);
					bean.setUsrUpd(auditContext.getCallerId());
					bean.setDtmUpd(new Date());
					bean.setMsStatusmobile(msm);

					this.getManagerDAO().update(bean);
					String notes = this.messageSource.getMessage("businesslogic.taskdistribution.assigntask",
							new Object[] { "Round Robin" }, this.retrieveLocaleAudit(auditContext));
					// insert into tr_taskHistory
					insertTaskHistory(auditContext, msStatustaskSurvey, bean, notes, GlobalVal.CODE_PROCESS_ASSIGNMENT,
							amMsuser.getFullName());

					result.put("taskH", bean);
					result.put("uuidProcess", uuidProcess);
				}

				result.put("userAssign", amMsuser);

			}
		}
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public Map<String, Object> distributeLowTask(List<TrTaskH> listTaskDist, AmMssubsystem subsystem, String uuidMh,
			String type, String mode, AuditContext auditContext) {
		Map<String, Object> result = new HashMap<>();
		String svyJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, auditContext);
		MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_NEW, auditContext);
		for (int i = 0; i < listTaskDist.size(); i++) {
			TrTaskH bean = listTaskDist.get(i);
			String uuidBranch = String.valueOf(bean.getMsBranch().getUuidBranch());
			AmMsuser userMh = null;

			String[][] params = { { "uuidBranch", uuidBranch }, { "uuidManager", uuidMh } };
			if ("branch".equals(mode)) {
				userMh = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(uuidMh));
				params = new String[][] { { "uuidBranch", String.valueOf(userMh.getMsBranch().getUuidBranch()) },
						{ "uuidManager", uuidMh }, { "jobCode", svyJobCode } };
			}
			Object[] userAssign = (Object[]) this.getManagerDAO()
					.selectOneNative("branch".equals(mode) ? "services.submitorder.getUserAssignLowTaskByBranch"
							: "services.submitorder.getUserAssignLowTask", params);

			if (userAssign != null) {
				AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
						Long.valueOf(String.valueOf(userAssign[0])));
				long uuidProcess = getUuidProcess(bean, subsystem);
				MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(bean, uuidProcess, subsystem, 0);
				bean.setAmMsuser(amMsuser);
				bean.setAssignDate(new Date());
				bean.setMsStatustask(msStatustaskSurvey);
				bean.setUsrUpd(auditContext.getCallerId());
				bean.setDtmUpd(new Date());
				bean.setMsStatusmobile(msm);

				if (null == type) {
					this.getManagerDAO().update(bean);

					this.getManagerDAO().update(bean);
					String notes = this.messageSource.getMessage("businesslogic.taskdistribution.assigntask",
							new Object[] { "Low Task" }, this.retrieveLocaleAudit(auditContext));
					// insert into tr_taskHistory
					insertTaskHistory(auditContext, msStatustaskSurvey, bean, notes, GlobalVal.CODE_PROCESS_ASSIGNMENT,
							amMsuser.getFullName());
				}

				result.put("userAssign", amMsuser);
				result.put("taskH", bean);
				result.put("uuidProcess", uuidProcess);
			}
		}
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public Map<String, Object> distributeZipCode(List<TrTaskH> listTaskDist, AmMssubsystem subsystem, String zipCode,
			String uuidMh, String type, String mode, AuditContext auditContext) {
		Map<String, Object> result = new HashMap<>();
		String svyJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, auditContext);
		MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_NEW, auditContext);
		for (int i = 0; i < listTaskDist.size(); i++) {
			TrTaskH bean = listTaskDist.get(i);
			String uuidBranch = String.valueOf(bean.getMsBranch().getUuidBranch());
			AmMsuser userMh = null;

			String[][] params = { { "uuidBranch", uuidBranch }, { "zipCode", zipCode }, { "uuidManager", uuidMh } };

			if ("branch".equals(mode)) {
				userMh = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(uuidMh));
				params = new String[][] { { "uuidBranch", String.valueOf(userMh.getMsBranch().getUuidBranch()) },
						{ "uuidManager", uuidMh }, { "zipCode", zipCode }, { "jobCode", svyJobCode } };
			}
			Object[] userAssign = (Object[]) this.getManagerDAO()
					.selectOneNative("branch".equals(mode) ? "services.submitorder.getUserAssignZipCodeByBranch"
							: "services.submitorder.getUserAssignZipCode", params);

			if (userAssign != null) {
				AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
						Long.valueOf(String.valueOf(userAssign[0])));
				long uuidProcess = getUuidProcess(bean, subsystem);
				MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(bean, uuidProcess, subsystem, 0);
				bean.setAmMsuser(amMsuser);
				bean.setAssignDate(new Date());
				bean.setMsStatustask(msStatustaskSurvey);
				bean.setUsrUpd(auditContext.getCallerId());
				bean.setDtmUpd(new Date());
				bean.setMsStatusmobile(msm);

				if (null == type) {
					this.getManagerDAO().update(bean);
					this.getManagerDAO().update(bean);
					String notes = this.messageSource.getMessage("businesslogic.taskdistribution.assigntask",
							new Object[] { "Zip Code" }, this.retrieveLocaleAudit(auditContext));
					// insert into tr_taskHistory
					insertTaskHistory(auditContext, msStatustaskSurvey, bean, notes, GlobalVal.CODE_PROCESS_ASSIGNMENT,
							amMsuser.getFullName());
				}

				result.put("userAssign", amMsuser);
				result.put("taskH", bean);
				result.put("uuidProcess", uuidProcess);
			}
		}
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public Map<String, Object> distributeToCMS(List<TrTaskH> listTaskDist, AmMssubsystem subsystem, AmMsuser user,
			AuditContext auditContext) {
		Map<String, Object> result = new HashMap<>();
		MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_NEW, auditContext);
		for (int i = 0; i < listTaskDist.size(); i++) {
			TrTaskH bean = listTaskDist.get(i);

			if (user != null) {
				long uuidProcess = getUuidProcess(bean, subsystem);
				MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(bean, uuidProcess, subsystem, 0);
				bean.setAmMsuser(user);
				bean.setAssignDate(new Date());
				bean.setMsStatustask(msStatustaskSurvey);
				bean.setUsrUpd(auditContext.getCallerId());
				bean.setDtmUpd(new Date());
				bean.setMsStatusmobile(msm);

				this.getManagerDAO().update(bean);
				this.getManagerDAO().update(bean);
				String notes = this.messageSource.getMessage("businesslogic.taskdistribution.assigntask",
						new Object[] { "Self Assignment" }, this.retrieveLocaleAudit(auditContext));
				// insert into tr_taskHistory
				insertTaskHistory(auditContext, msStatustaskSurvey, bean, notes, GlobalVal.CODE_PROCESS_ASSIGNMENT,
						user.getFullName());

				result.put("taskH", bean);
				result.put("uuidProcess", uuidProcess);
			} else {
				LOG.warn("TaskID '{}' has no user assigned!", bean.getTaskId());
			}
		}
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public Map<String, Object> distributeGeofencing(List<TrTaskH> listTaskDist, AmMssubsystem subsystem,
			String locationSurvey, String uuidMh, String type, String mode, AuditContext auditContext) {
		if (StringUtils.isEmpty(locationSurvey)) {
			return Collections.emptyMap();
		}
		Map<String, Object> result = new HashMap<>();
		String svyJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, auditContext);
		double latPoint = Double.valueOf(locationSurvey.split(";")[0]);
		double lngPoint = Double.valueOf(locationSurvey.split(";")[1]);
		AmMsuser userMh = null;

		MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_NEW, auditContext);

		for (int i = 0; i < listTaskDist.size(); i++) {
			TrTaskH bean = listTaskDist.get(i);
			String userAssign = null;

			String uuidBranch = String.valueOf(bean.getMsBranch().getUuidBranch());
			String[][] params = { { "uuidBranch", uuidBranch }, { "uuidManager", uuidMh } };
			List<Map<String, Object>> availableUser = this.getManagerDAO()
					.selectAllNative("services.submitorder.getAllUserAssignLowTask", params, null);
			if ("branch".equals(mode)) {
				userMh = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(uuidMh));
				params = new String[][] { { "uuidBranch", String.valueOf(userMh.getMsBranch().getUuidBranch()) },
						{ "uuidManager", uuidMh }, { "jobCode", svyJobCode } };
			}
			if ("branch".equals(mode)) {
				availableUser = this.getManagerDAO().selectAllNative("services.submitorder.getAllUserAssignLowTask",
						params, null);
			}
			for (int j = 0; j < availableUser.size(); j++) {
				Map<String, Object> userData = availableUser.get(j);
				boolean isAssignable = false;
				String areaType = String.valueOf(userData.get("d3"));

				if (areaType.equals(GlobalVal.AREA_POLY)) {
					Object[][] areaPathParam = {
							{ Restrictions.eq("msArea.uuidArea", Long.valueOf(String.valueOf(userData.get("d2")))) } };
					List<MsAreapath> listAreapath = (List<MsAreapath>) this.getManagerDAO()
							.selectAll(MsAreapath.class, areaPathParam, null).get(GlobalKey.MAP_RESULT_LIST);
					double[][] pathCoordinate = new double[listAreapath.size()][2];
					for (int k = 0; k < listAreapath.size(); k++) {
						pathCoordinate[k][0] = listAreapath.get(k).getLatitude().doubleValue();
						pathCoordinate[k][1] = listAreapath.get(k).getLongitude().doubleValue();
					}
					if (this.polyContain(latPoint, lngPoint, pathCoordinate)) {
						isAssignable = true;
					}

				} else if (areaType.equals(GlobalVal.AREA_CIRCLE)) {
					double centerLng = Double.valueOf(String.valueOf(userData.get("d4")));
					double centerLat = Double.valueOf(String.valueOf(userData.get("d5")));
					long radius = Long.valueOf(String.valueOf(userData.get("d6")));
					if (this.distanceBetween(latPoint, lngPoint, centerLng, centerLat) < radius) {
						isAssignable = true;
					}
				}
				if (isAssignable) {
					userAssign = String.valueOf(userData.get("d0"));
					break;
				}
			}
			if (userAssign != null) {
				AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(userAssign));
				long uuidProcess = getUuidProcess(bean, subsystem);
				MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(bean, uuidProcess, subsystem, 0);
				bean.setAmMsuser(amMsuser);
				bean.setAssignDate(new Date());
				bean.setMsStatustask(msStatustaskSurvey);
				bean.setUsrUpd(auditContext.getCallerId());
				bean.setDtmUpd(new Date());
				bean.setMsStatusmobile(msm);

				if (null == type) {
					this.getManagerDAO().update(bean);
					this.getManagerDAO().update(bean);
					String notes = this.messageSource.getMessage("businesslogic.taskdistribution.assigntask",
							new Object[] { "Geofencing" }, this.retrieveLocaleAudit(auditContext));
					// insert into tr_taskHistory
					insertTaskHistory(auditContext, msStatustaskSurvey, bean, notes, GlobalVal.CODE_PROCESS_ASSIGNMENT,
							amMsuser.getFullName());
				}

				result.put("userAssign", amMsuser);
				result.put("taskH", bean);
				result.put("uuidProcess", uuidProcess);
			}
		}
		return result;
	}

	private double distanceBetween(double lat1, double lng1, double lat2, double lng2) {
		// Haversine method in calculating distance between 2 points of a sphere
		double earthRadius = 6371.0; // kilometers (or 3958.75 miles)
		double dLat = Math.toRadians(lat2 - lat1);
		double dLng = Math.toRadians(lng2 - lng1);
		double sindLat = Math.sin(dLat / 2);
		double sindLng = Math.sin(dLng / 2);
		double a = Math.pow(sindLat, 2)
				+ Math.pow(sindLng, 2) * Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2));
		double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
		double result = earthRadius * c * 1000; // result in meters
		return result;
	}

	private boolean polyContain(double pointLat, double pointLng, double[][] vertices) {
		boolean result = false;
		int i, j;
		for (i = 0, j = vertices.length - 1; i < vertices.length; j = i++) {
			boolean t1 = (vertices[i][0] > pointLat) != (vertices[j][0] > pointLat);
			boolean t2 = (pointLng < (vertices[j][1] - vertices[i][1]) * (pointLat - vertices[i][0])
					/ (vertices[j][0] - vertices[i][0]) + vertices[i][1]);
			if (t1 && t2) {
				result = !result;
			}
		}
		return result;
	}

	@Transactional
	@Override
	public List<BigInteger> assignByKat(List<BigInteger> listUser, String zipCode, String formType,
			String currentUser, Long uuidProdCategory, String isPiloting, String formName, String jobCode, String isPilotingCae, AuditContext callerId) {
		String startToday = (new SimpleDateFormat("yyyy-MM-dd").format(new Date())) + " 00:00:00.000";
		String endToday = (new SimpleDateFormat("yyyy-MM-dd").format(new Date())) + " 23:59:59.997";
		AmMssubsystem amMssubsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		AmMsattribute amMsattribute = this.getManagerDAO().selectOne(AmMsattribute.class, new Object[][] {
			{ Restrictions.eq("atbCode", "LAST_LOCATION_TIMESTAMP") },
			{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", amMssubsystem.getUuidMsSubsystem()) } });
		
		List<BigInteger> copyListUser = new ArrayList<>(listUser);
		listUser.clear();
		int limit = 500;
		int countLoop = (int) Math.ceil(((double) copyListUser.size()) / limit);
		if (0 == countLoop) {
			countLoop = 1;
		}
		for (int i = 1; i < countLoop + 1; i++) {
			List <BigInteger> listUserTemp = new ArrayList();
			if (!copyListUser.isEmpty()) {
				int startIndex = (i - 1) * limit;
				int endIndex = 0;
				if (i != countLoop) {
					endIndex = ((i - 1) * limit + limit);
				} else {
					endIndex = copyListUser.size();
				}
				listUserTemp = copyListUser.subList(startIndex, endIndex);
			}
			Stack<Object[]> paramsStack = new Stack<>();
			StringBuilder queryBuilder = new StringBuilder().append("SELECT AMM.UUID_MS_USER ")
					.append("FROM MS_MAPPING_USER_KAT MMUK WITH (NOLOCK) ")
					.append("JOIN AM_MSUSER AMM WITH (NOLOCK) ON AMM.LOGIN_ID = MMUK.USERNAME ");
			
			//tambahan untuk CR POLO
			//CR CAE FASE 1 - penyesuaian untuk distribusi task by role
			if ((GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName) || "1".equalsIgnoreCase(isPilotingCae))&& StringUtils.isNotBlank(jobCode)) {
				if (jobCode.contains(",")) {
					List jobAssign = Arrays.asList(StringUtils.split(jobCode, ","));
					paramsStack.push(new Object[]{"jobCode", jobAssign});
					queryBuilder.append("JOIN MS_JOB MJ WITH(NOLOCK) ON MJ.UUID_JOB = AMM.UUID_JOB AND MJ.JOB_CODE IN ( :jobCode ) ");
				} else {
					paramsStack.push(new Object[]{"jobCode", jobCode});
					queryBuilder.append("JOIN MS_JOB MJ WITH(NOLOCK) ON MJ.UUID_JOB = AMM.UUID_JOB AND MJ.JOB_CODE = :jobCode ");
				}
			}
			
			queryBuilder.append("JOIN MS_BRANCH MSB WITH (NOLOCK) ON MSB.UUID_BRANCH = AMM.UUID_BRANCH ");
			if ("1".equalsIgnoreCase(isPilotingCae)) {
				queryBuilder.append(" AND MSB.IS_PILOTING_CAE = '1' ");
			} else if ("0".equalsIgnoreCase(isPilotingCae)) {
				queryBuilder.append(" AND ISNULL(MSB.IS_PILOTING_CAE, '0') = '0' ");
			}
			queryBuilder.append("JOIN AM_ATTRIBUTEOFMEMBER AMAOM WITH (NOLOCK) ON AMAOM.UUID_MS_USER = AMM.UUID_MS_USER ")
					.append("JOIN AM_MSATTRIBUTE AMMA WITH (NOLOCK) ON AMMA.UUID_MS_ATTRIBUTE = AMAOM.UUID_MS_ATTRIBUTE ")
					.append("WHERE MMUK.KAT_CODE = :zipCode ")
					.append("AND MMUK.IS_ACTIVE = 1 AND AMM.IS_ACTIVE = 1 ")
					.append("AND MMUK.TBL_PRODUCT_CATEGORY_ID = :uuidProdCategory ")
					.append("AND MSB.KONVEN_SYARIAH = :formType ");
			
			AmGeneralsetting gensetTaskAssignLogin = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_IS_TASK_ASSIGN_LOGIN, callerId);
			
			if (gensetTaskAssignLogin != null && gensetTaskAssignLogin.getGsValue().equals("1")) {
				LOG.info("Distribute KAT Check User Login");
				queryBuilder.append("AND (AMMA.UUID_MS_ATTRIBUTE = :uuidMsAttribute ")
				.append("AND CONVERT(DATETIME, AMAOM.ATTRIBUTE_VALUE, 103) BETWEEN :startDate AND :endDate) ");
				
				paramsStack.push(new Object[]{"uuidMsAttribute", amMsattribute.getUuidMsAttribute()});
				paramsStack.push(new Object[]{"startDate", startToday});
				paramsStack.push(new Object[]{"endDate", endToday});
			}
								
			if (!listUserTemp.isEmpty()) {
				paramsStack.push(new Object[]{"listUser", listUserTemp});
				queryBuilder.append("AND AMM.UUID_MS_USER IN (:listUser) ");
			}
			
			if (StringUtils.isNotBlank(currentUser)) {
				paramsStack.push(new Object[]{"currentUser", currentUser});
				queryBuilder.append("AND AMM.UUID_MS_USER != :currentUser ");
			}
			
			if (StringUtils.isNotBlank(isPiloting)) {
				paramsStack.push(new Object[]{"isPiloting", isPiloting});
				queryBuilder.append("AND ISNULL(MSB.IS_PILOTING, '0') = :isPiloting ");
			}
			
			paramsStack.push(new Object[]{"zipCode", zipCode});
			paramsStack.push(new Object[]{"formType", formType});
			paramsStack.push(new Object[]{"uuidProdCategory", uuidProdCategory});
			
			Object[][] paramAssign = new Object[paramsStack.size()][2];
		    for (int j = 0; j < paramsStack.size(); j++) {
				Object[] objects = paramsStack.get(j);
				paramAssign[j] = objects;
			}
	
			List<Map<String, Object>> result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(),
					paramAssign);
			for (Map resultMap : result) {
				BigInteger uuidMsUser = (BigInteger) resultMap.get("d0");
				listUser.add(uuidMsUser);
			}
		}
		LOG.info("List User Distribute KAT {}, Product Category {} : {}", zipCode, uuidProdCategory, listUser);
		return listUser;
	}

	@Transactional
	@Override
	public List<BigInteger> assignByDealer(List<BigInteger> listUser, String dealerCode, String mode, String formType,
			String currentUser, String isPiloting, String formName, String jobCode, String isPilotingCae, AuditContext callerId) {
		String startToday = (new SimpleDateFormat("yyyy-MM-dd").format(new Date())) + " 00:00:00.000";
		String endToday = (new SimpleDateFormat("yyyy-MM-dd").format(new Date())) + " 23:59:59.997";
		AmMssubsystem amMssubsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		AmMsattribute amMsattribute = this.getManagerDAO().selectOne(AmMsattribute.class, new Object[][] {
			{ Restrictions.eq("atbCode", "LAST_LOCATION_TIMESTAMP") },
			{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", amMssubsystem.getUuidMsSubsystem()) } });
		
		List<BigInteger> copyListUser = new ArrayList<>(listUser);
		listUser.clear();
		int limit = 500;
		int countLoop = (int) Math.ceil(((double) copyListUser.size()) / limit);
		if (0 == countLoop) {
			countLoop = 1;
		}
		for (int i = 1; i < countLoop + 1; i++) {
			List <BigInteger> listUserTemp = new ArrayList();
			if (!copyListUser.isEmpty()) {
				int startIndex = (i - 1) * limit;
				int endIndex = 0;
				if (i != countLoop) {
					endIndex = ((i - 1) * limit + limit);
				} else {
					endIndex = copyListUser.size();
				}
				listUserTemp = copyListUser.subList(startIndex, endIndex);
			}
			
			Stack<Object[]> paramsStack = new Stack<>();
			StringBuilder queryBuilder = new StringBuilder().append("SELECT AMM.UUID_MS_USER ")
					.append("FROM MS_MAPPING_USER_DEALER MMUK WITH (NOLOCK) ")
					.append("JOIN AM_MSUSER AMM WITH (NOLOCK) ON AMM.LOGIN_ID = MMUK.USERNAME ");
			
			//tambahan untuk CR POLO
			//CR CAE FASE 1 - penyesuaian untuk distribusi task by role
			if ((GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName) || "1".equalsIgnoreCase(isPilotingCae)) && StringUtils.isNotBlank(jobCode)) {
				if (jobCode.contains(",")) {
					List jobAssign = Arrays.asList(StringUtils.split(jobCode, ","));
					paramsStack.push(new Object[]{"jobCode", jobAssign});
					queryBuilder.append("JOIN MS_JOB MJ WITH(NOLOCK) ON MJ.UUID_JOB = AMM.UUID_JOB AND MJ.JOB_CODE IN ( :jobCode ) ");
				} else {
					paramsStack.push(new Object[]{"jobCode", jobCode});
					queryBuilder.append("JOIN MS_JOB MJ WITH(NOLOCK) ON MJ.UUID_JOB = AMM.UUID_JOB AND MJ.JOB_CODE = :jobCode ");
				}
			}
			
			queryBuilder.append("JOIN MS_BRANCH MSB WITH (NOLOCK) ON MSB.UUID_BRANCH = AMM.UUID_BRANCH ");
			if ("1".equalsIgnoreCase(isPilotingCae)) {
				queryBuilder.append(" AND MSB.IS_PILOTING_CAE = '1' ");
			} else if ("0".equalsIgnoreCase(isPilotingCae)) {
				queryBuilder.append(" AND ISNULL(MSB.IS_PILOTING_CAE, '0') = '0' ");
			}
			queryBuilder.append("JOIN AM_ATTRIBUTEOFMEMBER AMAOM WITH (NOLOCK) ON AMAOM.UUID_MS_USER = AMM.UUID_MS_USER ")
					.append("JOIN AM_MSATTRIBUTE AMMA WITH (NOLOCK) ON AMMA.UUID_MS_ATTRIBUTE = AMAOM.UUID_MS_ATTRIBUTE ")
					.append("WHERE MMUK.DEALER_CODE = :dealerCode ").append("AND MMUK.IS_ACTIVE = 1 AND AMM.IS_ACTIVE = 1 ")
					.append("AND MMUK.DEALER_TYPE = :dealerType ").append("AND MSB.KONVEN_SYARIAH = :formType ");
			
			AmGeneralsetting gensetTaskAssignLogin = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_IS_TASK_ASSIGN_LOGIN, callerId);
			
			if (gensetTaskAssignLogin != null && gensetTaskAssignLogin.getGsValue().equals("1")) {
				LOG.info("Distribute Dealer Check User Login");
				queryBuilder.append("AND (AMMA.UUID_MS_ATTRIBUTE = :uuidMsAttribute AND ")
				.append("CONVERT(DATETIME, AMAOM.ATTRIBUTE_VALUE, 103) BETWEEN :startDate AND :endDate) ");
				
				paramsStack.push(new Object[]{"uuidMsAttribute", amMsattribute.getUuidMsAttribute()});
				paramsStack.push(new Object[]{"startDate", startToday});
				paramsStack.push(new Object[]{"endDate", endToday});
			}
			
			if (!listUserTemp.isEmpty()) {
				paramsStack.push(new Object[]{"listUser", listUserTemp});
				queryBuilder.append("AND AMM.UUID_MS_USER IN (:listUser) ");
			}
			
			if (StringUtils.isNotBlank(currentUser)) {
				paramsStack.push(new Object[]{"currentUser", currentUser});
				queryBuilder.append("AND AMM.UUID_MS_USER != :currentUser ");
			}
			
			if (StringUtils.isNotBlank(isPiloting)) {
				paramsStack.push(new Object[]{"isPiloting", isPiloting});
				queryBuilder.append("AND ISNULL(MSB.IS_PILOTING, '0') = :isPiloting ");
			}
			
			paramsStack.push(new Object[]{"dealerCode", dealerCode});
			paramsStack.push(new Object[]{"dealerType", mode});
			paramsStack.push(new Object[]{"formType", formType});
			
			Object[][] paramAssign = new Object[paramsStack.size()][2];
		    for (int j = 0; j < paramsStack.size(); j++) {
				Object[] objects = paramsStack.get(j);
				paramAssign[j] = objects;
			}

			List<Map<String, Object>> result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(),
					paramAssign);
			for (Map resultMap : result) {
				BigInteger uuidMsUser = (BigInteger) resultMap.get("d0");
				listUser.add(uuidMsUser);
			}
		}
		
		LOG.info("List User Distribute Dealer {} : {}", dealerCode, listUser);
		return listUser;
	}

	@Transactional
	@Override
	public List<BigInteger> assignByLoad(List<BigInteger> listUser, String formType, String currentUser,
			String isPiloting, String formName, String jobCode, String isPilotingCae, AuditContext callerId) {
		List<BigInteger> copyListUser = new ArrayList<>(listUser);
		listUser.clear();
		int limit = 500;
		int countLoop = (int) Math.ceil(((double) copyListUser.size()) / limit);
		if (0 == countLoop) {
			countLoop = 1;
		}
		for (int i = 1; i < countLoop + 1; i++) {
			List <BigInteger> listUserTemp = new ArrayList();
			if (!copyListUser.isEmpty()) {
				int startIndex = (i - 1) * limit;
				int endIndex = 0;
				if (i != countLoop) {
					endIndex = ((i - 1) * limit + limit);
				} else {
					endIndex = copyListUser.size();
				}
				listUserTemp = copyListUser.subList(startIndex, endIndex);
			}
			Stack<Object[]> paramsStack = new Stack<>();
			StringBuilder paramsQueryString = new StringBuilder();
			StringBuilder paramsQueryString2 = new StringBuilder();
			StringBuilder joinQueryString = new StringBuilder();
			StringBuilder joinQueryString2 = new StringBuilder();
			String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
			
			AmMssubsystem mssubsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
			MsStatustask statusTaskPending = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION, mssubsystem.getUuidMsSubsystem(), callerId);
			
			if (!listUserTemp.isEmpty()) {
				paramsStack.push(new Object[]{"listUser", listUserTemp});
				paramsQueryString.append("AND AMMU.UUID_MS_USER IN (:listUser) ");
				paramsQueryString2.append("AND AMM.UUID_MS_USER IN (:listUser) ");
			}
			if (StringUtils.isNotBlank(currentUser)) {
				paramsStack.push(new Object[]{"currentUser", currentUser});
				paramsQueryString.append("AND AMMU.UUID_MS_USER != :currentUser ");
				paramsQueryString2.append("AND AMM.UUID_MS_USER != :currentUser ");
			}
			if (StringUtils.isNotBlank(isPiloting)) {
				paramsStack.push(new Object[]{"isPiloting", isPiloting});
				paramsQueryString.append("AND ISNULL(MSB.IS_PILOTING, '0') = :isPiloting ");
				paramsQueryString2.append("AND ISNULL(MSBR.IS_PILOTING, '0') = :isPiloting ");
				joinQueryString.append("JOIN MS_BRANCH MSB WITH (NOLOCK) ON AMMU.UUID_BRANCH = MSB.UUID_BRANCH ");
				joinQueryString2.append("JOIN MS_BRANCH MSBR WITH (NOLOCK) ON AMM.UUID_BRANCH = MSBR.UUID_BRANCH ");
			}
			
			if (StringUtils.isNotBlank(isPilotingCae)) {
				if(!joinQueryString.toString().toUpperCase().contains("JOIN MS_BRANCH MSB")) {
					joinQueryString.append(" JOIN MS_BRANCH MSB WITH (NOLOCK) ON AMMU.UUID_BRANCH = MSB.UUID_BRANCH ");
					joinQueryString2.append("JOIN MS_BRANCH MSBR WITH (NOLOCK) ON AMM.UUID_BRANCH = MSBR.UUID_BRANCH ");
				} 
				if ("1".equalsIgnoreCase(isPilotingCae)) {
					paramsQueryString.append(" AND MSB.IS_PILOTING_CAE = '1' ");
					paramsQueryString2.append(" AND MSBR.IS_PILOTING_CAE = '1' ");
				} else if ("0".equalsIgnoreCase(isPilotingCae)) {
					paramsQueryString.append(" AND ISNULL(MSB.IS_PILOTING_CAE, '0') = '0' ");
					paramsQueryString2.append(" AND ISNULL(MSBR.IS_PILOTING_CAE, '0') = '0' ");
				}
					
			}
			

			//tambahan untuk CR POLO
			if ((GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName) || "1".equalsIgnoreCase(isPilotingCae)) && StringUtils.isNotBlank(jobCode)) {
				if (jobCode.contains(",")) {
					List jobAssign = Arrays.asList(StringUtils.split(jobCode, ","));
					paramsQueryString.append("AND MSJO.JOB_CODE in ( :jobCode )");
					paramsQueryString2.append("AND MSJ.JOB_CODE in ( :jobCode )");
					paramsStack.push(new Object[]{"jobCode", jobAssign});
				} else {
					paramsQueryString.append("AND MSJO.JOB_CODE = :jobCode ");
					paramsQueryString2.append("AND MSJ.JOB_CODE = :jobCode ");
					paramsStack.push(new Object[]{"jobCode", jobCode});
				}
			}
			
			StringBuilder queryBuilder = new StringBuilder()
					.append("SELECT AMMU.UNIQUE_ID, COUNT(TRTH.UUID_TASK_H) AS MIN_TASK ")
					.append("FROM AM_MSUSER AMMU WITH (NOLOCK) ")
					.append("JOIN MS_JOB MSJO WITH (NOLOCK) ON MSJO.UUID_JOB = AMMU.UUID_JOB ")
					.append("LEFT JOIN TR_TASK_H TRTH WITH (NOLOCK) ON TRTH.UUID_MS_USER = AMMU.UUID_MS_USER ")
					.append("AND (TRTH.ASSIGN_DATE BETWEEN :startDate AND :endDate OR TRTH.UUID_STATUS_TASK = :uuidStatusTask) ")
					.append(joinQueryString)
					.append("WHERE AMMU.IS_ACTIVE='1' ")
					.append(paramsQueryString)
					.append("GROUP BY AMMU.UNIQUE_ID HAVING COUNT(TRTH.UUID_TASK_H) = ( ")
					.append("SELECT TOP(1) COUNT(UUID_TASK_H) AS TASK_COUNT ")
					.append("FROM AM_MSUSER AMM WITH (NOLOCK) ")
					.append("JOIN MS_JOB MSJ WITH (NOLOCK) ON MSJ.UUID_JOB = AMM.UUID_JOB ")
					.append("LEFT JOIN TR_TASK_H TRT WITH (NOLOCK) ON TRT.UUID_MS_USER = AMM.UUID_MS_USER ")
					.append("AND (TRT.ASSIGN_DATE BETWEEN :startDate AND :endDate OR TRT.UUID_STATUS_TASK = :uuidStatusTask) ")
					.append(joinQueryString2)
					.append("WHERE AMM.IS_ACTIVE='1' ")
					.append(paramsQueryString2)
					.append("GROUP BY AMM.UNIQUE_ID ORDER BY TASK_COUNT ASC)");
			
			paramsStack.push(new Object[]{"startDate", currentDate + " 00:00:00.000"});
			paramsStack.push(new Object[]{"endDate", currentDate + " 23:59:59.997"});
			paramsStack.push(new Object[]{"uuidStatusTask", statusTaskPending.getUuidStatusTask()});
			Object[][] paramAssign = new Object[paramsStack.size()][2];
		    for (int j = 0; j < paramsStack.size(); j++) {
				Object[] objects = paramsStack.get(j);
				paramAssign[j] = objects;
			}
			List<Map<String, Object>> result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(),
					paramAssign);
			
			if (!result.isEmpty()) {
				String startToday = (new SimpleDateFormat("yyyy-MM-dd").format(new Date())) + " 00:00:00.000";
				String endToday = (new SimpleDateFormat("yyyy-MM-dd").format(new Date())) + " 23:59:59.997";
				AmMssubsystem amMssubsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
				AmMsattribute amMsattribute = this.getManagerDAO().selectOne(AmMsattribute.class, new Object[][] {
					{ Restrictions.eq("atbCode", "LAST_LOCATION_TIMESTAMP") },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", amMssubsystem.getUuidMsSubsystem()) } });
				if (0 != listUserTemp.size()) {
					for (Map resultMap : result) {
						List listUserFromUnique = null;
						
						AmGeneralsetting gensetTaskAssignLogin = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_IS_TASK_ASSIGN_LOGIN, callerId);
						
						if (gensetTaskAssignLogin != null && gensetTaskAssignLogin.getGsValue().equals("1")) {
							LOG.info("Distribute Load Check User Login");
							Object[][] param = { 
									{ "uniqueId", String.valueOf(resultMap.get("d0")) }, { "listUser", listUserTemp },
									{ "uuidMsAttribute", amMsattribute.getUuidMsAttribute() },
									{ "startDate", startToday }, { "endDate", endToday } };
							
							listUserFromUnique =  this.getManagerDAO().selectAllNativeString("SELECT AMMU.UUID_MS_USER "
									+ " FROM AM_MSUSER AMMU WITH (NOLOCK) "
									+ " JOIN AM_ATTRIBUTEOFMEMBER AMAOM WITH (NOLOCK) ON AMAOM.UUID_MS_USER = AMMU.UUID_MS_USER "
									+ " JOIN AM_MSATTRIBUTE AMMA WITH (NOLOCK) ON AMMA.UUID_MS_ATTRIBUTE = AMAOM.UUID_MS_ATTRIBUTE "
									+ " WHERE AMMU.UNIQUE_ID = :uniqueId AND AMMU.UUID_MS_USER IN (:listUser)"
									+ " AND (AMMA.UUID_MS_ATTRIBUTE = :uuidMsAttribute AND "
									+ " CONVERT(DATETIME, AMAOM.ATTRIBUTE_VALUE, 103) BETWEEN :startDate AND :endDate) ", param);
						} else {
							Object[][] param = { 
									{ "uniqueId", String.valueOf(resultMap.get("d0")) }, { "listUser", listUserTemp }};
							
							listUserFromUnique =  this.getManagerDAO().selectAllNativeString("SELECT AMMU.UUID_MS_USER "
									+ " FROM AM_MSUSER AMMU WITH (NOLOCK) "
									+ " JOIN AM_ATTRIBUTEOFMEMBER AMAOM WITH (NOLOCK) ON AMAOM.UUID_MS_USER = AMMU.UUID_MS_USER "
									+ " JOIN AM_MSATTRIBUTE AMMA WITH (NOLOCK) ON AMMA.UUID_MS_ATTRIBUTE = AMAOM.UUID_MS_ATTRIBUTE "
									+ " WHERE AMMU.UNIQUE_ID = :uniqueId AND AMMU.UUID_MS_USER IN (:listUser)", param);
						}
						
						for (int k = 0; k < listUserFromUnique.size(); k++) {
							Map mapListUserFromUnique = (Map) listUserFromUnique.get(k);
							listUser.add((BigInteger) mapListUserFromUnique.get("d0"));
						}
					}
				} else {
					for (Map resultMap : result) {
						List listUserFromUnique = null;
						
						AmGeneralsetting gensetTaskAssignLogin = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_IS_TASK_ASSIGN_LOGIN, callerId);
						
						if (gensetTaskAssignLogin != null && gensetTaskAssignLogin.getGsValue().equals("1")) {
							LOG.info("Distribute Load Check User Login");
							Object[][] param = { { "uniqueId", String.valueOf(resultMap.get("d0")) }, {"branchType", formType},
									{ "uuidMsAttribute", amMsattribute.getUuidMsAttribute() },
									{ "startDate", startToday }, { "endDate", endToday } };
							
							StringBuilder query = new StringBuilder();
							query.append(" SELECT AMMU.UUID_MS_USER ");
							query.append(" FROM AM_MSUSER AMMU WITH (NOLOCK) ");
							query.append(" JOIN MS_BRANCH MSB WITH (NOLOCK) ON MSB.UUID_BRANCH = AMMU.UUID_BRANCH ");
							if ("1".equalsIgnoreCase(isPilotingCae)) {
								query.append(" AND MSB.IS_PILOTING_CAE = '1' ");
							} else if ("0".equalsIgnoreCase(isPilotingCae)) {
								query.append(" AND ISNULL(MSB.IS_PILOTING_CAE, '0') = '0' ");
							}
							query.append(" JOIN AM_ATTRIBUTEOFMEMBER AMAOM WITH (NOLOCK) ON AMAOM.UUID_MS_USER = AMMU.UUID_MS_USER ");
							query.append(" JOIN AM_MSATTRIBUTE AMMA WITH (NOLOCK) ON AMMA.UUID_MS_ATTRIBUTE = AMAOM.UUID_MS_ATTRIBUTE ");
							query.append(" WHERE AMMU.UNIQUE_ID = :uniqueId AND MSB.KONVEN_SYARIAH = :branchType ");
							query.append(" AND (AMMA.UUID_MS_ATTRIBUTE = :uuidMsAttribute AND ");
							query.append(" CONVERT(DATETIME, AMAOM.ATTRIBUTE_VALUE, 103) BETWEEN :startDate AND :endDate) ");
							
							listUserFromUnique =  this.getManagerDAO().selectAllNativeString(query.toString(), param);
						} else {
							Object[][] param = { { "uniqueId", String.valueOf(resultMap.get("d0")) }, {"branchType", formType}};
							
							StringBuilder query = new StringBuilder();
							query.append(" SELECT AMMU.UUID_MS_USER ");
							query.append(" FROM AM_MSUSER AMMU WITH (NOLOCK) ");
							query.append(" JOIN MS_BRANCH MSB WITH (NOLOCK) ON MSB.UUID_BRANCH = AMMU.UUID_BRANCH ");
							if ("1".equalsIgnoreCase(isPilotingCae)) {
								query.append(" AND MSB.IS_PILOTING_CAE = '1' ");
							} else if ("0".equalsIgnoreCase(isPilotingCae)) {
								query.append(" AND ISNULL(MSB.IS_PILOTING_CAE, '0') = '0' ");
							}
							query.append(" JOIN AM_ATTRIBUTEOFMEMBER AMAOM WITH (NOLOCK) ON AMAOM.UUID_MS_USER = AMMU.UUID_MS_USER ");
							query.append(" JOIN AM_MSATTRIBUTE AMMA WITH (NOLOCK) ON AMMA.UUID_MS_ATTRIBUTE = AMAOM.UUID_MS_ATTRIBUTE ");
							query.append(" WHERE AMMU.UNIQUE_ID = :uniqueId AND MSB.KONVEN_SYARIAH = :branchType ");
							listUserFromUnique =  this.getManagerDAO().selectAllNativeString(query.toString(), param);
						}
						
						for (int k = 0; k < listUserFromUnique.size(); k++) {
							BigInteger uuidMsUser = (BigInteger) ((Map) listUserFromUnique.get(k)).get("d0");
							listUser.add(uuidMsUser);
						}
					}
				}
			}
		}
		LOG.info("List User Distribute Load {} : {}", copyListUser, listUser);
		return listUser;
	}

	@Transactional
    @Override
	public Map poolingTaskDistribution(Long uuidProdCategory, String nik, String zipCode, String dealerCode, String formType,
			String currentUser, String isPiloting, String formName, String jobCode, String isPilotingCae, AuditContext callerId) {
		Map result = new HashMap<>();
		List <BigInteger> listUser = new ArrayList();
		StringBuilder errorEmpty = new StringBuilder();

		if (StringUtils.isNotBlank(nik) && ("1".equalsIgnoreCase(isPilotingCae) || GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName))) {
			Object [][] paramTask = null;
			
			StringBuilder query = new StringBuilder();
			query.append(" SELECT    TOP(1) amu2.UUID_MS_USER ");
			query.append(" FROM      TR_TASK_H trth WITH (NOLOCK) "); 
			query.append(" JOIN      MS_STATUSTASK ms WITH (NOLOCK) ON ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK AND (ms.STATUS_CODE = 'N' OR ms.STATUS_CODE = 'WP') "); 
			query.append(" JOIN      AM_MSUSER amu WITH(NOLOCK) ON amu.UUID_MS_USER = trth.UUID_MS_USER "); 
			query.append(" JOIN 	 AM_MSUSER amu2 WITH(NOLOCK) ON amu2.UNIQUE_ID = amu.UNIQUE_ID AND amu2.IS_ACTIVE = '1' ");

			if (StringUtils.isNotBlank(jobCode)) {
				//CR CAE FASE 1 - penyesuaian untuk distribusi task by role
				if (jobCode.contains(",")) {
					List jobAssign = Arrays.asList(StringUtils.split(jobCode, ","));
					paramTask = new Object[][]{ {"nik", nik}, {"formName", GlobalVal.FORM_OTS}, {"tblProdCategoryId", String.valueOf(uuidProdCategory)} ,{"jobCode", jobAssign}};
					query.append("JOIN MS_JOB MJ WITH(NOLOCK) ON MJ.UUID_JOB = amu2.UUID_JOB AND MJ.JOB_CODE IN ( :jobCode ) ");
				} else {
					paramTask = new Object[][]{ {"nik", nik}, {"formName", GlobalVal.FORM_OTS}, {"tblProdCategoryId", String.valueOf(uuidProdCategory)} ,{"jobCode", jobCode}};
					query.append("JOIN MS_JOB MJ WITH(NOLOCK) ON MJ.UUID_JOB = amu2.UUID_JOB AND MJ.JOB_CODE = :jobCode ");
				}
			}
			
			query.append(" JOIN      MS_BRANCH mb WITH(NOLOCK) ON mb.UUID_BRANCH = amu2.UUID_BRANCH "); 
			
			if ("1".equalsIgnoreCase(isPilotingCae)) {
				query.append(" 			 AND mb.IS_PILOTING_CAE = '1' ");
			}
			
			query.append(" JOIN      MS_FORM mf WITH (NOLOCK) ON mf.UUID_FORM = trth.UUID_FORM ");
			query.append(" JOIN      MS_MAPPING_USER_KAT MUK WITH (NOLOCK) ON MUK.USERNAME = amu2.LOGIN_ID AND MUK.TBL_PRODUCT_CATEGORY_ID = :tblProdCategoryId AND MUK.IS_ACTIVE = '1'");
			query.append(" WHERE     trth.NIK = :nik ");
			query.append("           AND mf.FORM_NAME != :formName ");
			query.append(" ORDER BY  trth.ASSIGN_DATE DESC ");
			
			Object userByNik = this.getManagerDAO().selectOneNativeString(query.toString(), paramTask);
		
			if (null != userByNik) { 
				listUser.add((BigInteger) userByNik); 
			} 
		}
		
		if (null == listUser || listUser.isEmpty()) {
			if (GlobalVal.JENIS_PEMBIAYAAN_KONVENSIONAL_CODE.equalsIgnoreCase(formType)) {
				formType = GlobalVal.JENIS_PEMBIAYAAN_KONVENSIONAL;
			} else {
				formType = GlobalVal.JENIS_PEMBIAYAAN_SYARIAH;
			}
			Object[][] paramAssign = { { "tblProductCategoryId", uuidProdCategory } };
			List listModeAssign =  this.getManagerDAO().selectAllNativeString("SELECT TTAM.ASSIGN_CODE " + 
					"FROM TBL_MAP_PROD_ASSIGN TMPA WITH (NOLOCK) " + 
					"JOIN TBL_TASK_ASSIGN_MODE TTAM WITH (NOLOCK) ON TTAM.TBL_TASK_ASSIGN_MODE_ID = TMPA.TBL_TASK_ASSIGN_MODE_ID " + 
					"WHERE TMPA.TBL_PRODUCT_CATEGORY_ID = :tblProductCategoryId " +
					"ORDER BY TMPA.SEQ_ORDER ASC", paramAssign);
			
			for (int k = 0; k < listModeAssign.size(); k++) {
				String mode = String.valueOf(((Map) listModeAssign.get(k)).get("d0"));
				if (GlobalVal.MAPPING_KAT.equals(mode)) {
					if (StringUtils.isNotBlank(zipCode)) {
						listUser = this.assignByKat(listUser, zipCode, formType, currentUser, uuidProdCategory, isPiloting, formName, jobCode, isPilotingCae, callerId);
					} else {
						if (!("").equals(errorEmpty.toString())) {
							errorEmpty.append(" | ");
						}
						errorEmpty.append("KAT Code value cannot be empty");
					}
				} else if (GlobalVal.MAPPING_DEALER_TYPE_FOCUS.equals(mode) || GlobalVal.MAPPING_DEALER_TYPE_NON_FOCUS.equals(mode)) {
					if (StringUtils.isNotBlank(dealerCode)) {
						listUser = this.assignByDealer(listUser, dealerCode, mode, formType, currentUser, isPiloting, formName, jobCode, isPilotingCae, callerId);
					} else {
						if (!("").equals(errorEmpty.toString())) {
							errorEmpty.append(" | ");
						}
						errorEmpty.append("Dealer Code value cannot be empty");
					}
				} else if (GlobalVal.MAPPING_LOAD.equals(mode)) {
					listUser = this.assignByLoad(listUser, formType, currentUser, isPiloting, formName, jobCode, isPilotingCae, callerId);
				}
				if (listUser.isEmpty()) {
//					errorEmpty.append("Assigned User Not Found");
					break;
				}
			}
		}
		
		if (StringUtils.isNotBlank(String.valueOf(errorEmpty))) {
			result.put("errMsg", errorEmpty);
		}
		result.put("listUser", listUser); 
		return result;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public void doAlternate1(Map bean, AuditContext callerId) throws ParseException {
		AmGeneralsetting amGeneralSetting = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_DURATION_ALT_1, callerId);
		
		String formName = bean.get("d10") != null ? bean.get("d10").toString() : "";
		String gsVal = amGeneralSetting.getGsValue();
		
		if (GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName)) {
			AmGeneralsetting amGeneralSettingVisit = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_DURATION_ALT_VISIT_1, callerId);
			if(null != amGeneralSettingVisit && StringUtils.isNotBlank(amGeneralSettingVisit.getGsValue())) {
				gsVal = amGeneralSettingVisit.getGsValue();
			}
		}
		
		Integer minutes = (Integer) bean.get("d2");
		if (minutes > Integer.valueOf(gsVal)) {
			Long uuidProdCategory = (Long) (Long.valueOf(bean.get("d1").toString()) != null ? Long.valueOf(bean.get("d1").toString()) : "");
			String subZipCode = bean.get("d4") != null ? bean.get("d4").toString() : "%";
			String dealerCode = bean.get("d5") != null ? bean.get("d5").toString() : "%";
			String formType = bean.get("d6") != null ? bean.get("d6").toString() : "%";
			String currentUser = bean.get("d7") != null ? bean.get("d7").toString() : "";
			String isPiloting = bean.get("d9") != null ? bean.get("d9").toString() : "0";
			String jobCode = "";
			String nik = bean.get("d11") != null ? bean.get("d11").toString() : "";
			
			String uuidTask = bean.get("d0").toString();
			String isPilotingCae = (String) this.getManagerDAO().selectOneNativeString("SELECT IS_PILOTING_CAE FROM TR_TASK_H WITH(NOLOCK) WHERE UUID_TASK_H = :uuidTaskH ", new Object[][] {{"uuidTaskH", uuidTask}}); 
			
			if (StringUtils.isNotBlank(nik) && "1".equalsIgnoreCase(isPilotingCae)) {
				if (!isAllowToAlternate(nik, currentUser)) {
					return;
				}
			} 
			
			if (GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName)) {
				Object[][] paramJob = {{"uuidMsUser", currentUser}};
				StringBuilder queryJob = new StringBuilder(" SELECT JOB_CODE ")
						.append(" FROM AM_MSUSER amu with(NOLOCK) ")
						.append(" JOIN MS_JOB mj with(NOLOCK) ON mj.UUID_JOB = amu.UUID_JOB ")
						.append(" WHERE amu.UUID_MS_USER = :uuidMsUser ");
				jobCode = (String) this.getManagerDAO().selectOneNativeString(queryJob.toString(), paramJob);
			} else if ("1".equalsIgnoreCase(isPilotingCae)) {
				jobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_ROLE_TASK_CAE, callerId);
			}
			
			Map resultTaskDistribution = this.poolingTaskDistribution(uuidProdCategory, null, subZipCode, dealerCode, formType, currentUser, isPiloting, formName, jobCode, isPilotingCae, callerId);
			List<BigInteger> listAllUser = (List<BigInteger>) resultTaskDistribution.get("listUser");
			AmMsuser userCMO = null;
			if (!listAllUser.isEmpty()) {
				Random rand = new Random();
				Long uuidCMO = listAllUser.get(rand.nextInt(listAllUser.size())).longValue();
				userCMO = this.getManagerDAO().selectOne(AmMsuser.class, new Object[][] {
						{ Restrictions.eq("uuidMsUser", uuidCMO) } });
				this.getManagerDAO().fetch(userCMO.getMsBranch());
			}
					
			if ("1".equals(isPiloting)) {
				String applNo = bean.get("d8") != null ? bean.get("d8").toString() : "%";
				Object[][] paramPiloting = { {"applNo", applNo} };
				List pilotingTask = this.getManagerDAO().selectAllNativeString(
						"SELECT UUID_TASK_H " +
						"FROM   TR_TASK_H TTH WITH (NOLOCK) " +
						"JOIN   MS_STATUSTASK MST WITH(NOLOCK) ON MST.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK " +
						"WHERE  APPL_NO = :applNo " +
						"       AND (MST.STATUS_CODE = 'N' OR MST.STATUS_CODE = 'WP') " +
						"       AND TTH.IS_ALTERNATE1 = '0'", paramPiloting);
				if (null == pilotingTask || pilotingTask.isEmpty()) {
					String uuidTaskH = bean.get("d0").toString();
					pilotingTask = new ArrayList<>();
					Map map = new HashMap<>();
					map.put("d0", uuidTaskH);
					pilotingTask.add(map);
				}
				for (int k = 0; k < pilotingTask.size(); k++) {
					Long uuidTaskH = Long.valueOf((((Map) pilotingTask.get(k)).get("d0")).toString());
					TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] { { Restrictions.eq("uuidTaskH", uuidTaskH) } });
					
					this.getManagerDAO().fetch(trTaskH);
					this.getManagerDAO().fetch(trTaskH.getMsStatustask());
					this.getManagerDAO().fetch(trTaskH.getAmMsuser());
					
					//skip alternate for task survey from task visit
					if( !GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName) && StringUtils.isNotBlank(trTaskH.getTaskIdPolo()) ) {
						if(isSurveyFromVisit(String.valueOf(trTaskH.getUuidTaskH()))) {
							continue;
						}
					}
					//end alternate for task survey from task visit
						
					Object[][] param = { {"uuidTaskH", trTaskH.getUuidTaskH()} };
					String fullName =  (String) this.getManagerDAO().selectOneNativeString(
							"SELECT AMU.FULL_NAME FROM TR_TASK_H TTH WITH (NOLOCK)"
							+ "JOIN AM_MSUSER AMU WITH (NOLOCK) ON TTH.UUID_MS_USER = AMU.UUID_MS_USER WHERE UUID_TASK_H = :uuidTaskH", param );
						
					if (null != userCMO) {
						trTaskH.setAmMsuser(userCMO);
						trTaskH.setMsBranch(userCMO.getMsBranch());
						fullName = userCMO.getFullName();
						
						//start set default answer untuk referantor task survey
						if ("1".equals(trTaskH.getIsPilotingCae()) && StringUtils.isNotBlank(trTaskH.getTaskIdPolo())) {
							GetReferantorResponse referantorDetail = intFormLogic.getReferantorCodeFromNc(userCMO.getUniqueId(), callerId);
							
					  		updateReferantor(referantorDetail.getReferantorNo(), GlobalVal.REFERANTOR1_CODE_CAE, trTaskH.getUuidTaskH(), callerId);
							updateReferantor(referantorDetail.getReferantorName(), GlobalVal.REFERANTOR1_NAME_CAE, trTaskH.getUuidTaskH(), callerId);
					  	} else {
							if(GlobalVal.MSCOREP.equalsIgnoreCase(trTaskH.getFlagSource())) {
								if (StringUtils.isBlank(trTaskH.getTaskIdPolo())) {
									updateReferantor(userCMO.getUniqueId(), GlobalVal.REFERANTOR1_CODE, trTaskH.getUuidTaskH(), callerId);
									updateReferantor(fullName, GlobalVal.REFERANTOR1_NAME, trTaskH.getUuidTaskH(), callerId);			
								}	
							}
					  	}
						//end set default answer untuk referantor task survey
						
						//start set default answer untuk referantor task visit
						if(GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName)) {
							Object[][] paramPoloData = {{Restrictions.eq("taskIdPolo",trTaskH.getTaskIdPolo())}, {Restrictions.eq("isSuccess","1")},
									{Restrictions.eq("groupTaskId", trTaskH.getUuidTaskH())}};
							TblPoloData poloData = this.getManagerDAO().selectOne(TblPoloData.class, paramPoloData);
							AddTaskPoloRequest requestTaskPolo = gson.fromJson(poloData.getJsonRequest(), AddTaskPoloRequest.class);
							if (requestTaskPolo.getNamaTask().equals("Penawaran")) {
								//2022-12-23 untuk referantor 1 diset dengan user assign
								GetReferantorResponse referantorDetail = intFormLogic.getReferantorCodeFromNc(userCMO.getUniqueId(), callerId);
								if(referantorDetail!=null) {
									updateReferantor(referantorDetail.getReferantorNo(), GlobalVal.REF_STV_REFCODE, trTaskH.getUuidTaskH(), callerId);
									updateReferantor(referantorDetail.getReferantorName(), GlobalVal.REF_STV_REFNAME, trTaskH.getUuidTaskH(), callerId);			
								}
								
								if (null != userCMO.getAmMsuser()) {
									updateReferantor(retrieveSpvCro(String.valueOf(userCMO.getAmMsuser().getUuidMsUser())),
											GlobalVal.REF_STV_SPV, trTaskH.getUuidTaskH(), callerId);
								}
							}
						}
						//end set default answer untuk referantor task visit
					}
							
					Date currentDate = new Date();
					trTaskH.setUsrUpd("SYSTEM");
					trTaskH.setDtmUpd(currentDate);
					trTaskH.setAssignDate(currentDate);
					trTaskH.setIsAlternate1("1");
							
					this.getManagerDAO().update(trTaskH);
							
					insertTaskHistory(callerId, trTaskH.getMsStatustask(), trTaskH, "Task Assign By Alternate 1",
							GlobalVal.CODE_PROCESS_ASSIGNMENT, fullName);
				}
			}
			else {
				TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] {
						{ Restrictions.eq("uuidTaskH", Long.valueOf(bean.get("d0").toString())) } });
				this.getManagerDAO().fetch(trTaskH.getMsStatustask());
				this.getManagerDAO().fetch(trTaskH.getAmMsuser());

				Object[][] param = { {"uuidTaskH", trTaskH.getUuidTaskH()} };
				String fullName =  (String) this.getManagerDAO().selectOneNativeString(
						"SELECT AMU.FULL_NAME FROM TR_TASK_H TTH WITH (NOLOCK)"
						+ "JOIN AM_MSUSER AMU WITH (NOLOCK) ON TTH.UUID_MS_USER = AMU.UUID_MS_USER WHERE UUID_TASK_H = :uuidTaskH", param );
						
				if (null != userCMO) {
					trTaskH.setAmMsuser(userCMO);
					trTaskH.setMsBranch(userCMO.getMsBranch());
					fullName = userCMO.getFullName();
				}
											
				Date currentDate = new Date();
				trTaskH.setUsrUpd("SYSTEM");
				trTaskH.setDtmUpd(currentDate);
				trTaskH.setAssignDate(currentDate);
				trTaskH.setIsAlternate1("1");
						
				this.getManagerDAO().update(trTaskH);
						
				insertTaskHistory(callerId, trTaskH.getMsStatustask(), trTaskH, "Task Assign By Alternate 1",
						GlobalVal.CODE_PROCESS_ASSIGNMENT, fullName);
			}
		}
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
    public void doAlternate2(Map bean, AuditContext callerId) {
		AmGeneralsetting amGeneralSetting = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_DURATION_ALT_2, callerId);
		
		String formName = bean.get("d10") != null ? bean.get("d10").toString() : "";
		String gsVal = amGeneralSetting.getGsValue();
		
		if (GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName)) {
			AmGeneralsetting amGeneralSettingVisit = commonLogic.retrieveGs(GlobalKey.GENERALSETTING_DURATION_ALT_VISIT_2, callerId);
			if(null != amGeneralSettingVisit && StringUtils.isNotBlank(amGeneralSettingVisit.getGsValue())) {
				gsVal = amGeneralSettingVisit.getGsValue();
			}
		}
		
		Integer minutes = (Integer) bean.get("d2");
		if (minutes > Integer.valueOf(gsVal)) {
			Long uuidProdCategory = (Long) (Long.valueOf(bean.get("d1").toString()) != null ? Long.valueOf(bean.get("d1").toString()) : "");
			String subZipCode = bean.get("d4") != null ? bean.get("d4").toString() : "%";
			String dealerCode = bean.get("d5") != null ? bean.get("d5").toString() : "%";
			String formType = bean.get("d6") != null ? bean.get("d6").toString() : "%";
			String currentUser = bean.get("d7") != null ? bean.get("d7").toString() : "";
			String isPiloting = bean.get("d9") != null ? bean.get("d9").toString() : "0";
			String jobCode = "";
			String nik =  bean.get("d11") != null ? bean.get("d11").toString() : "";
			
			String uuidTask = bean.get("d0").toString();
			String isPilotingCae = (String) this.getManagerDAO().selectOneNativeString("SELECT IS_PILOTING_CAE FROM TR_TASK_H WITH(NOLOCK) WHERE UUID_TASK_H = :uuidTaskH ", new Object[][] {{"uuidTaskH", uuidTask}}); 
			
			if (StringUtils.isNotBlank(nik) && "1".equalsIgnoreCase(isPilotingCae)) {
				if (!isAllowToAlternate(nik, currentUser)) {
					return;
				}
			} 
			
			if (GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName)) {
				Object[][] paramJob = {{"uuidMsUser", currentUser}};
				StringBuilder queryJob = new StringBuilder(" SELECT JOB_CODE ")
						.append(" FROM AM_MSUSER amu with(NOLOCK) ")
						.append(" JOIN MS_JOB mj with(NOLOCK) ON mj.UUID_JOB = amu.UUID_JOB ")
						.append(" WHERE amu.UUID_MS_USER = :uuidMsUser ");
				jobCode = (String) this.getManagerDAO().selectOneNativeString(queryJob.toString(), paramJob);
			}  else if ("1".equalsIgnoreCase(isPilotingCae)) {
				jobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_ROLE_TASK_CAE, callerId);
			}
			
			Map resultTaskDistribution = this.poolingTaskDistribution(uuidProdCategory, null, subZipCode, dealerCode, formType, currentUser, isPiloting, formName, jobCode, isPilotingCae, callerId);
			List<BigInteger> listAllUser = (List<BigInteger>) resultTaskDistribution.get("listUser");
			AmMsuser userCMO = null;
			if (!listAllUser.isEmpty()) {
				Random rand = new Random();
				Long uuidCMO = listAllUser.get(rand.nextInt(listAllUser.size())).longValue();
				userCMO = this.getManagerDAO().selectOne(AmMsuser.class, new Object[][] {
						{ Restrictions.eq("uuidMsUser", uuidCMO) } });
				this.getManagerDAO().fetch(userCMO.getMsBranch());
			}
					
			if ("1".equals(isPiloting)) {
				String applNo = bean.get("d8") != null ? bean.get("d8").toString() : "%";
				Object[][] paramPiloting = { {"applNo", applNo} };
				List pilotingTask = this.getManagerDAO().selectAllNativeString(
						"SELECT UUID_TASK_H " +
						"FROM   TR_TASK_H TTH WITH (NOLOCK) " +
						"JOIN   MS_STATUSTASK MST WITH(NOLOCK) ON MST.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK " +
						"WHERE  APPL_NO = :applNo " +
						"       AND (MST.STATUS_CODE = 'N' OR MST.STATUS_CODE = 'WP') " +
						"       AND TTH.IS_ALTERNATE2 = '0'", paramPiloting);
				if (null == pilotingTask || pilotingTask.isEmpty()) {
					String uuidTaskH = bean.get("d0").toString();
					pilotingTask = new ArrayList<>();
					Map map = new HashMap<>();
					map.put("d0", uuidTaskH);
					pilotingTask.add(map);
				}
				for (int k = 0; k < pilotingTask.size(); k++) {
					Long uuidTaskH = Long.valueOf((((Map) pilotingTask.get(k)).get("d0")).toString());
					TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] { { Restrictions.eq("uuidTaskH", uuidTaskH) } });
					this.getManagerDAO().fetch(trTaskH.getMsStatustask());
					this.getManagerDAO().fetch(trTaskH.getAmMsuser());
					
					//skip alternate for task survey from task visit
					if( !GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName) && StringUtils.isNotBlank(trTaskH.getTaskIdPolo()) ) {
						if(isSurveyFromVisit(String.valueOf(trTaskH.getUuidTaskH()))) {
							continue;
						}
					}
					//end alternate for task survey from task visit
							
					Object[][] param = { {"uuidTaskH", trTaskH.getUuidTaskH()} };
					String fullName =  (String) this.getManagerDAO().selectOneNativeString(
							"SELECT AMU.FULL_NAME FROM TR_TASK_H TTH WITH (NOLOCK)"
							+ "JOIN AM_MSUSER AMU ON TTH.UUID_MS_USER = AMU.UUID_MS_USER WHERE UUID_TASK_H = :uuidTaskH", param );
							
					if (null != userCMO) {
						trTaskH.setAmMsuser(userCMO);
						trTaskH.setMsBranch(userCMO.getMsBranch());
						fullName = userCMO.getFullName();
						
						//start set default answer untuk referantor task survey
						if ("1".equals(trTaskH.getIsPilotingCae()) && StringUtils.isNotBlank(trTaskH.getTaskIdPolo())) {
							GetReferantorResponse referantorDetail = intFormLogic.getReferantorCodeFromNc(userCMO.getUniqueId(), callerId);
							
					  		updateReferantor(referantorDetail.getReferantorNo(), GlobalVal.REFERANTOR1_CODE_CAE, trTaskH.getUuidTaskH(), callerId);
							updateReferantor(referantorDetail.getReferantorName(), GlobalVal.REFERANTOR1_NAME_CAE, trTaskH.getUuidTaskH(), callerId);
					  	} else {
							if(GlobalVal.MSCOREP.equalsIgnoreCase(trTaskH.getFlagSource())) {
								if (StringUtils.isBlank(trTaskH.getTaskIdPolo())) {
									updateReferantor(userCMO.getUniqueId(), GlobalVal.REFERANTOR1_CODE, trTaskH.getUuidTaskH(), callerId);
									updateReferantor(fullName, GlobalVal.REFERANTOR1_NAME, trTaskH.getUuidTaskH(), callerId);			
								}	
							}
					  	}
						//end set default answer untuk referantor task survey
						
						//start set default answer untuk referantor task visit
						if(GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(formName)) {
							Object[][] paramPoloData = {{Restrictions.eq("taskIdPolo",trTaskH.getTaskIdPolo())}, {Restrictions.eq("isSuccess","1")},
									{Restrictions.eq("groupTaskId", trTaskH.getUuidTaskH())}};
							TblPoloData poloData = this.getManagerDAO().selectOne(TblPoloData.class, paramPoloData);
							AddTaskPoloRequest requestTaskPolo = gson.fromJson(poloData.getJsonRequest(), AddTaskPoloRequest.class);
							if (requestTaskPolo.getNamaTask().equals("Penawaran")) {
								//2022-12-23 untuk referantor diset dengan user assign
								GetReferantorResponse referantorDetail = intFormLogic.getReferantorCodeFromNc(userCMO.getUniqueId(), callerId);
								if(referantorDetail != null) {
									updateReferantor(referantorDetail.getReferantorNo(), GlobalVal.REF_STV_REFCODE, trTaskH.getUuidTaskH(), callerId);
									updateReferantor(referantorDetail.getReferantorName(), GlobalVal.REF_STV_REFNAME, trTaskH.getUuidTaskH(), callerId);			
								}
								
								if (null != userCMO.getAmMsuser()) {
									updateReferantor(retrieveSpvCro(String.valueOf(userCMO.getAmMsuser().getUuidMsUser())),
											GlobalVal.REF_STV_SPV, trTaskH.getUuidTaskH(), callerId);
								}
							}
						}
						//end set default answer untuk referantor task visit
					}
							
					Date currentDate = new Date();
					trTaskH.setUsrUpd("SYSTEM");
					trTaskH.setDtmUpd(currentDate);
					trTaskH.setAssignDate(currentDate);
					trTaskH.setIsAlternate2("1");
							
					this.getManagerDAO().update(trTaskH);
							
					insertTaskHistory(callerId, trTaskH.getMsStatustask(), trTaskH, "Task Assign By Alternate 2",
							GlobalVal.CODE_PROCESS_ASSIGNMENT, fullName);
				}
			}
			else {
				TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] {
						{ Restrictions.eq("uuidTaskH", Long.valueOf(bean.get("d0").toString())) } });
				this.getManagerDAO().fetch(trTaskH.getMsStatustask());
				this.getManagerDAO().fetch(trTaskH.getAmMsuser());
						
				Object[][] param = { {"uuidTaskH", trTaskH.getUuidTaskH()} };
				String fullName =  (String) this.getManagerDAO().selectOneNativeString(
						"SELECT AMU.FULL_NAME FROM TR_TASK_H TTH WITH (NOLOCK)"
						+ "JOIN AM_MSUSER AMU ON TTH.UUID_MS_USER = AMU.UUID_MS_USER WHERE UUID_TASK_H = :uuidTaskH", param );
						
				if (null != userCMO) {
					trTaskH.setAmMsuser(userCMO);
					trTaskH.setMsBranch(userCMO.getMsBranch());
					fullName = userCMO.getFullName();
				}
											
				Date currentDate = new Date();
				trTaskH.setUsrUpd("SYSTEM");
				trTaskH.setDtmUpd(currentDate);
				trTaskH.setAssignDate(currentDate);
				trTaskH.setIsAlternate2("1");
						
				this.getManagerDAO().update(trTaskH);
						
				insertTaskHistory(callerId, trTaskH.getMsStatustask(), trTaskH, "Task Assign By Alternate 2",
						GlobalVal.CODE_PROCESS_ASSIGNMENT, fullName);
			}
		}	
    }
	
	@Transactional
	@Override
    public List listTaskUnassign(AuditContext callerId) {
    	StringBuilder queryBuilder = new StringBuilder();
    	queryBuilder.append("SELECT     DISTINCT tth.UUID_TASK_H, tth.ASSIGN_DATE, DATEDIFF (MINUTE, tth.ASSIGN_DATE, GETDATE()) as Minute, ");
    	queryBuilder.append("           tth.UUID_MS_USER, amu.SPV_ID, tth.NOTES, ms.UUID_STATUS_TASK, ms.STATUS_TASK_DESC, tth.IS_ALTERNATE1, tth.APPL_NO, mb.IS_PILOTING ");
    	queryBuilder.append("FROM       TR_TASK_H tth WITH (NOLOCK) ");
    	queryBuilder.append("JOIN       MS_FORM mf WITH (NOLOCK) ON tth.UUID_FORM = mf.UUID_FORM ");
    	queryBuilder.append("LEFT JOIN  ( ");
		queryBuilder.append("               SELECT tp.TASK_ID_POLO, gt.UUID_TASK_H, tp.PRODUCT_CATEGORY_CODE ");
		queryBuilder.append("               FROM   TBL_POLO_DATA tp WITH (NOLOCK) ");
		queryBuilder.append("               JOIN   MS_GROUPTASK gt WITH (NOLOCK) ON gt.GROUP_TASK_ID = tp.GROUP_TASK_ID AND IS_SUCCESS = '1' ");
		queryBuilder.append("           ) tpgt ON tpgt.TASK_ID_POLO = tth.TASK_ID_POLO and tpgt.UUID_TASK_H = tth.UUID_TASK_H ");
		queryBuilder.append("LEFT JOIN  ( ");
		queryBuilder.append("               SELECT tp.ORDER_NO_CAE, gt.UUID_TASK_H, tp.PRODUCT_CATEGORY_CODE ");
		queryBuilder.append("               FROM   TBL_CAE_DATA tp WITH (NOLOCK) ");
		queryBuilder.append("               JOIN   MS_GROUPTASK gt WITH (NOLOCK) ON gt.GROUP_TASK_ID = tp.GROUP_TASK_ID AND IS_SUCCESS = '1' ");
		queryBuilder.append("           ) tcae ON tcae.ORDER_NO_CAE = tth.ORDER_NO_CAE and tcae.UUID_TASK_H = tth.UUID_TASK_H ");
		queryBuilder.append("JOIN       TBL_PRODUCT_CATEGORY pc WITH (NOLOCK) ON ( ");
		queryBuilder.append("               tth.UUID_FORM = pc.UUID_FORM_FOTO ");
		queryBuilder.append("               OR tth.UUID_FORM = pc.UUID_FORM ");
		queryBuilder.append("               OR ( ");
		queryBuilder.append("                   tth.UUID_FORM = pc.UUID_FORM_COMPLETED ");
		queryBuilder.append("                   AND ISNULL(tcae.PRODUCT_CATEGORY_CODE, tpgt.PRODUCT_CATEGORY_CODE) = pc.PRODUCT_CATEGORY_CODE ");
		queryBuilder.append("                   AND tth.IS_PILOTING_CAE = '1' ");
		queryBuilder.append("               ) ");
		queryBuilder.append("            ) ");
		queryBuilder.append("JOIN       AM_MSUSER amu WITH (NOLOCK) ON tth.UUID_MS_USER = amu.UUID_MS_USER ");
		queryBuilder.append("JOIN       MS_BRANCH mb WITH (NOLOCK) ON amu.UUID_BRANCH = mb.UUID_BRANCH ");
		queryBuilder.append("JOIN       MS_STATUSTASK ms WITH (NOLOCK) ON tth.UUID_STATUS_TASK = ms.UUID_STATUS_TASK ");
		queryBuilder.append("WHERE      (ms.STATUS_CODE = 'N' OR ms.STATUS_CODE = 'WP') ");
		queryBuilder.append("           AND tth.IS_ALTERNATE1 = CASE ");
		queryBuilder.append("               WHEN 0 < ( ");
		queryBuilder.append("                   SELECT COUNT(1) ");
		queryBuilder.append("                   FROM   TBL_MAP_PROD_ASSIGN tmpa WITH (NOLOCK) ");
		queryBuilder.append("                   JOIN   TBL_TASK_ASSIGN_MODE ttam WITH (NOLOCK) ON tmpa.TBL_TASK_ASSIGN_MODE_ID = ttam.TBL_TASK_ASSIGN_MODE_ID ");
		queryBuilder.append("                   WHERE  ttam.ASSIGN_CODE = 'ALT01' AND tmpa.TBL_PRODUCT_CATEGORY_ID = pc.TBL_PRODUCT_CATEGORY_ID ");
		queryBuilder.append("               ) THEN 1 ");
		queryBuilder.append("               ELSE 0 ");
		queryBuilder.append("            END ");
		queryBuilder.append("            AND tth.IS_ALTERNATE2 = CASE "); 
		queryBuilder.append("               WHEN 0 < ( ");
		queryBuilder.append("                   SELECT COUNT(1) "); 
		queryBuilder.append("                   FROM   TBL_MAP_PROD_ASSIGN tmpa WITH (NOLOCK) "); 
		queryBuilder.append("                   JOIN   TBL_TASK_ASSIGN_MODE ttam WITH (NOLOCK) ON tmpa.TBL_TASK_ASSIGN_MODE_ID = ttam.TBL_TASK_ASSIGN_MODE_ID "); 
		queryBuilder.append("                   WHERE  ttam.ASSIGN_CODE = 'ALT02' AND tmpa.TBL_PRODUCT_CATEGORY_ID = pc.TBL_PRODUCT_CATEGORY_ID ");
		queryBuilder.append("               ) THEN 1 ");
		queryBuilder.append("               ELSE 0 ");
		queryBuilder.append("            END ");
		queryBuilder.append("            AND tth.IS_UNASSIGN = 0 ");
		queryBuilder.append("            AND tth.PROMISE_DATE IS NULL "); 
		queryBuilder.append("            AND ISNULL(tth.IS_SELFASSIGNMENT,'0') != '1' "); 
		queryBuilder.append("            AND pc.JENIS_PEMBIAYAAN = SUBSTRING(mb.KONVEN_SYARIAH, 1, 1)");
    	return this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), null);
    }
	
	@Transactional
	@Override
    public List listTaskUnassignCS(AuditContext callerId) {
    	StringBuilder queryBuilder = new StringBuilder();
    	queryBuilder.append("SELECT DISTINCT tth.UUID_TASK_H, tth.ASSIGN_DATE, DATEDIFF (MINUTE, tth.ASSIGN_DATE, GETDATE()) as Minute, ");
    	queryBuilder.append("		tth.UUID_MS_USER, amu.SPV_ID, tth.NOTES, ms.UUID_STATUS_TASK, ms.STATUS_TASK_DESC, tth.IS_UNASSIGN, tth.APPL_NO, mb.IS_PILOTING ");
    	queryBuilder.append("FROM TR_TASK_H tth WITH (NOLOCK) ");
    	queryBuilder.append("JOIN MS_FORM mf WITH (NOLOCK) ON tth.UUID_FORM = mf.UUID_FORM ");
    	queryBuilder.append("JOIN MS_BRANCH mb WITH(NOLOCK) ON tth.UUID_BRANCH = mb.UUID_BRANCH ");
		queryBuilder.append("JOIN AM_MSUSER amu WITH(NOLOCK) ON tth.UUID_MS_USER = amu.UUID_MS_USER ");
		queryBuilder.append("LEFT JOIN MS_JOB mj WITH(NOLOCK) ON amu.UUID_JOB = mj.UUID_JOB ");
		queryBuilder.append("JOIN MS_STATUSTASK ms WITH(NOLOCK) ON tth.UUID_STATUS_TASK = ms.UUID_STATUS_TASK ");
		queryBuilder.append("WHERE mf.FORM_NAME = 'Form Task Promise To Survey' AND ms.STATUS_TASK_DESC = 'PENDING' AND mj.JOB_CODE = 'CS' AND tth.IS_UNASSIGN = 0 ");
    	return this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), null);
    }
	
	@Transactional
	@Override
    public List listTaskUnassignPreApproval(AuditContext callerId) {
    	StringBuilder queryBuilder = new StringBuilder();
    	queryBuilder.append("SELECT DISTINCT tth.UUID_TASK_H, tth.ASSIGN_DATE, DATEDIFF (MINUTE, tth.ASSIGN_DATE, GETDATE()) as Minute, ");
    	queryBuilder.append("		tth.UUID_MS_USER, amu.SPV_ID, tth.NOTES, ms.UUID_STATUS_TASK, ms.STATUS_TASK_DESC, tth.IS_UNASSIGN, tth.APPL_NO, mb.IS_PILOTING ");
    	queryBuilder.append("FROM TR_TASK_H tth WITH (NOLOCK) ");
    	queryBuilder.append("JOIN MS_FORM mf WITH (NOLOCK) ON tth.UUID_FORM = mf.UUID_FORM ");
    	queryBuilder.append("JOIN MS_BRANCH mb WITH(NOLOCK) ON tth.UUID_BRANCH = mb.UUID_BRANCH ");
		queryBuilder.append("JOIN AM_MSUSER amu WITH(NOLOCK) ON tth.UUID_MS_USER = amu.UUID_MS_USER ");
		queryBuilder.append("LEFT JOIN MS_JOB mj WITH(NOLOCK) ON amu.UUID_JOB = mj.UUID_JOB ");
		queryBuilder.append("JOIN MS_STATUSTASK ms WITH(NOLOCK) ON tth.UUID_STATUS_TASK = ms.UUID_STATUS_TASK ");
		queryBuilder.append("WHERE mf.FORM_NAME = 'Form Task Promise To Survey' AND ms.STATUS_TASK_DESC = 'PENDING' AND mj.JOB_CODE != 'CS' AND tth.OPSI_PENANGANAN = 'Di Cabang' AND tth.IS_UNASSIGN = 0 ");
    	return this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), null);
    }
	
	@Transactional
	@Override
    public List listTaskUnassignOTSbyCabang(AuditContext callerId) {
		
    	String query = "SELECT	A.GROUP_TASK_ID, A.UUID_TASK_H AS UUID_PTS, tth.UUID_TASK_H AS UUID_OTS, A.UUID_MS_USER, \r\n" + 
    			"    	mf.FORM_NAME, ms.STATUS_TASK_DESC, A.OPSI_PENANGANAN\r\n" + 
    			"FROM (\r\n" + 
    			"    SELECT	tth.UUID_TASK_H, mg.GROUP_TASK_ID, tth.UUID_MS_USER, tth.PROMISE_DATE,\r\n" + 
    			"    		ms.STATUS_TASK_DESC, tth.OPSI_PENANGANAN\r\n" + 
    			"    FROM TR_TASK_H tth WITH(NOLOCK)\r\n" + 
    			"    JOIN MS_FORM mf ON tth.UUID_FORM = mf.UUID_FORM\r\n" + 
    			"    JOIN MS_GROUPTASK mg ON tth.UUID_TASK_H = mg.UUID_TASK_H\r\n" + 
    			"    JOIN MS_STATUSTASK ms ON tth.UUID_STATUS_TASK = ms.UUID_STATUS_TASK\r\n" + 
    			"    WHERE 1=1\r\n" + 
    			"    	AND mf.FORM_NAME = 'Form Task Promise To Survey'\r\n" + 
    			"    	AND tth.PROMISE_DATE IS NOT NULL\r\n" + 
    			"    	AND tth.OPSI_PENANGANAN = 'Di Cabang'\r\n" + 
    			"    	AND ms.STATUS_CODE IN ('N', 'S')\r\n" + 
    			"    	AND tth.UUID_MS_USER IS NOT NULL\r\n" + 
    			") A\r\n" + 
    			"JOIN MS_GROUPTASK mg ON A.GROUP_TASK_ID = mg.GROUP_TASK_ID\r\n" + 
    			"JOIN TR_TASK_H tth WITH(NOLOCK) ON mg.UUID_TASK_H = tth.UUID_TASK_H\r\n" + 
    			"JOIN MS_FORM mf ON tth.UUID_FORM = mf.UUID_FORM\r\n" + 
    			"JOIN MS_STATUSTASK ms ON tth.UUID_STATUS_TASK = ms.UUID_STATUS_TASK\r\n" + 
    			"WHERE mf.FORM_NAME = 'Form Task OTS'\r\n" + 
    			"    AND ms.STATUS_CODE = 'A'";
    			
    	return this.getManagerDAO().selectAllNativeString(query, null);
    }
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public List getTaskAlternate1(AuditContext callerId) {
		Object[][] params = { {"isAlternate1", 0}, {"assignCode", GlobalVal.MAPPING_ALTERNATE_1} };
		StringBuilder queryBuilder = new StringBuilder();
		queryBuilder.append("SELECT     tth.UUID_TASK_H, pc.TBL_PRODUCT_CATEGORY_ID, DATEDIFF(MINUTE, ASSIGN_DATE, GETDATE()) AS MINUTE, "); 
		queryBuilder.append("           tth.UUID_FORM, SUBZIPCODE, DEALER_CODE, pc.JENIS_PEMBIAYAAN, tth.UUID_MS_USER, tth.APPL_NO, mb.IS_PILOTING, mf.FORM_NAME, tth.nik ");
		queryBuilder.append("FROM       TR_TASK_H tth WITH (NOLOCK) ");
		queryBuilder.append("JOIN       MS_STATUSTASK mst WITH (NOLOCK) ON tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK "); 
		queryBuilder.append("JOIN       MS_BRANCH mb WITH (NOLOCK) ON tth.UUID_BRANCH = mb.UUID_BRANCH ");
		queryBuilder.append("LEFT JOIN  ( ");
		queryBuilder.append("               SELECT tp.TASK_ID_POLO, gt.UUID_TASK_H, tp.PRODUCT_CATEGORY_CODE ");
		queryBuilder.append("               FROM   TBL_POLO_DATA tp WITH (NOLOCK) ");
		queryBuilder.append("               JOIN   MS_GROUPTASK gt WITH (NOLOCK) ON gt.GROUP_TASK_ID = tp.GROUP_TASK_ID AND IS_SUCCESS = '1' ");
		queryBuilder.append("           ) tpgt ON tpgt.TASK_ID_POLO = tth.TASK_ID_POLO AND tpgt.UUID_TASK_H = tth.UUID_TASK_H ");
		queryBuilder.append("LEFT JOIN  ( ");
		queryBuilder.append("               SELECT tp.ORDER_NO_CAE, gt.UUID_TASK_H, tp.PRODUCT_CATEGORY_CODE ");
		queryBuilder.append("               FROM   TBL_CAE_DATA tp WITH (NOLOCK) ");
		queryBuilder.append("               JOIN   MS_GROUPTASK gt WITH (NOLOCK) ON gt.GROUP_TASK_ID = tp.GROUP_TASK_ID AND IS_SUCCESS = '1' ");
		queryBuilder.append("           ) tcae ON tcae.ORDER_NO_CAE = tth.ORDER_NO_CAE AND tcae.UUID_TASK_H = tth.UUID_TASK_H "); 
		queryBuilder.append("JOIN       TBL_PRODUCT_CATEGORY pc WITH (NOLOCK) ON ( ");
		queryBuilder.append("               tth.UUID_FORM = pc.UUID_FORM_FOTO ");
		queryBuilder.append("               OR tth.UUID_FORM = pc.UUID_FORM ");
		queryBuilder.append("               OR ( ");
		queryBuilder.append("                   tth.UUID_FORM = pc.UUID_FORM_COMPLETED ");
		queryBuilder.append("                   AND ISNULL(tcae.PRODUCT_CATEGORY_CODE, tpgt.PRODUCT_CATEGORY_CODE) = pc.PRODUCT_CATEGORY_CODE ");
		queryBuilder.append("                   AND tth.IS_PILOTING_CAE = '1' ");
		queryBuilder.append("               ) ");
		queryBuilder.append("            ) ");
		queryBuilder.append("JOIN        TBL_MAP_PROD_ASSIGN mpa WITH (NOLOCK) ON pc.TBL_PRODUCT_CATEGORY_ID = mpa.TBL_PRODUCT_CATEGORY_ID AND pc.JENIS_PEMBIAYAAN = SUBSTRING(mb.KONVEN_SYARIAH, 1, 1) ");
		queryBuilder.append("JOIN        TBL_TASK_ASSIGN_MODE tam WITH (NOLOCK) ON mpa.TBL_TASK_ASSIGN_MODE_ID = tam.TBL_TASK_ASSIGN_MODE_ID ");
		queryBuilder.append("JOIN        MS_FORM mf WITH (NOLOCK) ON mf.UUID_FORM = tth.UUID_FORM ");
		queryBuilder.append("WHERE       (mst.STATUS_CODE = 'N' OR mst.STATUS_CODE = 'WP') ");
		queryBuilder.append("            AND tth.PROMISE_DATE IS NULL ");
		queryBuilder.append("            AND ISNULL(tth.IS_SELFASSIGNMENT,'0') != '1' ");
		queryBuilder.append("            AND IS_ALTERNATE1 = :isAlternate1 ");
		queryBuilder.append("            AND ASSIGN_CODE = :assignCode");
		
		return this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), params);
	}
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public List getTaskAlternate2(AuditContext callerId) {
		Object[][] params = { {"isAlternate1", 1}, {"isAlternate2", 0}, {"assignCode", GlobalVal.MAPPING_ALTERNATE_2} };
		StringBuilder queryBuilder = new StringBuilder();
		queryBuilder.append("SELECT     tth.UUID_TASK_H, pc.TBL_PRODUCT_CATEGORY_ID, DATEDIFF(MINUTE, ASSIGN_DATE, GETDATE()) AS MINUTE, ");
		queryBuilder.append("           tth.UUID_FORM, SUBZIPCODE, DEALER_CODE, pc.JENIS_PEMBIAYAAN, tth.UUID_MS_USER, tth.APPL_NO, mb.IS_PILOTING, mf.FORM_NAME, tth.nik ");
		queryBuilder.append("FROM       TR_TASK_H tth WITH (NOLOCK) ");
		queryBuilder.append("JOIN       MS_STATUSTASK mst WITH (NOLOCK) ON tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK "); 
		queryBuilder.append("JOIN       MS_BRANCH mb WITH (NOLOCK) ON tth.UUID_BRANCH = mb.UUID_BRANCH ");
		queryBuilder.append("LEFT JOIN  ( ");
		queryBuilder.append("               SELECT tp.TASK_ID_POLO, gt.UUID_TASK_H, tp.PRODUCT_CATEGORY_CODE ");
		queryBuilder.append("               FROM   TBL_POLO_DATA tp WITH (NOLOCK) ");
		queryBuilder.append("               JOIN   MS_GROUPTASK gt WITH (NOLOCK) ON gt.GROUP_TASK_ID = tp.GROUP_TASK_ID AND IS_SUCCESS = '1' ");
		queryBuilder.append("           ) tpgt ON tpgt.TASK_ID_POLO = tth.TASK_ID_POLO AND tpgt.UUID_TASK_H = tth.UUID_TASK_H ");
		queryBuilder.append("LEFT JOIN  ( ");
		queryBuilder.append("               SELECT tp.ORDER_NO_CAE, gt.UUID_TASK_H, tp.PRODUCT_CATEGORY_CODE ");
		queryBuilder.append("               FROM   TBL_CAE_DATA tp WITH (NOLOCK) ");
		queryBuilder.append("               JOIN   MS_GROUPTASK gt WITH (NOLOCK) ON gt.GROUP_TASK_ID = tp.GROUP_TASK_ID AND IS_SUCCESS = '1' ");
		queryBuilder.append("           ) tcae ON tcae.ORDER_NO_CAE = tth.ORDER_NO_CAE AND tcae.UUID_TASK_H = tth.UUID_TASK_H "); 
		queryBuilder.append("JOIN       TBL_PRODUCT_CATEGORY pc WITH (NOLOCK) ON ( ");
		queryBuilder.append("               tth.UUID_FORM = pc.UUID_FORM_FOTO ");
		queryBuilder.append("               OR tth.UUID_FORM = pc.UUID_FORM ");
		queryBuilder.append("               OR ( ");
		queryBuilder.append("                   tth.UUID_FORM = pc.UUID_FORM_COMPLETED ");
		queryBuilder.append("                   AND ISNULL(tcae.PRODUCT_CATEGORY_CODE, tpgt.PRODUCT_CATEGORY_CODE) = pc.PRODUCT_CATEGORY_CODE ");
		queryBuilder.append("                   AND tth.IS_PILOTING_CAE = '1' ");
		queryBuilder.append("               ) ");
		queryBuilder.append("            ) ");
		queryBuilder.append("JOIN        TBL_MAP_PROD_ASSIGN mpa WITH (NOLOCK) ON pc.TBL_PRODUCT_CATEGORY_ID = mpa.TBL_PRODUCT_CATEGORY_ID AND pc.JENIS_PEMBIAYAAN = SUBSTRING(mb.KONVEN_SYARIAH, 1, 1) ");
		queryBuilder.append("JOIN        TBL_TASK_ASSIGN_MODE tam WITH (NOLOCK) ON mpa.TBL_TASK_ASSIGN_MODE_ID = tam.TBL_TASK_ASSIGN_MODE_ID ");
		queryBuilder.append("JOIN        MS_FORM mf WITH (NOLOCK) ON mf.UUID_FORM = tth.UUID_FORM ");
		queryBuilder.append("WHERE       (mst.STATUS_CODE = 'N' OR mst.STATUS_CODE = 'WP') ");
		queryBuilder.append("            AND tth.PROMISE_DATE IS NULL ");
		queryBuilder.append("            AND ISNULL(tth.IS_SELFASSIGNMENT,'0') != '1' ");
		queryBuilder.append("            AND IS_ALTERNATE1 = :isAlternate1 ");
		queryBuilder.append("            AND IS_ALTERNATE2 = :isAlternate2 ");
		queryBuilder.append("            AND ASSIGN_CODE = :assignCode");
		
		return this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), params);
	}
	
	@Transactional
	@Override
	public void updateReferantor(String answer, String refId, long uuidTaskH, AuditContext callerId) {
		Object[][] paramsTaskDReferantor = {{"refId", refId}, {"uuidTaskH", uuidTaskH}, {"value", answer}, {"usrUpd", callerId.getCallerId()}};
		StringBuilder queryTaskDReferantor = new StringBuilder(" UPDATE TTD SET INT_TEXT_ANSWER = :value , TTD.USR_UPD = :usrUpd , TTD.DTM_UPD = GETDATE() ")
				.append(" FROM TR_TASK_H TTH WITH(NOLOCK) ")
				.append(" JOIN TR_TASK_D TTD WITH(NOLOCK) ON TTH.UUID_TASK_H = TTD.UUID_TASK_H ")
				.append(" JOIN MS_QUESTION MQ WITH(NOLOCK) ON MQ.UUID_QUESTION = TTD.UUID_QUESTION AND MQ.REF_ID = :refId ")
				.append(" WHERE TTH.UUID_TASK_H = :uuidTaskH");
		this.getManagerDAO().updateNativeString(queryTaskDReferantor.toString(), paramsTaskDReferantor);
	}
	
	private boolean isSurveyFromVisit(String uuidTaskSurvey) {
		String[][] prm0 = {{"uuid", uuidTaskSurvey}, {"formName", GlobalVal.FORM_VISIT_POLO}};
		String qry = "select count(th.UUID_TASK_H) as cnt "
				+ "from MS_GROUPTASK gt (nolock) join TR_TASK_H th (nolock) on gt.UUID_TASK_H = th.UUID_TASK_H "
				+ "join MS_FORM f (nolock) on f.UUID_FORM = th.UUID_FORM "
				+ "where gt.GROUP_TASK_ID = (select GROUP_TASK_ID from MS_GROUPTASK (nolock) where UUID_TASK_H = :uuid ) "
				+ "and FORM_NAME = :formName";
		Integer cnt = (Integer) this.getManagerDAO().selectOneNativeString(qry, prm0);
		
		return 0 != cnt;
	}
	
	private boolean isAllowToAlternate (String nik, String userAssign) {
		StringBuilder query = new StringBuilder();
		query.append(" SELECT count(1) from TR_TASK_H WITH(NOLOCK) ");
		query.append(" WHERE NIK = :nik ");
		query.append(" AND UUID_MS_USER = :uuidUser ");
		query.append(" AND PROMISE_DATE BETWEEN :start AND :end ");
		
		Object [][] params = new Object[][] {{"nik", nik}, {"uuidUser", userAssign}, {"start", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 00:00:00.000"}, {"end", DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 23:59:59.997"}};
		Integer totTaskPromised = (Integer) this.getManagerDAO().selectOneNativeString(query.toString(), params);
		return totTaskPromised == 0;
	}
	
	@Async
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void doUnassign(Map bean, AmGeneralsetting gsDuration, AuditContext callerId) {
		Map temp = bean;    		
		Integer difTime = Integer.parseInt(temp.get("d2").toString());
		Integer durationFromGenset =  Integer.parseInt(gsDuration.getGsValue());
		if (difTime > durationFromGenset) {    			   
			String uuidTaskH = temp.get("d0").toString();
			AmMsuser userCMO = null;
			MsBranch msBranch = null;
			if (null != temp.get("d4")) {
				String spvId = temp.get("d4").toString();
				userCMO = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(spvId));
			   	this.getManagerDAO().fetch(userCMO.getMsBranch());
			   	msBranch = userCMO.getMsBranch();
			   	LOG.info("SPV : {}", userCMO.getLoginId());
			}
			LOG.info("User : {}", userCMO);
			Date date = new Date();
			AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);   
			
			String isPiloting = temp.get("d10") != null ? temp.get("d10").toString() : "0";
			if ("1".equals(isPiloting)) {
				String applNo = temp.get("d9") != null ? temp.get("d9").toString() : "%";
				processTaskPilotingToUnassign(applNo, uuidTaskH, date, userCMO, msBranch, uuidMsSub, callerId);
			} 
			else {
				Object[][] param2 = { {Restrictions.eq("uuidTaskH",  Long.valueOf(uuidTaskH))} };    			
    			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, param2);
    			updateTaskToUnassign(trTaskH, date, userCMO, msBranch, uuidMsSub, callerId);
			}
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void doUnassignCS(Map bean, AmGeneralsetting gsDuration, AuditContext callerId) {
		Map temp = bean; 
		Integer difTime = Integer.parseInt(temp.get("d2").toString());
		Integer durationFromGenset =  Integer.parseInt(gsDuration.getGsValue());
		if (difTime > durationFromGenset) {    			   
			String uuidTaskH = temp.get("d0").toString();
			AmMsuser userCS = null;
			MsBranch msBranch = null;
			if (null != temp.get("d3")) {
				String user = temp.get("d3").toString();
				userCS = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(user));
			   	this.getManagerDAO().fetch(userCS.getMsBranch());
			   	msBranch = userCS.getMsBranch();
			}
			Date date = new Date();
			AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);   
			
			String isPiloting = temp.get("d10") != null ? temp.get("d10").toString() : "0";
			if ("1".equals(isPiloting)) {
				String applNo = temp.get("d9") != null ? temp.get("d9").toString() : "%";
				processTaskPilotingToUnassignCS(applNo, uuidTaskH, date, userCS, msBranch, uuidMsSub, callerId);
			} 
			else {
				Object[][] param2 = { {Restrictions.eq("uuidTaskH",  Long.valueOf(uuidTaskH))} };    			
    			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, param2);
    			updateTaskToUnassignCS(trTaskH, date, userCS, msBranch, uuidMsSub, callerId);
			}
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String doAssignOts(Map bean, AuditContext callerId) {
		LOG.info("Start Claim Task OTS");
		
		BigInteger uuidMsUserBigInteger = (BigInteger) bean.get("d3");
		Long uuidMsUser = uuidMsUserBigInteger.longValue();
		BigInteger uuidTaskHBigInteger = (BigInteger) bean.get("d2");
		Long uuidTaskH = uuidTaskHBigInteger.longValue();
		
		LOG.info("UUID MS_USER : {}, UUID_TASK_OTS : {} ", uuidMsUser, uuidTaskH);
		
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, new Object [][] { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_NEW) } });
		
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, new Object [][] { { Restrictions.eq("uuidMsUser", Long.valueOf(uuidMsUser)) } });
		this.getManagerDAO().fetch(amMsuser.getMsBranch());
		MsBranch msBranch = amMsuser.getMsBranch();
		
		LOG.info("USER : {}, UUID_TASK_OTS : {} ", amMsuser.getLoginId(), uuidTaskH);
		LOG.info("Branch Code : {}", msBranch.getBranchCode());
		
		amMsuser = this.getManagerDAO().selectOne("from AmMsuser a "
				+ "join fetch a.msBranch b "
				+ "join fetch a.msJob c "
				+ "where b.branchCode = :branchCode "
				+ "and c.jobCode = 'BH' and a.isActive = 1 ", new Object[][] {{"branchCode", msBranch.getBranchCode()}});
		
		TrTaskH taskH = this.getManagerDAO().selectOne("from TrTaskH tth "
				+ "join fetch tth.msStatustask mst "
				+ "join fetch mst.amMssubsystem ams "
				+ "where tth.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", Long.valueOf(uuidTaskH)}});
		
		taskH.setAmMsuser(amMsuser);
		taskH.setMsBranch(msBranch);
		taskH.setAssignDate(new Date());
		taskH.setDtmUpd(new Date());
		taskH.setUsrUpd(String.valueOf(amMsuser.getUuidMsUser()));
		taskH.setMsStatusmobile(msm);
		taskH.setIsUnassign("0");
		this.getManagerDAO().update(taskH);
		
		//hit updatePolo
	  	intFormLogic.updateDataPolo(null, taskH, null, "Pending", "F", null, null, null, null, callerId);
	  	
		Object[][] params2 = {
				{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION) },
				{Restrictions.eq("amMssubsystem.uuidMsSubsystem", amMsuser.getAmMssubsystem().getUuidMsSubsystem()) } };
		MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
		
		LOG.info("status task : {}", msStatustask.getStatusTaskDesc());
		
		taskH.setMsStatustask(msStatustask);
		
	    this.getManagerDAO().update(taskH);

		LOG.info("status task {}:{}",taskH.getUuidTaskH(),taskH.getMsStatustask().getStatusTaskDesc());

		TrTaskhistory trTaskhistory = new TrTaskhistory();
		trTaskhistory.setCodeProcess(GlobalVal.CODE_PROCESS_ASSIGNMENT);
		trTaskhistory.setActor(amMsuser.getFullName());
		trTaskhistory.setFieldPerson(amMsuser.getFullName());
		trTaskhistory.setMsStatustask(msStatustask);
		trTaskhistory.setTrTaskH(taskH);
		trTaskhistory.setNotes(taskH.getNotes());
		trTaskhistory.setUsrCrt(String.valueOf(amMsuser.getUuidMsUser()));
		trTaskhistory.setDtmCrt(new Date());

		this.getManagerDAO().update(taskH);
		
		if (StringUtils.isNotBlank(taskH.getOrderId())) {
			UpdateStatusMainDealerResponse updStatusTaskMainDealer = this.intFormLogic.updateStatusTaskMainDealer(taskH, callerId);
			if (!"00".equals(updStatusTaskMainDealer.getResponseCode())) {
				throw new RemoteException(updStatusTaskMainDealer.getResponseMessage());
			}
		}
			
		this.getManagerDAO().insert(trTaskhistory);
		
		LOG.info("End Assign Task OTS by Cabang");
		
		return GlobalVal.SERVICES_RESULT_SUCCESS; 
	}
	
	private void processTaskPilotingToUnassign(String applNo, String uuidTaskH, Date date, AmMsuser userCMO, MsBranch msBranch, AmMssubsystem uuidMsSub, AuditContext callerId) {
		Object[][] paramPiloting = { {"applNo", applNo} };
		List pilotingTask = this.getManagerDAO().selectAllNativeString(
				"SELECT UUID_TASK_H " +
				"FROM   TR_TASK_H TTH WITH (NOLOCK) " +
				"JOIN   MS_STATUSTASK MST WITH(NOLOCK) ON MST.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK " +
				"WHERE  APPL_NO = :applNo AND tth.PROMISE_DATE IS NULL " +
				"       AND (MST.STATUS_CODE = 'N' OR MST.STATUS_CODE = 'WP') " +
				"       AND TTH.IS_UNASSIGN = '0'", paramPiloting);
		if (null == pilotingTask || pilotingTask.isEmpty()) {
			pilotingTask = new ArrayList<>();
			Map map = new HashMap<>();
			map.put("d0", uuidTaskH);
			pilotingTask.add(map);
		}
		TrTaskH trTaskHUpdPolo = null;
		for (int k = 0; k < pilotingTask.size(); k++) {
			Long uuidTaskHPiloting = Long.valueOf((((Map) pilotingTask.get(k)).get("d0")).toString());
			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] { { Restrictions.eq("uuidTaskH", uuidTaskHPiloting) } });
			if (trTaskH.getPromiseDate() != null) {
				return;
			}
			this.getManagerDAO().fetch(trTaskH.getMsStatustask());
			if (GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION.equals(trTaskH.getMsStatustask().getStatusCode())) {
				trTaskHUpdPolo = trTaskH;
			}
			
			//skip alternate for task survey from task visit
			if( !GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName()) 
					&& StringUtils.isNotBlank(trTaskH.getTaskIdPolo()) 
					&& isSurveyFromVisit(String.valueOf(trTaskH.getUuidTaskH()))) {
					continue;
			}
			//end alternate for task survey from task visit
			
			updateTaskToUnassign(trTaskH, date, userCMO, msBranch, uuidMsSub, callerId);
		}
		
		if(StringUtils.isNotBlank(trTaskHUpdPolo.getTaskIdPolo()) || StringUtils.isNotBlank(trTaskHUpdPolo.getOrderNoCae())) {
			intFormLogic.updateDataPoloAsync(null, trTaskHUpdPolo, null, "UNASSIGNED", "T", null, callerId);
		}
	}
	
	private void processTaskPilotingToUnassignCS(String applNo, String uuidTaskH, Date date, AmMsuser userCMO, MsBranch msBranch, AmMssubsystem uuidMsSub, AuditContext callerId) {
		Object[][] paramPiloting = { {"applNo", applNo} };
		List pilotingTask = this.getManagerDAO().selectAllNativeString(
				"SELECT UUID_TASK_H " +
				"FROM   TR_TASK_H TTH WITH (NOLOCK) " +
				"JOIN   MS_STATUSTASK MST WITH(NOLOCK) ON MST.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK " +
				"WHERE  APPL_NO = :applNo AND tth.PROMISE_DATE IS NULL " +
				"       AND (MST.STATUS_CODE = 'N' OR MST.STATUS_CODE = 'WP') " +
				"       AND TTH.IS_UNASSIGN = '0'", paramPiloting);
		if (null == pilotingTask || pilotingTask.isEmpty()) {
			pilotingTask = new ArrayList<>();
			Map map = new HashMap<>();
			map.put("d0", uuidTaskH);
			pilotingTask.add(map);
		}
		TrTaskH trTaskHUpdPolo = null;
		for (int k = 0; k < pilotingTask.size(); k++) {
			Long uuidTaskHPiloting = Long.valueOf((((Map) pilotingTask.get(k)).get("d0")).toString());
			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] { { Restrictions.eq("uuidTaskH", uuidTaskHPiloting) } });
			if (trTaskH.getPromiseDate() != null) {
				return;
			}
			this.getManagerDAO().fetch(trTaskH.getMsStatustask());
			if (GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION.equals(trTaskH.getMsStatustask().getStatusCode())) {
				trTaskHUpdPolo = trTaskH;
			}
			
			//skip alternate for task survey from task visit
			if( !GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName()) 
					&& StringUtils.isNotBlank(trTaskH.getTaskIdPolo()) 
					&& isSurveyFromVisit(String.valueOf(trTaskH.getUuidTaskH()))) {
					continue;
			}
			//end alternate for task survey from task visit
			
			updateTaskToUnassignCS(trTaskH, date, userCMO, msBranch, uuidMsSub, callerId);
		}
		
		if(StringUtils.isNotBlank(trTaskHUpdPolo.getTaskIdPolo()) || StringUtils.isNotBlank(trTaskHUpdPolo.getOrderNoCae())) {
			intFormLogic.updateDataPolo(null, trTaskHUpdPolo, null, "UNASSIGNED", "T", null, null, null, null, callerId);
		}
	}
	
	private void updateTaskToUnassign(TrTaskH trTaskH, Date date, AmMsuser userCMO, MsBranch msBranch, AmMssubsystem uuidMsSub, AuditContext callerId) {
		long uuidProcess = this.getUuidProcess(trTaskH, trTaskH.getMsStatustask().getAmMssubsystem());
		String statusCode = wfEngineLogic.reverseCurrentTask(uuidProcess, trTaskH.getUuidTaskH());
		MsStatustask msStatustask = this.commonLogic.retrieveStatusTask(statusCode, uuidMsSub.getUuidMsSubsystem(), callerId);
		if(msStatustask != null) {
			trTaskH.setMsStatustask(msStatustask);
		    trTaskH.setDtmUpd(date);
		    trTaskH.setUsrUpd("SYSTEM");
		    trTaskH.setAmMsuser(userCMO);
		    trTaskH.setMsBranch(msBranch);
		    trTaskH.setIsUnassign("1");
		    trTaskH.setOpsiPenanganan(null);
			this.getManagerDAO().update(trTaskH);
			
			String fullName = StringUtils.EMPTY;
			if (null != userCMO) {
				fullName = userCMO.getFullName();
			}
			  
			// Update status task when task unassigned
			if (StringUtils.isNotBlank(trTaskH.getOrderId())) {
				UpdateStatusMainDealerResponse updStatusTaskMainDealer = this.intFormLogic.updateStatusTaskMainDealer(trTaskH, callerId);
				if (!"00".equals(updStatusTaskMainDealer.getResponseCode())) {
					throw new RemoteException(updStatusTaskMainDealer.getResponseMessage());
				}
			}
			
			insertTaskHistory(callerId, msStatustask, trTaskH, "Task Unassign by Unassign Job", GlobalVal.CODE_PROCESS_UNASSIGNED, fullName);
		}
	}
	
	private void updateTaskToUnassignCS(TrTaskH trTaskH, Date date, AmMsuser userCMO, MsBranch msBranch, AmMssubsystem uuidMsSub, AuditContext callerId) {
		long uuidProcess = this.getUuidProcess(trTaskH, trTaskH.getMsStatustask().getAmMssubsystem());
		String statusCode = wfEngineLogic.reverseCurrentTask(uuidProcess, trTaskH.getUuidTaskH());
		MsStatustask msStatustask = this.commonLogic.retrieveStatusTask(statusCode, uuidMsSub.getUuidMsSubsystem(), callerId);
		if(msStatustask != null) {
			trTaskH.setMsStatustask(msStatustask);
		    trTaskH.setDtmUpd(date);
		    trTaskH.setUsrUpd("SYSTEM");
		    trTaskH.setAmMsuser(userCMO);
		    trTaskH.setMsBranch(msBranch);
		    trTaskH.setIsUnassign("1");
			this.getManagerDAO().update(trTaskH);
			
			String fullName = StringUtils.EMPTY;
			if (null != userCMO) {
				fullName = userCMO.getFullName();
			}
			  
			// Update status task when task unassigned
			if (StringUtils.isNotBlank(trTaskH.getOrderId())) {
				UpdateStatusMainDealerResponse updStatusTaskMainDealer = this.intFormLogic.updateStatusTaskMainDealer(trTaskH, callerId);
				if (!"00".equals(updStatusTaskMainDealer.getResponseCode())) {
					throw new RemoteException(updStatusTaskMainDealer.getResponseMessage());
				}
			}
			
			insertTaskHistory(callerId, msStatustask, trTaskH, "Task Unassign by Unassign Job", GlobalVal.CODE_PROCESS_UNASSIGNED, fullName);
		}
	}

	@Transactional
	@Override
	public String getFieldPersonCaeFromTaskVisit(String taskIdPolo, String uuidMsUser, String uuidTaskVisit, AuditContext callerId) {
		AmGeneralsetting jobVisit = this.getManagerDAO().selectOne(AmGeneralsetting.class, new Object[][] {{Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_ROLE_TASK_POLO)}});
		String jobCode = jobVisit != null? jobVisit.getGsValue():StringUtils.EMPTY;
		
		if(StringUtils.isBlank(jobCode)) {
			return null;
		}
		
		BigInteger groupTaskId = (BigInteger) this.getManagerDAO().selectOneNativeString("SELECT GROUP_TASK_ID FROM MS_GROUPTASK WITH(NOLOCK) WHERE UUID_TASK_H = :uuidTaskH ", new Object[][] {{"uuidTaskH", uuidTaskVisit}});
		
		TblPoloData poloData = this.getManagerDAO().selectOne(TblPoloData.class, new Object[][] {{Restrictions.eq("taskIdPolo", taskIdPolo)}, {Restrictions.eq("groupTaskId", groupTaskId.longValue())}});
		AddTaskPoloRequest poloReq = new Gson().fromJson(poloData.getJsonRequest(), AddTaskPoloRequest.class);
		String[] listJenisTask = jobCode.split(";");
		for (int i = 0; i < listJenisTask.length; i++) {
			String[] settingRole = listJenisTask[i].split("=");
			if (settingRole[0].equalsIgnoreCase(poloReq.getJenisTask())) {
				jobCode = settingRole[1];
				break;
			}
		}
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT UNIQUE_ID FROM AM_MSUSER AMU WITH(NOLOCK) ");
		query.append(" JOIN MS_JOB MJ WITH(NOLOCK) ON MJ.UUID_JOB = AMU.UUID_JOB ");
		query.append(" WHERE AMU.UUID_MS_USER = :uuidMsUser ");
		
		Object jobParamVisit = null;
		if (jobCode.contains(",")) {
			List jobAssign = Arrays.asList(StringUtils.split(jobCode, ","));
			query.append("AND MJ.JOB_CODE in ( :jobCode )");
			jobParamVisit = jobAssign;
		} else {
			query.append("AND MJ.JOB_CODE = :jobCode ");
			jobParamVisit = jobCode;
		}
		String uniqueId = (String) this.getManagerDAO().selectOneNativeString(query.toString(), new Object[][] {{"uuidMsUser", uuidMsUser}, {"jobCode", jobParamVisit}});
		
		if (StringUtils.isNotBlank(uniqueId)) {
			StringBuilder queryGetUserRoleCae = new StringBuilder();
			queryGetUserRoleCae.append(" SELECT TOP 1 LOGIN_ID FROM AM_MSUSER AMU WITH(NOLOCK) ");
			queryGetUserRoleCae.append(" JOIN MS_JOB MJ WITH(NOLOCK) ON MJ.UUID_JOB = AMU.UUID_JOB ");
			queryGetUserRoleCae.append(" WHERE AMU.UNIQUE_ID = :uniqueId ");
			queryGetUserRoleCae.append(" AND AMU.IS_ACTIVE = '1' ");
			
			Object jobParamCae = null;
			AmGeneralsetting jobCae = this.getManagerDAO().selectOne(AmGeneralsetting.class, new Object[][] {{Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_ROLE_TASK_CAE)}});
			String jobCodeCae = jobCae != null? jobCae.getGsValue():StringUtils.EMPTY;
			if (jobCodeCae.contains(",")) {
				List jobAssign = Arrays.asList(StringUtils.split(jobCodeCae, ","));
				queryGetUserRoleCae.append("AND MJ.JOB_CODE in ( :jobCode )");
				jobParamCae = jobAssign;
			} else {
				queryGetUserRoleCae.append("AND MJ.JOB_CODE = :jobCode ");
				jobParamCae = jobCodeCae;
			}
			
			return (String) this.getManagerDAO().selectOneNativeString(queryGetUserRoleCae.toString(), new Object[][] {{"uniqueId", uniqueId}, {"jobCode", jobParamCae}});
		}
		
		return null;
	}
}