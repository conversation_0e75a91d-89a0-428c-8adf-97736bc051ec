package com.adins.mss.businesslogic.impl.common;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType.Status;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.TaskListLogic;
import com.adins.mss.businesslogic.api.survey.InquiryTaskSurveyLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.FormException;
import com.adins.mss.exceptions.LoginException;
import com.adins.mss.exceptions.LoginException.Reason;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.FinalTrTaskhistory;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.AssignTaskListBean;
import com.adins.mss.services.model.common.GetInquiryDetailAnswer;
import com.adins.mss.services.model.common.GetInquiryDetailBean;
import com.adins.mss.services.model.common.GetInquiryDetailHistory;
import com.adins.mss.services.model.common.GetListInquiryHeaderBean;
import com.adins.mss.services.model.common.GetListInquiryHeaderRequest;
import com.adins.mss.services.model.common.RetrieveTaskPoBeanRequest;
import com.adins.mss.services.model.common.TaskGroupListMT;
import com.adins.mss.services.model.common.TaskListBean;
import com.adins.mss.services.model.common.TaskListMTResponse;
import com.adins.mss.services.model.common.TaskListPoBean;
import com.adins.mss.services.model.common.TaskStatusBean;
import com.adins.mss.services.model.common.TaskUpdateBean;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.gson.Gson;

@SuppressWarnings({"unchecked", "rawtypes"})
public class GenericTaskListLogic extends BaseLogic implements TaskListLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericTaskListLogic.class);
	
	public static final String[] LIST_SEARCH_PARAMS = { 
	/*0*/	"customerName",
	/*1*/	"branchId",
	/*2*/	"customerAddress",
	/*3*/	"applNo",
	/*4*/	"fullName",
	/*5*/	"assignDateStart", 
	/*6*/	"assignDateEnd", 
	/*7*/	"submitDateStart", 
	/*8*/	"submitDateEnd",
	/*9*/	"retrieveDateStart",
	/*10*/	"retrieveDateEnd",
	/*11*/	"branchIdLogin",
	/*12*/	"statusId",
	/*13*/	"subsystemId",
	/*14*/	"priorityId",
	/*15*/	"formId",
	/*16*/	"currentDate",
	/*17*/	"start",
	/*18*/	"end",
	/*19*/	"odr",
	/*20*/	"sendDateStart",
	/*21*/	"sendDateEnd",
	/*22*/	"uuidUser",
	/*23*/  "promiseDateStart",
	/*24*/	"promiseDateEnd"
	};

	private GeolocationLogic geocoder;
	private CommonLogic commonLogic;
	private MessageSource messageSource;
	private InquiryTaskSurveyLogic inquiryTaskSurveyLogic;

	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setInquiryTaskSurveyLogic(
			InquiryTaskSurveyLogic inquiryTaskSurveyLogic) {
		this.inquiryTaskSurveyLogic = inquiryTaskSurveyLogic;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public List<TaskListBean> getTaskListByUuid(AuditContext callerId) {
		AmMsuser loginUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		this.getManagerDAO().fetch(loginUser.getMsBranch());
		
		Map parameters = (Map) callerId.getParameters();
		String buildVersion = ((String)parameters.get("applicationVersion")).split("-")[0];
		if("1".equals(loginUser.getMsBranch().getIsPiloting())) {
			String gsValPiloting = (String)this.getManagerDAO().selectOneNativeString("SELECT GS_VALUE FROM AM_GENERALSETTING WITH(NOLOCK) WHERE GS_CODE = 'MS_PRM07_VERS_P' ", null);
			if(StringUtils.isNotBlank(gsValPiloting)) {
				List buildVersionPilotingList = Arrays.asList(gsValPiloting.split(";"));
				if(!buildVersionPilotingList.contains(buildVersion)) {
					String gsValNonPiloting = (String)this.getManagerDAO().selectOneNativeString("SELECT GS_VALUE FROM AM_GENERALSETTING WITH(NOLOCK) WHERE GS_CODE = 'MS_PRM07_VERS' ", null);
					List buildVersionList = Arrays.asList(gsValNonPiloting.split(";"));
					if(buildVersionList.contains(buildVersion)) {
						throw new LoginException("Need Update APK Using Piloting Version", Reason.LOGIN_REQUIRED);
					}
				}
			}else {
				throw new LoginException("Need Update APK Using Piloting Version", Reason.LOGIN_REQUIRED);
			}
		}
		
		String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
		if (!"1".equals(loginUser.getIsActive())) {
			throw new LoginException(this.messageSource.getMessage("businesslogic.login.inactiveadmin",
					new Object[] {loginUser.getLoginId()}, this.retrieveLocaleAudit(callerId)),
					Reason.LOGIN_INACTIVE);	
		}
		
		String[][] params = { {"uuidLoginUser", callerId.getCallerId()}, {"end", currentDate + " 23:59:59.997"} };
		List listResult = this.getManagerDAO().selectAllNative("services.common.task.getListTaskList", params, null);
	
		if (listResult == null || listResult.isEmpty()) {
			return Collections.emptyList();
		}

		List<TaskListBean> listTask = new ArrayList<>();
		Iterator itr = listResult.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			TaskListBean tlb = new TaskListBean();
			tlb.setUuidTaskH(mp.get("d0").toString());
			tlb.setCustomerName((String) mp.get("d1"));
			tlb.setCustomerPhone((String) mp.get("d2"));
			tlb.setCustomerAddress((String) mp.get("d3"));
			tlb.setNotes((String) mp.get("d4"));
			tlb.setZipCode((String) mp.get("d5"));
			tlb.setLatitude(String.valueOf((BigDecimal) mp.get("d6")));
			tlb.setLongitude(String.valueOf((BigDecimal) mp.get("d7")));
			if (mp.get("d8") != null) {
				tlb.setAssignmentDate((Date) mp.get("d8"));
			}
			tlb.setPriority((String) mp.get("d9"));
			tlb.setSchemeId(mp.get("d10").toString());
			if (mp.get("d11") != null) {
				tlb.setFormLastUpdate((Date) mp.get("d11"));
			}
			tlb.setApplNo((String) mp.get("d12"));
			Integer isVerification = (Integer) mp.get("d13");
			tlb.setIsVerification(isVerification.toString());
			tlb.setIsPreviewServer((String) mp.get("d14"));
			if (mp.get("d15") != null) {
				tlb.setDtmcrt((Date) mp.get("d15"));
			}
			tlb.setIsPrintable((String) mp.get("d16"));
			tlb.setTaskId(mp.get("d17").toString());
			if (mp.get("d21") != null) {
				tlb.setPtsDate((Date) mp.get("d21"));
			}
			tlb.setFormVersion(String.valueOf(mp.get("d22")));
			tlb.setOd(String.valueOf(mp.get("d23")));
			tlb.setInstNo(String.valueOf(mp.get("d24")));
			tlb.setAmtDue(String.valueOf(mp.get("d25")));
			
			if (mp.get("d26") != null) {
				tlb.setPromisedDate((Date) mp.get("d26"));
			}
			
			tlb.setKeluranhan("");
			if (mp.get("d27") != null) {
				tlb.setKeluranhan((String) mp.get("d27"));
			}
			
			if (mp.get("d28") != null) {
				tlb.setIsRevisit((String) mp.get("d28"));
			}
			
			if (mp.get("d29") != null) {
				tlb.setVisitType((String) mp.get("d29"));
			}
			
			if (mp.get("d30") != null) {
				tlb.setStatusFollowup((String) mp.get("d30"));
			}
			
			if (null != mp.get("d31")) {
				tlb.setStatus((String) mp.get("d31"));
			}
			
			if (null != mp.get("d32")) {
				tlb.setIsPilotingCae((String) mp.get("d32"));
			}
			
			if (null != mp.get("d33")) {
				tlb.setNotesCrm((String) mp.get("d33"));
			}
			if (null != mp.get("d34")) {
				tlb.setIsPreApproval((Integer) mp.get("d34"));
			} else {
				tlb.setIsPreApproval(0);
			}
			if (null != mp.get("d35")) {
				tlb.setMandatoryLevel((Integer) mp.get("d35"));
			} else {
				tlb.setMandatoryLevel(0);
			}
			if (null != mp.get("d36")) {
				tlb.setSourceData((String) mp.get("d36"));
			}
			if (null != mp.get("d37")) {
				tlb.setProductName((String) mp.get("d37"));
			}
			
			if (null != mp.get("d38")) {
				tlb.setJenisAsset((String) mp.get("d38"));
			}
			
			listTask.add(tlb);
		}
			
		return listTask;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public List<TaskStatusBean> getTaskStatusList(AuditContext callerId, List<TaskStatusBean> listTaskStatus) {
		List<TaskStatusBean> listTaskStatusUpdate = new ArrayList<TaskStatusBean>();
		
		Map<String, Object> mapTaskStatus = new HashMap<String, Object>();
		
		if (listTaskStatus == null || listTaskStatus.isEmpty()) {
			return Collections.emptyList();
		}
		
		for(TaskStatusBean taskStatus : listTaskStatus){
			new HashMap<String, Object>();
			mapTaskStatus.put(taskStatus.getUuidTaskH(), taskStatus);
		}
		
		Object[][] params = { {"uuidTaskH", mapTaskStatus.keySet()}};
		List listResult = this.getManagerDAO().selectAllNative("services.common.task.getTaskStatusList", params, null);
		

		Iterator itr = listResult.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			
			TaskStatusBean updateTaskStatus = new TaskStatusBean();
			
			TaskStatusBean taskStatus = (TaskStatusBean) mapTaskStatus.get(mp.get("d0").toString());
			
			updateTaskStatus.setUuidTaskH(mp.get("d0").toString());
			
			if(mp.get("d1") != null && (taskStatus.getApplNo() == null || !taskStatus.getApplNo().equals(mp.get("d1").toString()))){
				updateTaskStatus.setApplNo(mp.get("d1").toString());
			}
			if(mp.get("d2") != null && (taskStatus.getStatusApplication() == null || !taskStatus.getStatusApplication().equals(mp.get("d2").toString()))){
				updateTaskStatus.setStatusApplication(mp.get("d2").toString());
			}
			if(mp.get("d3") != null && (taskStatus.getIsSentConfins() == null || !taskStatus.getIsSentConfins().equals(mp.get("d3").toString()))){
				updateTaskStatus.setIsSentConfins(mp.get("d3").toString());
			}
			
			listTaskStatusUpdate.add(updateTaskStatus);
		}
		
		return listTaskStatusUpdate;
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public List<TaskUpdateBean> getTaskUpdateListByUuid(AuditContext callerId) {
		AmMsuser loginUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		this.getManagerDAO().fetch(loginUser.getMsBranch());
	
		String[][] params = { {"uuidLoginUser", callerId.getCallerId()}};
		List listResult = this.getManagerDAO().selectAllNative("services.common.task.getListTaskUpdateList", params, null);
	
		if (listResult == null || listResult.isEmpty()) {
			return Collections.emptyList();
		}

		List<TaskUpdateBean> listTask = new ArrayList<TaskUpdateBean>();
		Iterator itr = listResult.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			TaskUpdateBean tu = new TaskUpdateBean();
			
			tu.setUuidTaskupdate(mp.get("d0").toString());
			tu.setUuidUser(mp.get("d1").toString());
			tu.setCustomerName((String) mp.get("d2"));
			tu.setCustomerPhone((String) mp.get("d3"));
			tu.setCustomerAddress((String) mp.get("d4"));
			tu.setNotes((String) mp.get("d5"));
			tu.setAssignmentDate((Date) mp.get("d6"));
			tu.setApplNo((String) mp.get("d7"));
			tu.setUuidScheme(mp.get("d8").toString());
			tu.setFormName((String) mp.get("d9"));
			tu.setPendingNotes((String) mp.get("d10"));
			tu.setDocuproFeedback((String) mp.get("d11"));
			tu.setCategory((String) mp.get("d12"));
			tu.setSubCategory((String) mp.get("d13"));
			tu.setReasonDetail((String) mp.get("d14"));
			tu.setValidasi((String) mp.get("d15"));
			tu.setIsPreApproval((Integer) mp.get("d16"));
			listTask.add(tu);
		}
			
		return listTask;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public TaskListMTResponse getTaskListByUuidMT(AuditContext callerId) {
		TaskListMTResponse listGroupTaskResponse = new TaskListMTResponse();
		Status status = new Status();
		
		List<TaskGroupListMT> listGroupTaskListMT = new ArrayList<TaskGroupListMT>();

		AmMsuser loginUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));

		if (!"1".equals(loginUser.getIsActive())) {
			throw new LoginException(this.messageSource.getMessage("businesslogic.login.inactiveadmin",
					new Object[] {loginUser.getLoginId()}, this.retrieveLocaleAudit(callerId)),
					Reason.LOGIN_INACTIVE);
		}
		
		String paramGetLastAttendance[][] = {{"userId", callerId.getCallerId()}};
		Date lastAttendance = (Date)this.getManagerDAO().selectOneNative(
				"services.common.user.getLastAttendance", paramGetLastAttendance);
				
		if (null == lastAttendance) {
			status.setMessage(this.messageSource.getMessage("businesslogic.absensi.needattendance", 
					null, this.retrieveLocaleAudit(callerId)));
			
			listGroupTaskResponse.setListGroupTaskList(Collections.EMPTY_LIST);
			listGroupTaskResponse.setStatus(status);
			return listGroupTaskResponse;
		}
		
		String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
		String lastAttendanceDate = DateFormatUtils.format(lastAttendance, "yyyy-MM-dd");
		if (!lastAttendanceDate.equalsIgnoreCase(currentDate)) {
			status.setMessage(this.messageSource.getMessage("businesslogic.absensi.needattendance", 
					null, this.retrieveLocaleAudit(callerId)));
			
			listGroupTaskResponse.setListGroupTaskList(Collections.EMPTY_LIST);
			listGroupTaskResponse.setStatus(status);
			return listGroupTaskResponse;
		}
		
		String[][] params = { { "uuidLoginUser", callerId.getCallerId() }, 
				{"start", lastAttendanceDate+" 00:00:00"},
				{"end", lastAttendanceDate+" 23:59:59"},
				{"lastAttendance", lastAttendanceDate}};
		List listResultGroupTaskId = this.getManagerDAO().selectAllNative(
				"services.common.task.getListGroupTaskIdMT", params, null);
		List<String> listGroupTaskId = new ArrayList<String>();
		
		Iterator itrtr = listResultGroupTaskId.iterator();
		while (itrtr.hasNext()) {
			Map map = (Map) itrtr.next();
			listGroupTaskId.add((String) map.get("d0"));
		}

		if (listResultGroupTaskId != null && !listResultGroupTaskId.isEmpty()) {
			for (int i = 0; i < listGroupTaskId.size(); i++) {
				TaskGroupListMT taskListMTResponse = new TaskGroupListMT();
				List<TaskListBean> listTaskListBean = new ArrayList<TaskListBean>();

				String[][] paramss = {
						{ "uuidLoginUser", callerId.getCallerId() },
						{ "groupTaskId", listGroupTaskId.get(i) } };
				List listResult = this.getManagerDAO().selectAllNative(
						"services.common.task.getListTaskListMT", paramss, null);

				String groupSeq = null, groupTaskId = null;
				Iterator itr = listResult.iterator();
				while (itr.hasNext()) {
					Map mp = (Map) itr.next();
					TaskListBean tlb = new TaskListBean();
					tlb.setUuidTaskH(mp.get("d0").toString());
					tlb.setCustomerName((String) mp.get("d1"));
					tlb.setCustomerAddress((String) mp.get("d2"));
					tlb.setAssignmentDate((Date) mp.get("d3"));
					tlb.setDtmcrt((Date) mp.get("d4"));
					tlb.setLatitude(mp.get("d5").toString());
					tlb.setLongitude(mp.get("d6").toString());
					tlb.setPriority((String) mp.get("d7"));
					tlb.setTaskSeq((String) mp.get("d8"));
					tlb.setSchemeId((String) mp.get("d9"));
					tlb.setTaskId((String) mp.get("d10"));
					tlb.setNotes((String) mp.get("d11"));
					tlb.setIsPrintable((String) mp.get("d12"));
					tlb.setCustomerPhone((String) mp.get("d15"));
					tlb.setUuidLocation((mp.get("d16") == null) ? null : mp.get("d16").toString());
					listTaskListBean.add(tlb);
					groupTaskId = (String) mp.get("d13");
					groupSeq = (String) mp.get("d14");				
				}
				taskListMTResponse.setGroupSeq(groupSeq);
				taskListMTResponse.setGroupTaskId(groupTaskId);
				taskListMTResponse.setListTaskList(listTaskListBean);
				listGroupTaskListMT.add(taskListMTResponse);
			}
		}		

		listGroupTaskResponse.setListGroupTaskList(listGroupTaskListMT);
		listGroupTaskResponse.setStatus(status);
		return listGroupTaskResponse;
	}

	private void updateDownloadDate(long uuidTaskH, Date startDtm, Date downloadDate, AuditContext callerId) {
		if (startDtm == null && downloadDate == null) {
			MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_DOWNLOADED, callerId);
			Object[][] queryParams = { { "downloadDate", new Date() },
					{ "uuidTaskH", uuidTaskH }, { "statusMobile", msm.getStatusMobileSeqNo() } };
			this.getManagerDAO().updateNativeString(
					"UPDATE tr_task_h SET download_date = :downloadDate, STATUS_MOBILE_SEQ_NO = :statusMobile WHERE uuid_task_h = :uuidTaskH",
					queryParams);
		}
	}

	@Transactional(readOnly = true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<AssignTaskListBean> getHeaderTaskListAssign(Date startDate,
			Date endDate, AuditContext callerId) {
		List<AssignTaskListBean> listTask = new ArrayList<AssignTaskListBean>();

		Object[][] params = { { "uuidUser", callerId.getCallerId() } };

		List listResult = this.getManagerDAO().selectAllNative(
				"services.common.task.getListTaskListAssign", params, null);

		if (listResult != null && !listResult.isEmpty()) {
			Iterator itr = listResult.iterator();
			while (itr.hasNext()) {
				Map mp = (Map) itr.next();
				AssignTaskListBean tlb = new AssignTaskListBean();
				tlb.setKey(mp.get("d0").toString());
				tlb.setValue((String) mp.get("d1"));
				tlb.setFlag(mp.get("d2").toString());
				tlb.setFormName((String) mp.get("d3"));
				listTask.add(tlb);
			}
		}

		return listTask;
	}

	@Transactional(readOnly = true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<AssignTaskListBean> getHeaderTaskListReAssign(Date startDate,
			Date endDate, AuditContext callerId) {
		List<AssignTaskListBean> listTask = new ArrayList<AssignTaskListBean>();

		AmMsuser usr = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
				new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		String tmpStartDate = df.format(startDate) + " 00:00:00.000";
		String tmpEndDate = df.format(endDate) + " 23:59:59.997";
		Object[][] params = {
				{ "startDate", tmpStartDate },
				{ "endDate", tmpEndDate },
				{ "uuidBranch", usr.getMsBranch().getUuidBranch() },
				{ "uuidSubsystem",
						usr.getAmMssubsystem().getUuidMsSubsystem() } };

		List listResult = this.getManagerDAO().selectAllNative(
				"services.common.task.getListTaskListReAssign", params, null);

		if (listResult != null && !listResult.isEmpty()) {
			Iterator itr = listResult.iterator();
			while (itr.hasNext()) {
				Map mp = (Map) itr.next();
				AssignTaskListBean tlb = new AssignTaskListBean();
				tlb.setKey(mp.get("d0").toString());
				tlb.setValue((String) mp.get("d1"));
				tlb.setFlag(mp.get("d2").toString());
				tlb.setFormName((String) mp.get("d3"));
				listTask.add(tlb);
			}
		}

		return listTask;
	}

	@Transactional(readOnly = true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<AssignTaskListBean> getDetailTask(long uuidTaskH, AuditContext callerId) {				
		TrTaskH dbModel = this.getManagerDAO().selectOne(TrTaskH.class, uuidTaskH);
		this.updateDownloadDate(uuidTaskH, dbModel.getStartDtm(), dbModel.getDownloadDate(), callerId);		

		if (PropertiesHelper.isTaskDJson()) {
			return this.detailTaskFromJson(uuidTaskH, callerId);
		}
		else {
			return this.detailTaskFromRow(uuidTaskH, callerId);
		}
	}
	
	private List<AssignTaskListBean> detailTaskFromJson(long uuidTaskH, AuditContext callerId) {
		Assert.isTrue(uuidTaskH > 0L);
		
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", uuidTaskH}});
		
		if (docDb == null || StringUtils.isBlank(docDb.getDocument())){
			return Collections.emptyList();
		}
		
		Gson gson = new Gson();
		TaskDocumentBean tdb = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
		if (tdb.getAnswers() == null || tdb.getAnswers().isEmpty()){
			return Collections.emptyList();
		}

		List<AssignTaskListBean> resultDetails = new ArrayList<AssignTaskListBean>();
		List<AnswerBean> answers = tdb.getAnswers();
		for (AnswerBean answer : answers) {
			AssignTaskListBean vb = new AssignTaskListBean();
			vb.setKey(answer.getQuestion().getLabel());
			
			String answerType = answer.getQuestion().getAnswerTypeCode();
			if (MssTool.isChoiceQuestion(answerType) && answer.getIntOptAnswers() != null) {
				vb.setFlag("0");
				
				for (int i = 0; i < answer.getIntOptAnswers().size(); i++) {
					if (i > 0) {
						try {
							AssignTaskListBean cloneBean = (AssignTaskListBean) BeanUtils.cloneBean(vb);
							cloneBean.setValue(answer.getIntOptAnswers().get(i).getDesc());
							resultDetails.add(cloneBean);
						} 
						catch (IllegalAccessException | InstantiationException | InvocationTargetException
								| NoSuchMethodException e) {
							LOG.error("Error on cloning multipleAnswer", e);
						}
					}
					else {
						vb.setValue(answer.getIntOptAnswers().get(i).getDesc());
						resultDetails.add(vb);
					}
				}
				
				continue;
			}
			else if (MssTool.isImageQuestion(answerType) ||
					GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType)) {				
				if (MssTool.isImageQuestion(answerType)) {
					vb.setFlag("3");
				}
				
				if (answer.getLobAnswer() != null && answer.getLobAnswer().isHasImage()) {
					vb.setValue(String.valueOf(answer.getQuestion().getUuidQuestion()));
				}
				
				if (GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType)) {				
					com.adins.mss.model.taskdjson.LocationBean location = answer.getLocation();
					if (null != location && null != location.getLat() && null != location.getLng()) {
						vb.setValue(StringUtils.join(location.getLat(), ",", location.getLng()));
					}
					else if (location != null) {
						LocationBean bean = new LocationBean();
						bean.setMcc(location.getMcc());
						bean.setMnc(location.getMnc());
						bean.setLac(location.getLac());
						bean.setCellid(location.getCid());
						bean = this.processLocation(bean, callerId);
						if (bean.getCoordinate() != null) {
							vb.setValue(bean.getCoordinate().getLatitude() + "," + bean.getCoordinate().getLongitude());
						}
						else {
							vb.setValue("");
						}
					}
					else {
						vb.setValue("");
					}
					
					if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType)) {
						vb.setFlag("2");
					}
					else { //image with Geodata/GPS
						vb.setFlag("1");
					}
				}
			}
			else {
				vb.setFlag("0");
				vb.setValue(StringUtils.stripToEmpty(answer.getIntTxtAnswer()));
			}
			
			resultDetails.add(vb);
		}
		
		return resultDetails;
	}
	
	private List<AssignTaskListBean> detailTaskFromRow(long uuidTaskH, AuditContext callerId) {	
		Object[][] params = { { "uuidTaskH", uuidTaskH } };
		List listResult = this.getManagerDAO().selectAllNative("services.common.task.getDetailTask", params, null);

		if (listResult == null || listResult.isEmpty()) {
			return Collections.emptyList();
		}
		
		List<AssignTaskListBean> listTask = new ArrayList<AssignTaskListBean>();
		Iterator itr = listResult.iterator();
		while (itr.hasNext()) {
			Map mp = (Map) itr.next();
			AssignTaskListBean tlb = new AssignTaskListBean();
			tlb.setKey((String) mp.get("d0"));
			tlb.setValue((String) mp.get("d1"));
			tlb.setFlag((String) mp.get("d2"));
			if ("2".equals(tlb.getFlag())) {
				String[] val = tlb.getValue().split(",");
				if (val.length > 2) {
					LocationBean bean = new LocationBean();
					bean.setMcc(Integer.parseInt(val[0]));
					bean.setMnc(Integer.parseInt(val[1]));
					bean.setLac(Integer.parseInt(val[2]));
					bean.setCellid(Integer.parseInt(val[3]));
					bean = this.processLocation(bean, callerId);
					if (bean.getCoordinate() != null) {
						tlb.setValue(bean.getCoordinate().getLatitude() + "," + bean.getCoordinate().getLongitude());
					}
					else {
						tlb.setValue("");
					}
				}
			}
			else if ("3".equals(tlb.getFlag())) {
				TrTaskdetaillob trTaskdetaillob = this.getManagerDAO().selectOne(
						"from TrTaskdetaillob ttd join fetch ttd.trTaskH tth join fetch ttd.msQuestion mq join fetch mq.msAnswertype "
							+ "where tth.uuidTaskH = :uuidTaskH and mq.uuidQuestion = :uuidQuestion", 
						new Object[][] {{"uuidTaskH", uuidTaskH}, {"uuidQuestion", tlb.getValue()}});
				if (GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA.equals(trTaskdetaillob.getMsQuestion().getMsAnswertype().getCodeAnswerType())
						|| GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA.equals(trTaskdetaillob.getMsQuestion().getMsAnswertype().getCodeAnswerType())) {
					if ("1".equals(trTaskdetaillob.getIsGps())
							&& null != trTaskdetaillob.getLatitude()
							&& null != trTaskdetaillob.getLongitude()) {
						tlb.setValue(trTaskdetaillob.getLatitude() + "," + trTaskdetaillob.getLongitude());
					}
					else {
						LocationBean bean = new LocationBean();
						bean.setMcc(trTaskdetaillob.getMcc());
						bean.setMnc(trTaskdetaillob.getMnc());
						bean.setLac(trTaskdetaillob.getLac());
						bean.setCellid(trTaskdetaillob.getCellId());
						bean = this.processLocation(bean, callerId);
						if (bean.getCoordinate() != null) {
							tlb.setValue(bean.getCoordinate().getLatitude() + "," + bean.getCoordinate().getLongitude());
						}
						else {
							tlb.setValue("");
						}
					}
				}
			}
			listTask.add(tlb);
		}
		
		return listTask;
	}

	public LocationBean processLocation(LocationBean bean, AuditContext callerId) {
		List<LocationBean> listLocations = new ArrayList<LocationBean>();
		listLocations.add(bean);
		this.geocoder.geocodeCellId(listLocations, callerId);

		return bean;
	}

	@Transactional(readOnly = true)
	@Override
	public List<AssignTaskListBean> getUserAssign(long uuidTaskH,
			AuditContext callerId) {
		List<AssignTaskListBean> listTask = new ArrayList<AssignTaskListBean>();

		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(callerId.getCallerId()));
		Object[][] params = {
				{ "uuidLoginUser", amMsuser.getUuidMsUser() },
				{ "uuidBranchLogin", amMsuser.getMsBranch().getUuidBranch() } };

		List listResult = this.getManagerDAO().selectAllNative(
				"services.common.task.getUserAssign", params, null);

		if (listResult != null && !listResult.isEmpty()) {
			Iterator itr = listResult.iterator();
			while (itr.hasNext()) {
				Map mp = (Map) itr.next();
				AssignTaskListBean tlb = new AssignTaskListBean();
				tlb.setKey(mp.get("d0").toString());
				tlb.setValue((String) mp.get("d1"));
				tlb.setFlag((String) mp.get("d2"));
				listTask.add(tlb);
			}
		}

		return listTask;
	}

	@Transactional(readOnly = true)
	@Override
	public List<AssignTaskListBean> getUserReAssign(long uuidTaskH, AuditContext callerId) {
		List<AssignTaskListBean> listTask = new ArrayList<AssignTaskListBean>();

		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTaskH);

		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(callerId.getCallerId()));
		Object[][] params = {
				{ "uuidLoginUser", callerId.getCallerId() },
				{ "uuidOldUserAssign", trTaskH.getAmMsuser().getUuidMsUser() },
				{ "uuidBranchLogin", amMsuser.getMsBranch().getUuidBranch() } };

		List listResult = this.getManagerDAO().selectAllNative(
				"services.common.task.getReUserAssign", params, null);

		if (listResult != null && !listResult.isEmpty()) {
			Iterator itr = listResult.iterator();
			while (itr.hasNext()) {
				Map mp = (Map) itr.next();
				AssignTaskListBean tlb = new AssignTaskListBean();
				tlb.setKey(mp.get("d0").toString());
				tlb.setValue((String) mp.get("d1"));
				tlb.setFlag((String) mp.get("d2"));
				listTask.add(tlb);
			}
		}

		return listTask;
	}

	@Transactional
	@Override
	public Map getListInquiryHeader(GetListInquiryHeaderRequest request, AuditContext callerId) {
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		
		Object[][] params = new Object[25][2];
		
		params[0][0] = LIST_SEARCH_PARAMS[0];
		params[0][1] = request.getSearchBy();
		
		//advanced search by branch
//		if ("".equals(this.getSearchings().get(0).getVal(1)) ) {
			params[1][0] = LIST_SEARCH_PARAMS[1];
			params[1][1] = "%";
//		}
		
		if ("0".equals(request.getFilterSearchBy())) {
			//advanced search by CustomerName
			params[2][0] = LIST_SEARCH_PARAMS[2];
			params[2][1] = "%";
			params[0][0] = LIST_SEARCH_PARAMS[0];
			params[0][1] = request.getSearchBy();
			params[3][0] = LIST_SEARCH_PARAMS[3];
			params[3][1] = "%";
			params[4][0] = LIST_SEARCH_PARAMS[4];
			params[4][1] = "%";
		} 
		else if ("1".equals(request.getFilterSearchBy())) {
		//advanced search by Application
			params[0][0] = LIST_SEARCH_PARAMS[0];
			params[0][1] = "%";
			params[2][0] = LIST_SEARCH_PARAMS[2];
			params[2][1] = "%";
			params[3][0] = LIST_SEARCH_PARAMS[3];
			params[3][1] = request.getSearchBy();
			params[4][0] = LIST_SEARCH_PARAMS[4];
			params[4][1] = "%";
		} 
		else if ("2".equals(request.getFilterSearchBy())) {
		//advanced search by FieldPerson
			params[0][0] = LIST_SEARCH_PARAMS[0];
			params[0][1] = "%";
			params[2][0] = LIST_SEARCH_PARAMS[2];
			params[2][1] = "%";
			params[3][0] = LIST_SEARCH_PARAMS[3];
			params[3][1] = "%";
			params[4][0] = LIST_SEARCH_PARAMS[4];
			params[4][1] = request.getSearchBy();
		} 
		else {
			LOG.info("Searching type = {} ", request.getSearchBy());
		}
		
		String dateStart = StringUtils.EMPTY;
		String dateEnd = StringUtils.EMPTY;
		
		try {
			if (!"%".equals(request.getDateStart())) {
				dateStart = DateFormatUtils.format(DateUtils.parseDate(request.getDateStart(), "ddMMyyyy"), "yyyy-MM-dd") +" 00:00:00.000";
				dateEnd = DateFormatUtils.format(DateUtils.parseDate(request.getDateEnd(), "ddMMyyyy"), "yyyy-MM-dd") +" 23:59:59.997";
			}
		} catch (Exception e) {
			throw new FormException(e.getMessage(), e, com.adins.mss.exceptions.FormException.Reason.INVALID_SCRIPT);
		}
		
		if ("0".equals(request.getFilterDateLov())) {
			//advanced search by AssignmentDate
			params[5][0] = LIST_SEARCH_PARAMS[5];
			if(StringUtils.isNotBlank(dateStart))
				params[5][1] = dateStart;
			else
				params[5][1] = "%";
			
			params[6][0] = LIST_SEARCH_PARAMS[6];
			if(StringUtils.isNotBlank(dateEnd))
				params[6][1] = dateEnd;
			else
				params[6][1] = "%";
			params[7][0] = LIST_SEARCH_PARAMS[7];
			params[7][1] = "%";
			params[8][0] = LIST_SEARCH_PARAMS[8];
			params[8][1] = "%";
			params[9][0] = LIST_SEARCH_PARAMS[9];
			params[9][1] = "%";
			params[10][0] = LIST_SEARCH_PARAMS[10];
			params[10][1] = "%";
			params[20][0] = LIST_SEARCH_PARAMS[20];
			params[20][1] = "%";
			params[21][0] = LIST_SEARCH_PARAMS[21];
			params[21][1] = "%";
			params[23][0] = LIST_SEARCH_PARAMS[23];
			params[23][1] = "%";
			params[24][0] = LIST_SEARCH_PARAMS[24];
			params[24][1] = "%";
		}
		else if ("1".equals(request.getFilterDateLov())) {
		//advanced search by SubmitDate
			params[5][0] = LIST_SEARCH_PARAMS[5];
			params[5][1] = "%";
			params[6][0] = LIST_SEARCH_PARAMS[6];
			params[6][1] = "%";
			params[7][0] = LIST_SEARCH_PARAMS[7];
			if(StringUtils.isNotBlank(dateStart))
				params[7][1] = dateStart;
			else
				params[7][1] = "%";
			
			params[8][0] = LIST_SEARCH_PARAMS[8];
			if(StringUtils.isNotBlank(dateEnd))
				params[8][1] = dateEnd;
			else
				params[8][1] = "%";
			params[9][0] = LIST_SEARCH_PARAMS[9];
			params[9][1] = "%";
			params[10][0] = LIST_SEARCH_PARAMS[10];
			params[10][1] = "%";
			params[20][0] = LIST_SEARCH_PARAMS[20];
			params[20][1] = "%";
			params[21][0] = LIST_SEARCH_PARAMS[21];
			params[21][1] = "%";
			params[23][0] = LIST_SEARCH_PARAMS[23];
			params[23][1] = "%";
			params[24][0] = LIST_SEARCH_PARAMS[24];
			params[24][1] = "%";
		}
		else if ("2".equals(request.getFilterDateLov())) {
			//advanced search by retrieve date
				params[5][0] = LIST_SEARCH_PARAMS[5];
				params[5][1] = "%";
				params[6][0] = LIST_SEARCH_PARAMS[6];
				params[6][1] = "%";
				params[7][0] = LIST_SEARCH_PARAMS[7];
				params[7][1] = "%";
				params[8][0] = LIST_SEARCH_PARAMS[8];
				params[8][1] = "%";
				params[9][0] = LIST_SEARCH_PARAMS[9];
				if(StringUtils.isNotBlank(dateStart))
					params[9][1] = dateStart;
				else
					params[9][1] = "%";
				
				params[10][0] = LIST_SEARCH_PARAMS[10];
				if(StringUtils.isNotBlank(dateEnd))
					params[10][1] = dateEnd;
				else 
					params[10][1] = "%";
				params[20][0] = LIST_SEARCH_PARAMS[20];
				params[20][1] = "%";
				params[21][0] = LIST_SEARCH_PARAMS[21];
				params[21][1] = "%";
				params[23][0] = LIST_SEARCH_PARAMS[23];
				params[23][1] = "%";
				params[24][0] = LIST_SEARCH_PARAMS[24];
				params[24][1] = "%";
		}
		else if ("3".equals(request.getFilterDateLov())) {
			//advanced search by send date
				params[5][0] = LIST_SEARCH_PARAMS[5];
				params[5][1] = "%";
				params[6][0] = LIST_SEARCH_PARAMS[6];
				params[6][1] = "%";
				params[7][0] = LIST_SEARCH_PARAMS[7];
				params[7][1] = "%";
				params[8][0] = LIST_SEARCH_PARAMS[8];
				params[8][1] = "%";
				params[9][0] = LIST_SEARCH_PARAMS[9];
				params[9][1] = "%";
				params[10][0] = LIST_SEARCH_PARAMS[10];
				params[10][1] = "%";
	
				params[20][0] = LIST_SEARCH_PARAMS[20];
				if(StringUtils.isNotBlank(dateStart))
					params[20][1] = dateStart;
				else
					params[20][1] = "%";
				
				params[21][0] = LIST_SEARCH_PARAMS[21];
				if(StringUtils.isNotBlank(dateEnd))
					params[21][1] = dateEnd;
				else 
					params[21][1] = "%";
				params[23][0] = LIST_SEARCH_PARAMS[23];
				params[23][1] = "%";
				params[24][0] = LIST_SEARCH_PARAMS[24];
				params[24][1] = "%";
				
			}
		else if ("4".equals(request.getFilterDateLov())) {
			//advanced search by send date
				params[5][0] = LIST_SEARCH_PARAMS[5];
				params[5][1] = "%";
				params[6][0] = LIST_SEARCH_PARAMS[6];
				params[6][1] = "%";
				params[7][0] = LIST_SEARCH_PARAMS[7];
				params[7][1] = "%";
				params[8][0] = LIST_SEARCH_PARAMS[8];
				params[8][1] = "%";
				params[9][0] = LIST_SEARCH_PARAMS[9];
				params[9][1] = "%";
				params[10][0] = LIST_SEARCH_PARAMS[10];
				params[10][1] = "%";
				params[20][0] = LIST_SEARCH_PARAMS[20];
				params[20][1] = "%";
				params[21][0] = LIST_SEARCH_PARAMS[21];
				params[21][1] = "%";
				params[23][0] = LIST_SEARCH_PARAMS[23];
				if(StringUtils.isNotBlank(dateStart))
					params[23][1] = dateStart;
				else
					params[23][1] = "%";
				
				params[24][0] = LIST_SEARCH_PARAMS[24];
				if(StringUtils.isNotBlank(dateEnd))
					params[24][1] = dateEnd;
				else 
					params[24][1] = "%";
				
		}
		else {
			LOG.info("Searching type = {} ", request.getFilterDateLov());
		}
		params[11][0] = LIST_SEARCH_PARAMS[11];
		params[11][1] = usr.getMsBranch().getUuidBranch();
		
		params[12][0] = LIST_SEARCH_PARAMS[12];
		if(!StringUtils.equals("%", request.getTaskStatus())){
			Object [][] prmStatusTask = {{"statusCode", request.getTaskStatus()},
					{"subsystem", usr.getAmMssubsystem().getUuidMsSubsystem()}};
			String uuidStatusTask = String.valueOf(
					this.getManagerDAO().selectOneNativeString("select UUID_STATUS_TASK from MS_STATUSTASK where STATUS_CODE = :statusCode"
							+ " and UUID_MS_SUBSYSTEM = :subsystem", prmStatusTask)
					);
			params[12][1] = uuidStatusTask;
		}
		else {
			params[12][1] = request.getTaskStatus();
		}
		
		params[13][0] = LIST_SEARCH_PARAMS[13];
		params[13][1] = usr.getAmMssubsystem().getUuidMsSubsystem();
		
//		this.getSearchings().get(0).setVal(9,this.getLoginBean().getBean().getMsBranch().getUuidBranch());
		
		
		int	pageNumber = request.getPageNumber();
		int pageSize = GlobalVal.ROW_PER_PAGE;
		
		params[14][0] = LIST_SEARCH_PARAMS[14];
		if(!StringUtils.equals("%", request.getPriority())){
			Object [][] prmPriority = {{"priorityDesc", request.getPriority()}};
			String uuidPriority = String.valueOf(
					this.getManagerDAO().selectOneNativeString("select uuid_priority from ms_priority "
							+ "where UPPER(priority_desc) like UPPER('%'+:priorityDesc+'%')", prmPriority)
					);
			params[14][1] = uuidPriority;
		} else {
			params[14][1] = request.getPriority();
		}
		
		params[15][0] = LIST_SEARCH_PARAMS[15];
		params[15][1] = request.getForm();
		
		params[16][0] = LIST_SEARCH_PARAMS[16];
		params[16][1] = DateFormatUtils.format(new Date(), "yyyy-MM-dd")+" 23:59:59.997";
		
		params[17][0] = LIST_SEARCH_PARAMS[17];
		params[17][1] = String.valueOf((pageNumber-1)*pageSize+1);
		
		params[18][0] = LIST_SEARCH_PARAMS[18];
		params[18][1] = String.valueOf((pageNumber-1)*pageSize+pageSize);
		
//		this.getSearchings().get(0).setVal(10, String.valueOf((pageNumber-1)*pageSize+1));
//		this.getSearchings().get(0).setVal(11, String.valueOf((pageNumber-1)*pageSize+pageSize));
//		String[][] params = resolveFilters(0, LIST_SEARCH_PARAMS); 
		
//		if (this.form.getOrderBy() != null && !"0".equals(this.form.getOrderBy()) ){
//			params[19][0] = LIST_SEARCH_PARAMS[19];
//			params[19][1] = this.form.getOrderBy()+this.form.getTypeOrder();
//		}
//		else {
			params[19][0] = LIST_SEARCH_PARAMS[19];
			params[19][1] = "0";
//		}
		
		Object[][] paramsCnt = new Object[20][2];
		for (int i = 0; i < 17; i++) {
			paramsCnt[i][0] = params[i][0];
			paramsCnt[i][1] = params[i][1];
		}	
		
		params[22][0]= LIST_SEARCH_PARAMS[22];
		params[22][1] = usr.getUuidMsUser();
		
		paramsCnt[17][0] = params[22][0];
		paramsCnt[17][1] = params[22][1];
		
		paramsCnt[18][0] = params[20][0];
		paramsCnt[18][1] = params[20][1];
		
		paramsCnt[19][0] = params[21][0];
		paramsCnt[19][1] = params[21][1];
		
		List<Map<String, Object>> list = inquiryTaskSurveyLogic.listInquiryTaskSurveyNativeString(params, callerId);
		Integer cnt = inquiryTaskSurveyLogic.countListInquiryTaskSurveyNativeString(paramsCnt, callerId);
		
		List<GetListInquiryHeaderBean> listTask = new ArrayList<GetListInquiryHeaderBean>();
		
		for (Map<String, Object> mp : list) {
			GetListInquiryHeaderBean obj = new GetListInquiryHeaderBean();
			obj.setUuidTaskH(mp.get("d0").toString());
			obj.setCustomerName((String) mp.get("d1"));
			obj.setApplicationId((String) mp.get("d2"));
			obj.setAssignDate((String) mp.get("d3"));
			obj.setRetrieveDate((String) mp.get("d4"));
			obj.setSubmitDate((String) mp.get("d5"));
			obj.setSurveyor((String) mp.get("d6"));
			obj.setStatus((String) mp.get("d7"));
			obj.setSendDate((String) mp.get("d8"));
			obj.setBranchName((String) mp.get("d9"));
			obj.setFlag((String) mp.get("d10"));
			obj.setPromisedDate((String) mp.get("d11"));
			obj.setTaskId((String) mp.get("d12"));
			listTask.add(obj);
		}
		
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("list", listTask);
		result.put("cnt", cnt);
		
		return result;
	}
	
	@Transactional
	@Override
	public Map getListInquiryDetail(String uuidTaskH, String flag, AuditContext callerId) {
		Map mapResult = new HashMap();
		try {
			GetInquiryDetailBean detailHeader = new GetInquiryDetailBean();
			List<GetInquiryDetailAnswer> listAnswer = new ArrayList<GetInquiryDetailAnswer>();
			List<GetInquiryDetailHistory> listHistory = new ArrayList<>();
			
			Object[] detailTask = null;
			List<TrTaskBean> listTaskAnswer = new ArrayList<TrTaskBean>();
			List<Map<String, Object>> listTaskHistory = new ArrayList<Map<String, Object>>();
			
			Object[][] params ={{"paramUuidTaskH", Long.valueOf(uuidTaskH)}};
			
			if ("2".equals(flag)) {
				detailTask = inquiryTaskSurveyLogic.getDetailTaskSurveyFinal(params, callerId);
				listTaskAnswer = inquiryTaskSurveyLogic.getInquiryTaskSurveyDetailAnswerHistoryFinal(params, null, null, null, callerId);
				listTaskHistory = inquiryTaskSurveyLogic.getInquiryTaskSurveyDetailTaskHistoryFinal(params, null, callerId);
			} else {
				detailTask = inquiryTaskSurveyLogic.getDetailTaskSurvey(params, StringUtils.EMPTY, callerId);
				listTaskAnswer = inquiryTaskSurveyLogic.getInquiryTaskSurveyDetailAnswerHistory(params, null, null, null, callerId);
				listTaskHistory = inquiryTaskSurveyLogic.getInquiryTaskSurveyDetailTaskHistory(params, null, callerId);
			}
			
			
			detailHeader.setUuidTaskH(detailTask[0].toString());
			detailHeader.setTaskId((String) detailTask[1]);
			detailHeader.setApplicationId((String) detailTask[2]);
			detailHeader.setFormName((String) detailTask[3]);
			detailHeader.setBranchName((String) detailTask[4]);
			detailHeader.setPriority((String) detailTask[5]);
			detailHeader.setNotes((String) detailTask[6]);
			detailHeader.setName((String) detailTask[7]);
			detailHeader.setAddress((String) detailTask[8]);
			detailHeader.setPhone((String) detailTask[9]);
			detailHeader.setZipcode((String) detailTask[10]);
			detailHeader.setAssignmentDate((String) detailTask[11]);
			detailHeader.setRetrieveDate((String) detailTask[12]);
			detailHeader.setSubmittedDate((String) detailTask[13]);
			detailHeader.setFieldPersonName((String) detailTask[14]);
			detailHeader.setTaskStatus((String) detailTask[15]);
			detailHeader.setSendDate((String) detailTask[17]);
			detailHeader.setPromisedDate((String) detailTask[18]);
			
			if (!listTaskAnswer.isEmpty()) {
				TrTaskBean obj = listTaskAnswer.get(0);
				detailHeader.setLatBranch(obj.getLatBranch());
				detailHeader.setLongBranch(obj.getLngBranch());
			}
			
			String oldLabel = StringUtils.EMPTY;
			
			for (TrTaskBean bean : listTaskAnswer) {
				GetInquiryDetailAnswer obj = new GetInquiryDetailAnswer();
				String textAns = StringUtils.EMPTY;
				String intTextAns = StringUtils.EMPTY;
				
				if (oldLabel.equals(bean.getQuestionText()) && 
						(GlobalVal.ANSWER_TYPE_MULTIPLE.equals(bean.getMsQuestion().getMsAnswertype().getCodeAnswerType()) ||
								GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(bean.getMsQuestion().getMsAnswertype().getCodeAnswerType()) ||
								GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(bean.getMsQuestion().getMsAnswertype().getCodeAnswerType())) ) {
					GetInquiryDetailAnswer lastObj = listAnswer.get(listAnswer.size()-1);
					if (StringUtils.isNotBlank(bean.getOptionText())) {
						textAns = "[" + bean.getOptionText() + "]" + " " + bean.getTextAnswer();
						lastObj.setTextAnswer(lastObj.getTextAnswer() + "\n" + textAns);
					}
					if (StringUtils.isNotBlank(bean.getIntOptionText())) {
						intTextAns = "[" + bean.getIntOptionText() + "]" + " " + bean.getIntTextAnswer();
						lastObj.setIntTextAnswer(lastObj.getIntTextAnswer() + "\n" + intTextAns);
					}
				} else {
					String uuidTaskD = String.valueOf(bean.getUuidTaskD());
					if ("1".equals(bean.getHasImage())) {
						// UUID_TASK_D#FLAG_FINAL#IS_REJECTED
						uuidTaskD = String.valueOf(bean.getUuidTaskD()) + "#" + flag + "#" + "0";
					}
					obj.setUuidTaskD(uuidTaskD);
					obj.setLatitude(bean.getLat());
					obj.setLongitude(bean.getLng());
//					obj.setLatBranch(bean.getLatBranch());
//					obj.setLongBranch(bean.getLngBranch());
					obj.setIsAsset(bean.getIsAsset());
					obj.setDistance(bean.getDistance());
					obj.setHasImage(bean.getHasImage());
					obj.setQuestionLabel(bean.getQuestionText());
					obj.setCodeAnswerType(bean.getMsQuestion().getMsAnswertype().getCodeAnswerType());
					obj.setAccuracy(String.valueOf(bean.getAccuracy()));
					if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_LU_OFFLINE.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_LU_OFFLINE_WITH_SUGGESTION.equals(obj.getCodeAnswerType()) ) {
						if (StringUtils.isNotBlank(bean.getOptionText())) {
							textAns = "[" + bean.getOptionText() + "]" + " " + bean.getTextAnswer();
						}
						if (StringUtils.isNotBlank(bean.getIntOptionText())) {
							intTextAns = "[" + bean.getIntOptionText() + "]" + " " + bean.getIntTextAnswer();
						}
						obj.setTextAnswer(textAns);
						obj.setIntTextAnswer(intTextAns);
					} else {
						obj.setTextAnswer( (StringUtils.isNotBlank(bean.getOptionText())) ? "[" + bean.getOptionText() + "]" : bean.getTextAnswer() );
						obj.setIntTextAnswer( (StringUtils.isNotBlank(bean.getIntOptionText())) ? "[" + bean.getIntOptionText() + "]" : bean.getIntTextAnswer() );
					}
					listAnswer.add(obj);
				}
				
				oldLabel = bean.getQuestionText();
			}
			
			for (Map<String, Object> mp : listTaskHistory) {
				GetInquiryDetailHistory obj = new GetInquiryDetailHistory();
				obj.setUuidTaskH(mp.get("d10")+"");
				obj.setTaskProcess((String) mp.get("d1"));
				obj.setActor((String) mp.get("d2"));
				obj.setFieldPerson((String) mp.get("d3"));
				String date = StringUtils.EMPTY;
				if (null != mp.get("d4")) {
					Date d = (Date) mp.get("d4");
					date = DateFormatUtils.format(d, "dd-MMM-yyyy HH:mm");
				}
				obj.setDate(date);
				obj.setNotes((String) mp.get("d5"));
				obj.setStatus((String) mp.get("d6"));
				obj.setCodeProcess((String) mp.get("d7"));
				obj.setFlag((String) mp.get("d8"));
				listHistory.add(obj);
			}
			
			mapResult.put("detailHeader", detailHeader);
			mapResult.put("listAnswer", listAnswer);
			mapResult.put("listHistory", listHistory);
		} catch (Exception e) {
			throw new FormException(e.getMessage(), e, com.adins.mss.exceptions.FormException.Reason.INVALID_SCRIPT);
		}
		
		return mapResult;
	}
	
	@Transactional
	@Override
	public Map getListInquiryDetailHistory(String uuidTaskHistory, String flag, String codeProcess, AuditContext callerId) {
		Map mapResult = new HashMap();
		try {
			List<GetInquiryDetailAnswer> listAnswer = new ArrayList<GetInquiryDetailAnswer>();
			
			List<TrTaskBean> listTaskAnswer = new ArrayList<TrTaskBean>();
			
			if ("2".equals(flag)) {
				FinalTrTaskhistory taskHist = this.getManagerDAO().selectOne(FinalTrTaskhistory.class, Long.valueOf(uuidTaskHistory));
				String uuidTaskRejectedHistory = "";
				if (null != taskHist.getFinalTrTaskrejectedhistory()) {
					uuidTaskRejectedHistory = taskHist.getFinalTrTaskrejectedhistory().getUuidTaskRejectedHistory()+"";
				}
				
				String taskH = inquiryTaskSurveyLogic.getTaskByHistroyFinal(uuidTaskHistory, callerId);
				if (null == taskH) {
					taskH = taskHist.getFinalTrTaskH().getUuidTaskH()+"";
				}
				
				Object[][] params ={{"paramUuidTaskH", taskH}};
				listTaskAnswer = inquiryTaskSurveyLogic.getInquiryTaskSurveyDetailAnswerHistoryFinal(params, null, codeProcess, uuidTaskRejectedHistory, callerId);
			} else {
				TrTaskhistory taskHist = this.getManagerDAO().selectOne(TrTaskhistory.class, Long.valueOf(uuidTaskHistory));
				String uuidTaskRejectedHistory = "";
				if (null != taskHist.getTrTaskrejectedhistory()) {
					uuidTaskRejectedHistory = taskHist.getTrTaskrejectedhistory().getUuidTaskRejectedHistory()+"";
				}
				
				String taskH = inquiryTaskSurveyLogic.getTaskByHistroy(uuidTaskHistory, callerId);
				if (null == taskH) {
					taskH = taskHist.getTrTaskH().getUuidTaskH()+"";
				}
				
				Object[][] params ={{"paramUuidTaskH", taskH}};
				listTaskAnswer = inquiryTaskSurveyLogic.getInquiryTaskSurveyDetailAnswerHistory(params, null, codeProcess, uuidTaskRejectedHistory, callerId);
			}
			
			String oldLabel = StringUtils.EMPTY;
			
			for (TrTaskBean bean : listTaskAnswer) {
				GetInquiryDetailAnswer obj = new GetInquiryDetailAnswer();
				String textAns = StringUtils.EMPTY;
				String intTextAns = StringUtils.EMPTY;
				
				if (oldLabel.equals(bean.getQuestionText()) && 
						(GlobalVal.ANSWER_TYPE_MULTIPLE.equals(bean.getMsQuestion().getMsAnswertype().getCodeAnswerType()) ||
								GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(bean.getMsQuestion().getMsAnswertype().getCodeAnswerType()) ||
								GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(bean.getMsQuestion().getMsAnswertype().getCodeAnswerType())) ) {
					GetInquiryDetailAnswer lastObj = listAnswer.get(listAnswer.size()-1);
					if (StringUtils.isNotBlank(bean.getOptionText())) {
						textAns = "[" + bean.getOptionText() + "]" + " " + bean.getTextAnswer();
						lastObj.setTextAnswer(lastObj.getTextAnswer() + "\n" + textAns);
					}
					if (StringUtils.isNotBlank(bean.getIntOptionText())) {
						intTextAns = "[" + bean.getIntOptionText() + "]" + " " + bean.getIntTextAnswer();
						lastObj.setIntTextAnswer(lastObj.getIntTextAnswer() + "\n" + intTextAns);
					}
				} else {
					String uuidTaskD = String.valueOf(bean.getUuidTaskD());
					if ("1".equals(bean.getHasImage())) {
						// UUID_TASK_D#FLAG_FINAL#IS_REJECTED
						uuidTaskD = String.valueOf(bean.getUuidTaskD()) + "#" + flag + "#" + "1";
					}
					obj.setUuidTaskD(uuidTaskD);
					obj.setLatitude(bean.getLat());
					obj.setLongitude(bean.getLng());
//					obj.setLatBranch(bean.getLatBranch());
//					obj.setLongBranch(bean.getLngBranch());
					obj.setIsAsset(bean.getIsAsset());
					obj.setDistance(bean.getDistance());
					obj.setHasImage(bean.getHasImage());
					obj.setQuestionLabel(bean.getQuestionText());
					obj.setCodeAnswerType(bean.getMsQuestion().getMsAnswertype().getCodeAnswerType());
					obj.setAccuracy(String.valueOf(bean.getAccuracy()));
					if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_LU_OFFLINE.equals(obj.getCodeAnswerType()) ||
							GlobalVal.ANSWER_TYPE_LU_OFFLINE_WITH_SUGGESTION.equals(obj.getCodeAnswerType()) ) {
						if (StringUtils.isNotBlank(bean.getOptionText())) {
							textAns = "[" + bean.getOptionText() + "]" + " " + bean.getTextAnswer();
						}
						if (StringUtils.isNotBlank(bean.getIntOptionText())) {
							intTextAns = "[" + bean.getIntOptionText() + "]" + " " + bean.getIntTextAnswer();
						}
						obj.setTextAnswer(textAns);
						obj.setIntTextAnswer(intTextAns);
					} else {
						obj.setTextAnswer( (StringUtils.isNotBlank(bean.getOptionText())) ? "[" + bean.getOptionText() + "]" : bean.getTextAnswer() );
						obj.setIntTextAnswer( (StringUtils.isNotBlank(bean.getIntOptionText())) ? "[" + bean.getIntOptionText() + "]" : bean.getIntTextAnswer() );
					}
					listAnswer.add(obj);
				}
				oldLabel = bean.getQuestionText();
			}
			
			mapResult.put("detailHeader", null);
			mapResult.put("listAnswer", listAnswer);
			mapResult.put("listHistory", null);
		} catch (Exception e) {
			throw new FormException(e.getMessage(), e, com.adins.mss.exceptions.FormException.Reason.INVALID_SCRIPT);
		}
		
		return mapResult;
	}

	@Transactional(readOnly=true)
	@Override
	public List<TaskListPoBean> getTaskListPo(AuditContext callerId) {
		List<TaskListPoBean> listTaskPo = new ArrayList<>();
		
		Object[][] prmListTaskPo = { {"uuidMsUser", callerId.getCallerId()} };
		StringBuilder queryGetListTaskPo = new StringBuilder();
		queryGetListTaskPo.append("SELECT TTH.UUID_TASK_H, CONVERT(DATETIME, STPE.PO_EXP_DATE), STPE.PO_STAT  ");
		queryGetListTaskPo.append("FROM   STG_TASK_PO_EXP STPE WITH (NOLOCK) ");
		queryGetListTaskPo.append("JOIN   TR_TASK_H TTH WITH (NOLOCK) ON TTH.UUID_TASK_H = STPE.TASK_ID ");
		queryGetListTaskPo.append("WHERE  TTH.UUID_MS_USER = :uuidMsUser ");
		
		List<Map<String, Object>> listData = this.getManagerDAO().selectAllNativeString(queryGetListTaskPo.toString(), prmListTaskPo);
		if (null != listData && !listData.isEmpty()) {
			for (int i = 0; i < listData.size(); i++) {
				Map<String, Object> mapData = listData.get(i);
				
				TaskListPoBean bean = new TaskListPoBean();
				bean.setUuidTaskH(mapData.get("d0").toString());
				bean.setExpiredDate((Date) mapData.get("d1"));
				bean.setStatusPo(mapData.get("d2").toString());
				
				listTaskPo.add(bean);
			}
		}
		
		return listTaskPo;
	}

	@Transactional(readOnly=true)
	@Override
	public List<TaskListBean> retrieveTaskPo(List<RetrieveTaskPoBeanRequest> listUuidTaskH, AuditContext callerId) {
		List<TaskListBean> taskListPo = new ArrayList<>();
		if (null != listUuidTaskH && !listUuidTaskH.isEmpty()) {
			List<Long> listTaskH = new ArrayList<>();
			for (int i = 0; i < listUuidTaskH.size(); i++) {
				RetrieveTaskPoBeanRequest bean = listUuidTaskH.get(i);
				
				listTaskH.add(Long.valueOf(bean.getUuidTaskH()));
			}
			Object[][] prmTaskListPo = { {"listUuidTaskH", listTaskH} };
			
			StringBuilder queryListPo = new StringBuilder();
			queryListPo.append("SELECT  tskh.UUID_TASK_H as uuidTaskH, ");
			queryListPo.append("        tskh.CUSTOMER_NAME as customerName, ");
			queryListPo.append("        tskh.CUSTOMER_PHONE as customerPhone, ");
			queryListPo.append("        tskh.CUSTOMER_ADDRESS as customerAddress, ");
			queryListPo.append("        tskh.NOTES as notes, ");
			queryListPo.append("        tskh.ZIP_CODE as zipCode, ");
			queryListPo.append("        tskh.LATITUDE as latitude, ");
			queryListPo.append("        tskh.LONGITUDE as longitude, ");
			queryListPo.append("        tskh.ASSIGN_DATE as assignmentDate, ");
			queryListPo.append("        mp.PRIORITY_DESC as priority, ");
			queryListPo.append("        tskh.UUID_FORM as schemeId, ");
			queryListPo.append("        msf.FORM_LAST_UPDATE as formLastUpdate, ");
			queryListPo.append("        tskh.APPL_NO as applNo, ");
			queryListPo.append("        dbo.F_IS_VERIFICATION(tskh.UUID_TASK_H) as isVerification, ");
			queryListPo.append("        case when msf.PREPROCESSING_SP is null then '0' else '1' end as isPreviewServer, ");
			queryListPo.append("        tskh.DTM_CRT as dtmcrt, ");
			queryListPo.append("        tskh.TASK_ID as taskId, ");
			queryListPo.append("        tskh.PTS_DATE AS ptsDate, ");
			queryListPo.append("        tskh.FORM_VERSION as formVersion, ");
			queryListPo.append("        tskh.PROMISE_DATE as promisedDate, ");
			queryListPo.append("        tskh.KELURAHAN as keluranhan, ");
			queryListPo.append("        ISNULL(tskh.IS_REVISIT, '0') as isRevisit, ");
			queryListPo.append("        tskh.VISIT_TYPE as visitType, ");
			queryListPo.append("        tskh.STATUS_FOLLOW_UP as statusFollowUp, ");
			queryListPo.append("        mst.STATUS_TASK_DESC as status, ");
			queryListPo.append("        tskh.SUBMIT_DATE as submitDate ");
			queryListPo.append("FROM    TR_TASK_H tskh WITH (NOLOCK) ");
			queryListPo.append("JOIN    MS_FORM msf WITH (NOLOCK) ON tskh.uuid_form = msf.uuid_form ");
			queryListPo.append("JOIN    MS_STATUSTASK mst WITH (NOLOCK) ON tskh.uuid_status_task = mst.uuid_status_task ");
			queryListPo.append("JOIN    MS_PRIORITY mp WITH (NOLOCK) on mp.uuid_priority = tskh.uuid_priority ");
			queryListPo.append("WHERE   tskh.UUID_TASK_H IN (:listUuidTaskH) ");
			
			List<Map<String, Object>> listData = this.getManagerDAO().selectAllNativeString(queryListPo.toString(), prmTaskListPo);
			if (null == listData || listData.isEmpty()) {
				return Collections.emptyList();
			}
			
			Iterator itr = listData.iterator();
			while (itr.hasNext()) {
				Map mp = (Map) itr.next();

				TaskListBean tlb = new TaskListBean();
				tlb.setUuidTaskH(mp.get("d0").toString());
				tlb.setCustomerName((String) mp.get("d1"));
				tlb.setCustomerPhone((String) mp.get("d2"));
				tlb.setCustomerAddress((String) mp.get("d3"));
				tlb.setNotes((String) mp.get("d4"));
				tlb.setZipCode((String) mp.get("d5"));
				tlb.setLatitude(String.valueOf(mp.get("d6")));
				tlb.setLongitude(String.valueOf(mp.get("d7")));
				if (null != mp.get("d8")) {
					tlb.setAssignmentDate((Date) mp.get("d8"));
				}
				tlb.setPriority((String) mp.get("d9"));
				tlb.setSchemeId(mp.get("d10").toString());
				if (null != mp.get("d11")) {
					tlb.setFormLastUpdate((Date) mp.get("d11"));
				}
				tlb.setApplNo((String) mp.get("d12"));
				Integer isVerification = (Integer) mp.get("d13");
				tlb.setIsVerification(isVerification.toString());
				tlb.setIsPreviewServer((String) mp.get("d14"));
				if (null != mp.get("d15")) {
					tlb.setDtmcrt((Date) mp.get("d15"));
				}
				tlb.setTaskId(mp.get("d16").toString());
				if (null != mp.get("d17")) {
					tlb.setPtsDate((Date) mp.get("d17"));
				}
				tlb.setFormVersion(String.valueOf(mp.get("d18")));				
				if (null != mp.get("d19")) {
					tlb.setPromisedDate((Date) mp.get("d19"));
				}				
				tlb.setKeluranhan("");
				if (null != mp.get("d20")) {
					tlb.setKeluranhan((String) mp.get("d20"));
				}				
				if (null != mp.get("d21")) {
					tlb.setIsRevisit((String) mp.get("d21"));
				}				
				if (null != mp.get("d22")) {
					tlb.setVisitType((String) mp.get("d22"));
				}				
				if (null != mp.get("d23")) {
					tlb.setStatusFollowup((String) mp.get("d23"));
				}				
				if (null != mp.get("d24")) {
					tlb.setStatus((String) mp.get("d24"));
				}
				if (null != mp.get("d25")) {
					tlb.setSubmitDate((Date) mp.get("d25"));
				}
				
				taskListPo.add(tlb);
			}
		}
		
		return taskListPo;
	}

}
