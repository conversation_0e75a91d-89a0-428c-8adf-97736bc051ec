package com.adins.mss.businesslogic.impl.common;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.Reader;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Clob;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Stack;
import java.util.TimeZone;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.text.StrBuilder;
import org.hibernate.criterion.Restrictions;
import org.joda.time.Period;
import org.joda.time.PeriodType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.common.TaskServiceLogic;
import com.adins.mss.businesslogic.api.common.TaskVerificationLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.UnassignTaskLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.constants.enums.MobileDefAnswer;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.MsTskdistributionofmodule;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.TrTasksurveydata;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.QuestionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.AnswerItemBean;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.gson.Gson;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericTaskVerificationLogic extends BaseLogic implements
		TaskVerificationLogic, MessageSourceAware {
	private AuditInfo auditInfoH;
	private static final Logger LOG = LoggerFactory
			.getLogger(GenericTaskVerificationLogic.class);

	public GenericTaskVerificationLogic() {
		String[] pkColsH = { "uuidTaskH" };
		String[] pkDbColsH = { "UUID_TASK_H" };
		String[] colsH = { "uuidTaskH", "msStatustask.uuidStatusTask",
				"verificationDate" };
		String[] dbColsH = { "UUID_TASK_H", "UUID_STATUS_TASK",
				"VERIFICATION_DATE" };
		this.auditInfoH = new AuditInfo("TR_TASK_H", pkColsH, pkDbColsH, colsH,
				dbColsH);
	}

	@Autowired
	private GeolocationLogic geocoder;
	private IntFormLogic intFormLogic;
	private ImageStorageLogic imageStorageLogic;

	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}

	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
		this.imageStorageLogic = imageStorageLogic;
	}

	@Autowired
	private CommonLogic commonLogic;

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	@Autowired
	private TaskDistributionLogic taskDistributionLogic;

	@Autowired
	private UnassignTaskLogic unassignTaskLogic;

	@Autowired
	private TaskServiceLogic taskServiceLogic;

	public void setTaskDistributionLogic(TaskDistributionLogic taskDistributionLogic) {
		this.taskDistributionLogic = taskDistributionLogic;
	}
	
	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	public void setUnassignTaskLogic(UnassignTaskLogic unassignTaskLogic) {
		this.unassignTaskLogic = unassignTaskLogic;
	}

	public void setTaskServiceLogic(TaskServiceLogic taskServiceLogic) {
		this.taskServiceLogic = taskServiceLogic;
	}

	@Override
	public TrTaskH getTask(long uuidTaskH, AuditContext callerId) {
		TrTaskH resultTask = this.getManagerDAO().selectOne(
				"from TrTaskH tth " + "join fetch tth.msBranch mb "
				+ "join fetch tth.msForm mf "
				+ "join fetch tth.amMsuser amu "
				+ "join fetch tth.msPriority mp "
				+ "join fetch tth.msStatustask ms "
				+ "join fetch ms.amMssubsystem ams "
				+ "where tth.uuidTaskH = :uuidTaskH",
				new Object[][] { { "uuidTaskH", uuidTaskH } });
		if (resultTask != null) {
			if (StringUtils.isEmpty(resultTask.getApplNo())) {
				resultTask.setApplNo("-");
			}
			if (StringUtils.isEmpty(resultTask.getResult())) {
				resultTask.setResult("-");
			}
			if (StringUtils.isEmpty(resultTask.getNotes())) {
				resultTask.setNotes("-");
			}
		}
		return resultTask;
	}

	@Override
	public String getStatusId(Object[][] statusParams, AuditContext auditContext) {
		MsStatustask statusTask = this.getManagerDAO().selectOne(
				MsStatustask.class, statusParams);
		String statusUuid = String.valueOf(statusTask.getUuidStatusTask());

		return statusUuid;
	}

	@Override
	public List<Long> getUserList(long loginUser, AuditContext callerId) {
		Object[][] paramUser = { { "parentUser", loginUser } };
		List uuidUserList = new ArrayList<>();
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"task.verification.listUser", paramUser, null);

		for (int i = 0; i < list.size(); i++) {
			Map<?, ?> temp = (Map<?, ?>) list.get(i);
			if (null != temp.get("d0").toString()) {
				uuidUserList.add(Long.valueOf(temp.get("d0").toString()));
			}
		}

		return uuidUserList;
	}

	@Override
	public Long[] getBranchByLogin(long uuidBranch, AuditContext callerId) {
		Object[][] paramBranch = { { "branchId", uuidBranch } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"task.verification.getBranchListCombo", paramBranch, null);

		Long[] uuidBranches = new Long[list.size() + 1];
		for (int i = 0; i < list.size(); i++) {
			Map<?, ?> temp = (Map<?, ?>) list.get(i);
			uuidBranches[i] = Long.valueOf(String.valueOf(temp.get("d0")));
		}
		return uuidBranches;
	}

	@Override
	public Map<String, String> getBranchListCombo(String branchId,
			AuditContext callerId) {
		Map<String, String> result = Collections.emptyMap();
		String[][] params = { { "branchId", branchId } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"task.verification.getBranchListCombo", params, null);
		Map<String, String> comboList = new HashMap<String, String>();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				Map<?, ?> temp2 = (Map<?, ?>) list.get(i);
				comboList.put(String.valueOf(temp2.get("d0")), (String) temp2.get("d2") + " - "
								+ (String) temp2.get("d1"));
			}
			result = sortByValues(comboList);
		} 
		else {
			result.put("%", "ALL");
		}
		return result;
	}

	Map<String, String> sortByValues(Map<String, String> comboList) {
		List list = new LinkedList(comboList.entrySet());

		Collections.sort(list, new Comparator() {
			public int compare(Object o1, Object o2) {
				return ((Comparable) ((Map.Entry) (o1)).getValue())
						.compareTo(((Map.Entry) (o2)).getValue());
			}
		});

		Map sortedHashMap = new LinkedHashMap();
		sortedHashMap.put("%", "ALL");
		for (Iterator it = list.iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			sortedHashMap.put(entry.getKey(), entry.getValue());
		}
		return sortedHashMap;
	}

	@Override
	public Map<String, Object> listTask(String uuidForm, String dateStart, String dateEnd,
			int dateType, String branchAdvSearch, String custNameSearch,
			String custAddressSearch, String applNoSearch,
			String fieldPersonSearch, String uuidStatus, List<Long> userUuids,
			Long[] branchHiearchy, String mode, int pageNumber, int pageSize,
			AuditContext auditContext) {
		Map<String, Object> paramMap = new HashMap<>();
		Map<String, Object> result = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		try {
			if (("0".equals(uuidForm) || uuidForm == null || "".equals(uuidForm))) {

			} 
			else {
				condition.append(" and tth.msForm.uuidForm=:uuidForm");
				paramMap.put("uuidForm", Long.valueOf(uuidForm));
			}

			if (StringUtils.isNotBlank(branchAdvSearch)) {
				if ("".equals(custNameSearch) && !("%").equals(branchAdvSearch)) {
					condition.append(" and tth.msBranch.uuidBranch=:branchAdvSearch");
					paramMap.put("branchAdvSearch",	Long.valueOf(branchAdvSearch));
				}
			}

			if (dateStart != null && !("").equals(dateStart)) {
				DateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss.SSS");
				Date formatDateStart= format.parse(dateStart + " 00:00:00.000");
				Date formatDateEnd = format.parse(dateEnd + " 23:59:59.997");
				if (dateType == 0) {
					// if search by assignment date
					if (StringUtils.isNotBlank(applNoSearch) && !("%").equals(applNoSearch)) {
						condition.append(" and tth.applNo like :applNo");
						paramMap.put("applNo", "%" + applNoSearch + "%");
					}
					condition.append(" and tth.customerName like :customerName");
					condition.append(" and tth.customerAddress like :customerAddress");
					condition.append(" and tth.amMsuser.fullName like :fieldPersonSearch");
					condition.append(" and tth.assignDate between :dateStart and :dateEnd");
					condition.append(" and tth.msStatustask.uuidStatusTask = :uuidStatus");
					paramMap.put("customerName", "%" + custNameSearch + "%");
					paramMap.put("customerAddress", "%" + custAddressSearch	+ "%");
					paramMap.put("fieldPersonSearch", "%" + fieldPersonSearch + "%");
					paramMap.put("dateStart", formatDateStart);
					paramMap.put("dateEnd", formatDateEnd);
					paramMap.put("uuidStatus", Long.valueOf(uuidStatus));
					if ("branch".equalsIgnoreCase(mode)) {
						condition.append(" and tth.msBranch.uuidBranch in (:branchHiearchy)");
						paramMap.put("branchHiearchy", branchHiearchy);
					} 
					else {
						condition.append(" and tth.amMsuser.uuidMsUser in (:userUuids)");
						paramMap.put("userUuids", userUuids);
					}
				} 
				else if (dateType == 1) {
					// if search by submit date
					if (StringUtils.isNotBlank(applNoSearch) && !("%").equals(applNoSearch)) {
						condition.append(" and tth.applNo like :applNo");
						paramMap.put("applNo", "%" + applNoSearch + "%");
					}

					condition.append(" and tth.customerName like :customerName");
					condition.append(" and tth.customerAddress like :customerAddress");
					condition.append(" and tth.amMsuser.fullName like :fieldPersonSearch");
					condition.append(" and tth.submitDate between :dateStart and :dateEnd");
					condition.append(" and tth.msStatustask.uuidStatusTask = :uuidStatus");
					paramMap.put("customerName", "%" + custNameSearch + "%");
					paramMap.put("customerAddress", "%" + custAddressSearch + "%");
					paramMap.put("fieldPersonSearch", "%" + fieldPersonSearch + "%");
					paramMap.put("dateStart", formatDateStart);
					paramMap.put("dateEnd", formatDateEnd);
					paramMap.put("uuidStatus", Long.valueOf(uuidStatus));
					if ("branch".equalsIgnoreCase(mode)) {
						condition.append(" and tth.msBranch.uuidBranch in (:branchHiearchy)");
						paramMap.put("branchHiearchy", branchHiearchy);
					} 
					else {
						condition.append(" and tth.amMsuser.uuidMsUser in (:userUuids)");
						paramMap.put("userUuids", userUuids);
					}
				}
			} 
			else if (("".equals(dateStart) || (dateStart == null))) {
				// if seach by date not selected
				if (StringUtils.isNotBlank(applNoSearch) && !("%").equals(applNoSearch)) {
					condition.append(" and tth.applNo like :applNo");
					paramMap.put("applNo", "%" + applNoSearch + "%");
				}

				condition.append(" and tth.customerName like :customerName");
				condition.append(" and tth.customerAddress like :customerAddress");
				condition.append(" and tth.amMsuser.fullName like :fieldPersonSearch");
				condition.append(" and tth.msStatustask.uuidStatusTask = :uuidStatus");
				paramMap.put("customerName", "%" + custNameSearch + "%");
				paramMap.put("customerAddress", "%" + custAddressSearch + "%");
				paramMap.put("fieldPersonSearch", "%" + fieldPersonSearch + "%");
				paramMap.put("uuidStatus", Long.valueOf(uuidStatus));
				if ("branch".equalsIgnoreCase(mode)) {
					condition.append(" and tth.msBranch.uuidBranch in (:branchHiearchy)");
					paramMap.put("branchHiearchy", branchHiearchy);
				} 
				else {
					condition.append(" and tth.amMsuser.uuidMsUser in (:userUuids)");
					paramMap.put("userUuids", userUuids);
				}
			}
			StringBuilder orderQuery = new StringBuilder();
			orderQuery.append(" order by tth.msPriority.uuidPriority asc, tth.submitDate asc");

			result = this.getManagerDAO().selectAll(
					"from TrTaskH tth " + "join fetch tth.msForm "
					+ "join fetch tth.msBranch "
					+ "join fetch tth.msStatustask "
					+ "join fetch tth.amMsuser "
					+ "join fetch tth.msPriority " + "where 1=1"
					+ condition.toString() + orderQuery.toString(),
					"select count(*) " + "from TrTaskH tth "
					+ "join tth.msForm " + "join tth.msBranch "
					+ "join tth.msStatustask " + "join tth.amMsuser "
					+ "where 1=1" + condition.toString(), paramMap,
					pageNumber, pageSize);
		} 
		catch (ParseException e) {
			LOG.error("Error on parsing search date", e);
		}
		return result;
	}

	private List getDetail(String uuidForm, String uuidTaskH,
			String uuidQuestion, AuditContext callerId) {
		String[][] params = { { "uuidTaskH", uuidTaskH }, 
				{ "uuidQuestion", uuidQuestion }, { "uuidForm", uuidForm } };
		List resultDetail = this.getManagerDAO().selectAllNative(
				"task.verification.getDetail", params, null);
		return resultDetail;
	}

	private TrTaskD getDetail(String uuidTaskD) {
		TrTaskD resultDetail = this.getManagerDAO().selectOne(
				"from TrTaskD ttd " + "left join fetch ttd.msLovByLovId ml "
				+ "join fetch ttd.msQuestion mq "
				+ "join fetch mq.msAnswertype ma "
				+ "left join fetch mq.msAssettag "
				+ "where ttd.uuidTaskD = :uuidTaskD",
				new Object[][] { { "uuidTaskD", Long.valueOf(uuidTaskD) } });
		return resultDetail;
	}

	private TrTaskD getDetailByLov(Object[][] params, AuditContext callerId) {
		BigInteger uuidTaskD = (BigInteger) this.getManagerDAO()
				.selectOneNative("task.verification.getDetailByLov", params);
		if (uuidTaskD == null) {
			return null;
		}
		TrTaskD result = this.getManagerDAO().selectOne(TrTaskD.class,
				uuidTaskD.longValue());
		return result;
	}

	@Override
	public Map<String, Object> listAnswer(TrTaskH taskH, AmMsuser userLoggedIn,
			AuditContext callerId) {
		MsFormhistory msFormhistory = this.commonLogic.retrieveMsFormhistory(
				taskH.getMsForm().getUuidForm(), taskH.getFormVersion(), callerId);
		long uuidFormHistory = msFormhistory.getUuidFormHistory();

		List<TrTaskBean> listAnswer = new ArrayList<TrTaskBean>();
		Map<String, List<String>> multiByGroup = new HashMap<String, List<String>>();
		Map<String, List<String>> multiByGroupDesc = new HashMap<String, List<String>>();

		// get uuid branch
		long uuidBranch = userLoggedIn.getMsBranch().getUuidBranch();

		boolean loadFromJson = PropertiesHelper.isTaskDJson();
		if (loadFromJson) {
			TaskDocumentBean tdb = this.retrieveTaskDJsonAsBean(taskH, callerId);
			if (tdb != null) {
				listAnswer = this.listTaskDJsonWithQuestionSet(tdb,	uuidFormHistory, callerId);

				MultipleTaskDResult multiByGroupResult = this.processMultipleAnswerInJson(listAnswer, tdb);
				multiByGroup = multiByGroupResult.getMultiByGroup();
				multiByGroupDesc = multiByGroupResult.getMultiByGroupDesc();
			}
		} 
		else {
			listAnswer = this.listTaskDWithQuestionset(taskH, userLoggedIn, uuidFormHistory, callerId);
			MultipleTaskDResult multiByGroupResult = this.removeMultipleTaskDRow(listAnswer);
			multiByGroup = multiByGroupResult.getMultiByGroup();
			multiByGroupDesc = multiByGroupResult.getMultiByGroupDesc();
		}

		Boolean hasImage = this.checkHasImage(listAnswer);
		Map<String, LinkedHashMap<String, String>> mapOptionByLov = this.prepareLov(listAnswer, uuidBranch);

		Map<String, Object> newResultAnswer = new HashMap<>();
		newResultAnswer.put(GlobalKey.MAP_RESULT_LIST, listAnswer);
		newResultAnswer.put(GlobalKey.MAP_RESULT_SIZE, listAnswer.size());
		newResultAnswer.put("otherChecked", multiByGroup);
		newResultAnswer.put("descChecked", multiByGroupDesc);
		newResultAnswer.put("hasImage", hasImage);
		newResultAnswer.put("uuidFormHistory", String.valueOf(uuidFormHistory));
		newResultAnswer.put("mapOptionByLov", mapOptionByLov);

		return newResultAnswer;
	}

	private LinkedHashMap<String, String> mapOptAnsByBranch(long uuidBranch, TrTaskD taskAnswer) {
		StringBuilder sql1 = new StringBuilder(
				"SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE ")
				.append("FROM MS_LOV t1 with (nolock) ")
				.append("left join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV ")
				.append("WHERE LOV_GROUP = :lovGroup ")
				.append("and t1.IS_DELETED = :isDeleted ")
				.append("and t1.IS_ACTIVE = :isActive ")
				.append("and mbl.UUID_LOV is null");

		StringBuilder sql2 = new StringBuilder(" UNION ")
				.append("SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE ")
				.append("FROM MS_LOV t1 with (nolock) ")
				.append("join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV ")
				.append("WHERE LOV_GROUP = :lovGroup ")
				.append("and t1.IS_DELETED = :isDeleted ")
				.append("and t1.IS_ACTIVE = :isActive ")
				.append("and mbl.UUID_BRANCH = :uuidBranch");

		Map<String, Object> params = new HashMap<>();
		params.put("lovGroup", taskAnswer.getMsQuestion().getLovGroup());
		params.put("uuidBranch", uuidBranch);
		params.put("isDeleted", "0");
		params.put("isActive", "1");

		if (taskAnswer.getMsLovByLovId() != null) {
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint1())) {
				sql1.append(" and t1.CONSTRAINT_1 IN (:constraint1, '%')");
				sql2.append(" and t1.CONSTRAINT_1 IN (:constraint1, '%')");
				params.put("constraint1", taskAnswer.getMsLovByLovId().getConstraint1());
			}
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint2())) {
				sql1.append(" and t1.CONSTRAINT_2 IN (:constraint2, '%')");
				sql2.append(" and t1.CONSTRAINT_2 IN (:constraint2, '%')");
				params.put("constraint2", taskAnswer.getMsLovByLovId().getConstraint2());
			}
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint3())) {
				sql1.append(" and t1.CONSTRAINT_3 IN (:constraint3, '%')");
				sql2.append(" and t1.CONSTRAINT_3 IN (:constraint3, '%')");
				params.put("constraint3", taskAnswer.getMsLovByLovId().getConstraint3());
			}
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint4())) {
				sql1.append(" and t1.CONSTRAINT_4 IN (:constraint4, '%')");
				sql2.append(" and t1.CONSTRAINT_4 IN (:constraint4, '%')");
				params.put("constraint4", taskAnswer.getMsLovByLovId().getConstraint4());
			}
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint5())) {
				sql1.append(" and t1.CONSTRAINT_5 IN (:constraint5, '%')");
				sql2.append(" and t1.CONSTRAINT_5 IN (:constraint5, '%')");
				params.put("constraint5", taskAnswer.getMsLovByLovId().getConstraint5());
			}
		}

		sql2.append(" ORDER BY t1.SEQUENCE");
		sql1.append(sql2.toString());
		List<?> optionAnswersList = this.getManagerDAO().selectAllNativeString(
				sql1.toString(), params);
		LinkedHashMap<String, String> optionAnswer = new LinkedHashMap<String, String>();
		if (!optionAnswersList.isEmpty()) {
			for (int x = 0; x < optionAnswersList.size(); x++) {
				Map<?, ?> map = (Map<?, ?>) optionAnswersList.get(x);
				optionAnswer.put(map.get("d0").toString(), map.get("d1").toString());
			}
		}

		return optionAnswer;
	}

	private String clobToString(java.sql.Clob data) {
		final StringBuilder sb = new StringBuilder();

		try {
			final Reader reader = data.getCharacterStream();
			final BufferedReader br = new BufferedReader(reader);

			int b;
			while (-1 != (b = br.read())) {
				sb.append((char) b);
			}

			br.close();
		} 
		catch (SQLException | IOException e) {
			throw new RuntimeException(e);
		}

		return sb.toString();
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	private void updateTaskD(TrTaskD taskDetail, AuditContext callerId) {
		taskDetail.setUsrUpd(callerId.getCallerId());
		taskDetail.setDtmUpd(new Date());

		this.getManagerDAO().update(taskDetail);
	}
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateTaskHStatus(String[] uuidAnswers, String[] textAnswers,
			String[] latAns, String[] longAns, long uuidTaskH,
			String[] uuidQuestions, AmMsuser loginBean, Integer index,
			String notes, AuditContext callerId) {
		TrTaskH taskH = (TrTaskH) this.getTask(uuidTaskH, callerId);
		if (!GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equals(taskH.getMsStatustask().getStatusCode())) {
			
			throw new ChangeException(this.messageSource.getMessage(
					"businesslogic.error.changeexception", null, this.retrieveLocaleAudit(callerId)),
					taskH.getMsStatustask().getStatusCode());
		}
		long uuidProcess = getUuidProcess(taskH, loginBean.getAmMssubsystem());

		List<Long> nextTask = wfEngineLogic.getNextTaskUuid(uuidProcess, taskH.getUuidTaskH());

		String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess, taskH.getUuidTaskH(), 
				(Long) nextTask.get(index));

		Object[][] params2 = {
				{ Restrictions.eq("statusCode", statusCode) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", loginBean
						.getAmMssubsystem().getUuidMsSubsystem()) } };
		MsStatustask msStatustask = this.getManagerDAO().selectOne(
				MsStatustask.class, params2);

		taskH.setMsStatustask(msStatustask);
		taskH.setUsrUpd(callerId.getCallerId());
		taskH.setDtmUpd(new Date());
		taskH.setVerificationDate(new Date());
		taskH.setVerificationNotes(notes);

		this.auditManager.auditEdit(taskH, auditInfoH, callerId.getCallerId(), "");
		this.getManagerDAO().update(taskH);

		if (uuidAnswers != null && uuidAnswers.length > 0) {
			boolean saveAsJson = PropertiesHelper.isTaskDJson();
			if (saveAsJson) {
				this.saveAnswersJson(taskH, uuidQuestions, textAnswers, latAns, longAns, callerId);
			} 
			else {
				this.saveAnswers(uuidAnswers, textAnswers, taskH, uuidQuestions, callerId);
			}
		}

		String codeProcess = StringUtils.EMPTY;
		String notesV = StringUtils.EMPTY;

		if (index == 0) {
			notesV = "Task with ID: " + taskH.getTaskId() + " has been verified";
			codeProcess = GlobalVal.CODE_PROCESS_VERIFIED;
		} 
		else {
			notesV = "Task with ID: " + taskH.getTaskId() + " has been rejected on Verification";
			codeProcess = GlobalVal.CODE_PROCESS_REJECTED_VERIFICATION;
		}
		insertTaskHistory(callerId, msStatustask, taskH,
				notesV + " | " + notes, codeProcess, taskH.getAmMsuser().getFullName());

		if (GlobalVal.SUBSYSTEM_MO.equals(taskH.getFlagSource())) {
			commitOrder(loginBean, notes, taskH, loginBean.getAmMssubsystem(), index, codeProcess, callerId);
		}

		String taskID = String.valueOf(taskH.getTaskId());
		String subsystemCode = loginBean.getAmMssubsystem().getSubsystemName();
		String flagSource = taskH.getFlagSource();

		// 06-10-2015 Task Verification To Interface NC
		if (!GlobalVal.SUBSYSTEM_MC.equals(subsystemCode)) {
			intFormLogic.submitResult(callerId, taskID, "1");
		}
		if (!GlobalVal.SUBSYSTEM_MO.equals(subsystemCode)) {
			intFormLogic.saveResult(callerId, taskID, flagSource, subsystemCode, callerId.getCallerId(), "1");
		}
	}

	@Override
	public List<Map<String, Object>> mapOptAnsByLov(Map<String, String> valueArr, long uuidForm, 
			long uuidQuestion, long uuidFormHistory, AuditContext auditContext) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();

		// get uuid branch
		Object[][] paramsUsr = { { Restrictions.eq("uuidMsUser",
				Long.valueOf(auditContext.getCallerId())) } };
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, paramsUsr);
		long uuidBranch = user.getMsBranch().getUuidBranch();
		// end get uuid branch
		MsQuestion msQuestion = commonLogic.retrieveQuestionByuuidQuestionQset(uuidQuestion,
				Long.valueOf(user.getAmMssubsystem().getUuidMsSubsystem()),
				uuidFormHistory, auditContext);

		MsAnswertype msAnswerType = msQuestion.getMsAnswertype();

		Object[][] paramsRel = {
				{ "choiceFilter", "%{" + msQuestion.getRefId() + "}%" },
				{ "uuidFormHistory", uuidFormHistory } };
		List<Map<String, Object>> msQuestionRel = this.getManagerDAO().selectAllNativeString(
						"select UUID_QUESTION, UUID_FORM_HISTORY, CHOICE_FILTER"
						+ " from MS_FORMQUESTIONSET"
						+ " where UUID_FORM_HISTORY = :uuidFormHistory"
						+ " and CHOICE_FILTER like :choiceFilter",
						paramsRel);

		for (int a = 0; a < msQuestionRel.size(); a++) {
			Map temprel = (Map) msQuestionRel.get(a);
			MsQuestion msQuestion2 = commonLogic.retrieveQuestionByuuidQuestionQset(
					Long.valueOf(temprel.get("d0").toString()), 
					Long.valueOf(user.getAmMssubsystem().getUuidMsSubsystem()), 
					Long.valueOf(temprel.get("d1").toString()), auditContext);
			Map<String, Object> map = new HashMap<String, Object>();

			if (a == 0) {
				String[] choiceFilter = temprel.get("d2").toString().split(",");

				Stack<Object[]> paramStack = new Stack<>();

				String lovGroup = "NULL";
				if (msQuestion2.getLovGroup().isEmpty()) {
					paramStack.push(new Object[] { "lovGroup", lovGroup });
				} 
				else {
					paramStack.push(new Object[] { "lovGroup", msQuestion2.getLovGroup() });
				}
				paramStack.push(new Object[] { "uuidBranch", uuidBranch });
				paramStack.push(new Object[] { "isActive", "1" });

				for (int i = 0; i < choiceFilter.length; i++) {
					String refId = choiceFilter[i].replace("{",
							StringUtils.EMPTY).replace("}", StringUtils.EMPTY);
					String temp = valueArr.get(refId);
					if (temp != null) {
						paramStack.push(new Object[] { "constraint" + (i + 1),
								valueArr.get(refId) });
					}
				}

				Object[][] sqlParams = new Object[paramStack.size()][2];
				for (int k = 0; k < paramStack.size(); k++) {
					Object[] objects = paramStack.get(k);
					sqlParams[k] = objects;
				}

				List<?> msLov = new ArrayList<>();
				List<MsLov> listLov = new ArrayList<>();

				switch (paramStack.size()-3) {
					case 0:
						msLov = this.getManagerDAO().selectAllNative(
								"task.verification.getLovByBranchC0", sqlParams, null);
						break;
					case 1:
						msLov = this.getManagerDAO().selectAllNative(
								"task.verification.getLovByBranchC1", sqlParams, null);
						break;
					case 2:
						msLov = this.getManagerDAO().selectAllNative(
								"task.verification.getLovByBranchC2", sqlParams, null);
						break;
					case 3:
						msLov = this.getManagerDAO().selectAllNative(
								"task.verification.getLovByBranchC3", sqlParams, null);
						break;
					case 4:
						msLov = this.getManagerDAO().selectAllNative(
								"task.verification.getLovByBranchC4", sqlParams, null);
						break;
					case 5:
						msLov = this.getManagerDAO().selectAllNative(
								"task.verification.getLovByBranchC5", sqlParams, null);
						break;
					default:
						break;
				}

				for (int i = 0; i < msLov.size(); i++) {
					Map map2 = (Map) msLov.get(i);
					MsLov lovlov = new MsLov();
					lovlov.setUuidLov(Long.valueOf(map2.get("d0").toString()));
					lovlov.setIsActive((String) map2.get("d1"));
					lovlov.setIsDeleted((String) map2.get("d2"));
					lovlov.setUsrCrt((String) map2.get("d3"));
					lovlov.setDtmCrt((Date) map2.get("d4"));
					lovlov.setUsrUpd((String) map2.get("d5"));
					lovlov.setDtmUpd((Date) map2.get("d6"));
					lovlov.setLovGroup((String) map2.get("d7"));
					lovlov.setCode((String) map2.get("d8"));
					lovlov.setDescription((String) map2.get("d9"));
					lovlov.setSequence((int) map2.get("d10"));
					lovlov.setConstraint1((String) map2.get("d11"));
					lovlov.setConstraint2((String) map2.get("d12"));
					lovlov.setConstraint3((String) map2.get("d13"));
					lovlov.setConstraint4((String) map2.get("d14"));
					lovlov.setConstraint5((String) map2.get("d15"));
					listLov.add(lovlov);
				}

				map.put("msLov", listLov);
				map.put("value", !listLov.isEmpty() ? listLov.get(0).getCode()
						: StringUtils.EMPTY);
			}
			map.put("ansType", msAnswerType.getCodeAnswerType());
			map.put("msQuestion", msQuestion2);
			map.put("choiceFilter", temprel.get("d2").toString());

			if (!temprel.get("d2").toString().isEmpty()) {
				map.put("isCF", "1");
			} 
			else {
				map.put("isCF", "0");
			}

			result.add(map);
		}

		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	private void insertNewDetail(TrTaskD newTaskD, AuditContext callerId) {
		newTaskD.setDtmCrt(new Date());
		newTaskD.setUsrCrt(callerId.getCallerId());

		this.getManagerDAO().insert(newTaskD);
	}

	@Override
	public List viewMapPhoto(String uuid, AuditContext callerId) {
		Object[][] params = { { Restrictions.eq("trTaskH.uuidTaskH", Long.valueOf(uuid)) } };
		Map tmp = this.getManagerDAO().list(TrTaskdetaillob.class, params, null);
		List<TrTaskdetaillob> result = (List<TrTaskdetaillob>) tmp.get(GlobalKey.MAP_RESULT_LIST);

		ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);

		if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {// from folder
			for (int i = 0; i < result.size(); i++) {
				if (result.get(i).getImagePath() != null) {
					String path = result.get(i).getImagePath();
					File file = new File(path);
					byte imageData[] = imageStorageLogic.retrieveImageFileSystemByFile(file);
					result.get(i).setLobFile(imageData);
				}
			}
		}
		this.processLocation(result, callerId);

		return result;
	}

	private void processLocation(List<TrTaskdetaillob> bean, AuditContext callerId) {
		for (int i = 0; i < bean.size(); i++) {
			boolean isGps = false;
			if (bean.get(i).getLatitude() != null
					|| bean.get(i).getLongitude() != null) {
				if (bean.get(i).getLatitude().compareTo(BigDecimal.ZERO) == 0
						|| bean.get(i).getLongitude().compareTo(BigDecimal.ZERO) == 0) {
					isGps = false;
				} 
				else {
					isGps = !(bean.get(i).getLatitude().doubleValue() == 0d 
							&& bean.get(i).getLongitude().doubleValue() == 0d) ? true : false;
				}
			}
			if (!isGps) {
				bean.get(i).setLatitude(null);
				bean.get(i).setLongitude(null);
				if (bean.get(i).getMcc() != null && bean.get(i).getMnc() != null
						&& bean.get(i).getLac() != null && bean.get(i).getCellId() != null) {
					List<LocationBean> listLocation = new ArrayList<LocationBean>();
					LocationBean locationBean = new LocationBean();
					locationBean.setCellid(bean.get(i).getCellId());
					locationBean.setLac(bean.get(i).getLac());
					locationBean.setMcc(bean.get(i).getMcc());
					locationBean.setMnc(bean.get(i).getMnc());
					listLocation.add(locationBean);
					this.geocoder.geocodeCellId(listLocation, callerId);
					if (listLocation.get(0).getCoordinate() != null) {
						bean.get(i).setLatitude(new BigDecimal(locationBean.getCoordinate()
								.getLatitude()));
						bean.get(i).setLongitude(new BigDecimal(locationBean.getCoordinate()
								.getLongitude()));
						bean.get(i).setAccuracy(new Integer(locationBean.getAccuracy()));
					}
					bean.get(i).setIsGps("0");
				}
			} 
			else {
				bean.get(i).setIsGps("1");
			}
		}

	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	private void saveAnswers(String[] uuidAnswers, String[] textAnswers, TrTaskH trTaskH,
			String[] uuidQuestions, AuditContext auditContext) {
		List<TrTaskD> taskDs = new ArrayList<>(uuidAnswers.length);

		MsFormhistory msFormhistory = this.commonLogic.retrieveMsFormhistory(
				trTaskH.getMsForm().getUuidForm(), trTaskH.getFormVersion(),
				auditContext);
		long uuidFormHistory = msFormhistory.getUuidFormHistory();
		for (int i = 0; i < uuidAnswers.length; i++) { // FIX 2018-01-18 Pertanyaan terakhir selalu tidak terproses (karena length-1)
			if ("^".equals(textAnswers[i]) || "^#".equals(textAnswers[i])) {
				continue;
			}

			MsQuestion quest = this.commonLogic.retrieveQuestionByuuidQuestionQset(
							Long.parseLong(uuidQuestions[i]), 
							trTaskH.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem(), 
							uuidFormHistory, auditContext);

			TrTaskD answerDetail = this.getDetail(uuidAnswers[i]);
			if (answerDetail == null) {
				answerDetail = new TrTaskD();
				answerDetail.setTrTaskH(trTaskH);
				answerDetail.setMsQuestion(quest);
				answerDetail.setQuestionText(quest.getQuestionLabel());
			}

			boolean flagUpdate = this.transformDataByAnswerType(uuidFormHistory, answerDetail, uuidAnswers[i],
					textAnswers[i], taskDs, textAnswers, auditContext);

			if (flagUpdate) {
				if (answerDetail.getMsQuestion().getMsAssettag() != null
						&& GlobalVal.ASSET_TAG_PRODUCT.equalsIgnoreCase(answerDetail.getMsQuestion()
						.getMsAssettag().getAssetTagName())) {
					this.insertTaskSurveyData(trTaskH, answerDetail.getMsLovByFinLovId().getUuidLov(), 
							auditContext.getCallerId());
				}
				if (answerDetail.getUuidTaskD() == 0L) {
					this.insertNewDetail(answerDetail, auditContext);
				} 
				else {
					this.updateTaskD(answerDetail, auditContext);
				}
			}
		}
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	private void saveAnswersJson(TrTaskH trTaskH, String[] uuidQuestions,
			String[] textAnswers, String[] latAns, String[] longAns,
			AuditContext auditContext) {
		Gson gson = new Gson();
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		TaskDocumentBean tdb = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);

		MsFormhistory msFormhistory = this.commonLogic.retrieveMsFormhistory(trTaskH.getMsForm().getUuidForm(), 
				trTaskH.getFormVersion(), auditContext);
		long uuidFormHistory = msFormhistory.getUuidFormHistory();

		List<AnswerBean> answers = tdb.getAnswers();
		for (int i = 0; i < uuidQuestions.length; i++) {
			if ("^".equals(textAnswers[i]) || "^#".equals(textAnswers[i])) {
				continue;
			}

			long uuidQuestion = Long.parseLong(uuidQuestions[i]);

			MsQuestion quest = this.commonLogic.retrieveQuestionByuuidQuestionQset(uuidQuestion, 
					trTaskH.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem(), 
					uuidFormHistory, auditContext);

			int countLatLong = 0;
			String latitude = null, longitude = null;
			if (GlobalVal.ANSWER_TYPE_LOCATION.equals(quest.getMsAnswertype()
					.getCodeAnswerType()) && latAns.length > 0) {
				latitude = latAns[countLatLong];
				longitude = longAns[countLatLong];
				countLatLong++;
			}

			int idxAnswer = tdb.findAnswerIndex(quest.getUuidQuestion());

			AnswerBean answer = this.transformToJsonByAnswerType(trTaskH,
					uuidFormHistory, quest, textAnswers[i], latitude,
					longitude, tdb, idxAnswer, textAnswers, auditContext);

			if (idxAnswer == -1) {
				answers.add(answer);
			}
		}

		docDb.setDocument(gson.toJson(tdb, TaskDocumentBean.class));
		docDb.setDtmUpd(new Date());
		docDb.setUsrUpd(auditContext.getCallerId());

		this.getManagerDAO().update(docDb);
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	private boolean transformDataByAnswerType(long uuidFormHistory, TrTaskD answerDetail, 
			String uuidAnswer, String textAnswer, List<TrTaskD> taskDs, String[] textAnswers, 
			AuditContext auditContext) {
		Assert.notNull(answerDetail);

		String answerType = answerDetail.getMsQuestion().getMsAnswertype().getCodeAnswerType();
		String lovGroup = answerDetail.getMsQuestion().getLovGroup();

		boolean flagOption = false;
		boolean flagNeedUpdateDb = true;
		if (GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(answerType)
				|| GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(answerType)) {
			flagOption = true;
			String[] selectedAnswer = textAnswer.replace("#", "").split("\\^");
			if (selectedAnswer.length > 0) {
				MsLov lovtmp = this.getLov(answerDetail.getMsQuestion().getUuidQuestion(), textAnswers, 
						selectedAnswer[0], lovGroup, uuidFormHistory, taskDs);
				answerDetail.setMsLovByFinLovId(lovtmp);
				answerDetail.setFinOptionText((lovtmp == null) ? null : lovtmp.getDescription());
				if (selectedAnswer.length > 1) {
					answerDetail.setFinTextAnswer(selectedAnswer[1]);
				}
			}
		} 
		else if (MssTool.isMultipleQuestion(answerType)) {
			flagOption = true;
			String[] multiAnswer = textAnswer.split("#");
			List<MsLov> checkboxAns = new ArrayList<MsLov>();
			List<String> checkboxDescAns = new ArrayList<String>();
			for (int j = 0; j < multiAnswer.length; j++) {
				String[] multiAns = multiAnswer[j].split("\\^");
				MsLov lov = this.getLov(answerDetail.getMsQuestion().getUuidQuestion(), textAnswers, 
						multiAns[0], lovGroup, uuidFormHistory, taskDs);
				if (lov != null) {
					checkboxAns.add(lov);
				}
				if (GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
					if (multiAns.length > 1) {
						checkboxDescAns.add(multiAns[1]);
					} 
					else {
						checkboxDescAns.add(StringUtils.EMPTY);
					}
				}
			}

			// update
			if (!checkboxAns.isEmpty()) {
				for (int k = 0; k < checkboxAns.size(); k++) {
					if (checkboxAns.get(k) == null) {
						continue;
					}

					Object[][] paramsTaskD = {
							{ "uuidTaskH", answerDetail.getTrTaskH().getUuidTaskH() },
							{ "uuidQuestion", answerDetail.getMsQuestion().getUuidQuestion() },
							{ "msLovId", checkboxAns.get(k).getUuidLov() } };
					TrTaskD checkboxDetail = this.getDetailByLov(paramsTaskD, auditContext);
					if (checkboxDetail != null) {
						// update others bcoz checkboxAns.get(k) exist dan bukan
						// k==0
						if (String.valueOf(checkboxDetail.getUuidTaskD())
								.equalsIgnoreCase(uuidAnswer)) {
							// jika sama dengan uuidAnswer, set TaskDetail
							answerDetail.setMsLovByFinLovId(checkboxAns.get(k));
							answerDetail.setFinOptionText(checkboxDetail.getOptionText());
							if (!checkboxDescAns.isEmpty()) {
								answerDetail.setFinTextAnswer(checkboxDescAns.get(k));
							}
							this.updateTaskD(answerDetail, auditContext);
							flagNeedUpdateDb = false;
						} 
						else {
							// jika tidak sama,update sendiri
							checkboxDetail.setMsLovByFinLovId(checkboxAns.get(k));
							checkboxDetail.setFinOptionText(checkboxDetail.getOptionText());
							if (!checkboxDescAns.isEmpty()) {
								checkboxDetail.setFinTextAnswer(checkboxDescAns.get(k));
							}
							this.updateTaskD(checkboxDetail, auditContext);
							flagNeedUpdateDb = false;
						}
					} 
					else {
						// insert new bcoz checkboxAns.get(k) not exist
						TrTaskD newCheckboxAns = new TrTaskD();
						newCheckboxAns.setTrTaskH(answerDetail.getTrTaskH());
						newCheckboxAns.setMsQuestion(answerDetail.getMsQuestion());
						newCheckboxAns.setQuestionText(answerDetail.getQuestionText());
						newCheckboxAns.setMsLovByFinLovId(checkboxAns.get(k));
						newCheckboxAns.setFinOptionText(checkboxAns.get(k).getDescription());
						if (!checkboxDescAns.isEmpty()) {
							newCheckboxAns.setFinTextAnswer(checkboxDescAns.get(k));
						}
						this.insertNewDetail(newCheckboxAns, auditContext);
						flagNeedUpdateDb = false;
					}
				}
			}
		} 
		else if (GlobalVal.ANSWER_TYPE_RADIO.equals(answerType)
				|| GlobalVal.ANSWER_TYPE_DROPDOWN.equals(answerType)) {
			flagOption = true;
			String code = textAnswer.replace("^", "").replace("#", "");
			MsLov lov = this.getLov(answerDetail.getMsQuestion().getUuidQuestion(), textAnswers, 
					code, lovGroup, uuidFormHistory, taskDs);
			answerDetail.setMsLovByFinLovId(lov);
			if (lov != null) {
				answerDetail.setFinOptionText(lov.getDescription());
			}
		} 
		else if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType)
				&& StringUtils.isNotBlank(textAnswer)) {
			answerDetail.setFinTextAnswer(textAnswer.replace(".00", "").replace(",", ""));
		} 
		else if (answerDetail.getLatitude() != null
				&& answerDetail.getLongitude() != null && !flagOption) {
			answerDetail.setFinTextAnswer(answerDetail.getTextAnswer());
		} 
		else if (answerDetail.getLatitude() == null
				&& answerDetail.getLongitude() == null && !flagOption) {
			// text,textmultiline,numeric,decimal,date,datetime,text with
			// suggestion,luonline
			answerDetail.setFinTextAnswer(StringUtils.trimToNull(textAnswer));
		}

		return flagNeedUpdateDb;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	private void insertTaskSurveyData(TrTaskH trTaskH, long optionAnswerId,
			String userUpdateName) {
		Object[][] prm = { { Restrictions.eq("uuidTaskId", trTaskH.getUuidTaskH()) } };
		TrTasksurveydata taskSurveyData = this.getManagerDAO().selectOne(TrTasksurveydata.class, prm);

		if (taskSurveyData == null) {
			taskSurveyData = new TrTasksurveydata();
			taskSurveyData.setTrTaskH(trTaskH);
			taskSurveyData.setUsrCrt(userUpdateName);
			taskSurveyData.setDtmCrt(new Date());
			taskSurveyData.setProductLovId(optionAnswerId);
			this.getManagerDAO().insert(taskSurveyData);
		} 
		else {
			taskSurveyData.setProductLovId(optionAnswerId);
			taskSurveyData.setUsrUpd(userUpdateName);
			taskSurveyData.setDtmUpd(new Date());
			this.getManagerDAO().update(taskSurveyData);
		}
	}

	private MsLov getLov(long uuidQuestion, String[] textAnswer, String code,
			String lovGroup, long uuidFormHistory, List<TrTaskD> taskDs) {
		MsLov lov = new MsLov();
		Object[][] paramQRel = {{ Restrictions.eq("msQuestion.uuidQuestion", uuidQuestion) },
				{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) } };
		MsFormquestionset msQuestionRel = this.getManagerDAO().selectOne(MsFormquestionset.class, paramQRel);

		List<String> constraints = new ArrayList<String>();
		if (null != msQuestionRel) {
			if (!msQuestionRel.getChoiceFilter().isEmpty()) {
				String[] choiceFilters = msQuestionRel.getChoiceFilter().split(",");
				for (int a = choiceFilters.length - 1; a < choiceFilters.length && a >= 0; a--) {
					String refId = choiceFilters[a].replace("{",
							StringUtils.EMPTY).replace("}", StringUtils.EMPTY);
					int idxTaskDs = 0;
					for (TrTaskD taskD : taskDs) {
						if (taskD == null) {
							idxTaskDs++;
							continue;
						}

						String refIdAnswer = taskD.getMsQuestion() == null ? null
								: taskD.getMsQuestion().getRefId();
						if (StringUtils.equals(refId, refIdAnswer)) {
							constraints.add(textAnswer[idxTaskDs]);
						}
						idxTaskDs++;
					}
				}
			}
		}

		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[] { Restrictions.eq("code", code) });
		paramStack.push(new Object[] { Restrictions.eq("lovGroup", lovGroup) });
		paramStack.push(new Object[] { Restrictions.eq("isActive", "1") });

		int x = 1;
		for (int j = constraints.size() - 1; j < constraints.size() && j >= 0; j--) {
			paramStack.push(new Object[] { Restrictions.eq("constraint" + x, constraints.get(j)) });
			x++;
		}

		Object[][] sqlParams = new Object[paramStack.size()][2];
		for (int k = 0; k < paramStack.size(); k++) {
			Object[] objects = paramStack.get(k);
			sqlParams[k] = objects;
		}

		lov = this.getManagerDAO().selectOne(MsLov.class, sqlParams);

		return lov;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateDetail(String textAnswer, String optionAnswerId,
			String latitude, String longitude, String uuidForm,
			String uuidTaskH, String uuidQuestion, String answerType,
			AuditContext auditContext) {
		if (null != optionAnswerId) {
			this.updateTaskD(Long.valueOf(optionAnswerId), textAnswer,
					latitude, longitude, uuidForm, uuidTaskH, uuidQuestion,
					answerType, auditContext);
		} 
		else {
			this.updateTaskD(null, textAnswer, latitude, longitude, uuidForm,
					uuidTaskH, uuidQuestion, answerType, auditContext);
		}
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	private TrTaskD updateTaskD(Long optionAnswerId, String textAnswer,
			String latitude, String longitude, String uuidForm,
			String uuidTaskH, String uuidQuestion, String answerType,
			AuditContext auditContext) {
		TrTaskD result = null;
		List answerDetail = this.getDetail(uuidForm, uuidTaskH, uuidQuestion, auditContext);

		int countMultiple = 0;
		if (null != answerDetail && !answerDetail.isEmpty()) {
			if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(answerType)
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType)
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
				if (countMultiple < answerDetail.size()) {
					Map temp = (Map) answerDetail.get(countMultiple);
					String uuid = temp.get("d0").toString();
					result = this.getManagerDAO().selectOne(TrTaskD.class, Long.valueOf(uuid));
				}

				if (null != result) {
					MsLov msLovByFinLovId = this.getManagerDAO().selectOne(MsLov.class, optionAnswerId);
					if (msLovByFinLovId != null) {
						result.setMsLovByFinLovId(msLovByFinLovId);
						result.setFinOptionText(msLovByFinLovId.getDescription());
						result.setFinTextAnswer(textAnswer);
						this.updateTaskD(result, auditContext);
					}
				}
				else {
					TrTaskD newCheckboxAns = new TrTaskD();
					MsLov msLovByFinLovId = this.getManagerDAO().selectOne(MsLov.class, optionAnswerId);
					if (msLovByFinLovId != null) {
						TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, Long.valueOf(uuidTaskH));
						MsQuestion msQuestion = this.getManagerDAO().selectOne(
								MsQuestion.class, Long.valueOf(uuidQuestion));
						newCheckboxAns.setTrTaskH(taskH);
						newCheckboxAns.setMsQuestion(msQuestion);
						newCheckboxAns.setQuestionText(msQuestion.getQuestionLabel());
						newCheckboxAns.setMsLovByFinLovId(msLovByFinLovId);
						newCheckboxAns.setFinOptionText(msLovByFinLovId.getDescription());
						newCheckboxAns.setFinTextAnswer(textAnswer);
						this.insertNewDetail(newCheckboxAns, auditContext);
					}
				}
				countMultiple++;
			} 
			else {
				Map temp = (Map) answerDetail.get(0);
				String uuid = temp.get("d0").toString();
				result = this.getManagerDAO().selectOne(TrTaskD.class, Long.valueOf(uuid));

				if (null != result) {
					if (null != result.getMsLovByLovId()) {
						MsLov lov = this.getManagerDAO().selectOne(MsLov.class, optionAnswerId);
						result.setMsLovByFinLovId(lov);
						result.setFinOptionText(lov.getDescription());
					} 
					else {
						if (latitude != null && longitude != null) {
							BigDecimal decLatitude = new BigDecimal(latitude);
							BigDecimal decLongitude = new BigDecimal(longitude);

							result.setLatitude(decLatitude);
							result.setLongitude(decLongitude);
							result.setFinTextAnswer(textAnswer);
						} 
						else if (latitude == null && longitude == null) {
							result.setFinTextAnswer(textAnswer);
						}
					}
					this.updateTaskD(result, auditContext);
				}
			}
		}
		return result;
	}

	@Override
	public List getWfEngineNextStep(TrTaskH trTaskH, AmMssubsystem msSubsystem,
			AuditContext callerId) {
		List<String> result = null;
		List wfDesc = new ArrayList<>();

		long uuid = trTaskH.getUuidTaskH();
		long uuidProcess = this.getUuidProcess(trTaskH, msSubsystem);

		List<Long> uuidNextTask = wfEngineLogic.getNextTaskUuid(uuidProcess, uuid);
		result = wfEngineLogic.getNextTask(uuidProcess, uuid);

		for (int i = 0; i < result.size(); i++) {
			String statusCode = result.get(i).toString();
			String[][] params = {{ "statusCode", statusCode },
					{ "msSubsystem", String.valueOf(msSubsystem.getUuidMsSubsystem()) } };
			String descNexttask = (String) this.getManagerDAO().selectOneNativeString(
					"select STATUS_TASK_DESC " + "from MS_STATUSTASK "
					+ "where STATUS_CODE = :statusCode "
					+ "AND UUID_MS_SUBSYSTEM = :msSubsystem ",
					params);

			Map a = new HashMap<>();
			a.put("uuidNextTask", uuidNextTask.get(i));
			a.put("statusNextTask", statusCode);
			a.put("descNextTask", descNexttask);
			wfDesc.add(a);
		}
		return wfDesc;
	}

	@Override
	public Map<String, Object> getSuggestedUser(long uuidTaskH, String mode,
			AuditContext callerId) {
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTaskH);
		List<TrTaskH> listTaskDist = new ArrayList<TrTaskH>();
		listTaskDist.add(trTaskH);

		// cek task distribution berdasarkan subsystem
		// distribution berdasarkan type round robin, low task, dan zipcode
		Object[][] prmSubsystem = { { Restrictions.eq("subsystemName", GlobalVal.SUBSYSTEM_MS) } };
		AmMssubsystem subsystem = this.getManagerDAO().selectOne(AmMssubsystem.class, prmSubsystem);

		MsTskdistributionofmodule msTaskDistModul = this.getManagerDAO().selectOne(
				"from MsTskdistributionofmodule mtd "
				+ "join fetch mtd.amMssubsystem ms "
				+ "join fetch mtd.msTaskdistribution mt "
				+ "where ms.uuidMsSubsystem = :uuidMsSubsystem",
				new Object[][] { { "uuidMsSubsystem",
				subsystem.getUuidMsSubsystem() } });

		String uuidMh = callerId.getCallerId();
		if (msTaskDistModul == null || StringUtils.isBlank(uuidMh)
				|| GlobalVal.MODULE_TASK_DISTRIBUTION_OFF
						.equals(msTaskDistModul.getMsTaskdistribution().getCode())) {
			return Collections.emptyMap();
		}

		Map resultMap = new HashMap<>();
		Map<String, Object> result = null;
		if (GlobalVal.MODULE_TASK_DISTRIBUTION_ROUND_ROBIN
				.equals(msTaskDistModul.getMsTaskdistribution().getCode())) {
			result = taskDistributionLogic.distributeRoundRobin(listTaskDist,
					subsystem, uuidMh, "verify", mode, callerId);
		} 
		else if (GlobalVal.MODULE_TASK_DISTRIBUTION_LOW_TASK
				.equals(msTaskDistModul.getMsTaskdistribution().getCode())) {
			result = taskDistributionLogic.distributeLowTask(listTaskDist,
					subsystem, uuidMh, "verify", mode, callerId);
		} 
		else if (GlobalVal.MODULE_TASK_DISTRIBUTION_ZIP_CODE
				.equals(msTaskDistModul.getMsTaskdistribution().getCode())) {
			result = taskDistributionLogic.distributeZipCode(listTaskDist,
					subsystem, trTaskH.getZipCode(), uuidMh, "verify", mode,
					callerId);
		} 
		else if (GlobalVal.MODULE_TASK_DISTRIBUTION_GEOFENCING
				.equals(msTaskDistModul.getMsTaskdistribution().getCode())
				&& trTaskH.getLatitude() != null && trTaskH.getLongitude() != null) {
			result = taskDistributionLogic.distributeGeofencing(listTaskDist, subsystem,
					trTaskH.getLatitude() + ";" + trTaskH.getLongitude(), uuidMh, "verify", mode, callerId);
		}
		if (null != result) {
			AmMsuser amMsuser = (AmMsuser) result.get("userAssign");
			if (amMsuser != null) {
				List<AmMsuser> listAmMsusers = new ArrayList<AmMsuser>();
				listAmMsusers.add(amMsuser);
				Integer[][] assignment = unassignTaskLogic.getAssignment(listAmMsusers, callerId);
				String[][] lastLoc = unassignTaskLogic.getLastLoc(listAmMsusers, callerId);
				resultMap.put("suggestedUser", amMsuser);
				resultMap.put("assignment", assignment);
				resultMap.put("lastLoc", lastLoc);
			}
		}

		return resultMap;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void rejectWithResurvey(long uuidTaskH, long uuidMsUser,
			String notes, String mode, AmMsuser loginBean, AuditContext callerId) {

		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH tth " + "join fetch tth.msBranch mb "
				+ "join fetch tth.msForm mf "
				+ "join fetch tth.amMsuser amu "
				+ "join fetch tth.msPriority mp "
				+ "join fetch tth.msStatustask ms "
				+ "join fetch ms.amMssubsystem ams "
				+ "where tth.uuidTaskH = :uuidTaskH",
				new Object[][] { { "uuidTaskH", uuidTaskH } });

		MsStatustask sts = this.getManagerDAO().selectOne(MsStatustask.class,
				trTaskH.getMsStatustask().getUuidStatusTask());
		if (!GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equals(sts.getStatusCode())
				&& !GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(sts.getStatusCode())) {
			throw new ChangeException(this.messageSource.getMessage(
					"businesslogic.error.changeexception", null, this.retrieveLocaleAudit(callerId)), sts.getStatusCode());
		}

		AmMsuser amMsuser = this.getManagerDAO().selectOne(
				"from AmMsuser u " 
				+ "join fetch u.amMssubsystem "
				+ "where u.uuidMsUser = :uuidMsUser",
				new Object[][] { { "uuidMsUser", uuidMsUser } });

		String prevTaskId = trTaskH.getTaskId();

		String formVersion = String.valueOf(trTaskH.getFormVersion());

		taskServiceLogic.addTask(callerId, trTaskH.getFlagSource(),
				GlobalVal.SUBSYSTEM_MS, trTaskH.getMsForm().getFormName(),
				formVersion, trTaskH.getMsPriority().getPriorityDesc(), trTaskH.getCustomerName(), 
				trTaskH.getCustomerAddress(), trTaskH.getCustomerPhone(), trTaskH.getZipCode(),
				trTaskH.getNotes() + " | Verification Notes : " + notes,
				trTaskH.getMsBranch().getBranchCode(), trTaskH.getApplNo(),
				amMsuser.getLoginId(), MobileDefAnswer.DB.toString(), null,
				trTaskH.getLatitude() == null ? StringUtils.EMPTY : trTaskH.getLatitude().toString(),
				trTaskH.getLongitude() == null ? StringUtils.EMPTY : trTaskH.getLongitude().toString(), 
				null, loginBean.getFullName(), null, prevTaskId, mode, trTaskH.getIsPilotingCae());

		Object[][] paramsStatus = {
				{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_REJECTED_WITH_RESURVEY) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", 
						amMsuser.getAmMssubsystem().getUuidMsSubsystem()) } };

		MsStatustask msStatustask = this.getManagerDAO().selectOne(
				MsStatustask.class, paramsStatus);
		trTaskH.setMsStatustask(msStatustask);
		trTaskH.setDtmUpd(new Date());
		trTaskH.setUsrUpd(callerId.getCallerId());
		trTaskH.setVerificationNotes(notes);
		this.getManagerDAO().update(trTaskH);

		String notesH = "Task with ID: " + trTaskH.getTaskId() + " has been rejected with resurvey";

		this.insertTaskHistory(callerId, msStatustask, trTaskH, notesH + " | " + notes, 
				GlobalVal.CODE_PROCESS_REJECTED_WITH_RESURVEY, amMsuser.getLoginId(), loginBean, null);
	}

	@Override
	public Map<String, Object> getValue(long uuidQuestion, long uuidFormHistory, AuditContext callerId) {
		Map<String, Object> result = new HashMap<>();
		List<MsQuestion> listQuestion = new ArrayList<MsQuestion>();

		Object[][] paramsUsr = { { Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId())) } };
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, paramsUsr);

		MsQuestion msQuestion = commonLogic.retrieveQuestionByuuidQuestionQset(
				uuidQuestion, user.getAmMssubsystem().getUuidMsSubsystem(), uuidFormHistory, callerId);

		Object[][] paramsRel = {{ Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion()) },
				{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) } };
		MsFormquestionset msQuestionRel = this.getManagerDAO().selectOne(MsFormquestionset.class, paramsRel);

		if (null != msQuestionRel) {
			String[] choiceFilters = msQuestionRel.getChoiceFilter().split(",");
			for (int i = 0; i < choiceFilters.length; i++) {
				MsQuestion beanQuestion = new MsQuestion();
				beanQuestion.setRefId(choiceFilters[i].replace("{",
						StringUtils.EMPTY).replace("}", StringUtils.EMPTY));
				listQuestion.add(beanQuestion);
			}
		}

		result.put("beanQuestion", msQuestion);
		result.put("msQuestion", listQuestion);

		return result;
	}

	@Override
	public List<Map<String, Object>> autoCalculate(List<Map<String, String>> refIdsWithAnswer,
			long uuidQuestion, long uuidFormHistory, AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		MsQuestion msQuestion = commonLogic.retrieveQuestionByUuid(uuidQuestion, callerId);

		Object[][] paramsRel = {{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) },
				{ Restrictions.like("calculate", '%' + msQuestion.getRefId() + '%') } };
		Map<String, Object> msQuestionRel = this.getManagerDAO().list(
				MsFormquestionset.class, paramsRel, null);
		List<MsFormquestionset> listQuestionRel = (List<MsFormquestionset>) msQuestionRel
				.get(GlobalKey.MAP_RESULT_LIST);

		List listQuestionCur = (List) this.getManagerDAO().selectAllNativeString(
				"SELECT REF_ID, CODE_ANSWER_TYPE "
				+ "FROM MS_QUESTION msq with (nolock) "
				+ "INNER JOIN MS_ANSWERTYPE msa with (nolock)"
				+ " ON msq.UUID_ANSWER_TYPE = msa.UUID_ANSWER_TYPE "
				+ "WHERE msa.code_answer_type IN ('003', '004', '005')",
				null);

		for (int j = 0; j < listQuestionCur.size(); j++) {
			boolean exist = false;
			Map<String, Object> temp = (Map<String, Object>) listQuestionCur.get(j);
			for (int i = 0; i < refIdsWithAnswer.size(); i++) {
				Map<String, String> mapList = refIdsWithAnswer.get(i);
				if (temp.get("d0").toString().equals(mapList.get("refid"))) {
					exist = true;
				}
			}

			if (!exist) {
				Map<String, String> map = new HashMap<>();
				map.put("id", "0");
				map.put("refid", temp.get("d0").toString());
				map.put("value", "0");
				refIdsWithAnswer.add(map);
			}
		}

		for (MsFormquestionset msFormquestionset : listQuestionRel) {
			Map<String, Object> mapResult = new HashMap<>();
			String idResult = "";
			String[] calculateArr = msFormquestionset.getCalculate().split("start");
			calculateArr = calculateArr[calculateArr.length - 1].split("end");
			calculateArr = calculateArr[0].split("=");
			String calculate = calculateArr[1].replace("_var", "").replace("/*", "")
					.replace("*/", "").replace(" ", "").replace("$", "");

			int result = 0;

			if (!calculate.contains("dateformatter.age")) {
				for (int i = 0; i < refIdsWithAnswer.size(); i++) {
					Map<String, String> mapList = refIdsWithAnswer.get(i);
					calculate = calculate.replaceAll("\\b" + mapList.get("refid") + "\\b",
							mapList.get("value") == null ? "0" : mapList.get("value"));

					if (msFormquestionset.getRefId().equals(mapList.get("refid"))) {
						idResult = mapList.get("id");
					}
				}

				ScriptEngineManager mgr = new ScriptEngineManager();
				ScriptEngine engine = mgr.getEngineByName("JavaScript");

				try {
					Object evalResult = engine.eval(calculate);
					if (evalResult instanceof Double) {
						result = ((Double) evalResult).intValue();
					} 
					else if (evalResult instanceof Integer) {
						result = ((Integer) evalResult).intValue();
					} 
					else if (evalResult instanceof BigDecimal) {
						result = ((BigDecimal) evalResult).intValue();
					}
				} 
				catch (ScriptException e) {
					LOG.error("Exception on autocalculate evaluation", e);
				}

			} 
			else {
				calculate = calculate.replaceAll("dateformatter.age", "");
				String refIdDate = calculate.substring(1, calculate.length() - 1);
				String date = null;
				for (int i = 0; i < refIdsWithAnswer.size(); i++) {
					Map<String, String> mapList2 = refIdsWithAnswer.get(i);
					String refId = mapList2.get("refid").toString();
					if (msFormquestionset.getRefId().equals(mapList2.get("refid"))) {
						idResult = mapList2.get("id");
					}
					if (Objects.equals(refIdDate, refId)) {
						date = mapList2.get("value").toString();
					}
				}

				SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
				Date birth2 = null;
				try {
					birth2 = dateFormat.parse(date);
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date " + date, e);
				}
				Date now = new Date();
				long timeBetween = now.getTime() - birth2.getTime();
				double yearsBetween = timeBetween / 3.15576e+10;
				int ageV = (int) Math.floor(yearsBetween);
				result = (int) ageV;
			}
			if (idResult != "" && idResult != "0") {
				mapResult.put("id", idResult);
				mapResult.put("result", result);
				listResult.add(mapResult);
			}
		}

		return listResult;
	}

	@Override
	public List getFormListCombo(AuditContext callerId) {
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
		String[][] paramsForm = { { "isActive", "1" }, { "subsystemName", subsysName } };
		List result = this.getManagerDAO().selectAllNativeString(
				"SELECT form.UUID_FORM, form.FORM_NAME "
				+ "FROM MS_FORM form WITH (NOLOCK) "
				+ "JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
				+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
				+ "WHERE form.IS_ACTIVE = :isActive "
				+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName",
				paramsForm);

		return result;
	}

	@Override
	public List<Map<String, Object>> copyValue(List<Map<String, String>> refIdsWithAnswer,
			long uuidQuestion, long uuidFormHistory, long uuidUser, int seqQuest, AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		MsQuestion msQuestion = this.getManagerDAO().selectOne(MsQuestion.class, uuidQuestion);
		Object[][] param = { { "refId", msQuestion.getRefId() }, { "uuidFormHistory", uuidFormHistory } };
		List<Map<String, Object>> listQuest = this.getManagerDAO().selectAllNative(
				"task.verification.getQuestRelevantCopy", param, null);
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));

		SimpleDateFormat format = new SimpleDateFormat();
		for (int i = 0; i < refIdsWithAnswer.size(); i++) {
			Map map = refIdsWithAnswer.get(i);
			if (Integer.parseInt(map.get("id").toString()) < seqQuest) {
				continue;
			}

			MsQuestion qset = commonLogic.retrieveQuestionByRefIdQset( map.get("refid").toString(), 
					user.getAmMssubsystem().getUuidMsSubsystem(), uuidFormHistory, callerId);
			Object[][] param2 = {{ Restrictions.eq("msQuestion.uuidQuestion", qset.getUuidQuestion()) },
					{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) } };
			MsFormquestionset questRel = this.getManagerDAO().selectOne(MsFormquestionset.class, param2);
			if (questRel == null || StringUtils.isBlank(questRel.getQuestionValue())) {
				continue;
			}

			String convertedExpression = questRel.getQuestionValue();

			boolean needReplacing = true;
			while (needReplacing) {
				int idxOfOpenBrace = convertedExpression.indexOf('{');
				if (idxOfOpenBrace != -1) {
					// // there's {, prepare to replace what inside the {}
					int idxOfCloseBrace = convertedExpression.indexOf('}');
					String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
					int idxOfOpenAbs = identifier.indexOf("$");
					String flatAnswer = "";
					if (idxOfOpenAbs != -1) { // value yang bukan reff_id
						String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
						if (finalIdentifier.equalsIgnoreCase("LOGIN_ID")) {
							if (map.get("answerType").toString().equals("001")
									|| map.get("answerType").toString().equals("002")) {
								flatAnswer = user.getLoginId();
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("UUID_USER")) {
							if (map.get("answerType").toString().equals("001")
									|| map.get("answerType").toString().equals("002")) {
								flatAnswer = String.valueOf(uuidUser);
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("UUID_BRANCH")) {
							if (map.get("answerType").toString().equals("001")
									|| map.get("answerType").toString().equals("002")) {
								flatAnswer = String.valueOf(user.getMsBranch().getUuidBranch());
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("BRANCH_ID")) {
							if (map.get("answerType").toString().equals("001")
									|| map.get("answerType").toString().equals("002")) {
								flatAnswer = user.getMsBranch().getBranchCode();
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("BRANCH_Name")) {
							if (map.get("answerType").toString().equals("001")
									|| map.get("answerType").toString().equals("002")) {
								flatAnswer = user.getMsBranch().getBranchName();
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("UUID_DEALER")) {
							if (map.get("answerType").toString().equals("001")
									|| map.get("answerType").toString().equals("002")) {
								flatAnswer = String.valueOf(user.getMsDealer().getUuidDealer());
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("DEALER_NAME")) {
							if (map.get("answerType").toString().equals("001")
									|| map.get("answerType").toString().equals("002")) {
								flatAnswer = user.getMsDealer().getDealerName();
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("FLAG_JOB")) {
							if (map.get("answerType").toString().equals("001")
									|| map.get("answerType").toString().equals("002")) {
								flatAnswer = user.getMsJob().getJobCode();
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("THISYEAR")) {
							if (map.get("answerType").toString().equals("003")
									|| map.get("answerType").toString().equals("004")
									|| map.get("answerType").toString().equals("005")) {
								Calendar cal = Calendar.getInstance(TimeZone.getDefault());
								flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("NOWADAYS")) {
							Calendar cal = Calendar.getInstance(TimeZone.getDefault());
							if (map.get("answerType").toString().equals("013")) {
								format = new SimpleDateFormat("dd/MM/yyyy");
								flatAnswer = format.format(cal.getTime());
							} 
							else if (map.get("answerType").toString().equals("015")) {
								format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
								flatAnswer = format.format(cal.getTime());
							}
						}
					} 
					else { // value yang reff_id
						if (!listQuest.isEmpty()) { // FIX 2018-01-18 menyamakan dengan TeleSurvey, Calculate yang bergantung pada CopyValue tidak berfungsi
							for (int j = 0; j < i; j++) {
								Map map2 = refIdsWithAnswer.get(j);
								if (map2.get("refid").equals(identifier)) {
									flatAnswer = map2.get("value").toString();
								}
							}
						}
					}
					if (flatAnswer != null && flatAnswer.length() > 0) {
						convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
					} 
					else {
						needReplacing = false;
					}
				} 
				else {
					needReplacing = false;
				}
			}

			if (!convertedExpression.contains("{")) {
				if (!convertedExpression.toUpperCase().contains("COPY")) {
					Map<String, Object> mapResult = new HashMap<>();
					mapResult.put("id", map.get("id"));
					mapResult.put("result", convertedExpression);
					mapResult.put("ansType", map.get("answerType"));
					listResult.add(mapResult);
				} 
				else {
					convertedExpression = convertedExpression.replaceAll("(?i)COPY", "");
					convertedExpression = convertedExpression.replace("(", "");
					convertedExpression = convertedExpression.replace(")", "");
					// 2018-01-18 menyamakan dengan TeleSurvey
					String arg[] = convertedExpression.split(",");
					boolean condition = false;
					String kondisi = convert(refIdsWithAnswer, arg[0]);
					ScriptEngineManager mgr = new ScriptEngineManager();
					ScriptEngine engine = mgr.getEngineByName("JavaScript");
					try {
						condition = (boolean) engine.eval(kondisi);
					} 
					catch (ScriptException e) {
						LOG.error("Exception on copy value evaluation", e);
					}
					if (condition) {
						Map<String, Object> mapResult = new HashMap<>();
						mapResult.put("id", map.get("id"));
						mapResult.put("result", arg[1]);
						mapResult.put("ansType", map.get("answerType"));
						listResult.add(mapResult);
					} 
					else {
						if (arg.length > 2) {
							Map<String, Object> mapResult = new HashMap<>();
							mapResult.put("id", map.get("id"));
							mapResult.put("result", arg[2]);
							mapResult.put("ansType", map.get("answerType"));
							listResult.add(mapResult);
						}
					}
				}
			}
		}

		return listResult;
	}

	private boolean cekArgumen(String convertedExpression, String answerType) {
		boolean result = false;
		SimpleDateFormat format = new SimpleDateFormat();
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");

		if (convertedExpression.contains("dateDifference")) {
			convertedExpression = convertedExpression.replace("dateDifference", "");
			convertedExpression = convertedExpression.replace(")", "");
			convertedExpression = convertedExpression.replace("(", "");
			String[] arg = convertedExpression.split(",");
			long time1 = 0, time2 = 0;
			if (answerType.equals(GlobalVal.ANSWER_TYPE_DATE)) {
				format = new SimpleDateFormat("dd/MM/yyyy");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			} 
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_TIME)) {
				format = new SimpleDateFormat("dd/MM/yyyy");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			} 
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATETIME)) {
				format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			Period difference = new Period(time1, time2,
					PeriodType.yearMonthDayTime());
			if (arg[2].contains("DAY")) {
				int day = difference.getDays();
				arg[2] = arg[2].replace("|DAY|", day + "");
			} 
			else if (arg[2].contains("MONTH")) {
				int month = difference.getMonths();
				arg[2] = arg[2].replace("|MONTH|", month + "");
			} 
			else if (arg[2].contains("YEAR")) {
				int year = difference.getYears();
				arg[2] = arg[2].replace("|YEAR|", year + "");
			} 
			else if (arg[2].contains("HOUR")) {
				int hour = difference.getHours();
				arg[2] = arg[2].replace("|HOUR|", hour + "");
			} 
			else if (arg[2].contains("MINUTE")) {
				int minute = difference.getMinutes();
				arg[2] = arg[2].replace("|MINUTE|", minute + "");
			} 
			else if (arg[2].contains("SECOND")) {
				int second = difference.getYears();
				arg[2] = arg[2].replace("|SECOND|", second + "");
			}
			arg[2] = arg[2].contains("==") ? arg[2].replace("==", "<=") : arg[2];
			try {
				result = (boolean) engine.eval(arg[2]);
			} 
			catch (ScriptException e) {
				LOG.error("Exception on date evaluation", e);
			}
		} 
		else if (convertedExpression.contains("<=") || convertedExpression.contains("<")
				|| convertedExpression.contains(">=") || convertedExpression.contains(">")
				|| convertedExpression.contains("!=") || convertedExpression.contains("==")) {
			convertedExpression = convertedExpression.replace("(", "");
			convertedExpression = convertedExpression.replace(")", "");
			String[] arg = convertedExpression.split("<=|<|>=|>|!=|==");
			String cek = "";
			String delimeter = "";
			if (convertedExpression.contains("<=")) {
				delimeter = "<=";
			} 
			else if (convertedExpression.contains("<")) {
				delimeter = "<";
			} 
			else if (convertedExpression.contains(">=")) {
				delimeter = ">=";
			} 
			else if (convertedExpression.contains(">")) {
				delimeter = ">";
			} 
			else if (convertedExpression.contains("!=")) {
				delimeter = "!=";
			} 
			else if (convertedExpression.contains("==")) {
				delimeter = "==";
			}
			if (answerType.equals(GlobalVal.ANSWER_TYPE_DATE)) {
				long time1, time2;
				format = new SimpleDateFormat("dd/MM/yyyy");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 + delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			} 
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_TIME)) {
				long time1, time2;
				format = new SimpleDateFormat("dd/MM/yyyy");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 + delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			} 
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATETIME)) {
				long time1, time2;
				format = new SimpleDateFormat("dd/MM/yyyyHH:mm");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 + delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			} 
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_TEXT)
					|| answerType.equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)) {
				arg[0] = arg[0].replace(" ", "");
				arg[1] = arg[1].replace(" ", "");
				if (delimeter.equals("==")) {
					cek = (String) (arg[0].equalsIgnoreCase(arg[1]) ? true : false + "");
				} 
				else if (delimeter.equals("!=")) {
					cek = (String) (!arg[0].equalsIgnoreCase(arg[1]) ? true : false + "");
				} 
				else {
					cek = convertedExpression;
				}
			} 
			else {
				cek = convertedExpression;
			}

			try {
				result = (boolean) engine.eval(cek);
			} 
			catch (ScriptException e) {
				LOG.error("Exception on date evaluation", e);
			}
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> validation(
			List<Map<String, String>> refIdsWithAnswer, long uuidQuestion, long uuidFormHistory, 
			AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();

		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		boolean finalResult = false;

		for (int k = 0; k < refIdsWithAnswer.size(); k++) {
			Map map = refIdsWithAnswer.get(k);
			Object[][] paramsUsr = { { Restrictions.eq("uuidMsUser",
					Long.valueOf(callerId.getCallerId())) } };
			AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, paramsUsr);

			MsQuestion msQuestion = commonLogic.retrieveQuestionByRefIdQset(map.get("refid").toString(), 
					user.getAmMssubsystem().getUuidMsSubsystem(), uuidFormHistory, callerId);

			Object[][] paramsRel = {{ Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion()) },
					{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) } };
			MsFormquestionset msQuestionRel = this.getManagerDAO().selectOne( MsFormquestionset.class, paramsRel);

			if (msQuestionRel == null || StringUtils.isBlank(msQuestionRel.getQuestionValidation())) {
				continue;
			}

			String convertedExpression = msQuestionRel.getQuestionValidation();
			String answerType = (String) map.get("answerType");
			String answerString = (String) map.get("value");
			String id = (String) map.get("id");

			if (StringUtils.isBlank(answerString)) {
				continue;
			}

			boolean needReplacing = true;
			while (needReplacing) {
				int idxOfOpenBrace = convertedExpression.indexOf('{');
				if (idxOfOpenBrace != -1) {
					// there's {, prepare to replace what inside the {}
					int idxOfCloseBrace = convertedExpression.indexOf('}');
					String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
					int idxOfOpenAbs = identifier.indexOf("$");
					String flatAnswer = "";
					if (idxOfOpenAbs != -1) {
						String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
						if (finalIdentifier.equalsIgnoreCase("ANSWER")) {
							try {
								if (answerType.equals(GlobalVal.ANSWER_TYPE_TIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									SimpleDateFormat formatDate = new SimpleDateFormat("yyyyMMdd");
									Date date2 = null;
									try {
										date2 = formatDate.parse(answerString);
									} 
									catch (Exception e) {
										date2 = format.parse(answerString);
									}
									Calendar now = Calendar.getInstance(TimeZone.getDefault());
									Calendar date = Calendar.getInstance(TimeZone.getDefault());
									date.setTime(date2);
									date.set(Calendar.YEAR, now.get(Calendar.YEAR));
									date.set(Calendar.MONTH, now.get(Calendar.MONTH));
									date.set(Calendar.DAY_OF_MONTH, now.get(Calendar.DAY_OF_MONTH));
									flatAnswer = format.format(date.getTime());
								} 
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATETIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									SimpleDateFormat formatDate = new SimpleDateFormat("ddMMyyyyHHmmss");
									Date date2 = null;
									try {
										date2 = formatDate.parse(answerString);
									} 
									catch (Exception e) {
										date2 = format.parse(answerString);
									}
									Calendar date = Calendar.getInstance(TimeZone.getDefault());
									date.setTime(date2);
									flatAnswer = format.format(date.getTime());
								} 
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATE)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									SimpleDateFormat formatDate = new SimpleDateFormat("ddMMyyyy");
									Date date2 = null;
									try {
										date2 = formatDate.parse(answerString);
									} catch (Exception e) {
										date2 = format.parse(answerString);
									}
									Calendar date = Calendar.getInstance(TimeZone.getDefault());
									date.setTime(date2);
									flatAnswer = format.format(date.getTime());
								} 
								else {
									flatAnswer = answerString;
								}
							} 
							catch (Exception e) {
								LOG.error("Error on validation", e);
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("THISYEAR")) {
							Calendar cal = Calendar.getInstance(TimeZone.getDefault());
							flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
						}
						else if (finalIdentifier.equalsIgnoreCase("NOWADAYS")) {
							Calendar cal = Calendar.getInstance(TimeZone.getDefault());
							try {
								if (answerType.equals("014")) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									flatAnswer = format.format(cal.getTime());
								}
								else if (answerType.equals("015")) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									flatAnswer = format.format(cal.getTime());
								} 
								else if (answerType.equals("013")) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									flatAnswer = format.format(cal.getTime());
								} 
								else {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									flatAnswer = format.format(cal.getTime());
								}
							} 
							catch (Exception e) {
								LOG.error("Error on validation", e);
							}
						} 
						else if (finalIdentifier.equalsIgnoreCase("YESTERDAY")) {
							Calendar cal = Calendar.getInstance(TimeZone.getDefault());
							cal.add(Calendar.DATE, -1);
							try {
								if (answerType.equals(GlobalVal.ANSWER_TYPE_TIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									flatAnswer = format.format(cal.getTime());
								} 
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATETIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									flatAnswer = format.format(cal.getTime());
								} 
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATE)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									flatAnswer = format.format(cal.getTime());
								} 
								else {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									flatAnswer = format.format(cal.getTime());
								}
							} 
							catch (Exception e) {
								LOG.error("Error on validation", e);
							}
						}
					} 
					else { // if finalIdentifier = refId
						for (int j = 0; j < refIdsWithAnswer.size() - 1; j++) {
							Map map2 = refIdsWithAnswer.get(j);
							if (map2.get("refid").equals(identifier)) {
								flatAnswer = map2.get("value").toString();
							}
						}
					}
					if (flatAnswer != null && flatAnswer.length() > 0) {
						convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
					}
				} 
				else {
					needReplacing = false;
				}
			}
			String validasi = this.valid(convertedExpression, answerType);
			try {
				finalResult = (boolean) engine.eval(validasi);
			} 
			catch (ScriptException e) {
				LOG.error("Exception on validation evaluation", e);
			}

			Map<String, Object> result = new HashMap<>();
			result.put("id", id);
			result.put("validasi", finalResult);
			result.put("message", msQuestionRel.getQuestionErrorMessage());
			listResult.add(result);
		}
		return listResult;
	}

	private String valid(String script, String type) {
		String convertedExpression = script;

		StrBuilder scriptValidasi = new StrBuilder();
		if (convertedExpression.contains("&&") || convertedExpression.toUpperCase().contains(
				"AND".toUpperCase())) {
			convertedExpression = convertedExpression.replaceAll("(?i)AND", "&&");
			String[] arg = convertedExpression.split("&&");
			for (int i = 0; i < arg.length; i++) {
				scriptValidasi.append("&&");
				if (arg[i].contains("||") || arg[i].toUpperCase().contains("OR".toUpperCase())) {
					convertedExpression = convertedExpression.replaceAll("(?i)OR", "||");
					String[] arg2 = arg[i].split("\\|\\|");
					for (int j = 0; j < arg2.length; j++) {
						if (scriptValidasi.endsWith("&&")) {
							scriptValidasi.append(cekArgumen(arg2[i], type));
						} 
						else {
							scriptValidasi.append("||").append(cekArgumen(arg2[i], type));
						}
					}
				} 
				else {
					scriptValidasi.append(cekArgumen(arg[i], type));
				}
			}
		} 
		else if (convertedExpression.contains("||") || convertedExpression.toUpperCase().contains(
						"OR".toUpperCase())) {
			convertedExpression = convertedExpression.replaceAll("(?i)OR", "||");
			String[] arg = convertedExpression.split("\\|\\|");
			for (int i = 0; i < arg.length; i++) {
				scriptValidasi.append("||").append(cekArgumen(arg[i], type));
			}
		} 
		else {
			scriptValidasi.append(cekArgumen(convertedExpression, type));
		}

		if (scriptValidasi.startsWith("||") || scriptValidasi.startsWith("&&")) {
			scriptValidasi.delete(0, 2);
		}

		return scriptValidasi.toString();
	}

	@Override
	public Map<String, Object> luOnline(AmMsuser userLoggedIn, String refId,
			String lovGroup, String searchVal, long uuidFormHistory,
			String choice, AuditContext callerId) {
		StringBuilder choiceFilterVal = new StringBuilder();
		MsQuestion quest = this.commonLogic.retrieveQuestionByRefIdQset(refId,
				userLoggedIn.getAmMssubsystem().getUuidMsSubsystem(),
				uuidFormHistory, callerId);
		Object[][] paramsRel = {{ Restrictions.eq("msQuestion.uuidQuestion", quest.getUuidQuestion()) },
				{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) } };
		MsFormquestionset msQuestionRel = this.getManagerDAO().selectOne(MsFormquestionset.class, paramsRel);
		String[] opt = choice.split(";");
		if (msQuestionRel != null) {
			if (msQuestionRel.getChoiceFilter() != null && msQuestionRel.getChoiceFilter().length() > 0) {
				String filter = msQuestionRel.getChoiceFilter();
				String[] arg = filter.split(",");
				for (int i = 0; i < arg.length; i++) {
					arg[i] = arg[i].replace("{", "").replace("}", "");
					for (int j = 0; j < opt.length; j++) {
						String arg2[] = opt[j].split(",");
						if (arg2[0].equalsIgnoreCase(arg[i]) && arg2.length > 1) {
							choiceFilterVal.append(arg2[1]).append("@@@");
						}
					}
				}
			}
		}
		List<AnswerItemBean> list = (List<AnswerItemBean>) intFormLogic.luOnline(refId, lovGroup, searchVal,
						choiceFilterVal.toString(), callerId);

		Map<String, Object> result = new HashMap();
		result.put(GlobalKey.MAP_RESULT_LIST, list);
		result.put(GlobalKey.MAP_RESULT_SIZE, list.size());
		return result;
	}

	@Override
	public List<Map<String, Object>> relevant(List<Map<String, String>> refIdsWithAnswer, String uuidForm,
			String uuidTaskH, AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		String convertedExpression = "";
		for (int k = 0; k < refIdsWithAnswer.size(); k++) {

			Map map = refIdsWithAnswer.get(k);
			Object[][] params = { { "refId", (String) map.get("refid") },
					{ "uuidForm", uuidForm }, { "uuidtaskH", uuidTaskH } };
			String script = (String) this.getManagerDAO().selectOneNative(
					"survey.tele.getRelevantScript", params);
			Integer relQuest = (Integer) this.getManagerDAO().selectOneNative(
					"task.verification.getAllRelevanQuest", params);
			String scriptmandatory = (String) this.getManagerDAO()
					.selectOneNative(
							"task.verification.etRelevantMandatoryScript",
							params);
			if (StringUtils.isEmpty(script) && relQuest < 1
					&& StringUtils.isEmpty(scriptmandatory)) {
				map.put("isRel", true);
				continue;
			} 
			else if (relQuest > 0) {
				map.put("isRel", false);
			} 
			else if (StringUtils.isNotEmpty(script)) {
				boolean res = false;
				ScriptEngineManager mgr = new ScriptEngineManager();
				ScriptEngine engine = mgr.getEngineByName("JavaScript");
				convertedExpression = this.convert(refIdsWithAnswer, script);
				try {
					res = (boolean) engine.eval(convertedExpression);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on relevant evaluation", e);
				}
				map.put("isRel", res);
			}
			if (StringUtils.isNotEmpty(scriptmandatory)) {
				boolean res = false;
				ScriptEngineManager mgr = new ScriptEngineManager();
				ScriptEngine engine = mgr.getEngineByName("JavaScript");
				convertedExpression = this.convert(refIdsWithAnswer,
						scriptmandatory);
				try {
					res = (boolean) engine.eval(convertedExpression);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on relevant mandatory evaluation", e);
				}
				map.put("isMandatory", res);
			}
			listResult.add(map);
		}
		return listResult;
	}

	public String convert(List<Map<String, String>> list, String script) {
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		boolean result = false;
		String convertedExpression = script;
		boolean needReplacing = true;
		String flatAnswer = "";
		while (needReplacing) {
			int idxOfOpenBrace = convertedExpression.indexOf('{');
			if (idxOfOpenBrace != -1) {
				// there's {, prepare to replace what inside the {}
				int idxOfCloseBrace = convertedExpression.indexOf('}');
				String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
				for (int j = 0; j < list.size(); j++) {
					Map map2 = list.get(j);
					if (map2.get("refid").equals(identifier)) {
						flatAnswer = map2.get("value").toString();
					}
				}
				if (flatAnswer != null && flatAnswer.length() > 0) {
					convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
				} 
				else if (flatAnswer != null && flatAnswer.length() == 0) {
					convertedExpression = convertedExpression.replace("{" + identifier + "}", "-");
				}
			} 
			else {
				needReplacing = false;
			}

		}
		String[] arg = convertedExpression.toUpperCase().split(
				"(&&)|(AND)|(OR)|(\\|\\|)");
		for (int i = 0; i < arg.length; i++) {
			if (arg[i].contains("<=") || arg[i].contains("<")
					|| arg[i].contains(">=") || arg[i].contains(">")
					|| arg[i].contains("!=") || arg[i].contains("==")) {
				arg[i] = arg[i].replace("(", "");
				arg[i] = arg[i].replace(")", "");
				arg[i] = arg[i].replace(" ", "");
				String[] arg2 = arg[i].split("<=|<|>=|>|!=|==");
				String cek = "";
				String delimeter = "";
				if (arg[i].contains("<=")) {
					delimeter = "<=";
				} 
				else if (arg[i].contains("<")) {
					delimeter = "<";
				} 
				else if (arg[i].contains(">=")) {
					delimeter = ">=";
				} 
				else if (arg[i].contains(">")) {
					delimeter = ">";
				} 
				else if (arg[i].contains("!=")) {
					delimeter = "!=";
				} 
				else if (arg[i].contains("==")) {
					delimeter = "==";
				}
				if (delimeter.equals("==")) {
					cek = (String) (arg2[0].equalsIgnoreCase(arg2[1]) ? true + "" : false + "");
				} 
				else if (delimeter.equals("!=")) {
					cek = (String) (!arg2[0].equalsIgnoreCase(arg2[1]) ? true + "" : false + "");
				} 
				else {
					cek = arg[i];
				}
				try {
					result = (boolean) engine.eval(cek);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on evaluation", e);
				}
			}
			convertedExpression = convertedExpression.toUpperCase().replace(" ", "");
			convertedExpression = convertedExpression.toUpperCase().replace(arg[i], result + "");
		}
		convertedExpression = convertedExpression.toUpperCase().replace("OR", "||");
		convertedExpression = convertedExpression.toUpperCase().replace("AND", "&&");
		convertedExpression = convertedExpression.toLowerCase();
		return convertedExpression;
	}

	private List<TrTaskBean> listTaskDWithQuestionset(TrTaskH trTaskH,
			AmMsuser userLoggedIn, long uuidFormHistory, AuditContext callerId) {
		Object[][] paramsNative = { { "uuidTaskH", trTaskH.getUuidTaskH() },
				{ "uuidBranch", userLoggedIn.getMsBranch().getUuidBranch() },
				{ "formVersion", trTaskH.getFormVersion() },
				{ "uuidForm", trTaskH.getMsForm().getUuidForm() } };

		List resultAns = this.getManagerDAO().selectAllNative("task.verification.getAllTaskD2QSet", 
				paramsNative, null);
		if (resultAns == null || resultAns.isEmpty()) {
			return Collections.emptyList();
		}

		List<TrTaskBean> listAnswer = new ArrayList<>(resultAns.size());

		for (int i = 0; i < resultAns.size(); i++) {
			Map taskMap = (Map) resultAns.get(i);
			TrTaskBean bean = new TrTaskBean();

			MsQuestion msQuestion = commonLogic.retrieveQuestionByuuidQuestionQset(
					Long.valueOf(String.valueOf(taskMap.get("d6"))), 
					userLoggedIn.getAmMssubsystem().getUuidMsSubsystem(), uuidFormHistory, callerId);

			long uuidLov = Long.valueOf(taskMap.get("d24").toString());
			MsLov msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, uuidLov);
			MsLov msLovByIntLovId = new MsLov();
			MsLov msLovByFinLovId = new MsLov();

			if (!StringUtils.isBlank(taskMap.get("d25").toString())
					&& !"0".equals(taskMap.get("d25").toString())) {
				msLovByIntLovId = this.getManagerDAO().selectOne(MsLov.class, 
						Long.valueOf(taskMap.get("d25").toString()));
			}
			msLovByFinLovId.setUuidLov(Long.valueOf(taskMap.get("d26").toString()));

			bean.setUuidTaskD(Long.valueOf(taskMap.get("d0").toString()));
			bean.setUsrCrt(taskMap.get("d1").toString());
			bean.setDtmCrt((Date) taskMap.get("d2"));
			bean.setUsrUpd(taskMap.get("d3").toString());
			bean.setDtmUpd((Date) taskMap.get("d4"));
			bean.setTrTaskH(trTaskH);
			bean.setMsQuestion(msQuestion);
			bean.setQuestionText(taskMap.get("d7").toString());
			bean.setTextAnswer(taskMap.get("d8").toString());
			bean.setOptionText(taskMap.get("d9").toString());
			bean.setImagePath(taskMap.get("d10").toString());
			if (null != taskMap.get("d11") && null != taskMap.get("d12")) {
				bean.setLatitude(new BigDecimal(taskMap.get("d11").toString()));
				bean.setLongitude(new BigDecimal(taskMap.get("d12").toString()));
			} 
			else {
				bean.setLatitude(null);
				bean.setLongitude(null);
			}
			bean.setAccuracy(Integer.parseInt(taskMap.get("d13").toString()));
			bean.setMcc(Integer.parseInt(taskMap.get("d14").toString()));
			bean.setMnc(Integer.parseInt(taskMap.get("d15").toString()));
			bean.setLac(Integer.parseInt(taskMap.get("d16").toString()));
			bean.setCellId(Integer.parseInt(taskMap.get("d17").toString()));
			bean.setTimestampTask((Date) taskMap.get("d18"));
			bean.setIsGps(taskMap.get("d19").toString());
			bean.setIsGsm(taskMap.get("d20").toString());
			bean.setGeolocationProvider(taskMap.get("d21").toString());
			bean.setIsVisibleQa(taskMap.get("d22").toString());
			bean.setIntTextAnswer(taskMap.get("d23").toString());
			bean.setMsLovByLovId(msLovByLovId);
			bean.setMsLovByIntLovId(msLovByIntLovId);
			bean.setMsLovByFinLovId(msLovByFinLovId);
			bean.setIntOptionText(taskMap.get("d27").toString());
			bean.setFinTextAnswer(taskMap.get("d28").toString());
			bean.setFinOptionText(taskMap.get("d29").toString());
			bean.setFinUseImageFlag(taskMap.get("d30").toString());
			bean.setIsReadonly(taskMap.get("d31").toString());
			bean.setIsConverted(taskMap.get("d33").toString());
			bean.setOptAnswer(clobToString((Clob) taskMap.get("d38")));
			bean.setIsRelevant(taskMap.get("d40").toString());
			bean.setUuidFormHistory(taskMap.get("d41").toString());
			bean.setLob(taskMap.get("d0").toString());

			listAnswer.add(bean);
		}

		return listAnswer;
	}

	private Boolean checkHasImage(List<TrTaskBean> answers) {
		if (answers == null || answers.isEmpty()) {
			return Boolean.FALSE;
		}
		for (Iterator<TrTaskBean> iterator = answers.iterator(); iterator.hasNext();) {
			TrTaskBean bean = iterator.next();

			if (null != bean.getLatitude() && null != bean.getLongitude()
					&& !BigDecimal.ZERO.equals(bean.getLatitude())
					&& !BigDecimal.ZERO.equals(bean.getLongitude())) {
				return Boolean.TRUE;
			}
		}

		return Boolean.FALSE;
	}

	private class MultipleTaskDResult {
		private Map<String, List<String>> multiByGroup;
		private Map<String, List<String>> multiByGroupDesc;

		public MultipleTaskDResult(Map<String, List<String>> multiByGroup2,
				Map<String, List<String>> multiByGroupDesc2) {
			super();
			this.multiByGroup = multiByGroup2;
			this.multiByGroupDesc = multiByGroupDesc2;
		}

		public Map<String, List<String>> getMultiByGroup() {
			return multiByGroup;
		}

		public Map<String, List<String>> getMultiByGroupDesc() {
			return multiByGroupDesc;
		}
	}

	private MultipleTaskDResult removeMultipleTaskDRow(List<TrTaskBean> answers) {
		if (answers == null || answers.isEmpty()) {
			return new MultipleTaskDResult(Collections.EMPTY_MAP, Collections.EMPTY_MAP);
		}

		Map<String, List<String>> multiByGroup = new HashMap<String, List<String>>();
		Map<String, List<String>> multiByGroupDesc = new HashMap<String, List<String>>();
		List<String> optionChecked = new ArrayList<String>();
		List<String> descChecked = new ArrayList<String>();

		long lastUuidQuestion = 0l;
		int idx = 0;
		TrTaskBean taskAnswerPrev = null;

		for (Iterator<? extends TrTaskD> iterator = answers.iterator(); iterator.hasNext();) {
			TrTaskBean taskAnswerCurr = (TrTaskBean) iterator.next();

			MsAnswertype msAnswertype = taskAnswerCurr.getMsQuestion().getMsAnswertype();

			if (taskAnswerCurr.getMsLovByLovId() != null 
					&& MssTool.isMultipleQuestion(msAnswertype.getCodeAnswerType())) {
				optionChecked.add(taskAnswerCurr.getMsLovByLovId().getCode());
				descChecked.add(taskAnswerCurr.getTextAnswer());
			}

			if (taskAnswerPrev != null) {
				MsAnswertype msAnswertypePrev = taskAnswerPrev.getMsQuestion().getMsAnswertype();

				if ((lastUuidQuestion != taskAnswerCurr.getMsQuestion().getUuidQuestion() 
						&& 0l != lastUuidQuestion 
						&& MssTool.isMultipleQuestion(msAnswertypePrev.getCodeAnswerType()))
						|| (!iterator.hasNext() && MssTool.isMultipleQuestion(msAnswertype.getCodeAnswerType()))) {
					TrTaskBean source = null;
					if (iterator.hasNext()) {
						source = taskAnswerPrev;
					} 
					else {
						source = taskAnswerCurr;
					}

					multiByGroup.put(source.getMsLovByLovId().getLovGroup()
							+ "[" + source.getMsQuestion().getRefId() + "]["
							+ idx + "]", optionChecked);
					multiByGroupDesc.put(source.getMsLovByLovId().getLovGroup()
							+ "[" + source.getMsQuestion().getRefId() + "]["
							+ idx + "]", descChecked);
					optionChecked = new ArrayList<String>();
					descChecked = new ArrayList<String>();
					idx++;
				}
			}

			if (lastUuidQuestion == taskAnswerCurr.getMsQuestion().getUuidQuestion()) {
				iterator.remove();
			}

			taskAnswerPrev = taskAnswerCurr;
			lastUuidQuestion = taskAnswerCurr.getMsQuestion().getUuidQuestion();
		}

		return new MultipleTaskDResult(multiByGroup, multiByGroupDesc);
	}

	private Map<String, LinkedHashMap<String, String>> prepareLov(List<TrTaskBean> listAnswer, 
			long uuidBranch) {
		if (listAnswer == null || listAnswer.isEmpty()) {
			return Collections.emptyMap();
		}

		NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);

		Map<String, LinkedHashMap<String, String>> mapOptionByLov = new HashMap<String, 
				LinkedHashMap<String, String>>();
		for (int j = 0; j < listAnswer.size(); j++) {
			TrTaskBean taskAnswer = listAnswer.get(j);

			MsAnswertype msAnswertype = taskAnswer.getMsQuestion().getMsAnswertype();
			if (StringUtils.isNotEmpty(taskAnswer.getOptAnswer())) {
				LinkedHashMap<String, String> optionByLov = new LinkedHashMap<String, String>();

				String optAnswer = taskAnswer.getOptAnswer();
				String[] listOpt = StringUtils.split(optAnswer, "|||");
				for (int i = 0; i < listOpt.length; i++) {
					String[] opt = StringUtils.split(listOpt[i], "@@@");
					optionByLov.put(opt[0], opt[1]);
				}
				mapOptionByLov.put(taskAnswer.getMsQuestion().getLovGroup()
						+ "[" + taskAnswer.getMsQuestion().getRefId() + "]["
						+ j + "]", optionByLov);
			} 
			else if (taskAnswer.getMsLovByLovId() != null) {
				if (StringUtils.isNotBlank(taskAnswer.getMsQuestion().getLovGroup())) {
					LinkedHashMap<String, String> optionByLov = this.mapOptAnsByBranch(uuidBranch, taskAnswer);
					mapOptionByLov.put(taskAnswer.getMsQuestion().getLovGroup()
							+ "[" + taskAnswer.getMsQuestion().getRefId()
							+ "][" + j + "]", optionByLov);
				}
			}

			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(msAnswertype.getCodeAnswerType())) {
				if (taskAnswer.getTextAnswer() != null && "0".equals(taskAnswer.getTextAnswer())) {
					taskAnswer.setTextAnswer(formatKurs.format(NumberUtils.toDouble(taskAnswer.getTextAnswer())));
				}
			}
		}

		return mapOptionByLov;
	}

	private TaskDocumentBean retrieveTaskDJsonAsBean(TrTaskH trTaskH,
			AuditContext callerId) {
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		if (docDb == null || StringUtils.isBlank(docDb.getDocument())) {
			return null;
		}
		Gson gson = new Gson();

		TaskDocumentBean document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
		return document;
	}

	private List<TrTaskBean> listTaskDJsonWithQuestionSet(TaskDocumentBean document, 
			long uuidFormHistory, AuditContext callerId) {
		List<MsFormquestionset> questionset = this.commonLogic
				.retrieveMsFormquestionset(uuidFormHistory, callerId);
		if (questionset == null || questionset.isEmpty()) {
			return Collections.emptyList();
		}
		List<TrTaskBean> listAnswer = new ArrayList<>();
		MsLov emptyMsLov = new MsLov();
		for (Iterator<MsFormquestionset> iterator = questionset.iterator(); iterator.hasNext();) {
			MsFormquestionset question = iterator.next();
			TrTaskBean bean = new TrTaskBean();

			bean.setUuidTaskD(0L);
			bean.setMsQuestion(question.getMsQuestion());
			bean.setQuestionText(question.getQuestionLabel());
			bean.setIsReadonly(question.getIsReadonly());
			bean.setUuidFormHistory(String.valueOf(question.getMsFormhistory().getUuidFormHistory()));
			bean.setMsLovByLovId(emptyMsLov); // diperlukan saat prepareLov

			boolean isRelevant = this.isInRelevant(questionset, question.getRefId());
			bean.setIsRelevant((isRelevant) ? "1" : "0");
			listAnswer.add(bean);

			int idxQ = document.findAnswerIndex(question.getMsQuestion().getUuidQuestion());
			if (idxQ == -1) {
				continue;
			}
			AnswerBean answer = document.getAnswers().get(idxQ);

			bean.setTextAnswer(answer.getTxtAnswer());

			if (answer.getOptAnswers() != null && !answer.getOptAnswers().isEmpty()) {
				long uuidLov = answer.getOptAnswers().get(0).getUuid();
				bean.setMsLovByLovId(this.getManagerDAO().selectOne(MsLov.class, uuidLov));
				bean.setOptionText(answer.getOptAnswers().get(0).getDesc());
			}

			if (answer.getLocation() != null) {
				com.adins.mss.model.taskdjson.LocationBean location = answer.getLocation();

				if (location.getLat() != null) {
					bean.setLatitude(new BigDecimal(location.getLat().doubleValue()));
				}
				if (location.getLng() != null) {
					bean.setLongitude(new BigDecimal(location.getLng().doubleValue()));
				}
				bean.setAccuracy(location.getAccuracy());
			}
			if (answer.getLobAnswer() != null && answer.getLobAnswer().isHasImage()) {
				bean.setLob(String.valueOf(answer.getLobAnswer().getId()));
			} 
			else {
				bean.setLob("0");
			}
		}

		return listAnswer;
	}

	private boolean isInRelevant(List<MsFormquestionset> questionset,
			String refId) {
		if (StringUtils.isBlank(refId) || questionset == null || questionset.isEmpty()) {
			return false;
		}
		for (Iterator<MsFormquestionset> iterator = questionset.iterator(); iterator.hasNext();) {
			MsFormquestionset question = iterator.next();
			if (StringUtils.isBlank(question.getRelevant())) {
				continue;
			}
			if (StringUtils.containsIgnoreCase(question.getRelevant(), refId)) {
				return true;
			}
		}

		return false;
	}

	private MultipleTaskDResult processMultipleAnswerInJson(
			List<? extends TrTaskD> taskDs, TaskDocumentBean document) {
		if (taskDs == null || taskDs.isEmpty() || document.getAnswers() == null
				|| document.getAnswers().isEmpty()) {
			return new MultipleTaskDResult(Collections.EMPTY_MAP, Collections.EMPTY_MAP);
		}

		Map<String, List<String>> multiByGroup = new HashMap<String, List<String>>();
		Map<String, List<String>> multiByGroupDesc = new HashMap<String, List<String>>();

		int idx = 0;
		for (Iterator iterator = taskDs.iterator(); iterator.hasNext();) {
			TrTaskD taskD = (TrTaskD) iterator.next();
			String answerType = taskD.getMsQuestion().getMsAnswertype().getCodeAnswerType();

			if (!MssTool.isMultipleQuestion(answerType)) {
				continue;
			}

			int idxAnswer = document.findAnswerIndex(taskD.getMsQuestion().getUuidQuestion());
			AnswerBean answer = document.getAnswers().get(idxAnswer);
			if (idxAnswer == -1 || answer == null
					|| answer.getOptAnswers() == null
					|| answer.getOptAnswers().isEmpty()) {
				continue;
			}

			List<String> optionChecked = new ArrayList<String>();
			List<String> descChecked = new ArrayList<String>();

			List<OptionBean> checkList = answer.getOptAnswers();
			for (Iterator<OptionBean> checkIter = checkList.iterator(); checkIter.hasNext();) {
				OptionBean optionBean = checkIter.next();
				optionChecked.add(optionBean.getCode());
				descChecked.add(optionBean.getFreeText());
			}

			multiByGroup.put(taskD.getMsQuestion().getLovGroup() + "["
					+ taskD.getMsQuestion().getRefId() + "][" + idx + "]", optionChecked);
			multiByGroupDesc.put(taskD.getMsQuestion().getLovGroup() + "["
					+ taskD.getMsQuestion().getRefId() + "][" + idx + "]", descChecked);
			idx++;
		}

		return new MultipleTaskDResult(multiByGroup, multiByGroupDesc);
	}

	private AnswerBean transformToJsonByAnswerType(TrTaskH taskH,
			long uuidFormHistory, MsQuestion quest, String textAnswer,
			String latitude, String longitude, TaskDocumentBean document,
			int idxAnswer, String[] textAnswers, AuditContext auditContext) {
		Assert.notNull(document);

		AnswerBean answer = (idxAnswer == -1) ? new AnswerBean() : document.getAnswers().get(idxAnswer);
		QuestionBean questionBean = new QuestionBean(quest.getUuidQuestion(), quest.getQuestionLabel(), 
				quest.getRefId(), quest.getMsAnswertype().getCodeAnswerType(),
				(quest.getMsOrdertag() == null ? null : quest.getMsOrdertag().getTagName()), 
				(quest.getMsAssettag() == null ? null : quest.getMsAssettag().getAssetTagName()),
				(quest.getMsCollectiontag() == null ? null : quest.getMsCollectiontag().getTagName()));
		answer.setQuestion(questionBean);

		String answerType = quest.getMsAnswertype().getCodeAnswerType();
		String lovGroup = quest.getLovGroup();

		boolean flagOption = false;

		if (GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(answerType)
				|| GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(answerType)) {
			flagOption = true;
			String[] selectedAnswer = textAnswer.replace("#", "").split("\\^");
			if (selectedAnswer.length > 0) {
				List<OptionBean> options = (answer.getFinOptAnswers() == null) 
						? new ArrayList<OptionBean>() : answer.getFinOptAnswers();

				MsLov lovtmp = this.getLovInJson(quest.getUuidQuestion(), textAnswers, selectedAnswer[0], 
						lovGroup, uuidFormHistory, document.getAnswers());
				if (lovtmp != null) {
					OptionBean option = new OptionBean();
					option.setUuid(lovtmp.getUuidLov());
					option.setCode(lovtmp.getCode());
					option.setDesc(lovtmp.getDescription());
					if (selectedAnswer.length > 1) {
						option.setFreeText(selectedAnswer[1]);
					}
					if (!options.contains(option)) {
						options.add(option);
					}
					answer.setFinOptAnswers(options);
				}
			}
		} 
		else if (MssTool.isMultipleQuestion(answerType)) {
			flagOption = true;
			String[] multiAnswer = textAnswer.split("#");
			List<MsLov> checkboxAns = new ArrayList<MsLov>();
			List<String> checkboxDescAns = new ArrayList<String>();
			for (int j = 0; j < multiAnswer.length; j++) {
				String[] multiAns = multiAnswer[j].split("\\^");
				MsLov lov = this.getLovInJson(quest.getUuidQuestion(),
						textAnswers, multiAns[0], lovGroup, uuidFormHistory, document.getAnswers());
				if (lov != null) {
					checkboxAns.add(lov);
				}
				if (GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
					if (multiAns.length > 1) {
						checkboxDescAns.add(multiAns[1]);
					} 
					else {
						checkboxDescAns.add(StringUtils.EMPTY);
					}
				}
			}

			// update
			if (!checkboxAns.isEmpty()) {
				List<OptionBean> options = (answer.getFinOptAnswers() == null) ? new ArrayList<OptionBean>()
						: answer.getFinOptAnswers();
				for (int k = 0; k < checkboxAns.size(); k++) {
					if (checkboxAns.get(k) == null) {
						continue;
					}

					OptionBean option = new OptionBean();
					option.setUuid(checkboxAns.get(k).getUuidLov());
					option.setCode(checkboxAns.get(k).getCode());
					option.setDesc(checkboxAns.get(k).getDescription());
					if (!checkboxDescAns.isEmpty()) {
						option.setFreeText(checkboxDescAns.get(k));
					}
					if (!options.contains(option)) {
						options.add(option);
					}
					answer.setFinOptAnswers(options);
				}
			}
		} 
		else if (GlobalVal.ANSWER_TYPE_RADIO.equals(answerType)
				|| GlobalVal.ANSWER_TYPE_DROPDOWN.equals(answerType)) {
			flagOption = true;
			String code = textAnswer.replace("^", "").replace("#", "");

			List<OptionBean> options = (answer.getFinOptAnswers() == null) ? new ArrayList<OptionBean>()
					: answer.getFinOptAnswers();

			MsLov lov = this.getLovInJson(quest.getUuidQuestion(), textAnswers, code, lovGroup, 
					taskH.getMsForm().getUuidForm(), document.getAnswers());
			if (lov != null) {
				OptionBean option = new OptionBean();
				option.setUuid(lov.getUuidLov());
				option.setCode(lov.getCode());
				option.setDesc(lov.getDescription());
				if (!options.contains(option)) {
					options.add(option);
				}
				answer.setFinOptAnswers(options);
			}
		} 
		else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType) && !flagOption) {
			BigDecimal decLatitude = (NumberUtils.isNumber(latitude)) ? new BigDecimal(latitude) : null;
			BigDecimal decLongitude = (NumberUtils.isNumber(longitude)) ? new BigDecimal(longitude) : null;

			if (decLatitude != null) {
				com.adins.mss.model.taskdjson.LocationBean locationBean = (answer.getLocation() == null) 
						? new com.adins.mss.model.taskdjson.LocationBean() : answer.getLocation();
				locationBean.setLat(decLatitude.doubleValue());
				locationBean.setLng(decLongitude.doubleValue());
				answer.setLocation(locationBean);
				answer.setFinTxtAnswer(answer.getTxtAnswer());
			}
		} 
		else if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType) && StringUtils.isNotBlank(textAnswer)) {
			answer.setFinTxtAnswer(textAnswer.replace(".00", "").replace(",", ""));
		} 
		else if (!flagOption) {
			// text,textmultiline,numeric,decimal,date,datetime,text with
			// suggestion,luonline
			answer.setFinTxtAnswer(StringUtils.stripToNull(textAnswer));
		}

		return answer;
	}

	private MsLov getLovInJson(long uuidQuestion, String[] textAnswers,
			String code, String lovGroup, long uuidFormHistory,
			List<AnswerBean> answers) {

		Object[][] paramsRlv = {{ Restrictions.eq("msQuestion.uuidQuestion", uuidQuestion) },
				{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) } };
		// MsFormQuestionset
		MsFormquestionset msQuestionRel = this.getManagerDAO().selectOne(MsFormquestionset.class, paramsRlv);

		List<String> constraints = new ArrayList<String>();
		if (null != msQuestionRel && StringUtils.isNotBlank(msQuestionRel.getChoiceFilter())) {
			String[] choiceFilters = msQuestionRel.getChoiceFilter().split(",");
			for (int a = choiceFilters.length - 1; a < choiceFilters.length && a >= 0; a--) {
				String refId = choiceFilters[a].replace("{", StringUtils.EMPTY).replace("}", StringUtils.EMPTY);

				int idxTaskDs = 0;
				for (AnswerBean answer : answers) {
					if (answer == null) {
						idxTaskDs++;
						continue;
					}

					String refIdAnswer = answer.getQuestion() == null ? null : answer.getQuestion().getRefId();
					if (StringUtils.equals(refId, refIdAnswer)) {
						constraints.add(textAnswers[idxTaskDs]);
					}
					idxTaskDs++;
				}
			}
		}

		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[] { Restrictions.eq("lovGroup", lovGroup) });
		paramStack.push(new Object[] { Restrictions.eq("code", code) });
		paramStack.push(new Object[] { Restrictions.eq("isActive", "1") });

		int x = 1;
		for (int j = constraints.size() - 1; j < constraints.size() && j >= 0; j--) {
			paramStack.push(new Object[] { Restrictions.eq("constraint" + x,
					constraints.get(j)) });
			x++;
		}

		Object[][] sqlParams = new Object[paramStack.size()][2];
		for (int k = 0; k < paramStack.size(); k++) {
			Object[] objects = paramStack.get(k);
			sqlParams[k] = objects;
		}

		MsLov lov = this.getManagerDAO().selectOne(MsLov.class, sqlParams);

		return lov;
	}
}