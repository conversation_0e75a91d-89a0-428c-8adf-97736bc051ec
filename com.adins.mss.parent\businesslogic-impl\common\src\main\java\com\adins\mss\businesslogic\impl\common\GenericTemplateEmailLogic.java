package com.adins.mss.businesslogic.impl.common;

import java.util.Date;
import java.util.Map;

import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.mss.businesslogic.api.common.TemplateEmailLogic;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.model.MsTemplate;

@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericTemplateEmailLogic extends BaseLogic implements
		TemplateEmailLogic {
	private AuditInfo auditInfo;

	public GenericTemplateEmailLogic() {
		String[] pkCols = { "uuidTemplate" };
		String[] pkDbCols = { "UUID_TEMPLATE" };
		String[] cols = { "uuidTemplate", "description", "subject", 
				"sender", "templateName", "templateType", "content", 
				"contentType" };
		String[] dbCols = { "UUID_TEMPLATE", "DESCRIPTION", "SUBJECT", "SENDER",
				"TEMPLATE_NAME", "TEMPLATE_TYPE", "CONTENT", "CONTENT_TYPE" };
		this.auditInfo = new AuditInfo("MS_TEMPLATE", pkCols, pkDbCols, cols,
				dbCols);
	}

	@Override
	public MsTemplate getTemplate(String params, AuditContext callerId) {
		MsTemplate result = this.getManagerDAO().selectOne(MsTemplate.class, Long.valueOf(params));
		return result;
	}

	@Override
	public Map<String, Object> listTemplate(Object params, Object orders,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = null;
		result = this.getManagerDAO().selectAll(MsTemplate.class, params,
				orders, pageNumber, pageSize);
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void updateTemplate(MsTemplate mstemp, AuditContext callerId) {
		MsTemplate dbModel = this.getManagerDAO().selectOne(
				MsTemplate.class, mstemp.getUuidTemplate());

		dbModel.setDescription(mstemp.getDescription());
		dbModel.setSubject(mstemp.getSubject());
		dbModel.setSender(mstemp.getSender());
		dbModel.setTemplateName(mstemp.getTemplateName());
		dbModel.setTemplateType("EMAIL");
		dbModel.setContent(mstemp.getContent());
		dbModel.setContentType("text/html");
		dbModel.setUsrUpd(callerId.getCallerId());
		dbModel.setDtmUpd(new Date());

		this.auditManager.auditEdit(dbModel, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(dbModel);
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void insertTemplate(MsTemplate mstemp, AuditContext callerId) {
		mstemp.setDtmCrt(new Date());
		mstemp.setUsrCrt(callerId.getCallerId());
		mstemp.setTemplateType("EMAIL");
		mstemp.setContentType("text/html");
		
		this.getManagerDAO().insert(mstemp);
		this.auditManager.auditAdd(mstemp, auditInfo, callerId.getCallerId(), "");
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteTemplate(String uuid, AuditContext callerId) {
		MsTemplate mstemp = new MsTemplate();
		mstemp.setUuidTemplate(Long.valueOf(uuid));

		this.auditManager.auditDelete(mstemp, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().delete(mstemp);
	}
}
