package com.adins.mss.businesslogic.impl.common;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.UpdateReadOpenTaskLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.custom.MessageEventBean;
import com.adins.mss.services.model.common.UpdateReadOpenTaskBean;
import com.google.gson.Gson;

public class GenericUpdateReadOpenTaskLogic extends BaseLogic implements UpdateReadOpenTaskLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericUpdateReadOpenTaskLogic.class);
	
	private MessageSource messageSource;
	private CommonLogic commonLogic;
	
	public MessageSource getMessageSource() {
		return messageSource;
	}

	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateReadOpenTask(UpdateReadOpenTaskBean taskH, AuditContext callerId){			
		TrTaskH bean = this.getManagerDAO().selectOne(TrTaskH.class, NumberUtils.toLong(taskH.getUuidTaskH()));
		Date readDate = taskH.getReadDate();
		Date startDtm = taskH.getStartDtm();

		if (bean.getReadDate() == null && bean.getStartDtm() == null && bean.getDownloadDate() != null) {
			bean.setDtmUpd(new Date());
			bean.setReadDate(readDate);			
			MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_READ, callerId);
			bean.setMsStatusmobile(msm);
		}
		if (readDate != null && startDtm != null && bean.getDownloadDate() != null) {
			bean.setDtmUpd(new Date());
			bean.setStartDtm(startDtm);
			MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_ON_PROGRESS, callerId);
			bean.setMsStatusmobile(msm);
		}
		
		this.getManagerDAO().update(bean);
	}
	
	@Override
	@Async
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	public void sendMessageToJms(UpdateReadOpenTaskBean taskH, AuditContext callerId) {
		TrTaskH bean = this.getManagerDAO().selectOne(TrTaskH.class, NumberUtils.toLong(taskH.getUuidTaskH()));
		Date readDate = taskH.getReadDate();
		Date startDtm = taskH.getStartDtm();
		
		AmMsuser user = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", 
			new Object[][] {{"uuidMsUser", NumberUtils.toLong(callerId.getCallerId())}});
		//SSE
		Gson gson = new Gson();
		MessageEventBean msgBean = new MessageEventBean();
		msgBean.setEvent(GlobalVal.EVENT_NEWS);
		msgBean.setUuidUser(user.getImei()+user.getUuidMsUser());
		msgBean.setDate(DateFormatUtils.format(new Date(), "HH:mm:ss"));
		String task = StringUtils.EMPTY;
		if(GlobalVal.SUBSYSTEM_MC.equals(user.getAmMssubsystem().getSubsystemName())){
			task = (bean.getAgreementNo()!=null?bean.getAgreementNo():bean.getTaskId())+" - "+bean.getCustomerName();
		} 
		else{
			task = (bean.getApplNo()!=null?bean.getApplNo():bean.getTaskId())+" - "+bean.getCustomerName();
		}
		
		Object[] msgArgs = {user.getLoginId(), user.getFullName(), task};
		if(readDate != null && bean.getStartDtm() == null && bean.getDownloadDate() != null){
			msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.readtask", msgArgs, this.retrieveLocaleAudit(callerId)));
			try{
				msgBean.setUuidMessageBean(this.getManagerDAO().getUUID());
				sendMessage(gson.toJson(msgBean));
				LOG.info("Send message to JMS Success");
			}
			catch(Exception e){
				LOG.error("Send message to JMS Failed", e);
			}
		}
		else if(readDate != null && startDtm != null && bean.getDownloadDate() != null){
			msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.starttask", msgArgs, this.retrieveLocaleAudit(callerId)));

			try{
				msgBean.setUuidMessageBean(this.getManagerDAO().getUUID());
				sendMessage(gson.toJson(msgBean));
				LOG.info("Send message to JMS Success");
			}
			catch(Exception e){
				LOG.error("Send message to JMS Failed", e);
			}
		}
		
	}
}
