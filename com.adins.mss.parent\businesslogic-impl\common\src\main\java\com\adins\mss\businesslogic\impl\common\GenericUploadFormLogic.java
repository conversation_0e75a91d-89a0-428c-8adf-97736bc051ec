package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Row;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.UploadFormLogic;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.util.MssTool;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericUploadFormLogic extends BaseLogic implements UploadFormLogic  {

    private static final Logger LOG = LoggerFactory.getLogger(GenericUploadFormLogic.class);
    
	private static final String[] TEMPLATE_HEADER_QUESTION = {  "Question Label", "Identifier", "Answer Type", "Image Quality",
		"LOV Group" ,"Max Length", "Regex Pattern", "Asset Tagging","Is Mandatory","Read Only","Is Visible", "Is Active"};
	private static final int[] HEADER_COLUMN_WIDTH_QUESTION = { 30 * 256, 20 * 256, 20 * 256, 20 * 256, 
		30 * 256, 20 * 256, 20 * 256, 30 * 256, 20 * 256, 20 * 256, 20 * 256  ,20 * 256 };
	
	private static final String[] TEMPLATE_HEADER_GROUP = {  "Question Group Label","Question Label" ,  "Is Active"};
	private static final int[] HEADER_COLUMN_WIDTH_GROUP = { 30 * 256, 20 * 256, 20 * 256 };
	
	private static final String[] TEMPLATE_HEADER_FORM = {  "Form Name", "Question Group Label", "Form Authorize" ,"Form Category","Is Active"};
	private static final int[] HEADER_COLUMN_WIDTH_FORM = { 30 * 256, 30 * 256, 20 * 256 ,20 * 256, 20 * 256};

	private static final String[] TEMPLATE_HEADER_RELEVANSI = {  "Question Label", "Question Group Label", "Form Name" , "Relevant", "Choice Filter","Calculate" };
	private static final int[] HEADER_COLUMN_WIDTH_RELEVANSI = { 30 * 256, 30 * 256, 20 * 256 ,20 * 256, 20 * 256, 20 * 256};
	
	private static final String[] TEMPLATE_HEADER_MASTER = { "Answer Type", "LOV Group", "Image Quality","Form Category", "Tagging"};
	private static final int[] HEADER_COLUMN_WIDTH_MASTER = { 30 * 256, 20 * 256, 20 * 256,20 * 256,20 * 256 };
	
	@Override
	public List listUploadForm(Object params, Object orders, AuditContext callerId) {

		List result = this.getManagerDAO().selectAllNative("eform.upload.getFormList", params, orders);
		return result;
	}
	
	@Override
	public String getSubsystem(String callerId){
		String subsystem = "";
		Object[][] params = {{Restrictions.eq("uuidMsUser", Long.valueOf(callerId))}};
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, params);
		if (null != amMsuser) {
			subsystem = String.valueOf(amMsuser.getAmMssubsystem().getUuidMsSubsystem());
		}
		
		return subsystem;
	}
	
	@Override
	public List listQuestion(Object params, AuditContext callerId) {
		
		List result = this.getManagerDAO().selectAllNative("eform.upload.listQuestion", params, null);
		return result;
	}

	@Override
	public List<Map<String, Object>> listGroup(AuditContext callerId) {
		
		List group = new ArrayList();
		List listGroup = this.getManagerDAO().selectAllNative(
				"eform.lov.getGroupLov", null, null);

		if (!listGroup.isEmpty()) {
			for (int x = 0; x < listGroup.size(); x++) {
				Map map = (Map) listGroup.get(x);
				group.add(map.get("d0"));
			}
		}

		return group;
	}

	@Override
	public byte[] exportExcel(AuditContext callerId) {

		HSSFWorkbook workbook = this.createXlsTemplate(callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	public HSSFWorkbook createXlsTemplate(AuditContext callerId) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet1 = workbook.createSheet("Question List");
			HSSFSheet sheet2 = workbook.createSheet("Question Group");
			HSSFSheet sheet3 = workbook.createSheet("Form");
			HSSFSheet sheet4 = workbook.createSheet("Relevansi");
			HSSFSheet sheet5 = workbook.createSheet("Master");
			this.createHeader(workbook, sheet1, sheet2, sheet3, sheet4,sheet5, callerId);
			this.setTextDataFormat(workbook, sheet1, sheet2, sheet3, sheet4, sheet5, callerId);
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createHeader(HSSFWorkbook workbook, HSSFSheet sheet1, HSSFSheet sheet2,
			HSSFSheet sheet3, HSSFSheet sheet4, HSSFSheet sheet5, AuditContext callerId) {
		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);

		HSSFRow row1 = sheet1.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_QUESTION.length; i++) {
			HSSFCell cell = row1.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_QUESTION[i]);
			cell.setCellStyle(style);
			sheet1.setColumnWidth(i, HEADER_COLUMN_WIDTH_QUESTION[i]);
		}
		
		HSSFRow row2 = sheet2.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_GROUP.length; i++) {
			HSSFCell cell = row2.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_GROUP[i]);
			cell.setCellStyle(style);
			sheet1.setColumnWidth(i, HEADER_COLUMN_WIDTH_GROUP[i]);
		}
		
		HSSFRow row3 = sheet3.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_FORM.length; i++) {
			HSSFCell cell = row3.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_FORM[i]);
			cell.setCellStyle(style);
			sheet1.setColumnWidth(i, HEADER_COLUMN_WIDTH_FORM[i]);
		}
		
		HSSFRow row4 = sheet4.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_RELEVANSI.length; i++) {
			HSSFCell cell = row4.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_RELEVANSI[i]);
			cell.setCellStyle(style);
			sheet1.setColumnWidth(i, HEADER_COLUMN_WIDTH_RELEVANSI[i]);
		}
		
		HSSFRow row5 = sheet5.createRow(0);

		for (int i = 0; i < TEMPLATE_HEADER_MASTER.length; i++) {
			HSSFCell cell = row5.createCell(i);
			cell.setCellValue(TEMPLATE_HEADER_MASTER[i]);
			cell.setCellStyle(style);
			sheet1.setColumnWidth(i, HEADER_COLUMN_WIDTH_MASTER[i]);
		}
	}

	private void setTextDataFormat(HSSFWorkbook workbook, HSSFSheet sheet1, HSSFSheet sheet2 ,
			HSSFSheet sheet3 ,HSSFSheet sheet4, HSSFSheet sheet5, AuditContext callerId) {
		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("General"));

		for (int i = 0; i < TEMPLATE_HEADER_QUESTION.length; i++) {
			sheet1.setDefaultColumnStyle(i, style);
			sheet2.setDefaultColumnStyle(i, style);
			sheet3.setDefaultColumnStyle(i, style);
			sheet4.setDefaultColumnStyle(i, style);
			sheet5.setDefaultColumnStyle(i, style);
		}
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public byte[] processSpreadSheetFile(File uploadedFile, AuditContext callerId) {
		byte[] errorUploadByte = null;

			try {
				this.parseSpreadsheetToUploadForm(uploadedFile,callerId);
			} 
			catch (IOException e) {
				errorUploadByte = this.errorUpload();
				return errorUploadByte;
			}

		return errorUploadByte;
	}
	
	//error condition untuk di action
	private byte[] errorUpload(){
		byte[] tmp = new byte[1];
		tmp[0]=1;//for condition in action
		return tmp;
	}

	public String getLovExist(MsLov msl) {

		String lovGroup = StringUtils.trimToNull(msl.getLovGroup());
		String code = StringUtils.trimToNull(msl.getCode());
		String constraint1 = StringUtils.trimToNull(msl.getConstraint1());
		String constraint2 = StringUtils.trimToNull(msl.getConstraint2());
		String constraint3 = StringUtils.trimToNull(msl.getConstraint3());
		String constraint4 = StringUtils.trimToNull(msl.getConstraint4());
		String constraint5 = StringUtils.trimToNull(msl.getConstraint5());

		Object[] cons1 = (constraint1 == null) ? new Object[] { Restrictions.isNull("constraint1") }
		        : new Object[] { Restrictions.eq("constraint1", constraint1) };
		
		Object[] cons2 = (constraint2 == null) ? new Object[] { Restrictions.isNull("constraint2") }
		        : new Object[] { Restrictions.eq("constraint2", constraint2) };
		
		Object[] cons3 = (constraint3 == null) ? new Object[] { Restrictions.isNull("constraint3") }
		        : new Object[] { Restrictions.eq("constraint3", constraint3) };
		
		Object[] cons4 = (constraint4 == null) ? new Object[] { Restrictions.isNull("constraint4") }
		        : new Object[] { Restrictions.eq("constraint4", constraint4) };
		
		Object[] cons5 = (constraint5 == null) ? new Object[] { Restrictions.isNull("constraint5") }
		        : new Object[] { Restrictions.eq("constraint5", constraint5) };		

		Object[][] queryParams = { { Restrictions.eq("lovGroup", lovGroup) },
				{ Restrictions.eq("code", code) },
				cons1, cons2, cons3, cons4, cons5 };

		MsLov result = this.getManagerDAO().selectOne(MsLov.class, queryParams);

		String uuid = "";
		if (result != null) {
			uuid = String.valueOf(result.getUuidLov());
		}
		
		return uuid;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private Map parseSpreadsheetToUploadForm( File uploadedFile, AuditContext callerId ) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		List<MsQuestion> result = new ArrayList<>();
		List<MsQuestion> resultError = new ArrayList<>();

		FileInputStream inputStream = new FileInputStream(uploadedFile);
		
		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		HSSFSheet sheet = wb.getSheetAt(0);
		HSSFCell cell;
		HSSFRow row;
		
		int tmp = 0;
		int cols = 0;
		int rows = sheet.getPhysicalNumberOfRows();
		
		for(int i = 0; i < 10 || i < rows; i++) {
	        row = sheet.getRow(i);
	        if(row != null) {
	            tmp = sheet.getRow(i).getPhysicalNumberOfCells();
	            if(tmp > cols) cols = tmp;
	        }
	    }
		
		for(int i = 0; i < rows; i++) {
	        row = sheet.getRow(i);
	        if(row != null) {
	            for(int j = 0; j < cols; j++) {
	                cell = row.getCell(j);
	                if(cell != null) {
	                    // Your code here
	                	for (int r = 1; r < rows; r++) {
	            			row = sheet.getRow(r);
	            			if (row == null) {
	            				continue;
	            			}
	            			
	                		boolean isEmptyRow = checkEmptyRow(row); 
	                				
	                		if (isEmptyRow == true){
	                			continue;
	                		}
	                		
	                		Object [][] paramUser = {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}};
	                		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, paramUser);

	                		MsQuestion msQuestion = new MsQuestion();
	            			for (int c = 0; c < 9; c++) { //kolom di excel
	            				cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
	            								
	            				String value = "";
	            				
	            				// if intValue -1, then sequence is posted with string
	            				msQuestion.setDtmCrt(new Date());
	            				msQuestion.setUsrCrt(callerId.getCallerId().toString());
	            				msQuestion.setAmMssubsystem(amMsuser.getAmMssubsystem());
	            				if (cell != null) {
	            					switch (cell.getCellType()) {
	                					case HSSFCell.CELL_TYPE_NUMERIC:
	                						value = String.valueOf((int) cell.getNumericCellValue());
	                						break;
	                
	                					case HSSFCell.CELL_TYPE_STRING:
	                						value = cell.getStringCellValue();
	                						break;
	                
	                					default:
	            					}
	            				}

	            				switch (c) {
	                				case 0:
	                					msQuestion.setQuestionLabel(value);
	                					break;
	                				case 1:
	                					msQuestion.setIsActive(value);
	                					break;
	                				case 2:
	                					msQuestion.setRefId(value);
	                					break;
	                				case 3:
	                					msQuestion.setLovGroup(value);
	                					break;
	                				case 4:
	                					msQuestion.setIsVisible(value);
	                					break;
	                				case 5:
	                					msQuestion.setIsMandatory(value);
	                					break;
	                				case 6:
	                					Object[][] params = {{"codeAnswerType", value}};
	                					MsAnswertype msAnswertype = this.getManagerDAO().selectOne(MsAnswertype.class, params);
	                					if(null != msAnswertype){
	                						msQuestion.setMsAnswertype(msAnswertype);
	                					}
	                					break;
	                				case 7:
	                					msQuestion.setIsReadonly(value);
	                					break;
	                				case 8:	                					
	                					if(! MssTool.isMultipleQuestion(msQuestion.getMsAnswertype().getCodeAnswerType()) ||
	                							! MssTool.isImageQuestion(msQuestion.getMsAnswertype().getCodeAnswerType())){
	                						msQuestion.setMaxLength(Integer.valueOf(value));
	                					}
	                					break;
	            				}
	            				wb.close();
	            			}
	            			result.add(msQuestion);
	            			this.getManagerDAO().insert(msQuestion);
	            		}
	            		
	            		paramParse.put("result", result);
	            		paramParse.put("resultError", resultError);
	                }
	            }
	        }
	    }
		return paramParse;
		
	}

	private boolean checkEmptyRow(HSSFRow row) {
		String[] isEmptyCell = new String [10]  ;
		Arrays.fill(isEmptyCell, "");

		for (int c = 0; c < 10; c++) {
			HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
			if (cell == null){
				isEmptyCell[c]="empty";
			}
		}
		
		if ("empty".equals(isEmptyCell[0]) && "empty".equals(isEmptyCell[1]) 
				&& "empty".equals(isEmptyCell[2])&& "empty".equals(isEmptyCell[3])
				&& "empty".equals(isEmptyCell[4])&& "empty".equals(isEmptyCell[5])
				&& "empty".equals(isEmptyCell[6])&& "empty".equals(isEmptyCell[7])
				&& "empty".equals(isEmptyCell[8])&& "empty".equals(isEmptyCell[9])){
			return true;
		}
		else{
			return false;
		}
	}
}
