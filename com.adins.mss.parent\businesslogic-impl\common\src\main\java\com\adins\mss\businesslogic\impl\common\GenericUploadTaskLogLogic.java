package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.UploadTaskLogLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.exceptions.UploadTaskException;
import com.adins.mss.exceptions.UploadTaskException.Reason;
import com.adins.mss.model.TrUploadtasklog;

@SuppressWarnings("rawtypes")
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericUploadTaskLogLogic extends BaseLogic implements 
		UploadTaskLogLogic, MessageSourceAware {
	
	@Autowired
	private MessageSource messageSource;
	
	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	@Override
	public Map<String, Object> getUploadTaskLogList(Object params, 
			Object paramsCount, Object orders, AuditContext callerId) {
		Map<String, Object> mapResult = new HashMap<String, Object>();
		List list = this.getManagerDAO().selectAllNative(
				"task.uploadtasklog.getList", params, orders);
		Integer count = (Integer) this.getManagerDAO().selectOneNative(
				"task.uploadtasklog.getListCount", paramsCount);
		
		mapResult.put(GlobalKey.MAP_RESULT_LIST, list);
		mapResult.put(GlobalKey.MAP_RESULT_SIZE, count);
		return mapResult;
	}

	@Override
	public Map<String, Object> exportErrorUploadTaskFile(String seqNo, 
			AuditContext auditContext) {
		Map<String, Object> mapResult = new HashMap<String, Object>();
		Object[][] param = { {Restrictions.eq("seqno", Long.valueOf(seqNo))} };
		TrUploadtasklog trUploadtasklog = this.getManagerDAO().selectOne(
				TrUploadtasklog.class, param);
		byte[] exp = null;
		if("csv".equalsIgnoreCase(trUploadtasklog.getFileExtension())) {
			exp = getReportResultCSV(trUploadtasklog, auditContext);
		} else {
			exp = getErrorExcelFile(trUploadtasklog, auditContext);
		}
		
		String[] splitFilename = StringUtils.split(trUploadtasklog.getErrorFileLocation(), "\\");
		String filename = splitFilename[splitFilename.length-1];
		
		mapResult.put("errorResult", exp);
		mapResult.put("filename", filename);
		
		return mapResult;
	}
	
	private byte[] getErrorExcelFile(TrUploadtasklog trUploadtasklog, AuditContext callerId){
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		HSSFWorkbook wb = null;
		if (trUploadtasklog != null) {
			try {
				File file = new File(trUploadtasklog.getErrorFileLocation());
				if (file.exists()) {
					FileInputStream fis = new FileInputStream(file);
					wb = new HSSFWorkbook(fis);
					wb.write(stream);
					fis.close();
				} 
				else {
					throw new UploadTaskException(this.messageSource.getMessage(
							"businesslogic.uploadtask.excelnotfound", null,
							this.retrieveLocaleAudit(callerId)), 
							Reason.ERROR_NOT_EXIST);
				}
			} 
			catch (IOException ex) {
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.uploadtask.excelgeterror", null,
						this.retrieveLocaleAudit(callerId)), 
						Reason.ERROR_PARSING);
			} 
			finally {
				try {
					if (wb != null) {
						wb.close();
					}
				} 
				catch (IOException e) {
					throw new UploadTaskException(this.messageSource.getMessage(
							"businesslogic.uploadtask.excelcloseerror", null,
							this.retrieveLocaleAudit(callerId)), 
							Reason.ERROR_GENERATE);
				}
			}
		}
		return stream.toByteArray();
	}
	
	private byte[] getReportResultCSV(TrUploadtasklog trUploadtasklog, AuditContext callerId) {
		byte[] result = null;
		if (trUploadtasklog != null) {
			try {
				File file = new File(trUploadtasklog.getErrorFileLocation());
				if (file.exists()) {
					Path path = Paths.get(trUploadtasklog.getErrorFileLocation());
					result = Files.readAllBytes(path);
				} 
				else {
					throw new UploadTaskException(
							this.messageSource.getMessage(
							"businesslogic.report.nocsv", null, this.retrieveLocaleAudit(callerId)), 
							Reason.ERROR_NOT_EXIST);
				}
			} 
			catch (IOException ex) {
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.report.errordata", null, this.retrieveLocaleAudit(callerId)), Reason.ERROR_PARSING);
			}
		}
		return result;
	}
}
