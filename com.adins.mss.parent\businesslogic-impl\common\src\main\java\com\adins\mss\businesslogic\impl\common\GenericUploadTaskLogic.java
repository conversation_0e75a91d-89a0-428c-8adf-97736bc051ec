package com.adins.mss.businesslogic.impl.common;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.collection.ViewHistoryLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.TaskServiceLogic;
import com.adins.mss.businesslogic.api.common.UploadTaskLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.exceptions.UploadTaskException;
import com.adins.mss.exceptions.UploadTaskException.Reason;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsLocation;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrUploadtasklog;
import com.adins.mss.model.TrViewcollactivity;
import com.adins.mss.model.TrViewinstallmentcard;
import com.adins.mss.model.TrViewpaymenthistoryD;
import com.adins.mss.model.TrViewpaymenthistoryH;
import com.adins.mss.model.custom.TaskBean;
import com.adins.mss.model.custom.UploadTaskBean;
import com.adins.mss.services.model.newconfins.AddTaskDetailBean;
import com.adins.mss.services.model.openpublic.common.CollectionActivityBean;
import com.adins.mss.services.model.openpublic.common.InstallmentCardBean;
import com.adins.mss.services.model.openpublic.common.PaymentHistoryBean;
import com.google.common.base.Stopwatch;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericUploadTaskLogic extends BaseLogic implements
		UploadTaskLogic, MessageSourceAware {

	private static final Logger LOG = LoggerFactory
			.getLogger(GenericUploadTaskLogic.class);

	@Autowired
	private TaskServiceLogic taskServiceLogic;

	@Autowired
	private CommonLogic commonLogic;

	@Autowired
	private MessageSource messageSource;
	
	@Autowired
	private ViewHistoryLogic viewHistoryLogic;

	public void setTaskServiceLogic(TaskServiceLogic taskServiceLogic) {
		this.taskServiceLogic = taskServiceLogic;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	public void setViewHistoryLogic(ViewHistoryLogic viewHistoryLogic) {
		this.viewHistoryLogic = viewHistoryLogic;
	}

	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	private static final String[] TEMPLATE_HEADER_MS = {
			GlobalVal.HEADER_APPLICATION_NO, GlobalVal.HEADER_PRIORITY,
			GlobalVal.HEADER_BRANCH_ID, GlobalVal.HEADER_MOBILE_USER_ID,
			GlobalVal.HEADER_CUSTOMER_NAME, GlobalVal.HEADER_CUSTOMER_PHONE,
			GlobalVal.HEADER_CUSTOMER_ADDRESS, GlobalVal.HEADER_NOTES,
			GlobalVal.HEADER_LATITUDE, GlobalVal.HEADER_LONGITUDE };
	private static final String[] TEMPLATE_HEADER_MC = {
			GlobalVal.HEADER_AGREEMENT_NO, GlobalVal.HEADER_PRIORITY,
			GlobalVal.HEADER_MOBILE_USER_ID, GlobalVal.HEADER_CUSTOMER_NAME,
			GlobalVal.HEADER_CUSTOMER_PHONE, GlobalVal.HEADER_CUSTOMER_ADDRESS,
			GlobalVal.HEADER_NOTES, GlobalVal.HEADER_SURVEY_ASSIGNMENT_ID,
			GlobalVal.HEADER_LATITUDE, GlobalVal.HEADER_LONGITUDE };
	private static final String[] TEMPLATE_HEADER_MC_INSTALLMENT_SCEDULER = {
			GlobalVal.HEADER_AGREEMENT_NO, GlobalVal.HEADER_BRANCH_CODE,
			GlobalVal.HEADER_INSTALLMENT_NO, GlobalVal.HEADER_DUE_DATE,
			GlobalVal.HEADER_INSTALLMENT_AMOUNT, GlobalVal.HEADER_INSTL_PAID_DATE,
			GlobalVal.HEADER_INSTL_PAID_AMOUNT, GlobalVal.HEADER_LC_INSTL_AMOUNT,
			GlobalVal.HEADER_LC_INSTL_PAID, GlobalVal.HEADER_LC_INSTL_WAIVED,
			GlobalVal.HEADER_PRINCIPAL_AMOUNT, GlobalVal.HEADER_INTEREST_AMOUNT,
			GlobalVal.HEADER_OS_PRINCIPAL_AMOUNT, GlobalVal.HEADER_OS_INTEREST_AMOUNT,
			GlobalVal.HEADER_LC_DAYS, GlobalVal.HEADER_LC_ADMIN_FEE,
			GlobalVal.HEADER_LC_ADMIN_FEE_PAID, GlobalVal.HEADER_LC_ADMIN_FEE_WAIVED };
	private static final String[] TEMPLATE_HEADER_MC_COLLECTION_HISTORY = {
			GlobalVal.HEADER_AGREEMENT_NO, GlobalVal.HEADER_BRANCH_CODE,
			GlobalVal.HEADER_ACTIVITY_DATE,GlobalVal.HEADER_COLLECTOR_NAME,
			GlobalVal.HEADER_ACTIVITY, GlobalVal.HEADER_RESULT, GlobalVal.HEADER_PTP_DATE,
			GlobalVal.HEADER_NOTES, GlobalVal.HEADER_OVER_DUE_DAYS,
			GlobalVal.HEADER_NEXT_PLAN_DATE, GlobalVal.HEADER_NEXT_PALAN_ACTION};
	private static final String[] TEMPLATE_HEADER_MC_PAYMENT_HISTORY = {
			GlobalVal.HEADER_AGREEMENT_NO, GlobalVal.HEADER_BRANCH_CODE,
			GlobalVal.HEADER_RECEIPT_NO,GlobalVal.HEADER_VALUE_DATE,
			GlobalVal.HEADER_POSTING_DATE, GlobalVal.HEADER_PAYMENT_AMOUNT,
			GlobalVal.HEADER_INSTALLMENT_AMOUNT, GlobalVal.HEADER_INSTALLMENT_NUMBER,
			GlobalVal.HEADER_TRANSACTION_TYPE, GlobalVal.HEADER_WOP_CODE, 
			GlobalVal.HEADER_TRANSACTION_ID, GlobalVal.HEADER_PAYMENT_ALLOCATION_NAME,
			GlobalVal.HEADER_OS_AMOUNT_OD, GlobalVal.HEADER_RECEIVE_AMOUNT  };
	private static final String[] TEMPLATE_HEADER_MT = {
			GlobalVal.HEADER_PRIORITY, GlobalVal.HEADER_BRANCH_ID,
			GlobalVal.HEADER_MOBILE_USER_ID, GlobalVal.HEADER_LOCATION_NAME,
			GlobalVal.HEADER_CUSTOMER_PHONE, GlobalVal.HEADER_NOTES,
			GlobalVal.HEADER_DATETIME_ASSIGNMENT, GlobalVal.HEADER_TRIP,
			GlobalVal.HEADER_STOP };

	@Override
	public List<TrUploadtasklog> getUploadTaskLogList(AuditContext callerId) {
		Map<String, Object> mapResult = this.getManagerDAO().list(
				"from TrUploadtasklog tu "
				+ "join fetch tu.amMsuser mu "
				+ "join fetch mu.amMssubsystem "
				+ "where tu.isFinish = :isFinish", 
				new Object[][] {{"isFinish", "0"}});
		List<TrUploadtasklog> listResult = (List<TrUploadtasklog>) mapResult
				.get(GlobalKey.MAP_RESULT_LIST);
		return listResult;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void doUploadTaskProcess(TrUploadtasklog trUploadTaskLog,
			AuditContext callerId) {
		LOG.info("Start Job Parse Excel to Database. Filename={}.",
				trUploadTaskLog.getInputFilename());

		Object[][] paramPath = { { Restrictions.eq("gsCode",
				GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH) } };
		AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, paramPath);

		if (amGeneralSetting == null) {
			throw new EntityNotFoundException(this.messageSource.getMessage(
					"businesslogic.uploadtask.locationnotdefined", null,
					this.retrieveLocaleAudit(callerId)),
					GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH);
		}

		String path = amGeneralSetting.getGsValue();
		File processedFile = null;
		FileInputStream inputStream = null;
		HSSFWorkbook wb = null;
		Date dateStart = null;
		String uploader = null;

		// create folder date success/error
		File doneFolder = new File(path
				+ DateFormatUtils.format(trUploadTaskLog.getDtmUpload(),
						"yyyy-MM-dd"));
		if (!doneFolder.exists()) {
			doneFolder.mkdirs();
		}

		try {
			Stopwatch swLoop = Stopwatch.createStarted();
			dateStart = new Date();
			String formName = StringUtils.EMPTY;
			String formVersion = StringUtils.EMPTY;
			Map<String, Object> parseMap = new HashMap<String, Object>();
			processedFile = new File(path + trUploadTaskLog.getInputFilename());
			inputStream = new FileInputStream(processedFile);
			wb = new HSSFWorkbook(inputStream);
			HSSFSheet sheet = wb.getSheetAt(0);

			uploader = trUploadTaskLog.getAmMsuser().getLoginId();

			// get form
			HSSFRow row = sheet.getRow(0);
			HSSFCell cellForm = row.getCell(0);
			if (null == cellForm
					|| !GlobalVal.HEADER_FORM_NAME.equals(cellForm
							.getStringCellValue())) {
				wb.close();
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.uploadtask.wrongtemplate", null,
						this.retrieveLocaleAudit(callerId)), 
						UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}
			HSSFCell cell = row.getCell(1);
			if (null != cell) {
				formName = cell.getStringCellValue();
			}
			
			// get version
			HSSFRow rowVersion = sheet.getRow(1);
			HSSFCell cellVersion = rowVersion.getCell(0);
			if (null == cellVersion 
					|| !GlobalVal.HEADER_FORM_VERSION.equals(cellVersion.getStringCellValue())) {
				wb.close();
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.uploadtask.wrongversion", null,
						this.retrieveLocaleAudit(callerId)), 
						UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}
			HSSFCell cellVers = rowVersion.getCell(1);

			if (GlobalVal.SUBSYSTEM_MC.equals(trUploadTaskLog.getAmMsuser()
					.getAmMssubsystem().getSubsystemName())) {
				if (null != cellVers) {
					formVersion = cellVers.getStringCellValue();
				}
				parseMap = parseExcelIntoCollection(wb, sheet,
						TEMPLATE_HEADER_MC, formName, GlobalVal.SUBSYSTEM_MC,
						callerId);
			} 
			else if (GlobalVal.SUBSYSTEM_MT.equals(trUploadTaskLog
					.getAmMsuser().getAmMssubsystem().getSubsystemName())) {
				parseMap = parseExcelIntoCollection(wb, sheet,
						TEMPLATE_HEADER_MT, formName, GlobalVal.SUBSYSTEM_MT,
						callerId);
			} 
			else {
				if (null != cellVers) {
					formVersion = cellVers.getStringCellValue();
				}
				parseMap = parseExcelIntoCollection(wb, sheet,
						TEMPLATE_HEADER_MS, formName, GlobalVal.SUBSYSTEM_MS,
						callerId);
			}

			parseMap.put("formName", formName);
			parseMap.put("formVersion", formVersion);
			Map mapReturn = loopDataToInsert(parseMap,
					trUploadTaskLog.getAmMsuser(), formName, formVersion, callerId, uploader);
			byte[] errorUploadByte = (byte[]) mapReturn.get("errorUploadByte");
			String errorFileLocation = StringUtils.EMPTY;

			if (null != errorUploadByte) {
				// failed parse excel to database
				errorFileLocation = this.writeErrorFile(doneFolder,
						trUploadTaskLog.getInputFilename(), errorUploadByte);
			}

			// move uploaded file to done folder + date
			processedFile.renameTo(new File(doneFolder.getPath()
					+ SystemUtils.FILE_SEPARATOR + processedFile.getName()));

			Date dateFinish = new Date();
			trUploadTaskLog.setProcessStartTime(dateStart);
			trUploadTaskLog.setProcessFinishTime(dateFinish);
			trUploadTaskLog.setProcessDurationSeconds((int)Math
					.abs(dateFinish.getTime() - dateStart.getTime()) / 1000);
			trUploadTaskLog
					.setInputCount((Integer) mapReturn.get("inputCount"));
			trUploadTaskLog.setCountSuccess((Integer) mapReturn
					.get("countSuccess"));
			trUploadTaskLog
					.setCountError((Integer) mapReturn.get("countError"));
			if (StringUtils.isNotBlank(errorFileLocation)) {
				trUploadTaskLog.setErrorFileLocation(errorFileLocation);
			}
			trUploadTaskLog.setIsFinish("1");
			this.getManagerDAO().update(trUploadTaskLog);
			swLoop.stop();
			LOG.info(
					"Job Parse Excel to Database. Filename={}. Elapsed time=<{}>ms",
					trUploadTaskLog.getInputFilename(),
					swLoop.elapsed(TimeUnit.MILLISECONDS));
		} 
		catch (Exception ex) {
			Date dateFinish = new Date();
			String message = "Parsing excel error.";
			Workbook xlsError = this.createXlsError(message);

			String errorFileLocation = null;
			ByteArrayOutputStream stream = null;
			try {
				stream = new ByteArrayOutputStream();
				xlsError.write(stream);
				errorFileLocation = this.writeErrorFile(doneFolder,
						trUploadTaskLog.getInputFilename(),
						stream.toByteArray());

//				 move uploaded file to done folder + date
				processedFile
						.renameTo(new File(doneFolder.getPath()
								+ SystemUtils.FILE_SEPARATOR
								+ processedFile.getName()));
			} 
			catch (IOException e) {
				LOG.error("Error writing warning excel", e);
			} 
			finally {
				try {
					if (stream != null) {
						stream.flush();
						stream.close();
					}
					if (xlsError != null)
						xlsError.close();
				} 
				catch (IOException e) {
					LOG.error("Error closing workbook", e);
				}
			}

			trUploadTaskLog.setProcessStartTime(dateStart);
			trUploadTaskLog.setProcessFinishTime(dateFinish);
			trUploadTaskLog.setProcessDurationSeconds((int)Math
					.abs(dateFinish.getTime() - dateStart.getTime()) / 1000);
			trUploadTaskLog.setErrorFileLocation(errorFileLocation);
			trUploadTaskLog.setIsFinish("1");
			this.getManagerDAO().update(trUploadTaskLog);
			LOG.warn("Error on Job Parse Excel to Database. Filename={}",
					trUploadTaskLog.getInputFilename(), ex);
		} 
		finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (wb != null) {
					wb.close();
				}
			} 
			catch (IOException e) {
				LOG.error(
						"SCHEDULER error while close workbook. params[filename={}]",
						trUploadTaskLog.getInputFilename());
			}
		}
	}

	private String writeErrorFile(File doneFolder, String inputFileName,
			byte[] errorUploadByte) throws IOException {
		// failed parse excel to database
		String errorFileLocation = doneFolder.getPath()
				+ SystemUtils.FILE_SEPARATOR + "ErrorTaskUpload-"
				+ inputFileName;
		FileOutputStream fileOut = new FileOutputStream(errorFileLocation);
		try {
			fileOut.write(errorUploadByte);
			fileOut.flush();
		} 
		finally {
			fileOut.close();
		}

		return errorFileLocation;
	}

	@Override
	public Map<String, Object> processSpreadSheetSaveToFile(File uploadedFile,
			String fileName, AmMsuser loginBean, AuditContext callerId)
			throws IOException {
		Map<String, Object> ret = new HashMap<String, Object>();
		Date date = new Date();
		Stopwatch totalSw = Stopwatch.createStarted();
		Stopwatch sw = Stopwatch.createUnstarted();
		Map parseMap;
		try {
			sw.start();
			if (GlobalVal.SUBSYSTEM_MS.equals(loginBean.getAmMssubsystem()
					.getSubsystemName())) {
				parseMap = this.parseSpreadsheetToTaskBeans(uploadedFile,
						TEMPLATE_HEADER_MS, GlobalVal.SUBSYSTEM_MS, callerId,
						true);
			} 
			else {
				parseMap = this.parseSpreadsheetToTaskBeans(uploadedFile,
						TEMPLATE_HEADER_MC, GlobalVal.SUBSYSTEM_MC, callerId,
						true);
			}
			sw.stop();
			LOG.debug("Excel parsing time: {}(ms).",
					sw.elapsed(TimeUnit.MILLISECONDS));
		} 
		catch (Exception e) {
			ret = this.errorUpload(e);
			return ret;
		}
		Map result = (Map) parseMap.get("result");
		String formName = (String) result.get("formName");
	
		if (null == formName || StringUtils.EMPTY.equals(formName)) {
			Object[] obj = { GlobalVal.HEADER_FORM_NAME };
			throw new UploadTaskException(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)),
					Reason.ERROR_NOT_EXIST);
		} 
		else {
			MsForm msForm = this.getManagerDAO().selectOne(
				"from MsForm mf "
				+ "join fetch mf.amMssubsystem mfs "
				+ "where mf.formName = :formName "
				+ "and mfs.uuidMsSubsystem = :uuidMsSubsystem",
				new Object[][] {{"formName", formName}, 
				{"uuidMsSubsystem", loginBean.getAmMssubsystem().getUuidMsSubsystem()}});
			if (null == msForm) {
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.global.formunregistered", null,
						this.retrieveLocaleAudit(callerId)), Reason.ERROR_NOT_EXIST);
			} 
			else {
				if (!loginBean.getAmMssubsystem().getSubsystemName()
						.equals(msForm.getAmMssubsystem().getSubsystemName())) {
					throw new UploadTaskException(this.messageSource.getMessage(
							"businesslogic.global.formunregistered", null,
							this.retrieveLocaleAudit(callerId)), Reason.ERROR_NOT_EXIST);
				}
			}
		}
		
		try {
			saveFile(uploadedFile, loginBean, formName, fileName, date,
					callerId);
		} 
		catch (Exception e) {
			throw new UploadTaskException(this.messageSource.getMessage(
					"businesslogic.global.errorgenxld", null, this.retrieveLocaleAudit(callerId)),
					e, Reason.ERROR_GENERATE);
		}

		totalSw.stop();
		LOG.debug("===Finish processing excel time: {}(ms).",
				totalSw.elapsed(TimeUnit.MILLISECONDS));
		return ret;
	}

	@Override
	public Map<String, Object> processSpreadSheetFile(File uploadedFile,
			String fileName, AmMsuser loginBean, AuditContext callerId,
			String uploader) {
		Map<String, Object> ret = new HashMap<String, Object>();
		byte[] errorUploadByte = null;
		Stopwatch totalSw = Stopwatch.createStarted();
		Stopwatch sw = Stopwatch.createUnstarted();
		Map parseMap;
		try {
			if (GlobalVal.SUBSYSTEM_MS.equals(loginBean.getAmMssubsystem()
					.getSubsystemName())) {
				parseMap = this.parseSpreadsheetToTaskBeans(uploadedFile,
						TEMPLATE_HEADER_MS, GlobalVal.SUBSYSTEM_MS, callerId,
						false);
			} 
			else {
				sw.start();
				parseMap = this.parseSpreadsheetToTaskBeans(uploadedFile,
						TEMPLATE_HEADER_MC, GlobalVal.SUBSYSTEM_MC, callerId,
						false);
				sw.stop();
				LOG.debug("Excel parsing time: {}(ms).",
						sw.elapsed(TimeUnit.MILLISECONDS));
			}
		} 
		catch (Exception e) {
			ret = this.errorUpload(e);
			return ret;
		}
		Map result = (Map) parseMap.get("result");
		String formName = (String) result.get("formName");
		String formVersion = (String) result.get("formVersion");
		MsForm msForm = null;		
		
		if (null == formName || StringUtils.EMPTY.equals(formName)) {
			Object[] obj = { GlobalVal.HEADER_FORM_NAME };
			throw new UploadTaskException(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)),
					Reason.ERROR_NOT_EXIST);
		} 
		else {
			msForm = this.getManagerDAO().selectOne(
					"from MsForm mf join fetch mf.amMssubsystem where mf.formName = :formName", 
					new Object[][] {{"formName", formName}});
			if (null == msForm) {
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.global.formunregistered", null,
						this.retrieveLocaleAudit(callerId)), Reason.ERROR_NOT_EXIST);
			} 
			else {
				if (!loginBean.getAmMssubsystem().getSubsystemName()
						.equals(msForm.getAmMssubsystem().getSubsystemName())) {
					throw new UploadTaskException(this.messageSource.getMessage(
							"businesslogic.global.formunregistered", null,
							this.retrieveLocaleAudit(callerId)), Reason.ERROR_NOT_EXIST);
				}
			}
		}
		
		// form version
		if (null == formVersion || StringUtils.EMPTY.equals(formVersion)) {
			Object[] obj = { GlobalVal.HEADER_FORM_VERSION };
			throw new UploadTaskException(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)),
					Reason.ERROR_NOT_EXIST);
		} 
		else {
			MsFormhistory msFormhistory = this.getManagerDAO().selectOne(
				"from MsFormhistory mfh "
				+ "join fetch mfh.msForm mf "
				+ "join fetch mf.amMssubsystem "
				+ "where mf.uuidForm = :uuidForm "
				+ "and mfh.formVersion = :formVersion", 
				new Object[][] {{"uuidForm", msForm.getUuidForm()}, 
						{"formVersion", formVersion}});
			if (null == msFormhistory) {
				throw new UploadTaskException(this.messageSource.getMessage(
						"businesslogic.global.formversnotappropriate", null,
						this.retrieveLocaleAudit(callerId)), Reason.ERROR_NOT_EXIST);
			} 
			else {
				if (!loginBean.getAmMssubsystem().getSubsystemName()
						.equals(msFormhistory.getMsForm().getAmMssubsystem().getSubsystemName())) {
					throw new UploadTaskException(this.messageSource.getMessage(
							"businesslogic.global.formversnotappropriate", null,
							this.retrieveLocaleAudit(callerId)), Reason.ERROR_NOT_EXIST);
				}
			}
		}

		Map mapReturn = loopDataToInsert(result, loginBean, formName, formVersion, callerId,
				uploader);
		errorUploadByte = (byte[]) mapReturn.get("errorUploadByte");

		if (null != errorUploadByte) {
			ret.put("exp", errorUploadByte);
		}

		totalSw.stop();
		LOG.debug("===Finish processing excel time: {}(ms).",
				totalSw.elapsed(TimeUnit.MILLISECONDS));
		return ret;
	}

	private Map parseSpreadsheetToTaskBeans(File uploadedFile,
			String[] template, String subsystemName, AuditContext callerId,
			boolean isSaveToFile) throws IOException {
		Map<String, Object> paramParse = new HashMap<>();
		Map<String, Object> result = new HashMap<String, Object>();

		FileInputStream inputStream = null;

		try {
			inputStream = new FileInputStream(uploadedFile);

			HSSFWorkbook wb = new HSSFWorkbook(inputStream);
			HSSFSheet sheet = wb.getSheetAt(0);
			String formName = StringUtils.EMPTY;

			validateUploadtTask(wb, sheet, subsystemName);

			// get form
			HSSFRow row = sheet.getRow(0);
			HSSFCell cellForm = row.getCell(0);
			if (null == cellForm
					|| !GlobalVal.HEADER_FORM_NAME.equals(cellForm
							.getStringCellValue())) {
				wb.close();
				throw new IOException("Wrong Template.");
			}
			HSSFCell cell = row.getCell(1);
			if (null != cell) {
				formName = cell.getStringCellValue();
			}

			if (!isSaveToFile) {
				result = parseExcelIntoCollection(wb, sheet, template,
						formName, subsystemName, callerId);
			}
			result.put("formName", formName);

			paramParse.put("result", result);
			return paramParse;
		} 
		finally {
			if (inputStream != null) {
				inputStream.close();
			}
		}
	}

	private void validateUploadtTask(HSSFWorkbook wb, HSSFSheet sheet,
			String subsystemName) throws IOException {
		int rows = sheet.getLastRowNum();
		int numberOfData = 0;

		// check total task uploaded
		Object[][] paramsGs = { { Restrictions.eq("gsCode",
				GlobalKey.GENERALSETTING_MAX_TASK_UPLOAD) } };
		AmGeneralsetting generalsetting = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, paramsGs);
		int maxTaskUpload = generalsetting == null ? GlobalVal.MAX_TASK_UPLOAD
				: Integer.parseInt(generalsetting.getGsValue());
		for (int r = 0; r <= rows; r++) {
			HSSFRow row = sheet.getRow(r);
			if (r > 3) {
				boolean isEmptyRow = checkEmptyRow(row, row.getLastCellNum());

				if (numberOfData > maxTaskUpload + 1) {
					break;
				}

				if (isEmptyRow == true) {
					continue;
				} 
				else {
					numberOfData++;
				}
			}
		}

		if (numberOfData > maxTaskUpload) {
			wb.close();
			throw new UploadTaskException("Maximum task size exceeded",
					Reason.ERROR_MAX_EXCEEDED);
		}
	}

	private Map parseExcelIntoCollection(HSSFWorkbook wb, HSSFSheet sheet,
			String[] template, String formName, String subsystemName,
			AuditContext callerId) throws IOException {
		Map<String, Object> result = new HashMap<String, Object>();
		List<String> identifierList = new ArrayList<String>();
		List<UploadTaskBean> resultList = new ArrayList<UploadTaskBean>();
		List<UploadTaskBean> resultError = new ArrayList<UploadTaskBean>();
		List<TaskBean> taskBeanList = new ArrayList<TaskBean>();
		int rows = sheet.getLastRowNum();
		int totIdentifier = 0;
		for (int r = 0; r <= rows; r++) {
			HSSFRow row = sheet.getRow(r);

			// get identifier
			if (r == 3) {
				int firstColumn = row.getFirstCellNum();
				int lastColumn = row.getLastCellNum();
				totIdentifier = lastColumn - firstColumn;
				int idxIdentifier = 0;
				for (int i = firstColumn; i < lastColumn; i++) {
					if (idxIdentifier < totIdentifier) {
						HSSFCell cell = row.getCell(i);
						String identifier = cell.getStringCellValue();
						// 2016-02-24 Fix when firstColumn start from 0 instead
						// of after template.length
						if (StringUtils.isBlank(identifier)) {
							continue;
						}

						identifierList.add(identifier);
						idxIdentifier++;
					}
				}
			}

			// check if row null
			if (r > 4) {
				boolean isEmptyRow = checkEmptyRow(row, row.getLastCellNum());

				if (isEmptyRow == true) {
					continue;
				}

				UploadTaskBean uploadTaskBean = new UploadTaskBean();
				AddTaskDetailBean addTaskDetailBeanArr[] = new AddTaskDetailBean[identifierList
						.size()];
				int idx = 0;
				for (int c = 0; c < identifierList.size() + template.length; c++) {

					HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);

					String value = "";

					// if intValue -1, then sequence is posted with string
					uploadTaskBean.setDtmCrt(new Date());
					uploadTaskBean.setUsrCrt(callerId.getCallerId().toString());

					if (cell != null) {
						switch (cell.getCellType()) {
							case HSSFCell.CELL_TYPE_NUMERIC:
								DataFormatter dataFormatter = new DataFormatter(
										Locale.US);
								value = dataFormatter.formatCellValue(cell);
								break;

							case HSSFCell.CELL_TYPE_STRING:
								value = cell.getStringCellValue();
								break;

							default:
						}
					}

					// header
					uploadTaskBean = this.setHeader(c, value.toString(),
							uploadTaskBean, subsystemName);
					

					// detail
					if (c > template.length - 1) {
						AddTaskDetailBean addTaskDetailBean = new AddTaskDetailBean();
						addTaskDetailBean.setRefID(identifierList.get(idx));
						addTaskDetailBean.setValue(value);
						addTaskDetailBeanArr[idx] = addTaskDetailBean;
						idx++;
					}

					wb.close();
				}

				uploadTaskBean.setAddTaskDetailBeans(addTaskDetailBeanArr);
				StringBuilder errorText = checkingTaskUpload(formName, 
						uploadTaskBean, subsystemName, resultList, callerId);
				if (errorText.length() == 0) {
					resultList.add(uploadTaskBean);
				} 
				else {
					resultError.add(uploadTaskBean);
					TaskBean taskBean = new TaskBean();
					taskBean.setErrorText(errorText);
					taskBeanList.add(taskBean);
				}
			}
		}
		
		result.put("taskBeanList", taskBeanList); // nyimpen tulisan error
		result.put("identifierList", identifierList);
		result.put("resultList", resultList);							
		result.put("resultError", resultError);	
		
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystemName)) {
			List<InstallmentCardBean> resultInstallment = new ArrayList<InstallmentCardBean>();
			List<CollectionActivityBean> resultCollActivity = new ArrayList<CollectionActivityBean>();
			List<PaymentHistoryBean> resultPayment = new ArrayList<PaymentHistoryBean>();
			List<InstallmentCardBean> resultErrorInstallment = new ArrayList<InstallmentCardBean>();
			List<CollectionActivityBean> resultErrorCollActivity = new ArrayList<CollectionActivityBean>();
			List<PaymentHistoryBean> resultErrorPayment = new ArrayList<PaymentHistoryBean>();
			//sheet Installment
			HSSFSheet sheetinstallment = sheet.getWorkbook().getSheetAt(1);
			int rowinstallment = sheetinstallment.getLastRowNum();
			for (int ri = 1; ri <= rowinstallment; ri++) {
				HSSFRow rowsinstallment = sheetinstallment.getRow(ri);
				// check if row null
				if (ri > 0) {
					boolean isEmptyRowinstallment = checkEmptyRow(
							rowsinstallment, rowsinstallment.getLastCellNum());

					if (isEmptyRowinstallment == true) {
						continue;
					}

					InstallmentCardBean installmentCardBean = new InstallmentCardBean();

					for (int ci = 0; ci < rowsinstallment.getLastCellNum(); ci++) {

						HSSFCell cell = rowsinstallment.getCell(ci,
								Row.RETURN_BLANK_AS_NULL);

						String value = "";

						if (cell != null) {
							switch (cell.getCellType()) {
								case HSSFCell.CELL_TYPE_NUMERIC:
									DataFormatter dataFormatter = new DataFormatter(
											Locale.US);
									value = dataFormatter.formatCellValue(cell);
									break;

								case HSSFCell.CELL_TYPE_STRING:
									value = cell.getStringCellValue();
									break;

								default:
							}
						}

						installmentCardBean = this.setHeader(ci,
								value.toString(), installmentCardBean);

						wb.close();
					}
					StringBuilder errorText = checkingInstallmentCard(installmentCardBean, callerId);
					if (errorText.length() == 0) {
						resultInstallment.add(installmentCardBean);
					} 
					else {
						installmentCardBean.setErrorText(errorText);
						resultErrorInstallment.add(installmentCardBean);						
					}
				}
			}
			result.put("resultInstallment", resultInstallment);
			result.put("resultErrorInstallment", resultErrorInstallment);
				
			//sheet coll activity
			HSSFSheet sheetcollactivity = sheet.getWorkbook().getSheetAt(2);
			int rowcollactivity = sheetcollactivity.getLastRowNum();
			for (int rc = 1; rc <= rowcollactivity; rc++) {
				HSSFRow rowscollactivity = sheetcollactivity.getRow(rc);
				// check if row null
				if (rc > 0) {
					boolean isEmptyRowcollactivity = checkEmptyRow(
							rowscollactivity, rowscollactivity.getLastCellNum());

					if (isEmptyRowcollactivity == true) {
						continue;
					}

					CollectionActivityBean collectionActivityBean = new CollectionActivityBean();

					for (int cc = 0; cc < rowscollactivity.getLastCellNum(); cc++) {

						HSSFCell cell = rowscollactivity.getCell(cc,
								Row.RETURN_BLANK_AS_NULL);

						String value = "";

						if (cell != null) {
							switch (cell.getCellType()) {
								case HSSFCell.CELL_TYPE_NUMERIC:
									DataFormatter dataFormatter = new DataFormatter(
											Locale.US);
									value = dataFormatter
										.formatCellValue(cell);
									break;

								case HSSFCell.CELL_TYPE_STRING:
									value = cell.getStringCellValue();
									break;

								default:
							}
						}

						// header
						collectionActivityBean = this.setHeader(cc,
								value.toString(), collectionActivityBean);

						wb.close();
					}
					StringBuilder errorText = checkingCollectionActivity(collectionActivityBean, callerId);
					if (errorText.length() == 0) {
						resultCollActivity.add(collectionActivityBean);
					} 
					else {
						collectionActivityBean.setErrorText(errorText);
						resultErrorCollActivity.add(collectionActivityBean);						
					}
				}	
			}
			result.put("resultCollActivity", resultCollActivity);
			result.put("resultErrorCollActivity", resultErrorCollActivity);
			
			//sheet payment history
			HSSFSheet sheetpayment = sheet.getWorkbook().getSheetAt(3);
			int rowpayment = sheetpayment.getLastRowNum();
			for (int rp = 1; rp <= rowpayment; rp++) {
				HSSFRow rowspayment = sheetpayment.getRow(rp);

				// check if row null
				if (rp > 0) {
					boolean isEmptyRowpayment = checkEmptyRow(
							rowspayment, rowspayment.getLastCellNum());

					if (isEmptyRowpayment == true) {
						continue;
					}
					PaymentHistoryBean paymentHistoryBean = new PaymentHistoryBean();

					for (int ci = 0; ci < rowspayment.getLastCellNum(); ci++) {

						HSSFCell cell = rowspayment.getCell(ci,
								Row.RETURN_BLANK_AS_NULL);

						String value = "";
						
						if (cell != null) {
							switch (cell.getCellType()) {
								case HSSFCell.CELL_TYPE_NUMERIC:
									DataFormatter dataFormatter = new DataFormatter(
											Locale.US);
									value = dataFormatter.formatCellValue(cell);
									break;

								case HSSFCell.CELL_TYPE_STRING:
									value = cell.getStringCellValue();
									break;

								default:
							}
						}

						// header
						paymentHistoryBean = this.setHeader(ci,
								value.toString(), paymentHistoryBean);

						wb.close();
					}
					StringBuilder errorText = checkingPayment(paymentHistoryBean, callerId);
					if (errorText.length() == 0) {
						resultPayment.add(paymentHistoryBean);
					} 
					else {
						paymentHistoryBean.setErrorText(errorText);
						resultErrorPayment.add(paymentHistoryBean);						
					}
				}
			}	
			result.put("resultPayment", resultPayment);	
			result.put("resultErrorPayment", resultErrorPayment);	
		}		
		return result;
	}

	private Map loopDataToInsert(Map result, AmMsuser loginBean,
			String formName, String formVersion, AuditContext callerId, String uploader) {
		Map<String, Object> mapReturn = new HashMap<String, Object>();
		Stopwatch sw = Stopwatch.createUnstarted();
		byte[] errorUploadByte = null;
		int taskSuccess = 0;
		List<TaskBean> taskBeanList = (List<TaskBean>) result.get("taskBeanList");
		List identifierList = (List) result.get("identifierList");
		List listofTask = (List) result.get("resultList");
		List<UploadTaskBean> listofErrorTask = (List<UploadTaskBean>) result.get("resultError");
		taskSuccess = listofTask.size();

		Map<String, String> mapUserBranch = new HashMap<>(); // for string key:loginId,value

		MsStatustask msStatustaskP = this.commonLogic.retrieveStatusTask(GlobalVal.COLLECTION_STATUS_TASK_DISTRIBUTION,
				loginBean.getAmMssubsystem().getUuidMsSubsystem(), callerId);

		MsStatustask msStatustaskC = this.commonLogic.retrieveStatusTask(GlobalVal.COLLECTION_STATUS_TASK_CANCELED,
				loginBean.getAmMssubsystem().getUuidMsSubsystem(), callerId);

		for (int i = 0; i < listofTask.size(); i++) {
			sw.start();
			UploadTaskBean uploadTaskBean = (UploadTaskBean) listofTask.get(i);

			String processCode = StringUtils.EMPTY;
			String branchCode = StringUtils.EMPTY;
			String agreementApplNo = StringUtils.EMPTY;

			AmMsuser collUserBean = new AmMsuser();
			if (GlobalVal.SUBSYSTEM_MS.equals(loginBean.getAmMssubsystem()
					.getSubsystemName())) {
				processCode = GlobalVal.PROCESS_CODE_MSCORE;
				branchCode = uploadTaskBean.getMsBranch().getBranchCode();
				agreementApplNo = uploadTaskBean.getApplNo();
			} 
			else if (GlobalVal.SUBSYSTEM_MT.equals(loginBean
					.getAmMssubsystem().getSubsystemName())) {
				processCode = GlobalVal.PROCESS_CODE_MTCORE;
				branchCode = uploadTaskBean.getMsBranch().getBranchCode();

			} 
			else {
				processCode = GlobalVal.PROCESS_CODE_MC;

				branchCode = mapUserBranch.get(uploadTaskBean.getAmMsuser()
						.getLoginId());
				if (branchCode == null) {
					collUserBean = this.getManagerDAO().selectOne(
							"from AmMsuser u "
							+ "join fetch u.msBranch "
							+ "where u.loginId = :loginId", 
							new Object[][] {{"loginId", uploadTaskBean.getAmMsuser()
								.getLoginId()}});
					if (null != collUserBean) {
						branchCode = collUserBean.getMsBranch().getBranchCode();
						mapUserBranch.put(uploadTaskBean.getAmMsuser()
								.getLoginId(), branchCode);
					}
				}
				agreementApplNo = uploadTaskBean.getAgreementNo();
			}

			if (null == uploadTaskBean.getIsCancelled()
					|| !GlobalVal.ACTION_CANCEL.equals(uploadTaskBean
							.getIsCancelled().toUpperCase())) {
				try {
					final String taskId = "";
					if (GlobalVal.SUBSYSTEM_MT.equals(loginBean
							.getAmMssubsystem().getSubsystemName())) {
						taskServiceLogic.addTaskMT(callerId,
								processCode, loginBean.getAmMssubsystem()
										.getSubsystemName(), formName,
								uploadTaskBean.getMsPriority()
										.getPriorityDesc(), branchCode,
								uploadTaskBean.getAmMsuser().getLoginId(),
								uploadTaskBean.getMsLocation()
										.getLocationName(), uploadTaskBean
										.getCustomerPhone(), uploadTaskBean
										.getNotes(), uploadTaskBean
										.getAssignDate(), uploadTaskBean
										.getGroupSeq(), uploadTaskBean
										.getTaskSeq(), "N", taskId, loginBean
										.getLoginId(), uploadTaskBean
										.getAddTaskDetailBeans(), null,
								uploadTaskBean.getAssignDate(), uploader);
					} 
					else if (GlobalVal.SUBSYSTEM_MC.equals(loginBean
							.getAmMssubsystem().getSubsystemName())) {
						taskServiceLogic.addTask(callerId,
								processCode, loginBean.getAmMssubsystem()
										.getSubsystemName(), formName,
										formVersion,
								uploadTaskBean.getMsPriority()
										.getPriorityDesc(), uploadTaskBean
										.getCustomerName(), uploadTaskBean
										.getCustomerAddress(), uploadTaskBean
										.getCustomerPhone(), null,
								uploadTaskBean.getNotes(), branchCode,
								agreementApplNo, uploadTaskBean.getAmMsuser()
										.getLoginId(), "Y", uploadTaskBean
										.getSurveyAssignmentID(),
								uploadTaskBean.getLatitude() == null ? null
										: uploadTaskBean.getLatitude()
												.toString(), uploadTaskBean
										.getLongitude() == null ? null
										: uploadTaskBean.getLongitude()
												.toString(), taskId, loginBean
										.getLoginId(), uploadTaskBean
										.getAddTaskDetailBeans(), null, null, null);

					} 
					else {
						taskServiceLogic.addTask(callerId,
								processCode, loginBean.getAmMssubsystem()
										.getSubsystemName(), formName,
										formVersion,
								uploadTaskBean.getMsPriority()
										.getPriorityDesc(), uploadTaskBean
										.getCustomerName(), uploadTaskBean
										.getCustomerAddress(), uploadTaskBean
										.getCustomerPhone(), null,
								uploadTaskBean.getNotes(), branchCode,
								agreementApplNo, uploadTaskBean.getAmMsuser()
										.getLoginId(), "Y", uploadTaskBean
										.getSurveyAssignmentID(),
								uploadTaskBean.getLatitude() == null ? null
										: uploadTaskBean.getLatitude()
												.toString(), uploadTaskBean
										.getLongitude() == null ? null
										: uploadTaskBean.getLongitude()
												.toString(), taskId, loginBean
										.getLoginId(), uploadTaskBean
										.getAddTaskDetailBeans(), null, null, null);
					}
				} 
				catch (Exception e) {
					LOG.error(
							"Error while add uploaded task.(line {}) Customer Name : {}, "
							+ "Ref Number : {}, Message : {}",
							(i + 4), uploadTaskBean.getCustomerName(),
							agreementApplNo, e.getMessage(), e);
					StringBuilder errorUpload = new StringBuilder();
					errorUpload.append(e.getMessage());
					listofErrorTask.add(uploadTaskBean);
					TaskBean taskBean = new TaskBean();
					taskBean.setErrorText(errorUpload);
					taskBeanList.add(taskBean);
					taskSuccess--;
				}
			} 
			else {
				if (GlobalVal.SUBSYSTEM_MC.equals(loginBean.getAmMssubsystem()
						.getSubsystemName())) {
					DateTime currentTime = new DateTime();
					currentTime = currentTime.minusMillis(currentTime
							.getMillisOfDay());
					Date minDate = currentTime.toDate();
					currentTime = currentTime.plusDays(1).minusMillis(3);
					Date maxDate = currentTime.toDate();

					TrTaskH trTaskH = this.getManagerDAO().selectOne(
							"from TrTaskH tth "
							+ "join fetch tth.msStatustask mst "
							+ "where tth.agreementNo = :agreementNo "
							+ "and mst.uuidStatusTask = :uuidStatusTask "
							+ "and tth.dtmCrt between :minDate and :maxDate", 
							new Object[][] {{"agreementNo", agreementApplNo}, 
							{"uuidStatusTask", msStatustaskP.getUuidStatusTask()}, 
							{"minDate", minDate}, {"maxDate", maxDate}});

					if (null != trTaskH) {
						if (!GlobalVal.SURVEY_STATUS_TASK_RELEASED
								.equals(trTaskH.getMsStatustask()
										.getStatusCode())) {

							trTaskH.setMsStatustask(msStatustaskC);
							this.getManagerDAO().update(trTaskH);

							String codeProcess = GlobalVal.CODE_PROCESS_CANCELED;
							String notes = "Canceled by "
									+ loginBean.getFullName();
							String fieldPerson = null;
							if (collUserBean == null) {
								AmMsuser taskUser = trTaskH.getAmMsuser();
								fieldPerson = (taskUser == null) ? null
										: taskUser.getFullName();
							} 
							else {
								collUserBean.getFullName();
							}
							insertTaskHistory(callerId, msStatustaskC, trTaskH,
									notes, codeProcess, fieldPerson, loginBean, null);
						}
					}
				}
			}

			sw.stop();
			LOG.debug("Task [{}] processing time: {}(ms).", i,
					sw.elapsed(TimeUnit.MILLISECONDS));

		}
		int counttaskError = listofErrorTask.size();
		if (GlobalVal.SUBSYSTEM_MC.equals(loginBean.getAmMssubsystem()
				.getSubsystemName())) {
			List listofInstallment = (List) result.get("resultInstallment");
			List<InstallmentCardBean> listofErrorInstallment = (List<InstallmentCardBean>) result
					.get("resultErrorInstallment");		
			int installmentSuccess = listofInstallment.size();
			//insert instalment
			for (int i = 0; i < listofInstallment.size(); i++) {
				InstallmentCardBean installmentCardBean = (InstallmentCardBean) listofInstallment.get(i);	
				try {
					Object [][] params = {{Restrictions.eq("agreementNo", 
							installmentCardBean.getAgreementNo())},
										 {Restrictions.eq("installmentNo", 
							checkEmptyShort(installmentCardBean.getInstallmentNo()))}};
					TrViewinstallmentcard trViewinstallmentcard = this.getManagerDAO()
							.selectOne(TrViewinstallmentcard.class, params);
					if (trViewinstallmentcard == null) {
						trViewinstallmentcard = new TrViewinstallmentcard();
						trViewinstallmentcard.setDtmCrt(new Date());
						trViewinstallmentcard.setUsrCrt(loginBean.getLoginId());
					}
					else {
						trViewinstallmentcard.setDtmUpd(new Date());
						trViewinstallmentcard.setUsrUpd(loginBean.getLoginId());
					}
					trViewinstallmentcard.setAgreementNo(installmentCardBean.getAgreementNo());
					trViewinstallmentcard.setBranchCode(installmentCardBean.getBranchCode());
					trViewinstallmentcard.setInstallmentNo(checkEmptyShort(
							installmentCardBean.getInstallmentNo()));
					trViewinstallmentcard.setDueDate(toDateFormat(GenericTaskServiceLogic.UPLOAD_TASK_DATE, 
							installmentCardBean.getDueDate()));
					trViewinstallmentcard.setInstallmentAmount(checkEmptyBigdecimal(
							installmentCardBean.getInstallmentAmount()));
					trViewinstallmentcard.setInstlPaidDate(toDateFormat(GenericTaskServiceLogic.UPLOAD_TASK_DATE, 
							installmentCardBean.getInstlPaidDate()));
					trViewinstallmentcard.setInstlPaidAmount(checkEmptyBigdecimal(
							installmentCardBean.getInstlPaidAmount()));
					trViewinstallmentcard.setLcInstlAmount(checkEmptyBigdecimal(
							installmentCardBean.getLcInstlAmount()));
					trViewinstallmentcard.setLcInstlPaid(checkEmptyBigdecimal(
							installmentCardBean.getLcInstlPaid()));
					trViewinstallmentcard.setLcInstlWaived(checkEmptyBigdecimal(
							installmentCardBean.getLcInstlWaived()));
					trViewinstallmentcard.setPrincipalAmount(checkEmptyBigdecimal(
							installmentCardBean.getPrincipalAmount()));
					trViewinstallmentcard.setInterestAmount(checkEmptyBigdecimal(
							installmentCardBean.getInterestAmount()));
					trViewinstallmentcard.setOsPrincipalAmount(checkEmptyBigdecimal(
							installmentCardBean.getOsPrincipalAmount()));
					trViewinstallmentcard.setOsInterestAmount(checkEmptyBigdecimal(
							installmentCardBean.getOsInterestAmount()));
					trViewinstallmentcard.setLcDays(checkEmptyInteger(
							installmentCardBean.getLcDays()));
					trViewinstallmentcard.setLcAdminFee(checkEmptyBigdecimal(
							installmentCardBean.getLcAdminFee()));
					trViewinstallmentcard.setLcAdminFeePaid(checkEmptyBigdecimal(
							installmentCardBean.getLcAdminFeePaid()));
					trViewinstallmentcard.setLcAdminFeeWaive(checkEmptyBigdecimal(
							installmentCardBean.getLcAdminFeeWaive()));
					if (trViewinstallmentcard.getDtmUpd() != null) {
						this.getManagerDAO().update(trViewinstallmentcard);
					}
					else {
						this.getManagerDAO().insert(trViewinstallmentcard);
					}
				} 
				catch (Exception e) {
					LOG.error(
							"Error while Add Installment Schedule (AgreementNo: {}, "
							+ "Installment No : {}, Message : {}",
							installmentCardBean.getAgreementNo(),
							installmentCardBean.getInstallmentNo(), e.getMessage(), e);
					StringBuilder errorUpload = new StringBuilder();
					errorUpload.append(e.getMessage());
					installmentCardBean.setErrorText(errorUpload);
					listofErrorInstallment.add(installmentCardBean);
					installmentSuccess--;
				}
			}
			
			
			List listofCollActivity = (List) result.get("resultCollActivity");
			List<CollectionActivityBean> listofErrorCollActivity = (List<CollectionActivityBean>) result
					.get("resultErrorCollActivity");
			int collActivitySuccess = listofCollActivity.size();
			//insert coll activity
			for (int i = 0; i < listofCollActivity.size(); i++) {

				CollectionActivityBean collectionActivityBean = 
						(CollectionActivityBean) listofCollActivity.get(i);
				try {
					TrViewcollactivity trViewcollactivity = new TrViewcollactivity();
					trViewcollactivity.setDtmCrt(new Date());
					trViewcollactivity.setUsrCrt(loginBean.getLoginId());
					trViewcollactivity.setAgreementNo(collectionActivityBean.getAgreementNo());
					trViewcollactivity.setBranchCode(collectionActivityBean.getBranchCode());
					trViewcollactivity.setActivityDate(toDateFormat(
							GenericTaskServiceLogic.UPLOAD_TASK_DATE, 
							collectionActivityBean.getActivityDate()));
					trViewcollactivity.setCollectorName(collectionActivityBean.getCollectorName());
					trViewcollactivity.setActivity(collectionActivityBean.getActivity());
					trViewcollactivity.setResult(collectionActivityBean.getResult());
					trViewcollactivity.setPtpDate(toDateFormat(
							GenericTaskServiceLogic.UPLOAD_TASK_DATE,
							collectionActivityBean.getPtpDate()));
					trViewcollactivity.setNotes(collectionActivityBean.getNotes());
					trViewcollactivity.setOverDueDays(checkEmptyInteger(
							collectionActivityBean.getOverDueDays()));
					trViewcollactivity.setNextPlanDate(toDateFormat(
							GenericTaskServiceLogic.UPLOAD_TASK_DATE,
							collectionActivityBean.getNextPlanDate()));
					trViewcollactivity.setNextPlanAction(collectionActivityBean.getNextPlanAction());					
					this.getManagerDAO().insert(trViewcollactivity);
				} 
				catch (Exception e) {
					LOG.error(
							"Error while Add Collection Activity (AgreementNo: {}, "
							+ "Installment No : {}, Message : {}",
							collectionActivityBean.getAgreementNo(), e.getMessage(), e);
					StringBuilder errorUpload = new StringBuilder();
					errorUpload.append(e.getMessage());
					collectionActivityBean.setErrorText(errorUpload);
					listofErrorCollActivity.add(collectionActivityBean);
					collActivitySuccess--;
				}
			}
			
			List listofResultPayment = (List) result.get("resultPayment");
			List<PaymentHistoryBean> listofErrorPayment = (List<PaymentHistoryBean>) result
					.get("resultErrorPayment");
			int paymentSuccess = listofResultPayment.size();
			//insert resultpayment
			for (int i = 0; i < listofResultPayment.size(); i++) {
				PaymentHistoryBean paymentHistoryBean = (PaymentHistoryBean) listofResultPayment.get(i);
				try {
					Object [][] prm = {{Restrictions.eq("agreementNo", 
							paymentHistoryBean.getAgreementNo())},
							{Restrictions.eq("installmentNumber", 
							Integer.parseInt(paymentHistoryBean.getInstallmentNumber()))}};
					TrViewpaymenthistoryH exist = this.getManagerDAO()
							.selectOne(TrViewpaymenthistoryH.class, prm);
					if (null == exist) {
						TrViewpaymenthistoryH trViewpaymenthistoryH = new TrViewpaymenthistoryH();
						TrViewpaymenthistoryD trViewpaymenthistoryD = new TrViewpaymenthistoryD();
						trViewpaymenthistoryH.setDtmCrt(new Date());
						trViewpaymenthistoryH.setUsrCrt(loginBean.getLoginId());
						trViewpaymenthistoryH.setAgreementNo(paymentHistoryBean.getAgreementNo());
						trViewpaymenthistoryH.setBranchCode(paymentHistoryBean.getBranchCode());
						trViewpaymenthistoryH.setReceiptNo(paymentHistoryBean.getReceiptNo());
						trViewpaymenthistoryH.setValueDate(toDateFormat(
								GenericTaskServiceLogic.UPLOAD_TASK_DATE,
								paymentHistoryBean.getValueDate()));
						trViewpaymenthistoryH.setPostingDate(toDateFormat(
								GenericTaskServiceLogic.UPLOAD_TASK_DATE,
								paymentHistoryBean.getPostingDate()));
						trViewpaymenthistoryH.setPaymentAmount(checkEmptyBigdecimal(
								paymentHistoryBean.getPaymentAmount()));
						trViewpaymenthistoryH.setInstallmentAmount(checkEmptyBigdecimal(
								paymentHistoryBean.getInstallmentAmount()));
						trViewpaymenthistoryH.setInstallmentNumber(checkEmptyInteger(
								paymentHistoryBean.getInstallmentNumber()));
						trViewpaymenthistoryH.setTransactionType(paymentHistoryBean.getTransactionType());
						trViewpaymenthistoryH.setWopCode(paymentHistoryBean.getWopCode());
						trViewpaymenthistoryH.setTransactionId(checkEmptyLong(
								paymentHistoryBean.getTransactionId()));
						this.getManagerDAO().insert(trViewpaymenthistoryH);	
						trViewpaymenthistoryH = this.getManagerDAO().selectOne(
								TrViewpaymenthistoryH.class, 
								trViewpaymenthistoryH.getUuidViewPaymentHistoryH());
						trViewpaymenthistoryD.setTrViewpaymenthistoryH(trViewpaymenthistoryH);
						trViewpaymenthistoryD.setDtmCrt(new Date());
						trViewpaymenthistoryD.setUsrCrt(loginBean.getLoginId());
						trViewpaymenthistoryD.setPaymentAllocationName(
								paymentHistoryBean.getPaymentAllocationName());
						trViewpaymenthistoryD.setOsAmountOd(checkEmptyBigdecimal(
								paymentHistoryBean.getOsAmountOd()));
						trViewpaymenthistoryD.setReceiveAmount(checkEmptyBigdecimal(
								paymentHistoryBean.getReceiveAmount()));
						this.getManagerDAO().insert(trViewpaymenthistoryD);			
					}
					else {
						throw new RemoteException(
								this.messageSource.getMessage("businesslogic.uploadtask.alreadyexist",
			                    new Object[] {paymentHistoryBean.getAgreementNo(), 
								paymentHistoryBean.getInstallmentNumber()}, this.retrieveLocaleAudit(callerId)));
					}
				} 
				catch (Exception e) {
					LOG.error(
							"Error while Add Payment History (AgreementNo: {}, "
							+ "Transaction Id : {}, Message : {}",
							paymentHistoryBean.getAgreementNo(),
							paymentHistoryBean.getTransactionId(), e.getMessage(), e);
					StringBuilder errorUpload = new StringBuilder();
					errorUpload.append(e.getMessage());
					paymentHistoryBean.setErrorText(errorUpload);
					listofErrorPayment.add(paymentHistoryBean);
					paymentSuccess--;
				}
			}
			
			if (!listofErrorTask.isEmpty() || !listofErrorInstallment.isEmpty() 
					|| !listofErrorCollActivity.isEmpty() || !listofErrorPayment.isEmpty()) {
				Map<String,Object> tempError = new HashMap<String,Object>();
				tempError.put("listofErrorTask", listofErrorTask);
				tempError.put("listofErrorInstallment", listofErrorInstallment);
				tempError.put("listofErrorCollActivity", listofErrorCollActivity);
				tempError.put("listofErrorPayment", listofErrorPayment);
				tempError.put("installmentSuccess", installmentSuccess);
				tempError.put("collActivitySuccess", collActivitySuccess);
				tempError.put("paymentSuccess", paymentSuccess);
				try {
					errorUploadByte = exportErrorTaskMC(tempError, taskSuccess,
							identifierList, taskBeanList, loginBean
									.getAmMssubsystem().getSubsystemName(), formVersion, callerId);
				} 
				catch (SQLException e) {
					throw new UploadTaskException(this.messageSource.getMessage(
							"businesslogic.global.errorgenxld", null,
							this.retrieveLocaleAudit(callerId)), e, Reason.ERROR_GENERATE);
				}
			}
			counttaskError = counttaskError + listofErrorInstallment.size() + 
					listofErrorCollActivity.size() + listofErrorPayment.size();
			taskSuccess = taskSuccess + installmentSuccess + collActivitySuccess + paymentSuccess;
		}
		else {
			if (!listofErrorTask.isEmpty()) {
				try {
					errorUploadByte = exportErrorTask(listofErrorTask, taskSuccess,
							identifierList, taskBeanList, loginBean
									.getAmMssubsystem().getSubsystemName(), formVersion, callerId);
				} 
				catch (SQLException e) {
					throw new UploadTaskException(this.messageSource.getMessage(
							"businesslogic.global.errorgenxld", null,
							this.retrieveLocaleAudit(callerId)), e, Reason.ERROR_GENERATE);
				}
			}
		}
		
		mapReturn.put("errorUploadByte", errorUploadByte);
		mapReturn.put("inputCount", taskSuccess + counttaskError);
		mapReturn.put("countSuccess", taskSuccess);
		mapReturn.put("countError", counttaskError);
		return mapReturn;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private void saveFile(File uploadedFile, AmMsuser loginBean,
			String formName, String fileName, Date date, AuditContext callerId)
			throws IOException {
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		FileInputStream inputStream = new FileInputStream(uploadedFile);
		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		String path = StringUtils.EMPTY;
		String storeFileName = StringUtils.EMPTY;

		LOG.info("File name from uploaded file : {}", fileName);
		String[] splitFile = StringUtils.split(fileName,
				SystemUtils.FILE_SEPARATOR);
		String splitFileName = splitFile[splitFile.length - 1];
		LOG.info("File name after remove prefix file path : {}", splitFileName);

		try {
			storeFileName = StringUtils.remove(splitFileName, ".xls") + "_"
					+ loginBean.getLoginId() + "_"
					+ DateFormatUtils.format(date, "yyyyMMdd HHmmss") + ".xls";
			LOG.info("Store file name : {}", storeFileName);

			Object[][] param = { { Restrictions.eq("gsCode",
					GlobalKey.GENERALSETTING_XLS_UPLOAD_PATH) } };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(
					AmGeneralsetting.class, param);

			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.uploadtask.locationnotdefined", null,
						this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}

			path = amGeneralSetting.getGsValue() + storeFileName;
			LOG.info("Stored file location : {}", path);
			wb.write(stream);
		} 
		finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (wb != null) {
					wb.close();
				}
			} 
			catch (IOException e) {
				LOG.error(
						"SCHEDULER error while close workbook. params[form={}]",
						formName);
			}
		}

		byte[] exp = stream.toByteArray();

		TrUploadtasklog trUploadTaskLog = new TrUploadtasklog();
		trUploadTaskLog.setAmMsuser(loginBean);
		trUploadTaskLog.setDtmUpload(date);
		trUploadTaskLog.setInputFilename(storeFileName);
		trUploadTaskLog.setIsFinish("0");
		this.getManagerDAO().insert(trUploadTaskLog);

		FileOutputStream fileOut = new FileOutputStream(path);
		try {
			fileOut.write(exp);
			fileOut.flush();
		} 
		finally {
			fileOut.close();
		}
	}
	
	//get data sheet installment
	private InstallmentCardBean setHeader(Integer cell, String value,
			InstallmentCardBean installmentCardBean) {
		try {
			switch (cell) {
				case 0:
					installmentCardBean.setAgreementNo(value); // Agreement No
					break;
				case 1:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setBranchCode(value); // Branch Code
					} 
					else {
						installmentCardBean.setBranchCode(null);
					}
					break;
				case 2:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setInstallmentNo(value); // Installment No
					} 
					else {
						installmentCardBean.setInstallmentNo(null);
					}
					break;
				case 3:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setDueDate(value); // Due Date
					} 
					else {
						installmentCardBean.setDueDate(null);
					}
					break;
				case 4:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setInstallmentAmount(value); // Installment Amount
					} 
					else {
						installmentCardBean.setInstallmentAmount(null);
					}
					break;
				case 5:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setInstlPaidDate(value);
					} 
					else {
						installmentCardBean.setInstlPaidDate(null);
					}
					break;
				case 6:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setInstlPaidAmount(value); // Instl Paid Amount
					} 
					else {
						installmentCardBean.setInstlPaidAmount("0");
					}
					break;
				case 7:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setLcInstlAmount(value); // Lc Instl Amount
					} 
					else {
						installmentCardBean.setLcInstlAmount("0");
					}
					break;
				case 8:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setLcInstlPaid(value);
					} 
					else {
						installmentCardBean.setLcInstlPaid("0");
					}
					break;
				case 9:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setLcInstlWaived(value); // Lc Instl Waived
					} 
					else {
						installmentCardBean.setLcInstlWaived("0");
					}
					break;
				case 10:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setPrincipalAmount(value); // Principal Amount
					} 
					else {
						installmentCardBean.setPrincipalAmount("0");
					}
					break;
				case 11:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setInterestAmount(value);// Interest Amount
					} 
					else {
						installmentCardBean.setInterestAmount("0");
					}
					break;
				case 12:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setOsPrincipalAmount(value); // Os Principal Amount
					} 
					else {
						installmentCardBean.setOsPrincipalAmount("0");
					}
					break;
				case 13:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setOsInterestAmount(value); // Os Interest Amount
					} 
					else {
						installmentCardBean.setOsInterestAmount("0");
					}
					break;
				case 14:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setLcDays(value); // Lc Days
					} 
					else {
						installmentCardBean.setLcDays("0");
					}
					break;
				case 15:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setLcAdminFee(value); //Admin fee
					} 
					else {
						installmentCardBean.setLcAdminFee("0");
					}
					break;
				case 16:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setLcAdminFeePaid(value); // Lc Admin Fee Paid
					} 
					else {
						installmentCardBean.setLcAdminFeePaid("0");
					}
					break;
				case 17:
					if (StringUtils.isNotBlank(value)) {
						installmentCardBean.setLcAdminFeeWaive(value); // Lc Admin Fee Waive
					} 
					else {
						installmentCardBean.setLcAdminFeeWaive("0");
					}
					break;
			}
		}
		catch(Exception e) {
			throw new DatabaseException(
					DatabaseException.Reason.OTHER_DATA_ACCESS_EXCEPTION, e.getCause().getMessage(), e); 
		}
		return installmentCardBean;
	}
	
	//get data sheet coll activity
	private CollectionActivityBean setHeader(Integer cell, String value,
			CollectionActivityBean collectionActivityBean) {
		switch (cell) {
			case 0:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setAgreementNo(value);
				}
				else{
					collectionActivityBean.setAgreementNo(null);
				}
				break;
			case 1:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setBranchCode(value);
				} 
				else {
					collectionActivityBean.setBranchCode(null);
				}
				break;
			case 2:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setActivityDate(value);
				} 
				else {
					collectionActivityBean.setActivityDate(null);
				}
				break;
			case 3:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setCollectorName(value);
				} 
				else {
					collectionActivityBean.setCollectorName(null);
				}
				break;
			case 4:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setActivity(value);
				} 
				else {
					collectionActivityBean.setActivity(null);
				}
				break;
			case 5:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setResult(value);
				} 
				else {
					collectionActivityBean.setResult(null);
				}
				break;
			case 6:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setPtpDate(value);
				} 
				else {
					collectionActivityBean.setPtpDate(null);
				}
				break;
			case 7:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setNotes(value);
				} 
				else {
					collectionActivityBean.setNotes(null);
				}
				break;
			case 8:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setOverDueDays(value);
				} 
				else {
					collectionActivityBean.setOverDueDays("0");
				}
				break;
			case 9:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setNextPlanDate(value);
				} 
				else {
					collectionActivityBean.setNextPlanDate(null);
				}
				break;
			case 10:
				if (StringUtils.isNotBlank(value)) {
					collectionActivityBean.setNextPlanAction(value);
				} 
				else {
					collectionActivityBean.setNextPlanAction(null);
				}
				break;
		}
		return collectionActivityBean;
	}
	
	//get data sheet payment history
	private PaymentHistoryBean setHeader(Integer cell, String value,
			PaymentHistoryBean paymentHistoryBean ) {
		switch (cell) {
			case 0:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setAgreementNo(value); 
				} 
				else {
					paymentHistoryBean.setAgreementNo(null); // Agreement No
				}
				break;
			case 1:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setBranchCode(value); // Branch Code
				} 
				else {
					paymentHistoryBean.setBranchCode(null);
				}
				break;
			case 2:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setReceiptNo(value);
				} 
				else {
					paymentHistoryBean.setReceiptNo(null);
				}
				break;
			case 3:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setValueDate(value);
				} 
				else {
					paymentHistoryBean.setValueDate(null);
				}
				break;
			case 4:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setPostingDate(value);
				} 
				else {
					paymentHistoryBean.setPostingDate(null);
				}
				break;
			case 5:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setPaymentAmount(value);
				} 
				else {
					paymentHistoryBean.setPaymentAmount(null);
				}
				break;
			case 6:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setInstallmentAmount(value);
				} 
				else {
					paymentHistoryBean.setInstallmentAmount(null);
				}
				break;
			case 7:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setInstallmentNumber(value);
				} 
				else {
					paymentHistoryBean.setInstallmentNumber(null);
				}
				break;
			case 8:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setTransactionType(value);
				} 
				else {
					paymentHistoryBean.setTransactionType(null);
				}
				break;
			case 9:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setWopCode(value);
				} 
				else {
					paymentHistoryBean.setWopCode(null);
				}
				break;
			case 10:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setTransactionId(value);;
				} 
				else {
					paymentHistoryBean.setTransactionId(null);
				}
				break;
			case 11:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setPaymentAllocationName(value);
				} 
				else {
					paymentHistoryBean.setPaymentAllocationName(null);
				}
				break;
			case 12:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setOsAmountOd(value);
				} 
				else {
					paymentHistoryBean.setOsAmountOd(null);
				}
				break;
			case 13:
				if (StringUtils.isNotBlank(value)) {
					paymentHistoryBean.setReceiveAmount(value);
				} 
				else {
					paymentHistoryBean.setReceiveAmount(null);
				}
				break;
		}
		return paymentHistoryBean;
	}

	private UploadTaskBean setHeader(Integer cell, String value,
			UploadTaskBean uploadTaskBean, String subsystemName) {
		if (GlobalVal.SUBSYSTEM_MS.equals(subsystemName)) {
			switch (cell) {
				case 0:
					uploadTaskBean.setApplNo(value);
					break;
				case 1:
					MsPriority msPriority = new MsPriority();
					msPriority.setPriorityDesc(value);
					uploadTaskBean.setMsPriority(msPriority);
					break;
				case 2:
					MsBranch msBranch = new MsBranch();
					msBranch.setBranchCode(value);
					uploadTaskBean.setMsBranch(msBranch);
					break;
				case 3:
					AmMsuser amMsuser = new AmMsuser();
					amMsuser.setLoginId(value);
					uploadTaskBean.setAmMsuser(amMsuser);
					break;
				case 4:
					uploadTaskBean.setCustomerName(value);
					break;
				case 5:
					uploadTaskBean.setCustomerPhone(value);
					break;
				case 6:
					uploadTaskBean.setCustomerAddress(value);
					break;
				case 7:
					uploadTaskBean.setNotes(value);
					break;
				case 8:
					if (StringUtils.isNotBlank(value)) {
						uploadTaskBean.setLatitude(new BigDecimal(value));
					} 
					else {
						uploadTaskBean.setLatitude(null);
					}
					break;
				case 9:
					if (StringUtils.isNotBlank(value)) {
						uploadTaskBean.setLongitude(new BigDecimal(value));
					} 
					else {
						uploadTaskBean.setLongitude(null);
					}
					break;
				case 10:
					if (!value.isEmpty()) {
						uploadTaskBean.setIsCancelled(value);
						;
					} 
					else {
						uploadTaskBean.setIsCancelled(null);
						;
					}
					break;
			}
		} 
		else if (GlobalVal.SUBSYSTEM_MT.equals(subsystemName)) {
			switch (cell) {
				case 0:
					MsPriority msPriority = new MsPriority();
					msPriority.setPriorityDesc(value);
					uploadTaskBean.setMsPriority(msPriority);
					break;
				case 1:
					MsBranch msBranch = new MsBranch();
					msBranch.setBranchCode(value);
					uploadTaskBean.setMsBranch(msBranch);
					break;
				case 2:
					AmMsuser amMsuser = new AmMsuser();
					amMsuser.setLoginId(value);
					uploadTaskBean.setAmMsuser(amMsuser);
					break;
				case 3:
					MsLocation msLocation = new MsLocation();
					msLocation.setLocationName(value);
					uploadTaskBean.setMsLocation(msLocation);
					break;
				case 4:
					uploadTaskBean.setCustomerPhone(value);
					break;
				case 5:
					uploadTaskBean.setNotes(value);
					break;
				case 6:
					SimpleDateFormat format = new SimpleDateFormat(
							"yyyy-MM-dd HH:mm:ss");
					try {
						uploadTaskBean.setAssignDate(format.parse(value));
					} 
					catch (ParseException e) {
						value = null;
					}
					break;
				case 7:
					uploadTaskBean.setGroupSeq(value);
					break;
				case 8:
					uploadTaskBean.setTaskSeq(value);
					break;
				case 9:
					if (!value.isEmpty()) {
						uploadTaskBean.setIsCancelled(value);
					} 
					else {
						uploadTaskBean.setIsCancelled(null);
					}
					break;
			}
		} 
		else {
			switch (cell) {
				case 0:
					uploadTaskBean.setAgreementNo(value);
					break;
				case 1:
					MsPriority msPriority = new MsPriority();
					msPriority.setPriorityDesc(value);
					uploadTaskBean.setMsPriority(msPriority);
					break;
				case 2:
					AmMsuser amMsuser = new AmMsuser();
					amMsuser.setLoginId(value);
					uploadTaskBean.setAmMsuser(amMsuser);
					break;
				case 3:
					uploadTaskBean.setCustomerName(value);
					break;
				case 4:
					uploadTaskBean.setCustomerPhone(value);
					break;
				case 5:
					uploadTaskBean.setCustomerAddress(value);
					break;
				case 6:
					uploadTaskBean.setNotes(value);
					break;
				case 7:
					uploadTaskBean.setSurveyAssignmentID(value);
					break;
				case 8:
					if (StringUtils.isNotBlank(value)) {
						uploadTaskBean.setLatitude(new BigDecimal(value));
					} 
					else {
						uploadTaskBean.setLatitude(null);
					}
					break;
				case 9:
					if (StringUtils.isNotBlank(value)) {
						uploadTaskBean.setLongitude(new BigDecimal(value));
					} 
					else {
						uploadTaskBean.setLongitude(null);
					}
					break;
				case 10:
					if (!value.isEmpty()) {
						uploadTaskBean.setIsCancelled(value);
					} 
					else {
						uploadTaskBean.setIsCancelled(null);
					}
					break;
			}
		}
		return uploadTaskBean;
	}

	// error condition untuk di action
	private Map<String, Object> errorUpload(Exception e) {
		Map<String, Object> ret = new HashMap<String, Object>();
		byte[] tmp = new byte[1];
		String msg = StringUtils.EMPTY;
		if (e instanceof UploadTaskException) {
			if (((UploadTaskException) e).getReason().equals(
					Reason.ERROR_MAX_EXCEEDED)) {
				tmp[0] = 2;
			} 
			else if (((UploadTaskException) e).getReason().equals(
					Reason.ERROR_TEMPLATE)) {
				tmp[0] = 1;
			}
			msg = e.getMessage();
			ret.put("exp", tmp);
			ret.put("msg", msg);
		} 
		else {
			tmp[0] = 3;
			ret.put("exp", tmp);
		}
		return ret;
	}

	private boolean checkEmptyRow(HSSFRow row, int totRow) {
		int nullValues = 0;
		for (int c = 0; c < totRow; c++) {
			HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
			if (cell == null) {
				nullValues++;
			}
		}

		if (nullValues == totRow) {
			return true;
		} 
		else {
			return false;
		}
	}

	public StringBuilder checkingTaskUpload(String formName, 
			UploadTaskBean uploadTaskBean, String subsystemName,
			List<UploadTaskBean> resultList, AuditContext callerId) {
		StringBuilder errorUpload = new StringBuilder();
		if (formName == null || (StringUtils.EMPTY).equals(formName)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { GlobalVal.HEADER_FORM_NAME };
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (uploadTaskBean.getMsPriority().getPriorityDesc() == null
				|| (StringUtils.EMPTY).equals(uploadTaskBean.getMsPriority()
						.getPriorityDesc())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { GlobalVal.HEADER_PRIORITY };
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (GlobalVal.SUBSYSTEM_MC.equals(subsystemName)) {
			if (uploadTaskBean.getAgreementNo() == null
					|| ("").equals(uploadTaskBean.getAgreementNo())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_AGREEMENT_NO };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (uploadTaskBean.getAmMsuser().getLoginId() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean.getAmMsuser()
							.getLoginId())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_MOBILE_USER_ID };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
		}
		if (GlobalVal.SUBSYSTEM_MS.equals(subsystemName)) {
			if (uploadTaskBean.getApplNo() == null
					|| ("").equals(uploadTaskBean.getApplNo())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_APPLICATION_NO };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (uploadTaskBean.getMsBranch().getBranchCode() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean.getMsBranch()
							.getBranchCode())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_BRANCH_ID };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
		}
		if (GlobalVal.SUBSYSTEM_MT.equals(subsystemName)) {
			if (uploadTaskBean.getMsBranch().getBranchCode() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean.getMsBranch()
							.getBranchCode())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_BRANCH_ID };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (uploadTaskBean.getAmMsuser().getLoginId() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean.getAmMsuser()
							.getLoginId())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_MOBILE_USER_ID };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (uploadTaskBean.getMsLocation().getLocationName() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean
							.getMsLocation().getLocationName())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_LOCATION_NAME };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (uploadTaskBean.getAssignDate() == null) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_DATETIME_ASSIGNMENT };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (uploadTaskBean.getAssignDate() != null) {
				Date current = new Date();
				int dt = uploadTaskBean.getAssignDate().compareTo(current);
				if (dt < 0) {
					Object[] obj = { GlobalVal.HEADER_DATETIME_ASSIGNMENT };
					errorUpload.append(this.messageSource.getMessage(
							"businesslogic.global.datetimeexpired", obj,
							this.retrieveLocaleAudit(callerId)));
				}
			}
			if (uploadTaskBean.getTaskSeq() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean.getTaskSeq())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_TRIP };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (uploadTaskBean.getGroupSeq() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean.getGroupSeq())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_STOP };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}

			// cek validasi k-3
			if ((uploadTaskBean.getAmMsuser().getLoginId() != null)
					&& (uploadTaskBean.getAssignDate() != null)
					&& (uploadTaskBean.getTaskSeq() != null)
					&& (uploadTaskBean.getGroupSeq() != null)) {

				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				String dateAssign = sdf.format(uploadTaskBean.getAssignDate());

				// getUserUUID
				boolean isUserExist = true;
				String mobileUserID = uploadTaskBean.getAmMsuser().getLoginId();
				AmMsuser amMsUser = new AmMsuser();
				String groupTaskID = StringUtils.EMPTY;
				if (!StringUtils.isBlank(mobileUserID)) {
					Object[][] paramGetMsUser = { { Restrictions.eq("loginId",
							mobileUserID) } };
					amMsUser = this.getManagerDAO().selectOne(AmMsuser.class,
							paramGetMsUser);
					if (null == amMsUser) {
						isUserExist = false;
					}
				}

				boolean isDataRegistered = false;
				// check to form
				if (!resultList.isEmpty()) {
					UploadTaskBean upTaskBean = new UploadTaskBean();
					for (int i = 0; i < resultList.size(); i++) {
						upTaskBean = resultList.get(i);
						String assignDateUpload = sdf.format(uploadTaskBean
								.getAssignDate());
						String assignDateUp = sdf.format(upTaskBean
								.getAssignDate());
						if ((uploadTaskBean.getAmMsuser().getLoginId()
								.equals(upTaskBean.getAmMsuser().getLoginId()))
								&& (assignDateUpload.equals(assignDateUp))
								&& (uploadTaskBean.getTaskSeq()
										.equals(upTaskBean.getTaskSeq()))
								&& (uploadTaskBean.getGroupSeq()
										.equals(upTaskBean.getGroupSeq()))) {
							isDataRegistered = true;
						}
					}
				}

				// check to database
				if (isUserExist && (isDataRegistered == false)) {
					// EXISTTASKSEQ*10
					String existsTaskSeq = String.valueOf((Integer
							.valueOf(uploadTaskBean.getTaskSeq()) * 1));

					Object[][] params = { { "dateAssignStart", dateAssign+" 00:00:00.000" },
							{ "dateAssignEnd", dateAssign+" 23:59:59.997" },
							{ "uuidMobileUserID", amMsUser.getUuidMsUser() },
							{ "taskSeq", existsTaskSeq },
							{ "groupSeq", uploadTaskBean.getGroupSeq() } };
					List list = this.getManagerDAO().selectAllNative(
							"services.common.task.getListErrorValidasi", params, null);
					if (!list.isEmpty()) {
						isDataRegistered = true;
						Map map = (Map) list.get(0);
						groupTaskID = map.get("d2").toString();
					} 
					else {
						Object[][] params2 = { { "uuidMobileUserID", amMsUser.getUuidMsUser() },
								   { "groupSeq", uploadTaskBean.getGroupSeq() } };
						List grupTaskId = this.getManagerDAO().selectAllNative(
								"services.common.task.getGroupTaskIdByDtmCrt", params2, null);
						if (!grupTaskId.isEmpty()){
							Map map = (Map) grupTaskId.get(0);
							groupTaskID = map.get("d2").toString();
						}
					}
				}

				if (!isUserExist) {
					if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(this.messageSource.getMessage(
							"businesslogic.global.mobileuseridnotvalid", null,
							this.retrieveLocaleAudit(callerId)));
				}
				if (isDataRegistered) {
					if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
						errorUpload.append(" | ");
					}
					errorUpload.append(this.messageSource.getMessage(
							"businesslogic.global.duplicatevalueuptaskmt",
							null, this.retrieveLocaleAudit(callerId)));
				}

				if (isUserExist && !isDataRegistered) {
					if (isClosingTask(String.valueOf(amMsUser.getUuidMsUser()), dateAssign)) {
						errorUpload.append(this.messageSource.getMessage(
								"businesslogic.global.alreadyclosingtask",
								null, this.retrieveLocaleAudit(callerId)));
					}
				}

				if (isUserExist && !isDataRegistered
						&& !StringUtils.isBlank(uploadTaskBean.getGroupSeq())) {
					if (!StringUtils.isBlank(groupTaskID)) {
						String[] prevTaskSeq = getPrevTaskSeq(groupTaskID);
						if (!StringUtils.isBlank(prevTaskSeq[0])) {
							if (Integer.valueOf(prevTaskSeq[0]) > Integer
									.valueOf(uploadTaskBean.getTaskSeq())*1) {
								errorUpload.append(this.messageSource.getMessage(
										"businesslogic.uploadtask.isdoingtask",
										null, this.retrieveLocaleAudit(callerId)));
							}
						}
					}
				}
			}
		} 
		else {
			if (uploadTaskBean.getCustomerName() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean
							.getCustomerName())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_CUSTOMER_NAME };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (uploadTaskBean.getCustomerPhone() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean
							.getCustomerPhone())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_CUSTOMER_PHONE };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (uploadTaskBean.getCustomerAddress() == null
					|| (StringUtils.EMPTY).equals(uploadTaskBean
							.getCustomerAddress())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { GlobalVal.HEADER_CUSTOMER_ADDRESS };
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
		}

		return errorUpload;
	}
	
	public StringBuilder checkingPayment(PaymentHistoryBean paymentHistoryBean, AuditContext callerId) {
		StringBuilder errorUpload = new StringBuilder();
		// Agreement No mandatory length 
		if (isMandatory(paymentHistoryBean.getAgreementNo())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Agreement No"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getAgreementNo(),20)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Agreement No", "20"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
		
	    //branchCode mandatory length isExist
		if (isMandatory(paymentHistoryBean.getBranchCode())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Branch Code"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getBranchCode(),20)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Branch Code", "20"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (!isBranchExist(paymentHistoryBean.getBranchCode())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Branch Code"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.branchnotexist", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //receiptNo length
		if (isMaxLength(paymentHistoryBean.getReceiptNo(),80)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Receipt No", "80"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //valueDate mandatory date
		if (isMandatory(paymentHistoryBean.getValueDate())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Value Date"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isValidDateFormat(GenericTaskServiceLogic.UPLOAD_TASK_DATE, 
				paymentHistoryBean.getValueDate())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Value Date", GenericTaskServiceLogic.UPLOAD_TASK_DATE};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.date", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //postingDate mandatory date
		if(isMandatory(paymentHistoryBean.getPostingDate())){
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Posting Date"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isValidDateFormat(GenericTaskServiceLogic.UPLOAD_TASK_DATE, paymentHistoryBean.getPostingDate())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Posting Date", GenericTaskServiceLogic.UPLOAD_TASK_DATE};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.date", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //paymentAmount mandatory length number
		if (isMandatory(paymentHistoryBean.getPaymentAmount())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Payment Amount"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getPaymentAmount(),12)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Payment Amount", "12"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isNumeric(paymentHistoryBean.getPaymentAmount())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Payment Amount"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //installmentAmount mandatory length number
		if (isMandatory(paymentHistoryBean.getInstallmentAmount())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Installment Amount"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getInstallmentAmount(),12)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Installment Amount", "12"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isNumeric(paymentHistoryBean.getInstallmentAmount())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Installment Amount"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //installmentNumber mandatory length number
		if (isMandatory(paymentHistoryBean.getInstallmentNumber())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Installment Number"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getInstallmentNumber(),3)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Installment Number", "3"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isNumeric(paymentHistoryBean.getInstallmentNumber())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Installment Number"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //transactionType mandatory length
		if (isMandatory(paymentHistoryBean.getTransactionType())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Transaction Type"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getTransactionType(),100)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Transaction Type", "100"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //wopCode mandatory length
		if (isMaxLength(paymentHistoryBean.getWopCode(),20)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "WOP Code", "20"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //transactionId length number mandatory
		if (isMandatory(paymentHistoryBean.getTransactionId())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Transaction Id"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getTransactionId(),12)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Transaction Id", "12"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isNumeric(paymentHistoryBean.getTransactionId())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Transaction Id"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //paymentAllocationName mandatory length
		if (isMandatory(paymentHistoryBean.getPaymentAllocationName())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Payment Allocation Name"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getPaymentAllocationName(),100)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Payment Allocation Name", "100"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //osAmountOd length mandatory number
		if (isMandatory(paymentHistoryBean.getOsAmountOd()) ){
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Os Amount Od"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getOsAmountOd(),12)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Os Amount Od", "12"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isNumeric(paymentHistoryBean.getOsAmountOd())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Os Amount Od"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
		}
	   //receiveAmount mandatory length number
		if (isMandatory(paymentHistoryBean.getReceiveAmount())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Receive Amount"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isMaxLength(paymentHistoryBean.getReceiveAmount(),12)) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Receive Amount", "12"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
		}
		if (isNumeric(paymentHistoryBean.getReceiveAmount())) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { "Receive Amount"};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
		}
		
		// Agreement No and Installment No is already paid
		Object[][] paramPayment = {{Restrictions.eq("agreementNo", 
							paymentHistoryBean.getAgreementNo())},
				   {Restrictions.eq("installmentNumber", 
						   checkEmptyInteger(paymentHistoryBean.getInstallmentNumber()))}};
		TrViewpaymenthistoryH bean = this.getManagerDAO().selectOne(TrViewpaymenthistoryH.class, 
				paramPayment);
		if (bean != null) {
			if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
				errorUpload.append(" | ");
			}
			Object[] obj = { bean.getAgreementNo(), bean.getInstallmentNumber()};
			errorUpload.append(this.messageSource.getMessage(
					"businesslogic.uploadtask.alreadyexist", obj, this.retrieveLocaleAudit(callerId)));
		}
		return errorUpload;
	}
	
	
	public StringBuilder checkingCollectionActivity(CollectionActivityBean collectionActivityBean, AuditContext callerId) {
		StringBuilder errorUpload = new StringBuilder();
			// Agreement No mandatory length 
			if (isMandatory(collectionActivityBean.getAgreementNo())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Agreement No"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (isMaxLength(collectionActivityBean.getAgreementNo(),20)) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Agreement No", "20"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			//branchCode mandatory length isExist
			if(isMandatory(collectionActivityBean.getBranchCode())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Branch Code"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isMaxLength(collectionActivityBean.getBranchCode(),20)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Branch Code", "20"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(!isBranchExist(collectionActivityBean.getBranchCode())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Branch Code"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.branchnotexist", obj, this.retrieveLocaleAudit(callerId)));
			}
		    //activityDate mandatory date
			if(isMandatory(collectionActivityBean.getActivityDate())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Activity Date"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isValidDateFormat(GenericTaskServiceLogic.UPLOAD_TASK_DATE, 
					collectionActivityBean.getActivityDate())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Activity Date", GenericTaskServiceLogic.UPLOAD_TASK_DATE};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.date", obj, this.retrieveLocaleAudit(callerId)));
			}
		    //collectorName mandatory length
			if(isMandatory(collectionActivityBean.getCollectorName())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Collector Name"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isMaxLength(collectionActivityBean.getCollectorName(),80)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Collector Name", "80"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
		    //activity mandatory length
			if(isMandatory(collectionActivityBean.getActivity())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Activity"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isMaxLength(collectionActivityBean.getActivity(),80)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Activity", "80"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
		    //result mandatory length
			if(isMandatory(collectionActivityBean.getResult())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Result"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isMaxLength(collectionActivityBean.getResult(),80)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Result", "80"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
		    //ptpDate date
			if(isValidDateFormat(GenericTaskServiceLogic.UPLOAD_TASK_DATE, collectionActivityBean.getPtpDate())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "PTP Date", GenericTaskServiceLogic.UPLOAD_TASK_DATE};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.date", obj, this.retrieveLocaleAudit(callerId)));
			}
		    //notes length
			if(isMaxLength(collectionActivityBean.getNotes(),2048)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Notes", "2048"};
				errorUpload.append(messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
		    //overDueDays mandatory number length
			if(isMandatory(collectionActivityBean.getOverDueDays())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Over Due Days"};
				errorUpload.append(messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(collectionActivityBean.getOverDueDays())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Over Due Days"};
				errorUpload.append(messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isMaxLength(collectionActivityBean.getOverDueDays(),3)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Over Due Days", "3"};
				errorUpload.append(messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
		    //nextPlanDate date
			if(isValidDateFormat(GenericTaskServiceLogic.UPLOAD_TASK_DATE, collectionActivityBean.getNextPlanDate())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Next Plan Date", GenericTaskServiceLogic.UPLOAD_TASK_DATE};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.date", obj, this.retrieveLocaleAudit(callerId)));
			}
		    //nextPlanAction length
			if(isMaxLength(collectionActivityBean.getNextPlanAction(),100)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Next Plan Action", "100"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			//collector name exist
			if(!isCollectorNameExist(collectionActivityBean.getCollectorName())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Collector Name"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.collnamenotexist", obj, this.retrieveLocaleAudit(callerId)));
			}
			
		return errorUpload;
	}

	

	public StringBuilder checkingInstallmentCard(InstallmentCardBean installmentCardBean, AuditContext callerId) {
		StringBuilder errorUpload = new StringBuilder();
		// Agreement No mandatory length 
			if(isMandatory(installmentCardBean.getAgreementNo())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Agreement No"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isMaxLength(installmentCardBean.getAgreementNo(),20)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Agreement No", "20"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Branch Code mandatory length isExist
			if(isMandatory(installmentCardBean.getBranchCode())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Branch Code"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isMaxLength(installmentCardBean.getBranchCode(),20)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Branch Code", "20"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(!isBranchExist(installmentCardBean.getBranchCode())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Branch Code"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.branchnotexist", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Installment No mandatory length number
			if(isMandatory(installmentCardBean.getInstallmentNo())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Installment No"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isMaxLength(installmentCardBean.getInstallmentNo(),3)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Installment No", "3"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(installmentCardBean.getInstallmentNo())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Installment No"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Due Date	mandatory dateformat
			if(isMandatory(installmentCardBean.getDueDate())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Due Date"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isValidDateFormat(GenericTaskServiceLogic.UPLOAD_TASK_DATE, installmentCardBean.getDueDate())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Due Date", GenericTaskServiceLogic.UPLOAD_TASK_DATE};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.date", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Installment Amount mandatory length number
			if(isMandatory(installmentCardBean.getInstallmentAmount())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Installment Amount"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.global.mandatory", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isMaxLength(installmentCardBean.getInstallmentAmount(),12)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Installment Amount", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(installmentCardBean.getInstallmentAmount())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Installment Amount"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// InstlPaidDate dateformat
			if(isValidDateFormat(GenericTaskServiceLogic.UPLOAD_TASK_DATE, 
					installmentCardBean.getInstlPaidDate())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Instl Paid Date", GenericTaskServiceLogic.UPLOAD_TASK_DATE};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.date", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Instl Paid Amount number length
			if(isMaxLength(installmentCardBean.getInstlPaidAmount(),12)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Instl Paid Amount", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(installmentCardBean.getInstlPaidAmount())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Instl Paid Amount"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}				
		// Lc Instl Amount number length
			if(isMaxLength(installmentCardBean.getLcInstlAmount(),12)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Instl Amount", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(installmentCardBean.getLcInstlAmount())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Instl Amount"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// LcInstlPaid number length
			if(isMaxLength(installmentCardBean.getLcInstlPaid(),12)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Instl Paid", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(installmentCardBean.getLcInstlPaid())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Instl Paid"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Lc Instl Waived number length
			if(isMaxLength(installmentCardBean.getLcInstlWaived(),12)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Instl Waived", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(installmentCardBean.getLcInstlWaived())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Instl Waived"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Principal Amount number length
			if(isMaxLength(installmentCardBean.getPrincipalAmount(),12)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Principal Amount", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(installmentCardBean.getPrincipalAmount())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Principal Amount"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Interest Amount number length
			if(isMaxLength(installmentCardBean.getInterestAmount(),12)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Interest Amount", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(installmentCardBean.getInterestAmount())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Interest Amount"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Os Principal Amount number length
			if(isMaxLength(installmentCardBean.getOsPrincipalAmount(),12)){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Os Principal Amount", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if(isNumeric(installmentCardBean.getOsPrincipalAmount())){
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Os Principal Amount"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Os Interest Amount number length
			if (isMaxLength(installmentCardBean.getOsInterestAmount(),12)) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Os Interest Amount", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (isNumeric(installmentCardBean.getOsInterestAmount())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Os Interest Amount"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Lc Days number length
			if (isMaxLength(installmentCardBean.getLcDays(),3)) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Days", "3"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (isNumeric(installmentCardBean.getLcDays())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Days"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Lc Admin Fee number length
			if (isMaxLength(installmentCardBean.getLcAdminFee(),12)) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Admin Fee", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (isNumeric(installmentCardBean.getLcAdminFee())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Admin Fee"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Lc Admin Fee Paid number length
			if (isMaxLength(installmentCardBean.getLcAdminFeePaid(),12)) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Admin Fee Paid", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (isNumeric(installmentCardBean.getLcAdminFeePaid())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Admin Fee Paid"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		// Lc Admin Fee Waive number length
			if (isMaxLength(installmentCardBean.getLcAdminFeeWaive(),12)) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Admin Fee Waive", "12"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.maxlength", obj, this.retrieveLocaleAudit(callerId)));
			}
			if (isNumeric(installmentCardBean.getLcAdminFeeWaive())) {
				if (!(StringUtils.EMPTY).equals(errorUpload.toString())) {
					errorUpload.append(" | ");
				}
				Object[] obj = { "Lc Admin Fee Waive"};
				errorUpload.append(this.messageSource.getMessage(
						"businesslogic.uploadtask.numeric", obj, this.retrieveLocaleAudit(callerId)));
			}
		return errorUpload;
	}
	
	private boolean isClosingTask(String uuidMsuserDriver, String dateAssign) {
		Object[][] params = { { "uuidMsuserDriver", uuidMsuserDriver },
				{ "dateAssign", dateAssign } };
		Object[] todayAttendance = (Object[]) this.getManagerDAO()
				.selectOneNative("services.common.task.isClosing", params);
		if (todayAttendance != null) {
			return true;
		}
		else {
			return false;
		}
	}

	public String[] getPrevTaskSeq(String groupTaskID) {
		Object[][] params = { { "groupTaskID", groupTaskID } };
		Object[] taskSeq = (Object[]) this.getManagerDAO().selectOneNative(
				"services.common.task.getPrevTaskSeq", params);

		String[] prevTaskSeq = new String[2];
		if (taskSeq != null) {
			prevTaskSeq[0] = (String) taskSeq[3];
			prevTaskSeq[1] = (String) taskSeq[4];
		}

		return prevTaskSeq;
	}

	private byte[] exportErrorTask(List listTask, int taskSuccess,
			List identifierList, List taskBeanList, String subsystemName, String formVersion, AuditContext callerId)
			throws SQLException {

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			HSSFWorkbook workbook;
			if (GlobalVal.SUBSYSTEM_MS.equals(subsystemName)) {
				workbook = this.createXlsTemplateErrorTask(listTask,
						taskSuccess, identifierList, taskBeanList,
						subsystemName, TEMPLATE_HEADER_MS, formVersion, callerId);
			} else if (GlobalVal.SUBSYSTEM_MT.equals(subsystemName)) {
				workbook = this.createXlsTemplateErrorTask(listTask,
						taskSuccess, identifierList, taskBeanList,
						subsystemName, TEMPLATE_HEADER_MT, formVersion, callerId);
			} else {
				workbook = this.createXlsTemplateErrorTask(listTask,
						taskSuccess, identifierList, taskBeanList,
						subsystemName, TEMPLATE_HEADER_MC, formVersion, callerId);
			}
			workbook.write(stream);
		} catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	private byte[] exportErrorTaskMC(Map<String,Object> listTask, int taskSuccess,
			List identifierList, List taskBeanList, String subsystemName, String formVersion, AuditContext callerId)
			throws SQLException {

		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			HSSFWorkbook workbook;
			workbook = this.createXlsTemplateErrorTaskMC(listTask,
					taskSuccess, identifierList, taskBeanList,
					subsystemName, TEMPLATE_HEADER_MC, formVersion, callerId);
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private Workbook createXlsError(String errorMessage) {
		// ByteArrayOutputStream stream = new ByteArrayOutputStream();
		HSSFWorkbook workbook = new HSSFWorkbook();
		HSSFSheet sheet = workbook.createSheet("Task List");

		HSSFCellStyle styleHeader = workbook.createCellStyle();
		HSSFFont fontHeader = workbook.createFont();
		fontHeader.setBoldweight((short) 1000);
		styleHeader.setFont(fontHeader);

		HSSFRow row = sheet.createRow(1);
		HSSFCell cell = row.createCell(0);
		cell.setCellValue(errorMessage);
		cell.setCellStyle(styleHeader);
		sheet.setColumnWidth(0, 30 * 256);

		return workbook;
	}

	private HSSFWorkbook createXlsTemplateErrorTask(List listTask,
			int taskSuccess, List identifierList, List taskBeanList,
			String subsystemName, String[] template, String formVersion, AuditContext callerId) {

		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Task List");
			this.createHeaderErrorUpload(workbook, sheet, taskSuccess,
					listTask.size(), identifierList, template, "Task", formVersion, callerId);
			this.setTextDataFormatErrorUpload(workbook, sheet, identifierList,
					template);
			if (GlobalVal.SUBSYSTEM_MS.equals(subsystemName)) {
				this.setDataErrorUpload(workbook, sheet, listTask,
						identifierList, taskBeanList, TEMPLATE_HEADER_MS,
						subsystemName);
			} 
			else if (GlobalVal.SUBSYSTEM_MT.equals(subsystemName)) {
				this.setDataErrorUpload(workbook, sheet, listTask,
						identifierList, taskBeanList, TEMPLATE_HEADER_MT,
						subsystemName);
			} 
			else {
				this.setDataErrorUpload(workbook, sheet, listTask,
						identifierList, taskBeanList, TEMPLATE_HEADER_MC,
						subsystemName);
			}
		} 
		catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}
	
	private HSSFWorkbook createXlsTemplateErrorTaskMC(Map<String,Object> listTask,
			int taskSuccess, List identifierList, List taskBeanList,
			String subsystemName, String[] template, String formVersion, AuditContext callerId) {

		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			List temp = null;
			int countSuccess = 0;
			temp = (List) listTask.get("listofErrorTask");
			
			HSSFSheet sheet = workbook.createSheet("Task List");
			this.createHeaderErrorUpload(workbook, sheet, taskSuccess,
					temp.size(), identifierList, template, "Task", formVersion, callerId);
			this.setTextDataFormatErrorUpload(workbook, sheet, identifierList,
					template);
			this.setDataErrorUpload(workbook, sheet, (List) listTask.get("listofErrorTask"),
					identifierList, taskBeanList, TEMPLATE_HEADER_MC,
					subsystemName);
			
			//installment schedule
			temp = (List) listTask.get("listofErrorInstallment");
			countSuccess = (int) listTask.get("installmentSuccess");
			HSSFSheet sheet2 = workbook.createSheet("Installment Schedule");
			this.createHeaderErrorUpload(workbook, sheet2, countSuccess,
					temp.size(), null, TEMPLATE_HEADER_MC_INSTALLMENT_SCEDULER, 
					"Installment Schedule", formVersion, callerId);
			this.setTextDataFormatErrorUpload(workbook, sheet2, null,
					TEMPLATE_HEADER_MC_INSTALLMENT_SCEDULER);
			this.setDataErrorMC(workbook, sheet2, listTask,TEMPLATE_HEADER_MC_INSTALLMENT_SCEDULER,1);
			
			//Collection History
			temp = (List) listTask.get("listofErrorCollActivity");
			countSuccess = (int) listTask.get("collActivitySuccess");
			HSSFSheet sheet3 = workbook.createSheet("Collection History");
			this.createHeaderErrorUpload(workbook, sheet3, countSuccess,
					temp.size(), null, TEMPLATE_HEADER_MC_COLLECTION_HISTORY, 
					"Coll Activity", formVersion, callerId);
			this.setTextDataFormatErrorUpload(workbook, sheet3, null,
					TEMPLATE_HEADER_MC_COLLECTION_HISTORY);
			this.setDataErrorMC(workbook, sheet3, listTask,TEMPLATE_HEADER_MC_COLLECTION_HISTORY,2);
			
			//Payment History
			temp = (List) listTask.get("listofErrorPayment");
			countSuccess = (int) listTask.get("paymentSuccess");
			HSSFSheet sheet4 = workbook.createSheet("Payment History");
			this.createHeaderErrorUpload(workbook, sheet4, countSuccess,
					temp.size(), null, TEMPLATE_HEADER_MC_PAYMENT_HISTORY, 
					"Payment History", formVersion, callerId);
			this.setTextDataFormatErrorUpload(workbook, sheet4, null,
					TEMPLATE_HEADER_MC_PAYMENT_HISTORY);
			this.setDataErrorMC(workbook, sheet4, listTask, TEMPLATE_HEADER_MC_PAYMENT_HISTORY,3);
			
		} 
		catch (Exception ex) {
			throw new RuntimeException(ex);
		}
		return workbook;
	}

	private void createHeaderErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet, int taskSuccess, int taskError,
			List identifierList, String[] template, String Name, String formVersion, AuditContext callerId) {

		HSSFCellStyle style = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		style.setFont(font);

		HSSFRow row0 = sheet.createRow(0);
		HSSFRow row1 = sheet.createRow(1);
		HSSFRow row2 = sheet.createRow(2);

		HSSFCell cell0 = row0.createCell(0);
		Object[] obj1 = { Name, taskSuccess };
		cell0.setCellValue(this.messageSource.getMessage(
				"businesslogic.uploadtask.tottaskupload", obj1, this.retrieveLocaleAudit(callerId)));
		cell0.setCellStyle(style);

		HSSFCell cell1 = row1.createCell(0);
		Object[] obj2 = { Name, taskError };
		cell1.setCellValue(this.messageSource.getMessage(
				"businesslogic.uploadtask.tottaskerr", obj2, this.retrieveLocaleAudit(callerId)));
		cell1.setCellStyle(style);

		HSSFCell cell2 = row2.createCell(0);
		Object[] obj3 = { Name };
		cell2.setCellValue(this.messageSource.getMessage(
				"businesslogic.uploadtask.errupload", obj3, this.retrieveLocaleAudit(callerId)));
		cell2.setCellStyle(style);
		
		HSSFRow row3 = sheet.createRow(3);
		HSSFCell cell3 = row3.createCell(0);
		cell3.setCellValue("Form Version : " + formVersion);
		cell3.setCellStyle(style);
		
		HSSFRow row5 = sheet.createRow(5);

		int idx = 0;
		if (identifierList == null) {
			for (int i = 0; i < template.length + 1; i++) {
				HSSFCell cell = row5.createCell(i);
				if (i < template.length) {
					cell.setCellValue(template[i]);
					cell.setCellStyle(style);
					sheet.setColumnWidth(i, 20 * 256);
				} 
				else if (i == template.length) {
					cell.setCellValue("Errors");
					cell.setCellStyle(style);
					sheet.setColumnWidth(i, 80 * 256);
				}
			}
		}
		else {
			for (int i = 0; i < (template.length + identifierList.size()) + 1; i++) {
				HSSFCell cell = row5.createCell(i);
				if (i < template.length) {
					cell.setCellValue(template[i]);
					cell.setCellStyle(style);
					sheet.setColumnWidth(i, 20 * 256);
				} 
				else if (i == template.length + identifierList.size()) {
					cell.setCellValue("Errors");
					cell.setCellStyle(style);
					sheet.setColumnWidth(i, 80 * 256);
				} 
				else {
					Object[][] paramsQuestion = { { Restrictions.eq("refId",
							identifierList.get(idx).toString()) } };
					MsQuestion msQuestion = this.getManagerDAO().selectOne(
							MsQuestion.class, paramsQuestion);
					cell.setCellValue(msQuestion.getQuestionLabel().toString());
					cell.setCellStyle(style);
					sheet.setColumnWidth(i, 20 * 256);
					idx++;
				}
			}
		}
	}

	private void setDataErrorUpload(HSSFWorkbook workbook, HSSFSheet sheet,
			List listTask, List identifierList, List taskBeanList,
			String[] template, String subsystemName) {

		int j = 6;

		for (int k = 0; k < listTask.size(); k++) {
			UploadTaskBean uploadTaskBean = (UploadTaskBean) listTask.get(k);
			TaskBean taskBean = (TaskBean) taskBeanList.get(k);
			HSSFRow row = sheet.createRow(j);

			int totColumn = template.length + identifierList.size();
			int idx = 0;
			for (int i = 0; i < totColumn + 1; i++) {

				HSSFCell cell = row.createCell(i);
				sheet.setColumnWidth(i, 20 * 256);
				// header
				cell = this
						.setCellValue(i, cell, uploadTaskBean, subsystemName);

				AddTaskDetailBean addTaskDetailBean[] = uploadTaskBean
						.getAddTaskDetailBeans();
				// detail
				if (i > template.length - 1 && i < totColumn) {
					cell.setCellValue(addTaskDetailBean[idx].getValue());
					idx++;
				} 
				else if (i == totColumn) {
					cell.setCellValue(taskBean.getErrorText().toString());
					sheet.setColumnWidth(i, 80 * 256);
				}
				// }
			}
			j++;
		}
	}
	
	private void setDataErrorMC(HSSFWorkbook workbook, HSSFSheet sheet,
			Map<String,Object> listTask, String[] template, int m) {
		List temp = null;
		InstallmentCardBean installmentCardBean = null;
		CollectionActivityBean collectionActivityBean = null;
		PaymentHistoryBean paymentHistoryBean = null;
				
		if (m == 1) {
			temp = (List) listTask.get("listofErrorInstallment");
		}
		else if (m == 2) {
			temp = (List) listTask.get("listofErrorCollActivity");
		}
		else if (m == 3){
			temp = (List) listTask.get("listofErrorPayment");
		}
		int j = 6;
		for (int k = 0; k < temp.size(); k++) {
			if (m == 1) {
				installmentCardBean = (InstallmentCardBean) temp.get(k);
			}
			else if (m == 2) {
				collectionActivityBean = (CollectionActivityBean) temp.get(k);
			}
			else if (m == 3) {
				paymentHistoryBean = (PaymentHistoryBean) temp.get(k);
			}
			
			HSSFRow row = sheet.createRow(j);

			int totColumn = template.length;
			for (int i = 0; i <= totColumn; i++) {
				HSSFCell cell = row.createCell(i);
				sheet.setColumnWidth(i, 20 * 256);
				if (i == totColumn) {
					sheet.setColumnWidth(i, 80 * 256);
				}
				// header
				if (m == 1) {
					cell = this.setCellValueInstallmentSchedule(i, cell, installmentCardBean);
				}
				else if (m == 2) {
					cell = this.setCellValueCollActivity(i, cell, collectionActivityBean);
				}
				else if (m == 3) {
					cell = this.setCellValuePaymentHistory(i, cell, paymentHistoryBean);
				}
			}
			j++;
		}
	}

	private HSSFCell setCellValue(Integer i, HSSFCell cell,
			UploadTaskBean uploadTaskBean, String subsystemName) {
		if (GlobalVal.SUBSYSTEM_MS.equals(subsystemName)) {
			switch (i) {
				case 0:
					cell.setCellValue(uploadTaskBean.getApplNo().toString());
					break;
				case 1:
					cell.setCellValue(uploadTaskBean.getMsPriority()
							.getPriorityDesc().toString());
					break;
				case 2:
					cell.setCellValue(uploadTaskBean.getMsBranch().getBranchCode()
							.toString());
					break;
				case 3:
					cell.setCellValue(uploadTaskBean.getAmMsuser().getLoginId()
							.toString());
					break;
				case 4:
					cell.setCellValue(uploadTaskBean.getCustomerName().toString());
					break;
				case 5:
					cell.setCellValue(uploadTaskBean.getCustomerPhone().toString());
					break;
				case 6:
					cell.setCellValue(uploadTaskBean.getCustomerAddress()
							.toString());
					break;
				case 7:
					cell.setCellValue(uploadTaskBean.getNotes().toString());
					break;
				case 8:
					if (null != uploadTaskBean.getLatitude()) {
						cell.setCellValue(uploadTaskBean.getLatitude().toString());
					} 
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 9:
					if (null != uploadTaskBean.getLongitude()) {
						cell.setCellValue(uploadTaskBean.getLongitude().toString());
					} 
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 10:
					if (null != uploadTaskBean.getIsCancelled()) {
						cell.setCellValue(uploadTaskBean.getIsCancelled()
								.toString());
					}
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
			}
		} 
		else if (GlobalVal.SUBSYSTEM_MT.equals(subsystemName)) {
			switch (i) {
				case 0:
					if (uploadTaskBean.getMsPriority() != null) {
						cell.setCellValue(uploadTaskBean.getMsPriority()
								.getPriorityDesc().toString());
					}
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 1:
					if (uploadTaskBean.getMsBranch() != null) {
						cell.setCellValue(uploadTaskBean.getMsBranch()
								.getBranchCode().toString());
					} 
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 2:
					if (uploadTaskBean.getAmMsuser() != null) {
						cell.setCellValue(uploadTaskBean.getAmMsuser().getLoginId()
								.toString());
					}
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 3:
					if (uploadTaskBean.getMsLocation() != null) {
						cell.setCellValue(uploadTaskBean.getMsLocation()
								.getLocationName().toString());
					}
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 4:
					if (uploadTaskBean.getCustomerPhone() != null) {
						cell.setCellValue(uploadTaskBean.getCustomerPhone()
								.toString());
					}
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 5:
					if (uploadTaskBean.getNotes() != null) {
						cell.setCellValue(uploadTaskBean.getNotes().toString());
					}
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 6:
					if (uploadTaskBean.getAssignDate() != null) {
						cell.setCellValue(uploadTaskBean.getAssignDate().toString());
					}
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 7:
					cell.setCellValue(uploadTaskBean.getGroupSeq().toString());
					break;
				case 8:
					cell.setCellValue(uploadTaskBean.getTaskSeq().toString());
					break;
				case 9:
					if (null != uploadTaskBean.getIsCancelled()) {
						cell.setCellValue(uploadTaskBean.getIsCancelled()
								.toString());
					} 
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
			}
		} 
		else {
			switch (i) {
				case 0:
					cell.setCellValue(uploadTaskBean.getAgreementNo().toString());
					break;
				case 1:
					cell.setCellValue(uploadTaskBean.getMsPriority()
							.getPriorityDesc().toString());
					break;
				case 2:
					cell.setCellValue(uploadTaskBean.getAmMsuser().getLoginId()
							.toString());
					break;
				case 3:
					cell.setCellValue(uploadTaskBean.getCustomerName().toString());
					break;
				case 4:
					cell.setCellValue(uploadTaskBean.getCustomerPhone().toString());
					break;
				case 5:
					cell.setCellValue(uploadTaskBean.getCustomerAddress()
							.toString());
					break;
				case 6:
					cell.setCellValue(uploadTaskBean.getNotes().toString());
					break;
				case 7:
					cell.setCellValue(uploadTaskBean.getSurveyAssignmentID()
							.toString());
					break;
				case 8:
					if (null != uploadTaskBean.getLatitude()) {
						cell.setCellValue(uploadTaskBean.getLatitude().toString());
					} 
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 9:
					if (null != uploadTaskBean.getLongitude()) {
						cell.setCellValue(uploadTaskBean.getLongitude().toString());
					} 
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
				case 10:
					if (null != uploadTaskBean.getIsCancelled()) {
						cell.setCellValue(uploadTaskBean.getIsCancelled()
								.toString());
					} 
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
					break;
			}
		}
		return cell;
	}
	
	private HSSFCell setCellValueInstallmentSchedule(Integer i, HSSFCell cell,
			InstallmentCardBean installmentCardBean) {
		switch (i) {
			case 0:
				cell.setCellValue(installmentCardBean.getAgreementNo());
				break;
			case 1:
				cell.setCellValue(installmentCardBean.getBranchCode());
				break;
			case 2:
				cell.setCellValue(installmentCardBean.getInstallmentNo());
				break;
			case 3:
				cell.setCellValue(installmentCardBean.getDueDate());
				break;
			case 4:
				cell.setCellValue(installmentCardBean.getInstallmentAmount());
				break;
			case 5:
				cell.setCellValue(installmentCardBean.getInstlPaidDate());
				break;
			case 6:
				cell.setCellValue(installmentCardBean.getInstlPaidAmount());
				break;
			case 7:
				cell.setCellValue(installmentCardBean.getLcInstlAmount());
				break;
			case 8:
				cell.setCellValue(installmentCardBean.getLcInstlPaid());
				break;
			case 9:
				cell.setCellValue(installmentCardBean.getLcInstlWaived());
				break;
			case 10:
				cell.setCellValue(installmentCardBean.getPrincipalAmount());
				break;
			case 11:
				cell.setCellValue(installmentCardBean.getInterestAmount());
				break;
			case 12:
				cell.setCellValue(installmentCardBean.getOsPrincipalAmount());
				break;
			case 13:
				cell.setCellValue(installmentCardBean.getOsInterestAmount());
				break;
			case 14:
				cell.setCellValue(installmentCardBean.getLcDays());
				break;
			case 15:
				cell.setCellValue(installmentCardBean.getLcAdminFee());
				break;
			case 16:
				cell.setCellValue(installmentCardBean.getLcAdminFeePaid());
				break;
			case 17:
				cell.setCellValue(installmentCardBean.getLcAdminFeeWaive());
				break;
			case 18:
				cell.setCellValue(installmentCardBean.getErrorText().toString());
				break;
		}
		return cell;
	}
	
	private HSSFCell setCellValueCollActivity(Integer i, HSSFCell cell,
			CollectionActivityBean collectionActivityBean) {
		switch (i) {
			case 0:
				cell.setCellValue(collectionActivityBean.getAgreementNo());
				break;
			case 1:
				cell.setCellValue(collectionActivityBean.getBranchCode());
				break;
			case 2:
				cell.setCellValue(collectionActivityBean.getActivityDate());
				break;
			case 3:
				cell.setCellValue(collectionActivityBean.getCollectorName());
				break;
			case 4:
				cell.setCellValue(collectionActivityBean.getActivity());
				break;
			case 5:
				cell.setCellValue(collectionActivityBean.getResult());
				break;
			case 6:
				cell.setCellValue(collectionActivityBean.getPtpDate());
				break;
			case 7:
				cell.setCellValue(collectionActivityBean.getNotes());
				break;
			case 8:
				cell.setCellValue(collectionActivityBean.getOverDueDays());
				break;
			case 9:
				cell.setCellValue(collectionActivityBean.getNextPlanDate());
				break;
			case 10:
				cell.setCellValue(collectionActivityBean.getNextPlanAction());
				break;
			case 11:
				cell.setCellValue(collectionActivityBean.getErrorText().toString());
				break;
		}
		return cell;
	}
	
	private HSSFCell setCellValuePaymentHistory(Integer i, HSSFCell cell,
			PaymentHistoryBean paymentHistoryBean) {
		switch (i) {
			case 0:
				cell.setCellValue(paymentHistoryBean.getAgreementNo());
				break;
			case 1:
				cell.setCellValue(paymentHistoryBean.getBranchCode());
				break;
			case 2:
				cell.setCellValue(paymentHistoryBean.getReceiptNo());
				break;
			case 3:
				cell.setCellValue(paymentHistoryBean.getValueDate());
				break;
			case 4:
				cell.setCellValue(paymentHistoryBean.getPostingDate());
				break;
			case 5:
				cell.setCellValue(paymentHistoryBean.getPaymentAmount());
				break;
			case 6:
				cell.setCellValue(paymentHistoryBean.getInstallmentAmount());
				break;
			case 7:
				cell.setCellValue(paymentHistoryBean.getInstallmentNumber());
				break;
			case 8:
				cell.setCellValue(paymentHistoryBean.getTransactionType());
				break;
			case 9:
				cell.setCellValue(paymentHistoryBean.getWopCode());
				break;
			case 10:
				cell.setCellValue(paymentHistoryBean.getTransactionId());
				break;
			case 11:
				cell.setCellValue(paymentHistoryBean.getPaymentAllocationName());
				break;
			case 12:
				cell.setCellValue(paymentHistoryBean.getOsAmountOd());
				break;
			case 13:
				cell.setCellValue(paymentHistoryBean.getReceiveAmount());
				break;
			case 14:
				cell.setCellValue(paymentHistoryBean.getErrorText().toString());
				break;
		}
		return cell;
	}

	private void setTextDataFormatErrorUpload(HSSFWorkbook workbook,
			HSSFSheet sheet, List identifierList, String[] template) {

		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("@"));

		if (identifierList == null) {
			for (int i = 0; i < template.length + 1; i++) {
				sheet.setDefaultColumnStyle(i, style);
			}
		}
		else {
			for (int i = 0; i < template.length + identifierList.size() + 1; i++) {
				sheet.setDefaultColumnStyle(i, style);
			}
		}

	}
	
	private boolean isNumeric(String str) {
		return !StringUtils.isNumeric(str);
	}
	
	private boolean isMaxLength(String str, int maxLength) {
		if (StringUtils.isBlank(str)) {
			return false;
		}
		return str.length() > maxLength;
	}
	
	private boolean isMandatory(String str){
		return StringUtils.isBlank(str);
	}
	
	private boolean isValidDateFormat(String format, String value) {
    	if (StringUtils.isBlank(value)) {
			return false;
		}
        Date date = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            date = sdf.parse(value);
            if (!value.equals(sdf.format(date))) {
                date = null;
            }
        } 
        catch (ParseException ex) {
            ex.printStackTrace();
        }
        return date == null;
    }
    
    private Date toDateFormat(String format, String value) {
    	if (StringUtils.isBlank(value)) {
			return null;
		}
    	else if ("null".equals(value)) {
			return null;
    	}
    	
        Date date = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            date = sdf.parse(value);
        } 
        catch (ParseException ex) {
            ex.printStackTrace();
        }
        return date;
    }
    
	private BigDecimal checkEmptyBigdecimal(String in) {
		BigDecimal bd;
		if (StringUtils.isBlank(in)) {
			return null;
		}
		else if ("null".equals(in)) {
			return null;
		}
		else {
			bd = new BigDecimal(in);
		}
		return bd;
	}
	
	private Integer checkEmptyInteger(String in) {
		Integer i = 0;
		if (StringUtils.isBlank(in)) {
			return null;
		}
		else if ("null".equals(in)) {
			return null;
		}
		else {
			i = Integer.valueOf(in);
		}
		return i;
	}
	
	private Short checkEmptyShort(String in) {
		Short i = 0;
		if (StringUtils.isBlank(in)) {
			return null;
		}
		else if ("null".equals(in)) {
			return null;
		}
		else {
			i = Short.valueOf(in);
		}
		return i;
	}
	private Long checkEmptyLong(String in) {
		Long i = Long.parseLong("0");
		if (StringUtils.isBlank(in)) {
			return null;
		}
		else if ("null".equals(in)) {
			return null;
		}
		else {
			i = Long.valueOf(in);
		}
		return i;
	}
	
	private boolean isBranchExist (String branchCode) {
		boolean result = true;
		Object [][] param = {{Restrictions.eq("branchCode", branchCode)}};
		MsBranch exist = this.getManagerDAO().selectOne(MsBranch.class, param);
		if (exist == null) {
			result = false;
		}
		return result;
	}
	
	private boolean isCollectorNameExist (String collName) {
		boolean result = true;
		Object [][] param = {{Restrictions.eq("loginId", collName)}};
		AmMsuser exist = this.getManagerDAO().selectOne(AmMsuser.class, param);
		if (exist == null) {
			result = false;
		}
		return result;
	}

}
