package com.adins.mss.businesslogic.impl.common;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.TaskVerificationLogic;
import com.adins.mss.businesslogic.api.common.UserLogic;
import com.adins.mss.businesslogic.api.survey.UnassignTaskLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.services.model.common.UserBean;

public class GenericUserLogic extends BaseLogic implements UserLogic, MessageSourceAware{
	private static final Logger LOG = LoggerFactory.getLogger(GenericUserLogic.class);
    
    @Autowired
    private MessageSource messageSource;
	@Autowired
	private TaskVerificationLogic taskVerificationLogic;	
	@Autowired
	private UnassignTaskLogic unassignTaskLogic;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	public void setUnassignTaskLogic(UnassignTaskLogic unassignTaskLogic) {
		this.unassignTaskLogic = unassignTaskLogic;
	}

	public void setTaskVerificationLogic(TaskVerificationLogic taskVerificationLogic) {
		this.taskVerificationLogic = taskVerificationLogic;
	}
	
	@SuppressWarnings("unchecked")
	@Transactional(readOnly=true,isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<UserBean> getListUser(long uuidTaskH, String mode, AuditContext callerId) {
		List<UserBean> result = new ArrayList<UserBean>();
		
		AmMsuser loginBean = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
		
		//suggested user
		Map<String, Object> suggestedUsrMap = taskVerificationLogic.getSuggestedUser(
			uuidTaskH, mode, 
			callerId
		);
		
		AmMsuser su = (AmMsuser) suggestedUsrMap.get("suggestedUser");
		Integer[][] assignmentSu = (Integer[][]) suggestedUsrMap.get("assignment");
		
		if (null != su) {
			UserBean suggestedUser = new UserBean();
			suggestedUser.setUuidMsUser(String.valueOf(su.getUuidMsUser()));
			suggestedUser.setFullname(su.getFullName() + " (" + assignmentSu[0][2] + ")");
			suggestedUser.setIsSuggested("1");
			
			result.add(suggestedUser);
		}
		
		//common user
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if(mode != null && !"".equals(mode)){
			resultMap = unassignTaskLogic.listUser(mode,
					loginBean,
					callerId
			);
		}
		else{
			resultMap = unassignTaskLogic.listUser(null,
				loginBean,
				callerId
			);
		}
		
		Map<String, Object> mapResult = (Map<String, Object>) resultMap.get("result");
		List<AmMsuser> listUser = (List<AmMsuser>) mapResult.get(GlobalKey.MAP_RESULT_LIST);
		Integer[][] assignment = (Integer[][]) resultMap.get("assignment");
		
		for (int i = 0; i < listUser.size(); i++) {
			AmMsuser temp = listUser.get(i);
			UserBean bean = new UserBean();
			
			bean.setUuidMsUser(String.valueOf(temp.getUuidMsUser()));
			bean.setFullname(temp.getFullName() + " (" + assignment[i][2] + ")");
			bean.setIsSuggested("0");
			
			if (null != su) {
				if (!String.valueOf(su.getUuidMsUser()).equals(String.valueOf(temp.getUuidMsUser()))) {
					result.add(bean);
				}
			} else {
				result.add(bean);
			}
		}
		
		return result;
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public AmMsuser fetchUserByUuid(long uuidUser, AuditContext callerId) {
		AmMsuser result = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", uuidUser}});		
		
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
    @Override
    public void substractCashOnHand(BigDecimal totSetoran, String loginId, AuditContext callerId) {
          Object [][] paramUser = {{"loginId", loginId }, {"totSetoran",totSetoran}};
          this.getManagerDAO().updateNativeString("UPDATE am_msuser SET cash_on_hand = "
        		  + "(ISNULL(cash_on_hand, 0) - CAST(:totSetoran as DECIMAL)) "
                      + "WHERE uuid_ms_user = :loginId", paramUser);
    }
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateCashOnHand(long uuidTaskH, String totalBayar, AuditContext callerId) {
		String uuidUser = callerId.getCallerId();
		if (StringUtils.isNotBlank(totalBayar)){
			AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(uuidUser));            
			
			if (null == user.getCashOnHand()){
				user.setCashOnHand(new BigDecimal(0));
			}
				
			LOG.info("Initial cash on hand for user {} is {}", user.getLoginId(), user.getCashOnHand());	 
			Object params[][] = { {"cashOnHand", totalBayar}, {"uuidUser", uuidUser} };						
			this.getManagerDAO().updateNativeString("UPDATE am_msuser SET cash_on_hand = CONVERT(BIGINT, :cashOnHand) + ISNULL(cash_on_hand, 0) WHERE uuid_ms_user = :uuidUser", params);
			
			LOG.info("Final cash on hand for user "+user.getLoginId()+" is "+user.getCashOnHand());
		}
	}
	
	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public BigDecimal getCashOnHand(AuditContext callerId) {
		AmMsuser  user  = new AmMsuser();
		Object [][] paramUser = {{Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId()))}};
		user = this.getManagerDAO().selectOne(AmMsuser.class, paramUser);
   
		LOG.info("Cash on hand for user {} is {}", user.getLoginId(), user.getCashOnHand());
		return user.getCashOnHand();
	}
}
