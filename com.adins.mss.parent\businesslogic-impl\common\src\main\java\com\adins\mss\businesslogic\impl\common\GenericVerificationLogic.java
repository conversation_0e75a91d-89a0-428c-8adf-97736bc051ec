package com.adins.mss.businesslogic.impl.common;

import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.sql.Blob;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.VerificationLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.custom.UploadImageBean;
import com.adins.mss.model.custom.ViewImageRequestBean;
import com.adins.mss.model.custom.ViewImageResponseBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.LocationBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.VerificationDBean;
import com.adins.mss.services.model.common.VerificationLBean;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;

@SuppressWarnings({"unchecked","rawtypes"})
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericVerificationLogic extends BaseLogic implements VerificationLogic {	
	private static final Logger LOG = LoggerFactory.getLogger(GenericVerificationLogic.class);	
	private Gson gson = new Gson();
	
	private ImageStorageLogic imageStorageLogic;
	private CommonLogic commonLogic;
	private IntFormLogic intFormLogic;
	
	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }	

    public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
    
    public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	@Override
	public List listTask(long uuidTaskH, AuditContext callerId){
		List listTask = this.listTaskActive(uuidTaskH, callerId);
		return listTask;
	}
	
	private List listTaskActive(long uuidTaskH, AuditContext callerId){
		List listTask = null;
		
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH h join fetch h.msStatustask join fetch h.msForm where h.uuidTaskH = :uuidTaskH", new Object[][]{ {"uuidTaskH", uuidTaskH} });
		ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);
		
		boolean loadFromJson = PropertiesHelper.isTaskDJson();
		
		if (loadFromJson) {
			List<MsFormquestionset> formQuestionset = this.commonLogic.retrieveMsFormquestionset(
					trTaskH.getMsForm().getUuidForm(), trTaskH.getFormVersion(), callerId);
			listTask = this.loadFromJson(trTaskH, formQuestionset, saveImgLoc, callerId);
		}
		else {
			listTask = this.loadFromRow(trTaskH, saveImgLoc, callerId);
		}
		
		this.updateDownloadDate(uuidTaskH, trTaskH.getStartDtm(), trTaskH.getDownloadDate(), callerId);
		
		return listTask;
	}
	
	private List loadFromRow(TrTaskH trTaskH, ImageStorageLocation saveImgLoc, AuditContext callerId) {
		List listTaskD = new ArrayList<>();
		
		Object[][] param = { {"uuidTaskH", trTaskH.getUuidTaskH()} };
		List taskDs = null;
		if (GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equals(trTaskH.getMsStatustask().getStatusCode())) {
			taskDs = this.getManagerDAO().selectForList(VerificationDBean.class, "services.common.form.verificationDVerify", param, null);
		} 
		else if (GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(trTaskH.getMsStatustask().getStatusCode())) {
			taskDs = this.getManagerDAO().selectForList(VerificationDBean.class, "services.common.form.verificationDApproval", param, null);
		} 
		else {
			taskDs = this.getManagerDAO().selectForList(VerificationDBean.class, "services.common.form.verificationDSurvey", param, null);
		}			
		listTaskD.addAll(taskDs);
		
		List taskL = this.getManagerDAO().selectForList(VerificationLBean.class, "services.common.form.VerificationLob", param, null);
		
		Iterator<VerificationLBean> itl = taskL.iterator();
		while (itl.hasNext()) {
			VerificationLBean vlb = itl.next();
			vlb.setIsResurvey(isResurvey(vlb.getUuidTaskD(), callerId));
			
			if (ImageStorageLocation.DATABASE == saveImgLoc) {
				if(null != vlb.getLob()) {
					vlb.setHasDefaultImage("1");
				}
				else {
					vlb.setHasDefaultImage("0");
				}
				
				/*
				try {
					Blob blob = vlb.getLob();
					if (blob != null) {
						byte[] img = blob.getBytes(1, (int) blob.length());
	                    vlb.setImage(BaseEncoding.base64().encode(img));
					}
					else {
						vlb.setHasDefaultImage("0");
					}
				} 
				catch (SQLException e) {
					LOG.error(e.getMessage(), e);
                    throw new DatabaseException(DatabaseException.Reason.OTHER_DATA_ACCESS_EXCEPTION, e);
				}
				*/
			}
			else if (ImageStorageLocation.DMS == saveImgLoc) {
				if(null != vlb.getImgPath() || StringUtils.isNotBlank(vlb.getImgPath())) {
					/*
					Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
			        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
			        Date cutoffDate = null;
			        Object [][] params = {{"uuidTaskH", trTaskH.getTaskId()}};
					Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
							"SELECT	GROUP_TASK_ID " + 
							"FROM	MS_GROUPTASK with(nolock) " + 
							"WHERE	UUID_TASK_H = :uuidTaskH ", params);
					String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(trTaskH.getMsForm().getUuidForm()));
					
					try {
						cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
					} catch (ParseException e1) {
						e1.printStackTrace();
					}
					
					TrTaskdetaillob mapResult = this.getManagerDAO().selectOne(TrTaskdetaillob.class, Long.valueOf(vlb.getImgPath()));
					
					TrTaskH trTaskHParent = null;
					
					if(null != trTaskH.getTrTaskH()) {
						trTaskHParent = trTaskH.getTrTaskH();
					} else {
						trTaskHParent = trTaskH;
					}
					
			        Date taskDate = null;
			        if(null != trTaskHParent.getSubmitDate()) {
			        	taskDate = trTaskHParent.getSubmitDate();
			        } else {
			        	taskDate = trTaskHParent.getDtmCrt();
			        }
					
					ViewImageRequestBean request = new ViewImageRequestBean();
					UploadImageBean imageBean = new UploadImageBean();
					
					if(taskDate.before(cutoffDate)) {
						imageBean.setTaskId(mapResult.getTrTaskH().getTaskId());
						imageBean.setId(mapResult.getTrTaskH().getTaskId());
					} else if (taskDate.after(cutoffDate)) {
						imageBean.setTaskId(groupTaskIdForm);
						imageBean.setId(groupTaskIdForm);
					}
					
					String dmsType = "survey";
					
					imageBean.setJenisDoc(mapResult.getMsQuestion().getQuestionLabel());
					imageBean.setUsername(mapResult.getTrTaskH().getAmMsuser().getUniqueId());
					imageBean.setType(dmsType);
					imageBean.setRefId(mapResult.getMsQuestion().getRefId());
					request.setUploadImageBean(imageBean);
					
					ViewImageResponseBean response = this.intFormLogic.viewImageDMS(callerId, request);
					if (null != response.getByteImage()) {
						vlb.setImage(BaseEncoding.base64().encode(response.getByteImage()));
						//vlb.setImage("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");
					}
					*/
					
					vlb.setHasDefaultImage("1");
				} else {
					vlb.setHasDefaultImage("0");
				}
				
			}
			else {
				if(null != vlb.getImgPath() || StringUtils.isNotBlank(vlb.getImgPath())) {
					vlb.setHasDefaultImage("1");
				} else {
					vlb.setHasDefaultImage("0");
				}
				/*
				if (StringUtils.isNotBlank(vlb.getImgPath())) {
					String path = vlb.getImgPath();
					File file = new File(path);
					byte imageData[] = imageStorageLogic.retrieveImageFileSystemByFile(file);
					vlb.setImage(BaseEncoding.base64().encode(imageData));
				}
				*/
			}
			listTaskD.add(vlb);
		}
		
		return listTaskD;
	}
	
	private List loadFromJson(TrTaskH trTaskH, List<MsFormquestionset> formQuestionset, ImageStorageLocation saveImgLoc, AuditContext callerId) {
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		if (docDb == null || StringUtils.isBlank(docDb.getDocument())){
			return Collections.emptyList();
		}
		
		TaskDocumentBean document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
		List<AnswerBean> answers = document.getAnswers();
		if (answers == null || answers.isEmpty()){
			return Collections.emptyList();
		}
		
		VerificationType type = null; 
		switch (trTaskH.getMsStatustask().getStatusCode()){
			case GlobalVal.SURVEY_STATUS_TASK_VERIFICATION:
				type = VerificationType.LOAD_SUBMITTED; break;
			case GlobalVal.SURVEY_STATUS_TASK_APPROVAL:
				type = VerificationType.LOAD_VERIFIED; break;
			default:
				type = VerificationType.LOAD_DEFAULT;
		}
		
		List listTaskD = new ArrayList<>();
		for (Iterator<AnswerBean> iterator = answers.iterator(); iterator.hasNext();) {
			AnswerBean answer = iterator.next();			
			VerificationDBean taskD = new VerificationDBean();
			
			MsFormquestionset msFormquestion = this.findMsFormquestion(formQuestionset, answer.getQuestion().getUuidQuestion());
			String uuidQuestionGroup = String.valueOf(msFormquestion.getMsQuestiongroup().getUuidQuestionGroup());
			String uuidQuestion = String.valueOf(answer.getQuestion().getUuidQuestion());
			
			taskD.setUuidTaskD(StringUtils.join(String.valueOf(trTaskH.getUuidTaskH()), "-", uuidQuestionGroup, "-", uuidQuestion));
			taskD.setUuidQuestionGroup(uuidQuestionGroup);
			taskD.setUuidQuestion(uuidQuestion);
			taskD.setTagName(answer.getQuestion().getTagOrderName());
			taskD.setUuidTaskH(String.valueOf(trTaskH.getUuidTaskH()));
			taskD.setQuestionText(answer.getQuestion().getLabel());
			taskD.setTimestackTask(null); //timestamp_task digunakan di tele/verif saat load, tapi tidak pernah save
			taskD.setRegex(null);
			taskD.setIsReadonly(null);
			taskD.setLovId("");
			if (answer.getLocation() != null) {
				LocationBean location = answer.getLocation();
				taskD.setLatitude((location.getLat() == null) ? null : location.getLat().toString());
				taskD.setLongitude((location.getLng() == null) ? null : location.getLng().toString());
				taskD.setMcc(String.valueOf(location.getMcc()));
				taskD.setMnc(String.valueOf(location.getMnc()));
				taskD.setLac(String.valueOf(location.getLac()));
				taskD.setCid(String.valueOf(location.getCid()));
				taskD.setAccuracy(location.getAccuracy());				
			}
			if (answer.getLobAnswer() != null && answer.getLobAnswer().isHasImage()) {				
				TrTaskdetaillob lobDb = this.getManagerDAO().selectOne(TrTaskdetaillob.class, answer.getLobAnswer().getId());
				if (ImageStorageLocation.DATABASE == saveImgLoc) {
					taskD.setImage(BaseEncoding.base64().encode(lobDb.getLobFile()));
				}
				else {
					if (StringUtils.isNotBlank(lobDb.getImagePath())) {
						String path = lobDb.getImagePath();
						File file = new File(path);
						byte imageData[] = imageStorageLogic.retrieveImageFileSystemByFile(file);
						taskD.setImage(BaseEncoding.base64().encode(imageData));
					}
				}
			}
			
			Iterator<OptionBean> iterOptions = null;
			if (type == VerificationType.LOAD_DEFAULT) {
				taskD.setTextAnswer(StringUtils.stripToEmpty(answer.getIntTxtAnswer()));
				if (answer.getIntOptAnswers() != null && !answer.getIntOptAnswers().isEmpty()) {
					iterOptions = answer.getIntOptAnswers().iterator();
				}
			}
			if (type == VerificationType.LOAD_SUBMITTED) {
				taskD.setTextAnswer(StringUtils.stripToEmpty(answer.getTxtAnswer()));
				if (answer.getOptAnswers() != null && !answer.getOptAnswers().isEmpty()) {
					iterOptions = answer.getOptAnswers().iterator();
				}
			}
			if (type == VerificationType.LOAD_VERIFIED) {
				taskD.setTextAnswer(StringUtils.stripToEmpty(answer.getFinTxtAnswer()));
				if (answer.getFinOptAnswers() != null && !answer.getFinOptAnswers().isEmpty()) {
					iterOptions = answer.getFinOptAnswers().iterator();
				}
			}
			
			listTaskD.add(taskD);
			
			if (iterOptions == null){
				continue;
			}
			
			int ctr = 0;
			while (iterOptions.hasNext()) {
				OptionBean option = iterOptions.next();
				if (ctr > 0) {
					try {
						VerificationDBean clone = (VerificationDBean) BeanUtils.cloneBean(taskD);
						clone.setLovId(String.valueOf(option.getUuid()));
						clone.setCode(option.getCode());
						clone.setTextAnswer(StringUtils.stripToEmpty(option.getFreeText()));
						listTaskD.add(clone);
					} 
					catch (IllegalAccessException | InstantiationException | InvocationTargetException | NoSuchMethodException e) {
						LOG.error("Error on cloning taskD", e);
					}
				}
				else {
					taskD.setLovId(String.valueOf(option.getUuid()));
					taskD.setCode(option.getCode());
					taskD.setTextAnswer(StringUtils.stripToEmpty(option.getFreeText()));
				}
				ctr++;
			}
		}
		
		return listTaskD;
	}
	
	private MsFormquestionset findMsFormquestion(List<MsFormquestionset> msFormquestionset, long uuidQuestion) {
		if (msFormquestionset == null || msFormquestionset.isEmpty()){
			return null;
		}
		
		for (Iterator<MsFormquestionset> iterator = msFormquestionset.iterator(); iterator.hasNext();) {
			MsFormquestionset msFormquestion = iterator.next();
			if (msFormquestion.getMsQuestion().getUuidQuestion() == uuidQuestion) {
				return msFormquestion;
			}
		}
		
		return null;
	}
	
	private void updateDownloadDate(long uuidTaskH, Date startDtm, Date downloadDate, AuditContext callerId) {
		if (startDtm == null && downloadDate == null) {
			MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_DOWNLOADED, callerId);
			Object[][] queryParams = { {"downloadDate", new Date()}, {"uuidTaskH", uuidTaskH}, {"statusMobile", msm.getStatusMobileSeqNo()} };
			this.getManagerDAO().updateNativeString(
					"UPDATE tr_task_h SET download_date = :downloadDate, STATUS_MOBILE_SEQ_NO = :statusMobile WHERE uuid_task_h = :uuidTaskH",
					queryParams);
		}
	}
	
	private enum VerificationType {
		LOAD_DEFAULT,
		LOAD_SUBMITTED,
		LOAD_VERIFIED
	}

	@Override
	public List getVerificationImage(long uuidTaskH, long uuidQuestion, AuditContext callerId) {
		
		List result = null;
		
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH h join fetch h.msStatustask join fetch h.msForm where h.uuidTaskH = :uuidTaskH", new Object[][]{ {"uuidTaskH", uuidTaskH} });
		
		ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);
		
		result = getImageFromRow(trTaskH, uuidQuestion, saveImgLoc, callerId);
		
		return result;
	}
	private List getImageFromRow(TrTaskH trTaskH, long uuidQuestion, ImageStorageLocation saveImgLoc, AuditContext callerId) {
		
		List listTaskD = new ArrayList<>();
		
		Object[][] param = {{"uuidTaskH", trTaskH.getUuidTaskH()}, {"uuidQuestion", uuidQuestion }};
		
		List taskL = this.getManagerDAO().selectForList(VerificationLBean.class, "services.common.form.getImageVerification", param, null);
		
		Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
        Date cutoffDate = null;
        Object [][] params = {{"uuidTaskH", trTaskH.getTaskId()}};
		Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
				"SELECT	GROUP_TASK_ID " + 
				"FROM	MS_GROUPTASK with(nolock) " + 
				"WHERE	UUID_TASK_H = :uuidTaskH ", params);
		String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(trTaskH.getMsForm().getUuidForm()));

		try {
			cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
		} catch (ParseException e1) {
			e1.printStackTrace();
		}
		
		Iterator<VerificationLBean> itl = taskL.iterator();
		while (itl.hasNext()) {
			VerificationLBean vlb = itl.next();
			
			if (ImageStorageLocation.DATABASE == saveImgLoc) {
				try {
					Blob blob = vlb.getLob();
					if (blob != null) {
						byte[] img = blob.getBytes(1, (int) blob.length());
	                    vlb.setImage(BaseEncoding.base64().encode(img));
					}
					//Debug
					//vlb.setImage("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");
				} 
				catch (SQLException e) {
					LOG.error(e.getMessage(), e);
                    throw new DatabaseException(DatabaseException.Reason.OTHER_DATA_ACCESS_EXCEPTION, e);
				}
			}
			else if (ImageStorageLocation.DMS == saveImgLoc) {
				if (StringUtils.isNotBlank(vlb.getImgPath())) {
					TrTaskdetaillob mapResult = this.getManagerDAO().selectOne(TrTaskdetaillob.class, Long.valueOf(vlb.getImgPath()));
					
					TrTaskH trTaskHParent = null;
					
					if(null != trTaskH.getTrTaskH()) {
						trTaskHParent = trTaskH.getTrTaskH();
					} else {
						trTaskHParent = trTaskH;
					}
					
			        Date taskDate = null;
			        if(null != trTaskHParent.getSubmitDate()) {
			        	taskDate = trTaskHParent.getSubmitDate();
			        } else {
			        	taskDate = trTaskHParent.getDtmCrt();
			        }
					
					ViewImageRequestBean request = new ViewImageRequestBean();
					UploadImageBean imageBean = new UploadImageBean();
					
					if(taskDate.before(cutoffDate)) {
						imageBean.setTaskId(mapResult.getTrTaskH().getTaskId());
						imageBean.setId(mapResult.getTrTaskH().getTaskId());
					} else if (taskDate.after(cutoffDate)) {
						imageBean.setTaskId(groupTaskIdForm);
						imageBean.setId(groupTaskIdForm);
					}
					
					String dmsType = "survey";
					
					imageBean.setJenisDoc(mapResult.getMsQuestion().getQuestionLabel());
					imageBean.setUsername(mapResult.getTrTaskH().getAmMsuser().getUniqueId());
					imageBean.setType(dmsType);
					imageBean.setRefId(mapResult.getMsQuestion().getRefId());
					request.setUploadImageBean(imageBean);
					
					ViewImageResponseBean response = this.intFormLogic.viewImageDMS(callerId, request);
					if (null != response.getByteImage()) {
						vlb.setImage(BaseEncoding.base64().encode(response.getByteImage()));
					}
					//Debug
					//vlb.setImage("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");
				}
			}
			else {
				if (StringUtils.isNotBlank(vlb.getImgPath())) {
					String path = vlb.getImgPath();
					File file = new File(path);
					byte imageData[] = imageStorageLogic.retrieveImageFileSystemByFile(file);
					vlb.setImage(BaseEncoding.base64().encode(imageData));
				}
			}
			listTaskD.add(vlb);
		}
		return listTaskD;
	}
	
	private String isResurvey(String uuidTaskD, AuditContext auditContext) {
		
		Object[][] paramsD = {{ Restrictions.eq("uuidTaskDetailLob", Long.parseLong(uuidTaskD)) }};
		
		TrTaskdetaillob lob = this.getManagerDAO().selectOne(TrTaskdetaillob.class, paramsD);
		
		if(null != lob) {
			if(null != lob.getIsResurvey()) {
				return lob.getIsResurvey();
			} else {
				String versiBuildNumber = auditContext.getParameters().get("applicationVersion").toString().split("-")[1];
				AmGeneralsetting apkCaeVersion = commonLogic.retrieveGs("FIN_APK_CAE_VERSION", auditContext);
				String gsValue = StringUtils.EMPTY;
				if (apkCaeVersion != null) {
					gsValue = apkCaeVersion.getGsValue();
				}
				if(StringUtils.isNotBlank(gsValue) && Integer.valueOf(gsValue) <= Integer.valueOf(versiBuildNumber)) {
					return null;
				} else {
					return "0";
				}
				
			}
		}
		return null;
	}
}
