package com.adins.mss.businesslogic.impl.common;

import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bouncycastle.util.encoders.Base64;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.DistanceUtils;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ViewLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskupdate;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.model.custom.UploadImageBean;
import com.adins.mss.model.custom.ViewImageRequestBean;
import com.adins.mss.model.custom.ViewImageResponseBean;
import com.adins.mss.util.CipherTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.base.Stopwatch;

@SuppressWarnings({"rawtypes", "unchecked"})
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericViewLogic  extends BaseLogic implements ViewLogic{
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	private static final Logger LOG = LoggerFactory.getLogger(GenericViewLogic.class);
	private GeolocationLogic geocoder;
	private CommonLogic commonLogic;
	private IntFormLogic intFormLogic;

	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	private String paramUuidTaskH = "uuidTaskH";

	@Override
	public Map<String, Object> getViewResullt(String taskId, String subsystemCode, String isIPPublic, int isTaskLink,
			AuditContext callerId) {
		Map<String, Object> mapResult = new HashMap<String, Object>();
		Stopwatch swttl = Stopwatch.createStarted();
		Stopwatch sw = Stopwatch.createStarted();
//		taskId = this.checkTaskIdNc(taskId, subsystemCode); //2018-01-31 TaskID sudah tidak ada prefix SVY/COL
		String[][] params ={{"taskId", taskId}};
		Object[] result = getDetailTaskSurvey(params, callerId);
		sw.stop();
		LOG.info("GET DETAIL FORM HEADER : "+sw.elapsed(TimeUnit.MILLISECONDS));
	
		sw.reset();
		sw.start();
		Object[][] paramsDetail = { {"uuidTask", result[0]}, {"uuidForm", result[18]} };
		List dataList = null;
		if (PropertiesHelper.isTaskDJson()) {
			boolean processAsset = (GlobalVal.SUBSYSTEM_MS.equals(subsystemCode)) ? true : false;
			dataList = commonLogic.listAnswersFromJson(NumberUtils.toLong(result[0].toString()), false, false, processAsset, callerId);
		}
		else { 
			dataList = getInquiryTaskSurveyDetailAnswerHistory(paramsDetail, null, null, callerId);
		}
		sw.stop();
		LOG.info("GET DETAIL LIST : " + sw.elapsed(TimeUnit.MILLISECONDS));
		
		sw.reset();
		sw.start();
		String[][] paramsHistory ={{"paramUuidTaskH", result[0].toString()}};
		List dataList2 = getInquiryTaskSurveyDetailTaskHistory(paramsHistory, null, callerId);
		sw.stop();
		LOG.info("GET DETAIL HISTORY : " + sw.elapsed(TimeUnit.MILLISECONDS));
		
		List dataList3 = null;
		List dataList4 = null;
		
		if(isTaskLink == 1) {
			dataList3 = getTaskLink(result[0].toString(), isIPPublic, callerId);
			dataList4 = getTaskUpdate(result[0].toString(), callerId);
		}
			
		mapResult.put("detailTask", result);
		mapResult.put("dataList", dataList);
		mapResult.put("dataList2", dataList2);
		mapResult.put("dataList3", dataList3 != null ? dataList3:new ArrayList<>());
		mapResult.put("dataList4", dataList4 != null ? dataList4:new ArrayList<>());
		
		swttl.stop();
		LOG.info("TOTAL LOGIC : " + swttl.elapsed(TimeUnit.MILLISECONDS));
		return mapResult;
	}
	
	@Override
	public List getInquiryDetail(String uuid, AuditContext callerId, String codeProcess) {
		TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, Long.valueOf(uuid));
		Object[][] paramsDetail = { {"uuidTask", taskH.getUuidTaskH()}, 
				{"uuidForm", taskH.getMsForm().getUuidForm()} };
		List dataList = getInquiryTaskSurveyDetailAnswerHistory(paramsDetail,
				null, codeProcess, callerId); 
		return dataList;
	}
	
	public List getInquiryTaskSurveyDetailAnswerHistory(Object[][] params, String[][] order, 
			String codeProcess, AuditContext callerId) {
		
		List <TrTaskBean> result = new ArrayList<TrTaskBean>();
		List list = this.getManagerDAO().selectForListOfMap(
				"view.viewresult.answerHistory", params, order);
		String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
		for (int i = 0; i < list.size(); i++) {
			TrTaskBean taskCollection = new TrTaskBean();
			Map map = (HashMap) list.get(i);
			taskCollection.setFlagSource("1");
			if (null != map.get("latitude")) {
			    taskCollection.setLat(map.get("latitude").toString());
			}
			if (null != map.get("longitude")) {
			    taskCollection.setLng(map.get("longitude").toString()); 
			}
			if (null != map.get("accuracy")) {
			    taskCollection.setAccuracy(Integer.parseInt(map.get("accuracy").toString()));
			}
			taskCollection.setLob((String) map.get("uuidTaskDetailLob"));
			
			taskCollection.setQuestionText(map.get("questionText").toString());
			if (GlobalVal.CODE_PROCESS_VERIFIED.equals(codeProcess)) {
				taskCollection.setTextAnswer(map.get("finTextAnswer").toString());
				taskCollection.setOptionText(map.get("finOptionText").toString());
			} 
			else {
				taskCollection.setTextAnswer(map.get("textAnswer").toString());
				taskCollection.setOptionText(map.get("optionText").toString());						
			}
			taskCollection.setHasImage(map.get("hasImage").toString());
			taskCollection.setIntTextAnswer(map.get("intTextAnswer").toString());
			taskCollection.setIntOptionText(map.get("intOptionText").toString());
			taskCollection.setIsAsset(map.get("asset").toString());
			taskCollection.setLatBranch(map.get("latBranch") == null ? null : map.get("latBranch").toString());
			taskCollection.setLngBranch(map.get("lngBranch") == null ? null : map.get("lngBranch").toString());
			taskCollection.setAssetTagName(map.get("assetTagName") == null ? null:map.get("assetTagName").toString());
			if ("1".equals(taskCollection.getIsAsset())) {
				double lat = NumberUtils.toDouble(taskCollection.getLat(), 0);
				double lng = NumberUtils.toDouble(taskCollection.getLng(), 0);
				if (null == taskCollection.getLat() && null == taskCollection.getLng()
						&& null != map.get("cellId")
						&& null != map.get("lac")
						&& null != map.get("mnc")
						&& null != map.get("mcc")) {
					List<LocationBean> listLocations = new ArrayList<LocationBean>();
					LocationBean locationBean = new LocationBean();
					locationBean.setCellid(NumberUtils.toInt(map.get("cellId").toString()));
					locationBean.setLac(NumberUtils.toInt(map.get("lac").toString()));
					locationBean.setMcc(NumberUtils.toInt(map.get("mcc").toString()));
					locationBean.setMnc(NumberUtils.toInt(map.get("mnc").toString()));
					listLocations.add(locationBean);
					this.geocoder.geocodeCellId(listLocations, callerId);
					
					lat = locationBean.getCoordinate() == null
							? 0 : locationBean.getCoordinate().getLatitude();
					lng = locationBean.getCoordinate() == null
							? 0 : locationBean.getCoordinate().getLongitude();
				}
				
				if (lat != 0 && lng != 0 && taskCollection.getLatBranch() != null &&
						taskCollection.getLngBranch() != null) {
					double distance = DistanceUtils.getDistanceInKm(lat, lng, 
							NumberUtils.toDouble(taskCollection.getLatBranch()), 
							NumberUtils.toDouble(taskCollection.getLngBranch()));
					NumberFormat nf = DecimalFormat.getInstance(Locale.US);
					String dtc = nf.format(distance);
					
					taskCollection.setDistance(dtc);
				}
			}
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("codeAnswerType").toString())) {
				NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
				if (StringUtils.isNotBlank((String) map.get("textAnswer"))) {
					taskCollection.setTextAnswer("Rp "+ formatKurs.format(
							NumberUtils.toDouble(map.get("textAnswer").toString())));	
				}
				if (StringUtils.isNotBlank((String) map.get("intTextAnswer"))) {
					taskCollection.setIntTextAnswer("Rp "+ formatKurs.format(
							NumberUtils.toDouble(map.get("intTextAnswer").toString())));	
				}
			}
			
			MsQuestion msQuestion = new MsQuestion();
			MsAnswertype msAnswertype = new MsAnswertype();
			msAnswertype.setCodeAnswerType(map.get("codeAnswerType").toString());
			msQuestion.setMsAnswertype(msAnswertype);
			
			taskCollection.setMsQuestion(msQuestion);
		
			
			if (result.contains(taskCollection)) {
			    continue;
			}
			if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(map.get("codeAnswerType").toString())
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(map.get("codeAnswerType").toString())
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(map.get("codeAnswerType").toString())) {
				if (GlobalVal.CODE_PROCESS_SUBMITTED.equals(codeProcess) || null == codeProcess) {
					if (!taskCollection.getOptionText().isEmpty()) {
						result.add(taskCollection);
					}
				} 
				else {
					result.add(taskCollection);
				}
			} 
			else {
				result.add(taskCollection);
			}
		}
		
		if("1".equals(link_encrypt)){
			for(int i=0; i<result.size(); i++){
				if("1".equals(result.get(i).getHasImage())){
					String[] temp = {result.get(i).getLob().toString()};
					result.get(i).setLob(CipherTool.encryptData(temp).get(0).toString());
				}
			}
		}
		return result;
	}

	public List getInquiryTaskSurveyDetailTaskHistory(Object params, Object order, 
			AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative("view.viewresult.taskHistory", 
				params, order);
		return list;
	}

	public Object[] getDetailTaskSurvey(Object params, AuditContext callerId) {
		Object[] result = (Object[]) this.getManagerDAO().selectOneNative(
				"view.viewresult.detailtasksurvey", params);
		return result;
	}
	
	@Override
	public Map<String, Object> getViewGroupTask(String groupTaskId, AuditContext callerId) {
		Map<String, Object> mapResult = new HashMap<String, Object>();
		List result = getDetailGroupTaskWithEncript(groupTaskId, callerId);
		MsGrouptask msGroupTask = getMsGroupTask(groupTaskId, callerId);
		
		mapResult.put("listTask", result);
		mapResult.put("msGroupTask", msGroupTask);
		return mapResult;
	}

	public List getDetailGroupTaskWithEncript(String groupTaskId, AuditContext callerId) {
		List<Map<String, Object>> result = new ArrayList<>();
		String[][] params = {{"groupTaskId", groupTaskId}};
		List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNative(
				"view.viewresult.getDetailGroupTask", params, null);
		
		if (!resultList.isEmpty()) {
			for (Map map : resultList) {
				String taskId = (String) map.get("d1");
				try {
					map.put("d7", taskId);
				} 
				catch (Exception e) {
					throw new RuntimeException(e.getMessage());
				}
				result.add(map);
			}
		}
		
		return result;
	}
	
	public MsGrouptask getMsGroupTask(String groupTaskId, AuditContext callerId) {
		MsGrouptask msGroupTask = new MsGrouptask();
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		condition.append(" and mgt.groupTaskId=:groupTaskId");
		paramMap.put("groupTaskId", groupTaskId);

		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by mgt.dtmCrt DESC");
		
		Map<String, Object> map = this.getManagerDAO().selectAll(
				"from MsGrouptask mgt join fetch mgt.msBranch where 1=1"
						+ condition.toString() + orderQuery.toString(),
				paramMap);
		
		List<MsGrouptask> listResult = (List) map.get(GlobalKey.MAP_RESULT_LIST);
		if (!listResult.isEmpty()) {
			for (MsGrouptask bean : listResult) {
				msGroupTask = bean;
				if (StringUtils.isNotBlank(bean.getApplNo())) {
					break;
				}
			}
		}
		return msGroupTask;
	}
	
	@Transactional(readOnly = true)
	public List getTaskLink(String uuidTask, String isIPPublic, AuditContext callerId) {
		String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
		String webPath = StringUtils.EMPTY;
		if ("1".equals(isIPPublic)) {
			webPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE_JSP_PUBLIC);
		}
		else {
			webPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE_JSP);
		}
		String path = webPath+"/getNCHTMLViewSurveyResult2?";
		List taskLinkList = new ArrayList<>();
		Object paramGroupTask[][] = {{paramUuidTaskH, new Long(uuidTask)}};
		BigInteger groupTaskId = (BigInteger)this.getManagerDAO().selectOneNativeString("SELECT GROUP_TASK_ID FROM MS_GROUPTASK WITH(NOLOCK) WHERE UUID_TASK_H = :uuidTaskH "
				+ "UNION SELECT GROUP_TASK_ID FROM FINAL_MS_GROUPTASK WITH (NOLOCK) WHERE UUID_TASK_H = :uuidTaskH", paramGroupTask);
		if(groupTaskId!=null && StringUtils.isNotBlank(groupTaskId.toString())) {
			Object params[][] = {{"groupTaskId", groupTaskId}};
			taskLinkList = this.getManagerDAO().selectAllNative("view.viewresult.getTaskLink", params, null);
		}
		
			if(taskLinkList!=null && !taskLinkList.isEmpty()) {
				for(int i=0; i<taskLinkList.size();i++) {
					Map map = (Map)taskLinkList.get(i);
					String webLink = path;
					if("1".equals(link_encrypt)){
						String[] link = {"taskId="+(String)map.get("d0")};
						webLink = webLink+ CipherTool.encryptData(link).get(0).toString();
					}else {
						webLink = webLink+ "taskId="+(String)map.get("d0");
					}
					map.put("link", webLink);
					taskLinkList.set(i, map);
				}
			}
		
		return taskLinkList;
	}
	
	@Transactional(readOnly = true)
	public List getTaskUpdate(String uuidTaskH, AuditContext callerId) {
		List<Map<String, Object>> taskUpdateList = new ArrayList<>();

		Object params[][] = {{paramUuidTaskH, uuidTaskH}};
		taskUpdateList = this.getManagerDAO().selectAllNative("view.viewresult.getTaskUpdate", params, null);

		for (Map<String, Object> taskUpdate : taskUpdateList) {
			String img = StringUtils.EMPTY;
			byte[] byteImg = null;

			TrTaskupdate trTaskUpdate = this.getManagerDAO().selectOne(TrTaskupdate.class,
					Long.valueOf(taskUpdate.get("d0").toString()));
			
			this.getManagerDAO().fetch(trTaskUpdate.getTrTaskH());
			TrTaskH taskH = trTaskUpdate.getTrTaskH();

			if (trTaskUpdate.getLobDocument() != null) {
				byteImg = trTaskUpdate.getLobDocument();
			} else if(trTaskUpdate.getDocumentPath() != null) {
				String uuid = trTaskUpdate.getDocumentPath();
				
				if ("1".equals(this.link_encrypt)) {
					String[] temp = { uuid };
					uuid = CipherTool.encryptData(temp).get(0).toString();
				}
		
				ViewImageRequestBean request = new ViewImageRequestBean();
				UploadImageBean imageBean = new UploadImageBean();
				
				//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
				Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
		        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
		        Date cutoffDate = null;
		        Date taskDate = null;
		        if(null != trTaskUpdate.getDtmUpd()) {
		        	taskDate = trTaskUpdate.getDtmUpd();
		        } else {
		        	taskDate = trTaskUpdate.getDtmCrt();
		        }
		        Object [][] paramsGroup = {{paramUuidTaskH, taskH.getTaskId()}};
				Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
						"SELECT	GROUP_TASK_ID " + 
						"FROM	MS_GROUPTASK with(nolock) " + 
						"WHERE	UUID_TASK_H = :uuidTaskH ", paramsGroup);
				String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(taskH.getMsForm().getUuidForm()));

				try {
					cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
				} catch (ParseException e1) {
					e1.printStackTrace();
				}

				if(taskDate.before(cutoffDate)) {
					imageBean.setTaskId(taskH.getTaskId());
					imageBean.setId(taskH.getTaskId());
				} else if (taskDate.after(cutoffDate)) {
					imageBean.setTaskId(groupTaskIdForm);
					imageBean.setId(groupTaskIdForm);
				}
				String dmsType = "survey";
				
				imageBean.setJenisDoc("Document Task Update");
				if(taskH.getAmMsuser() != null){
					imageBean.setUsername(taskH.getAmMsuser().getUniqueId());
				} else {
					imageBean.setUsername("admin");
				}
				imageBean.setType(dmsType);
				imageBean.setRefId("DOC_TASK_UPDATE_" + trTaskUpdate.getUuidTaskUpdate());
				request.setUploadImageBean(imageBean);
				ViewImageResponseBean response = this.intFormLogic.viewImageDMS(callerId, request);

				byteImg = response.getByteImage();
			}
			
			if (byteImg != null) {
				try {
					img = new String(Base64.encode((byte[]) byteImg), "ASCII");
		
					taskUpdate.put("lob", img);
				} catch (UnsupportedEncodingException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}

		return taskUpdateList;
	}
	
	@Override
	public String checkTaskH(Object[] params, AuditContext callerId) {
		Integer cnt = (Integer) this.getManagerDAO().selectOneNativeString("SELECT count(1) FROM TR_TASK_H WITH (NOLOCK) WHERE UUID_TASK_H = :uuidTaskH", params);
		String count = String.valueOf(cnt);
		return count;
	}

}
