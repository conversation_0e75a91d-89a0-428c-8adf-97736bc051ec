package com.adins.mss.businesslogic.impl.interfacing;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigInteger;
import java.sql.Blob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.HibernateException;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.interfacing.StagingTableLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ConfigSubmitTableType;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.exceptions.SubmitTaskException;
import com.adins.mss.exceptions.SubmitTaskException.Reason;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.gson.Gson;

@SuppressWarnings({"unchecked","rawtypes"})
@Transactional(readOnly=true)
public class GenericStagingTableLogic extends BaseLogic implements StagingTableLogic {
	private Logger logger = LoggerFactory.getLogger(GenericStagingTableLogic.class);

	private ImageStorageLogic imageStorageLogic;
	
	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
		this.imageStorageLogic = imageStorageLogic;
	}

	private Map<String, Object[]> mapRefIdAnsJson(long uuidTaskH, ImageStorageLocation isl) {
		Gson gson = new Gson();
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", uuidTaskH}});
		if (docDb == null || StringUtils.isNotBlank(docDb.getDocument())) {
			return Collections.emptyMap();
		}
		
		TaskDocumentBean document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);			
		
		Map<String, Object[]> mpRefIdAns = new HashMap<>();
		List<AnswerBean> answers = document.getAnswers();
		for (AnswerBean answer : answers) {
			String answerType = answer.getQuestion().getAnswerTypeCode();
			if (MssTool.isMultipleQuestion(answerType)) {
				if (answer.getOptAnswers() == null || answer.getOptAnswers().isEmpty()) {
					continue;
				}
				
				List<String> selectedOptions = new ArrayList<>(answer.getOptAnswers().size());
				for (OptionBean option : answer.getOptAnswers()) {
					String str = option.getCode();
					if (MssTool.isChoiceWithDescriptionQuestion(answerType))
						str += "|" + option.getFreeText();
					selectedOptions.add(str);
				}
								
				Object[] obj = new Object[3];
				obj[0] = StringUtils.join(selectedOptions, ", ");
				obj[1] = answerType;
				obj[2] = null;
				mpRefIdAns.put(answer.getQuestion().getRefId(), obj);
			}
			else if (MssTool.isChoiceQuestion(answerType) && !MssTool.isMultipleQuestion(answerType)) {
				if (answer.getOptAnswers() == null || answer.getOptAnswers().isEmpty()) {
					continue;
				}
				
				OptionBean option = answer.getOptAnswers().get(0);
				String str = option.getCode();
				if (MssTool.isChoiceWithDescriptionQuestion(answerType))
					str += "|" + option.getFreeText();
				
				Object[] obj = new Object[3];
				obj[0] = str;
				obj[1] = answerType;
				obj[2] = null;
				mpRefIdAns.put(answer.getQuestion().getRefId(), obj);
			}
			else if (MssTool.isImageQuestion(answerType)) {
				if (answer.getLobAnswer() == null || !answer.getLobAnswer().isHasImage())
					continue;
				
				byte[] image = null;
				if (isl == ImageStorageLocation.DATABASE) {
					image = this.imageStorageLogic.retrieveImageBlob(answer.getLobAnswer().getId(), false);
				}
				else if (isl == ImageStorageLocation.FILE_SYSTEM) {
					image = this.imageStorageLogic.retrieveImageFileSystemByTaskD(answer.getLobAnswer().getId(), false);
				}
				
				Object[] obj = new Object[3];
				obj[0] = StringUtils.EMPTY;
				obj[1] = answerType;
				obj[2] = image;
				mpRefIdAns.put(answer.getQuestion().getRefId(), obj);
			}
			else {
				Object[] obj = new Object[3];
				obj[0] = answer.getTxtAnswer();
				obj[1] = answerType;
				obj[2] = null;
				mpRefIdAns.put(answer.getQuestion().getRefId(), obj);
			}
		}
		
		return mpRefIdAns;
	}
	
	private Map<String, Object[]> mapRefIdAnsRow(long uuidTaskH, ImageStorageLocation isl) {
		Object[][] paramAnswer = { {"uuidTaskH", uuidTaskH} };
		
		List<Map<String, Object>> listRefId = this.getManagerDAO().selectAllNative("common.configsubmittable.getListAnsRefId", paramAnswer, null);		
		if (listRefId.isEmpty()) {
			return Collections.emptyMap();
		}
		
		Map<String, Object[]> mpRefIdAns = new HashMap<>();
		
		for (Map<String, Object> mp : listRefId) {
			//RefID | Answer | AnswerType | HasImage | ImagePath | LobFile
			boolean isExist = false;
			if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(mp.get("d2")) ||
					GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(mp.get("d2")) ||
					GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(mp.get("d2"))) {
				Object[] obj = mpRefIdAns.get((String) mp.get("d0"));
				
				if (null != obj) {
					isExist = true;
					obj[0] = obj[0]+", "+mp.get("d1");
					mpRefIdAns.put((String) mp.get("d0"), obj);
				}
			}
			
			byte[] img = null;
			if ("1".equals(mp.get("d3"))) { //if hasImage
				if (ImageStorageLocation.DATABASE == isl) {
					try {
						Blob blob = (Blob) mp.get("d5");
						if (blob != null) {
							img = blob.getBytes(1, (int) blob.length());
						}
					} catch (SQLException e) {
	                    throw new DatabaseException(DatabaseException.Reason.OTHER_DATA_ACCESS_EXCEPTION, e);
					}
				}
				else if (ImageStorageLocation.FILE_SYSTEM == isl) {
					if (null != mp.get("d4")) {
						String path = (String) mp.get("d4");
						File file = new File(path);
						try (FileInputStream stream = new FileInputStream(file)) {							
							img = new byte[(int) file.length()];
							stream.read(img);
						} catch(Exception e){
							throw new RuntimeException(e);
						}
					}
				}
			}
			if (!isExist) {
				Object[] obj = new Object[4];
				obj[0] = mp.get("d1");
				obj[1] = mp.get("d2");
				obj[2] = img;
				obj[3] = mp.get("d6");
				mpRefIdAns.put((String) mp.get("d0"), obj);
			}
		}
		
		return mpRefIdAns;		
	}
	
	//tambahan terkait add task from staging dan submit task to staging
	@Override
	public String submitToTable(AuditContext auditContext, String taskID, String idForm) {
		Object[][] params = { {Restrictions.eq("taskId", taskID)} };
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, params);
		this.getManagerDAO().fetch(trTaskH.getAmMsuser());
		this.getManagerDAO().fetch(trTaskH.getAmMsuser().getMsBranch());
		this.getManagerDAO().fetch(trTaskH.getAmMsuser().getMsBranch().getMsBranch());
		
		if (StringUtils.isBlank(idForm)) {
			idForm = trTaskH.getMsForm().getFormName();
		}

		logger.info("CHECKING STATUS TASK : [{}] - {}, WITH TASK ID : {}", trTaskH.getMsStatustask().getStatusCode(), trTaskH.getMsStatustask().getStatusTaskDesc(), taskID);		
		
		if (!GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(trTaskH.getMsStatustask().getStatusCode())) {
			logger.warn("submitToTable not run, status task '{}'='{}'", taskID, trTaskH.getMsStatustask().getStatusCode());
			return GlobalVal.SAVE_TABLE_RESULT_SUCCESS;
		}
		
		String[][] paramForm = { {"idForm", idForm} };
		
		int totalTable = (int) this.getManagerDAO().selectOneNative("common.configsubmittable.getCountListTable", paramForm);
		if (totalTable <= 0) {
			logger.warn("submitToTable not run, target table is empty");
			return GlobalVal.SAVE_TABLE_RESULT_SUCCESS;
		}
		
		List<Map<String, Object>> listParams = this.getManagerDAO().selectAllNative("common.configsubmittable.getListParams", paramForm, null);
		ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(auditContext);
		
		Map<String, Object[]> mpRefIdAns = null;
		if (PropertiesHelper.isTaskDJson()) {
			mpRefIdAns = this.mapRefIdAnsJson(trTaskH.getUuidTaskH(), isl);
		}
		else {
			mpRefIdAns = this.mapRefIdAnsRow(trTaskH.getUuidTaskH(), isl);
		}		
		
		List<StringBuilder> insTableStaging = new ArrayList<>();
		if (!listParams.isEmpty()) {
			StringBuilder sb = new StringBuilder();
			List listColumn = new ArrayList();
			List listAnswer = new ArrayList();
			String tableName = StringUtils.EMPTY;
			boolean isMultiRow = false;
			int itrTable = 0;
			int idxMultiRow = -1;
			
			for (int itr=0; itr<listParams.size(); itr++) {
				Map<String, Object> mp = listParams.get(itr);
				if (!mp.get("d0").equals(tableName)) {
					if (StringUtils.isNotBlank(tableName)) {
						insTableStaging = appendList(isMultiRow, idxMultiRow, listColumn, listAnswer, sb, insTableStaging);
						
						listColumn = new ArrayList();
						listAnswer = new ArrayList();
						itrTable = 0;
						idxMultiRow = -1;
						isMultiRow = false;
					}
					
					sb = new StringBuilder();
					tableName = (String) mp.get("d0");
					sb.append("insert into ");
					if (tableName.contains("[MULTIPLE]_")) {
						isMultiRow = true;
						sb.append(StringUtils.remove(tableName, "[MULTIPLE]_"));
					} 
					else {
						sb.append(tableName);
					}
					
				}
				
				String refId = (String) mp.get("d2");
				String defAnswer = (String) mp.get("d3");
				String dateFormat = (String) mp.get("d4");
				
				String ansTypeMap = StringUtils.EMPTY;
				String ansMap = StringUtils.EMPTY;
				byte[] imgMap = null;
				
				if (refId.contains("+")) {
					String[] listRefId = StringUtils.split(refId, "+");
					for (String id : listRefId) {
						Object[] objMap = mpRefIdAns.get(id);
						if (null != objMap) {
							String val = (String) objMap[0];
							if ("null".equalsIgnoreCase(val)) {
								val = "";
							}
							ansMap += StringUtils.trimToEmpty(val);
						}
					}
				} else {
					Object[] objMap = mpRefIdAns.get(refId);
					if (null != objMap) {
						ansMap = (String) objMap[0];
						ansTypeMap = (String) objMap[1];
						imgMap = (byte[]) objMap[2];
					}
					
					if (StringUtils.isNotBlank(ansMap)) {
						if (GlobalVal.ANSWER_TYPE_DATE.equals(ansTypeMap)) {
							Date dtFrom = null;
							try {
								dtFrom = DateUtils.parseDate(ansMap, "dd/MM/yyyy");
							}
							catch (ParseException e) {
								this.logger.error("Error on parsing date", e);
								throw new RuntimeException(e);
							} 
							ansMap = DateFormatUtils.format(dtFrom, "yyyy-MM-dd HH:mm:ss");
						} 
						else if (GlobalVal.ANSWER_TYPE_DATETIME.equals(ansTypeMap)) {
							Date dtFrom = null;
							try {
								dtFrom = DateUtils.parseDate(ansMap, "dd/MM/yyyy HH:mm:ss");
							}
							catch (ParseException e) {
								this.logger.error("Error on parsing date", e);
								throw new RuntimeException(e);
							} 
							ansMap = DateFormatUtils.format(dtFrom, "yyyy-MM-dd HH:mm:ss");
						} 
						else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(ansTypeMap)) {
							ansMap = ansMap.replaceAll("\\r\\n|\\r|\\n", " ");
						} 
						else if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(ansTypeMap) ||
								GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(ansTypeMap) ||
								GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(ansTypeMap) ) {
							idxMultiRow = itrTable;
						}
					}
				}
				String answer = (StringUtils.isBlank(ansMap)) ? defAnswer : ansMap;
				answer = submitToTableGetDefAnswer(answer, trTaskH, mpRefIdAns);
				
				if (StringUtils.isNotBlank(dateFormat)) {
					Date d = null;
					try {
						d = DateUtils.parseDate(answer, "yyyy-MM-dd HH:mm:ss");
					}
					catch (ParseException e) {
						this.logger.error("Error on parsing date", e);
						throw new RuntimeException(e);
					}
					answer = DateFormatUtils.format(d, dateFormat);
				}
				
				listColumn.add(mp.get("d1"));
				if (GlobalVal.ANSWER_TYPE_IMAGE.equals(ansTypeMap) ||
						GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA.equals(ansTypeMap) ||
						GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS.equals(ansTypeMap) || 
						GlobalVal.ANSWER_TYPE_DRAWING.equals(ansTypeMap)) {
					if (null != imgMap) {
						char[] hexArray = "0123456789ABCDEF".toCharArray();
						char[] hexChars = new char[imgMap.length * 2];
					    for ( int j = 0; j < imgMap.length; j++ ) {
					        int v = imgMap[j] & 0xFF;
					        hexChars[j * 2] = hexArray[v >>> 4];
					        hexChars[j * 2 + 1] = hexArray[v & 0x0F];
					    }
						listAnswer.add("0x"+new String(hexChars));
					} 
					else {
						listAnswer.add(null);
					}
				} 
				else {
					if (null == answer) {
						listAnswer.add("null");
					} 
					else {
						listAnswer.add("'"+answer+"'");
					}
				}
				itrTable++;
			}
			
			insTableStaging = appendList(isMultiRow, idxMultiRow, listColumn, listAnswer, sb, insTableStaging);
			
		}
		
		for (StringBuilder sb : insTableStaging) {
			logger.info("READY TO INSERT : {}", sb.toString());
			try (Connection connection = this.getManagerDAO().getConnection()) {
				PreparedStatement psmt = connection.prepareStatement(sb.toString());
				psmt.executeUpdate();
				psmt.close();
			} catch (SQLException e) {
				throw new HibernateException(e);
			}
		}		
		
		return GlobalVal.SAVE_TABLE_RESULT_SUCCESS;
	}
	
	private String submitToTableGetDefAnswer(String answer, TrTaskH taskH, Map<String, Object[]> mpRefIdAns) {
		String value = StringUtils.EMPTY;
		
		if (ConfigSubmitTableType.SET_NEW_UUID.toString().equals(answer)) {
			value = this.getManagerDAO().getUUID();
		} 
		else if (ConfigSubmitTableType.SET_TO_NULL.toString().equals(answer)) {
			value = null;
		} 
		else if (ConfigSubmitTableType.SET_TODAY.toString().equals(answer)) {
			value = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
		} 
		else if (ConfigSubmitTableType.SET_TASK_ID.toString().equals(answer)) {
			value = String.valueOf(taskH.getTaskId());
		} 
		else if (ConfigSubmitTableType.SET_USER_LOGIN.toString().equals(answer)) {
			value = taskH.getAmMsuser().getLoginId();
		}
		else if (ConfigSubmitTableType.SET_USER_REGION.toString().equals(answer)) {
			value = taskH.getAmMsuser().getMsBranch().getMsRegion().getRegionCode();
		}
		else if (ConfigSubmitTableType.SET_USER_BRANCH.toString().equals(answer)) {
			value = taskH.getAmMsuser().getMsBranch().getBranchCode();
		}
		else if (ConfigSubmitTableType.SET_USER_BRANCH_PARENT.toString().equals(answer)) {
			if (null != taskH.getAmMsuser().getMsBranch().getMsBranch()) {
				value = taskH.getAmMsuser().getMsBranch().getMsBranch().getBranchCode();
			}
		}
		else if (ConfigSubmitTableType.SET_CUST_NAME.toString().equals(answer)) {
			value = taskH.getCustomerName();
		} 
		else if (ConfigSubmitTableType.SET_CUST_PHONE.toString().equals(answer)) {
			value = taskH.getCustomerPhone();
		} 
		else if (ConfigSubmitTableType.SET_CUST_ADDR.toString().equals(answer)) {
			value = taskH.getCustomerAddress();
		} 
		else if (ConfigSubmitTableType.SET_NOTES.toString().equals(answer)) {
			value = taskH.getNotes();
		} 
		else if (ConfigSubmitTableType.SET_AGR_NO.toString().equals(answer)) {
			value = taskH.getAgreementNo();
		} 
		else if (ConfigSubmitTableType.SET_APPL_NO.toString().equals(answer)) {
			value = taskH.getApplNo();
		} 
		else if (ConfigSubmitTableType.SET_SUBMIT_DATE.toString().equals(answer)) {
			value = DateFormatUtils.format(taskH.getSubmitDate(), "yyyy-MM-dd HH:mm:ss");
		} 
		else if (ConfigSubmitTableType.SET_COPY.toString().equals(StringUtils.left(answer, ConfigSubmitTableType.SET_COPY.toString().length()))) {
			value = getAnswerValue(answer, mpRefIdAns);
		} 
		else if (ConfigSubmitTableType.SET_LOV.toString().equals(StringUtils.left(answer, ConfigSubmitTableType.SET_LOV.toString().length()))) {
			value = getLovValue(answer, mpRefIdAns);
		} 
		else {
			value = answer;
		}
		
		return value;
	}
	
	private String getAnswerValue(String answer, Map<String, Object[]> mpRefIdAns) {
		String value = StringUtils.EMPTY;
		answer = StringUtils.removeStart(answer, "SET_COPY(");
		answer = StringUtils.removeEnd(answer, ")");
		String[] objAnsSplit = answer.split(",");
		String[] objAns = new String[3];
		
		if (objAnsSplit.length >= 3) {
			int idxTrueVal = 1;
			int idxFalseVal = 2;
			objAns[0] = StringUtils.trimToEmpty(objAnsSplit[0]).trim();
			
			String trueCondition = StringUtils.trimToEmpty(objAnsSplit[idxTrueVal]);
			if (ConfigSubmitTableType.SET_COPY.toString().equals(StringUtils.left(trueCondition, ConfigSubmitTableType.SET_COPY.toString().length()))) {
				idxFalseVal = 4;
				objAns[1] = objAnsSplit[idxTrueVal] + "," + objAnsSplit[idxTrueVal+1] + "," + objAnsSplit[idxTrueVal+2];
				objAns[1] = objAns[1].trim();
			} else {
				objAns[1] = trueCondition.trim();
			}
			
			String falseCondition = StringUtils.trimToEmpty(objAnsSplit[idxFalseVal]);
			if (ConfigSubmitTableType.SET_COPY.toString().equals(StringUtils.left(falseCondition, ConfigSubmitTableType.SET_COPY.toString().length()))) {
				objAns[2] = objAnsSplit[idxFalseVal] + "," + objAnsSplit[idxFalseVal+1] + "," + objAnsSplit[idxFalseVal+2];
				objAns[2] = objAns[2].trim();
			} else {
				objAns[2] = falseCondition.trim();
			}
			//Object arr 0 (Logical Expression)
			String logicalExp = objAns[0].replace(" ", "")
					.replace("AND", "&&").replace("OR", "||");
			ScriptEngineManager mgr = new ScriptEngineManager();
			ScriptEngine engine = mgr.getEngineByName("JavaScript");
			
			String[] arrRefId = StringUtils.substringsBetween(logicalExp, "{", "}");
			for (String refId : arrRefId) {
				Object[] objMap = mpRefIdAns.get(refId);
				String ansMap = StringUtils.EMPTY;
				if (null != objMap) {
					ansMap = (String) objMap[0];
				}
				
				if (StringUtils.isNotBlank(ansMap)) {
					logicalExp = logicalExp.replace("{"+refId+"}", "'"+ansMap+"'");
				} else {
					logicalExp = logicalExp.replace("{"+refId+"}", "''");
				}
			}
			
			try {
				int tf = ((boolean) engine.eval(logicalExp)) ? 1 : 2;
				String answerValue = objAns[tf];
				if (ConfigSubmitTableType.SET_COPY.toString().equals(StringUtils.left(answerValue, ConfigSubmitTableType.SET_COPY.toString().length()))) {
					//Recursive get Copy Value
					value = getAnswerValue(answerValue, mpRefIdAns);
				} else {
					String refId = StringUtils.substringBetween(answerValue, "{", "}");
					if (StringUtils.isNotBlank(refId)) {
						Object[] objMap = mpRefIdAns.get(refId);
						String ansMap = StringUtils.EMPTY;
						if (null != objMap) {
							ansMap = (String) objMap[0];
						}
						value = ansMap;
					} else {
						value = answerValue;
					}
				}
			} catch (ScriptException e) {
				throw new SubmitTaskException(e.getMessage(), e, Reason.ERROR_SUBMIT);
			}
		}
		return value;
	}
	
	private String getLovValue(String answer, Map<String, Object[]> mpRefIdAns) {
		String value = StringUtils.EMPTY;
		answer = StringUtils.removeStart(answer, "SET_LOV(");
		answer = StringUtils.removeEnd(answer, ")");
		String[] objAnsSplit = answer.split(",");
		
		if (objAnsSplit.length == 2) {
			String refId = objAnsSplit[0];
			String format = objAnsSplit[1];
			Object[] objMap = mpRefIdAns.get(refId);
			if (null != objMap) {
				BigInteger lovId = (BigInteger) objMap[3];
				MsLov lov = this.getManagerDAO().selectOne(MsLov.class, Long.parseLong(lovId+""));
				if (null != lov) {
					if ("CODE".equalsIgnoreCase(format)) {
						value = lov.getCode();
					} else if ("DESCRIPTION".equalsIgnoreCase(format)) {
						value = lov.getDescription();
					} else if ("CONSTRAINT1".equalsIgnoreCase(format)) {
						value = lov.getConstraint1();
					} else if ("CONSTRAINT2".equalsIgnoreCase(format)) {
						value = lov.getConstraint2();
					} else if ("CONSTRAINT3".equalsIgnoreCase(format)) {
						value = lov.getConstraint3();
					} else if ("CONSTRAINT4".equalsIgnoreCase(format)) {
						value = lov.getConstraint4();
					} else if ("CONSTRAINT5".equalsIgnoreCase(format)) {
						value = lov.getConstraint5();
					}
				}
			}
		}
		return value;
	}
	
	private List appendList(boolean isMultiRow, int idxMultiRow, List listColumn, List listAnswer,
								StringBuilder sb, List insTableStaging) {
		if (isMultiRow) {
			if (idxMultiRow != -1) {
				String colMul = (String) listColumn.get(idxMultiRow);
				String ansMul = (String) listAnswer.get(idxMultiRow);
				
				listColumn.remove(idxMultiRow);
				listAnswer.remove(idxMultiRow);
				
				String[] objAns = ansMul.split(",");
				String tmpPrefix = sb.toString();
				for (int z=0; z<objAns.length; z++) {
					List listTmpColumn = new ArrayList<>();
					List listTmpAns = new ArrayList<>();
					sb = new StringBuilder();
					
					listTmpColumn.addAll(listColumn);
					listTmpAns.addAll(listAnswer);
					sb.append(tmpPrefix);
					
					String ans = objAns[z];
					ans = StringUtils.removeStart(ans, "'");
					ans = StringUtils.removeEnd(ans, "'");
					ans = StringUtils.trim(ans);
					listTmpColumn.add(colMul);
					listTmpAns.add("'"+ans+"'");
					
					Object[] arrColumn = listTmpColumn.toArray();
					Object[] arrAnswer = listTmpAns.toArray();
					sb.append("( ").append(StringUtils.join(arrColumn, ",")).append(" )");
					sb.append(" values ");
					sb.append("( ").append(StringUtils.join(arrAnswer, ",")).append(" )");
					insTableStaging.add(sb);
				}
			}
			
		} else {
			Object[] arrColumn = listColumn.toArray();
			Object[] arrAnswer = listAnswer.toArray();
			sb.append("( ").append(StringUtils.join(arrColumn, ",")).append(" )");
			sb.append(" values ");
			sb.append("( ").append(StringUtils.join(arrAnswer, ",")).append(" )");
			insTableStaging.add(sb);
		}
		return insTableStaging;
	}
}
