package com.adins.mss.businesslogic.impl.interfacing;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.enums.InterfaceType;

public class IntFormLogicFactoryBean implements FactoryBean<IntFormLogic>, ApplicationContextAware {
   private ApplicationContext applicationContext;
    private String interfaceType;
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public void setInterfaceType(String interfaceType) {
        this.interfaceType = interfaceType;
    }

    @Override
    public IntFormLogic getObject() throws Exception {
        IntFormLogic obj = null;
        
        if (InterfaceType.STAGING.toString().equals(this.interfaceType)) {
            obj = this.applicationContext.getBean(IntStagingFormLogic.class);
        }
        else if (InterfaceType.NEW_CONFINS.toString().equals(this.interfaceType)) {
            obj = this.applicationContext.getBean(IntNcFormLogic.class);
        }
        else if (InterfaceType.NONE.toString().equals(this.interfaceType)) {
            obj = this.applicationContext.getBean(IntNoneFormLogic.class);
        }
        
        return obj;
    }

    @Override
    public Class<IntFormLogic> getObjectType() {
        return IntFormLogic.class;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }

}
