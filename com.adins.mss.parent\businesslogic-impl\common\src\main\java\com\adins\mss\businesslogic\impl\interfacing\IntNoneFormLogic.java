package com.adins.mss.businesslogic.impl.interfacing;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.LuOnlineLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TblApiDashboard;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.custom.UploadImageRequestBean;
import com.adins.mss.model.custom.ViewImageRequestBean;
import com.adins.mss.model.custom.ViewImageResponseBean;
import com.adins.mss.services.model.collection.CollectionHistoryResponse;
import com.adins.mss.services.model.collection.InstallmentScheduleResponse;
import com.adins.mss.services.model.collection.PaymentHistoryResponse;
import com.adins.mss.services.model.common.CheckESignRegistrationResponse;
import com.adins.mss.services.model.common.CheckEsignRegistrationRequest;
import com.adins.mss.services.model.common.CheckOtrValueResponse;
import com.adins.mss.services.model.common.CheckStatusTaskRequest;
import com.adins.mss.services.model.common.CheckStatusTaskResponse;
import com.adins.mss.services.model.common.GenerateInvitationRequest;
import com.adins.mss.services.model.common.GetDsrFromPoloResponse;
import com.adins.mss.services.model.common.GetReferantorResponse;
import com.adins.mss.services.model.common.InstantApprovalResponse;
import com.adins.mss.services.model.common.OnlineLuResponse;
import com.adins.mss.services.model.common.SendDataPhotoToApiResponse;
import com.adins.mss.services.model.common.SubmitNegativeCustPoloRequest;
import com.adins.mss.services.model.common.SubmitNegativeCustPoloResponse;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.TaxPersonalResponse;
import com.adins.mss.services.model.common.TeleStatusCheckCallbackRequest;
import com.adins.mss.services.model.common.UpdateDataPoloRequest;
import com.adins.mss.services.model.common.UpdateDataPoloResponse;
import com.adins.mss.services.model.common.ValidateBiometrikResponse;
import com.adins.mss.services.model.common.VerifyReferantorResponse;
import com.adins.mss.services.model.newconfins.CIFBean;
import com.adins.mss.services.model.newconfins.CheckDukcapilResponse;
import com.adins.mss.services.model.newconfins.SubmitNapRequest;
import com.adins.mss.services.model.newconfins.SubmitPage1Request;
import com.adins.mss.services.model.newconfins.SubmitPage2Request;
import com.adins.mss.services.model.newconfins.SubmitPreIARequest;
import com.adins.mss.services.model.newconfins.UpdateStatusMainDealerResponse;

@SuppressWarnings({"unchecked", "rawtypes"})
public class IntNoneFormLogic  extends BaseLogic implements IntFormLogic {
	
//	private static final Logger LOG = LoggerFactory.getLogger(IntNoneFormLogic.class);

	private IntFormLogic intFormLogic;
	private LuOnlineLogic luOnlineLogic;

	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}	
	
	public void setLuOnlineLogic(LuOnlineLogic luOnlineLogic) {
		this.luOnlineLogic = luOnlineLogic;
	}

	//Collection History
	@Override
	public CollectionHistoryResponse getListCollActHistByAgrNo(
			AuditContext auditContext, String taskID) {
		CollectionHistoryResponse responseResult = intFormLogic.getListCollActHistByAgrNo(auditContext, taskID);
		return responseResult;
	}

	//Installment Schedule
	@Override
	public InstallmentScheduleResponse getListInstSchdlByAgrNo(
			AuditContext auditContext, String taskID) {
		InstallmentScheduleResponse responseResult = intFormLogic.getListInstSchdlByAgrNo(auditContext, taskID);
		return responseResult;
	}

	//PaymentHistory
	@Override
	public PaymentHistoryResponse getListPayHistByAgrNo(
			AuditContext auditContext, String taskID) throws ParseException {
		PaymentHistoryResponse responseResult = intFormLogic.getListPayHistByAgrNo(auditContext, taskID);
		return responseResult;
	}
	
	@Override
	public String submitResult(AuditContext auditContext, String taskId, String isFinal) {
		return null;
	}

	@Override
	public byte[] requestPO(String orderNo, String callerId) {
		return null;
	}

	@Override
	public byte[] reportIncentive(String loginId, String startDate,
			String endDate, String callerId) {
		return null;
	}

	@Override
	public String synSchema(String schemaId, String action, String callerId) {
		return null;
	}

	@Override
	public String cancelTask(String taskID, String flagSource, String callerID) {
		return null;
	}

	@Override
	public String saveResult(AuditContext auditContext, String taskID, String flagSource,
			String subsystemCode, String callerID, String isFinal) {
		return null;
	}

	@Override
	public boolean authenticateUser(AmMsuser amMsUser, String password) {
		return false;
	}

	@Override
	public String saveDepositReport(String batchID) {
		return null;
	}

	@Override
	public Map getPathApp(String isIPPublic) {
		Map result = new HashMap();
		String webPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE);
		String jspPath = StringUtils.EMPTY;
		if ("1".equals(isIPPublic)) {
			jspPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE_JSP_PUBLIC);
		}
		else {
			jspPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE_JSP);
		}
		result.put("WEB", webPath);
		result.put("JSP", jspPath);
		return result;
	}
	
	//tambahan luOnline
	@Override
	@Transactional(readOnly = true)
	public OnlineLuResponse luOnline(String refId,String lovGroup,String searchVal,
			String choiceFilterVal,AuditContext callerId){
		return this.luOnlineLogic.luOnline(refId, lovGroup, searchVal, choiceFilterVal, callerId);
	}

	@Override
	@Transactional(readOnly = true)
	public boolean validateLuOnlineCode(String code, String lovGroup, AuditContext callerId) {
		return this.luOnlineLogic.validateLuOnlineCode(code, lovGroup, callerId);
	}

	@Override
	public String resetPassword(String loginId, Date dob, String email,
			String ktpNo, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String changePassword(String loginId, String oldPassword,
			String newPassword, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String insertLossDeal(AuditContext auditContext, String taskId,
			String isFinal) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitNegativeList(AuditContext auditContext, String taskId,
			String isFinal, String isRecommendation) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ViewImageResponseBean viewImageDMS(AuditContext auditContext,
			ViewImageRequestBean viewImageRequestBean) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String uploadImageResponse(AuditContext auditContext,
			UploadImageRequestBean uploadImageRequestBean) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitNap(AuditContext auditContext, String taskId,
			String isFinal) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public String submitTaskUpdate(AuditContext auditContext, String taskUpdateId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CIFBean revampDukcapil(AuditContext auditContext, String nik, String name, String birthPlace,
			String birthDate, String userId, String password, String ipUser) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CheckDukcapilResponse checkDukcapil(AuditContext auditContext, String isPilotingCAE, String nik, String name, String birthPlace, String birthDate, String userId, String password, String ipUser, CIFBean cifBean, String taskIdPolo, Integer isPreApproval) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public InstantApprovalResponse submitInstantApproval(AuditContext auditContext, String taskId, String isFinal) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map requestOCR(String imagePath, String type, Boolean isPilotingCAE, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<String> getPendingTaskId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public TaxPersonalResponse verifyTaxPersonal(AuditContext audit, String nik, String npwp, String income) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map checkTeleStatus(AuditContext audit, String phoneNumber, int phoneOwner, Boolean isPilotingCae, String taskIdPolo, Integer isPreApproval) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public MssResponseType teleStatusCheckCallback(TeleStatusCheckCallbackRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean requestTeleowner(String idNumber, String phoneNumber, Boolean isPilotingCAE, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public Map validatePage1(SubmitPage1Request submitData, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map validatePage2(SubmitPage2Request submitData, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map preparation(TrTaskH task, String flagStatus, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public ValidateBiometrikResponse validateBiometric(AuditContext auditContext, String nik, String name, String birthdate, String birthplace,
			String address, String identity_photo, String selfie_photo) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<String> getPendingPreparationTaskIds() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public UpdateDataPoloResponse updateDataPolo(UpdateDataPoloRequest request, TrTaskH trTaskH, String statusMss,
			String status, String flagVoidSla, String negativeCustomer, String reasonReject, String flagAutoDelete, String reasonDelete, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public SubmitNegativeCustPoloResponse submitNegativeCustomerPoloToWise(SubmitNegativeCustPoloRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CheckStatusTaskResponse getStatusProspectPolo(CheckStatusTaskRequest req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void checkProspekPoloAsync(AuditContext callerId) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Map submitPreIA(SubmitPreIARequest submitData, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public UpdateStatusMainDealerResponse updateStatusTaskMainDealer(TrTaskH taskH, AuditContext callerId) {
		return null;
		// TODO Auto-generated method stub
		
	}

	@Override
	public MssResponseType GenerateInvitationLink(GenerateInvitationRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public SendDataPhotoToApiResponse uploadIdCard(String base64, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitNapPilotingCae(AuditContext auditContext, String taskId, String isFinal) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitPolo(SubmitTaskDBean[] taskdBean, TrTaskH trTaskH, String taskType, String isPreOffline, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean checkNegativeCust(String idNumber, String taskIdPolo, String name, String birthPlace, String birthDate, String motherName, Integer isPreApproval, String contractNo,AuditContext auditContext) {
		// TODO Auto-generated method stub
		return false;
	}



	@Override
	public Map checkSlikCAE(String nik, String fullName, String birthdate, String birthplace, String mothername, 
			String nikPsgn, String fullNamePsgn, String birthdatePsgn, String birthplacePsgn, String mothernamePsgn, 
			String nikGrntr, String fullNameGrntr, String birthdateGrntr, String birthplaceGrntr, String mothernameGrntr, String ntf, 
			String officeCode, String officeRegionCode, String productOfferingCode, String callMode, String contractNo, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitTaskGuarantor(AuditContext auditContext, TrTaskH taskGuarantor) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map checkIncome(String nik, String income, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitTaskOTS(AuditContext auditContext, String flagSourceOts, TrTaskH taskOTS) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public GetDsrFromPoloResponse getDsrFromPolo(Map<String, String> paramApi, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String updateMobileAssignmentId(String mobileTaskId, String newAppNo, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public GetReferantorResponse getReferantorCodeFromNc(String uniqueId, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String updateFromMssCopyApp(String newTaskIdPolo, String orderNo, String groupTaskId, String uuidTaskH,
			AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitDocupro(AuditContext auditContext, TrTaskH trTaskH, SubmitNapRequest bean) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<String> getAutoSubmitTaskId(AmGeneralsetting maxRetry, AmGeneralsetting duration,
			AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getHistoryCreditKBIJ(String request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public OnlineLuResponse luOnlineMaskapaiAsuransi(String refId, String lovGroup, String searchVal, String choiceFilterVal,
			AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map checkBiometricCae(String officeCode, String officeRegionCode, String productOfferingCode, String nik,
			String selfiePhoto, String nikPsgn, String selfiePhotoPsgn, String nikGrtr, String selfiePhotoGrtr,
			String taskIdPolo, String contractNo, Integer isPreApproval, String includeSpouse, String includeGuarantor,
			AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}
 
	@Override
	public String updateStatusWiseIDE(TrTaskH trTaskH, String type, AmMsuser loginBean, TblApiDashboard tblBean,
			AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CheckESignRegistrationResponse CheckEsignRegistrationStatus(
			CheckEsignRegistrationRequest checkEsignRequest) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CheckOtrValueResponse GetOtrValue(String officeCode, String assetCode, String manufYear) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void updateStatusIDE(TrTaskH trTaskH, String status, String type, AmMsuser loginBean,
			TblApiDashboard tblBean, AuditContext callerId) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public UpdateDataPoloResponse updateDataPoloAsync(UpdateDataPoloRequest request, TrTaskH trTaskH, String statusMss,
			String status, String flagVoidSla, String negativeCustomer, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public VerifyReferantorResponse VerifyReferantor(String taskId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void updateDataPoloLayer(String json, TrTaskH trTaskH, AuditContext callerId) {
		// TODO Auto-generated method stub
		
	}
}
