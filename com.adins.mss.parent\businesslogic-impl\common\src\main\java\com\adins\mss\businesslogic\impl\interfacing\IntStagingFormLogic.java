package com.adins.mss.businesslogic.impl.interfacing;

import java.io.File;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.cxf.helpers.IOUtils;
import org.apache.cxf.jaxrs.client.WebClient;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.MssResponseType;
import com.adins.framework.tool.lang.FormatterUtils;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.LuOnlineLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.interfacing.StagingTableLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.constants.enums.SubmitMethodType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.TblApiDashboard;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.TrViewcollactivity;
import com.adins.mss.model.TrViewinstallmentcard;
import com.adins.mss.model.TrViewpaymenthistoryH;
import com.adins.mss.model.custom.UploadImageRequestBean;
import com.adins.mss.model.custom.ViewImageRequestBean;
import com.adins.mss.model.custom.ViewImageResponseBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.collection.CollectionHistoryBean;
import com.adins.mss.services.model.collection.CollectionHistoryResponse;
import com.adins.mss.services.model.collection.InstallmentScheduleBean;
import com.adins.mss.services.model.collection.InstallmentScheduleResponse;
import com.adins.mss.services.model.collection.PaymentHistoryDBean;
import com.adins.mss.services.model.collection.PaymentHistoryHBean;
import com.adins.mss.services.model.collection.PaymentHistoryHList;
import com.adins.mss.services.model.collection.PaymentHistoryResponse;
import com.adins.mss.services.model.common.CheckESignRegistrationResponse;
import com.adins.mss.services.model.common.CheckEsignRegistrationRequest;
import com.adins.mss.services.model.common.CheckOtrValueResponse;
import com.adins.mss.services.model.common.CheckStatusTaskRequest;
import com.adins.mss.services.model.common.CheckStatusTaskResponse;
import com.adins.mss.services.model.common.GenerateInvitationRequest;
import com.adins.mss.services.model.common.GetDsrFromPoloResponse;
import com.adins.mss.services.model.common.GetReferantorResponse;
import com.adins.mss.services.model.common.InstantApprovalResponse;
import com.adins.mss.services.model.common.OnlineLuResponse;
import com.adins.mss.services.model.common.SendDataPhotoToApiResponse;
import com.adins.mss.services.model.common.SubmitNegativeCustPoloRequest;
import com.adins.mss.services.model.common.SubmitNegativeCustPoloResponse;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.TaxPersonalResponse;
import com.adins.mss.services.model.common.TeleStatusCheckCallbackRequest;
import com.adins.mss.services.model.common.UpdateDataPoloRequest;
import com.adins.mss.services.model.common.UpdateDataPoloResponse;
import com.adins.mss.services.model.common.ValidateBiometrikResponse;
import com.adins.mss.services.model.common.VerifyReferantorResponse;
import com.adins.mss.services.model.newconfins.CIFBean;
import com.adins.mss.services.model.newconfins.CheckDukcapilResponse;
import com.adins.mss.services.model.newconfins.SubmitNapRequest;
import com.adins.mss.services.model.newconfins.SubmitPage1Request;
import com.adins.mss.services.model.newconfins.SubmitPage2Request;
import com.adins.mss.services.model.newconfins.SubmitPreIARequest;
import com.adins.mss.services.model.newconfins.UpdateStatusMainDealerResponse;
import com.adins.mss.services.model.openpublic.common.AuthenticateUserToStaging;
import com.adins.mss.services.model.openpublic.common.CancelTaskToStaging;
import com.adins.mss.services.model.openpublic.common.SyncSchemaBean;
import com.adins.mss.services.model.openpublic.common.SyncSchemaToStaging;
import com.adins.mss.services.model.openpublic.common.TaskResultStagingBean;
import com.adins.mss.services.model.openpublic.common.TaskResultToStaging;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;

@SuppressWarnings({"unchecked", "rawtypes"})
public class IntStagingFormLogic extends BaseLogic implements IntFormLogic, MessageSourceAware {

	private Gson gson = new Gson();
	private static final Logger LOG = LoggerFactory.getLogger(IntStagingFormLogic.class);

	private static final String DATE_FORMAT				= "ddMMyyyyHHmmss";
	
	private static final String URI						= SpringPropertiesUtils.getProperty(GlobalKey.STAGING_URI);
	private static final String PATH_AUTH_USER			= "/task/authUser";
	private static final String PATH_SAVE_RESULT		= "/task/saveResult";
	private static final String PATH_CANCEL_TASK		= "/task/cancelTask";
	private static final String PATH_SYN_SCHEMA 		= "/task/syncSchema";
	
	private MessageSource messageSource;
	private LuOnlineLogic luOnlineLogic;
	private ImageStorageLogic imageStorageLogic;
	private StagingTableLogic stagingTableLogic;
	
	public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    public void setLuOnlineLogic(LuOnlineLogic luOnlineLogic) {
		this.luOnlineLogic = luOnlineLogic;
	}

	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
		this.imageStorageLogic = imageStorageLogic;
	}

	public void setStagingTableLogic(StagingTableLogic stagingTableLogic) {
		this.stagingTableLogic = stagingTableLogic;
	}

	//Cancel Task
	@Transactional(readOnly=true)
	@Override
	public String cancelTask(String taskID, String flagSource, String callerID) {
		LOG.info("Begin CancelTask with taskID : {}", taskID);
		
		if (!(GlobalVal.SUBSYSTEM_MO.equals(flagSource) ||
				GlobalVal.SUBSYSTEM_MS.equals(flagSource) ||
				GlobalVal.SPVADHOC.equals(flagSource))) {
			WebClient client = WebClient.create(URI)
	                .accept(MediaType.APPLICATION_JSON).type(MediaType.APPLICATION_JSON)
	                .path(PATH_CANCEL_TASK);
			
			CancelTaskToStaging sendData = new CancelTaskToStaging();
			
			TrTaskH trTaskH = this.getManagerDAO().selectOne(
					"from TrTaskH tth join fetch tth.msForm mf join fetch mf.amMssubsystem ms where tth.taskId = :taskId", 
					new Object[][] {{"taskId", taskID}});
			
			sendData.setSubsystemCode(trTaskH.getMsForm().getAmMssubsystem().getSubsystemName());
			sendData.setNotes(trTaskH.getNotes());			
			sendData.setTaskId(taskID);
			
			String json = gson.toJson(sendData, CancelTaskToStaging.class);
			LOG.info("JSON Cancel Task : {}", json);
			
			Response response = client.post(json);
			InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
			String result = null;
			try {
				result = IOUtils.toString(isReader);
			}
			catch(Exception e) {
				throw new RuntimeException(e);
			}
			
			if ("0".equals(result) || response.getStatus() != Response.Status.OK.getStatusCode()){
				throw new RemoteException("Failed send to core system");
			}
			else {
				LOG.info("SUCCESS Cancel Task : {}", taskID);
			}
		}
		else {
			LOG.info("TASK ID : {}, NOT VALID TERMINAL ID : {} FOR CANCEL TASK", taskID, flagSource);
		}
		
		return GlobalVal.SERVICES_RESULT_SUCCESS;
		
	}
	
	//Sync Schema
	@Transactional(readOnly=true)
	@Override
	public String synSchema(String schemaId, String action, String callerId) {
		LOG.info("Begin Sync Staging Schema with Schema Name : {}", schemaId);
		String response = StringUtils.EMPTY;
		WebClient client = WebClient.create(URI)
                .accept(MediaType.APPLICATION_JSON).type(MediaType.APPLICATION_JSON)
                .path(PATH_SYN_SCHEMA);
		
		SyncSchemaToStaging sendData = getSyncSchema(schemaId, action, callerId);
		
		String json = gson.toJson(sendData, SyncSchemaToStaging.class);
		LOG.info("JSON SYNC SCHEMA : {}", json);
		Response postResponse = client.post(json);
		InputStreamReader isReader = new InputStreamReader((InputStream) postResponse.getEntity());
		String result = null;
		try {
			result = IOUtils.toString(isReader);
		}
		catch (Exception e) {
			throw new RuntimeException(e);
		}
		
		if ("0".equals(result) || postResponse.getStatus() != Response.Status.OK.getStatusCode()) {
			response = "0";
			LOG.warn("HTTP Error while Sync Schema to {} : ({}) - {} - Schema Name : {}",
					client.getCurrentURI(), postResponse.getStatus(), result, schemaId);
		}
		else {
			response = "1";
			LOG.info("Sync Schema : {}, Success Sync Schema with return : {}",
					schemaId, result);
		}
		
		return response;
	}
	
	//Authenticate User
	@Transactional(readOnly=true)
	@Override
	public boolean authenticateUser(AmMsuser amMsUser, String password) {
		LOG.info("Begin Authenticat User with userID : {}",amMsUser.getLoginId());
		WebClient client = WebClient.create(URI)
                .accept(MediaType.APPLICATION_JSON).type(MediaType.APPLICATION_JSON)
                .path(PATH_AUTH_USER);
		
		AuthenticateUserToStaging bean = new AuthenticateUserToStaging();
		bean.setUserId(amMsUser.getLoginId());
		bean.setPassword(password);
		
		boolean canLogin = false;
		
		String json = gson.toJson(bean, AuthenticateUserToStaging.class);
		LOG.info("JSON AUTHENTICATE USER : {}", json);
		Response response = client.post(json);
		InputStreamReader isReader = new InputStreamReader((InputStream) response.getEntity());
		String result = null;
		try {
			result = IOUtils.toString(isReader);
		}
		catch(Exception e) {
			throw new RuntimeException(e);
		}
		if ("0".equals(result) || response.getStatus() != Response.Status.OK.getStatusCode()) {
			LOG.warn("HTTP Error while AUTHENTICATE USER to {} : ({}) - {} - USER ID : {}",
					client.getCurrentURI(), response.getStatus(), result, amMsUser.getLoginId());
		}
		else {
			LOG.info("USER ID : {}, Success AUTHENTICATE USER with return : {}",
					amMsUser.getLoginId(), result);
			if ("1".equals(result))
				canLogin = true;
		}
		return canLogin;
	}
	
	//Save Result
	@Transactional(readOnly=true)
	@Override
	public String saveResult(AuditContext auditContext, String taskID, String flagSource, String subsystemCode, String callerID, String isFinal) {
		LOG.info("Begin Staging Save Result with taskId : {}", taskID);
		String response = StringUtils.EMPTY;
		
		Object[][] paramSubmitMethod = {{Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_SUBMIT_METHOD)}};
		AmGeneralsetting amGS = (AmGeneralsetting) this.getManagerDAO().selectOne(AmGeneralsetting.class, paramSubmitMethod);
		
		if (null != amGS && SubmitMethodType.DYNAMIC_TABLE.getSubmitMethodType().equals(amGS.getGsValue())) {			
			this.stagingTableLogic.submitToTable(auditContext, taskID, null);
			response = GlobalVal.SERVICES_RESULT_SUCCESS;			
		}
		else {
			//IF Empty Submit method Default use SubmitMethodType.Coding
			if (!(GlobalVal.SUBSYSTEM_MO.equals(flagSource) || GlobalVal.SUBSYSTEM_MS.equals(flagSource) ||
					GlobalVal.SPVADHOC.equals(flagSource))) {
				
				TaskResultToStaging bean = getTaskResult(taskID, subsystemCode, auditContext);
				
				if (null == bean) {
					response = "0";
					return response;
				}
				
				String json = gson.toJson(bean, TaskResultToStaging.class);
				LOG.info("JSON SAVE RESULT : {}", json);
				
				WebClient client = WebClient.create(URI)
		                .accept(MediaType.APPLICATION_JSON).type(MediaType.APPLICATION_JSON)
		                .path(PATH_SAVE_RESULT);
				Response postResponse = client.post(json);
				InputStreamReader isReader = new InputStreamReader((InputStream) postResponse.getEntity());
				String result = null;
				try {
					result = IOUtils.toString(isReader);
				}
				catch(Exception e) {
					throw new RuntimeException(e);
				}
				if ("0".equals(result) || postResponse.getStatus() != Response.Status.OK.getStatusCode()) {
					response = "0";
					LOG.warn("HTTP Error while SAVE RESULT to {} : ({}) - {} - TASK ID : {}",
							client.getCurrentURI(), postResponse.getStatus(), result, taskID);
					throw new RemoteException("Failed send to core system");
				}
				else {
					response = "1";
					LOG.info("SAVE RESULT : {}, Success REQUEST PO with return : {}",
							taskID, result);
				}
			}
			else {
				LOG.info("TASK ID : {}, NOT VALID TERMINAL ID : {} FROM SUBSYSTEM : {} FOR SAVE RESULT", taskID, flagSource, subsystemCode);
			}
		}
		return response;
	}
	
	//Collection History
	@Transactional(readOnly=true)
	@Override
	public CollectionHistoryResponse getListCollActHistByAgrNo(AuditContext auditContext, String taskID) {
		CollectionHistoryResponse responseResult = new CollectionHistoryResponse();
		List<CollectionHistoryBean> collActivityList = new ArrayList<CollectionHistoryBean>();
			
		String paramI[][] = { {"taskID",taskID} };
		LOG.info("TASK ID : " + taskID);
		Object[] queryResult = (Object[]) this.getManagerDAO().selectOneNativeString(
		        "select AGREEMENT_NO, UUID_TASK_H from tr_task_h with (nolock) where task_id = :taskID", paramI);
		if (queryResult == null)
		    throw new EntityNotFoundException(this.messageSource.getMessage(
		            "businesslogic.viewhistory.agreementnotfound", null, Locale.ENGLISH), taskID);
		
		String agreementNo = (String) queryResult[0];
		String uuidTaskH = (String) queryResult[1];
		LOG.info("AGREEMENT NO : " + agreementNo + " || UUID TASK H : " + uuidTaskH);
		
		Object paramA[][] = { {Restrictions.eq("agreementNo", agreementNo)} };	
		String[][] orders = { { "activityDate", GlobalVal.ROW_ORDER_DESC } };
		Map<String, Object> collHistList = this.getManagerDAO().list(TrViewcollactivity.class, paramA, orders);			
		List<TrViewcollactivity> trViewCollList = (List<TrViewcollactivity>) collHistList.get(GlobalKey.MAP_RESULT_LIST);
		LOG.info("SIZE : " + trViewCollList.size());
		
		for (TrViewcollactivity trViewColl : trViewCollList) {
			CollectionHistoryBean bean = new CollectionHistoryBean();
			bean.setUuidTaskId(uuidTaskH);
            bean.setAgreementNo(trViewColl.getAgreementNo());
			bean.setDtmCrt(trViewColl.getDtmCrt());
			bean.setUsrCrt(trViewColl.getUsrCrt());
			bean.setDtmUpd(trViewColl.getDtmUpd());
			bean.setUsrUpd(trViewColl.getUsrUpd());
			bean.setBranchCode(trViewColl.getBranchCode());
			bean.setActivityDate(trViewColl.getActivityDate());
			bean.setCollectorName(trViewColl.getCollectorName());
			bean.setActivity(trViewColl.getActivity());
			bean.setResult(trViewColl.getResult());
			bean.setPtpDate(trViewColl.getPtpDate());
			bean.setNotes(trViewColl.getNotes());
			bean.setOverDueDays(trViewColl.getOverDueDays()==null ? null : String.valueOf(trViewColl.getOverDueDays()));
			bean.setNextPlanAction(trViewColl.getNextPlanAction());
			bean.setNextPlanDate(trViewColl.getNextPlanDate());
			collActivityList.add(bean);
		}
		responseResult.setAgreementNo(agreementNo);
		responseResult.setCollectionHistoryList(collActivityList);
			
		return responseResult;
	}

	//Installment Schedule
	@Transactional(readOnly=true)
	@Override
	public InstallmentScheduleResponse getListInstSchdlByAgrNo(AuditContext auditContext, String taskID) {
		InstallmentScheduleResponse responseResult = new InstallmentScheduleResponse();
		List<InstallmentScheduleBean> listResult = new ArrayList<InstallmentScheduleBean>();
					
		String paramI[][] = { {"taskID",taskID} };
        Object[] queryResult = (Object[]) this.getManagerDAO().selectOneNativeString(
                "select AGREEMENT_NO, UUID_TASK_H from tr_task_h with (nolock) where task_id = :taskID", paramI);
        if (queryResult == null)
            throw new EntityNotFoundException(this.messageSource.getMessage(
                    "businesslogic.viewhistory.agreementnotfound", null, Locale.ENGLISH), taskID);
        
        String agreementNo = (String) queryResult[0];
        String uuidTaskH = (String) queryResult[1];
        
		Object paramA[][] = { {Restrictions.eq("agreementNo", agreementNo)} };
		String[][] orders = { { "installmentNo", GlobalVal.ROW_ORDER_ASC } };
		Map<String, Object> trViewInsList = this.getManagerDAO().list(TrViewinstallmentcard.class, paramA, orders);
		List<TrViewinstallmentcard> trViewInstallmentCardList = (List<TrViewinstallmentcard>) trViewInsList.get(GlobalKey.MAP_RESULT_LIST);
		
		for (TrViewinstallmentcard trViewInstallmentCard : trViewInstallmentCardList){
			InstallmentScheduleBean bean = new InstallmentScheduleBean();
			bean.setUuidTaskId(uuidTaskH);
			bean.setAgreementNo(trViewInstallmentCard.getAgreementNo());
			bean.setUsrCrt(trViewInstallmentCard.getUsrCrt());
			bean.setDtmCrt(trViewInstallmentCard.getDtmCrt());
			bean.setUsrUpd(trViewInstallmentCard.getUsrUpd());
			bean.setDtmUpd(trViewInstallmentCard.getDtmUpd());
			bean.setBranchCode(trViewInstallmentCard.getBranchCode());
			bean.setInstallmentNo(String.valueOf(trViewInstallmentCard.getInstallmentNo()));
			bean.setDueDate(trViewInstallmentCard.getDueDate());
			bean.setInstallmentAmount(toNumberFormatted(trViewInstallmentCard.getInstallmentAmount()));
			bean.setInstlPaidAmount(toNumberFormatted(trViewInstallmentCard.getInstlPaidAmount()));
			bean.setInstlPaidDate(trViewInstallmentCard.getInstlPaidDate());
			bean.setLcInstlAmount(toNumberFormatted(trViewInstallmentCard.getLcInstlAmount()));
			bean.setLcInstlPaid(toNumberFormatted(trViewInstallmentCard.getLcInstlPaid()));
			bean.setLcInstlWaived(toNumberFormatted(trViewInstallmentCard.getLcInstlWaived()));
			bean.setPrincipalAmount(toNumberFormatted(trViewInstallmentCard.getPrincipalAmount()));
			bean.setInterestAmount(toNumberFormatted(trViewInstallmentCard.getInterestAmount()));
			bean.setOsPrincipalAmount(toNumberFormatted(trViewInstallmentCard.getOsPrincipalAmount()));
			bean.setOsInterestAmount(toNumberFormatted(trViewInstallmentCard.getOsInterestAmount()));
			bean.setLcDays(trViewInstallmentCard.getLcDays()==null ? null : trViewInstallmentCard.getLcDays().toString());
			bean.setLcAdminFee(toNumberFormatted(trViewInstallmentCard.getLcAdminFee()));
			bean.setLcAdminFeePaid(toNumberFormatted(trViewInstallmentCard.getLcAdminFeePaid()));
			bean.setLcAdminFeeWaive(toNumberFormatted(trViewInstallmentCard.getLcAdminFeeWaive()));
			
			listResult.add(bean);
		}
		responseResult.setAgreementNo(agreementNo);
		responseResult.setInstallmentScheduleList(listResult);

		return responseResult;
	}

	//PaymentHistory
	@Transactional(readOnly=true)
	@Override
	public PaymentHistoryResponse getListPayHistByAgrNo(AuditContext auditContext, String taskID) throws ParseException {
		PaymentHistoryResponse responseResult = new PaymentHistoryResponse();
		List<PaymentHistoryHList> listResult = new ArrayList<PaymentHistoryHList>();
			
		String paramI[][] = { {"taskID",taskID} };
		Object[] queryResult = (Object[]) this.getManagerDAO().selectOneNativeString(
		        "select AGREEMENT_NO, UUID_TASK_H from tr_task_h with (nolock) where task_id = :taskID", paramI);
        if (queryResult == null)
            throw new EntityNotFoundException(this.messageSource.getMessage(
                    "businesslogic.viewhistory.agreementnotfound", null, Locale.ENGLISH), taskID);
        
		String agreementNo = (String) queryResult[0];
        String uuidTaskH = (String) queryResult[1];
        
		Object paramH[][] = { {Restrictions.eq("agreementNo", agreementNo)} };
		String[][] orders = { { "valueDate", GlobalVal.ROW_ORDER_ASC } };
		Map<String, Object> paymentHistList = this.getManagerDAO().list(TrViewpaymenthistoryH.class, paramH, orders);			
		List<TrViewpaymenthistoryH> trViewPaymentHistoryHList = (List<TrViewpaymenthistoryH>) paymentHistList.get(GlobalKey.MAP_RESULT_LIST);
		
		
		for (TrViewpaymenthistoryH trViewPaymentHistoryH : trViewPaymentHistoryHList){
			PaymentHistoryHList paymentHList = new PaymentHistoryHList();
			
			List<PaymentHistoryDBean> paymentHistoryDList = Collections.<PaymentHistoryDBean>emptyList();
			PaymentHistoryHBean bean = new PaymentHistoryHBean();
            bean.setUuidTaskId(uuidTaskH);
			bean.setAgreementNo(trViewPaymentHistoryH.getAgreementNo());
			bean.setUsrCrt(trViewPaymentHistoryH.getUsrCrt());
			bean.setDtmCrt(trViewPaymentHistoryH.getDtmCrt());
			bean.setUsrUpd(trViewPaymentHistoryH.getUsrUpd());
			bean.setDtmUpd(trViewPaymentHistoryH.getDtmUpd());
			bean.setBranchCode(trViewPaymentHistoryH.getBranchCode());
			bean.setReceiptNo(trViewPaymentHistoryH.getReceiptNo());
			bean.setValueDate(trViewPaymentHistoryH.getValueDate());
			bean.setPostingDate(trViewPaymentHistoryH.getPostingDate());
			bean.setPaymentAmount(toNumberFormatted(trViewPaymentHistoryH.getPaymentAmount()));
			bean.setInstallmentAmount(toNumberFormatted(trViewPaymentHistoryH.getInstallmentAmount()));
			bean.setInstallmentNumber(trViewPaymentHistoryH.getInstallmentNumber()==null ?
			        null : trViewPaymentHistoryH.getInstallmentNumber().toString());
			bean.setTransactionType(trViewPaymentHistoryH.getTransactionType());
			bean.setWopCode(trViewPaymentHistoryH.getWopCode());
			
			paymentHistoryDList = this.paymentHistoryD(auditContext,
					String.valueOf(trViewPaymentHistoryH.getUuidViewPaymentHistoryH()), uuidTaskH);
			
			paymentHList.setPaymentHistoryH(bean);
			paymentHList.setPaymentHistoryDList(paymentHistoryDList);
			
			listResult.add(paymentHList);
		}
		responseResult.setAgreementNo(agreementNo);
		responseResult.setPaymentHistoryHList(listResult);

		return responseResult;
	}
	
	private List<PaymentHistoryDBean> paymentHistoryD(AuditContext auditContext,
			String uuidHeader, String uuidTaskId) throws ParseException {
		List<PaymentHistoryDBean> listResult = new ArrayList<PaymentHistoryDBean>();
			
		String paramH[][] = { {"uuidHeader", uuidHeader} };
		List<Map<String, Object>> paymentDList = this.getManagerDAO().selectAllNativeString("select UUID_VIEW_PAYMENT_HISTORY_D, "+
				"NULL, DTM_CRT, USR_CRT, coalesce(DTM_UPD,'') DTM_UPD, coalesce(USR_UPD,'') USR_UPD,  "+
				"coalesce(PAYMENT_ALLOCATION_NAME,'') PAYMENT_ALLOCATION_NAME, coalesce(OS_AMOUNT_OD,0) OS_AMOUNT_OD, "+
				"coalesce(RECEIVE_AMOUNT,0) RECEIVE_AMOUNT from TR_VIEWPAYMENTHISTORY_D with (nolock) " +
				"where UUID_VIEW_PAYMENT_HISTORY_H = :uuidHeader", paramH);
		
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		for (int i = 0; i < paymentDList.size(); i++) {
			Map temp = (Map) paymentDList.get(i);
			
			PaymentHistoryDBean bean = new PaymentHistoryDBean();
			bean.setUuidViewPaymentHistoryD(temp.get("d0").toString());
			bean.setUuidTaskId(uuidTaskId);
			bean.setDtmCrt(df.parse(temp.get("d2").toString()));
			bean.setUsrCrt(temp.get("d3").toString());
			bean.setDtmUpd(df.parse(temp.get("d4").toString()));
			bean.setUsrUpd(temp.get("d5").toString());
			bean.setPaymentAllocationName(temp.get("d6").toString());
			bean.setOsAmountOd(toNumberFormatted(temp.get("d7")));
			bean.setReceiveAmount(toNumberFormatted(temp.get("d8")));
			
			listResult.add(bean);
		}

		return listResult;
	}
	
	@Transactional(readOnly=true)
	@Override
	public String submitResult(AuditContext auditContext, String taskId, String isFinal) {
		return null;
	}
	
	@Transactional(readOnly=true)
	@Override
	public byte[] requestPO(String orderNo, String callerId) {
		return null;
	}
	
	@Override
	public byte[] reportIncentive(String loginId, String startDate,
			String endDate, String callerId) {
		return null;
	}

	@Override
	public String saveDepositReport(String batchID) {
		return null;
	}
	
	//Get Data Sync Schema
	private SyncSchemaToStaging getSyncSchema(String schemaId, String action, String callerId) {
		SyncSchemaToStaging beanResult = new SyncSchemaToStaging();
		List<SyncSchemaBean> resultList = new ArrayList<SyncSchemaBean>();
			
		//get loginId from callerId
		AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId));
		
		//get MsForm
		MsForm msForm = this.getManagerDAO().selectOne(
				"from MsForm mf join fetch mf.amMssubsystem where mf.formName = :formName", new Object[][] {{ "formName", schemaId }});
		
		Integer formVersion = (Integer) this.getManagerDAO().selectOneNativeString(
				"SELECT MAX(FORM_VERSION) "
				+ "FROM MS_FORMHISTORY"
				+ " WHERE UUID_FORM = :uuidForm", new Object[][] {{"uuidForm", msForm.getUuidForm()}});
		Map<String, Object> resultQuestionSet = this.getManagerDAO().list(
				"from MsFormquestionset mfqs join fetch mfqs.msFormhistory mfh join fetch mfqs.msForm mf join fetch mfqs.msAnswertype mat "
				+ "where mf.uuidForm = :uuidForm and mfh.formVersion = :formVersion and mfqs.questionGroupIsActive = :isActive "
				+ "and mfqs.questionIsActive = :isActive",
				new Object[][]{{"uuidForm", msForm.getUuidForm()}, {"formVersion", formVersion}, {"isActive", "1"}});
		List<MsFormquestionset> listQuestionSet = (List) resultQuestionSet.get(GlobalKey.MAP_RESULT_LIST);
		if (listQuestionSet != null) {
			for (MsFormquestionset msQuestionSet : listQuestionSet) {
				SyncSchemaBean bean = new SyncSchemaBean();
				bean.setIdentifier(msQuestionSet.getRefId());
				bean.setLabel(msQuestionSet.getQuestionLabel());
				if(null != msQuestionSet.getMaxLength() )
					bean.setMaxLength(msQuestionSet.getMaxLength().toString());
				bean.setLovGroup(msQuestionSet.getLovGroup());
				bean.setAnswerType(msQuestionSet.getMsAnswertype().getAnswerTypeName());
				resultList.add(bean);
			}
		}
		beanResult.setUserUpdateName(amMsUser.getLoginId());
		beanResult.setAction(action);
		beanResult.setSchemaName(msForm.getFormName());
		beanResult.setIsActive(msForm.getIsActive());
		beanResult.setSyncSchemaBean(resultList);
		beanResult.setMrSrvySrc(msForm.getAmMssubsystem().getSubsystemName());
		
		return beanResult;
	}
	
	private List<TaskResultStagingBean> fromJson(long uuidTaskH, boolean isReadFromFinData) {			
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", uuidTaskH}});
		
		if (docDb == null || StringUtils.isBlank(docDb.getDocument()))
			return Collections.emptyList();
		
		TaskDocumentBean tdb = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
		if (tdb.getAnswers() == null || tdb.getAnswers().isEmpty())
			return Collections.emptyList();

		List<TaskResultStagingBean> answerList = new ArrayList<TaskResultStagingBean>();
		List<AnswerBean> answers = tdb.getAnswers();
		for (AnswerBean answer : answers) {
			List<OptionBean> options = (isReadFromFinData) ? answer.getFinOptAnswers() : answer.getOptAnswers();
			String textAnswer = (isReadFromFinData) ? answer.getFinTxtAnswer() : answer.getTxtAnswer();
			String lovCode = (options == null || options.isEmpty()) ? null : options.get(0).getCode();
			String txtAnswer = (lovCode == null) ? textAnswer : options.get(0).getFreeText();
			
			TaskResultStagingBean bean = new TaskResultStagingBean();
			
			bean.setIdentifier(answer.getQuestion().getRefId());
			bean.setAnswerTypeCode(answer.getQuestion().getAnswerTypeCode());
			bean.setQuestionText(answer.getQuestion().getLabel());
			bean.setTextAnswer(txtAnswer);
			bean.setLovID(lovCode);
			bean.setOptionText((lovCode == null) ? null : answer.getOptAnswers().get(0).getDesc());
			if (answer.getLocation() != null) {				
				if (answer.getLocation().getLat() != null) {
					bean.setLatitude(new BigDecimal(answer.getLocation().getLat().doubleValue()));
					bean.setLongitude(new BigDecimal(answer.getLocation().getLng().doubleValue()));
				}
				
				bean.setMnc(answer.getLocation().getMnc());
				bean.setMcc(answer.getLocation().getMcc());
				bean.setLac(answer.getLocation().getLac());
				bean.setCellID(answer.getLocation().getCid());
				bean.setAccuracy(answer.getLocation().getAccuracy());
				bean.setIsGps(String.valueOf(answer.getLocation().getIsGps()));
				bean.setIsGsm(String.valueOf(answer.getLocation().getIsGsm()));
				bean.setGeolocationProvieder(answer.getLocation().getGeolocationProvider());
			}
			answerList.add(bean);
		}
		
		return answerList;
	}
	
	private List<TaskResultStagingBean> fromTaskD(long uuidTaskH, boolean isReadFromFinData) {
		List<TaskResultStagingBean> answerList = new ArrayList<TaskResultStagingBean>();
		
		//getData Detail
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("uuidTaskH", uuidTaskH);

		final String lovEntity = (isReadFromFinData) ? "msLovByFinLovId" : "msLovByLovId";
		Map<String, Object> result = this.getManagerDAO().list(
				"from TrTaskD ttd join fetch ttd.msQuestion mq join fetch mq.msAnswertype left join fetch ttd." + lovEntity +
				" where ttd.trTaskH.uuidTaskH=:uuidTaskH", paramMap);
		List<TrTaskD> list = (List) result.get(GlobalKey.MAP_RESULT_LIST);

		if (!list.isEmpty()) {
			for (TrTaskD trTaskD : list) {
//				2018-02-01 Penjagaan resurvey ganti relevant, record taskD ada tapi tidak diisi
				if (trTaskD.getTextAnswer() == null && trTaskD.getOptionText() == null)
					continue;
				
				String textAnswer = (isReadFromFinData) ? trTaskD.getFinTextAnswer() : trTaskD.getTextAnswer();
				String optionText = (isReadFromFinData) ? trTaskD.getFinOptionText() : trTaskD.getOptionText();
				
				MsLov lovAnswer = (isReadFromFinData) ? trTaskD.getMsLovByFinLovId() : trTaskD.getMsLovByLovId();				
				String lovGroup = (lovAnswer == null) ? null : lovAnswer.getLovGroup();
				String lovCode = (lovAnswer == null) ? null : lovAnswer.getCode();
				
				TaskResultStagingBean bean = new TaskResultStagingBean();
				
				bean.setIdentifier(trTaskD.getMsQuestion().getRefId());
				bean.setAnswerTypeCode(trTaskD.getMsQuestion().getMsAnswertype().getCodeAnswerType());
				bean.setAnswerTypeDesc(trTaskD.getMsQuestion().getMsAnswertype().getAnswerTypeName());
				bean.setQuestionText(trTaskD.getQuestionText());
				bean.setTextAnswer(textAnswer);
				bean.setLovGroup(lovGroup);
				bean.setLovID(lovCode);
				bean.setOptionText(optionText);
				bean.setLongitude(trTaskD.getLongitude());
				bean.setLatitude(trTaskD.getLatitude());
				bean.setMnc(trTaskD.getMnc());
				bean.setMcc(trTaskD.getMcc());
				bean.setLac(trTaskD.getLac());
				bean.setCellID(trTaskD.getCellId());
				bean.setAccuracy(trTaskD.getAccuracy());
				bean.setIsGps(trTaskD.getIsGps());
				bean.setIsGsm(trTaskD.getIsGsm());
				bean.setGeolocationProvieder(trTaskD.getGeolocationProvider());
				answerList.add(bean);
			}
		}
		
		return answerList;
	}
	
	//Get Data Save Result
	private TaskResultToStaging getTaskResult(String taskId, String subsystemCode, AuditContext auditContext) {		
		TaskResultToStaging resultBean = new TaskResultToStaging();
		List<TaskResultStagingBean> answerList = new ArrayList<TaskResultStagingBean>();
			
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"from TrTaskH tth join fetch tth.msStatustask join fetch tth.msForm join fetch tth.msBranch "
						+ "where tth.taskId = :taskId", new Object[][] {{"taskId", taskId}});
		//Check Status Task
		LOG.info("STATUS TASK : [{}] - {}, WITH TASK ID : {}", trTaskH.getMsStatustask().getStatusCode(), trTaskH.getMsStatustask().getStatusTaskDesc(), taskId);
		if (!GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(trTaskH.getMsStatustask().getStatusCode()))
			return null;
		
		AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, trTaskH.getAmMsuser().getUuidMsUser());
		
		boolean isReadFromFinData = this.isWfHasVerification(trTaskH.getUuidTaskH());
		if (PropertiesHelper.isTaskDJson()) {
			List<TaskResultStagingBean> answerTextList = this.fromJson(trTaskH.getUuidTaskH(), isReadFromFinData);
			answerList.addAll(answerTextList);
		}
		else {
			List<TaskResultStagingBean> answerTextList = this.fromTaskD(trTaskH.getUuidTaskH(), isReadFromFinData);
			answerList.addAll(answerTextList);
		}
		
		//getData Image
		Map<String, Object> resultLOB = this.getManagerDAO().list(
				"from TrTaskdetaillob lob join fetch lob.msQuestion mq where 1=1"
						+ " and lob.trTaskH.uuidTaskH=:uuidTaskH", new Object[][]{{"uuidTaskH", trTaskH.getUuidTaskH()}});
		
		ImageStorageLocation isl = imageStorageLogic.retrieveGsImageLocation(auditContext);
		List<TrTaskdetaillob> listLOB = (List) resultLOB.get(GlobalKey.MAP_RESULT_LIST);
		if (!listLOB.isEmpty()) {
			for (TrTaskdetaillob trTaskDetailLOB : listLOB) {					
				String lovGroup = (trTaskDetailLOB.getMsLovByLovId() == null) ? null : trTaskDetailLOB.getMsLovByLovId().getLovGroup();
				String lovCode = (trTaskDetailLOB.getMsLovByLovId() == null) ? null : trTaskDetailLOB.getMsLovByLovId().getCode();
				
				TaskResultStagingBean bean = new TaskResultStagingBean();
				
				bean.setIdentifier(trTaskDetailLOB.getMsQuestion().getRefId());
				bean.setQuestionText(trTaskDetailLOB.getQuestionText());
				bean.setTextAnswer(trTaskDetailLOB.getTextAnswer());
				bean.setLovGroup(lovGroup);
				bean.setLovID(lovCode);
				bean.setOptionText(trTaskDetailLOB.getOptionText());
				bean.setLongitude(trTaskDetailLOB.getLongitude());
				bean.setLatitude(trTaskDetailLOB.getLatitude());
				bean.setMnc(trTaskDetailLOB.getMnc());
				bean.setMcc(trTaskDetailLOB.getMcc());
				bean.setLac(trTaskDetailLOB.getLac());
				bean.setCellID(trTaskDetailLOB.getCellId());
				bean.setAccuracy(trTaskDetailLOB.getAccuracy());
				bean.setIsGps(trTaskDetailLOB.getIsGps());
				bean.setIsGsm(trTaskDetailLOB.getIsGsm());
				bean.setGeolocationProvieder(trTaskDetailLOB.getGeolocationProvider());
				if (trTaskDetailLOB.getLobFile() != null)
					bean.setImage(BaseEncoding.base64().encode(trTaskDetailLOB.getLobFile()));
				else if (StringUtils.isNotBlank(trTaskDetailLOB.getImagePath()) && isl == ImageStorageLocation.FILE_SYSTEM) {
					byte[] image = this.imageStorageLogic.retrieveImageFileSystemByFile(new File(trTaskDetailLOB.getImagePath()));
					bean.setImage(BaseEncoding.base64().encode(image));
				}					
				answerList.add(bean);
			}
		}
		
		//getData Header
		resultBean.setTaskBranchCode(trTaskH.getMsBranch().getBranchCode());
		resultBean.setZipcode(trTaskH.getZipCode());
		resultBean.setFormName(trTaskH.getMsForm().getFormName());
		resultBean.setCustomerName(trTaskH.getCustomerName());
		resultBean.setCustomerAddress(trTaskH.getCustomerAddress());
		resultBean.setCustomerPhone(trTaskH.getCustomerPhone());
		resultBean.setNotes(trTaskH.getNotes());
		resultBean.setApplNo(trTaskH.getApplNo());
		resultBean.setAgreementNo(trTaskH.getAgreementNo());
		resultBean.setSubsystemCode(subsystemCode);
		resultBean.setTaskId(taskId);
		resultBean.setRetrieveDate(toDate(trTaskH.getAssignDate(), DATE_FORMAT));
		resultBean.setSubmitDate(toDate(trTaskH.getSubmitDate(), DATE_FORMAT));
		resultBean.setFieldPerson(amMsUser.getLoginId());
		resultBean.setResult(GlobalVal.SERVICES_RESULT_SUCCESS);
		resultBean.setAnswerList(answerList);
		resultBean.setLatitude(trTaskH.getLatitude());
		resultBean.setLongitude(trTaskH.getLongitude());
		
		return resultBean;
	}
	
	private String toDate(Object obj, String dateformat){
		String s = (null != obj) 
				? new SimpleDateFormat(dateformat).format(obj)
				: null;
		return s;
	}
	
	private String toNumberFormatted(Object obj) {
	    if (obj == null)
	        return null;
	    
	    if (!NumberUtils.isNumber(String.valueOf(obj)))
	        return null;
	    
	    return FormatterUtils.getNumberFormat(FormatterUtils.NFORMAT_US_2).format(obj);
	}

	@Override
	public Map getPathApp(String isIPPublic) {
		Map result = new HashMap();
		String webPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE);
		String jspPath = StringUtils.EMPTY;
		if ("1".equals(isIPPublic)) {
			jspPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE_JSP_PUBLIC);
		}
		else {
			jspPath = SpringPropertiesUtils.getProperty(GlobalKey.APP_BASE_JSP);
		}
		result.put("WEB", webPath);
		result.put("JSP", jspPath);
		return result;
	}
	
	//tambahan luOnline
	@Override
	@Transactional(readOnly = true)
	public OnlineLuResponse luOnline(String refId,String lovGroup,String searchVal,
			String choiceFilterVal,AuditContext callerId){
		return this.luOnlineLogic.luOnline(refId, lovGroup, searchVal, choiceFilterVal, callerId);
	}	

	@Override
	@Transactional(readOnly = true)
	public boolean validateLuOnlineCode(String code, String lovGroup, AuditContext callerId) {		
		return this.luOnlineLogic.validateLuOnlineCode(code, lovGroup, callerId);
	}

	@Override
	public String resetPassword(String loginId, Date dob, String email,
			String ktpNo, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String changePassword(String loginId, String oldPassword,
			String newPassword, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String insertLossDeal(AuditContext auditContext, String taskId,
			String isFinal) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitNegativeList(AuditContext auditContext, String taskId,
			String isFinal, String isRecommendation) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ViewImageResponseBean viewImageDMS(AuditContext auditContext,
			ViewImageRequestBean viewImageRequestBean) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String uploadImageResponse(AuditContext auditContext,
			UploadImageRequestBean uploadImageRequestBean) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitNap(AuditContext auditContext, String taskId,
			String isFinal) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public String submitTaskUpdate(AuditContext auditContext, String taskUpdateId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public InstantApprovalResponse submitInstantApproval(AuditContext auditContext, String taskId, String isFinal) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map requestOCR(String imagePath, String type, Boolean isPilotingCAE, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public List<String> getPendingTaskId() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public TaxPersonalResponse verifyTaxPersonal(AuditContext audit, String nik, String npwp, String income) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map checkTeleStatus(AuditContext audit, String phoneNumber, int phoneOwner, Boolean isPilotingCae, String taskIdPolo, Integer isPreApproval) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public MssResponseType teleStatusCheckCallback(TeleStatusCheckCallbackRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean requestTeleowner(String idNumber, String phoneNumber, Boolean isPilotingCAE, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public Map validatePage1(SubmitPage1Request submitData, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map validatePage2(SubmitPage2Request submitData, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map preparation(TrTaskH task, String flagStatus, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public ValidateBiometrikResponse validateBiometric(AuditContext auditContext, String nik, String name, String birthdate, String birthplace,
			String address, String identity_photo, String selfie_photo) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<String> getPendingPreparationTaskIds() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CIFBean revampDukcapil(AuditContext auditContext, String nik, String name, String birthPlace,
			String birthDate, String userId, String password, String ipUser) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CheckDukcapilResponse checkDukcapil(AuditContext auditContext, String isPilotingCAE, String nik, String name, String birthPlace, String birthDate, String userId, String password, String ipUser, CIFBean cifBean, String taskIdPolo, Integer isPreApproval) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public UpdateDataPoloResponse updateDataPolo(UpdateDataPoloRequest request, TrTaskH trTaskH, String statusMss,
			String status, String flagVoidSla, String negativeCustomer, String reasonReject, String flagAutoDelete, String reasonDelete, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public SubmitNegativeCustPoloResponse submitNegativeCustomerPoloToWise(SubmitNegativeCustPoloRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CheckStatusTaskResponse getStatusProspectPolo(CheckStatusTaskRequest req) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void checkProspekPoloAsync(AuditContext callerId) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public Map submitPreIA(SubmitPreIARequest submitData, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public UpdateStatusMainDealerResponse updateStatusTaskMainDealer(TrTaskH taskH, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public MssResponseType GenerateInvitationLink(GenerateInvitationRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public SendDataPhotoToApiResponse uploadIdCard(String base64, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitNapPilotingCae(AuditContext auditContext, String taskId, String isFinal) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitPolo(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, String taskType, String isPreOffline, AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean checkNegativeCust(String idNumber, String taskIdPolo, String name, String birthPlace, String birthDate, String motherName, Integer isPreApproval, String contractNo,AuditContext auditContext) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public Map checkSlikCAE(String nik, String fullName, String birthdate, String birthplace, String mothername, 
			String nikPsgn, String fullNamePsgn, String birthdatePsgn, String birthplacePsgn, String mothernamePsgn, 
			String nikGrntr, String fullNameGrntr, String birthdateGrntr, String birthplaceGrntr, String mothernameGrntr, String ntf, 
			String officeCode, String officeRegionCode, String productOfferingCode, String callMode, String contractNo, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitTaskGuarantor(AuditContext auditContext, TrTaskH taskGuarantor) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map checkIncome(String nik, String income, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitTaskOTS(AuditContext auditContext, String flagSourceOts, TrTaskH taskOTS) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public GetDsrFromPoloResponse getDsrFromPolo(Map<String, String> paramApi, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String updateMobileAssignmentId(String mobileTaskId, String newAppNo, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public GetReferantorResponse getReferantorCodeFromNc(String uniqueId, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String updateFromMssCopyApp(String newTaskIdPolo, String orderNo, String groupTaskId, String uuidTaskH,
			AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String submitDocupro(AuditContext auditContext, TrTaskH trTaskH, SubmitNapRequest bean) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<String> getAutoSubmitTaskId(AmGeneralsetting maxRetry, AmGeneralsetting duration,
			AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getHistoryCreditKBIJ(String request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public OnlineLuResponse luOnlineMaskapaiAsuransi(String refId, String lovGroup, String searchVal, String choiceFilterVal,
			AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Map checkBiometricCae(String officeCode, String officeRegionCode, String productOfferingCode, String nik,
			String selfiePhoto, String nikPsgn, String selfiePhotoPsgn, String nikGrtr, String selfiePhotoGrtr,
			String taskIdPolo, String contractNo, Integer isPreApproval, String includeSpouse, String includeGuarantor,
			AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}
 

	@Override
	public String updateStatusWiseIDE(TrTaskH trTaskH, String type, AmMsuser loginBean, TblApiDashboard tblBean,
			AuditContext callerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CheckESignRegistrationResponse CheckEsignRegistrationStatus(
			CheckEsignRegistrationRequest checkEsignRequest) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public CheckOtrValueResponse GetOtrValue(String officeCode, String assetCode, String manufYear) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void updateStatusIDE(TrTaskH trTaskH, String status, String type, AmMsuser loginBean,
			TblApiDashboard tblBean, AuditContext callerId) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public UpdateDataPoloResponse updateDataPoloAsync(UpdateDataPoloRequest request, TrTaskH trTaskH, String statusMss,
			String status, String flagVoidSla, String negativeCustomer, AuditContext auditContext) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public VerifyReferantorResponse VerifyReferantor(String taskId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void updateDataPoloLayer(String json, TrTaskH trTaskH, AuditContext callerId) {
		// TODO Auto-generated method stub
	}
}
