package com.adins.mss.function;

import java.io.BufferedReader;
import java.sql.Clob;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import javax.xml.bind.ParseConversionEvent;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.services.model.common.AddTaskCAERequest;
import com.adins.mss.services.model.common.AddTaskPoloRequest;
import com.adins.mss.services.model.common.AddTaskScoringCAERequest;
import com.adins.mss.services.model.common.KbijData;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class GenericKbijDataLogic extends BaseLogic implements KbijDataLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericKbijDataLogic.class);
	private Gson gson = new GsonBuilder().serializeNulls().create();

	@Override
	public AddTaskCAERequest formatDataKBIJ_addTaskCAE(AddTaskCAERequest req) {
		AddTaskCAERequest result = req;
		
		LOG.info("Data KBIJ Format Correction...");
		
		//Kolektibilitas / Jumlah Kontrak / Baki Debet
		String[] collectibilityList = new String[15];
		collectibilityList[0] = result.getCollectibility1_JumlahKontrak1_BakiDebet1();
		collectibilityList[1] = result.getCollectibility2_JumlahKontrak2_BakiDebet2();
		collectibilityList[2] = result.getCollectibility3_JumlahKontrak3_BakiDebet3();
		collectibilityList[3] = result.getCollectibility4_JumlahKontrak4_BakiDebet4();
		collectibilityList[4] = result.getCollectibility5_JumlahKontrak5_BakiDebet5();
		collectibilityList[5] = result.getCollectibility1_JumlahKontrak1_BakiDebet1_Psgn();
		collectibilityList[6] = result.getCollectibility2_JumlahKontrak2_BakiDebet2_Psgn();
		collectibilityList[7] = result.getCollectibility3_JumlahKontrak3_BakiDebet3_Psgn();
		collectibilityList[8] = result.getCollectibility4_JumlahKontrak4_BakiDebet4_Psgn();
		collectibilityList[9] = result.getCollectibility5_JumlahKontrak5_BakiDebet5_Psgn();
		collectibilityList[10] = result.getCollectibility1_JumlahKontrak1_BakiDebet1_Grtr();
		collectibilityList[11] = result.getCollectibility2_JumlahKontrak2_BakiDebet2_Grtr();
		collectibilityList[12] = result.getCollectibility3_JumlahKontrak3_BakiDebet3_Grtr();
		collectibilityList[13] = result.getCollectibility4_JumlahKontrak4_BakiDebet4_Grtr();
		collectibilityList[14] = result.getCollectibility5_JumlahKontrak5_BakiDebet5_Grtr();
		for(int i = 0; i < collectibilityList.length; i++) {
			String collectibility = collectibilityList[i];
			collectibilityList[i] = formatDataKBIJ_Collectibility(collectibility);
		}
		result.setCollectibility1_JumlahKontrak1_BakiDebet1(collectibilityList[0]);
		result.setCollectibility2_JumlahKontrak2_BakiDebet2(collectibilityList[1]);
		result.setCollectibility3_JumlahKontrak3_BakiDebet3(collectibilityList[2]);
		result.setCollectibility4_JumlahKontrak4_BakiDebet4(collectibilityList[3]);
		result.setCollectibility5_JumlahKontrak5_BakiDebet5(collectibilityList[4]);
		result.setCollectibility1_JumlahKontrak1_BakiDebet1_Psgn(collectibilityList[5]);
		result.setCollectibility2_JumlahKontrak2_BakiDebet2_Psgn(collectibilityList[6]);
		result.setCollectibility3_JumlahKontrak3_BakiDebet3_Psgn(collectibilityList[7]);
		result.setCollectibility4_JumlahKontrak4_BakiDebet4_Psgn(collectibilityList[8]);
		result.setCollectibility5_JumlahKontrak5_BakiDebet5_Psgn(collectibilityList[9]);
		result.setCollectibility1_JumlahKontrak1_BakiDebet1_Grtr(collectibilityList[10]);
		result.setCollectibility2_JumlahKontrak2_BakiDebet2_Grtr(collectibilityList[11]);
		result.setCollectibility3_JumlahKontrak3_BakiDebet3_Grtr(collectibilityList[12]);
		result.setCollectibility4_JumlahKontrak4_BakiDebet4_Grtr(collectibilityList[13]);
		result.setCollectibility5_JumlahKontrak5_BakiDebet5_Grtr(collectibilityList[14]);
		
		//Maks DPD/ Baki Debet/ Plafon
		String[] maxDpd_BakiDebet_PlafonList = new String[3];
		maxDpd_BakiDebet_PlafonList[0] = result.getMaxDpd_BakiDebet_Plafon(); 
		maxDpd_BakiDebet_PlafonList[1] = result.getMaxDpd_BakiDebet_Plafon_Psgn();
		maxDpd_BakiDebet_PlafonList[2] = result.getMaxDpd_BakiDebet_Plafon_Grtr();
		for(int i = 0; i < maxDpd_BakiDebet_PlafonList.length; i++) {
			String maxDpd_BakiDebet_Plafon = maxDpd_BakiDebet_PlafonList[i];
			maxDpd_BakiDebet_PlafonList[i] = formatDataKBIJ_MaxDpd_BakiDebet_Plafon(maxDpd_BakiDebet_Plafon);
		}
		result.setMaxDpd_BakiDebet_Plafon(maxDpd_BakiDebet_PlafonList[0]);
		result.setMaxDpd_BakiDebet_Plafon_Psgn(maxDpd_BakiDebet_PlafonList[1]);
		result.setMaxDpd_BakiDebet_Plafon_Grtr(maxDpd_BakiDebet_PlafonList[2]);
		
		//Baki Debet Terbesar/ Plafon Awal/ Latest DPD
		String[] maxBakiDebet_PlafonAwal_LatestDPDList = new String[3];
		maxBakiDebet_PlafonAwal_LatestDPDList[0] = result.getMaxBakiDebet_PlafonAwal_LatestDPD(); 
		maxBakiDebet_PlafonAwal_LatestDPDList[1] = result.getMaxBakiDebet_PlafonAwal_LatestDPD_Psgn();
		maxBakiDebet_PlafonAwal_LatestDPDList[2] = result.getMaxBakiDebet_PlafonAwal_LatestDPD_Grtr();
		for(int i = 0; i < maxBakiDebet_PlafonAwal_LatestDPDList.length; i++) {
			String maxBakiDebet_PlafonAwal_LatestDPD = maxBakiDebet_PlafonAwal_LatestDPDList[i];
			maxBakiDebet_PlafonAwal_LatestDPDList[i] = formatDataKBIJ_MaxBakiDebet_PlafonAwal_LatestDPD(maxBakiDebet_PlafonAwal_LatestDPD);
		}
		result.setMaxBakiDebet_PlafonAwal_LatestDPD(maxBakiDebet_PlafonAwal_LatestDPDList[0]);
		result.setMaxBakiDebet_PlafonAwal_LatestDPD_Psgn(maxBakiDebet_PlafonAwal_LatestDPDList[1]);
		result.setMaxBakiDebet_PlafonAwal_LatestDPD_Grtr(maxBakiDebet_PlafonAwal_LatestDPDList[2]);
		
		//Total Kontrak/ Plafon
		String[] totalKontrak_PlafonList = new String[3];
		totalKontrak_PlafonList[0] = result.getTotalKontrak_Plafon();
		totalKontrak_PlafonList[1] = result.getTotalKontrak_Plafon_Psgn();
		totalKontrak_PlafonList[2] = result.getTotalKontrak_Plafon_Grtr();
		for(int i = 0; i < totalKontrak_PlafonList.length; i++) {
			String totalKontrak_Plafon = totalKontrak_PlafonList[i];
			totalKontrak_PlafonList[i] = formatDataKBIJ_TotalKontrak_Plafon(totalKontrak_Plafon);
		}
		result.setTotalKontrak_Plafon(totalKontrak_PlafonList[0]);
		result.setTotalKontrak_Plafon_Psgn(totalKontrak_PlafonList[1]);
		result.setTotalKontrak_Plafon_Grtr(totalKontrak_PlafonList[2]);
		
		//Maks DPD/ Plafon
		String[] maxDPD_PlafonList = new String[3];
		maxDPD_PlafonList[0] = result.getMaxDPD_Plafon();
		maxDPD_PlafonList[1] = result.getMaxDPD_Plafon_Psgn();
		maxDPD_PlafonList[2] = result.getMaxDPD_Plafon_Grtr();
		for(int i = 0; i < maxDPD_PlafonList.length; i++) {
			String maxDPD_Plafon = maxDPD_PlafonList[i];
			maxDPD_PlafonList[i] = formatDataKBIJ_MaxDPD_Plafon(maxDPD_Plafon);
		}
		result.setMaxDPD_Plafon(maxDPD_PlafonList[0]);
		result.setMaxDPD_Plafon_Psgn(maxDPD_PlafonList[1]);
		result.setMaxDPD_Plafon_Grtr(maxDPD_PlafonList[2]);
		
		//Plafon Terbesar/ DPD
		String[] maxPlafon_DPDList = new String[3];
		maxPlafon_DPDList[0] = result.getMaxPlafon_DPD();
		maxPlafon_DPDList[1] = result.getMaxPlafon_DPD_Psgn();
		maxPlafon_DPDList[2] = result.getMaxPlafon_DPD_Grtr();
		for(int i = 0; i < maxPlafon_DPDList.length; i++) {
			String maxPlafon_DPD = maxPlafon_DPDList[i];
			maxPlafon_DPDList[i] = formatDataKBIJ_MaxPlafon_DPD(maxPlafon_DPD);
		} 
		result.setMaxPlafon_DPD(maxPlafon_DPDList[0]);
		result.setMaxPlafon_DPD_Psgn(maxPlafon_DPDList[1]);
		result.setMaxPlafon_DPD_Grtr(maxPlafon_DPDList[2]);
		
		//Reason
		result.setReason(formatDataKBIJ_Reason(result.getReason()));
		result.setReasonPsgn(formatDataKBIJ_Reason(result.getReasonPsgn()));
		result.setReasonGrtr(formatDataKBIJ_Reason(result.getReasonGrtr()));
		
		//Job Code
		result.setJobCodeIA(formatDataKBIJ_jobCode(result.getJobCodeIA(), result.getOpsiPenanganan()));
		
		//Opsi Penanganan
		result.setOpsiPenanganan(formatDataKBIJ_OpsiPenanganan(result.getOpsiPenanganan()));
		
		return result;
	}
	
	@Override
	public AddTaskScoringCAERequest formatDataKBIJ_addTaskScoring(AddTaskScoringCAERequest req) {
		/*-- Copy Paste from formatDataKBIJ --*/
		AddTaskScoringCAERequest result = req;
		
		LOG.info("Data KBIJ Format Correction...");
		
		//Kolektibilitas / Jumlah Kontrak / Baki Debet
		String[] collectibilityList = new String[15];
		collectibilityList[0] = result.getCollectibility1_JumlahKontrak1_BakiDebet1();
		collectibilityList[1] = result.getCollectibility2_JumlahKontrak2_BakiDebet2();
		collectibilityList[2] = result.getCollectibility3_JumlahKontrak3_BakiDebet3();
		collectibilityList[3] = result.getCollectibility4_JumlahKontrak4_BakiDebet4();
		collectibilityList[4] = result.getCollectibility5_JumlahKontrak5_BakiDebet5();
		collectibilityList[5] = result.getCollectibility1_JumlahKontrak1_BakiDebet1_Psgn();
		collectibilityList[6] = result.getCollectibility2_JumlahKontrak2_BakiDebet2_Psgn();
		collectibilityList[7] = result.getCollectibility3_JumlahKontrak3_BakiDebet3_Psgn();
		collectibilityList[8] = result.getCollectibility4_JumlahKontrak4_BakiDebet4_Psgn();
		collectibilityList[9] = result.getCollectibility5_JumlahKontrak5_BakiDebet5_Psgn();
		collectibilityList[10] = result.getCollectibility1_JumlahKontrak1_BakiDebet1_Grtr();
		collectibilityList[11] = result.getCollectibility2_JumlahKontrak2_BakiDebet2_Grtr();
		collectibilityList[12] = result.getCollectibility3_JumlahKontrak3_BakiDebet3_Grtr();
		collectibilityList[13] = result.getCollectibility4_JumlahKontrak4_BakiDebet4_Grtr();
		collectibilityList[14] = result.getCollectibility5_JumlahKontrak5_BakiDebet5_Grtr();
		for(int i = 0; i < collectibilityList.length; i++) {
			String collectibility = collectibilityList[i];
			collectibilityList[i] = formatDataKBIJ_Collectibility(collectibility);
		}
		result.setCollectibility1_JumlahKontrak1_BakiDebet1(collectibilityList[0]);
		result.setCollectibility2_JumlahKontrak2_BakiDebet2(collectibilityList[1]);
		result.setCollectibility3_JumlahKontrak3_BakiDebet3(collectibilityList[2]);
		result.setCollectibility4_JumlahKontrak4_BakiDebet4(collectibilityList[3]);
		result.setCollectibility5_JumlahKontrak5_BakiDebet5(collectibilityList[4]);
		result.setCollectibility1_JumlahKontrak1_BakiDebet1_Psgn(collectibilityList[5]);
		result.setCollectibility2_JumlahKontrak2_BakiDebet2_Psgn(collectibilityList[6]);
		result.setCollectibility3_JumlahKontrak3_BakiDebet3_Psgn(collectibilityList[7]);
		result.setCollectibility4_JumlahKontrak4_BakiDebet4_Psgn(collectibilityList[8]);
		result.setCollectibility5_JumlahKontrak5_BakiDebet5_Psgn(collectibilityList[9]);
		result.setCollectibility1_JumlahKontrak1_BakiDebet1_Grtr(collectibilityList[10]);
		result.setCollectibility2_JumlahKontrak2_BakiDebet2_Grtr(collectibilityList[11]);
		result.setCollectibility3_JumlahKontrak3_BakiDebet3_Grtr(collectibilityList[12]);
		result.setCollectibility4_JumlahKontrak4_BakiDebet4_Grtr(collectibilityList[13]);
		result.setCollectibility5_JumlahKontrak5_BakiDebet5_Grtr(collectibilityList[14]);
		
		//Maks DPD/ Baki Debet/ Plafon
		String[] maxDpd_BakiDebet_PlafonList = new String[3];
		maxDpd_BakiDebet_PlafonList[0] = result.getMaxDpd_BakiDebet_Plafon(); 
		maxDpd_BakiDebet_PlafonList[1] = result.getMaxDpd_BakiDebet_Plafon_Psgn();
		maxDpd_BakiDebet_PlafonList[2] = result.getMaxDpd_BakiDebet_Plafon_Grtr();
		for(int i = 0; i < maxDpd_BakiDebet_PlafonList.length; i++) {
			String maxDpd_BakiDebet_Plafon = maxDpd_BakiDebet_PlafonList[i];
			maxDpd_BakiDebet_PlafonList[i] = formatDataKBIJ_MaxDpd_BakiDebet_Plafon(maxDpd_BakiDebet_Plafon);
		}
		result.setMaxDpd_BakiDebet_Plafon(maxDpd_BakiDebet_PlafonList[0]);
		result.setMaxDpd_BakiDebet_Plafon_Psgn(maxDpd_BakiDebet_PlafonList[1]);
		result.setMaxDpd_BakiDebet_Plafon_Grtr(maxDpd_BakiDebet_PlafonList[2]);
		
		//Baki Debet Terbesar/ Plafon Awal/ Latest DPD
		String[] maxBakiDebet_PlafonAwal_LatestDPDList = new String[3];
		maxBakiDebet_PlafonAwal_LatestDPDList[0] = result.getMaxBakiDebet_PlafonAwal_LatestDPD(); 
		maxBakiDebet_PlafonAwal_LatestDPDList[1] = result.getMaxBakiDebet_PlafonAwal_LatestDPD_Psgn();
		maxBakiDebet_PlafonAwal_LatestDPDList[2] = result.getMaxBakiDebet_PlafonAwal_LatestDPD_Grtr();
		for(int i = 0; i < maxBakiDebet_PlafonAwal_LatestDPDList.length; i++) {
			String maxBakiDebet_PlafonAwal_LatestDPD = maxBakiDebet_PlafonAwal_LatestDPDList[i];
			maxBakiDebet_PlafonAwal_LatestDPDList[i] = formatDataKBIJ_MaxBakiDebet_PlafonAwal_LatestDPD(maxBakiDebet_PlafonAwal_LatestDPD);
		}
		result.setMaxBakiDebet_PlafonAwal_LatestDPD(maxBakiDebet_PlafonAwal_LatestDPDList[0]);
		result.setMaxBakiDebet_PlafonAwal_LatestDPD_Psgn(maxBakiDebet_PlafonAwal_LatestDPDList[1]);
		result.setMaxBakiDebet_PlafonAwal_LatestDPD_Grtr(maxBakiDebet_PlafonAwal_LatestDPDList[2]);
		
		//Total Kontrak/ Plafon
		String[] totalKontrak_PlafonList = new String[3];
		totalKontrak_PlafonList[0] = result.getTotalKontrak_Plafon();
		totalKontrak_PlafonList[1] = result.getTotalKontrak_Plafon_Psgn();
		totalKontrak_PlafonList[2] = result.getTotalKontrak_Plafon_Grtr();
		for(int i = 0; i < totalKontrak_PlafonList.length; i++) {
			String totalKontrak_Plafon = totalKontrak_PlafonList[i];
			totalKontrak_PlafonList[i] = formatDataKBIJ_TotalKontrak_Plafon(totalKontrak_Plafon);
		}
		result.setTotalKontrak_Plafon(totalKontrak_PlafonList[0]);
		result.setTotalKontrak_Plafon_Psgn(totalKontrak_PlafonList[1]);
		result.setTotalKontrak_Plafon_Grtr(totalKontrak_PlafonList[2]);
		
		//Maks DPD/ Plafon
		String[] maxDPD_PlafonList = new String[3];
		maxDPD_PlafonList[0] = result.getMaxDPD_Plafon();
		maxDPD_PlafonList[1] = result.getMaxDPD_Plafon_Psgn();
		maxDPD_PlafonList[2] = result.getMaxDPD_Plafon_Grtr();
		for(int i = 0; i < maxDPD_PlafonList.length; i++) {
			String maxDPD_Plafon = maxDPD_PlafonList[i];
			maxDPD_PlafonList[i] = formatDataKBIJ_MaxDPD_Plafon(maxDPD_Plafon);
		}
		result.setMaxDPD_Plafon(maxDPD_PlafonList[0]);
		result.setMaxDPD_Plafon_Psgn(maxDPD_PlafonList[1]);
		result.setMaxDPD_Plafon_Grtr(maxDPD_PlafonList[2]);
		
		//Plafon Terbesar/ DPD
		String[] maxPlafon_DPDList = new String[3];
		maxPlafon_DPDList[0] = result.getMaxPlafon_DPD();
		maxPlafon_DPDList[1] = result.getMaxPlafon_DPD_Psgn();
		maxPlafon_DPDList[2] = result.getMaxPlafon_DPD_Grtr();
		for(int i = 0; i < maxPlafon_DPDList.length; i++) {
			String maxPlafon_DPD = maxPlafon_DPDList[i];
			maxPlafon_DPDList[i] = formatDataKBIJ_MaxPlafon_DPD(maxPlafon_DPD);
		} 
		result.setMaxPlafon_DPD(maxPlafon_DPDList[0]);
		result.setMaxPlafon_DPD_Psgn(maxPlafon_DPDList[1]);
		result.setMaxPlafon_DPD_Grtr(maxPlafon_DPDList[2]);
		
		//Reason
		result.setReason(formatDataKBIJ_Reason(result.getReason()));
		result.setReasonPsgn(formatDataKBIJ_Reason(result.getReasonPsgn()));
		result.setReasonGrtr(formatDataKBIJ_Reason(result.getReasonGrtr()));
		
		return result;
	}
	
	@Override
	public KbijData formatDataKBIJ(KbijData dataKbij) {
		/*-- Copy Paste from formatDataKBIJ --*/
		KbijData result = dataKbij;
		
		LOG.info("Data KBIJ Format Correction...");
		
		//Kolektibilitas / Jumlah Kontrak / Baki Debet
		String[] collectibilityList = new String[15];
		collectibilityList[0] = result.getCollectibility1_JumlahKontrak1_BakiDebet1();
		collectibilityList[1] = result.getCollectibility2_JumlahKontrak2_BakiDebet2();
		collectibilityList[2] = result.getCollectibility3_JumlahKontrak3_BakiDebet3();
		collectibilityList[3] = result.getCollectibility4_JumlahKontrak4_BakiDebet4();
		collectibilityList[4] = result.getCollectibility5_JumlahKontrak5_BakiDebet5();
		collectibilityList[5] = result.getCollectibility1_JumlahKontrak1_BakiDebet1_Psgn();
		collectibilityList[6] = result.getCollectibility2_JumlahKontrak2_BakiDebet2_Psgn();
		collectibilityList[7] = result.getCollectibility3_JumlahKontrak3_BakiDebet3_Psgn();
		collectibilityList[8] = result.getCollectibility4_JumlahKontrak4_BakiDebet4_Psgn();
		collectibilityList[9] = result.getCollectibility5_JumlahKontrak5_BakiDebet5_Psgn();
		collectibilityList[10] = result.getCollectibility1_JumlahKontrak1_BakiDebet1_Grtr();
		collectibilityList[11] = result.getCollectibility2_JumlahKontrak2_BakiDebet2_Grtr();
		collectibilityList[12] = result.getCollectibility3_JumlahKontrak3_BakiDebet3_Grtr();
		collectibilityList[13] = result.getCollectibility4_JumlahKontrak4_BakiDebet4_Grtr();
		collectibilityList[14] = result.getCollectibility5_JumlahKontrak5_BakiDebet5_Grtr();
		for(int i = 0; i < collectibilityList.length; i++) {
			String collectibility = collectibilityList[i];
			collectibilityList[i] = formatDataKBIJ_Collectibility(collectibility);
		}
		result.setCollectibility1_JumlahKontrak1_BakiDebet1(collectibilityList[0]);
		result.setCollectibility2_JumlahKontrak2_BakiDebet2(collectibilityList[1]);
		result.setCollectibility3_JumlahKontrak3_BakiDebet3(collectibilityList[2]);
		result.setCollectibility4_JumlahKontrak4_BakiDebet4(collectibilityList[3]);
		result.setCollectibility5_JumlahKontrak5_BakiDebet5(collectibilityList[4]);
		result.setCollectibility1_JumlahKontrak1_BakiDebet1_Psgn(collectibilityList[5]);
		result.setCollectibility2_JumlahKontrak2_BakiDebet2_Psgn(collectibilityList[6]);
		result.setCollectibility3_JumlahKontrak3_BakiDebet3_Psgn(collectibilityList[7]);
		result.setCollectibility4_JumlahKontrak4_BakiDebet4_Psgn(collectibilityList[8]);
		result.setCollectibility5_JumlahKontrak5_BakiDebet5_Psgn(collectibilityList[9]);
		result.setCollectibility1_JumlahKontrak1_BakiDebet1_Grtr(collectibilityList[10]);
		result.setCollectibility2_JumlahKontrak2_BakiDebet2_Grtr(collectibilityList[11]);
		result.setCollectibility3_JumlahKontrak3_BakiDebet3_Grtr(collectibilityList[12]);
		result.setCollectibility4_JumlahKontrak4_BakiDebet4_Grtr(collectibilityList[13]);
		result.setCollectibility5_JumlahKontrak5_BakiDebet5_Grtr(collectibilityList[14]);
		
		//Maks DPD/ Baki Debet/ Plafon
		String[] maxDpd_BakiDebet_PlafonList = new String[3];
		maxDpd_BakiDebet_PlafonList[0] = result.getMaxDpd_BakiDebet_Plafon(); 
		maxDpd_BakiDebet_PlafonList[1] = result.getMaxDpd_BakiDebet_Plafon_Psgn();
		maxDpd_BakiDebet_PlafonList[2] = result.getMaxDpd_BakiDebet_Plafon_Grtr();
		for(int i = 0; i < maxDpd_BakiDebet_PlafonList.length; i++) {
			String maxDpd_BakiDebet_Plafon = maxDpd_BakiDebet_PlafonList[i];
			maxDpd_BakiDebet_PlafonList[i] = formatDataKBIJ_MaxDpd_BakiDebet_Plafon(maxDpd_BakiDebet_Plafon);
		}
		result.setMaxDpd_BakiDebet_Plafon(maxDpd_BakiDebet_PlafonList[0]);
		result.setMaxDpd_BakiDebet_Plafon_Psgn(maxDpd_BakiDebet_PlafonList[1]);
		result.setMaxDpd_BakiDebet_Plafon_Grtr(maxDpd_BakiDebet_PlafonList[2]);
		
		//Baki Debet Terbesar/ Plafon Awal/ Latest DPD
		String[] maxBakiDebet_PlafonAwal_LatestDPDList = new String[3];
		maxBakiDebet_PlafonAwal_LatestDPDList[0] = result.getMaxBakiDebet_PlafonAwal_LatestDPD(); 
		maxBakiDebet_PlafonAwal_LatestDPDList[1] = result.getMaxBakiDebet_PlafonAwal_LatestDPD_Psgn();
		maxBakiDebet_PlafonAwal_LatestDPDList[2] = result.getMaxBakiDebet_PlafonAwal_LatestDPD_Grtr();
		for(int i = 0; i < maxBakiDebet_PlafonAwal_LatestDPDList.length; i++) {
			String maxBakiDebet_PlafonAwal_LatestDPD = maxBakiDebet_PlafonAwal_LatestDPDList[i];
			maxBakiDebet_PlafonAwal_LatestDPDList[i] = formatDataKBIJ_MaxBakiDebet_PlafonAwal_LatestDPD(maxBakiDebet_PlafonAwal_LatestDPD);
		}
		result.setMaxBakiDebet_PlafonAwal_LatestDPD(maxBakiDebet_PlafonAwal_LatestDPDList[0]);
		result.setMaxBakiDebet_PlafonAwal_LatestDPD_Psgn(maxBakiDebet_PlafonAwal_LatestDPDList[1]);
		result.setMaxBakiDebet_PlafonAwal_LatestDPD_Grtr(maxBakiDebet_PlafonAwal_LatestDPDList[2]);
		
		//Total Kontrak/ Plafon
		String[] totalKontrak_PlafonList = new String[3];
		totalKontrak_PlafonList[0] = result.getTotalKontrak_Plafon();
		totalKontrak_PlafonList[1] = result.getTotalKontrak_Plafon_Psgn();
		totalKontrak_PlafonList[2] = result.getTotalKontrak_Plafon_Grtr();
		for(int i = 0; i < totalKontrak_PlafonList.length; i++) {
			String totalKontrak_Plafon = totalKontrak_PlafonList[i];
			totalKontrak_PlafonList[i] = formatDataKBIJ_TotalKontrak_Plafon(totalKontrak_Plafon);
		}
		result.setTotalKontrak_Plafon(totalKontrak_PlafonList[0]);
		result.setTotalKontrak_Plafon_Psgn(totalKontrak_PlafonList[1]);
		result.setTotalKontrak_Plafon_Grtr(totalKontrak_PlafonList[2]);
		
		//Maks DPD/ Plafon
		String[] maxDPD_PlafonList = new String[3];
		maxDPD_PlafonList[0] = result.getMaxDPD_Plafon();
		maxDPD_PlafonList[1] = result.getMaxDPD_Plafon_Psgn();
		maxDPD_PlafonList[2] = result.getMaxDPD_Plafon_Grtr();
		for(int i = 0; i < maxDPD_PlafonList.length; i++) {
			String maxDPD_Plafon = maxDPD_PlafonList[i];
			maxDPD_PlafonList[i] = formatDataKBIJ_MaxDPD_Plafon(maxDPD_Plafon);
		}
		result.setMaxDPD_Plafon(maxDPD_PlafonList[0]);
		result.setMaxDPD_Plafon_Psgn(maxDPD_PlafonList[1]);
		result.setMaxDPD_Plafon_Grtr(maxDPD_PlafonList[2]);
		
		//Plafon Terbesar/ DPD
		String[] maxPlafon_DPDList = new String[3];
		maxPlafon_DPDList[0] = result.getMaxPlafon_DPD();
		maxPlafon_DPDList[1] = result.getMaxPlafon_DPD_Psgn();
		maxPlafon_DPDList[2] = result.getMaxPlafon_DPD_Grtr();
		for(int i = 0; i < maxPlafon_DPDList.length; i++) {
			String maxPlafon_DPD = maxPlafon_DPDList[i];
			maxPlafon_DPDList[i] = formatDataKBIJ_MaxPlafon_DPD(maxPlafon_DPD);
		} 
		result.setMaxPlafon_DPD(maxPlafon_DPDList[0]);
		result.setMaxPlafon_DPD_Psgn(maxPlafon_DPDList[1]);
		result.setMaxPlafon_DPD_Grtr(maxPlafon_DPDList[2]);
		
		//total contract active
		String[] totalContractActiveList = new String[3];
		totalContractActiveList[0] = result.getTotalKontrakActive();
		totalContractActiveList[1] = result.getTotalKontrakActivePsgn();
		totalContractActiveList[2] = result.getTotalKontrakActiveGrtr();
		
		for(int i = 0; i < totalContractActiveList.length; i++) {
			String totalContractActive = totalContractActiveList[i];
			totalContractActiveList[i] = formatDataKBIJ_totalKontrakActive(totalContractActive);
		} 
		result.setTotalKontrakActive(totalContractActiveList[0]);
		result.setTotalKontrakActivePsgn(totalContractActiveList[1]);
		result.setTotalKontrakActiveGrtr(totalContractActiveList[2]);
		
		//Reason
		result.setReason(formatDataKBIJ_Reason(result.getReason()));
		result.setReasonPsgn(formatDataKBIJ_Reason(result.getReasonPsgn()));
		result.setReasonGrtr(formatDataKBIJ_Reason(result.getReasonGrtr()));
		
		return result;
	}
	
	@Override
	public AddTaskPoloRequest formatDataKBIJ_addTaskPolo(AddTaskPoloRequest req) {
		AddTaskPoloRequest result = req;
		LOG.info("Data KBIJ Format Correction...");
		
		result.setOpsiPenanganan(formatDataKBIJ_OpsiPenanganan(result.getOpsiPenanganan()));
		
		return result;
	}
	
	@Override
	public String getDataKBIJ(KbijData dataKbij, String kbijDataType) {
		String result = "";
		
		String newline = "\n";
		String prefix = " - ";
		
		if(GlobalVal.KBIJ_DATATYPE.COLLECT_CUST.code().equals(kbijDataType)) {
			result = dataKbij.getCollectibility1_JumlahKontrak1_BakiDebet1() 
					+ newline + dataKbij.getCollectibility2_JumlahKontrak2_BakiDebet2() 
					+ newline + dataKbij.getCollectibility3_JumlahKontrak3_BakiDebet3()
					+ newline + dataKbij.getCollectibility4_JumlahKontrak4_BakiDebet4()
					+ newline + dataKbij.getCollectibility5_JumlahKontrak5_BakiDebet5();
		} else if(GlobalVal.KBIJ_DATATYPE.COLLECT_SPOUSE.code().equals(kbijDataType)) {
			result = dataKbij.getCollectibility1_JumlahKontrak1_BakiDebet1_Psgn() 
					+ newline + dataKbij.getCollectibility2_JumlahKontrak2_BakiDebet2_Psgn() 
					+ newline + dataKbij.getCollectibility3_JumlahKontrak3_BakiDebet3_Psgn()
					+ newline + dataKbij.getCollectibility4_JumlahKontrak4_BakiDebet4_Psgn()
					+ newline + dataKbij.getCollectibility5_JumlahKontrak5_BakiDebet5_Psgn();
		} else if(GlobalVal.KBIJ_DATATYPE.COLLECT_GRTR.code().equals(kbijDataType)) {
			result = dataKbij.getCollectibility1_JumlahKontrak1_BakiDebet1_Grtr() 
					+ newline + dataKbij.getCollectibility2_JumlahKontrak2_BakiDebet2_Grtr() 
					+ newline + dataKbij.getCollectibility3_JumlahKontrak3_BakiDebet3_Grtr()
					+ newline + dataKbij.getCollectibility4_JumlahKontrak4_BakiDebet4_Grtr()
					+ newline + dataKbij.getCollectibility5_JumlahKontrak5_BakiDebet5_Grtr();
		} else if(GlobalVal.KBIJ_DATATYPE.REASON_CUST.code().equals(kbijDataType) && dataKbij.getReason() != null) {
			String[] list = dataKbij.getReason();
			result = prefix + list[0].trim();
			for(int i = 1; i < list.length; i++) {
				result = result + newline + prefix + list[i].trim();
			}
		} else if(GlobalVal.KBIJ_DATATYPE.REASON_SPOUSE.code().equals(kbijDataType) && dataKbij.getReasonPsgn() != null) {
			String[] list = dataKbij.getReasonPsgn();
			result = prefix + list[0].trim();
			for(int i = 1; i < list.length; i++) {
				result = result + newline + prefix + list[i].trim();
			}
		} else if(GlobalVal.KBIJ_DATATYPE.REASON_GRTR.code().equals(kbijDataType) && dataKbij.getReasonGrtr() != null) {
			String[] list = dataKbij.getReasonGrtr();
			result = prefix + list[0].trim();
			for(int i = 1; i < list.length; i++) {
				result = result + newline + prefix + list[i].trim();
			}
		}
		if(result == null) {
			result = "";
		}
		return result;
	}
	
	@Transactional
	@Override
	public Map<String, String> checkResultCode(String uuidTaskH) {
		Map<String, String> result = new HashMap<String, String>();
		
		String isHitAPI = "0";
		
		Object[][] params = {
				{"uuidTaskH", uuidTaskH},
		};
		String query =
				  "SELECT\r\n"
				  + "	'TBL_CAE_DATA' [SOURCE], C.JSON_REQUEST [JSON]\r\n"
				  + "FROM TR_TASK_H A with(nolock)\r\n"
				  + "JOIN MS_GROUPTASK B with(nolock) ON A.UUID_TASK_H = B.UUID_TASK_H\r\n"
				  + "JOIN TBL_CAE_DATA C with(nolock) ON B.GROUP_TASK_ID = C.GROUP_TASK_ID\r\n"
				  + "WHERE A.UUID_TASK_H = :uuidTaskH\r\n"
				  + "UNION\r\n"
				  + "SELECT\r\n"
				  + "	'TBL_POLO_DATA' [SOURCE], C.JSON_REQUEST [JSON]\r\n"
				  + "FROM TR_TASK_H A with(nolock)\r\n"
				  + "JOIN MS_GROUPTASK B with(nolock) ON A.UUID_TASK_H = B.UUID_TASK_H\r\n"
				  + "JOIN TBL_POLO_DATA C with(nolock) ON B.GROUP_TASK_ID = C.GROUP_TASK_ID\r\n"
				  + "WHERE A.UUID_TASK_H = :uuidTaskH";
		
		Object[] data = (Object[]) this.getManagerDAO().selectOneNativeString(query, params);
		String source = (String) data[0];
		Clob jsonClob = (Clob) data[1];
		String json = clobStringConversion(jsonClob);
		//json = "{\"BiroKreditRecomendationCode\":\"0\",\"BiroKreditRecomendationCodePsgn\":\"DEFAULT\",\"BiroKreditRecomendationCodeGrtr\":null}";
		KbijData req = gson.fromJson(json, KbijData.class);
		
		String[] resultCode = new String[3]; 
		resultCode[0] = req.getBiroKreditRecomendationCode();
		resultCode[1] = req.getBiroKreditRecomendationCodePsgn();
		resultCode[2] = req.getBiroKreditRecomendationCodeGrtr();
		LOG.info("resultCodeCust:{}, resultCodePsgn:{}, resultCodeGrtr:{}"
				,req.getBiroKreditRecomendationCode()
				,req.getBiroKreditRecomendationCodePsgn()
				,req.getBiroKreditRecomendationCodeGrtr());
		for(int i = 0; i < resultCode.length; i++) {
			String value = resultCode[i];
			if(value != null) {
				if(value.isEmpty()
					|| "FAILED".equalsIgnoreCase(value)
					|| "DEFAULT".equalsIgnoreCase(value)) {
					resultCode[i] = "1";
				} else {
					resultCode[i] = "0";
				}
			} else {
				resultCode[i] = "1";
			}
		}
		result.put("BiroKreditRecomendationCode", resultCode[0]);
		result.put("BiroKreditRecomendationCodePsgn", resultCode[1]);
		result.put("BiroKreditRecomendationCodeGrtr", resultCode[2]);
		
		for(Map.Entry<String, String> entry : result.entrySet()) {
			String value = entry.getValue();
			if("1".equalsIgnoreCase(value)) {
				isHitAPI = "1";
				break;
			}
			
		}
		result.put("isHitAPI", isHitAPI);
		return result;
	}
	
	private String formatDataKBIJ_Collectibility(String str) {
		String result = null;
		if(str != null) {
			String[] split = str.split("/");
			String seperator = " / ";
			if(split.length == 3) {
				
				String collectibility = split[0];
				String jumlahKontrak = split[1];
				String bakiDebet = currencyFormat(split[2]);
				
				result = collectibility + seperator + jumlahKontrak + seperator + bakiDebet;
				LOG.info("formatCorrectionDataKBIJ_Collectibility.success({}, {})", str, result);
				return result;
			}
			else {
				LOG.info("formatCorrectionDataKBIJ_Collectibility.skip({})", str);
				return str;
			}
		} else {
			return null;
		}
		
	}
	private String formatDataKBIJ_MaxDpd_BakiDebet_Plafon(String str) {
		String result = null;
		if(str != null) {
			String[] split = str.split("/");
			String seperator = " / ";
			if(split.length == 3) {
				String maksDPD = split[0];
				String bakiDebet = currencyFormat(split[1]);
				String plafon = currencyFormat(split[2]);
				
				result = maksDPD + seperator + bakiDebet + seperator + plafon;
				LOG.info("formatDataKBIJ_MaxDpdBakiDebetPlafon.success({}, {})", str, result);
				return result;
			}
			else {
				LOG.info("formatDataKBIJ_MaxDpdBakiDebetPlafon.skip({})", str);
				return str;
			}
		} else {
			return null;
		}
		
	}
	private String formatDataKBIJ_MaxBakiDebet_PlafonAwal_LatestDPD(String str) {
		String result = null;
		if(str != null) {
			String[] split = str.split("/");
			String seperator = " / ";
			if(split.length == 3) {
				String maxBakitDebet = currencyFormat(split[0]);
				String plafonAwal = currencyFormat(split[1]);
				String lastestDPD = split[2];
				
				result = maxBakitDebet + seperator + plafonAwal + seperator + lastestDPD;
				LOG.info("formatDataKBIJ_MaxBakiDebet_PlafonAwal_LatestDPD.success({}, {})", str, result);
				return result;
			}
			else {
				LOG.info("formatDataKBIJ_MaxBakiDebet_PlafonAwal_LatestDPD.skip({})", str);
				return str;
			}
		} else {
			return null;
		}

	}
	private String formatDataKBIJ_TotalKontrak_Plafon(String str) {
		String result = null;
		if(str != null) {
			String[] split = str.split("/");
			String seperator = " / ";
			if(split.length == 2) {
				String totalKontrak = split[0];
				String plafon = currencyFormat(split[1]);
				
				result = totalKontrak + seperator + plafon;
				LOG.info("formatDataKBIJ_TotalKontrak_Plafon.success({}, {})", str, result);
				return result;
			}
			else {
				LOG.info("formatDataKBIJ_TotalKontrak_Plafon.skip({})", str);
				return str;
			}
		} else {
			return null;
		}
		
	}
	private String formatDataKBIJ_MaxDPD_Plafon(String str) {
		String result = null;
		if(str != null) {
			String[] split = str.split("/");
			String seperator = " / ";
			if(split.length == 2) {
				String maxDPD = split[0];
				String plafon = currencyFormat(split[1]);
				
				result = maxDPD + seperator + plafon;
				LOG.info("formatDataKBIJ_MaxDPD_Plafon.success({}, {})", str, result);
				return result;
			}
			else {
				LOG.info("formatDataKBIJ_MaxDPD_Plafon.skip({})", str);
				return str;
			}
		} else {
			return null;
		}
	}
	private String formatDataKBIJ_MaxPlafon_DPD(String str) {
		String result = null;
		if(str != null) {
			String[] split = str.split("/");
			String seperator = " / ";
			if(split.length == 2) {
				String maxPlafon = currencyFormat(split[0]);
				String dpd = split[1];
				
				result = maxPlafon + seperator + dpd;
				LOG.info("formatDataKBIJ_MaxPlafon_DPD.success({}, {})", str, result);
				return result;
			}
			else {
				LOG.info("formatDataKBIJ_MaxPlafon_DPD.skip({})", str);
				return str;
			}
		} else {
			return null;
		}
	}
	
	private String formatDataKBIJ_totalKontrakActive(String str) {
		String result = null;
		if(str != null) {
			double number = Double.parseDouble(str);
			int parse = (int) number;
			result = Integer.toString(parse);
		} else {
			return null;
		}
		return result;
	}
	
	private String[] formatDataKBIJ_Reason(String[] reasonList) {
		String[] reason = reasonList;
		if (reason != null) {
			for (int i = 1; i < reason.length; i++) {
				reason[i] = " Prefix " + reason[i];
			}
			reasonList = reason;
		}
		return reasonList;
	}
	
	private String formatDataKBIJ_jobCode(String jobCodeIA, String opsiPenanganan) {
		if (null != opsiPenanganan) {
			if (GlobalVal.OPSI_CABANG.equalsIgnoreCase(opsiPenanganan) || "Di�Cabang".equalsIgnoreCase(opsiPenanganan)) {
				jobCodeIA = GlobalVal.JOB_CS;
			} else {
				return jobCodeIA;
			}
		}
		
		LOG.info("Job code : {}", jobCodeIA);
		return jobCodeIA;
	}
	
	private String formatDataKBIJ_OpsiPenanganan(String opsiPenanganan) {
		if (null != opsiPenanganan) {
			if (GlobalVal.OPSI_CABANG.equalsIgnoreCase(opsiPenanganan) || "Di�Cabang".equalsIgnoreCase(opsiPenanganan)) {
				opsiPenanganan = GlobalVal.OPSI_CABANG;
			} else if (GlobalVal.OPSI_RUMAH.equalsIgnoreCase(opsiPenanganan) || "Di�Rumah".equalsIgnoreCase(opsiPenanganan)) {
				opsiPenanganan = GlobalVal.OPSI_RUMAH;
			}
		}
		
		LOG.info("Opsi Penanganan: {}", opsiPenanganan);
		return opsiPenanganan;
	}
	
	private String currencyFormat(String str) {
		try {
			double amt = Double.parseDouble(str);
			NumberFormat numberFormat = NumberFormat.getNumberInstance(Locale.getDefault());
			numberFormat.setMaximumFractionDigits(0);
			return numberFormat.format(amt);
			//return numberFormat.format(amt).replace(",", ".");
		} catch (NumberFormatException e) {
			LOG.info("Error when format Currency: {}", str);
			throw new NumberFormatException();
		}
	}
	private String clobStringConversion(Clob clb) {
		if (clb == null)
			return "";
		StringBuilder str = new StringBuilder();
		String strng;
		BufferedReader bufferRead;
		try {
			bufferRead = new BufferedReader(clb.getCharacterStream());
			while ((strng = bufferRead.readLine()) != null) {
				str.append(strng);
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (java.io.IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return str.toString();
	}

	@Override
	public String test() {
		String result = "Test";
		return result;
	}
	
}
