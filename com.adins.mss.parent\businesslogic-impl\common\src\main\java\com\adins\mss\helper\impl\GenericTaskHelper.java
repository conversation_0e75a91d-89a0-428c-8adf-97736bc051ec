package com.adins.mss.helper.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.helper.TaskHelper;
import com.adins.mss.model.TrTaskH;

public class GenericTaskHelper extends BaseLogic implements TaskHelper, MessageSourceAware {
	
	@Autowired
	private MessageSource messageSource;

    public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	@Override
    public boolean isTaskSurveyAndAdHoc(long uuidTaskH, AuditContext callerId) {
        TrTaskH header = this.getTrTaskH(uuidTaskH, callerId);
        String subsystem = header.getMsForm().getAmMssubsystem().getSubsystemName();
        switch (subsystem) {
            case GlobalVal.SUBSYSTEM_MS:
                return this.isSurveyAdHoc(header);
            default:
                return false;
        }
    }
    
    @Override
    public boolean isSurveyAdHoc(long surveyUuidTaskH, AuditContext callerId) {            
        TrTaskH header = this.getTrTaskH(surveyUuidTaskH, callerId);
        return this.isSurveyAdHoc(header);
    }
    
    private TrTaskH getTrTaskH(long uuidTaskH, AuditContext callerId) {
        TrTaskH header = this.getManagerDAO().selectOne(
        	"from TrTaskH tth join fetch tth.msForm mf "
        	+ "join fetch mf.amMssubsystem ams where tth.uuidTaskH = :uuidTaskH", 
        	new Object[][] {{"uuidTaskH", uuidTaskH}});
        if (header == null){
            throw new EntityNotFoundException(this.messageSource.getMessage(
					"businesslogic.taskhelper.tasknotfound", null,
					this.retrieveLocaleAudit(callerId)), Long.toString(uuidTaskH));
        }
        return header;       
    }
    
    private boolean isSurveyAdHoc(TrTaskH header) {
        if (header.getAssignDate() == null)
            return true;
        
        return false;
    }
}
