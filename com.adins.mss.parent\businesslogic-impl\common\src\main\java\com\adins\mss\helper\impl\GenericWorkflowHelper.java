package com.adins.mss.helper.impl;

import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Transactional;

import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.MssWorkflowProcess;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.helper.WorkflowHelper;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.WfProcess;

public class GenericWorkflowHelper extends BaseLogic implements WorkflowHelper {

    @Transactional(readOnly=true)
    @Override
    public String identifyWfProcessCode(long uuidTaskH) {
        return this.getProcessCode(uuidTaskH);
    }

    @Transactional(readOnly=true)
    @Override
    public String identifyUuidWfProcess(long uuidTaskH) {
        String processCode = this.getProcessCode(uuidTaskH);
        return this.getUuidProcess(processCode);
    }

    private String getProcessCode(long uuidTaskH) {       
        Object[][] params = new Object[1][1];
        params[0][0] = Restrictions.eq("uuidTaskH", uuidTaskH);
        
        TrTaskH header = this.getManagerDAO().selectOne(
        	"from TrTaskH tth join fetch tth.msForm mf join fetch mf.amMssubsystem where tth.uuidTaskH = :uuidTaskH", 
        	new Object[][] {{"uuidTaskH", uuidTaskH}});
        if (header == null)
            throw new EntityNotFoundException("Task not found", Long.toString(uuidTaskH));
                
        String subsystem = header.getMsForm().getAmMssubsystem().getSubsystemName();
        
        switch (subsystem) {
            case GlobalVal.SUBSYSTEM_MS:
                if (isSurveyAdHoc(header))
                    return MssWorkflowProcess.MS_ADHOC.toString();
                else
                    return MssWorkflowProcess.MS_CORE.toString();
            case GlobalVal.SUBSYSTEM_MO:
                return MssWorkflowProcess.MO.toString();
                
            case GlobalVal.SUBSYSTEM_MC:
                return MssWorkflowProcess.MC.toString();
            
            default:
                throw new RuntimeException("Unknown subsystem detected in task");
        }
    }
    
    private boolean isSurveyAdHoc(TrTaskH header) {        
        return (header.getAssignDate() == null);
    }
    
    private String getUuidProcess(String processCode) {
        Object[][] params = new Object[1][1];
        params[0][0] = Restrictions.eq("processCode", processCode);
        
        WfProcess wfProcess = this.getManagerDAO().selectOne(WfProcess.class, params);
        if (wfProcess == null){
            throw new EntityNotFoundException("Process not found", processCode);
        }
        return String.valueOf(wfProcess.getUuidProcess());
            
    }
}
