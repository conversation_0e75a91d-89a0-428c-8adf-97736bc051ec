package com.adins.mss.provider;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.LdapException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.ldap.LdapLogic;
import com.adins.framework.tool.password.PasswordHash;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.ServicesUserException;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.services.model.common.LoginMultiUserBean;

public class CustomAuthenticationProvider extends BaseLogic implements AuthenticationProvider, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(CustomAuthenticationProvider.class);
	@Autowired
	private GlobalLogic  globalLogic;
	private IntFormLogic intFormLogic;
	private MessageSource messageSource;
    private LdapLogic ldapLogic;
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setLdapLogic(LdapLogic ldapLogic) {
        this.ldapLogic = ldapLogic;
    }

	@Override
	@Transactional(noRollbackFor={BadCredentialsException.class})
	public Authentication authenticate(Authentication authentication)
			throws AuthenticationException {
		String loginWithTenant = (String) authentication.getPrincipal();
		String loginId = StringUtils.EMPTY;
		if(loginWithTenant.contains("@")){
			loginId = StringUtils.substring(loginWithTenant, 0, StringUtils.indexOf(loginWithTenant, '@')); //Multitenant support
		}else{
			loginId = loginWithTenant;
		}
		
		//FIXME : digunakan apabila ID sales di order = loginId + O
		/*
		if(GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(application)){
			loginId = loginId+"O";
		}
		*/
		
		AuditContext auditContext = new AuditContext();
		auditContext.setCallerId(loginId);
		String password = (String) authentication.getCredentials();
		
		Integer ttlIsActive = (Integer) this.getManagerDAO().selectOneNativeString("select COUNT(1) from AM_MSUSER with (nolock) where LOGIN_ID like '"+loginId+"%' and IS_ACTIVE = 1", null);
    	if (ttlIsActive == 0) {
    		throw new ServicesUserException(
            		this.messageSource.getMessage("businesslogic.login.inactive", new Object[]{loginId}, this.retrieveLocaleAudit(auditContext)));
    	}

		String[][] paramMultiLogin = { {"uniqueId", loginId} };
    	List<Map<String, Object>> listLoginId = this.getManagerDAO().selectAllNativeString("select LOGIN_ID from AM_MSUSER with (nolock) where UNIQUE_ID = :uniqueId and IS_ACTIVE = 1 and IS_DELETED = 0", paramMultiLogin);
    	if (!listLoginId.isEmpty()) {
    		Map<String, Object> map = listLoginId.get(0);
    		loginId = (String) map.get("d0");
    	}
		
		Object[][] params = {{"loginId", loginId}};
		AmMsuser user = this.getManagerDAO().selectOne("from AmMsuser u join fetch u.amMssubsystem where u.loginId = :loginId", params);
		boolean isCanLogin = true;
		if (user == null) {
			throw new BadCredentialsException(this.messageSource.getMessage("businesslogic.login.idpassword", null, this.retrieveLocaleAudit(auditContext)));
        }
		
		if ("1".equals(SpringPropertiesUtils.getProperty(GlobalKey.LOGIN_BYPASS))) {
			isCanLogin = true;
		}
		else if (GlobalVal.FLAG_LOGIN_PROVIDER_DB.equals(user.getLoginProvider())) {
			if (!PasswordHash.validatePassword(password, user.getPassword())){
				isCanLogin = false;
			}
		}
		else if (GlobalVal.FLAG_LOGIN_PROVIDER_AD.equals(user.getLoginProvider())) {
		    isCanLogin = this.ldapAuth(loginId, password);            
		}
		else {
			LOG.info("AUTH User non DB");
			boolean loginResult = intFormLogic.authenticateUser(user, password);
			if (!loginResult){
				isCanLogin = false;
			}
		}

		if(!isCanLogin){
			Date dtmUpd = new Date();
			int failCount = user.getFailCount();
			String gsValue = globalLogic.getGsValue(GlobalKey.GENERALSETTING_MAX_FAIL_COUNT, null);
			Integer maxFailCount =  Integer.parseInt(gsValue);
			failCount += 1;
			user.setFailCount(failCount);
			user.setPrevLoggedFail(user.getLastLoggedFail());
			user.setLastLoggedFail(dtmUpd);
			user.setDtmUpd(dtmUpd);
			user.setUsrUpd(String.valueOf(user.getUuidMsUser()));
			
			Stack<Object[]> paramsStack = new Stack<>();
			paramsStack.push(new Object[]{"uuidUser", user.getUuidMsUser()});
			paramsStack.push(new Object[]{"failCount", failCount});
			paramsStack.push(new Object[]{"dtmUpd", dtmUpd});
			paramsStack.push(new Object[]{"uniqueId", user.getUniqueId()});
			
			String updFailCount = StringUtils.EMPTY;
			if (failCount >= maxFailCount) {
				user.setIsLocked("1");
				user.setLastLocked(dtmUpd);
				updFailCount = " ,IS_LOCKED = :isLocked, LAST_LOCKED = :lastLocked";
				paramsStack.push(new Object[]{"isLocked", "1"});
				paramsStack.push(new Object[]{"lastLocked", dtmUpd});
			}
			
			Object[][] paramUpdateMulti = new Object[paramsStack.size()][2];
		    for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				paramUpdateMulti[i] = objects;
			}
			
			StringBuilder sb = new StringBuilder();
			sb.append("UPDATE AM_MSUSER set FAIL_COUNT = :failCount, LAST_LOGGED_FAIL = :dtmUpd");
			sb.append(updFailCount);
			sb.append("	where UNIQUE_ID = :uniqueId");
			sb.append(" and IS_ACTIVE = 1");
			sb.append(" and IS_DELETED = 0");
			sb.append(" and UUID_MS_USER != :uuidUser");
			this.getManagerDAO().updateNativeString(sb.toString(), paramUpdateMulti);
			
			if ("1".equals(user.getIsLocked())) {
				throw new BadCredentialsException(
	            		this.messageSource.getMessage("businesslogic.login.locked", new Object[]{user.getUniqueId()}, this.retrieveLocaleAudit(auditContext)));
			}
			this.getManagerDAO().update(user);
			throw new BadCredentialsException(this.messageSource.getMessage("businesslogic.login.idpassword", null, this.retrieveLocaleAudit(auditContext)));
		}
		List<GrantedAuthority> auth = new ArrayList<GrantedAuthority>();
		auth.add(new SimpleGrantedAuthority("ROLE_APP"));
		
		if(user.getAmMssubsystem().getSubsystemName().equals(GlobalVal.SUBSYSTEM_MS)){
			auth.add(new SimpleGrantedAuthority("ROLE_MS"));
		}
		if(user.getAmMssubsystem().getSubsystemName().equals(GlobalVal.SUBSYSTEM_MO)){
			auth.add(new SimpleGrantedAuthority("ROLE_MO"));
		}
		if(user.getAmMssubsystem().getSubsystemName().equals(GlobalVal.SUBSYSTEM_MC)){
			auth.add(new SimpleGrantedAuthority("ROLE_MC"));
		}
		return new UsernamePasswordAuthenticationToken(loginId, password, auth);
	}

	@Override
	public boolean supports(Class<?> authentication) {
		return true;
	}
    
    boolean ldapAuth(String user, String password) throws LdapException {
        boolean ldapEnabled = BooleanUtils.toBoolean(SpringPropertiesUtils.getProperty(GlobalKey.LDAP_ENABLED));
        if (!ldapEnabled) {
            LOG.debug("LDAP Authentication disabled! Return true");
            return true;
        }
        
        return this.ldapLogic.authenticate(
        		SpringPropertiesUtils.getProperty(GlobalKey.LDAP_DOMAIN) + "\\" + user, password, false);
    }
}
