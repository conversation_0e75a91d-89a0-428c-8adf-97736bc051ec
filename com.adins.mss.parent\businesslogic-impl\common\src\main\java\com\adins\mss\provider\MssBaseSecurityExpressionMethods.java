package com.adins.mss.provider;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.InquiryTaskMode;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;

public class MssBaseSecurityExpressionMethods extends BaseLogic {
	
	private static final String QUERY_TASKH_BY_TASKID = "SELECT U.LOGIN_ID FROM TR_TASK_H H WITH (NOLOCK) LEFT JOIN AM_MSUSER U WITH (NOLOCK) ON H.UUID_MS_USER=U.UUID_MS_USER WHERE H.TASK_ID = :taskId";
	private static final String QUERY_TASKH_BY_UUID = "SELECT U.UNIQUE_ID FROM TR_TASK_H H WITH (NOLOCK) LEFT JOIN AM_MSUSER U WITH (NOLOCK) ON H.UUID_MS_USER=U.UUID_MS_USER WHERE H.UUID_TASK_H = :uuidTaskH";
	private static final String QUERY_FORM_AUTHORIZED = "SELECT 1 FROM MS_FORMOFGROUP FOG WITH (NOLOCK) INNER JOIN AM_MSGROUP G WITH (NOLOCK) ON FOG.UUID_MS_GROUP = G.UUID_MS_GROUP INNER JOIN AM_MEMBEROFGROUP MOG WITH (NOLOCK) ON MOG.UUID_MS_GROUP=G.UUID_MS_GROUP INNER JOIN AM_MSUSER U WITH (NOLOCK) ON MOG.UUID_MS_USER=U.UUID_MS_USER WHERE FOG.UUID_FORM = :uuidForm AND LOGIN_ID=:loginId";
	
	private static final String QUERY_FORM_AUTHORIZED_BY_CALLER_ID = "SELECT 1 FROM MS_FORMOFGROUP FOG WITH (NOLOCK) INNER JOIN AM_MSGROUP G WITH (NOLOCK) ON FOG.UUID_MS_GROUP = G.UUID_MS_GROUP INNER JOIN AM_MEMBEROFGROUP MOG WITH (NOLOCK) ON MOG.UUID_MS_GROUP=G.UUID_MS_GROUP INNER JOIN AM_MSUSER U WITH (NOLOCK) ON MOG.UUID_MS_USER=U.UUID_MS_USER WHERE FOG.UUID_FORM = :uuidForm AND U.UUID_MS_USER = :uuidMsUser";
	
	private static final String MENU_REFF_DASHBOARD_BY_BRANCH = "/monitoring/dashboardsurvey/ListByHierarkiBranch";
	private static final String LOGIN_ID = "loginId";
	private static final String UUID_MS_USER = "uuidMsUser";
	
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean hasTaskId(String taskId, Authentication authentication) {
		Object[][] params = { {"taskId", taskId} };
		List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNativeString(QUERY_TASKH_BY_TASKID, params);
		
		for (Map<String, Object> record : resultList) {
			if (!StringUtils.equalsIgnoreCase((String) record.get("d0"), authentication.getName())) {
				return false;
			}
		}		
		return true;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean hasTaskUuid(String uuidTaskH, Authentication authentication) {
		if (!NumberUtils.isDigits(uuidTaskH))
			return true;
		
		Object[][] paramsUser = { {Restrictions.eq(LOGIN_ID, authentication.getName())} };
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, paramsUser);
		
		if (null == user) {
			return false;
		}
		
		Object[][] params = { {"uuidTaskH", uuidTaskH} };
		List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNativeString(QUERY_TASKH_BY_UUID, params);
		
		for (Map<String, Object> record : resultList) {
			if (!StringUtils.equalsIgnoreCase((String) record.get("d0"), user.getUniqueId())) {
				return false;
			}
		}
		
		return true;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean hasFormUuid(String uuidForm, Authentication authentication) {
		OAuth2Authentication auth = (OAuth2Authentication)authentication;
		OAuth2Request req = auth.getOAuth2Request();
		Map<String,String> headerDetails = req.getRequestParameters();
		String application = headerDetails.get("application");
		String tempLoginId = StringUtils.EMPTY;
		if(GlobalVal.SUBSYSTEM_MO.equalsIgnoreCase(application)){
			//tempLoginId = authentication.getName()+"O";
			tempLoginId = authentication.getName();
			Object[][] params = { {Restrictions.eq(LOGIN_ID, tempLoginId)} };
			AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, params);
			if(user == null){
				tempLoginId = authentication.getName();
			}
		}
		else{
			tempLoginId = authentication.getName();
		}
		
		Object[][] params = { {"uuidForm", uuidForm}, {LOGIN_ID, tempLoginId} };		
		Integer resultList = (Integer) this.getManagerDAO()
				.selectOneNativeString(QUERY_FORM_AUTHORIZED, params);
		
		return (resultList != null);
	}
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean hasFormUuidByCallerID(String uuidForm, String callerId) {
		
		Object[][] params = { {"uuidForm", uuidForm}, {UUID_MS_USER, callerId} };		
		Integer resultList = (Integer) this.getManagerDAO()
				.selectOneNativeString(QUERY_FORM_AUTHORIZED_BY_CALLER_ID, params);
		
		return (resultList != null);
	}
	
	
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean isValidCallerId(String callerId, Authentication authentication) {
		Object[][] params = { {Restrictions.eq(LOGIN_ID, authentication.getName())} };
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, params);
		
		AmMsuser user2 = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId));
		
		if (null == user || null == user2) {
			return false;
		}
		
		boolean test = StringUtils.equalsIgnoreCase(user2.getUniqueId(), String.valueOf(user.getUniqueId()));
		
		
		return (StringUtils.equalsIgnoreCase(user2.getUniqueId(), String.valueOf(user.getUniqueId())));
		
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean isValidInquiryTaskId(Object[][] params, String modeAction, String callerId) {
		boolean result = Boolean.FALSE;

		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId));
		if (null == user) {
			return Boolean.FALSE;
		}
		
		if (InquiryTaskMode.USER.getInquiryTaskMode().equals(modeAction)) {
			Object[][] queryParams = { {"uuidUser", user.getUuidMsUser()}, 
					{"subsystemId", user.getAmMssubsystem().getUuidMsSubsystem()}, 
					{"branchIdLogin", user.getMsBranch().getUuidBranch()},
					{"paramUuidTaskH", params[0][1]} };
			
			result = checkIfUserValidTaskIdByUser(modeAction, queryParams);
		} else if (InquiryTaskMode.BRANCH.getInquiryTaskMode().equals(modeAction)) {
			Object[][] queryParams = { {"subsystemId", user.getAmMssubsystem().getUuidMsSubsystem()}, 
					{"branchIdLogin", user.getMsBranch().getUuidBranch()},
					{"paramUuidTaskH", params[0][1]} };
			
			result = checkIfUserValidTaskIdByBranch(modeAction, queryParams);
		} else if (InquiryTaskMode.REGION.getInquiryTaskMode().equals(modeAction)) {
			Object[][] queryParams = { {"subsystemId", user.getAmMssubsystem().getUuidMsSubsystem()}, 
					{"paramUuidTaskH", params[0][1]} };
			
			result = checkIfUserValidTaskIdByRegion(modeAction, queryParams);
		} else {
			return true;
		}
	
		return result;
	}
	
	private boolean checkIfUserValidTaskIdByUser(String modeAction, Object[][] queryParams) {
		Integer result = 0;
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT COUNT(1) ")
			.append("from ( ")
			.append("select trth.uuid_task_h ")
			.append("FROM tr_task_h trth with (nolock) ")
			.append("left outer JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user) ")
			.append("JOIN ms_statustask mssta with (nolock) ON(trth.uuid_status_task = mssta.uuid_status_task) ")
			.append("JOIN ms_branch msb with (nolock) ON(trth.uuid_branch = msb.uuid_branch) ")
			.append("JOIN (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = msu.UUID_MS_USER ")
			.append("WHERE mssta.uuid_ms_subsystem = :subsystemId ")
			.append("AND trth.uuid_branch = :branchIdLogin ")
			.append("AND trth.uuid_task_h = :paramUuidTaskH ")
			.append("union all ")
			.append("select trth.uuid_task_h ")
			.append("FROM final_tr_task_h trth with (nolock) ")
			.append("left outer JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user) ")
			.append("JOIN ms_statustask mssta with (nolock) ON(trth.uuid_status_task = mssta.uuid_status_task) ")
			.append("JOIN ms_branch msb with (nolock) ON(trth.uuid_branch = msb.uuid_branch) ")
			.append("JOIN (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = msu.UUID_MS_USER ")
			.append("WHERE mssta.uuid_ms_subsystem = :subsystemId ")
			.append("AND trth.uuid_branch = :branchIdLogin ")
			.append("AND trth.uuid_task_h = :paramUuidTaskH ")
			.append(") c ");
		
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), queryParams);
		
		if (result < 1) {
			return Boolean.FALSE;
		} else {
			return Boolean.TRUE;
		}
	}
	
	private boolean checkIfUserValidTaskIdByBranch(String modeAction, Object[][] queryParams) {
		Integer result = 0;
		
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT COUNT(*) from ( ")
		.append("SELECT trth.uuid_task_h, ")
		.append("isnull(trth.customer_name,'-') CUSTOMER_NAME, ")
		.append("isnull(trth.appl_no,'-') appl_no, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.assign_date, 113), 17),'-') as assign_date, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.download_date, 113),17),'-') as download_date, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.submit_date, 113),17),'-') as submit_date, ")
		.append("isnull(msu.full_name,'-') full_name, ")
		.append("isnull(mssta.status_task_desc,'-') status_task_desc, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.send_date, 113),17),'-') as send_date, ")
		.append("isnull(msb.branch_name,'-') as branchName, ")
		.append("'1' as flag, '0' as flagOrder, ")
		.append("trth.assign_date as assign_dt, ")
		.append("trth.download_date as download_dt, ")
		.append("trth.submit_date as submit_dt, ")
		.append("trth.send_date as send_dt ")
		.append("FROM tr_task_h trth with (nolock) ")
		.append("left outer JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user) ")
		.append("JOIN ms_statustask mssta with (nolock) ON(trth.uuid_status_task = mssta.uuid_status_task) ")
		.append("JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchIdLogin)) msb on trth.UUID_BRANCH = msb.UUID_BRANCH ")
		.append("WHERE mssta.uuid_ms_subsystem = :subsystemId ")
		.append("AND trth.uuid_task_h = :paramUuidTaskH ")
		.append("union ALL ")
		.append("SELECT trth.uuid_task_h, ")
		.append("isnull(trth.customer_name,'-') CUSTOMER_NAME, ")
		.append("isnull(trth.appl_no,'-') appl_no, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.assign_date, 113), 17),'-') as assign_date, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.download_date, 113),17),'-') as download_date, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.submit_date, 113),17),'-') as submit_date, ")
		.append("isnull(msu.full_name,'-') full_name, ")
		.append("isnull(mssta.status_task_desc,'-') status_task_desc, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.send_date, 113),17),'-') as send_date, ")
		.append("isnull(msb.branch_name,'-') as branchName, ")
		.append("'2' as flag, '0' as flagOrder, ")
		.append("trth.assign_date as assign_dt, ")
		.append("trth.download_date as download_dt, ")
		.append("trth.submit_date as submit_dt, ")
		.append("trth.send_date as send_dt ")
		.append("FROM final_tr_task_h trth with (nolock) ")
		.append("left outer JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user) ")
		.append("JOIN ms_statustask mssta with (nolock) ON(trth.uuid_status_task = mssta.uuid_status_task) ")
		.append("JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchIdLogin)) msb on trth.UUID_BRANCH = msb.UUID_BRANCH ")
		.append("WHERE mssta.uuid_ms_subsystem = :subsystemId ")
		.append("AND trth.uuid_task_h = :paramUuidTaskH ")
		.append(") c ");
		
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), queryParams);
		
		if (result < 1) {
			return Boolean.FALSE;
		} else {
			return Boolean.TRUE;
		}
	}
	
	private boolean checkIfUserValidTaskIdByRegion(String modeAction, Object[][] queryParams) {
		Integer result = 0;
		
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT COUNT(*) from ( ")
		.append("SELECT trth.uuid_task_h, ")
		.append("isnull(trth.customer_name,'-') CUSTOMER_NAME, ")
		.append("isnull(trth.appl_no,'-') appl_no, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.assign_date, 113), 17),'-') as assign_date, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.download_date, 113),17),'-') as download_date, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.submit_date, 113),17),'-') as submit_date, ")
		.append("isnull(msu.full_name,'-') full_name, ")
		.append("isnull(mssta.status_task_desc,'-') status_task_desc, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.send_date, 113),17),'-') as send_date, ")
		.append("isnull(msb.branch_name,'-') as branchName, ")
		.append("'1' as flag, '0' as flagOrder, ")
		.append("trth.assign_date as assign_dt, ")
		.append("trth.download_date as download_dt, ")
		.append("trth.submit_date as submit_dt, ")
		.append("trth.send_date as send_dt ")
		.append("FROM tr_task_h trth with (nolock) ")
		.append("left outer JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user) ")
		.append("JOIN ms_statustask mssta with (nolock) ON(trth.uuid_status_task = mssta.uuid_status_task) ")
		.append("JOIN MS_BRANCH msb on (msb.uuid_branch = trth.uuid_branch) ")
		.append("LEFT JOIN MS_REGION msr on (msr.uuid_region = msb.uuid_region) ")
		.append("WHERE mssta.uuid_ms_subsystem = :subsystemId ")
		.append("AND trth.uuid_task_h = :paramUuidTaskH ")
		.append("union ALL ")
		.append("SELECT trth.uuid_task_h, ")
		.append("isnull(trth.customer_name,'-') CUSTOMER_NAME, ")
		.append("isnull(trth.appl_no,'-') appl_no, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.assign_date, 113), 17),'-') as assign_date, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.download_date, 113),17),'-') as download_date, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.submit_date, 113),17),'-') as submit_date, ")
		.append("isnull(msu.full_name,'-') full_name, ")
		.append("isnull(mssta.status_task_desc,'-') status_task_desc, ")
		.append("isnull(LEFT(CONVERT(VARCHAR, trth.send_date, 113),17),'-') as send_date, ")
		.append("isnull(msb.branch_name,'-') as branchName, ")
		.append("'2' as flag, '0' as flagOrder, ")
		.append("trth.assign_date as assign_dt, ")
		.append("trth.download_date as download_dt, ")
		.append("trth.submit_date as submit_dt, ")
		.append("trth.send_date as send_dt ")
		.append("FROM final_tr_task_h trth with (nolock) ")
		.append("left outer JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user) ")
		.append("JOIN ms_statustask mssta with (nolock) ON(trth.uuid_status_task = mssta.uuid_status_task) ")
		.append("JOIN MS_BRANCH msb on (msb.uuid_branch = trth.uuid_branch) ")
		.append("LEFT JOIN MS_REGION msr on (msr.uuid_region = msb.uuid_region) ")
		.append("WHERE mssta.uuid_ms_subsystem = :subsystemId ")
		.append("AND trth.uuid_task_h = :paramUuidTaskH ")
		.append(") c ");
		
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), queryParams);
		
		if (result < 1) {
			return Boolean.FALSE;
		} else {
			return Boolean.TRUE;
		}
	}
	
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean isValidInquiryGroupTaskId(String groupTaskId, String modeAction, String callerId) {
		boolean result = Boolean.FALSE;

		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId));
		if (null == user) {
			return Boolean.FALSE;
		}
		
		if (InquiryTaskMode.USER.getInquiryTaskMode().equals(modeAction)) {
			Object[][] queryParams = { {"uuidUser", user.getUuidMsUser()}, 
					{"uuidSubsystem", user.getAmMssubsystem().getUuidMsSubsystem()}, 
					{"uuidBranch", user.getMsBranch().getUuidBranch()},
					{"groupTaskId", groupTaskId}};
			
			result = checkIfUserValidGroupTaskIdByUser(modeAction, queryParams);
		} else if (InquiryTaskMode.BRANCH.getInquiryTaskMode().equals(modeAction)) {
			Object[][] queryParams = { {"uuidSubsystem", user.getAmMssubsystem().getUuidMsSubsystem()}, 
					{"uuidBranchLogin", user.getMsBranch().getUuidBranch()},
					{"groupTaskId", groupTaskId}};
			
			result = checkIfUserValidGroupTaskIdByBranch(modeAction, queryParams);
		}
	
		return result;
	}
	
	private boolean checkIfUserValidGroupTaskIdByUser(String modeAction, Object[][] queryParams) {
		Integer resultCount = NumberUtils.INTEGER_ZERO;
		
		StringBuilder queryBuilderCount = new StringBuilder()
				.append("select count (distinct b.GROUP_TASK_ID) FROM ( ")
				.append("select distinct GROUP_TASK_ID as GROUP_TASK_ID, msgt.CUSTOMER_NAME as CUSTOMER_NAME,  ")
				.append("(SELECT TOP(1) msgt2.APPL_NO FROM MS_GROUPTASK msgt2 with (nolock) WHERE msgt2.GROUP_TASK_ID = msgt.GROUP_TASK_ID ")
				.append("ORDER BY DTM_CRT DESC) AS APPL_NO, ") 
				.append("BRANCH_NAME as BRANCH_NAME ")
				.append("from MS_GROUPTASK msgt with (nolock)  ")
				.append("join MS_BRANCH msb with (nolock) on msgt.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("join tr_task_h tth with (nolock) on tth.uuid_task_h = msgt.uuid_task_h ")
				.append("join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = tth.UUID_MS_USER ")
				.append("join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("where mst.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
				.append("and msgt.UUID_BRANCH = :uuidBranch ")
				.append("and msgt.GROUP_TASK_ID = :groupTaskId ")
				.append("UNION ALL ")
				.append("select distinct GROUP_TASK_ID as GROUP_TASK_ID, msgt.CUSTOMER_NAME as CUSTOMER_NAME,  ")
				.append("(SELECT TOP(1) msgt2.APPL_NO FROM MS_GROUPTASK msgt2 with (nolock) WHERE msgt2.GROUP_TASK_ID = msgt.GROUP_TASK_ID ")
				.append("ORDER BY DTM_CRT DESC) AS APPL_NO, ") 
				.append("BRANCH_NAME as BRANCH_NAME ")
				.append("from FINAL_MS_GROUPTASK msgt with (nolock)  ")
				.append("join MS_BRANCH msb with (nolock) on msgt.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("join final_tr_task_h tth with (nolock) on tth.uuid_task_h = msgt.uuid_task_h ")
				.append("join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = tth.UUID_MS_USER ")
				.append("join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("where mst.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
				.append("and msgt.UUID_BRANCH = :uuidBranch ")
				.append("and msgt.GROUP_TASK_ID = :groupTaskId ")
				.append(")b ");
		
	    resultCount = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderCount.toString(), queryParams);
		
		if (resultCount < 1) {
			return Boolean.FALSE;
		} else {
			return Boolean.TRUE;
		}
	}
	
	private boolean checkIfUserValidGroupTaskIdByBranch(String modeAction, Object[][] queryParams) {
		Integer result = 0;
		
		StringBuilder queryBuilderCount = new StringBuilder()
		.append("select count(DISTINCT b.GROUP_TASK_ID) from ( ")
		.append("select DISTINCT GROUP_TASK_ID ")
		.append("from MS_GROUPTASK msgt with (nolock)  ")
		.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchLogin)) msb on msgt.UUID_BRANCH = msb.UUID_BRANCH ")
		.append("join TR_TASK_H trth with (nolock) on msgt.UUID_TASK_H = trth.UUID_TASK_H ")
		.append("join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
		.append("where mst.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
		.append("and msgt.GROUP_TASK_ID = :groupTaskId ")
		.append("UNION ALL ")
		.append("select DISTINCT GROUP_TASK_ID ")
		.append("from FINAL_MS_GROUPTASK msgt with (nolock)  ")
		.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchLogin)) msb on msgt.UUID_BRANCH = msb.UUID_BRANCH ")
		.append("join FINAL_TR_TASK_H trth with (nolock) on msgt.UUID_TASK_H = trth.UUID_TASK_H ")
		.append("join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
		.append("where mst.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
		.append("and msgt.GROUP_TASK_ID = :groupTaskId ")
		.append(")b ");
		
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderCount.toString(), queryParams);
		
		if (result < 1) {
			return Boolean.FALSE;
		} else {
			return Boolean.TRUE;
		}
	}

	public boolean isValidSpvDashboard(long uuidSpv, String callerId) {
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId));
		if (null == user) {
			return Boolean.FALSE;
		}
		
		if (uuidSpv == user.getUuidMsUser()) {
			return Boolean.TRUE;
		}
		
		Object[][] paramsValidMenu = { {"uuidUserLogin", user.getUuidMsUser()}, 
				{"menuReff", MENU_REFF_DASHBOARD_BY_BRANCH} };
		
		StringBuilder queryBuilderMenuCount = new StringBuilder()
		.append("select count(mog.uuid_ms_user) from AM_MEMBEROFGROUP mog with (nolock) ")
		.append("left join AM_MENUOFGROUP mnog with (nolock) on mog.uuid_ms_group = mnog.uuid_ms_group ")
		.append("left join AM_MSMENU menu with (nolock) on mnog.uuid_ms_menu = menu.uuid_ms_menu ")
		.append("where mog.uuid_ms_user = :uuidUserLogin ")
		.append("and menu_reff = :menuReff ");
		
		Integer totalMenu = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderMenuCount.toString(), paramsValidMenu);
		
		if (totalMenu < 1) {
			return Boolean.FALSE;
		} else {
			Object[][] paramsJob = { {Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MS+GlobalKey.GENERALSETTING_JOBSPV)} };
			AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsJob);
			
			List listJobs = Arrays.asList(StringUtils.split(amGeneralsetting.getGsValue(), ";"));
			Object[][] queryParams = { {"jobCode", listJobs}, 
					{"uuidBranch", user.getMsBranch().getUuidBranch()},
					{"uuidSpv", uuidSpv} };
			
			StringBuilder queryBuilderCount = new StringBuilder()
			.append("select count(distinct UUID_MS_USER) ")
			.append("from am_msuser am with (nolock) ")
			.append("join MS_JOB J with (nolock) on am.UUID_JOB = J.UUID_JOB ")
			.append("join (select keyValue from dbo.getCabangByLogin(:uuidBranch)) hrkBranch on hrkBranch.keyValue = am.UUID_BRANCH  ")
			.append("where  J.JOB_CODE in (:jobCode) ")
			.append("and am.is_active = '1' and am.is_deleted = '0' ")
			.append("and uuid_ms_user = :uuidSpv ");
			
			Integer total = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderCount.toString(), queryParams);
			
			if (total < 1) {
				return Boolean.FALSE;
			} else {
				return Boolean.TRUE;
			}
		}
	}
	
	public boolean isValidUserChangePassword(long uuidMsUser, String callerId) {
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId));
		
		if (null == user) {
			return Boolean.FALSE;
		} else if (uuidMsUser != user.getUuidMsUser()) {
			return Boolean.FALSE;
		}
		
		return Boolean.TRUE;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean isValidReportDownload(String seqNo, String callerId) {
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId));
		if (null == user) {
			return Boolean.FALSE;
		}
		
		Object[][] paramsValidReportDownload = { {"uuidUserLogin", user.getUuidMsUser()}, 
				{"seqNo", seqNo} };
		
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT Count(SEQNO) ")
		.append("FROM TR_REPORTRESULTLOG rrl with (nolock) ")
		.append("LEFT JOIN AM_MSUSER usr with (nolock) on rrl.UUID_MS_USER = usr.UUID_MS_USER ")
		.append("WHERE rrl.UUID_MS_USER = :uuidUserLogin ")
		.append("and SEQNO = :seqNo ");
		
		Integer total = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), paramsValidReportDownload);
	
		if (total < 1) {
			return Boolean.FALSE;
		} else {
			return Boolean.TRUE;
		}
	}
}
