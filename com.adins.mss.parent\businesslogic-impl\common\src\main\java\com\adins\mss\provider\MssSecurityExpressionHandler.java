package com.adins.mss.provider;

import org.aopalliance.intercept.MethodInvocation;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler;
import org.springframework.security.core.Authentication;

import com.adins.framework.persistence.dao.api.ManagerDAO;

public class MssSecurityExpressionHandler extends DefaultMethodSecurityExpressionHandler {
	
	private ManagerDAO managerDao;
	
	public MssSecurityExpressionHandler() {
		setExpressionParser(getExpressionParser());
	}
	
	public void setManagerDao(ManagerDAO managerDao) {
		this.managerDao = managerDao;
	}

	public StandardEvaluationContext createEvaluationContextInternal(Authentication authentication, MethodInvocation mi) {		
		StandardEvaluationContext ec = super.createEvaluationContextInternal(authentication, mi);
		ec.setVariable("mss", new MssSecurityExpressionMethods(managerDao, authentication));		
		return ec;
	}
}
