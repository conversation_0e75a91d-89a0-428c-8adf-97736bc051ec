package com.adins.mss.provider;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.api.ManagerDAO;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.model.AmMsuser;

public class MssSecurityExpressionMethods extends BaseLogic {
	
	private ManagerDAO managerDao;
	private final Authentication authentication;
	
	private static final String QUERY_TASKH_BY_TASKID = "SELECT U.LOGIN_ID FROM TR_TASK_H H WITH (NOLOCK) LEFT JOIN AM_MSUSER U WITH (NOLOCK) ON H.UUID_MS_USER=U.UUID_MS_USER WHERE H.TASK_ID = :taskId";
	private static final String QUERY_TASKH_BY_UUID = "SELECT U.LOGIN_ID FROM TR_TASK_H H WITH (NOLOCK) LEFT JOIN AM_MSUSER U WITH (NOLOCK) ON H.UUID_MS_USER=U.UUID_MS_USER WHERE H.UUID_TASK_H = :uuidTaskH";
	private static final String QUERY_FORM_AUTHORIZED = "SELECT 1 FROM MS_FORMOFGROUP FOG WITH (NOLOCK) INNER JOIN AM_MSGROUP G WITH (NOLOCK) ON FOG.UUID_MS_GROUP = G.UUID_MS_GROUP INNER JOIN AM_MEMBEROFGROUP MOG WITH (NOLOCK) ON MOG.UUID_MS_GROUP=G.UUID_MS_GROUP INNER JOIN AM_MSUSER U WITH (NOLOCK) ON MOG.UUID_MS_USER=U.UUID_MS_USER WHERE FOG.UUID_FORM = :uuidForm AND LOGIN_ID=:loginId";
	
	public MssSecurityExpressionMethods(ManagerDAO managerDao, Authentication authentication) {
		this.authentication = authentication;
		this.managerDao = managerDao;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean hasTaskId(String taskId) {
		Object[][] params = { {Restrictions.eq("taskId", taskId)} };
		List<Map<String, Object>> resultList = this.managerDao.selectAllNativeString(QUERY_TASKH_BY_TASKID, params);
		
		for (Map<String, Object> record : resultList) {
			if (!StringUtils.equalsIgnoreCase((String) record.get("d0"), this.authentication.getName())) {
				return false;
			}
		}
		
		return true;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean hasTaskUuid(String uuidTaskH) {
		if (!NumberUtils.isDigits(uuidTaskH)){
			return true;
		}
		
		Object[][] params = { {Restrictions.eq("uuidTaskH", uuidTaskH)} };
		List<Map<String, Object>> resultList = this.managerDao.selectAllNativeString(QUERY_TASKH_BY_UUID, params);
		
		for (Map<String, Object> record : resultList) {
			if (!StringUtils.equalsIgnoreCase((String) record.get("d0"), this.authentication.getName())) {
				return false;
			}
		}
		
		return true;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean hasFormUuid(String uuidForm) {
		Object[][] params = { {Restrictions.eq("uuidForm", uuidForm)}, {Restrictions.eq("loginId", this.authentication.getName())} };		
		List<Map<String, Object>> resultList = this.managerDao.selectAllNativeString(QUERY_FORM_AUTHORIZED, params);
		
		return (resultList != null && !resultList.isEmpty());
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED, readOnly=true)
	public boolean isValidCallerId(String callerId) {
		Object[][] params = { {Restrictions.eq("loginId", this.authentication.getName())} };
		AmMsuser user = this.managerDao.selectOne(AmMsuser.class, params);
		if (user == null){
			return false;
		}
		
		//using startsWithIgnoreCase to support Multitenancy callerId 'UUID@TENANT'
		return (StringUtils.startsWithIgnoreCase(callerId, String.valueOf(user.getUuidMsUser())));
	}
}
