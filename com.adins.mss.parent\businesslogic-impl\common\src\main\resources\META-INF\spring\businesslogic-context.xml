<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
	http://www.springframework.org/schema/aop
	http://www.springframework.org/schema/aop/spring-aop.xsd">
	
	
	<bean id="GenericApprovalLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericApprovalLogic" scope="singleton" parent="BaseLogicBean" >
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="geocoder" ref="GenericGeolocationLogicBean"/>
		<property name="taskServiceLogic" ref="GenericTaskServiceLogicBean"/>
	</bean>
	<bean id="GenericSubmitApprovalLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericSubmitApprovalLogic" scope="singleton" parent="BaseLogicBean" >
		<property name="workflowHelper" ref="GenericWorkflowHelperBean" />
	</bean>
    <bean id="GenericCmsLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericCmsLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericGetImageLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericGetImageLogic" scope="singleton" parent="BaseLogicBean">
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
		<property name="lazyLoadLogic" ref="GenericLazyLoadLogicBean" />
    </bean>
	<bean id="GenericGetVerificationTaskListLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericGetVerificationTaskListLogic" scope="singleton" parent="BaseLogicBean">
		<property name="taskVerificationLogic" ref="GenericTaskVerificationLogicBean"/>
		<property name="geocoder" ref="GenericGeolocationLogicBean"/>
	</bean>
	<bean id="GenericSubmitVerificationLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericSubmitVerificationLogic" scope="singleton" parent="BaseLogicBean">
		<property name="taskVerificationLogic" ref="GenericTaskVerificationLogicBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>
	<bean id="GenericLocationHistoryUserLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLocationHistoryUserLogic" scope="singleton" parent="BaseLogicBean">
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>
    <bean id="GenericLoginUserLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLoginUserLogic" scope="singleton" parent="BaseLogicBean">
		<property name="globalLogic" ref="GenericGlobalLogicBean"/>
    	<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
    	<property name="ldapLogic" ref="DefaultLdapBean" />
    	<property name="commonLogic" ref="GenericCommonLogicBean" />
    </bean>
    <bean id="GenericLovServiceLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLovServiceLogic" scope="singleton" parent="BaseLogicBean" />
    <bean id="GenericLuAreaLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLuAreaLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericLuBranchLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLuBranchLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericLuDealerLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLuDealerLogic" scope="singleton" parent="BaseLogicBean"/>	
	<bean id="GenericLuJobLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLuJobLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericLuLovLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLuLovLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericLuQuestionLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLuQuestionLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericLuUserLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLuUserLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericLuReportLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLuReportLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericNewConfinsUserManagementLogic" class="com.adins.mss.businesslogic.impl.common.GenericNewConfinsUserManagementLogic" scope="singleton" parent="BaseLogicBean" />
    <bean id="GenericPrintTemplateBean" class="com.adins.mss.businesslogic.impl.common.GenericPrintTemplateLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericQuestionSetLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericQuestionSetLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericRefreshTaskLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericRefreshTaskLogic" scope="singleton" parent="BaseLogicBean"/>
    <bean id="GenericSchemeLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericSchemeLogic" scope="singleton" parent="BaseLogicBean" />
    <bean id="GenericSubmitTaskLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericSubmitTaskLogic" scope="singleton" parent="BaseLogicBean"> 
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="geocoder" ref="GenericGeolocationLogicBean"/>
		<property name="userLogic" ref="GenericUserLogicBean"/>
		<property name="globalLogic" ref="GenericGlobalLogicBean"/>
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
		<property name="geocodingConversionLogic" ref="GenericGeocodingConversionLogicBean" />
		<property name="taskServiceLogic" ref="GenericTaskServiceLogicBean" />
		<property name="submitLayerLogic" ref="GenericSubmitLayerLogicBean" />
		<property name="taskDistributionLogic" ref="GenericTaskDistributionLogicBean" />
		<property name="lazyLoadLogic" ref="GenericLazyLoadLogicBean" />
	</bean>
	<bean id="GenericSyncServiceLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericSyncServiceLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericTaskListLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericTaskListLogic" scope="singleton" parent="BaseLogicBean">
		<property name="geocoder" ref="GenericGeolocationLogicBean"/>
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="inquiryTaskSurveyLogic" ref="GenericInquiryTaskSurveyLogicBean" />
	</bean>
	<bean id="GenericTaskServiceLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericTaskServiceLogic" scope="singleton" parent="BaseLogicBean">
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="taskDistributionLogic" ref="GenericTaskDistributionLogicBean" />
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
		<property name="geocodingConversionLogic" ref="GenericGeocodingConversionLogicBean" />
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericUpdateReadOpenTaskLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericUpdateReadOpenTaskLogic" scope="singleton" parent="BaseLogicBean">
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>	
	<bean id="GenericVerificationLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericVerificationLogic" scope="singleton" parent="BaseLogicBean">
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericUserLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericUserLogic" scope="singleton" parent="BaseLogicBean">
		<property name="taskVerificationLogic" ref="GenericTaskVerificationLogicBean"/>
		<property name="unassignTaskLogic" ref="GenericUnassignTaskLogicBean" />
	</bean>
	
<!-- Modul COMMON -->
	<bean id="GenericImageStorageLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericImageStorageLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericCommonLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericCommonLogic" scope="singleton" parent="BaseLogicBean">
		<property name="geocoder" ref="GenericGeolocationLogicBean"/>
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
	</bean>
	<bean id="GenericBranchLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericBranchLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericDealerLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericDealerLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericFormLogic" class="com.adins.mss.businesslogic.impl.common.GenericFormLogic" scope="singleton" parent="BaseLogicBean">
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>
	<bean id="GenericTaskAssignmentLogic" class="com.adins.mss.businesslogic.impl.common.GenericTaskAssignmentLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericGeolocationLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericGeolocationLogic" scope="singleton" parent="BaseLogicBean">
		<property name="geocoder" ref="GoogleCellIdGeocoderBean" />
	</bean>	
	<bean id="GenericFileLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericFileLogic" scope="singleton" parent="BaseLogicBean">
		<property name="commonLogic" ref="GenericCommonLogicBean" />    
	</bean>
	<bean id="GenericGeocodingConversionLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericGeocodingConversionLogic" scope="singleton" parent="BaseLogicBean">
		<property name="context" ref="GeoApiContextBean"/>
	</bean>
	<bean id="GenericHolidayLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericHolidayLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericLovLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLovLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericMappingFormLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericMappingFormLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericQuestionGroupLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericQuestionGroupLogic" scope="singleton" parent="BaseLogicBean" />
	<bean id="GenericQuestionListLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericQuestionListLogic" scope="singleton" parent="BaseLogicBean" />
	<bean id="GenericRegionLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericRegionLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericTaskDistributionLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericTaskDistributionLogic" scope="singleton" parent="BaseLogicBean">
		<property name="globalLogic" ref="GenericGlobalLogicBean"/>
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericTaskVerificationLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericTaskVerificationLogic" scope="singleton" parent="BaseLogicBean">
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="geocoder" ref="GenericGeolocationLogicBean" />
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
		<property name="taskDistributionLogic" ref="GenericTaskDistributionLogicBean" />
		<property name="unassignTaskLogic" ref="GenericUnassignTaskLogicBean" />
		<property name="taskServiceLogic" ref="GenericTaskServiceLogicBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>
	<bean id="GenericTemplateEmailLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericTemplateEmailLogic" scope="singleton" parent="BaseLogicBean"/>	
	<bean id="GoogleCellIdGeocoderBean" class="com.adins.framework.tool.geolocation.GoogleCellIdGeocoder" />	
	<bean id="GenericAbsensiUserLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericAbsensiUserLogic" scope="singleton" parent="BaseLogicBean">
		<property name="geocoder" ref="GenericGeolocationLogicBean"/>
	</bean>
	<bean id="GenericUploadTaskLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericUploadTaskLogic" scope="singleton" parent="BaseLogicBean">
		<property name="taskServiceLogic" ref="GenericTaskServiceLogicBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="viewHistoryLogic" ref="GenericViewHistoryLogicBean" />
	</bean>
	<bean id="GenericReportDownloadLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericReportDownloadLogic" scope="singleton" parent="BaseLogicBean">
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>
	<bean id="GenericUploadTaskLogLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericUploadTaskLogLogic" scope="singleton" parent="BaseLogicBean" />
	<bean id="GenericLazyLoadLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLazyLoadLogic" scope="singleton" parent="BaseLogicBean">	
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>
	<bean id="GenericReportResultLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericReportResultLogic" scope="singleton" parent="BaseLogicBean" />
	<bean id="GenericViewLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericViewLogic" scope="singleton" parent="BaseLogicBean">
		<property name="geocoder" ref="GenericGeolocationLogicBean"/>
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericPushNotificationLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericPushNotificationLogic" scope="singleton" parent="BaseLogicBean" >
		<property name="userLogic" ref="GenericUserLogicBean"/>
	</bean>
	<bean id="GenericLuOnlineLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericLuOnlineLogic" scope="singleton" parent="BaseLogicBean" >
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericMappingUserDealerBean" class="com.adins.mss.businesslogic.impl.common.GenericMappingUserDealerLogic" scope="singleton" parent="BaseLogicBean" />
	<bean id="GenericMappingUserKatBean" class="com.adins.mss.businesslogic.impl.common.GenericMappingUserKatLogic" scope="singleton" parent="BaseLogicBean">
	    <property name="commonLogic" ref="GenericCommonLogicBean"/>
	</bean>
	<bean id="GenericAddTaskFromStagingBean" class="com.adins.mss.businesslogic.impl.common.GenericAddTaskFromStagingLogic" scope="singleton" parent="BaseLogicBean" >
		<property name="taskServiceLogic" ref="GenericTaskServiceLogicBean" />
		<property name="taskDistributionLogic" ref="GenericTaskDistributionLogicBean" />
	</bean>
	<bean id="GenericPushSyncBean" class="com.adins.mss.businesslogic.impl.common.GenericPushSyncLogic" scope="singleton" parent="BaseLogicBean"></bean>
	<bean id="GenericInvitationalESignLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericInvitationalESignLogic" scope="singleton" parent="BaseLogicBean">
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericAddTaskMainDealerLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericAddTaskMainDealerLogic" scope="singleton" parent="BaseLogicBean">
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericApprovalSettingLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericApprovalSettingLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericSubmitLayerLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericSubmitLayerLogic" scope="singleton" parent="BaseLogicBean">
    	<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
    	<property name="commonLogic" ref="GenericCommonLogicBean" />
    	<property name="geocoder" ref="GenericGeolocationLogicBean" />
    	<property name="createTaskLogic" ref="GenericCreateTaskLogicBean" />
    	<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
    	<property name="kbijDataLogic" ref="GenericKbijDataLogicBean" />
    </bean>
    <bean id="GenericReportTaskPreSurveyLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericReportTaskPreSurveyLogic" scope="singleton" parent="BaseLogicBean" />
    <bean id="GenericReportTaskPromiseToSurveyLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericReportTaskPromiseToSurveyLogic" scope="singleton" parent="BaseLogicBean" />
    <bean id="GenericReportTaskOtsLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericReportTaskOtsLogic" scope="singleton" parent="BaseLogicBean">
    	<property name="commonLogic" ref="GenericCommonLogicBean" />
    </bean>
<!-- HELPER -->
	<bean id="GenericTaskHelperBean" class="com.adins.mss.helper.impl.GenericTaskHelper" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericWorkflowHelperBean" class="com.adins.mss.helper.impl.GenericWorkflowHelper" scope="singleton" parent="BaseLogicBean"/>
    <bean id="DefaultLdapBean" class="com.adins.framework.tool.ldap.UnboundidLdapLogicImpl">
    	<property name="host" value="${ldap.host}" />
    	<property name="port" value="${ldap.port}" />
    </bean>
<!-- INTERFACING -->
	<bean id="interfacing.StagingTableBean" class="com.adins.mss.businesslogic.impl.interfacing.GenericStagingTableLogic" scope="singleton" parent="BaseLogicBean">
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
	</bean>
	<bean id="interfacing.NcFormBean" class="com.adins.mss.businesslogic.impl.interfacing.IntNcFormLogic" scope="singleton" parent="BaseLogicBean">
		<aop:scoped-proxy/>
		<property name="intFormLogic" ref="interfacing.StagingFormBean" />
		<property name="stagingTableLogic" ref="interfacing.StagingTableBean" />
		<property name="superAdminDms" value="${constant.userupddms}" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="insertHistoryLogic" ref="GenericInsertHistoryLogicBean" />
		<property name="lazyLoadLogic" ref="GenericLazyLoadLogicBean" />
		<property name="invitationalESignLogic" ref="GenericInvitationalESignLogicBean" />
	</bean>
	<bean id="interfacing.StagingFormBean" class="com.adins.mss.businesslogic.impl.interfacing.IntStagingFormLogic" scope="singleton" parent="BaseLogicBean">
		<aop:scoped-proxy/>
		<property name="luOnlineLogic" ref="GenericLuOnlineLogicBean" />
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
		<property name="stagingTableLogic" ref="interfacing.StagingTableBean" />
	</bean>
	<bean id="interfacing.NoneFormBean" class="com.adins.mss.businesslogic.impl.interfacing.IntNoneFormLogic" scope="singleton" parent="BaseLogicBean">
		<aop:scoped-proxy/>
		<property name="intFormLogic" ref="interfacing.StagingFormBean" />
		<property name="luOnlineLogic" ref="GenericLuOnlineLogicBean" />
	</bean>
	<bean id="interfacing.FormFactoryBean" class="com.adins.mss.businesslogic.impl.interfacing.IntFormLogicFactoryBean">
<!-- 		<aop:scoped-proxy/> -->
		<property name="interfaceType" value="${interface.type}" />
    </bean>
    
<!-- Report -->
	<bean id="GenericReportUserMemberLogic" class="com.adins.mss.businesslogic.impl.common.GenericReportUserMemberLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericReportAbsensiLogic" class="com.adins.mss.businesslogic.impl.common.GenericReportAbsensiLogic" scope="singleton" parent="BaseLogicBean">
		<property name="globalLogic" ref="GenericGlobalLogicBean"/>
	</bean>
	<bean id="GenericReportSlaLogic" class="com.adins.mss.businesslogic.impl.common.GenericReportSlaLogic" scope="singleton" parent="BaseLogicBean"/>

<!-- PROVIDER -->	
	<bean id="CustomAuthenticationProvider" class="com.adins.mss.provider.CustomAuthenticationProvider" scope="singleton" parent="BaseLogicBean" >
		<property name="globalLogic" ref="GenericGlobalLogicBean"/>
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="ldapLogic" ref="DefaultLdapBean" />
	</bean>
	
	<bean id="GenericSlaTaskPoloLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericSlaTaskPoloLogic" scope="singleton" parent="BaseLogicBean">
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>
	
	
	
	<!-- GEOAPICONTEXT -->	
	<bean id="GeoApiContextBean" class="com.google.maps.GeoApiContext" scope="singleton">
		<property name="apiKey" value="${google.api.staticmaps}"/>
<!-- 		<property name="proxy" ref="ProxyBean"/> -->
	</bean>
<!-- 	<bean id="ProxyBean" class="java.net.Proxy" scope="singleton"> -->
<!-- 		<constructor-arg index="0" value="HTTP"/> -->
<!-- 		<constructor-arg index="1" ref="InetSocketAddressBean"/> -->
<!-- 	</bean> -->
<!-- 	<bean id="InetSocketAddressBean" class="java.net.InetSocketAddress" factory-method="createUnresolved" scope="singleton">   -->
<!-- 	  	<constructor-arg index="0" value="${proxy.host}" />  -->
<!-- 		<constructor-arg index="1" value="${proxy.port}" />  -->
<!-- 	</bean> -->
<!-- 	<bean id="InetSocketAddressBean" class="java.net.InetSocketAddress" scope="singleton"> -->
<!-- 		<constructor-arg index="1" value="${proxy.port}" /> -->
<!-- 		<constructor-arg index="0" value="${proxy.host}" /> -->
<!-- 	</bean> -->
<!-- Modul FUNCTION -->
	<bean id="GenericKbijDataLogicBean" class="com.adins.mss.function.GenericKbijDataLogic" scope="singleton" parent="BaseLogicBean"/>
	
</beans>
	