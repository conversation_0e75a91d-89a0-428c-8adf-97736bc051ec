<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="common.file.getListUpdatedPO">
		<query-param name="isDeleted" type="string"/>
		<query-param name="datetime" type="string"/>
		select ID, RULE_ID, RULE_NAME from STAGING_RULE_FILE
			where IS_DELETED = :isDeleted
					and ISNULL(DTM_UPD, DTM_CRT) >= :datetime
					and IS_EXPORT_FILE = '1'
	</sql-query>
	
	<sql-query name="common.file.getFileOtr">
		<query-param name="ipPo" type="string"/>
		select top 1 RULE_DATA_ASSET_PRICE from STAGING_PRODUCT_OFFERING with (nolock)
			where ID = :ipPo
	</sql-query>
	
	<sql-query name="common.file.getFileDp">
		<query-param name="ipPo" type="string"/>
		select top 1 RULE_DATA_MIN_DP from STAGING_PRODUCT_OFFERING with (nolock)
			where ID = :ipPo
	</sql-query>
	
	<sql-query name="common.file.getFileTc">
		<query-param name="ipPo" type="string"/>
		select top 1 RULE_DATA_APP_TC from STAGING_PRODUCT_OFFERING with (nolock)
			where ID = :ipPo
	</sql-query>
	
	<sql-query name="common.file.getFileScoring">
		<query-param name="ipPo" type="string"/>
		select top 1 RULE_SCORING from STAGING_PRODUCT_OFFERING with (nolock)
			where ID = :ipPo
	</sql-query>
	
	<sql-query name="common.file.getFileMatrix">
		<query-param name="ipPo" type="string"/>
		select top 1 SCORING_MATRIX from STAGING_PRODUCT_OFFERING with (nolock)
			where ID = :ipPo
	</sql-query>
	
	<sql-query name="common.file.getFileData">
		<query-param name="id" type="string"/>
		select DATA from STAGING_RULE_FILE with (nolock) where ID = :id
	</sql-query>
	
	<sql-query name="common.file.updateIsExport">
		<query-param name="id" type="string"/>
		update STAGING_RULE_FILE set IS_EXPORT_FILE = '0' where ID = :id
	</sql-query>
	

</hibernate-mapping>