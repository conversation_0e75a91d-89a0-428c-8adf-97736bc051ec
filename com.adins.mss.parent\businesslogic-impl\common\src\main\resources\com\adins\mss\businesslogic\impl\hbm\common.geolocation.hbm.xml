<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

<sql-query name="common.geolocation.updateTrackingNotFound">
	<query-param name="mcc" type="int"/>
	<query-param name="mnc" type="int"/>
	<query-param name="lac" type="int"/>
	<query-param name="cellId" type="int"/>
   update TR_LOCATIONHISTORY
   	  set IS_CONVERTED = '1'
   	where MCC = :mcc
      and MNC = :mnc
      and LAC = :lac
      and CELL_ID = :cellId
      and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
      and is_gps = '0'	
</sql-query>

<sql-query name="common.geolocation.updateTracking">
	<query-param name="latitude" type="double"/>
	<query-param name="longitude" type="double"/>
	<query-param name="accuracy" type="int"/>
	<query-param name="provider" type="string"/>
	<query-param name="mcc" type="int"/>
	<query-param name="mnc" type="int"/>
	<query-param name="lac" type="int"/>
	<query-param name="cellId" type="int"/>
    UPDATE TR_LOCATIONHISTORY
       SET LATITUDE = :latitude,
		   LONGITUDE = :longitude,
		   ACCURACY = :accuracy,
		   GEOLOCATION_PROVIDER = :provider,
		   IS_CONVERTED = '1'		   
    WHERE MCC = :mcc
      and MNC = :mnc
      and LAC = :lac
      and CELL_ID = :cellId
      and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
      and is_gps = '0'
</sql-query>

<sql-query name="common.geolocation.updateTaskDNotFound">
	<query-param name="mcc" type="int"/>
	<query-param name="mnc" type="int"/>
	<query-param name="lac" type="int"/>
	<query-param name="cellId" type="int"/>
   update TR_TASK_D
   	  set IS_CONVERTED = '1'
   	where MCC = :mcc
      and MNC = :mnc
      and LAC = :lac
      and CELL_ID = :cellId
      and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
      and is_gps = '0'	
</sql-query>

<sql-query name="common.geolocation.updateTaskD">
	<query-param name="latitude" type="double"/>
	<query-param name="longitude" type="double"/>
	<query-param name="accuracy" type="int"/>
	<query-param name="provider" type="string"/>
	<query-param name="mcc" type="int"/>
	<query-param name="mnc" type="int"/>
	<query-param name="lac" type="int"/>
	<query-param name="cellId" type="int"/>
    UPDATE TR_TASK_D
       SET LATITUDE = :latitude,
		   LONGITUDE = :longitude,
		   ACCURACY = :accuracy,
		   GEOLOCATION_PROVIDER = :provider,
		   IS_CONVERTED = '1'		   
    WHERE MCC = :mcc
      and MNC = :mnc
      and LAC = :lac
      and CELL_ID = :cellId
      and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
      and is_gps = '0'
</sql-query>

<sql-query name="common.geolocation.updateTaskDLobNotFound">
	<query-param name="mcc" type="int"/>
	<query-param name="mnc" type="int"/>
	<query-param name="lac" type="int"/>
	<query-param name="cellId" type="int"/>
   update TR_TASKDETAILLOB
   	  set IS_CONVERTED = '1'
   	where MCC = :mcc
      and MNC = :mnc
      and LAC = :lac
      and CELL_ID = :cellId
      and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
      and is_gps = '0'	
</sql-query>

<sql-query name="common.geolocation.updateTaskDLob">
	<query-param name="latitude" type="double"/>
	<query-param name="longitude" type="double"/>
	<query-param name="accuracy" type="int"/>
	<query-param name="provider" type="string"/>
	<query-param name="mcc" type="int"/>
	<query-param name="mnc" type="int"/>
	<query-param name="lac" type="int"/>
	<query-param name="cellId" type="int"/>
    UPDATE TR_TASKDETAILLOB
       SET LATITUDE = :latitude,
		   LONGITUDE = :longitude,
		   ACCURACY = :accuracy,
		   GEOLOCATION_PROVIDER = :provider,
		   IS_CONVERTED = '1'		   
    WHERE MCC = :mcc
      and MNC = :mnc
      and LAC = :lac
      and CELL_ID = :cellId
      and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
      and is_gps = '0'
</sql-query>

<sql-query name="common.geolocation.updateTaskRejected">
	<query-param name="latitude" type="double"/>
	<query-param name="longitude" type="double"/>
	<query-param name="accuracy" type="int"/>
	<query-param name="provider" type="string"/>
	<query-param name="mcc" type="int"/>
	<query-param name="mnc" type="int"/>
	<query-param name="lac" type="int"/>
	<query-param name="cellId" type="int"/>
    UPDATE TR_TASKREJECTEDDETAIL
       SET LATITUDE = :latitude,
		   LONGITUDE = :longitude,
		   ACCURACY = :accuracy,
		   GEOLOCATION_PROVIDER = :provider
    WHERE MCC = :mcc
      and MNC = :mnc
      and LAC = :lac
      and CELL_ID = :cellId
      and is_gps = '0'
</sql-query>

<sql-query name="common.geolocation.updateTaskDetailLobNotFound">
	<query-param name="mcc" type="int"/>
	<query-param name="mnc" type="int"/>
	<query-param name="lac" type="int"/>
	<query-param name="cellId" type="int"/>
   update TR_TASKDETAILLOB
   	  set IS_CONVERTED = '1'
   	where MCC = :mcc
      and MNC = :mnc
      and LAC = :lac
      and CELL_ID = :cellId
      and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
      and is_gps = '1'
      and (LATITUDE = '0.000000' OR LONGITUDE = '0.000000')	
</sql-query>

<sql-query name="common.geolocation.updateTaskDetailLob">
	<query-param name="latitude" type="double"/>
	<query-param name="longitude" type="double"/>
	<query-param name="accuracy" type="int"/>
	<query-param name="provider" type="string"/>
	<query-param name="mcc" type="int"/>
	<query-param name="mnc" type="int"/>
	<query-param name="lac" type="int"/>
	<query-param name="cellId" type="int"/>
    UPDATE TR_TASKDETAILLOB
       SET LATITUDE = :latitude,
		   LONGITUDE = :longitude,
		   ACCURACY = :accuracy,
		   GEOLOCATION_PROVIDER = :provider,
		   IS_CONVERTED = '1'		   
    WHERE MCC = :mcc
      and MNC = :mnc
      and LAC = :lac
      and CELL_ID = :cellId
      and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
      and is_gps = '1'
      and (LATITUDE = '0.000000' OR LONGITUDE = '0.000000')
</sql-query>

<sql-query name="common.geolocation.updateLatLongTaskSurveyData">
    update ttsd 
		SET ttsd.HOME_LATITUDE = b.HOME_LATITUDE, 
		ttsd.HOME_LONGITUDE = b.HOME_LATITUDE, 
		ttsd.OFFICE_LATITUDE = b.OFFICE_LATITUDE , 
		ttsd.OFFICE_LONGITUDE = b.OFFICE_LONGITUDE, 
		ttsd.LEGAL_ADDR_LATITUDE = b.LEGAL_ADDR_LATITUDE, 
		ttsd.LEGAL_ADDR_LONGITUDE = b.LEGAL_ADDR_LONGITUDE, 
		ttsd.DRIVEWAY_LATITUDE = b.DRIVEWAY_LATITUDE, 
		ttsd.DRIVEWAY_LONGITUDE = b.DRIVEWAY_LONGITUDE, 
		ttsd.VEHICLE_LATITUDE = b.VEHICLE_LATITUDE, 
		ttsd.VEHICLE_LONGITUDE = b.VEHICLE_LONGITUDE 
		
		from tr_tasksurveydata ttsd join( 
		select ttdl.uuid_task_h, max( CASE 
		WHEN mat.asset_tag_name = 'HOME' and HOME_LATITUDE is null 
		THEN ttdl.LATITUDE 
		ELSE HOME_LATITUDE 
		END) HOME_LATITUDE, 
		max(CASE 
		WHEN mat.asset_tag_name = 'HOME' and HOME_LONGITUDE is null 
		THEN ttdl.longitude 
		ELSE HOME_LONGITUDE 
		END )HOME_LONGITUDE, 
		max( CASE 
		WHEN mat.asset_tag_name = 'STREET' and DRIVEWAY_LATITUDE is null 
		THEN ttdl.LATITUDE 
		ELSE DRIVEWAY_LATITUDE 
		END) DRIVEWAY_LATITUDE, 
		max( CASE 
		WHEN mat.asset_tag_name = 'STREET' and DRIVEWAY_LONGITUDE is null 
		THEN ttdl.longitude 
		ELSE DRIVEWAY_LONGITUDE 
		END )DRIVEWAY_LONGITUDE, 
		
		max(	CASE 
		WHEN mat.asset_tag_name = 'OFFICE' and OFFICE_LATITUDE is null 
		THEN ttdl.LATITUDE 
		ELSE OFFICE_LATITUDE 
		END )OFFICE_LATITUDE, 
		max( CASE 
		WHEN mat.asset_tag_name = 'OFFICE' and OFFICE_LONGITUDE is null 
		THEN ttdl.longitude 
		ELSE OFFICE_LONGITUDE 
		END )OFFICE_LONGITUDE, 
		max( CASE 
		WHEN mat.asset_tag_name = 'VEHICLE' and VEHICLE_LATITUDE is null 
		THEN ttdl.LATITUDE 
		ELSE VEHICLE_LATITUDE 
		END) VEHICLE_LATITUDE, 
		max( CASE 
		WHEN mat.asset_tag_name = 'VEHICLE' and VEHICLE_LONGITUDE is null 
		THEN ttdl.longitude 
		ELSE VEHICLE_LONGITUDE 
		END) VEHICLE_LONGITUDE, 
		max(CASE 
		WHEN mat.asset_tag_name = 'IDENTITY' and LEGAL_ADDR_LATITUDE is null 
		THEN ttdl.LATITUDE 
		ELSE LEGAL_ADDR_LATITUDE 
		END) LEGAL_ADDR_LATITUDE, 
		max( CASE 
		WHEN mat.asset_tag_name = 'IDENTITY' and LEGAL_ADDR_LONGITUDE is null 
		THEN ttdl.longitude 
		ELSE LEGAL_ADDR_LONGITUDE 
		END) LEGAL_ADDR_LONGITUDE 
	FROM tr_tasksurveydata tts 
	JOIN TR_TASKDETAILLOB ttdl on tts.uuid_task_id = ttdl.uuid_task_h 
	JOIN MS_QUESTION mq on mq.uuid_question = ttdl.question_id 
	JOIN MS_ASSETTAG mat on mat.uuid_asset_tag = mq.uuid_asset_tag 
	WHERE (HOME_LATITUDE is null and HOME_LONGITUDE is null) or 
	(OFFICE_LATITUDE is null and OFFICE_LONGITUDE is null) or 
	(LEGAL_ADDR_LATITUDE is null and LEGAL_ADDR_LONGITUDE is null) or 
	(DRIVEWAY_LATITUDE is null and DRIVEWAY_LONGITUDE is null) or 
	(VEHICLE_LATITUDE is null and VEHICLE_LONGITUDE is null)
	GROUP BY ttdl.uuid_task_h 
	) b on b.uuid_task_h = ttsd.uuid_task_id
</sql-query>

</hibernate-mapping>