<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
<sql-query name="common.getGsValue">
<query-param name="gsCode" type="string"/>
SELECT GS_VALUE, UUID_GENERAL_SETTING FROM AM_GENERALSETTING with (nolock) WHERE IS_ACTIVE='1' AND GS_CODE = :gsCode
</sql-query>

<sql-query name="common.dashboard.getUsers">
<query-param name="uuidSpv" type="long" />
	WITH N AS (
		SELECT
			msu.UUID_MS_USER,
			msu.FULL_NAME,
			msu.IS_LOGGED_IN,
			msu.UUID_MS_USER AS HIRARKI,
			CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2,
			0 as LEVEL,
			msu.LOGIN_ID,
			msu.INITIAL_NAME,
			msu_spv.LOGIN_ID login_id_spv,
			msu_spv.FULL_NAME full_name_spv,
			msu.UUID_JOB
	    FROM AM_MSUSER msu with (nolock) join AM_MSUSER msu_spv with (nolock) on msu.SPV_ID = msu_spv.UUID_MS_USER
	   WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
	UNION ALL
		SELECT
			msu2.UUID_MS_USER,
			msu2.FULL_NAME,
			msu2.IS_LOGGED_IN,
			N.HIRARKI,
			N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)),
			N.LEVEL+1,
			msu2.LOGIN_ID,
			msu2.INITIAL_NAME,
			N.LOGIN_ID login_id_spv,
			N.FULL_NAME full_name_spv,
			msu2.UUID_JOB
		FROM AM_MSUSER msu2 with (nolock), N
	   WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
	)
	select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID, INITIAL_NAME, login_id_spv, full_name_spv
	  from N join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
	  where j.IS_FIELD_PERSON = '1'
</sql-query>

<sql-query name="common.dashboard.getAreaOfUser">
<query-param name="uuidUser" type="long" />
   SELECT MA.AREA_TYPE_CODE, MA.LATITUDE, MA.LONGITUDE, MA.RADIUS
	 FROM MS_AREAOFUSER MAU with (nolock) 
		  LEFT JOIN MS_AREA MA with (nolock) ON MAU.UUID_AREA = MA.UUID_AREA
    WHERE MAU.UUID_MS_USER = :uuidUser
</sql-query>

<sql-query name="common.dashboard.getArea">
<query-param name="uuidUser" type="long" />
	SELECT MA.LATITUDE, MA.LONGITUDE, MAP.LATITUDE lat, MAP.LONGITUDE lng, MA.AREA_TYPE_CODE, MA.RADIUS
	  FROM MS_AREAOFUSER MAU with (nolock) 
		   LEFT OUTER JOIN MS_AREA MA with (nolock) ON MAU.UUID_AREA = MA.UUID_AREA
		   LEFT OUTER JOIN MS_AREAPATH MAP with (nolock) ON MA.UUID_AREA = MAP.UUID_AREA
	WHERE MAU.UUID_MS_USER = :uuidUser
</sql-query>

<!-- tambahan untuk add task from staging dan submit task to staging -->
	<sql-query name="common.configsubmittable.getListAnsRefId">
		<query-param name="uuidTaskH" type="long" />
		<!-- <query-param name="idForm" type="string" /> -->
			SELECT q.REF_ID,
				CASE 
					WHEN ans.CODE_ANSWER_TYPE in ( '001', '002', '003', '004', '005', '013', '015', '024', '025', '026' ) THEN td.TEXT_ANSWER
					WHEN ans.CODE_ANSWER_TYPE in ( '006', '009', '011' ) THEN lov.CODE
					WHEN ans.CODE_ANSWER_TYPE in ( '007', '008', '010', '012' ) THEN lov.CODE + ' | ' + td.TEXT_ANSWER
					ELSE td.TEXT_ANSWER
				END ANSWER, ans.code_answer_type, td.HAS_IMAGE, IMAGE_PATH, LOB_FILE, td.LOV_ID
			FROM (
				SELECT UUID_TASK_D, TEXT_ANSWER, LOV_ID, UUID_QUESTION, '0' AS HAS_IMAGE, null as IMAGE_PATH, null as LOB_FILE
					FROM TR_TASK_D WITH (NOLOCK)
					WHERE UUID_TASK_H = :uuidTaskH
				UNION
				SELECT UUID_TASK_DETAIL_LOB, TEXT_ANSWER, LOV_ID, QUESTION_ID, 
					CASE
						WHEN (LOB_FILE is not null) THEN '1'
						WHEN (IMAGE_PATH is not null) THEN '1'
						ELSE '0'
					END AS HAS_IMAGE, IMAGE_PATH, LOB_FILE
					FROM TR_TASKDETAILLOB WITH (NOLOCK)
					WHERE UUID_TASK_H = :uuidTaskH
				) td
				LEFT JOIN MS_QUESTION q WITH (NOLOCK) on td.UUID_QUESTION = q.UUID_QUESTION
				LEFT JOIN MS_ANSWERTYPE ans WITH (NOLOCK) on q.UUID_ANSWER_TYPE = ans.UUID_ANSWER_TYPE
				LEFT JOIN MS_LOV lov on td.LOV_ID = lov.UUID_LOV
		<!-- 		WHERE q.REF_ID in ( -->
		<!-- 				SELECT DISTINCT REF_ID -->
		<!-- 					FROM MS_CONFIGSUBMITTABLE WITH (NOLOCK) -->
		<!-- 					WHERE ID_FORM = :idForm -->
		<!-- 			) -->
	</sql-query>
	
	<sql-query name="common.configsubmittable.getCountListTable">
	<query-param name="idForm" type="string" />
		SELECT COUNT(DISTINCT UUID_CONFIG_SUBMIT_TABLE_ORDER) FROM MS_CONFIGSUBMITTABLE WITH (NOLOCK) WHERE ID_FORM = :idForm
	</sql-query>
	
	<sql-query name="common.configsubmittable.getListParams">
	<query-param name="idForm" type="string" />
		SELECT ord.TABLE_NAME, tbl.COLUMN_NAME, tbl.REF_ID, tbl.DEFAULT_ANSWER, tbl.DATE_FORMAT FROM MS_CONFIGSUBMITTABLE tbl WITH (NOLOCK)
			JOIN MS_CONFIGSUBMITTABLEORDER ord WITH (NOLOCK) on tbl.UUID_CONFIG_SUBMIT_TABLE_ORDER = ord.UUID_CONFIG_SUBMIT_TABLE_ORDER
			WHERE  tbl.ID_FORM = :idForm
			ORDER BY ord.TABLE_ORDER ASC
	</sql-query>
	
	<sql-query name="common.retrieveWfNextStatusCodes">
	<query-param name="uuidProcess" type="long" />
	<query-param name="startCode" type="string" />
		select a.ACTIVITY_CODE
		  from WF_PROCESSROUTE pr
			   inner join WF_ACTIVITY a on pr.UUID_ACTIVITY=a.UUID_ACTIVITY
		 where pr.UUID_PROCESS = :uuidProcess AND pr.ACTIVITY_ORDER > (
			select pr.ACTIVITY_ORDER
			  from WF_PROCESSROUTE pr
				   inner join WF_ACTIVITY a on pr.UUID_ACTIVITY=a.UUID_ACTIVITY
			WHERE pr.UUID_PROCESS = :uuidProcess AND a.ACTIVITY_CODE = :startCode
		 )
		 order by ACTIVITY_ORDER
	</sql-query>
	
	<sql-query name="common.retrieveAccuraryDetailLob">
		<query-param name="uuidTaskD" type="long"/>
		SELECT ACCURACY FROM TR_TASKDETAILLOB WHERE UUID_TASK_DETAIL_LOB = :uuidTaskD
	</sql-query>
	
	<sql-query name="common.retrieveAccuraryDetailLobFinal">
		<query-param name="uuidTaskD" type="long"/>
		SELECT ACCURACY FROM FINAL_TR_TASKDETAILLOB WHERE UUID_TASK_DETAIL_LOB = :uuidTaskD
	</sql-query>
	
	<sql-query name="common.getUuidFormHistory">
		<query-param name="uuidTaskH" type="long"/>
		<return-scalar column="uuid" type="long"/>
		select hist.uuid_form_history as uuid
		  from ms_formhistory hist
			   inner join tr_task_h h on hist.uuid_form=h.uuid_form and hist.form_version=h.form_version 
		 where uuid_task_h = :uuidTaskH
	</sql-query>
	
	<sql-query name="common.getBranchCoordinate">
		<query-param name="uuidTaskH" type="long"/>
		<return-scalar column="latitude" type="double"/>
		<return-scalar column="longitude" type="double"/>
		select b.latitude as latitude, b.longitude as longitude
		  from ms_branch b
			   inner join tr_task_h h on b.uuid_branch = h.uuid_branch 
		 where h.uuid_task_h = :uuidTaskH
	</sql-query>

	<sql-query name="common.datafiles.list">
		<query-param name="uuidBranchUser" type="string" />
		<query-param name="timestamp" type="date" />
		SELECT ID_DATAFILE, USR_CRT, DTM_CRT, USR_UPD, DTM_UPD, IS_ACTIVE, MAX_TIMESTAMP, FILE_URL, ALTERNATE_FILE_URL, HASH_SHA1
		  FROM (
			SELECT mdf.ID_DATAFILE, mdf.USR_CRT, mdf.DTM_CRT, mdf.USR_UPD, mdf.DTM_UPD, mdf.IS_ACTIVE, mdf.MAX_TIMESTAMP, mdf.FILE_URL, mdf.ALTERNATE_FILE_URL, mdf.HASH_SHA1
			  FROM MS_MOBILEDATAFILESOFBRANCH mdfob
			       INNER JOIN MS_MOBILEDATAFILES mdf on mdfob.id_datafile = mdf.id_datafile
			 WHERE mdfob.UUID_BRANCH = :uuidBranchUser
			UNION
			SELECT mdf.ID_DATAFILE, mdf.USR_CRT, mdf.DTM_CRT, mdf.USR_UPD, mdf.DTM_UPD, mdf.IS_ACTIVE, mdf.MAX_TIMESTAMP, mdf.FILE_URL, mdf.ALTERNATE_FILE_URL, mdf.HASH_SHA1
			  from MS_MOBILEDATAFILESOFBRANCH mdfob
			       inner join MS_MOBILEDATAFILES mdf on mdfob.id_datafile = mdf.id_datafile
				   inner join MS_BRANCH br on mdfob.UUID_BRANCH = br.UUID_BRANCH AND br.BRANCH_CODE = 'HO' --HO = link yg berlaku untuk semua cabang
		) TBL
		WHERE ISNULL(DTM_UPD, DTM_CRT) > :timestamp
		ORDER BY ID_DATAFILE
	</sql-query>

</hibernate-mapping>