<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

<sql-query name="common.locationhistory.listEmptyCoordinate">
     select top 1000
     	    mcc as mcc,
           	mnc as mnc,
            lac as lac,
            cell_id as cellId
       FROM TR_LOCATIONHISTORY with (nolock)
	  where is_gps = '0'
		and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
</sql-query>

<sql-query name="common.locationhistory.listTaskLobEmptyCoordinate">
     select top 1000
     	    mcc as mcc,
           	mnc as mnc,
            lac as lac,
            cell_id as cellId
       FROM TR_TASKDETAILLOB with (nolock)
	  where is_gps = '1'
	    and (LATITUDE = '0.000000' OR LONGITUDE = '0.000000')
		and (IS_CONVERTED = '0' OR IS_CONVERTED IS NULL)
</sql-query>

</hibernate-mapping>