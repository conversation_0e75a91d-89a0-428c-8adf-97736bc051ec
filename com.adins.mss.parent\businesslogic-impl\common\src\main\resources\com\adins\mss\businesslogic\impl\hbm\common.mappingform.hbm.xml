<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="common.mappingform.getQuestionQSet">
		<query-param name="paramUuidForm" type="long"/>
		<query-param name="paramFormVersion" type="string"/>
		select mfqs.UUID_QUESTION, mfqs.QUESTION_LABEL, mfqs.UUID_ANSWER_TYPE 
		from MS_FORMHISTORY mfh with (nolock)
			join MS_FORMQUESTIONSET mfqs with (nolock) on mfh.UUID_FORM_HISTORY = mfqs.UUID_FORM_HISTORY
		where mfh.UUID_FORM = :paramUuidForm 
			and mfh.FORM_VERSION = :paramFormVersion
		order by mfqs.QUESTION_GROUP_OF_FORM_SEQ asc, 
			mfqs.QUESTION_OF_GROUP_SEQ asc
	</sql-query>
	
	<sql-query name="common.mappingform.getFormOrderListVersioning">
		<query-param name="categoryDesc" type="string"/>
		<query-param name="formName" type="string"/>
		select mf.uuid_form, mfc.category_desc, mf.form_name, mfh.form_version
		from MS_FORM mf with (nolock)
			JOIN MS_FORMCATEGORY mfc with (nolock) on mf.UUID_FORM_CATEGORY = mfc.UUID_FORM_CATEGORY
			JOIN MS_FORMHISTORY mfh with (nolock) on mf.UUID_FORM = mfh.UUID_FORM
		where mf.FORM_NAME like '%' + :formName + '%' 
			and mf.is_active = '1'
			and mfc.CATEGORY_DESC = :categoryDesc
			and mf.uuid_form not IN 
			(
				select uuid_form
				from MS_MAPPINGFORM_H maph with (nolock)
				where uuid_form = mf.uuid_form 
					and mfh.form_version = maph.form_version
			)
		order by form_name, form_version
	</sql-query>
	
	<sql-query name="common.mappingform.getQuestionByAnswer">
		<query-param name="paramUuidForm" type="string"/>
		<query-param name="answerType" type="string"/>
		select c.UUID_QUESTION, c.QUESTION_LABEL, c.UUID_ANSWER_TYPE 
		from MS_QUESTIONGROUPOFFORM a with (nolock)
			inner join MS_QUESTIONOFGROUP b with (nolock)
				on a.UUID_QUESTION_GROUP = b.UUID_QUESTION_GROUP
			inner join MS_QUESTION c with (nolock)
				on b.UUID_QUESTION = c.UUID_QUESTION
		where UUID_FORM = :paramUuidForm
			AND C.UUID_ANSWER_TYPE = :answerType
		order by a.LINE_SEQ_ORDER asc, SEQ_ORDER asc
	</sql-query>
	
	<sql-query name="common.mappingform.getFormOrderListVersioning2">
		<query-param name="categoryDesc" type="string"/>
		<query-param name="formName" type="string"/>
		<query-param name="uuidFormList" type="string"/>
		select mf.uuid_form, mfc.category_desc, mf.form_name, mfh.form_version
		from MS_FORM mf with (nolock)
			JOIN MS_FORMCATEGORY mfc with (nolock) on mf.UUID_FORM_CATEGORY = mfc.UUID_FORM_CATEGORY
			JOIN MS_FORMHISTORY mfh with (nolock) on mf.UUID_FORM = mfh.UUID_FORM
		where mf.FORM_NAME like '%' + :formName + '%' 
			and mf.is_active = '1'
			and mfc.CATEGORY_DESC = :categoryDesc
			and mf.uuid_form not IN 
			(
				select uuid_form 
				from MS_MAPPINGFORM_H maph with (nolock)
				where uuid_form = mf.uuid_form 
					and mfh.form_version = maph.form_version
			)
		and mf.uuid_form not in (:uuidFormList)
	</sql-query>
	
</hibernate-mapping>