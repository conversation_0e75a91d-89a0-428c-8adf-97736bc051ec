<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="eform.form.listForm">
		<query-param name="isActive" type="string" />
		<query-param name="uuidMsSubsystem" type="string" />
		<query-param name="uuidQuestionGroup" type="string" />
		<query-param name="questionGroupLabel" type="string" />
		<query-param name="end" type="string" />
		<query-param name="start" type="string" />
		select * from ( 
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select UUID_QUESTION_GROUP, QUESTION_GROUP_LABEL , 
					ROW_NUMBER() OVER (order by QUESTION_GROUP_LABEL asc) AS rownum 
				from MS_QUESTIONGROUP with (nolock)
				where IS_ACTIVE=:isActive
					and UUID_MS_SUBSYSTEM=:uuidMsSubsystem
					and not UUID_QUESTION_GROUP in (:uuidQuestionGroup)
					and QUESTION_GROUP_LABEL like lower('%'+ :questionGroupLabel + '%')
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="eform.form.cntlistForm">
		<query-param name="isActive" type="string" />
		<query-param name="uuidMsSubsystem" type="string" />
		<query-param name="uuidQuestionGroup" type="string" />
		<query-param name="questionGroupLabel" type="string" />
		select count(*) as cnt FROM
		(
			select * 
			from MS_QUESTIONGROUP with (nolock)
			where IS_ACTIVE= :isActive
				and UUID_MS_SUBSYSTEM= :uuidMsSubsystem
				and not UUID_QUESTION_GROUP in (:uuidQuestionGroup)
				and QUESTION_GROUP_LABEL like lower('%'+ :questionGroupLabel + '%')
		) as a
	</sql-query>
	
	<sql-query name="eform.form.getUnChoosenGroup">
		<query-param name="uuidMsSubsystem" type="string" />
	    <query-param name="uuidForm" type="string" />
	    <query-param name="groupDesc" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		select b.* from (
			SELECT a.*,ROW_NUMBER() OVER (ORDER BY GROUP_DESCRIPTION asc) AS rownum FROM (
				Select *				
				from AM_MSGROUP with (nolock) 
				where uuid_ms_subsystem = :uuidMsSubsystem 
					and UUID_MS_GROUP not in (
						select uuid_ms_group 
						from MS_FORMOFGROUP with (nolock) 
						where uuid_form = :uuidForm
					) 
					and IS_ACTIVE = '1'
					and IS_DELETED = '0'
			)a WHERE a.GROUP_DESCRIPTION like lower('%'+ :groupDesc + '%') 
		) b where b.rownum between :start and :end
	</sql-query>
	
	<sql-query name="eform.form.getCountUnChoosenGroup">
		<query-param name="uuidMsSubsystem" type="string" />
	    <query-param name="uuidForm" type="string" />
	    <query-param name="groupDesc" type="string" />
		SELECT count(1) as row FROM (
			Select *,ROW_NUMBER() OVER (ORDER BY IS_ACTIVE) AS rownum				
			from AM_MSGROUP with (nolock) 
			where uuid_ms_subsystem = :uuidMsSubsystem 
				and UUID_MS_GROUP not in (
					select uuid_ms_group 
					from MS_FORMOFGROUP with (nolock) 
					where uuid_form = :uuidForm
				) 
				and IS_ACTIVE = '1'
				and IS_DELETED = '0'
		) a 
		where a.GROUP_DESCRIPTION like lower('%'+ :groupDesc + '%')
	</sql-query>

	<sql-query name="eform.form.getComboFormCtg">
		<query-param name="uuidSubsystem" type="string" />
		select UUID_FORM_CATEGORY, CATEGORY_DESC 
		from MS_FORMCATEGORY with (nolock) 
		where IS_ACTIVE = '1' 
			AND UUID_MS_SUBSYSTEM = :uuidSubsystem
	</sql-query>
	
	<sql-query name="eform.form.delAllGroup">
		<query-param name="uuidMsGroup" type="string" />
		<query-param name="uuidForm" type="string" />
		delete from MS_FORMOFGROUP 
		where UUID_MS_GROUP = :uuidMsGroup 
			and UUID_FORM = :uuidForm
	</sql-query>
	
	<sql-query name="eform.form.getIdentifierByForm">
		<query-param name="uuidForm" type="string" />
		select list.identifierName, list.questionLabel 
		from (
			SELECT qf.LINE_SEQ_ORDER as questionGroupOrder, q.QUESTION_LABEL as questionLabel,
				mqg.SEQ_ORDER as questionOrder, q.IS_READONLY as isReadonly, 
				q.REF_ID as identifierName
			FROM MS_QUESTIONGROUPOFFORM qf with (nolock) 
				LEFT join MS_FORM f with (nolock) ON qf.UUID_FORM = f.UUID_FORM
				LEFT JOIN MS_QUESTIONGROUP qg with (nolock) ON qf.UUID_QUESTION_GROUP = qg.UUID_QUESTION_GROUP
				LEFT JOIN MS_QUESTIONOFGROUP mqg with (nolock) ON mqg.UUID_QUESTION_GROUP = qg.UUID_QUESTION_GROUP
				LEFT JOIN MS_QUESTION q with (nolock) ON mqg.UUID_QUESTION = q.UUID_QUESTION
			WHERE f.UUID_FORM = :uuidForm
				AND q.IS_ACTIVE = '1' 
				AND qg.IS_ACTIVE = '1' 
				AND f.IS_ACTIVE = '1'
			
			UNION
			
			SELECT qf.LINE_SEQ_ORDER as questionGroupOrder, q.QUESTION_LABEL as questionLabel,
				mqg.SEQ_ORDER as questionOrder, q.IS_READONLY as isReadonly,
				q.REF_ID as identifierName
			FROM MS_QUESTIONGROUPOFFORM qf with (nolock) 
				LEFT join MS_FORM f with (nolock) ON qf.UUID_FORM = f.UUID_FORM
				LEFT JOIN MS_QUESTIONGROUP qg with (nolock) ON qf.UUID_QUESTION_GROUP = qg.UUID_QUESTION_GROUP
				LEFT JOIN MS_QUESTIONOFGROUP mqg with (nolock) ON mqg.UUID_QUESTION_GROUP = qg.UUID_QUESTION_GROUP
				LEFT JOIN MS_QUESTION q with (nolock) ON mqg.UUID_QUESTION = q.UUID_QUESTION
			WHERE f.UUID_FORM = :uuidForm
				AND q.IS_ACTIVE = '1' 
				AND qg.IS_ACTIVE = '1' 
				AND f.IS_ACTIVE = '1'
		) 
		list order by isReadonly DESC, questionGroupOrder, questionOrder
	</sql-query>
		
	<sql-query name="eform.form.getAnswerType">
		<query-param name="isActive" type="string" />
		select ANSWER_TYPE_NAME, CODE_ANSWER_TYPE 
		from MS_ANSWERTYPE with (nolock)
		where IS_ACTIVE = :isActive 
		order by CODE_ANSWER_TYPE
	</sql-query>
	
	<sql-query name="eform.form.getLovGroup">
		<query-param name="isActive" type="string" />
		select DISTINCT LOV_GROUP 
		from MS_LOV with (nolock)
		where IS_ACTIVE = :isActive
		union
		SELECT DISTINCT LOV_GROUP 
		FROM MS_MAPPING_COLUMN with (nolock)
		ORDER BY LOV_GROUP
	</sql-query>
	
	<sql-query name="eform.form.getTaggingMS">
		<query-param name="isActive" type="string" />
		select ASSET_TAG_NAME 
		from MS_ASSETTAG with (nolock)
		where IS_ACTIVE = :isActive
		order by ASSET_TAG_NAME
	</sql-query>
	
	<sql-query name="eform.form.getTaggingMO">
		<query-param name="isActive" type="string" />
		select TAG_NAME 
		from MS_ORDERTAG with (nolock)
		where IS_ACTIVE = :isActive
		order by TAG_NAME
	</sql-query>
	
	<sql-query name="eform.form.getTaggingMC">
		<query-param name="isActive" type="string" />
		select TAG_NAME 
		from MS_COLLECTIONTAG with (nolock)
		where IS_ACTIVE = :isActive
		order by TAG_NAME
	</sql-query>
	
	<sql-query name="eform.form.getFormCategory">
		<query-param name="isActive" type="string" />
		select CATEGORY_DESC 
		from MS_FORMCATEGORY with (nolock)
		where IS_ACTIVE = :isActive 
			and UUID_MS_SUBSYSTEM = :uuidMsSubsystem
		order by CATEGORY_DESC
	</sql-query>
	
	<sql-query name="eform.form.listQuestionGroup">
		<query-param name="uuidForm" type="text"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		select * from ( 
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT msq.UUID_QUESTION_GROUP,QUESTION_GROUP_LABEL, UUID_FORM, msqp.IS_ACTIVE,
					ROW_NUMBER() OVER (order by LINE_SEQ_ORDER) AS rownum 
				from MS_QUESTIONGROUPOFFORM msq with (nolock) 
				join MS_QUESTIONGROUP msqp with (nolock) on 
					msq.UUID_QUESTION_GROUP = msqp.UUID_QUESTION_GROUP
				where UUID_FORM = :uuidForm
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="eform.form.cntListQuestionGroup">
		<query-param name="uuidForm" type="text"/>
		select count(msq.UUID_QUESTION_GROUP) 
		from MS_QUESTIONGROUPOFFORM msq with (nolock)
		where UUID_FORM = :uuidForm
	</sql-query>
	
	<sql-query name="eform.form.listQuestionGroupByVersioning">
		<query-param name="uuidForm" type="text"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		select * from ( 
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				Select c.*, ROW_NUMBER() OVER (order by c.seq) as rownum from (
					select DISTINCT qset.UUID_QUESTION_GROUP, qset.QUESTION_GROUP_LABEL,
						qset.QUESTION_GROUP_IS_ACTIVE, qset.UUID_FORM_HISTORY, 
						qset.question_group_of_form_seq AS seq
				  	from MS_FORMQUESTIONSET qset
				  	where qset.UUID_FORM_HISTORY = 
				  	(
				  		select top 1 UUID_FORM_HISTORY 
				  		from MS_FORMHISTORY with (nolock)
				        where uuid_form = :uuidForm 
				        ORDER BY form_version desc
				    )
			   ) c
			) a <![CDATA[  WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="eform.form.cntListQuestionGroupByVersioning">
		<query-param name="uuidForm" type="text"/>
		select count (uuid) from 
		(
			select DISTINCT qset.UUID_QUESTION_GROUP as uuid
		   	from MS_FORMQUESTIONSET qset with (nolock)
		   	where qset.UUID_FORM_HISTORY = 
		   	(
		   		select top 1 UUID_FORM_HISTORY 
		   		from MS_FORMHISTORY with (nolock)
		        where uuid_form = :uuidForm 
		        ORDER BY form_version desc
		     )
		) a
	</sql-query>
	
	<sql-query name="eform.form.listQuestionForViewScheme">
		<query-param name="uuidForm" type="text"/>
		select  mf.UUID_FORM, mqg.UUID_QUESTION_GROUP, mf.FORM_NAME, QUESTION_GROUP_LABEL,QUESTION_LABEL,
			REF_ID, RELEVANT , SEQ_ORDER
		from MS_FORM mf  with (nolock)
			join MS_QUESTIONGROUPOFFORM mfg with (nolock) on mf.UUID_FORM = mfg.UUID_FORM 
			join MS_QUESTIONGROUP mqg with (nolock) on mfg.UUID_QUESTION_GROUP = mqg.UUID_QUESTION_GROUP  
			join MS_QUESTIONOFGROUP mqog with (nolock) on mqg.UUID_QUESTION_GROUP = mqog.UUID_QUESTION_GROUP 
			join MS_QUESTION mq with (nolock) on mqog.UUID_QUESTION = mq.UUID_QUESTION 
			left join MS_QUESTIONRELEVANT mqr with (nolock) on mq.UUID_QUESTION = mqr.UUID_QUESTION and mf.UUID_FORM = mqr.UUID_FORM 
		where mf.UUID_FORM = :uuidForm
		order by QUESTION_GROUP_LABEL, SEQ_ORDER
	</sql-query>
	
	<sql-query name="eform.form.listQuestionDetail">
		<query-param name="uuidQGroup" type="text"/>
		<query-param name="uuidForm" type="text"/> 
		SELECT QUESTION_GROUP_LABEL,QUESTION_LABEL, ANSWER_TYPE_NAME, 
			CASE 
				WHEN (RELEVANT IS NULL OR RELEVANT ='') THEN '-' 
				ELSE 'Y' 
				END AS RELEVANT, 
			Case
				WHEN (CHOICE_FILTER IS NULL OR CHOICE_FILTER = '') THEN '-'
				ELSE 'Y'
				END AS [CHOICE FILTER],
			CASE 
				WHEN (CALCULATE IS NULL OR CALCULATE ='') THEN '-'
				ELSE 'Y'
				END AS CALCULATE,
			CASE 
				WHEN (QUESTION_VALIDATION IS NULL OR QUESTION_VALIDATION ='') THEN '-'
				ELSE 'Y'
				END AS [QUESTION VALIDATION],
			CASE 
				WHEN (QUESTION_ERROR_MESSAGE IS NULL OR QUESTION_ERROR_MESSAGE ='') THEN '-'
				ELSE 'Y'
				END AS [QUESTION ERROR MESSAGE],
			CASE 
				WHEN (QUESTION_VALUE IS NULL OR QUESTION_VALUE ='') THEN '-'
				ELSE 'Y'
				END AS [QUESTION VALUE],
		mq.UUID_QUESTION, mf.UUID_FORM, mqg.UUID_QUESTION_GROUP,
			CASE 
				WHEN (RELEVANT_MANDATORY IS NULL OR RELEVANT_MANDATORY ='') THEN '-'
			 	ELSE 'Y'
			 	END AS [RELEVANT MANDATORY]
	    from MS_FORM mf with (nolock)
			join MS_QUESTIONGROUPOFFORM mfg with (nolock) on mf.UUID_FORM = mfg.UUID_FORM 
			join MS_QUESTIONGROUP mqg with (nolock) on mfg.UUID_QUESTION_GROUP = mqg.UUID_QUESTION_GROUP  
			join MS_QUESTIONOFGROUP mqog with (nolock) on mqg.UUID_QUESTION_GROUP = mqog.UUID_QUESTION_GROUP 
			join MS_QUESTION mq with (nolock) on mqog.UUID_QUESTION = mq.UUID_QUESTION 
			full outer join MS_QUESTIONRELEVANT mqr with (nolock) on mq.UUID_QUESTION = mqr.UUID_QUESTION 
				and mf.UUID_FORM = mqr.UUID_FORM and mqg.UUID_QUESTION_GROUP = mqr.UUID_QUESTION_GROUP 
			join MS_ANSWERTYPE msat with (nolock) on msat.UUID_ANSWER_TYPE = mq.UUID_ANSWER_TYPE 
		where mqg.UUID_QUESTION_GROUP = :uuidQGroup 
			AND (mf.UUID_FORM = :uuidForm or mf.UUID_FORM is null)
		order by SEQ_ORDER
	</sql-query>
	
	<sql-query name="eform.form.listQDetailPublish">
		<query-param name="uuidQGroup" type="text"/>
		<query-param name="uuidForm" type="text"/> 
		SELECT QUESTION_GROUP_LABEL, QUESTION_LABEL, ANSWER_TYPE_NAME, 
			CASE 
				WHEN (RELEVANT IS NULL OR RELEVANT ='') THEN '-' 
			 	ELSE 'Y' 
			 	END AS RELEVANT, 
			Case
				WHEN (CHOICE_FILTER IS NULL OR CHOICE_FILTER = '') THEN '-'
			 	ELSE 'Y'
			 	END AS [CHOICE FILTER],
			CASE 
			 	WHEN (CALCULATE IS NULL OR CALCULATE ='') THEN '-'
			 	ELSE 'Y'
			 	END AS CALCULATE,
			CASE 
			 	WHEN (QUESTION_VALIDATION IS NULL OR QUESTION_VALIDATION ='') THEN '-'
			 	ELSE 'Y'
			 	END AS [QUESTION VALIDATION],
			CASE 
			 	WHEN (QUESTION_ERROR_MESSAGE IS NULL OR QUESTION_ERROR_MESSAGE ='') THEN '-'
			 	ELSE 'Y'
			 	END AS [QUESTION ERROR MESSAGE],
			CASE 
			 	WHEN (QUESTION_VALUE IS NULL OR QUESTION_VALUE ='') THEN '-'
			 	ELSE 'Y'
			 	END AS [QUESTION VALUE],
		qset.UUID_QUESTION, qset.UUID_FORM, qset.UUID_QUESTION_GROUP,
			CASE 
			 	WHEN (RELEVANT_MANDATORY IS NULL OR RELEVANT_MANDATORY ='') THEN '-'
			 	ELSE 'Y'
			 	END AS [RELEVANT MANDATORY]
	    FROM  MS_FORMQUESTIONSET qset with (nolock)
			JOIN MS_FORMHISTORY hist with (nolock) ON qset.UUID_FORM_HISTORY = hist.UUID_FORM_HISTORY
    		JOIN MS_ANSWERTYPE ans with (nolock) ON qset.UUID_ANSWER_TYPE = ans.UUID_ANSWER_TYPE
		WHERE hist.UUID_FORM = :uuidForm
		AND hist.UUID_FORM_HISTORY = (
			SELECT TOP 1 UUID_FORM_HISTORY 
			FROM MS_FORMHISTORY with (nolock) 
			WHERE UUID_FORM = :uuidForm ORDER BY dtm_crt DESC
		)
		AND qset.UUID_QUESTION_GROUP = :uuidQGroup 
	</sql-query>
	
	<sql-query name="eform.form.getQuestionList11">
		<query-param name="uuidForm" type="string"/>
		select QUESTION_LABEL, REF_ID,ANSWER_TYPE_NAME, LOV_GROUP, MAX_LENGTH, msot.TAG_NAME as TAG_NAME1, 
			msst.ASSET_TAG_NAME, msct.TAG_NAME, mq.IS_MANDATORY ,IS_READONLY , IS_VISIBLE, mq.IS_ACTIVE, 
			mq.UUID_QUESTION , mq.REGEX_PATTERN , mq.IS_HOLIDAY_ALLOWED,mq.IMG_QLT
		from MS_FORM mf with (nolock)
			join MS_QUESTIONGROUPOFFORM mfg with (nolock) on mf.UUID_FORM = mfg.UUID_FORM 
			join MS_QUESTIONGROUP mqg with (nolock) on mfg.UUID_QUESTION_GROUP = mqg.UUID_QUESTION_GROUP  
			join MS_QUESTIONOFGROUP mqog with (nolock) on mqg.UUID_QUESTION_GROUP = mqog.UUID_QUESTION_GROUP 
			join MS_QUESTION mq with (nolock) on mqog.UUID_QUESTION = mq.UUID_QUESTION 
			left join MS_ORDERTAG msot with (nolock) on mq.UUID_ORDER_TAG = msot.UUID_ORDER_TAG
			left join MS_ASSETTAG msst with (nolock) on mq.UUID_ASSET_TAG = msst.UUID_ASSET_TAG
			left join MS_COLLECTIONTAG msct with (nolock) on mq.UUID_COLLECTION_TAG = msct.UUID_COLLECTION_TAG
			join MS_ANSWERTYPE msat with (nolock) on msat.UUID_ANSWER_TYPE = mq.UUID_ANSWER_TYPE 
		where mf.UUID_FORM = :uuidForm
		order by mq.DTM_CRT
	</sql-query>
	
	<sql-query name="eform.form.getQuestionGroup">
		<query-param name="uuidForm" type="text"/>
		select  QUESTION_GROUP_LABEL, mqg.IS_ACTIVE, QUESTION_LABEL,REF_ID, UUID_QUESTION_OF_GROUP, SEQ_ORDER
		from MS_FORM mf with (nolock)
			join MS_QUESTIONGROUPOFFORM mfg with (nolock) on mf.UUID_FORM = mfg.UUID_FORM 
			join MS_QUESTIONGROUP mqg with (nolock) on mfg.UUID_QUESTION_GROUP = mqg.UUID_QUESTION_GROUP  
			join MS_QUESTIONOFGROUP mqog with (nolock) on mqg.UUID_QUESTION_GROUP = mqog.UUID_QUESTION_GROUP 
			join MS_QUESTION mq with (nolock) on mqog.UUID_QUESTION = mq.UUID_QUESTION 
			left join MS_QUESTIONRELEVANT mqr with (nolock) on mq.UUID_QUESTION = mqr.UUID_QUESTION and mf.UUID_FORM = mqr.UUID_FORM 
		where mf.UUID_FORM = :uuidForm
		order by mqg.DTM_CRT , SEQ_ORDER
	</sql-query>
	
	<sql-query name="eform.form.getFormByUuid">
		<query-param name="uuidForm" type="text"/>
		select  DISTINCT FORM_NAME,CATEGORY_DESC,mf.IS_ACTIVE, QUESTION_GROUP_LABEL, 
			UUID_QUESTION_GROUP_OF_FORM, LINE_SEQ_ORDER
		from MS_FORM mf with (nolock)
			join MS_QUESTIONGROUPOFFORM mfg with (nolock) on mf.UUID_FORM = mfg.UUID_FORM 
			join MS_FORMCATEGORY mfc with (nolock) on mf.UUID_FORM_CATEGORY = mfc.UUID_FORM_CATEGORY
			join MS_QUESTIONGROUP mqg with (nolock) on mfg.UUID_QUESTION_GROUP = mqg.UUID_QUESTION_GROUP  
			join MS_QUESTIONOFGROUP mqog with (nolock) on mqg.UUID_QUESTION_GROUP = mqog.UUID_QUESTION_GROUP 
			join MS_QUESTION mq with (nolock) on mqog.UUID_QUESTION = mq.UUID_QUESTION 
			left join MS_QUESTIONRELEVANT mqr with (nolock) on mq.UUID_QUESTION = mqr.UUID_QUESTION and mf.UUID_FORM = mqr.UUID_FORM 
		where mf.UUID_FORM = :uuidForm
		order by LINE_SEQ_ORDER
	</sql-query>
	
	<sql-query name="eform.form.getRelevantOfForm">
		<query-param name="uuidForm" type="text"/>
		select FORM_NAME,QUESTION_GROUP_LABEL, QUESTION_LABEL, REF_ID, RELEVANT ,CHOICE_FILTER, 
			CALCULATE, QUESTION_VALIDATION,QUESTION_VALUE, QUESTION_ERROR_MESSAGE,  UUID_QUESTION_RELEVANT, 
			LINE_SEQ_ORDER, SEQ_ORDER, RELEVANT_MANDATORY
		from MS_FORM mf with (nolock)
			join MS_QUESTIONGROUPOFFORM mfg with (nolock) on mf.UUID_FORM = mfg.UUID_FORM 
			join MS_FORMCATEGORY mfc with (nolock) on mf.UUID_FORM_CATEGORY = mfc.UUID_FORM_CATEGORY
			join MS_QUESTIONGROUP mqg with (nolock) on mfg.UUID_QUESTION_GROUP = mqg.UUID_QUESTION_GROUP  
			join MS_QUESTIONOFGROUP mqog with (nolock) on mqg.UUID_QUESTION_GROUP = mqog.UUID_QUESTION_GROUP 
			join MS_QUESTION mq with (nolock) on mqog.UUID_QUESTION = mq.UUID_QUESTION 
			join MS_QUESTIONRELEVANT mqr with (nolock) on mq.UUID_QUESTION = mqr.UUID_QUESTION and mf.UUID_FORM = mqr.UUID_FORM 
		where mf.UUID_FORM = :uuidForm
		order by LINE_SEQ_ORDER, SEQ_ORDER
	</sql-query>
	
	<sql-query name="eform.form.getMsLovGroup">
		<query-param name="lovGroup" type="text"/>
		select distinct lov_group 
		from ms_lov with (nolock)
		where lov_group = :lovGroup
		union
		SELECT DISTINCT LOV_GROUP 
		FROM MS_MAPPING_COLUMN with (nolock)
		where lov_group = :lovGroup
	</sql-query>
	
	<sql-query name="eform.form.checkRelevant">
		<query-param name="uuidForm" type="text"/>
		<query-param name="refId" type="text"/>
		select distinct mq.UUID_QUESTION, QUESTION_LABEL, REF_ID, RELEVANT, SEQ_ORDER, LINE_SEQ_ORDER, LOV_GROUP
		from MS_FORM mf with (nolock) 
			join MS_QUESTIONGROUPOFFORM mfg with (nolock) on mf.UUID_FORM = mfg.UUID_FORM 
			join MS_QUESTIONGROUP mqg with (nolock) on mfg.UUID_QUESTION_GROUP = mqg.UUID_QUESTION_GROUP  
			join MS_QUESTIONOFGROUP mqog with (nolock) on mqg.UUID_QUESTION_GROUP = mqog.UUID_QUESTION_GROUP 
			join MS_QUESTION mq with (nolock) on mqog.UUID_QUESTION = mq.UUID_QUESTION 
			left join MS_QUESTIONRELEVANT mqr with (nolock) on mq.UUID_QUESTION = mqr.UUID_QUESTION and mf.UUID_FORM = mqr.UUID_FORM 
		where mf.UUID_FORM = :uuidForm and REF_ID = :refId
		order by LINE_SEQ_ORDER, SEQ_ORDER desc
	</sql-query>
	
	<sql-query name="eform.form.lovDescription">
		<query-param name="code" type="text"/>
		<query-param name="lovGroup" type="text"/>
		select description 
		from ms_lov with (nolock) 
		where lov_group = :lovGroup 
			and code = :code
	</sql-query>
	
	<sql-query name="eform.form.checkTagging">
		<query-param name="uuidForm" type="text"/>
		<query-param name="tagName" type="text"/>
		select f.FORM_NAME, qg.QUESTION_GROUP_LABEL, q.QUESTION_LABEL, q.REF_ID,
			coalesce(otag.tag_name, stag.asset_tag_name, ctag.tag_name) as Tag
		from ms_form f with (nolock)
			inner join MS_QUESTIONGROUPOFFORM qgf with (nolock) on f.UUID_FORM=qgf.UUID_FORM
			inner join MS_QUESTIONGROUP qg with (nolock) on
				qgf.UUID_QUESTION_GROUP=qg.UUID_QUESTION_GROUP
			inner join MS_QUESTIONOFGROUP qog with (nolock) on
				qg.UUID_QUESTION_GROUP=qog.UUID_QUESTION_GROUP
			inner join MS_QUESTION q with (nolock) on q.UUID_QUESTION=qog.UUID_QUESTION
			left join MS_ORDERTAG otag with (nolock) on q.UUID_ORDER_TAG= otag.UUID_ORDER_TAG
			left join MS_ASSETTAG stag with (nolock) on q.UUID_ASSET_TAG= stag.UUID_ASSET_TAG
			left join MS_COLLECTIONTAG ctag with (nolock) on q.UUID_COLLECTION_TAG = ctag.UUID_COLLECTION_TAG
		where qg.is_active='1' 
			AND q.is_active='1' 
			AND f.UUID_FORM = :uuidForm
			AND coalesce(otag.tag_name, stag.asset_tag_name, ctag.tag_name) = :tagName
	</sql-query>
	
	<sql-query name="eform.form.getPriority">
		SELECT PRIORITY_DESC AS 'PRIORITY'
		FROM MS_PRIORITY WITH (NOLOCK) 
		WHERE IS_ACTIVE = 1
	</sql-query>
	
	<sql-query name="eform.form.getBranchDescription">
		SELECT BRANCH_CODE AS 'BRANCH CODE', BRANCH_NAME AS 'BRANCH DESCRIPTION' 
		FROM MS_BRANCH WITH (NOLOCK) 
		WHERE IS_ACTIVE = 1
	</sql-query>
	
	<sql-query name="eform.form.getBranchDriver">
		SELECT B.BRANCH_CODE AS 'BRANCH CODE', A.LOGIN_ID AS DRIVER
		FROM MS_BRANCH B WITH (NOLOCK) 
			LEFT OUTER JOIN AM_MSUSER A WITH (NOLOCK) ON B.UUID_BRANCH = A.UUID_BRANCH
		WHERE A.UUID_JOB = (
			SELECT UUID_JOB 
			FROM MS_JOB WITH (NOLOCK) 
			WHERE JOB_CODE = 'DRIVER'
		) AND A.IS_ACTIVE = 1 
		ORDER BY B.BRANCH_CODE
	</sql-query>
	
	<sql-query name="eform.form.getZoneOfLocation">
		SELECT L.LOCATION_CODE AS LOCATION, Z.ZONE_CODE AS ZONE, L.LOCATION_ADDRESS AS ADDRESS, 
			L.LATITUDE, L.LONGITUDE
		FROM MS_ZONEOFLOCATION ZL WITH (NOLOCK)
			JOIN MS_LOCATION L WITH (NOLOCK) ON (L.UUID_LOCATION = ZL.UUID_LOCATION)
			JOIN MS_ZONE Z WITH (NOLOCK) ON (Z.UUID_ZONE = ZL.UUID_ZONE)
	</sql-query>
	
	<sql-query name="eform.form.getQuestionSet">
		<query-param name="uuidForm" type="string"/>
		SELECT MQ.UUID_QUESTION, MQ.IS_ACTIVE AS QUESTION_IS_ACTIVE, MQ.REF_ID, MQ.QUESTION_LABEL, 
			MQ.UUID_ANSWER_TYPE, ISNULL(MQ.LOV_GROUP,'') LOV_GROUP, MQ.IS_VISIBLE, MQ.IS_MANDATORY, 
			MQ.IS_READONLY, MQ.IS_HOLIDAY_ALLOWED, MQ.MAX_LENGTH, MQ.REGEX_PATTERN, MQ.UUID_ASSET_TAG, 
			MQ.UUID_COLLECTION_TAG, MQ.UUID_MS_SUBSYSTEM, MQ.UUID_ORDER_TAG, MQ.IMG_QLT, MQG.UUID_QUESTION_GROUP,
			MQG.IS_ACTIVE AS QUESTION_GROUP_IS_ACTIVE, MQG.QUESTION_GROUP_LABEL, 
			MQOG.SEQ_ORDER AS QUESTION_OF_GROUP_SEQ, MF.UUID_FORM, MF.IS_ACTIVE AS FORM_IS_ACTIVE, MF.FORM_NAME, 
			MF.UUID_FORM_CATEGORY, MF.IS_PRINTABLE, MFG.LINE_SEQ_ORDER, 
			ISNULL(mqr.RELEVANT,'') AS RELEVANT,  
			ISNULL(CAST(mqr.CALCULATE AS varchar(4000)),'') AS CALCULATE,
			ISNULL(mqr.CHOICE_FILTER,'') AS CHOICE_FILTER,
			ISNULL(mqr.QUESTION_VALIDATION,'') AS QUESTION_VALIDATION,
			ISNULL(mqr.QUESTION_ERROR_MESSAGE,'') AS QUESTION_ERROR_MESSAGE,
			ISNULL(mqr.QUESTION_VALUE,'') AS QUESTION_VALUE, 
			ISNULL(CAST(mqr.RELEVANT_MANDATORY AS varchar(2048)),'') AS RELEVANT_MANDATORY
		FROM MS_FORM MF with (nolock) 
			JOIN MS_QUESTIONGROUPOFFORM MFG with (nolock) ON MF.UUID_FORM = MFG.UUID_FORM
			JOIN MS_QUESTIONGROUP MQG with (nolock) ON MFG.UUID_QUESTION_GROUP = MQG.UUID_QUESTION_GROUP 
			JOIN MS_QUESTIONOFGROUP MQOG with (nolock) ON MQG.UUID_QUESTION_GROUP = MQOG.UUID_QUESTION_GROUP
			JOIN MS_QUESTION MQ with (nolock)ON MQOG.UUID_QUESTION = MQ.UUID_QUESTION
			LEFT JOIN MS_QUESTIONRELEVANT MQR with (nolock) ON MQ.UUID_QUESTION = MQR.UUID_QUESTION AND MF.UUID_FORM = MQR.UUID_FORM
			AND MQOG.UUID_QUESTION_GROUP = mqr.UUID_QUESTION_GROUP
		WHERE MF.UUID_FORM = :uuidForm
		ORDER BY FORM_NAME, MFG.LINE_SEQ_ORDER, MQOG.SEQ_ORDER
	</sql-query>
</hibernate-mapping>