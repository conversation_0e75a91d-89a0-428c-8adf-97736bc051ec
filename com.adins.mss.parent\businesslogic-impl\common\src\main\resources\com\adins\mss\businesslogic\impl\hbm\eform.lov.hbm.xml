<?xml version="1.0" ?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="eform.lov.getGroupLov">
		SELECT DISTINCT LOV_GROUP
		FROM MS_LOV with (nolock)
		WHERE IS_DELETED = '0' AND IS_ACTIVE = '1'
		ORDER BY LOV_GROUP
	</sql-query>
	
	<sql-query name="eform.lov.getGroupLovSync">
		SELECT DISTINCT LOV_GROUP
		FROM MS_LOV with (nolock)
		WHERE IS_DELETED = '0' AND IS_ACTIVE = '1'
		and LOV_GROUP not in ( select distinct LOV_GROUP from MS_LOV_GROUP_SYNC where IS_SYSTEM = 1 )
		ORDER BY LOV_GROUP
	</sql-query>
	
	<sql-query name="eform.lov.getListLovByLovGroupSync">
		<query-param name="lovGroup" type="string" />
	    <query-param name="code" type="string" />
	    <query-param name="description" type="string" />
	    <query-param name="isDeleted" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>	
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select lov.UUID_LOV, lov.LOV_GROUP, lov.IS_ACTIVE, lov.CODE, lov.DESCRIPTION,
						lov.SEQUENCE, lov.CONSTRAINT_1, lov.CONSTRAINT_2, lov.CONSTRAINT_3,
						lov.CONSTRAINT_4, lov.CONSTRAINT_5, COALESCE(lovg.IS_SYSTEM, '0') as IS_SYSTEM,
						ROW_NUMBER() OVER (ORDER BY lov.LOV_GROUP ASC,  lov.CODE ASC) AS rownum 
					from MS_LOV lov
					left join MS_LOV_GROUP_SYNC lovg on lov.LOV_GROUP = lovg.LOV_GROUP
				where lov.LOV_GROUP like UPPER('%'+ :lovGroup +'%') 
					and lov.CODE like UPPER('%'+ :code +'%') 
					and lov.DESCRIPTION like UPPER('%'+ :description +'%') 
					and lov.IS_DELETED = :isDeleted
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		 ) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="eform.lov.getListLovByLovGroupSyncCount">
		<query-param name="lovGroup" type="string" />
	    <query-param name="code" type="string" />
	    <query-param name="description" type="string" />
	    <query-param name="isDeleted" type="string" />
			select COUNT(1)
				from MS_LOV lov
				left join MS_LOV_GROUP_SYNC lovg on lov.LOV_GROUP = lovg.LOV_GROUP
			where lov.LOV_GROUP like UPPER('%'+ :lovGroup +'%') 
				and lov.CODE like UPPER('%'+ :code +'%') 
				and lov.DESCRIPTION like UPPER('%'+ :description +'%') 
				and lov.IS_DELETED = :isDeleted
	</sql-query>
	
	<sql-query name="eform.lov.listBranch">
	    <query-param name="uuidLov" type="string" />
	    <query-param name="branchCode" type="string" />
	    <query-param name="branchName" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>	
		 select * from (
		 	SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
		 	    SELECT MB.UUID_BRANCH, MB.BRANCH_CODE, MB.BRANCH_NAME, 
		 	    	(case when MBL.UUID_BRANCH IS null then 'false' else 'true' end) as FLAG
		 	    	, ROW_NUMBER() OVER (ORDER BY MB.BRANCH_CODE,  MB.BRANCH_NAME) AS rownum 
		    	FROM MS_BRANCH MB with (nolock) LEFT JOIN MS_BRANCHOFLOV MBL with (nolock) ON MB.UUID_BRANCH=MBL.UUID_BRANCH
		    	AND MBL.UUID_LOV = :uuidLov
		    	where MB.BRANCH_CODE LIKE '%'+ :branchCode +'%' 
		    		AND MB.BRANCH_NAME LIKE '%'+ :branchName +'%'
		    		AND (MBL.IS_DELETED = '1' OR MBL.IS_DELETED IS NULL)
		    		and MB.is_active ='1'
		  	) a <![CDATA[ WHERE a.ROWNUM <= :end
		 ) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="eform.lov.cntBranch">
	    <query-param name="uuidLov" type="string" />
	    <query-param name="branchCode" type="string" />
	    <query-param name="branchName" type="string" />
    	SELECT count(MB.UUID_BRANCH)
    	FROM MS_BRANCH MB with (nolock) LEFT JOIN MS_BRANCHOFLOV MBL with (nolock) ON MB.UUID_BRANCH=MBL.UUID_BRANCH
    	AND MBL.UUID_LOV = :uuidLov
    	where MB.BRANCH_CODE LIKE '%'+ :branchCode +'%' 
    		AND MB.BRANCH_NAME LIKE '%'+ :branchName +'%'
    		AND (MBL.IS_DELETED = '1' OR MBL.IS_DELETED IS NULL)
    		AND MB.is_active ='1'
	</sql-query>
</hibernate-mapping>