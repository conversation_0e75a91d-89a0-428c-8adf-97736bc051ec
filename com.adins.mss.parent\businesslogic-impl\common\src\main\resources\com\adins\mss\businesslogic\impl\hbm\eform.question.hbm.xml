<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="eform.question.getListLov">
		SELECT DISTINCT LOV_GROUP 
		FROM MS_LOV with (nolock) 
		WHERE IS_ACTIVE = '1'
		ORDER BY LOV_GROUP
	</sql-query>
	
	<sql-query name="eform.question.questionValidity">
		<query-param name="refId" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		SELECT COUNT(1) 
		FROM MS_QUESTION with (nolock) 
		WHERE REF_ID = :refId 
			AND UUID_MS_SUBSYSTEM = :uuidSubsystem
	</sql-query>
	
	<sql-query name="eform.question.questionAssetTaggingValidity">
		<query-param name="uuidTag" type="long"/>
		<query-param name="uuidQuestion" type="long"/>
		SELECT COUNT(1) 
		FROM MS_QUESTION with (nolock) 
		WHERE UUID_ASSET_TAG = :uuidTag
			AND IS_ACTIVE = '1' 
			AND UUID_QUESTION != :uuidQuestion
	</sql-query>
	
	<sql-query name="eform.question.questionCollTaggingValidity">
		<query-param name="uuidTag" type="long"/>
		<query-param name="uuidQuestion" type="long"/>
		SELECT COUNT(1) 
		FROM MS_QUESTION with (nolock) 
		WHERE UUID_COLLECTION_TAG = :uuidTag
			AND IS_ACTIVE = '1' 
			AND UUID_QUESTION != :uuidQuestion
	</sql-query>
	
	<sql-query name="eform.question.questionOrderTaggingValidity">
		<query-param name="uuidTag" type="long"/>
		<query-param name="uuidQuestion" type="long"/>
		SELECT COUNT(1) 
		FROM MS_QUESTION with (nolock) 
		WHERE UUID_ORDER_TAG = :uuidTag
			AND IS_ACTIVE = '1' 
			AND UUID_QUESTION != :uuidQuestion
	</sql-query>
	
	<sql-query name="eform.question.getListLuOnline">
		SELECT DISTINCT LOV_GROUP 
		FROM MS_MAPPING_COLUMN with (nolock) 
		ORDER BY LOV_GROUP
	</sql-query>
	
</hibernate-mapping>