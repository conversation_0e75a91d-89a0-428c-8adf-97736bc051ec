<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>	

	<sql-query name="eform.questiongroup.questionGroupValidity">
		<query-param name="questionGroupLabel" type="string"/>
		<query-param name="uuidSubsystem" type="string"/>
		SELECT COUNT(1) 
		FROM MS_QUESTIONGROUP with (nolock) 
		WHERE QUESTION_GROUP_LABEL = :questionGroupLabel 
			AND UUID_MS_SUBSYSTEM = :uuidSubsystem
	</sql-query>
	
	<sql-query name="eform.questiongroup.updateForm">
		<query-param name="uuidQuestionGroup" type="long"/>
		UPDATE MS_FORM
		SET FORM_LAST_UPDATE = CURRENT_TIMESTAMP
		WHERE UUID_FORM IN 
		(  
			SELECT UUID_FORM
			FROM MS_QUESTIONGROUPOFFORM with (nolock)
			WHERE UUID_QUESTION_GROUP = :uuidQuestionGroup
		)
	</sql-query>
</hibernate-mapping>