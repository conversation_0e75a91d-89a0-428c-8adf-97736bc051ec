<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="fcm.notification.getUserByBranch">
		<query-param name="uuidBranch" type="string" />
		Select FCM_TOKEN, UUID_MS_USER
		from AM_MSUSER as msUser with (nolock)
			JOIN MS_BRANCH as branch with (nolock)
			ON msUser.UUID_BRANCH = branch.UUID_BRANCH
		where branch.UUID_BRANCH = :uuidBranch
			AND FCM_TOKEN is not NULL 
			AND msUser.IS_ACTIVE = '1'
	</sql-query>

	<sql-query name="fcm.notification.getUserByDealer">
		<query-param name="uuidDealer" type="string" />
		Select FCM_TOKEN, UUID_MS_USER
		from AM_MSUSER as msUser with (nolock)
			JOIN MS_DEALER as dealer with (nolock)
			ON msUser.UUID_DEALER = dealer.UUID_DEALER
		where dealer.UUID_DEALER = :uuidDealer 
			AND FCM_TOKEN is not NULL 
			AND msUser.IS_ACTIVE = '1'
	</sql-query>

	<sql-query name="fcm.notification.getUserByGroup">
		<query-param name="uuidGroup" type="string" />
		Select msUser.FCM_TOKEN, msUser.UUID_MS_USER
		from AM_MSUSER as  msUser with (nolock)
			JOIN AM_MEMBEROFGROUP as member with (nolock)
			ON member.UUID_MS_USER = msUser.UUID_MS_USER
		WHERE member.UUID_MS_GROUP = :uuidGroup 
			AND msUser.FCM_TOKEN IS NOT NULL 
			AND msUser.IS_ACTIVE = '1'
	</sql-query>
	
	<sql-query name="fcm.notification.getAllUser">
		Select msUser.FCM_TOKEN, msUser.UUID_MS_USER
		from AM_MSUSER as msUser with (nolock)
		WHERE msUser.FCM_TOKEN IS NOT NULL 
			AND msUser.IS_ACTIVE = '1'
	</sql-query>
	
	<sql-query name="fcm.notification.getIndividu">
	<query-param name="userId" type="string"/>
	<query-param name="fullName" type="string"/>
	<query-param name="subsystemId" type="string"/>
	<query-param name="start" type="string"/>
	<query-param name="end" type="string"/>
	select * from (
		SELECT d.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
			SELECT a.UUID_MS_USER,A.LOGIN_ID, A.FULL_NAME, B.BRANCH_NAME,C.DESCRIPTION,
				ROW_NUMBER() OVER (ORDER BY BRANCH_CODE, FULL_NAME) AS rownum 
			FROM AM_MSUSER AS a  with (nolock)
				JOIN MS_BRANCH AS b  with (nolock) ON A.UUID_BRANCH = b.UUID_BRANCH
				JOIN MS_JOB as c  with (nolock) on a.UUID_JOB = c.UUID_JOB
			where a.FCM_TOKEN is not null 
				and a.LOGIN_ID LIKE '%'+:userId+'%' 
				and A.FULL_NAME like '%'+:fullName+'%'
				and c.IS_FIELD_PERSON = '1'
				and a.UUID_MS_SUBSYSTEM = :subsystemId
				and a.IS_ACTIVE = '1'
		) d <![CDATA[ WHERE d.rownum <= :end
	) e WHERE e.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="fcm.notification.getCountIndividu">
	<query-param name="userId" type="string"/>
	<query-param name="fullName" type="string"/>
	<query-param name="subsystemId" type="string"/>
		SELECT count(a.UUID_MS_USER)    
		FROM AM_MSUSER AS a with (nolock)
			JOIN MS_BRANCH AS b with (nolock) ON A.UUID_BRANCH = b.UUID_BRANCH
			JOIN MS_JOB as c with (nolock) on a.UUID_JOB = c.UUID_JOB
		where a.FCM_TOKEN is not null 
			and a.LOGIN_ID LIKE '%'+:userId+'%' 
			and A.FULL_NAME like '%'+:fullName+'%'
			and c.IS_FIELD_PERSON = '1'
			and a.UUID_MS_SUBSYSTEM = :subsystemId
			and a.IS_ACTIVE = '1'
	</sql-query>
	
	<sql-query name="fcm.notification.getSales">
	<query-param name="userId" type="string"/>
	<query-param name="fullName" type="string"/>
	<query-param name="subsystemId" type="string"/>
	<query-param name="start" type="string"/>
	<query-param name="end" type="string"/>
	select * from (
		SELECT d.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
			SELECT a.UUID_MS_USER,A.LOGIN_ID, A.FULL_NAME, B.DEALER_NAME,C.DESCRIPTION,
				ROW_NUMBER() OVER (ORDER BY DEALER_CODE, FULL_NAME) AS rownum 
			FROM AM_MSUSER AS a with (nolock) 	
				JOIN MS_DEALER AS b with (nolock) ON a.UUID_DEALER = b.uuid_dealer
				JOIN MS_JOB as c  with (nolock) on a.UUID_JOB = c.UUID_JOB
			where a.FCM_TOKEN is not null
				and a.LOGIN_ID LIKE '%'+:userId+'%' 
				and A.FULL_NAME like '%'+:fullName+'%'
				and c.IS_FIELD_PERSON = '1' and c.IS_BRANCH = '0'
				and a.UUID_MS_SUBSYSTEM = :subsystemId 
				and a.IS_ACTIVE = '1'
		) d <![CDATA[ WHERE d.rownum <= :end
	) e WHERE e.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="fcm.notification.getCountSales">
	<query-param name="userId" type="string"/>
	<query-param name="fullName" type="string"/>
	<query-param name="subsystemId" type="string"/>
		SELECT count(a.UUID_MS_USER)  
		FROM AM_MSUSER AS a  with (nolock)
			JOIN MS_JOB as c  with (nolock) on a.UUID_JOB = c.UUID_JOB
		where a.FCM_TOKEN is not null
			and a.LOGIN_ID LIKE '%'+:userId+'%' and A.FULL_NAME like '%'+:fullName+'%'
			and c.IS_FIELD_PERSON = '1' and c.IS_BRANCH = '0'
			and a.UUID_MS_SUBSYSTEM = :subsystemId 
			and a.IS_ACTIVE = '1'
	</sql-query>
	
	<sql-query name="fcm.notification.getGroupListBySubsystem">
	<query-param name="gruopName" type="string"/>
	<query-param name="gruopDesc" type="string"/>
	<query-param name="subsystemId" type="string"/>
	<query-param name="start" type="string"/>
	<query-param name="end" type="string"/>
		select * from (
			SELECT d.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (select DISTINCT grup.UUID_MS_GROUP, grup.GROUPNAME, grup.GROUP_DESCRIPTION,
						ROW_NUMBER() OVER (ORDER BY GROUPNAME) AS rownum 
				from AM_MSGROUP grup  with (nolock)
				join AM_MSSUBSYSTEM subs  with (nolock)
					on subs.UUID_MS_SUBSYSTEM = grup.UUID_MS_SUBSYSTEM
				join MS_GROUPOFJOB jobGrup  with (nolock)
					on grup.UUID_MS_GROUP = jobGrup.UUID_MS_GROUP
				join MS_JOB job  with (nolock)
					on job.UUID_JOB = jobGrup.UUID_JOB
				where grup.UUID_MS_SUBSYSTEM = :subsystemId
					AND job.IS_FIELD_PERSON = '1'
					AND grup.GROUPNAME like '%'+ :gruopName +'%' 
					AND grup.GROUP_DESCRIPTION like '%'+ :gruopDesc +'%'
			) d <![CDATA[ WHERE d.rownum <= :end
		) e WHERE e.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="fcm.notification.getCountGroupListBySubsystem">
	<query-param name="gruopName" type="string"/>
	<query-param name="gruopDesc" type="string"/>
	<query-param name="subsystemId" type="string"/>
		SELECT count(DISTINCT grup.UUID_MS_GROUP)  
		from AM_MSGROUP grup  with (nolock)
		join AM_MSSUBSYSTEM subs  with (nolock)
			on subs.UUID_MS_SUBSYSTEM = grup.UUID_MS_SUBSYSTEM
		join MS_GROUPOFJOB jobGrup  with (nolock)
			on grup.UUID_MS_GROUP = jobGrup.UUID_MS_GROUP
		join MS_JOB job  with (nolock)
			on job.UUID_JOB = jobGrup.UUID_JOB
		where grup.UUID_MS_SUBSYSTEM = :subsystemId
			AND job.IS_FIELD_PERSON = '1'
			AND grup.GROUPNAME like '%'+ :gruopName +'%' 
			AND grup.GROUP_DESCRIPTION like '%'+ :gruopDesc +'%'
	</sql-query>

</hibernate-mapping>