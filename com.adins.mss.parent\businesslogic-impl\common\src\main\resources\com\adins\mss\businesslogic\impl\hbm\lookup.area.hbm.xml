<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="lookup.area.listHirarkiArea">
		<query-param name="areaName" type="string" />
		<query-param name="uuidArea" type="string" />
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N  AS (
				select UUID_AREA, AREA_NAME, AREA_TYPE_CODE,LONGITUDE, LATITUDE,RADIUS, 0 as level
				from MS_AREA with (nolock)
				where UUID_AREA = :uuidArea and IS_ACTIVE = '1'
				union all
				select b.UUID_AREA, b.AREA_NAME, b.AREA_TYPE_CODE, 
					b.LONGITUDE, b.LATITUDE, b.<PERSON>DIUS, level + 1 
				from MS_AREA b with (nolock) 
				join n on b.PARENT_AREA_ID = n.UUID_AREA
		)
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT UUID_AREA, AREA_NAME, AREA_TYPE_CODE, LONGITUDE, LATITUDE, RADIUS, level,
					ROW_NUMBER() OVER (ORDER BY N.AREA_NAME) AS rownum 
				FROM N
				WHERE lower(AREA_NAME) like lower('%'+ :areaName +'%') 
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.area.countHirarkiArea">
		<query-param name="areaName" type="string" />
		<query-param name="uuidArea" type="string" />
		WITH N  AS (
			select UUID_AREA, AREA_NAME, AREA_TYPE_CODE,LONGITUDE, LATITUDE,RADIUS, 0 as level
			from MS_AREA with (nolock)
			where UUID_AREA = :uuidArea and IS_ACTIVE = '1'
			union all
			select b.UUID_AREA, b.AREA_NAME, b.AREA_TYPE_CODE, b.LONGITUDE, 
				b.LATITUDE, b.RADIUS, level + 1 
			from MS_AREA b with (nolock) 
			join n on b.PARENT_AREA_ID = n.UUID_AREA
		)
		SELECT count(1)
		FROM N
		WHERE lower(AREA_NAME) like lower('%'+ :areaName +'%') 
	</sql-query>

	<sql-query name="lookup.area.listOtherBranches">
		<query-param name="uuidBranch" type="string" />
		select mb.UUID_BRANCH, mb.BRANCH_CODE, mb.BRANCH_NAME, mb.LATITUDE, mb.LONGITUDE,mab.UUID_AREA 
		from MS_BRANCH mb with (nolock) 
		LEFT OUTER JOIN MS_AREAOFBRANCH mab with (nolock) 
		on mb.UUID_BRANCH=mab.UUID_BRANCH
		where mb.UUID_BRANCH  != :uuidBranch 
		and mb.BRANCH_CODE != 'HO'
		AND mb.IS_ACTIVE = '1'
	</sql-query>
	
	<sql-query name="lookup.area.areabranchuser">
		<query-param name="uuidBranch" type="string" />
		select map.SEQUENCE,map.LATITUDE,map.LONGITUDE 
		from MS_AREAOFBRANCH mab with (nolock) 
		join MS_AREAPATH map with (nolock) on mab.UUID_AREA = map.UUID_AREA
 		where mab.UUID_BRANCH = :uuidBranch order by map.sequence
	</sql-query>
	
	<sql-query name="lookup.area.areabranch">
		<query-param name="uuidBranch" type="string" />
		select msa.* from MS_AREAOFBRANCH mab with (nolock) 
		join MS_AREA msa with (nolock) on mab.UUID_AREA= msa.UUID_AREA
 		where mab.UUID_BRANCH = :uuidBranch 
	</sql-query>

</hibernate-mapping>