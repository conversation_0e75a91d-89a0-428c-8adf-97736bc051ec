<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="lookup.branch.listExcCriteria">
		<query-param name="branchCode" type="string" />
		<query-param name="branchName" type="string" />
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				select UUID_BRANCH, BRANCH_CODE, BRANCH_NAME,
					ROW_NUMBER() OVER (ORDER BY branch_code) AS rownum   
				from MS_BRANCH with (nolock)
				where lower(branch_code) like lower('%'+ :branchCode +'%') 
					AND lower(branch_Name) like lower('%'+ :branchName +'%')
					AND IS_ACTIVE = '1'
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="lookup.branch.listExcCriteriaCnt">
		<query-param name="branchCode" type="string" />
		<query-param name="branchName" type="string" />
			SELECT count(1) 
			from MS_BRANCH with (nolock)
			where lower(branch_code) like lower('%'+ :branchCode +'%') 
				AND lower(branch_Name) like lower('%'+ :branchName +'%')
				AND IS_ACTIVE = '1'
	</sql-query>
	
	<sql-query name="lookup.branch.listDobCriteria">
		<query-param name="uuidDealer" type="string"/>
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				select mb.uuid_branch, mb.branch_code, mb.branch_name,
				ROW_NUMBER() OVER (ORDER BY mb.branch_code)  AS rownum  
				from MS_DEALEROFBRANCH dob with (nolock) 
				left join ms_branch mb with (nolock)
					on dob.UUID_BRANCH = mb.UUID_BRANCH
				where dob.UUID_dealer = :uuidDealer
				AND lower(mb.branch_code) like lower('%'+ :branchCode +'%') 
				AND lower(mb.branch_Name) like lower('%'+ :branchName +'%')
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.branch.listDobCriteriaCnt">
		<query-param name="uuidDealer" type="string"/>
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
			SELECT count(1) 
			from MS_DEALEROFBRANCH dob with (nolock) 
			left join ms_branch mb with (nolock)
				on dob.UUID_BRANCH = mb.UUID_BRANCH
			where dob.UUID_dealer = :uuidDealer
			AND lower(mb.branch_code) like lower('%'+ :branchCode +'%') 
			AND lower(mb.branch_Name) like lower('%'+ :branchName +'%')
	</sql-query>
</hibernate-mapping>