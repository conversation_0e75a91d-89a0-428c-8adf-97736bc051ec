<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	
	<sql-query name="lookup.dealer.listExcCriteria">
		<query-param name="uuidDealer" type="string"/>
		<query-param name="dealerName" type="string" />
		<query-param name="dealerAddress" type="string" />
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH n(UUID_DEALER, DEALER_NAME, DEALER_ADDRESS,dealer_code,level) AS 
	   		(
			SELECT UUID_DEALER, DEALER_NAME, DEALER_ADDRESS,DEALER_CODE, 1 level
			FROM MS_DEALER with (nolock)
			WHERE UUID_DEALER = :uuidDealer and IS_ACTIVE = '1' 
				UNION ALL
			SELECT nplus1.UUID_DEALER, nplus1.DEALER_NAME, nplus1.DEALER_ADDRESS,
				nplus1.DEALER_CODE,n.level+1
			FROM MS_DEALER as nplus1 with (nolock), n
			WHERE n.UUID_DEALER = nplus1.parent_id and nplus1.IS_ACTIVE = '1'
			)
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select n.UUID_DEALER keyValue, n.DEALER_NAME dealerName, 
					n.DEALER_ADDRESS dealerAddress, n.level dealerLevel, n.DEALER_CODE dealerCode,
				ROW_NUMBER() OVER (ORDER BY n.DEALER_CODE)  AS rownum  
				from n
				where lower(n.dealer_name) like lower('%'+ :dealerName +'%') 
				AND lower(n.DEALER_ADDRESS) like lower('%'+ :dealerAddress +'%')
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.dealer.listExcCriteriaCnt">
		<query-param name="uuidDealer" type="string"/>
		<query-param name="dealerName" type="string" />
		<query-param name="dealerAddress" type="string" />
			SELECT count(1)
			from dbo.getDealerByLogin(:uuidDealer) n
			where lower(n.dealername) like lower('%'+ :dealerName +'%') 
			AND lower(n.DEALERADDRESS) like lower('%'+ :dealerAddress +'%')
	</sql-query>

	<sql-query name="lookup.dealer.listDobCriteria">
		<query-param name="uuidBranch" type="string"/>
		<query-param name="dealerName" type="string" />
		<query-param name="dealerAddress" type="string" />
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT md.UUID_Dealer, md.dealer_name, md.dealer_address,md.DEALER_CODE,
					md.DEALER_CODE dealerCode,
					ROW_NUMBER() OVER (ORDER BY md.DEALER_CODE) AS rownum  
				from MS_DEALEROFBRANCH dob with (nolock) 
				left outer join ms_dealer md with (nolock)
					on dob.UUID_Dealer = md.UUID_Dealer
				where dob.UUID_branch = :uuidBranch 
				and lower(md.dealer_name) like lower('%'+ :dealerName +'%') 
				AND lower(md.DEALER_ADDRESS) like lower('%'+ :dealerAddress +'%')
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.dealer.listDobCriteriaCnt">
		<query-param name="uuidBranch" type="string"/>
		<query-param name="dealerName" type="string" />
		<query-param name="dealerAddress" type="string" />
			SELECT count(1)
			from MS_DEALEROFBRANCH dob with (nolock) 
			left outer join ms_dealer md with (nolock)
				on dob.UUID_Dealer = md.UUID_Dealer
			where dob.UUID_branch = :uuidBranch 
			and lower(md.dealer_name) like lower('%'+ :dealerName +'%') 
			AND lower(md.DEALER_ADDRESS) like lower('%'+ :dealerAddress +'%')
	</sql-query>

</hibernate-mapping>