<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="lookup.job.listExcCriteriaAdmin">
		<query-param name="uuidJob" type="string"/>
		<query-param name="jobCode" type="string" />
		<query-param name="description" type="string" />
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT n.keyValue, n.jobCode, n.description, n.isBranch, n.jobLevel, 
					n.isField<PERSON>erson, ROW_NUMBER() OVER (ORDER BY n.jobCode) AS rownum  
				from dbo.getJobByLogin(:uuidJob) n 
				where lower(n.jobCode) like lower('%'+ :jobCode +'%') 
				AND lower(n.description) like lower('%'+ :description +'%')
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.job.listExcCriteriaAdminCnt">
		<query-param name="uuidJob" type="string"/>
		<query-param name="jobCode" type="string" />
		<query-param name="description" type="string" />
		SELECT count(1) 
		from dbo.getJobByLogin(:uuidJob) n 
		where lower(n.jobCode) like lower('%'+ :jobCode +'%') 
		AND lower(n.description) like lower('%'+ :description +'%')
	</sql-query>
	
	<sql-query name="lookup.job.listExcCriteriaLogin">
		<query-param name="uuidJob" type="string"/>
		<query-param name="jobCode" type="string" />
		<query-param name="description" type="string" />
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT n.keyValue, n.jobCode, n.description, n.isBranch, n.jobLevel, 
					n.isFieldPerson, ROW_NUMBER() OVER (ORDER BY n.jobCode)  AS rownum 
				from dbo.getJobByLogin(:uuidJob) n 
				where lower(n.jobCode) like lower('%'+ :jobCode +'%') 
				AND lower(n.description) like lower('%'+ :description +'%')
				AND  n.keyValue != :uuidJob
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.job.listExcCriteriaLoginCnt">
		<query-param name="uuidJob" type="string"/>
		<query-param name="jobCode" type="string" />
		<query-param name="description" type="string" />
		SELECT count(1)
		from dbo.getJobByLogin(:uuidJob) n 
		where lower(n.jobCode) like lower('%'+ :jobCode +'%') 
		AND lower(n.description) like lower('%'+ :description +'%')
		AND n.keyValue != :uuidJob
	</sql-query>

</hibernate-mapping>