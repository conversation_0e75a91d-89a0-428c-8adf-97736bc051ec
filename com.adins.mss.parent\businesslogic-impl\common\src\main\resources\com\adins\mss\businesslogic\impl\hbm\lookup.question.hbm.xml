<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="lookup.question.listQuestionForm">
		<query-param name="uuidForm" type="string" />
		<query-param name="questionLabel" type="string" />
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>		
		SELECT * FROM (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
			    SELECT DISTINCT MSQ.UUID_QUESTION, MSQ.QUESTION_LABEL
			    	, ROW_NUMBER() OVER (ORDER BY MSQ.QUESTION_LABEL ASC) AS rownum 
		    	FROM MS_QUESTION MSQ with (nolock) 
			    JOIN MS_QUESTIONOFGROUP MSQG with (nolock) 
			    	ON MSQ.UUID_QUESTION = MSQG.UUID_QUESTION
			    JOIN MS_QUESTIONGROUPOFFORM MSQGF with (nolock) 
			    	ON MSQG.UUID_QUESTION_GROUP = MSQGF.UUID_QUESTION_GROUP
		    	WHERE MSQGF.UUID_FORM = :uuidForm 
		    	AND	MSQ.QUESTION_LABEL like lower('%'+ :questionLabel +'%')
		  	) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="lookup.question.cntListQuestionForm">
	    <query-param name="uuidForm" type="string" />
		<query-param name="questionLabel" type="string" />
	    SELECT count(DISTINCT MSQ.UUID_QUESTION)
	    FROM MS_QUESTION MSQ with (nolock)
		JOIN MS_QUESTIONOFGROUP MSQG with (nolock) 
			ON MSQ.UUID_QUESTION = MSQG.UUID_QUESTION 
		JOIN MS_QUESTIONGROUPOFFORM MSQGF with (nolock) 
			ON MSQG.UUID_QUESTION_GROUP = MSQGF.UUID_QUESTION_GROUP 
		WHERE MSQGF.UUID_FORM = :uuidForm 
		AND MSQ.QUESTION_LABEL like lower('%'+ :questionLabel +'%')
	</sql-query>
	

</hibernate-mapping>