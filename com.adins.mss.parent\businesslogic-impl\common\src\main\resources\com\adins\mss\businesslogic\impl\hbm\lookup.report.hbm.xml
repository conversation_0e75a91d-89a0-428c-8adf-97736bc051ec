<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="lookup.report.getListHierarkiBranch">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="branchLogin" type="string"/>
		<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select keyValue, BRANCH_CODE, BRANCH_NAME, ROW_NUMBER() OVER (ORDER BY BRANCH_NAME, BRANCH_CODE) AS rownum 
				from dbo.getCabangByLogin(:branchLogin)
				where BRANCH_CODE like '%' + :branchCode + '%'
				and BRANCH_NAME like '%' + :branchName + '%'
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.report.getListHierarkiBranchCount">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="branchLogin" type="string"/>
		select count(BRANCH_CODE) 
		from dbo.getCabangByLogin(:branchLogin)
		where BRANCH_CODE like '%' + :branchCode + '%'
		and BRANCH_NAME like '%' + :branchName + '%'
	</sql-query>
	
	<sql-query name="lookup.report.getListHierarkiBranchByRegion">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="branchLogin" type="string"/>
		<query-param name="uuidRegion" type="string"/>
		<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select cabang.keyValue, cabang.BRANCH_CODE, cabang.BRANCH_NAME, ROW_NUMBER() OVER (ORDER BY cabang.BRANCH_NAME, cabang.BRANCH_CODE) AS rownum 
				from dbo.getCabangByLogin(:branchLogin) cabang
				join ms_branch mb with(nolock) on cabang.keyValue = mb.uuid_branch and mb.UUID_REGION = :uuidRegion
				where cabang.BRANCH_CODE like '%' + :branchCode + '%'
				and cabang.BRANCH_NAME like '%' + :branchName + '%'
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.report.getListHierarkiBranchCountByRegion">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="branchLogin" type="string"/>
		select count(cabang.BRANCH_CODE) 
		from dbo.getCabangByLogin(:branchLogin) cabang
		join ms_branch mb with(nolock) on cabang.keyValue = mb.uuid_branch and mb.UUID_REGION = :uuidRegion
		where cabang.BRANCH_CODE like '%' + :branchCode + '%'
		and cabang.BRANCH_NAME like '%' + :branchName + '%'
	</sql-query>
	
	<sql-query name="lookup.report.getUserSpvByBranch">
		<query-param name="userLoginId" type="string" />
		<query-param name="userName" type="string" />
		<query-param name="uuidBranchLogin" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="jobCode" type="string" />
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.UNIQUE_ID, 
				msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.UNIQUE_ID, 
				msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1' and msu2.UUID_MS_USER != msu2.SPV_ID
		)
		select * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME, c.BRANCH_NAME) AS rownum  
				FROM (
					select distinct UUID_MS_USER, UNIQUE_ID, FULL_NAME, cbg.BRANCH_NAME
					from N 
						join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
						join (
							SELECT keyvalue as uuid_branch, branch_name
							from dbo.getCabangByLogin(:uuidBranchLogin)
						) cbg on cbg.uuid_branch = n.uuid_branch 
					where  J.JOB_CODE in (:jobCode)
						and N.UUID_BRANCH like lower('%'+ :uuidBranch +'%')
						and UNIQUE_ID like '%' + :userLoginId + '%'
						and FULL_NAME like '%' + :userName + '%'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.report.getUserSpvByBranchCount">
		<query-param name="userLoginId" type="string" />
		<query-param name="userName" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="jobCode" type="string" />
	   	<query-param name="uuidBranchLogin" type="string" />
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.UNIQUE_ID, 
				msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.UNIQUE_ID, 
				msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1' and msu2.UUID_MS_USER != msu2.SPV_ID
		)
		SELECT count(1) 
		FROM (
			select distinct UUID_MS_USER, UNIQUE_ID, FULL_NAME, cbg.BRANCH_NAME
			from N 
				join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
				join (
					SELECT keyvalue as uuid_branch, branch_name
					from dbo.getCabangByLogin(:uuidBranchLogin)
				) cbg on cbg.uuid_branch = n.uuid_branch 
			where  J.JOB_CODE in (:jobCode)
				and N.UUID_BRANCH like lower('%'+ :uuidBranch +'%')
				and UNIQUE_ID like '%' + :userLoginId + '%'
				and FULL_NAME like '%' + :userName + '%'
		) c
	</sql-query>
	
	<sql-query name="lookup.report.getUserBySpvAllBranch">
		<query-param name="userLoginId" type="string" />
		<query-param name="userName" type="string" />
	   	<query-param name="subsystemCode" type="string" />
	   	<query-param name="uuidBranchLogin" type="string" />
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_SUBSYSTEM, msu.UUID_MS_USER, msu.FULL_NAME,
				msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 
				0 as LEVEL, msu.UNIQUE_ID, msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1' 
				and ISNULL(cast(msu.SPV_ID as varchar(max)), '%') like lower('%'+ :uuidSpv +'%')
			UNION ALL
			SELECT msu2.UUID_MS_SUBSYSTEM, msu2.UUID_MS_USER, msu2.FULL_NAME,
				N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, 
				msu2.UNIQUE_ID, msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID 
				AND msu2.IS_ACTIVE = '1'
		)
		select * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME, c.BRANCH_NAME) AS rownum  
				FROM (
					select distinct UUID_MS_USER, UNIQUE_ID, FULL_NAME, cbg.BRANCH_NAME
					from N
						join AM_MSSUBSYSTEM subs on N.UUID_MS_SUBSYSTEM = subs.UUID_MS_SUBSYSTEM
						join MS_JOB job on N.uuid_job = job.UUID_JOB
						join (
							SELECT keyvalue as uuid_branch, branch_name
							from dbo.getCabangByLogin(:uuidBranchLogin)
						) cbg on cbg.uuid_branch = n.uuid_branch 
					where subs.SUBSYSTEM_NAME = :subsystemCode
						and job.IS_FIELD_PERSON = '1'
						and UNIQUE_ID like '%' + :userLoginId + '%'
						and FULL_NAME like '%' + :userName + '%'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>

	<sql-query name="lookup.report.getUserBySpvAllBranchCount">
	   	<query-param name="userLoginId" type="string" />
		<query-param name="userName" type="string" />
	   	<query-param name="subsystemCode" type="string" />
	   	<query-param name="uuidBranchLogin" type="string" />
	   	<query-param name="uuidSpv" type="string" />
		WITH N AS (
			SELECT msu.UUID_MS_SUBSYSTEM, msu.UUID_MS_USER, msu.FULL_NAME,
				msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 
				0 as LEVEL, msu.UNIQUE_ID, msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1' 
				and ISNULL(cast(msu.SPV_ID as varchar(max)), '%') like lower('%'+ :uuidSpv +'%')
			UNION ALL
			SELECT msu2.UUID_MS_SUBSYSTEM, msu2.UUID_MS_USER, msu2.FULL_NAME,
				N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, 
				msu2.UNIQUE_ID, msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID 
				AND msu2.IS_ACTIVE = '1'
		)
		SELECT Count(1)
		FROM (
			select distinct UUID_MS_USER, UNIQUE_ID, FULL_NAME, cbg.BRANCH_NAME
			from N
				join AM_MSSUBSYSTEM subs on N.UUID_MS_SUBSYSTEM = subs.UUID_MS_SUBSYSTEM
				join MS_JOB job on N.uuid_job = job.UUID_JOB
				join (
					SELECT keyvalue as uuid_branch, branch_name
					from dbo.getCabangByLogin(:uuidBranchLogin)
				) cbg on cbg.uuid_branch = n.uuid_branch 
			where subs.SUBSYSTEM_NAME = :subsystemCode
				and job.IS_FIELD_PERSON = '1'
				and UNIQUE_ID like '%' + :userLoginId + '%'
				and FULL_NAME like '%' + :userName + '%'
		) c
	</sql-query>

	<sql-query name="lookup.report.getUserBySpvBranch">
	   	<query-param name="userLoginId" type="string" />
		<query-param name="userName" type="string" />
	   	<query-param name="subsystemCode" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_SUBSYSTEM, msu.UUID_MS_USER, msu.FULL_NAME, 
				msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 
				0 as LEVEL, msu.UNIQUE_ID, msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1' 
				and msu.UUID_BRANCH like lower('%'+ :uuidBranch +'%') 
				and ISNULL(cast(msu.SPV_ID as varchar(max)), '%') like lower('%'+ :uuidSpv +'%')
			UNION ALL
			SELECT msu2.UUID_MS_SUBSYSTEM, msu2.UUID_MS_USER, msu2.FULL_NAME, 
				N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, 
				msu2.UNIQUE_ID, msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID 
				AND msu2.IS_ACTIVE = '1'
		)
		select * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME, c.BRANCH_NAME) AS rownum  
				FROM (
					select distinct UUID_MS_USER, UNIQUE_ID, FULL_NAME, br.BRANCH_NAME
					from N
						join AM_MSSUBSYSTEM subs on N.UUID_MS_SUBSYSTEM = subs.UUID_MS_SUBSYSTEM
						join MS_JOB job on N.uuid_job = job.UUID_JOB
						join MS_BRANCH br on N.UUID_BRANCH = br.UUID_BRANCH
					where subs.SUBSYSTEM_NAME = :subsystemCode
						and job.IS_FIELD_PERSON = '1'
						and UNIQUE_ID like '%' + :userLoginId + '%'
						and FULL_NAME like '%' + :userName + '%'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.report.getUserBySpvBranchCount">
	   	<query-param name="userLoginId" type="string" />
		<query-param name="userName" type="string" />
	   	<query-param name="subsystemCode" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="uuidSpv" type="string" />
		WITH N AS (
			SELECT msu.UUID_MS_SUBSYSTEM, msu.UUID_MS_USER, msu.FULL_NAME, 
				msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 
				0 as LEVEL, msu.UNIQUE_ID, msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1' 
				and msu.UUID_BRANCH like lower('%'+ :uuidBranch +'%') 
				and ISNULL(cast(msu.SPV_ID as varchar(max)), '%') like lower('%'+ :uuidSpv +'%')
			UNION ALL
			SELECT msu2.UUID_MS_SUBSYSTEM, msu2.UUID_MS_USER, msu2.FULL_NAME, 
				N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, 
				msu2.UNIQUE_ID, msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID 
				AND msu2.IS_ACTIVE = '1'
		)
		SELECT Count(1) 
		FROM (
			select distinct UUID_MS_USER, UNIQUE_ID, FULL_NAME, br.BRANCH_NAME
			from N
				join AM_MSSUBSYSTEM subs on N.UUID_MS_SUBSYSTEM = subs.UUID_MS_SUBSYSTEM
				join MS_JOB job on N.uuid_job = job.UUID_JOB
				join MS_BRANCH br on N.UUID_BRANCH = br.UUID_BRANCH
			where subs.SUBSYSTEM_NAME = :subsystemCode
				and job.IS_FIELD_PERSON = '1'
				and UNIQUE_ID like '%' + :userLoginId + '%'
				and FULL_NAME like '%' + :userName + '%'
		) c
	</sql-query>
	
</hibernate-mapping>