<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="report.absensi.getAttendanceSummary">
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="branchId" type="string"/>
		<query-param name="subsystemId" type="string"/>
		SELECT <![CDATA[ MB.BRANCH_CODE, MB.BRANCH_NAME, 
			SUM(CASE WHEN FORMAT(datetime_IN, 'HH:mm:ss') < '08:00:00.000' THEN 1 ELSE 0 END) PREV8,
			SUM(CASE WHEN FORMAT(datetime_IN, 'HH:mm:ss') BETWEEN '08:00:00.000' AND '08:30:00.000' 
				THEN 1 ELSE 0 END) NEXTFR8,
			SUM(CASE WHEN FORMAT(datetime_IN, 'HH:mm:ss') BETWEEN '08:30:00.000' AND '09:00:00.000' 
				THEN 1 ELSE 0 END) NEXTFR83,
			SUM(CASE WHEN FORMAT(datetime_IN, 'HH:mm:ss') > '09:00:00.000' THEN 1 ELSE 0 END) NEXT9,
			SUM(CASE WHEN AMU.UUID_MS_USER IS NOT NULL  AND datetime_IN IS NULL THEN 1 ELSE 0 END) NOTATTEND, 
			COUNT(AMU.UUID_MS_USER) GRAND, MB.UUID_BRANCH ]]>
		FROM MS_BRANCH MB with (nolock)
			join (
				SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
				FROM dbo.getCabangByLogin(:branchId)
			) msb on mb.UUID_BRANCH = msb.UUID_BRANCH
			JOIN AM_MSUSER AMU with (nolock) ON MB.UUID_BRANCH = AMU.UUID_BRANCH
			INNER JOIN MS_JOB JOB with (nolock) ON AMU.UUID_JOB = JOB.UUID_JOB
		    	AND JOB.IS_BRANCH = '1' AND JOB.IS_FIELD_PERSON = '1'
			LEFT OUTER JOIN TR_ATTENDANCE TRA with (nolock) ON AMU.UUID_MS_USER = TRA.UUID_MS_USER
				AND MB.UUID_BRANCH = TRA.UUID_BRANCH
				AND datetime_IN BETWEEN :startDate AND :endDate
		WHERE AMU.UUID_MS_SUBSYSTEM = :subsystemId
				AND AMU.IS_ACTIVE = '1'
		GROUP BY MB.BRANCH_CODE, MB.BRANCH_NAME, MB.UUID_BRANCH
		ORDER BY BRANCH_CODE
	</sql-query>
	<sql-query name="report.absensi.getAttendanceDetail">
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="branchId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="subsystemId" type="string"/>
		SELECT FULL_NAME, FORMAT(datetime_IN, 'dd/MM/yyyy') date, 
			FORMAT(datetime_IN, 'HH:mm:ss') attendance_in, LATITUDE_IN, LONGITUDE_IN, ATTD_ADDRESS 
		FROM TR_ATTENDANCE TRA with (nolock)
			JOIN AM_MSUSER AMU with (nolock) ON TRA.UUID_MS_USER = AMU.UUID_MS_USER
			join (
				SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
				FROM dbo.getCabangByLogin(:branchId)
			) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
			INNER JOIN MS_JOB JOB with (nolock) ON AMU.UUID_JOB = JOB.UUID_JOB
		WHERE TRA.UUID_MS_USER LIKE :userId
			AND datetime_IN BETWEEN :startDate AND :endDate
			AND AMU.UUID_MS_SUBSYSTEM = :subsystemId
			AND JOB.IS_BRANCH = '1'
			AND JOB.IS_FIELD_PERSON = '1'
			AND AMU.IS_ACTIVE = '1'
		ORDER BY FULL_NAME, datetime_IN ASC
	</sql-query>
	<sql-query name="report.absensi.getAttendanceDetailByBranch">
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="branchId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="subsystemId" type="string"/>
		SELECT FULL_NAME, FORMAT(datetime_IN, 'dd/MM/yyyy') date, 
			FORMAT(datetime_IN, 'HH:mm:ss') attendance_in, LATITUDE_IN, LONGITUDE_IN, ATTD_ADDRESS
		FROM TR_ATTENDANCE TRA with (nolock)
			JOIN AM_MSUSER AMU with (nolock) ON TRA.UUID_MS_USER = AMU.UUID_MS_USER
			INNER JOIN MS_JOB JOB with (nolock) ON AMU.UUID_JOB = JOB.UUID_JOB
		WHERE TRA.UUID_BRANCH LIKE :branchId
			AND TRA.UUID_MS_USER LIKE :userId
			AND datetime_IN BETWEEN :startDate AND :endDate
			AND AMU.UUID_MS_SUBSYSTEM = :subsystemId
			AND JOB.IS_BRANCH = '1'
			AND JOB.IS_FIELD_PERSON = '1'
			AND AMU.IS_ACTIVE = '1'
		ORDER BY FULL_NAME, datetime_IN ASC
	</sql-query>
	<sql-query name="report.absensi.getUsersAll">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID, 
					ROW_NUMBER() OVER (ORDER BY FULL_NAME) AS rownum 
				from N
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="report.absensi.countUsersAll">
	   	<query-param name="uuidSpv" type="string" />
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select count(1)
		from N
	</sql-query>
	
	<sql-query name="report.absensi.getUsersAllByBranch">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystem" type="string"/>
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		select * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum 
				FROM (
					SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_MS_USER AS HIRARKI,
						CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
					FROM  AM_MSUSER msu with (nolock)
						inner join (
							SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
							FROM dbo.getCabangByLogin(:uuidBranch)
						) msb on msu.UUID_BRANCH = msb.UUID_BRANCH
						inner join ms_job mj on mj.uuid_job = msu.uuid_job
					WHERE msu.IS_ACTIVE = '1' 
						and msu.UUID_MS_SUBSYSTEM = :subsystem
						and mj.is_field_person = '1'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="report.absensi.getUsersAllByBranchSpv">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystem" type="string"/>
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
				inner join ms_job mj with (nolock) on mj.uuid_job = msu.uuid_job
			WHERE msu.IS_ACTIVE = '1' and msu.UUID_MS_SUBSYSTEM = :subsystem
				and mj.is_field_person = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum 
				FROM (
					select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
					from N 
					WHERE N.UUID_BRANCH = :uuidBranch
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="report.absensi.getUsersAllByBranchCount">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystem" type="string"/>
		SELECT Count(*) 
		FROM (
			select distinct UUID_MS_USER
			FROM  AM_MSUSER msu with (nolock)
				inner join (
					SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
					FROM dbo.getCabangByLogin(:uuidBranch)
				) msb on msu.UUID_BRANCH = msb.UUID_BRANCH
				inner join ms_job mj on mj.uuid_job = msu.uuid_job
			WHERE msu.IS_ACTIVE = '1' 
				and msu.UUID_MS_SUBSYSTEM = :subsystem
				and mj.is_field_person = '1'
		) c
	</sql-query>
	
	<sql-query name="report.absensi.getUsersAllByBranchCountSpv">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystem" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
				inner join ms_job mj with (nolock) on mj.uuid_job = msu.uuid_job
			WHERE msu.IS_ACTIVE = '1' and msu.UUID_MS_SUBSYSTEM = :subsystem
				and mj.is_field_person = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, msu2.UUID_BRANCH
			FROM  AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		SELECT Count(*) 
		FROM (
			select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
			from N 
			WHERE N.UUID_BRANCH = :uuidBranch
		) c
	</sql-query>
	
	<sql-query name="report.absensi.getUsersSurveyorAllByBranch">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystem" type="string"/>
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="jobCode" type="string"/>
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  
				FROM (
					select distinct msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, 
						msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 
						0 as LEVEL
					FROM  AM_MSUSER msu with (nolock)
						inner join (
							SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
							FROM dbo.getCabangByLogin(:uuidBranch)
						) msb on msu.UUID_BRANCH = msb.UUID_BRANCH
						inner join MS_JOB mj on mj.uuid_job = msu.uuid_job
					WHERE msu.IS_ACTIVE = '1' 
						and msu.UUID_MS_SUBSYSTEM = :subsystem
						and mj.is_field_person = '1'
						and mj.job_code = :jobCode
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="report.absensi.getUsersSurveyorAllByBranchSpv">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystem" type="string"/>
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="jobCode" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
				inner join ms_job mj on mj.uuid_job = msu.uuid_job
			WHERE msu.IS_ACTIVE = '1' and msu.UUID_MS_SUBSYSTEM = :subsystem
				and is_field_person = '1'
				and job_code = :jobCode
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, msu2.UUID_BRANCH
			FROM   AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  
				FROM (
					select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
					from N 
					WHERE N.UUID_BRANCH = :uuidBranch
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="report.absensi.getUsersSurveyorAllByBranchCount">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystem" type="string"/>
	   	<query-param name="jobCode" type="string"/>
		SELECT Count(*) 
		FROM (
			select distinct msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID
			FROM  AM_MSUSER msu with (nolock)
				inner join (
					SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
					FROM dbo.getCabangByLogin(:uuidBranch)
				) msb on msu.UUID_BRANCH = msb.UUID_BRANCH
				inner join MS_JOB mj on mj.uuid_job = msu.uuid_job
			WHERE msu.IS_ACTIVE = '1' 
				and msu.UUID_MS_SUBSYSTEM = :subsystem
				and mj.is_field_person = '1'
				and mj.job_code = :jobCode
		) c
	</sql-query>
	
	<sql-query name="report.absensi.getUsersSurveyorAllByBranchCountSpv">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystem" type="string"/>
	   	<query-param name="jobCode" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
				inner join ms_job mj on mj.uuid_job = msu.uuid_job
			WHERE msu.IS_ACTIVE = '1' and msu.UUID_MS_SUBSYSTEM = :subsystem
				and is_field_person = '1'
				and job_code = :jobCode
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, msu2.UUID_BRANCH
			FROM   AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		SELECT Count(*)  
		FROM (
			select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
			from N 
			WHERE N.UUID_BRANCH = :uuidBranch
		) c
	</sql-query>

	<sql-query name="report.absensi.getAllUsersBySpv">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.fullName) AS rownum  
				FROM (
					select amu.* 
					from dbo.getAllUserByLogin(:uuidUser) as amu
					where amu.keyValue != :uuidUser
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="report.absensi.getAllUsersBySpvCount">
	   	<query-param name="uuidUser" type="string" />
		select count(1) 
		from dbo.getAllUserByLogin(:uuidUser) as amu
		where amu.keyValue != :uuidUser
	</sql-query>
	<sql-query name="report.absensi.getAllUsersByBranchCount">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="uuidMsSubsystem" type="string" />
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="jobcode" type="string"/>
		SELECT Count(*) FROM (
			select distinct msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID
			FROM  AM_MSUSER msu with (nolock)
				join MS_JOB mj with (nolock) on mj.UUID_JOB = msu.UUID_JOB
				join (
					SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
					FROM dbo.getCabangByLogin(:uuidBranch)
				) msb on msu.UUID_BRANCH = msb.UUID_BRANCH
			WHERE msu.uuid_ms_subsystem = :uuidMsSubsystem
				and msu.UUID_MS_USER != :uuidUser
				and mj.JOB_CODE like '%' + :jobcode + '%'
		) c
	</sql-query>
	<sql-query name="report.absensi.getAllUsersByBranch">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="uuidMsSubsystem" type="string" />
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="jobcode" type="string"/>
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  
				FROM (
					select distinct msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
						CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID
					FROM AM_MSUSER msu with (nolock)
						join MS_JOB mj with (nolock) on mj.UUID_JOB = msu.UUID_JOB
						join (
							SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
							FROM dbo.getCabangByLogin(:uuidBranch)
						) msb on msu.UUID_BRANCH = msb.UUID_BRANCH
					WHERE msu.uuid_ms_subsystem = :uuidMsSubsystem
						and msu.UUID_MS_USER != :uuidUser
						and mj.JOB_CODE like '%' + :jobcode + '%'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
</hibernate-mapping>