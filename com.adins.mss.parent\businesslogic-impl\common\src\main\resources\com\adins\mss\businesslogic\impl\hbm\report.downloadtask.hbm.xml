<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="report.download.getlistbranch">
		<query-param name="uuidBranch" type="string"/>
		WITH N AS (
			SELECT msb.UUID_BRANCH, msb.PARENT_ID, msb.BRANCH_CODE, msb.BRANCH_NAME, msb.BRANCH_ADDRESS,
		  		msb.UUID_BRANCH AS HIRARKI,CAST(msb.UUID_BRANCH AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ
		  	FROM  MS_BRANCH msb with (nolock)
		 	WHERE msb.UUID_BRANCH = :uuidBranch

		  	UNION ALL

		  	SELECT msb2.UUID_BRANCH, msb2.PARENT_ID, msb2.<PERSON><PERSON>CH_CODE, msb2.<PERSON>ANCH_NAME, msb2.BR<PERSON>CH_ADDRESS,
		  		N.HIRARKI, N.HIRARKI2+'/'+CAST(msb2.UUID_BRANCH AS VARCHAR(MAX)), N.SEQ+1
		  	FROM  MS_BRANCH msb2 with (nolock),N
		  	WHERE N.UUID_BRANCH=msb2.PARENT_ID
		)
		Select n.UUID_BRANCH, n.BRANCH_NAME, n.BRANCH_CODE
		from N
		order by n.BRANCH_CODE
	</sql-query>
	
	<sql-query name="report.download.getlistuser">
		<query-param name="uuidBranch" type="string"/>
		<query-param name="subsystemName" type="string"/>
		SELECT usr.UUID_MS_USER as [key], usr.LOGIN_ID + ' - ' + usr.FULL_NAME as [value] 
		FROM AM_MSUSER usr with (nolock)
			join MS_JOB jb with (nolock) on usr.UUID_JOB = jb.UUID_JOB
			join AM_MSSUBSYSTEM subsys with (nolock) on usr.UUID_MS_SUBSYSTEM = subsys.UUID_MS_SUBSYSTEM
		WHERE usr.IS_ACTIVE = 1 
			AND jb.IS_FIELD_PERSON = 1 
			AND usr.UUID_BRANCH = :uuidBranch
			AND subsys.SUBSYSTEM_NAME = :subsystemName
		ORDER BY usr.FULL_NAME
	</sql-query> 
		
	<sql-query name="report.download.downloadtaskms">
		<query-param name="uuidUser" type="string"/>
		<query-param name="uuidSpv" type="string"/>
		<query-param name="uuidBranch" type="string"/>
		<query-param name="uuidForm" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		SELECT trth.APPL_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ammsuspv.FULL_NAME, 
			ammsu.LOGIN_ID, LEFT(CONVERT(VARCHAR, trth.ASSIGN_DATE, 113), 17) as assign_date,
			LEFT(CONVERT(VARCHAR, trth.DOWNLOAD_DATE, 113), 17) as download_date,
			LEFT(CONVERT(VARCHAR, trth.READ_DATE, 113), 17) as read_date,
			LEFT(CONVERT(VARCHAR, trth.START_DTM, 113), 17) as start_date,
			LEFT(CONVERT(VARCHAR, trth.SEND_DATE, 113), 17) as send_date,
			LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as submit_date,
			LEFT(CONVERT(VARCHAR, trth.PROMISE_DATE, 113), 17) as promise_date,
			LEFT(CONVERT(VARCHAR, trth.APPROVAL_DATE, 113), 17) as approval_date,	
			trth.CUSTOMER_NAME, 
			trth.CUSTOMER_PHONE, trth.CUSTOMER_ADDRESS, trth.NOTES, trth.LATITUDE,
			trth.LONGITUDE, trth.UUID_TASK_H, '1' as flag
		FROM TR_TASK_H trth with (nolock) 
			left join TR_TASKORDERDATA trtod with (nolock) on trth.UUID_TASK_H = trtod.UUID_TASK_ID
			left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM
			LEFT JOIN MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY
			left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER
			left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER
		WHERE trth.UUID_MS_USER like lower ('%' + :uuidUser + '%') 
			and trth.UUID_FORM = :uuidForm
			AND ammsu.UUID_BRANCH like lower('%'+ :uuidBranch +'%') 
			and ammsu.SPV_ID like lower('%'+ :uuidSpv +'%')
			AND msst.STATUS_CODE not in ('A')
			AND assign_date between :startDate and :endDate

		UNION ALL
		
		SELECT ftrth.APPL_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ammsuspv.FULL_NAME,  
			ammsu.LOGIN_ID, LEFT(CONVERT(VARCHAR, ftrth.ASSIGN_DATE, 113), 17) as assign_date,
			LEFT(CONVERT(VARCHAR, ftrth.DOWNLOAD_DATE, 113), 17) as download_date,
			LEFT(CONVERT(VARCHAR, ftrth.READ_DATE, 113), 17) as read_date,
			LEFT(CONVERT(VARCHAR, ftrth.START_DTM, 113), 17) as start_date,
			LEFT(CONVERT(VARCHAR, ftrth.SEND_DATE, 113), 17) as send_date,
			LEFT(CONVERT(VARCHAR, ftrth.SUBMIT_DATE, 113), 17) as submit_date,
			LEFT(CONVERT(VARCHAR, ftrth.PROMISE_DATE, 113), 17) as promise_date,
			LEFT(CONVERT(VARCHAR, ftrth.APPROVAL_DATE, 113), 17) as approval_date,	 ftrth.CUSTOMER_NAME, 
			ftrth.CUSTOMER_PHONE, ftrth.CUSTOMER_ADDRESS, ftrth.NOTES, ftrth.LATITUDE, ftrth.LONGITUDE,
			ftrth.UUID_TASK_H, '2' as flag
		FROM FINAL_TR_TASK_H ftrth with (nolock) 
			left join FINAL_TR_TASKORDERDATA ftrtod with (nolock) on ftrth.UUID_TASK_H = ftrtod.UUID_TASK_ID
			left join MS_FORM msf with (nolock) on ftrth.UUID_FORM = msf.UUID_FORM
			LEFT JOIN MS_PRIORITY msp with (nolock) on ftrth.UUID_PRIORITY = msp.UUID_PRIORITY
			left join MS_STATUSTASK msst with (nolock) on ftrth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = ftrth.UUID_MS_USER
			left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER
		WHERE ftrth.UUID_MS_USER like lower ('%' + :uuidUser + '%') 
			and ftrth.UUID_FORM = :uuidForm
			AND ammsu.UUID_BRANCH like lower('%'+ :uuidBranch +'%') 
			and ammsu.SPV_ID like lower('%'+ :uuidSpv +'%')
			AND msst.STATUS_CODE not in ('A')
			AND assign_date between :startDate and :endDate
	</sql-query>
	
	<sql-query name="report.download.downloadtaskmc">
		<query-param name="uuidUser" type="string"/>
		<query-param name="uuidSpv" type="string"/>
		<query-param name="uuidBranch" type="string"/>
		<query-param name="uuidForm" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		SELECT trth.AGREEMENT_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ammsuspv.FULL_NAME, 
			ammsu.LOGIN_ID, LEFT(CONVERT(VARCHAR, trth.SEND_DATE, 113), 17) as send_date, 
			LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as submit_date, trth.CUSTOMER_NAME, 
			trth.CUSTOMER_PHONE, trth.CUSTOMER_ADDRESS, trth.NOTES,(
				SELECT TASK_ID 
				FROM TR_TASK_H with (nolock) 
				WHERE UUID_TASK_H = trlink.UUID_TASK_H_COLLECT
			) as SURVEY_ID, trth.LATITUDE, trth.LONGITUDE, trth.UUID_TASK_H, '1' as flag
		FROM TR_TASK_H trth with (nolock) 
			left join TR_TASKORDERDATA trtod with (nolock) on trth.UUID_TASK_H = trtod.UUID_TASK_ID
			left join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM
			LEFT JOIN MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY
			left join MS_STATUSTASK msst with (nolock) on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = trth.UUID_MS_USER
			left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER
			left join TR_TASKLINK trlink with (nolock) on trth.UUID_TASK_H = trlink.UUID_TASK_H_COLLECT
		WHERE trth.UUID_MS_USER like lower ('%' + :uuidUser + '%') 
			and trth.UUID_FORM = :uuidForm
			AND ammsu.UUID_BRANCH like lower('%'+ :uuidBranch +'%') 
			and ammsu.SPV_ID like lower('%'+ :uuidSpv +'%')
			AND msst.STATUS_CODE not in ('A')
			AND assign_date between :startDate and :endDate

		UNION ALL
		
		SELECT ftrth.AGREEMENT_NO, msf.FORM_NAME, msst.STATUS_TASK_DESC, msp.PRIORITY_DESC, ammsuspv.FULL_NAME, 
			ammsu.LOGIN_ID, LEFT(CONVERT(VARCHAR, ftrth.SEND_DATE, 113), 17) as send_date, 
			LEFT(CONVERT(VARCHAR, ftrth.SUBMIT_DATE, 113), 17) as submit_date, ftrth.CUSTOMER_NAME, 
			ftrth.CUSTOMER_PHONE, ftrth.CUSTOMER_ADDRESS, ftrth.NOTES, (
				SELECT TASK_ID 
				FROM FINAL_TR_TASK_H with (nolock) 
				WHERE UUID_TASK_H = ftrlink.UUID_TASK_H_COLLECT
			) as SURVEY_ID, ftrth.LATITUDE, ftrth.LONGITUDE, ftrth.UUID_TASK_H, '2' as flag
		FROM FINAL_TR_TASK_H ftrth with (nolock) 
			left join FINAL_TR_TASKORDERDATA ftrtod with (nolock) on ftrth.UUID_TASK_H = ftrtod.UUID_TASK_ID
			left join MS_FORM msf with (nolock) on ftrth.UUID_FORM = msf.UUID_FORM
			LEFT JOIN MS_PRIORITY msp with (nolock) on ftrth.UUID_PRIORITY = msp.UUID_PRIORITY
			left join MS_STATUSTASK msst with (nolock) on ftrth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			left join AM_MSUSER ammsu with (nolock) on ammsu.UUID_MS_USER = ftrth.UUID_MS_USER
			left join AM_MSUSER ammsuspv with (nolock) on ammsu.SPV_ID = ammsuspv.UUID_MS_USER
			left join FINAL_TR_TASKLINK ftrlink with (nolock) on ftrth.UUID_TASK_H = ftrlink.UUID_TASK_H_COLLECT
		WHERE ftrth.UUID_MS_USER like lower ('%' + :uuidUser + '%') 
			and ftrth.UUID_FORM = :uuidForm
			AND ammsu.UUID_BRANCH like lower('%'+ :uuidBranch +'%') 
			and ammsu.SPV_ID like lower('%'+ :uuidSpv +'%')
			AND msst.STATUS_CODE not in ('A')
			AND assign_date between :startDate and :endDate
	</sql-query>
	
	<sql-query name="report.download.getquestionlist">
		<query-param name="uuidForm" type="long"/>
		SELECT q.UUID_QUESTION, q.QUESTION_LABEL, q.REF_ID
		FROM MS_FORM frm with (nolock) 
			join MS_QUESTIONGROUPOFFORM qgofrm with (nolock) on frm.UUID_FORM = qgofrm.UUID_FORM
			join MS_QUESTIONGROUP qg with (nolock) on qgofrm.UUID_QUESTION_GROUP = qg.UUID_QUESTION_GROUP
			join MS_QUESTIONOFGROUP qog with (nolock) on qog.UUID_QUESTION_GROUP = qg.UUID_QUESTION_GROUP
			join MS_QUESTION q with (nolock) on qog.UUID_QUESTION = q.UUID_QUESTION
		WHERE frm.UUID_FORM = :uuidForm
		ORDER BY qgofrm.LINE_SEQ_ORDER, qog.SEQ_ORDER
	</sql-query>
	
	<sql-query name="report.download.getquestionlistByFormVersion">
		<query-param name="uuidVersion" type="string"/>
		SELECT q.UUID_QUESTION, q.QUESTION_LABEL, q.REF_ID
		from MS_FORMQUESTIONSET q
		WHERE q.IS_VISIBLE = 1 and q.UUID_FORM_HISTORY = :uuidVersion
		ORDER BY q.question_group_of_form_seq, q.question_of_group_seq
	</sql-query>
	
	<sql-query name="report.download.getquestionlistByFormVersionOts">
		<query-param name="uuidVersion" type="string"/>
		SELECT   q.UUID_QUESTION, q.QUESTION_LABEL, q.REF_ID, MSA.CODE_ANSWER_TYPE
		FROM     MS_FORMQUESTIONSET q WITH (NOLOCK)
		JOIN     MS_ANSWERTYPE MSA WITH (NOLOCK) ON MSA.UUID_ANSWER_TYPE = q.UUID_ANSWER_TYPE
		WHERE    q.IS_VISIBLE = 1
		         and q.UUID_FORM_HISTORY = :uuidVersion
		ORDER BY q.question_group_of_form_seq, q.question_of_group_seq
	</sql-query>
	
	<sql-query name="report.download.getdetailanswer">
		<query-param name="uuidTaskH" type="string"/>
		<query-param name="uuidQuestion" type="string"/>
		WITH N AS (
		    SELECT trtd.UUID_TASK_H, trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, trtd.LOV_ID,
				trtd.OPTION_TEXT, trtd.IMAGE_PATH, trtd.LATITUDE, trtd.LONGITUDE, trtd.ACCURACY, 
				null as LOB_FILE, '' as UUID_TASK_DETAIL_LOB, trtd.INT_TEXT_ANSWER, trtd.INT_OPTION_TEXT, 
				trtd.INT_LOV_ID, msst.STATUS_CODE
			FROM TR_TASK_D trtd with (nolock) 
				INNER JOIN TR_TASK_H trth with (nolock) ON trtd.UUID_TASK_H = trth.UUID_TASK_H
				INNER JOIN MS_QUESTION msq with (nolock) ON trtd.UUID_QUESTION = msq.UUID_QUESTION
				INNER JOIN MS_STATUSTASK msst with (nolock) ON trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			WHERE trtd.UUID_TASK_H = :uuidTaskH AND msq.UUID_QUESTION = :uuidQuestion

		    UNION ALL

		    SELECT trtdl.UUID_TASK_H, trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.TEXT_ANSWER, 
		    	trtdl.LOV_ID, trtdl.OPTION_TEXT, trtdl.IMAGE_PATH, trtdl.LATITUDE, trtdl.LONGITUDE, 
		    	trtdl.ACCURACY, '1' LOB_FILE, trtdl.UUID_TASK_DETAIL_LOB, trtdl.INT_TEXT_ANSWER, 
		    	trtdl.INT_OPTION_TEXT, trtdl.INT_LOV_ID, msst.STATUS_CODE
			FROM TR_TASKDETAILLOB trtdl with (nolock) 
				INNER JOIN TR_TASK_H trth with (nolock) ON trtdl.UUID_TASK_H = trth.UUID_TASK_H
				INNER JOIN MS_QUESTION msq with (nolock) ON trtdl.QUESTION_ID = msq.UUID_QUESTION
				INNER JOIN MS_STATUSTASK msst with (nolock) ON trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			WHERE trtdl.UUID_TASK_H = :uuidTaskH AND msq.UUID_QUESTION = :uuidQuestion
		)
		SELECT ISNULL(N.TEXT_ANSWER, '') TEXT_ANSWER, ISNULL(N.LOV_ID, '') LOV_ID, 
			ISNULL(N.OPTION_TEXT, '') OPTION_TEXT,
			CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.LOB_FILE IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.LOB_FILE IS NULL) THEN '0'
				WHEN (msan.code_answer_type IN ('024', '030')) THEN '01'
				ELSE '10'
			END AS HAS_IMAGE, msan.code_answer_type, N.LATITUDE, N.LONGITUDE, N.ACCURACY,
			CASE
				WHEN (N.STATUS_CODE IN ('C', 'D') ) THEN '1' ELSE '0'
			END AS IS_INT_ANSWER, ISNULL(N.INT_TEXT_ANSWER, '') INT_TEXT_ANSWER, 
			ISNULL(N.INT_LOV_ID, '') INT_LOV_ID, ISNULL(N.INT_OPTION_TEXT, '') INT_OPTION_TEXT,
			msct.TAG_NAME as TAG, 
			case 
				WHEN msct.TAG_NAME = 'RV NUMBER' THEN (
					select RV_NUMBER 
					FROM TR_TASK_H trth 
					WHERE trth.UUID_TASK_H = N.UUID_TASK_H
				) 
				ELSE NULL 
			END AS RV
		FROM N 
			INNER JOIN MS_QUESTION msq with (nolock) ON msq.UUID_QUESTION = N.UUID_QUESTION
			INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
			INNER JOIN MS_QUESTIONOFGROUP msqg with (nolock) ON msqg.UUID_QUESTION = msq.UUID_QUESTION
			INNER JOIN MS_QUESTIONGROUP msg with (nolock) ON msg.UUID_QUESTION_GROUP = msqg.UUID_QUESTION_GROUP
			INNER JOIN MS_QUESTIONGROUPOFFORM msqof with (nolock) 
				ON msqof.UUID_QUESTION_GROUP = msqg.UUID_QUESTION_GROUP
			LEFT JOIN MS_COLLECTIONTAG msct with (nolock) ON msct.UUID_COLLECTION_TAG = msq.UUID_COLLECTION_TAG
	</sql-query>
	
	<sql-query name="report.download.getdetailanswerfinal">
		<query-param name="uuidTaskH" type="string"/>
		<query-param name="uuidQuestion" type="string"/>
		WITH N AS (
		    SELECT trtd.UUID_TASK_H, trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, trtd.LOV_ID,
				trtd.OPTION_TEXT, trtd.IMAGE_PATH, trtd.LATITUDE, trtd.LONGITUDE, trtd.ACCURACY, 
				null as LOB_FILE, '' as UUID_TASK_DETAIL_LOB, trtd.INT_TEXT_ANSWER, trtd.INT_OPTION_TEXT, 
				trtd.INT_LOV_ID, msst.STATUS_CODE
			FROM FINAL_TR_TASK_D trtd with (nolock) 
				INNER JOIN FINAL_TR_TASK_H trth with (nolock) ON trtd.UUID_TASK_H = trth.UUID_TASK_H
				INNER JOIN MS_QUESTION msq with (nolock) ON trtd.UUID_QUESTION = msq.UUID_QUESTION
				INNER JOIN MS_STATUSTASK msst with (nolock) ON trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			WHERE trtd.UUID_TASK_H = :uuidTaskH AND msq.UUID_QUESTION = :uuidQuestion

		    UNION ALL

		    SELECT trtdl.UUID_TASK_H, trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.TEXT_ANSWER, 
		    	trtdl.LOV_ID, trtdl.OPTION_TEXT, trtdl.IMAGE_PATH, trtdl.LATITUDE, trtdl.LONGITUDE, 
		    	trtdl.ACCURACY, '1' LOB_FILE, trtdl.UUID_TASK_DETAIL_LOB, trtdl.INT_TEXT_ANSWER, 
		    	trtdl.INT_OPTION_TEXT, trtdl.INT_LOV_ID, msst.STATUS_CODE
			FROM FINAL_TR_TASKDETAILLOB trtdl with (nolock) 
				INNER JOIN FINAL_TR_TASK_H trth with (nolock) ON trtdl.UUID_TASK_H = trth.UUID_TASK_H
				INNER JOIN MS_QUESTION msq with (nolock) ON trtdl.QUESTION_ID = msq.UUID_QUESTION
				INNER JOIN MS_STATUSTASK msst with (nolock) ON trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
			WHERE trtdl.UUID_TASK_H = :uuidTaskH AND msq.UUID_QUESTION = :uuidQuestion
		)
		SELECT ISNULL(N.TEXT_ANSWER, '') TEXT_ANSWER, ISNULL(N.LOV_ID, '') LOV_ID, 
			ISNULL(N.OPTION_TEXT, '') OPTION_TEXT,
			CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.LOB_FILE IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.LOB_FILE IS NULL) THEN '0'
				WHEN (msan.code_answer_type IN ('024', '030')) THEN '01'
				ELSE '10'
			END AS HAS_IMAGE, msan.code_answer_type, N.LATITUDE, N.LONGITUDE, N.ACCURACY,
			CASE
				WHEN (N.STATUS_CODE IN ('C', 'D') ) THEN '1' ELSE '0'
			END AS IS_INT_ANSWER, ISNULL(N.INT_TEXT_ANSWER, '') INT_TEXT_ANSWER, 
			ISNULL(N.INT_LOV_ID, '') INT_LOV_ID, ISNULL(N.INT_OPTION_TEXT, '') INT_OPTION_TEXT,
			msct.TAG_NAME as TAG, 
			case 
				WHEN msct.TAG_NAME = 'RV NUMBER' THEN (
					select RV_NUMBER 
					FROM TR_TASK_H trth 
					WHERE trth.UUID_TASK_H = N.UUID_TASK_H
				)
				ELSE NULL 
			END AS RV	
		FROM N 
			INNER JOIN MS_QUESTION msq with (nolock) ON msq.UUID_QUESTION = N.UUID_QUESTION
			INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
			INNER JOIN MS_QUESTIONOFGROUP msqg with (nolock) ON msqg.UUID_QUESTION = msq.UUID_QUESTION
			INNER JOIN MS_QUESTIONGROUP msg with (nolock) ON msg.UUID_QUESTION_GROUP = msqg.UUID_QUESTION_GROUP
			INNER JOIN MS_QUESTIONGROUPOFFORM msqof with (nolock) 
				ON msqof.UUID_QUESTION_GROUP = msqg.UUID_QUESTION_GROUP
			LEFT JOIN MS_COLLECTIONTAG msct with (nolock) ON msct.UUID_COLLECTION_TAG = msq.UUID_COLLECTION_TAG
	</sql-query>
	
	<sql-query name="report.download.getUsersAllByBranch">
		<query-param name="uuidBranchLogin" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="jobCode" type="string" />
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, 
				msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, 
				msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  
				FROM (
					select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
					from N 
						join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
						join (
							SELECT keyvalue as uuid_branch 
							from dbo.getCabangByLogin(:uuidBranchLogin)
						) cbg on cbg.uuid_branch = n.uuid_branch 
					where  J.JOB_CODE = :jobCode 
						and N.UUID_BRANCH like lower('%'+ :uuidBranch +'%')
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="report.download.getUsersAllByBranchCount">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="jobCode" type="string" />
	   	<query-param name="uuidBranchLogin" type="string" />
		WITH N AS (
			SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,
				CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, 
				msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, N.HIRARKI, 
				N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, 
				msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		SELECT count(1) 
		FROM (
			select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
			from N 
				join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
				join (
					SELECT keyvalue as uuid_branch 
					from dbo.getCabangByLogin(:uuidBranchLogin)
				) cbg on cbg.uuid_branch = n.uuid_branch 
			where  J.JOB_CODE = :jobCode 
				and N.UUID_BRANCH like lower('%'+ :uuidBranch +'%')
		) c
	</sql-query>
	
	<sql-query name="report.download.getUsersAllBySpv">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystemCode" type="string" />
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_SUBSYSTEM, msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, 
				msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 
				0 as LEVEL, msu.LOGIN_ID, msu.UUID_JOB
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1' 
				and msu.UUID_BRANCH like lower('%'+ :uuidBranch +'%') 
				and ISNULL(cast(msu.SPV_ID as varchar(max)), '%') like lower('%'+ :uuidSpv +'%')
			UNION ALL
			SELECT msu2.UUID_MS_SUBSYSTEM, msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, 
				N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, 
				msu2.LOGIN_ID, msu2.UUID_JOB
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID 
				AND msu2.IS_ACTIVE = '1'
		)
		select * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  
				FROM (
					select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
					from N
						join AM_MSSUBSYSTEM subs on N.UUID_MS_SUBSYSTEM = subs.UUID_MS_SUBSYSTEM
						join MS_JOB job on N.uuid_job = job.UUID_JOB
					where subs.SUBSYSTEM_NAME = :subsystemCode
						and job.IS_FIELD_PERSON = '1'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="report.download.getUsersAllBySpvCount">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystemCode" type="string" />
		WITH N AS (
			SELECT msu.UUID_MS_SUBSYSTEM, msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, 
				msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 
				0 as LEVEL, msu.LOGIN_ID, msu.UUID_JOB
			FROM AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1' 
				and msu.UUID_BRANCH like lower('%'+ :uuidBranch +'%') 
				and ISNULL(cast(msu.SPV_ID as varchar(max)), '%') like lower('%'+ :uuidSpv +'%')
			UNION ALL
			SELECT msu2.UUID_MS_SUBSYSTEM, msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, 
				N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), 
				N.LEVEL+1, msu2.LOGIN_ID, msu2.UUID_JOB
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID 
				AND msu2.IS_ACTIVE = '1'
		)
		SELECT Count(1) 
		FROM (
			select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
			from N
				join AM_MSSUBSYSTEM subs on N.UUID_MS_SUBSYSTEM = subs.UUID_MS_SUBSYSTEM
				join MS_JOB job on N.uuid_job = job.UUID_JOB
			where subs.SUBSYSTEM_NAME = :subsystemCode
				and job.IS_FIELD_PERSON = '1'
		) c
	</sql-query>
	
	<sql-query name="report.download.getTaskAnswers">
		<query-param name="uuidTaskH" type="string"/>		
		SELECT  UUID_QUESTION, QUESTION_TEXT, TEXT_ANSWER, LOV_ID, OPTION_TEXT, LATITUDE, 
			LONGITUDE, ACCURACY, HAS_IMAGE, INT_TEXT_ANSWER, INT_OPTION_TEXT, INT_LOV_ID, code_answer_type
	  	FROM (
			(SELECT trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, trtd.LOV_ID,
				trtd.OPTION_TEXT, trtd.LATITUDE, trtd.LONGITUDE, trtd.ACCURACY, '10' as HAS_IMAGE,
				trtd.INT_TEXT_ANSWER, trtd.INT_OPTION_TEXT, trtd.INT_LOV_ID, msan.code_answer_type
			FROM TR_TASK_D trtd with (nolock)
				INNER JOIN MS_QUESTION msq with (nolock) ON trtd.UUID_QUESTION = msq.UUID_QUESTION
				INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
			WHERE trtd.UUID_TASK_H = :uuidTaskH
			UNION ALL
			SELECT trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.TEXT_ANSWER, trtdl.LOV_ID,
				trtdl.OPTION_TEXT, trtdl.LATITUDE, trtdl.LONGITUDE, trtdl.ACCURACY,
				CASE
					WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NOT NULL) THEN '1'
					WHEN (msan.code_answer_type IN ('016','017','018','021') AND IMAGE_PATH IS NOT NULL) THEN '1'
					WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NULL) THEN '0'
					WHEN (msan.code_answer_type IN ('024', '030')) THEN '01'
					ELSE '10'
				END AS HAS_IMAGE, 
				trtdl.INT_TEXT_ANSWER, trtdl.INT_OPTION_TEXT, trtdl.INT_LOV_ID, msan.code_answer_type
			FROM TR_TASKDETAILLOB trtdl with (nolock)
				INNER JOIN MS_QUESTION msq with (nolock) ON trtdl.QUESTION_ID = msq.UUID_QUESTION
				INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
			WHERE trtdl.UUID_TASK_H = :uuidTaskH)
			
			UNION ALL
			
			(SELECT trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, trtd.LOV_ID,
				trtd.OPTION_TEXT, trtd.LATITUDE, trtd.LONGITUDE, trtd.ACCURACY, '10' as HAS_IMAGE,
				trtd.INT_TEXT_ANSWER, trtd.INT_OPTION_TEXT, trtd.INT_LOV_ID, msan.code_answer_type
			FROM FINAL_TR_TASK_D trtd with (nolock)
				INNER JOIN MS_QUESTION msq with (nolock) ON trtd.UUID_QUESTION = msq.UUID_QUESTION
				INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
			WHERE trtd.UUID_TASK_H = :uuidTaskH
			UNION ALL
			SELECT trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.TEXT_ANSWER, trtdl.LOV_ID,
				trtdl.OPTION_TEXT, trtdl.LATITUDE, trtdl.LONGITUDE, trtdl.ACCURACY,
				CASE
					WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NOT NULL) THEN '1'
					WHEN (msan.code_answer_type IN ('016','017','018','021') AND IMAGE_PATH IS NOT NULL) THEN '1'
					WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NULL) THEN '0'
					WHEN (msan.code_answer_type IN ('024', '030')) THEN '01'
					ELSE '10'
				END AS HAS_IMAGE,
				trtdl.INT_TEXT_ANSWER, trtdl.INT_OPTION_TEXT, trtdl.INT_LOV_ID, msan.code_answer_type
			FROM FINAL_TR_TASKDETAILLOB trtdl with (nolock)
				INNER JOIN MS_QUESTION msq with (nolock) ON trtdl.QUESTION_ID = msq.UUID_QUESTION
				INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
			WHERE trtdl.UUID_TASK_H = :uuidTaskH)
		) rd
	</sql-query>
	
	<sql-query name="report.download.getTaskAnswersCSV">
		<query-param name="uuidTaskH" type="string"/>
		<query-param name="refid" type="string"/>
		with n as (
			SELECT  UUID_QUESTION, QUESTION_TEXT, TEXT_ANSWER, LOV_ID, OPTION_TEXT, LATITUDE, LONGITUDE, 
				ACCURACY, HAS_IMAGE, INT_TEXT_ANSWER, INT_OPTION_TEXT, INT_LOV_ID, code_answer_type, 
				flag, UUID_TASK_H, UUID_LOB, UUID_FORM_HISTORY, RV_NUMBER
			FROM (
				(SELECT trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, trtd.LOV_ID,
					trtd.OPTION_TEXT, trtd.LATITUDE, trtd.LONGITUDE, trtd.ACCURACY, '10' as HAS_IMAGE,
					trtd.INT_TEXT_ANSWER, trtd.INT_OPTION_TEXT, trtd.INT_LOV_ID, msan.code_answer_type, 
					'1' as flag, trtd.UUID_TASK_H, NULL as UUID_LOB, fh.UUID_FORM_HISTORY, trth.RV_NUMBER
				FROM TR_TASK_D trtd with (nolock)
					INNER JOIN TR_TASK_H trth with (nolock) ON trtd.UUID_TASK_H = trth.UUID_TASK_H
					INNER JOIN MS_FORMHISTORY fh ON fh.UUID_FORM=trth.UUID_FORM and fh.FORM_VERSION=trth.FORM_VERSION
					INNER JOIN MS_QUESTION msq with (nolock) ON trtd.UUID_QUESTION = msq.UUID_QUESTION
					INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
				WHERE trtd.UUID_TASK_H = :uuidTaskH
				UNION ALL
				SELECT trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.TEXT_ANSWER, trtdl.LOV_ID,
					trtdl.OPTION_TEXT, trtdl.LATITUDE, trtdl.LONGITUDE, trtdl.ACCURACY,
					CASE
						WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','035') AND LOB_FILE IS NOT NULL) THEN '1'
						WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','035') AND IMAGE_PATH IS NOT NULL) THEN '1'
						WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','035') AND LOB_FILE IS NULL) THEN '0'
						WHEN (msan.code_answer_type IN ('024', '030')) THEN '01'
						ELSE '10'
					END AS HAS_IMAGE,
					trtdl.INT_TEXT_ANSWER, trtdl.INT_OPTION_TEXT, trtdl.INT_LOV_ID, msan.code_answer_type, 
					'1' as flag, trtdl.UUID_TASK_H, trtdl.UUID_TASK_DETAIL_LOB as UUID_LOB, fh.UUID_FORM_HISTORY, trth.RV_NUMBER
				FROM TR_TASKDETAILLOB trtdl with (nolock)
					INNER JOIN TR_TASK_H trth with (nolock) ON trtdl.UUID_TASK_H = trth.UUID_TASK_H
					INNER JOIN MS_FORMHISTORY fh ON fh.UUID_FORM=trth.UUID_FORM and fh.FORM_VERSION=trth.FORM_VERSION
					INNER JOIN MS_QUESTION msq with (nolock) ON trtdl.QUESTION_ID = msq.UUID_QUESTION
					INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
				WHERE trtdl.UUID_TASK_H = :uuidTaskH)
					
				UNION ALL

				(SELECT trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, trtd.LOV_ID,
					trtd.OPTION_TEXT, trtd.LATITUDE, trtd.LONGITUDE, trtd.ACCURACY, '10' as HAS_IMAGE,
					trtd.INT_TEXT_ANSWER, trtd.INT_OPTION_TEXT, trtd.INT_LOV_ID, msan.code_answer_type, 
					'2' as flag, trtd.UUID_TASK_H, NULL as UUID_LOB, fh.UUID_FORM_HISTORY, trth.RV_NUMBER
				FROM FINAL_TR_TASK_D trtd with (nolock)
					INNER JOIN FINAL_TR_TASK_H trth with (nolock) ON trtd.UUID_TASK_H = trth.UUID_TASK_H
					INNER JOIN MS_FORMHISTORY fh ON fh.UUID_FORM=trth.UUID_FORM and fh.FORM_VERSION=trth.FORM_VERSION
					INNER JOIN MS_QUESTION msq with (nolock) ON trtd.UUID_QUESTION = msq.UUID_QUESTION
					INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
				WHERE trtd.UUID_TASK_H = :uuidTaskH
				UNION ALL
				SELECT trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.TEXT_ANSWER, trtdl.LOV_ID,
					trtdl.OPTION_TEXT, trtdl.LATITUDE, trtdl.LONGITUDE, trtdl.ACCURACY,
					CASE
						WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','035') AND LOB_FILE IS NOT NULL) THEN '1'
						WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','035') AND IMAGE_PATH IS NOT NULL) THEN '1'
						WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','035') AND LOB_FILE IS NULL) THEN '0'
						WHEN (msan.code_answer_type IN ('024', '030')) THEN '01'
						ELSE '10'
					END AS HAS_IMAGE,
					trtdl.INT_TEXT_ANSWER, trtdl.INT_OPTION_TEXT, trtdl.INT_LOV_ID, msan.code_answer_type, 
					'2' as flag, trtdl.UUID_TASK_H, trtdl.UUID_TASK_DETAIL_LOB as UUID_LOB, fh.UUID_FORM_HISTORY, trth.RV_NUMBER
				FROM FINAL_TR_TASKDETAILLOB trtdl with (nolock)
					INNER JOIN FINAL_TR_TASK_H trth with (nolock) ON trtdl.UUID_TASK_H = trth.UUID_TASK_H
					INNER JOIN MS_FORMHISTORY fh ON fh.UUID_FORM=trth.UUID_FORM and fh.FORM_VERSION=trth.FORM_VERSION
					INNER JOIN MS_QUESTION msq with (nolock) ON trtdl.QUESTION_ID = msq.UUID_QUESTION
					INNER JOIN MS_ANSWERTYPE msan with (nolock) ON msq.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
				WHERE trtdl.UUID_TASK_H = :uuidTaskH)
			) rd
		)

		SELECT	A.UUID_QUESTION, A.QUESTION_LABEL, 
				CASE WHEN IS_MASKING = 1 THEN ISNULL(REPLICATE('*', LEN(A.TEXT_ANSWER)), '*') ELSE A.TEXT_ANSWER END TEXT_ANSWER,
				CASE WHEN IS_MASKING = 1 THEN ISNULL(REPLICATE('*', LEN(CAST(A.LOV_ID AS VARCHAR(25)))), '*') ELSE CAST(A.LOV_ID AS VARCHAR(25)) END LOV_ID,
				CASE WHEN IS_MASKING = 1 THEN ISNULL(REPLICATE('*', LEN(A.OPTION_TEXT)), '*') ELSE A.OPTION_TEXT END OPTION_TEXT, A.LATITUDE, A.LONGITUDE, A.ACCURACY, A.HAS_IMAGE,
				CASE WHEN IS_MASKING = 1 THEN ISNULL(REPLICATE('*', LEN(A.INT_TEXT_ANSWER)), '*') ELSE A.INT_TEXT_ANSWER END INT_TEXT_ANSWER,
				CASE WHEN IS_MASKING = 1 THEN ISNULL(REPLICATE('*', LEN(A.INT_OPTION_TEXT)), '*') ELSE A.INT_OPTION_TEXT END INT_OPTION_TEXT,
				CASE WHEN IS_MASKING = 1 THEN ISNULL(REPLICATE('*', LEN(CAST(A.INT_LOV_ID AS VARCHAR(25)))), '*') ELSE CAST(A.INT_LOV_ID AS VARCHAR(25)) END INT_LOV_ID,
				A.code_answer_type, A.TAG, A.RV, A.flag, A.UUID_LOB, A.UUID_FORM_HISTORY
		FROM (
			SELECT q.UUID_QUESTION, q.QUESTION_LABEL, 
				CASE 
					WHEN q.REF_ID IN (:refid)
					THEN 1
					ELSE 0
				END IS_MASKING, 
				n.TEXT_ANSWER, n.LOV_ID ,  n.OPTION_TEXT,   n.LATITUDE, n.LONGITUDE, n.ACCURACY, n.HAS_IMAGE, 
				n.INT_TEXT_ANSWER,   n.INT_OPTION_TEXT,  n.INT_LOV_ID,  n.code_answer_type, msct.TAG_NAME as TAG,
				CASE
					WHEN msct.TAG_NAME = 'RV NUMBER' THEN n.RV_NUMBER
					ELSE NULL 
				END AS RV, flag, n.UUID_LOB, n.UUID_FORM_HISTORY, q.question_group_of_form_seq, question_of_group_seq
			FROM MS_FORMQUESTIONSET q
				join MS_ANSWERTYPE ans on q.UUID_ANSWER_TYPE = ans.UUID_ANSWER_TYPE
				left join n on q.UUID_QUESTION = n.uuid_question
				left join ms_collectiontag msct ON msct.uuid_collection_tag = q.uuid_collection_tag
			WHERE q.UUID_FORM_HISTORY = n.UUID_FORM_HISTORY and q.IS_VISIBLE = '1'
		) A
		ORDER BY A.question_group_of_form_seq, A.question_of_group_seq
	</sql-query>
	
	<sql-query name="report.download.getUsersAllByBranchHierarchy">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystemCode" type="string" />
	   	<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
			SELECT msu.UUID_MS_SUBSYSTEM, msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, 
				msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 
				0 as LEVEL, msu.LOGIN_ID, msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1' 
				and ISNULL(cast(msu.SPV_ID as varchar(max)), '%') like lower('%'+ :uuidSpv +'%')
			UNION ALL
			SELECT msu2.UUID_MS_SUBSYSTEM, msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, 
				N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, 
				msu2.LOGIN_ID, msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID 
				AND msu2.IS_ACTIVE = '1'
		)
		select * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  
				FROM (
					select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
					from N
						join AM_MSSUBSYSTEM subs on N.UUID_MS_SUBSYSTEM = subs.UUID_MS_SUBSYSTEM
						join MS_JOB job on N.uuid_job = job.UUID_JOB
						join (
							SELECT keyvalue as uuid_branch 
							from dbo.getCabangByLogin(:uuidBranch)
						) cbg on cbg.uuid_branch = n.uuid_branch 
					where subs.SUBSYSTEM_NAME = :subsystemCode
						and job.IS_FIELD_PERSON = '1'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="report.download.getUsersAllByBranchHierarchyCount">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="subsystemCode" type="string" />
		WITH N AS (
			SELECT msu.UUID_MS_SUBSYSTEM, msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, 
				msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 
				0 as LEVEL, msu.LOGIN_ID, msu.UUID_JOB, msu.UUID_BRANCH
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1' 
				and ISNULL(cast(msu.SPV_ID as varchar(max)), '%') like lower('%'+ :uuidSpv +'%')
			UNION ALL
			SELECT msu2.UUID_MS_SUBSYSTEM, msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, 
				N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, 
				msu2.LOGIN_ID, msu2.UUID_JOB, msu2.UUID_BRANCH
			FROM AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID 
				AND msu2.IS_ACTIVE = '1'
		)
		SELECT Count(1)
		FROM (
			select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
			from N
				join AM_MSSUBSYSTEM subs on N.UUID_MS_SUBSYSTEM = subs.UUID_MS_SUBSYSTEM
				join MS_JOB job on N.uuid_job = job.UUID_JOB
				join (
					SELECT keyvalue as uuid_branch 
					from dbo.getCabangByLogin(:uuidBranch)
				) cbg on cbg.uuid_branch = n.uuid_branch 
			where subs.SUBSYSTEM_NAME = :subsystemCode
				and job.IS_FIELD_PERSON = '1'
		) c
	</sql-query>
</hibernate-mapping>