<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="report.result.getList">
		<query-param name="uuidUser" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * 
		FROM (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT rrl.SEQNO, usr.LOGIN_ID, usr.FULL_NAME,
					LEFT(CONVERT(VARCHAR, rrl.DTM_REQUEST, 113),20) as REQUEST_TIME, rrl.RPT_NAME, rrl.RPT_TYPE,
					COALESCE (LEFT(CONVERT(VARCHAR, rrl.PROCESS_START_TIME, 113),20), '-') as PROCESS_START_TIME,
					COALESCE (LEFT(CONVERT(VARCHAR, rrl.PROCESS_FINISH_TIME, 113),20), '-') as PROCESS_FINISH_TIME,
					COALESCE (rrl.PROCESS_DURATION_SECONDS, '-') as PROCESS_DURATION_SECONDS,
					(Case when rrl.REPORT_FILE_LOCATION is not null or rrl.REPORT_FILE_LOCATION != ''
						then '1' else '0' end) hasLocation, rrl.PROCESS_STATUS,
					ROW_NUMBER() OVER (ORDER BY rrl.SEQNO DESC) AS rownum
				FROM TR_REPORTRESULTLOG rrl with (nolock)
					LEFT JOIN AM_MSUSER usr with (nolock) on rrl.UUID_MS_USER = usr.UUID_MS_USER
				WHERE rrl.UUID_MS_USER = :uuidUser
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="report.result.getListCount">
		<query-param name="uuidUser" type="string"/>
		SELECT Count(*)
		FROM TR_REPORTRESULTLOG rrl with (nolock)
			LEFT JOIN AM_MSUSER usr with (nolock) on rrl.UUID_MS_USER = usr.UUID_MS_USER
		WHERE rrl.UUID_MS_USER = :uuidUser
	</sql-query>
</hibernate-mapping>