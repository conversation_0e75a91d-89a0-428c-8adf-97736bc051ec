<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="report.sla.getSlaSummary">
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="branchId" type="string"/>
		<query-param name="subsystemId" type="string"/>
		<query-param name="uuidForm" type="string"/>
		WITH N as (
			select * 
			from TR_TASK_H ttrh with(nolock)
			where ttrh.ASSIGN_DATE between :startDate and :endDate
			UNION ALL
			select * 
			from FINAL_TR_TASK_H fttrh with(nolock)
			where fttrh.ASSIGN_DATE between :startDate and :endDate
		)
		select a.branch_code, a.branch_name, isnull(a.totalSubmitTask,0) totalSubmitTask, 
			isnull(a.hour_dr,0) hour_dr, isnull(a.min_dr,0) min_dr, isnull(a.hour_rp,0) hour_rp, 
			isnull(a.min_rp,0) min_rp, isnull(a.hour_ps,0) hour_ps, isnull(a.min_ps,0) min_ps, 
			isnull(a.hour_s,0) hour_s, isnull(a.min_s,0) min_s, isnull(b.task_bf_sla,0) task_bf_sla, 
			isnull(b.hour_bf_sla,0) hour_bf_sla, isnull(b.min_bf_sla,0) min_bf_sla, 
			isnull(c.task_ov_sla,0) task_ov_sla, isnull(c.hour_ov_sla,0) hour_ov_sla, 
			isnull(c.min_ov_sla,0) min_ov_sla, a.uuid_branch, isnull(a.hour_ad,0) hour_ad, 
			isnull(a.min_ad,0) min_ad
		from (
			select msb.branch_code, msb.branch_name, count(*) totalSubmitTask,
				COALESCE(AVG(DATEDIFF(minute, assign_date, download_date))/60,0) as hour_ad,
				COALESCE(AVG(DATEDIFF(minute, assign_date, download_date))%60,0) as min_ad,
				COALESCE(AVG(DATEDIFF(minute, download_date, read_date))/60,0) as hour_dr,
				COALESCE(AVG(DATEDIFF(minute, download_date, read_date))%60,0) as min_dr,
				COALESCE(AVG(DATEDIFF(minute, read_date, start_dtm))/60,0) as hour_rp,
				COALESCE(AVG(DATEDIFF(minute, read_date, start_dtm))%60,0) as min_rp,
				COALESCE(AVG(DATEDIFF(minute, start_dtm, submit_date))/60,0) as hour_ps,
				COALESCE(AVG(DATEDIFF(minute, start_dtm, submit_date))%60,0) as min_ps,
				COALESCE(AVG(DATEDIFF(minute, download_date, submit_date))/60,0) as hour_s,
				COALESCE(AVG(DATEDIFF(minute, download_date, submit_date))%60,0) as min_s, msb.uuid_branch
			from N with (nolock)
				join ms_statustask mst with (nolock) on N.uuid_status_task = mst.uuid_status_task
				join ms_branch msb with (nolock) on N.uuid_branch = msb.uuid_branch
				JOIN MS_FORM form with (nolock) ON N.UUID_FORM = form.UUID_FORM
				JOIN (SELECT keyValue as UUID_BRANCH FROM dbo.getCabangByLogin(:branchId)) cbl
				on cbl.UUID_BRANCH = N.UUID_BRANCH 
			where submit_date is not null
				and mst.status_code != 'U'
				and mst.uuid_ms_subsystem = :subsystemId
				and N.assign_date between :startDate and :endDate
				and form.UUID_FORM like :uuidForm
			group by branch_code, branch_name, msb.uuid_branch
		) a
		left outer join (
			select msb.branch_code, msb.branch_name, count(*) task_bf_sla,
				AVG(DATEDIFF(minute, assign_date, submit_date))/60 as hour_bf_sla,
				AVG(DATEDIFF(minute, assign_date, submit_date))%60 as min_bf_sla
			from N with (nolock)
				join ms_statustask mst with (nolock) on N.uuid_status_task = mst.uuid_status_task
				join ms_branch msb with (nolock) on N.uuid_branch = msb.uuid_branch
				JOIN MS_FORM form with (nolock) ON N.UUID_FORM = form.UUID_FORM
				JOIN (SELECT keyValue as UUID_BRANCH FROM dbo.getCabangByLogin(:branchId)) cbl
				on cbl.UUID_BRANCH = N.UUID_BRANCH
			where submit_date is not null
				and mst.status_code != 'U'
				and mst.uuid_ms_subsystem = :subsystemId
				and N.assign_date between :startDate and :endDate
				and DATEDIFF(minute, assign_date, submit_date)/60.0 between 0 and (
					select gs_value 
					from am_generalsetting 
					where gs_code = 'SLA_TIME'
				)
				and form.UUID_FORM like :uuidForm
			group by branch_code, branch_name
		) b on a.branch_code = b.branch_code
		left outer join (
			select msb.branch_code, msb.branch_name, count(*) task_ov_sla,
				AVG(DATEDIFF(minute, assign_date, submit_date))/60 as hour_ov_sla,
				AVG(DATEDIFF(minute, assign_date, submit_date))%60 as min_ov_sla
			from N with (nolock)
				join ms_statustask mst with (nolock) on N.uuid_status_task = mst.uuid_status_task
				join ms_branch msb with (nolock) on N.uuid_branch = msb.uuid_branch
				JOIN MS_FORM form with (nolock) ON N.UUID_FORM = form.UUID_FORM
				JOIN (SELECT keyValue as UUID_BRANCH FROM dbo.getCabangByLogin(:branchId)) cbl
				on cbl.UUID_BRANCH = N.UUID_BRANCH
			where submit_date is not null
				and mst.status_code != 'U'
				and mst.uuid_ms_subsystem = :subsystemId
				and N.assign_date between :startDate and :endDate
				and DATEDIFF(minute, assign_date, submit_date)/60.0 > (
					select gs_value 
					from am_generalsetting 
					where gs_code = 'SLA_TIME'
				)
				and form.UUID_FORM like :uuidForm
			group by branch_code, branch_name
		) c on b.branch_code = c.branch_code
	</sql-query>
	
	<sql-query name="report.sla.getSlaDetail">
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="branchId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="subsystemId" type="string"/>
		<query-param name="uuidForm" type="string"/>
		WITH N as (
			select * 
			from TR_TASK_H ttrh with(nolock)
			where ttrh.ASSIGN_DATE between :startDate and :endDate
			UNION ALL
			select * 
			from FINAL_TR_TASK_H fttrh with(nolock)
			where fttrh.ASSIGN_DATE between :startDate and :endDate
		)
		select 
			ROW_NUMBER() OVER(ORDER BY msb.branch_code ASC) rownum,
				branch_code,
				branch_name,
				full_name,
				isnull(appl_no,''),
				format(assign_date,'yyyy-MM-dd hh:mm:ss') as assign_date,
				format(PROMISE_DATE,'yyyy-MM-dd hh:mm:ss') as promise_date,
				format(start_dtm,'yyyy-MM-dd hh:mm:ss') as strt_dtm,
				format(SEND_DATE,'yyyy-MM-dd hh:mm:ss') as send_date,
				format(submit_date,'yyyy-MM-dd hh:mm:ss') as submit_date,
				COALESCE((DATEDIFF(minute, promise_date, start_dtm))/60,0) as hour_ptson,
				COALESCE((DATEDIFF(minute, promise_date, start_dtm))%60,0) as min_ptson,
				COALESCE((DATEDIFF(minute, start_dtm, submit_date))/60,0) as hour_ps,
				COALESCE((DATEDIFF(minute, start_dtm, submit_date))%60,0) as min_ps, 
				COALESCE((DATEDIFF(minute, promise_date, submit_date))/60,0) as hour_ptssubmit,
				COALESCE((DATEDIFF(minute, promise_date, submit_date))%60,0) as min_ptssubmit,
				form.FORM_NAME,
				CASE WHEN 
					COALESCE(DATEDIFF(minute, assign_date, submit_date)/60.0,0.0) > 
						(select gs_value from am_generalsetting where gs_code = 'SLA_TIME') THEN '1'
				ELSE '0' END AS COLOR,
				N.uuid_branch
		from N with (nolock)
			join ms_branch msb with (nolock) on N.uuid_branch = msb.uuid_branch
			join am_msuser msu with (nolock) on N.uuid_ms_user = msu.uuid_ms_user
			join ms_statustask mst with (nolock) on N.uuid_status_task = mst.uuid_status_task
			JOIN MS_FORM form with (nolock) ON N.UUID_FORM = form.UUID_FORM
			JOIN (SELECT keyValue as UUID_BRANCH FROM dbo.getCabangByLogin(:branchId)) cbl
				on cbl.UUID_BRANCH = N.UUID_BRANCH
		where N.uuid_ms_user like :userId
			and submit_date is not null
			and mst.status_code != 'U'
			and mst.uuid_ms_subsystem = :subsystemId
			and N.assign_date between :startDate and :endDate
			and form.UUID_FORM like :uuidForm				
	</sql-query>
	<sql-query name="report.sla.getSlaDetailByBranch">
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="branchId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="subsystemId" type="string"/>
		<query-param name="uuidForm" type="string"/>
		WITH N as (
			select * 
			from TR_TASK_H ttrh with(nolock)
			where ttrh.ASSIGN_DATE between :startDate and :endDate
			UNION ALL
			select * 
			from FINAL_TR_TASK_H fttrh with(nolock)
			where fttrh.ASSIGN_DATE between :startDate and :endDate
		)
		select 
			ROW_NUMBER() OVER(ORDER BY msb.branch_code ASC) rownum,
			branch_code,
			branch_name,
			full_name,
			isnull(appl_no,''),
			format(assign_date,'yyyy-MM-dd hh:mm:ss') as assign_date,
			format(PROMISE_DATE,'yyyy-MM-dd hh:mm:ss') as promise_date,
			format(start_dtm,'yyyy-MM-dd hh:mm:ss') as strt_dtm,
			format(SEND_DATE,'yyyy-MM-dd hh:mm:ss') as send_date,
			format(submit_date,'yyyy-MM-dd hh:mm:ss') as submit_date,
			COALESCE((DATEDIFF(minute, promise_date, start_dtm))/60,0) as hour_ptson,
			COALESCE((DATEDIFF(minute, promise_date, start_dtm))%60,0) as min_ptson,
			COALESCE((DATEDIFF(minute, start_dtm, submit_date))/60,0) as hour_ps,
			COALESCE((DATEDIFF(minute, start_dtm, submit_date))%60,0) as min_ps, 
			COALESCE((DATEDIFF(minute, promise_date, submit_date))/60,0) as hour_ptssubmit,
			COALESCE((DATEDIFF(minute, promise_date, submit_date))%60,0) as min_ptssubmit,
			form.FORM_NAME,
			CASE WHEN 
				COALESCE(DATEDIFF(minute, assign_date, submit_date)/60.0,0.0) > 
					(select gs_value from am_generalsetting where gs_code = 'SLA_TIME') THEN '1'
			ELSE '0' END AS COLOR,
			N.uuid_branch
		from N with (nolock)
			join ms_branch msb with (nolock) on N.uuid_branch = msb.uuid_branch
			join am_msuser msu with (nolock) on N.uuid_ms_user = msu.uuid_ms_user
			join ms_statustask mst with (nolock) on N.uuid_status_task = mst.uuid_status_task
			JOIN MS_FORM form with (nolock) ON N.UUID_FORM = form.UUID_FORM
		where
			N.uuid_branch like :branchId
			and N.uuid_ms_user like :userId
			and submit_date is not null
			and mst.status_code != 'U'
			and mst.uuid_ms_subsystem = :subsystemId
			and N.assign_date between :startDate and :endDate
			and form.UUID_FORM like :uuidForm
	</sql-query>
</hibernate-mapping>