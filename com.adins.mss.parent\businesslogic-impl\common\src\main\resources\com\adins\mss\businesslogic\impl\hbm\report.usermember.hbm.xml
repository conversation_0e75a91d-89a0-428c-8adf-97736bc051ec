<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="report.usermember.getListReportAll">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="uuidMsSubSystem" type="string"/>
		select src.* from (
			select msb.BRANCH_NAME branch,
				sum(case when mj.IS_FIELD_PERSON='0' and amu.dtm_crt BETWEEN :startDate and :endDate 
					then 1 else 0 end) web,
				sum(case when mj.IS_FIELD_PERSON='1' and amu.dtm_crt BETWEEN :startDate and :endDate 
					then 1 else 0 end) mobile,
				sum(case when amu.IS_ACTIVE ='1' and amu.IS_DELETED = '0' 
					and amu.dtm_crt BETWEEN :startDate and :endDate then 1 else 0 end) active,
				sum(case when amu.IS_ACTIVE ='0' and amu.IS_DELETED = '0' 
					and amu.dtm_crt BETWEEN :startDate and :endDate then 1 else 0 end) inactive,
				msb.UUID_BRANCH uuid
			from AM_MSUSER amu with (nolock) 
				join MS_JOB mj with (nolock) on amu.UUID_JOB = mj.UUID_JOB
				join (
					SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
					FROM dbo.getCabangByLogin(:branchId)
				) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
			where amu.UUID_MS_SUBSYSTEM like :uuidMsSubSystem
				and amu.IS_DELETED = '0'
			group by msb.BRANCH_NAME,msb.UUID_BRANCH
		) src 
		where src.web + src.mobile > 0
		order by src.branch asc
	</sql-query>
	<sql-query name="report.usermember.getListBranch">
		<query-param name="branchId" type="string"/>
		select keyValue uuid, branch_code + ' - ' + branch_name bcode 
		from dbo.getCabangByLogin(:branchId)
	</sql-query>
	<sql-query name="report.usermember.getListReportDetail">
		<query-param name="branchId" type="string"/>
		<query-param name="groupId" type="string"/>
		<query-param name="uuidMsSubSystem" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="searchName" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				select amu.FULL_NAME name, amu.UNIQUE_ID, msj.DESCRIPTION job, amg.GROUP_DESCRIPTION,
					isnull(LEFT(CONVERT(VARCHAR, amu.DTM_CRT , 106), 17),'-') dateCreate,
					amu.IS_ACTIVE active, amu.IMEI deviceId,
					amu.IS_LOCKED,
					isnull(LEFT(CONVERT(VARCHAR, amu.LAST_LOCKED, 106), 17),'-') lastLocked,
					isnull(LEFT(CONVERT(VARCHAR, amu.LAST_LOGGED_IN, 106), 17),'-') lastLoggedIn,
					msb.BRANCH_NAME,
					ROW_NUMBER() OVER ( order by amu.IS_ACTIVE DESC,msj.DESCRIPTION ASC,amu.FULL_NAME
					ASC,amg.GROUP_DESCRIPTION ASC, amu.DTM_CRT ASC) AS rownum
				from AM_MSUSER amu with (nolock) 
					left join MS_JOB msj with (nolock) on amu.UUID_JOB=msj.UUID_JOB
					left join AM_MEMBEROFGROUP ammog with (nolock) on ammog.UUID_MS_USER = amu.UUID_MS_USER
					left join AM_MSGROUP amg with (nolock) on ammog.UUID_MS_GROUP = amg.UUID_MS_GROUP
					left join MS_BRANCH msb with (nolock) on amu.UUID_BRANCH = msb.UUID_BRANCH
				where amg.UUID_MS_GROUP like :groupId
					and amu.UUID_BRANCH = :branchId
					and amu.FULL_NAME like '%'+:searchName+'%'
					and amu.UUID_MS_SUBSYSTEM like :uuidMsSubSystem
					and amu.DTM_CRT BETWEEN :startDate and :endDate
					and amu.IS_DELETED = '0'
			) a  <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="report.usermember.getListReportDetailCount">
		<query-param name="branchId" type="string"/>
		<query-param name="groupId" type="string"/>
		<query-param name="uuidMsSubSystem" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="searchName" type="string"/>
		select count(1) 
		from AM_MSUSER amus 
		where amus.UUID_MS_USER in (
			select amu.UUID_MS_USER
			from AM_MSUSER amu with (nolock) 
				left join MS_JOB msj with (nolock) on amu.UUID_JOB=msj.UUID_JOB
				left join AM_MEMBEROFGROUP ammog with (nolock) on ammog.UUID_MS_USER = amu.UUID_MS_USER
				left join AM_MSGROUP amg with (nolock) on ammog.UUID_MS_GROUP = amg.UUID_MS_GROUP
			where amg.UUID_MS_GROUP like :groupId
				and amu.UUID_BRANCH = :branchId
				and amu.FULL_NAME like '%'+:searchName+'%'
				and amu.UUID_MS_SUBSYSTEM like :uuidMsSubSystem
				and amu.DTM_CRT BETWEEN :startDate and :endDate
				and amu.IS_DELETED = '0'
		)
	</sql-query>
	<sql-query name="report.usermember.getListReportDetailAll">
		<query-param name="branchId" type="string"/>
		<query-param name="groupId" type="string"/>
		<query-param name="uuidMsSubSystem" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="searchName" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				select amu.FULL_NAME name, amu.UNIQUE_ID, msj.DESCRIPTION job, amg.GROUP_DESCRIPTION,
					isnull(LEFT(CONVERT(VARCHAR, amu.DTM_CRT, 106), 17),'-') dateCreate,
					amu.IS_ACTIVE active, amu.IMEI deviceId,
					amu.IS_LOCKED,
					isnull(LEFT(CONVERT(VARCHAR, amu.LAST_LOCKED, 106), 17),'-') lastLocked,
					isnull(LEFT(CONVERT(VARCHAR, amu.LAST_LOGGED_IN, 106), 17),'-') lastLoggedIn,
					msb.BRANCH_NAME,
					ROW_NUMBER() OVER ( 
						order by amu.IS_ACTIVE DESC,msj.DESCRIPTION ASC,
						amu.FULL_NAME ASC,amg.GROUP_DESCRIPTION ASC, amu.DTM_CRT ASC
					) AS rownum
				from AM_MSUSER amu with (nolock) 
					left join MS_JOB msj with (nolock) on amu.UUID_JOB=msj.UUID_JOB
					inner join (
						SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
						FROM dbo.getCabangByLogin(:branchId)
					) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
					left join AM_MEMBEROFGROUP ammog with (nolock) on ammog.UUID_MS_USER = amu.UUID_MS_USER
					left join AM_MSGROUP amg with (nolock) on ammog.UUID_MS_GROUP = amg.UUID_MS_GROUP
				where amg.UUID_MS_GROUP like :groupId
					and amu.FULL_NAME like '%'+:searchName+'%'
					and amu.UUID_MS_SUBSYSTEM like :uuidMsSubSystem 
					and amu.DTM_CRT BETWEEN :startDate and :endDate
					and amu.IS_DELETED = '0'
			) a  <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="report.usermember.getListReportDetailAllCount">
		<query-param name="branchId" type="string"/>
		<query-param name="groupId" type="string"/>
		<query-param name="uuidMsSubSystem" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="searchName" type="string"/>
		select count(1) 
		from AM_MSUSER amus 
		where amus.UUID_MS_USER in (
			select amu.UUID_MS_USER
			from AM_MSUSER amu with (nolock) 
				left join MS_JOB msj with (nolock) on amu.UUID_JOB=msj.UUID_JOB
				inner join (
					SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
					FROM dbo.getCabangByLogin(:branchId)
				) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
				left join AM_MEMBEROFGROUP ammog with (nolock) on ammog.UUID_MS_USER = amu.UUID_MS_USER
				left join AM_MSGROUP amg with (nolock) on ammog.UUID_MS_GROUP = amg.UUID_MS_GROUP
			where amg.UUID_MS_GROUP like :groupId
				and amu.FULL_NAME like '%'+:searchName+'%'
				and amu.UUID_MS_SUBSYSTEM like :uuidMsSubSystem
				and amu.DTM_CRT BETWEEN :startDate and :endDate
				and amu.IS_DELETED = '0'
		)
	</sql-query>
	<sql-query name="report.usermember.countListReportDetail">
		<query-param name="branchId" type="string"/>
		<query-param name="groupId" type="string"/>
		<query-param name="uuidMsSubSystem" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="searchName" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		select COUNT(*)
		from AM_MSUSER amu with (nolock) 
			left join MS_JOB msj with (nolock) on amu.UUID_JOB=msj.UUID_JOB
			left join AM_MEMBEROFGROUP ammog with (nolock) on ammog.UUID_MS_USER = amu.UUID_MS_USER
			left join AM_MSGROUP amg with (nolock) on ammog.UUID_MS_GROUP = amg.UUID_MS_GROUP
			left join MS_BRANCH msb with (nolock) on amu.UUID_BRANCH = msb.UUID_BRANCH
		where amg.UUID_MS_GROUP like :groupId
			and amu.UUID_BRANCH = :branchId
			and amu.FULL_NAME like '%'+:searchName+'%'
			and amu.UUID_MS_SUBSYSTEM like :uuidMsSubSystem
			and amu.DTM_CRT BETWEEN :startDate and :endDate
			and amu.IS_DELETED = '0'
	</sql-query>
	<sql-query name="report.usermember.countListReportDetailAll">
		<query-param name="groupId" type="string"/>
		<query-param name="searchName" type="string"/>
		<query-param name="branchId" type="string"/>
		<query-param name="uuidMsSubSystem" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		select COUNT(*)
		from AM_MSUSER amu with (nolock) 
			left join MS_JOB msj with (nolock) on amu.UUID_JOB=msj.UUID_JOB
			inner join (
				SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
				FROM dbo.getCabangByLogin(:branchId)
			) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
			left join AM_MEMBEROFGROUP ammog with (nolock) on ammog.UUID_MS_USER = amu.UUID_MS_USER
			left join AM_MSGROUP amg with (nolock) on ammog.UUID_MS_GROUP = amg.UUID_MS_GROUP
		where amg.UUID_MS_GROUP like :groupId
		  and amu.FULL_NAME like '%'+:searchName+'%'
		  and amu.UUID_MS_SUBSYSTEM like :uuidMsSubSystem
		  and amu.DTM_CRT BETWEEN :startDate and :endDate
		  and amu.IS_DELETED = '0'
	</sql-query>
	<sql-query name="report.usermember.getListReportDetailReport">
		<query-param name="branchId" type="string"/>
		<query-param name="groupId" type="string"/>
		<query-param name="uuidMsSubSystem" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="searchName" type="string"/>
		SELECT * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				select amu.FULL_NAME name, amu.UNIQUE_ID, msb.BRANCH_NAME, msj.DESCRIPTION job, amg.GROUP_DESCRIPTION,
					isnull(LEFT(CONVERT(VARCHAR, amu.DTM_CRT , 106), 17),'-') dateCreate,
					amu.IS_ACTIVE active, 
					amu.IS_LOCKED,
					isnull(LEFT(CONVERT(VARCHAR, amu.LAST_LOCKED, 106), 17),'-') lastLocked,
					isnull(LEFT(CONVERT(VARCHAR, amu.LAST_LOGGED_IN, 106), 17),'-') lastLoggedIn,
					amu.IMEI deviceId,
					ROW_NUMBER() OVER ( 
						order by amu.IS_ACTIVE DESC, msj.DESCRIPTION ASC, amu.FULL_NAME ASC,
						amg.GROUP_DESCRIPTION ASC, amu.DTM_CRT ASC) AS rownum
				from AM_MSUSER amu with (nolock) 
					left join MS_JOB msj with (nolock) on amu.UUID_JOB=msj.UUID_JOB
					left join AM_MEMBEROFGROUP ammog with (nolock) on ammog.UUID_MS_USER = amu.UUID_MS_USER
					left join AM_MSGROUP amg with (nolock) on ammog.UUID_MS_GROUP = amg.UUID_MS_GROUP
					left join MS_BRANCH msb with (nolock) on amu.UUID_BRANCH = msb.UUID_BRANCH
				where amg.UUID_MS_GROUP like :groupId
					and amu.UUID_BRANCH = :branchId
					and amu.FULL_NAME like '%'+:searchName+'%'
					and amu.UUID_MS_SUBSYSTEM like :uuidMsSubSystem
					and amu.DTM_CRT BETWEEN :startDate and :endDate
					and amu.IS_DELETED = '0'
			) a 
		) b
	</sql-query>
	
	<sql-query name="report.usermember.getListReportDetailAllReport">
		<query-param name="branchId" type="string"/>
		<query-param name="groupId" type="string"/>
		<query-param name="uuidMsSubSystem" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="searchName" type="string"/>
		SELECT * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				select amu.FULL_NAME name, amu.UNIQUE_ID, msb.BRANCH_NAME, msj.DESCRIPTION job, amg.GROUP_DESCRIPTION,
					isnull(LEFT(CONVERT(VARCHAR, amu.DTM_CRT, 106), 17),'-') dateCreate,
					amu.IS_ACTIVE active, 
					amu.IS_LOCKED,
					isnull(LEFT(CONVERT(VARCHAR, amu.LAST_LOCKED, 106), 17),'-') lastLocked,
					isnull(LEFT(CONVERT(VARCHAR, amu.LAST_LOGGED_IN, 106), 17),'-') lastLoggedIn,
					amu.IMEI deviceId,
					ROW_NUMBER() OVER ( 
						order by amu.IS_ACTIVE DESC, msj.DESCRIPTION ASC, amu.FULL_NAME ASC,
						amg.GROUP_DESCRIPTION ASC, amu.DTM_CRT ASC) AS rownum
				from AM_MSUSER amu with (nolock) 
					left join MS_JOB msj with (nolock) on amu.UUID_JOB=msj.UUID_JOB
					inner join (
						SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
						FROM dbo.getCabangByLogin(:branchId)
					) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
					left join AM_MEMBEROFGROUP ammog with (nolock) on ammog.UUID_MS_USER = amu.UUID_MS_USER
					left join AM_MSGROUP amg with (nolock) on ammog.UUID_MS_GROUP = amg.UUID_MS_GROUP
				where amg.UUID_MS_GROUP like :groupId
					and amu.FULL_NAME like '%'+:searchName+'%'
					and amu.UUID_MS_SUBSYSTEM like :uuidMsSubSystem 
					and amu.DTM_CRT BETWEEN :startDate and :endDate
					and amu.IS_DELETED = '0'
			) a 
		) b
	</sql-query>
</hibernate-mapping>
