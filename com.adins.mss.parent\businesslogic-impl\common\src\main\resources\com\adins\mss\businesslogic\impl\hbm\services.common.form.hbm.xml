<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
<!-- GET IMAGE -->

	<sql-query name="services.common.form.image">
		<query-param name="uuidTaskH" type="long"/>
		<query-param name="uuidQuestion" type="long"/>
			SELECT tskd.LOB_FILE as image,tskd.IMAGE_PATH as imgPath
		      FROM TR_TASKDETAILLOB tskd with (nolock)
		      JOIN TR_TASK_H th with (nolock)
		        ON tskd.UUID_TASK_H = th.UUID_TASK_H
		     WHERE th.UUID_TASK_H = :uuidTaskH
		       and tskd.QUESTION_ID = :uuidQuestion
	</sql-query>



<!-- GET SCHEME -->
	<sql-query name="services.common.form.getFormFieldUser">
		<query-param name="uuidMsUser" type="long"/>
			select distinct fog.UUID_FORM, f.FORM_NAME
			from MS_FORMOFGROUP fog with (nolock)
				inner join AM_MSGROUP g with (nolock) on fog.UUID_MS_GROUP = g.UUID_MS_GROUP
				inner join AM_MEMBEROFGROUP mog with (nolock) on g.UUID_MS_GROUP = mog.UUID_MS_GROUP
				inner join AM_MSUSER msu with (nolock) on mog.UUID_MS_USER = msu.UUID_MS_USER
				inner join MS_FORM f with (nolock) on fog.UUID_FORM = f.UUID_FORM
				inner join MS_FORMHISTORY msf with (nolock) on f.UUID_FORM = msf.UUID_FORM
			where msu.IS_ACTIVE = '1' and g.IS_ACTIVE = '1' and msu.UUID_MS_USER = :uuidMsUser
	</sql-query>
	
	<sql-query name="services.common.form.getList">
		<query-param name="uuidForm" type="long"/>
			select 'uuid_scheme' as "key", CAST(UUID_FORM AS varchar) as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'scheme_description' as "key", ISNULL(FORM_NAME, '') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'scheme_last_update' as "key", 
			ISNULL(replace(convert(varchar(10),FORM_LAST_UPDATE,103),'/','')+replace(convert(varchar(8),FORM_LAST_UPDATE,14),':',''), null) as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'is_printable' as "key", ISNULL(IS_PRINTABLE, '') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'form_id' as "key", ISNULL(FORM_NAME, '') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'form_type' as "key", ISNULL(fc.CATEGORY_DESC, '') as "value"
			  from MS_FORM f with (nolock) inner join MS_FORMCATEGORY fc with (nolock) on f.UUID_FORM_CATEGORY = fc.UUID_FORM_CATEGORY
	            where f.UUID_FORM = :uuidForm and f.IS_ACTIVE = '1' and fc.IS_ACTIVE = '1'
			union
			select 'is_preview_server' as "key", ISNULL(PREPROCESSING_SP, '0') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'is_active' as "key", ISNULL(IS_ACTIVE, '0') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'form_version' as "key", CONVERT (VARCHAR (10), ISNULL(version.versi, '')) as "value"
			from (
				select top 1 form_version as versi
					from MS_FORMHISTORY with (nolock) where UUID_FORM = :uuidForm
					ORDER BY form_version desc
			) version
	</sql-query>
	
	<sql-query name="services.common.form.getOne">
		<query-param name="uuidForm" type="long"/>
			select 'uuid_scheme' as "key", CAST(UUID_FORM AS varchar) as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'scheme_description' as "key", ISNULL(FORM_NAME, '') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'scheme_last_update' as "key", 
				   ISNULL(replace(convert(varchar(10),FORM_LAST_UPDATE,103),'/','')+replace(convert(varchar(8),FORM_LAST_UPDATE,14),':',''), '') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'is_printable' as "key", ISNULL(IS_PRINTABLE, '') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'form_id' as "key", ISNULL(FORM_NAME, '') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'form_type' as "key", ISNULL(fc.CATEGORY_DESC, '') as "value"
			  from MS_FORM f with (nolock) inner join MS_FORMCATEGORY fc with (nolock)
			    on f.UUID_FORM_CATEGORY = fc.UUID_FORM_CATEGORY
			 where f.UUID_FORM = :uuidForm and f.IS_ACTIVE = '1' and fc.IS_ACTIVE = '1'
			union
			select 'is_preview_server' as "key", ISNULL(PREPROCESSING_SP, '0') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'is_active' as "key", ISNULL(IS_ACTIVE, '0') as "value"
			  from MS_FORM with (nolock) where UUID_FORM = :uuidForm and IS_ACTIVE = '1'
			union
			select 'form_version' as "key", CONVERT (VARCHAR (10), ISNULL(version.versi, '')) as "value"
			from (
				select top 1 form_version as versi
					from MS_FORMHISTORY with (nolock) where UUID_FORM = :uuidForm
					ORDER BY form_version desc
			) version
	</sql-query>	
	
	<sql-query name="services.common.form.getPrintForm">
		<query-param name="uuidMsUser" type="long"/>
			select distinct fog.UUID_FORM 
			  from MS_FORMOFGROUP fog with (nolock)
				   inner join AM_MSGROUP g with (nolock) on fog.UUID_MS_GROUP = g.UUID_MS_GROUP
				   inner join AM_MEMBEROFGROUP mog with (nolock) on g.UUID_MS_GROUP = mog.UUID_MS_GROUP
				   inner join AM_MSUSER msu with (nolock) on mog.UUID_MS_USER = msu.UUID_MS_USER
				   inner join MS_FORM f with (nolock) on fog.UUID_FORM = f.UUID_FORM
				   inner join (select keyValue as UUID_MS_USER from getUserByLogin(:uuidMsUser)) ubl on ubl.UUID_MS_USER = msu.UUID_MS_USER
			 where msu.IS_ACTIVE = '1' and g.IS_ACTIVE = '1' and f.IS_ACTIVE='1' and f.IS_PRINTABLE = '1'
	</sql-query>
	
	<sql-query name="services.common.form.getUuidPrint">
		<query-param name="uuidForm" type="long"/>
			select pof.UUID_PRINT_ITEM_OF_FORM from MS_FORM f with (nolock) left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
			 where f.UUID_FORM = :uuidForm
	</sql-query>
	
	<sql-query name="services.common.form.getPrintItem1">
		<query-param name="uuidForm" type="long"/>
			select distinct print_item_order,  uuid_scheme, uuid_print_item, print_type_id, print_item_label, question_id, question_group_id 
			from (
				select pof.LINE_SEQ_ORDER as print_item_order,
							   f.UUID_FORM as uuid_scheme,
							   pof.UUID_PRINT_ITEM_OF_FORM as uuid_print_item,
							   ty.CODE as print_type_id,
							   pof.PRINT_ITEM_LABEL as print_item_label,
							   pof.UUID_QUESTION as question_id,
								 (case when pof.UUID_QUESTION is not null then qgf.UUID_QUESTION_GROUP else null END) as question_group_id
						  from MS_FORM f with (nolock) 
								 left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
							   join MS_PRINTITEMTYPE ty with (nolock) on pof.UUID_PRINT_ITEM_TYPE = ty.UUID_PRINT_ITEM_TYPE
							   join MS_QUESTIONGROUPOFFORM qgf on pof.UUID_FORM = qgf.UUID_FORM and pof.UUID_FORM = f.UUID_FORM	
						 where f.UUID_FORM = :uuidForm and pof.UUID_QUESTION is not null
			
				UNION 
			
				select pof.LINE_SEQ_ORDER as print_item_order,
							   f.UUID_FORM as uuid_scheme,
							   pof.UUID_PRINT_ITEM_OF_FORM as uuid_print_item,
							   ty.CODE as print_type_id,
							   pof.PRINT_ITEM_LABEL as print_item_label,
							   pof.UUID_QUESTION as question_id,
								 (case when pof.UUID_QUESTION is not null then qgf.UUID_QUESTION_GROUP else null END) as question_group_id
						  from MS_FORM f with (nolock) 
								 left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
							   join MS_PRINTITEMTYPE ty with (nolock) on pof.UUID_PRINT_ITEM_TYPE = ty.UUID_PRINT_ITEM_TYPE
							   join MS_QUESTIONGROUPOFFORM qgf on pof.UUID_FORM = qgf.UUID_FORM and pof.UUID_FORM = f.UUID_FORM	
						 where f.UUID_FORM = :uuidForm and pof.UUID_QUESTION is null
			) as temp ORDER BY print_item_order
	</sql-query>
	<sql-query name="services.common.form.getPrintItem">
		<query-param name="uuidPrint" type="long"/>
		<query-param name="uuidForm" type="long"/>
			select 'uuid_print_item' as "key", ISNULL(pof.UUID_PRINT_ITEM_OF_FORM, '') as "value"
			  from MS_FORM f with (nolock) left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
			 where pof.UUID_PRINT_ITEM_OF_FORM = :uuidPrint and f.UUID_FORM = :uuidForm
			union
			select 'print_type_id' as "key", ISNULL(ty.CODE, '') as "value"
			  from MS_FORM f with (nolock) left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
			  join MS_PRINTITEMTYPE ty on pof.UUID_PRINT_ITEM_TYPE = ty.UUID_PRINT_ITEM_TYPE
			 where pof.UUID_PRINT_ITEM_OF_FORM = :uuidPrint and f.UUID_FORM = :uuidForm
			union
			select 'print_item_label' as "key", ISNULL(pof.PRINT_ITEM_LABEL, '') as "value"
			  from MS_FORM f with (nolock) left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
			 where pof.UUID_PRINT_ITEM_OF_FORM = :uuidPrint and f.UUID_FORM = :uuidForm
			union
			select 'question_id' as "key", ISNULL(pof.UUID_QUESTION, '') as "value"
			  from MS_FORM f with (nolock) left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
			 where pof.UUID_PRINT_ITEM_OF_FORM = :uuidPrint and f.UUID_FORM = :uuidForm
			union
			select 'print_item_order' as "key", ISNULL(cast(pof.LINE_SEQ_ORDER as varchar), null) as "value"
			  from MS_FORM f with (nolock) left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
			 where pof.UUID_PRINT_ITEM_OF_FORM = :uuidPrint and f.UUID_FORM = :uuidForm
			union
			select 'uuid_scheme' as "key", ISNULL(f.UUID_FORM, '') as "value"
			  from MS_FORM f with (nolock) left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
			 where pof.UUID_PRINT_ITEM_OF_FORM = :uuidPrint and f.UUID_FORM = :uuidForm
			union
			select 'question_group_id' as "key", (case when pof.UUID_QUESTION is not null then qgf.UUID_QUESTION_GROUP else null END) as "value"
			  from MS_FORM f with (nolock) left outer join MS_PRINTITEMOFFORM pof with (nolock) on f.UUID_FORM = pof.UUID_FORM
					join MS_QUESTIONGROUPOFFORM qgf with (nolock) on pof.UUID_FORM = qgf.UUID_FORM and pof.UUID_FORM = f.UUID_FORM
			 where pof.UUID_PRINT_ITEM_OF_FORM = :uuidPrint and f.UUID_FORM = :uuidForm
	</sql-query>


<!-- GET QUESTION SET BY FORM ID -->
	<sql-query name="services.common.form.getQuestionSetByFormIdMs2">
		<query-param name="formId" type="long"/>
		<query-param name="version" type="int"/>
		select	UUID_QUESTION_GROUP,
				QUESTION_GROUP_LABEL,
				QUESTION_GROUP_OF_FORM_SEQ,
				UUID_QUESTION, 
				QUESTION_LABEL, 
				QUESTION_OF_GROUP_SEQ,
				type.code_answer_type as answerType,
				CHOICE_FILTER, 
				IS_MANDATORY, 
				ISNULL(MAX_LENGTH, 0), 
				IS_VISIBLE, 
				IS_READONLY,
				REGEX_PATTERN, 
				RELEVANT, 
				ISNULL(CAST(CALCULATE AS varchar(4000)),'') AS CALCULATE,
				'' as constraintM, 
				LOV_GROUP, 
				REF_ID,
				tag.ASSET_TAG_NAME as tagName,
				IS_HOLIDAY_ALLOWED, 
				IMG_QLT, 
				QUESTION_VALIDATION, 
				QUESTION_ERROR_MESSAGE, 
				QUESTION_VALUE,
				hist.FORM_VERSION as FORM_VERSION,
				RELEVANT_MANDATORY
		from MS_FORMQUESTIONSET qset
			JOIN MS_FORMHISTORY hist on hist.UUID_FORM_HISTORY = qset.UUID_FORM_HISTORY
			JOIN MS_ANSWERTYPE type on type.UUID_ANSWER_TYPE = qset.UUID_ANSWER_TYPE
			LEFT JOIN MS_ASSETTAG tag on tag.uuid_asset_tag = qset.UUID_ASSET_TAG
		WHERE qset.UUID_FORM = :formId
			and hist.UUID_FORM_HISTORY = (select UUID_FORM_HISTORY from MS_FORMHISTORY where UUID_FORM = :formId and FORM_VERSION = :version)
			and QUESTION_IS_ACTIVE = '1' AND QUESTION_GROUP_IS_ACTIVE = '1' and FORM_IS_ACTIVE = '1'

	</sql-query>
	
	<sql-query name="services.common.form.getQuestionSetByFormIdMc2">
		<query-param name="formId" type="long"/>
		<query-param name="version" type="int"/>
		select	UUID_QUESTION_GROUP,
			QUESTION_GROUP_LABEL, 
			QUESTION_GROUP_OF_FORM_SEQ,
			UUID_QUESTION, 
			QUESTION_LABEL, 
			QUESTION_OF_GROUP_SEQ,
			type.code_answer_type as answerType,
			CHOICE_FILTER, 
			IS_MANDATORY, 
			MAX_LENGTH, 
			IS_VISIBLE, 
			IS_READONLY,
			REGEX_PATTERN,
			RELEVANT, 
			ISNULL(CAST(CALCULATE AS varchar(4000)),'') AS CALCULATE,
			'' as constraintM, 
			LOV_GROUP, 
			REF_ID,
			tag.TAG_NAME as tagName,
			IS_HOLIDAY_ALLOWED, 
			IMG_QLT,
			QUESTION_VALIDATION, 
			QUESTION_ERROR_MESSAGE, 
			QUESTION_VALUE,
			hist.FORM_VERSION as FORM_VERSION,
			RELEVANT_MANDATORY 
	from MS_FORMQUESTIONSET qset
		 JOIN MS_FORMHISTORY hist on hist.UUID_FORM_HISTORY = qset.UUID_FORM_HISTORY
		 JOIN MS_ANSWERTYPE type on type.UUID_ANSWER_TYPE = qset.UUID_ANSWER_TYPE
		 LEFT JOIN MS_COLLECTIONTAG tag on tag.uuid_collection_tag = qset.uuid_collection_tag
	WHERE qset.UUID_FORM = :formId
			and hist.UUID_FORM_HISTORY = (select UUID_FORM_HISTORY from MS_FORMHISTORY where UUID_FORM = :formId and FORM_VERSION = :version)
			and QUESTION_IS_ACTIVE = '1' AND QUESTION_GROUP_IS_ACTIVE = '1' and FORM_IS_ACTIVE = '1'
	</sql-query>
	
	<sql-query name="services.common.form.getQuestionSetByFormIdMo2">
		<query-param name="formId" type="long"/>
		<query-param name="version" type="int"/>
		select	UUID_QUESTION_GROUP,
				QUESTION_GROUP_LABEL,
				QUESTION_GROUP_OF_FORM_SEQ,
				UUID_QUESTION, 
				QUESTION_LABEL, 
				QUESTION_OF_GROUP_SEQ,
				type.code_answer_type as answerType,
				CHOICE_FILTER, 
				IS_MANDATORY, 
				MAX_LENGTH, 
				IS_VISIBLE, 
				IS_READONLY,
				REGEX_PATTERN,
				RELEVANT,
				ISNULL(CAST(CALCULATE AS varchar(4000)),'') AS CALCULATE,
				'' as constraintM, 
				LOV_GROUP, 
				REF_ID,
				tag.TAG_NAME as tagName,
				IS_HOLIDAY_ALLOWED, 
				IMG_QLT, 
				QUESTION_VALIDATION, 
				QUESTION_ERROR_MESSAGE, 
				QUESTION_VALUE,
				hist.FORM_VERSION as FORM_VERSION,
				RELEVANT_MANDATORY
		from MS_FORMQUESTIONSET qset
		     JOIN MS_FORMHISTORY hist on hist.UUID_FORM_HISTORY = qset.UUID_FORM_HISTORY
		     JOIN MS_ANSWERTYPE type on type.UUID_ANSWER_TYPE = qset.UUID_ANSWER_TYPE
		     LEFT JOIN MS_ORDERTAG tag on tag.uuid_order_tag = qset.uuid_order_tag
		WHERE qset.UUID_FORM = :formId
			and hist.UUID_FORM_HISTORY = (select UUID_FORM_HISTORY from MS_FORMHISTORY where UUID_FORM = :formId and FORM_VERSION = :version)
			and QUESTION_IS_ACTIVE = '1' AND QUESTION_GROUP_IS_ACTIVE = '1' and FORM_IS_ACTIVE = '1'
	</sql-query>

<!-- GET VERIFICATION -->
	
	<sql-query name="services.common.form.verificationDVerify">
		<query-param name="uuidTaskH" type="long"/>
			select cast(td.UUID_TASK_D as varchar) as uuidTaskD,
				   cast(qg.UUID_QUESTION_GROUP as varchar) as uuidQuestionGroup,
				   cast(td.UUID_QUESTION as varchar) as uuidQuestion,
				   ISNULL(td.TEXT_ANSWER, '') as textAnswer,
				   CASE 
					WHEN mat.code_answer_type in ('028', '029') THEN ISNULL(cast(td.INT_ID_STAGING_ASSET as varchar), '')
					ELSE ISNULL(cast(td.INT_LOV_ID as varchar), '')
					END as lovId,
				   ot.TAG_NAME as tagName,
				   CASE 
					WHEN mat.code_answer_type in ('028', '029') THEN td.INT_OPTION_TEXT
					ELSE l.CODE
					END as code,
				   cast(td.UUID_TASK_H as varchar) as uuidTaskH,
				   td.QUESTION_TEXT as questionText,
				   cast(td.LATITUDE as varchar) as latitude,
				   cast(td.LONGITUDE as varchar) as longitude,
				   cast(td.MCC as varchar) as mcc,
				   cast(td.MNC as varchar) as mnc,
				   cast(td.LAC as varchar) as lac,
				   cast(td.CELL_ID as varchar)as cid,
				   replace(convert(varchar(10),td.TIMESTAMP_TASK,103),'/','')+replace(convert(varchar(8),td.TIMESTAMP_TASK,14),':','') as timestackTask,
				   td.ACCURACY as accuracy,
				   td.regex as regex,
				   td.is_readonly as isReadonly
			  from TR_TASK_D td with (nolock) 
			  	   inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
				   inner join MS_QUESTION q with (nolock) on td.UUID_QUESTION = q.UUID_QUESTION
				   left join MS_ORDERTAG ot with (nolock) on q.UUID_ORDER_TAG = ot.UUID_ORDER_TAG
				   left join MS_ANSWERTYPE mat with (nolock) ON q.UUID_ANSWER_TYPE =mat.UUID_ANSWER_TYPE
				   left join MS_QUESTIONOFGROUP qg with (nolock) on td.UUID_QUESTION = qg.UUID_QUESTION
				   left join MS_QUESTIONGROUPOFFORM qgof with (nolock) ON qg.UUID_QUESTION_GROUP = qgof.UUID_QUESTION_GROUP AND qgof.uuid_form = th.uuid_form
				   left join MS_LOV l with (nolock) on td.LOV_ID = l.UUID_LOV
			 where td.UUID_TASK_H = :uuidTaskH
			   and mat.code_answer_type in ('001','002','003','004','005','006','007','008','009','010','011','012','013','015','025','028','029','046')
			   order by qgof.LINE_SEQ_ORDER, qg.SEQ_ORDER
	</sql-query>
	
	<sql-query name="services.common.form.verificationDSurvey">
		<query-param name="uuidTaskH" type="long"/>
			select cast(td.UUID_TASK_D as varchar) as uuidTaskD,
				   cast(qg.UUID_QUESTION_GROUP as varchar) as uuidQuestionGroup,
				   cast(td.UUID_QUESTION as varchar) as uuidQuestion,
				   ISNULL(td.INT_TEXT_ANSWER, '') as textAnswer,
				   CASE 
					WHEN mat.code_answer_type in ('028', '029') THEN ISNULL(cast(td.INT_ID_STAGING_ASSET as varchar), '')
					ELSE ISNULL(cast(td.INT_LOV_ID as varchar), '')
					END as lovId,
				   ot.TAG_NAME as tagName,
				   CASE 
					WHEN mat.code_answer_type in ('028', '029') THEN td.INT_OPTION_TEXT
					ELSE l.CODE
					END as code,
				   cast(td.UUID_TASK_H as varchar) as uuidTaskH,
				   td.QUESTION_TEXT as questionText,
				   cast(td.LATITUDE as varchar) as latitude,
				   cast(td.LONGITUDE as varchar) as longitude,
				   cast(td.MCC as varchar) as mcc,
				   cast(td.MNC as varchar) as mnc,
				   cast(td.LAC as varchar) as lac,
				   cast(td.CELL_ID as varchar)as cid,
				   replace(convert(varchar(10),td.TIMESTAMP_TASK,103),'/','')+replace(convert(varchar(8),td.TIMESTAMP_TASK,14),':','') as timestackTask,
				   td.ACCURACY as accuracy,
				   td.regex as regex,
				   td.is_readonly as isReadonly,
				   cast(td.UUID_QUESTION_MAPPING as varchar) as uuidQuestionMapping,
				   td.is_readonly_mapping as isReadonlyMapping
			  from TR_TASK_D td with (nolock)
			  	   inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
				   inner join MS_QUESTION q with (nolock) on td.UUID_QUESTION = q.UUID_QUESTION
				   left join MS_ORDERTAG ot with (nolock) on q.UUID_ORDER_TAG = ot.UUID_ORDER_TAG
				   left join MS_ANSWERTYPE mat with (nolock) ON q.UUID_ANSWER_TYPE =mat.UUID_ANSWER_TYPE
				   left join MS_QUESTIONOFGROUP qg with (nolock) on td.UUID_QUESTION = qg.UUID_QUESTION
				   inner join MS_QUESTIONGROUPOFFORM qgof with (nolock) ON qg.UUID_QUESTION_GROUP = qgof.UUID_QUESTION_GROUP AND qgof.uuid_form = th.uuid_form
				   left join MS_LOV l with (nolock) on td.INT_LOV_ID = l.UUID_LOV
			 where td.UUID_TASK_H = :uuidTaskH
			   and mat.code_answer_type in ('001','002','003','004','005','006','007','008','009','010','011','012','013','015','025','026','028','029','043','024','046')
			   order by qgof.LINE_SEQ_ORDER, qg.SEQ_ORDER
	</sql-query>
	
	<sql-query name="services.common.form.verificationDApproval">
		<query-param name="uuidTaskH" type="long"/>
			select cast(td.UUID_TASK_D as varchar) as uuidTaskD,
			   cast(qg.UUID_QUESTION_GROUP as varchar) as uuidQuestionGroup,
			   cast(td.UUID_QUESTION as varchar) as uuidQuestion,
			   ISNULL(td.FIN_TEXT_ANSWER, '') as textAnswer,
			   CASE 
				WHEN mat.code_answer_type in ('028', '029') THEN ISNULL(cast(td.INT_ID_STAGING_ASSET as varchar), '')
				ELSE ISNULL(cast(td.FIN_LOV_ID as varchar), '')
				END as lovId,
			   ot.TAG_NAME as tagName,
			   CASE 
				WHEN mat.code_answer_type in ('028', '029') THEN td.INT_OPTION_TEXT
				ELSE l.CODE
				END as code,
			   cast(td.UUID_TASK_H as varchar) as uuidTaskH,
			   td.QUESTION_TEXT as questionText,
			   cast(td.LATITUDE as varchar) as latitude,
			   cast(td.LONGITUDE as varchar) as longitude,
			   cast(td.MCC as varchar) as mcc,
			   cast(td.MNC as varchar) as mnc,
			   cast(td.LAC as varchar) as lac,
			   cast(td.CELL_ID as varchar)as cid,
			   replace(convert(varchar(10),td.TIMESTAMP_TASK,103),'/','')+replace(convert(varchar(8),td.TIMESTAMP_TASK,14),':','') as timestackTask,
			   td.ACCURACY as accuracy,
			   td.regex as regex,
			   td.is_readonly as isReadonly
			from TR_TASK_D td  with (nolock)
			   inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
			   inner join MS_QUESTION q with (nolock) on td.UUID_QUESTION = q.UUID_QUESTION
			   left join MS_ORDERTAG ot with (nolock) on q.UUID_ORDER_TAG = ot.UUID_ORDER_TAG
			   left join MS_ANSWERTYPE mat with (nolock) ON q.UUID_ANSWER_TYPE =mat.UUID_ANSWER_TYPE
			   left join MS_QUESTIONOFGROUP qg with (nolock) on td.UUID_QUESTION = qg.UUID_QUESTION
			   left join MS_QUESTIONGROUPOFFORM qgof with (nolock) ON qg.UUID_QUESTION_GROUP = qgof.UUID_QUESTION_GROUP AND qgof.uuid_form = th.uuid_form
			   left join MS_LOV l with (nolock) on td.FIN_LOV_ID = l.UUID_LOV
			where td.UUID_TASK_H = :uuidTaskH
			and mat.code_answer_type in ('001','002','003','004','005','006','007','008','009','010','011','012','013','015','025','026','028','029','046')
			order by qgof.LINE_SEQ_ORDER, qg.SEQ_ORDER
	</sql-query>
	
	<sql-query name="services.common.form.VerificationLob">
		<query-param name="uuidTaskH" type="long"/>
			select cast(tdl.UUID_TASK_DETAIL_LOB as varchar) as uuidTaskD,
				   cast(qg.UUID_QUESTION_GROUP as varchar) as uuidQuestionGroup,
				   cast(tdl.QUESTION_ID as varchar) as uuidQuestion,
				   tdl.TEXT_ANSWER as textAnswer,
				   cast(tdl.LOV_ID as varchar) as lovId,
				   ot.TAG_NAME as tagName,
				   l.CODE as code,
				   cast(tdl.UUID_TASK_H as varchar) as uuidTaskH,
				   tdl.QUESTION_TEXT as questionText,
				   cast(tdl.LATITUDE as varchar) as latitude,
				   cast(tdl.LONGITUDE as varchar) as longitude,
				   cast(tdl.MCC as varchar) as mcc,
				   cast(tdl.MNC as varchar) as mnc,
				   cast(tdl.LAC as varchar) as lac,
				   cast(tdl.CELL_ID as varchar)as cid,
				   replace(convert(varchar(10),tdl.TIMESTAMP_DETAIL,103),'/','')+replace(convert(varchar(8),tdl.TIMESTAMP_DETAIL,14),':','') as timestackTask,
				   tdl.ACCURACY as accuracy,
				   tdl.LOB_FILE as lob,
				   tdl.image_path as imgPath,
				   tdl.IS_READONLY as isReadonly,
				   cast(tdl.UUID_QUESTION_MAPPING as varchar) as uuidQuestionMapping,
				   tdl.is_readonly_mapping as isReadonlyMapping
			  from TR_TASKDETAILLOB tdl with (nolock) inner join TR_TASK_H th on tdl.UUID_TASK_H = th.UUID_TASK_H
				   inner join MS_QUESTION q with (nolock) on tdl.QUESTION_ID = q.UUID_QUESTION
				   left join MS_ORDERTAG ot with (nolock) on q.UUID_ORDER_TAG = ot.UUID_ORDER_TAG
				   left join MS_ANSWERTYPE mat with (nolock) ON q.UUID_ANSWER_TYPE =mat.UUID_ANSWER_TYPE
				   left join MS_QUESTIONOFGROUP qg with (nolock) on tdl.QUESTION_ID = qg.UUID_QUESTION
				   inner join MS_QUESTIONGROUPOFFORM qgof with (nolock) ON qg.UUID_QUESTION_GROUP = qgof.UUID_QUESTION_GROUP AND qgof.uuid_form = th.uuid_form
				   left join MS_LOV l with (nolock) on tdl.LOV_ID = l.UUID_LOV
			 where tdl.UUID_TASK_H = :uuidTaskH
			   and mat.code_answer_type in ('016','017','018','021', '038', '039')
	</sql-query>
	<sql-query name="services.common.form.getImageVerification">
		<query-param name="uuidTaskH" type="long"/>
		<query-param name="uuidQuestion" type="long"/>
			select cast(tdl.UUID_TASK_DETAIL_LOB as varchar) as uuidTaskD,
				   cast(qg.UUID_QUESTION_GROUP as varchar) as uuidQuestionGroup,
				   cast(tdl.QUESTION_ID as varchar) as uuidQuestion,
				   tdl.TEXT_ANSWER as textAnswer,
				   cast(tdl.LOV_ID as varchar) as lovId,
				   ot.TAG_NAME as tagName,
				   l.CODE as code,
				   cast(tdl.UUID_TASK_H as varchar) as uuidTaskH,
				   tdl.QUESTION_TEXT as questionText,
				   cast(tdl.LATITUDE as varchar) as latitude,
				   cast(tdl.LONGITUDE as varchar) as longitude,
				   cast(tdl.MCC as varchar) as mcc,
				   cast(tdl.MNC as varchar) as mnc,
				   cast(tdl.LAC as varchar) as lac,
				   cast(tdl.CELL_ID as varchar)as cid,
				   replace(convert(varchar(10),tdl.TIMESTAMP_DETAIL,103),'/','')+replace(convert(varchar(8),tdl.TIMESTAMP_DETAIL,14),':','') as timestackTask,
				   tdl.ACCURACY as accuracy,
				   tdl.LOB_FILE as lob,
				   tdl.image_path as imgPath,
				   tdl.IS_READONLY as isReadonly
			  from TR_TASKDETAILLOB tdl with (nolock) inner join TR_TASK_H th on tdl.UUID_TASK_H = th.UUID_TASK_H
				   inner join MS_QUESTION q with (nolock) on tdl.QUESTION_ID = q.UUID_QUESTION
				   left join MS_ORDERTAG ot with (nolock) on q.UUID_ORDER_TAG = ot.UUID_ORDER_TAG
				   left join MS_ANSWERTYPE mat with (nolock) ON q.UUID_ANSWER_TYPE =mat.UUID_ANSWER_TYPE
				   left join MS_QUESTIONOFGROUP qg with (nolock) on tdl.QUESTION_ID = qg.UUID_QUESTION
				   inner join MS_QUESTIONGROUPOFFORM qgof with (nolock) ON qg.UUID_QUESTION_GROUP = qgof.UUID_QUESTION_GROUP AND qgof.uuid_form = th.uuid_form
				   left join MS_LOV l with (nolock) on tdl.LOV_ID = l.UUID_LOV
			 where tdl.UUID_TASK_H = :uuidTaskH and q.UUID_QUESTION = :uuidQuestion
			   and mat.code_answer_type in ('016','017','018','021','031','038', '039')
	</sql-query>
</hibernate-mapping>