<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

<sql-query name="services.common.task.getListTaskList">
	<query-param name="uuidLoginUser" type="long"/>
	<query-param name="end" type="string"/>
			SELECT tskh.UUID_TASK_H as uuidTaskH,
                       tskh.customer_name as customerName,
                       tskh.customer_phone as customerPhone,
                       tskh.customer_address as customerAddress,
                       tskh.notes as notes,
                       tskh.zip_code as zipCode,
                       tskh.latitude as latitude,
                       tskh.longitude as longitude,
                       tskh.assign_date as assignmentDate,
                       mp.priority_desc as priority,                       
                       tskh.uuid_form as schemeId,
                       msf.form_last_update as schemeLastUpdate,
                       CASE mss.SUBSYSTEM_NAME WHEN 'MS' THEN ISNULL(tskh.appl_no, '')
							WHEN 'MC' THEN ISNULL(tskh.AGREEMENT_NO, '') END as applNo,
                       dbo.F_IS_VERIFICATION(tskh.UUID_TASK_H) as isVerification,
                       case when msf.preprocessing_sp is null then '0' else '1' end as isPreviewServer,
                       tskh.dtm_crt as dtmCrt,
                       ISNULL(msf.is_printable,0) as isPrintable,
                       tskh.task_id as taskId,
                       null as voiceNote,
                       tskh.START_DTM as startDtm,
                       tskh.DOWNLOAD_DATE AS downloadDate,
					   tskh.PTS_DATE AS ptsDate,
					   tskh.form_version as formVersion,
					   ISNULL(ttcd.od, 0) as od,
					   ISNULL(ttcd.inst_no, 0) as instNo,
					   ISNULL(ttcd.amount_due, 0) as amtDue,
					   tskh.PROMISE_DATE as promisedDate,
					   tskh.KELURAHAN as kelurahan,
					   ISNULL(tskh.IS_REVISIT, '0') as isRevisit,
					   tskh.visit_type as visit_type,
					   tskh.status_follow_up as status_follow_up,
					   mst.STATUS_TASK_DESC as status_task_desc,
					   tskh.IS_PILOTING_CAE as is_piloting_cae,
					   tskh.NOTES_CRM as notes_crm,
					   tskh.IS_PRE_APPROVAL as is_pre_approval,
					   tskh.MANDATORY_LEVEL AS mandatory_level,
					   tskh.SOURCE_DATA as source_data,
					   tskh.PRODUCT_NAME as productName,
					   tskh.JENIS_ASSET as jenisAsset
             FROM tr_task_h tskh with (nolock) left join ms_form msf with (nolock)
                       ON tskh.uuid_form = msf.uuid_form
                       left JOIN am_msuser msu with (nolock) ON msu.UUID_MS_USER = tskh.UUID_MS_USER
                       left JOIN AM_MSSUBSYSTEM mss with (nolock) ON mss.UUID_MS_SUBSYSTEM = msu.UUID_MS_SUBSYSTEM
                       left JOIN ms_statustask mst with (nolock) ON tskh.uuid_status_task = mst.uuid_status_task
                       left JOIN ms_priority mp with (nolock) on mp.uuid_priority = tskh.uuid_priority
                       left JOIN tr_taskcolldata ttcd with (nolock) on ttcd.uuid_task_id = tskh.uuid_task_h
		     WHERE tskh.UUID_MS_USER = :uuidLoginUser
		       AND mst.status_code IN ('N', 'WP')
		       AND msu.IS_ACTIVE = '1'
		       <![CDATA[and tskh.ASSIGN_DATE <= :end]]>
		       ORDER BY tskh.uuid_priority, tskh.ASSIGN_DATE
</sql-query>

<sql-query name="services.common.task.getTaskStatusList">
	<query-param name="uuidTaskH" type="long"/>
			SELECT uuid_task_h as uuidTaskH, appl_no as applNo, 
			(
				select status
				from STAGING_CREDIT_PROCESS_X_RANK cpx with(nolock)
				where tth.appl_no = cpx.MOBILE_TASK_CODE or tth.appl_no = cpx.app_no
			) status,
			case when mst.status_code = 'S' then 1 else 0 end as isSentConfins
			from TR_TASK_H tth with(nolock)
			join ms_statustask mst with(nolock) on mst.uuid_status_task = tth.uuid_status_task
			join am_mssubsystem mss with(nolock) on mss.uuid_ms_subsystem = mst.uuid_ms_subsystem
			where UUID_TASK_H in (:uuidTaskH) and mss.subsystem_name = 'MS'
</sql-query>

<sql-query name="services.common.task.getListTaskUpdateList">
	<query-param name="uuidLoginUser" type="long"/>
			SELECT 		tsku.UUID_TASK_UPDATE as uuidTaskUpdate,
						tskh.UUID_MS_USER as uuidUser,
						tskh.CUSTOMER_NAME as customerName,
						tskh.CUSTOMER_PHONE as customerPhone,
						tskh.CUSTOMER_ADDRESS as customerAddress,
						tskh.NOTES as notes,
						tsku.ASSIGN_DATE as assignmentDate,
						tskh.APPL_NO as applNo,
						msf.UUID_FORM as uuidScheme,
						msf.FORM_NAME as formName,
						tsku.PENDING_NOTES as pendingNotes,
						tsku.DOCUPRO_FEEDBACK as docuproFeedback,
						tsku.CATEGORY as category,
						tsku.SUB_CATEGORY AS subCategory,
						tsku.REASON_DETAIL AS reasonDetail,
						tsku.VALIDASI as validasi,
						tskh.IS_PRE_APPROVAL
			FROM 		TR_TASKUPDATE tsku WITH (NOLOCK)
			JOIN 		TR_TASK_H tskh WITH (NOLOCK) ON tskh.UUID_TASK_H = tsku.UUID_TASK_H
			JOIN 		MS_FORM msf WITH (NOLOCK) ON msf.UUID_FORM = tskh.UUID_FORM
			WHERE 		tskh.UUID_MS_USER = :uuidLoginUser
						AND tsku.IS_PENDING = '1'
						AND EXISTS (select 1 
									from  STAGING_CREDIT_PROCESS_X_RANK scx  
									WHERE scx.STATUS != 'GLV' AND scx.APP_NO = tskh.APPL_NO 
									UNION ALL
									select 1
									from  STAGING_CREDIT_PROCESS_X_RANK scx  
									WHERE scx.STATUS != 'GLV' AND scx.MOBILE_TASK_CODE = tskh.APPL_NO
						)
			ORDER BY 	tsku.ASSIGN_DATE
</sql-query>

<sql-query name="services.common.task.getListTaskListMT">
	<query-param name="uuidLoginUser" type="long"/>
	<query-param name="groupTaskId" type="string"/>
		SELECT A.UUID_TASK_H, C.LOCATION_CODE AS CUSTOMER_NAME, C.LOCATION_ADDRESS AS CUSTOMER_ADDRESS, A.ASSIGN_DATE, A.DTM_CRT, 
		C.LATITUDE, C.LONGITUDE, D.PRIORITY_DESC, B.TASK_SEQ, A.UUID_FORM, 
		A.TASK_ID, A.NOTES, F.IS_PRINTABLE, B.GROUP_TASK_ID, B.GROUP_SEQ, A.CUSTOMER_PHONE, A.UUID_LOCATION, A.START_DTM as startDtm,
                       A.DOWNLOAD_DATE AS downloadDate
		FROM TR_TASK_H A WITH (NOLOCK)
		JOIN MS_GROUPTASK B WITH (NOLOCK) ON (A.UUID_TASK_H = B.UUID_TASK_H)
		JOIN MS_LOCATION C WITH (NOLOCK) ON (A.UUID_LOCATION = C.UUID_LOCATION)
		JOIN MS_PRIORITY D WITH (NOLOCK) ON (A.UUID_PRIORITY = D.UUID_PRIORITY)
		JOIN MS_STATUSTASK E WITH (NOLOCK) ON (A.UUID_STATUS_TASK = E.UUID_STATUS_TASK)
		JOIN MS_FORM F WITH (NOLOCK) ON (A.UUID_FORM = F.UUID_FORM)
		WHERE A.UUID_MS_USER = :uuidLoginUser
			AND B.GROUP_TASK_ID = :groupTaskId
			AND E.STATUS_CODE = 'N'
			AND A.ASSIGN_DATE BETWEEN convert(varchar(25), CONVERT(date, GETDATE()),25)+ ' 00:00:00'
			and convert(varchar(25), CONVERT(date, GETDATE()),25)+ ' 23:59:59'
		ORDER BY D.UUID_PRIORITY, A.ASSIGN_DATE
</sql-query>

<sql-query name="services.common.task.getListGroupTaskIdMT">
	<query-param name="uuidLoginUser" type="long"/>
	<query-param name="start" type="string"/>
	<query-param name="end" type="string"/>
	<query-param name="lastAttendance" type="string"/>

	SELECT B.GROUP_TASK_ID
	FROM TR_TASK_H A WITH (NOLOCK)
	JOIN MS_GROUPTASK B WITH (NOLOCK) ON (A.UUID_TASK_H = B.UUID_TASK_H)
	JOIN MS_STATUSTASK C WITH (NOLOCK) ON (A.UUID_STATUS_TASK = C.UUID_STATUS_TASK)
	WHERE A.UUID_MS_USER = :uuidLoginUser
			AND C.STATUS_CODE = 'N'
			AND (
				A.ASSIGN_DATE BETWEEN :start and :end OR 
				<![CDATA[(A.ASSIGN_DATE < :lastAttendance )]]>
			)
	GROUP BY B.GROUP_TASK_ID
	
</sql-query>

<sql-query name="services.common.task.refreshTask">
	<query-param name="dtmCrt" type="Date"/>
	<query-param name="uuidMsUser" type="long"/>
	<![CDATA[
		SELECT COUNT(1)
      	  FROM TR_TASK_H tskh with (nolock) INNER JOIN MS_STATUSTASK sttsk with (nolock)
      	    ON tskh.UUID_STATUS_TASK = sttsk.UUID_STATUS_TASK
         WHERE tskh.DTM_CRT > :dtmCrt
           AND tskh.UUID_MS_USER = :uuidMsUser
           AND sttsk.STATUS_CODE = 'N'
     ]]>	
</sql-query>

<sql-query name="services.common.task.getStatus">
	<query-param name="taskId" type="String"/>
		SELECT ISNULL(st.STATUS_CODE,'') as statusTask 
			FROM TR_TASK_H tr with (nolock)
			JOIN MS_STATUSTASK st with (nolock)
			ON tr.UUID_STATUS_TASK = st.UUID_STATUS_TASK
			WHERE tr.TASK_ID = :taskId
</sql-query>

<sql-query name="services.common.task.checkUserId">
	<query-param name="callerId" type="long"/>
		SELECT UUID_MS_User
			FROM AM_MSUSER with (nolock)
			WHERE UUID_MS_User = :callerId
</sql-query>

<sql-query name="services.common.task.getResultText">
	<query-param name="taskId" type="String"/>
	<query-param name="loginId" type="String"/>
		SELECT ISNULL(us.FULL_NAME,'') AS fullName
		,ISNULL(br.BRANCH_CODE,'') AS branchCode
		,ISNULL(th.APPL_NO,'') AS applNo
		,ISNULL(fr.FORM_NAME,'') AS formName
		,ISNULL(st.STATUS_CODE,'') AS statusCode
		,ISNULL(pr.PRIORITY_DESC,'') AS priorityDesc
		,th.ASSIGN_DATE AS assignDate
		,th.DOWNLOAD_DATE AS downloadDate
		,th.SUBMIT_DATE AS submitDate
		,th.START_DTM AS startDtm
		,ISNULL(th.CUSTOMER_NAME,'') AS customerName
		,ISNULL(th.CUSTOMER_PHONE,'') AS customerPhone
		,ISNULL(th.CUSTOMER_ADDRESS,'') AS customerAddress
		,ISNULL(th.NOTES,'') AS notes
		,ISNULL(th.TASK_ID,'') AS taskId
		,ISNULL(at.code_answer_type,'') AS answerTypeId
		,ISNULL(at.ANSWER_TYPE_NAME,'') AS answerTypeName
		,ISNULL((SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Text'
													,'Text Multiline'
													,'Text Multiline Separate'
													,'Currency'
													,'Numeric'
													,'Decimal'
													,'Multiple one description'
													,'Multiple with description'
													,'Radio with description'
													,'Dropdown with description'
													,'Date'
													,'Time'
													,'Date Time'
													,'LuOnline')
			THEN (select td.TEXT_ANSWER) END ),'') AS textAnswer
		,ISNULL((SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Multiple'
													,'Multiple one description'
													,'Multiple with description'
													,'Radio'
													,'Radio with description'
													,'Dropdown'
													,'Dropdown with description'
													,'Lookup'
													,'Lookup w/ Filter')
			THEN (select CAST(td.LOV_ID AS VARCHAR)) END ),'') AS lovId
		FROM TR_TASK_H th with (nolock)
		JOIN AM_MSUSER us with (nolock) ON th.UUID_MS_USER = us.UUID_MS_USER
		JOIN MS_BRANCH br with (nolock) ON th.UUID_BRANCH = br.UUID_BRANCH
		JOIN MS_FORM fr with (nolock) ON th.UUID_FORM = fr.UUID_FORM
		JOIN MS_STATUSTASK st with (nolock) ON th.UUID_STATUS_TASK = st.UUID_STATUS_TASK
		JOIN MS_PRIORITY pr with (nolock) ON th.UUID_PRIORITY = pr.UUID_PRIORITY
		JOIN TR_TASK_D td with (nolock) ON th.UUID_TASK_H = td.UUID_TASK_H
		JOIN MS_QUESTION q with (nolock) ON td.UUID_QUESTION = q.UUID_QUESTION
		JOIN MS_ANSWERTYPE at with (nolock) ON q.UUID_ANSWER_TYPE = at.UUID_ANSWER_TYPE
		WHERE us.LOGIN_ID = :loginId
		AND th.TASK_ID = :taskId
</sql-query>

<sql-query name="services.common.task.getResultImage">
	<query-param name="taskId" type="String"/>
	<query-param name="loginId" type="String"/>
		SELECT ISNULL(us.FULL_NAME,'') AS fullName
		,ISNULL(br.BRANCH_CODE,'') AS branchCode
		,ISNULL(th.APPL_NO,'') AS applNo
		,ISNULL(fr.FORM_NAME,'') AS formName
		,ISNULL(st.STATUS_CODE,'') AS statusCode
		,ISNULL(pr.PRIORITY_DESC,'') AS priorityDesc
		,th.ASSIGN_DATE AS assignDate
		,th.DOWNLOAD_DATE AS downloadDate
		,th.SUBMIT_DATE AS submitDate
		,th.START_DTM AS startDtm
		,ISNULL(th.CUSTOMER_NAME,'') AS customerName
		,ISNULL(th.CUSTOMER_PHONE,'') AS customerPhone
		,ISNULL(th.CUSTOMER_ADDRESS,'') AS customerAddress
		,ISNULL(th.NOTES,'') AS notes
		,ISNULL(th.TASK_ID,'') AS taskId
		,ISNULL(at.code_answer_type,'') AS answerTypeId
		,ISNULL(at.ANSWER_TYPE_NAME,'') AS answerTypeName
		,(SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Image'
													,'Image with Geodata'
													,'Image with GPS'
													,'Drawing')
			THEN (select lb.LOB_FILE) END ) AS lobFile
		,(SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Image with Geodata'
													,'Image with GPS'
													,'Location')
			THEN (select lb.LATITUDE) END ) AS latitude
		,(SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Image with Geodata'
													,'Image with GPS'
													,'Location')
			THEN (select lb.LONGITUDE) END ) AS longitude
		,ISNULL((SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Image with Geodata'
													,'Image with GPS'
													,'Location')
			THEN (select lb.ACCURACY) END),'0' ) AS accuracy
		,ISNULL((SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Image with Geodata'
													,'Location')
			THEN (select lb.MCC) END),'0' ) AS mcc
		,ISNULL((SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Image with Geodata'
													,'Location')
			THEN (select lb.MNC) END),'0' ) AS mnc
		,ISNULL((SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Image with Geodata'
													,'Location')
			THEN (select lb.LAC) END),'0' ) AS lac
		,ISNULL((SELECT CASE WHEN at.ANSWER_TYPE_NAME IN ('Image with Geodata'
													,'Location')
			THEN (select lb.CELL_ID) END),'0' ) AS cellId
		,lb.IMAGE_PATH as imagePath
		FROM TR_TASK_H th with (nolock)
		JOIN AM_MSUSER us with (nolock) ON th.UUID_MS_USER = us.UUID_MS_USER
		JOIN MS_BRANCH br with (nolock) ON th.UUID_BRANCH = br.UUID_BRANCH
		JOIN MS_FORM fr with (nolock) ON th.UUID_FORM = fr.UUID_FORM
		JOIN MS_STATUSTASK st with (nolock) ON th.UUID_STATUS_TASK = st.UUID_STATUS_TASK
		JOIN MS_PRIORITY pr with (nolock) ON th.UUID_PRIORITY = pr.UUID_PRIORITY
		JOIN TR_TASKDETAILLOB lb with (nolock) ON th.UUID_TASK_H = lb.UUID_TASK_H
		JOIN MS_QUESTION q with (nolock) ON lb.QUESTION_ID = q.UUID_QUESTION
		JOIN MS_ANSWERTYPE at with (nolock) ON q.UUID_ANSWER_TYPE = at.UUID_ANSWER_TYPE
		WHERE us.LOGIN_ID = :loginId
		AND th.TASK_ID = :taskId
</sql-query>

	<sql-query name="services.common.task.getFormHist">
		<query-param name="uuidForm" type="long" />
		<query-param name="formVersion" type="string" />
		select UUID_FORM_HISTORY from MS_FORMHISTORY WITH(NOLOCK)
		where UUID_FORM = :uuidForm
		and FORM_VERSION = :formVersion
	</sql-query>
	
<sql-query name="services.common.task.getScheme">
	<query-param name="formName" type="String"/>
		
	SELECT ISNULL(msg.UUID_QUESTION_GROUP,'') AS questionGroupId
		,ISNULL(msg.QUESTION_GROUP_LABEL,'') AS questionGroupName
		,ISNULL(msqgf.LINE_SEQ_ORDER,'') AS questionGroupOrder
		,ISNULL(msq.UUID_QUESTION,'') AS questionId
		,ISNULL(msq.QUESTION_LABEL,'') AS questionLabel
		,ISNULL(msqog.SEQ_ORDER,'') AS questionOrder
		,ISNULL(msa.CODE_ANSWER_TYPE,'') AS answerType
		,'' AS optionText
		,ISNULL(msqr.CHOICE_FILTER ,'') AS choiceFilter
		,ISNULL(msq.IS_MANDATORY,'') AS isMandatory
		,ISNULL(msq.MAX_LENGTH,'') AS maxLength
		,ISNULL(msq.IS_VISIBLE,'') AS isVisible
		,ISNULL(msq.IS_READONLY,'') AS isReadonly
		,ISNULL(msq.REGEX_PATTERN,'') AS regex
		,ISNULL(msqr.RELEVANT,'') AS relevantQuestion
		,ISNULL(msqr.CALCULATE,'') AS calculate
		,'' AS constraintMessage
		FROM MS_QUESTIONGROUP msg with (nolock)
		JOIN MS_QUESTIONOFGROUP msqog with (nolock) ON msg.UUID_QUESTION_GROUP = msqog.UUID_QUESTION_GROUP
		JOIN MS_QUESTION msq with (nolock) ON msqog.UUID_QUESTION = msq.UUID_QUESTION
		JOIN MS_ANSWERTYPE msa with (nolock) ON msq.UUID_ANSWER_TYPE = msa.UUID_ANSWER_TYPE
		JOIN MS_QUESTIONGROUPOFFORM msqgf with (nolock) ON msg.UUID_QUESTION_GROUP = msqgf.UUID_QUESTION_GROUP
		JOIN MS_FORM msf with (nolock) ON msqgf.UUID_FORM = msf.UUID_FORM
		JOIN MS_QUESTIONRELEVANT msqr with (nolock) ON msq.UUID_QUESTION = msqr.UUID_QUESTION
		WHERE msf.FORM_NAME = :formName
		
</sql-query>

<sql-query name="services.common.task.getListTaskListAssign">
	<query-param name="uuidUser" type="long"/>
		SELECT tskh.task_id,
			   tskh.customer_name as customerName,                   
			   tskh.UUID_TASK_H as uuidTaskH,
			     mf.form_name
             FROM tr_task_h tskh with (nolock)
					LEFT JOIN am_msuser msu with (nolock) ON msu.UUID_MS_USER = :uuidUser
					LEFT JOIN ms_form mf with (nolock) ON mf.UUID_FORM = tskh.UUID_FORM	
					LEFT JOIN ms_statustask mst with (nolock) ON tskh.uuid_status_task = mst.uuid_status_task
					LEFT JOIN TR_TASKLINK ttl with (nolock) ON tskh.UUID_TASK_H = ttl.UUID_TASK_H_SURVEY		
		     WHERE tskh.UUID_BRANCH = msu.uuid_branch
		       AND mst.status_code = 'A'
			   AND mst.UUID_MS_SUBSYSTEM = msu.uuid_ms_subsystem
		       AND msu.IS_ACTIVE = '1'
			   AND msu.UUID_MS_SUBSYSTEM = msu.uuid_ms_subsystem
		     ORDER BY tskh.task_id asc
</sql-query>

<sql-query name="services.common.task.getListTaskListReAssign">
	<query-param name="uuidBranch" type="long"/>
	<query-param name="startDate" type="string"/>
	<query-param name="endDate" type="string"/>
	<query-param name="uuidSubsystem" type="long"/>
			SELECT tskh.task_id,
				   tskh.customer_name as customerName,                   
				   tskh.UUID_TASK_H as uuidTaskH,
			  	   mf.form_name				     
             FROM tr_task_h tskh with (nolock)
					LEFT JOIN am_msuser msu with (nolock) ON msu.UUID_MS_USER = tskh.UUID_MS_USER
					LEFT JOIN ms_form mf with (nolock) ON mf.UUID_FORM = tskh.UUID_FORM	
					LEFT JOIN ms_statustask mst with (nolock) ON tskh.uuid_status_task = mst.uuid_status_task
					LEFT JOIN TR_TASKLINK ttl with (nolock) ON tskh.UUID_TASK_H = ttl.UUID_TASK_H_SURVEY
		     WHERE tskh.UUID_BRANCH = :uuidBranch
		       AND mst.status_code = 'N'
			   AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
		       AND msu.IS_ACTIVE = '1'
			   AND msu.UUID_MS_SUBSYSTEM = :uuidSubsystem
			   AND tskh.ASSIGN_DATE between :startDate and :endDate
			   AND tskh.UUID_MS_USER is not null
		     ORDER BY tskh.TASK_ID asc
</sql-query>

	<sql-query name="services.common.task.getDetailTask">
		<query-param name="uuidTaskH" type="long"/>
		select c.* from (select b.* from (select a.*, mof.LINE_SEQ_ORDER, mog.SEQ_ORDER from (
				SELECT ttd.QUESTION_TEXT as "key",
					CASE WHEN mat.code_answer_type in ('001','002','003','004','005','013','015','025','026') THEN ttd.INT_TEXT_ANSWER
								WHEN  mat.code_answer_type in ('006','007','008','009','010','011','012')  THEN ttd.INT_OPTION_TEXT
							WHEN  mat.code_answer_type in ('024') and (ttd.LATITUDE is not null or ttd.LATITUDE is not null ) 
							THEN cast(ttd.LATITUDE as varchar) + ',' + cast(ttd.LONGITUDE as varchar)
							 WHEN  mat.code_answer_type in ('024') and ttd.IS_GPS = '0' and ttd.IS_GSM = '1' and  ttd.IS_CONVERTED = '0' 
							 THEN cast(ttd.MCC as varchar) + ',' + cast(ttd.MNC as varchar) + ',' + cast(ttd.LAC as varchar) + ',' + cast(ttd.CELL_ID as varchar)
							ELSE ''
						 END AS "value",
					CASE WHEN mat.code_answer_type in('001','002','003','004','005','013','015','025','026') THEN '0'
								WHEN  mat.code_answer_type in ('006','007','008','009','010','011','012') THEN '0'
							WHEN  mat.code_answer_type in ('024') THEN '2'
							ELSE ''
						 END AS flag,
						 CAST(mq.UUID_QUESTION as VARCHAR) as UUID_QUESTION, '4' as "order"
				FROM TR_TASK_D ttd with (nolock) LEFT JOIN MS_QUESTION mq with (nolock)
					ON ttd.UUID_QUESTION = mq.UUID_QUESTION
					LEFT JOIN MS_ANSWERTYPE mat with (nolock)
					ON mat.UUID_ANSWER_TYPE = mq.UUID_ANSWER_TYPE
				WHERE ttd.UUID_TASK_H = :uuidTaskH
				UNION
				SELECT ttdl.QUESTION_TEXT AS "key",
					CASE WHEN mat.code_answer_type in ('016','017','018','021')  THEN CAST(ttdl.QUESTION_ID as varchar)
							ELSE ''
					 END AS "value",
					CASE WHEN mat.code_answer_type in('017','018') THEN '3'
						 WHEN mat.code_answer_type in('016','021') THEN '1'
							ELSE ''
					 END AS flag,
					 CAST(mq.UUID_QUESTION as VARCHAR) as UUID_QUESTION, '4' as "order"
				FROM TR_TASKDETAILLOB ttdl with (nolock) LEFT JOIN MS_QUESTION mq with (nolock)
					ON ttdl.QUESTION_ID = mq.UUID_QUESTION
					LEFT JOIN MS_ANSWERTYPE mat with (nolock)
					ON mat.UUID_ANSWER_TYPE = mq.UUID_ANSWER_TYPE
				WHERE ttdl.UUID_TASK_H = :uuidTaskH
				) a
			left outer JOIN MS_QUESTIONOFGROUP mog with (nolock)
				ON a.UUID_QUESTION = mog.UUID_QUESTION 
			left outer JOIN MS_QUESTIONGROUPOFFORM mof with (nolock)
				ON mog.UUID_QUESTION_GROUP = mof.UUID_QUESTION_GROUP
			left outer JOIN MS_FORM mf with (nolock)
				ON mf.UUID_FORM = mof.UUID_FORM
			where mf.UUID_FORM = (select uuid_form from tr_task_h where uuid_task_h = :uuidTaskH)
		) b
		UNION
		SELECT 'Customer Name' AS "key", trth.CUSTOMER_NAME as value, '0' as flag, '' as UUID_QUESTION, '1' as "order", '0' LINE_SEQ_ORDER, '0' SEQ_ORDER
		FROM TR_TASK_H trth with (nolock)
		where trth.UUID_TASK_H = :uuidTaskH
		UNION
		SELECT 'Customer Address' AS "key", trth.CUSTOMER_ADDRESS as value, '0' as flag, '' as UUID_QUESTION, '2' as "order", '0' LINE_SEQ_ORDER, '0' SEQ_ORDER
		FROM TR_TASK_H trth with (nolock)
		where trth.UUID_TASK_H = :uuidTaskH
		UNION
		SELECT 'Customer Phone' AS "key", trth.CUSTOMER_PHONE as value, '0' as flag, '' as UUID_QUESTION, '3' as "order", '0' LINE_SEQ_ORDER, '0' SEQ_ORDER
		FROM TR_TASK_H trth with (nolock)
		where trth.UUID_TASK_H = :uuidTaskH) c
		order by "order", c.LINE_SEQ_ORDER, c.SEQ_ORDER asc
	</sql-query>
	
	<sql-query name="services.common.task.getUserAssign">
		<query-param name="uuidLoginUser" type="long"/>
		<query-param name="uuidBranchLogin" type="long"/>
		   SELECT a.keyValue, a.fullName, b.DESCRIPTION
			 FROM dbo.getUserByLogin(:uuidLoginUser) a left join MS_JOB b with (nolock)
			   ON a.uuid_job = b.UUID_JOB
			   left join AM_MSUSER c with (nolock) on a.keyValue = c.uuid_ms_user
			WHERE b.IS_FIELD_PERSON = '1'	
			  AND a.keyValue not in (:uuidLoginUser)
			  and c.uuid_branch in (:uuidBranchLogin)
	</sql-query>

	<sql-query name="services.common.task.getReUserAssign">
		<query-param name="uuidLoginUser" type="long"/>
		<query-param name="uuidBranchLogin" type="long"/>
		<query-param name="uuidOldUserAssign" type="long"/>
		   SELECT a.keyValue, a.fullName, b.DESCRIPTION
			 FROM dbo.getUserByLogin(:uuidLoginUser) a left join MS_JOB b with (nolock)
			   ON a.uuid_job = b.UUID_JOB
			   left join AM_MSUSER c with (nolock) on a.keyValue = c.uuid_ms_user
			WHERE b.IS_FIELD_PERSON = '1'	
			  AND a.keyValue not in (:uuidLoginUser, :uuidOldUserAssign)
			  and c.uuid_branch in (:uuidBranchLogin)
	</sql-query>

	<sql-query name="services.common.task.getFormFromSPV">
		<query-param name="uuidSpvUser" type="long"/>
		SELECT DISTINCT frm.FORM_NAME
		  FROM MS_FORM frm with (nolock)
			JOIN MS_FORMOFGROUP frmg with (nolock) ON frm.UUID_FORM = frmg.UUID_FORM
			JOIN AM_MEMBEROFGROUP mbrg with (nolock) ON frmg.UUID_MS_GROUP = mbrg.UUID_MS_GROUP
			JOIN AM_MSUSER usr with (nolock) ON mbrg.UUID_MS_USER = usr.UUID_MS_USER
			WHERE usr.SPV_ID = :uuidSpvUser
	</sql-query>
		<sql-query name="services.common.task.getBranchFromDealer">
		<query-param name="uuidDealer" type="long"/>
		SELECT DOB.UUID_BRANCH FROM MS_DEALEROFBRANCH DOB JOIN MS_BRANCH MB ON DOB.UUID_BRANCH = MB.UUID_BRANCH
		WHERE DOB.UUID_DEALER = :uuidDealer
		AND MB.BRANCH_CODE !='HO'
	</sql-query>
	
	<sql-query name="services.common.task.getGroupTaskIDByAssignDate">
		<query-param name="dateAssignStart" type="string" />
		<query-param name="dateAssignEnd" type="string" />
		<query-param name="uuidMobileUserID" type="long" />
		<query-param name="groupSeq" type="string" />
		SELECT A.ASSIGN_DATE, A.UUID_MS_USER, B.GROUP_TASK_ID, B.TASK_SEQ
		FROM TR_TASK_H A LEFT OUTER JOIN  MS_GROUPTASK B ON (A.UUID_TASK_H = B.UUID_TASK_H)
		WHERE A.ASSIGN_DATE BETWEEN :dateAssignStart AND :dateAssignEnd
			AND A.UUID_MS_USER = :uuidMobileUserID 
			AND B.GROUP_SEQ = :groupSeq
		ORDER BY ASSIGN_DATE DESC
	</sql-query>
	
	<sql-query name="services.common.task.getGroupTaskIdByDtmCrt">
		<query-param name="uuidMobileUserID" type="long" />
		<query-param name="groupSeq" type="string" />
		SELECT TOP 1 A.ASSIGN_DATE, A.UUID_MS_USER, B.GROUP_TASK_ID, B.TASK_SEQ
		FROM TR_TASK_H A LEFT OUTER JOIN  MS_GROUPTASK B ON (A.UUID_TASK_H = B.UUID_TASK_H)
		WHERE A.UUID_MS_USER = :uuidMobileUserID 
			AND B.GROUP_SEQ = :groupSeq
			ORDER BY B.DTM_CRT DESC
	</sql-query>
	
	<sql-query name="services.common.task.getListErrorValidasi">
		<query-param name="dateAssign" type="string" />
		<query-param name="uuidMobileUserID" type="long" />
		<query-param name="taskSeq" type="string" />
		<query-param name="groupSeq" type="string" />
		SELECT A.ASSIGN_DATE, A.UUID_MS_USER, B.GROUP_TASK_ID, B.TASK_SEQ
		FROM TR_TASK_H A LEFT OUTER JOIN  MS_GROUPTASK B ON (A.UUID_TASK_H = B.UUID_TASK_H)
		WHERE A.ASSIGN_DATE = :dateAssignStart AND :dateAssignEnd
			AND A.UUID_MS_USER = :uuidMobileUserID 
			AND B.TASK_SEQ = :taskSeq
			AND B.GROUP_SEQ = :groupSeq
		ORDER BY ASSIGN_DATE DESC
	</sql-query>

	<sql-query name="services.common.task.isClosing">
		<query-param name="uuidMsuserDriver" type="long" />
		<query-param name="dateAssign" type="string" />
		SELECT TOP 1 *
		FROM TR_ATTENDANCE WITH (NOLOCK)
		WHERE UUID_MS_USER = :uuidMsuserDriver
			AND CAST (ATTENDANCE_DATE AS DATE) = CAST (:dateAssign AS DATE)
			AND datetime_OUT IS NOT NULL
	</sql-query>
	
	<sql-query name="services.common.task.getPrevTaskSeq">
		<query-param name="groupTaskID" type="string" />
		SELECT TOP 1 A.UUID_GROUP_TASK, A.GROUP_TASK_ID, A.GROUP_SEQ, A.TASK_SEQ, ST.STATUS_CODE
		FROM MS_GROUPTASK A WITH (NOLOCK)
		JOIN TR_TASK_H TH WITH (NOLOCK) ON (A.UUID_TASK_H = TH.UUID_TASK_H)
		JOIN MS_STATUSTASK ST WITH (NOLOCK) ON (TH.UUID_STATUS_TASK = ST.UUID_STATUS_TASK)
		WHERE A.GROUP_TASK_ID = :groupTaskID
			AND TH.START_DTM IS NOT NULL
			AND TH.SUBMIT_DATE IS NULL
		ORDER BY A.TASK_SEQ DESC
	</sql-query>
	
	<sql-query name="services.common.task.copyTaskDAsInitAnswer">
		<query-param name="uuidTaskHSource" type="long" />
		<query-param name="uuidTaskH" type="long" />
		<query-param name="usrCrt" type="string" />
		INSERT INTO TR_TASK_D
		      (USR_CRT, DTM_CRT, UUID_TASK_H, UUID_QUESTION, QUESTION_TEXT,
		       INT_TEXT_ANSWER, INT_LOV_ID, INT_OPTION_TEXT, LATITUDE, LONGITUDE, MCC, MNC, LAC, CELL_ID, ACCURACY,
		       IS_GPS, IS_GSM, GEOLOCATION_PROVIDER, IS_CONVERTED, INT_ID_STAGING_ASSET, UUID_QUESTION_MAPPING, IS_READONLY_MAPPING )
		SELECT :usrCrt, GETDATE(), :uuidTaskH, UUID_QUESTION, QUESTION_TEXT,
		       TEXT_ANSWER, LOV_ID, OPTION_TEXT, LATITUDE, LONGITUDE, MCC, MNC, LAC, CELL_ID, ACCURACY,
		       IS_GPS, IS_GSM, GEOLOCATION_PROVIDER, IS_CONVERTED, ID_STAGING_ASSET, UUID_QUESTION_MAPPING, IS_READONLY_MAPPING 
		  FROM TR_TASK_D
		 WHERE UUID_TASK_H = :uuidTaskHSource   
	</sql-query>
	
	<sql-query name="services.common.task.copyTaskSurveyData">
		<query-param name="uuidTaskHSource" type="long" />
		<query-param name="uuidTaskH" type="long" />
		<query-param name="usrCrt" type="string" />
		INSERT INTO TR_TASKSURVEYDATA
		 		(UUID_TASK_ID, USR_CRT, DTM_CRT, HOME_LATITUDE, HOME_LONGITUDE, OFFICE_LATITUDE, OFFICE_LONGITUDE, 
		 		LEGAL_ADDR_LATITUDE, LEGAL_ADDR_LONGITUDE, DRIVEWAY_LATITUDE, DRIVEWAY_LONGITUDE, VEHICLE_LATITUDE, 
		 		VEHICLE_LONGITUDE, PRODUCT_LOV_ID, USR_UPD, DTM_UPD)
		 SELECT :uuidTaskH, :usrCrt, GETDATE(),HOME_LATITUDE, HOME_LONGITUDE, OFFICE_LATITUDE, OFFICE_LONGITUDE, 
		 		LEGAL_ADDR_LATITUDE, LEGAL_ADDR_LONGITUDE, DRIVEWAY_LATITUDE, DRIVEWAY_LONGITUDE, VEHICLE_LATITUDE, 
		 		VEHICLE_LONGITUDE, PRODUCT_LOV_ID, null, null
		 	FROM TR_TASKSURVEYDATA WHERE UUID_TASK_ID = :uuidTaskHSource    
	</sql-query>
	
	<sql-query name="services.common.task.copyTaskDInitAsInitAnswer">
		<query-param name="uuidTaskHSource" type="long" />
		<query-param name="uuidTaskH" type="long" />
		<query-param name="usrCrt" type="string" />
		INSERT INTO TR_TASK_D
		      (USR_CRT, DTM_CRT, UUID_TASK_H, UUID_QUESTION, QUESTION_TEXT,
		       INT_TEXT_ANSWER, INT_LOV_ID, INT_OPTION_TEXT, LATITUDE, LONGITUDE, MCC, MNC, LAC, CELL_ID, ACCURACY,
		       IS_GPS, IS_GSM, GEOLOCATION_PROVIDER, IS_CONVERTED)
		SELECT :usrCrt, GETDATE(), :uuidTaskH, UUID_QUESTION, QUESTION_TEXT,
		       INT_TEXT_ANSWER, INT_LOV_ID, INT_OPTION_TEXT, LATITUDE, LONGITUDE, MCC, MNC, LAC, CELL_ID, ACCURACY,
		       IS_GPS, IS_GSM, GEOLOCATION_PROVIDER, IS_CONVERTED
		  FROM TR_TASK_D
		 WHERE UUID_TASK_H = :uuidTaskHSource   
	</sql-query>
	
	<sql-query name="services.common.task.copyTaskDJsonAsInitAnswer">
		<query-param name="uuidTaskHSource" type="long" />
		<query-param name="uuidTaskH" type="long" />
		<query-param name="usrCrt" type="string" />
		INSERT INTO TR_TASKDOCUMENT
				(UUID_TASK_ID, DTM_CRT, USR_CRT, DOCUMENT)
		SELECT :uuidTaskH, GETDATE(), :usrCrt, DOCUMENT
		  FROM TR_TASKDOCUMENT
		 WHERE UUID_TASK_ID = :uuidTaskHSource   
	</sql-query>
	
	<sql-query name="services.common.task.copyTaskdetaillobAsInitAnswer">
		<query-param name="uuidTaskHSource" type="long" />
		<query-param name="uuidTaskH" type="long" />
		<query-param name="usrCrt" type="string" />
		INSERT INTO TR_TASKDETAILLOB
				(USR_CRT, DTM_CRT, UUID_TASK_H, QUESTION_ID, QUESTION_TEXT,
				 INT_TEXT_ANSWER, INT_LOV_ID, INT_OPTION_TEXT, IMAGE_PATH, LATITUDE, LONGITUDE, ACCURACY, MCC, MNC, LAC, CELL_ID,
				 IS_GPS, IS_GSM, GEOLOCATION_PROVIDER, IS_VISIBLE_QA, LOB_FILE, IS_CONVERTED, UUID_QUESTION_MAPPING, IS_READONLY_MAPPING)
		SELECT :usrCrt, GETDATE(), :uuidTaskH, QUESTION_ID, QUESTION_TEXT,
				 TEXT_ANSWER, LOV_ID, OPTION_TEXT, IMAGE_PATH, LATITUDE, LONGITUDE, ACCURACY, MCC, MNC, LAC, CELL_ID,
				 IS_GPS, IS_GSM, GEOLOCATION_PROVIDER, IS_VISIBLE_QA, LOB_FILE, IS_CONVERTED, UUID_QUESTION_MAPPING, IS_READONLY_MAPPING
		  FROM TR_TASKDETAILLOB
		 WHERE UUID_TASK_H = :uuidTaskHSource
	</sql-query>
	
	<sql-query name="services.common.task.copyTaskdetaillobInitAsInitAnswer">
		<query-param name="uuidTaskHSource" type="long" />
		<query-param name="uuidTaskH" type="long" />
		<query-param name="usrCrt" type="string" />
		INSERT INTO TR_TASKDETAILLOB
				(USR_CRT, DTM_CRT, UUID_TASK_H, QUESTION_ID, QUESTION_TEXT,
				 INT_TEXT_ANSWER, INT_LOV_ID, INT_OPTION_TEXT, IMAGE_PATH, LATITUDE, LONGITUDE, ACCURACY, MCC, MNC, LAC, CELL_ID,
				 IS_GPS, IS_GSM, GEOLOCATION_PROVIDER, IS_VISIBLE_QA, LOB_FILE, IS_CONVERTED)
		SELECT :usrCrt, GETDATE(), :uuidTaskH, QUESTION_ID, QUESTION_TEXT,
				 INT_TEXT_ANSWER, INT_LOV_ID, INT_OPTION_TEXT, IMAGE_PATH, LATITUDE, LONGITUDE, ACCURACY, MCC, MNC, LAC, CELL_ID,
				 IS_GPS, IS_GSM, GEOLOCATION_PROVIDER, IS_VISIBLE_QA, LOB_FILE, IS_CONVERTED
		  FROM TR_TASKDETAILLOB
		 WHERE UUID_TASK_H = :uuidTaskHSource
	</sql-query>
	
	<sql-query name="services.common.task.copyTaskhistoryForPts">
		<query-param name="uuidTaskHSource" type="long" />
		<query-param name="taskIdSource" type="string" />
		<query-param name="uuidTaskH" type="long" />
		<query-param name="usrCrt" type="string" />
		INSERT INTO TR_TASKHISTORY
				(USR_CRT, DTM_CRT, UUID_TASK_H, UUID_STATUS_TASK, NOTES,
				 FIELD_PERSON, ACTOR, CODE_PROCESS, UUID_TASK_REJECTED_HISTORY)
		SELECT USR_CRT, DTM_CRT, :uuidTaskH, UUID_STATUS_TASK, LEFT(notes + ' | PTS dari Task ID: ' + :taskIdSource, 2048),
			   FIELD_PERSON, ACTOR, CODE_PROCESS, UUID_TASK_REJECTED_HISTORY
		  FROM TR_TASKHISTORY
		 WHERE UUID_TASK_H = :uuidTaskHSource
	</sql-query>
	
	<sql-query name="services.common.task.lastFormVersion">
		<query-param name="uuidForm" type="long" />
		select max(FORM_VERSION) from MS_FORMHISTORY
			where UUID_FORM = :uuidForm
	</sql-query>
		<sql-query name="service.common.task.getQuestionCheckSumQSet">
		<query-param name="uuidCollectionTag" type="long" />
		<query-param name="uuidForm" type="long" />
		<query-param name="uuidMsSubsystem" type="long" />
			<query-param name="formVersion" type="string" />
	select  
		mqs.uuid_question, mqs.question_label
		from MS_FORMHISTORY mfh join MS_FORMQUESTIONSET mqs on mfh.UUID_FORM_HISTORY = mqs.UUID_FORM_HISTORY
	where mqs.uuid_form = :uuidForm
		and mqs.uuid_ms_subsystem = :uuidMsSubsystem
		and mqs.uuid_collection_tag = :uuidCollectionTag
		and mfh.FORM_VERSION = :formVersion
	</sql-query>
	
	<sql-query name="services.common.task.insertNewTaskArchive">
		<query-param name="uuidTaskHMobile" type="string"/>
		<query-param name="uuidTaskHWeb" type="long"/>
		<query-param name="callerId" type="string"/>
		INSERT INTO TR_NEWTASK_ARCHIVE
		  (UUID_TASK_H_MOBILE, UUID_TASK_H_WEB, DTM_CRT, USR_CRT)
		VALUES
		  (:uuidTaskHMobile, :uuidTaskHWeb, GETDATE(), :callerId)
	</sql-query>
	
	<sql-query name="services.common.task.checkDuplicateTask">
		<query-param name="uuidTaskHMobile" type="string"/>
		SELECT UUID_TASK_H_WEB as uuid
		  FROM TR_NEWTASK_ARCHIVE
		 WHERE UUID_TASK_H_MOBILE = CAST(:uuidTaskHMobile as UNIQUEIDENTIFIER)
	</sql-query>
	
	<sql-query name="services.common.task.getAnswerCodeByAssetTag">
		<query-param name="uuidTaskH" type="long" />
		<query-param name="uuidAssetTag" type="long" />
		select lov.CODE from TR_TASK_D td with (nolock)
			join MS_QUESTION q with (nolock) on td.UUID_QUESTION = q.UUID_QUESTION
			join MS_ASSETTAG ast with (nolock) on q.UUID_ASSET_TAG = ast.UUID_ASSET_TAG
			join MS_LOV lov with (nolock) on td.LOV_ID = lov.UUID_LOV
			where UUID_TASK_H = :uuidTaskH
				and ast.UUID_ASSET_TAG = :uuidAssetTag
	</sql-query>
	
	<sql-query name="services.common.task.getAnswerCodeByAssetTagName">
		<query-param name="uuidTaskH" type="long" />
		<query-param name="assetTagName" type="string" />
			SELECT lov.CODE 
			FROM   TR_TASK_D td WITH (NOLOCK)
			JOIN   MS_QUESTION q WITH (NOLOCK) ON td.UUID_QUESTION = q.UUID_QUESTION
			JOIN   MS_ASSETTAG ast WITH (NOLOCK) ON q.UUID_ASSET_TAG = ast.UUID_ASSET_TAG
			JOIN   MS_LOV lov WITH (NOLOCK) ON td.LOV_ID = lov.UUID_LOV
			WHERE  UUID_TASK_H = :uuidTaskH
				   AND ast.ASSET_TAG_NAME = :assetTagName
	</sql-query>
	
	<sql-query name="services.common.task.getAnswerCodeByAssetTagNameForFinal">
		<query-param name="uuidTaskH" type="long" />
		<query-param name="assetTagName" type="string" />
			SELECT lov.CODE 
			FROM   FINAL_TR_TASK_D td WITH (NOLOCK)
			JOIN   MS_QUESTION q WITH (NOLOCK) ON td.UUID_QUESTION = q.UUID_QUESTION
			JOIN   MS_ASSETTAG ast WITH (NOLOCK) ON q.UUID_ASSET_TAG = ast.UUID_ASSET_TAG
			JOIN   MS_LOV lov WITH (NOLOCK) ON td.LOV_ID = lov.UUID_LOV
			WHERE  UUID_TASK_H = :uuidTaskH
				   AND ast.ASSET_TAG_NAME = :assetTagName
	</sql-query>
	
	<sql-query name="services.common.task.getListLobNap">
		<query-param name="uuidTaskH" type="long" />
		SELECT UUID_TASK_DETAIL_LOB, QUESTION_ID, mfqs.REF_ID, ISNULL(q.ASSET_DOC_CODE,'') as ASSET_DOC_CODE, IMAGE_PATH, 	
				CASE
					WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NOT NULL) THEN '1'
					WHEN (msan.code_answer_type IN ('016','017','018','021') AND IMAGE_PATH IS NOT NULL) THEN '1'
					WHEN (msan.code_answer_type IN ('016','017','018','021') AND LOB_FILE IS NULL) THEN '0'
					WHEN (msan.code_answer_type = '024') THEN '01'
				ELSE '10' END AS HAS_IMAGE
			FROM TR_TASKDETAILLOB td with (nolock) 
				INNER JOIN TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
				INNER JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = th.UUID_FORM AND mfh.FORM_VERSION = th.FORM_VERSION
				INNER JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
		  						and mfqs.UUID_QUESTION = td.QUESTION_ID
				INNER JOIN MS_ANSWERTYPE msan with (nolock) ON mfqs.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
				INNER JOIN MS_QUESTION q with (nolock) ON mfqs.UUID_QUESTION = q.UUID_QUESTION
			WHERE td.UUID_TASK_H = :uuidTaskH
			 and ((msan.code_answer_type IN ('016','017','018','021') AND IMAGE_PATH IS NOT NULL)
			 OR (msan.code_answer_type IN ('016','017','018','021') AND IMAGE_PATH IS NOT NULL) )
	</sql-query>
	
	<sql-query name="services.common.task.getPilotingCAE">
		<query-param name="branchCode" type="String" />
		SELECT IS_PILOTING_CAE FROM MS_BRANCH with(nolock) where BRANCH_CODE = :branchCode
	</sql-query>
	
</hibernate-mapping>