<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="services.common.user.getListGs">
		<query-param name="applicationType" type="string" />
		select CASE WHEN CHARINDEX('_PRM', GS_CODE)=3 OR GS_CODE LIKE
		'%_ACCURACY' THEN SUBSTRING(GS_CODE, 4, LEN(GS_CODE))
		ELSE GS_CODE END AS GSCODE,
		GS_VALUE, UUID_GENERAL_SETTING
		from AM_GENERALSETTING with (nolock)
		where GS_CODE
		IN
		(:applicationType +'_ACCURACY', :applicationType +'_PRM01_TRCK', :applicationType
		+'_PRM02_TRIN',
		:applicationType +'_PRM08_LGIN', :applicationType +'_PRM03_ASIN', :applicationType
		+'_PRM04_F5IN', :applicationType +'_PRM06_IMGQ', :applicationType
		+'_PRM06_IMGHQ',
		:applicationType +'_PRM12_PART', :applicationType +'_PRM07_VERS', :applicationType
		+'_PRM09_LINK', :applicationType +'_PRM13_DIST',
		:applicationType +'_PRM14_VIB', :applicationType +'_PRM15_TON',
		:applicationType +'_PRM16_ACN','SLA_TIME', :applicationType +'_PRM17_TMLN',
		:applicationType +'_PRM18_LOG',
		:applicationType +'_PRM19_ACC_G',:applicationType +'_PRM20_ACC_Y',:applicationType
		+'_PRM21_TASK_LIST', :applicationType +'_PRM23_LIST', :applicationType +'_PRM24_REF', 
		:applicationType +'_PRM07_VERS_P',:applicationType +'_PRM09_LINK_P',:applicationType +'_PRM07_VERS_PC',
		'MAX_RETRY_DSR', 'HTMLSTYLE_QUESTION_LABEL_REPLACER', 'PLACEHOLDER_QUESTION', 'AUTO_HIDE_QUESTION', 'REF_ID_TTD_MASKAPAI', 
		'MAX_RETRY_INVITATION_ESIGN', 'MAX_RETRY_CHECK_REG_ESIGN', 'MAX_RETRY_VALIDATION_CHECK' ,'MAX_DURATION_VISIT_DATE' )
		and is_active = '1'
		union
		select GS_CODE, GS_VALUE, UUID_GENERAL_SETTING
		from AM_GENERALSETTING with (nolock)
		where is_active = '1' and GS_CODE
		LIKE :applicationType + '%'
		AND GS_CODE NOT LIKE '%_PRM%' AND GS_CODE NOT LIKE '%_ACCURACY'
	</sql-query>

	<sql-query name="services.common.user.getListLoginUser">
		<query-param name="loginId" type="string" />
		select 'flag_job' as "key", ISNULL(JOB_CODE, '') as "value"
		from
		AM_MSUSER amu with (nolock) left join MS_JOB mj with (nolock)
		on
		amu.UUID_JOB = mj.UUID_JOB where LOGIN_ID = :loginId and amu.IS_ACTIVE
		= '1' and amu.IS_DELETED = '0'
		union
		select 'job_description' as "key",
		ISNULL(DESCRIPTION, '') as "value"
		from AM_MSUSER amu with (nolock)
		left join MS_JOB mj with (nolock)
		on amu.UUID_JOB = mj.UUID_JOB where
		LOGIN_ID = :loginId and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'fullname' as "key", ISNULL(FULL_NAME, '') as "value"
		from
		AM_MSUSER amu with (nolock) where LOGIN_ID = :loginId and
		amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'branch_id' as
		"key", ISNULL(BRANCH_CODE, '') as "value"
		from AM_MSUSER amu with
		(nolock) left join MS_BRANCH mb with (nolock) on amu.UUID_BRANCH =
		mb.UUID_BRANCH
		where LOGIN_ID = :loginId and amu.IS_ACTIVE = '1' and
		amu.IS_DELETED = '0'
		union
		select 'branch_name' as "key",
		ISNULL(BRANCH_NAME, '') as "value"
		from AM_MSUSER amu with (nolock)
		left join MS_BRANCH mb with (nolock) on amu.UUID_BRANCH =
		mb.UUID_BRANCH
		where LOGIN_ID = :loginId and amu.IS_ACTIVE = '1' and
		amu.IS_DELETED = '0'
		union
		select 'branch_address' as "key",
		ISNULL(BRANCH_ADDRESS, '') as "value"
		from AM_MSUSER amu with (nolock)
		left join MS_BRANCH mb with (nolock) on amu.UUID_BRANCH =
		mb.UUID_BRANCH
		where LOGIN_ID = :loginId and amu.IS_ACTIVE = '1' and
		amu.IS_DELETED = '0'
		union
		select 'dealer_name' as "key",
		ISNULL(DEALER_NAME, '') as "value"
		from AM_MSUSER amu with (nolock)
		left join MS_DEALER md with (nolock) on amu.UUID_DEALER =
		md.UUID_DEALER
		where LOGIN_ID = :loginId and amu.IS_ACTIVE = '1' and
		amu.IS_DELETED = '0'
		union
		select 'login_id' as "key", ISNULL(LOGIN_ID,
		'') as "value"
		from AM_MSUSER amu with (nolock) where LOGIN_ID =
		:loginId and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select
		'uuid_user' as "key", ISNULL(UUID_MS_USER, '') as "value"
		from
		AM_MSUSER amu with (nolock) where LOGIN_ID = :loginId and
		amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'chg_pwd' as
		"key", ISNULL(CHANGE_PWD_LOGIN, 0) as "value"
		from AM_MSUSER amu with
		(nolock) where lOGIN_ID = :loginId and amu.IS_ACTIVE = '1' and
		amu.IS_DELETED = '0'
		union
		select 'pwd_exp' as "key",
		ISNULL(IS_PASSWORD_EXPIRED, 0) as "value"
		from AM_MSUSER amu with
		(nolock) where lOGIN_ID = :loginId and amu.IS_ACTIVE = '1' and
		amu.IS_DELETED = '0'
		union
		select 'cash_limit' as "key", case when
		ISNULL(amu.cash_limit, 0) > 0 then CONVERT(VARCHAR(10), ISNULL(amu.cash_limit, 0)) else
		CONVERT(VARCHAR(10), ISNULL(msb.CASH_LIMIT_DEFAULT, 0)) end
		as "value"
		from AM_MSUSER amu with (nolock)
		join MS_BRANCH msb with
		(nolock) on amu.UUID_BRANCH = msb.UUID_BRANCH
		where lOGIN_ID = :loginId and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'cash_on_hand' as "key", CONVERT(VARCHAR(10), ISNULL(CASH_ON_HAND, 0)) as
		"value"
		from AM_MSUSER amu with (nolock) where lOGIN_ID = :loginId and
		amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'is_branch' as "key", ISNULL(mj.is_branch, '') as "value"
		from
		AM_MSUSER amu with (nolock) left join MS_JOB mj with (nolock)
		on
		amu.UUID_JOB = mj.UUID_JOB where lOGIN_ID = :loginId and amu.IS_ACTIVE
		= '1' and amu.IS_DELETED = '0'
		union
		select 'uuid_branch' as "key",
		ISNULL(uuid_Branch, 0) as "value"
		from AM_MSUSER amu with (nolock)
		where lOGIN_ID = :loginId and amu.IS_ACTIVE = '1' and amu.IS_DELETED =
		'0'
		union
		select 'uuid_dealer' as "key", ISNULL(uuid_dealer, 0) as
		"value"
		from AM_MSUSER amu with (nolock) where lOGIN_ID = :loginId and
		amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
	</sql-query>

	<sql-query name="services.common.user.getListLoginUserByUuid"> 
 		<query-param name="uuid" type="long" /> 
 		
		select 'flag_job' as "key", ISNULL(JOB_CODE, '') as "value"
		from AM_MSUSER amu with (nolock) left join MS_JOB mj with (nolock)
		on amu.UUID_JOB = mj.UUID_JOB where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'job_description' as "key", ISNULL(DESCRIPTION, '') as "value"
		from AM_MSUSER amu with (nolock) left join MS_JOB mj with (nolock)
		on amu.UUID_JOB = mj.UUID_JOB where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'fullname' as "key", ISNULL(FULL_NAME, '') as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'branch_id' as "key", ISNULL(BRANCH_CODE, '') as "value"
		from AM_MSUSER amu with (nolock) left join MS_BRANCH mb with (nolock) on amu.UUID_BRANCH =
		mb.UUID_BRANCH
		where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'branch_name' as "key", ISNULL(BRANCH_NAME, '') as "value"
		from AM_MSUSER amu with (nolock) left join MS_BRANCH mb with (nolock) on amu.UUID_BRANCH =
		mb.UUID_BRANCH
		where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'branch_address' as "key", ISNULL(BRANCH_ADDRESS, '') as "value"
		from AM_MSUSER amu with (nolock) left join MS_BRANCH mb with (nolock) on amu.UUID_BRANCH =
		mb.UUID_BRANCH
		where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'dealer_name' as "key", ISNULL(DEALER_NAME, '') as "value"
		from AM_MSUSER amu with (nolock) left join MS_DEALER md with (nolock) on amu.UUID_DEALER =
		md.UUID_DEALER
		where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'login_id' as "key", ISNULL(LOGIN_ID, '') as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'uuid_user' as "key", COALESCE(CAST(UUID_MS_USER AS VARCHAR), '', CAST(UUID_MS_USER AS VARCHAR)) as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'chg_pwd' as "key", ISNULL(CHANGE_PWD_LOGIN, 0) as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'pwd_exp' as "key", ISNULL(IS_PASSWORD_EXPIRED, 0) as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'cash_limit' as "key", CAST(CAST(COALESCE(amu.cash_limit, '0', amu.cash_limit) AS BIGINT) AS VARCHAR) as "value"
		from AM_MSUSER amu with (nolock)
		join MS_BRANCH msb with (nolock) on amu.UUID_BRANCH = msb.UUID_BRANCH 
		where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'cash_on_hand' as "key", CAST(CAST(COALESCE(CASH_ON_HAND, '0', amu.cash_limit) AS BIGINT) AS VARCHAR) as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union		
		select 'is_branch' as "key", ISNULL(mj.is_branch, '') as "value"
		from AM_MSUSER amu with (nolock) left join MS_JOB mj with (nolock)
		on amu.UUID_JOB = mj.UUID_JOB where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'uuid_branch' as "key", COALESCE(CAST (uuid_Branch AS VARCHAR), '', CAST (uuid_Branch AS VARCHAR)) as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		select 'uuid_dealer' as "key", COALESCE(CAST (uuid_dealer AS VARCHAR), '', CAST (uuid_dealer AS VARCHAR)) as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		union
		SELECT 'uuid_group' as "key", convert(varchar(1000), ISNULL((SELECT CAST(UUID_MS_GROUP AS VARCHAR) + ';'
        FROM AM_MSUSER amu with (nolock) left join AM_MEMBEROFGROUP member with (nolock) on amu.UUID_MS_USER = member.UUID_MS_USER
       	WHERE amu.UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
      	ORDER BY UUID_MS_GROUP
        FOR XML PATH('')
        ), '') ) as "value"
        UNION
		select 'is_tracking' as "key", ISNULL(IS_TRACKING, '') as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		UNION
		select 'start_time' as "key", ISNULL(TRACKING_START_TIME, '') as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		UNION
		select 'end_time' as "key", ISNULL(TRACKING_END_TIME, '') as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		UNION
		select 'tracking_days' as "key", ISNULL(TRACKING_DAYS, '') as "value"
		from AM_MSUSER amu with (nolock) where UUID_MS_USER = :uuid and amu.IS_ACTIVE = '1' and amu.IS_DELETED = '0'
		UNION
		select 'is_piloting' as "key", ISNULL(msb.IS_PILOTING, '0') as "value"
		from AM_MSUSER amu with (nolock)
		join ms_branch msb with (nolock) on msb.uuid_branch = amu.uuid_branch	
		UNION
		select 'is_piloting_cae' as "key", ISNULL(msb.IS_PILOTING_CAE, '0') as "value"
		from AM_MSUSER amu with (nolock)
		join ms_branch msb with (nolock) on msb.uuid_branch = amu.uuid_branch
		where UUID_MS_USER = :uuid
			and amu.IS_ACTIVE = '1'
			and amu.IS_DELETED = '0'
		UNION
		select 'pushsync_time' as "key", ISNULL(msb.PUSH_SYNC_TIME, (SELECT GS_VALUE FROM AM_GENERALSETTING WITH(NOLOCK) WHERE GS_CODE='MS_PUSHSYNC_TIME' )) as "value"
		from AM_MSUSER amu with (nolock)
			join ms_branch msb with (nolock) on msb.uuid_branch = amu.uuid_branch
		where UUID_MS_USER = :uuid
			and amu.IS_ACTIVE = '1'
			and amu.IS_DELETED = '0'
		UNION
		select 'branch_type' as "key", ISNULL(msb.KONVEN_SYARIAH, '0') as "value"
		from AM_MSUSER amu with (nolock)
		join ms_branch msb with (nolock) on msb.uuid_branch = amu.uuid_branch
		where UUID_MS_USER = :uuid
			and amu.IS_ACTIVE = '1'
			and amu.IS_DELETED = '0'
 	</sql-query> 

	<sql-query name="services.common.user.getLastTimeStamp">
		<query-param name="surveyor" type="string" />
		SELECT top 1 datetime
		FROM tr_locationhistory with (nolock)
		WHERE
		uuid_ms_user = :surveyor
		order by datetime desc
	</sql-query>

	<sql-query name="services.common.user.getCheckInTime">
		<query-param name="userId" type="long" />
		<query-param name="startDate" type="string" />
		<query-param name="endDate" type="string" />
		SELECT datetime_IN
		FROM TR_ATTENDANCE with (nolock)
		WHERE UUID_MS_USER =
		(:userId)
		AND ATTENDANCE_DATE BETWEEN :startDate and :endDate
		AND
		datetime_in IS NOT NULL
	</sql-query>
	
	<sql-query name="services.common.user.getLastTimeStampDataUsage">
		<query-param name="surveyor" type="string" />
		SELECT top 1 isnull(dtm_upd, dtm_crt)
		FROM TR_DATAUSAGELOG with (nolock)
		WHERE uuid_ms_user = :surveyor
		and dtm_crt between convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
		order by isnull(dtm_upd, dtm_crt) desc
	</sql-query>
	<sql-query name="services.common.user.getDataUsageLog">
		<query-param name="surveyor" type="string" />
		SELECT top 1 SEQNO
		FROM TR_DATAUSAGELOG with (nolock)
		WHERE uuid_ms_user = :surveyor
		and dtm_crt between convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
		order by isnull(dtm_upd, dtm_crt) desc
	</sql-query>

	<sql-query name="services.common.user.getListPath">
		<query-param name="uuidArea" type="string" />
		SELECT UUID_AREA, SEQUENCE, LATITUDE, LONGITUDE FROM
		MS_AREAPATH with
		(nolock) WHERE UUID_AREA = :uuidArea
		ORDER BY SEQUENCE
	</sql-query>

	<sql-query name="services.common.user.getUuidAttendance">
		<query-param name="userId" type="long" />
		<query-param name="startDate" type="string" />
		<query-param name="endDate" type="string" />
		SELECT uuid_attendance
		FROM TR_ATTENDANCE with (nolock)
		WHERE
		UUID_MS_USER = (:userId)
		AND ATTENDANCE_DATE BETWEEN :startDate and
		:endDate
		AND datetime_in IS NOT NULL
	</sql-query>
	<sql-query name="services.common.user.getLastAttendance">
		<query-param name="userId" type="string" />
		SELECT TOP 1 ATTENDANCE_DATE FROM TR_ATTENDANCE WITH(NOLOCK)
		WHERE UUID_MS_USER = :userId
		ORDER BY DTM_CRT DESC
	</sql-query>
	<!-- attendance out exclude untuk sementara <sql-query name="services.common.user.countAbsenKeluar"> 
		<query-param name="userId" type="string"/> <query-param name="timestamp" 
		type="string"/> SELECT COUNT(1) FROM TR_ATTENDANCE WHERE UUID_MS_USER = (:userId) 
		AND CONVERT(VARCHAR(10),attendance_date,103) = CONVERT(VARCHAR(10),:timestamp,103) 
		AND datetime_out IS NOT NULL </sql-query> attendance out -->
</hibernate-mapping>