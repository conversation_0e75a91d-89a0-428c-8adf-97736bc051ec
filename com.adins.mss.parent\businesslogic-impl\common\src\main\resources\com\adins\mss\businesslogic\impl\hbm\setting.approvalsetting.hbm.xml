<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="setting.approvalsetting.listApprovalSettingOts">
		SELECT msa.UUID_SETTING_APPROVAL_OTS, mf.FORM_NAME, 
			msa.IS_APPROVAL_ON_WOM, msa.IS_APPROVAL_ON_DE, mj.DESCRIPTION, msa.FLAG_SOURCE, mjAssign.DESCRIPTION as descAssign
		FROM MS_SETTINGAPPROVALOTS msa with (nolock) 
			JOIN MS_FORM mf with (nolock) ON msa.UUID_FORM = mf.UUID_FORM 
			JOIN MS_JOB mj with (nolock) ON msa.UUID_JOB = mj.UUID_JOB
			LEFT JOIN MS_JOB mjAssign with (nolock) ON mjAssign.UUID_JOB = msa.UUID_JOB_ASSIGN
		ORDER BY mf.FORM_NAME ASC
	</sql-query>
	<sql-query name="setting.approvalsetting.cntListApprovalSettingOts">
		SELECT COUNT(msa.UUID_SETTING_APPROVAL_OTS)
		FROM MS_SETTINGAPPROVALOTS msa with (nolock)
	</sql-query>
</hibernate-mapping>