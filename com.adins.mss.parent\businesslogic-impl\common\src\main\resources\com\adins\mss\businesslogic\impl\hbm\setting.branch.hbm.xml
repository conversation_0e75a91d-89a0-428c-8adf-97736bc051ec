<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="setting.branch.deleteAreaPath">
		<query-param name="uuidArea" type="string" />
		DELETE FROM MS_AREAPATH
		WHERE UUID_AREA = :uuidArea
	</sql-query>
	
	<sql-query name="setting.branch.cekUserExist">
		<query-param name="uuidBranch" type="string" />
		SELECT COUNT(1) 
		FROM AM_MSUSER with (nolock) 
		WHERE UUID_BRANCH = :uuidBranch
	</sql-query>
	
	<sql-query name="setting.branch.cekHasChild">
		<query-param name="uuidBranch" type="string" />
		SELECT COUNT(1) 
		FROM MS_BRANCH with (nolock) 
		WHERE PARENT_ID = :uuidBranch
	</sql-query>
	
	<sql-query name="setting.branch.deleteZipCode">
		<query-param name="uuidBranch" type="string" />
		DELETE FROM MS_ZIPCODEOFBRANCH
		WHERE UUID_BRANCH = :uuidBranch
	</sql-query>
	
	<sql-query name="setting.branch.getSubBranchArea">
		<query-param name="uuidBranch" type="string"/>
		select msb.UUID_BRANCH, msa.UUID_AREA, msa.AREA_TYPE_CODE, 
			msa.RADIUS, msa.LATITUDE, msa.LONGITUDE
		from MS_BRANCH msb with (nolock) join MS_AREAOFBRANCH msab with (nolock)
			on msb.uuid_branch = msab.UUID_BRANCH 
			join MS_AREA msa with (nolock)
			on msa.UUID_AREA = msab.UUID_AREA 
		where msb.PARENT_ID = :uuidBranch
	</sql-query>
	
	<sql-query name="setting.branch.getUserBranchArea">
		<query-param name="uuidBranch" type="string"/>
		select msb.UUID_BRANCH, msa.UUID_AREA, msa.AREA_TYPE_CODE, 
			msa.RADIUS, msa.LATITUDE, msa.LONGITUDE 
		from am_msuser msu with (nolock)
			join ms_branch msb with (nolock) on msb.uuid_branch = msu.uuid_branch
			join ms_areaofuser msaou with (nolock) on msu.uuid_ms_user = msaou.uuid_ms_user
			join MS_AREA msa with (nolock) on msa.UUID_AREA = msaou.UUID_AREA
		where msu.uuid_branch = :uuidBranch
	</sql-query>
	
	<sql-query name="setting.branch.getBranchArea">
		<query-param name="uuidBranch" type="string"/>
		select msb.UUID_BRANCH, msa.UUID_AREA, msa.AREA_TYPE_CODE, 
		msa.RADIUS, msa.LATITUDE, msa.LONGITUDE
		from MS_BRANCH msb with (nolock) join MS_AREAOFBRANCH msab with (nolock)
			on msb.uuid_branch = msab.UUID_BRANCH 
			join MS_AREA msa with (nolock)
			on msa.UUID_AREA = msab.UUID_AREA 
		where msb.UUID_BRANCH = :uuidBranch
	</sql-query>
	
	<sql-query name="setting.branch.getParentBranchArea">
		<query-param name="uuidBranch" type="string"/>
		select msb.UUID_BRANCH, msa.UUID_AREA, msa.AREA_TYPE_CODE, 
			msa.RADIUS, msa.LATITUDE, msa.LONGITUDE
		from MS_BRANCH msb with (nolock) join MS_AREAOFBRANCH msab with (nolock)
			on msb.uuid_branch = msab.UUID_BRANCH 
			join MS_AREA msa with (nolock)
			on msa.UUID_AREA = msab.UUID_AREA 
		where msb.BRANCH_CODE != 'HO' 
			and msb.UUID_BRANCH = (select PARENT_ID from MS_BRANCH where UUID_BRANCH = :uuidBranch)
	</sql-query>
		
	<sql-query name="setting.branch.listBranch">
    <query-param name="branchCode" type="string" />
    <query-param name="branchName" type="string" />
    <query-param name="start" type="string"/>
	<query-param name="end" type="string"/>
	<query-param name="isPiloting" type="string"/>
		WITH N AS (
		  SELECT msb.UUID_BRANCH, msb.PARENT_ID, msb.BRANCH_CODE, msb.BRANCH_NAME, msb.BRANCH_ADDRESS, msb.COLOR,
		  msb.UUID_BRANCH AS HIRARKI,CAST(msb.UUID_BRANCH AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ, 
		  ISNULL(msb.CASH_LIMIT_DEFAULT,0) as CASH_LIMIT_DEFAULT, msb.IS_ACTIVE, msb.IS_PILOTING, msb.PUSH_SYNC_TIME
		  FROM  MS_BRANCH msb with (nolock)
		  WHERE msb.PARENT_ID IS NULL

		  UNION ALL

		  SELECT msb2.UUID_BRANCH, msb2.PARENT_ID, msb2.BRANCH_CODE, msb2.BRANCH_NAME, msb2.BRANCH_ADDRESS, msb2.COLOR,
		  N.HIRARKI, N.HIRARKI2+'/'+CAST(msb2.UUID_BRANCH AS VARCHAR(MAX)), N.SEQ+1,
		  ISNULL(msb2.CASH_LIMIT_DEFAULT,0) as CASH_LIMIT_DEFAULT, msb2.IS_ACTIVE, msb2.IS_PILOTING, msb2.PUSH_SYNC_TIME
		  FROM  MS_BRANCH msb2 with (nolock),N
		  WHERE N.UUID_BRANCH=msb2.PARENT_ID
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				Select n.UUID_BRANCH, n.BRANCH_CODE, n.BRANCH_NAME, n.PARENT_ID, d.BRANCH_NAME PARENT_NAME,n.BRANCH_ADDRESS,coalesce(n.COLOR,'')as COLOR, n.seq level, 
					ROW_NUMBER() OVER (ORDER BY N.HIRARKI, N.HIRARKI2) AS rownum, n.CASH_LIMIT_DEFAULT, n.is_active, n.IS_PILOTING, n.PUSH_SYNC_TIME			
				from N left outer join MS_BRANCH d with (nolock) 
					on n.PARENT_ID=d.UUID_BRANCH
				where lower(n.BRANCH_CODE) like lower('%'+ :branchCode +'%') 
					and lower(n.BRANCH_NAME) like lower('%'+ :branchName +'%')
					and isnull(n.is_piloting, '0') like lower('%'+ :isPiloting +'%') 
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="setting.branch.listBranch2">
    <query-param name="branchCode" type="string" />
    <query-param name="branchName" type="string" />
    <query-param name="start" type="string"/>
	<query-param name="end" type="string"/>
	<query-param name="isPilotingCAE" type="string"/>
		WITH N AS (
		  SELECT msb.UUID_BRANCH, msb.PARENT_ID, msb.BRANCH_CODE, msb.BRANCH_NAME, msb.BRANCH_ADDRESS, msb.COLOR,
		  msb.UUID_BRANCH AS HIRARKI,CAST(msb.UUID_BRANCH AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ, 
		  ISNULL(msb.CASH_LIMIT_DEFAULT,0) as CASH_LIMIT_DEFAULT, msb.IS_ACTIVE, msb.IS_PILOTING_CAE, msb.PUSH_SYNC_TIME
		  FROM  MS_BRANCH msb with (nolock)
		  WHERE msb.PARENT_ID IS NULL

		  UNION ALL

		  SELECT msb2.UUID_BRANCH, msb2.PARENT_ID, msb2.BRANCH_CODE, msb2.BRANCH_NAME, msb2.BRANCH_ADDRESS, msb2.COLOR,
		  N.HIRARKI, N.HIRARKI2+'/'+CAST(msb2.UUID_BRANCH AS VARCHAR(MAX)), N.SEQ+1,
		  ISNULL(msb2.CASH_LIMIT_DEFAULT,0) as CASH_LIMIT_DEFAULT, msb2.IS_ACTIVE, msb2.IS_PILOTING_CAE, msb2.PUSH_SYNC_TIME
		  FROM  MS_BRANCH msb2 with (nolock),N
		  WHERE N.UUID_BRANCH=msb2.PARENT_ID
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				Select n.UUID_BRANCH, n.BRANCH_CODE, n.BRANCH_NAME, n.PARENT_ID, d.BRANCH_NAME PARENT_NAME,n.BRANCH_ADDRESS,coalesce(n.COLOR,'')as COLOR, n.seq level, 
					ROW_NUMBER() OVER (ORDER BY N.HIRARKI, N.HIRARKI2) AS rownum, n.CASH_LIMIT_DEFAULT, n.is_active, n.IS_PILOTING_CAE, n.PUSH_SYNC_TIME			
				from N left outer join MS_BRANCH d with (nolock) 
					on n.PARENT_ID=d.UUID_BRANCH
				where lower(n.BRANCH_CODE) like lower('%'+ :branchCode +'%') 
					and lower(n.BRANCH_NAME) like lower('%'+ :branchName +'%')
					and isnull(n.is_piloting_CAE, '0') like lower('%'+ :isPilotingCAE +'%') 
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="setting.branch.listBranchCount">
    <query-param name="branchCode" type="string" />
    <query-param name="branchName" type="string" />
    <query-param name="isPiloting" type="string" />
		WITH N AS (
		  SELECT msb.UUID_BRANCH, msb.PARENT_ID, msb.BRANCH_CODE, msb.BRANCH_NAME, msb.BRANCH_ADDRESS,
		  msb.UUID_BRANCH AS HIRARKI,CAST(msb.UUID_BRANCH AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ,
		  ISNULL(msb.CASH_LIMIT_DEFAULT,0) as CASH_LIMIT_DEFAULT, msb.IS_PILOTING		  
		  FROM  MS_BRANCH msb with (nolock)
		  WHERE msb.PARENT_ID IS NULL

		  UNION ALL

		  SELECT msb2.UUID_BRANCH, msb2.PARENT_ID, msb2.BRANCH_CODE, msb2.BRANCH_NAME, msb2.BRANCH_ADDRESS,
		  N.HIRARKI, N.HIRARKI2+'/'+CAST(msb2.UUID_BRANCH AS VARCHAR(MAX)), N.SEQ+1,
		  ISNULL(msb2.CASH_LIMIT_DEFAULT,0) as CASH_LIMIT_DEFAULT, msb2.IS_PILOTING
		  FROM  MS_BRANCH msb2 with (nolock),N
		  WHERE N.UUID_BRANCH=msb2.PARENT_ID
		)
		Select count(1)
		from N left outer join MS_BRANCH d with (nolock) 
			on n.PARENT_ID=d.UUID_BRANCH
		where lower(n.BRANCH_CODE) like lower('%'+ :branchCode +'%') 
			and lower(n.BRANCH_NAME) like lower('%'+ :branchName +'%')
			and isnull(n.is_piloting, '0') like lower('%'+ :isPiloting +'%') 
	</sql-query>
	
	<sql-query name="setting.branch.listBranchCount2">
    <query-param name="branchCode" type="string" />
    <query-param name="branchName" type="string" />
    <query-param name="isPilotingCAE" type="string" />
		WITH N AS (
		  SELECT msb.UUID_BRANCH, msb.PARENT_ID, msb.BRANCH_CODE, msb.BRANCH_NAME, msb.BRANCH_ADDRESS,
		  msb.UUID_BRANCH AS HIRARKI,CAST(msb.UUID_BRANCH AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ,
		  ISNULL(msb.CASH_LIMIT_DEFAULT,0) as CASH_LIMIT_DEFAULT, msb.IS_PILOTING_CAE		  
		  FROM  MS_BRANCH msb with (nolock)
		  WHERE msb.PARENT_ID IS NULL

		  UNION ALL

		  SELECT msb2.UUID_BRANCH, msb2.PARENT_ID, msb2.BRANCH_CODE, msb2.BRANCH_NAME, msb2.BRANCH_ADDRESS,
		  N.HIRARKI, N.HIRARKI2+'/'+CAST(msb2.UUID_BRANCH AS VARCHAR(MAX)), N.SEQ+1,
		  ISNULL(msb2.CASH_LIMIT_DEFAULT,0) as CASH_LIMIT_DEFAULT, msb2.IS_PILOTING_CAE
		  FROM  MS_BRANCH msb2 with (nolock),N
		  WHERE N.UUID_BRANCH=msb2.PARENT_ID
		)
		Select count(1)
		from N left outer join MS_BRANCH d with (nolock) 
			on n.PARENT_ID=d.UUID_BRANCH
		where lower(n.BRANCH_CODE) like lower('%'+ :branchCode +'%') 
			and lower(n.BRANCH_NAME) like lower('%'+ :branchName +'%')
			and isnull(n.is_piloting_CAE, '0') like lower('%'+ :isPilotingCAE +'%') 
	</sql-query>

</hibernate-mapping>