<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="setting.cms.listStok">
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
		  SELECT mst.UUID_STK_PROMO, mst.PROMO_NAME, mst.PROMO_SHORT_DESC, mst.STARTEFF_DATE, mst.parent_promo_id ,
			1 as SEQ, MST.ENDEFF_DATE, 
			CAST(CONVERT(varchar(8),mst.STARTEFF_DATE, 112) AS VARCHAR(MAX)) AS ORDERPARENT, mst.IS_BRANCH,
			mst.UUID_STK_PROMO AS HIRARKI,CAST(mst.UUID_STK_PROMO AS VARCHAR(MAX)) AS HIRARKI2
		  FROM  MS_STKPROMO mst with (nolock)
		  WHERE mst.parent_promo_id IS NULL 

		  UNION ALL

		  SELECT mst2.UUID_STK_PROMO, mst2.PROMO_NAME, mst2.PROMO_SHORT_DESC, mst2.STARTEFF_DATE, mst2.parent_promo_id ,
			N.SEQ+1, MST2.ENDEFF_DATE,
			N.ORDERPARENT, mst2.IS_BRANCH,
			N.HIRARKI, N.HIRARKI2+'/'+CAST(mst2.UUID_STK_PROMO AS VARCHAR(MAX))
		  FROM  MS_STKPROMO mst2 with (nolock), N
		  WHERE N.UUID_STK_PROMO=mst2.parent_promo_id 
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				Select n.UUID_STK_PROMO, n.PROMO_NAME, n.PROMO_SHORT_DESC, n.STARTEFF_DATE, n.parent_promo_id ,
					d.PROMO_NAME parent, n.seq level,  
					ROW_NUMBER() OVER (ORDER BY N.HIRARKI, N.HIRARKI2, N.ORDERPARENT DESC, N.SEQ ASC, N.STARTEFF_DATE DESC) AS rownum, 
					N.ENDEFF_DATE, N.IS_BRANCH
				from N left outer join MS_STKPROMO d with (nolock) 
					on n.parent_promo_id=d.UUID_STK_PROMO 
				WHERE N.IS_BRANCH = '1'
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="setting.cms.listStokByDealer">
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
		  SELECT mst.UUID_STK_PROMO, mst.PROMO_NAME, mst.PROMO_SHORT_DESC, mst.STARTEFF_DATE, mst.parent_promo_id ,
			1 as SEQ, MST.ENDEFF_DATE, 
			CAST(CONVERT(varchar(8),mst.STARTEFF_DATE, 112) AS VARCHAR(MAX)) AS ORDERPARENT, mst.IS_BRANCH,
			mst.UUID_STK_PROMO AS HIRARKI,CAST(mst.UUID_STK_PROMO AS VARCHAR(MAX)) AS HIRARKI2
		  FROM  MS_STKPROMO mst with (nolock)
		  WHERE mst.parent_promo_id IS NULL 

		  UNION ALL

		  SELECT mst2.UUID_STK_PROMO, mst2.PROMO_NAME, mst2.PROMO_SHORT_DESC, mst2.STARTEFF_DATE, mst2.parent_promo_id ,
			N.SEQ+1, MST2.ENDEFF_DATE,
			N.ORDERPARENT, mst2.IS_BRANCH,
			N.HIRARKI, N.HIRARKI2+'/'+CAST(mst2.UUID_STK_PROMO AS VARCHAR(MAX))
		  FROM  MS_STKPROMO mst2 with (nolock), N
		  WHERE N.UUID_STK_PROMO=mst2.parent_promo_id 
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				Select n.UUID_STK_PROMO, n.PROMO_NAME, n.PROMO_SHORT_DESC, n.STARTEFF_DATE, n.parent_promo_id ,
					d.PROMO_NAME parent, n.seq level,  
					ROW_NUMBER() OVER (ORDER BY N.HIRARKI, N.HIRARKI2, N.ORDERPARENT DESC, N.SEQ ASC, N.STARTEFF_DATE DESC) AS rownum, 
					N.ENDEFF_DATE, N.IS_BRANCH
				from N left outer join MS_STKPROMO d with (nolock) 
				on n.parent_promo_id=d.UUID_STK_PROMO 
				WHERE N.IS_BRANCH = '0'
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="setting.cms.cntStok">
		WITH N AS (
		  SELECT mst.UUID_STK_PROMO, mst.PROMO_NAME, mst.PROMO_SHORT_DESC, mst.STARTEFF_DATE, mst.parent_promo_id ,
			1 as SEQ, MST.ENDEFF_DATE, 
			CAST(CONVERT(varchar(8),mst.STARTEFF_DATE, 112) AS VARCHAR(MAX)) AS ORDERPARENT, mst.IS_BRANCH,
			mst.UUID_STK_PROMO AS HIRARKI,CAST(mst.UUID_STK_PROMO AS VARCHAR(MAX)) AS HIRARKI2
		  FROM  MS_STKPROMO mst with (nolock)
		  WHERE mst.parent_promo_id IS NULL 

		  UNION ALL

		  SELECT mst2.UUID_STK_PROMO, mst2.PROMO_NAME, mst2.PROMO_SHORT_DESC, mst2.STARTEFF_DATE, mst2.parent_promo_id ,
			N.SEQ+1, MST2.ENDEFF_DATE,
			N.ORDERPARENT, mst2.IS_BRANCH,
			N.HIRARKI, N.HIRARKI2+'/'+CAST(mst2.UUID_STK_PROMO AS VARCHAR(MAX))
		  FROM  MS_STKPROMO mst2 with (nolock), N
		  WHERE N.UUID_STK_PROMO=mst2.parent_promo_id 
		)
		Select count(1)
		from N left outer join MS_STKPROMO d with (nolock) 
			on n.parent_promo_id=d.UUID_STK_PROMO 
		WHERE N.IS_BRANCH = '1'
	</sql-query>
	<sql-query name="setting.cms.cntStokByDealer">
		WITH N AS (
		  SELECT mst.UUID_STK_PROMO, mst.PROMO_NAME, mst.PROMO_SHORT_DESC, mst.STARTEFF_DATE, mst.parent_promo_id ,
			1 as SEQ, MST.ENDEFF_DATE, 
			CAST(CONVERT(varchar(8),mst.STARTEFF_DATE, 112) AS VARCHAR(MAX)) AS ORDERPARENT, mst.IS_BRANCH,
			mst.UUID_STK_PROMO AS HIRARKI,CAST(mst.UUID_STK_PROMO AS VARCHAR(MAX)) AS HIRARKI2
		  FROM  MS_STKPROMO mst with (nolock)
		  WHERE mst.parent_promo_id IS NULL 

		  UNION ALL

		  SELECT mst2.UUID_STK_PROMO, mst2.PROMO_NAME, mst2.PROMO_SHORT_DESC, mst2.STARTEFF_DATE, mst2.parent_promo_id ,
			N.SEQ+1, MST2.ENDEFF_DATE,
			N.ORDERPARENT, mst2.IS_BRANCH,
			N.HIRARKI, N.HIRARKI2+'/'+CAST(mst2.UUID_STK_PROMO AS VARCHAR(MAX))
		  FROM  MS_STKPROMO mst2 with (nolock), N
		  WHERE N.UUID_STK_PROMO=mst2.parent_promo_id 
		)
		Select count(1)
		from N left outer join MS_STKPROMO d with (nolock) 
			on n.parent_promo_id=d.UUID_STK_PROMO 
		WHERE N.IS_BRANCH = '0'
	</sql-query>
		
	<sql-query name="setting.cms.listPromoOfBranch">
	    <query-param name="uuidStkpromos" type="string" />
	    <query-param name="uuidBranches" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		with n as(
			SELECT mb.UUID_BRANCH, mb.BRANCH_CODE, mb.BRANCH_NAME
			FROM MS_PROMOOFBRANCH mpb with (nolock) JOIN MS_BRANCH mb with (nolock)
			on mpb.UUID_BRANCH=mb.UUID_BRANCH
			WHERE mpb.UUID_STK_PROMO in ( :uuidStkpromos )
			UNION
			select UUID_BRANCH, BRANCH_CODE, BRANCH_NAME
			FROM MS_BRANCH with (nolock)
			WHERE UUID_BRANCH in ( :uuidBranches )
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select UUID_BRANCH, BRANCH_CODE, BRANCH_NAME, ROW_NUMBER() OVER (ORDER BY BRANCH_CODE, BRANCH_NAME) AS rownum 
				from n
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="setting.cms.listPromoOfDealer">
	    <query-param name="uuidStkpromos" type="string" />
	    <query-param name="uuidDealers" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		with n as(
			SELECT md.UUID_DEALER, md.DEALER_CODE, md.DEALER_NAME
			FROM MS_PROMOOFDEALER mpd with (nolock) JOIN MS_DEALER md with (nolock)
			on mpd.UUID_DEALER=md.UUID_DEALER
			WHERE mpd.UUID_STK_PROMO in ( :uuidStkpromos )
			UNION
			select UUID_DEALER, DEALER_CODE, DEALER_NAME
			FROM MS_DEALER with (nolock)
			WHERE UUID_DEALER in ( :uuidDealers )
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select UUID_DEALER, DEALER_CODE, DEALER_NAME, ROW_NUMBER() OVER (ORDER BY DEALER_CODE, DEALER_NAME) AS rownum 
				from n
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="setting.cms.cntPromoOfBranch">
	    <query-param name="uuidStkpromos" type="string" />
	    <query-param name="uuidBranches" type="string" />
		with n as(
			SELECT mb.UUID_BRANCH, mb.BRANCH_CODE, mb.BRANCH_NAME
			FROM MS_PROMOOFBRANCH mpb with (nolock) JOIN MS_BRANCH mb with (nolock)
			on mpb.UUID_BRANCH=mb.UUID_BRANCH
			WHERE mpb.UUID_STK_PROMO in ( :uuidStkpromos )
			UNION
			select UUID_BRANCH, BRANCH_CODE, BRANCH_NAME
			FROM MS_BRANCH with (nolock)
			WHERE UUID_BRANCH in ( :uuidBranches )
		)
		select count(UUID_BRANCH) 
		from n
	</sql-query>
	<sql-query name="setting.cms.cntPromoOfDealer">
	    <query-param name="uuidStkpromos" type="string" />
	    <query-param name="uuidDealers" type="string" />
		with n as(
			SELECT md.UUID_DEALER, md.DEALER_CODE, md.DEALER_NAME
			FROM MS_PROMOOFDEALER mpd with (nolock) JOIN MS_DEALER md with (nolock)
			on mpd.UUID_DEALER=md.UUID_DEALER
			WHERE mpd.UUID_STK_PROMO in ( :uuidStkpromos )
			UNION
			select UUID_DEALER, DEALER_CODE, DEALER_NAME
			FROM MS_DEALER with (nolock)
			WHERE UUID_DEALER in ( :uuidDealers )
		)
		select count(UUID_DEALER) 
		from n
	</sql-query>
	<sql-query name="setting.cms.updateSequence">
	    <query-param name="uuidStkpromo" type="string" />
	    <query-param name="uuidContent" type="string" />
		update MS_STKPROMOCONTENT
		set SEQUENCE =  SEQUENCE - 10
		WHERE UUID_STK_PROMO = :uuidStkpromo 
		and SEQUENCE > (select SEQUENCE from MS_STKPROMOCONTENT 
						where UUID_STK_PROMO_CONTENT = :uuidContent )
	</sql-query>
	
	<sql-query name="setting.cms.deleteBranchToChild">
	    <query-param name="uuidStkpromo" type="string" />
	    <query-param name="uuidBranch" type="string" />
		WITH N AS (
		  SELECT mst.UUID_STK_PROMO, mst.PROMO_NAME, mst.PROMO_SHORT_DESC, mst.STARTEFF_DATE, mst.parent_promo_id ,
			mst.UUID_STK_PROMO AS HIRARKI,CAST(mst.UUID_STK_PROMO AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ, MST.ENDEFF_DATE
		  FROM  MS_STKPROMO mst with (nolock)
		  WHERE mst.UUID_STK_PROMO = :uuidStkpromo
		  UNION ALL
		  SELECT mst2.UUID_STK_PROMO, mst2.PROMO_NAME, mst2.PROMO_SHORT_DESC, mst2.STARTEFF_DATE, mst2.parent_promo_id ,
			N.HIRARKI, N.HIRARKI2+'/'+CAST(mst2.UUID_STK_PROMO AS VARCHAR(MAX)), N.SEQ+1, MST2.ENDEFF_DATE
		  FROM  MS_STKPROMO mst2 with (nolock),N
		  WHERE N.UUID_STK_PROMO=mst2.parent_promo_id
		)
		delete from MS_PROMOOFBRANCH
		where UUID_BRANCH = :uuidBranch
			and UUID_STK_PROMO in (Select n.UUID_STK_PROMO from N)
	</sql-query>
	
	<sql-query name="setting.cms.deleteDealerToChild">
	    <query-param name="uuidStkpromo" type="string" />
	    <query-param name="uuidDealer" type="string" />
		WITH N AS (
		  SELECT mst.UUID_STK_PROMO, mst.PROMO_NAME, mst.PROMO_SHORT_DESC, mst.STARTEFF_DATE, mst.parent_promo_id ,
		mst.UUID_STK_PROMO AS HIRARKI,CAST(mst.UUID_STK_PROMO AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ, MST.ENDEFF_DATE
		  FROM  MS_STKPROMO mst with (nolock)
		  WHERE mst.UUID_STK_PROMO = :uuidStkpromo

		  UNION ALL

		  SELECT mst2.UUID_STK_PROMO, mst2.PROMO_NAME, mst2.PROMO_SHORT_DESC, mst2.STARTEFF_DATE, mst2.parent_promo_id ,
		N.HIRARKI, N.HIRARKI2+'/'+CAST(mst2.UUID_STK_PROMO AS VARCHAR(MAX)), N.SEQ+1, MST2.ENDEFF_DATE
		  FROM  MS_STKPROMO mst2 with (nolock),N
		  WHERE N.UUID_STK_PROMO=mst2.parent_promo_id
		)
		delete from MS_PROMOOFDEALER
		where UUID_DEALER = :uuidDealer
			and UUID_STK_PROMO in 
				(Select n.UUID_STK_PROMO from N )
	</sql-query>
	<sql-query name="setting.cms.valDelPromo">
	    <query-param name="uuidStkpromo" type="string" />
		select msp.UUID_STK_PROMO, mpb.UUID_BRANCH from MS_STKPROMO msp with (nolock)
		join MS_PROMOOFBRANCH mpb with (nolock) on msp.UUID_STK_PROMO = mpb.UUID_STK_PROMO
		where mpb.UUID_BRANCH not in 
			(select UUID_BRANCH from MS_PROMOOFBRANCH 
			where UUID_STK_PROMO = msp.PARENT_PROMO_ID)
		and msp.UUID_STK_PROMO = :uuidStkPromo
	</sql-query>
	<sql-query name="setting.cms.valDelPromoByDealer">
	    <query-param name="uuidStkpromo" type="string" />
		select msp.UUID_STK_PROMO, mpd.UUID_DEALER from MS_STKPROMO msp with (nolock)
		join MS_PROMOOFDEALER mpd with (nolock) on msp.UUID_STK_PROMO = mpd.UUID_STK_PROMO
		where mpd.UUID_DEALER not in 
			(select UUID_DEALER from MS_PROMOOFDEALER 
			where UUID_STK_PROMO = msp.PARENT_PROMO_ID)
		and msp.UUID_STK_PROMO = :uuidStkPromo
	</sql-query>
	<sql-query name="setting.cms.getChildPromo">
	    <query-param name="uuidStkPromo" type="string" />
		WITH n(UUID_STK_PROMO,STARTEFF_DATE, ENDEFF_DATE,IS_ACTIVE, level) AS 
		(
			SELECT UUID_STK_PROMO,STARTEFF_DATE, ENDEFF_DATE, IS_ACTIVE , 1 level
			FROM MS_STKPROMO with (nolock)
			WHERE UUID_STK_PROMO = :uuidStkPromo and IS_ACTIVE = '1'
						 UNION ALL
			SELECT nplus1.UUID_STK_PROMO, nplus1.STARTEFF_DATE, nplus1.ENDEFF_DATE, nplus1.IS_ACTIVE,n.level+1
			FROM MS_STKPROMO as nplus1 with (nolock), n
			WHERE n.UUID_STK_PROMO = nplus1.PARENT_PROMO_ID  and nplus1.IS_ACTIVE = '1'
		 )
		 SELECT UUID_STK_PROMO, STARTEFF_DATE, ENDEFF_DATE, n.IS_ACTIVE ,level as userLevel
		FROM n
		where UUID_STK_PROMO != :uuidStkPromo
	</sql-query>
	<sql-query name="setting.cms.updateStart">
	    <query-param name="uuidStkpromo" type="string" />
	    <query-param name="startEffDate" type="string" />
		UPDATE MS_STKPROMO SET STARTEFF_DATE = :startEffDate WHERE UUID_STK_PROMO IN (:uuidStkpromo)
	</sql-query>
	<sql-query name="setting.cms.updateEnd">
	    <query-param name="uuidStkpromo" type="string" />
	    <query-param name="endEffDate" type="string" />
		UPDATE MS_STKPROMO SET ENDEFF_DATE = :endEffDate WHERE UUID_STK_PROMO IN (:uuidStkpromo)
	</sql-query>
	<sql-query name="setting.cms.deleteContentPromo">
	    <query-param name="uuidStkpromo" type="string" />
		delete from Ms_Stkpromocontent where uuid_stk_promo = :uuidStkpromo
	</sql-query>
</hibernate-mapping>