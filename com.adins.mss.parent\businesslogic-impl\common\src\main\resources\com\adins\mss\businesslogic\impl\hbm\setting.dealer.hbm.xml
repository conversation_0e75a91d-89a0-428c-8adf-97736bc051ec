<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="setting.dealer.listDealer">
	    <query-param name="dealerName" type="string" />
	    <query-param name="groupDealer" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		WITH N AS (
		  SELECT msd.UUID_DEALER, msd.PARENT_ID, msd.DEALER_NAME, msd.DEALER_ADDRESS, 
		  		msd.ORDER_TARGET, msd.DEALER_CODE, msd.UUID_DEALER AS HIRARKI,
		  		CAST(msd.DEALER_NAME AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ
		  FROM  MS_DEALER msd with (nolock)
		  WHERE msd.PARENT_ID IS NULL

		  UNION ALL

		  SELECT msd2.UUID_DEALER, msd2.PARENT_ID, msd2.DEALER_NAME, msd2.DEALER_ADDRESS, 
		  		msd2.ORDER_TARGET, msd2.DEALER_CODE, N.HIRARKI, 
		  		N.HIRARKI2+'/'+CAST(msd2.DEALER_NAME AS VARCHAR(MAX)), N.SEQ+1
		  FROM  MS_DEALER msd2 with (nolock),N
		  WHERE N.UUID_DEALER=msd2.PARENT_ID
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				Select n.UUID_DEALER, n.dealer_name, n.dealer_address, n.parent_id, 
						d.DEALER_NAME parentName, n.ORDER_TARGET, n.seq level, 
						ROW_NUMBER() OVER (ORDER BY N.HIRARKI, N.HIRARKI2) AS rownum, N.DEALER_CODE
				from N left outer join MS_DEALER d with (nolock) on n.PARENT_ID=d.UUID_DEALER
				where lower(n.dealer_name) like lower('%'+ :dealerName +'%') 
				and lower(coalesce(d.DEALER_NAME,'')) like lower('%'+ :groupDealer +'%') 
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="setting.dealer.cntDealer">
	    <query-param name="dealerName" type="string" />
	    <query-param name="groupDealer" type="string" />
		WITH N AS (
		  SELECT msd.UUID_DEALER, msd.PARENT_ID, msd.DEALER_NAME, msd.DEALER_ADDRESS, msd.ORDER_TARGET,
				msd.UUID_DEALER AS HIRARKI,CAST(msd.DEALER_NAME AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ
		  FROM  MS_DEALER msd with (nolock)
		  WHERE msd.PARENT_ID IS NULL

		  UNION ALL

		  SELECT msd2.UUID_DEALER, msd2.PARENT_ID, msd2.DEALER_NAME, msd2.DEALER_ADDRESS, msd2.ORDER_TARGET,
				N.HIRARKI, N.HIRARKI2+'/'+CAST(msd2.DEALER_NAME AS VARCHAR(MAX)), N.SEQ+1
		  FROM  MS_DEALER msd2 with (nolock),N
		  WHERE N.UUID_DEALER=msd2.PARENT_ID
		)
		Select count(n.UUID_DEALER)
		from N left outer join MS_DEALER d with (nolock) on n.PARENT_ID=d.UUID_DEALER
		where lower(n.dealer_name) like lower('%'+ :dealerName +'%') 
		and lower(coalesce(d.DEALER_NAME,'')) like lower('%'+ :groupDealer +'%') 
	</sql-query>
	<sql-query name="setting.dealer.listBranch">
	    <query-param name="uuidDealer" type="string" />
	    <query-param name="branchCode" type="string" />
	    <query-param name="branchName" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
	    select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
			    SELECT MB.UUID_BRANCH, MB.BRANCH_CODE, MB.BRANCH_NAME, 
			    	(case when MDB.UUID_BRANCH IS null then 'false' else 'true' end) as FLAG
			    	, ROW_NUMBER() OVER (ORDER BY MB.BRANCH_CODE,  MB.BRANCH_NAME) AS rownum 
		    	FROM ms_branch MB with (nolock) LEFT JOIN 
		    		MS_DEALEROFBRANCH MDB with (nolock) ON MB.UUID_BRANCH=MDB.UUID_BRANCH
		    	AND MDB.UUID_DEALER = :uuidDealer
		    	where mdb.UUID_BRANCH is null and MB.BRANCH_CODE LIKE '%'+ :branchCode +'%' 
		    	AND MB.BRANCH_NAME LIKE '%'+ :branchName +'%'
		  	) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="setting.dealer.cntBranch">
	    <query-param name="uuidDealer" type="string" />
	    <query-param name="branchCode" type="string" />
	    <query-param name="branchName" type="string" />
	    SELECT count(mb.UUID_BRANCH)
    	FROM ms_branch MB with (nolock) LEFT JOIN 
    		MS_DEALEROFBRANCH MDB with (nolock) ON MB.UUID_BRANCH=MDB.UUID_BRANCH
    	AND MDB.UUID_DEALER = :uuidDealer
    	where mdb.UUID_BRANCH is null and MB.BRANCH_CODE LIKE '%'+ :branchCode +'%' 
    	AND MB.BRANCH_NAME LIKE '%'+ :branchName +'%'
	</sql-query>
</hibernate-mapping>