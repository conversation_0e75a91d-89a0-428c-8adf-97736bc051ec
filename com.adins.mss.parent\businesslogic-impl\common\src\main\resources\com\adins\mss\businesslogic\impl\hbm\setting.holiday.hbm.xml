<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD 3.0//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="setting.holiday.listHolidaySelect">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select uuid_branch, branch_code, branch_name, 
					ROW_NUMBER() OVER (ORDER BY BRANCH_CODE) AS rownum 
				from ms_branch with (nolock)
				where uuid_holiday_h is null
				and lower(branch_code) like '%' + :branchCode + '%'
				and lower(branch_name) like '%' + :branchName + '%'
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="setting.holiday.cntListBranchNull">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
			select count(uuid_branch) 
			from ms_branch with (nolock)
			where uuid_holiday_h is null
			and lower(branch_code) like '%' + :branchCode + '%'
			and lower(branch_name) like '%' + :branchName + '%'
	</sql-query>

	<sql-query name="setting.holiday.listTahunDb">
		<query-param name="uuidHolidayH" type="string"/>
		select distinct(cast(year(h_date) as varchar(4))) as tahun, 1 as coba
		from ms_holiday_d with (nolock)
		where uuid_holiday_h = :uuidHolidayH
		order by tahun
	</sql-query>
	
	<sql-query name="setting.holiday.holidayDateExist">
		<query-param name="uuidHolidayH" type="string"/>
		<query-param name="dateAdd" type="string"/>
		select CAST(UUID_HOLIDAY_D as varchar)
		from ms_holiday_d with (nolock)
		where uuid_holiday_h = :uuidHolidayH
		and Convert(varchar(30),h_date, 121) = :dateAdd + ' 00:00:00.000'
	</sql-query>
	
	<sql-query name="setting.holiday.yearExist">
		<query-param name="uuidHolidayH" type="string"/>
		<query-param name="startYear" type="string"/>
		<query-param name="endYear" type="string"/>
		select COUNT(1)
		from ms_holiday_d with (nolock)
		where uuid_holiday_h = :uuidHolidayH
		and (left(Convert(varchar(30),h_date, 121), 4)) between :startYear and :endYear
	</sql-query>
	
	<sql-query name="setting.holiday.getHolidayByYear">
		<query-param name="tahun" type="string"/>
		<query-param name="uuidHolidayH" type="string"/>
		select distinct(flag_day) as hari, 1 as coba
		from ms_holiday_d with (nolock)
		where uuid_holiday_h = :uuidHolidayH
		and flag_holiday = 0
		and year(h_date) = :tahun
		order by hari
	</sql-query>
	
	<sql-query name="setting.holiday.getAllDateByYear">
		<query-param name="tahun" type="string"/>
		<query-param name="uuidHolidayH" type="string"/>
		select UUID_HOLIDAY_D, FLAG_HOLIDAY, FLAG_DAY
		from ms_holiday_d with (nolock)
		where uuid_holiday_h = :uuidHolidayH
		and year(h_date) = :tahun
		and flag_holiday != 2
	</sql-query>
	<sql-query name="setting.holiday.holidayCodeValidity">
		<query-param name="holidayCode" type="string"/>
		SELECT COUNT(1) 
		FROM MS_HOLIDAY_H with (nolock) 
		WHERE HOLIDAY_CODE = :holidayCode
	</sql-query>
	<sql-query name="setting.holiday.deleteHolidayDetail">
		<query-param name="uuidHolidayH" type="string"/>
		DELETE MS_HOLIDAY_D 
		WHERE UUID_HOLIDAY_H = :uuidHolidayH
	</sql-query>
</hibernate-mapping>