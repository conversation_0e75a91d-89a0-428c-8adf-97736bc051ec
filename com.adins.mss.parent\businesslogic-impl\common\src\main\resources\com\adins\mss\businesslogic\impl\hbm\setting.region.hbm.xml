<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="setting.region.listBranch">
	    <query-param name="branchName" type="string" />
	    <query-param name="branchCode" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		 select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (SELECT MSBR.UUID_BRANCH, MSBR.BRANCH_CODE, MSBR.BRANCH_NAME, 
    				(case when MSROB.UUID_BRANCH IS null then 'false' else 'true' end) as FLAG,
			    	ROW_NUMBER() OVER (ORDER BY MSBR.BRANCH_CODE) AS rownum 
		    	FROM ms_branch MSBR with (nolock) 
		    	LEFT JOIN MS_REGIONOFBRANCH MSROB with (nolock) 
		    	ON MSBR.UUID_BRANCH=MSROB.UUID_BRANCH
		    	where MSROB.UUID_BRANCH is null 
		    	AND lower(MSBR.BRANCH_CODE) LIKE '%'+ :branchCode +'%' 
		    	AND lower(MSBR.BRANCH_NAME) LIKE '%'+ :branchName +'%'
		  	) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="setting.region.regionValidity">
		<query-param name="regionId" type="string"/>
		SELECT COUNT(1) 
		FROM MS_REGION with (nolock) 
		WHERE REGION_CODE = :regionId
	</sql-query>
		<sql-query name="setting.region.cntBranch">
	    <query-param name="branchCode" type="string" />
	    <query-param name="branchName" type="string" />
	    SELECT count(MSBR.UUID_BRANCH)
    	FROM ms_branch MSBR with (nolock) 
    	LEFT JOIN MS_REGIONOFBRANCH MSROB with (nolock) 
    	ON MSBR.UUID_BRANCH=MSROB.UUID_BRANCH
    	where MSROB.UUID_BRANCH is null
    	AND lower(MSBR.BRANCH_CODE) LIKE '%'+ :branchCode +'%'
    	AND lower(MSBR.BRANCH_NAME) LIKE '%'+ :branchName +'%'
	</sql-query>
</hibernate-mapping>