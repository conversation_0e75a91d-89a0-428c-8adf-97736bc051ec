<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

<!-- Approval Mobile START -->

	<sql-query name="task.approval.header">
		<query-param name="uuidUser" type="long"/>
			SELECT tskh.UUID_TASK_H as uuidTaskH,
				   tskh.CUSTOMER_NAME as customerName,
				   tskh.CUSTOMER_PHONE as customerPhone,
				   tskh.CUSTOMER_ADDRESS as customerAddress,
				   tskh.NOTES as notes,
				   cast(tskh.LATITUDE as varchar) as latitude,
				   cast(tskh.LONGITUDE as varchar) as longitude,
				   tskh.UUID_FORM as schemeId,
				   ISNULL(replace(convert(varchar(10),msf.FORM_LAST_UPDATE,103),'/','')+replace(convert(varchar(8),msf.FORM_LAST_UPDATE,14),':',''), null) as schemeLastUpdate,
				   ISNULL(tskh.APPL_NO, '') as applNo,
				   cast(dbo.F_IS_VERIFICATION(tskh.UUID_TASK_H) as varchar) as isVerification,
				   case when msf.PREPROCESSING_SP is null then '0' else '1' end as isPreviewServer,
				   ISNULL(replace(convert(varchar(10),tskh.DTM_CRT,103),'/','')+replace(convert(varchar(8),tskh.DTM_CRT,14),':',''), null) as dtmCrt,
				   ISNULL(msf.IS_PRINTABLE, '0') as isPrintable,
				   tskh.TASK_ID as taskId,
				   mst.STATUS_CODE as status,
				   tskh.ZIP_CODE as zipCode,
				   ISNULL(replace(convert(varchar(10),tskh.ASSIGN_DATE,103),'/','')+replace(convert(varchar(8),tskh.ASSIGN_DATE,14),':',''), null) as assignDate,
				   CONVERT(varchar(5), tskh.form_version) as form_version
			  FROM TR_TASK_H tskh with (nolock) inner join MS_FORM msf with (nolock) ON tskh.UUID_FORM = msf.UUID_FORM
				   LEFT JOIN (select keyValue as UUID_MS_USER, fullName as FULL_NAME
					        	from getUserByLogin(:uuidUser)
					       	   where keyValue != :uuidUser) msu ON msu.UUID_MS_USER = tskh.UUID_MS_USER
				   INNER JOIN MS_STATUSTASK mst with (nolock) ON tskh.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			 WHERE mst.status_code = 'P'
			 	   AND msf.IS_ACTIVE = '1'
		  ORDER BY tskh.UUID_TASK_H, tskh.DTM_CRT
	</sql-query>
	
	<sql-query name="task.approval.headerByBranch">
		<query-param name="branchId" type="long"/>
		<query-param name="subsystemId" type="long"/>
			SELECT tskh.UUID_TASK_H as uuidTaskH,
				   tskh.CUSTOMER_NAME as customerName,
				   tskh.CUSTOMER_PHONE as customerPhone,
				   tskh.CUSTOMER_ADDRESS as customerAddress,
				   tskh.NOTES as notes,
				   cast(tskh.LATITUDE as varchar) as latitude,
				   cast(tskh.LONGITUDE as varchar) as longitude,
				   tskh.UUID_FORM as schemeId,
				   ISNULL(replace(convert(varchar(10),msf.FORM_LAST_UPDATE,103),'/','')+replace(convert(varchar(8),msf.FORM_LAST_UPDATE,14),':',''), null) as schemeLastUpdate,
				   ISNULL(tskh.APPL_NO, '') as applNo,
				   cast(dbo.F_IS_VERIFICATION(tskh.UUID_TASK_H) as varchar) as isVerification,
				   case when msf.PREPROCESSING_SP is null then '0' else '1' end as isPreviewServer,
				   ISNULL(replace(convert(varchar(10),tskh.DTM_CRT,103),'/','')+replace(convert(varchar(8),tskh.DTM_CRT,14),':',''), null) as dtmCrt,
				   ISNULL(msf.IS_PRINTABLE, '0') as isPrintable,
				   tskh.TASK_ID as taskId,
				   mst.STATUS_CODE as status,
				   tskh.ZIP_CODE as zipCode,
				   ISNULL(replace(convert(varchar(10),tskh.ASSIGN_DATE,103),'/','')+replace(convert(varchar(8),tskh.ASSIGN_DATE,14),':',''), null) as assignDate,
				   CONVERT(varchar(5), tskh.form_version) as form_version
			  FROM TR_TASK_H tskh with (nolock) inner join MS_FORM msf with (nolock) ON tskh.UUID_FORM = msf.UUID_FORM
				   INNER JOIN MS_STATUSTASK mst with (nolock) ON tskh.UUID_STATUS_TASK = mst.UUID_STATUS_TASK AND MST.UUID_MS_SUBSYSTEM = :subsystemId
				   join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)) msb on tskh.UUID_BRANCH = msb.UUID_BRANCH
			 WHERE mst.status_code = 'P'
			 	   AND msf.IS_ACTIVE = '1'
		  ORDER BY tskh.UUID_TASK_H, tskh.DTM_CRT
	</sql-query>
	
	<sql-query name="task.approval.detail">
		<query-param name="uuidTaskH" type="long"/>
			SELECT ttd.QUESTION_TEXT as "key",
			CASE WHEN mat.code_answer_type in ('001','002','003','004','005','013','015','025','026', '046') THEN ISNULL(ttd.FIN_TEXT_ANSWER, ttd.TEXT_ANSWER)
			      WHEN  mat.code_answer_type in ('006','007','008','009','010','011','012')  THEN ISNULL(ttd.FIN_OPTION_TEXT, ttd.OPTION_TEXT)
				  WHEN  mat.code_answer_type in ('024') and (ttd.LATITUDE is not null or ttd.LATITUDE is not null ) 
				  THEN cast(ttd.LATITUDE as varchar) + ',' + cast(ttd.LONGITUDE as varchar)
				   WHEN  mat.code_answer_type in ('024') and ttd.IS_GPS = '0' and ttd.IS_GSM = '1' and  ttd.IS_CONVERTED = '0' 
				   THEN cast(ttd.MCC as varchar) + ',' + cast(ttd.MNC as varchar) + ',' + cast(ttd.LAC as varchar) + ',' + cast(ttd.CELL_ID as varchar)
				  ELSE ''
			   END AS "value",
			CASE WHEN mat.code_answer_type in('001','002','003','004','005','013','015','025','026', '046') THEN '0'
			      WHEN  mat.code_answer_type in ('006','007','008','009','010','011','012') THEN '0'
				  WHEN  mat.code_answer_type in ('024') THEN '2'
				  ELSE ''
			   END AS flag
			FROM TR_TASK_D ttd with (nolock) LEFT JOIN MS_QUESTION mq with (nolock)
				ON ttd.UUID_QUESTION = mq.UUID_QUESTION
				LEFT JOIN MS_ANSWERTYPE mat with (nolock)
				ON mat.UUID_ANSWER_TYPE = mq.UUID_ANSWER_TYPE
			WHERE ttd.UUID_TASK_H = :uuidTaskH
			UNION
			SELECT ttdl.QUESTION_TEXT AS "key",
				CASE WHEN mat.code_answer_type in ('016','017','018','021')  THEN CAST(ttdl.QUESTION_ID as Varchar)
					  ELSE ''
				 END AS "value",
				CASE WHEN mat.code_answer_type in('016','017','018','021') THEN '1'
					  ELSE ''
				 END AS flag
			FROM TR_TASKDETAILLOB ttdl with (nolock) LEFT JOIN MS_QUESTION mq with (nolock)
				ON ttdl.QUESTION_ID = mq.UUID_QUESTION
				LEFT JOIN MS_ANSWERTYPE mat with (nolock)
				ON mat.UUID_ANSWER_TYPE = mq.UUID_ANSWER_TYPE
			WHERE ttdl.UUID_TASK_H = :uuidTaskH
	</sql-query>
<!-- Approval Mobile END -->

	<sql-query name="task.approval.approvalList">
	    <query-param name="uuidUser" type="long" />
	    <query-param name="branchName" type="string" />
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
	    <query-param name="fieldPersonName" type="string" />
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="submittedDateStart" type="string"/>
		<query-param name="submittedDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="uuidForm" type="long"/>
		SELECT * FROM (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select 
					trth.UUID_TASK_H,
					msb.BRANCH_NAME,
					trth.CUSTOMER_NAME,
					trth.APPL_NO,
					msu.FULL_NAME,
					trth.SUBMIT_DATE,
					msp.PRIORITY_DESC,
					msp.ICON_PRIORITY,
					ROW_NUMBER() OVER (ORDER BY msp.PRIORITY_DESC asc,trth.submit_date asc) AS rownum,
					trth.ASSIGN_DATE 
				from tr_task_h trth with (nolock)
					JOIN MS_BRANCH msb with (nolock) ON trth.UUID_BRANCH = msb.UUID_BRANCH
					JOIN (select keyValue as UUID_MS_USER, fullName as FULL_NAME
					        from getUserByLogin(:uuidUser)
					       where keyValue != :uuidUser) msu ON trth.UUID_MS_USER = msu.UUID_MS_USER
					JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
					JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				where mst.STATUS_CODE = 'P'
					AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
					AND msb.BRANCH_NAME LIKE LOWER ('%'+ :branchName +'%')
					AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
					AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
					AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
					AND msu.FULL_NAME LIKE LOWER ('%'+ :fieldPersonName +'%')
					AND trth.SUBMIT_DATE IS NOT NULL
					AND trth.SUBMIT_DATE BETWEEN (CASE WHEN :submittedDateStart = '%' then '1990-01-01' else :submittedDateStart END) 
						AND (CASE WHEN :submittedDateEnd = '%' then :currentDate else :submittedDateEnd END)
					AND (trth.ASSIGN_DATE BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01' else :assignmentDateStart END) 
						AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END) OR trth.ASSIGN_DATE IS NULL)						
					AND UUID_FORM LIKE ('%'+ :uuidForm +'%')
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.approval.countApprovalList">
	    <query-param name="uuidUser" type="long" />
	    <query-param name="branchName" type="string" />
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
	    <query-param name="fieldPersonName" type="string" />
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="submittedDateStart" type="string"/>
		<query-param name="submittedDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="uuidForm" type="long"/>
		select count(1)
		from tr_task_h trth with (nolock)
			JOIN MS_BRANCH msb with (nolock) ON trth.UUID_BRANCH = msb.UUID_BRANCH
			JOIN (select keyValue as UUID_MS_USER, fullName as FULL_NAME
			        from getUserByLogin(:uuidUser)
			       where keyValue != :uuidUser) msu ON trth.UUID_MS_USER = msu.UUID_MS_USER
			JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
			JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		where mst.STATUS_CODE = 'P'
			AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
			AND msb.BRANCH_NAME LIKE LOWER ('%'+ :branchName +'%')
			AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
			AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
			AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
			AND msu.FULL_NAME LIKE LOWER ('%'+ :fieldPersonName +'%')
			AND trth.SUBMIT_DATE IS NOT NULL
			AND trth.SUBMIT_DATE BETWEEN (CASE WHEN :submittedDateStart = '%' then '1990-01-01 00:00:00' else :submittedDateStart END) 
				AND (CASE WHEN :submittedDateEnd = '%' then :currentDate else :submittedDateEnd END)
			AND (trth.ASSIGN_DATE BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01 00:00:00' else :assignmentDateStart END) 
				AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END) OR trth.ASSIGN_DATE IS NULL)				
			AND UUID_FORM LIKE ('%'+ :uuidForm +'%')
	</sql-query>
	
	<sql-query name="task.approval.approvalDetailQSet">
		<query-param name="uuidTaskH" type="long" />
			WITH N AS(		
			SELECT  trtd.UUID_QUESTION AS QUESTION,
					mfqs.QUESTION_LABEL, msat.ANSWER_TYPE_NAME,
					CASE
						WHEN msat.code_answer_type in ('001','002','003','004','005','013','014','015','025','026','046')
						  THEN ISNULL(trtd.FIN_TEXT_ANSWER, trtd.TEXT_ANSWER)
						WHEN msat.code_answer_type in ('006','007','008','009','010','011','012')
						  THEN ISNULL(trtd.FIN_OPTION_TEXT, trtd.OPTION_TEXT)
					END AS ANSWER, msat.code_answer_type, trtd.UUID_TASK_H, mfqs.UUID_QUESTION, '0' as hasImage,
					'' as accuracy,
					mfqs.UUID_FORM,
					mfqs.QUESTION_GROUP_OF_FORM_SEQ as LINE_SEQ_ORDER, mfqs.QUESTION_OF_GROUP_SEQ as SEQ_ORDER,
					trtd.LATITUDE,
					trtd.LONGITUDE,
					ISNULL(trtd.FIN_TEXT_ANSWER, trtd.TEXT_ANSWER) as FIN_TEXT_ANSWER,
					mfqs.REF_ID
			FROM TR_TASK_D trtd with (nolock) 
				JOIN TR_TASK_H tth on trtd.UUID_TASK_H=tth.UUID_TASK_H
				JOIN MS_FORMHISTORY mfh ON mfh.UUID_FORM = tth.UUID_FORM and mfh.FORM_VERSION = tth.FORM_VERSION
				JOIN MS_FORMQUESTIONSET mfqs on mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY AND mfqs.UUID_QUESTION = trtd.UUID_QUESTION
				JOIN MS_ANSWERTYPE msat with (nolock) ON mfqs.UUID_ANSWER_TYPE = msat.UUID_ANSWER_TYPE
			WHERE trtd.UUID_TASK_H = :uuidTaskH
			UNION ALL
			SELECT  trtdl.QUESTION_ID AS QUESTION,
					mfqs.QUESTION_LABEL, msat.ANSWER_TYPE_NAME,
					CASE
						WHEN msat.code_answer_type in ('016','017','018','021', '038', '039')
						  THEN CAST(trtdl.UUID_TASK_DETAIL_LOB AS varchar) 
					END AS ANSWER, msat.code_answer_type, trtdl.UUID_TASK_H, mfqs.UUID_QUESTION, 
					CASE
					  WHEN trtdl.LOB_FILE IS NOT NULL THEN '1'
					  WHEN trtdl.IMAGE_PATH IS NOT NULL THEN '1' ELSE '0'
					END AS hasImage,
					trtdl.accuracy,
					mfqs.UUID_FORM,
					mfqs.QUESTION_GROUP_OF_FORM_SEQ as LINE_SEQ_ORDER, mfqs.QUESTION_OF_GROUP_SEQ as SEQ_ORDER,
					trtdl.LATITUDE,
					trtdl.LONGITUDE,
					trtdl.FIN_TEXT_ANSWER,
					mfqs.REF_ID
			FROM TR_TASKDETAILLOB trtdl with (nolock) 
				JOIN TR_TASK_H tth on trtdl.UUID_TASK_H=tth.UUID_TASK_H AND tth.UUID_TASK_H = :uuidTaskH
				JOIN MS_FORMHISTORY mfh ON mfh.UUID_FORM = tth.UUID_FORM and mfh.FORM_VERSION = tth.FORM_VERSION
				JOIN MS_FORMQUESTIONSET mfqs on mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY AND mfqs.UUID_QUESTION = trtdl.QUESTION_ID
				JOIN MS_ANSWERTYPE msat with (nolock) ON mfqs.UUID_ANSWER_TYPE = msat.UUID_ANSWER_TYPE
		)
		SELECT N.QUESTION as uuidQuestion,
			   N.QUESTION_LABEL as questionText,
		       N.ANSWER_TYPE_NAME as "msQuestion.msAnswertype.answerTypeName",
		       N.ANSWER AS textAnswer,
		       N.code_answer_type as "msQuestion.msAnswertype.codeAnswerType",
		       N.hasImage as hasImage,
		       N.accuracy as accuracy,
		       N.LATITUDE as latitude,
		       N.LONGITUDE as longitude,
		       N.FIN_TEXT_ANSWER as finTextAnswer,
		       N.REF_ID as "msQuestion.refId"
		FROM N
		WHERE N.UUID_TASK_H = :uuidTaskH
		ORDER BY N.LINE_SEQ_ORDER, N.SEQ_ORDER asc
	</sql-query>
	<sql-query name="task.approval.viewMapPhoto">
		<query-param name="uuidTaskH" type="long" />		
		SELECT trtdl.UUID_TASK_H,
			trtdl.QUESTION_TEXT,
			'' as TEXT_ANSWER,
			'' as OPTION_TEXT,
			CASE
				WHEN (trtdl.LOB_FILE IS NOT NULL or trtdl.IMAGE_PATH IS NOT NULL) THEN '1'
				ELSE '0'
			END as HAS_IMAGE,
			trtdl.LATITUDE,
			trtdl.LONGITUDE,
			NULL AS LOB_FILE,
			trtdl.UUID_TASK_DETAIL_LOB,
			trtdl.IMAGE_PATH
		FROM
			TR_TASKDETAILLOB trtdl with (nolock)
		WHERE
			LONGITUDE is not null and LATITUDE is not null and
			UUID_TASK_H = :uuidTaskH
	</sql-query>
	
	<sql-query name="task.approval.approvalListByBranch">
	    <query-param name="uuidBranch" type="long" />
	    <query-param name="branchName" type="string" />
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
	    <query-param name="fieldPersonName" type="string" />
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="submittedDateStart" type="string"/>
		<query-param name="submittedDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="uuidForm" type="long"/>
		SELECT * FROM (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select 
					trth.UUID_TASK_H,
					msb.BRANCH_NAME,
					trth.CUSTOMER_NAME,
					trth.APPL_NO,
					msu.FULL_NAME,
					trth.SUBMIT_DATE,
					msp.PRIORITY_DESC,
					msp.ICON_PRIORITY,
					ROW_NUMBER() OVER (ORDER BY msp.PRIORITY_DESC asc,trth.submit_date asc) AS rownum,
					trth.ASSIGN_DATE 
				from tr_task_h trth with (nolock)
				    join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) msb on trth.UUID_BRANCH = msb.UUID_BRANCH
					JOIN AM_MSUSER msu with (nolock) ON trth.UUID_MS_USER = msu.UUID_MS_USER
					JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
					JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					JOIN MS_FORM form with (nolock) ON trth.UUID_FORM = form.UUID_FORM
				where mst.STATUS_CODE = 'P'
					AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
					AND msb.BRANCH_NAME LIKE LOWER ('%'+ :branchName +'%')
					AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
					AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
					AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
					AND msu.FULL_NAME LIKE LOWER ('%'+ :fieldPersonName +'%')
					AND trth.SUBMIT_DATE IS NOT NULL
					AND trth.SUBMIT_DATE BETWEEN (CASE WHEN :submittedDateStart = '%' then '1990-01-01' else :submittedDateStart END) 
						AND (CASE WHEN :submittedDateEnd = '%' then :currentDate else :submittedDateEnd END)
					AND (trth.ASSIGN_DATE BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01' else :assignmentDateStart END) 
						AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END) OR trth.ASSIGN_DATE IS NULL)	
					AND form.UUID_FORM LIKE ('%'+ :uuidForm +'%')
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
<sql-query name="task.approval.countApprovalListbyBranch">
	    <query-param name="uuidBranch" type="long" />
	    <query-param name="branchName" type="string" />
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
	    <query-param name="fieldPersonName" type="string" />
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="submittedDateStart" type="string"/>
		<query-param name="submittedDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="uuidForm" type="long"/>
		select count(1)
		from tr_task_h trth with (nolock)
				    join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) msb on trth.UUID_BRANCH = msb.UUID_BRANCH
			JOIN AM_MSUSER msu with (nolock) ON trth.UUID_MS_USER = msu.UUID_MS_USER
			JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
			JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			JOIN MS_FORM form with (nolock) ON trth.UUID_FORM = form.UUID_FORM
		where mst.STATUS_CODE = 'P'
			AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
			AND msb.BRANCH_NAME LIKE LOWER ('%'+ :branchName +'%')
			AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
			AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
			AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
			AND msu.FULL_NAME LIKE LOWER ('%'+ :fieldPersonName +'%')
			AND trth.SUBMIT_DATE IS NOT NULL
			AND trth.SUBMIT_DATE BETWEEN (CASE WHEN :submittedDateStart = '%' then '1990-01-01 00:00:00' else :submittedDateStart END) 
				AND (CASE WHEN :submittedDateEnd = '%' then :currentDate else :submittedDateEnd END)
			AND (trth.ASSIGN_DATE BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01 00:00:00' else :assignmentDateStart END) 
				AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END) OR trth.ASSIGN_DATE IS NULL)			
			AND form.UUID_FORM LIKE ('%'+ :uuidForm +'%')
	</sql-query>
	
	<sql-query name="task.approval.approvalDataEntry">
	    <query-param name="branchName" type="string" />
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
	    <query-param name="fieldPersonName" type="string" />
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="submittedDateStart" type="string"/>
		<query-param name="submittedDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="uuidForm" type="long"/>
		<query-param name="uuidJob" type="long"/>
		SELECT * FROM (
   			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
    			select 
     				trth.UUID_TASK_H,
				    msb.BRANCH_NAME,
				    trth.CUSTOMER_NAME,
				    trth.APPL_NO,
				    msu.FULL_NAME,     
				    trth.SUBMIT_DATE,
				    msp.PRIORITY_DESC,
				    msp.ICON_PRIORITY,
				    ROW_NUMBER() OVER (ORDER BY msp.PRIORITY_DESC asc,trth.submit_date asc) AS rownum,
				    trth.ASSIGN_DATE 
    			from tr_task_h trth with (nolock)
     				JOIN MS_BRANCH msb with (nolock) ON trth.UUID_BRANCH = msb.UUID_BRANCH     
			    	JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
			    	JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			    	JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
			    	JOIN MS_FORMCATEGORY msfc with (nolock) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY   
			    	JOIN AM_MSUSER msu with (nolock) ON msu.UUID_MS_USER = trth.UUID_MS_USER
			    	JOIN MS_SETTINGAPPROVALOTS msa with (nolock) ON trth.UUID_FORM = msa.UUID_FORM AND msa.UUID_JOB_ASSIGN = msu.UUID_JOB
    			where mst.STATUS_CODE = 'PD'
     				AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
     				AND msb.BRANCH_NAME LIKE LOWER ('%'+ :branchName +'%')
     				AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
			    	AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
			    	AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
			    	AND msu.FULL_NAME LIKE LOWER ('%'+ :fieldPersonName +'%')
			    	AND trth.SUBMIT_DATE IS NOT NULL
			    	AND trth.SUBMIT_DATE BETWEEN (CASE WHEN :submittedDateStart = '%' then '1990-01-01' else :submittedDateStart END) 
			    		AND (CASE WHEN :submittedDateEnd = '%' then :currentDate else :submittedDateEnd END)
			    	AND (trth.ASSIGN_DATE BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01' else :assignmentDateStart END) 
			    		AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END) OR trth.ASSIGN_DATE IS NULL)      
			    	AND msf.UUID_FORM LIKE ('%'+ :uuidForm +'%')
			    	AND msa.UUID_JOB = :uuidJob
			    	AND msa.IS_APPROVAL_ON_DE = '1'
			    ) a <![CDATA[ WHERE a.ROWNUM <= :end
			) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.approval.countApprovalDataEntry">
	    <query-param name="branchName" type="string" />
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
	    <query-param name="fieldPersonName" type="string" />
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="submittedDateStart" type="string"/>
		<query-param name="submittedDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="uuidForm" type="long"/>
		<query-param name="uuidJob" type="long"/>
		select count(1)
  		from tr_task_h trth with (nolock)
			JOIN MS_BRANCH msb with (nolock) ON trth.UUID_BRANCH = msb.UUID_BRANCH   
	   		JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
	   		JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
	   		JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
	   		JOIN MS_FORMCATEGORY msfc with (nolock) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY
	   		JOIN AM_MSUSER msu with (nolock) ON msu.UUID_MS_USER = trth.UUID_MS_USER
	   		JOIN MS_SETTINGAPPROVALOTS msa with (nolock) ON trth.UUID_FORM = msa.UUID_FORM AND msa.UUID_JOB_ASSIGN = msu.UUID_JOB
  		where mst.STATUS_CODE = 'PD'
   			AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
   			AND msb.BRANCH_NAME LIKE LOWER ('%'+ :branchName +'%')
   			AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
   			AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
   			AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
   			AND msu.FULL_NAME LIKE LOWER ('%'+ :fieldPersonName +'%')
   			AND trth.SUBMIT_DATE IS NOT NULL
   			AND trth.SUBMIT_DATE BETWEEN (CASE WHEN :submittedDateStart = '%' then '1990-01-01 00:00:00' else :submittedDateStart END) 
    			AND (CASE WHEN :submittedDateEnd = '%' then :currentDate else :submittedDateEnd END)
   			AND (trth.ASSIGN_DATE BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01 00:00:00' else :assignmentDateStart END) 
    			AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END) OR trth.ASSIGN_DATE IS NULL)    
   			AND msf.UUID_FORM LIKE ('%'+ :uuidForm +'%')
   			AND msa.UUID_JOB = :uuidJob
			AND msa.IS_APPROVAL_ON_DE = '1'
	</sql-query>
	
	<sql-query name="task.approval.approvalList.onWomf">	   
	    <query-param name="branchName" type="string" />
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
	    <query-param name="fieldPersonName" type="string" />
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="submittedDateStart" type="string"/>
		<query-param name="submittedDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="uuidForm" type="long"/>
		<query-param name="uuidJob" type="long"/>
		SELECT * FROM (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select 
					trth.UUID_TASK_H,
					msb.BRANCH_NAME,
					trth.CUSTOMER_NAME,
					trth.APPL_NO,
					msu.FULL_NAME,					
					trth.SUBMIT_DATE,
					msp.PRIORITY_DESC,
					msp.ICON_PRIORITY,
					ROW_NUMBER() OVER (ORDER BY msp.PRIORITY_DESC asc,trth.submit_date asc) AS rownum,
					trth.ASSIGN_DATE 
				from tr_task_h trth with (nolock)
					JOIN MS_BRANCH msb with (nolock) ON trth.UUID_BRANCH = msb.UUID_BRANCH					
					JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
					JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
     				JOIN MS_FORMCATEGORY msfc with (nolock) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY   
     				JOIN AM_MSUSER msu with (nolock) ON msu.UUID_MS_USER = trth.UUID_MS_USER
     				JOIN MS_SETTINGAPPROVALOTS msa with (nolock) ON trth.UUID_FORM = msa.UUID_FORM AND msa.UUID_JOB_ASSIGN = msu.UUID_JOB
				where mst.STATUS_CODE = 'PW'
					AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
					AND msb.BRANCH_NAME LIKE LOWER ('%'+ :branchName +'%')
					AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
					AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
					AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
					AND msu.FULL_NAME LIKE LOWER ('%'+ :fieldPersonName +'%')
					AND trth.SUBMIT_DATE IS NOT NULL
					AND trth.SUBMIT_DATE BETWEEN (CASE WHEN :submittedDateStart = '%' then '1990-01-01' else :submittedDateStart END) 
						AND (CASE WHEN :submittedDateEnd = '%' then :currentDate else :submittedDateEnd END)
					AND (trth.ASSIGN_DATE BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01' else :assignmentDateStart END) 
						AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END) OR trth.ASSIGN_DATE IS NULL)						
					AND msf.UUID_FORM LIKE ('%'+ :uuidForm +'%')
					AND msa.UUID_JOB = :uuidJob
					AND msa.IS_APPROVAL_ON_WOM = '1'
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.approval.countApprovalList.onWomf">	    
	    <query-param name="branchName" type="string" />
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
	    <query-param name="fieldPersonName" type="string" />
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="submittedDateStart" type="string"/>
		<query-param name="submittedDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="uuidForm" type="long"/>
		<query-param name="uuidJob" type="long"/>
		select count(1)
		from tr_task_h trth with (nolock)
			JOIN MS_BRANCH msb with (nolock) ON trth.UUID_BRANCH = msb.UUID_BRANCH			
			JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
			JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
     		JOIN MS_FORMCATEGORY msfc with (nolock) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY
     		JOIN AM_MSUSER msu with (nolock) ON msu.UUID_MS_USER = trth.UUID_MS_USER
     		JOIN MS_SETTINGAPPROVALOTS msa with (nolock) ON trth.UUID_FORM = msa.UUID_FORM AND msa.UUID_JOB_ASSIGN = msu.UUID_JOB
		where mst.STATUS_CODE = 'PW'
			AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
			AND msb.BRANCH_NAME LIKE LOWER ('%'+ :branchName +'%')
			AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
			AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
			AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
			AND msu.FULL_NAME LIKE LOWER ('%'+ :fieldPersonName +'%')
			AND trth.SUBMIT_DATE IS NOT NULL
			AND trth.SUBMIT_DATE BETWEEN (CASE WHEN :submittedDateStart = '%' then '1990-01-01 00:00:00' else :submittedDateStart END) 
				AND (CASE WHEN :submittedDateEnd = '%' then :currentDate else :submittedDateEnd END)
			AND (trth.ASSIGN_DATE BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01 00:00:00' else :assignmentDateStart END) 
				AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END) OR trth.ASSIGN_DATE IS NULL)				
			AND msf.UUID_FORM LIKE ('%'+ :uuidForm +'%')
			AND msa.UUID_JOB = :uuidJob
			AND msa.IS_APPROVAL_ON_WOM = '1'
	</sql-query>
</hibernate-mapping>