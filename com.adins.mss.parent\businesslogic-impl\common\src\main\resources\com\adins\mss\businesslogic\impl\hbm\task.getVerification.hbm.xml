<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="task.getVerification.header">
		<query-param name="uuidUser" type="long"/>
			SELECT tskh.UUID_TASK_H as uuidTaskH,
				   tskh.CUSTOMER_NAME as customerName,
				   tskh.CUSTOMER_PHONE as customerPhone,
				   tskh.CUSTOMER_ADDRESS as customerAddress,
				   tskh.NOTES as notes,
				   cast(tskh.LATITUDE as varchar) as latitude,
				   cast(tskh.LONGITUDE as varchar) as longitude,
				   tskh.UUID_FORM as schemeId,
				   ISNULL(replace(convert(varchar(10),msf.FORM_LAST_UPDATE,103),'/','')+replace(convert(varchar(8),msf.FORM_LAST_UPDATE,14),':',''), null) as schemeLastUpdate,
				   ISNULL(tskh.APPL_NO, '') as applNo,
				   cast(dbo.F_IS_VERIFICATION(tskh.UUID_TASK_H) as varchar) as isVerification,
				   case when msf.PREPROCESSING_SP is null then '0' else '1' end as isPreviewServer,
				   ISNULL(replace(convert(varchar(10),tskh.DTM_CRT,103),'/','')+replace(convert(varchar(8),tskh.DTM_CRT,14),':',''), null) as dtmCrt,
				   ISNULL(msf.IS_PRINTABLE, '0') as isPrintable,
				   tskh.TASK_ID as taskId,
				   mst.STATUS_CODE as status,
				   tskh.ZIP_CODE as zipCode,
				   ISNULL(replace(convert(varchar(10),tskh.ASSIGN_DATE,103),'/','')+replace(convert(varchar(8),tskh.ASSIGN_DATE,14),':',''), null) as assignDate,
				   CONVERT(varchar(5), tskh.form_version) as form_version
			  FROM TR_TASK_H tskh with (nolock) 
			  	   inner join MS_FORM msf with (nolock) ON tskh.UUID_FORM = msf.UUID_FORM
				   LEFT JOIN am_msuser msu with (nolock) ON msu.UUID_MS_USER = tskh.UUID_MS_USER
				   INNER JOIN MS_STATUSTASK mst with (nolock) ON tskh.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				   		AND mst.UUID_MS_SUBSYSTEM = msu.uuid_ms_subsystem
			 WHERE mst.status_code = 'V'
				   AND msu.IS_ACTIVE = '1'
				   AND msf.IS_ACTIVE = '1'
				   and msu.SPV_ID = :uuidUser
		  ORDER BY tskh.UUID_TASK_H, tskh.DTM_CRT
	</sql-query>
	
	<sql-query name="task.getVerification.headerByBranch">
		<query-param name="branchIds" type="long"/>
		<query-param name="subsystemId" type="long"/>
		SELECT tskh.UUID_TASK_H as uuidTaskH,
				 tskh.CUSTOMER_NAME as customerName,
				 tskh.CUSTOMER_PHONE as customerPhone,
				 tskh.CUSTOMER_ADDRESS as customerAddress,
				 tskh.NOTES as notes,
				 cast(tskh.LATITUDE as varchar) as latitude,
				 cast(tskh.LONGITUDE as varchar) as longitude,
				 tskh.UUID_FORM as schemeId,
				 ISNULL(replace(convert(varchar(10),msf.FORM_LAST_UPDATE,103),'/','')+replace(convert(varchar(8),msf.FORM_LAST_UPDATE,14),':',''), null) as schemeLastUpdate,
				 ISNULL(tskh.APPL_NO, '') as applNo,
				 cast(dbo.F_IS_VERIFICATION(tskh.UUID_TASK_H) as varchar) as isVerification,
				 case when msf.PREPROCESSING_SP is null then '0' else '1' end as isPreviewServer,
				 ISNULL(replace(convert(varchar(10),tskh.DTM_CRT,103),'/','')+replace(convert(varchar(8),tskh.DTM_CRT,14),':',''), null) as dtmCrt,
				 ISNULL(msf.IS_PRINTABLE, '0') as isPrintable,
				 tskh.TASK_ID as taskId,
				 mst.STATUS_CODE as status,
				 tskh.ZIP_CODE as zipCode,
				 ISNULL(replace(convert(varchar(10),tskh.ASSIGN_DATE,103),'/','')+replace(convert(varchar(8),tskh.ASSIGN_DATE,14),':',''), null) as assignDate,
				 CONVERT(varchar(5), tskh.form_version) as form_version
		FROM TR_TASK_H tskh with (nolock) 
			inner join MS_FORM msf with (nolock) ON tskh.UUID_FORM = msf.UUID_FORM
			LEFT JOIN am_msuser msu with (nolock) ON msu.UUID_MS_USER = tskh.UUID_MS_USER
			INNER JOIN MS_STATUSTASK mst with (nolock) ON tskh.UUID_STATUS_TASK = mst.UUID_STATUS_TASK	
				AND mst.UUID_MS_SUBSYSTEM = msu.uuid_ms_subsystem
			join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchIds)) msb on tskh.UUID_BRANCH = msb.UUID_BRANCH
		WHERE mst.status_code = 'V'
			AND msu.IS_ACTIVE = '1'
			AND msf.IS_ACTIVE = '1'
			AND MST.UUID_MS_SUBSYSTEM = :subsystemId
		ORDER BY tskh.UUID_TASK_H, tskh.DTM_CRT
	</sql-query>

	<sql-query name="task.getVerification.detail">
		<query-param name="uuidTaskH" type="long"/>
			SELECT ttd.QUESTION_TEXT as "key",
			CASE WHEN mat.code_answer_type in ('001','002','003','004','005','013','015','025','026') THEN ttd.TEXT_ANSWER
			      WHEN  mat.code_answer_type in ('006','007','008','009','010','011','012')  THEN ttd.OPTION_TEXT
				  WHEN  mat.code_answer_type in ('024') and (ttd.LATITUDE is not null or ttd.LATITUDE is not null ) 
				  THEN cast(ttd.LATITUDE as varchar) + ',' + cast(ttd.LONGITUDE as varchar)
				   WHEN  mat.code_answer_type in ('024') and ttd.IS_GPS = '0' and ttd.IS_GSM = '1' and  ttd.IS_CONVERTED = '0' 
				   THEN cast(ttd.MCC as varchar) + ',' + cast(ttd.MNC as varchar) + ',' + cast(ttd.LAC as varchar) + ',' + cast(ttd.CELL_ID as varchar)
				  ELSE ''
			   END AS "value",
			CASE WHEN mat.code_answer_type in('001','002','003','004','005','013','015','025','026') THEN '0'
			      WHEN  mat.code_answer_type in ('006','007','008','009','010','011','012') THEN '0'
				  WHEN  mat.code_answer_type in ('024') THEN '2'
				  ELSE ''
			   END AS flag
			FROM TR_TASK_D ttd with (nolock) LEFT JOIN MS_QUESTION mq with (nolock)
				ON ttd.UUID_QUESTION = mq.UUID_QUESTION
				LEFT JOIN MS_ANSWERTYPE mat with (nolock)
				ON mat.UUID_ANSWER_TYPE = mq.UUID_ANSWER_TYPE
			WHERE ttd.UUID_TASK_H = :uuidTaskH
			UNION
			SELECT ttdl.QUESTION_TEXT AS "key",
				CASE WHEN mat.code_answer_type in ('016','017','018','021')  THEN CAST(ttdl.UUID_TASK_DETAIL_LOB AS VARCHAR)
					  ELSE ''
				 END AS "value",
				CASE WHEN mat.code_answer_type in('016','017','018','021') THEN '3'
					  ELSE ''
				 END AS flag
			FROM TR_TASKDETAILLOB ttdl with (nolock) LEFT JOIN MS_QUESTION mq with (nolock)
				ON ttdl.QUESTION_ID = mq.UUID_QUESTION
				LEFT JOIN MS_ANSWERTYPE mat with (nolock)
				ON mat.UUID_ANSWER_TYPE = mq.UUID_ANSWER_TYPE
			WHERE ttdl.UUID_TASK_H = :uuidTaskH
	</sql-query>
</hibernate-mapping>