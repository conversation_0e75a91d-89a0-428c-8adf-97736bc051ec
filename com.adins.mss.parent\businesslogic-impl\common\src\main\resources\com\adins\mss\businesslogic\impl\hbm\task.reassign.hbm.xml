<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="task.reassign.getUsers">
	<query-param name="uuidSpv" type="string" />
	<query-param name="fullname" type="string" />
	<query-param name="loginId" type="string" />
	<query-param name="start" type="string" />
	<query-param name="end" type="string" />
		WITH N AS (
				SELECT
					msu.UUID_MS_USER,
					msu.FULL_NAME,
					msu.IS_LOGGED_IN,
					msu.UUID_MS_USER AS HIRARKI,
					CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2,
					0 as LEVEL,
					msu.LOGIN_ID,
					msu.INITIAL_NAME,
					msu_spv.LOGIN_ID login_id_spv,
					msu_spv.FULL_NAME full_name_spv,
					msu.UUID_JOB,
					msu.UUID_BRANCH
				FROM AM_MSUSER msu with (nolock) join AM_MSUSER msu_spv with (nolock) on msu.SPV_ID = msu_spv.UUID_MS_USER
			   WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
			UNION ALL
				SELECT
					msu2.UUID_MS_USER,
					msu2.FULL_NAME,
					msu2.IS_LOGGED_IN,
					N.HIRARKI,
					N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)),
					N.LEVEL+1,
					msu2.LOGIN_ID,
					msu2.INITIAL_NAME,
					N.LOGIN_ID login_id_spv,
					N.FULL_NAME full_name_spv,
					msu2.UUID_JOB,
					msu2.UUID_BRANCH
				FROM AM_MSUSER msu2 with (nolock), N
			   WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
			)
			SELECT * from (
				SELECT a.*, ROW_NUMBER() OVER ( ORDER BY rownum) AS recnum FROM (	
					select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID, INITIAL_NAME, login_id_spv, full_name_spv, br.BRANCH_CODE, br.BRANCH_NAME,
						ROW_NUMBER() OVER (ORDER BY LOGIN_ID ASC) AS rownum
					  from N join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
					  join MS_BRANCH br with (nolock) on N.UUID_BRANCH = br.UUID_BRANCH
					  where j.IS_FIELD_PERSON = '1'
					  and FULL_NAME like UPPER('%'+ :fullname +'%')
					  and LOGIN_ID like UPPER('%'+ :loginId +'%')
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.reassign.getUsersCnt">
	<query-param name="uuidSpv" type="string" />
	<query-param name="fullname" type="string" />
	<query-param name="loginId" type="string" />
		WITH N AS (
				SELECT
					msu.UUID_MS_USER,
					msu.FULL_NAME,
					msu.IS_LOGGED_IN,
					msu.UUID_MS_USER AS HIRARKI,
					CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2,
					0 as LEVEL,
					msu.LOGIN_ID,
					msu.INITIAL_NAME,
					msu_spv.LOGIN_ID login_id_spv,
					msu_spv.FULL_NAME full_name_spv,
					msu.UUID_JOB,
					msu.UUID_BRANCH
				FROM AM_MSUSER msu with (nolock) join AM_MSUSER msu_spv with (nolock) on msu.SPV_ID = msu_spv.UUID_MS_USER
			   WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
			UNION ALL
				SELECT
					msu2.UUID_MS_USER,
					msu2.FULL_NAME,
					msu2.IS_LOGGED_IN,
					N.HIRARKI,
					N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)),
					N.LEVEL+1,
					msu2.LOGIN_ID,
					msu2.INITIAL_NAME,
					N.LOGIN_ID login_id_spv,
					N.FULL_NAME full_name_spv,
					msu2.UUID_JOB,
					msu2.UUID_BRANCH
				FROM AM_MSUSER msu2 with (nolock), N
			   WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
			)
			select count(*)
			  from N join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
			  join MS_BRANCH br with (nolock) on N.UUID_BRANCH = br.UUID_BRANCH
			  where j.IS_FIELD_PERSON = '1'
			  and FULL_NAME like UPPER('%'+ :fullname +'%')
			  and LOGIN_ID like UPPER('%'+ :loginId +'%')
	</sql-query>

	<sql-query name="task.reassign.getExcUserList">
	   	<query-param name="uuidSpv" type="long" />
	   	<query-param name="uuidSvy" type="long" />
		WITH N AS (
		SELECT 
		msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_BRANCH, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
		UNION ALL
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.LOGIN_ID, msu2.UUID_BRANCH, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID, msb.BRANCH_NAME 
		from N JOIN MS_BRANCH msb with (nolock) ON N.UUID_BRANCH = msb.UUID_BRANCH
		where UUID_MS_USER != :uuidSvy order by FULL_NAME
	</sql-query>
	
</hibernate-mapping>