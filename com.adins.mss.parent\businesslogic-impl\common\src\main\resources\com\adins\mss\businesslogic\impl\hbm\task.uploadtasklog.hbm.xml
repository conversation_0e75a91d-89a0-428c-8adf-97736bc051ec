<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="task.uploadtasklog.getList">
		<query-param name="start" type="string" />
		<query-param name="end" type="string" />
		<query-param name="uuidUser" type="string" />
		SELECT * FROM (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT tu.SEQNO, 
						usr.LOGIN_ID,
						usr.FULL_NAME,
						LEFT(CONVERT(VARCHAR, tu.DTM_UPLOAD, 113),20) as UPLOAD_TIME,
						tu.INPUT_FILENAME,
						COALESCE (LEFT(CONVERT(VARCHAR, tu.PROCESS_START_TIME, 113),20), '-') as PROCESS_START_TIME,
						COALESCE (LEFT(CONVERT(VARCHAR, tu.PROCESS_FINISH_TIME, 113),20), '-') as PROCESS_FINISH_TIME,
						COALESCE (tu.PROCESS_DURATION_SECONDS, '-') as PROCESS_DURATION_SECONDS,
						COALESCE (tu.INPUT_COUNT, '-') as INPUT_COUNT,
						COALESCE (tu.COUNT_SUCCESS, '-') as COUNT_SUCCESS,
						COALESCE (tu.COUNT_ERROR, '-') as COUNT_ERROR,
						(Case when tu.ERROR_FILE_LOCATION is not null or tu.ERROR_FILE_LOCATION != ''
								then '1'
								else '0' end) hasError,
						ROW_NUMBER() OVER (ORDER BY tu.SEQNO DESC) AS rownum,
						COALESCE (tu.type, '-') as TYPE
					FROM TR_UPLOADTASKLOG tu with (nolock)
					LEFT JOIN AM_MSUSER usr with (nolock) on tu.UUID_MS_USER = usr.UUID_MS_USER
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.uploadtasklog.getListCount">
		SELECT Count(*)
		FROM TR_UPLOADTASKLOG tu with (nolock)
	</sql-query>

</hibernate-mapping>