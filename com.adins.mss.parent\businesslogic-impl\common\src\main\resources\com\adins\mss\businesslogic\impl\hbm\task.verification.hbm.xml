<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="task.verification.listUser">
	    <query-param name="parentUser" type="string" />
		  WITH N AS (
		  SELECT msu.UUID_MS_USER, msu.SPV_ID,
		  msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 1 as SEQ
		  FROM  AM_MSUSER msu with (nolock)
		  WHERE msu.SPV_ID =:parentUser
		  UNION ALL

		  SELECT msu2.UUID_MS_USER, msu2.SPV_ID, 
		  N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.SEQ+1
		  FROM  AM_MSUSER msu2 with (nolock),N
		  WHERE N.UUID_MS_USER=msu2.SPV_ID
		)
			SELECT n.UUID_MS_USER
			FROM N left outer join AM_MSUSER d on n.SPV_ID=d.UUID_MS_USER
	</sql-query>
	
	<sql-query name="task.verification.getBranchListCombo">
    <query-param name="branchId" type="string" />
		select keyValue, BRANCH_NAME,branch_code 
		from dbo.getCabangByLogin(:branchId) order by branch_code
	</sql-query>

	<sql-query name="task.verification.listOptionAnswerWithConstraint">
	    <query-param name="lovGroup" type="string" />
	     <query-param name="constraint1" type="string" />
	     <query-param name="isActive" type="string" />
		 SELECT CODE,DESCRIPTION,SEQUENCE
		 FROM MS_LOV with (nolock)
		 WHERE LOV_GROUP = :lovGroup
		 	AND CONSTRAINT_1 = :constraint1
		 	AND IS_ACTIVE = :isActive
		 ORDER BY DESCRIPTION
	</sql-query>
	
	<sql-query name="task.verification.listOptionAnswerWithConstraintByBranch">
	    <query-param name="lovGroup" type="string" />
	     <query-param name="uuidBranch" type="string" />
	     <query-param name="constraint1" type="string" />
	     <query-param name="constraint2" type="string" />
	     <query-param name="constraint3" type="string" />
	     <query-param name="constraint4" type="string" />
	     <query-param name="constraint5" type="string" />
	     <query-param name="isActive" type="string" />
		 SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE
				FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
				WHERE LOV_GROUP = :lovGroup
				and t1.IS_ACTIVE = :isActive
				and mbl.UUID_LOV is null
				and (t1.CONSTRAINT_1 IN (:constraint1) OR t1.CONSTRAINT_1 IS NULL)
				and (t1.CONSTRAINT_2 IN (:constraint2) OR t1.CONSTRAINT_2 IS NULL)
				and (t1.CONSTRAINT_3 IN (:constraint3) OR t1.CONSTRAINT_3 IS NULL)
				and (t1.CONSTRAINT_4 IN (:constraint4) OR t1.CONSTRAINT_4 IS NULL)
				and (t1.CONSTRAINT_5 IN (:constraint5) OR t1.CONSTRAINT_5 IS NULL)
			 union
			 SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE
				 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
				 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_ACTIVE = :isActive
				 and mbl.UUID_BRANCH = :uuidBranch
				 and (t1.CONSTRAINT_1 IN (:constraint1) OR t1.CONSTRAINT_1 IS NULL)
				 and (t1.CONSTRAINT_2 IN (:constraint2) OR t1.CONSTRAINT_2 IS NULL)
				 and (t1.CONSTRAINT_3 IN (:constraint3) OR t1.CONSTRAINT_3 IS NULL)
				 and (t1.CONSTRAINT_4 IN (:constraint4) OR t1.CONSTRAINT_4 IS NULL)
				 and (t1.CONSTRAINT_5 IN (:constraint5) OR t1.CONSTRAINT_5 IS NULL)
				 ORDER BY t1.SEQUENCE
	</sql-query>
	
	<sql-query name="task.verification.listOptionAnswerNoConstraint">
	    <query-param name="lovGroup" type="string" />
	    <query-param name="isActive" type="string" />
		 SELECT CODE,DESCRIPTION,SEQUENCE
		 FROM MS_LOV with (nolock)
		 WHERE LOV_GROUP =:lovGroup
		 	AND IS_ACTIVE = :isActive
		 ORDER BY DESCRIPTION
	</sql-query>
	
	
	<sql-query name="task.verification.listOptionAnswerNoConstraintByBranch">
	    <query-param name="lovGroup" type="string" />
	    <query-param name="uuidBranch" type="string" />
	    <query-param name="isActive" type="string" />
		 SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE
				FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
				WHERE LOV_GROUP = :lovGroup
				and t1.IS_ACTIVE = :isActive
				and mbl.UUID_LOV is null
			 union
			 SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE
				 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
				 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_ACTIVE = :isActive
				 and mbl.UUID_BRANCH = :uuidBranch
				 ORDER BY t1.SEQUENCE
	</sql-query>
	
	
	<sql-query name="task.verification.getLovNoCons">
		<query-param name="lovGroup" type="string" />
		<query-param name="code" type="string" />
		 SELECT UUID_LOV
		 FROM MS_LOV with (nolock)
		 WHERE LOV_GROUP = :lovGroup
		 	AND code = :code
		 	AND (CONSTRAINT_1 IS NULL OR CONSTRAINT_1 = '')
		 	AND (CONSTRAINT_2 IS NULL OR CONSTRAINT_2 = '')
		 	AND (CONSTRAINT_3 IS NULL OR CONSTRAINT_3 = '')
		 	AND (CONSTRAINT_4 IS NULL OR CONSTRAINT_4 = '')
		 	AND (CONSTRAINT_5 IS NULL OR CONSTRAINT_5 = '')
		 	AND IS_ACTIVE = 1
	</sql-query>
	
	<sql-query name="task.verification.getDetailByLov">
		<query-param name="msLovId" type="string" />
		<query-param name="uuidTaskH" type="string" />
		<query-param name="uuidQuestion" type="string" />
		 SELECT UUID_TASK_D
		 FROM TR_TASK_D with (nolock)
		 WHERE UUID_TASK_H = :uuidTaskH
		 AND UUID_QUESTION = :uuidQuestion
		 AND (LOV_ID = :msLovId OR INT_LOV_ID = :msLovId)		  
	</sql-query>
	
	<sql-query name="task.verification.getDetail">
		<query-param name="uuidTaskH" type="string" />
		<query-param name="uuidQuestion" type="string" />
		<query-param name="uuidForm" type="string" />
		 SELECT a.UUID_TASK_D
		 FROM TR_TASK_D a with (nolock) left join tr_task_h b with (nolock) on a.uuid_task_h = b.uuid_task_h
		 WHERE a.uuid_task_h = :uuidTaskH
		 and a.uuid_question = :uuidQuestion
		 and b.uuid_form = :uuidForm
	</sql-query>
	
	<sql-query name="task.verification.getLovByBranchC0">
	    <query-param name="lovGroup" type="string" />
	     <query-param name="uuidBranch" type="string" />
	     <query-param name="isActive" type="string" />
		 SELECT t1.*
				FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
				WHERE LOV_GROUP = :lovGroup
				and t1.IS_ACTIVE = :isActive
				and mbl.UUID_LOV is null
			 union
			 SELECT t1.*
				 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
				 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_ACTIVE = :isActive
				 and mbl.UUID_BRANCH = :uuidBranch
	</sql-query>
	
	<sql-query name="task.verification.getLovByBranchC1">
	    <query-param name="lovGroup" type="string" />
	     <query-param name="uuidBranch" type="string" />
	     <query-param name="constraint1" type="string" />
	     <query-param name="isActive" type="string" />
		 SELECT t1.*
				FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
				WHERE LOV_GROUP = :lovGroup
				and t1.IS_ACTIVE = :isActive
				and mbl.UUID_LOV is null
				and t1.CONSTRAINT_1 = :constraint1 
			 union
		SELECT t1.*
				 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
				 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_ACTIVE = :isActive
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.CONSTRAINT_1 = :constraint1 
	</sql-query>
	
	<sql-query name="task.verification.getLovByBranchC2">
	    <query-param name="lovGroup" type="string" />
	     <query-param name="uuidBranch" type="string" />
	     <query-param name="constraint1" type="string" />
	     <query-param name="constraint2" type="string" />
	     <query-param name="isActive" type="string" />
		 SELECT t1.*
				FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
				WHERE LOV_GROUP = :lovGroup
				and t1.IS_ACTIVE = :isActive
				and mbl.UUID_LOV is null
				and t1.CONSTRAINT_1 = :constraint1
				and t1.CONSTRAINT_2 = :constraint2  
			 union
		SELECT t1.*
				 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
				 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_ACTIVE = :isActive
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.CONSTRAINT_1 = :constraint1 
				 and t1.CONSTRAINT_2 = :constraint2
	</sql-query>
	
	<sql-query name="task.verification.getLovByBranchC3">
	    <query-param name="lovGroup" type="string" />
	     <query-param name="uuidBranch" type="string" />
	     <query-param name="constraint1" type="string" />
	     <query-param name="constraint2" type="string" />
	     <query-param name="constraint3" type="string" />
	     <query-param name="isActive" type="string" />
		 SELECT t1.*
				FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
				WHERE LOV_GROUP = :lovGroup
				and t1.IS_ACTIVE = :isActive
				and mbl.UUID_LOV is null
				and t1.CONSTRAINT_1 = :constraint1
				and t1.CONSTRAINT_2 = :constraint2  
				and t1.CONSTRAINT_3 = :constraint3 
			 union
		SELECT t1.*
				 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
				 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_ACTIVE = :isActive
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.CONSTRAINT_1 = :constraint1 
				 and t1.CONSTRAINT_2 = :constraint2
				 and t1.CONSTRAINT_3 = :constraint3
	</sql-query>
	
	<sql-query name="task.verification.getLovByBranchC4">
	    <query-param name="lovGroup" type="string" />
	     <query-param name="uuidBranch" type="string" />
	     <query-param name="constraint1" type="string" />
	     <query-param name="constraint2" type="string" />
	     <query-param name="constraint3" type="string" />
	      <query-param name="constraint4" type="string" />
	     <query-param name="isActive" type="string" />
		 SELECT t1.*
				FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
				WHERE LOV_GROUP = :lovGroup
				and t1.IS_ACTIVE = :isActive
				and mbl.UUID_LOV is null
				and t1.CONSTRAINT_1 = :constraint1
				and t1.CONSTRAINT_2 = :constraint2  
				and t1.CONSTRAINT_3 = :constraint3 
				and t1.CONSTRAINT_4 = :constraint4 
			 union
		SELECT t1.*
				 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
				 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_ACTIVE = :isActive
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.CONSTRAINT_1 = :constraint1 
				 and t1.CONSTRAINT_2 = :constraint2
				 and t1.CONSTRAINT_3 = :constraint3
				 and t1.CONSTRAINT_4 = :constraint4
	</sql-query>
	
	<sql-query name="task.verification.getLovByBranchC5">
	    <query-param name="lovGroup" type="string" />
	     <query-param name="uuidBranch" type="string" />
	     <query-param name="constraint1" type="string" />
	     <query-param name="constraint2" type="string" />
	     <query-param name="constraint3" type="string" />
	     <query-param name="constraint4" type="string" />
	     <query-param name="constraint5" type="string" />
	     <query-param name="isActive" type="string" />
		 SELECT t1.*
				FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
				WHERE LOV_GROUP = :lovGroup
				and t1.IS_ACTIVE = :isActive
				and mbl.UUID_LOV is null
				and t1.CONSTRAINT_1 = :constraint1
				and t1.CONSTRAINT_2 = :constraint2  
				and t1.CONSTRAINT_3 = :constraint3 
				and t1.CONSTRAINT_4 = :constraint4 
				and t1.CONSTRAINT_5 = :constraint5
			 union
		SELECT t1.*
				 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
				 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_ACTIVE = :isActive
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.CONSTRAINT_1 = :constraint1 
				 and t1.CONSTRAINT_2 = :constraint2
				 and t1.CONSTRAINT_3 = :constraint3
				 and t1.CONSTRAINT_4 = :constraint4
				 and t1.CONSTRAINT_5 = :constraint5
	</sql-query>

	<sql-query name="task.verification.getAllTaskD2QSet">
		<query-param name="uuidTaskH" type="long" />
		<query-param name="uuidForm" type="long" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="formVersion" type="int" />
		WITH MSRD (
			UUID_TASK, USR_CRT, DTM_CRT, USR_UPD, DTM_UPD, UUID_TASK_H,
			UUID_QUESTION, QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT, IMAGE_PATH,
			LATITUDE, LONGITUDE, ACCURACY, MCC, MNC, LAC, CELL_ID, TIMESTAMP_TASK, IS_GPS, IS_GSM, GEOLOCATION_PROVIDER,
			IS_VISIBLE_QA, INT_TEXT_ANSWER, LOV_ID, INT_LOV_ID, FIN_LOV_ID, INT_OPTION_TEXT, FIN_TEXT_ANSWER, FIN_OPTION_TEXT,
			FIN_USE_IMAGE_FLAG, IS_READONLY, REGEX, IS_CONVERTED, IS_IMAGE, LOB_FILE
		) AS (				
			SELECT trtd.UUID_TASK_D, trtd.USR_CRT, trtd.DTM_CRT, trtd.USR_UPD, trtd.DTM_UPD, trtd.UUID_TASK_H,
					trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, trtd.OPTION_TEXT, trtd.IMAGE_PATH,
					trtd.LATITUDE, trtd.LONGITUDE, trtd.ACCURACY, trtd.MCC, trtd.MNC, trtd.LAC, trtd.CELL_ID, trtd.TIMESTAMP_TASK, trtd.IS_GPS, trtd.IS_GSM, trtd.GEOLOCATION_PROVIDER,
					trtd.IS_VISIBLE_QA, trtd.INT_TEXT_ANSWER, trtd.LOV_ID, trtd.INT_LOV_ID, trtd.FIN_LOV_ID, trtd.INT_OPTION_TEXT,
					trtd.FIN_TEXT_ANSWER, trtd.FIN_OPTION_TEXT, trtd.FIN_USE_IMAGE_FLAG, trtd.is_readonly, trtd.regex, trtd.IS_CONVERTED, '0' is_image, '' AS LOB_FILE
			  FROM TR_TASK_D trtd with (nolock)
			 where trtd.UUID_TASK_H = :uuidTaskH
				UNION ALL	
			SELECT trtd.UUID_TASK_DETAIL_LOB, trtd.USR_CRT, trtd.DTM_CRT, trtd.USR_UPD, trtd.DTM_UPD, trtd.UUID_TASK_H,
					trtd.QUESTION_ID, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, trtd.OPTION_TEXT, trtd.IMAGE_PATH,
					trtd.LATITUDE, trtd.LONGITUDE, trtd.ACCURACY, trtd.MCC, trtd.MNC, trtd.LAC, trtd.CELL_ID, trtd.TIMESTAMP_DETAIL, trtd.IS_GPS, trtd.IS_GSM, trtd.GEOLOCATION_PROVIDER,
					trtd.IS_VISIBLE_QA, trtd.INT_TEXT_ANSWER, trtd.LOV_ID, trtd.INT_LOV_ID, trtd.FIN_LOV_ID, trtd.INT_OPTION_TEXT,
					trtd.FIN_TEXT_ANSWER, trtd.FIN_OPTION_TEXT, trtd.FIN_USE_IMAGE_FLAG, '' AS is_readonly, '' AS regex, trtd.IS_CONVERTED, '1' is_image, '1' LOB_FILE
			  FROM TR_TASKDETAILLOB trtd with (nolock)
			 where trtd.UUID_TASK_H= :uuidTaskH
		), 
		QUESTION_SET AS (
			SELECT
				UUID_QUESTION, QUESTION_LABEL, REF_ID, CHOICE_FILTER, RELEVANT, QUESTION_GROUP_OF_FORM_SEQ, QUESTION_OF_GROUP_SEQ, mfh.UUID_FORM, mfqs.UUID_FORM_HISTORY, mfqs.LOV_GROUP, mfqs.is_readonly, mfqs.regex_pattern
			FROM 
				MS_FORMHISTORY mfh with (nolock)
				JOIN MS_FORMQUESTIONSET mfqs with (nolock) on mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
			WHERE
				mfh.UUID_FORM = :uuidForm
				AND mfh.FORM_VERSION = :formVersion
				AND mfqs.QUESTION_IS_ACTIVE = '1'
				AND mfqs.QUESTION_GROUP_IS_ACTIVE = '1'
		)
		SELECT
				ISNULL(MSRD.UUID_TASK, 0) UUID_TASK, ISNULL(MSRD.USR_CRT, '') USR_CRT, MSRD.DTM_CRT DTM_CRT, ISNULL(MSRD.USR_UPD, '') USR_UPD, MSRD.DTM_UPD DTM_UPD, ISNULL(MSRD.UUID_TASK_H, 0) UUID_TASK_H,
				ISNULL(qs.UUID_QUESTION, 0) UUID_QUESTION, ISNULL(qs.QUESTION_LABEL, '') QUESTION_TEXT, ISNULL(MSRD.TEXT_ANSWER, '') TEXT_ANSWER, ISNULL(MSRD.OPTION_TEXT, '') OPTION_TEXT, ISNULL(MSRD.IMAGE_PATH, '') IMAGE_PATH,
				MSRD.LATITUDE, MSRD.LONGITUDE, ISNULL(MSRD.ACCURACY, 0) ACCURACY, ISNULL(MSRD.MCC, 0) MCC, ISNULL(MSRD.MNC, 0) MNC, ISNULL(MSRD.LAC, 0) LAC, ISNULL(MSRD.CELL_ID, 0) CELL_ID, MSRD.TIMESTAMP_TASK TIMESTAMP_TASK, ISNULL(MSRD.IS_GPS, '') IS_GPS, ISNULL(MSRD.IS_GSM, '') IS_GSM, ISNULL(MSRD.GEOLOCATION_PROVIDER, '') GEOLOCATION_PROVIDER,
				ISNULL(MSRD.IS_VISIBLE_QA, '') IS_VISIBLE_QA, ISNULL(MSRD.INT_TEXT_ANSWER, '') INT_TEXT_ANSWER, ISNULL(MSRD.LOV_ID, 0) LOV_ID, ISNULL(MSRD.INT_LOV_ID, 0) INT_LOV_ID, ISNULL(MSRD.FIN_LOV_ID, 0) FIN_LOV_ID, ISNULL(MSRD.INT_OPTION_TEXT, '') INT_OPTION_TEXT,
				ISNULL(MSRD.FIN_TEXT_ANSWER, '') FIN_TEXT_ANSWER, ISNULL(MSRD.FIN_OPTION_TEXT, '') FIN_OPTION_TEXT, ISNULL(MSRD.FIN_USE_IMAGE_FLAG, '') FIN_USE_IMAGE_FLAG, ISNULL(qs.is_readonly, '') is_readonly, ISNULL(qs.regex_pattern, '') regex, ISNULL(MSRD.IS_CONVERTED, '') IS_CONVERTED, ISNULL(MSRD.is_image, '') is_image, MSRD.LOB_FILE LOB_FILE,
				ISNULL(qs.QUESTION_GROUP_OF_FORM_SEQ, 0) LINE_SEQ_ORDER, ISNULL(qs.QUESTION_OF_GROUP_SEQ, 0) SEQ_ORDER,
				CASE 
					   WHEN msrd.LOV_ID IS NOT NULL AND msrd.LOV_ID !='' 
							AND (ml.CONSTRAINT_1 IS NULL OR ml.CONSTRAINT_1 = '') THEN
							 (SELECT ass.CODE + '@@@' + ass.DESCRIPTION  + '|||' FROM (
									SELECT ML1.CODE,  ML1.DESCRIPTION, ML1.SEQUENCE as SEQ
									FROM MS_LOV ML1 with (nolock) JOIN MS_LOV ML2 with (nolock) 
									ON  ML1.LOV_GROUP = ML2.LOV_GROUP
									left join MS_BRANCHOFLOV MBL with (nolock) on ML1.UUID_LOV = MBL.UUID_LOV
									WHERE ML2.UUID_LOV = ML.UUID_LOV
									AND ML1.IS_ACTIVE = '1'
									AND MBL.UUID_LOV is null
								UNION ALL
									SELECT ML1.CODE,  ML1.DESCRIPTION, ML1.SEQUENCE as SEQ
									FROM MS_LOV ML1 with (nolock) JOIN MS_LOV ML2 with (nolock) 
									ON  ML1.LOV_GROUP = ML2.LOV_GROUP
									left join MS_BRANCHOFLOV MBL with (nolock) on ML1.UUID_LOV = MBL.UUID_LOV
									WHERE ML2.UUID_LOV = ML.UUID_LOV
									AND ML1.IS_ACTIVE = '1'
									and mbl.UUID_BRANCH = :uuidBranch
								) ass ORDER BY ass.SEQ
								FOR XML PATH('')) 
					   WHEN msrd.LOV_ID IS NOT NULL AND msrd.LOV_ID !=''  THEN
						 (SELECT ass.CODE + '@@@' + ass.DESCRIPTION  + '|||' FROM (
								SELECT ML1.CODE, ML1.DESCRIPTION, ML1.SEQUENCE as SEQ
								FROM MS_LOV ML1 with (nolock) JOIN MS_LOV ML2 with (nolock)
								ON  ML1.LOV_GROUP = ML2.LOV_GROUP
								left join MS_BRANCHOFLOV MBL with (nolock) on ML1.UUID_LOV = MBL.UUID_LOV
								WHERE ML2.UUID_LOV = ML.UUID_LOV
									AND ML1.IS_ACTIVE = '1'
								   AND mbl.UUID_LOV is null
								   AND ML1.CONSTRAINT_1 = ML2.CONSTRAINT_1
								   AND ISNULL(ML1.CONSTRAINT_2, '') = ISNULL(ML2.CONSTRAINT_2, '')
								   AND ISNULL(ML1.CONSTRAINT_3, '') = ISNULL(ML2.CONSTRAINT_3, '')
							UNION ALL
								SELECT ML1.CODE, ML1.DESCRIPTION, ML1.SEQUENCE as SEQ
								FROM MS_LOV ML1 with (nolock) JOIN MS_LOV ML2 with (nolock)
								ON  ML1.LOV_GROUP = ML2.LOV_GROUP
								left join MS_BRANCHOFLOV MBL with (nolock) on ML1.UUID_LOV = MBL.UUID_LOV
								WHERE ML2.UUID_LOV = ML.UUID_LOV
									AND ML1.IS_ACTIVE = '1'
									and mbl.UUID_BRANCH = :uuidBranch
									AND ML1.CONSTRAINT_1 = ML2.CONSTRAINT_1
									AND ISNULL(ML1.CONSTRAINT_2, '') = ISNULL(ML2.CONSTRAINT_2, '')
									AND ISNULL(ML1.CONSTRAINT_3, '') = ISNULL(ML2.CONSTRAINT_3, '')
						) ass ORDER BY ass.SEQ
						FOR XML PATH('')) 
				ELSE ''END AS LOV,
				ISNULL(qs.REF_ID, '') REF_ID,
				(SELECT
					CASE
						WHEN COUNT(RELEVANT) != 0 THEN '1'
						ELSE '0'
					END 
				   FROM QUESTION_SET innqs
				  WHERE innqs.RELEVANT LIKE '%{'+qs.REF_ID+'}%') AS IS_RELEVANT,
				ISNULL(qs.CHOICE_FILTER, '') CHOICE_FILTER,
				ISNULL(qs.UUID_FORM_HISTORY, '') UUID_FORM_HISTORY
		  FROM QUESTION_SET qs with (nolock)
		       LEFT JOIN MSRD with (nolock) ON msrd.UUID_QUESTION = qs.UUID_QUESTION
			   LEFT JOIN MS_LOV ML with (nolock) ON MSRD.LOV_ID = ML.UUID_LOV
		ORDER BY qs.QUESTION_GROUP_OF_FORM_SEQ, qs.QUESTION_OF_GROUP_SEQ
	</sql-query>
	
	<sql-query name="task.verification.getAllRelevanQuest">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="string" />
		<query-param name="uuidtaskH" type="string" />
			select count (code_answer_type)
			from MS_FORMQUESTIONSET qset
			join MS_ANSWERTYPE anstype on qset.uuid_answer_type = anstype.uuid_answer_type
			   where 
		    relevant like '%' + :refId + '%'
		    and UUID_FORM_HISTORY = (
		         select UUID_FORM_HISTORY from MS_FORMHISTORY
		         where UUID_FORM = :uuidForm and form_version = (
		           select form_version from tr_Task_h where uuid_task_h = :uuidtaskH
		          )
		        )
				and code_answer_type in ('016', '017', '018', '021')
	</sql-query>
	
	<sql-query name="task.verification.etRelevantMandatoryScript">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="string" />
		<query-param name="uuidtaskH" type="string" />
		select RELEVANT_MANDATORY from MS_FORMQUESTIONSET
			where 
				UUID_QUESTION = ( 
									select UUID_QUESTION from MS_QUESTION
									where ref_id = :refId
								)
				and UUID_FORM_HISTORY = (
									select UUID_FORM_HISTORY from MS_FORMHISTORY
									where UUID_FORM = :uuidForm and form_version = (
											select form_version from tr_Task_h where uuid_task_h = :uuidtaskH
										)
								)
	</sql-query>
		
	<sql-query name="task.verification.getQuestRelevantCopy">
		<query-param name="refId" type="string" />
		<query-param name="uuidFormHistory" type="long" />
		select UUID_QUESTION, REF_ID
		  from MS_FORMQUESTIONSET
		 where QUESTION_VALUE like '%' + :refId + '%'
		   and UUID_FORM_HISTORY = :uuidFormHistory
	</sql-query>
	</hibernate-mapping>