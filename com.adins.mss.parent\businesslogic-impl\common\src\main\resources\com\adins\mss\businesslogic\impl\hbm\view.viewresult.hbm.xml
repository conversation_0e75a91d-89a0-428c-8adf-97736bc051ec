<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="view.viewresult.answerHistory">
		<query-param name="uuidTask" type="long" />
   		<query-param name="uuidForm" type="long" />
		SELECT UUID_TASK_H as uuidTaskH,
			   ISNULL(rd.QUESTION_TEXT, mfqs.QUESTION_LABEL) as questionText,
			   ISNULL(REPLACE(TEXT_ANSWER,'\n','&lt;br&gt;'), '') as textAnswer,
			   ISNULL(OPTION_TEXT, '') as optionText,
			   CASE
					WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','035') AND LOB_FILE IS NOT NULL) THEN '1'
					WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','035') AND IMAGE_PATH IS NOT NULL) THEN '1'
					WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','035') AND LOB_FILE IS NULL) THEN '0'
					WHEN (msan.code_answer_type = '024') THEN '01'
					ELSE '10'
			   END AS hasImage,
			   rd.LATITUDE as latitude,
			   rd.LONGITUDE as longitude,
			   NULL as lobFile,
			   ISNULL(CAST(UUID_TASK_DETAIL_LOB AS VARCHAR), '') as uuidTaskDetailLob,
			   msan.code_answer_type as codeAnswerType,
			   ISNULL(REPLACE(INT_TEXT_ANSWER,'\n','&lt;br&gt;'), '') as intTextAnswer,
			   ISNULL(INT_OPTION_TEXT, '') as intOptionText,
			   ISNULL(FIN_TEXT_ANSWER, '') as finTextAnswer,
			   ISNULL(FIN_OPTION_TEXT, '') as finOptionText,
			   CASE
					WHEN (lower(msat.ASSET_TAG_NAME) IN ('home','office','legal address')) THEN '1'
					ELSE '0'
			   END AS asset,
			   MB.LATITUDE as latBranch,
			   MB.LONGITUDE as lngBranch,
			   MCC as mcc, MNC as mnc, LAC as lac, CELL_ID as cellId, ACCURACY as accuracy,
			   mfqs.UUID_QUESTION_GROUP as uuidQuestionGroup,
			   mfqs.QUESTION_GROUP_OF_FORM_SEQ as questionGroupOfFormSeq,
			   mfqs.QUESTION_OF_GROUP_SEQ as questionOfGroupSeq, msat.ASSET_TAG_NAME as assetTagName
		FROM (
			SELECT td.UUID_TASK_H, UUID_QUESTION as uuidQuestion,
				   QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
				   IMAGE_PATH, td.LATITUDE, td.LONGITUDE, NULL AS LOB_FILE, NULL AS UUID_TASK_DETAIL_LOB,
				   INT_TEXT_ANSWER, INT_OPTION_TEXT,
				   FIN_TEXT_ANSWER, FIN_OPTION_TEXT,
				   NULL AS LATBRANCH, NULL AS LNGBRANCH,
				   MCC, MNC, LAC, CELL_ID, ACCURACY, UUID_BRANCH, th.UUID_FORM, th.FORM_VERSION
			  FROM TR_TASK_D td with (nolock) inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
			 WHERE td.UUID_TASK_H = :uuidTask
			 UNION
			 SELECT td.UUID_TASK_H, UUID_QUESTION as uuidQuestion,
				   QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
				   IMAGE_PATH, td.LATITUDE, td.LONGITUDE, NULL AS LOB_FILE, NULL AS UUID_TASK_DETAIL_LOB,
				   INT_TEXT_ANSWER, INT_OPTION_TEXT,
				   FIN_TEXT_ANSWER, FIN_OPTION_TEXT,
				   NULL AS LATBRANCH, NULL AS LNGBRANCH,
				   MCC, MNC, LAC, CELL_ID, ACCURACY, UUID_BRANCH, th.UUID_FORM, th.FORM_VERSION
			  FROM FINAL_TR_TASK_D td with (nolock) inner join FINAL_TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
			 WHERE td.UUID_TASK_H = :uuidTask
			UNION
			SELECT td.UUID_TASK_H, QUESTION_ID as uuidQuestion,
				   QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
				   IMAGE_PATH, td.LATITUDE, td.LONGITUDE, 
				   CASE
						WHEN (LOB_FILE is not null) THEN '1'
						ELSE NULL
				   END AS LOB_FILE,
				   UUID_TASK_DETAIL_LOB AS UUID_TASK_DETAIL_LOB,
				   INT_TEXT_ANSWER, INT_OPTION_TEXT,
				   FIN_TEXT_ANSWER, FIN_OPTION_TEXT,
				   NULL AS LATBRANCH, NULL AS LNGBRANCH,
				   MCC, MNC, LAC, CELL_ID, ACCURACY, UUID_BRANCH, th.UUID_FORM, th.FORM_VERSION
			  FROM TR_TASKDETAILLOB td with (nolock) inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
			 WHERE td.UUID_TASK_H = :uuidTask
			 UNION
			SELECT td.UUID_TASK_H, QUESTION_ID as uuidQuestion,
				   QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
				   IMAGE_PATH, td.LATITUDE, td.LONGITUDE, 
				   CASE
						WHEN (LOB_FILE is not null) THEN '1'
						ELSE NULL
				   END AS LOB_FILE,
				   UUID_TASK_DETAIL_LOB AS UUID_TASK_DETAIL_LOB,
				   INT_TEXT_ANSWER, INT_OPTION_TEXT,
				   FIN_TEXT_ANSWER, FIN_OPTION_TEXT,
				   NULL AS LATBRANCH, NULL AS LNGBRANCH,
				   MCC, MNC, LAC, CELL_ID, ACCURACY, UUID_BRANCH, th.UUID_FORM, th.FORM_VERSION
			  FROM FINAL_TR_TASKDETAILLOB td with (nolock) inner join FINAL_TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
			 WHERE td.UUID_TASK_H = :uuidTask
		) rd
		 INNER JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = rd.UUID_FORM AND mfh.FORM_VERSION = rd.FORM_VERSION
		  INNER JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
		  and mfqs.UUID_QUESTION = rd.uuidQuestion
		  INNER JOIN MS_ANSWERTYPE msan with (nolock) ON mfqs.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
		  LEFT JOIN  MS_BRANCH MB with (nolock) ON rd.UUID_BRANCH = MB.UUID_BRANCH
		  LEFT OUTER JOIN MS_ASSETTAG MSAT with (nolock) ON MSAT.UUID_ASSET_TAG = mfqs.UUID_ASSET_TAG
		WHERE mfh.UUID_FORM = :uuidForm
	ORDER BY  mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ
	</sql-query>

	<sql-query name="view.viewresult.taskHistory">
		<query-param name="paramUuidTaskH" type="long" />
		SELECT trths.uuid_task_h, CASE 
				WHEN trths.CODE_PROCESS = '001' THEN 'Assignment' 
				WHEN trths.CODE_PROCESS = '002' THEN 'Re-assignment'
				WHEN trths.CODE_PROCESS = '003' THEN 'Submitted' 
				WHEN trths.CODE_PROCESS = '004' THEN 'Verified' 
				WHEN trths.CODE_PROCESS = '005' THEN 'Approval' 
				WHEN trths.CODE_PROCESS = '006' THEN 'Released'
				WHEN trths.code_process = '007' THEN 'Deleted'
				WHEN trths.CODE_PROCESS = '008' THEN 'Rejected'
				WHEN trths.CODE_PROCESS = '009' THEN 'Rejected on Verification'
				WHEN trths.CODE_PROCESS = '010' THEN 'Rejected on Approval'
				WHEN trths.CODE_PROCESS = '011' THEN 'On Core'
				WHEN trths.CODE_PROCESS = '015' THEN 'Unassigned'
				WHEN trths.CODE_PROCESS = '013' THEN 'Rejected with Re-Survey'
				WHEN trths.CODE_PROCESS = '014' THEN 'Promise to Survey'
				WHEN trths.CODE_PROCESS = '012' THEN 'Canceled'
			END TASK_PROCESS,
			trths.ACTOR, trths.FIELD_PERSON,
			trths.DTM_CRT, 
			trths.notes, mssta.status_task_desc, trths.code_process
		FROM tr_taskhistory trths  with (nolock)
			JOIN ms_statustask mssta with (nolock) ON (trths.uuid_status_task = mssta.uuid_status_task)
		WHERE
			trths.uuid_task_h = :paramUuidTaskH
		UNION
		SELECT trths.uuid_task_h, CASE 
				WHEN trths.CODE_PROCESS = '001' THEN 'Assignment' 
				WHEN trths.CODE_PROCESS = '002' THEN 'Re-assignment'
				WHEN trths.CODE_PROCESS = '003' THEN 'Submitted' 
				WHEN trths.CODE_PROCESS = '004' THEN 'Verified' 
				WHEN trths.CODE_PROCESS = '005' THEN 'Approval' 
				WHEN trths.CODE_PROCESS = '006' THEN 'Released'
				WHEN trths.code_process = '007' THEN 'Deleted'
				WHEN trths.CODE_PROCESS = '008' THEN 'Rejected'
				WHEN trths.CODE_PROCESS = '009' THEN 'Rejected on Verification'
				WHEN trths.CODE_PROCESS = '010' THEN 'Rejected on Approval'
				WHEN trths.CODE_PROCESS = '011' THEN 'On Core'
				WHEN trths.CODE_PROCESS = '015' THEN 'Unassigned'
				WHEN trths.CODE_PROCESS = '013' THEN 'Rejected with Re-Survey'
				WHEN trths.CODE_PROCESS = '014' THEN 'Promise to Survey'
				WHEN trths.CODE_PROCESS = '012' THEN 'Canceled'
			END TASK_PROCESS,
			trths.ACTOR, trths.FIELD_PERSON,
			trths.DTM_CRT, 
			trths.notes, mssta.status_task_desc, trths.code_process
		FROM final_tr_taskhistory trths  with (nolock)
			JOIN ms_statustask mssta with (nolock) ON (trths.uuid_status_task = mssta.uuid_status_task)
		WHERE
			trths.uuid_task_h = :paramUuidTaskH
		ORDER BY DTM_CRT desc
	</sql-query>
	<sql-query name="view.viewresult.detailtasksurvey">
		<query-param name="taskId" type="string" />
			SELECT 
				trth.uuid_task_h, 
				trth.TASK_ID, 
				trth.appl_no,
				msf.FORM_NAME,
				msb.BRANCH_NAME,
				msp.PRIORITY_DESC, 
				trth.NOTES, 
				trth.CUSTOMER_NAME, 
				trth.CUSTOMER_ADDRESS, 
				trth.CUSTOMER_PHONE, 
				trth.ZIP_CODE,  
				LEFT(CONVERT(VARCHAR, trth.assign_date, 113), 17) as assign_date, 
				LEFT(CONVERT(VARCHAR, trth.download_date, 113),17) as download_date,
				LEFT(CONVERT(VARCHAR, trth.submit_date, 113),17) as submit_date, 
				msu.full_name,
				mssta.status_task_desc,
				null,
				LEFT(CONVERT(VARCHAR, trth.send_date, 113),17) as send_date,
				trth.UUID_FORM
			FROM tr_task_h trth with (nolock)
				LEFT OUTER JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user)
				LEFT OUTER JOIN ms_statustask mssta with (nolock) ON(trth.uuid_status_task = mssta.uuid_status_task)
				LEFT OUTER JOIN ms_branch msb with (nolock) ON(trth.uuid_branch = msb.uuid_branch)
				LEFT OUTER join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM
				LEFT OUTER JOIN MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY
			WHERE trth.TASK_ID = :taskId
			UNION ALL
			SELECT
				ftrth.uuid_task_h, 
				ftrth.TASK_ID, 
				ftrth.appl_no,
				msf.FORM_NAME,
				msb.BRANCH_NAME,
				msp.PRIORITY_DESC, 
				ftrth.NOTES, 
				ftrth.CUSTOMER_NAME, 
				ftrth.CUSTOMER_ADDRESS, 
				ftrth.CUSTOMER_PHONE, 
				ftrth.ZIP_CODE,  
				LEFT(CONVERT(VARCHAR, ftrth.assign_date, 113), 17) as assign_date, 
				LEFT(CONVERT(VARCHAR, ftrth.download_date, 113),17) as download_date,
				LEFT(CONVERT(VARCHAR, ftrth.submit_date, 113),17) as submit_date, 
				msu.full_name,
				mssta.status_task_desc,
				null,
				LEFT(CONVERT(VARCHAR, ftrth.send_date, 113),17) as send_date,
				ftrth.UUID_FORM
			FROM final_tr_task_h ftrth with (nolock)
				LEFT OUTER JOIN am_msuser msu with (nolock) ON(ftrth.uuid_ms_user = msu.uuid_ms_user)
				LEFT OUTER JOIN ms_statustask mssta with (nolock) ON(ftrth.uuid_status_task = mssta.uuid_status_task)
				LEFT OUTER JOIN ms_branch msb with (nolock) ON(ftrth.uuid_branch = msb.uuid_branch)
				LEFT OUTER join MS_FORM msf with (nolock) on ftrth.UUID_FORM = msf.UUID_FORM
				LEFT OUTER JOIN MS_PRIORITY msp with (nolock) on ftrth.UUID_PRIORITY = msp.UUID_PRIORITY
			WHERE ftrth.TASK_ID = :taskId
	</sql-query>
	
	<sql-query name="view.viewresult.getDetailGroupTask">
		<query-param name="groupTaskId" type="string" />
		select trth.UUID_TASK_H, TASK_ID, ASSIGN_DATE, SEND_DATE, SUBMIT_DATE, FORM_NAME, STATUS_TASK_DESC
		from MS_GROUPTASK  msgt with (nolock)
			join TR_TASK_H trth with (nolock) on msgt.UUID_TASK_H = trth.UUID_TASK_H
			join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM
			join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		where GROUP_TASK_ID = :groupTaskId
	</sql-query>
	<sql-query name="view.viewresult.getTaskLink">
		<query-param name="groupTaskId" type="string" />
<!-- 	<query-param name="uuidTaskH" type="string" /> -->
		select b.TASK_ID, b.STATUS_TASK_DESC, b.UUID_TASK_H, b.FORM_NAME, b.FLAG
		from(
			select task_id as TASK_ID, STATUS_TASK_DESC as STATUS_TASK_DESC, tt.uuid_task_h as UUID_TASK_H, FORM_NAME as FORM_NAME, '1' as FLAG 
			from MS_GROUPTASK mgr with (nolock)
			inner join tr_task_h tt with (nolock) on tt.UUID_TASK_H = mgr.UUID_TASK_H
			inner join MS_STATUSTASK mst with (nolock) on mst.UUID_STATUS_TASK = tt.UUID_STATUS_TASK
			inner join ms_form mf with (nolock) on mf.uuid_form = tt.uuid_form
			where GROUP_TASK_ID = :groupTaskId
<!-- 		and tt.UUID_TASK_H != :uuidTaskH -->
			UNION ALL
			select task_id as TASK_ID, STATUS_TASK_DESC as STATUS_TASK_DESC, 
				tt.uuid_task_h as UUID_TASK_H, FORM_NAME as FORM_NAME, '2' as FLAG 
			from FINAL_MS_GROUPTASK mgr with (nolock)
				inner join FINAL_tr_task_h tt with (nolock) on tt.UUID_TASK_H = mgr.UUID_TASK_H
				inner join MS_STATUSTASK mst with (nolock) on mst.UUID_STATUS_TASK = tt.UUID_STATUS_TASK
				inner join ms_form mf with (nolock) on mf.uuid_form = tt.uuid_form
			where GROUP_TASK_ID = :groupTaskId
<!-- 		and tt.UUID_TASK_H != :uuidTaskH -->
		)b order by b.task_id desc
	</sql-query>
	<sql-query name="view.viewresult.getTaskUpdate">
	<query-param name="uuidTaskH" type="string" />
		select b.uuid_task_update, b.pending_notes, b.docupro_feedback, b.feedback_notes
		from(
			select uuid_task_update, pending_notes, docupro_feedback, feedback_notes
			from tr_taskupdate ttu with(nolock)
			join MS_GROUPTASK mgr with (nolock) on mgr.UUID_TASK_H = ttu.UUID_TASK_H
			join MS_GROUPTASK mgr2 with (nolock) on mgr2.GROUP_TASK_ID = mgr.GROUP_TASK_ID
			where mgr2.UUID_TASK_H = :uuidTaskH
		)b order by b.uuid_task_update desc
	</sql-query>
	
</hibernate-mapping>