package com.adins.mss.businesslogic.impl.common;

import static org.junit.Assert.*;

import java.lang.reflect.Field;

import org.junit.Before;
import org.junit.Test;

/**
 * JUnit test class for GenericReportTaskPreSurveyLogic new mapping entries
 * Tests the new LMBG_PNYLSN_SNGKT mapping entry
 * Created to achieve 100% code coverage for new lines in revision 6d55721 to 367b8b2
 */
public class GenericReportTaskPreSurveyLogicTest {

    private GenericReportTaskPreSurveyLogic genericReportTaskPreSurveyLogic;
    
    @Before
    public void setUp() {
        genericReportTaskPreSurveyLogic = new GenericReportTaskPreSurveyLogic();
    }
    
    @Test
    public void testNewMappingEntryExists() throws Exception {
        // Test that the new mapping entry for "Lembaga Penyelesaian Sengketa" exists
        // We need to access the private HEADER_REFID field using reflection
        
        Field headerRefIdField = GenericReportTaskPreSurveyLogic.class.getDeclaredField("HEADER_REFID");
        headerRefIdField.setAccessible(true);
        String[][] headerRefId = (String[][]) headerRefIdField.get(null);
        
        // Look for the new mapping entry
        boolean foundLmbgPnylsnSngkt = false;
        String expectedDescription = "Lembaga Penyelesaian Sengketa";
        String expectedCode = "LMBG_PNYLSN_SNGKT";
        
        for (String[] mapping : headerRefId) {
            if (mapping.length >= 2 && 
                expectedDescription.equals(mapping[0]) && 
                expectedCode.equals(mapping[1])) {
                foundLmbgPnylsnSngkt = true;
                break;
            }
        }
        
        assertTrue("LMBG_PNYLSN_SNGKT mapping should exist in HEADER_REFID", foundLmbgPnylsnSngkt);
    }
    
    @Test
    public void testMappingEntryPosition() throws Exception {
        // Test that the new mapping entry is positioned correctly in the array
        Field headerRefIdField = GenericReportTaskPreSurveyLogic.class.getDeclaredField("HEADER_REFID");
        headerRefIdField.setAccessible(true);
        String[][] headerRefId = (String[][]) headerRefIdField.get(null);
        
        // Find the position of the new mapping
        int lmbgPosition = -1;
        int preAsuransiPosition = -1;
        int prePemberitahuanPosition = -1;
        int prePenggunaanDataPosition = -1;
        
        for (int i = 0; i < headerRefId.length; i++) {
            if (headerRefId[i].length >= 2) {
                if ("Maskapai Asuransi".equals(headerRefId[i][0])) {
                    preAsuransiPosition = i;
                } else if ("Persetujuan penerimaan pemberitahuan dari WOM Finance tentang produk, program, dan kegiatan WOM Finance melalui berbagai saluran komunikasi, termasuk email, telepon, SMS, dan media lainnya".equals(headerRefId[i][0])) {
                    prePemberitahuanPosition = i;
                } else if ("Persetujuan penggunaan data pribadi pemohon dalam pemasaran produk WOMF Finance dan produk mitra WOM Finance sesuai peraturan dan hukum yang berlaku".equals(headerRefId[i][0])) {
                    prePenggunaanDataPosition = i;
                } else if ("Lembaga Penyelesaian Sengketa".equals(headerRefId[i][0])) {
                    lmbgPosition = i;
                }
            }
        }
        
        assertTrue("LMBG_PNYLSN_SNGKT mapping should be found", lmbgPosition >= 0);
        assertTrue("PRE_PENGGUNAAN_DATA mapping should be found", prePenggunaanDataPosition >= 0);
        
        // The new mapping should be positioned after PRE_PENGGUNAAN_DATA
        assertTrue("LMBG_PNYLSN_SNGKT should be positioned after PRE_PENGGUNAAN_DATA", 
                   lmbgPosition > prePenggunaanDataPosition);
    }
    
    @Test
    public void testMappingEntryFormat() throws Exception {
        // Test that the new mapping entry has the correct format
        Field headerRefIdField = GenericReportTaskPreSurveyLogic.class.getDeclaredField("HEADER_REFID");
        headerRefIdField.setAccessible(true);
        String[][] headerRefId = (String[][]) headerRefIdField.get(null);
        
        String[] lmbgMapping = null;
        for (String[] mapping : headerRefId) {
            if (mapping.length >= 2 && "Lembaga Penyelesaian Sengketa".equals(mapping[0])) {
                lmbgMapping = mapping;
                break;
            }
        }
        
        assertNotNull("LMBG_PNYLSN_SNGKT mapping should be found", lmbgMapping);
        assertEquals("Mapping should have exactly 2 elements", 2, lmbgMapping.length);
        assertEquals("First element should be the description", "Lembaga Penyelesaian Sengketa", lmbgMapping[0]);
        assertEquals("Second element should be the code", "LMBG_PNYLSN_SNGKT", lmbgMapping[1]);
    }
    
    @Test
    public void testMappingEntryUniqueness() throws Exception {
        // Test that the new mapping entry is unique (no duplicates)
        Field headerRefIdField = GenericReportTaskPreSurveyLogic.class.getDeclaredField("HEADER_REFID");
        headerRefIdField.setAccessible(true);
        String[][] headerRefId = (String[][]) headerRefIdField.get(null);
        
        int lmbgCount = 0;
        for (String[] mapping : headerRefId) {
            if (mapping.length >= 2 && "LMBG_PNYLSN_SNGKT".equals(mapping[1])) {
                lmbgCount++;
            }
        }
        
        assertEquals("LMBG_PNYLSN_SNGKT code should appear exactly once", 1, lmbgCount);
    }
    
    @Test
    public void testMappingEntryCodeFormat() throws Exception {
        // Test that the code follows the expected naming convention
        Field headerRefIdField = GenericReportTaskPreSurveyLogic.class.getDeclaredField("HEADER_REFID");
        headerRefIdField.setAccessible(true);
        String[][] headerRefId = (String[][]) headerRefIdField.get(null);
        
        String lmbgCode = null;
        for (String[] mapping : headerRefId) {
            if (mapping.length >= 2 && "Lembaga Penyelesaian Sengketa".equals(mapping[0])) {
                lmbgCode = mapping[1];
                break;
            }
        }
        
        assertNotNull("LMBG_PNYLSN_SNGKT code should be found", lmbgCode);
        assertTrue("Code should be uppercase", lmbgCode.equals(lmbgCode.toUpperCase()));
        assertTrue("Code should contain underscores", lmbgCode.contains("_"));
        assertFalse("Code should not contain spaces", lmbgCode.contains(" "));
        assertFalse("Code should not be empty", lmbgCode.isEmpty());
    }
    
    @Test
    public void testMappingEntryDescriptionFormat() throws Exception {
        // Test that the description is properly formatted
        Field headerRefIdField = GenericReportTaskPreSurveyLogic.class.getDeclaredField("HEADER_REFID");
        headerRefIdField.setAccessible(true);
        String[][] headerRefId = (String[][]) headerRefIdField.get(null);
        
        String lmbgDescription = null;
        for (String[] mapping : headerRefId) {
            if (mapping.length >= 2 && "LMBG_PNYLSN_SNGKT".equals(mapping[1])) {
                lmbgDescription = mapping[0];
                break;
            }
        }
        
        assertNotNull("LMBG_PNYLSN_SNGKT description should be found", lmbgDescription);
        assertFalse("Description should not be empty", lmbgDescription.isEmpty());
        assertTrue("Description should start with capital letter", 
                   Character.isUpperCase(lmbgDescription.charAt(0)));
        assertEquals("Description should match expected text", 
                     "Lembaga Penyelesaian Sengketa", lmbgDescription);
    }
    
    @Test
    public void testHeaderRefIdArrayIntegrity() throws Exception {
        // Test that adding the new mapping doesn't break the array structure
        Field headerRefIdField = GenericReportTaskPreSurveyLogic.class.getDeclaredField("HEADER_REFID");
        headerRefIdField.setAccessible(true);
        String[][] headerRefId = (String[][]) headerRefIdField.get(null);
        
        assertNotNull("HEADER_REFID should not be null", headerRefId);
        assertTrue("HEADER_REFID should have at least one entry", headerRefId.length > 0);
        
        // Check that all entries have the expected structure
        for (int i = 0; i < headerRefId.length; i++) {
            assertNotNull("Entry " + i + " should not be null", headerRefId[i]);
            assertTrue("Entry " + i + " should have at least 2 elements", headerRefId[i].length >= 2);
            assertNotNull("Entry " + i + " description should not be null", headerRefId[i][0]);
            assertNotNull("Entry " + i + " code should not be null", headerRefId[i][1]);
        }
    }
    
    @Test
    public void testMappingEntryConsistencyWithExistingEntries() throws Exception {
        // Test that the new mapping follows the same pattern as existing entries
        Field headerRefIdField = GenericReportTaskPreSurveyLogic.class.getDeclaredField("HEADER_REFID");
        headerRefIdField.setAccessible(true);
        String[][] headerRefId = (String[][]) headerRefIdField.get(null);
        
        String[] lmbgMapping = null;
        String[] preAsuransiMapping = null;
        
        for (String[] mapping : headerRefId) {
            if (mapping.length >= 2) {
                if ("Lembaga Penyelesaian Sengketa".equals(mapping[0])) {
                    lmbgMapping = mapping;
                } else if ("Maskapai Asuransi".equals(mapping[0])) {
                    preAsuransiMapping = mapping;
                }
            }
        }
        
        assertNotNull("LMBG mapping should be found", lmbgMapping);
        assertNotNull("PRE_ASURANSI mapping should be found", preAsuransiMapping);
        
        // Both should have the same structure
        assertEquals("Both mappings should have same length", 
                     preAsuransiMapping.length, lmbgMapping.length);
    }
}
