package com.adins.mss.businesslogic.impl.common;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.constants.enums.ImageStorageLocation;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath*:META-INF/spring/*-context.xml"
})
@TransactionConfiguration(
        transactionManager="transactionManager"
)
@Ignore
public class ImageStorageLogicTest {
    @Autowired
    private ImageStorageLogic imageStorageLogic;
    
    @Test
    public void retrieveImageStorageLocationTest() {
        ImageStorageLocation isl = imageStorageLogic.retrieveGsImageLocation(null);
//        Assert.assertTrue(ImageStorageLocation.DATABASE == isl);
    }
    
    @Test
    public void retrieveImageLocationPathTest() {
        Path path = imageStorageLogic.retrieveGsImageFileSystemPath(null);
        System.out.println(Paths.get(path.toString(), "2017-08-25"));
    }
    
    @Test
    public void retrieveImageRejectedBlob() throws FileNotFoundException, IOException {
        byte[] image = imageStorageLogic.retrieveRejectedImageBlob(4, true); //db mss-db-svr/AITMSS_SIT
        if (image == null) {
            System.out.println("no rejected image");
            return;
        }
//        IOUtils.write(image, new FileOutputStream("D:\\rejected_final.jpg"));
    }
    
    @Test
    public void retrieveImageFilesystemByTaskD() throws FileNotFoundException, IOException {
        String uuid = "4";
        byte[] image = imageStorageLogic.retrieveImageFileSystemByTaskD(Long.valueOf(uuid), false); //ppd-dbdev-svr/TAFS_MSS_UAT
//        IOUtils.write(image, new FileOutputStream("D:\\image.jpg"));
    }
    
    @Test
    public void retrieveImageFilesystemByFile() throws FileNotFoundException, IOException {
        File file = new File("D:\\TaskImageSubmitted\\2015-12-21\\9049EA07-A7CF-11E5-9B7F-5B08DC735868.jpg");
        byte[] image = imageStorageLogic.retrieveImageFileSystemByFile(file);
//        IOUtils.write(image, new FileOutputStream("D:\\image.jpg"));
    }
    
    @Test
    public void retrieveImageFilesystemByPath() throws FileNotFoundException, IOException {
        Path file = Paths.get("D:\\TaskImageSubmitted", "2015-12-21", "9049EA07-A7CF-11E5-9B7F-5B08DC735868.jpg");
        byte[] image = imageStorageLogic.retrieveImageFileSystemByPath(file);
//        IOUtils.write(image, new FileOutputStream("D:\\image.jpg"));
    }
}
