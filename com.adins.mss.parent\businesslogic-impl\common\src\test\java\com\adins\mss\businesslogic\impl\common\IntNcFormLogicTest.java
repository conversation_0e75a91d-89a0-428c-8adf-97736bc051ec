package com.adins.mss.businesslogic.impl.common;

import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations={
        "classpath*:META-INF/spring/*-context.xml"
})
@TransactionConfiguration(transactionManager="transactionManager", defaultRollback=false)
@Transactional
@Ignore
public class IntNcFormLogicTest {
	@Autowired
	@Qualifier("interfacing.StagingFormBean")
	private IntFormLogic intFormLogic;
	
	@Test
	public void confinsMoCheck() {
		String taskId = "20569";
		String isFinal = "1";
		//intFormLogic.submitResult(new AuditContext(), taskId, isFinal);
	}
	
	@Test
	public void confinsMsCheck() {
		String taskId = "10563";
		String isFinal = "1";
		//intFormLogic.saveResult(new AuditContext(), taskId, "NC", "MS", "12", isFinal);
	}
	
	@Test
	public void confinsMcCheck() {
		String taskId = "20557";
		String isFinal = "1";
		//intFormLogic.saveResult(new AuditContext(), taskId, "NC", "MC", "12", isFinal);
	}
}