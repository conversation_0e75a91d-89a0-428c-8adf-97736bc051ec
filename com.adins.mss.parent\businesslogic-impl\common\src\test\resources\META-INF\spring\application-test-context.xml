<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:tx="http://www.springframework.org/schema/tx"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
    http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx.xsd">

    <bean id="dataSource" class="org.springframework.jdbc.datasource.DriverManagerDataSource">
        <property name="driverClassName" value="net.sourceforge.jtds.jdbc.Driver" />
		<property name="url" value="***************************************************************************************** "/>
        <property name="username" value="sa" />
        <property name="password" value="AdIns2017" />
	</bean>

	<bean id="hibernateSessionFactoryBean" class="org.springframework.orm.hibernate4.LocalSessionFactoryBean">
		<property name="dataSource" ref="dataSource"/>
        <property name="hibernateProperties">
			<props>
				<prop key="hibernate.dialect">org.hibernate.dialect.SQLServerDialect</prop>
                <prop key="hibernate.show_sql">false</prop>
                <prop key="hibernate.format_sql">false</prop>
                <prop key="hibernate.jdbc.use_streams_for_binary">true</prop>
            </props>
        </property>
		<property name="packagesToScan">
			<list>
				<value>com.adins.mss.model</value>
			</list>
		</property>
		<property name="mappingLocations">
			<list>
				<value>classpath*:/com/adins/mss/businesslogic/impl/hbm/*.hbm.xml</value>
			</list>
		</property>
    </bean>
	
	<tx:annotation-driven />
	
	<bean id="transactionManager" class="org.springframework.orm.hibernate4.HibernateTransactionManager">
	    <property name="sessionFactory" ref="hibernateSessionFactoryBean" />
	</bean>

	<bean id="propHolder" class="com.adins.framework.tool.properties.SpringPropertiesUtils">
		<property name="locations">
			<list>
				<value>classpath:application.properties</value>
			</list>
		</property>
	</bean>
 
</beans>