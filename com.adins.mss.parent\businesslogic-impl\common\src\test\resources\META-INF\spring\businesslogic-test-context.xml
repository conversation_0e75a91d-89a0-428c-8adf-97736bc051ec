<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
	http://www.springframework.org/schema/aop
	http://www.springframework.org/schema/aop/spring-aop.xsd">
	
    <bean id="GlobalManagerDAO" class="com.adins.framework.persistence.dao.hibernate.manager.global.GlobalManagerDao" scope="singleton">
		<property name="sessionFactory" ref="hibernateSessionFactoryBean"/>
    </bean>

    <bean id="AuditlogLogicBean" class="com.adins.mss.base.businesslogic.AuditlogLogic" scope="singleton">
     	<property name="managerDAO" ref="GlobalManagerDAO"/>
    </bean>
    
    <bean class="org.springframework.beans.factory.config.MethodInvokingFactoryBean">
	    <property name="staticMethod" value="com.adins.framework.persistence.dao.hibernate.session.GlobalSessionFactory.setAuditManagerInstance"/>
	    <property name="arguments">
	        <list>
	            <ref bean="AuditlogLogicBean" />
	        </list>
	   </property>
	</bean>
    
	<bean id="BaseLogicBean" class="com.adins.mss.base.businesslogic.BaseLogic" scope="singleton">
     	<property name="managerDAO" ref="GlobalManagerDAO"/>
    </bean>

    <bean id="GenericImageStorageLogicBean" class="com.adins.mss.businesslogic.impl.common.GenericImageStorageLogic" scope="singleton" parent="BaseLogicBean" />
    
</beans>