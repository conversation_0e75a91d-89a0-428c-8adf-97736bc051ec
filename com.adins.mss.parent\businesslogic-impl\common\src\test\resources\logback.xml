<?xml version="1.0" encoding="UTF-8"?>

<configuration debug="false" >

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%date{yyyy-MM-dd HH:mm:ss.SSS} %level:[%thread] \(%line\)%logger{0}.%method: %message%n</pattern>
    </encoder>
  </appender>

  <logger name="org.apache.struts2.util.TextProviderHelper" level="ERROR"/>
  <logger name="com.adins.framework.mvc.struts2.interceptor.PushPullParamsInterceptor" level="DEBUG"/>
  <logger name="com.adins.framework.service.base.aspect.AuditContextAspect" level="DEBUG"/>
  <logger name="com.adins" level="TRACE"/>
  <logger name="org.hibernate" level="WARN"/>
  
  <root level="INFO">
    <appender-ref ref="STDOUT"/>
  </root>
</configuration>