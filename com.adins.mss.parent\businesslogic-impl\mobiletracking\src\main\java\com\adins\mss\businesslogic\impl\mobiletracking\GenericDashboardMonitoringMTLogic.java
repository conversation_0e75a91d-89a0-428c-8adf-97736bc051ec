package com.adins.mss.businesslogic.impl.mobiletracking;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.persistence.dao.model.AuditInfo;
import com.adins.framework.tool.lang.FormatterUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.mobiletracking.DashboardMonitoringMTLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrLocationhistory;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.custom.DashboardMonitoringBean;

@SuppressWarnings({ "rawtypes", "unchecked", "deprecation" })
public class GenericDashboardMonitoringMTLogic extends BaseLogic implements
		DashboardMonitoringMTLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericDashboardMonitoringMTLogic.class);
	private AuditInfo auditInfo;

	@Autowired
	private CommonLogic commonLogic;

	public GenericDashboardMonitoringMTLogic() {
		String[] pkCols = { "uuidTaskH" };
		String[] pkDbCols = { "UUID_TASK_H" };
		String[] cols = { "uuidTaskH", "amMsuser.uuidMsUser", "msStatustask.uuidStatusTask", "assignDate", "downloadDate", "readDate", "startDtm" };
		String[] dbCols = { "UUID_TASK_H", "UUID_MS_USER", "UUID_STATUS_TASK",
				"ASSIGN_DATE", "DOWNLOAD_DATE", "READ_DATE", "START_DTM" };
		this.auditInfo = new AuditInfo("TR_TASK_H", pkCols, pkDbCols, cols,
				dbCols);
	}

	@Transactional(readOnly = true)
	@Override
	public List<DashboardMonitoringBean> getUser(String uuidUser,
			String uuidSubSystem, String modeMap, String startDate,
			String endDate, AuditContext callerId) throws ParseException {
		List<DashboardMonitoringBean> result = null;
		String[][] params = { { "uuidSPV", uuidUser } };
		Date minDate = null;
		Date maxDate = null;
		try {
			this.getManagerDAO().startSession();
			List list = this.getUserList(params, modeMap, callerId);

			List<DashboardMonitoringBean> dmBean = new ArrayList<DashboardMonitoringBean>();
			SimpleDateFormat formatter = new SimpleDateFormat(
					"yyyy-MM-dd HH:mm:ss");
			for (int i = 0; i < list.size(); i++) {
				Map temp = (Map) list.get(i);

				if ("TR".equals(modeMap)) {
					minDate = formatter.parse(startDate);
					maxDate = formatter.parse(endDate);
				}

				if ("DM".equals(modeMap)) {
					DateTime currentTime = new DateTime();
					currentTime = currentTime.minusMillis(currentTime
							.getMillisOfDay());
					minDate = currentTime.toDate();
					currentTime = currentTime.plusDays(1).minusMillis(3);
					maxDate = currentTime.toDate();
				}

				Object[][] paramsTrack = {
						{ Restrictions.eq("amMsuser.uuidMsUser", temp.get("d0")
								.toString()) },
						{ Restrictions.between("datetime", minDate, maxDate) } };
				String[][] orderTrack = { { "datetime",
						GlobalVal.ROW_ORDER_DESC } };

				Map<String, Object> mapTrack = this.getManagerDAO().list(
						TrLocationhistory.class, paramsTrack, orderTrack);
				List<TrLocationhistory> listTrlh = (List<TrLocationhistory>) mapTrack
						.get(GlobalKey.MAP_RESULT_LIST);

				DashboardMonitoringBean bean = new DashboardMonitoringBean();				

				bean.setUuidMsUser(Long.parseLong(temp.get("d0").toString()));
				bean.setFullName(temp.get("d1").toString());
				bean.setIsLoggedIn(temp.get("d2").toString());
				bean.setInitialName(temp.get("d3").toString());
				bean.setTracking(listTrlh);
				if ("DM".equals(modeMap)) {
					String[][] paramsUuid = {
							{ "uuidUser", String.valueOf(bean.getUuidMsUser()) },
							{ "start", DateFormatUtils.format(new Date(), "yyyy-MM-dd") + " 00:00:00.000" },
							{ "end", DateFormatUtils.format(new Date(), "yyyy-MM-dd") + " 23:59:59.997" } };					
					bean.setCurrentDate(DateFormatUtils.format(new Date(), "dd MMMM yyyy"));
					bean.setAttendanceIn((String) this.getManagerDAO().selectOneNative(
							"survey.dm.getAttendanceIn", paramsUuid));
					bean.setLastTimeDetected(!listTrlh.isEmpty()
							? DateFormatUtils.format(listTrlh.get(0).getDtmCrt(), "hh:mm:ss") : "-");					
					bean = getTaskRecapitulation(String.valueOf(bean.getUuidMsUser()),
							uuidSubSystem, bean, callerId);
				}
				dmBean.add(bean);
			}
			result = dmBean;
		} finally {
			this.getManagerDAO().endSession();
		}
		return result;
	}

	private List getUserList(Object params, String modeMap,
			AuditContext callerId) {
		List result = null;
		if ("DM".equals(modeMap)) {
			result = this.getManagerDAO().selectAllNative(
					"mobiletracking.dm.getUser", params, null);
		} else if ("TR".equals(modeMap)) {
			result = this.getManagerDAO().selectAllNative(
					"mobiletracking.dm.getUserTR", params, null);
		}
		return result;
	}

	private List getUserList2(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"mobiletracking.dm.getUser2", params, null);
		return result;
	}

	public List getUndoneTask(String uuidUser, String uuidSubSystem,
			String currentDate, AuditContext callerId) {
		List result = null;
		String[][] params = { { "uuidUser", uuidUser },
				{ "uuidSubSystem", uuidSubSystem },
				{ "end", currentDate + " 23:59:59.997" } };
		result = this.getManagerDAO().selectAllNative(
				"mobiletracking.dm.undoneTask", params, null);
		return result;
	}

	public List getCollectedLocation(String uuidUser, String uuidSubSystem,
			String currentDate, AuditContext callerId) {
		List result = null;
		String[][] params = { { "uuidSPV", uuidUser },
				{ "uuidSubSystem", uuidSubSystem },
				{ "start", currentDate + " 00:00:00.000" },
				{ "end", currentDate + " 23:59:59.997" } };
		result = this.getManagerDAO().selectAllNative(
				"mobiletracking.dm.collectedLocation2", params, null);
		return result;
	}

	private DashboardMonitoringBean getTaskRecapitulation(String uuidColl,
			String idSubsystem, DashboardMonitoringBean bean,
			AuditContext callerId) {
		Object[][] params = {
				{ "uuidUser", uuidColl },
				{ "idSubsystem", idSubsystem },
				{
						"start",
						DateFormatUtils.format(new Date(), "dd MMMM yyyy")
								+ " 00:00:00.000" },
				{
						"end",
						DateFormatUtils.format(new Date(), "dd MMMM yyyy")
								+ " 23:59:59.997" } };
		Object[] taskRecapitulation = (Object[]) this.getManagerDAO()
				.selectOneNative("mobiletracking.dm.getTaskRecapitulation",
						params);
		bean.setCountNewTask((int) taskRecapitulation[0]);
		bean.setCountRead((int) taskRecapitulation[1]);
		bean.setCountDownloaded((int) taskRecapitulation[2]);
		bean.setCountTaskSubmitted((int) taskRecapitulation[3]);
		bean.setCountUploadingImg((int) taskRecapitulation[4]);
		bean.setCountOutVer((int) taskRecapitulation[5]);
		bean.setCountStart((int) taskRecapitulation[6]);
		return bean;
	}

	@Transactional(readOnly = true)
	@Override
	public List getTaskMonitoring(String uuidColl, String uuidSubsystem,
			AuditContext callerId) {
		List result = null;
		try {
			this.getManagerDAO().startSession();
			String[][] params = {
					{ "uuidColl", uuidColl },
					{ "uuidSubsystem", uuidSubsystem },
					{
							"start",
							DateFormatUtils.format(new Date(), "dd MMMM yyyy")
									+ " 00:00:00.000" },
					{
							"end",
							DateFormatUtils.format(new Date(), "dd MMMM yyyy")
									+ " 23:59:59.997" } };
			result = this.getManagerDAO().selectAllNative(
					"mobiletracking.dm.taskmonitoring.load", params, null);
		} finally {
			this.getManagerDAO().endSession();
		}
		return result;
	}

	@Transactional(readOnly = true)
	@Override
	public AmMsuser getUser(String uuidColl, AuditContext callerId) {
		AmMsuser result = null;
		try {
			this.getManagerDAO().startSession();
			if (uuidColl.contains("@"))
				uuidColl = uuidColl.substring(0, uuidColl.indexOf("@"));
			result = this.getManagerDAO().selectOne(AmMsuser.class, uuidColl);
			this.getManagerDAO().fetch(result.getAmMsuser());
		} finally {
			this.getManagerDAO().endSession();
		}
		return result;
	}

	private String getIdStatusTask(String statusTask, String idSubsystem) {
		Object[][] params = {
				{ Restrictions.eq("statusCode", statusTask) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", idSubsystem) } };
		MsStatustask result = this.getManagerDAO().selectOne(
				MsStatustask.class, params);
		return String.valueOf(result.getUuidStatusTask());
	}

	private TrTaskH updateAssignIn(TrTaskH obj, String idColl, AmMsuser actor,
			MsStatustask taskDisribute, AuditContext callerId) {
		obj.setMsStatustask(taskDisribute);
		obj.setApprovalDate(null);
		obj.setDownloadDate(null);
		obj.setSubmitDate(null);
		obj.setReadDate(null);
		obj.setStartDtm(null);
		obj.setRfaDate(null);
		obj.setSubmitDate(null);
		obj.setAssignDate(new Date());
		obj.setAmMsuser(this.getManagerDAO().selectOne(AmMsuser.class, idColl));
		obj.setUsrUpd(callerId.getCallerId());
		obj.setDtmUpd(new Date());
		this.auditManager.auditEdit(obj, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(obj);
		return obj;
	}

	private void insertTaskHistory(TrTaskH task, String note, String actor,
			AmMsuser fieldPerson, String codeProcess, AuditContext callerId) {
		TrTaskhistory obj = new TrTaskhistory();
		obj.setDtmCrt(new Date());
		obj.setUsrCrt(callerId.getCallerId());
		obj.setActor(actor);
		obj.setFieldPerson(fieldPerson.getFullName());
		obj.setNotes(note);
		obj.setTrTaskH(task);
		obj.setMsStatustask(task.getMsStatustask());
		obj.setCodeProcess(codeProcess);
		this.getManagerDAO().insert(obj);
	}

	private void updateAssignOut(TrTaskH obj, String idColl, AmMsuser actor,
			MsStatustask taskDistribute, AuditContext callerId) {
		obj.setAmMsuser(this.getManagerDAO().selectOne(AmMsuser.class, idColl));
		obj.setMsStatustask(taskDistribute);
		obj.setAssignDate(new Date());
		obj.setDownloadDate(null);
		obj.setApprovalDate(null);
		obj.setReadDate(null);
		obj.setSubmitDate(null);
		obj.setStartDtm(null);
		obj.setUsrUpd(callerId.getCallerId());
		obj.setDtmUpd(new Date());
		this.auditManager.auditEdit(obj, auditInfo, callerId.getCallerId(), "");
		this.getManagerDAO().update(obj);
	}

	@Override
	public DashboardMonitoringBean collectionTracking(String uuidColl,
			String startDate, String endDate, AuditContext callerId) {
		DashboardMonitoringBean result = new DashboardMonitoringBean();
		try {
			this.getManagerDAO().startSession();
			AmMsuser collUser = this.getManagerDAO().selectOne(AmMsuser.class,
					uuidColl);
			result.setUuidMsUser(collUser.getUuidMsUser());
			result.setFullName(collUser.getFullName());
			result.setIsLoggedIn(collUser.getIsLoggedIn());
			result = getCollArea(result, uuidColl);
			result = getTracking(result, uuidColl, new Date(startDate),
					new Date(endDate));
		} finally {
			this.getManagerDAO().endSession();
		}
		return result;
	}

	private DashboardMonitoringBean getCollArea(DashboardMonitoringBean bean,
			String uuidUser) {
		String[][] paramsArea = { { "uuidUser", uuidUser } };
		List areaList = this.getManagerDAO().selectAllNative(
				"mobiletracking.dm.getArea", paramsArea, null);
		BigDecimal[][] areaLoc = new BigDecimal[areaList.size()][2];
		for (int i = 0; i < areaList.size(); i++) {
			Map tempArea = (Map) areaList.get(i);
			if (i == 0) {
				bean.setAreaName((String) tempArea.get("d0"));
				bean.setAreaType((String) tempArea.get("d1"));
				bean.setRadius((int) tempArea.get("d2"));
				bean.setLatitude(new BigDecimal(tempArea.get("d3").toString()));
				bean.setLongitude(new BigDecimal(tempArea.get("d4").toString()));
			}
			if (tempArea.get("d5") != null && tempArea.get("d6") != null) {
				areaLoc[i][0] = new BigDecimal(tempArea.get("d5").toString());
				areaLoc[i][1] = new BigDecimal(tempArea.get("d6").toString());
			}
		}
		bean.setArea(areaLoc);
		return bean;
	}

	private DashboardMonitoringBean getTracking(DashboardMonitoringBean bean,
			String uuidUser, Date startDate, Date endDate) {
		Object[][] paramsTrack = {
				{ Restrictions.eq("amMsuser.uuidMsUser", uuidUser) },
				{ Restrictions.between("datetime", startDate, endDate) } };
		String[][] orderTrack = { { "datetime", GlobalVal.ROW_ORDER_DESC } };
		Map track = this.getManagerDAO().list(TrLocationhistory.class,
				paramsTrack, orderTrack);
		List<TrLocationhistory> trackingList = (List) track
				.get(GlobalKey.MAP_RESULT_LIST);
		if (!trackingList.isEmpty()) {
			bean.setLatitude(trackingList.get(0).getLatitude());
			bean.setLongitude(trackingList.get(0).getLongitude());
		}
		bean.setTracking(trackingList);
		return bean;
	}

	@Transactional(readOnly = true)
	@Override
	public List<DashboardMonitoringBean> retrieveCollectionTagging(
			String uuidSpv, String uuidSubsystem, AuditContext callerId) {
		List<DashboardMonitoringBean> result = new ArrayList<>();
		result.addAll(this.fetchUndoneTask(uuidSpv, uuidSubsystem, callerId));
		result.addAll(this.fetchCollected(uuidSpv, uuidSubsystem, callerId));
		return result;
	}

	private List<DashboardMonitoringBean> fetchUndoneTask(String uuidSpv,
			String uuidSubSystem, AuditContext callerId) {
		if (StringUtils.isBlank(uuidSpv) || StringUtils.isBlank(uuidSubSystem))
			return Collections.emptyList();
		String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
		List undoneTask = this.getUndoneTask(uuidSpv, uuidSubSystem,
				currentDate, callerId);
		if (undoneTask == null || undoneTask.isEmpty())
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		BigDecimal lat = null;
		BigDecimal lng = null;

		for (int i = 0; i < undoneTask.size(); i++) {
			Map mapUndoneTask = (Map) undoneTask.get(i);
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setUuidTaskH((String) mapUndoneTask.get("d0"));
			bean.setCustName((String) mapUndoneTask.get("d1"));
			bean.setCustAddress((String) mapUndoneTask.get("d2"));

			if (null == mapUndoneTask.get("d3"))
				lat = null;
			else
				lat = (BigDecimal) mapUndoneTask.get("d3");
			bean.setLatitude(lat);

			if (null == mapUndoneTask.get("d4"))
				lng = null;
			else
				lng = (BigDecimal) mapUndoneTask.get("d4");
			bean.setLongitude(lng);

			bean.setFlag("UT");
			bean.setInitialName((String) mapUndoneTask.get("d5"));
			bean.setGroupSeq((String) mapUndoneTask.get("d6"));
			bean.setTaskSeq((String) mapUndoneTask.get("d7"));
			String[][] paramsUuid = { { "uuidTaskH",
					(String) mapUndoneTask.get("d0") } };

			// bean.setCollectedReceived(formatKurs.format((BigDecimal)this.getManagerDAO().selectOneNative("collection.dm.paymentCollectedTask",
			// paramsUuid)));
			result.add(bean);
		}

		return result;
	}

	private List<DashboardMonitoringBean> fetchCollected(String uuidSpv,
			String uuidSubSystem, AuditContext callerId) {
		if (StringUtils.isBlank(uuidSpv) || StringUtils.isBlank(uuidSubSystem))
			return Collections.emptyList();

		String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
		List collLocation = getCollectedLocation(uuidSpv, uuidSubSystem,
				currentDate, callerId);
		if (collLocation == null || collLocation.isEmpty())
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		BigDecimal lat = null;
		BigDecimal lng = null;

		for (int i = 0; i < collLocation.size(); i++) {
			Map mapCollLocation = (Map) collLocation.get(i);
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setUuidTaskH((String) mapCollLocation.get("d0"));
			bean.setCustName((String) mapCollLocation.get("d1"));
			bean.setCustAddress((String) mapCollLocation.get("d2"));
			bean.setApplNo((String) mapCollLocation.get("d3"));

			if (null == mapCollLocation.get("d4"))
				lat = null;
			else
				lat = (BigDecimal) mapCollLocation.get("d4");
			bean.setLatitude(lat);

			if (null == mapCollLocation.get("d5"))
				lng = null;
			else
				lng = (BigDecimal) mapCollLocation.get("d5");
			bean.setLongitude(lng);

			bean.setFlag("CL");
			bean.setInitialName((String) mapCollLocation.get("d6"));
			bean.setGroupSeq((String) mapCollLocation.get("d7"));
			bean.setTaskSeq((String) mapCollLocation.get("d8"));

			String[][] paramsUuid = { { "uuidTaskH",
					mapCollLocation.get("d0").toString() } };
			BigDecimal totalPaymentTask = (BigDecimal) this.getManagerDAO()
					.selectOneNative("collection.dm.totalPaymentTask",
							paramsUuid);
			bean.setTotalPayment(toNumberFormatted(totalPaymentTask));

			BigDecimal paymentCollTask = (BigDecimal) this.getManagerDAO()
					.selectOneNative("collection.dm.paymentCollectedTask",
							paramsUuid);
			bean.setCollectedReceived(toNumberFormatted(paymentCollTask));
			result.add(bean);
		}

		return result;
	}

	@Transactional(readOnly = true)
	@Override
	public DashboardMonitoringBean retrieveInfoWindowData(String uuidCollector,
			String uuidSubsystem, AuditContext callerId) {
		if (StringUtils.isBlank(uuidCollector))
			return null;

		Date sysdate = new Date();
		String[][] paramsUuid = {
				{ "uuidUser", uuidCollector },
				{
						"start",
						DateFormatUtils.format(sysdate, "yyyy-MM-dd")
								+ " 00:00:00.000" },
				{
						"end",
						DateFormatUtils.format(sysdate, "yyyy-MM-dd")
								+ " 23:59:59.997" } };
		Object[] calculation = (Object[]) this.getManagerDAO().selectOneNative(
				"collection.dm.calculation", paramsUuid);

		DashboardMonitoringBean bean = new DashboardMonitoringBean();
		bean.setCurrentDate(DateFormatUtils.format(sysdate, "dd MMMM yyyy"));
		bean.setAttendanceIn((String) this.getManagerDAO().selectOneNative(
				"survey.dm.getAttendanceIn", paramsUuid));
		bean.setTotalPayment(toNumberFormatted(calculation == null ? null
				: calculation[0]));
		bean.setCollectedReceived(toNumberFormatted(calculation == null ? null
				: calculation[1]));
		bean.setNumberOfDeposited((Integer) (calculation == null ? 0
				: calculation[2]));
		bean.setLastDepositTime((String) this.getManagerDAO().selectOneNative(
				"collection.dm.lastDepositTime", paramsUuid));
		bean.setTotalDeposited(toNumberFormatted(calculation == null ? null
				: calculation[3]));
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class,
				uuidCollector);
		bean.setCashOnHandSvy(toNumberFormatted((user.getCashOnHand()==null) ? 0 : user.getCashOnHand()));
		// bean.setCashOnHand(toNumberFormatted(calculation == null ? null :
		// Double.valueOf(calculation[1].toString()) -
		// Double.valueOf(calculation[3].toString())));
		bean = this.getTaskRecapitulation(uuidCollector, uuidSubsystem, bean,
				callerId);

		return bean;
	}

	private String toNumberFormatted(Object obj) {
		if (obj == null || !NumberUtils.isNumber(String.valueOf(obj)))
			obj = NumberUtils.DOUBLE_ZERO;

		return FormatterUtils.getNumberFormat(FormatterUtils.NFORMAT_US_2)
				.format(obj);
	}

	private Double getDouble(String data) {
		return Double.valueOf(StringUtils.isNotBlank(data) ? data : "0");
	}

	@Transactional(readOnly = true)
	@Override
	public boolean checkIsSpv(AmMsuser amMsUser, AuditContext auditContext) {
		this.getManagerDAO().fetch(amMsUser.getMsJob());
		Object[][] params = { { Restrictions.eq("gsCode",
				GlobalVal.SUBSYSTEM_MC + GlobalKey.GENERALSETTING_JOBSPV) } };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, params);
		if (amGeneralsetting.getGsValue().equalsIgnoreCase(
				amMsUser.getMsJob().getJobCode())) {
			return true;
		}
		return false;
	}

	@Transactional(readOnly = true)
	@Override
	public AmMsuser getAmSpv(String uuidSpv, AuditContext auditContext) {
		AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class,
				uuidSpv);
		if (null != amMsUser) {
			this.getManagerDAO().fetch(amMsUser.getAmMssubsystem());
			this.getManagerDAO().fetch(amMsUser.getMsBranch());
		}
		return amMsUser;
	}

	@Transactional(readOnly = true)
	@Override
	public Map<String, Object> getHierarkiBranchLogin(Object params,
			Object orders, int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String, Object>();
		try {
			this.getManagerDAO().startSession();
			String[][] tempParam = (String[][]) params;
			List listCriteria = new ArrayList();
			Integer countCriteria = 0;

			if (StringUtils.isBlank(tempParam[0][1])) {
				tempParam[0][1] = "%";
			}
			if (StringUtils.isBlank(tempParam[1][1])) {
				tempParam[1][1] = "%";
			}

			String[][] queryParamList = {
					{ tempParam[0][0], tempParam[0][1] },
					{ tempParam[1][0], tempParam[1][1] },
					{ tempParam[2][0], tempParam[2][1] },
					{ "start", String.valueOf((pageNumber - 1) * pageSize + 1) },
					{
							"end",
							String.valueOf((pageNumber - 1) * pageSize
									+ pageSize) } };
			String[][] queryParamCount = {
					{ tempParam[0][0], tempParam[0][1] },
					{ tempParam[1][0], tempParam[1][1] },
					{ tempParam[2][0], tempParam[2][1] } };

			String queryListName = "mobiletracking.dm.getListHierarkiCabang";
			String queryCountName = "mobiletracking.dm.getListHierarkiCabangCount";

			listCriteria = this.getManagerDAO().selectAllNative(queryListName,
					queryParamList, null);
			countCriteria = (Integer) this.getManagerDAO().selectOneNative(
					queryCountName, queryParamCount);

			result.put(GlobalKey.MAP_RESULT_LIST, listCriteria);
			result.put(GlobalKey.MAP_RESULT_SIZE, countCriteria);

		} finally {
			this.getManagerDAO().endSession();
		}
		return result;
	}

	@Transactional(readOnly = true)
	@Override
	public Map getSpvByBranch(String uuidBranch, int pageNo, int pageSize,
			AuditContext auditContext) {
		Map mapResult = new HashMap();
		Object[][] paramsJob = { { Restrictions.eq("gsCode",
				GlobalVal.SUBSYSTEM_MT + GlobalKey.GENERALSETTING_JOBSPV) } };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, paramsJob);
		Object[][] params = { { "start", (pageNo - 1) * pageSize + 1 },
				{ "end", (pageNo - 1) * pageSize + pageSize },
				{ "jobCode", amGeneralsetting.getGsValue() },
				{ "uuidBranch", uuidBranch } };
		List result = this.getManagerDAO().selectAllNative(
				"mobiletracking.dm.getSpvListByBranch", params, null);

		Object[][] paramsCount = {
				{ "jobCode", amGeneralsetting.getGsValue() },
				{ "uuidBranch", uuidBranch } };
		Integer total = (Integer) this.getManagerDAO().selectOneNative(
				"mobiletracking.dm.getSpvListByBranchCount", paramsCount);

		mapResult.put(GlobalKey.MAP_RESULT_LIST, result);
		mapResult.put(GlobalKey.MAP_RESULT_SIZE, total);
		return mapResult;
	}

	public String getLimitCohEnabled(String gsCode) {
		String limitCohEnabled = "0";
		try {
			this.getManagerDAO().startSession();
			Object[][] params = { { Restrictions.eq("gsCode", gsCode) } };
			AmGeneralsetting result = this.getManagerDAO().selectOne(
					AmGeneralsetting.class, params);
			if (result != null) {
				limitCohEnabled = result.getGsValue();
			}
		} finally {
			this.getManagerDAO().endSession();
		}
		return limitCohEnabled;
	}

	@Override
	@Transactional(readOnly = true)
	public List<DashboardMonitoringBean> retrieveMobileLocation(long uuidSpv,
			long uuidSubsytem, AuditContext callerId) {
		try {
			this.getManagerDAO().startSession();

			List<DashboardMonitoringBean> userList = this
					.retrieveSubordinate(uuidSpv);

			AmMsuser spv = this.getManagerDAO().selectOne(AmMsuser.class,
					uuidSpv);
			MsBranch msBranch = spv.getMsBranch();

			for (Iterator<DashboardMonitoringBean> iterator = userList
					.iterator(); iterator.hasNext();) {
				DashboardMonitoringBean bean = iterator.next();

				String timestamp = this.commonLogic.getLastLocationTimestamp(
						bean.getUuidMsUser(), uuidSubsytem, callerId);

				// user's lastLocation attribute not found, set default to
				// area's LatLng
				if (StringUtils.isBlank(timestamp)
						|| !this.isTimestampToday(timestamp,
								GlobalVal.FORMAT_DM_TIMESTAMP)) {
					List<DashboardMonitoringBean> listAreaOfUser = this
							.retrieveArea(String.valueOf(bean.getUuidMsUser()));

					if (listAreaOfUser == null || listAreaOfUser.isEmpty()) {
						// iterator.remove(); //user will not be shown on map
						bean.setLatitude(msBranch.getLatitude());
						bean.setLongitude(msBranch.getLongitude());
						bean.setLastTimeDetected("-");
						continue;
					}

					DashboardMonitoringBean area1 = listAreaOfUser.get(0);
					bean.setLatitude(area1.getLatitude());
					bean.setLongitude(area1.getLongitude());
					bean.setLastTimeDetected("-");
				} else {
					Date date = this.timestampToDate(timestamp,
							GlobalVal.FORMAT_DM_TIMESTAMP);
					String lastTimeDetected = FormatterUtils.formatDate(date,
							GlobalVal.FORMAT_DM_LAST_DETECTED);
					bean.setLastTimeDetected(lastTimeDetected);

					// if timestamp found, lastLoc is assumed exists too
					String lastLoc = this.commonLogic.getLastLocation(
							bean.getUuidMsUser(), uuidSubsytem, callerId);
					String[] latLng = StringUtils.split(lastLoc, ',');
					bean.setLatitude(new BigDecimal(latLng[0]));
					bean.setLongitude(new BigDecimal(latLng[1]));
				}
			}

			return userList;
		} finally {
			this.getManagerDAO().endSession();
		}
	}

	@Transactional(readOnly = true)
	@Override
	public String getTrackingInterval(AuditContext callerId) {
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class,
				callerId.getCallerId());
		this.getManagerDAO().fetch(user.getAmMssubsystem());
		return this.getGsValue(user.getAmMssubsystem().getSubsystemName()
				+ GlobalKey.GENERALSETTING_TRACKING_INTERVAL);
	}

	private String getGsValue(String gsCode) {
		try {
			this.getManagerDAO().startSession();
			String[][] params = { { "gsCode", gsCode } };
			List<Map<String, Object>> result = this.getManagerDAO()
					.selectAllNative("common.getGsValue", params, null);

			for (Map<String, Object> gs : result) {
				return (String) gs.get("d0");
			}
		} finally {
			this.getManagerDAO().endSession();
		}

		return null;
	}

	@Transactional(readOnly = true)
	@Override
	public List<DashboardMonitoringBean> retrieveLocationHistory(
			String[] uuidMobileUsers, Date startDate, Date endDate,
			AuditContext callerId) {
		if (ArrayUtils.isEmpty(uuidMobileUsers) || startDate == null
				|| endDate == null)
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		for (String uuidUser : uuidMobileUsers) {
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setUuidMsUser(Long.valueOf(uuidUser));
			List<TrLocationhistory> tracking = this.retrieveLocationHistory(
					uuidUser, startDate, endDate, callerId);
			bean.setTracking(tracking);
			result.add(bean);
		}

		return result;
	}

	@Transactional(readOnly = true)
	@Override
	public List<TrLocationhistory> retrieveLocationHistory(
			String uuidMobileUser, Date startDate, Date endDate,
			AuditContext callerId) {
		if (StringUtils.isBlank(uuidMobileUser) || startDate == null
				|| endDate == null)
			return Collections.emptyList();

		Object[][] paramsTrack = {
				{ Restrictions.eq("amMsuser.uuidMsUser", uuidMobileUser) },
				{ Restrictions.between("datetime", startDate, endDate) } };
		String[][] orderTrack = { { "datetime", GlobalVal.ROW_ORDER_DESC } };

		Map<String, Object> mapTrack = this.getManagerDAO().list(
				TrLocationhistory.class, paramsTrack, orderTrack);

		return (List<TrLocationhistory>) mapTrack
				.get(GlobalKey.MAP_RESULT_LIST);
	}

	@Transactional(readOnly = true)
	private List<DashboardMonitoringBean> retrieveSubordinate(long uuidSpv) {
		Object[][] params = { { "uuidSpv", uuidSpv } };
		List<Map<String, Object>> queryResultList = this.getManagerDAO()
				.selectAllNative("common.dashboard.getUsers", params, null);

		if (queryResultList == null || queryResultList.isEmpty())
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		for (Map<String, Object> user : queryResultList) {
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setUuidMsUser((Long) user.get("d0"));
			bean.setFullName((String) user.get("d1"));
			bean.setIsLoggedIn((String) user.get("d2"));
			bean.setLoginId((String) user.get("d3"));
			bean.setInitialName((String) user.get("d4"));

			AmMsuser beanSpv = new AmMsuser();

			beanSpv.setLoginId((String) user.get("d5"));
			beanSpv.setFullName((String) user.get("d6"));

			bean.setAmMsuser(beanSpv);

			result.add(bean);
		}

		return result;
	}

	private boolean isTimestampToday(String timestamp, String format) {
		if (StringUtils.isBlank(timestamp))
			return false;

		try {
			Date date = FormatterUtils.parseDate(timestamp, format);
			return (new DateTime(date).toLocalDate()).equals(new LocalDate());
		} catch (ParseException pe) {
			return false;
		}
	}

	private List<DashboardMonitoringBean> retrieveArea(String uuidUser) {
		String[][] params = { { "uuidUser", uuidUser } };
		List<Map<String, Object>> queryResultList = this
				.getManagerDAO()
				.selectAllNative("common.dashboard.getAreaOfUser", params, null);

		if (queryResultList == null || queryResultList.isEmpty())
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		for (Map<String, Object> area : queryResultList) {
			DashboardMonitoringBean bean = new DashboardMonitoringBean();
			bean.setAreaType((String) area.get("d0"));
			bean.setLatitude((BigDecimal) area.get("d1"));
			bean.setLongitude((BigDecimal) area.get("d2"));
			if (GlobalVal.AREA_CIRCLE.equalsIgnoreCase(bean.getAreaType())) {
				bean.setRadius((area.get("d3") == null) ? 0 : ((Integer) area
						.get("d3")).intValue());
			}

			result.add(bean);
		}

		return result;
	}

	private Date timestampToDate(String timestamp, String format) {
		try {
			return FormatterUtils.parseDate(timestamp, format);
		} catch (ParseException pe) {
			return null;
		}
	}

	@Transactional(readOnly = true)
	@Override
	public DashboardMonitoringBean retrieveLocationHistoryMT(
			String uuidMobileUser, Date startDate, Date endDate,
			AuditContext callerId) {
		if (StringUtils.isBlank(uuidMobileUser) || startDate == null
				|| endDate == null)
			return null;

		DashboardMonitoringBean bean = new DashboardMonitoringBean();
		bean.setUuidMsUser(Long.valueOf(uuidMobileUser));
		List<TrLocationhistory> tracking = this.retrieveLocationHistory(
				uuidMobileUser, startDate, endDate, callerId);
		bean.setTracking(tracking);

		return bean;
	}

	@Transactional(readOnly = true)
	@Override
	public List<DashboardMonitoringBean> retrieveMobileArea(
			String[] uuidMobileUsers, AuditContext callerId) {
		if (ArrayUtils.isEmpty(uuidMobileUsers))
			return Collections.emptyList();

		List<DashboardMonitoringBean> result = new ArrayList<>();

		for (String uuidUser : uuidMobileUsers) {
			DashboardMonitoringBean bean = this.retrieveMobileArea(uuidUser);
			if (bean != null) {
				result.add(bean);
			}
		}

		return result;
	}

	private DashboardMonitoringBean retrieveMobileArea(String uuidUser) {
		if (StringUtils.isBlank(uuidUser))
			return null;

		String[][] paramsArea = { { "uuidUser", uuidUser } };
		List<Map<String, Object>> listArea = this.getManagerDAO()
				.selectAllNative("common.dashboard.getArea", paramsArea, null);
		BigDecimal[][] area = new BigDecimal[listArea.size()][2];

		DashboardMonitoringBean resultBean = new DashboardMonitoringBean();
		String areaType = null;
		for (int x = 0; x < listArea.size(); x++) {
			Map<String, Object> record = listArea.get(x);
			if (areaType == null) {
				areaType = (String) record.get("d4");
				resultBean.setAreaType(areaType);
			}

			if (GlobalVal.AREA_CIRCLE.equalsIgnoreCase(areaType)) {
				resultBean.setRadius((record.get("d5") == null) ? 0
						: ((Integer) record.get("d5")).intValue());
				area[0][0] = (BigDecimal) record.get("d0");
				area[0][1] = (BigDecimal) record.get("d1");
			} else {
				area[x][0] = (BigDecimal) record.get("d2");
				area[x][1] = (BigDecimal) record.get("d3");
			}
		}
		resultBean.setArea(area);
		resultBean.setUuidMsUser(Long.valueOf(uuidUser));

		return resultBean;
	}

	@Override
	@Transactional(readOnly = true)
	public List userList(String uuidSpv) {
		List userList = Collections.EMPTY_LIST;
		Object params[][] = {
				{ Restrictions.eq("amMsuser.uuidMsUser", uuidSpv) },
				{ Restrictions.eq("isActive", "1") } };
		Map result = this.getManagerDAO().selectAll(AmMsuser.class, params,
				null);
		userList = (List) result.get(GlobalKey.MAP_RESULT_LIST);
		return userList;
	}
}
