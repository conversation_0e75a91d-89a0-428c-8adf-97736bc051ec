package com.adins.mss.businesslogic.impl.mobiletracking;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.mobiletracking.LuGroupTaskLogic;

@SuppressWarnings("rawtypes")
public class GenericLuGroupTaskLogic extends BaseLogic implements LuGroupTaskLogic {

	@Transactional(readOnly=true)
	@Override
	public List listGroupTask(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("lookup.grouptask.listGroupTask", params, null);		
		return result;
	}
	
	@Transactional(readOnly=true)
	@Override
	public Integer countListGroupTask(Object params, AuditContext callerId) { 
		Integer result = (Integer)this.getManagerDAO().selectOneNative("lookup.grouptask.cntGroupTask", params);		
		return result;
	}
}
