package com.adins.mss.businesslogic.impl.mobiletracking;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.mobiletracking.LuLocationLogic;

@SuppressWarnings("rawtypes")
public class GenericLuLocationLogic extends BaseLogic implements LuLocationLogic {

	@Transactional(readOnly=true)
	@Override
	public List listLocation(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("lookup.location.listLocation", params, null);		
		return result;
	}
	
	@Transactional(readOnly=true)
	@Override
	public Integer countListLocation(Object params, AuditContext callerId) { 
		Integer result = (Integer)this.getManagerDAO().selectOneNative("lookup.location.cntLocation", params);		
		return result;
	}
}
