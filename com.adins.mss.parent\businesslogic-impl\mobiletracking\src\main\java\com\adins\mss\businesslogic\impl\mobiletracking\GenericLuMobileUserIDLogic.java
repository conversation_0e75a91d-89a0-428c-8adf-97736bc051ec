package com.adins.mss.businesslogic.impl.mobiletracking;

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.mobiletracking.LuMobileUserIDLogic;

@SuppressWarnings("rawtypes")
public class GenericLuMobileUserIDLogic extends BaseLogic implements LuMobileUserIDLogic {

	@Transactional(readOnly=true)
	@Override
	public List listMobileUserID(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("lookup.mobileuserid.listMobileUserID", params, null);		
		return result;
	}
	
	@Transactional(readOnly=true)
	@Override
	public Integer countListMobileUserID(Object params, AuditContext callerId) { 
		Integer result = (Integer)this.getManagerDAO().selectOneNative("lookup.mobileuserid.cntMobileUserID", params);		
		return result;
	}
}
