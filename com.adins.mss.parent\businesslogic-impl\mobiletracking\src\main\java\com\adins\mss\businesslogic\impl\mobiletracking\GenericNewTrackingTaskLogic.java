package com.adins.mss.businesslogic.impl.mobiletracking;

import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.mobiletracking.NewTrackingTaskLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.EntityNotUniqueException;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.MsLocation;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;

public class GenericNewTrackingTaskLogic extends BaseLogic implements
		NewTrackingTaskLogic {
	@Autowired
	private CommonLogic commonLogic;

	@Transactional(readOnly = true)
	@Override
	public Map<String, Object> getCombo(AmMsuser amMsuser, AuditContext callerId) {
		Map<String, Object> resultMap = new HashMap();

		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap
				.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);

		// Combo List Form
		Object[][] paramsForm = { { Restrictions.eq("isActive", "1") },
				{ "amMssubsystem.subsystemName", subsysName } };
		Map<String, Object> mapForm = this.getManagerDAO().list(MsForm.class,
				paramsForm, null);
		List<MsForm> listFormCombo = (List<MsForm>) mapForm
				.get(GlobalKey.MAP_RESULT_LIST);
		Map<String, String> formCombo = new HashMap<String, String>();
		if (!listFormCombo.isEmpty()) {
			for (int i = 0; i < listFormCombo.size(); i++) {
				formCombo.put(String.valueOf(listFormCombo.get(i).getUuidForm()),
						String.valueOf(listFormCombo.get(i).getFormName()));
			}
		}

		// Combo List Priority
		Map<String, String> priorityCombo = new HashMap<String, String>();
		Object[][] paramPriority = { { Restrictions.eq("isActive", "1") } };
		Map<String, Object> result = new HashMap<>();
		result = this.getManagerDAO().selectAll(MsPriority.class,
				paramPriority, null);
		List<MsPriority> listPriority = (List) result
				.get(GlobalKey.MAP_RESULT_LIST);
		if (listPriority != null) {
			for (MsPriority msPriority : listPriority) {
				priorityCombo.put(String.valueOf(msPriority.getUuidPriority()),
						(String) msPriority.getPriorityDesc());
			}
		}

		resultMap.put("formCombo", formCombo);
		resultMap.put("priorityCombo", priorityCombo);

		return resultMap;
	}

	@Transactional
	@Override
	public void insertNewTrackingTask(AuditContext auditContext,
			String uuidMsuserDriver, String uuidPriority, String uuidForm,
			String dateAssign, String uuidLocation, String notes,
			String uuidGroupTask, String existsTaskSeq) {

		// AMMSUSER (DRIVER)
		Object[][] paramUserDriver = { { Restrictions.eq("uuidMsUser",
				uuidMsuserDriver) } };
		AmMsuser amMsUserDriver = this.getManagerDAO().selectOne(
				AmMsuser.class, paramUserDriver);

		// AMMSUSER (SPV)
		Object[][] paramUserSPV = { { Restrictions.eq("uuidMsUser",
				auditContext.getCallerId()) } };
		AmMsuser amMsUserSPV = this.getManagerDAO().selectOne(AmMsuser.class,
				paramUserSPV);

		// MSBRANCH
		Object[][] paramBranch = { { Restrictions.eq("uuidBranch", amMsUserSPV
				.getMsBranch().getUuidBranch()) } };
		MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class,
				paramBranch);

		// MSPRIORITY
		Object[][] paramPriority = { { Restrictions.eq("uuidPriority",
				uuidPriority) } };
		MsPriority msPriority = this.getManagerDAO().selectOne(
				MsPriority.class, paramPriority);

		// MSFORM
		Object[][] paramForm = { { Restrictions.eq("uuidForm", uuidForm) } };
		MsForm msForm = this.getManagerDAO().selectOne(MsForm.class, paramForm);

		// MSLOCATION
		Object[][] paramLocation = { { Restrictions.eq("uuidLocation",
				uuidLocation) } };
		MsLocation msLocation = this.getManagerDAO().selectOne(
				MsLocation.class, paramLocation);

		// MSGROUPTASK
		Object[][] paramGroupTask = { { Restrictions.eq("uuidGroupTask",
				uuidGroupTask) } };
		MsGrouptask msGrouptask = this.getManagerDAO().selectOne(
				MsGrouptask.class, paramGroupTask);

		// FLAGSOURCE
		Map callerIdMap = auditContext.getParameters();
		String subsysName = (String) callerIdMap
				.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);

		String flagSource = null;
		if (GlobalVal.SUBSYSTEM_MT.equals(subsysName)) {
			flagSource = GlobalVal.PROCESS_CODE_MTCORE;
		}

		// TASKID
		String sequenceCode = GlobalVal.SEQUENCE_TASK_ID_MOBILE_TRACKING;
		String taskId = this.commonLogic.retrieveNewTaskId(sequenceCode);

		// DATEASSIGN
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date dateAssignD = null;
		try {
			dateAssignD = sdf.parse(dateAssign);
		} catch (ParseException e) {
			e.printStackTrace();
		}

		// GROUPTASKID, GROUPSEQ
		String[] prevGroupTask = new String[2];
		String existsGroupTaskId = null;
		String existsGroupSeq = null;
		String[] prevTaskSeq = null;

		// EXISTTASKSEQ*10
//		if (!StringUtils.isBlank(existsTaskSeq)){
//			existsTaskSeq = String
//					.valueOf((Integer.valueOf(existsTaskSeq) * 10));
//		}
		
		if (!StringUtils.isBlank(uuidGroupTask) ) {
			prevGroupTask = getPrevGrouptask(uuidGroupTask);
			existsGroupTaskId = prevGroupTask[0];
			existsGroupSeq = prevGroupTask[1];

			if (isTaskExist(dateAssign, uuidMsuserDriver, existsGroupSeq,
					existsTaskSeq)) {
				throw new EntityNotUniqueException(
						"Mobile User ID, Date Asignment, Group Sequence, and Task Sequence combination already registered.",
						dateAssign);
			}

			if (isClosingTask(uuidMsuserDriver, dateAssign)) {
				throw new EntityNotUniqueException(
						"Driver already closing task.", existsGroupSeq);
			}

			prevTaskSeq = getPrevTaskSeq(String.valueOf(msGrouptask.getGroupTaskId()));
			if (!StringUtils.isBlank(prevTaskSeq[0])) {
				if (Integer.valueOf(prevTaskSeq[0]) > Integer
						.valueOf(existsTaskSeq)) {
					throw new EntityNotUniqueException(
							"Driver is doing previous task. Please check task sequence input.",
							existsTaskSeq);
//					throw new EntityNotUniqueException(
//							"Driver is doing previous task " + prevTaskSeq[0]
//									+ ". Please check task sequence input.",
//							existsTaskSeq);
				}
			}
		} else {
			existsGroupSeq = this.getPrevGroupSeq(uuidMsuserDriver, dateAssign);
			if (!StringUtils.isBlank(existsGroupSeq))
				existsGroupSeq = String.valueOf((Integer
						.valueOf(existsGroupSeq) + 1));
			if (isClosingTask(uuidMsuserDriver, dateAssign)) {
				throw new EntityNotUniqueException(
						"Driver already closing task.", existsGroupSeq);
			}
		}

		TrTaskH trTaskH = new TrTaskH();
		trTaskH.setUsrCrt(amMsUserSPV.getLoginId());
		trTaskH.setDtmCrt(new Date());
		trTaskH.setAmMsuser(amMsUserDriver);
		trTaskH.setMsBranch(msBranch);
		trTaskH.setMsPriority(msPriority);
		trTaskH.setMsForm(msForm);
		trTaskH.setAssignDate(dateAssignD);
		trTaskH.setCustomerName(amMsUserDriver.getFullName());
		// trTaskH.setCustomerPhone(phoneNo);
		trTaskH.setCustomerAddress(msLocation.getLocationAddress());
		trTaskH.setNotes(notes);
		trTaskH.setFlagSource(flagSource);
		trTaskH.setLatitude(msLocation.getLatitude());
		trTaskH.setLongitude(msLocation.getLongitude());
		trTaskH.setTaskId(taskId);
		trTaskH.setMsLocation(msLocation);
		this.getManagerDAO().insert(trTaskH);

		String groupTaskId = (StringUtils.isBlank(existsGroupTaskId)) ? this.commonLogic
				.retrieveNewTaskId(GlobalVal.SEQUENCE_GROUP_TASK_ID)
				: existsGroupTaskId;
		String groupSeq = (StringUtils.isBlank(existsGroupSeq)) ? "1"
				: existsGroupSeq;
		String taskSeq = (StringUtils.isBlank(existsTaskSeq)) ? "1"
				: (existsTaskSeq);

		MsGrouptask msGroupTask = new MsGrouptask();
		msGroupTask.setTrTaskH(trTaskH);
		msGroupTask.setGroupTaskId(Long.parseLong(groupTaskId));
		msGroupTask.setUsrCrt(amMsUserSPV.getLoginId());
		msGroupTask.setDtmCrt(new Date());
		msGroupTask.setMsBranch(msBranch);
		msGroupTask.setCustomerName(amMsUserDriver.getFullName());
		msGroupTask.setTaskSeq(Integer.valueOf(taskSeq));
		msGroupTask.setGroupSeq(Integer.valueOf(groupSeq));
		this.getManagerDAO().insert(msGroupTask);

		AmMssubsystem amMsSubsystem = (AmMssubsystem) this.commonLogic
				.retrieveSubsystemByName(subsysName, auditContext);

		String[][] params = { { "processCode", flagSource } };
		BigInteger uuidProcess = (BigInteger) this.getManagerDAO().selectOneNativeString(
				"select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :processCode", params);

		String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess.longValue(), trTaskH.getUuidTaskH());

		if (trTaskH.getAmMsuser() != null) {
			statusCode = wfEngineLogic.commitCurrentTask(uuidProcess.longValue(), trTaskH.getUuidTaskH());
		}

		MsStatustask msStatustask = this.commonLogic.retrieveStatusTask(
				statusCode, amMsSubsystem.getUuidMsSubsystem(), auditContext);
		trTaskH.setMsStatustask(msStatustask);
		this.getManagerDAO().update(trTaskH);

		String uuidHistory = StringUtils.upperCase(this.getManagerDAO()
				.getUUID());
		String fieldPerson = (null == amMsUserDriver) ? null : amMsUserDriver
				.getFullName();
		TrTaskhistory trTaskHistory = new TrTaskhistory(
				trTaskH.getMsStatustask(), trTaskH, amMsUserSPV.getLoginId(),
				new Date(), "Assignment task by " + trTaskH.getFlagSource(),
				fieldPerson, amMsUserSPV.getLoginId(),
				GlobalVal.CODE_PROCESS_ASSIGNMENT);
		this.getManagerDAO().insert(trTaskHistory);
	}

	private boolean isClosingTask(String uuidMsuserDriver, String dateAssign) {
		Object[][] params = { { "uuidMsuserDriver", uuidMsuserDriver },
				{ "dateAssign", dateAssign } };
		Object[] todayAttendance = (Object[]) this.getManagerDAO()
				.selectOneNative("task.newtracking.isClosing", params);
		if (todayAttendance == null)
			return false;
		else
			return true;
	}

	public String[] getPrevGrouptask(String uuidGroupTask) {
		// get group_task_id and group_seq by uuid from MS_GROUP_TASK
		Object[][] params = { { "uuidGroupTask", uuidGroupTask } };
		Object[] groupTask = (Object[]) this.getManagerDAO().selectOneNative(
				"task.newtracking.getPrevGroupTask", params);

		String[] prevGroup = new String[2];
		if (groupTask != null) {
			prevGroup[0] = (String) groupTask[1];
			prevGroup[1] = (String) groupTask[2];
		}
		return prevGroup;
	}

	public String getPrevGroupSeq(String uuidMsuserDriver, String dateAssign) {
		Object[][] params = { { "uuidMsuserDriver", uuidMsuserDriver },
				{ "dateAssign", dateAssign } };
		Object[] groupSeq = (Object[]) this.getManagerDAO().selectOneNative(
				"task.newtracking.getPrevGroupSeq", params);

		String prevGroupSeq = new String();
		if (groupSeq != null) {
			prevGroupSeq = (String) groupSeq[1];
		}

		return prevGroupSeq;
	}

	public String[] getPrevTaskSeq(String groupTaskID) {
		Object[][] params = { { "groupTaskID", groupTaskID } };
		Object[] taskSeq = (Object[]) this.getManagerDAO().selectOneNative(
				"task.newtracking.getPrevTaskSeq", params);

		String[] prevTaskSeq = new String[2];
		if (taskSeq != null) {
			prevTaskSeq[0] = (String) taskSeq[3];
			prevTaskSeq[1] = (String) taskSeq[4];
		}

		return prevTaskSeq;
	}

	public boolean isTaskExist(String dateAssign, String uuidMsuserDriver,
			String existsGroupSeq, String existsTaskSeq) {
		Object[][] params = { { "dateAssign", dateAssign },
				{ "uuidMsuserDriver", uuidMsuserDriver },
				{ "taskSeq", existsTaskSeq }, { "groupSeq", existsGroupSeq } };
		Object[] taskExist = (Object[]) this.getManagerDAO().selectOneNative(
				"task.newtracking.isTaskExist", params);

		if (taskExist != null) {
			return true;
		} else {
			return false;
		}
	}
}
