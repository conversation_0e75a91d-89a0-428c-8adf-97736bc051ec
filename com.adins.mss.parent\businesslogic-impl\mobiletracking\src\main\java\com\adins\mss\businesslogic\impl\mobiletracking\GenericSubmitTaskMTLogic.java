package com.adins.mss.businesslogic.impl.mobiletracking;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.mobiletracking.SubmitTaskMTLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.exceptions.UserLoginIdNotExistsException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.MsLocation;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.MsZone;
import com.adins.mss.model.MsZoneoflocation;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskrejecteddetail;
import com.adins.mss.model.TrTaskrejectedhistory;
import com.adins.mss.model.custom.MessageEventBean;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.SubmitTaskHBean;
import com.adins.mss.services.model.common.SyncLocationBean;
import com.adins.mss.services.model.common.SyncZoneBean;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;

@SuppressWarnings({"unchecked","rawtypes"})
public class GenericSubmitTaskMTLogic extends BaseLogic implements SubmitTaskMTLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericSubmitTaskMTLogic.class);
	

	private MessageSource messageSource;
	private GeolocationLogic geocoder;
	
	@Autowired
	private CommonLogic commonLogic;
    private ImageStorageLogic imageStorageLogic;
	
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}    
    public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }
    
	@Override
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	public String submitTask(AuditContext auditContext, String application,
			SubmitTaskHBean taskHBean, SubmitTaskDBean[] taskDBean,
			SyncLocationBean locationBean, SyncZoneBean zoneBean, String imei,
			String androidId, String groupTaskId) {
		DateTime startDateTime = new DateTime();
		LOG.info("Submitting task.....");
		String paramH[][] = { {"uuidTaskH", taskHBean.getUuid_task_h()} };
		String taskId = StringUtils.EMPTY;
		String isFinal = StringUtils.EMPTY;
		String flagSource = StringUtils.EMPTY;
		AmMssubsystem subsystem = null;
		boolean firstSend = true;
		
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, taskHBean.getUuid_user());
		this.getManagerDAO().fetch(usr.getMsBranch());
		this.getManagerDAO().fetch(usr.getAmMssubsystem());	
		
		//cek mode submit
		AmGeneralsetting beanGeneral = this.getManagerDAO().selectOne(AmGeneralsetting.class, 
										new Object[][]{ {Restrictions.eq("gsCode", usr.getAmMssubsystem().getSubsystemName()+GlobalKey.GENERALSETTING_MODE_LOGIN_SUBMIT)}});
		if (beanGeneral.getGsValue() == null || "".equals(beanGeneral.getGsValue())){
			beanGeneral.setGsValue(GlobalVal.MODE_IMEI);
		}
		
		//jika MODE_SUBMIT == IMEI
		if(GlobalVal.MODE_IMEI.equalsIgnoreCase(beanGeneral.getGsValue())){
			if(imei!=null){
				if(!imei.equals(usr.getImei()) && !imei.equals(usr.getImei2())){
					throw new UserLoginIdNotExistsException(this.messageSource.getMessage(
							"businesslogic.global.imeinotvalid", new Object[]{imei}, this.retrieveLocaleAudit(auditContext)));
				}
			}
		}else if(GlobalVal.MODE_ANDROID_ID.equalsIgnoreCase(beanGeneral.getGsValue())){
			if(androidId!=null){
				if(usr.getAndroidId()==null || !usr.getAndroidId().equals(androidId)){
					throw new UserLoginIdNotExistsException(this.messageSource.getMessage(
		            		"businesslogic.global.androididnotvalid", null, this.retrieveLocaleAudit(auditContext)));
				}	
			}
		}	

		TrTaskH taskHCekUser = this.getManagerDAO().selectOne(TrTaskH.class, taskHBean.getUuid_task_h());
		TrTaskrejectedhistory taskRejected = null;
		boolean rejected = false;
		
		//Task Rejected
		if (null != taskHCekUser) {
			this.getManagerDAO().fetch(taskHCekUser.getMsStatustask());
			if (!String.valueOf(usr.getUuidMsUser()).equals(String.valueOf(taskHCekUser.getAmMsuser().getUuidMsUser())) || (GlobalVal.SURVEY_STATUS_TASK_DELETED.equals(taskHCekUser.getMsStatustask().getStatusCode()))) {
				rejected = true;
				Object[][] paramTaskRejectedHist = { {Restrictions.eq("amMsuser.uuidMsUser", usr.getUuidMsUser())},
				        {Restrictions.eq("trTaskH.uuidTaskH", taskHCekUser.getUuidTaskH())} };
				taskRejected = this.getManagerDAO().selectOne(TrTaskrejectedhistory.class, paramTaskRejectedHist);
				if (taskRejected == null) {
					
					taskRejected = new TrTaskrejectedhistory();
					taskRejected.setAmMsuser(usr);
					taskRejected.setDtmCrt(new Date());
					taskRejected.setTrTaskH(taskHCekUser);
					taskRejected.setReasonCode(GlobalVal.CODE_PROCESS_REJECTED);
					
					this.getManagerDAO().insert(taskRejected);
					insertTaskHistory(auditContext, taskHCekUser.getMsStatustask(), taskHCekUser, "Task from " + taskHCekUser.getFlagSource() + "has been rejected.", 
							GlobalVal.CODE_PROCESS_REJECTED, usr.getFullName(), usr, taskRejected);
				}
			}
		}
					
		subsystem = usr.getAmMssubsystem();
		
		//START Insert into Table TR_TASK_H with return TaskId
		String checkH = null;
		if (GlobalVal.SUBSYSTEM_MS.equals(subsystem.getSubsystemName())) {
			checkH = (taskHCekUser == null) ? null : String.valueOf(taskHCekUser.getUuidTaskH());
		}
		else {
            MsStatustask msStatusTask = (taskHCekUser == null) ? null : taskHCekUser.getMsStatustask();
            checkH = (msStatusTask == null) ? null : msStatusTask.getStatusCode();
		}
		
		String curTaskId = (taskHCekUser == null) ? null : String.valueOf(taskHCekUser.getTaskId());
		
		TrTaskH trTaskH;
		MsForm msForm  = this.getManagerDAO().selectOne(MsForm.class, taskHBean.getUuid_scheme());
		Map<Integer, MsQuestion> msQuestions = getAllMsQuestion(taskDBean);
		
		boolean flag = false; 
		if(checkH == null && !rejected) {
			trTaskH = new TrTaskH();
			trTaskH.setUuidTaskH(Long.valueOf(taskHBean.getUuid_task_h()));
			trTaskH.setMsForm(msForm);
			trTaskH.setUsrCrt(auditContext.getCallerId());
			trTaskH.setDtmCrt(new Date());
			flagSource = GlobalVal.PROCESS_CODE_MTADHOC;
			trTaskH.setFlagSource(flagSource);
			flag = true;
			
			//Priority
			Object paramPriority[][] = { {Restrictions.eq("priorityDesc", GlobalVal.TAKS_PRIORTY_NORMAL)} };
			MsPriority msPriority = this.getManagerDAO().selectOne(MsPriority.class, paramPriority);
			trTaskH.setMsPriority(msPriority);
			
			//Location
			Object paramLocation[][] = {{Restrictions.eq("uuidLocation", locationBean.getUuid_location())}};
			MsLocation loc = this.getManagerDAO().selectOne(MsLocation.class, paramLocation);
			if(loc == null){
				loc = new MsLocation();
				loc.setUuidLocation(Long.valueOf(locationBean.getUuid_location()));
				loc.setUsrCrt(auditContext.getCallerId());
				loc.setDtmCrt(new Date());
				loc.setLocationCode(locationBean.getLocation_code());
				loc.setLocationName(locationBean.getLocation_name());
				loc.setLocationAddress(locationBean.getLocation_address());
				loc.setLatitude(new BigDecimal(locationBean.getLatitude()));
				loc.setLongitude(new BigDecimal(locationBean.getLongitude()));
				loc.setIsActive("1");
				this.getManagerDAO().insert(loc);
				
				//Zone
				Object paramZone[][] = {{Restrictions.eq("uuidZone", zoneBean.getUuid_zone())}};
				MsZone zone = this.getManagerDAO().selectOne(MsZone.class, paramZone);
				if(zone == null){
					zone = new MsZone();
					zone.setUuidZone(Long.valueOf(zoneBean.getUuid_zone()));
					zone.setUsrCrt(auditContext.getCallerId());
					zone.setDtmCrt(new Date());
					zone.setZoneCode(zoneBean.getZone_code());
					zone.setZoneName(zoneBean.getZone_name());
					zone.setIsActive("1");
					this.getManagerDAO().insert(zone);
				}
				
				MsZoneoflocation zoneOfLoc = new MsZoneoflocation();
				zoneOfLoc.setUsrCrt(auditContext.getCallerId());
				zoneOfLoc.setDtmCrt(new Date());
				zoneOfLoc.setMsZone(zone);
				zoneOfLoc.setMsLocation(loc);
				this.getManagerDAO().insert(zoneOfLoc);
			}
			trTaskH.setMsLocation(loc);
		} else {
			trTaskH = taskHCekUser; //this.getManagerDAO().selectOne(TrTaskH.class, taskHBean.getUuid_task_h());
			if (!rejected) {
				if (taskHCekUser != null && String.valueOf(usr.getUuidMsUser()).equals(String.valueOf(taskHCekUser.getAmMsuser().getUuidMsUser()))) {
					trTaskH.setUsrUpd(auditContext.getCallerId());
					trTaskH.setDtmUpd(new Date());
					flag = true;
				}
				else {
					taskId = trTaskH.getTaskId();
				}
			} else {
				taskId = trTaskH.getTaskId();
			}
			MsStatustask tempStatus = trTaskH.getMsStatustask();
			if (null != tempStatus) {
				if (GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equals(tempStatus.getStatusCode()) ||
						GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(tempStatus.getStatusCode()) ||
						GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(tempStatus.getStatusCode()) ) {
					return trTaskH.getTaskId();
				}
			}
		}
		
		if (flag) {
			trTaskH.setCustomerName(locationBean.getLocation_name());
			trTaskH.setCustomerAddress(locationBean.getLocation_address());
			trTaskH.setNotes(taskHBean.getNotes());
			if (!rejected)
				trTaskH.setSubmitDate(new Date());
			trTaskH.setSendDate(taskHBean.getSubmitDate());
			trTaskH.setAmMsuser(usr);
			trTaskH.setMsBranch(usr.getMsBranch());
			trTaskH.setIsDraft("0");
			
			if (null != taskHBean.getStartDtm()) {
				Date startDtm = taskHBean.getStartDtm();
				trTaskH.setStartDtm(startDtm);
			}
			if (null != taskHBean.getReadDtm()) {
				Date readDtm = taskHBean.getReadDtm();
				trTaskH.setReadDate(readDtm);
			}
			
//				if (taskHBean.getVoice_note() != null)
//					trTaskH.setVoiceNote(BaseEncoding.base64().decode(taskHBean.getVoice_note()));
			
			if (null == trTaskH.getFlagSource()
					|| StringUtils.EMPTY.equals(trTaskH.getFlagSource())) {
					trTaskH.setFlagSource(GlobalVal.SUBSYSTEM_MT);				
			}
			
			trTaskH.setIsAppNotified("0");
			if (curTaskId != null){
				trTaskH.setTaskId(curTaskId);
				taskId = curTaskId;
			} else {
				taskId = commonLogic.retrieveNewTaskId(GlobalVal.SEQUENCE_TASK_ID_MOBILE_TRACKING);
				trTaskH.setTaskId(taskId);
			}
			
			if (checkH == null){
				this.getManagerDAO().insert(trTaskH);
				if(StringUtils.isBlank(groupTaskId)|| "null".equalsIgnoreCase(groupTaskId)){
					//insert ms_grouptask
					MsGrouptask msGrouptask = new MsGrouptask();
					msGrouptask.setGroupTaskId(trTaskH.getUuidTaskH());
					msGrouptask.setTrTaskH(trTaskH);
					msGrouptask.setMsBranch(trTaskH.getMsBranch());
					msGrouptask.setUsrCrt(auditContext.getCallerId());
					msGrouptask.setDtmCrt(new Date());
					msGrouptask.setCustomerName(trTaskH.getCustomerName());
					
					Date currentDate = new Date();
					String start = DateFormatUtils.format(currentDate, "yyyy-MM-dd")+" 00:00:00";
					String end = DateFormatUtils.format(currentDate, "yyyy-MM-dd")+" 23:59:59";
					
					
					String paramGetGroupTask[][] = {{"uuidUser", auditContext.getCallerId()}, {"start", start}, {"end", end}};
					Integer prevGroupSeq = (Integer)this.getManagerDAO().selectOneNative("task.newTaskMT.getGroupSeq", paramGetGroupTask);
					msGrouptask.setGroupSeq(prevGroupSeq+1);
					msGrouptask.setTaskSeq(1);
					this.getManagerDAO().insert(msGrouptask);						
				} else {
					//insert ms_grouptask
					MsGrouptask msGrouptask = new MsGrouptask();
					msGrouptask.setGroupTaskId(Long.valueOf(groupTaskId));
					msGrouptask.setTrTaskH(trTaskH);
					msGrouptask.setMsBranch(trTaskH.getMsBranch());
					msGrouptask.setUsrCrt(auditContext.getCallerId());
					msGrouptask.setDtmCrt(new Date());
					msGrouptask.setCustomerName(trTaskH.getCustomerName());
					
					String paramGetGroupTask[][] = {{"groupTaskId", groupTaskId}};
					String prevGroupSeq = (String)this.getManagerDAO().selectOneNative("task.newTaskMT.getGroupSeqFromGroupTaskid", paramGetGroupTask);
					msGrouptask.setGroupSeq(Integer.valueOf(prevGroupSeq));
					String prevTaskSeqSubm = (String)this.getManagerDAO().selectOneNative("task.newTaskMT.getTaskSeqFromGroupTaskId", paramGetGroupTask);
					msGrouptask.setTaskSeq(Integer.valueOf(prevTaskSeqSubm));
					this.getManagerDAO().insert(msGrouptask);
				}
				firstSend = true;
			}
			else if (checkH != null && "N".equals(checkH)){
				this.getManagerDAO().update(trTaskH);
				firstSend = true;
			}
			else {
				this.getManagerDAO().update(trTaskH);
				firstSend = false;
			}
			//End insert into Table TR_TASK_H
		}
		
		//2016-09-16 SUM - select all taskD first instead of select one for all answers
		Map<String, TrTaskD> listTaskD = this.listTaskDAsMap(String.valueOf(trTaskH.getUuidTaskH()));
					
		//LOOPING FOR TR_TASK_D
		ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(auditContext);
        Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
                ? this.imageStorageLogic.retrieveGsImageFileSystemPath(auditContext) : null;
		
		if (taskDBean.length > 1) {
			//delete multiple detail
			String[] answerTypeArr = {GlobalVal.ANSWER_TYPE_MULTIPLE, GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION, GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION};
			Object[][] paramsMulti = {
					{"uuidTaskH", trTaskH.getUuidTaskH()},
					{"answerType", Arrays.asList(answerTypeArr)}
			};
			this.getManagerDAO().deleteNativeString("delete d from tr_task_d d "
					+"left join ms_question q on d.uuid_question = q.uuid_question "
					+"left join ms_answertype at on q.uuid_answer_type = at.uuid_answer_type "
					+"where d.uuid_task_h = :uuidTaskH and "
					+"at.code_answer_type in (:answerType)", paramsMulti);
		}
		for (int i=0; i < taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
			
			String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String uuidTaskD = taskDBean[i].getUuid_task_d();
			String optionAnswerId = taskDBean[i].getOption_answer_id();
			String textAnswer = taskDBean[i].getText_answer();
			String questionText = taskDBean[i].getQuestion_label();
			String latitude = taskDBean[i].getLatitude();
			String longitude = taskDBean[i].getLongitude();
			String mcc = taskDBean[i].getMcc();
			String mnc = taskDBean[i].getMnc();
			String lac = taskDBean[i].getLac();
			String cellId = taskDBean[i].getCid();
			String gpsTime = taskDBean[i].getGps_time();
			String accuracy = taskDBean[i].getAccuracy();
			String image = taskDBean[i].getImage();
			isFinal = taskDBean[i].getIs_final();
						
			if (flag) {
				if(GlobalVal.ANSWER_TYPE_IMAGE.equals(answerType) || 
						GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA.equals(answerType) ||
						GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS.equals(answerType) ||
						GlobalVal.ANSWER_TYPE_DRAWING.equals(answerType)){										
						//insert into Table TR_TASKDETAILLOB
						insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
								latitude, longitude, mcc, mnc, lac, cellId, accuracy, isl, imagePath);						
				} else {
					//insert into Table TR_TASK_D
					insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
							latitude, longitude, mcc, mnc, lac, cellId, accuracy, listTaskD);
				}
			}
			else {					
				this.insertTaskRejectedDetail(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, 
						textAnswer, latitude, longitude, mcc, mnc, lac, cellId, accuracy, image, taskRejected);
			}	
		}
		
		//finish insert task
		
		//error timeout kalau tidak di commit dulu
		
		//commit status task and insert history
		
		this.getManagerDAO().fetch(trTaskH.getMsStatustask());
		if (flag) {
			MsStatustask msStatusTask;
			
			long uuidProcess = getUuidProcess(trTaskH, subsystem);
			String codeProcess = StringUtils.EMPTY;
			
			//Check is final & update status task

			if (firstSend && isFinal != null && "0".equals(isFinal)) {
				msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
				
			} else if((firstSend && isFinal != null && "1".equals(isFinal))) {					
				//get status from workflow
				msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
				
				//get status from workflow
				msStatusTask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
				
				String notes = StringUtils.EMPTY;
				if(GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(msStatusTask.getStatusCode())) {
					codeProcess = GlobalVal.CODE_PROCESS_VERIFIED;
					notes = "Task from " + trTaskH.getFlagSource() + " has been verified.";
				} else if (GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(msStatusTask.getStatusCode())) {
					codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
					notes = "Task from " + trTaskH.getFlagSource() + " has been approved.";
				} else {
					codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
					notes = "Task from " + trTaskH.getFlagSource() + " has been submitted.";
				}
				
				//INSERT INTO CHECK HISTORY
				insertTaskHistory(auditContext, msStatusTask, trTaskH, notes, 
						codeProcess, usr.getFullName(), usr, null);
				
			} else if (!firstSend && isFinal != null && "1".equals(isFinal)) {
				
				//get status from workflow
				MsStatustask msStatusTask2 = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
			
//					TrTaskH bean = this.getManagerDAO().selectOne(TrTaskH.class, trTaskH.getUuidTaskH());
				if (GlobalVal.SURVEY_STATUS_TASK_UPLOADING.equals(msStatusTask2.getStatusCode())) {
					msStatusTask2 = commitWfAndUpdateTaskH(trTaskH, uuidProcess, subsystem, 0);
				}
				
				String notes = StringUtils.EMPTY;
				if(GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(msStatusTask2.getStatusCode())) {
					codeProcess = GlobalVal.CODE_PROCESS_VERIFIED;
					notes = "Task from " + trTaskH.getFlagSource() + " has been verified.";
				} else if (GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(msStatusTask2.getStatusCode())) {
					codeProcess = GlobalVal.CODE_PROCESS_APPROVED;
					notes = "Task from " + trTaskH.getFlagSource() + " has been approved.";
				} else {
					codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
					notes = "Task from " + trTaskH.getFlagSource() + " has been submitted.";
				}
				
				//INSERT INTO CHECK HISTORY SURVEY
				insertTaskHistory(auditContext, msStatusTask2, trTaskH, notes, 
						codeProcess, usr.getFullName(), usr, null);
			}
		}
					
		LOG.info("End of submit task : " + new DateTime().minusMillis(startDateTime.getMillisOfDay()).getMillisOfDay());
			
		return taskId;
	}
	private Map<Integer, MsQuestion>  getAllMsQuestion(SubmitTaskDBean  taskDBean[]){
		Map<Integer, MsQuestion> result = new HashMap<Integer, MsQuestion>();
		for(int i=0; i<taskDBean.length; i++){
			MsQuestion msQuestion = this.getManagerDAO().selectOne(MsQuestion.class, taskDBean[i].getQuestion_id());
			result.put(i, msQuestion);
		}
		return result;
	}
	
	private void insertTaskD(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String optionAnswerId,
			String questionText, String textAnswer, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Map<String, TrTaskD> listTaskD){
		DateTime startDateTime = new DateTime();
		LOG.info("Inserting task detail... ");
		MsLov msLovByLovId =  null;
		TrTaskD taskD = null;
		if (!GlobalVal.ANSWER_TYPE_MULTIPLE.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
            //COMMENTED - 2016-09-15 - fetch from Map<String, TrTaskD> listTaskD instead of query to DB
//		    Object[][] params = {
//					{Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
//					{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())}
//				};
//			taskD = this.getManagerDAO().selectOne(TrTaskD.class, params);
			taskD = listTaskD.get(msQuestion.getUuidQuestion());
		}
		if(StringUtils.isNotBlank(optionAnswerId)){
			msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, optionAnswerId);
		}
		if(GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())
				|| GlobalVal.ANSWER_TYPE_DATETIME.equals(msQuestion.getMsAnswertype().getCodeAnswerType())){
			if(textAnswer != null){
				DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
				try {
					Date result = df.parse(textAnswer);
					if(GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
						textAnswer = (null != result) 
								? new SimpleDateFormat("dd/MM/yyyy").format(result)
								: null;
					} else {
						textAnswer = (null != result) 
								? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result)
								: null;
					}
				} catch (ParseException e) {
					e.printStackTrace();
				} 
			}
		}
		if (null != taskD) {
			
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setDtmUpd(new Date());
			if (msLovByLovId != null) {
				taskD.setMsLovByLovId(msLovByLovId);
				taskD.setOptionText(msLovByLovId.getDescription());
			}
			taskD.setQuestionText(questionText);
			taskD.setTextAnswer(textAnswer);
			taskD.setLatitude(checkEmptyBigdecimal(latitude) );
			taskD.setLongitude(checkEmptyBigdecimal(longitude) );
			if(taskD.getLatitude()!=null && taskD.getLongitude()!=null){
				taskD.setIsGps("1");
			}
			taskD.setMcc(checkEmptyInteger(mcc) );
			taskD.setMnc(checkEmptyInteger(mnc) );
			taskD.setLac(checkEmptyInteger(lac) );
			taskD.setCellId(checkEmptyInteger(cellId));
			taskD.setAccuracy(checkEmptyInteger(accuracy) );
			if (taskD.getMcc() != null && taskD.getMnc() != null
				&& taskD.getLac() != null && taskD.getCellId() != null) {
				this.getLocationByCellId(taskD, auditContext);
			}
			this.getManagerDAO().update(taskD);
		} else {				
			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUuidTaskD(Long.valueOf(uuidTaskD));
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			trTaskD.setTrTaskH(trTaskH);
			trTaskD.setMsQuestion(msQuestion);
			if (msLovByLovId != null) {
				trTaskD.setMsLovByLovId(msLovByLovId);
				trTaskD.setOptionText(msLovByLovId.getDescription());
			}
			trTaskD.setQuestionText(questionText);
			trTaskD.setTextAnswer(textAnswer);
			trTaskD.setLatitude(checkEmptyBigdecimal(latitude) );
			trTaskD.setLongitude(checkEmptyBigdecimal(longitude) );
			if (trTaskD.getLatitude()!=null && trTaskD.getLongitude()!=null) {
				trTaskD.setIsGps("1");
			}
			trTaskD.setMcc(checkEmptyInteger(mcc) );
			trTaskD.setMnc(checkEmptyInteger(mnc) );
			trTaskD.setLac(checkEmptyInteger(lac) );
			trTaskD.setCellId(checkEmptyInteger(cellId));
			trTaskD.setAccuracy(checkEmptyInteger(accuracy) );
			this.getManagerDAO().insert(trTaskD);
			
		}
		LOG.info("End of insert task detail : " + new DateTime().minusMillis(startDateTime.getMillisOfDay()).getMillisOfDay());
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private void insertDetailLob(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD,
			String questionText, String base64Image, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, ImageStorageLocation saveImgLoc, Path imgLoc){
		
			Object params[][] = { 
					{Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
					{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())} 
			};
			TrTaskdetaillob trTaskdetaillob = this.getManagerDAO().selectOne(TrTaskdetaillob.class, params);

			if (trTaskdetaillob == null) {
				trTaskdetaillob = new TrTaskdetaillob();
				trTaskdetaillob.setUuidTaskDetailLob(Long.parseLong(uuidTaskD));
				trTaskdetaillob.setUsrCrt(auditContext.getCallerId());
				trTaskdetaillob.setDtmCrt(new Date());
			} else {
				trTaskdetaillob.setUsrUpd(auditContext.getCallerId());
				trTaskdetaillob.setDtmUpd(new Date());
			}
			
			trTaskdetaillob.setTrTaskH(trTaskH);
			trTaskdetaillob.setMsQuestion(msQuestion);
			
			if (ImageStorageLocation.DATABASE == saveImgLoc) {
				if (base64Image != null && !base64Image.isEmpty()) {
					trTaskdetaillob.setLobFile(BaseEncoding.base64().decode(base64Image));
				}
			}
            else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
                if (base64Image != null && !base64Image.isEmpty()) {
                    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    Date date = new Date();
                    Path outputPath = Paths.get(imgLoc.toString(), dateFormat.format(date));

                    String absoluteFilename = trTaskH.getTaskId() + "_" + msQuestion.getRefId() + "_"
                            + uuidTaskD + ".jpg";
                    
                    String outputFile = this.imageStorageLogic.storeImageFileSystem(
                            BaseEncoding.base64().decode(base64Image), outputPath, absoluteFilename);
                    trTaskdetaillob.setImagePath(outputFile);
                }
            }
			trTaskdetaillob.setQuestionText(questionText);
			trTaskdetaillob.setLatitude(checkEmptyBigdecimal(latitude) );
			trTaskdetaillob.setLongitude(checkEmptyBigdecimal(longitude) );
			
			if (trTaskdetaillob.getLatitude()!=null && trTaskdetaillob.getLongitude()!=null)
				trTaskdetaillob.setIsGps("1");
			
			trTaskdetaillob.setMcc(checkEmptyInteger(mcc) );
			trTaskdetaillob.setMnc(checkEmptyInteger(mnc) );
			trTaskdetaillob.setLac(checkEmptyInteger(lac) );
			trTaskdetaillob.setCellId(checkEmptyInteger(cellId));
			trTaskdetaillob.setAccuracy(checkEmptyInteger(accuracy) );
			
			this.getManagerDAO().update(trTaskdetaillob); //DAO will do saveOrUpdate
	}
	

	private Integer checkEmptyInteger(String in){
		Integer i = 0;
		if (StringUtils.isBlank(in)) return null;
		else i = Integer.valueOf(in);
		return i;
	}
	
	private BigDecimal checkEmptyBigdecimal(String in){
		BigDecimal bd;
		if (StringUtils.isBlank(in)) return null;
		else bd = new BigDecimal(in);
		return bd;
	}
	
	private void getLocationByCellId(TrTaskD taskD, AuditContext callerId){
		List<LocationBean> listLocations = new ArrayList<LocationBean>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(taskD.getCellId());
		locationBean.setLac(taskD.getLac().intValue());
		locationBean.setMcc(taskD.getMcc().intValue());
		locationBean.setMnc(taskD.getMnc().intValue());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, callerId);

		if (locationBean.getCoordinate() != null) {
			taskD.setLatitude(new BigDecimal(locationBean.getCoordinate()
					.getLatitude()));
			taskD.setLongitude(new BigDecimal(locationBean.getCoordinate()
					.getLongitude()));
			taskD.setAccuracy(new Integer(locationBean.getAccuracy()));
			
			taskD.setTextAnswer("Coord : "+taskD.getLatitude().setScale(6, RoundingMode.CEILING)+", "+taskD.getLongitude().setScale(6, RoundingMode.CEILING)+" Accuracy : "+taskD.getAccuracy()+" m");
			taskD.setIsConverted("1");
		}
	}
	private List<TrTaskD> listTaskD(String uuidTaskH) {
		if (StringUtils.isBlank(uuidTaskH)) {
			return Collections.emptyList();
		}
		
		Object[][] params = { {Restrictions.eq("trTaskH.uuidTaskH", uuidTaskH)} };
		Map<String, Object> result = this.getManagerDAO().list(TrTaskD.class, params, null);
		
		return (List<TrTaskD>) result.get(GlobalKey.MAP_RESULT_LIST);
	}
	private Map<String, TrTaskD> listTaskDAsMap(String uuidTaskH) {
		List<TrTaskD> result = this.listTaskD(uuidTaskH);
		
		if (result == null || result.isEmpty())
			return Collections.emptyMap();
		
		Map<String, TrTaskD> resultMap = new HashMap<>(result.size());
		for (Iterator iterator = result.iterator(); iterator.hasNext();) {
			TrTaskD trTaskD = (TrTaskD) iterator.next();
			resultMap.put(String.valueOf(trTaskD.getMsQuestion().getUuidQuestion()), trTaskD);
		}
		
		return resultMap;
	}
	
	private void insertTaskRejectedDetail(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String optionAnswerId,
			String questionText, String textAnswer, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, String image, TrTaskrejectedhistory taskRejH) {
		
		TrTaskrejecteddetail taskRejectDetail = new TrTaskrejecteddetail();

		taskRejectDetail.setTrTaskH(trTaskH);
		
		if(image != null) taskRejectDetail.setImageBlob(BaseEncoding.base64().decode(image));
		
		taskRejectDetail.setDtmCrt(new Date());
		taskRejectDetail.setUsrCrt(auditContext.getCallerId());
		taskRejectDetail.setQuestionText(questionText);
		taskRejectDetail.setTextAnswer(textAnswer);
		taskRejectDetail.setLatitude(checkEmptyBigdecimal(latitude) );
		taskRejectDetail.setLongitude(checkEmptyBigdecimal(longitude) );
		
		if(taskRejectDetail.getLatitude()!=null && taskRejectDetail.getLongitude()!=null)
			taskRejectDetail.setIsGps("1");
		
		taskRejectDetail.setMcc(checkEmptyInteger(mcc) );
		taskRejectDetail.setMnc(checkEmptyInteger(mnc) );
		taskRejectDetail.setLac(checkEmptyInteger(lac) );
		taskRejectDetail.setCellId(checkEmptyInteger(cellId));
		taskRejectDetail.setAccuracy(checkEmptyInteger(accuracy) );
		taskRejectDetail.setTrTaskrejectedhistory(taskRejH);
		taskRejectDetail.setTimestampRejected(new Date());
		taskRejectDetail.setQuestionId(msQuestion.getUuidQuestion());
		
		MsLov msLovByLovId = null;
		if(StringUtils.isNotBlank(optionAnswerId)){
			msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, optionAnswerId);
		}
		if (msLovByLovId != null) {
			taskRejectDetail.setLovId(msLovByLovId.getUuidLov());
			taskRejectDetail.setOptionText(msLovByLovId.getDescription());
		}
		this.getManagerDAO().insert(taskRejectDetail);
	}
	
	@Transactional(readOnly=true)
	@Override
	@Async
	public void sendMessageToJms(AuditContext auditContext, String application,
			 SubmitTaskDBean taskDBean[], String taskId) {
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(auditContext.getCallerId()));
		Object params[][] = {{Restrictions.eq("taskId", taskId)}};
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, params);
		boolean sendLob = false;
		boolean sendDetail = false;
		for (int i=0; i < taskDBean.length; i++) {
			if (StringUtils.isNotBlank(taskDBean[i].getImage())) {										
				sendLob = true;
			} else {
				sendDetail = true;
			}
		}
		
		//SSE
		MessageEventBean msgBean = new MessageEventBean();
		msgBean.setEvent(GlobalVal.EVENT_NEWS);
		msgBean.setUuidUser(usr.getImei()+usr.getUuidMsUser());
		
		Object[] msgArgs = {usr.getLoginId(), usr.getFullName(), (trTaskH.getAgreementNo()!=null?trTaskH.getAgreementNo():trTaskH.getTaskId()), trTaskH.getCustomerName()};
		if(sendLob && !sendDetail){
			msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.uploadimage", msgArgs, this.retrieveLocaleAudit(auditContext)));
		} else {
			msgBean.setNotes(this.messageSource.getMessage("businesslogic.jms.submittask", msgArgs, this.retrieveLocaleAudit(auditContext)));
		}
		msgBean.setDate(DateFormatUtils.format(new Date(), "HH:mm:ss"));
		msgBean.setUuidMessageBean(this.getManagerDAO().getUUID());
		Gson gson = new Gson();

		try{
			sendMessage(gson.toJson(msgBean));
			LOG.info("Send message to JMS Success");
		}catch(Exception e){
			LOG.error("Send message to JMS Failed, e");
		}
		
	}
}
