<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">
	<bean id="GenericSubmitTaskMTLogicBean" class="com.adins.mss.businesslogic.impl.mobiletracking.GenericSubmitTaskMTLogic" scope="prototype" parent="BaseLogicBean">
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="geocoder" ref="GenericGeolocationLogicBean"/>
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
	</bean>
	<bean id="GenericDashboardMonitoringMTLogicBean" class="com.adins.mss.businesslogic.impl.mobiletracking.GenericDashboardMonitoringMTLogic" scope="prototype" parent="BaseLogicBean"/>
		<bean id="GenericLuGroupTaskLogicBean" class="com.adins.mss.businesslogic.impl.mobiletracking.GenericLuGroupTaskLogic" scope="prototype" parent="BaseLogicBean"/>
	<bean id="GenericLuLocationLogicBean" class="com.adins.mss.businesslogic.impl.mobiletracking.GenericLuLocationLogic" scope="prototype" parent="BaseLogicBean"/>
	<bean id="GenericLuMobileUserIDLogicBean" class="com.adins.mss.businesslogic.impl.mobiletracking.GenericLuMobileUserIDLogic" scope="prototype" parent="BaseLogicBean"/>
	<bean id="GenericNewTrackingTaskLogicBean" class="com.adins.mss.businesslogic.impl.mobiletracking.GenericNewTrackingTaskLogic" scope="prototype" parent="BaseLogicBean"/>
</beans>