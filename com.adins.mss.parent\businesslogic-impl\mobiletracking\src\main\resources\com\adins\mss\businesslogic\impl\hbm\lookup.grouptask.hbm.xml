<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="lookup.grouptask.listGroupTask">
		<query-param name="groupSeq" type="string" />
	    <query-param name="taskSeq" type="string" />
	    <query-param name="spvID" type="string" />
	    <query-param name="flagSource" type="string"/>
	    <query-param name="today" type="string"/>
	    <query-param name="mobileUID" type="string"/>
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
			SELECT * FROM (
				SELECT A.*, ROW_NUMBER() OVER (ORDER BY ROWNUM) AS RECNUM FROM (
					SELECT GT.UUID_GROUP_TASK, GT.GROUP_SEQ, GT.TASK_SEQ, TH.ASSIGN_DATE, TH.START_DTM, TH.SUBMIT_DATE, GT.GROUP_TASK_ID,
						ROW_NUMBER() OVER (ORDER BY GT.GROUP_SEQ, GT.TASK_SEQ) AS ROWNUM
					FROM MS_GROUPTASK GT WITH (NOLOCK)
					JOIN TR_TASK_H TH WITH (NOLOCK) ON (GT.UUID_TASK_H = TH.UUID_TASK_H)
					JOIN AM_MSUSER AM WITH (NOLOCK) ON (TH.UUID_MS_USER = AM.UUID_MS_USER)
					WHERE AM.SPV_ID = :spvID AND
						GT.GROUP_SEQ LIKE '%'+:groupSeq+'%' AND
						GT.TASK_SEQ LIKE '%'+:taskSeq+'%' AND
						TH.FLAG_SOURCE = :flagSource AND
						TH.SUBMIT_DATE is NULL AND
						CAST(TH.ASSIGN_DATE AS DATE) >= CAST(:today AS DATE) AND
						TH.UUID_MS_USER = :mobileUID
				) A <![CDATA[ WHERE A.ROWNUM <= :end
			) B WHERE B.RECNUM >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.grouptask.cntGroupTask">
		<query-param name="groupSeq" type="string" />
	    <query-param name="taskSeq" type="string" />
	    <query-param name="spvID" type="string" />
	    <query-param name="flagSource" type="string"/>
	    <query-param name="today" type="string"/>
	    <query-param name="mobileUID" type="string"/>
			SELECT COUNT (*)
			FROM MS_GROUPTASK GT WITH (NOLOCK) 
			JOIN TR_TASK_H TH WITH (NOLOCK) ON (GT.UUID_TASK_H = TH.UUID_TASK_H)
			JOIN AM_MSUSER AM WITH (NOLOCK) ON (TH.UUID_MS_USER = AM.UUID_MS_USER)
			WHERE AM.SPV_ID = :spvID AND
				GT.GROUP_SEQ LIKE '%'+:groupSeq+'%' AND
				GT.TASK_SEQ LIKE '%'+:taskSeq+'%' AND
				TH.FLAG_SOURCE = :flagSource AND
						TH.SUBMIT_DATE is NULL AND
						CAST(TH.ASSIGN_DATE AS DATE) >= CAST(:today AS DATE) AND
						TH.UUID_MS_USER = :mobileUID
	</sql-query>

</hibernate-mapping>