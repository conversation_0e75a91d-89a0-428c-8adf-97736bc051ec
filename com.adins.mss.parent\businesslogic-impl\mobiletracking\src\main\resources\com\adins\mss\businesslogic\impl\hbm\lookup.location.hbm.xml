<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="lookup.location.listLocation">
		<query-param name="locationCode" type="string" />
	    <query-param name="locationName" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
			SELECT * FROM (
				SELECT A.*, ROW_NUMBER() OVER (ORDER BY ROWNUM) AS RECNUM FROM (
					SELECT L.UUID_LOCATION, L.LOCATION_CODE AS 'LOCATION CODE', L.LOCATION_NAME AS 'LOCATION NAME', L.LATITUDE, L.LONGITUDE, 
						ROW_NUMBER() OVER (ORDER BY L.UUID_LOCATION) AS ROWNUM
					FROM MS_ZONEOFLOCATION ZL WITH (NOLOCK)
					JOIN MS_LOCATION L WITH (NOLOCK) ON (L.UUID_LOCATION = ZL.UUID_LOCATION)
					JOIN MS_ZONE Z WITH (NOLOCK) ON (Z.UUID_ZONE = ZL.UUID_ZONE)
					WHERE lower(L.LOCATION_CODE) like lower('%'+:locationCode+'%')
						AND lower(L.LOCATION_NAME) like lower('%'+:locationName+'%')
				) A <![CDATA[ WHERE A.ROWNUM <= :end
			) B WHERE B.RECNUM >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.location.cntLocation">
		<query-param name="locationCode" type="string" />
	    <query-param name="locationName" type="string" />
			SELECT COUNT (*)
			FROM MS_ZONEOFLOCATION ZL WITH (NOLOCK)
				JOIN MS_LOCATION L WITH (NOLOCK) ON (L.UUID_LOCATION = ZL.UUID_LOCATION)
				JOIN MS_ZONE Z WITH (NOLOCK) ON (Z.UUID_ZONE = ZL.UUID_ZONE)
			WHERE lower(L.LOCATION_CODE) like lower('%'+:locationCode+'%')
				AND lower(L.LOCATION_NAME) like lower('%'+:locationName+'%')
	</sql-query>

</hibernate-mapping>