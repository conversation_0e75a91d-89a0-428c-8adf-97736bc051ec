<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="lookup.mobileuserid.listMobileUserID">
		<query-param name="loginID" type="string" />
	    <query-param name="fullName" type="string" />
	    <query-param name="spvID" type="string" />
	    <query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
			SELECT * FROM (
				SELECT A.*, ROW_NUMBER() OVER (ORDER BY ROWNUM) AS RECNUM FROM (
					SELECT AM.UUID_MS_USER, AM.LOGIN_ID AS 'Mobile User ID', AM.FULL_NAME AS 'Full Name', BR.BRANCH_CODE AS 'Branch', 
						ROW_NUMBER() OVER (ORDER BY AM.UUID_JOB) AS ROWNUM
					FROM MS_BRANCH BR WITH (NOLOCK)
						LEFT OUTER JOIN AM_MSUSER AM WITH (NOLOCK) ON BR.UUID_BRANCH = AM.UUID_BRANCH
					WHERE AM.UUID_JOB = (SELECT J.UUID_JOB FROM MS_JOB J WITH (NOLOCK) WHERE J.JOB_CODE = 'DRIVER') 						
						AND lower(AM.LOGIN_ID) like lower('%'+:loginID+'%')
						AND lower(AM.FULL_NAME) like lower('%'+:fullName+'%')
						AND AM.SPV_ID = :spvID
						AND AM.IS_ACTIVE='1'
				) A <![CDATA[ WHERE A.ROWNUM <= :end
			) B WHERE B.RECNUM >= :start ]]>
	</sql-query>
	
	<sql-query name="lookup.mobileuserid.cntMobileUserID">
		<query-param name="loginID" type="string" />
	    <query-param name="fullName" type="string" />
	    <query-param name="spvID" type="string" />
			SELECT COUNT (*)
			FROM MS_BRANCH BR WITH (NOLOCK)
				LEFT OUTER JOIN AM_MSUSER AM WITH (NOLOCK) ON BR.UUID_BRANCH = AM.UUID_BRANCH
			WHERE AM.UUID_JOB = (SELECT J.UUID_JOB FROM MS_JOB J WITH (NOLOCK) WHERE J.JOB_CODE = 'DRIVER') 
				AND lower(AM.LOGIN_ID) like lower ('%'+:loginID+'%')
				AND lower(AM.FULL_NAME) like lower ('%'+:fullName+'%')
				AND AM.SPV_ID = :spvID
	</sql-query>

</hibernate-mapping>