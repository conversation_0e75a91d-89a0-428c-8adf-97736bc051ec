<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="mobiletracking.dm.getUser">
	    <query-param name="uuidSPV" type="string" />
	    WITH N AS (
		SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.INITIAL_NAME, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSPV AND msu.IS_ACTIVE = '1'
		
		UNION ALL
		
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.INITIAL_NAME, N.H<PERSON>, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, INITIAL_NAME from N order by FULL_NAME
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getUserTR">
    <query-param name="uuidSPV" type="string" />
	    WITH N AS (
		SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.INITIAL_NAME, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.UUID_MS_USER = :uuidSPV AND msu.IS_ACTIVE = '1'
		
		UNION ALL
		
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.INITIAL_NAME, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, INITIAL_NAME from N order by FULL_NAME
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getUser2">
    <query-param name="uuidSPV" type="string" />
   	<query-param name="uuidColl" type="string" />
	    WITH N AS (
		SELECT msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_BRANCH, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSPV AND msu.IS_ACTIVE = '1'
		
		UNION ALL
		
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.LOGIN_ID, msu2.UUID_BRANCH, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID, msb.BRANCH_NAME 
		from N JOIN MS_BRANCH msb with (nolock) ON N.UUID_BRANCH = msb.UUID_BRANCH where UUID_MS_USER != :uuidColl order by FULL_NAME
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getArea">
	    <query-param name="uuidCollector" type="string" />
		SELECT MA.LATITUDE, MA.LONGITUDE, MAP.LATITUDE lat, MAP.LONGITUDE lng, MA.AREA_TYPE_CODE, MA.RADIUS
		FROM MS_AREAOFUSER MAU with (nolock) 
			LEFT OUTER JOIN MS_AREA MA with (nolock) ON MAU.UUID_AREA = MA.UUID_AREA
			LEFT OUTER JOIN MS_AREAPATH MAP with (nolock) ON MA.UUID_AREA = MAP.UUID_AREA
		WHERE MAU.UUID_MS_USER = :uuidCollector
	</sql-query>
	
	<sql-query name="mobiletracking.dm.undoneTask">
    <query-param name="uuidUser" type="string" />
    <query-param name="uuidSubSystem" type="string" />
		SELECT trth.UUID_TASK_H, 
			ISNULL(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,
			ISNULL(trth.CUSTOMER_ADDRESS,'-') CUSTOMER_ADDRESS,
			trth.LATITUDE, 
			trth.LONGITUDE,
			ammu.INITIAL_NAME,
			mg.GROUP_SEQ,
			mg.TASK_SEQ
		FROM TR_TASK_H trth with (nolock)
		JOIN MS_GROUPTASK mg with (nolock) on trth.uuid_task_h = mg.uuid_task_h 
		LEFT JOIN MS_STATUSTASK msst with (nolock)
			ON trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
		LEFT JOIN AM_MSUSER ammu with (nolock)
			ON trth.UUID_MS_USER = ammu.UUID_MS_USER
		WHERE msst.STATUS_CODE = 'N'
			AND ammu.UUID_MS_USER = :uuidUser
			AND ammu.UUID_MS_SUBSYSTEM = :uuidSubSystem
			<![CDATA[and trth.ASSIGN_DATE <= :end]]>
	</sql-query>
	
	<sql-query name="mobiletracking.dm.surveyLocation">
    <query-param name="uuidSPV" type="string" />
    <query-param name="uuidSubSystem" type="string" />
    <query-param name="start" type="string" />
    <query-param name="end" type="string" />
		SELECT trth.UUID_TASK_H, 
			ISNULL(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,
			ISNULL(trth.CUSTOMER_ADDRESS,'-') CUSTOMER_ADDRESS,
			ISNULL(trth.AGREEMENT_NO,'-') APPL_NO,   
			trth.LATITUDE, 
			trth.LONGITUDE,
			ammu.INITIAL_NAME
		FROM TR_TASK_H trth with (nolock) INNER JOIN TR_TASK_D trtd with (nolock)
			ON trth.UUID_TASK_H = trtd.UUID_TASK_H
		LEFT JOIN MS_QUESTION msq with (nolock) 
			ON trtd.UUID_QUESTION = msq.UUID_QUESTION
		LEFT JOIN MS_ASSETTAG msat with (nolock) 
			ON msq.UUID_ASSET_TAG = msat.UUID_ASSET_TAG
		LEFT JOIN AM_MSUSER ammu with (nolock)
			ON trth.UUID_MS_USER = ammu.UUID_MS_USER
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			AND msat.ASSET_TAG_NAME = 'HOME'
		--	AND trth.UUID_TASK_H IN
		--		(SELECT trtd2.UUID_TASK_H FROM TR_TASK_D trtd2 with (nolock))
			AND ammu.SPV_ID = :uuidSPV
			AND ammu.UUID_MS_SUBSYSTEM = :uuidSubSystem
	</sql-query>
	
	<sql-query name="mobiletracking.dm.collectedLocation">
    <query-param name="uuidSPV" type="string" />
    <query-param name="uuidSubSystem" type="string" />
    <query-param name="start" type="string" />
    <query-param name="end" type="string" />
		SELECT trth.UUID_TASK_H, 
			ISNULL(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,
			ISNULL(trth.CUSTOMER_ADDRESS,'-') CUSTOMER_ADDRESS,
			ISNULL(trth.AGREEMENT_NO,'-') APPL_NO,   
			trtd.LATITUDE, 
			trtd.LONGITUDE,
			ammu.INITIAL_NAME
		FROM TR_TASK_H trth with (nolock) INNER JOIN TR_TASK_D trtd with (nolock)
			ON trth.UUID_TASK_H = trtd.UUID_TASK_H
		LEFT JOIN MS_QUESTION msq with (nolock) 
			ON trtd.UUID_QUESTION = msq.UUID_QUESTION
		LEFT JOIN MS_COLLECTIONTAG msct with (nolock) 
			ON msq.UUID_COLLECTION_TAG = msct.UUID_COLLECTION_TAG
		LEFT JOIN AM_MSUSER ammu with (nolock)
			ON trth.UUID_MS_USER = ammu.UUID_MS_USER
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			AND msct.TAG_NAME = 'COLLECTION LOCATION'
		--	AND trth.UUID_TASK_H IN
		--		(SELECT trtd2.UUID_TASK_H FROM TR_TASK_D trtd2 with (nolock))
			AND ammu.SPV_ID = :uuidSPV
			AND ammu.UUID_MS_SUBSYSTEM = :uuidSubSystem
	</sql-query>
	
<sql-query name="mobiletracking.dm.collectedLocation2">
    <query-param name="uuidSPV" type="string" />
    <query-param name="uuidSubSystem" type="string" />
    <query-param name="start" type="string" />
    <query-param name="end" type="string" />
		SELECT DISTINCT trth.UUID_TASK_H, 
			ISNULL(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,
			ISNULL(trth.CUSTOMER_ADDRESS,'-') CUSTOMER_ADDRESS,
			ISNULL(trth.AGREEMENT_NO,'-') APPL_NO,   
			trth.LATITUDE, 
			trth.LONGITUDE,
			ammu.INITIAL_NAME,
			mg.GROUP_SEQ,
			mg.TASK_SEQ			
		FROM TR_TASK_H trth with (nolock) INNER JOIN TR_TASK_D trtd with (nolock)
			ON trth.UUID_TASK_H = trtd.UUID_TASK_H
		JOIN MS_GROUPTASK mg with (nolock)
			 ON trth.uuid_task_h = mg.uuid_task_h 
		LEFT JOIN MS_QUESTION msq with (nolock) 
			ON trtd.UUID_QUESTION = msq.UUID_QUESTION
		LEFT JOIN AM_MSUSER ammu with (nolock)
			ON trth.UUID_MS_USER = ammu.UUID_MS_USER
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
		--	AND msct.TAG_NAME = 'COLLECTION LOCATION'
		--	AND trth.UUID_TASK_H IN
		--		(SELECT trtd2.UUID_TASK_H FROM TR_TASK_D trtd2 with (nolock))
			AND ammu.UUID_MS_USER = :uuidSPV
			AND ammu.UUID_MS_SUBSYSTEM = :uuidSubSystem
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getTaskRecapitulation">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="idSubsystem" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select 
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
				join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
				where ASSIGN_DATE IS NOT NULL
				and DOWNLOAD_DATE IS NULL
				and mst.STATUS_CODE = 'N'
				and UUID_MS_USER = :uuidUser
				<![CDATA[and trth.ASSIGN_DATE <= :end]]>
			) as newTask,
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
			join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
				where READ_DATE IS NOT NULL
				and START_DTM IS NULL
				and submit_date IS NULL
				and mst.STATUS_CODE = 'N'
				and UUID_MS_USER = :uuidUser
				<![CDATA[and trth.ASSIGN_DATE <= :end]]>
			) as reads,
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
			join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
				where DOWNLOAD_DATE IS NOT NULL
				AND READ_DATE IS NULL
				AND START_DTM IS NULL
				and submit_date IS NULL
				and mst.STATUS_CODE = 'N'
				and UUID_MS_USER = :uuidUser
				<![CDATA[and trth.ASSIGN_DATE <= :end]]>
			) as download, 
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
				join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
				where SUBMIT_DATE IS NOT NULL
				and MST.STATUS_CODE NOT IN ('N','U','D')
				and SUBMIT_DATE BETWEEN :start AND :end
				and UUID_MS_USER = :uuidUser
				AND ( trth.ASSIGN_DATE BETWEEN :start AND :end)
			) as submit,
			(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
				join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
				where mst.STATUS_CODE = 'U'
				and mst.UUID_MS_SUBSYSTEM = :idSubsystem
				and SUBMIT_DATE IS NOT NULL
				and START_DTM IS NOT NULL
				and trth.uuid_ms_user = :uuidUser
				AND ( trth.ASSIGN_DATE BETWEEN :start AND :end)
			) as upload, 
			(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
				join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
				where mst.STATUS_CODE = 'V'
				and START_DTM IS NOT NULL
				and mst.UUID_MS_SUBSYSTEM = :idSubsystem
				and trth.uuid_ms_user = :uuidUser
				<![CDATA[and trth.ASSIGN_DATE <= :end]]>
			) as verifikasi,
			(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
				join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
				where mst.STATUS_CODE = 'N'
				and mst.UUID_MS_SUBSYSTEM = :idSubsystem
				and START_DTM IS NOT NULL 
				and submit_date IS NULL
				and trth.uuid_ms_user = :uuidUser
				<![CDATA[and trth.ASSIGN_DATE <= :end]]>
			) as survey
	</sql-query>
	
	<sql-query name="mobiletracking.dm.taskmonitoring.load">
	   	<query-param name="uuidColl" type="string" />
	   	<query-param name="uuidSubsystem" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	select sub.* from (
		SELECT trth.UUID_TASK_H, trth.TASK_ID, trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, trth.CUSTOMER_PHONE, trth.ZIP_CODE, msf.FORM_NAME, 
			CASE 
				WHEN trth.ASSIGN_DATE IS NOT NULL AND trth.DOWNLOAD_DATE IS NULL and ms.STATUS_CODE = 'N'
					THEN 'red' 
				WHEN trth.DOWNLOAD_DATE IS NOT NULL AND trth.READ_DATE IS NULL AND trth.START_DTM IS NULL
					THEN 'orange'
				WHEN trth.READ_DATE IS NOT NULL AND trth.START_DTM IS NULL 
					THEN 'blue'
				WHEN ms.STATUS_CODE = 'N' AND ms.UUID_MS_SUBSYSTEM = :uuidSubsystem
					AND trth.START_DTM IS NOT NULL AND trth.SUBMIT_DATE IS NULL   
					THEN 'purple'
				WHEN ms.STATUS_CODE = 'U' AND ms.UUID_MS_SUBSYSTEM = :uuidSubsystem 
					AND trth.SUBMIT_DATE IS NOT NULL and trth.START_DTM IS NOT NULL
					THEN 'yellow'
				WHEN trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end and MS.STATUS_CODE NOT IN ('N','U','D')
					THEN 'green'
			END as status, msl.LOCATION_NAME, trth.ASSIGN_DATE
			FROM TR_TASK_H trth with (nolock) JOIN MS_STATUSTASK ms with (nolock) ON trth.UUID_STATUS_TASK = ms.UUID_STATUS_TASK
			JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
			JOIN MS_LOCATION msl with (nolock) ON trth.UUID_LOCATION = msl.UUID_LOCATION
			WHERE trth.UUID_MS_USER = :uuidColl
			AND ((trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end) OR (trth.SUBMIT_DATE IS NULL))
			AND ms.STATUS_CODE NOT IN ('C','D') 
			) sub where <![CDATA[(sub.status in ('red','blue','orange','purple') and sub.ASSIGN_DATE <= :end) ]]>
			OR (sub.status in ('yellow','green') and sub.ASSIGN_DATE BETWEEN :start AND :end)
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getUsersAll">
	   	<query-param name="uuidSpv" type="string" />
		WITH N AS (
		SELECT 
		msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
		UNION ALL
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME,	msu2.IS_LOGGED_IN, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID from N order by FULL_NAME
	</sql-query>
	
	<sql-query name="mobiletracking.dm.countUsersAllByBranch">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="jobCode" type="string" />
		SELECT count(*) FROM AM_MSUSER usr with (nolock)
			JOIN MS_JOB jb with (nolock) on usr.UUID_JOB = jb.UUID_JOB
			WHERE UUID_BRANCH = :uuidBranch 
				AND jb.JOB_CODE = :jobCode AND usr.IS_ACTIVE = '1'
	</sql-query>
	<sql-query name="mobiletracking.dm.getUsersExc">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidSvy" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		WITH N AS (
		SELECT 
		msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_BRANCH, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
		UNION ALL
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.LOGIN_ID, msu2.UUID_BRANCH, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
		select N.UUID_MS_USER, N.FULL_NAME, N.IS_LOGGED_IN, N.LOGIN_ID, msb.BRANCH_NAME,
				ROW_NUMBER() OVER (ORDER BY FULL_NAME) AS rownum
		from N JOIN MS_BRANCH msb with (nolock) ON N.UUID_BRANCH = msb.UUID_BRANCH
		where UUID_MS_USER != :uuidSvy
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="mobiletracking.dm.getUsersExcCnt">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidSvy" type="string" />
	   	WITH N AS (
		SELECT 
		msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_BRANCH, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
		UNION ALL
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.LOGIN_ID, msu2.UUID_BRANCH, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select count(1)
		from N JOIN MS_BRANCH msb with (nolock) ON N.UUID_BRANCH = msb.UUID_BRANCH
		where UUID_MS_USER != :uuidSvy
	   	
	</sql-query>
	<sql-query name="mobiletracking.dm.getUsersExcAssignInOut">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidSvy" type="string" />
		WITH N AS (
		SELECT 
		msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_BRANCH, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
		UNION ALL
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.LOGIN_ID, msu2.UUID_BRANCH, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID, msb.BRANCH_NAME 
		from N JOIN MS_BRANCH msb with (nolock) ON N.UUID_BRANCH = msb.UUID_BRANCH
		where UUID_MS_USER != :uuidSvy order by FULL_NAME
	</sql-query>
	<sql-query name="mobiletracking.dm.getAsset">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="asset" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		SELECT msat.ASSET_TAG_NAME, msat.ASSET_TAG_IMAGE, trtdl.LATITUDE, trtdl.LONGITUDE, 
			trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, ISNULL(LEFT(CONVERT(VARCHAR(30), trth.ASSIGN_DATE, 113),17), '') assignDate, 
			ISNULL(LEFT(CONVERT(VARCHAR(30), trth.SUBMIT_DATE , 113),17), '') submitDate, trth.UUID_TASK_H 
		FROM TR_TASK_H trth with (nolock) 
		LEFT OUTER JOIN TR_TASKDETAILLOB trtdl with (nolock) on trth.UUID_TASK_H = trtdl.UUID_TASK_H
		LEFT OUTER JOIN MS_QUESTION msq with (nolock) on trtdl.QUESTION_ID = msq.UUID_QUESTION
		LEFT OUTER JOIN MS_ASSETTAG msat with (nolock) on msat.UUID_ASSET_TAG = msq.UUID_ASSET_TAG 
		LEFT OUTER JOIN MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			and trth.UUID_MS_USER = :uuidUser
			and trtdl.IS_GPS = '1'
		  and msat.ASSET_TAG_NAME = :asset
		  and mst.STATUS_CODE not in ('RS')
	</sql-query>
	<sql-query name="mobiletracking.dm.getAttendanceIn">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		SELECT MIN(CONVERT(VARCHAR(5),datetime_IN,108) ) FROM TR_ATTENDANCE with (nolock)
 		WHERE datetime_IN BETWEEN :start AND :end
		AND UUID_MS_USER= :uuidUser
	</sql-query>
	<sql-query name="mobiletracking.dm.getLastTimeDetected">
	   	<query-param name="uuidUser" type="string" />
		select MAX(CONVERT(VARCHAR(5), datetime, 108) ) from TR_LOCATIONHISTORY with (nolock) 
		where datetime BETWEEN :start AND :end
		and UUID_MS_USER= :uuidUser
	</sql-query>
	<sql-query name="mobiletracking.dm.getTotalTaskSurveyor">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="startTW" type="string" />
	   	<query-param name="endTW" type="string" />
	   	<query-param name="startLW" type="string" />
	   	<query-param name="endLW" type="string" />
	   	<query-param name="startTM" type="string" />
	   	<query-param name="endTM" type="string" />
	   	<query-param name="startLM" type="string" />
	   	<query-param name="endLM" type="string" />
	   	<query-param name="currentDate" type="string" />
		select 
			(select SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startTW AND :endTW
			) as assignTW, 
			(select SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startLW AND :endLW
			) as assignLW, 
			(select SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startTW AND :endTW
			) as submitTW, 
			(select SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startLW AND :endLW
			) as submitLW, 
			(select SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startTM AND :endTM
			) as assignTM, 
			(select SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startLM AND :endLM
			) as assignLM, 
			(select SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startTM AND :endTM
			) as submitTM, 
			(select SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startLM AND :endLM
			) as submitLM, 
			(select SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE = :currentDate) as newTask
	</sql-query>	
	<sql-query name="mobiletracking.dm.getTotalTaskBranch">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="startTW" type="string" />
	   	<query-param name="endTW" type="string" />
	   	<query-param name="startLW" type="string" />
	   	<query-param name="endLW" type="string" />
	   	<query-param name="startTM" type="string" />
	   	<query-param name="endTM" type="string" />
	   	<query-param name="startLM" type="string" />
	   	<query-param name="endLM" type="string" />
   		<query-param name="currentDate" type="string" />
		SELECT 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE = :currentDate
				 ) as assignTd,
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startTW AND :endTW
				 ) as assignTW, 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startLW AND :endLW
				 ) as assignLW, 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startTM AND :endTM
				 ) as assignTM, 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startLM AND :endLM
				 ) as assignLM, 
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE = :currentDate
				 ) as submitTd, 
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startTW AND :endTW
				 ) as submitTW, 
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startLW AND :endLW
				 ) as submitLW,
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startTM AND :endTM
				 ) as submitTM,
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startLM AND :endLM
				 ) as submitLM,
			(SELECT SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE = :currentDate
				 ) as newTaskTd,
			(SELECT SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startTW AND :endTW
				 ) as newTaskTW,
			(SELECT SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_MS_USER IN (select keyValue from getUserByLogin(:uuidSpv))
				 AND DAILY_DATE BETWEEN :startTM AND :endTM
				 ) as newTaskTM
	</sql-query>
	<sql-query name="mobiletracking.dm.getTotalTaskBranchByBranch">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="startTW" type="string" />
	   	<query-param name="endTW" type="string" />
	   	<query-param name="startLW" type="string" />
	   	<query-param name="endLW" type="string" />
	   	<query-param name="startTM" type="string" />
	   	<query-param name="endTM" type="string" />
	   	<query-param name="startLM" type="string" />
	   	<query-param name="endLM" type="string" />
   		<query-param name="currentDate" type="string" />
		SELECT 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE = :currentDate
				 ) as assignTd,
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTW AND :endTW
				 ) as assignTW, 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startLW AND :endLW
				 ) as assignLW, 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTM AND :endTM
				 ) as assignTM, 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startLM AND :endLM
				 ) as assignLM, 
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE = :currentDate
				 ) as submitTd, 
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTW AND :endTW
				 ) as submitTW, 
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startLW AND :endLW
				 ) as submitLW,
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTM AND :endTM
				 ) as submitTM,
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startLM AND :endLM
				 ) as submitLM,
			(SELECT SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE = :currentDate
				 ) as newTaskTd,
			(SELECT SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTW AND :endTW
				 ) as newTaskTW,
			(SELECT SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTM AND :endTM
				 ) as newTaskTM
	</sql-query>
	<sql-query name="mobiletracking.dm.taskmonitoring.getFormByGroupTask">
	   	<query-param name="groupTaskId" type="string" />
	   	<query-param name="subsystemId" type="string" />
	   	SELECT UUID_FORM, FORM_NAME
		FROM MS_FORM with (nolock)
		WHERE IS_ACTIVE = '1'
			AND UUID_MS_SUBSYSTEM = :subsystemId
		EXCEPT
		SELECT DISTINCT MF.UUID_FORM, MF.FORM_NAME
		FROM MS_GROUPTASK MG with (nolock) 
			JOIN TR_TASK_H TRTH with (nolock) ON MG.UUID_TASK_H = TRTH.UUID_TASK_H
			JOIN MS_FORM MF with (nolock) ON TRTH.UUID_FORM = MF.UUID_FORM
		WHERE MG.GROUP_TASK_ID = 
			(SELECT GROUP_TASK_ID FROM MS_GROUPTASK with (nolock) WHERE UUID_GROUP_TASK = :groupTaskId)
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getSpvList">
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="jobCode" type="string"/>
		WITH N AS (
			SELECT 
			msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, msu.UUID_JOB
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME,	msu2.IS_LOGGED_IN, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, msu2.UUID_JOB
			FROM   AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  FROM (
					select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
					from N join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
					where  J.JOB_CODE = :jobCode
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getSpvListCount">
		<query-param name="jobCode" type="string"/>
		WITH N AS (
			SELECT 
			msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, msu.UUID_JOB
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME,	msu2.IS_LOGGED_IN, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, msu2.UUID_JOB
			FROM   AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select COUNT(1) from (
			select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
			from N join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
			where  J.JOB_CODE = :jobCode
		) c
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getListHierarkiCabang">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="branchLogin" type="string"/>
		<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select keyValue, BRANCH_CODE, BRANCH_NAME, ROW_NUMBER() OVER (ORDER BY BRANCH_CODE) AS rownum 
				from dbo.getCabangByLogin(:branchLogin)
				where BRANCH_CODE like '%' + :branchCode + '%'
				and BRANCH_NAME like '%' + :branchName + '%'
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getListHierarkiCabangCount">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="branchLogin" type="string"/>
		select count(BRANCH_CODE) 
		from dbo.getCabangByLogin(:branchLogin)
		where BRANCH_CODE like '%' + :branchCode + '%'
		and BRANCH_NAME like '%' + :branchName + '%'
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getSpvListByBranch">
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="jobCode" type="string"/>
		<query-param name="uuidBranch" type="string"/>
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  FROM (
					select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
					from am_msuser am with (nolock) 
					join MS_JOB J with (nolock) on am.UUID_JOB = J.UUID_JOB
					join MS_BRANCH mb with (nolock) on mb.UUID_BRANCH = am.UUID_BRANCH
					where  J.JOB_CODE = :jobCode
					and am.uuid_branch = :uuidBranch
					and am.is_active = '1' and am.is_deleted = '0'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getSpvListByBranchCount">
		<query-param name="jobCode" type="string"/>
		<query-param name="uuidBranch" type="string"/>
		select COUNT(1) from (
			select UUID_MS_USER
			from am_msuser am with (nolock) 
			join MS_JOB J with (nolock) on am.UUID_JOB = J.UUID_JOB
			join MS_BRANCH mb with (nolock) on mb.UUID_BRANCH = am.UUID_BRANCH
			where  J.JOB_CODE = :jobCode
			and am.uuid_branch = :uuidBranch
			and am.is_active = '1' and am.is_deleted = '0'
		) c
	</sql-query>
	<sql-query name="mobiletracking.dm.getGroupTaskList">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.GROUP_TASK_ID) AS rownum  FROM (
					select distinct(GROUP_TASK_ID), CUSTOMER_NAME, APPL_NO, BRANCH_NAME, UUID_GROUP_TASK
					from MS_GROUPTASK mg with (nolock) inner join ms_branch mb with (nolock)
					on mb.uuid_branch = mg.uuid_branch
					where mg.customer_name like '%' + :customerName + '%'
					and mg.uuid_branch = :msBranch.uuidBranch 
					and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]> 
	</sql-query>
	<sql-query name="mobiletracking.dm.getGroupTaskListCount">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
		select count(distinct(group_task_id))
		from MS_GROUPTASK mg with (nolock) inner join ms_branch mb with (nolock)
			on mb.uuid_branch = mg.uuid_branch
		where mg.customer_name like '%' + :customerName + '%'
		and mg.uuid_branch = :msBranch.uuidBranch 
		and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'					
	</sql-query>
	
	<sql-query name="mobiletracking.dm.getGroupTaskListAll">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.GROUP_TASK_ID) AS rownum  FROM (
					select distinct(GROUP_TASK_ID), CUSTOMER_NAME, APPL_NO, mb.BRANCH_NAME, UUID_GROUP_TASK
					from MS_GROUPTASK mg with (nolock) inner join ms_branch mb with (nolock)
					on mb.uuid_branch = mg.uuid_branch
					join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:msBranch.uuidBranch)) msb on mg.UUID_BRANCH = msb.UUID_BRANCH
					where mg.customer_name like '%' + :customerName + '%'
					and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]> 
	</sql-query>
	<sql-query name="mobiletracking.dm.getGroupTaskListCountAll">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
		select count(distinct(group_task_id))
		from MS_GROUPTASK mg with (nolock) inner join ms_branch mb with (nolock)
			on mb.uuid_branch = mg.uuid_branch
			join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:msBranch.uuidBranch)) msb on mg.UUID_BRANCH = msb.UUID_BRANCH
		where mg.customer_name like '%' + :customerName + '%'
		and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'					
	</sql-query>
	<sql-query name="mobiletracking.dm.getGroupTaskListByHierarkiUser">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="applNo" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.GROUP_TASK_ID) AS rownum  FROM (
					select distinct(GROUP_TASK_ID), mg.CUSTOMER_NAME, mg.APPL_NO, BRANCH_NAME, UUID_GROUP_TASK
					from MS_GROUPTASK mg with(nolock) inner join ms_branch mb with (nolock)
					on mb.uuid_branch = mg.uuid_branch
					inner join tr_task_h tth with (nolock)
					on tth.uuid_task_h = mg.uuid_task_h
					inner join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = tth.UUID_MS_USER
					where mg.customer_name like '%' + :customerName + '%'
					and mg.uuid_branch = :msBranch.uuidBranch
					and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]> 
	</sql-query>
	<sql-query name="mobiletracking.dm.getGroupTaskListByHierarkiUserCount">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
	   	<query-param name="uuidUser" type="string" />
		select count(distinct(group_task_id))
		from MS_GROUPTASK mg with(nolock) inner join ms_branch mb with (nolock)
			on mb.uuid_branch = mg.uuid_branch
			inner join tr_task_h tth with (nolock)
			on tth.uuid_task_h = mg.uuid_task_h
			inner join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = tth.UUID_MS_USER
		where mg.customer_name like '%' + :customerName + '%'
			and mg.uuid_branch = :msBranch.uuidBranch
			and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'
	</sql-query>
</hibernate-mapping>