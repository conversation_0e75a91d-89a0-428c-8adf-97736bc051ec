<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>	
	<sql-query name="task.newTaskMT.getGroupSeq">
		<query-param name="uuidUser" type="string" />
		<query-param name="start" type="string" />
		<query-param name="end" type="string" />
		SELECT COUNT(MSG.UUID_GROUP_TASK) FROM MS_GROUPTASK MSG with (nolock)
		join TR_TASK_H trth with(nolock) on trth.UUID_TASK_H = MSG.UUID_TASK_H
		where trth.UUID_MS_USER = :uuidUser 
		and msg.dtm_crt BETWEEN :start and :end
	</sql-query>
	
	<sql-query name="task.newTaskMT.getGroupSeqFromGroupTaskId">
		<query-param name="groupTaskId" type="string" />
		SELECT MSG.GROUP_SEQ FROM MS_GROUPTASK MSG with (nolock)
		WHERE MSG.GROUP_TASK_ID = :groupTaskId
	</sql-query>
	
	<sql-query name="task.newTaskMT.getGroupSeqFromGroupTaskid">
		<query-param name="groupTaskId" type="string" />
		SELECT TOP 1 MSG.GROUP_SEQ FROM MS_GROUPTASK MSG with (nolock)
		WHERE MSG.GROUP_TASK_ID = :groupTaskId
		ORDER BY MSG.GROUP_SEQ DESC
	</sql-query>
	
	<sql-query name="task.newTaskMT.getTaskSeqFromGroupTaskId">
		<query-param name="groupTaskId" type="string" />
		SELECT TOP 1 a.TASK_SEQ from MS_GROUPTASK a
		join TR_TASK_H b on b.UUID_TASK_H=a.UUID_TASK_H
		where GROUP_TASK_ID=:groupTaskId
		ORDER BY SUBMIT_DATE DESC
	</sql-query>

</hibernate-mapping>