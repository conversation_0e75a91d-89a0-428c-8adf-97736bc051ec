<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>	
	<sql-query name="task.newtracking.getPrevGroupTask">
		<query-param name="uuidGroupTask" type="string" />
		SELECT TOP 1 A.UUID_GROUP_TASK, A.GROUP_TASK_ID, A.GROUP_SEQ
		FROM MS_GROUPTASK A WITH (NOLOCK)
		WHERE A.UUID_GROUP_TASK = :uuidGroupTask
	</sql-query>
	
<!-- 	<sql-query name="task.newtracking.getPrevTaskSeq"> -->
<!-- 		<query-param name="groupTaskID" type="string" /> -->
<!-- 		SELECT TOP 1 A.UUID_GROUP_TASK, A<PERSON><PERSON>_TASK_ID, <PERSON><PERSON>, <PERSON><PERSON>_SE<PERSON>, ST.STATUS_CODE -->
<!-- 		FROM MS_GROUPTASK A WITH (NOLOCK) -->
<!-- 		JOIN TR_TASK_H TH WITH (NOLOCK) ON (A.UUID_TASK_H = TH.UUID_TASK_H) -->
<!-- 		JOIN MS_STATUSTASK ST WITH (NOLOCK) ON (TH.UUID_STATUS_TASK = ST.UUID_STATUS_TASK) -->
<!-- 		WHERE A.GROUP_TASK_ID = :groupTaskID -->
<!-- 			AND TH.DOWNLOAD_DATE IS NOT NULL -->
<!-- 		ORDER BY A.TASK_SEQ DESC -->
<!-- 	</sql-query> -->
	<sql-query name="task.newtracking.getPrevTaskSeq">
		<query-param name="groupTaskID" type="string" />
		SELECT TOP 1 A.UUID_GROUP_TASK, A.GROUP_TASK_ID, A.GROUP_SEQ, A.TASK_SEQ, ST.STATUS_CODE
		FROM MS_GROUPTASK A WITH (NOLOCK)
		JOIN TR_TASK_H TH WITH (NOLOCK) ON (A.UUID_TASK_H = TH.UUID_TASK_H)
		JOIN MS_STATUSTASK ST WITH (NOLOCK) ON (TH.UUID_STATUS_TASK = ST.UUID_STATUS_TASK)
		WHERE A.GROUP_TASK_ID = :groupTaskID
			AND TH.START_DTM IS NOT NULL
			AND TH.SUBMIT_DATE IS NULL
		ORDER BY A.TASK_SEQ DESC
	</sql-query>
	
	<sql-query name="task.newtracking.getPrevGroupSeq">
		<query-param name="uuidMsuserDriver" type="string" />
		<query-param name="dateAssign" type="string" />
		SELECT TOP 1 GT.UUID_GROUP_TASK, GT.GROUP_SEQ
		FROM MS_GROUPTASK GT WITH (NOLOCK)
			JOIN TR_TASK_H TH WITH (NOLOCK) ON (GT.UUID_TASK_H = TH.UUID_TASK_H)
		WHERE TH.UUID_MS_USER = :uuidMsuserDriver
			AND CAST(TH.ASSIGN_DATE AS DATE) = CAST(:dateAssign AS DATE)
		ORDER BY GT.GROUP_TASK_ID DESC
	</sql-query>
	
	<sql-query name="task.newtracking.isClosing">
		<query-param name="uuidMsuserDriver" type="string" />
		<query-param name="dateAssign" type="string" />
		SELECT TOP 1 *
		FROM TR_ATTENDANCE WITH (NOLOCK)
		WHERE UUID_MS_USER = :uuidMsuserDriver
			AND CAST (ATTENDANCE_DATE AS DATE) = CAST (:dateAssign AS DATE)
			AND datetime_OUT IS NOT NULL
	</sql-query>
	
	<sql-query name="task.newtracking.isTaskExist">
		<query-param name="dateAssign" type="string" />
		<query-param name="uuidMsuserDriver" type="string" />
		<query-param name="taskSeq" type="string" />
		<query-param name="groupSeq" type="string" />
		SELECT TOP 1 A.ASSIGN_DATE, A.UUID_MS_USER, B.GROUP_TASK_ID, B.TASK_SEQ
		FROM TR_TASK_H A LEFT OUTER JOIN  MS_GROUPTASK B ON (A.UUID_TASK_H = B.UUID_TASK_H)
		WHERE CAST(A.ASSIGN_DATE AS DATE) = CAST(:dateAssign AS DATE) 
			AND A.UUID_MS_USER = :uuidMsuserDriver 
			AND B.TASK_SEQ = :taskSeq
			AND B.GROUP_SEQ = :groupSeq
		ORDER BY ASSIGN_DATE
	</sql-query>

</hibernate-mapping>