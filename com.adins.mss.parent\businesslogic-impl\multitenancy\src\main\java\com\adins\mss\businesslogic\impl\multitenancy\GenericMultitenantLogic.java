package com.adins.mss.businesslogic.impl.multitenancy;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.multitenancy.MultitenantLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.multitenancy.model.MsTenant;
import com.adins.mss.multitenancy.util.MultitenancyUtils;

public class GenericMultitenantLogic extends BaseLogic implements MultitenantLogic {
    
    @Transactional
    @Override
    public void logoutAll(AuditContext callerId) {
        //#multitenancy
        String schema = MultitenancyUtils.extractTenantId(callerId.getCallerId(),
                GlobalVal.TENANT_TOKEN);
     
        String[][] params = {{"usrUpd", callerId.getCallerId()}};
        this.getManagerDAO().updateNative("am.login.releaseAllUserLoggedIn", params);
    }    
    
    @Transactional(readOnly=true)
    @SuppressWarnings("unchecked")
    @Override
    public List<MsTenant> listTenants(String tenantCodeFilter, AuditContext callerId) {
    	Object[][] params = new Object[2][1];
        params[0][0] = Restrictions.eq("isActive", "1");
        if (StringUtils.isNotBlank(tenantCodeFilter)) {
            params[1][0] = Restrictions.eq("tenantCode", StringUtils.upperCase(tenantCodeFilter));
        }
        Map<String, Object> result = this.getManagerDAO().list(MsTenant.class, params, null);
        List<MsTenant> resultList = (List<MsTenant>) result.get(GlobalKey.MAP_RESULT_LIST);
        if (resultList == null)
            return Collections.<MsTenant> emptyList();
        else
            return resultList;
    }

    @Transactional(readOnly=true)
	@Override
	public int countTenants(AuditContext callerId) {
        Object[][] params = new Object[1][1];
        params[0][0] = Restrictions.eq("isActive", "1");
    	Map<String, Object> result = this.getManagerDAO().count(MsTenant.class, params);
    	return (int) ((long) result.get(GlobalKey.MAP_RESULT_SIZE));
	}
    
}
