package com.adins.mss.businesslogic.impl.multitenancy;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.service.security.ApiKeyAuthenticator;
import com.adins.framework.service.security.ClientDetails;
import com.adins.framework.service.security.InvalidApiKeyException;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.multitenancy.MultitenantException;
import com.adins.mss.multitenancy.model.MsTenant;

@Transactional
public class TenantApiKeyAuthenticator extends BaseLogic implements ApiKeyAuthenticator {

	private static final Logger LOG = LoggerFactory.getLogger(TenantApiKeyAuthenticator.class);
	
	 @Transactional(readOnly=true)
     @Override
     public ClientDetails authenticate(String apiKey) {
             return this.authenticate(apiKey, null);
     }
	
	 @Transactional(readOnly=true)
	 @Override
     public ClientDetails authenticate(String apiKey, String remoteAddress) {
		 if (!StringUtils.contains(apiKey, '@')) {
             throw new InvalidApiKeyException("Api Key does not contains tenant code");
		 }
		
		 String[] apiKeyStripped = StringUtils.splitPreserveAllTokens(apiKey, '@');
         String key = apiKeyStripped[0];
         String tenantCode = StringUtils.upperCase(apiKeyStripped[1]);
		
         Object[][] sqlParams = { {Restrictions.eq("tenantCode", tenantCode)} };
         MsTenant tenant = this.getManagerDAO().selectOne(MsTenant.class, sqlParams);
		
         if (tenant == null) {
	     	LOG.error("Tenant code not found. Id={}", tenantCode);
	     	throw new MultitenantException("Invalid tenant code",
	        	MultitenantException.Reason.INVALID_TENANT_CODE);
	     }
		
         if (!StringUtils.equals(key, tenant.getApiKey())) {
             throw new InvalidApiKeyException();
         }
		
         ClientDetails client = new ClientDetails();
         client.setClientId(tenant.getTenantName());
		
         return client;
	}

}
