package com.adins.mss.businesslogic.impl.order;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Blob;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.HibernateException;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.order.CancelOrderLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.TrTaskorderdata;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.services.model.order.CancelOrderBean;
import com.adins.mss.services.model.order.CancelOrderDetail;
import com.google.common.io.BaseEncoding;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericCancelOrderLogic extends BaseLogic implements CancelOrderLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericCancelOrderLogic.class);
	    
    private ImageStorageLogic imageStorageLogic;

	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }

	@Override
	public List listCancelOrder(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.order.cancelOrderList", params, null);		
		return result;
	}
	
	@Override
	public Integer countListCancelOrder(Object params, AuditContext callerId) {
		Integer result = (Integer)this.getManagerDAO().selectOneNative("task.order.countCancelOrderList", params);		
		return result;
	}
	
	@Override
	public List getCancelOrder(long uuid, AuditContext callerId) {
		List result = new ArrayList<>();
		Object[][] params = { { "uuidTask", uuid } };
		List list = this.getManagerDAO().selectAllNative(
				"task.order.getCancelOrderHeader", params, null);
		for (int i = 0; i < list.size(); i++) {
			Map map = (HashMap) list.get(i);
			result.add(map);
		}
		return result;
	}

	@Override
	public Map detailCancelOrder(Object[][] params, AuditContext callerId) {		
		Boolean hasImage = Boolean.FALSE;

		List<Map<String, Object>> result = this.getManagerDAO().selectForListOfMap("task.order.getDetailOrderFromQSet", params, null);
		
		for (int i = 0; i < result.size(); i++) {
			Map map = (HashMap) result.get(i);
			
			if ("1".equals(map.get("isImage").toString())) { //d9=isImage
				if (!"1".equals((String) map.get("hasImage"))){
					map.put("textAnswer", StringUtils.EMPTY);
				}
				
				if (null != map.get("latitude")  && null != map.get("latitude")) {
					hasImage = Boolean.TRUE;
				}
			}
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("msQuestion.msAnswertype.codeAnswerType").toString())){
				NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
				if (StringUtils.isNotBlank((String) map.get("textAnswer"))) {
					map.put("textAnswer", formatKurs.format(NumberUtils.toDouble((String) map.get("textAnswer"))));
				}
			}
		}
		
		Map resultMap = new HashMap<>();		
		resultMap.put("resultList", result);
		resultMap.put("hasImage", hasImage);
		
		return resultMap;
	}
	
	private String getFullNameById(long uuidUser){
		AmMsuser bean =this.getManagerDAO().selectOne(AmMsuser.class, uuidUser);
		return bean.getFullName();
	}
	
	@Override
	public List viewMapPhoto(long uuid, AuditContext callerId) {
		List <TrTaskBean> result = new ArrayList<TrTaskBean>();
		Object[][] params = {{ "uuidTaskH", uuid } };
		List list = this.getManagerDAO().selectAllNative("task.cancelorder.viewMapPhoto", params, null);
		
		for (int i = 0; i < list.size(); i++) {
			Map map = (HashMap) list.get(i);
			if (map.get("d5")==null && map.get("d6")==null) {
				continue; //have no lat,lng
			}
			
			TrTaskBean cancelOrder = new TrTaskBean(); //Use approvalBean because has same parameter				
			if ("1".equals((String) map.get("d4"))) {
				cancelOrder.setLob(map.get("d8").toString());
			}
			
			cancelOrder.setHasImage(map.get("d4").toString());
			cancelOrder.setLatitude(new BigDecimal(map.get("d5").toString()));
			cancelOrder.setLongitude(new BigDecimal(map.get("d6").toString()));
			result.add(cancelOrder);				
		}
		return result;
	}
	
//	==================================================***** CANCEL ORDER MOBILE INTERFACE *****==================================================
	@Override
	public List<CancelOrderBean> cancelOrderHeader(String orderNumber, Date endDate, Date startDate, String custName, AuditContext callerId){
		List<CancelOrderBean> listResponseServer = new ArrayList<CancelOrderBean>();
		if((endDate != null && startDate != null)&& orderNumber == null && custName==null){
				AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
				DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
				String tmpStartDate = df.format(startDate)+" 00:00:00.000";
				String tmpEndDate = df.format(endDate)+" 23:59:59.997";
				Object[][] date = {{"uuidUser", callerId.getCallerId()},{"startDate", tmpStartDate}, {"endDate", tmpEndDate}};
				if (null != amMsuser) {
					List nomor = this.getManagerDAO().selectAllNative("task.order.cancelOrder.getHeader1A", date, null);
					Iterator itr = nomor.iterator();
					while(itr.hasNext()){
						CancelOrderBean cb = new CancelOrderBean();
						Map mp = (Map) itr.next();
						cb.setKey((String) mp.get("d0"));
						cb.setValue((String) mp.get("d1"));
						cb.setFlag(mp.get("d2").toString());
						listResponseServer.add(cb);
					}
				}
				else{
					List nomor = this.getManagerDAO().selectAllNative("task.order.cancelOrder.getHeader1B", date, null);
					Iterator itr = nomor.iterator();
					while(itr.hasNext()){
						CancelOrderBean cb = new CancelOrderBean();
						Map mp = (Map) itr.next();
						cb.setKey((String) mp.get("d0"));
						cb.setValue((String) mp.get("d1"));
						cb.setFlag(mp.get("d2").toString());
						listResponseServer.add(cb);
					}
				}
		}
		else if((endDate == null && startDate == null)&& orderNumber != null && custName==null){
				AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
				Object[][] orNum = {{"uuidUser", callerId.getCallerId()}, {"orderNumber", orderNumber}};
				if(!(String.valueOf(amMsuser.getAmMsuser().getUuidMsUser()).isEmpty())){
					List nomor = this.getManagerDAO().selectAllNative("task.order.cancelOrder.getHeader2A", orNum, null);
					Iterator itr = nomor.iterator();
					while(itr.hasNext()){
						CancelOrderBean cb = new CancelOrderBean();
						Map mp = (Map) itr.next();
						cb.setKey((String) mp.get("d0"));
						cb.setValue((String) mp.get("d1"));
						cb.setFlag(mp.get("d2").toString());
						listResponseServer.add(cb);
					}
				}
				else {
					List nomor = this.getManagerDAO().selectAllNative("task.order.cancelOrder.getHeader2B", orNum, null);
					Iterator itr = nomor.iterator();
					while(itr.hasNext()){
						CancelOrderBean cb = new CancelOrderBean();
						Map mp = (Map) itr.next();
						cb.setKey((String) mp.get("d0"));
						cb.setValue((String) mp.get("d1"));
						cb.setFlag(mp.get("d2").toString());
						listResponseServer.add(cb);
					}
				}
		}
		else if((endDate == null && startDate == null)&& orderNumber == null && custName!=null){
				AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
				Object[][] custNm = {{"uuidUser", callerId.getCallerId()}, {"custName", custName}};
				if(!(String.valueOf(amMsuser.getAmMsuser().getUuidMsUser()).isEmpty())){
					List nomor = this.getManagerDAO().selectAllNative("task.order.cancelOrder.getHeader3A", custNm, null);
					Iterator itr = nomor.iterator();
					while(itr.hasNext()){
						CancelOrderBean cb = new CancelOrderBean();
						Map mp = (Map) itr.next();
						cb.setKey((String) mp.get("d0"));
						cb.setValue((String) mp.get("d1"));
						cb.setFlag(mp.get("d2").toString());
						listResponseServer.add(cb);
					}
				}
				else {
					List nomor = this.getManagerDAO().selectAllNative("task.order.cancelOrder.getHeader3B", custNm, null);
					Iterator itr = nomor.iterator();
					while(itr.hasNext()){
						CancelOrderBean cb = new CancelOrderBean();
						Map mp = (Map) itr.next();
						cb.setKey((String) mp.get("d0"));
						cb.setValue((String) mp.get("d1"));
						cb.setFlag(mp.get("d2").toString());
						listResponseServer.add(cb);
					}
				}
		}
		return listResponseServer;
	}
	
	@Override
	public List<CancelOrderDetail> cancelOrderDetail(long uuidTaskH, AuditContext callerId){
		List<CancelOrderDetail> orderDetail = new ArrayList<CancelOrderDetail>();
			Object[][] id = {{"uuidTaskH", uuidTaskH}};
			CancelOrderDetail db = new CancelOrderDetail();
			List detailNon = this.getManagerDAO().selectAllNative("task.order.cancelOrder.getDetailNon", id, null);
			List detailI = this.getManagerDAO().selectAllNative("task.order.cancelOrder.getDetailI", id, null);
    			String oldLabel = StringUtils.EMPTY;
    			StringBuilder multiAnswer = new StringBuilder();
				for (int i = 0; i < detailNon.size(); i++) {
					Map mp = (Map) detailNon.get(i);
					
					if (!oldLabel.equals((String) mp.get("d0")) && !StringUtils.EMPTY.equals(oldLabel)) {
						orderDetail.add(db);
					}
					
					if (!oldLabel.equals((String) mp.get("d0"))) {
						db = new CancelOrderDetail();
						multiAnswer.setLength(0);
					}
					
					db.setKey((String) mp.get("d0"));
					if (StringUtils.isEmpty((String) mp.get("d2"))){
						db.setValue((String) mp.get("d1"));
					}
					else {
						if (!StringUtils.EMPTY.equals(multiAnswer.toString())) {
							multiAnswer.append("\n");
						}
						multiAnswer.append((String) mp.get("d2"));
						if (!((String) mp.get("d1")).isEmpty()){
							multiAnswer.append(" - " + (String) mp.get("d1"));
						}
						db.setValue(multiAnswer.toString());
					}
					db.setFlag("0");
					
					oldLabel = (String) mp.get("d0");
				}				
				LOG.trace("list : {}", db);
				Iterator itrI = detailI.iterator();
				while (itrI.hasNext()) {
					db = new CancelOrderDetail();
					Map mp = (Map)itrI.next();
					db.setKey((String) mp.get("d0"));
					db.setValue((String) mp.get("d1"));
					db.setFlag((String) mp.get("d2"));
					orderDetail.add(db);
				}
				LOG.trace("list 5 : {}", db);
		return orderDetail;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String cancelOrder(String orderNumber, String notes, AuditContext callerId){
			Object[][] order = {{Restrictions.eq("orderNo", orderNumber)}};
			TrTaskorderdata trTaskorderdata = this.getManagerDAO().selectOne(TrTaskorderdata.class, order);
			long uuidTaskId = trTaskorderdata.getUuidTaskId();
			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTaskId);
			MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, trTaskH.getMsStatustask().getUuidStatusTask());			
			
			if (!(GlobalVal.SURVEY_STATUS_TASK_UNASSIGN.equals(msStatustask.getStatusCode())||
				GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION.equals(msStatustask.getStatusCode())||
				GlobalVal.SURVEY_STATUS_TASK_VERIFICATION.equals(msStatustask.getStatusCode())||
				GlobalVal.SURVEY_STATUS_TASK_APPROVAL.equals(msStatustask.getStatusCode())) ) {
				throw new ChangeException("Status of this data has been changed, please repeat transactions.", msStatustask.getStatusCode());					
			}
			
			long subSystemO = this.getSubSystemId(uuidTaskId, callerId);
			Object[][] paramsSTO = { { Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DELETED) }, 
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", subSystemO)} };
			//update status task menjadi deleted tidak menggunakan wf karena setelah status A bukan D jika di commit wf
			msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, paramsSTO);
			trTaskH.setMsStatustask(msStatustask);
			trTaskH.setUsrUpd(callerId.getCallerId());
			trTaskH.setDtmUpd(new Date());
			trTaskH.setApprovalDate(new Date());
			this.getManagerDAO().update(trTaskH);
			
			insertTaskHistoryM(msStatustask, trTaskH, notes, callerId);
			
			Object[][] taskId = {{"uuidTaskId", uuidTaskId}};
			List svy = this.getManagerDAO().selectAllNative("task.order.cancelOrder.getSurveyId", taskId, null);
			if (svy != null && !svy.isEmpty()) {
				Map mp = (Map) svy.get(0);
				BigInteger surveyId = (BigInteger) mp.get("d0");
				
				//get Subsystem ID
				long subSystem = this.getSubSystemId(surveyId.longValue(), callerId);
				Object[][] paramsST = { { Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DELETED) }, 
						{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", subSystem)} };
				msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, paramsST);
				Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_DELETED) } };
				MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
				for (int i=0; i < svy.size(); i++) {
					mp = (Map) svy.get(i);
					surveyId = (BigInteger) mp.get("d0");
					trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, surveyId.longValue());						
					
					//update status task menjadi deleted tidak menggunakan wf karena setelah status A bukan D jika di commit wf						
					trTaskH.setMsStatustask(msStatustask);
					trTaskH.setUsrUpd(callerId.getCallerId());
					trTaskH.setDtmUpd(new Date());
					trTaskH.setApprovalDate(new Date());
					if(trTaskH.getSubmitDate() == null){
						trTaskH.setMsStatusmobile(msm);
					}
					this.getManagerDAO().update(trTaskH);
					
					insertTaskHistoryM(msStatustask, trTaskH, notes, callerId);
				}
			}
						
		return "Success.";
		
	}
	
	private long getSubSystemId(long uuidTaskH, AuditContext callerId){
		Object[][] uuid = {{"uuidTaskH", uuidTaskH}};
		BigInteger subSys = (BigInteger) this.getManagerDAO().selectOneNativeString(
				"select mst.UUID_MS_SUBSYSTEM from TR_TASK_H th" +
				" inner join MS_STATUSTASK mst on th.UUID_STATUS_TASK = mst.UUID_STATUS_TASK" +
				" where th.UUID_TASK_H = :uuidTaskH", uuid);
		return subSys.longValue();
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private void insertTaskHistoryM(MsStatustask msStatusTask, TrTaskH trTaskH, String notes, AuditContext auditContext) {
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(auditContext.getCallerId()));
		String fieldPerson = StringUtils.EMPTY;
		if (null != trTaskH.getAmMsuser()) {
			fieldPerson = this.getFullNameById(trTaskH.getAmMsuser().getUuidMsUser());
		}
		
		TrTaskhistory trTaskHistory = new TrTaskhistory(msStatusTask, trTaskH, auditContext.getCallerId(),
				new Date(), notes, fieldPerson,
				amMsuser.getFullName(), GlobalVal.CODE_PROCESS_DELETED);
		
		this.getManagerDAO().insert(trTaskHistory);
	}
	
	@Override
	public List listCancelOrderByHierarkiUser(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.order.cancelOrderListByHierarkiUser", params, null);		
		return result;
	}
	
	@Override
	public Integer countListCancelOrderByHierarkiUser(Object params, AuditContext callerId) {
		Integer result = (Integer)this.getManagerDAO().selectOneNative("task.order.countCancelOrderListByHierarkiUser", params);		
		return result;
	}
}