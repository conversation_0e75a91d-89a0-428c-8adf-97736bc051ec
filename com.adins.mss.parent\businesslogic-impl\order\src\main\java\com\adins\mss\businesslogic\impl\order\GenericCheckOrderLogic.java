package com.adins.mss.businesslogic.impl.order;

import java.math.BigInteger;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.order.CheckOrderLogic;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.services.model.order.CheckOrderBean;
import com.adins.mss.services.model.order.SubListResponseServerBean;
@SuppressWarnings("rawtypes")
public class GenericCheckOrderLogic extends BaseLogic implements CheckOrderLogic  {
//	private static final Logger LOG = LoggerFactory.getLogger(GenericCheckOrderLogic.class);
	

	@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<CheckOrderBean> listResponseServer(Date startDate, Date endDate, String orderNumber, String custName, AuditContext callerId){
		List<CheckOrderBean> checkOrder = new ArrayList<CheckOrderBean>();
		if((startDate != null && endDate != null) || orderNumber == null && custName == null){
			AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			String tmpStartDate = df.format(startDate)+" 00:00:00.000";
			String tmpEndDate = df.format(endDate)+" 23:59:59.997";
			Object[][] params = {{"uuidUser", Long.valueOf(callerId.getCallerId())}, {"startDate", tmpStartDate}, {"endDate", tmpEndDate}};
			if(!(String.valueOf(amMsuser.getAmMsuser().getUuidMsUser())).isEmpty()){
				List order = (List) this.getManagerDAO().selectAllNative("services.order.checkOrder.checkOrder2A", params, null);
				if(order != null){
					Iterator itr = order.iterator();
					while(itr.hasNext()){
						Map co = (Map) itr.next();
						CheckOrderBean coBean = new CheckOrderBean();
						coBean.setSubListResponseServer(this.subListResponseServerBean(((BigInteger) co.get("d2")).longValue(), callerId));
						coBean.setKey((String) co.get("d0"));
						coBean.setValue((String) co.get("d1"));
						checkOrder.add(coBean);
					}
				}
			}
			else{
				List order = (List) this.getManagerDAO().selectAllNative("services.order.checkOrder.checkOrder2B", params, null);
				if(order != null){
					Iterator itr = order.iterator();
					while(itr.hasNext()){
						Map co = (Map) itr.next();
						CheckOrderBean coBean = new CheckOrderBean();
						coBean.setSubListResponseServer(this.subListResponseServerBean(((BigInteger) co.get("d2")).longValue(), callerId));
						coBean.setKey((String) co.get("d0"));
						coBean.setValue((String) co.get("d1"));
						checkOrder.add(coBean);
					}
				}
			}
		}
		else if((startDate == null && endDate == null && custName == null) || orderNumber != null){
			AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
			Object[][] param = {{"uuidUser", Long.valueOf(callerId.getCallerId())}, {"orderNumber", orderNumber}};
			if(!(String.valueOf(amMsuser.getAmMsuser().getUuidMsUser())).isEmpty()){
				List order = (List) this.getManagerDAO().selectAllNative("services.order.checkOrder.checkOrder1A", param, null);				
				if(order != null){
					Iterator itr = order.iterator();
					while(itr.hasNext()){
						Map co = (Map) itr.next();
						CheckOrderBean coBean = new CheckOrderBean();
						coBean.setSubListResponseServer(this.subListResponseServerBean(((BigInteger) co.get("d2")).longValue(), callerId));
						coBean.setKey((String) co.get("d0"));
						coBean.setValue((String) co.get("d1"));
						checkOrder.add(coBean);
					}
				}
			}
			else{
				List order = (List) this.getManagerDAO().selectAllNative("services.order.checkOrder.checkOrder1B", param, null);				
				if(order != null){
					Iterator itr = order.iterator();
					while(itr.hasNext()){
						Map co = (Map) itr.next();
						CheckOrderBean coBean = new CheckOrderBean();
						coBean.setSubListResponseServer(this.subListResponseServerBean(((BigInteger) co.get("d2")).longValue(), callerId));
						coBean.setKey((String) co.get("d0"));
						coBean.setValue((String) co.get("d1"));
						checkOrder.add(coBean);
					}
				}
			}
		}
		else if((startDate == null && endDate == null) || custName != null){
			AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
			Object[][] param = {{"uuidUser", Long.valueOf(callerId.getCallerId())}, {"custName", custName}};
			if(!(String.valueOf(amMsuser.getAmMsuser().getUuidMsUser())).isEmpty()){
				List order = (List) this.getManagerDAO().selectAllNative("services.order.checkOrder.checkOrder3A", param, null);				
				if(order != null){
					Iterator itr = order.iterator();
					while(itr.hasNext()){
						Map co = (Map) itr.next();
						CheckOrderBean coBean = new CheckOrderBean();
						coBean.setSubListResponseServer(this.subListResponseServerBean(((BigInteger) co.get("d2")).longValue(), callerId));
						coBean.setKey((String) co.get("d0"));
						coBean.setValue((String) co.get("d1"));
						checkOrder.add(coBean);
					}
				}
			}
			else{
				List order = (List) this.getManagerDAO().selectAllNative("services.order.checkOrder.checkOrder3B", param, null);				
				if(order != null){
					Iterator itr = order.iterator();
					while(itr.hasNext()){
						Map co = (Map) itr.next();
						CheckOrderBean coBean = new CheckOrderBean();
						coBean.setSubListResponseServer(this.subListResponseServerBean(((BigInteger) co.get("d2")).longValue(), callerId));
						coBean.setKey((String) co.get("d0"));
						coBean.setValue((String) co.get("d1"));
						checkOrder.add(coBean);
					}
				}
			}
		}
		return checkOrder ;
	}
	
	private List<SubListResponseServerBean> subListResponseServerBean(long idTask, AuditContext callerId){
		List<SubListResponseServerBean> subListResponseServer = new ArrayList<SubListResponseServerBean>();
		Object[][] uuid = {{"idTask", idTask}};
		List statusTask = this.getManagerDAO().selectAllNativeString("select convert(varchar(15),tth.DTM_CRT,106)" +
					"+' '+convert(varchar(5),tth.DTM_CRT,14) as DATE, mst.STATUS_TASK_DESC as description from TR_TASKHISTORY tth with (nolock)"
					+ " inner join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK"
					+ " where UUID_TASK_H = :idTask order by DATE asc", uuid);
		if(statusTask != null){
			Iterator itr = statusTask.iterator();
			while(itr.hasNext()){
				SubListResponseServerBean bean = new SubListResponseServerBean();
				Map mp = (Map) itr.next();
				String value = (String)mp.get("d1") + " - " + (String)mp.get("d0");
				bean.setValue(value);
				subListResponseServer.add(bean);
			}
		}
		return subListResponseServer;
	}
}
