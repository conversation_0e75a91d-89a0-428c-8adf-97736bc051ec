package com.adins.mss.businesslogic.impl.order;

import java.sql.Blob;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.exception.DatabaseException;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.order.ContentNewsLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.services.model.order.ContentNewsDetail;
import com.adins.mss.services.model.order.ContentNewsHeader;
import com.google.common.io.BaseEncoding;

@SuppressWarnings({ "unchecked", "rawtypes" })
public class GenericContentNewsLogic extends BaseLogic implements ContentNewsLogic  {
	private static final Logger LOG = LoggerFactory.getLogger(GenericContentNewsLogic.class);
	@Transactional(readOnly=true,isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List contentNewsHeader (AuditContext callerId, ContentNewsHeader[] listContentHeader){
		List<ContentNewsDetail> listHeader = new ArrayList<ContentNewsDetail>();
		Object[][] param = {{Restrictions.eq("gsCode",GlobalKey.GENERALSETTING_FLAG_CMS)}};
		AmGeneralsetting flagCMS = (AmGeneralsetting) this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
		
		if(listContentHeader==null){
				long uuid = Long.valueOf(callerId.getCallerId());
				Object[][] id = {{"uuid", uuid}};
				AmMsuser user = this.getManagerDAO().selectOne("from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", uuid}});		
				
				String queryName = "services.order.contentNews.getHeader";
				if("0".equals(user.getMsJob().getIsBranch())){
					queryName = "services.order.contentNews.getHeaderDealer";
				}				
				if("0".equals(flagCMS.getGsValue())){
					queryName = "services.order.contentNews.getHeaderByDealer";
					if("1".equals(user.getMsJob().getIsBranch())){
						queryName = "services.order.contentNews.getHeaderDealerBranch";
					}
				}
				
				List list = this.getManagerDAO().selectAllNative(queryName, id, null);
					
				Iterator itr = list.iterator();
				while(itr.hasNext()){
					ContentNewsDetail header = new ContentNewsDetail();
					Map mp = (Map) itr.next();
					header.setUuidStkPromo(mp.get("d0").toString());
					header.setPromoName((String) mp.get("d1"));
					header.setPromoShortDesc((String) mp.get("d2"));
					header.setStarteffDate((String) mp.get("d3"));
					header.setEndeffDate((String) mp.get("d4"));
					header.setParentPromoId((mp.get("d5") == null) ? null : mp.get("d5").toString());
					
					listHeader.add(header);						
				}
		}
		else if(listContentHeader!=null){
				long uuid = Long.valueOf(callerId.getCallerId());
				AmMsuser user = this.getManagerDAO().selectOne("from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", uuid}});
				//get  listHeader
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
				for(int i = 0; i < listContentHeader.length; i++){
					String uuidStkPromo2 = listContentHeader[i].getUuidStkPromo2();
					Date dtmUpd = listContentHeader[i].getDtmUpd();
					String tmpDtmUpd = df.format(dtmUpd)+" 00:00:00.000";;
					String queryName = "services.order.contentNews.getHeaderUp";
					if("0".equals(user.getMsJob().getIsBranch())){
						queryName = "services.order.contentNews.getHeaderUpDealer";
					}
					if("0".equals(flagCMS.getGsValue())){
						queryName = "services.order.contentNews.getHeaderUpByDealer";
						if("1".equals(user.getMsJob().getIsBranch())){
							queryName = "services.order.contentNews.getHeaderUpByDealerBranch";
						}
					}
					Object[][] params = {{"uuid_mobile_content_h", uuidStkPromo2}, {"last_update", tmpDtmUpd}, {"uuid", uuid}};
					List head = this.getManagerDAO().selectAllNative(queryName, params, null);					
					
					Iterator reput = head.iterator();
					while(reput.hasNext()){
						ContentNewsDetail header = new ContentNewsDetail();
						Map mp = (Map) reput.next();
						header.setUuidStkPromo(mp.get("d0").toString());
						header.setPromoName((String) mp.get("d1"));
						header.setPromoShortDesc((String) mp.get("d2"));
						header.setStarteffDate((String) mp.get("d3"));
						header.setEndeffDate((String) mp.get("d4"));
						header.setParentPromoId((mp.get("d5") == null) ? null : mp.get("d5").toString());
						
						listHeader.add(header);
					}					
				}
		}
		return listHeader;
	}
	
	@Transactional(readOnly=true,isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List contentNewsDetail(String uuid_mobile_content_h, AuditContext callerId){			
		long uuidMobileContentH = Long.valueOf(uuid_mobile_content_h);
		List<ContentNewsDetail> listDetail = new ArrayList<ContentNewsDetail>();
		AmMsuser user = this.getManagerDAO().selectOne("from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", NumberUtils.toLong(callerId.getCallerId())}});
		Object[][] param = {{Restrictions.eq("gsCode",GlobalKey.GENERALSETTING_FLAG_CMS)}};
		AmGeneralsetting flagCMS = (AmGeneralsetting) this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
		String queryName = "services.order.contentNews.filter";
		if("0".equals(user.getMsJob().getIsBranch())){
			queryName = "services.order.contentNews.filterDealer";
		}
		if("0".equals(flagCMS.getGsValue())){
			queryName = "services.order.contentNews.filterByDealer";
			if("1".equals(user.getMsJob().getIsBranch())){
				queryName = "services.order.contentNews.filterByDealerBranch";
			}
		}
		Object[][] idUser = {{"uuid", NumberUtils.toLong(callerId.getCallerId())}, {"uuid_mobile_content_h", uuidMobileContentH}};
		Object check =  this.getManagerDAO().selectOneNative(queryName, idUser) ;
		
		if(check != null){
			List<ContentNewsDetail> p = detailP(uuidMobileContentH, callerId, String.valueOf(check));
			if(p != null){
				listDetail.addAll(p);
			}
		}
		else if(check == null){
			List<ContentNewsDetail> np = detailNP(uuidMobileContentH, callerId);
			if(np!=null){
				listDetail.addAll(np);
			}
		}
		return listDetail;
	}
	
	private List<ContentNewsDetail> detailP(long uuid_mobile_content_h, AuditContext callerId, String check){
		List<ContentNewsDetail> list = null;
		Object[][] mobileH = {{"uuid_mobile_content_h", uuid_mobile_content_h}, {"parent", check}};
		List p = this.getManagerDAO().selectAllNative("services.order.contentNews.getDetailContentP", mobileH, null);
		Iterator itp = p.iterator();
		while (itp.hasNext()) {
			if (list == null) {
				list = new ArrayList<>();
			}
			ContentNewsDetail detail = new ContentNewsDetail();
			Map mp = (Map) itp.next();
			detail.setUuidStkPromoContent(mp.get("d0").toString());
			detail.setIsActive((String) mp.get("d1"));
			byte[] image = null;
			try {
				Blob blob = (Blob) mp.get("d2");
				if (blob != null) {
					image = blob.getBytes(1, (int) blob.length());
					detail.setContent(new String(BaseEncoding.base64().encode(image)));
				}
			}
			catch (SQLException e) {
				LOG.error(e.getMessage(),e);
				throw new DatabaseException(DatabaseException.Reason.OTHER_DATA_ACCESS_EXCEPTION, e);
			}
			detail.setContentType((String) mp.get("d3"));
			detail.setSequence((Short) mp.get("d4"));
			detail.setUsrCrt((String) mp.get("d5"));
			detail.setDtmCrt((Date) mp.get("d6"));
			detail.setUsrUpd((String) mp.get("d7"));
			detail.setStarteffDate((String) mp.get("d8"));
			detail.setEndeffDate((String) mp.get("d9"));
			detail.setDtmUpd((Date) mp.get("d10"));
			list.add(detail);
		}			
		return list;
	}
	
	private List detailNP(long uuid_mobile_content_h, AuditContext callerId){
		List<ContentNewsDetail> list = null;
		Object[][] mobileH = {{"uuid_mobile_content_h", uuid_mobile_content_h}};
		List np = this.getManagerDAO().selectAllNative("services.order.contentNews.getDetailContentNP", mobileH, null);
		Iterator itr = np.iterator();
		while (itr.hasNext()) {
			if (list == null) {
				list = new ArrayList<ContentNewsDetail>();
			}
			ContentNewsDetail detail = new ContentNewsDetail();
			Map mp = (Map) itr.next();
			detail.setUuidStkPromoContent(mp.get("d0").toString());
			detail.setIsActive((String) mp.get("d1"));
			byte[] image = null;
			try {
				Blob blob = (Blob) mp.get("d2");
				if (blob != null) {
					image = blob.getBytes(1, (int)blob.length());
		            detail.setContent(new String(BaseEncoding.base64().encode(image)));
				}
			} 
			catch (SQLException e) {
				LOG.error(e.getMessage(),e);
				throw new DatabaseException(DatabaseException.Reason.OTHER_DATA_ACCESS_EXCEPTION, e);
			}
			detail.setContentType((String) mp.get("d3"));
			detail.setSequence((Short) mp.get("d4"));
			detail.setUsrCrt((String) mp.get("d5"));
			detail.setDtmCrt((Date) mp.get("d6"));
			detail.setUsrUpd((String) mp.get("d7"));
			detail.setStarteffDate((String) mp.get("d8"));
			detail.setEndeffDate((String) mp.get("d9"));
			detail.setDtmUpd((Date) mp.get("d10"));
				
			list.add(detail);
		}
		return list;
	}
}
