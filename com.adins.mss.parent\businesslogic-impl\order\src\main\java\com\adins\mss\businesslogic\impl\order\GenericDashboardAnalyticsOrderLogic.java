package com.adins.mss.businesslogic.impl.order;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.order.DashboardAnalyticsOrderLogic;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.services.model.common.DataAnalyticResponse;


@SuppressWarnings({ "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericDashboardAnalyticsOrderLogic extends BaseLogic implements
		DashboardAnalyticsOrderLogic {

	@Override
	public List getPerformanceMTD(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }, { "uuidSubsystem", uuidSubsystem } };
		List result = this.getManagerDAO().selectAllNative(
				"report.dashboardanalyticsorder.getPerformanceMTD", params, null);
		return result;
	}
	
	@Override
	public List getPerformanceToday(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }, { "uuidSubsystem", uuidSubsystem } };
		List result = this.getManagerDAO().selectAllNative(
				"report.dashboardanalyticsorder.getPerformanceToday", params, null);
		return result;
	}
	
	@Override
	public List getDealerStatusMTD(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }, { "uuidSubsystem", uuidSubsystem } };
		List result = this.getManagerDAO().selectAllNative(
				"report.dashboardanalyticsorder.getDealerStatusMTD", params, null);
		return result;
	}
	
	@Override
	public List getDealerStatusToday(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }, { "uuidSubsystem", uuidSubsystem } };
		List result = this.getManagerDAO().selectAllNative(
				"report.dashboardanalyticsorder.getDealerStatusToday", params, null);
		return result;
	}
	
	@Override
	public List getTodayStatus(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }, { "uuidSubsystem", uuidSubsystem } };
		List result = this.getManagerDAO().selectAllNative(
				"report.dashboardanalyticsorder.getTodayStatus", params, null);
		return result;
	}
	
	@Override
	public String getAutoupdateInterval(AuditContext callerId){
		Object params[][] = {{Restrictions.eq("gsCode", "MO_PRM22_DAN")}};
		AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		return result.getGsValue();
	}

	@Override
	public List getDataAnalyticMobile(String task, String diagram, AuditContext callerId) {
		final String SUBSYSTEM = "MO";
		final String TASK_ALLMTD = "All MTD", TASK_ALLTODAY = "All Today", TASK_TODAY = "Today", TASK_MTD = "MTD";
		final String DIAG_PERFORMANCE = "performance", DIAG_DEALERSTATUS = "dealerStatus", DIAG_TODAYSTATUS = "todayStatus";
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(callerId.getCallerId()));
		long uuidBranch = user.getMsBranch().getUuidBranch();
		long uuidSubsystem = user.getAmMssubsystem().getUuidMsSubsystem();
		List<DataAnalyticResponse> listResult = new ArrayList<DataAnalyticResponse>();
		if(TASK_ALLMTD.equalsIgnoreCase(task) || TASK_ALLTODAY.equalsIgnoreCase(task) 
				|| TASK_TODAY.equalsIgnoreCase(task)){
			if((TASK_TODAY.equalsIgnoreCase(task) && DIAG_PERFORMANCE.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List performanceToday = this.getPerformanceToday(uuidBranch, uuidSubsystem, callerId);
				for (int i = 0; i < performanceToday.size(); i++) {
					Map mapResult = (Map) performanceToday.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_PERFORMANCE);
					bean.setSubsystem(SUBSYSTEM);
					bean.setStatusTask(String.valueOf(mapResult.get("d0")));
					bean.setJumlah(String.valueOf(mapResult.get("d1")));
					bean.setDayDate(String.valueOf(mapResult.get("d2")));
					listResult.add(bean);
				}
			}
			if((TASK_TODAY.equalsIgnoreCase(task) && DIAG_DEALERSTATUS.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List dealerStatusToday = this.getDealerStatusToday(uuidBranch, uuidSubsystem, callerId);
				for (int i = 0; i < dealerStatusToday.size(); i++) {
					Map mapResult = (Map) dealerStatusToday.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_DEALERSTATUS);
					bean.setSubsystem(SUBSYSTEM);
					bean.setDealerName(String.valueOf(mapResult.get("d0")));
					bean.setAssignIn(String.valueOf(mapResult.get("d1")));
					bean.setGoLive(String.valueOf(mapResult.get("d2")));
					listResult.add(bean);
				}
			}
			if((TASK_TODAY.equalsIgnoreCase(task) && DIAG_TODAYSTATUS.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List todayStatus = this.getTodayStatus(uuidBranch, uuidSubsystem, callerId);
				for (int i = 0; i < todayStatus.size(); i++) {
					Map mapResult = (Map) todayStatus.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_TODAYSTATUS);
					bean.setSubsystem(SUBSYSTEM);
					bean.setStatusLabel(String.valueOf(mapResult.get("d0")));
					bean.setDesc(String.valueOf(mapResult.get("d1")));
					bean.setColor(String.valueOf(mapResult.get("d2")));
					bean.setJumlah(String.valueOf(mapResult.get("d3")));
					listResult.add(bean);
				}
			}
			if(TASK_ALLMTD.equalsIgnoreCase(task)){
				List performanceMTD = this.getPerformanceMTD(uuidBranch, uuidSubsystem, callerId);
				for (int i = 0; i < performanceMTD.size(); i++) {
					Map mapResult = (Map) performanceMTD.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_MTD);
					bean.setDiagram(DIAG_PERFORMANCE);
					bean.setSubsystem(SUBSYSTEM);
					bean.setStatusTask(String.valueOf(mapResult.get("d0")));
					bean.setJumlah(String.valueOf(mapResult.get("d1")));
					bean.setDayDate(String.valueOf(mapResult.get("d2")));
					listResult.add(bean);
				}
				List dealerStatusMTD = this.getDealerStatusMTD(uuidBranch, uuidSubsystem, callerId);
				for (int i = 0; i < dealerStatusMTD.size(); i++) {
					Map mapResult = (Map) dealerStatusMTD.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_MTD);
					bean.setDiagram(DIAG_DEALERSTATUS);
					bean.setSubsystem(SUBSYSTEM);
					bean.setDealerName(String.valueOf(mapResult.get("d0")));
					bean.setAssignIn(String.valueOf(mapResult.get("d1")));
					bean.setGoLive(String.valueOf(mapResult.get("d2")));
					listResult.add(bean);
				}
			}
		}
		return listResult;
	}
	
}
