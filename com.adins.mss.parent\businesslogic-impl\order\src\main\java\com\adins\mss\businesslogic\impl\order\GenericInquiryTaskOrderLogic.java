package com.adins.mss.businesslogic.impl.order;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.order.InquiryTaskOrderLogic;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.FinalTrTaskH;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.custom.InquiryTaskOrderFinalBean;
import com.adins.mss.model.custom.TrTaskBean;
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
@SuppressWarnings({ "rawtypes", "unchecked" })
public class GenericInquiryTaskOrderLogic extends BaseLogic implements
		InquiryTaskOrderLogic {

	@Override
	public List listInquiryTaskOrderNativeString(Object params,
			String isBranch, AuditContext callerId) {
		List resultList = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();

		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
		StringBuilder ordersQueryString = this.sqlPagingOrderBuilder((String) ((Object[][]) params)[15][1]);
		
			StringBuilder queryBuilder =null;
			Object[][] sqlParams= null;
			if ("1".equals(isBranch)) {
				queryBuilder = new StringBuilder()
						.append("SELECT * from (")
						.append(" SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (")
						.append(" SELECT b.UUID_TASK_H, b.CUSTOMER_NAME, b.ORDER_NO, b.DEALER_NAME, b.SALES, b.BRANCH_NAME, b.SUBMIT_DATE,")
						.append(" b.ASSIGN_DATE, b.SURVEYOR_NAME, b.STATUS_TASK_DESC, b.FLAG, :odr as flagOrder,")
						.append(" ROW_NUMBER() OVER (")
						.append(ordersQueryString)
						.append(" ) as rownum FROM (")
						.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
						.append(" isnull(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,")
						.append(" isnull(trtod.ORDER_NO,'-') ORDER_NO,")
						.append(" isnull(msd.DEALER_NAME,'-') DEALER_NAME,")
						.append(" isnull(ammsu.FULL_NAME,'-') SALES,")
						.append(" isnull(msb.BRANCH_NAME,'-') BRANCH_NAME,")
						.append(" isnull(LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17),'-') as SUBMIT_DATE,")
						.append(" isnull(LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17),'-') as ASSIGN_DATE,")
						.append(" isnull(ammsu2.FULL_NAME,'-') as SURVEYOR_NAME,")
						.append(" isnull(msst.STATUS_TASK_DESC,'-') STATUS_TASK_DESC,")
						.append(" msb.UUID_BRANCH as UUID_BRANCH,")
						.append(" msd.UUID_DEALER as UUID_DEALER,")
						.append(" msst.STATUS_CODE as STATUS_CODE,")
						.append(" msst.IS_ACTIVE as IS_ACTIVE,")
						.append(" '1' as FLAG,")
						.append(" :odr as FlagOdr,")
						.append(" trth.SUBMIT_DATE as SUBMIT_DT,")
						.append(" trtod.DTM_CRT as ASSIGN_DT")
						.append(" FROM TR_TASK_H trth ")
						.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
						.append(" left join TR_TASKORDERDATA trtod on trth.UUID_TASK_H = trtod.UUID_TASK_ID")
						.append(" left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchDealer)) msb")
						.append(" on trth.UUID_BRANCH = msb.UUID_BRANCH")
						.append(" left join MS_DEALER msd on trtod.DEALER_ID = msd.UUID_DEALER")
						.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem")
						.append(" left join AM_MSUSER ammsu2 on ammsu2.UUID_MS_USER = trtod.CA_ID")
						.append(" WHERE msst.IS_ACTIVE = '1'")
						.append(paramsQueryString)
						.append(" UNION ALL")
						.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
						.append(" isnull(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,")
						.append(" isnull(trtod.ORDER_NO,'-') ORDER_NO,")
						.append(" isnull(msd.DEALER_NAME,'-') DEALER_NAME,")
						.append(" isnull(ammsu.FULL_NAME,'-') SALES,")
						.append(" isnull(msb.BRANCH_NAME,'-') BRANCH_NAME,")
						.append(" isnull(LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17),'-') as SUBMIT_DATE,")
						.append(" isnull(LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17),'-') as ASSIGN_DATE,")
						.append(" isnull(ammsu2.FULL_NAME,'-') as SURVEYOR_NAME,")
						.append(" isnull(msst.STATUS_TASK_DESC,'-') STATUS_TASK_DESC,")
						.append(" msb.UUID_BRANCH as UUID_BRANCH,")
						.append(" msd.UUID_DEALER as UUID_DEALER,")
						.append(" msst.STATUS_CODE as STATUS_CODE,")
						.append(" msst.IS_ACTIVE as IS_ACTIVE,")
						.append(" '2' as FLAG,")
						.append(" :odr as FlagOdr,")
						.append(" trth.SUBMIT_DATE as SUBMIT_DT,")
						.append(" trtod.DTM_CRT as ASSIGN_DT")
						.append(" FROM FINAL_TR_TASK_H trth ")
						.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
						.append(" left join FINAL_TR_TASKORDERDATA trtod on trth.UUID_TASK_H = trtod.UUID_TASK_ID")
						.append(" left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchDealer)) msb on")
						.append(" trth.UUID_BRANCH = msb.UUID_BRANCH")
						.append(" left join MS_DEALER msd on trtod.DEALER_ID = msd.UUID_DEALER")
						.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem")
						.append(" left join AM_MSUSER ammsu2 on ammsu2.UUID_MS_USER = trtod.CA_ID")
						.append(" WHERE msst.IS_ACTIVE = '1'")
						.append(paramsQueryString)
						.append(" ) b ");
			} 
			else {
				queryBuilder = new StringBuilder()
						.append("SELECT * from (")
						.append(" SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
						.append(" SELECT b.UUID_TASK_H, b.CUSTOMER_NAME, b.ORDER_NO, b.DEALER_NAME, b.SALES, b.BRANCH_NAME,")
						.append(" b.SUBMIT_DATE, b.ASSIGN_DATE, b.SURVEYOR_NAME, b.STATUS_TASK_DESC, b.FLAG, :odr as flagOrder,")
						.append(" ROW_NUMBER() OVER (")
						.append(ordersQueryString)
						.append(" ) as rownum FROM ( ")
						.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
						.append(" trth.CUSTOMER_NAME as CUSTOMER_NAME,")
						.append(" trtod.ORDER_NO as ORDER_NO,")
						.append(" msd.DEALER_NAME as DEALER_NAME,")
						.append(" ammsu.FULL_NAME as SALES,")
						.append(" msb.BRANCH_NAME as BRANCH_NAME,")
						.append(" LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as SUBMIT_DATE,")
						.append(" LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as ASSIGN_DATE,")
						.append(" ammsu2.FULL_NAME as SURVEYOR_NAME,")
						.append(" msst.STATUS_TASK_DESC as STATUS_TASK_DESC,")
						.append(" msb.UUID_BRANCH as UUID_BRANCH,")
						.append(" msd.UUID_DEALER as UUID_DEALER,")
						.append(" trth.UUID_MS_USER as UUID_MS_USER,")
						.append(" msst.STATUS_CODE as STATUS_CODE,")
						.append(" msst.IS_ACTIVE as IS_ACTIVE,")
						.append(" '1' as FLAG,")
						.append(" :odr as FlagOdr,")
						.append(" trth.SUBMIT_DATE as SUBMIT_DT,")
						.append(" trtod.DTM_CRT as ASSIGN_DT")
						.append(" FROM TR_TASK_H trth ")
						.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
						.append(" left join TR_TASKORDERDATA trtod on trth.UUID_TASK_H = trtod.UUID_TASK_ID")
						.append(" left join MS_BRANCH msb on trth.UUID_BRANCH = msb.UUID_BRANCH")
						.append(" left join MS_DEALER msd on trtod.DEALER_ID = msd.UUID_DEALER")
						.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER")
						.append(" and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem")
						.append(" left join AM_MSUSER ammsu2 on ammsu2.UUID_MS_USER = trtod.CA_ID")
						.append(" join (select keyValue from dbo.getUserByLogin(:uuidBranchDealer)) hrkUser on")
						.append(" hrkUser.keyValue = ammsu.UUID_MS_USER")
						.append(" WHERE msst.IS_ACTIVE='1'")
						.append(paramsQueryString)
						.append(" UNION ALL")
						.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
						.append(" trth.CUSTOMER_NAME as CUSTOMER_NAME,")
						.append(" trtod.ORDER_NO as ORDER_NO,")
						.append(" msd.DEALER_NAME as DEALER_NAME,")
						.append(" ammsu.FULL_NAME as SALES,")
						.append(" msb.BRANCH_NAME as BRANCH_NAME,")
						.append(" LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as SUBMIT_DATE,")
						.append(" LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as ASSIGN_DATE,")
						.append(" ammsu2.FULL_NAME as SURVEYOR_NAME,")
						.append(" msst.STATUS_TASK_DESC as STATUS_TASK_DESC,")
						.append(" msb.UUID_BRANCH as UUID_BRANCH,")
						.append(" msd.UUID_DEALER as UUID_DEALER,")
						.append(" trth.UUID_MS_USER as UUID_MS_USER,")
						.append(" msst.STATUS_CODE as STATUS_CODE,")
						.append(" msst.IS_ACTIVE as IS_ACTIVE,")
						.append(" '2' as FLAG,")
						.append(" :odr as FlagOdr,")
						.append(" trth.SUBMIT_DATE as SUBMIT_DT,")
						.append(" trtod.DTM_CRT as ASSIGN_DT")
						.append(" FROM FINAL_TR_TASK_H trth")
						.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
						.append(" left join TR_TASKORDERDATA trtod on trth.UUID_TASK_H = trtod.UUID_TASK_ID")
						.append(" left join MS_BRANCH msb on trth.UUID_BRANCH = msb.UUID_BRANCH")
						.append(" left join MS_DEALER msd on trtod.DEALER_ID = msd.UUID_DEALER")
						.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER")
						.append(" and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem")
						.append(" left join AM_MSUSER ammsu2 on ammsu2.UUID_MS_USER = trtod.CA_ID")
						.append(" join (select keyValue from dbo.getUserByLogin(:uuidBranchDealer)) hrkUser on")
						.append(" hrkUser.keyValue = ammsu.UUID_MS_USER")
						.append(" WHERE msst.IS_ACTIVE = '1'")
						.append(paramsQueryString)
						.append(" ) b ");
			}
			
			queryBuilder.append(") a WHERE a.rownum <= :end");
			queryBuilder.append(") b WHERE b.recnum >= :start");
			paramsStack.push(new Object[] { "odr",
					(String) ((Object[][]) params)[15][1] });
			paramsStack.push(new Object[] { "start",
					((Object[][]) params)[13][1] });
			paramsStack.push(new Object[] { "end",
					((Object[][]) params)[14][1] });

			sqlParams = new Object[paramsStack.size()][2];
			for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
			resultList = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		return resultList;
	}

	@Override
	public Integer countListInquiryTaskOrderNativeString(Object params,
			String isBranch, AuditContext callerId) {
		Integer result = NumberUtils.INTEGER_ZERO;
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
			StringBuilder queryBuilder = null;
			Object[][] sqlParams = null;
			if ("1".equals(isBranch)) {
				queryBuilder = new StringBuilder()
				.append("SELECT COUNT(1)")
				.append(" FROM (")
				.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
				.append(" isnull(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,")
				.append(" isnull(trtod.ORDER_NO,'-') ORDER_NO, ")
				.append(" isnull(msd.DEALER_NAME,'-') DEALER_NAME,")
				.append(" isnull(ammsu.FULL_NAME,'-') SALES,")
				.append(" isnull(msb.BRANCH_NAME,'-') BRANCH_NAME,")
				.append(" isnull(LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17),'-') as SUBMIT_DATE,")
				.append(" isnull(LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17),'-') as ASSIGN_DATE,")
				.append(" isnull(ammsu2.FULL_NAME,'-') as SURVEYOR_NAME,")
				.append(" isnull(msst.STATUS_TASK_DESC,'-') STATUS_TASK_DESC,")
				.append(" msb.UUID_BRANCH as UUID_BRANCH,")
				.append(" msd.UUID_DEALER as UUID_DEALER,")
				.append(" msst.STATUS_CODE as STATUS_CODE,")
				.append(" msst.IS_ACTIVE as IS_ACTIVE,")
				.append(" '1' as FLAG,")
				.append(" trth.SUBMIT_DATE as SUBMIT_DT,")
				.append(" trtod.DTM_CRT as ASSIGN_DT")
				.append(" FROM TR_TASK_H trth")
				.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
				.append(" left join TR_TASKORDERDATA trtod on trth.UUID_TASK_H = trtod.UUID_TASK_ID")
				.append(" left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchDealer)) msb")
				.append(" on trth.UUID_BRANCH = msb.UUID_BRANCH")
				.append(" left join MS_DEALER msd on trtod.DEALER_ID = msd.UUID_DEALER")
				.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem")
				.append(" left join AM_MSUSER ammsu2 on ammsu2.UUID_MS_USER = trtod.CA_ID")
				.append(" WHERE msst.IS_ACTIVE = '1'")
				.append(paramsQueryString)
				.append(" UNION ALL")
				.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
				.append(" isnull(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME,")
				.append(" isnull(trtod.ORDER_NO,'-') ORDER_NO,")
				.append(" isnull(msd.DEALER_NAME,'-') DEALER_NAME,")
				.append(" isnull(ammsu.FULL_NAME,'-') SALES,")
				.append(" isnull(msb.BRANCH_NAME,'-') BRANCH_NAME,")
				.append(" isnull(LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17),'-') as SUBMIT_DATE,")
				.append(" isnull(LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17),'-') as ASSIGN_DATE,")
				.append(" isnull(ammsu2.FULL_NAME,'-') as SURVEYOR_NAME,")
				.append(" isnull(msst.STATUS_TASK_DESC,'-') STATUS_TASK_DESC,")
				.append(" msb.UUID_BRANCH as UUID_BRANCH,")
				.append(" msd.UUID_DEALER as UUID_DEALER,")
				.append(" msst.STATUS_CODE as STATUS_CODE,")
				.append(" msst.IS_ACTIVE as IS_ACTIVE,")
				.append(" '2' as FLAG,")
				.append(" trth.SUBMIT_DATE as SUBMIT_DT,")
				.append(" trtod.DTM_CRT as ASSIGN_DT")
				.append(" FROM FINAL_TR_TASK_H trth")
				.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
				.append(" left join FINAL_TR_TASKORDERDATA trtod on trth.UUID_TASK_H = trtod.UUID_TASK_ID")
				.append(" left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchDealer)) msb on")
				.append(" trth.UUID_BRANCH = msb.UUID_BRANCH")
				.append(" left join MS_DEALER msd on trtod.DEALER_ID = msd.UUID_DEALER")
				.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem")
				.append(" left join AM_MSUSER ammsu2 on ammsu2.UUID_MS_USER = trtod.CA_ID")
				.append(" WHERE msst.IS_ACTIVE = '1'")
				.append(paramsQueryString)
				.append(" ) b ");
			} 
			else {
				queryBuilder = new StringBuilder()
				.append("SELECT COUNT(1) FROM (")
				.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
				.append(" trth.CUSTOMER_NAME as CUSTOMER_NAME,")
				.append(" trtod.ORDER_NO as ORDER_NO,")
				.append(" msd.DEALER_NAME as DEALER_NAME,")
				.append(" ammsu.FULL_NAME as SALES,")
				.append(" msb.BRANCH_NAME as BRANCH_NAME,")
				.append(" LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as SUBMIT_DATE,")
				.append(" LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as ASSIGN_DATE,")
				.append(" ammsu2.FULL_NAME as SURVEYOR_NAME,")
				.append(" msst.STATUS_TASK_DESC as STATUS_TASK_DESC,")
				.append(" msb.UUID_BRANCH as UUID_BRANCH,")
				.append(" msd.UUID_DEALER as UUID_DEALER,")
				.append(" trth.UUID_MS_USER as UUID_MS_USER,")
				.append(" msst.STATUS_CODE as STATUS_CODE,")
				.append(" msst.IS_ACTIVE as IS_ACTIVE,")
				.append(" '1' as FLAG,")
				.append(" trth.SUBMIT_DATE as SUBMIT_DT,")
				.append(" trtod.DTM_CRT as ASSIGN_DT")
				.append(" FROM TR_TASK_H trth ")
				.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
				.append(" left join TR_TASKORDERDATA trtod on trth.UUID_TASK_H = trtod.UUID_TASK_ID")
				.append(" left join MS_BRANCH msb on trth.UUID_BRANCH = msb.UUID_BRANCH")
				.append(" left join MS_DEALER msd on trtod.DEALER_ID = msd.UUID_DEALER")
				.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER")
				.append(" and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem")
				.append(" left join AM_MSUSER ammsu2 on ammsu2.UUID_MS_USER = trtod.CA_ID")
				.append(" join (select keyValue from dbo.getUserByLogin(:uuidBranchDealer)) hrkUser on")
				.append(" hrkUser.keyValue = ammsu.UUID_MS_USER")
				.append(" WHERE msst.IS_ACTIVE = '1'")
				.append(paramsQueryString)
				.append(" UNION ALL")
				.append(" SELECT trth.UUID_TASK_H as UUID_TASK_H,")
				.append(" trth.CUSTOMER_NAME as CUSTOMER_NAME,")
				.append(" trtod.ORDER_NO as ORDER_NO,")
				.append(" msd.DEALER_NAME as DEALER_NAME,")
				.append(" ammsu.FULL_NAME as SALES,")
				.append(" msb.BRANCH_NAME as BRANCH_NAME,")
				.append(" LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as SUBMIT_DATE,")
				.append(" LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as ASSIGN_DATE,")
				.append(" ammsu2.FULL_NAME as SURVEYOR_NAME,")
				.append(" msst.STATUS_TASK_DESC as STATUS_TASK_DESC,")
				.append(" msb.UUID_BRANCH as UUID_BRANCH,")
				.append(" msd.UUID_DEALER as UUID_DEALER,")
				.append(" trth.UUID_MS_USER as UUID_MS_USER,")
				.append(" msst.STATUS_CODE as STATUS_CODE,")
				.append(" msst.IS_ACTIVE as IS_ACTIVE,")
				.append(" '2' as FLAG,")
				.append(" trth.SUBMIT_DATE as SUBMIT_DT,")
				.append(" trtod.DTM_CRT as ASSIGN_DT")
				.append(" FROM FINAL_TR_TASK_H trth ")
				.append(" left join MS_STATUSTASK msst on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK")
				.append(" left join TR_TASKORDERDATA trtod on trth.UUID_TASK_H = trtod.UUID_TASK_ID")
				.append(" left join MS_BRANCH msb on trth.UUID_BRANCH = msb.UUID_BRANCH")
				.append(" left join MS_DEALER msd on trtod.DEALER_ID = msd.UUID_DEALER")
				.append(" left join AM_MSUSER ammsu on ammsu.UUID_MS_USER = trth.UUID_MS_USER")
				.append(" and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem")
				.append(" left join AM_MSUSER ammsu2 on ammsu2.UUID_MS_USER = trtod.CA_ID")
				.append(" join (select keyValue from dbo.getUserByLogin(:uuidBranchDealer)) hrkUser on")
				.append(" hrkUser.keyValue = ammsu.UUID_MS_USER")
				.append(" WHERE msst.IS_ACTIVE = '1'")
				.append(paramsQueryString)
				.append(" ) b ");
			}
			sqlParams = new Object[paramsStack.size()][2];
		    for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
			result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		
		return result;
	}
	
	/*
	 * 0 uuidBranch 1 uuidDealer 2 customerName 3 orderNo 4 salesName
	 * 5 orderDateStart 6 orderDateEnd 7 surveyDateStart 8 surveyDateEnd 9
	 * uuidSubsystem 10 uuidBranchDealer 11 currentDate 14 odr (12) 15
	 * sendDateStart (13) 16 sendDateEnd (14) 17 uuidUser (15) 12 start (16) 13
	 * end (17)
	 */
	private StringBuilder sqlPagingBuilder(Object[][] params,Stack<Object[]> paramStack) {
		if (params == null){
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();

		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
		DateTime currentDate = formatter.parseDateTime((String) params[11][1]);
		currentDate = currentDate.minusMillis(3);
		// ---SUBMIT_DATE
		DateTime orderDateStart = null, orderDateEnd = null;
		if (params.length > 5&& !StringUtils.equals("%", (String) params[5][1])) {
			orderDateStart = formatter.parseDateTime((String) params[5][1]);
			orderDateStart = orderDateStart.withMillisOfDay(0);
		}
		if (params.length > 5&& !StringUtils.equals("%", (String) params[6][1])) {
			orderDateEnd = formatter.parseDateTime((String) params[6][1]);
			orderDateEnd = orderDateEnd.minusMillis(3);
		}
		
		if (orderDateStart != null && orderDateEnd != null) {
			sb.append(" AND COALESCE(trth.SUBMIT_DATE, '1990-01-01 00:00:00') BETWEEN :orderDateStart AND :orderDateEnd");
			paramStack.push(new Object[] { "orderDateStart",orderDateStart.toDate() });
			paramStack.push(new Object[] { "orderDateEnd",orderDateEnd.toDate() });
		} 
		else if (orderDateStart == null && orderDateEnd != null) {
			sb.append(" AND COALESCE(trth.SUBMIT_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :orderDateEnd");
			paramStack.push(new Object[] { "orderDateEnd",orderDateEnd.toDate() });
		} 
		else if (orderDateStart != null && orderDateEnd == null) {
			sb.append(" AND COALESCE(trth.SUBMIT_DATE, '1990-01-01 00:00:00') BETWEEN :orderDateStart AND :currentDate");
			paramStack.push(new Object[] { "orderDateStart",orderDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
		else if(orderDateStart == null && orderDateEnd == null){
			sb.append(" AND COALESCE(trth.SUBMIT_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate");
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		}

		// ---SURVEY_DATE
		DateTime surveyDateStart = null, surveyDateEnd = null;
		if (params.length > 7&& !StringUtils.equals("%", (String) params[7][1])) {
			surveyDateStart = formatter.parseDateTime((String) params[7][1]);
			surveyDateStart = surveyDateStart.withMillisOfDay(0);
		}
		if (params.length > 7&& !StringUtils.equals("%", (String) params[8][1])) {
			surveyDateEnd = formatter.parseDateTime((String) params[8][1]);
			surveyDateEnd = surveyDateEnd.plusDays(1).minusMillis(3);
		}
		if (surveyDateStart != null && surveyDateEnd != null) {
			sb.append(" AND COALESCE(trtod.DTM_CRT, '1990-01-01 00:00:00') BETWEEN :surveyDateStart AND :surveyDateEnd");
			paramStack.push(new Object[] { "surveyDateStart",surveyDateStart.toDate() });
			paramStack.push(new Object[] { "surveyDateEnd",surveyDateEnd.toDate() });
		} 
		else if (surveyDateStart == null && surveyDateEnd != null) {
			sb.append(" AND COALESCE(trtod.DTM_CRT, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :surveyDateEnd");
			paramStack.push(new Object[] { "surveyDateEnd",surveyDateEnd.toDate() });
		} 
		else if (surveyDateStart != null && surveyDateEnd == null) {
			sb.append(" AND COALESCE(trtod.DTM_CRT, '1990-01-01 00:00:00') BETWEEN :surveyDateStart AND :currentDate");
			paramStack.push(new Object[] { "surveyDateStart",surveyDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
		else if(surveyDateStart == null && surveyDateEnd == null){
			sb.append(" AND COALESCE(trtod.DTM_CRT, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate");
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		}

		// ---OTHERS
		if(!StringUtils.equals("%", (String) params[0][1])) {
			sb.append(" AND msb.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[] { "uuidBranch", Long.valueOf((String) params[0][1]) });
		}
		
		if(!StringUtils.equals("%", (String) params[1][1])) {
			sb.append(" AND COALESCE(msd.UUID_DEALER,'') = :uuidDealer ");
			paramStack.push(new Object[] { "uuidDealer", Long.valueOf((String) params[1][1]) });
		}
		paramStack.push(new Object[] { "uuidSubsystem", Long.valueOf((String) params[9][1]) });

		sb.append(" AND trth.CUSTOMER_NAME LIKE '%' + :customerName + '%'");
		paramStack.push(new Object[] { "customerName",(String) params[2][1] });

		sb.append(" AND trtod.ORDER_NO LIKE '%' + :orderNo + '%'");
		paramStack
				.push(new Object[] { "orderNo", (String) params[3][1] });

		sb.append(" AND ammsu.FULL_NAME LIKE '%' + :salesName + '%'");
		paramStack.push(new Object[] { "salesName", (String) params[4][1] });
		
		if(!StringUtils.equals("%", (String) params[12][1])) {
			sb.append(" AND msst.STATUS_CODE = :statusTask ");
			paramStack.push(new Object[] { "statusTask",(String) params[12][1] });
		}
		paramStack.push(new Object[] { "uuidBranchDealer", (String) params[10][1] }); // subquery
																					// hierarchy
																					// branch
		return sb;
	}
	
	private StringBuilder sqlPagingOrderBuilder(String orders) {
		if (StringUtils.isEmpty(orders) || "0".equals(orders)) {
			orders = "2A"; // set default order by assignDate DESC
		}

		String[] orderCols = { "b.CUSTOMER_NAME", "b.ORDER_NO",
				"b.DEALER_NAME", "b.SALES",
				"b.BRANCH_NAME",
				"b.ASSIGN_DT", "b.SUBMIT_DT",
				"b.SURVEYOR_NAME", "b.STATUS_TASK_DESC"};
		StringBuilder queryOrder = new StringBuilder(" ORDER BY ");

		int colIdx = NumberUtils.toInt(StringUtils.substring(orders, 0,
				StringUtils.length(orders) - 1));
		colIdx = (colIdx > 0) ? colIdx - 1 : colIdx;
		queryOrder.append(orderCols[colIdx]);
		if (StringUtils.endsWith(orders, "D")) {
			queryOrder.append(" DESC");
		} 
		else {
			queryOrder.append(" ASC");
		}

		return queryOrder;
	}

	@Override
	public List getHeader(long uuid, long idSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidTask", uuid }, { "uuidSubsystem", idSubsystem } };
		List result = new ArrayList<>();
		
		List list = this.getManagerDAO().selectAllNative(
				"task.inquirytaskorder.getHeader", params, null);
		for (int i = 0; i < list.size(); i++) {
			Map map = (HashMap) list.get(i);
			result.add(map);
		}
			
		return result;
	}

	@Override
	public List getHeaderFinal(long uuid, long idSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidTask", uuid }, { "uuidSubsystem", idSubsystem } };
		List result = new ArrayList<>();
		
		List list = this.getManagerDAO().selectAllNative(
				"task.inquirytaskorder.getHeaderFinal", params, null);
		for (int i = 0; i < list.size(); i++) {
			Map map = (HashMap) list.get(i);
			result.add(map);
		}
		
		return result;
	}

	@Override
	public List getHistory(long uuid, AuditContext callerId) {
		Object[][] params = { { "uuidTask", uuid } };
		List result = this.getManagerDAO().selectAllNative(
				"task.inquirytaskorder.getHistory", params, null);
		return result;
	}

	@Override
	public List getHistoryFinal(long uuid, AuditContext callerId) {
		Object[][] params = { { "uuidTask", uuid } };
		List result = this.getManagerDAO().selectAllNative(
				"task.inquirytaskorder.getHistoryFinal", params, null);		
		return result;
	}

	@Override
	public List getAnswer(long uuid, AuditContext callerId) {
		List<TrTaskBean> result = new ArrayList<TrTaskBean>();

		TrTaskH bean = this.getManagerDAO().selectOne(
				"from TrTaskH t join fetch t.msForm where t.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", uuid}});
		Object[][] params = { { "uuidTask", bean.getUuidTaskH() },
				{ "uuidForm", bean.getMsForm().getUuidForm() } };

		List list = this.getManagerDAO().selectAllNative(
				"task.inquirytaskorder.getAnswerQuestionSet", params, null);
		
		NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
		
		for (int i = 0; i < list.size(); i++) {
			TrTaskBean taskOrder = new TrTaskBean();
			Map map = (HashMap) list.get(i);

			if (!map.get("d8").toString().isEmpty()) {
				Object[][] accDetailLobParams = { { "uuidTaskD", map.get("d8") } };
				Integer accuracy = (Integer) this.getManagerDAO().selectOneNative(
						"common.retrieveAccuraryDetailLob", accDetailLobParams);
				taskOrder.setAccuracy(accuracy);
				taskOrder.setLob(map.get("d8").toString());
			}

			taskOrder.setQuestionText(map.get("d1").toString());
			taskOrder.setTextAnswer(map.get("d2").toString());
			taskOrder.setOptionText(map.get("d3").toString());
			taskOrder.setHasImage(map.get("d4").toString());

			if (null != map.get("d5")){
				taskOrder.setLatitude(new BigDecimal(map.get("d5").toString()));
			}
			if (null != map.get("d6")){
				taskOrder.setLongitude(new BigDecimal(map.get("d6").toString()));
			}
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("d9").toString())) {
				if (StringUtils.isNotBlank((String) map.get("d2"))){
					taskOrder.setTextAnswer("Rp " + formatKurs.format(NumberUtils.toDouble(map.get("d2").toString())));
				}
			}

			MsQuestion msQuestion = new MsQuestion();
			MsAnswertype msAnswertype = new MsAnswertype();
			msAnswertype.setCodeAnswerType(map.get("d9").toString());
			msQuestion.setMsAnswertype(msAnswertype);

			taskOrder.setMsQuestion(msQuestion);
			taskOrder.setFlagSource(map.get("d10").toString());

			result.add(taskOrder);
		}
		return result;
	}

	@Override
	public List getAnswerFinal(long uuid, AuditContext callerId) {
		List<InquiryTaskOrderFinalBean> result = new ArrayList<InquiryTaskOrderFinalBean>();
		
		FinalTrTaskH bean = this.getManagerDAO().selectOne(
				"from FinalTrTaskH t join fetch t.msForm where t.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", uuid}});
		Object[][] params = { { "uuidTask", bean.getUuidTaskH() },
				{ "uuidForm", bean.getMsForm().getUuidForm() } };

		List list = this.getManagerDAO().selectAllNative(
				"task.inquirytaskorder.getAnswerFinalQuestionSet", params, null);
		
		NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
		
		for (int i = 0; i < list.size(); i++) {
			InquiryTaskOrderFinalBean taskOrder = new InquiryTaskOrderFinalBean();
			Map map = (HashMap) list.get(i);

			if (!(map.get("d8").toString().isEmpty())) {
				Object[][] accDetailLobParams = { { "uuidTaskD", map.get("d8") } };
				Integer accuracy = (Integer) this.getManagerDAO().selectOneNative(
						"common.retrieveAccuraryDetailLobFinal", accDetailLobParams);
				taskOrder.setAccuracy(accuracy);
				taskOrder.setLob(map.get("d8").toString());
			}

			taskOrder.setQuestionText(map.get("d1").toString());
			taskOrder.setTextAnswer(map.get("d2").toString());
			taskOrder.setOptionText(map.get("d3").toString());
			taskOrder.setHasImage(map.get("d4").toString());

			if (null != map.get("d5")){
				taskOrder.setLatitude(new BigDecimal(map.get("d5").toString()));
			}
			if (null != map.get("d6")){
				taskOrder.setLongitude(new BigDecimal(map.get("d6").toString()));
			}
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("d9").toString())) {
				if (StringUtils.isNotBlank((String) map.get("d2")))
					taskOrder.setTextAnswer("Rp " + formatKurs.format(NumberUtils.toDouble(map.get("d2").toString())));
			}

			MsQuestion msQuestion = new MsQuestion();
			MsAnswertype msAnswertype = new MsAnswertype();
			msAnswertype.setCodeAnswerType(map.get("d9").toString());
			msQuestion.setMsAnswertype(msAnswertype);

			taskOrder.setMsQuestion(msQuestion);
			taskOrder.setFlagSource("d10");

			result.add(taskOrder);
		}
		return result;
	}

	@Override
	public List getListTaskSurvey(long uuid, AuditContext callerId) {
		Object[][] params = { { "uuidTask", uuid } };
		List result = this.getManagerDAO().selectAllNative(
				"task.inquirytaskorder.getListTaskSurvey", params, null);		
		return result;
	}

	@Override
	public List getListTaskSurveyFinal(long uuid, AuditContext callerId) {
		Object[][] params = { { "uuidTask", uuid } };
		List result = this.getManagerDAO().selectAllNative(
				"task.inquirytaskorder.getListTaskSurveyFinal", params, null);		
		return result;
	}
}
