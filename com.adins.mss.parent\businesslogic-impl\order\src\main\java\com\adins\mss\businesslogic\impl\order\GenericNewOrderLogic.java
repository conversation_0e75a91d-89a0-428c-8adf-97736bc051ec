package com.adins.mss.businesslogic.impl.order;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.TimeZone;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.Period;
import org.joda.time.PeriodType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.order.NewOrderLogic;
import com.adins.mss.businesslogic.api.order.SubmitTaskOrderLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.SubmitTaskException;
import com.adins.mss.exceptions.SubmitTaskException.Reason;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsMappingformH;
import com.adins.mss.model.MsOrdertag;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.SubmitTaskHBean;
@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericNewOrderLogic extends BaseLogic implements NewOrderLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericNewOrderLogic.class);
	
	private SubmitTaskOrderLogic submitTaskOrderLogic;
	private CommonLogic commonLogic;
	
	public void setSubmitTaskOrderLogic(SubmitTaskOrderLogic submitTaskOrderLogic){
		this.submitTaskOrderLogic = submitTaskOrderLogic;
	}	
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	@Override
	public List<Map<String, Object>> getForm(long userId,
			AuditContext callerId) {
		List<Map<String,Object>> result = new ArrayList<>();
			Object[][] params = {{"uuidUser",userId}};
			result = this.getManagerDAO().selectAllNative("task.neworder.getPublishedForm", params, null);
		return result;
	}
	
	@Override
	public List<Map<String, Object>> listAnswer(long uuidForm, AuditContext callerId){
		AmMssubsystem cekMsSubsystem = this.commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, true, callerId);
		
		Object param [][] = {{Restrictions.eq("msForm.uuidForm", uuidForm)}};
		String paramorder [][] = {{"dtmCrt", GlobalVal.ROW_ORDER_DESC}};
		List hist = (List) this.getManagerDAO().selectAll(MsFormhistory.class, param, paramorder, 1, 1).get(GlobalKey.MAP_RESULT_LIST);
		MsFormhistory formHist = (MsFormhistory) hist.get(0);
		
		//cek mapping form
		Object paramMap[][] = { {Restrictions.eq("msForm.uuidForm", uuidForm)},{Restrictions.eq("isActive","1")}, 
				{Restrictions.eq("formVersion",formHist.getFormVersion())} };
		MsMappingformH msMapFormH = this.getManagerDAO().selectOne(MsMappingformH.class, paramMap);
							
		if (msMapFormH == null && cekMsSubsystem!=null) {
			MsForm msForm = this.commonLogic.retrieveMsFormByUuid(uuidForm, callerId);
			throw new SubmitTaskException("Form "+msForm.getFormName()+" belum di-mapping ke Form Survey", Reason.ERROR_SUBMIT);
		}
		
		Object[][] params = new Object[][]{{"uuidForm", uuidForm}};
		List<Map<String,Object>> listQuestions = this.getManagerDAO().selectAllNative("task.neworder.getQuestionsSetList", params, null);
		List<Map<String,Object>> result = new ArrayList<>(listQuestions.size());
		for (int i = 0; i < listQuestions.size(); i++) {
            Map<String, Object> temp = listQuestions.get(i);
            temp.put("d12", temp.get("d12") != null ? temp.get("d12") : StringUtils.EMPTY);
            result.add(temp);
		}
		
		List<Map<String,Object>> resultFinal = new ArrayList<>();
		if (!result.isEmpty()) {
			for (int i=0; i<result.size(); i++) {
				Map<String,Object> mapResult = result.get(i);
				String calculateString = StringUtils.EMPTY;
				if (mapResult.get("d12") != null) {
					calculateString = (String) mapResult.get("d12");
				}
				String calculateTemp = StringUtils.EMPTY;
				String calculate = StringUtils.EMPTY;
				String results = StringUtils.EMPTY;
				if (StringUtils.isNotBlank(calculateString)) {
					String[] calculateArr = calculateString.split("start");
					calculateArr = calculateArr[calculateArr.length-1].split("end");
					calculateTemp = calculateArr[0].replace("_var", StringUtils.EMPTY).replace("/*", StringUtils.EMPTY).replace("*/", StringUtils.EMPTY).replace(" ", StringUtils.EMPTY);
					calculateTemp = calculateTemp.replace("$", StringUtils.EMPTY).replace("result=", StringUtils.EMPTY);

					mapResult.put("d15", calculateTemp);
					calculate = calculateTemp.trim().replace("+", ",").replace("-", ",")
							.replace("/", ",").replace("%", ",").replace("*", ",");
					if (calculate.contains(",")) {
						String[] calculateArrTemp = calculate.split(",");
						
						int check=0;
						for(int j=0;j<calculateArrTemp.length;j++){
							if((!StringUtils.isNumeric(calculateArrTemp[j]) && (calculateArrTemp[j] != null))){
								check ++;
							}
						}
						String[] tempArr= new String[check];
						int tempTtl = 0;
						for (int k=0;k<calculateArrTemp.length;k++) {
							if ((!StringUtils.isNumeric(calculateArrTemp[k]) && (calculateArrTemp[k] != null))) {
								tempArr[k-tempTtl]=calculateArrTemp[k];
							} 
							else {
								tempTtl += 1;
							}
						}
						results = StringUtils.join(tempArr,",");
					}
					else {
						results = calculate;
					}
					mapResult.put("d16", results);
				}
				else {
					mapResult.put("d15", StringUtils.EMPTY);
					mapResult.put("d16", StringUtils.EMPTY);
				}
				resultFinal.add(mapResult);
			}
		}

		return result;
	}
	
	@Override
	public List<Map<String, String>> getInitLov(String lovGroup,long uuidDealer,String[] constraints, AuditContext callerId){
		List<Map<String,String>> result = new ArrayList<>();
			
			List<Map<String,Object>> listLov = new ArrayList<>();
			if( GlobalVal.DEALEROFBRANCH.equalsIgnoreCase(lovGroup) ){
				Object[][] params = {{"lovGroup",lovGroup},{"constraint1",uuidDealer}};
				listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch", params, null);
			}
			else{
				if(constraints.length == 0){
					String[][] params = {{"lovGroup",lovGroup}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov", params, null);
				}
				else if(constraints.length == 1){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov1", params, null);
				}
				else if(constraints.length == 2){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]},{"constraint2",constraints[1]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov2", params, null);
				}
				else if(constraints.length == 3){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]},
										{"constraint2",constraints[1]},{"constraint3",constraints[2]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov3", params, null);
				}
				else if(constraints.length == 4){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]},{"constraint2",constraints[1]},
										{"constraint3",constraints[2]},{"constraint4",constraints[3]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov4", params, null);
				}
				else if(constraints.length == 5){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]},{"constraint2",constraints[1]},
										{"constraint3",constraints[2]},{"constraint4",constraints[3]},{"constraint5",constraints[4]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov5", params, null);
				}
			}
			for(int i=0;i<listLov.size();i++){
				Map map = (HashMap) listLov.get(i);
				Map<String,String> tmp = new HashMap<>();
				tmp.put("uuid", map.get("d0").toString());
				tmp.put("id",map.get("d1")!=null ? map.get("d1").toString():StringUtils.EMPTY);
				tmp.put("name", map.get("d2").toString());
				result.add(tmp);
			}
		
		return result;
	}
	
	@Override
	public List<Map<String, String>> getInitLovBranch(String lovGroup,long uuidDealer,String[] constraints, AuditContext callerId){
		List<Map<String,String>> result = new ArrayList<>();
		
		Object[][] paramsUsr = {
				{ Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId()))}};
		
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, paramsUsr);
		long uuidBranch = user.getMsBranch().getUuidBranch();
		
			List<Map<String,Object>> listLov = new ArrayList<>();
			if( GlobalVal.DEALEROFBRANCH.equalsIgnoreCase(lovGroup) ){
				Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}, {"constraint1",uuidDealer}};
				listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch2", params, null);
			}
			else{
				if("1".equals(user.getMsJob().getIsBranch())){
					if(constraints.length == 0){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch00", params, null);
					}
					else if(constraints.length == 1){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}, {"constraint1",constraints[0]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch01", params, null);
					}
					else if(constraints.length == 2){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch02", params, null);
					}
					else if(constraints.length == 3){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch03", params, null);
					}
					else if(constraints.length == 4){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]},{"constraint4",constraints[3]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch04", params, null);
					}
					else if(constraints.length == 5){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]},{"constraint4",constraints[3]},
											{"constraint5",constraints[4]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch05", params, null);
					}
				} 
				else {
					Object branchList [] = getBranchFromDealer(user.getMsDealer().getUuidDealer());
					if(constraints.length == 0){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch00A", params, null);
					}
					else if(constraints.length == 1){
						Object [][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList}, {"constraint1",constraints[0]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch01A", params, null);
					}
					else if(constraints.length == 2){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch02A", params, null);
					}
					else if(constraints.length == 3){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList},
											{"constraint1",constraints[0]},{"constraint2",constraints[1]},{"constraint3",constraints[2]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch03A", params, null);
					}
					else if(constraints.length == 4){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]},{"constraint4",constraints[3]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch04A", params, null);
					}
					else if(constraints.length == 5){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]},{"constraint4",constraints[3]},
											{"constraint5",constraints[4]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch05A", params, null);
					}
				}
				
			}
			for(int i=0;i<listLov.size();i++){
				Map map = (HashMap) listLov.get(i);
				Map<String,String> tmp = new HashMap<>();
				tmp.put("uuid", map.get("d0").toString());
				tmp.put("id",map.get("d1")!=null ? map.get("d1").toString():StringUtils.EMPTY);
				tmp.put("name", map.get("d2").toString());
				result.add(tmp);
			}
		return result;
	}
	
	public String[] getBranchFromDealer(long uuidDealer) {

		Object[][] params = { { "uuidDealer", uuidDealer } };

		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"services.common.task.getBranchFromDealer", params, null);
		if (!list.isEmpty()) {
			String[] stringResult = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = map.get("d0").toString();	
			}
			return stringResult;
		} 
		else {
			return null;
		}
	}

	@Override
	public List<Map<String, String>> getLov(String[] answerCFilter,String lovGroup, long uuidDealer,String jobCode, AuditContext callerId){
		List<Map<String,String>> result = new ArrayList<>();
			
			Stack<Object[]> paramStack = new Stack<>();
			paramStack.push(new Object[]{ Restrictions.eq("lovGroup", lovGroup)});
			paramStack.push(new Object[]{ Restrictions.eq("isActive", "1")});
			paramStack.push(new Object[]{ Restrictions.eq("isDeleted", "0")});
			for (int i = 0; i < answerCFilter.length; i++) {
				Object[] value = {answerCFilter[i],"%"};
				paramStack.push(new Object[]{ Restrictions.in("constraint" + (i+1), value)});
			}
			if ( GlobalVal.LOV_TAG_BRANCH_OF_DEALER.equalsIgnoreCase(lovGroup) && GlobalVal.JOB_SD.equalsIgnoreCase(jobCode)) {
				paramStack.push(new Object[]{ Restrictions.eq("code", uuidDealer)});
			}
			
			Object[][] sqlParams = new Object[paramStack.size()][2];
		    for (int k = 0; k < paramStack.size(); k++) {
				Object[] objects = paramStack.get(k);
				sqlParams[k] = objects;
			}
		    
		    String[][] orders = {{"description", "ASC"}};
			Map<String, Object> msLov = this.getManagerDAO().list(MsLov.class, sqlParams, orders);
			List<MsLov> listLov = (List<MsLov>) msLov.get(GlobalKey.MAP_RESULT_LIST);
			for(int i=0;i<listLov.size();i++){
				MsLov lov = (MsLov) listLov.get(i);
				Map<String,String> tmp = new HashMap<>();
				tmp.put("uuid", String.valueOf(lov.getUuidLov()));
				tmp.put("id", lov.getCode());
				tmp.put("name", lov.getDescription());
				result.add(tmp);
			}
		
		return result;
	}
	
	@Override
	public List<Map<String, String>> getLovBranch(String[] answerCFilter,String lovGroup, long uuidDealer, String jobCode, AuditContext callerId){
		List<Map<String,String>> result = new ArrayList<>();
		AmMsuser user = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
		long uuidBranch = user.getMsBranch().getUuidBranch();
			
		List<Map<String,Object>> listLov = new ArrayList<>();
			if( GlobalVal.DEALEROFBRANCH.equalsIgnoreCase(lovGroup) ){
				
				if("1".equals(user.getMsJob().getIsBranch())){
					Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}, {"constraint1",String.valueOf(uuidDealer)}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch2", params, null);
				}
				else {
					Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer}, {"constraint1",String.valueOf(uuidDealer)}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch2D", params, null);
				}
			}
			else{
				if(answerCFilter.length == 0){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch00", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch00D", params, null);
					}
				}
				else if(answerCFilter.length == 1){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}, {"constraint1",answerCFilter[0]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch01", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer}, {"constraint1",answerCFilter[0]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch01D", params, null);
					}
				}
				else if(answerCFilter.length == 2){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch02", params, null);
					}
					else{
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch02D", params, null);
					}
				}
				else if(answerCFilter.length == 3){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch03", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch03D", params, null);
					}
				}
				else if(answerCFilter.length == 4){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]},{"constraint4",answerCFilter[3]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch04", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]},{"constraint4",answerCFilter[3]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch04D", params, null);
					}
				}
				else if(answerCFilter.length == 5){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",answerCFilter[0]},
								{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]},{"constraint4",answerCFilter[3]},
								{"constraint5",answerCFilter[4]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch05", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer},{"constraint1",answerCFilter[0]},
								{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]},{"constraint4",answerCFilter[3]},
								{"constraint5",answerCFilter[4]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch05D", params, null);
					}
				}
			}
			for(int i=0;i<listLov.size();i++){
				Map map = (HashMap) listLov.get(i);
				Map<String,String> tmp = new HashMap<>();
				tmp.put("uuid", map.get("d0").toString());
				tmp.put("id",map.get("d1")!=null ? map.get("d1").toString():StringUtils.EMPTY);
				tmp.put("name", map.get("d2").toString());
				result.add(tmp);
			}
		
		return result;
	}

	@Override
	public void insert(String uniqueTaskId, String name, String phone, String address, String notes,
			String data, String form, long uuidUser, long formHistory,  AuditContext callerId) {
		MsFormhistory mfh = this.getManagerDAO().selectOne(MsFormhistory.class, formHistory);
		SubmitTaskHBean taskHBean = new SubmitTaskHBean();
		taskHBean.setUuid_task_h(uniqueTaskId);
		taskHBean.setUuid_scheme(form);
		taskHBean.setCustomer_name(name);
		taskHBean.setCustomer_phone(phone);
		taskHBean.setCustomer_address(address);
		taskHBean.setNotes(notes);
		taskHBean.setUuid_user(String.valueOf(uuidUser));
		taskHBean.setFormVersion(mfh.getFormVersion());
		
		String[] answers = data.split("#");
		SubmitTaskDBean[] taskDBean = new SubmitTaskDBean[answers.length];
		for(int i=0;i<answers.length;i++){
			taskDBean[i] = new SubmitTaskDBean();
			String[] tempData = answers[i].split("~");
			String answer = tempData[4].equalsIgnoreCase("-1")?StringUtils.EMPTY:tempData[4];
			taskDBean[i].setUuid_task_d(this.getManagerDAO().getUUID());
			taskDBean[i].setQuestion_id(tempData[0]);
			taskDBean[i].setQuestion_group_id(tempData[3]);
			if (tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO)||
				tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION)||
				tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN)||
				tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION)||
				tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION)){
				if(!answer.equalsIgnoreCase(StringUtils.EMPTY)){
					Object[][] msLovParams = {{"uuidLov",Long.valueOf(answer)}};
					String code = (String)this.getManagerDAO().selectOneNative("task.neworder.getMsLov", msLovParams); // so slowly
					taskDBean[i].setOption_answer_id(answer);
					taskDBean[i].setLov(code);
					
					MsQuestion msQuestion = this.commonLogic.retrieveQuestionByUuid(Long.parseLong(tempData[0]), callerId);
					MsOrdertag orderTag = msQuestion.getMsOrdertag();
					if (orderTag != null && GlobalVal.ORDER_TAG_JOB_MH.equals(orderTag.getTagName())) {
						taskDBean[i].setOption_answer_id(code);
					}
				}
			}
			else if(tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DATE)){
				SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
				SimpleDateFormat df2 = new SimpleDateFormat("ddMMyyyyHHmmss");
				try {
					Date date = df.parse(answer);
					taskDBean[i].setText_answer(df2.format(date));
				} 
				catch (ParseException e) {
					LOG.error(e.getMessage(), e);
				}
			}
			else if(tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_IMAGE)
					|| tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA)
					|| tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS)
					|| tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DRAWING)){
				if(!answer.equalsIgnoreCase(StringUtils.EMPTY)){
					String image = StringUtils.EMPTY;
					String[] imageTemp = answer.split("\\|");
					if (imageTemp.length!=1){
						image = imageTemp[3].split(",")[1];
						taskDBean[i].setLatitude(imageTemp[0]);
						taskDBean[i].setLongitude(imageTemp[1]);
						taskDBean[i].setAccuracy(imageTemp[2]);
					}
					else {
						image = imageTemp[0].split(",")[1];
					}
					taskDBean[i].setImage(image);
				}
			}
			else if (tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_LOCATION)) {
				if (StringUtils.isEmpty(answer)) {
					continue;
				}
				
				final String defaultAccuracy = "10"; 
				String[] points = StringUtils.split(answer, ",", 2);
				taskDBean[i].setLatitude(points[0]);
				taskDBean[i].setLongitude(points[1]);
				taskDBean[i].setAccuracy(defaultAccuracy);
				taskDBean[i].setMcc("0");
				taskDBean[i].setMnc("0");
				taskDBean[i].setLac("0");
				taskDBean[i].setCid("0");
				taskDBean[i].setText_answer(String.format("Coord : %1$s, %2$s Accuracy : %3$s m",
						new Object[]{points[0], points[1], defaultAccuracy}));
			}
			else {
				taskDBean[i].setText_answer(answer);
			}
			taskDBean[i].setQuestion_label(tempData[2]);
			taskDBean[i].setIs_final("1");		
		}
		submitTaskOrderLogic.submitTask(callerId, GlobalVal.SUBSYSTEM_MO, taskHBean, taskDBean, null, null);
	}
	
	@Override
	public List<Map<String, Object>> copyValue(List<Map<String, String>> list, long uuidForm, long uuidQuestion, 
			long uuidUser, int seqQuest, AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		MsQuestion msQuestion = this.getManagerDAO().selectOne( MsQuestion.class, uuidQuestion);
		Object [][] param = {{"refId", msQuestion.getRefId() },
							 { "uuidForm", uuidForm}};
		List <Map<String, Object>> listQuest = this.getManagerDAO().selectAllNative("task.neworder.getQuestRelevantCopy", param, null);
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, uuidUser);
		String convertedExpression = "";
		SimpleDateFormat format = new SimpleDateFormat();
			for (int i=0; i<list.size(); i++){
					Map map = list.get(i);
				if(Integer.parseInt(map.get("id").toString()) >= seqQuest){

					Object [][] prm = {{"refId", (String) map.get("refid")},
										{"uuidForm", uuidForm }};
					String script = (String) this.getManagerDAO().selectOneNative ("task.neworder.getScriptCopyValue", prm);
					
					convertedExpression = script;
					if (convertedExpression == null || convertedExpression.length() == 0) {
						continue;
					} 
					else {
						boolean needReplacing = true;
						while (needReplacing) {
							int idxOfOpenBrace = convertedExpression.indexOf('{');
							if (idxOfOpenBrace != -1) {
								int idxOfCloseBrace = convertedExpression.indexOf('}');
								String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
								int idxOfOpenAbs = identifier.indexOf("$");
								String flatAnswer = "";
								if (idxOfOpenAbs != -1) { // value yang bukan reff_id
									String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
									if (finalIdentifier.equals("LOGIN_ID")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getLoginId();
										}
									} 
									else if (finalIdentifier.equals("UUID_USER")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = String.valueOf(uuidUser);
										}
									}
									else if (finalIdentifier.equals("UUID_BRANCH")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = String.valueOf(user.getMsBranch().getUuidBranch());
										}
									} 
									else if (finalIdentifier.equals("BRANCH_ID")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getMsBranch().getBranchCode();
										}
									} 
									else if (finalIdentifier.equals("BRANCH_Name")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getMsBranch().getBranchName();
										}
									}
									else if (finalIdentifier.equals("UUID_DEALER")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = String.valueOf(user.getMsDealer().getUuidDealer());
										}
									} 
									else if (finalIdentifier.equals("DEALER_NAME")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getMsDealer().getDealerName();
										}
									} 
									else if (finalIdentifier.equals("FLAG_JOB")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getMsJob().getJobCode();
										}
									} 
									else if (finalIdentifier.equals("THISYEAR")) {
										if(map.get("answerType").toString().equals("003")|| map.get("answerType").toString().equals("004")||
												map.get("answerType").toString().equals("005")){
											Calendar cal = Calendar.getInstance(TimeZone.getDefault());
											flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
										}
									} 
									else if (finalIdentifier.equals("NOWADAYS")) {
										Calendar cal = Calendar.getInstance(TimeZone.getDefault());
										if(map.get("answerType").toString().equals("013")){
											format = new SimpleDateFormat("yyyy-MM-dd");
											flatAnswer = format.format(cal.getTime());
										} 
										else if(map.get("answerType").toString().equals("015")){
											format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
											flatAnswer = format.format(cal.getTime());
										}
									}
								} 
								else {
									if (!listQuest.isEmpty()){
										for (int j=0; j<i; j++){
											Map map2 = list.get(j);
											if (map2.get("refid").equals(identifier)){
												flatAnswer = map2.get("value").toString();
											}
										}
									}
								}
								if (flatAnswer != null && flatAnswer.length() > 0) {
									convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
								} 
								else {
									needReplacing = false;
								}
							} 
							else {
								needReplacing = false;
							}
						}
					}
					if (!convertedExpression.contains("{")){
						if(!convertedExpression.toUpperCase().contains("COPY".toUpperCase())){
							Map<String, Object> mapResult = new HashMap<>();
							mapResult.put("id", map.get("id"));
							mapResult.put("result", convertedExpression);
							mapResult.put("ansType", map.get("answerType"));
							listResult.add(mapResult);
						}
						else{
							convertedExpression = convertedExpression.replaceAll("(?i)COPY", "");
							convertedExpression = convertedExpression.replace("(", "");
							convertedExpression = convertedExpression.replace(")", "");
							String arg [] = convertedExpression.split(",");
							boolean condition=false;
							String kondisi = convert(list, arg[0]);
							ScriptEngineManager mgr = new ScriptEngineManager();
							ScriptEngine engine = mgr.getEngineByName("JavaScript");
							try {
								condition = (boolean) engine.eval(kondisi);
							} catch (ScriptException e) {
								LOG.error("Exception on copyvalue evaluation", e);
							}
							if(condition) {
								Map<String, Object> mapResult = new HashMap<>();
								mapResult.put("id", map.get("id"));
								mapResult.put("result", arg[1]);
								mapResult.put("ansType", map.get("answerType"));
								listResult.add(mapResult);
							}
							else{
								if(arg.length>2){
									Map<String, Object> mapResult = new HashMap<>();
									mapResult.put("id", map.get("id"));
									mapResult.put("result", arg[2]);
									mapResult.put("ansType", map.get("answerType"));
									listResult.add(mapResult);
								}
							}
						}
					}
				}
			}
			return listResult;
	}
	
	public boolean cekArgumen (String convertedExpression, String answerType){
		boolean result = false;
		SimpleDateFormat format = new SimpleDateFormat();
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		
		if(convertedExpression.contains("dateDifference")){
			convertedExpression = convertedExpression.replace("dateDifference", "");
			convertedExpression = convertedExpression.replace(")", "");
			convertedExpression = convertedExpression.replace("(", "");
			String arg [] = convertedExpression.split(",");
			long time1 = 0, time2 = 0;
			if(answerType.equals("013")){
				format = new SimpleDateFormat("yyyy-MM-dd");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("014")){
				format = new SimpleDateFormat("yyyy-MM-dd");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("015")){
				format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			Period difference = new Period(time1, time2, PeriodType.yearMonthDayTime());
			if(arg[2].contains("DAY")){
				int day = difference.getDays();
				arg[2] = arg[2].replace("|DAY|", day+"");
			}
			else if(arg[2].contains("MONTH")){
				int month = difference.getMonths();
				arg[2] = arg[2].replace("|MONTH|", month+"");
			} 
			else if(arg[2].contains("YEAR")){
				int year = difference.getYears();
				arg[2] = arg[2].replace("|YEAR|", year+"");
			}
			else if(arg[2].contains("HOUR")){
				int hour = difference.getHours();
				arg[2] = arg[2].replace("|HOUR|", hour+"");
			}
			else if(arg[2].contains("MINUTE")){
				int minute = difference.getMinutes();
				arg[2] = arg[2].replace("|MINUTE|", minute+"");
			}
			else if(arg[2].contains("SECOND")){
				int second = difference.getYears();
				arg[2] = arg[2].replace("|SECOND|", second+"");
			}
			arg[2] = arg[2].contains("==")?arg[2].replace("==", "<="):arg[2];
			try {
				result = (boolean) engine.eval(arg[2]);
			} 
			catch (ScriptException e) {
				LOG.error("Exception on date evaluation", e);
			}
		}
		else if (convertedExpression.contains("<=") || convertedExpression.contains("<") || convertedExpression.contains(">=") || convertedExpression.contains(">") || convertedExpression.contains("!=") || convertedExpression.contains("==")){
			convertedExpression = convertedExpression.replace("(", "");
			convertedExpression = convertedExpression.replace(")", "");
			String [] arg = convertedExpression.split("<=|<|>=|>|!=|==");
			String cek = "";
			String delimeter="";
			if (convertedExpression.contains("<=")){
				delimeter = "<=";
			}
			else if (convertedExpression.contains("<")){
				delimeter = "<";
			}
			else if (convertedExpression.contains(">=")){
				delimeter = ">=";
			}
			else if (convertedExpression.contains(">")){
				delimeter = ">";
			}
			else if (convertedExpression.contains("!=")){
				delimeter = "!=";
			}
			else if (convertedExpression.contains("==")){
				delimeter = "==";
			}
			if(answerType.equals("013")){
				long time1, time2;
				format = new SimpleDateFormat("yyyy-MM-dd");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 +delimeter + time2;
				} catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("014")){
				long time1, time2;
				format = new SimpleDateFormat("yyyy-MM-dd");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 + delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("015")){
				long time1, time2;
				format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 + delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("001") || answerType.equals("002")){
				arg[0] = arg[0].replace(" ", "");
				arg[1] = arg[1].replace(" ", "");
				if (delimeter.equals("==")){
					boolean a = (arg[0].equalsIgnoreCase(arg[1])?true : false);
					cek = a +"" ;
				}
				else if (delimeter.equals("!=")){
					boolean a = (!arg[0].equalsIgnoreCase(arg[1])?true : false);
					cek = a + "";
				}
				else{
					cek = convertedExpression;
				}
			}
			else{
				cek = convertedExpression;
			}
			try {
				result = (boolean) engine.eval(cek);
			} 
			catch (ScriptException e) {
				LOG.error("Exception on date evaluation", e);
			}
		}
		return result;
	}
	
	@Override
	public List<Map<String, Object>> validation (List<Map<String, String>> list, long uuidForm, long uuidQuestion, AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		SimpleDateFormat format = new SimpleDateFormat();
		SimpleDateFormat formatDate = new SimpleDateFormat();
		String convertedExpression="";
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		boolean finalResult = false;

		for (int k = 0; k<list.size(); k++){
			Map map = list.get(k) ;

			Object[][] paramsRel = {{ "refId", (String) map.get("refid") },
					{ "uuidForm", uuidForm} };
			Object [] obj = (Object []) this.getManagerDAO().selectOneNative("task.neworder.getScriptValidation", paramsRel);
			String script = obj[0].toString();
			String msg = obj[1].toString();
			if (StringUtils.isNotBlank(script)){
				convertedExpression = script;
			}
			else{
				continue;
			}
			
			String answerType = (String) map.get("answerType");
			String answerString = (String) map.get("value");
			String id = (String) map.get("id");
			
			if (convertedExpression == null || convertedExpression.length() == 0 
					|| answerString.length() == 0) {
				continue;
			} 
			else {
				boolean needReplacing = true;
				while (needReplacing) {
					int idxOfOpenBrace = convertedExpression.indexOf('{');
					if (idxOfOpenBrace != -1) {
						int idxOfCloseBrace = convertedExpression.indexOf('}');
						String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
						int idxOfOpenAbs = identifier.indexOf("$");
						String flatAnswer = "";
						if (idxOfOpenAbs != -1) {
							String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
							if (finalIdentifier.equals("ANSWER")) {
								try {
									if (answerType.equals("014")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										formatDate = new SimpleDateFormat("yyyyMMdd");
										Date date2 = null;
										try {
											date2 = format.parse(answerString);
										} 
										catch (Exception e) {
											date2 = formatDate.parse(answerString);
										}
										Calendar now = Calendar.getInstance(TimeZone.getDefault());
										Calendar date = Calendar.getInstance(TimeZone.getDefault());
										date.setTime(date2);
										date.set(Calendar.YEAR, now.get(Calendar.YEAR));
										date.set(Calendar.MONTH, now.get(Calendar.MONTH));
										date.set(Calendar.DAY_OF_MONTH,now.get(Calendar.DAY_OF_MONTH));
										flatAnswer = format.format(date.getTime());
									} 
									else if (answerType.equals("015")) {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										formatDate = new SimpleDateFormat("ddMMyyyyHHmmss");
										Date date2 = null;
										try {
											date2 = format.parse(answerString);
										} 
										catch (Exception e) {
											date2 = formatDate.parse(answerString);
										}
										Calendar date = Calendar.getInstance(TimeZone.getDefault());
										date.setTime(date2);
										flatAnswer = format.format(date.getTime());
									} 
									else if (answerType.equals("013")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										formatDate = new SimpleDateFormat("ddMMyyyy");
										Date date2 = null;
										try {
											date2 = format.parse(answerString);
										} 
										catch (Exception e) {
											date2 = formatDate.parse(answerString);
										}
										Calendar date = Calendar.getInstance(TimeZone.getDefault());
										date.setTime(date2);
										flatAnswer = format.format(date.getTime());
									} 
									else {
										flatAnswer = answerString;
									}
								} catch (Exception e) {
								}
							}
							else if (finalIdentifier.equals("THISYEAR")) {
								Calendar cal = Calendar.getInstance(TimeZone.getDefault());
								flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
							} 
							else if (finalIdentifier.equals("NOWADAYS")) {
								Calendar cal = Calendar.getInstance(TimeZone.getDefault());

									if (answerType.equals("014")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										flatAnswer = format.format(cal.getTime());
									} 
									else if (answerType.equals("015")) {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										flatAnswer = format.format(cal.getTime());
									} 
									else if (answerType.equals("013")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										flatAnswer = format.format(cal.getTime());
									}
									else {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										flatAnswer = format.format(cal.getTime());
									}
							} 
							else if (finalIdentifier.equals("YESTERDAY")) {
								Calendar cal = Calendar.getInstance(TimeZone.getDefault());
								cal.add(Calendar.DATE, -1);
									if (answerType.equals("014")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										flatAnswer = format.format(cal.getTime());
									} 
									else if (answerType.equals("015")) {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										flatAnswer = format.format(cal.getTime());
									} 
									else if (answerType.equals("013")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										flatAnswer = format.format(cal.getTime());
									} 
									else {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										flatAnswer = format.format(cal.getTime());
									}
							}
						}
						else{
							for (int j=0; j<list.size()-1; j++){
								Map map2 = list.get(j);
								if (map2.get("refid").equals(identifier)){
									flatAnswer = map2.get("value").toString();
								}
							}
						}
						if (flatAnswer != null && flatAnswer.length() > 0) {
							convertedExpression = convertedExpression.replace(
									"{" + identifier + "}", flatAnswer);
						}
					} 
					else {
						needReplacing = false;
					}
				}
				String validasi = valid(convertedExpression, answerType);
				try {
					finalResult = (boolean) engine.eval(validasi);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on validation evaluation", e);
				}	
			}
			Map<String, Object> result = new HashMap<>();
			result.put("id", id);
			result.put("validasi", finalResult);
			result.put("message", msg);
			listResult.add(result);	
		}
		return listResult;
	}

	@Override
	public List<Map<String, Object>> relevant(List<Map<String, String>> list, long uuidForm,  AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		String convertedExpression = "";
		for (int k = 0; k<list.size(); k++){
			
			Map map = list.get(k) ;
			Object[][] params = {{ "refId", (String) map.get("refid") },
								 {"uuidForm", uuidForm}};
			String script = (String) this.getManagerDAO().selectOneNative("task.neworder.getRelevantScript", params);
			
			if (script == null || script.length() == 0) {
				map.put("isRel", true);
				listResult.add(map);
				continue;
			}
			else {
				boolean res = false;
				ScriptEngineManager mgr = new ScriptEngineManager();
				ScriptEngine engine = mgr.getEngineByName("JavaScript");
				convertedExpression = this.convert(list, script);
				try {
					res = (boolean) engine.eval(convertedExpression);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on relevant evaluation", e);
				}
				map.put("isRel", res);
				listResult.add(map);
			}
		}
		return listResult;
	}
	
	public String valid (String script, String type){
		String convertedExpression = script;
		String [] arg = convertedExpression.toUpperCase().split("(&&)|( AND )|( OR )|(\\|\\|)");
		boolean hasil = false;
		for	(int i =0; i<arg.length; i++){
			String finalscript = arg[i].replace("(", "");
			finalscript = finalscript.replace(")","");
			hasil = cekArgumen(finalscript, type);
			convertedExpression = convertedExpression.toUpperCase().replace(arg[i], hasil+"");
		}
		return convertedExpression;
	}
	
	public String convert (List<Map<String, String>> list, String script){
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		boolean result = false;
		String convertedExpression = script;
		boolean needReplacing = true;
		String flatAnswer = "";
		while (needReplacing) {
			int idxOfOpenBrace = convertedExpression.indexOf('{');
			if (idxOfOpenBrace != -1) {
				int idxOfCloseBrace = convertedExpression.indexOf('}');
				String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
				for (int j=0; j<list.size(); j++){
					Map map2 = list.get(j);
					if (map2.get("refid").equals(identifier)){
						String [] split = map2.get("value").toString().split("\\^");
						flatAnswer = split[0];
					}
				}
				if (flatAnswer != null && flatAnswer.length() > 0) {
					convertedExpression = convertedExpression.replace(
							"{" + identifier + "}", flatAnswer);
				}
				else if(flatAnswer != null && flatAnswer.length() == 0){
					convertedExpression = convertedExpression.replace(
							"{" + identifier + "}", "-");
				}
			}
			else {
				needReplacing = false;
			}
		}
		String [] arg = convertedExpression.toUpperCase().split("(&&)|( AND )|( OR )|(\\|\\|)");
		for	(int i =0; i<arg.length; i++){
			if (arg[i].contains("<=") || arg[i].contains("<") || arg[i].contains(">=") || arg[i].contains(">") || arg[i].contains("!=") || arg[i].contains("==")){
				arg[i] = arg[i].replace("(", "");
				arg[i] = arg[i].replace(")", "");
				arg[i] = arg[i].replace(" ", "");
				String [] arg2 = arg[i].split("<=|<|>=|>|!=|==");
				String cek = "";
				String delimeter="";
				if (arg[i].contains("<=")){
					delimeter = "<=";
				}
				else if (arg[i].contains("<")){
					delimeter = "<";
				}
				else if (arg[i].contains(">=")){
					delimeter = ">=";
				}
				else if (arg[i].contains(">")){
					delimeter = ">";
				}
				else if (arg[i].contains("!=")){
					delimeter = "!=";
				}
				else if (arg[i].contains("==")){
					delimeter = "==";
				}
				if (delimeter.equals("==")){
					cek = (String) (arg2[0].equalsIgnoreCase(arg2[1])?true + "" : false + "");
				}
				else if (delimeter.equals("!=")){
					cek = (String) (!arg2[0].equalsIgnoreCase(arg2[1])?true + "" : false + "");
				}
				else{
					cek = arg[i];
				}
				
				try {
					result = (boolean) engine.eval(cek);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on evaluation", e);
				}
			}
			convertedExpression = convertedExpression.toUpperCase().replace(arg[i], result+"");
		}
		convertedExpression = convertedExpression.toUpperCase().replace("OR", "||");
		convertedExpression = convertedExpression.toUpperCase().replace("AND", "&&");
		convertedExpression = convertedExpression.toLowerCase();
		return convertedExpression;
	}
}
