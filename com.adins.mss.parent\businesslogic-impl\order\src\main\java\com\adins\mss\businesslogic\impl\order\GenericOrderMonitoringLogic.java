package com.adins.mss.businesslogic.impl.order;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.order.OrderMonitoringLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.custom.OrderMonitoringBean;

@SuppressWarnings("rawtypes")
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericOrderMonitoringLogic extends BaseLogic implements
		OrderMonitoringLogic {

	@Override
	public Map<String, Object> listOrderMonitoringBranchExc(Object params, AuditContext callerId) {		
		String[][] tempParam = (String[][]) params;		 
		String statusApprove = getStatusApprove(callerId);
		Object[] slaTime = this.getSlaVal(callerId);
			
		Map<String, Object> queryParamList = new HashMap<>();
		queryParamList.put(tempParam[0][0], tempParam[0][1]);
		queryParamList.put("statusApprove", statusApprove);
		queryParamList.put("batasHijau", Integer.valueOf(slaTime[0].toString()));
		queryParamList.put("batasKuning", Integer.valueOf(slaTime[1].toString()));
		
		String queryListName = "inquiry.ordermonitoring.orderMonitoringListBranchExc"; //filter = branchid
		List queryResult = this.getManagerDAO().selectAllNative(queryListName, queryParamList, null);
		List<OrderMonitoringBean> resultList = this.setlistOrderMonitoring(queryResult);
		
		Map<String, Object> result = new HashMap<String,Object>();
		result.put(GlobalKey.MAP_RESULT_LIST, resultList);
		result.put(GlobalKey.MAP_RESULT_SIZE, resultList.size());
		 
		return result;
	}

	@Override
	public Map<String, Object> listOrderMonitoringBranchDob(Object params, AuditContext callerId) {		
		String[][] tempParam = (String[][]) params;
		String statusApprove = getStatusApprove(callerId);
		Object[] slaTime = this.getSlaVal(callerId);
			
		Map<String, Object> queryParamList = new HashMap<>();
		queryParamList.put(tempParam[0][0], tempParam[0][1]);
		queryParamList.put("statusApprove", statusApprove);
		queryParamList.put("batasHijau", Integer.valueOf(slaTime[0].toString()));
		queryParamList.put("batasKuning", Integer.valueOf(slaTime[1].toString()));
		
		String queryListName = "inquiry.ordermonitoring.orderMonitoringListBranchDob"; //filter = dealerid
		List queryResult = this.getManagerDAO().selectAllNative(queryListName, queryParamList, null);
		List<OrderMonitoringBean> resultList = this.setlistOrderMonitoring(queryResult);

		Map<String, Object> result = new HashMap<String, Object>();
		result.put(GlobalKey.MAP_RESULT_LIST, resultList);
		result.put(GlobalKey.MAP_RESULT_SIZE, resultList.size());

		return result;
	}

	@Override
	public Map<String, Object> listOrderMonitoringDealerDob(Object params,
			 AuditContext callerId) {
		String[][] tempParam = (String[][]) params;
		String statusApprove = getStatusApprove(callerId);
		Object[] slaTime = this.getSlaVal(callerId);
			
		Map<String, Object> queryParamList = new HashMap<>();
		queryParamList.put(tempParam[0][0], tempParam[0][1]);
		queryParamList.put(tempParam[1][0], tempParam[1][1]);
		queryParamList.put("statusApprove", statusApprove);
		queryParamList.put("batasHijau", Integer.valueOf(slaTime[0].toString()));
		queryParamList.put("batasKuning", Integer.valueOf(slaTime[1].toString()));

		String queryListName = "inquiry.ordermonitoring.orderMonitoringListDealerDob";
		List queryResult = this.getManagerDAO().selectAllNative(queryListName, queryParamList, null);
		List<OrderMonitoringBean> resultList = this.setlistOrderMonitoring(queryResult);

		Map<String, Object> result = new HashMap<String, Object>();
		result.put(GlobalKey.MAP_RESULT_LIST, resultList);
		result.put(GlobalKey.MAP_RESULT_SIZE, resultList.size());

		return result;
	}

	@Override
	public Map<String, Object> listOrderMonitoringDealerExc(Object params,
			AuditContext callerId) {
		String[][] tempParam = (String[][]) params;
		String statusApprove = getStatusApprove(callerId);
		Object[] slaTime = this.getSlaVal(callerId);
			
		Map<String, Object> queryParamList = new HashMap<>();
		queryParamList.put(tempParam[0][0], tempParam[0][1]);
		queryParamList.put(tempParam[1][0], tempParam[1][1]);
		queryParamList.put("statusApprove", statusApprove);
		queryParamList.put("batasHijau", Integer.valueOf(slaTime[0].toString()));
		queryParamList.put("batasKuning", Integer.valueOf(slaTime[1].toString()));

		String queryListName = "inquiry.ordermonitoring.orderMonitoringListDealerExc";

		List queryResult = this.getManagerDAO().selectAllNative(queryListName, queryParamList, null);
		List<OrderMonitoringBean> resultList = setlistOrderMonitoring(queryResult);

		Map<String, Object> result = new HashMap<String, Object>();
		result.put(GlobalKey.MAP_RESULT_LIST, resultList);
		result.put(GlobalKey.MAP_RESULT_SIZE, resultList.size());

		return result;
	}
	
	public List<OrderMonitoringBean> setlistOrderMonitoring(List listOrder) {
		List<OrderMonitoringBean> listResult = new ArrayList<OrderMonitoringBean>();
		for (int i = 0; i < listOrder.size(); i++) {
			Map map = (Map) listOrder.get(i);
			OrderMonitoringBean omBean = new OrderMonitoringBean();
			omBean.setUuidMsuser(String.valueOf(map.get("d1")));
			omBean.setName(String.valueOf(map.get("d2")));
			omBean.setDealerId(String.valueOf(map.get("d10")));
			omBean.setDealerName(String.valueOf(map.get("d11")));
			List<OrderMonitoringBean> list = new ArrayList<OrderMonitoringBean>();
			for (int j = i; j < listOrder.size(); j++) {
				Map mapBean = (Map) listOrder.get(j);
				if (omBean.getUuidMsuser().equals(String.valueOf(mapBean.get("d1")))) {
					OrderMonitoringBean bean = new OrderMonitoringBean();
					bean.setUuidTaskh(String.valueOf(mapBean.get("d0")));
					bean.setUuidStatusTask(String.valueOf(mapBean.get("d3")));
					bean.setStatusTaskDesc(String.valueOf(mapBean.get("d4")));
					bean.setSubmitdate(String.valueOf(mapBean.get("d5")));
					bean.setJam(String.valueOf(mapBean.get("d6")));
					bean.setMenit(String.valueOf(mapBean.get("d7")));
					bean.setDtmcrt(String.valueOf(mapBean.get("d8")));
					bean.setColor(String.valueOf(mapBean.get("d9")));
					bean.setOrderNo(String.valueOf(mapBean.get("d12")));
					bean.setTaskId(String.valueOf(mapBean.get("d13")));
					bean.setCustomerName(String.valueOf(mapBean.get("d14")));
					list.add(bean);
				} 
				else {
					i=j-1;
					break;
				}
				i=j;
			}
			omBean.setListTaskOrder(list);
			listResult.add(omBean);
		}

		return listResult;
	}

	@Override
	public Object[] getSlaVal(AuditContext callerId) {
		String[][] params = {
			{"slaG", "ODRMNT_SLA_G"}, 
			{"slaY", "ODRMNT_SLA_Y"}, 
			{"slaR", "ODRMNT_SLA_R"}
		};
		Object[] sla = (Object[]) this.getManagerDAO().selectOneNativeString("SELECT " +
			"(SELECT GS_VALUE FROM AM_GENERALSETTING with (nolock) WHERE GS_CODE = :slaG) as a, "+
			"(SELECT GS_VALUE FROM AM_GENERALSETTING with (nolock) WHERE GS_CODE = :slaY) as b, "+
			"(SELECT GS_VALUE FROM AM_GENERALSETTING with (nolock) WHERE GS_CODE = :slaR) as c", params);
		return sla;
	}
	
	@Override
	public Map<String, Object> listOrderMonitoringByDealer(Object params,
			AuditContext callerId) {
		String[][] tempParam = (String[][]) params;
		String statusApprove = getStatusApprove(callerId);
		Object[] slaTime = this.getSlaVal(callerId);
			
		Map<String, Object> queryParamList = new HashMap<>();
		queryParamList.put(tempParam[0][0], tempParam[0][1]);
		queryParamList.put("statusApprove", statusApprove);
		queryParamList.put("batasHijau", Integer.valueOf(slaTime[0].toString()));
		queryParamList.put("batasKuning", Integer.valueOf(slaTime[1].toString()));

		String queryListName = "inquiry.ordermonitoring.orderMonitoringListByDealer";

		List queryResult = this.getManagerDAO().selectAllNative(queryListName,
				queryParamList, null);
		List<OrderMonitoringBean> resultList = setlistOrderMonitoring(queryResult);

		Map<String, Object> result = new HashMap<String, Object>();
		result.put(GlobalKey.MAP_RESULT_LIST, resultList);
		result.put(GlobalKey.MAP_RESULT_SIZE, resultList.size());
		return result;
	}

	public String getStatusApprove(AuditContext callerId) {
		String result = "O";
		Object[][] param = {{Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_ORDER_APPROVE_STATUS_CODE)}};
		AmGeneralsetting amGS = (AmGeneralsetting) this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
		if (amGS != null && StringUtils.isNotBlank(amGS.getGsValue())) {
			result = amGS.getGsValue();
		}
		return result;
	}
	
	@Override
	public boolean checkMenuList(Object params, Boolean isBranch, AuditContext callerId) {
		Integer countSub;
		if(isBranch){
			countSub = (Integer) this.getManagerDAO().selectOneNativeString(
					"select count(*) from ms_branch with (nolock) where parent_id = :uuidBranch",params);
		} 
		else {
			countSub = (Integer) this.getManagerDAO().selectOneNativeString(
					"select count(*) from ms_dealer with (nolock) where parent_id = :uuidDealer",params);
		}
		
		if(countSub == 0){
			return true;
		} 
		else {
			return false;
		}
	}
}
