package com.adins.mss.businesslogic.impl.order;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.order.ReportOrderMonitoringLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsDealer;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings("rawtypes")
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportOrderMonitoringLogic extends BaseLogic 
		implements ReportOrderMonitoringLogic, MessageSourceAware {
	private String[] HEADER_SUMMARY = { "No", "Branch Name",
		"Total Task", "Task Order Approved", "Task Order Canceled",
		"Task Order Rejected","Task Order On Progress" };
	private int[] SUMMARY_COLUMN_WIDTH = { 10 * 256, 20 * 256,
		20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256};
	private String[] HEADER_DETAIL = { "No", "Branch Name",
		"Dealer Name", "Order No", "Status"};
	private int[] DETAIL_COLUMN_WIDTH = { 10 * 256, 20 * 256,
		20 * 256, 20 * 256, 20 * 256};
	private static final Logger LOG = LoggerFactory.getLogger(GenericReportOrderMonitoringLogic.class);
	
	@Autowired
    private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	@Override
	public List<Map<String, Object>> getBranchListCombo(String branchId,
			AuditContext callerId) {
		List<Map<String,Object>> result;
		String[][] params={{"branchId",branchId}};
		result = this.getManagerDAO().selectAllNative("report.ordermonitoring.getListBranch", params, null);
		return result;
	}

	@Override
	public List<Map<String, Object>> getDealerListCombo(String dealerId,
			AuditContext callerId) {
		List<Map<String,Object>> result;
		String[][] params={{"dealerId",dealerId}};
		result = this.getManagerDAO().selectAllNative("report.ordermonitoring.getListDealer", params, null);
		return result;
	}
	
	@Override
	public List getReportOrderMonitoring(String[][] params, String type,
			String isBranch, AuditContext callerId) {
		List result = null;
		if (isBranch.equalsIgnoreCase("1")) {
			if (type.equalsIgnoreCase("0")) {
				result = this.getManagerDAO().selectAllNative(
						"report.ordermonitoring.getListReportAll", params, null);
			} 
			else {
				result = this.getManagerDAO().selectAllNative(
						"report.ordermonitoring.getListReport", params, null);
			}
		}
		else {
			if (type.equalsIgnoreCase("0")) {
				result = this.getManagerDAO().selectAllNative(
						"report.ordermonitoring.getListReportDealerAll", params, null);
			}
			else {
				result = this.getManagerDAO().selectAllNative(
						"report.ordermonitoring.getListReportDealer", params, null);
			}
		}
		return result;
	}
	
	@Override
	public List getReportOrderMonitoringDetail(String[][] params, String type,
			String type2, String isBranch, AuditContext callerId) {
		List result = null;
		//branch			
		if (isBranch.equalsIgnoreCase("1")) {
			if (type.equalsIgnoreCase("0")) {
				result = this.getManagerDAO().selectAllNative(
						"report.ordermonitoring.getListReportDetailAll", params, null);
			}
			else {
				result = this.getManagerDAO().selectAllNative(
						"report.ordermonitoring.getListReportDetail", params, null);
			}
		}
		//dealer
		else {
			if (type.equalsIgnoreCase("0")) {//dealer all
				if (type2.equalsIgnoreCase("0")) {//search user all
					result = this.getManagerDAO().selectAllNative(
							"report.ordermonitoring.getListReportDealerDetailAll", params, null);
				}
				else {
					result = this.getManagerDAO().selectAllNative(
							"report.ordermonitoring.getListReportDealerDetail", params, null);
				}
			}
			else {
				if (type2.equalsIgnoreCase("0")) {
					result = this.getManagerDAO().selectAllNative(
							"report.ordermonitoring.getListReportDealerDetailAll2", params, null);
				}
				else {
					result = this.getManagerDAO().selectAllNative(
							"report.ordermonitoring.getListReportDealerDetail2", params, null);
				}
			}
		}
		return result;
	}
	
	@Override
	public byte[] exportExcel(String[][] params, String task, String type, String type2,
			String isBranch, AuditContext callerId) {	
		XSSFWorkbook workbook = this.createXlsTemplate(params,task,type,type2,isBranch,callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	public XSSFWorkbook createXlsTemplate(String[][] params, String task, String type, String type2,
			String isBranch, AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Report Order Monitoring");
			if (task.equalsIgnoreCase("summary")) {
				List result= this.getReportOrderMonitoring(params, type, isBranch, callerId);
				HEADER_SUMMARY[1]=isBranch.equalsIgnoreCase("0") ? "Dealer Name":HEADER_SUMMARY[1];
				this.createDataSummary(workbook, sheet, result, params[1][1], params[2][1]);
			}
			else {
				List result= this.getReportOrderMonitoringDetail(params, type, type2, isBranch, callerId);
				if (isBranch.equalsIgnoreCase("0")) {
					HEADER_DETAIL[1]="Dealer Name";
					HEADER_DETAIL[2]="Sales Name";
					this.createDataDetail(workbook,sheet,result, params[0][1], params[1][1]);
				}
				else {
					this.createDataDetail(workbook,sheet,result, params[1][1], params[2][1]);
				}
			}
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private void createDataSummary(XSSFWorkbook workbook, XSSFSheet sheet, List result, String startDate, 
			String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_SUMMARY.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT SUMMARY PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(HEADER_SUMMARY[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, SUMMARY_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_SUMMARY.length-1));
		//Data row
		for (int i = 0; i < result.size();i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			//data cell
			for (int j = 1; j < HEADER_SUMMARY.length;j++) {
				XSSFCell cell = rowData.createCell(j);
				if (j == 1) { //Set data to string
					cell.setCellValue(temp.get("d"+(j-1)).toString());
					cell.setCellStyle(styles.get("cell"));
				}
				else {
					cell.setCellValue(Integer.parseInt(temp.get("d"+(j-1)).toString()));
					cell.setCellStyle(styles.get("cell"));
				}
			}
		} 
		//Total Data
		XSSFRow rowData = sheet.createRow(rowcell++);
		XSSFCell cellNo = rowData.createCell(0);
		cellNo.setCellValue("Total");
		cellNo.setCellStyle(styles.get("header"));
		XSSFCell cell = rowData.createCell(1);
        String ref = (char)('B') + "2:" + (char)('B') + Integer.toString(result.size()+1);
        cell.setCellFormula("COUNTA(" + ref + ")");	
        cell.setCellStyle(styles.get("header"));
        
        for (int i = 0; i < HEADER_SUMMARY.length-2; i++) {    // -2  karena sudah cell A dan B sudah diisi
        	cell = rowData.createCell(i+2);
        	ref = (char)('C'+i) + "3:" + (char)('C'+i) + Integer.toString(result.size()+2);
            cell.setCellFormula("SUM(" + ref + ")");
            cell.setCellStyle(styles.get("header"));
        }
        //End Total Data       	
	}
	
	private void createDataDetail(XSSFWorkbook workbook, XSSFSheet sheet, List result, 
			String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_DETAIL.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT DETAIL PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(HEADER_DETAIL[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, DETAIL_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_DETAIL.length-1));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			//data cell
			for (int j = 1; j < HEADER_DETAIL.length; j++) {
				XSSFCell cell = rowData.createCell(j);
				cell.setCellValue(temp.get("d"+(j-1)) == null || 
						temp.get("d"+(j-1)).toString().equalsIgnoreCase("") ? "-":temp.get("d"+(j-1)).toString());
				cell.setCellStyle(styles.get("cell"));		
			}
		}       	
	}	
	
	private static Map<String, CellStyle> createStyles(Workbook wb){
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style;

        style = wb.createCellStyle();
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styles.put("cell", style);

        style = wb.createCellStyle();
        Font font = wb.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(CellStyle.SOLID_FOREGROUND);
        styles.put("header",style );
        return styles;
    }

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(String[][] params, String task,
			String type, String type2, String isBranch, AuditContext callerId) {
		String branchId = null;
		String userId = null;
		String startDate = null;
		String endDate = null;
		
		for (int i=0; i<params.length; i++) {
			if ("branchId".equals(params[i][0])) {
				branchId = params[i][1];
			} 
			else if ("userId".equals(params[i][0])) {
				userId = params[i][1];
			} 
			else if ("startDate".equals(params[i][0])) {
				startDate = params[i][1];
			} 
			else if ("endDate".equals(params[i][0])) {
				endDate = params[i][1];
			}
		}
		
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setUuidLoginId(callerId.getCallerId());
		reportBean.setUuidBranch(branchId);
		reportBean.setUuidUser(userId);
		reportBean.setStartDate(startDate);
		reportBean.setEndDate(endDate);
		reportBean.setTask(task);
		reportBean.setType(type);
		reportBean.setType2(type2);
		reportBean.setIsBranch(isBranch);
		reportBean.setParamsAction(params);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

		AmMsuser amMsUser = new AmMsuser();
		amMsUser.setUuidMsUser(Long.valueOf(callerId.getCallerId()));
		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("Order Monitoring Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_ORDER_MONITORING.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", 
				null, this.retrieveLocaleAudit(callerId));
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog,
			AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(reportBean.getParamsAction(), 
				reportBean.getTask(), reportBean.getType(), reportBean.getType2(), 
				reportBean.getIsBranch(), callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("OrderMonitoring_");
			
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				sb.append("_BRANCH-");
				if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
					reportBean.setUuidBranch(null);
				}
				if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
					MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
							Long.valueOf(reportBean.getUuidBranch()));
					sb.append(msBranch.getBranchCode());
				} 
				else {
					sb.append("ALL");
				}
			}
			
			if (StringUtils.isNotBlank(reportBean.getUuidDealer())) {
				sb.append("_DEALER-");
				if ("0".equals(reportBean.getUuidDealer()) || "%".equals(reportBean.getUuidDealer())) {
					reportBean.setUuidDealer(null);
				}
				if (StringUtils.isNotBlank(reportBean.getUuidDealer())) {
					MsDealer msDealer = this.getManagerDAO().selectOne(MsDealer.class, 
							Long.valueOf(reportBean.getUuidDealer()));
					sb.append(msDealer.getDealerCode());
				} 
				else {
					sb.append("ALL");
				}
			}
			
			if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
				sb.append("_USER-");
				if ("0".equals(reportBean.getUuidUser()) || "%".equals(reportBean.getUuidUser())) {
					reportBean.setUuidUser(null);
				}
				if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
					AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
							Long.valueOf(reportBean.getUuidUser()));
					sb.append(user.getLoginId());
				} 
				else {
					sb.append("ALL");
				}
			}
			
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(
				trReportResultLog.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime()
				.getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}
	
	@Override
	public List getUserByDealer(String uuidMsUser, String uuidDealer, String uuidBranch, 
			int pageNo, int pageSize, AuditContext callerId) {
		List result = null;
		Object[][] params = { { "uuidDealer", uuidDealer },
				{ "uuidBranch", uuidBranch },
				{ "start", (pageNo - 1) * pageSize + 1 },
				{ "end", (pageNo - 1) * pageSize + pageSize },
				{ "uuidMsUser", uuidMsUser }};
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
			.append("WITH N AS ( ")
			.append("SELECT ")
			.append("msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI, ")
			.append("CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID ")
			.append("FROM  AM_MSUSER msu with (nolock) ")
			.append("WHERE msu.IS_ACTIVE = '1' ")
			.append(paramsQueryString)
			.append("UNION ALL ")
			.append("SELECT msu2.UUID_MS_USER, msu2.FULL_NAME,	msu2.IS_LOGGED_IN, N.HIRARKI, ")
			.append("N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID ")
			.append("FROM AM_MSUSER msu2 with (nolock),N ")
			.append("WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1' ")
			.append(") ")
			.append("select * from ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  FROM ( ")
			.append("select distinct N.UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID ")
			.append("from N ")
			.append("JOIN (SELECT keyValue as UUID_MS_USER ")
			.append("FROM dbo.getUserByLogin(:uuidMsUser) where keyValue != :uuidMsUser) ammsu  ")
			.append("on ammsu.UUID_MS_USER = N.UUID_MS_USER ")
			.append(") c ")
			.append(") a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		paramsStack.push(new Object[]{"uuidMsUser", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[2][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[3][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
		for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}
	
	/*
	 * 0 { { "uuidDealer", uuidDealer },
	 * 1 { "uuidBranch", uuidBranch },
	 * 2 { "start", (pageNo - 1) * pageSize + 1 },
	 * 3 { "end", (pageNo - 1) * pageSize + pageSize },
	 * 4 { "uuidMsUser", uuidMsUser }}
	 */
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID DEALER
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and msu.UUID_DEALER = :uuidDealer ");
			paramStack.push(new Object[]{"uuidDealer", Long.valueOf((String) params[0][1])});
		}
		
		//---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("and msu.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{"uuidBranch", Long.valueOf((String) params[1][1])});
		}
		return sb;
	}
	
	@Override
	public Integer countUserByDealer(String uuidMsUser, String uuidDealer, 
			String uuidBranch, AuditContext callerId) {
		Integer result = NumberUtils.INTEGER_ZERO;
		Object[][] params = { { "uuidDealer", uuidDealer },
				{"uuidBranch",uuidBranch}, { "uuidMsUser", uuidMsUser } };
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
			.append("WITH N AS ( ")
			.append("SELECT ")
			.append("msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI, ")
			.append("CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID ")
			.append("FROM  AM_MSUSER msu with (nolock) ")
			.append("WHERE msu.IS_ACTIVE = '1' ")
			.append(paramsQueryString)
			.append("UNION ALL ")
			.append("SELECT msu2.UUID_MS_USER, msu2.FULL_NAME,	msu2.IS_LOGGED_IN, N.HIRARKI, ")
			.append("N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID ")
			.append("FROM AM_MSUSER msu2 with (nolock),N ")
			.append("WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1' ")
			.append(") ")
			.append("SELECT Count(*)  FROM ( ")
			.append("select distinct N.UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID ")
			.append("from N ")
			.append("JOIN (SELECT keyValue as UUID_MS_USER ")
			.append("FROM dbo.getUserByLogin(:uuidMsUser) where keyValue != :uuidMsUser) ammsu  ")
			.append("on ammsu.UUID_MS_USER = N.UUID_MS_USER ")
			.append(") c ");
		paramsStack.push(new Object[]{"uuidMsUser", ((Object[][]) params)[2][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
		for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		
		return result;
	}
	
	@Override
	public List<Map<String, Object>> getDealerListComboByBranch(String branchId,
			AuditContext callerId) {
		List<Map<String,Object>> result;
		String[][] params={{"branchId",branchId}};
		result = this.getManagerDAO().selectAllNative("report.orderperformance.getListDealerByBranch", params, null);
		
		return result;
	}
	
	@Override
	public String getStatusApprove(AuditContext callerId) {
		String result = "O";
		Object[][] param = {{Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_ORDER_APPROVE_STATUS_CODE)}};
		AmGeneralsetting amGS = (AmGeneralsetting) this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
		if (amGS != null) {
			result = amGS.getGsValue();
		}
		return result;
	}
}
