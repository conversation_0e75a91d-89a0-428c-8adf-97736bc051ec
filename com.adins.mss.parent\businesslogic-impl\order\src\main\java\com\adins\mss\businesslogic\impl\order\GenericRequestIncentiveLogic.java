package com.adins.mss.businesslogic.impl.order;

import java.util.List;

import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.order.RequestIncentiveLogic;

@SuppressWarnings("rawtypes")
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericRequestIncentiveLogic extends BaseLogic 
		implements RequestIncentiveLogic{

	private IntFormLogic intFormLogic;
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	@Override
	public List listRequestIncentive(Object params, AuditContext callerId) {
		List result = null;
		result = this.getManagerDAO().selectAllNative(
				"report.requestincentive.report.requestincentiveListUser", params, null);
		return result;
	}

	@Override
	public Integer countRequestIncentive(Object params, AuditContext callerId) {
		Integer result;
		result = (Integer)this.getManagerDAO().selectOneNative(
				"report.requestincentive.report.requestincentiveCountListUser", params);
		
		return result;
	}
	
	@Override
	public byte[] printIncentive(String loginId, String startDate, String endDate, AuditContext auditContext) {
		byte[] po = intFormLogic.reportIncentive(loginId, startDate, endDate, auditContext.getCallerId());
		return po;
	}
}
