package com.adins.mss.businesslogic.impl.order;

import java.util.List;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.order.RequestPOLogic;

@SuppressWarnings({"rawtypes"})
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericRequestPOLogic extends BaseLogic implements RequestPOLogic {

	private IntFormLogic intFormLogic;

	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}


	@Override
	public List listTaskDO(Object params, String isBranch, AuditContext callerId) {
		List result = null;
		if ("1".equals(isBranch)) {
			Stack<Object[]> paramsStack = new Stack<>();
			StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
			StringBuilder queryBuilder = new StringBuilder()
					.append("SELECT * from ( ")
					.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
					.append("SELECT *, ")
					.append("ROW_NUMBER() OVER( ")
					.append("ORDER BY  CASE WHEN :odr = '1A' THEN C.CUSTOMER_NAME ")
					.append("WHEN :odr  = '2A' THEN c.ORDER_NO  ")
					.append("WHEN :odr  = '3A' THEN c.DEALER_NAME ")
					.append("WHEN :odr  = '4A' THEN c.SURVEYOR_NAME ")
					.append("WHEN :odr  = '5A' THEN c.BRANCH_NAME ")
					.append("WHEN :odr  = '6A' THEN CONVERT(VARCHAR, c.submit1, 121) ")
					.append("WHEN :odr  = '7A' THEN CONVERT(VARCHAR, c.submit2, 121) ")
					.append("WHEN :odr  = '8A' THEN c.SURVEYOR_NAME ")
					.append("WHEN :odr  = '9A' THEN c.STATUS_TASK_DESC ")
					.append("ELSE c.flagodr ")
					.append("END ASC, ")
					.append("CASE WHEN  :odr= '1D' THEN c.CUSTOMER_NAME ")
					.append("WHEN  :odr = '2D' THEN c.ORDER_NO  ")
					.append("WHEN  :odr = '3D' THEN c.DEALER_NAME ")
					.append("WHEN  :odr = '4D' THEN c.SURVEYOR_NAME ")
					.append("WHEN  :odr = '5D' THEN c.BRANCH_NAME ")
					.append("WHEN  :odr = '6D' THEN CONVERT(VARCHAR, c.submit1, 121) ")
					.append("WHEN  :odr = '7D' THEN CONVERT(VARCHAR, c.submit2, 121) ")
					.append("WHEN  :odr = '8D' THEN c.SURVEYOR_NAME ")
					.append("WHEN 	:odr = '9D' THEN c.STATUS_TASK_DESC ")
					.append("ELSE c.flagodr  ")
					.append("END DESC)AS rownum ")
					.append("FROM ")
					.append("(SELECT trth.UUID_TASK_H,  ")
					.append("isnull(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME, ")
					.append("isnull(trtod.ORDER_NO,'-') ORDER_NO,  ")
					.append("isnull(msd.DEALER_NAME,'-') DEALER_NAME, ")
					.append("isnull(ammsu.FULL_NAME,'-') SALES, ")
					.append("isnull(msb.BRANCH_NAME,'-') BRANCH_NAME, ")
					.append("isnull(LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17),'-') as SUBMIT_DATE, ")
					.append("isnull(LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17),'-') as ASSIGN_DATE, ")
					.append("isnull(ammsu2.FULL_NAME,'-') as SURVEYOR_NAME,  ")
					.append("isnull(msst.STATUS_TASK_DESC,'-') STATUS_TASK_DESC, ")
					.append("':odr' as flagodr, ")
					.append("'1' as flag, ")
					.append("COALESCE(trth.SUBMIT_DATE, '-') as submit1, ")
					.append("COALESCE(trtod.DTM_CRT, '-') as submit2 ")
					.append("FROM TR_TASK_H trth with (nolock) left join MS_STATUSTASK msst with (nolock) ")
					.append("on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
					.append("left join TR_TASKORDERDATA trtod with (nolock) ")
					.append("on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
					.append("left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME ")
					.append("FROM dbo.getCabangByLogin(:uuidBranchDealer)) msb ")
					.append("on trth.UUID_BRANCH = msb.UUID_BRANCH ")
					.append("left join MS_DEALER msd with (nolock) ")
					.append("on trtod.DEALER_ID = msd.UUID_DEALER ")
					.append("left join AM_MSUSER ammsu with (nolock)  ")
					.append("on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
					.append("left join AM_MSUSER ammsu2 with (nolock) ")
					.append("on ammsu2.UUID_MS_USER = trtod.CA_ID ")
					.append("WHERE msst.STATUS_CODE IN ('DLO', 'GLV') ")
					.append(paramsQueryString)
					.append("UNION ALL ")
					.append("SELECT trth.UUID_TASK_H,  ")
					.append("isnull(trth.CUSTOMER_NAME,'-') CUSTOMER_NAME, ")
					.append("isnull(trtod.ORDER_NO,'-') ORDER_NO,  ")
					.append("isnull(msd.DEALER_NAME,'-') DEALER_NAME, ")
					.append("isnull(ammsu.FULL_NAME,'-') SALES, ")
					.append("isnull(msb.BRANCH_NAME,'-') BRANCH_NAME, ")
					.append("isnull(LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17),'-') as SUBMIT_DATE, ")
					.append("isnull(LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17),'-') as ASSIGN_DATE, ")
					.append("isnull(ammsu2.FULL_NAME,'-') as SURVEYOR_NAME,  ")
					.append("isnull(msst.STATUS_TASK_DESC,'-') STATUS_TASK_DESC, ")
					.append("':odr' as flagodr, ")
					.append("'2' as flag, ")
					.append("COALESCE(trth.SUBMIT_DATE, '-') as submit1, ")
					.append("COALESCE(trtod.DTM_CRT, '-') as submit2 ")
					.append("FROM final_TR_TASK_H trth with (nolock) ")
					.append("left join MS_STATUSTASK msst with (nolock) ")
					.append("on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
					.append("left join TR_TASKORDERDATA trtod with (nolock) ")
					.append("on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
					.append("left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME ")
					.append("FROM dbo.getCabangByLogin(:uuidBranchDealer)) msb ")
					.append("on trth.UUID_BRANCH = msb.UUID_BRANCH ")
					.append("left join MS_DEALER msd with (nolock) ")
					.append("on trtod.DEALER_ID = msd.UUID_DEALER ")
					.append("left join AM_MSUSER ammsu with (nolock) ")
					.append("on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
					.append("left join AM_MSUSER ammsu2 with (nolock) ")
					.append("on ammsu2.UUID_MS_USER = trtod.CA_ID ")
					.append("WHERE msst.STATUS_CODE IN ('DLO', 'GLV') ")
					.append(paramsQueryString).append(") c ")
					.append(") a WHERE a.rownum <= :end ")
					.append(") b WHERE b.recnum >= :start ");
			paramsStack.push(new Object[] { "odr", ((Object[][]) params)[14][1] });
			paramsStack.push(new Object[] { "uuidBranchDealer", ((Object[][]) params)[10][1] });
			paramsStack.push(new Object[] { "uuidSubsystem", ((Object[][]) params)[9][1] });
			paramsStack.push(new Object[] { "start", ((Object[][]) params)[12][1] });
			paramsStack.push(new Object[] { "end", ((Object[][]) params)[13][1] });
			Object[][] sqlParams = new Object[paramsStack.size()][2];
			for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
			result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		} 
		else {
			Stack<Object[]> paramsStack = new Stack<>();
			StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
			StringBuilder queryBuilder = new StringBuilder()
					.append("SELECT * from ( ")
					.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
					.append("SELECT *, ")
					.append("ROW_NUMBER() OVER( ")
					.append("ORDER BY  CASE WHEN :odr = '1A' THEN C.CUSTOMER_NAME  ")
					.append("WHEN :odr  = '2A' THEN c.ORDER_NO  ")
					.append("WHEN :odr  = '3A' THEN c.DEALER_NAME ")
					.append("WHEN :odr  = '4A' THEN c.SURVEYOR_NAME ")
					.append("WHEN :odr  = '5A' THEN c.BRANCH_NAME ")
					.append("WHEN :odr  = '6A' THEN CONVERT(VARCHAR, c.submit1, 121) ")
					.append("WHEN :odr  = '7A' THEN CONVERT(VARCHAR, c.submit2, 121) ")
					.append("WHEN :odr  = '8A' THEN c.SURVEYOR_NAME ")
					.append("WHEN :odr  = '9A' THEN c.STATUS_TASK_DESC  ")
					.append("ELSE c.flagodr  ")
					.append("END ASC, ")
					.append("CASE WHEN  :odr= '1D' THEN c.CUSTOMER_NAME  ")
					.append("WHEN  :odr = '2D' THEN c.ORDER_NO  ")
					.append("WHEN  :odr = '3D' THEN c.DEALER_NAME ")
					.append("WHEN  :odr = '4D' THEN c.SURVEYOR_NAME ")
					.append("WHEN  :odr = '5D' THEN c.BRANCH_NAME ")
					.append("WHEN  :odr = '6D' THEN CONVERT(VARCHAR, c.submit1, 121) ")
					.append("WHEN  :odr = '7D' THEN CONVERT(VARCHAR, c.submit2, 121) ")
					.append("WHEN  :odr = '8D' THEN c.SURVEYOR_NAME ")
					.append("WHEN 	:odr = '9D' THEN c.STATUS_TASK_DESC  ")
					.append("ELSE c.flagodr  ")
					.append("END DESC)AS rownum ")
					.append("FROM ")
					.append("(SELECT trth.UUID_TASK_H,  ")
					.append("trth.CUSTOMER_NAME,  ")
					.append("trtod.ORDER_NO,  ")
					.append("msd.DEALER_NAME,  ")
					.append("ammsu.FULL_NAME SALES, ")
					.append("msb.BRANCH_NAME, ")
					.append("LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as SUBMIT_DATE, ")
					.append("LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as ASSIGN_DATE, ")
					.append("ammsu2.FULL_NAME SURVEYOR_NAME,  ")
					.append("msst.STATUS_TASK_DESC, ")
					.append("':odr' as flagodr, ")
					.append("'1' as flag, ")
					.append("COALESCE(trth.SUBMIT_DATE, '-') as submit1, ")
					.append("COALESCE(trtod.DTM_CRT, '-') as submit2 ")
					.append("FROM TR_TASK_H trth with (nolock) left join MS_STATUSTASK msst with (nolock) ")
					.append("on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
					.append("left join TR_TASKORDERDATA trtod with (nolock) ")
					.append("on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
					.append("left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME ")
					.append("FROM dbo.getCabangByLogin(:uuidBranchDealer)) msb ")
					.append("on trth.UUID_BRANCH = msb.UUID_BRANCH ")
					.append("left join MS_DEALER msd with (nolock)  ")
					.append("on trtod.DEALER_ID = msd.UUID_DEALER ")
					.append("left join AM_MSUSER ammsu with (nolock) ")
					.append("on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
					.append("left join AM_MSUSER ammsu2 with (nolock) ")
					.append("on ammsu2.UUID_MS_USER = trtod.CA_ID ")
					.append("WHERE msst.STATUS_CODE IN ('DLO', 'GLV') ")
					.append(paramsQueryString)
					.append("UNION ALL ")
					.append("SELECT trth.UUID_TASK_H, ")
					.append("trth.CUSTOMER_NAME, ")
					.append("trtod.ORDER_NO, ")
					.append("msd.DEALER_NAME,  ")
					.append("ammsu.FULL_NAME SALES, ")
					.append("msb.BRANCH_NAME, ")
					.append("LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as SUBMIT_DATE, ")
					.append("LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as ASSIGN_DATE, ")
					.append("ammsu2.FULL_NAME SURVEYOR_NAME, ")
					.append("msst.STATUS_TASK_DESC, ")
					.append("':odr' as flagodr, ")
					.append("'2' as flag, ")
					.append("COALESCE(trth.SUBMIT_DATE, '-') as submit1, ")
					.append("COALESCE(trtod.DTM_CRT, '-') as submit2 ")
					.append("FROM final_TR_TASK_H trth with (nolock) left join MS_STATUSTASK msst with (nolock) ")
					.append("on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
					.append("left join TR_TASKORDERDATA trtod with (nolock) ")
					.append("on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
					.append("left join MS_BRANCH msb with (nolock)  ")
					.append("on trth.UUID_BRANCH = msb.UUID_BRANCH ")
					.append("left join (SELECT keyValue as UUID_DEALER, dealerName as DEALER_NAME ")
					.append("from dbo.getDealerByLogin(:uuidBranchDealer)) msd ")
					.append("on trtod.DEALER_ID = msd.UUID_DEALER ")
					.append("left join AM_MSUSER ammsu with (nolock)  ")
					.append("on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
					.append("left join AM_MSUSER ammsu2 with (nolock) ")
					.append("on ammsu2.UUID_MS_USER = trtod.CA_ID ")
					.append("WHERE msst.STATUS_CODE IN ('DLO', 'GLV') ")
					.append(paramsQueryString).append(") c ")
					.append(") a WHERE a.rownum <= :end ")
					.append(") b WHERE b.recnum >= :start ");
			paramsStack.push(new Object[] { "odr", ((Object[][]) params)[14][1] });
			paramsStack.push(new Object[] { "uuidBranchDealer", ((Object[][]) params)[10][1] });
			paramsStack.push(new Object[] { "uuidSubsystem", ((Object[][]) params)[9][1] });
			paramsStack.push(new Object[] { "start", ((Object[][]) params)[12][1] });
			paramsStack.push(new Object[] { "end", ((Object[][]) params)[13][1] });
			Object[][] sqlParams = new Object[paramsStack.size()][2];
			for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
			result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		}
		return result;
	}

	/* 0 "uuidBranch", */
	/* 1 "uuidDealer", */
	/* 2 "customerName", */
	/* 3 "orderNo", */
	/* 4 "salesName", */
	/* 5 "orderDateStart", */
	/* 6 "orderDateEnd", */
	/* 7 "surveyDateStart", */
	/* 8 "surveyDateEnd", */
	/* 9 "uuidSubsystem", */
	/* 10 "uuidBranchDealer", */
	/* 11 "currentDate", */
	/* 12 "start", */
	/* 13 "end", */
	/* 14 "odr" */
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		// ---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("msb.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[] { "uuidBranch", Long.valueOf((String) params[0][1]) });
		}

		// ---UUID_DEALER
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("AND COALESCE(msd.UUID_DEALER,'') = :uuidDealer ");
			paramStack.push(new Object[] { "uuidDealer", Long.valueOf((String) params[1][1]) });
		}

		// ---CUSTOMER_NAME
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("AND trth.CUSTOMER_NAME like UPPER('%'+ :customerName +'%') ");
			paramStack.push(new Object[] { "customerName", (String) params[2][1] });
		}

		// ---ORDER_NO
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append("AND trtod.ORDER_NO like UPPER('%'+ :orderNo +'%') ");
			paramStack.push(new Object[] { "orderNo", (String) params[3][1] });
		}

		// ---SALES_NAME
		if (!StringUtils.equals("%", (String) params[4][1])) {
			sb.append("AND ammsu.FULL_NAME like UPPER('%'+ :salesName +'%') ");
			paramStack.push(new Object[] { "salesName", (String) params[4][1] });
		}

		sb.append("AND trth.SUBMIT_DATE BETWEEN (CASE ")
			.append("WHEN :orderDateStart = '%' then '1990-01-01 00:00:00' else :orderDateStart END) ");
		sb.append("AND (CASE WHEN :orderDateEnd = '%' then :currentDate else :orderDateEnd END) ");
		sb.append("AND COALESCE(trth.ASSIGN_DATE, GETDATE()) BETWEEN (")
			.append("CASE WHEN :surveyDateStart = '%' then '1990-01-01 00:00:00' else :surveyDateStart END) ");
		sb.append("AND (CASE WHEN :surveyDateEnd = '%' then :currentDate else :surveyDateEnd END) ");
		paramStack.push(new Object[] { "orderDateStart", (String) params[5][1] });
		paramStack.push(new Object[] { "orderDateEnd", (String) params[6][1] });
		paramStack.push(new Object[] { "currentDate", (String) params[11][1] });
		paramStack.push(new Object[] { "surveyDateStart", (String) params[7][1] });
		paramStack.push(new Object[] { "surveyDateEnd", (String) params[8][1] });
		return sb;
	}

	@Override
	public Integer countListTaskDO(Object params, String isBranch, AuditContext callerId) {
		Integer result = NumberUtils.INTEGER_ZERO;
		if ("1".equals(isBranch)) {
			Stack<Object[]> paramsStack = new Stack<>();
			StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
			StringBuilder queryBuilder = new StringBuilder()
					.append("SELECT COUNT(1) ")
					.append("from ")
					.append("(select trth.uuid_task_h ")
					.append("FROM TR_TASK_H trth with (nolock) left join MS_STATUSTASK msst with (nolock) ")
					.append("on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
					.append("left join TR_TASKORDERDATA trtod with (nolock) ")
					.append("on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
					.append("left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME ")
					.append("FROM dbo.getCabangByLogin(:uuidBranchDealer)) msb ")
					.append("on trth.UUID_BRANCH = msb.UUID_BRANCH ")
					.append("left join MS_DEALER msd with (nolock) ")
					.append("on trtod.DEALER_ID = msd.UUID_DEALER ")
					.append("left join AM_MSUSER ammsu with (nolock) ")
					.append("on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
					.append("left join AM_MSUSER ammsu2 with (nolock) ")
					.append("on ammsu2.UUID_MS_USER = trtod.CA_ID ")
					.append("WHERE msst.STATUS_CODE IN ('DLO', 'GLV') ")
					.append(paramsQueryString)
					.append("union all ")
					.append("select trth.uuid_task_h ")
					.append("FROM FINAL_TR_TASK_H trth with (nolock) left join MS_STATUSTASK msst with (nolock) ")
					.append("on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
					.append("left join TR_TASKORDERDATA trtod with (nolock) ")
					.append("on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
					.append("left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME ")
					.append("FROM dbo.getCabangByLogin(:uuidBranchDealer)) msb ")
					.append("on trth.UUID_BRANCH = msb.UUID_BRANCH ")
					.append("left join MS_DEALER msd with (nolock) ")
					.append("on trtod.DEALER_ID = msd.UUID_DEALER ")
					.append("left join AM_MSUSER ammsu with (nolock) ")
					.append("on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
					.append("left join AM_MSUSER ammsu2 with (nolock) ")
					.append("on ammsu2.UUID_MS_USER = trtod.CA_ID ")
					.append("WHERE msst.STATUS_CODE IN ('DLO', 'GLV') ")
					.append(paramsQueryString).append(") b ");
			paramsStack.push(new Object[] { "uuidBranchDealer", ((Object[][]) params)[10][1] });
			paramsStack.push(new Object[] { "uuidSubsystem", ((Object[][]) params)[9][1] });
			Object[][] sqlParams = new Object[paramsStack.size()][2];
			for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
			result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		} 
		else {
			Stack<Object[]> paramsStack = new Stack<>();
			StringBuilder paramsQueryStringCountDealer = this.sqlPagingBuilderCountDealer(
					(Object[][]) params, paramsStack);
			StringBuilder queryBuilder = new StringBuilder()
					.append("SELECT COUNT(1) ")
					.append("from ")
					.append("(select trth.uuid_task_h ")					
					.append("FROM TR_TASK_H trth with (nolock) left join MS_STATUSTASK msst with (nolock) ")
					.append("on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
					.append("left join TR_TASKORDERDATA trtod with (nolock) ")
					.append("on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
					.append("left join MS_BRANCH msb with (nolock)  ")
					.append("on trth.UUID_BRANCH = msb.UUID_BRANCH ")
					.append("left join (SELECT keyValue as UUID_DEALER, dealerName as DEALER_NAME ")
					.append("from dbo.getDealerByLogin(:uuidBranchDealer)) msd ")
					.append("on trtod.DEALER_ID = msd.UUID_DEALER ")
					.append("left join AM_MSUSER ammsu with (nolock) ")
					.append("on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
					.append("left join AM_MSUSER ammsu2 with (nolock)  ")
					.append("on ammsu2.UUID_MS_USER = trtod.CA_ID ")
					.append("WHERE msst.STATUS_CODE IN ('DLO', 'GLV') ")
					.append(paramsQueryStringCountDealer)
					.append("union all ")
					.append("select trth.uuid_task_h ")
					.append("FROM FINAL_TR_TASK_H trth with (nolock) left join MS_STATUSTASK msst with (nolock) ")
					.append("on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK ")
					.append("left join TR_TASKORDERDATA trtod with (nolock) ")
					.append("on trth.UUID_TASK_H = trtod.UUID_TASK_ID ")
					.append("left join MS_BRANCH msb with (nolock)  ")
					.append("on trth.UUID_BRANCH = msb.UUID_BRANCH ")
					.append("left join (SELECT keyValue as UUID_DEALER, dealerName as DEALER_NAME ")
					.append("from dbo.getDealerByLogin(:uuidBranchDealer)) msd ")
					.append("on trtod.DEALER_ID = msd.UUID_DEALER ")
					.append("left join AM_MSUSER ammsu with (nolock) ")
					.append("on ammsu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem ")
					.append("left join AM_MSUSER ammsu2 with (nolock)  ")
					.append("on ammsu2.UUID_MS_USER = trtod.CA_ID ")
					.append("WHERE msst.STATUS_CODE IN ('DLO', 'GLV') ")
					.append(paramsQueryStringCountDealer).append(") b ");
			paramsStack.push(new Object[] { "uuidSubsystem", ((Object[][]) params)[9][1] });
			paramsStack.push(new Object[] { "uuidBranchDealer", ((Object[][]) params)[10][1] });
			Object[][] sqlParams = new Object[paramsStack.size()][2];
			for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
			result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		}
		return result;
	}

	/* 0 "uuidBranch", */
	/* 1 "uuidDealer", */
	/* 2 "customerName", */
	/* 3 "orderNo", */
	/* 4 "salesName", */
	/* 5 "orderDateStart", */
	/* 6 "orderDateEnd", */
	/* 7 "surveyDateStart", */
	/* 8 "surveyDateEnd", */
	/* 9 "uuidSubsystem", */
	/* 10 "uuidBranchDealer", */
	/* 11 "currentDate", */
	private StringBuilder sqlPagingBuilderCountDealer(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		// ---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("msb.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[] { "uuidBranch", Long.valueOf((String) params[0][1]) });
		}

		// ---UUID_DEALER
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("AND COALESCE(msd.UUID_DEALER,'') = :uuidDealer ");
			paramStack.push(new Object[] { "uuidDealer", Long.valueOf((String) params[1][1]) });
		}

		// ---CUSTOMER_NAME
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("AND trth.CUSTOMER_NAME like UPPER('%'+ :customerName +'%') ");
			paramStack.push(new Object[] { "customerName", (String) params[2][1] });
		}

		// ---ORDER_NO
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append("AND trtod.ORDER_NO like UPPER('%'+ :orderNo +'%') ");
			paramStack.push(new Object[] { "orderNo", (String) params[3][1] });
		}

		// ---SALES_NAME
		if (!StringUtils.equals("%", (String) params[4][1])) {
			sb.append("AND ammsu.FULL_NAME like UPPER('%'+ :salesName +'%') ");
			paramStack.push(new Object[] { "salesName", (String) params[4][1] });
		}

		sb.append("AND trth.SUBMIT_DATE BETWEEN (CASE ")
			.append("WHEN :orderDateStart = '%' then '1990-01-01 00:00:00' else :orderDateStart END) ");
		sb.append("AND (CASE WHEN :orderDateEnd = '%' then :currentDate else :orderDateEnd END) ");
		sb.append("AND COALESCE(trth.ASSIGN_DATE, GETDATE()) BETWEEN (")
			.append("CASE WHEN :surveyDateStart = '%' then '1990-01-01 00:00:00' else :surveyDateStart END) ");
		sb.append("AND (CASE WHEN :surveyDateEnd = '%' then :currentDate else :surveyDateEnd END) ");
		paramStack.push(new Object[] { "orderDateStart", (String) params[5][1] });
		paramStack.push(new Object[] { "orderDateEnd", (String) params[6][1] });
		paramStack.push(new Object[] { "currentDate", (String) params[11][1] });
		paramStack.push(new Object[] { "surveyDateStart", (String) params[7][1] });
		paramStack.push(new Object[] { "surveyDateEnd", (String) params[8][1] });
		return sb;
	}

	@Override
	public byte[] getPrintPO(String orderNo, AuditContext callerId) {
		byte[] po = intFormLogic.requestPO(orderNo, callerId.getCallerId());
		return po;
	}
}
