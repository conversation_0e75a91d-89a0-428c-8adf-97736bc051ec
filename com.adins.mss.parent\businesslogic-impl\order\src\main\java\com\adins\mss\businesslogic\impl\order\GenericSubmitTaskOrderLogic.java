package com.adins.mss.businesslogic.impl.order;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.order.SubmitTaskOrderLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.exceptions.SubmitTaskException;
import com.adins.mss.exceptions.SubmitTaskException.Reason;
import com.adins.mss.exceptions.UserLoginIdNotExistsException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsMappingformD;
import com.adins.mss.model.MsMappingformH;
import com.adins.mss.model.MsOrdertag;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.MsTskdistributionofmodule;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.TrTasklink;
import com.adins.mss.model.TrTaskorderdata;
import com.adins.mss.model.TrTasksurveydata;
import com.adins.mss.model.custom.MappingFormBean;
import com.adins.mss.model.custom.SubmitTaskResultBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.ImageBean;
import com.adins.mss.model.taskdjson.LocationBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.QuestionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.common.SubmitTaskHBean;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

@SuppressWarnings({"rawtypes","unchecked"})
public class GenericSubmitTaskOrderLogic extends BaseLogic implements SubmitTaskOrderLogic, MessageSourceAware {	
	private static final Logger LOG = LoggerFactory.getLogger(GenericSubmitTaskOrderLogic.class);	
	private Gson gson = new Gson();
	
	@Autowired
	private CommonLogic commonLogic;
	
	@Autowired
	private TaskDistributionLogic taskDistributionLogic;

	private IntFormLogic intFormLogic;
	private MessageSource messageSource; 
    private ImageStorageLogic imageStorageLogic;
	private GeolocationLogic geocoder;
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public void setCommonLogic(CommonLogic commonLogic) {
        this.commonLogic = commonLogic;
    }

    public void setTaskDistributionLogic(TaskDistributionLogic taskDistributionLogic) {
		this.taskDistributionLogic = taskDistributionLogic;
	}
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    
    public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }
	
	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}

	@Transactional
	@Override
	public SubmitTaskResultBean submitTask(AuditContext auditContext, String application, SubmitTaskHBean taskHBean,
			SubmitTaskDBean[] taskDBean, String imei, String androidId) {
		String taskId = StringUtils.EMPTY;
		String uuidNewTaskH = null;
		String isFinal = StringUtils.EMPTY;
		boolean firstSend = true;
			
		AmMsuser usr = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msDealer join fetch u.amMssubsystem join fetch u.msJob join fetch u.msBranch where u.uuidMsUser = :uuidMsUser",
				new Object[][]{ {"uuidMsUser", Long.valueOf(taskHBean.getUuid_user())}});
		
		//cek mode dan validasi submit
		this.validateDeviceId(usr, imei, androidId, auditContext);		
		
		AmMssubsystem cekMsSubsystem = this.commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, true, auditContext);
		
		//cek mapping form
		Object[][] paramMap = { {Restrictions.eq("msForm.uuidForm", Long.valueOf(taskHBean.getUuid_scheme()))},
				{Restrictions.eq("isActive","1")}, {Restrictions.eq("formVersion", taskHBean.getFormVersion())} };
		MsMappingformH msMapFormH = this.getManagerDAO().selectOne(MsMappingformH.class, paramMap);		
		MsForm msForm = this.commonLogic.retrieveMsFormByUuid(Long.valueOf(taskHBean.getUuid_scheme()), auditContext);
		
		if (msMapFormH == null && cekMsSubsystem != null) {
			throw new SubmitTaskException(this.messageSource.getMessage(
					"businesslogic.submit.surveynomapping", new Object[]{msForm.getFormName()}, this.retrieveLocaleAudit(auditContext)),
					Reason.NO_ORDER_SURVEY_MAPPING);
		} //TODO Handling modul Survey aktif, tapi flownya ORDER-CONFINS-SURVEY 
		
		//START Insert into Table TR_TASK_H with return TaskId
		TrTaskH trTaskH = null;
		boolean saveNewTaskArchive = false;
		if (NumberUtils.isDigits(taskHBean.getUuid_task_h())) { //Data text dari mobile pertama kali uuidTaskH isinya GUID
			trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, Long.valueOf(taskHBean.getUuid_task_h()));
		}
		else { //NEW_ORDER 2018-02-06 penjagaan duplikat submit
			String uniqueTaskH = taskHBean.getUuid_task_h();
			Map<String, Object> resultMap = this.getManagerDAO().selectForMap("services.common.task.checkDuplicateTask",
					new Object[][]{{"uuidTaskHMobile", uniqueTaskH}});
			if (null != resultMap) {
				BigInteger existingTaskId = (BigInteger) resultMap.get("uuid");
				LOG.warn("Duplicate new order submit!!");
				uuidNewTaskH = existingTaskId.toString();
				taskId = uuidNewTaskH;
				return new SubmitTaskResultBean(taskId, uuidNewTaskH);
			}
			
			saveNewTaskArchive = true;
		}
		
		MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(Long.valueOf(taskHBean.getUuid_scheme()),
				taskHBean.getFormVersion(), auditContext);
		Map<Integer, MsQuestion> msQuestions = this.getAllMsQuestion(taskDBean, formHist, auditContext);
		String mhId = StringUtils.EMPTY;
		
		Object[][] paramsBranchHO = {{Restrictions.eq("branchName", GlobalVal.BRANCH_HO)}};
		MsBranch msBranchHO = this.getManagerDAO().selectOne(MsBranch.class, paramsBranchHO);
					
		if (trTaskH == null) {//New Order
			//Save data customer
			trTaskH = new TrTaskH();			
			trTaskH.setUsrCrt(auditContext.getCallerId());
			trTaskH.setDtmCrt(new Date());
			trTaskH.setFormVersion(taskHBean.getFormVersion());
			
			MsBranch msBranch = this.getBranch(msQuestions, taskDBean);// branch
			if (cekMsSubsystem != null) {
				//get mhid dan branch
				mhId = this.getMhId(msQuestions, taskDBean);//UUID mh
				AmMsuser usrMh = null; 
				if (!StringUtils.isBlank(mhId)) {
					usrMh = this.getManagerDAO().selectOne("from AmMsuser u join fetch u.msBranch where u.uuidMsUser=:uuidMsUser",
							new Object[][]{ {"uuidMsUser", Long.valueOf(mhId)} });
				}
				
				if (null == msBranch) {
					if (null == usrMh) {
						if ("1".equals(usr.getMsJob().getIsBranch())) { //CMO MultiFinance
							trTaskH.setMsBranch(usr.getMsBranch());
						} 
						else { //Salesman Dealer
							trTaskH.setMsBranch(msBranchHO);
						}
					}
					else {
						trTaskH.setMsBranch(usrMh.getMsBranch());
					}
				} 
				else {
					if (null == usrMh) {
						trTaskH.setMsBranch(msBranch);
					} 
					else {
						trTaskH.setMsBranch(usrMh.getMsBranch());
					}
				}
			} 
			else {
				if (msBranch == null) {
					throw new UserLoginIdNotExistsException("Branch is not valid");
				}
				trTaskH.setMsBranch(msBranch);
			}
			
			trTaskH.setAmMsuser(usr);
			trTaskH.setMsForm(msForm);
			
			//Priorty
			MsPriority msPriorty = this.commonLogic.retrieveMsPriority(GlobalVal.TAKS_PRIORTY_NORMAL, true, auditContext);
			trTaskH.setMsPriority(msPriorty);
								
			trTaskH.setZipCode(this.getZipCode(msQuestions, taskDBean));
			trTaskH.setSubmitDate(new Date());
			trTaskH.setSendDate(taskHBean.getSubmitDate()); //mobile submit time
			trTaskH.setCustomerName(taskHBean.getCustomer_name());
			trTaskH.setCustomerPhone(taskHBean.getCustomer_phone());
			trTaskH.setCustomerAddress(taskHBean.getCustomer_address());
			trTaskH.setNotes(taskHBean.getNotes());
			trTaskH.setIsDraft("0");
			if (cekMsSubsystem != null) {
				trTaskH.setFlagSource(GlobalVal.SUBSYSTEM_MO);
			}
			else {
				trTaskH.setFlagSource(GlobalVal.SUBSYSTEM_SMO);
			}
			trTaskH.setIsAppNotified("0");
			

			//insert tr taskH
			this.getManagerDAO().insert(trTaskH);
			uuidNewTaskH = String.valueOf(trTaskH.getUuidTaskH());
			trTaskH.setTaskId(uuidNewTaskH);
			taskId = uuidNewTaskH;
			
			//Set trTask order data
			TrTaskorderdata trTaskOrderData = new TrTaskorderdata();
			trTaskOrderData.setUuidTaskId(trTaskH.getUuidTaskH());
			trTaskOrderData.setTrTaskH(trTaskH);
			trTaskOrderData.setUsrCrt(auditContext.getCallerId());
			trTaskOrderData.setDtmCrt(new Date());
			
			//cek apabila yang submit user cabang, maka dealerId diambil dari jawaban pertanyaan tagging SUPPLIER
			if ("1".equals(usr.getMsJob().getIsBranch())) { //CMO MultiFinance, dealer value dari jawaban form
				String uuidDealer = this.getDealerId(msQuestions, taskDBean);
				trTaskOrderData.setDealerId(Long.valueOf(uuidDealer));
			} else { //Salesman Dealer, dealer value dari atribute user
				trTaskOrderData.setDealerId(usr.getMsDealer().getUuidDealer());
			}
			
			if (mhId != null && !"".equals(mhId)) {
				trTaskOrderData.setMhId(Long.valueOf(mhId));
			}
			trTaskOrderData.setOrderNo(this.commonLogic.retrieveNewTaskId(GlobalVal.SEQUENCE_ORDER_NO_CODE));
			//insert tr task order data
			this.getManagerDAO().insert(trTaskOrderData);					
		
			if (saveNewTaskArchive) {
				Map<String, Object> paramNewTaskArchive = new HashMap<>();
				paramNewTaskArchive.put("uuidTaskHMobile", taskHBean.getUuid_task_h());
				paramNewTaskArchive.put("uuidTaskHWeb", uuidNewTaskH);
				paramNewTaskArchive.put("callerId", auditContext.getCallerId());
				this.getManagerDAO().insertNative("services.common.task.insertNewTaskArchive", paramNewTaskArchive);
			}
		} 
		else { //Uploading Image
			trTaskH.setUsrUpd(auditContext.getCallerId());
			trTaskH.setDtmUpd(new Date());
			taskId = String.valueOf(trTaskH.getTaskId());
			firstSend = false;
		}
		
        ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(auditContext);
        Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
                ? this.imageStorageLogic.retrieveGsImageFileSystemPath(auditContext) : null;
		
		boolean saveAsJson = PropertiesHelper.isTaskDJson();
		if (saveAsJson) {
			isFinal = this.saveTaskDIntoJson(taskDBean, trTaskH, msQuestions, isl, imagePath, auditContext);
		}
		else {
			//2016-09-15 SUM - select all taskD first instead of select one for all answers
			Map<Long, TrTaskD> listTaskD = this.listTaskDAsMap(String.valueOf(trTaskH.getUuidTaskH()));
			isFinal = this.saveTaskDIntoRow(taskDBean, trTaskH, msQuestions, listTaskD, isl, imagePath, auditContext);
		}
		//finish insert task order
		
		//commit status task and insert history
		long uuidProcess = this.getUuidProcess(trTaskH, usr.getAmMssubsystem());
						
		// 2017-06-06 SUM Validasi untuk status Order yang sudah terkirim is_final=1 sebelumnya 
		if (!firstSend) {
			List<String> statusCodesAfterUpload = this.commonLogic.retrieveWfNextStatusCodes(uuidProcess, GlobalVal.ORDER_STATUS_TASK_UPLOADING);
			if (statusCodesAfterUpload.contains(trTaskH.getMsStatustask().getStatusCode())) {
				return new SubmitTaskResultBean(String.valueOf(trTaskH.getTaskId()), null);
			}
		}
		
		if (firstSend && "0".equals(isFinal) ) {
			//commit workflow & update status taskH
			MsStatusmobile msm = this.commonLogic.retrieveStatusMobile(GlobalVal.STATUS_MOBILE_UPLOADING, auditContext);
			trTaskH.setMsStatusmobile(msm);
			commitWfAndUpdateTaskH(trTaskH, uuidProcess, usr.getAmMssubsystem(), 0);
		} 
		else if (firstSend && "1".equals(isFinal)) {
			//Create task survey if mapping found		
			if (cekMsSubsystem != null) {
				this.createTaskSurvey(auditContext, usr, trTaskH, cekMsSubsystem, msMapFormH, msBranchHO);
			}
			
			//commit workflow & update status taskH
			MsStatustask msStatustask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, usr.getAmMssubsystem(), 0);
			
			//commit workflow & update status taskH
			Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_SUBMITTED) } };
			MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
			trTaskH.setMsStatusmobile(msm);
			msStatustask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, usr.getAmMssubsystem(), 0);
			
			//insert into tr_taskHistory
			insertTaskHistory(auditContext, msStatustask, trTaskH, "Task from " + trTaskH.getFlagSource() + " has been submitted.",
					GlobalVal.CODE_PROCESS_SUBMITTED, getFullNameById(taskHBean.getUuid_user()), usr, null);
		} 
		else if (!firstSend && "1".equals(isFinal)) {
			//penambahan count untuk menandai bahwa image tersebut sudah
			//pernah dikirim atau belum, 1 untuk sudah
//			if (!count.equals("1")) { //2017-06-06 SUM Validasi dilepas (AITMSSPRD-536, Mobile selesai uploading, tapi status di server masih Uploading)  
				//Create task survey if mapping found	
				if (cekMsSubsystem != null) {
					this.createTaskSurvey(auditContext, usr, trTaskH, cekMsSubsystem, msMapFormH, msBranchHO);
				}
				
				//commit workflow & update status taskH
				Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_SUBMITTED) } };
				MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
				trTaskH.setMsStatusmobile(msm);
				MsStatustask msStatustask = commitWfAndUpdateTaskH(trTaskH, uuidProcess, usr.getAmMssubsystem(), 0);
				
				//insert into tr_taskHistory
				insertTaskHistory(auditContext, msStatustask, trTaskH, "Task from " + trTaskH.getFlagSource() + " has been submitted.",
						GlobalVal.CODE_PROCESS_SUBMITTED, getFullNameById(taskHBean.getUuid_user()), usr, null);
//			}
		}		
		
		//06-10-2015 Submit Task By TaskId
		intFormLogic.submitResult(auditContext, taskId, isFinal);
		return new SubmitTaskResultBean(taskId, uuidNewTaskH);
	}
	
	public void createTaskSurvey(AuditContext auditContext, AmMsuser userSubmit, TrTaskH trTaskHOrder,
			AmMssubsystem subsystemMs, MsMappingformH msMapFormH, MsBranch msBranchHO) {
		Object queryParams[][] = { {Restrictions.eq("msMappingformH.uuidMappingFormH", msMapFormH.getUuidMappingFormH())} };
		MsMappingformD bean = this.getManagerDAO().selectOne(MsMappingformD.class, queryParams);		
		if (bean == null) {
			return;
		}
		
		Type collectionType = new TypeToken<Collection<MappingFormBean>>(){}.getType();
		List<MappingFormBean> listFormQuestion = gson.fromJson(bean.getQuestionMapping(), collectionType);
		if (listFormQuestion == null || listFormQuestion.isEmpty()) {
			return;
		}
		
		SubmitTaskDBean[] taskDBeanOrder = null;
		boolean saveAsJson = PropertiesHelper.isTaskDJson();
		if (saveAsJson) {
			taskDBeanOrder = this.getSubmitTaskDBeanFromJson(trTaskHOrder);
		}
		else {
			taskDBeanOrder = this.getSubmitTaskDBean(trTaskHOrder.getUuidTaskH()); //not use passed in argument.. (AITMSSPRD-239)
		}
			
		MsPriority normalPriority = this.commonLogic.retrieveMsPriority(GlobalVal.TAKS_PRIORTY_NORMAL, true, auditContext);
		MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
				trTaskHOrder.getMsForm().getUuidForm(), trTaskHOrder.getFormVersion(), auditContext);
		
		Map<Integer, MsQuestion> msQuestions = this.getAllMsQuestion(taskDBeanOrder, formHist, auditContext);
		MsBranch msBranchMfSelected = this.getBranch(msQuestions, taskDBeanOrder);
		String mhId = this.getMhId(msQuestions, taskDBeanOrder);
		String ptsDate = this.getPtsDate(msQuestions, taskDBeanOrder);
		String locationSurvey = this.getLocationSurvey(msQuestions, taskDBeanOrder);
		
		BigDecimal latHeader = null;
		BigDecimal longiHeader = null;
		if (StringUtils.isNotEmpty(locationSurvey)){
			latHeader = BigDecimal.valueOf(Double.valueOf(locationSurvey.split(";")[0]));
			longiHeader = BigDecimal.valueOf(Double.valueOf(locationSurvey.split(";")[1]));
		}
		
		AmMsuser userMh = null;
		if (mhId != null && !"".equals(mhId)) {
			userMh = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(mhId));
		}
					
		List<TrTaskH> listTaskDist = new ArrayList<TrTaskH>();
		for (int j = 0; j < listFormQuestion.size(); j++) {
			MappingFormBean formQuestion = listFormQuestion.get(j);

			TrTaskH taskHSurvey = this.saveTaskHSurvey(userSubmit, trTaskHOrder, latHeader, longiHeader,
					normalPriority, msBranchHO, userMh, msBranchMfSelected, ptsDate, formQuestion, auditContext);
			
			this.saveTaskLink(trTaskHOrder.getUuidTaskH(), taskHSurvey.getUuidTaskH(), auditContext);
			
			this.saveGroupTaskSurvey(taskHSurvey, auditContext);

            ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(auditContext);
            Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
                    ? this.imageStorageLogic.retrieveGsImageFileSystemPath(auditContext) : null;
			
			//insert into tr_taskd and tr_taskdetaillob base on mapping to data default
			List<MappingFormBean> listQuestion = formQuestion.getListQuestionMapping();
			if (saveAsJson) {
				this.saveTaskDSurveyIntoJson(taskHSurvey, listQuestion, msQuestions, taskDBeanOrder, isl, imagePath, auditContext);
			}
			else {
				this.saveTaskDSurveyIntoRow(taskHSurvey, listQuestion, msQuestions, taskDBeanOrder, isl, imagePath, auditContext);
			}
			listTaskDist.add(taskHSurvey);

			long uuidProcess = this.getUuidProcess(taskHSurvey, subsystemMs);
			MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(taskHSurvey, uuidProcess, subsystemMs, 0);
			
			//insert into tr_taskHistory
			AmMsuser mos = new AmMsuser();
			mos.setFullName("MOS");
			this.insertTaskHistory(auditContext, msStatustaskSurvey, taskHSurvey, "MOS", GlobalVal.CODE_PROCESS_UNASSIGNED, null, mos, null);
		}
		
		if (userSubmit == null || StringUtils.isNotBlank(ptsDate)) {
			return;
		}
				
		//cek task distribution berdasarkan subsystem
		//distribution berdasarkan type round robin, low task, dan zipcode
		
		if ("1".equals(userSubmit.getMsJob().getIsSelfAssignment())) {
			Object[][] prmUser={ {Restrictions.eq("loginId", userSubmit.getLoginId().substring(0, userSubmit.getLoginId().length()-1))} };
			AmMsuser userCMS = this.getManagerDAO().selectOne(AmMsuser.class, prmUser);
			Map<String, Object> result = taskDistributionLogic.distributeToCMS(listTaskDist, subsystemMs, userCMS, auditContext);
			if (null != result) {
				if (null != userCMS) {
					TrTaskH taskbean = (TrTaskH) result.get("taskH");
					commitOrder(userCMS, taskbean.getNotes(), taskbean, subsystemMs, 0, GlobalVal.CODE_PROCESS_ASSIGNMENT, auditContext);
				}
			}
		}
		else if ("1".equals(userSubmit.getMsJob().getIsAutomaticDistribution())) {
			Object[][] prm={ {"uuidMsSubsystem", subsystemMs.getUuidMsSubsystem()} };
			MsTskdistributionofmodule msTaskDistModul =  this.getManagerDAO().selectOne(
					"from MsTskdistributionofmodule tdm join fetch tdm.msTaskdistribution where tdm.amMssubsystem.uuidMsSubsystem=:uuidMsSubsystem", prm);
			if (msTaskDistModul == null ||
					GlobalVal.MODULE_TASK_DISTRIBUTION_OFF.equals(msTaskDistModul.getMsTaskdistribution().getCode())) {
				return;
			}
						
			TrTaskorderdata trTaskOrderData = this.getManagerDAO().selectOne(TrTaskorderdata.class, trTaskHOrder.getUuidTaskH());
			Long uuidMh = trTaskOrderData.getMhId();
			if (uuidMh == null){
				return;
			}
			
			AmMsuser beanMh = this.getManagerDAO().selectOne(AmMsuser.class, uuidMh);			
			String taskDistCode = msTaskDistModul.getMsTaskdistribution().getCode();
					
			Map<String, Object> result = null;
			if (GlobalVal.MODULE_TASK_DISTRIBUTION_ROUND_ROBIN.equals(taskDistCode)){
				result = taskDistributionLogic.distributeRoundRobin(listTaskDist, subsystemMs, String.valueOf(uuidMh), null, null, auditContext);
			} 
			else if (GlobalVal.MODULE_TASK_DISTRIBUTION_LOW_TASK.equals(taskDistCode)){
				result = taskDistributionLogic.distributeLowTask(listTaskDist, subsystemMs, String.valueOf(uuidMh), null, null, auditContext);
			} 
			else if (GlobalVal.MODULE_TASK_DISTRIBUTION_ZIP_CODE.equals(taskDistCode)){
				result = taskDistributionLogic.distributeZipCode(listTaskDist, subsystemMs, trTaskHOrder.getZipCode(), String.valueOf(uuidMh), null, null, auditContext);
			} 
			else if (GlobalVal.MODULE_TASK_DISTRIBUTION_GEOFENCING.equals(taskDistCode)){
				result = taskDistributionLogic.distributeGeofencing(listTaskDist, subsystemMs, locationSurvey, String.valueOf(uuidMh), null, null, auditContext);
			}
			if (null != result && !result.isEmpty()) {
				AmMsuser amMsuser = (AmMsuser) result.get("userAssign");
				if (null != amMsuser) {
					TrTaskH taskbean = (TrTaskH) result.get("taskH");
					commitOrder(beanMh, taskbean.getNotes(), taskbean, subsystemMs, 0, GlobalVal.CODE_PROCESS_ASSIGNMENT, auditContext);
				}
			}			
		}
	}
	
	private SubmitTaskDBean[] getSubmitTaskDBeanFromJson(TrTaskH taskHOrder) {
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", taskHOrder.getUuidTaskH()}});
		if (docDb == null){
			return new SubmitTaskDBean[0];
		}
		
		String jsonDocument = docDb.getDocument();
		TaskDocumentBean tdb = gson.fromJson(jsonDocument, TaskDocumentBean.class);
		List<AnswerBean> answers = tdb.getAnswers();
		
		List<SubmitTaskDBean> taskDs = new ArrayList<SubmitTaskDBean>(answers.size());
		
		for (Iterator iterator = answers.iterator(); iterator.hasNext();) {
			AnswerBean answerBean = (AnswerBean) iterator.next();
			if (answerBean.getLobAnswer() != null) {
				continue;
			}
			
			SubmitTaskDBean taskD = new SubmitTaskDBean();
			taskD.setQuestion_id(String.valueOf(answerBean.getQuestion().getUuidQuestion()));
			taskD.setQuestion_label(answerBean.getQuestion().getLabel());
			taskD.setText_answer(StringUtils.stripToEmpty(answerBean.getTxtAnswer()));
			
			if (answerBean.getLocation() != null) {
				LocationBean location = answerBean.getLocation();
				if (location.getLat() != null) {
					taskD.setLatitude(location.getLat().toString());
				}
				if (location.getLng() != null) {
					taskD.setLongitude(location.getLng().toString());
				}
				if (location.getAccuracy() != null) {
					taskD.setAccuracy(location.getAccuracy().toString());
				}
				taskD.setMcc(String.valueOf(location.getMcc()));
				taskD.setMnc(String.valueOf(location.getMnc()));
				taskD.setLac(String.valueOf(location.getLac()));
				taskD.setCid(String.valueOf(location.getCid()));
			}
			
			if (answerBean.getOptAnswers() != null) {
				List<OptionBean> options = answerBean.getOptAnswers();
				
				int ctr = 0;
				for (Iterator iterator2 = options.iterator(); iterator2.hasNext();) {
					OptionBean optionBean = (OptionBean) iterator2.next();
					
					if (ctr > 0) {						
						try {
							SubmitTaskDBean clone = (SubmitTaskDBean) BeanUtils.cloneBean(taskD);
							clone.setOption_answer_id(String.valueOf(optionBean.getUuid()));
							taskDs.add(clone);
						} 
						catch (IllegalAccessException | InstantiationException | InvocationTargetException | NoSuchMethodException e) {
							LOG.error("Error on cloning taskD", e);
						}
					} 
					else {
						taskD.setOption_answer_id(String.valueOf(optionBean.getUuid()));
						taskDs.add(taskD);
					}
					
					ctr++;
				}
			}
			else {
				taskDs.add(taskD);
			}			
		}
		
		Object[][] paramsTask = {{Restrictions.eq("trTaskH.uuidTaskH", taskHOrder.getUuidTaskH())}};
		Map resultTaskDL = this.getManagerDAO().list(TrTaskdetaillob.class, paramsTask, null);
		List <TrTaskdetaillob> listTaskDL = (List) resultTaskDL.get(GlobalKey.MAP_RESULT_LIST);
		for (TrTaskdetaillob bean : listTaskDL) {
			SubmitTaskDBean temp = new SubmitTaskDBean();
			temp.setQuestion_id(String.valueOf(bean.getMsQuestion().getUuidQuestion()));
			temp.setQuestion_label(bean.getQuestionText());
			
			if (bean.getLatitude() != null) {
				temp.setLatitude(bean.getLatitude().toString());
			}
			
			if (bean.getLongitude() != null) {
				temp.setLongitude(bean.getLongitude().toString());
			}
			
			if (bean.getCellId() != null) {
				temp.setCid(bean.getCellId().toString());
			}
			
			if (bean.getMcc() != null) {
				temp.setMcc(bean.getMcc().toString());
			}
			
			if (bean.getMnc() != null) {
				temp.setMnc(bean.getMnc().toString());
			}
			
			if (bean.getLac() != null) {
				temp.setLac(bean.getLac().toString());
			}
			
			if (bean.getAccuracy()!= null) {
				temp.setAccuracy(bean.getAccuracy().toString());
			}
			if (bean.getLobFile()!=null) {
				temp.setImage(BaseEncoding.base64().encode(bean.getLobFile()));
			}
			taskDs.add(temp);
		}
		SubmitTaskDBean[] result = new SubmitTaskDBean[taskDs.size()];
		return taskDs.toArray(result);
	}
	
	private SubmitTaskDBean[] getSubmitTaskDBean(long uuidTaskH) {
		Object[][] paramsTask = {{Restrictions.eq("trTaskH.uuidTaskH", uuidTaskH)}};
		Map resultTaskD = this.getManagerDAO().list(
				"from TrTaskD ttd left join fetch ttd.msLovByLovId where ttd.trTaskH.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", uuidTaskH}});
		List <TrTaskD> listTaskD = (List) resultTaskD.get(GlobalKey.MAP_RESULT_LIST);
		
		Map resultTaskDL = this.getManagerDAO().list(TrTaskdetaillob.class, paramsTask, null);
		List <TrTaskdetaillob> listTaskDL = (List) resultTaskDL.get(GlobalKey.MAP_RESULT_LIST);
		
		SubmitTaskDBean[] taskDs = new SubmitTaskDBean[listTaskD.size()+listTaskDL.size()];
		int idx = 0;
		for (TrTaskD bean : listTaskD){
			SubmitTaskDBean temp = new SubmitTaskDBean();
			temp.setQuestion_id(String.valueOf(bean.getMsQuestion().getUuidQuestion()));
			temp.setQuestion_label(bean.getQuestionText());
			
			if (bean.getMsLovByLovId() != null) {
				temp.setOption_answer_id(String.valueOf(bean.getMsLovByLovId().getUuidLov()));
			}
			if (StringUtils.isNotBlank(bean.getTextAnswer()) || StringUtils.isNotEmpty(bean.getTextAnswer())) {
				temp.setText_answer(bean.getTextAnswer());
			} 
			else {
				temp.setText_answer("");
			}
			
			if (bean.getLatitude() != null) {
				temp.setLatitude(bean.getLatitude().toString());
			}
			
			if (bean.getLongitude() != null) {
				temp.setLongitude(bean.getLongitude().toString());
			}
			
			if (bean.getCellId() != null) {
				temp.setCid(bean.getCellId().toString());
			}
			
			if (bean.getMcc() != null) {
				temp.setMcc(bean.getMcc().toString());
			}
			
			if (bean.getMnc() != null) {
				temp.setMnc(bean.getMnc().toString());
			}
			
			if (bean.getLac() != null) {
				temp.setLac(bean.getLac().toString());
			}
			
			if (bean.getAccuracy()!= null) {
				temp.setAccuracy(bean.getAccuracy().toString());
			}
			
			taskDs[idx] = temp;
			idx++;
		}
		for (TrTaskdetaillob bean : listTaskDL) {	
			SubmitTaskDBean temp = new SubmitTaskDBean();
			temp.setQuestion_id(String.valueOf(bean.getMsQuestion().getUuidQuestion()));
			temp.setQuestion_label(bean.getQuestionText());
			
			if (bean.getLatitude() != null) {
				temp.setLatitude(bean.getLatitude().toString());
			}
			
			if (bean.getLongitude() != null) {
				temp.setLongitude(bean.getLongitude().toString());
			}
			
			if (bean.getCellId() != null) {
				temp.setCid(bean.getCellId().toString());
			}
			
			if (bean.getMcc() != null) {
				temp.setMcc(bean.getMcc().toString());
			}
			
			if (bean.getMnc() != null) {
				temp.setMnc(bean.getMnc().toString());
			}
			
			if (bean.getLac() != null) {
				temp.setLac(bean.getLac().toString());
			}
			
			if (bean.getAccuracy()!= null) {
				temp.setAccuracy(bean.getAccuracy().toString());
			}
			if (bean.getLobFile()!=null) {
				temp.setImage(BaseEncoding.base64().encode(bean.getLobFile()));
			}
			taskDs[idx] = temp;
			idx++;
		}
		return taskDs;
	}
	
	private void insertTaskD(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String questionText,
			String optionAnswerId, String textAnswer, String latitude, String longitude,
			String mcc, String mnc, String lac, String cellId, String accuracy, Map<Long, TrTaskD> listTaskD){
		
		MsLov msLovByLovId =  null;
		TrTaskD taskD = null;
		if (!GlobalVal.ANSWER_TYPE_MULTIPLE.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			taskD = listTaskD.get(msQuestion.getUuidQuestion());
		}
		if (StringUtils.isNotBlank(optionAnswerId)) {
			Object[][] queryParamsMh = {{Restrictions.eq("tagName", GlobalVal.ORDER_TAG_JOB_MH)}};
			MsOrdertag msOrderTagMh = this.getManagerDAO().selectOne(MsOrdertag.class, queryParamsMh);
			
			Object[][] queryParamsZc = {{Restrictions.eq("tagName", GlobalVal.ORDER_TAG_ZIP_CODE)}};
			MsOrdertag msOrderTagZc = this.getManagerDAO().selectOne(MsOrdertag.class, queryParamsZc);
			
			Object[][] queryParamsMfB = {{Restrictions.eq("tagName", GlobalVal.ORDER_TAG_MF_BRANCH)}};
			MsOrdertag msOrderTagMfB = this.getManagerDAO().selectOne(MsOrdertag.class, queryParamsMfB);
			
			Object[][] queryParamsDealer = {{Restrictions.eq("tagName", GlobalVal.ORDER_TAG_DEALER_NAME)}};
			MsOrdertag msOrderTagDealer = this.getManagerDAO().selectOne(MsOrdertag.class, queryParamsDealer);
			
			if (null != msQuestion.getMsOrdertag()) {
				if (String.valueOf(msOrderTagMh.getUuidOrderTag()).equals(String.valueOf(msQuestion.getMsOrdertag().getUuidOrderTag()))) {
					Object[][] paramsLov = {{Restrictions.eq("code",optionAnswerId)}, {Restrictions.eq("lovGroup", "LOOKUPMH")}};
					msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, paramsLov);
				} 
				else if (String.valueOf(msOrderTagZc.getUuidOrderTag()).equals(String.valueOf(msQuestion.getMsOrdertag().getUuidOrderTag()))) {
					msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
				} 
				else if (String.valueOf(msOrderTagMfB.getUuidOrderTag()).equals(String.valueOf(msQuestion.getMsOrdertag().getUuidOrderTag()))) {
					msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
				} 
				else if (String.valueOf(msOrderTagDealer.getUuidOrderTag()).equals(String.valueOf(msQuestion.getMsOrdertag().getUuidOrderTag()))) {
					msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
				}
			} 
			else {
				msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
			}
		}
		
		//begin formatting date
		if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())
				|| GlobalVal.ANSWER_TYPE_DATETIME.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			if (textAnswer != null) {
				textAnswer = this.dateAnswerToText(textAnswer, msQuestion.getMsAnswertype().getCodeAnswerType());
			}
		}
		//end formatting date
		
		if (null != taskD) {
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setDtmUpd(new Date());
			if (msLovByLovId != null) {
				taskD.setMsLovByLovId(msLovByLovId);
				taskD.setOptionText(msLovByLovId.getDescription());
			}
			taskD.setQuestionText(questionText);
			taskD.setTextAnswer(textAnswer);
			taskD.setLatitude(checkEmptyBigdecimal(latitude) );
			taskD.setLongitude(checkEmptyBigdecimal(longitude) );
			if (taskD.getLatitude()!=null && taskD.getLongitude()!=null &&
					taskD.getLatitude().intValue() != 0 && taskD.getLongitude().intValue() != 0){
				taskD.setIsGps("1");
			}
			taskD.setMcc(checkEmptyInteger(mcc));
			taskD.setMnc(checkEmptyInteger(mnc));
			taskD.setLac(checkEmptyInteger(lac));
			taskD.setCellId(checkEmptyInteger(cellId));
			taskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(taskD.getIsGps()) && taskD.getMcc() != null && taskD.getMnc() != null
					&& taskD.getLac() != null && taskD.getCellId() != null) {
				this.getLocationByCellId(taskD, auditContext);
			}
			this.getManagerDAO().update(taskD);
		} 
		else {
			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			trTaskD.setTrTaskH(trTaskH);
			trTaskD.setMsQuestion(msQuestion);
			trTaskD.setQuestionText(questionText);
			if (msLovByLovId != null) {
				trTaskD.setMsLovByLovId(msLovByLovId);
				trTaskD.setOptionText(msLovByLovId.getDescription());
			}
			trTaskD.setTextAnswer(textAnswer);
			trTaskD.setLatitude(checkEmptyBigdecimal(latitude) );
			trTaskD.setLongitude(checkEmptyBigdecimal(longitude) );
			if (trTaskD.getLatitude()!=null && trTaskD.getLongitude()!=null &&
					trTaskD.getLatitude().intValue() != 0 && trTaskD.getLongitude().intValue() != 0){
				trTaskD.setIsGps("1");
			}
			trTaskD.setMcc(checkEmptyInteger(mcc));
			trTaskD.setMnc(checkEmptyInteger(mnc));
			trTaskD.setLac(checkEmptyInteger(lac));
			trTaskD.setCellId(checkEmptyInteger(cellId));
			trTaskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(trTaskD.getIsGps()) && trTaskD.getMcc() != null && trTaskD.getMnc() != null
					&& trTaskD.getLac() != null && trTaskD.getCellId() != null) {
				this.getLocationByCellId(trTaskD, auditContext);
			}
			this.getManagerDAO().insert(trTaskD);
			
		}
	}
	
	private long insertDetailLob(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String questionText,
			String base64Image, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Boolean isGps, Boolean isConverted, ImageStorageLocation saveImgLoc, Path imgLoc, boolean checkExisting){
		
		if (checkExisting) {
			Object[][] paramTaskD = {
				{Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
				{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())}
			};
			TrTaskdetaillob bean = (TrTaskdetaillob) this.getManagerDAO().selectOne(TrTaskdetaillob.class, paramTaskD);
	
			if (bean != null) {
				return 0L;
			}
		}
		
		TrTaskdetaillob trTaskDetailLob = new TrTaskdetaillob();
		trTaskDetailLob.setUsrCrt(auditContext.getCallerId());
		trTaskDetailLob.setDtmCrt(new Date());	
		trTaskDetailLob.setTrTaskH(trTaskH);
		trTaskDetailLob.setMsQuestion(msQuestion);
		
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				trTaskDetailLob.setLobFile(BaseEncoding.base64().decode(base64Image));
			}
		}
        else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
            if (base64Image != null && !base64Image.isEmpty()) {
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date date = new Date();
                Path outputPath = Paths.get(imgLoc.toString(), dateFormat.format(date));

                String absoluteFilename = trTaskH.getTaskId() + "_" + msQuestion.getRefId() + "_"
                        + uuidTaskD + ".jpg";
                
                String outputFile = this.imageStorageLogic.storeImageFileSystem(
                        BaseEncoding.base64().decode(base64Image), outputPath, absoluteFilename);
                trTaskDetailLob.setImagePath(outputFile);
            }
        }
		trTaskDetailLob.setQuestionText(questionText);
		trTaskDetailLob.setLatitude(checkEmptyBigdecimal(latitude));
		trTaskDetailLob.setLongitude(checkEmptyBigdecimal(longitude));
		
		if (Boolean.TRUE.equals(isGps) || (trTaskDetailLob.getLatitude()!=null && trTaskDetailLob.getLongitude()!=null &&
				trTaskDetailLob.getLatitude().intValue()!=0 && trTaskDetailLob.getLongitude().intValue()!=0) &&
				!Boolean.TRUE.equals(isConverted)) {
			trTaskDetailLob.setIsGps("1");
		}
		else if (Boolean.FALSE.equals(isGps)) {
			trTaskDetailLob.setIsGps("0");
		}
		trTaskDetailLob.setMcc(checkEmptyInteger(mcc));
		trTaskDetailLob.setMnc(checkEmptyInteger(mnc));
		trTaskDetailLob.setLac(checkEmptyInteger(lac));
		trTaskDetailLob.setCellId(checkEmptyInteger(cellId));
		trTaskDetailLob.setAccuracy(checkEmptyInteger(accuracy));
		if (!Boolean.TRUE.equals(isConverted) && !"1".equals(trTaskDetailLob.getIsGps()) && trTaskDetailLob.getMcc() != null && trTaskDetailLob.getMnc() != null
				&& trTaskDetailLob.getLac() != null && trTaskDetailLob.getCellId() != null) {
			this.getLocationByCellId(trTaskDetailLob, auditContext);
		}
		else if (Boolean.TRUE.equals(isConverted)) {
			trTaskDetailLob.setIsConverted("1");
		}
		this.getManagerDAO().insert(trTaskDetailLob);
		
		return trTaskDetailLob.getUuidTaskDetailLob();
	}
	
	private void insertTaskDSurvey(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, 
			String optionAnswerId, String textAnswer, String latitude, String longitude,
			String mcc, String mnc, String lac, String cellId, String accuracy) {
		MsLov msLovByLovId =  null;
		TrTaskD taskD = null;
		if (!GlobalVal.ANSWER_TYPE_MULTIPLE.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) ||
				!GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) ||
				!GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			Object[][] params = {
					{Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
					{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())}
				};
			taskD = this.getManagerDAO().selectOne(TrTaskD.class, params);
		} 
		else {
			String[][] paramsDel = {
					{"uuidTaskH", String.valueOf(trTaskH.getUuidTaskH())},
					{"uuidQuestion", String.valueOf(msQuestion.getUuidQuestion())},
					{"uuidForm", String.valueOf(trTaskH.getMsForm().getUuidForm())}
				};
			this.getManagerDAO().deleteNativeString("delete from tr_task_d where uuid_task_h = :uuidTaskH and uuid_question = :uuidQuestion and uuid_form = :uuidForm", paramsDel);
		}
		if (null != taskD) {
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setDtmUpd(new Date());
			taskD.setIntTextAnswer(textAnswer);
			taskD.setLatitude(checkEmptyBigdecimal(latitude));
			taskD.setLongitude(checkEmptyBigdecimal(longitude));
			taskD.setMcc(checkEmptyInteger(mcc));
			taskD.setMnc(checkEmptyInteger(mnc));
			taskD.setLac(checkEmptyInteger(lac));
			taskD.setCellId(checkEmptyInteger(cellId));
			taskD.setAccuracy(checkEmptyInteger(accuracy));
			
			this.getManagerDAO().update(taskD);
		} 
		else {
			
			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			
			if (StringUtils.isNotBlank(optionAnswerId)) {
				msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
				trTaskD.setMsLovByIntLovId(msLovByLovId);
				trTaskD.setIntOptionText(msLovByLovId.getDescription());
			}			
						
			trTaskD.setTrTaskH(trTaskH);
			trTaskD.setMsQuestion(msQuestion);		
			trTaskD.setQuestionText(msQuestion.getQuestionLabel());
			trTaskD.setIntTextAnswer(textAnswer);
			trTaskD.setLatitude(checkEmptyBigdecimal(latitude));
			trTaskD.setLongitude(checkEmptyBigdecimal(longitude));
			trTaskD.setMcc(checkEmptyInteger(mcc));
			trTaskD.setMnc(checkEmptyInteger(mnc));
			trTaskD.setLac(checkEmptyInteger(lac));
			trTaskD.setCellId(checkEmptyInteger(cellId));
			trTaskD.setAccuracy(checkEmptyInteger(accuracy));
			
			this.getManagerDAO().insert(trTaskD);
		}
	}
	
	private void insertDetailLobSurvey(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD,
			String base64Image, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, ImageStorageLocation saveImgLoc, Path imgLoc) {

		TrTaskdetaillob trTaskDetailLob = new TrTaskdetaillob();
		trTaskDetailLob.setUsrCrt(auditContext.getCallerId());
		trTaskDetailLob.setDtmCrt(new Date());
		
		trTaskDetailLob.setTrTaskH(trTaskH);
		trTaskDetailLob.setMsQuestion(msQuestion);
		trTaskDetailLob.setQuestionText(msQuestion.getQuestionLabel());
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				trTaskDetailLob.setLobFile(BaseEncoding.base64().decode(base64Image));
			}
		}
        else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
            if (base64Image != null && !base64Image.isEmpty()) {
                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date date = new Date();
                Path outputPath = Paths.get(imgLoc.toString(), dateFormat.format(date));

                String absoluteFilename = trTaskH.getTaskId() + "_" + msQuestion.getRefId() + "_"
                        + uuidTaskD + ".jpg";
                
                String outputFile = this.imageStorageLogic.storeImageFileSystem(
                        BaseEncoding.base64().decode(base64Image), outputPath, absoluteFilename);
                trTaskDetailLob.setImagePath(outputFile);
            }
        }
		trTaskDetailLob.setLatitude(checkEmptyBigdecimal(latitude) );
		trTaskDetailLob.setLongitude(checkEmptyBigdecimal(longitude) );
		
		if (trTaskDetailLob.getLatitude()!=null && trTaskDetailLob.getLongitude()!=null)
			trTaskDetailLob.setIsGps("1");
		
		trTaskDetailLob.setMcc(checkEmptyInteger(mcc) );
		trTaskDetailLob.setMnc(checkEmptyInteger(mnc) );
		trTaskDetailLob.setLac(checkEmptyInteger(lac) );
		trTaskDetailLob.setCellId(checkEmptyInteger(cellId));
		trTaskDetailLob.setAccuracy(checkEmptyInteger(accuracy) );
		
		this.getManagerDAO().insert(trTaskDetailLob);
	}
	
	
	private BigDecimal checkEmptyBigdecimal(String in){
		if (StringUtils.isBlank(in)) {
			return null;
		}
		else{
			return new BigDecimal(in);		
		}
	}
	
	private Integer checkEmptyInteger(String in){		
		if (StringUtils.isBlank(in)){
			return null;
		}
		else {
			return Integer.valueOf((int) Math.round(new Double(in)));		
		}
	}
	
	private String getMhId(Map mapQuestion, SubmitTaskDBean[] taskDBean){
		Object[][] queryParams = new Object[1][1];
		queryParams[0][0] = Restrictions.eq("tagName", GlobalVal.ORDER_TAG_JOB_MH);
		MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, queryParams);
		
		if (msOrderTag == null ){
			return null;
		}
		
		for (int i=0; i<taskDBean.length; i++) {
			for (int j=0; j<mapQuestion.size(); j++) {
				MsQuestion msQuestion = (MsQuestion) mapQuestion.get(j);
				if (String.valueOf(msQuestion.getUuidQuestion()).equals(taskDBean[i].getQuestion_id())) {
					if (msQuestion.getMsOrdertag() != null &&
							msOrderTag.getUuidOrderTag() == msQuestion.getMsOrdertag().getUuidOrderTag()) {
						String[][] params = {
								{"code", taskDBean[i].getLov()},
								{"lovGroup", msQuestion.getLovGroup()} };
						if (null != taskDBean[i].getLov()) {
							Object[] msLov = (Object[])this.getManagerDAO().selectOneNative("services.submitorder.getMsLov", params);
							if (msLov != null) {
								return taskDBean[i].getLov();
							}
						} 
						else {
							MsLov lovBean = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(taskDBean[i].getOption_answer_id()));
							if (lovBean != null) {
								return lovBean.getCode();
							}
						}
					}
				}
			}
		}
		
		return StringUtils.EMPTY;
	}
	
	private String getZipCode(Map mapQuestion, SubmitTaskDBean[] taskDBean){
		Object[][] queryParams = new Object[1][1];
		queryParams[0][0] = Restrictions.eq("tagName", GlobalVal.ORDER_TAG_ZIP_CODE);
		MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, queryParams);
		
		if (msOrderTag == null ){
			return null;
		}
		
		for (int i=0; i<taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) mapQuestion.get(i);
			String[][] params = {
				{"code", taskDBean[i].getLov()},
				{"lovGroup", msQuestion.getLovGroup()}
			};
			
			if (msQuestion.getMsOrdertag() != null && msOrderTag.getUuidOrderTag() == msQuestion.getMsOrdertag().getUuidOrderTag()) {
				Object[] msLov = (Object[]) this.getManagerDAO().selectOneNative("services.submitorder.getMsLov", params);
				if (msLov != null) {
					return taskDBean[i].getLov();
				}					
			}
		}
		
		return null;
	}
	
	private MsBranch getBranch(Map mapQuestion, SubmitTaskDBean[] taskDBean){
		Object[][] queryParams = new Object[1][1];
		queryParams[0][0] = Restrictions.eq("tagName", GlobalVal.ORDER_TAG_MF_BRANCH);
		MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, queryParams);
		for (int i=0; i<taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) mapQuestion.get(i);
			
			if (msQuestion.getMsOrdertag() != null &&
					msOrderTag.getUuidOrderTag() == msQuestion.getMsOrdertag().getUuidOrderTag()) {
				
				MsLov lov =  this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(taskDBean[i].getOption_answer_id()));
				if (lov == null) {
					return null;
				}
				MsBranch result = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(lov.getCode()));
				return result;
				
			}
		}
		
		return null;
	}
	
	private String getPtsDate(Map mapQuestion, SubmitTaskDBean[] taskDBean){
		Object[][] queryParams = { {Restrictions.eq("tagName", GlobalVal.ORDER_TAG_PTS_DATE)} } ;
		MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, queryParams);
		if (msOrderTag == null) {
			return null;
		}
		
		for (int i=0; i<taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) mapQuestion.get(i);
			
			if (msQuestion.getMsOrdertag() != null &&
					msOrderTag.getUuidOrderTag() == msQuestion.getMsOrdertag().getUuidOrderTag()) {
				return taskDBean[i].getText_answer();					
			}
		}
		
		return null;
	}
	
	private String getLocationSurvey(Map mapQuestion, SubmitTaskDBean[] taskDBean){
		Object[][] queryParams = { {Restrictions.eq("tagName", GlobalVal.ORDER_TAG_SURVEY_LOCATION)} } ;
		MsOrdertag msOrderTag = this.getManagerDAO().selectOne(MsOrdertag.class, queryParams);
		if (msOrderTag == null) {
			return null;
		}
		for (int i=0; i<taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) mapQuestion.get(i);
			
			if (msQuestion.getMsOrdertag() != null &&
					msOrderTag.getUuidOrderTag() == msQuestion.getMsOrdertag().getUuidOrderTag()) {
				return taskDBean[i].getLatitude() + ";" + taskDBean[i].getLongitude();
			}
		}
		
		return null;
	}
	
	private String getDealerId(Map  mapQuestion, SubmitTaskDBean[] taskDBean){
		for (int i=0; i<taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) mapQuestion.get(i);		
			if (msQuestion.getMsOrdertag() != null &&
					msQuestion.getMsOrdertag().getTagName().equals(GlobalVal.ORDER_TAG_DEALER_NAME)) {
				String lovId = taskDBean[i].getOption_answer_id();
				if (StringUtils.isNotEmpty(lovId)) {
					Object[][] paramsLov = {{Restrictions.eq("uuidLov", Long.valueOf(lovId))}};
					MsLov msLov  = this.getManagerDAO().selectOne(MsLov.class, paramsLov);
					if (null != msLov) {
							return msLov.getCode();
					}
				}
				break;
			}
		}
		return null;
	}
	
	private Map<Integer, MsQuestion> getAllMsQuestion(SubmitTaskDBean[] taskDBean, MsFormhistory formHist, AuditContext auditContext){
		Map<Integer, MsQuestion> result = new HashMap<>();
		 
		for (int i=0; i<taskDBean.length; i++) {
			Object [][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", Long.valueOf(formHist.getUuidFormHistory()))},
								{Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(taskDBean[i].getQuestion_id()))}};
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
			
			MsQuestion msQuestion = new MsQuestion();
			msQuestion.setUuidQuestion(Long.parseLong(taskDBean[i].getQuestion_id()));
			msQuestion.setRefId(qset.getRefId());
			msQuestion.setQuestionLabel(qset.getQuestionLabel());
			msQuestion.setMsAnswertype(qset.getMsAnswertype());
			msQuestion.setMsAssettag(qset.getMsAssettag());
			msQuestion.setMsCollectiontag(qset.getMsCollectiontag());
			msQuestion.setMsOrdertag(qset.getMsOrdertag());
			msQuestion.setLovGroup(qset.getLovGroup());
			msQuestion.setIsVisible(qset.getIsVisible());
			msQuestion.setIsMandatory(qset.getIsMandatory());
			msQuestion.setIsReadonly(qset.getIsReadonly());
			msQuestion.setIsHolidayAllowed(qset.getIsHolidayAllowed());
			msQuestion.setMaxLength(qset.getMaxLength());
			msQuestion.setRegexPattern(qset.getRegexPattern());
			msQuestion.setAmMssubsystem(qset.getAmMssubsystem());
			msQuestion.setImgQlt(qset.getImgQlt());
			result.put(i, msQuestion);
		}
		return result;
	}
	
	private String getFullNameById(String uuidUser){
		AmMsuser bean =this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(uuidUser));
		return bean.getFullName();
	}
	
	private Date stringToDate(String date, String dateformat){
		DateFormat format = new SimpleDateFormat(dateformat);
		Date d = null;
		try{
			d = format.parse(date);
		} 
		catch (Exception e){
			LOG.error(e.getMessage(),e);
			e.printStackTrace();
		}
		return d;
	}
	
	private String dateAnswerToText(String dateAnswer, String answerTypeCode) {
		DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
		try {
			Date result = df.parse(dateAnswer);
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)) {
				return (null != result) ? new SimpleDateFormat("dd/MM/yyyy").format(result) : null;
			}
			else {
				return (null != result) ? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result) : null;
			}
		}
		catch (ParseException e) {
			LOG.warn("Cannot parse '{}' to answer type {}", dateAnswer, answerTypeCode, e);
			return null;
		}
	}
	
	private List<TrTaskD> listTaskD(String uuidTaskH) {
		if (StringUtils.isBlank(uuidTaskH)) {
			return Collections.emptyList();
		}
		
		Object[][] params = { {"uuidTaskH", Long.valueOf(uuidTaskH)} };
		Map<String, Object> result = this.getManagerDAO().list(
				"from TrTaskD td join fetch td.msQuestion where td.trTaskH.uuidTaskH = :uuidTaskH", params);
		
		return (List<TrTaskD>) result.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	private Map<Long, TrTaskD> listTaskDAsMap(String uuidTaskH) {
		List<TrTaskD> result = this.listTaskD(uuidTaskH);
		
		if (result == null || result.isEmpty()){
			return Collections.emptyMap();
		}
		
		Map<Long, TrTaskD> resultMap = new HashMap<>(result.size());
		for (Iterator iterator = result.iterator(); iterator.hasNext();) {
			TrTaskD trTaskD = (TrTaskD) iterator.next();
			resultMap.put(trTaskD.getMsQuestion().getUuidQuestion(), trTaskD);
		}
		
		return resultMap;
	}
	
	private void validateDeviceId(AmMsuser usr, String imei, String androidId, AuditContext auditContext) {
		AmGeneralsetting beanGeneral = this.getManagerDAO().selectOne(AmGeneralsetting.class,
				new Object[][] { { Restrictions.eq("gsCode", usr.getAmMssubsystem().getSubsystemName() + GlobalKey.GENERALSETTING_MODE_LOGIN_SUBMIT) } });
		if (beanGeneral.getGsValue() == null || "".equals(beanGeneral.getGsValue())) {
			beanGeneral.setGsValue(GlobalVal.MODE_IMEI);
		}
		
		if (GlobalVal.MODE_IMEI.equalsIgnoreCase(beanGeneral.getGsValue())) { // jika MODE_SUBMIT == IMEI
			if (imei != null && !imei.equals(usr.getImei()) && !imei.equals(usr.getImei2())) {
				throw new UserLoginIdNotExistsException(this.messageSource.getMessage(
						"businesslogic.global.imeinotvalid", new Object[] { imei }, this.retrieveLocaleAudit(auditContext)));				
			}
		} 
		else if (GlobalVal.MODE_ANDROID_ID.equalsIgnoreCase(beanGeneral.getGsValue())) { // jika MODE_SUBMIT = ANDROID_ID			
			if (androidId != null &&
					(usr.getAndroidId() == null || !usr.getAndroidId().equals(androidId))) {
				throw new UserLoginIdNotExistsException(this.messageSource.getMessage(
						"businesslogic.global.androididnotvalid", null, this.retrieveLocaleAudit(auditContext)));				
			}
		}
	}
	
	private String saveTaskDIntoJson(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, Map<Integer, MsQuestion> msQuestions,
			ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		String isFinal = null;
		
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		TaskDocumentBean document = null;
		boolean newDoc = false;
		if (docDb == null) {
			docDb = new TrTaskdocument(trTaskH, new Date(), auditContext.getCallerId());
			document = new TaskDocumentBean();
			newDoc = true;
		}
		else {
			document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
			docDb.setDtmUpd(new Date());
			docDb.setUsrUpd(auditContext.getCallerId());
			newDoc = false;
		}
		
		List<AnswerBean> answers = (newDoc) ? new ArrayList<AnswerBean>(taskDBean.length) : document.getAnswers();		
		if (newDoc) {
			document.setAnswers(answers);
		}
		
		for (int i=0; i < taskDBean.length; i++) {
			isFinal = taskDBean[i].getIs_final();
			
			MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
			String answerTypeCode = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String optionAnswerId = StringUtils.stripToEmpty(taskDBean[i].getOption_answer_id());
			String questionText = StringUtils.stripToEmpty(taskDBean[i].getQuestion_label());
			String textAnswer = StringUtils.stripToEmpty(taskDBean[i].getText_answer());
			String latitude = StringUtils.stripToNull(taskDBean[i].getLatitude());						
			String longitude = StringUtils.stripToNull(taskDBean[i].getLongitude());
			String mcc = StringUtils.stripToNull(taskDBean[i].getMcc());
			String mnc = StringUtils.stripToNull(taskDBean[i].getMnc());
			String lac = StringUtils.stripToNull(taskDBean[i].getLac());				
			String cellId = StringUtils.stripToNull(taskDBean[i].getCid());				
			String accuracy = StringUtils.stripToNull(taskDBean[i].getAccuracy());	
			String image = StringUtils.stripToNull(taskDBean[i].getImage());
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerTypeCode) &&
					StringUtils.isNotEmpty(textAnswer) && StringUtils.containsAny(textAnswer, ',', '.')) {
				textAnswer = textAnswer.substring(0, textAnswer.length()-2).replace(",", "").replace(".", "");				
			}
			
			int idxAnswer = document.findAnswerIndex(msQuestion.getUuidQuestion());
			
			AnswerBean answer = this.constructAnswer(
					document, idxAnswer, msQuestion, answerTypeCode, questionText, textAnswer, optionAnswerId,
					latitude, longitude, accuracy, mcc, mnc, lac, cellId, trTaskH, image, isl, imagePath, false, true, auditContext);
			
			if (idxAnswer == -1) {
				answers.add(answer);
			}
		}
		
		docDb.setDocument(gson.toJson(document, TaskDocumentBean.class));
		
		if (newDoc) {
			this.getManagerDAO().insert(docDb);
		}
		else {
			this.getManagerDAO().update(docDb);
		}
	
		
		return isFinal;
	}
	
	private String saveTaskDIntoRow(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, Map<Integer, MsQuestion> msQuestions,
			Map<Long, TrTaskD> listTaskD, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		String isFinal = null;
		
		//LOOPING FOR TR_TASK_D
		for (int i=0; i < taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
			String answerTypeCode = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String optionAnswerId = StringUtils.EMPTY;
			if (taskDBean[i].getOption_answer_id() != null) {
				optionAnswerId = taskDBean[i].getOption_answer_id();
			}
			
			String textAnswer = StringUtils.EMPTY;					
			if (taskDBean[i].getText_answer() != null) {
				textAnswer = taskDBean[i].getText_answer();
				if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerTypeCode)) {
					if (textAnswer.contains(",")||textAnswer.contains(".")) {
						textAnswer = textAnswer.substring(0,textAnswer.length()-2).replace(",", "").replace(".", "");
					}
				}
			}
			
			String questionText = StringUtils.EMPTY;
			if (taskDBean[i].getQuestion_label() != null) {
				questionText = taskDBean[i].getQuestion_label();
			}
			
			String latitude = StringUtils.EMPTY;
			if (taskDBean[i].getLatitude() != null) {
				latitude = taskDBean[i].getLatitude();
			}
			
			String longitude = StringUtils.EMPTY;
			if (taskDBean[i].getLongitude() != null) {
				longitude = taskDBean[i].getLongitude();
			}
			
			String mcc = StringUtils.EMPTY;
			if (taskDBean[i].getMcc() != null) {
				mcc = taskDBean[i].getMcc();
			}
			
			String mnc = StringUtils.EMPTY;
			if (taskDBean[i].getMnc() != null) {
				mnc = taskDBean[i].getMnc();
			}
			
			String lac = StringUtils.EMPTY;
			if (taskDBean[i].getLac() != null) {
				lac = taskDBean[i].getLac();
			}
			
			String cellId = StringUtils.EMPTY;
			if (taskDBean[i].getCid() != null) {
				cellId = taskDBean[i].getCid();
			}
			
			String accuracy = StringUtils.EMPTY;
			if (taskDBean[i].getAccuracy() != null) {
				accuracy = taskDBean[i].getAccuracy();	
			}
			
			String image = StringUtils.EMPTY;
			if (taskDBean[i].getImage() != null) {
				image = taskDBean[i].getImage();
			}
			
			isFinal = taskDBean[i].getIs_final();

			String uuidTaskD = this.getManagerDAO().getUUID();
			if (GlobalVal.ANSWER_TYPE_IMAGE.equals(answerTypeCode) || 
					GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA.equals(answerTypeCode) ||
					GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS.equals(answerTypeCode)||
					GlobalVal.ANSWER_TYPE_DRAWING.equals(answerTypeCode)) {
				//insert into Table TR_TASKDETAILLOB
				this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, isl, imagePath, true);
			} 
			else {
				//insert into Table TR_TASK_D
				this.insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, optionAnswerId, 
						textAnswer, latitude, longitude, mcc, mnc, lac, cellId, accuracy, listTaskD);
			}
		}
		
		return isFinal;
	}
	
	private void insertTaskSurveyData(TrTaskH trTaskH, String uuidLov, String userUpdateName){
		TrTasksurveydata taskSurveyData = null;
		Object[][] prm = {{Restrictions.eq("uuidTaskId", trTaskH.getUuidTaskH())}};
		taskSurveyData = this.getManagerDAO().selectOne(TrTasksurveydata.class, prm);
		if (taskSurveyData == null){
			taskSurveyData = new TrTasksurveydata();
			taskSurveyData.setTrTaskH(trTaskH);
	    	taskSurveyData.setUsrCrt(userUpdateName);
	    	taskSurveyData.setDtmCrt(new Date());
	    	taskSurveyData.setProductLovId(Long.valueOf(uuidLov));
	    	this.getManagerDAO().insert(taskSurveyData);
		} 
		else {
			taskSurveyData.setProductLovId(Long.valueOf(uuidLov));
			taskSurveyData.setUsrUpd(userUpdateName);
			taskSurveyData.setDtmUpd(new Date());
			this.getManagerDAO().update(taskSurveyData);
		}
	}
	
	private TrTaskH saveTaskHSurvey(AmMsuser userSubmit, TrTaskH trTaskHOrder,
			BigDecimal latHeader, BigDecimal longiHeader, MsPriority surveyPriority,
			MsBranch msBranchHO, AmMsuser userMh, MsBranch msBranchMfSelected,
			String ptsDate, MappingFormBean formQuestion, AuditContext auditContext) {
		//insert into tr_taskh
		TrTaskH taskHSurvey = new TrTaskH();
		taskHSurvey.setUsrCrt(auditContext.getCallerId());
		taskHSurvey.setDtmCrt(new Date());
		
		if (null == msBranchMfSelected) {
			if (null == userMh) {
				if ("1".equals(userSubmit.getMsJob().getIsSelfAssignment())) {
					taskHSurvey.setMsBranch(userSubmit.getMsBranch());
				} 
				else {
					taskHSurvey.setMsBranch(msBranchHO);
				}
			} 
			else {
				taskHSurvey.setMsBranch(userMh.getMsBranch());
				taskHSurvey.setAmMsuser(userMh);
			}
		} 
		else {
			if (null == userMh) {
				taskHSurvey.setMsBranch(msBranchMfSelected);
			} 
			else {
				taskHSurvey.setMsBranch(userMh.getMsBranch());
				taskHSurvey.setAmMsuser(userMh);
			}
		}					
		
		//getMsForm from questionMap formid
		MsForm msForm = this.commonLogic.retrieveMsFormByUuid(Long.parseLong(formQuestion.getFormId()), auditContext);
		taskHSurvey.setMsForm(msForm);
	
		taskHSurvey.setMsPriority(surveyPriority);
		taskHSurvey.setCustomerName(trTaskHOrder.getCustomerName());
		taskHSurvey.setCustomerPhone(trTaskHOrder.getCustomerPhone());
		taskHSurvey.setCustomerAddress(trTaskHOrder.getCustomerAddress());
		taskHSurvey.setZipCode(trTaskHOrder.getZipCode());
		taskHSurvey.setNotes(trTaskHOrder.getNotes());
		//trTaskHMap.setTaskId(this.commonLogic.retrieveNewTaskId(GlobalVal.SEQUENCE_TASK_ID_MOBILE_SURVEY));
		taskHSurvey.setIsDraft("0");
		taskHSurvey.setFlagSource("MO");							
		taskHSurvey.setIsAppNotified("0");
		taskHSurvey.setLatitude(latHeader);
		taskHSurvey.setLongitude(longiHeader);
		taskHSurvey.setFormVersion(formQuestion.getFormVersion());
		if (StringUtils.isNotBlank(ptsDate)){
			taskHSurvey.setPtsDate(stringToDate(ptsDate, "dd/MM/yyyy HH:mm:ss"));
		}
		this.getManagerDAO().insert(taskHSurvey);
		
		taskHSurvey.setTaskId(String.valueOf(taskHSurvey.getUuidTaskH()));
		
		return taskHSurvey;
	}
	
	private void saveTaskLink(long uuidTrTaskHOrder, long uuidTrTaskHSurvey, AuditContext auditContext) {
		TrTasklink trTaskLink = new TrTasklink();							
		trTaskLink.setUsrCrt(auditContext.getCallerId());
		trTaskLink.setDtmCrt(new Date());
		trTaskLink.setUuidTaskHOrder(uuidTrTaskHOrder);
		trTaskLink.setUuidTaskHSurvey(uuidTrTaskHSurvey);
		this.getManagerDAO().insert(trTaskLink);
	}
	
	private void saveGroupTaskSurvey(TrTaskH taskHSurvey, AuditContext auditContext) {
		MsGrouptask msGrouptask = new MsGrouptask();
		msGrouptask.setGroupTaskId(taskHSurvey.getUuidTaskH());
		msGrouptask.setTrTaskH(taskHSurvey);
		msGrouptask.setMsBranch(taskHSurvey.getMsBranch());
		msGrouptask.setUsrCrt(auditContext.getCallerId());
		msGrouptask.setDtmCrt(new Date());
		msGrouptask.setCustomerName(taskHSurvey.getCustomerName());
		msGrouptask.setApplNo(taskHSurvey.getApplNo());
		this.getManagerDAO().insert(msGrouptask);
	}
	
	private void saveTaskDSurveyIntoJson(TrTaskH taskHSurvey, List<MappingFormBean> listQuestion, Map<Integer, MsQuestion> questions,
			SubmitTaskDBean[] taskDBeanOrder, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		if (taskDBeanOrder == null){
			return;
		}
		
		if (listQuestion == null || listQuestion.isEmpty()){
			return;
		}
		
		TrTaskdocument docDb = new TrTaskdocument(taskHSurvey, new Date(), auditContext.getCallerId());
		TaskDocumentBean document = new TaskDocumentBean();
		List<AnswerBean> answers = new ArrayList<AnswerBean>();		
		document.setAnswers(answers);
		
		MsFormhistory formHistory = this.commonLogic.retrieveMsFormhistory(taskHSurvey.getMsForm().getUuidForm(),
				taskHSurvey.getFormVersion(), auditContext);
		
		for (int i = 0; i < listQuestion.size(); i++) {
			MappingFormBean mappingQuestion = listQuestion.get(i); //uuidQ1=Svy(target) uuidQ2=Odr(source)
			
			for (int k = 0; k < taskDBeanOrder.length; k++) {
				String uuidQuestionD = taskDBeanOrder[k].getQuestion_id();
				if (mappingQuestion.getUuidQuestion2().equals(uuidQuestionD)) { //kalau jawaban Order ada di Mapping -> insert
					String optionAnswerId = taskDBeanOrder[k].getOption_answer_id();
					String textAnswer = taskDBeanOrder[k].getText_answer();
					String latitude = taskDBeanOrder[k].getLatitude();
					String longitude = taskDBeanOrder[k].getLongitude();
					String mcc = taskDBeanOrder[k].getMcc();
					String mnc = taskDBeanOrder[k].getMnc();
					String lac = taskDBeanOrder[k].getLac();
					String cellId = taskDBeanOrder[k].getCid();
					String accuracy = taskDBeanOrder[k].getAccuracy();
					String image = taskDBeanOrder[k].getImage();
					
					Object [][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHistory.getUuidFormHistory())},
							{Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(mappingQuestion.getUuidQuestion1()))}};
					MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
					MsQuestion msQuestionSvy = 	qset.getMsQuestion();
					msQuestionSvy.setQuestionLabel(qset.getQuestionLabel());
					
					int idxAnswer = document.findAnswerIndex(msQuestionSvy.getUuidQuestion());
					
					AnswerBean answer = this.constructAnswer(document, idxAnswer, msQuestionSvy,
							msQuestionSvy.getMsAnswertype().getCodeAnswerType(), msQuestionSvy.getQuestionLabel(),
							textAnswer, optionAnswerId, latitude, longitude,
							accuracy, mcc, mnc, lac, cellId, taskHSurvey, image, isl, imagePath, true, false, auditContext);
					
					if (idxAnswer == -1) {
						answers.add(answer);
					}
					
					break;
				}
			}
		}
		
		docDb.setDocument(gson.toJson(document, TaskDocumentBean.class));
		this.getManagerDAO().insert(docDb);		
	}	
	
	private void saveTaskDSurveyIntoRow(TrTaskH taskHSurvey, List<MappingFormBean> listQuestion, Map<Integer, MsQuestion> questions,
			SubmitTaskDBean[] taskDBeanOrder, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		Object [][] prmFormHist = {{Restrictions.eq("msForm.uuidForm", taskHSurvey.getMsForm().getUuidForm())},
				{Restrictions.eq("formVersion", taskHSurvey.getFormVersion())}};
		MsFormhistory formHistory = this.getManagerDAO().selectOne(MsFormhistory.class, prmFormHist);
		
		for (int i = 0; i < listQuestion.size(); i++) {
			MappingFormBean mappingQuestion = listQuestion.get(i); //uuidQ1=Svy(target) uuidQ2=Odr(source)
			
			for (int k = 0; k < taskDBeanOrder.length; k++) {
				String uuidQuestionD = taskDBeanOrder[k].getQuestion_id();
				if (mappingQuestion.getUuidQuestion2().equals(uuidQuestionD)) { //kalau jawaban Order ada di Mapping -> insert
					MsQuestion msQuestion2 = questions.get(k);
					
					String optionAnswerId = taskDBeanOrder[k].getOption_answer_id();
					String textAnswer = taskDBeanOrder[k].getText_answer();
					String latitude = taskDBeanOrder[k].getLatitude();
					String longitude = taskDBeanOrder[k].getLongitude();
					String mcc = taskDBeanOrder[k].getMcc();
					String mnc = taskDBeanOrder[k].getMnc();
					String lac = taskDBeanOrder[k].getLac();
					String cellId = taskDBeanOrder[k].getCid();
					String accuracy = taskDBeanOrder[k].getAccuracy();
					String image = taskDBeanOrder[k].getImage();
					String uuidTaskD = this.getManagerDAO().getUUID().toUpperCase();
					
					Object [][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHistory.getUuidFormHistory())},
							{Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(mappingQuestion.getUuidQuestion1()))}};
					MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
					MsQuestion msQuestionSvy = 	qset.getMsQuestion();
					msQuestionSvy.setQuestionLabel(qset.getQuestionLabel());
					
					if (msQuestionSvy.getMsAssettag() != null){
						if (GlobalVal.ASSET_TAG_PRODUCT.equalsIgnoreCase(msQuestionSvy.getMsAssettag().getAssetTagName()) && 
								(StringUtils.isNotBlank(optionAnswerId))){
							this.insertTaskSurveyData(taskHSurvey, optionAnswerId, auditContext.getCallerId());
						}
					}
					
					if (MssTool.isImageQuestion(msQuestion2.getMsAnswertype().getCodeAnswerType())){
						//insert into Table TR_TASKDETAILLOB
						insertDetailLobSurvey(auditContext, taskHSurvey, msQuestionSvy, uuidTaskD, image, 
								latitude, longitude, mcc, mnc, lac, cellId, accuracy, isl, imagePath);
					} 
					else {
						//insert into Table TR_TASK_D
						insertTaskDSurvey(auditContext, taskHSurvey, msQuestionSvy, uuidTaskD, optionAnswerId, 
								textAnswer, latitude, longitude, mcc, mnc, lac, cellId, accuracy);
					}
					
					break;
				}
			}
		}
	}
	
	private AnswerBean constructAnswer(TaskDocumentBean document, int idxAnswer, MsQuestion msQuestion,
			String answerTypeCode, String questionText, String textAnswer, String optionAnswerId,
			String latitude, String longitude, String accuracy, String mcc, String mnc, String lac, String cellId,
			TrTaskH trTaskH, String image, ImageStorageLocation isl, Path imagePath,
			boolean saveToInt, boolean checkExisting, AuditContext auditContext) {
		AnswerBean answer = (idxAnswer == -1) ? new AnswerBean() : document.getAnswers().get(idxAnswer);
		QuestionBean questionBean = new QuestionBean(
				msQuestion.getUuidQuestion(), questionText, msQuestion.getRefId(), answerTypeCode,
				(msQuestion.getMsOrdertag() == null ? null : msQuestion.getMsOrdertag().getTagName()),
				(msQuestion.getMsAssettag() == null ? null : msQuestion.getMsAssettag().getAssetTagName()),
				(msQuestion.getMsCollectiontag() == null ? null : msQuestion.getMsCollectiontag().getTagName()));
		answer.setQuestion(questionBean);
		
		if (MssTool.isChoiceQuestion(answerTypeCode)) {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			List<OptionBean> options = null;
			if (saveToInt) {
				options = (answer.getIntOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getIntOptAnswers();
			}
			else {
				options = (answer.getOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getOptAnswers();
			}
			MsLov msLovByLovId = null; 
			if (StringUtils.isNotBlank(optionAnswerId)) {
				if (null != msQuestion.getMsOrdertag() && GlobalVal.ORDER_TAG_JOB_MH.equals(msQuestion.getMsOrdertag().getTagName())) {						
					Object[][] paramsLov = {{Restrictions.eq("code", optionAnswerId)}, {Restrictions.eq("lovGroup", "LOOKUPMH")}};
					msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, paramsLov);						
				}
				else {
					msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
				}
			}
			if (msLovByLovId != null) {
				OptionBean option = new OptionBean();
				option.setUuid(msLovByLovId.getUuidLov());
				option.setCode(msLovByLovId.getCode());
				option.setDesc(msLovByLovId.getDescription());
				option.setFreeText(StringUtils.trimToNull(textAnswer));
				if (!options.contains(option)) {
					options.add(option);
				}
				
				if (saveToInt) {
					answer.setIntOptAnswers(options);
				}
				else {
					answer.setOptAnswers(options);
				}
			}				
		}
		else if (MssTool.isImageQuestion(answerTypeCode) || GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
			answer.setOptAnswers(null);
			Boolean isGps = null;
			Boolean isConverted = null;
			
			if (mcc != null) {
				LocationBean locationBean = (answer.getLocation() == null) ? new LocationBean() : answer.getLocation();
				locationBean.setMcc(checkEmptyInteger(mcc).intValue());
				locationBean.setMnc(checkEmptyInteger(mnc).intValue());
				locationBean.setLac(checkEmptyInteger(lac).intValue());
				locationBean.setCid(checkEmptyInteger(cellId).intValue());
				locationBean.setAccuracy(checkEmptyInteger(accuracy));
				
				if (latitude != null && longitude != null
						&& new BigDecimal(latitude).intValue() != 0 && new BigDecimal(longitude).intValue() != 0) {
					locationBean.setLat(checkEmptyBigdecimal(latitude).doubleValue());
					locationBean.setLng(checkEmptyBigdecimal(longitude).doubleValue());
					locationBean.setIsGps(1);
				}
				else {
					com.adins.framework.tool.geolocation.model.LocationBean convertedLocation =
							this.getLocationByCellId(locationBean.getMcc(), locationBean.getMnc(),
							locationBean.getLac(), locationBean.getCid(), auditContext);
					if (convertedLocation.getCoordinate() != null) {
						locationBean.setAccuracy(convertedLocation.getAccuracy());
						locationBean.setLat(convertedLocation.getCoordinate().getLatitude());
						locationBean.setLng(convertedLocation.getCoordinate().getLongitude());
						locationBean.setIsGps(0);
						locationBean.setIsConverted(1);
						
						latitude = Double.toString(convertedLocation.getCoordinate().getLatitude());
						longitude = Double.toString(convertedLocation.getCoordinate().getLongitude());
						accuracy = Integer.toString(convertedLocation.getAccuracy());
					}
					isGps = Boolean.FALSE;
					isConverted = Boolean.TRUE;
				}
				
				answer.setLocation(locationBean);
			}

			if (MssTool.isImageQuestion(answerTypeCode)) {
				ImageBean imageBean = (answer.getLobAnswer() == null) ? new ImageBean() : answer.getLobAnswer();
				if (image != null && !image.isEmpty()) {
					imageBean.setHasImage(true);
					answer.setLobAnswer(imageBean);
				}
				
				//insert into Table TR_TASKDETAILLOB
				String uuidTaskD = this.getManagerDAO().getUUID();
				long idLob = this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, isGps, isConverted, isl, imagePath, checkExisting);
				if (idLob > 0L)
					imageBean.setId(idLob);
			}
			else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
				if (saveToInt) {
					answer.setIntTxtAnswer(textAnswer);
				}
				else {
					answer.setTxtAnswer(textAnswer);
				}
			}
		}
		else {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)
					|| GlobalVal.ANSWER_TYPE_DATETIME.equals(answerTypeCode)) {
				textAnswer = this.dateAnswerToText(textAnswer, answerTypeCode);
			}
			if (saveToInt) {
				answer.setIntOptAnswers(null);
				answer.setIntTxtAnswer(textAnswer);
			}
			else {
				answer.setOptAnswers(null);
				answer.setTxtAnswer(textAnswer);
			}			
		}
		
		return answer;
	}
	
	private com.adins.framework.tool.geolocation.model.LocationBean getLocationByCellId(int mcc, int mnc, int lac, int cellId,
			AuditContext auditContext) {
		List<com.adins.framework.tool.geolocation.model.LocationBean> listLocations =
				new ArrayList<com.adins.framework.tool.geolocation.model.LocationBean>();
		com.adins.framework.tool.geolocation.model.LocationBean locationBean =
				new com.adins.framework.tool.geolocation.model.LocationBean();
		locationBean.setCellid(cellId);
		locationBean.setLac(lac);
		locationBean.setMnc(mnc);
		locationBean.setMcc(mcc);
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);
		return locationBean;
	}
	
	private void getLocationByCellId(TrTaskD taskD, AuditContext auditContext){
		List<com.adins.framework.tool.geolocation.model.LocationBean> listLocations =
				new ArrayList<com.adins.framework.tool.geolocation.model.LocationBean>();
		com.adins.framework.tool.geolocation.model.LocationBean locationBean =
				new com.adins.framework.tool.geolocation.model.LocationBean();
		locationBean.setCellid(taskD.getCellId());
		locationBean.setLac(taskD.getLac().intValue());
		locationBean.setMcc(taskD.getMcc().intValue());
		locationBean.setMnc(taskD.getMnc().intValue());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			taskD.setLatitude(new BigDecimal(locationBean.getCoordinate()
					.getLatitude()));
			taskD.setLongitude(new BigDecimal(locationBean.getCoordinate()
					.getLongitude()));
			taskD.setAccuracy(new Integer(locationBean.getAccuracy()));
			
			taskD.setTextAnswer("Coord : "+taskD.getLatitude().setScale(6, RoundingMode.HALF_UP)+", "+taskD.getLongitude().setScale(6, RoundingMode.HALF_UP)+" Accuracy : "+taskD.getAccuracy()+" m");
			taskD.setIsConverted("1");
		}
	}
	
	private void getLocationByCellId(TrTaskdetaillob detailLob, AuditContext auditContext){
		List<com.adins.framework.tool.geolocation.model.LocationBean> listLocations =
				new ArrayList<com.adins.framework.tool.geolocation.model.LocationBean>();
		com.adins.framework.tool.geolocation.model.LocationBean locationBean =
				new com.adins.framework.tool.geolocation.model.LocationBean();
		locationBean.setCellid(detailLob.getCellId());
		locationBean.setLac(detailLob.getLac().intValue());
		locationBean.setMcc(detailLob.getMcc().intValue());
		locationBean.setMnc(detailLob.getMnc().intValue());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			detailLob.setLatitude(new BigDecimal(locationBean.getCoordinate()
					.getLatitude()));
			detailLob.setLongitude(new BigDecimal(locationBean.getCoordinate()
					.getLongitude()));
			detailLob.setAccuracy(new Integer(locationBean.getAccuracy()));
			
			detailLob.setTextAnswer("Coord : "+detailLob.getLatitude().setScale(6, RoundingMode.HALF_UP)+", "+detailLob.getLongitude().setScale(6, RoundingMode.HALF_UP)+" Accuracy : "+detailLob.getAccuracy()+" m");
			detailLob.setIsConverted("1");
		}
	}
}
