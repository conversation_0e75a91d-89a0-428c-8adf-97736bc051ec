<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

	<bean id="GenericCheckOrderLogicBean" class="com.adins.mss.businesslogic.impl.order.GenericCheckOrderLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericContentNewsLogicBean" class="com.adins.mss.businesslogic.impl.order.GenericContentNewsLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericOrderMonitoringLogicBean" class="com.adins.mss.businesslogic.impl.order.GenericOrderMonitoringLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericInquiryTaskOrderLogicBean" class="com.adins.mss.businesslogic.impl.order.GenericInquiryTaskOrderLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericRequestPOLogicBean" class="com.adins.mss.businesslogic.impl.order.GenericRequestPOLogic" scope="singleton" parent="BaseLogicBean"> 
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericCancelOrderBean" class="com.adins.mss.businesslogic.impl.order.GenericCancelOrderLogic" scope="singleton" parent="BaseLogicBean" >
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
	</bean>
	<bean id="GenericSubmitTaskOrderLogicBean" class="com.adins.mss.businesslogic.impl.order.GenericSubmitTaskOrderLogic" scope="singleton" parent="BaseLogicBean">
		<property name="commonLogic" ref="GenericCommonLogicBean" />
		<property name="taskDistributionLogic" ref="GenericTaskDistributionLogicBean" />
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
		<property name="imageStorageLogic" ref="GenericImageStorageLogicBean" />
		<property name="geocoder" ref="GenericGeolocationLogicBean"/>
	</bean>	
	<bean id="GenericRequestIncentiveLogicBean" class="com.adins.mss.businesslogic.impl.order.GenericRequestIncentiveLogic" scope="singleton" parent="BaseLogicBean">
		<property name="intFormLogic" ref="interfacing.FormFactoryBean" />
	</bean>
	<bean id="GenericNewOrderLogicBean" class="com.adins.mss.businesslogic.impl.order.GenericNewOrderLogic" scope="singleton" parent="BaseLogicBean">
		<property name="submitTaskOrderLogic" ref="GenericSubmitTaskOrderLogicBean" />
		<property name="commonLogic" ref="GenericCommonLogicBean" />
	</bean>
	
<!-- 	Report -->
	<bean id="GenericDashboardAnalyticsOrderLogicBean" class="com.adins.mss.businesslogic.impl.order.GenericDashboardAnalyticsOrderLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericReportOrderMonitoringBean" class="com.adins.mss.businesslogic.impl.order.GenericReportOrderMonitoringLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericReportSlaOrderBean" class="com.adins.mss.businesslogic.impl.order.GenericReportSlaOrderLogic" scope="singleton" parent="BaseLogicBean"/>
	<bean id="GenericReportOrderPerformanceBean" class="com.adins.mss.businesslogic.impl.order.GenericReportOrderPerformanceLogic" scope="singleton" parent="BaseLogicBean"/>
</beans>