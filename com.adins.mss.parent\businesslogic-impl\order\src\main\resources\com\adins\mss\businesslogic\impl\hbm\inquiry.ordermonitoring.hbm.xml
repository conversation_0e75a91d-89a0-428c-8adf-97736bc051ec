<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
		
	<sql-query name="inquiry.ordermonitoring.orderMonitoringListDealerExc">
		<query-param name="id" type="string" />
		<query-param name="userId" type="String"/>
		<query-param name="statusApprove" type="string"/>
		<query-param name="batasHijau" type="int"/>
		<query-param name="batasKuning" type="int"/>
		<![CDATA[
		WITH N AS (
		  select	TTH.UUID_TASK_H, TTH.TASK_ID, TTH.CUSTOMER_NAME, TTH.APPROVAL_DATE,
					TTOD.DTM_CRT AS ORDER_DATE, TTOD.ORDER_NO, TTOD.DEALER_ID, MD.DEALER_NAME, 
					AMU.UUID_MS_USER, AMU.LOGIN_ID, AMU.FULL_NAME,
					TTH.UUID_STATUS_TASK, MST.STATUS_CODE, MST.STATUS_TASK_DESC,			
					dbo.GET_DATEDIFFWITHHOLIDAY(TTH.UUID_BRANCH, TTHI.DTM_CRT) as SLA_MINUTES,
					TTHI.DTM_CRT AS STATUS_DATE
			from tr_task_h TTH with (nolock)
				join (
					SELECT keyValue as UUID_MS_USER, loginId as LOGIN_ID, fullName AS FULL_NAME
					  FROM getUserByLogin(:userId)
				) AMU on TTH.UUID_MS_USER = AMU.UUID_MS_USER
				join MS_STATUSTASK MST with (nolock) on TTH.UUID_STATUS_TASK = MST.UUID_STATUS_TASK
				join AM_MSSUBSYSTEM AMS with (nolock) on AMS.UUID_MS_SUBSYSTEM = MST.UUID_MS_SUBSYSTEM
				left join TR_TASKHISTORY TTHI with (nolock) on (TTHI.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK and TTH.UUID_TASK_H = TTHI.UUID_TASK_H)
				left join TR_TASKORDERDATA TTOD with (nolock) on TTH.UUID_TASK_H = TTOD.UUID_TASK_Id
				left join MS_DEALER MD with (nolock) on MD.UUID_DEALER = TTOD.DEALER_ID
			where TTOD.DEALER_ID = :id
				and AMS.SUBSYSTEM_NAME = 'MO'
				and (TTH.APPROVAL_DATE IS NULL OR
					 TTH.APPROVAL_DATE BETWEEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00' AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997')
				and TTH.DTM_UPD BETWEEN (
						CASE
						  WHEN MST.STATUS_CODE IN ('R', :statusApprove, 'D') 
							THEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00'
						  ELSE '1990-01-01 00:00:00'
						END) AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997'
		)
		SELECT	UUID_TASK_H, UUID_MS_USER, LOGIN_ID + ' - ' + FULL_NAME as name,
				UUID_STATUS_TASK, STATUS_TASK_DESC,
				FORMAT(ORDER_DATE, 'dd/MM/yyyy HH:mm:ss') as orderDate,
				ISNULL(CAST((SLA_MINUTES / 60) AS VARCHAR), '-') as jam,
				ISNULL(CAST((SLA_MINUTES % 60) AS VARCHAR), '-') as menit,
				ISNULL(FORMAT(STATUS_DATE, 'dd/MM/yyyy HH:mm:ss'), '-') as statusDate,
				(CASE
					WHEN STATUS_DATE IS NULL OR STATUS_CODE = 'D' THEN 'D'
					WHEN APPROVAL_DATE IS NOT NULL THEN 'F'
					WHEN SLA_MINUTES - :batasHijau <= 0 THEN 'G'
					WHEN SLA_MINUTES - :batasKuning <= 0 THEN 'Y'
					ELSE 'R'
				END) as color,
				DEALER_ID, DEALER_NAME,
				ORDER_NO, TASK_ID, CUSTOMER_NAME
		  FROM N
		 ORDER BY LOGIN_ID, ORDER_DATE
		 ]]>
	</sql-query>
	
	<sql-query name="inquiry.ordermonitoring.orderMonitoringListDealerDob">
		<query-param name="id" type="string" />
		<query-param name="userId" type="string"/>
		<query-param name="statusApprove" type="string"/>
		<query-param name="batasHijau" type="int"/>
		<query-param name="batasKuning" type="int"/>
		<![CDATA[
		WITH N AS (
		  select	TTH.UUID_TASK_H, TTH.TASK_ID, TTH.CUSTOMER_NAME, TTH.APPROVAL_DATE,
					TTOD.DTM_CRT AS ORDER_DATE, TTOD.ORDER_NO, TTOD.DEALER_ID, MD.DEALER_NAME, 
					AMU.UUID_MS_USER, AMU.LOGIN_ID, AMU.FULL_NAME,
					TTH.UUID_STATUS_TASK, MST.STATUS_CODE, MST.STATUS_TASK_DESC,			
					dbo.GET_DATEDIFFWITHHOLIDAY(TTH.UUID_BRANCH, TTHI.DTM_CRT) as SLA_MINUTES,
					TTHI.DTM_CRT AS STATUS_DATE
			from tr_task_h TTH with (nolock)
				join (
					SELECT keyValue as UUID_MS_USER, loginId as LOGIN_ID, fullName AS FULL_NAME
					  FROM getUserByLogin(:userId)
				) AMU on TTH.UUID_MS_USER = AMU.UUID_MS_USER
				join MS_STATUSTASK MST with (nolock) on TTH.UUID_STATUS_TASK = MST.UUID_STATUS_TASK
				join AM_MSSUBSYSTEM AMS with (nolock) on AMS.UUID_MS_SUBSYSTEM = MST.UUID_MS_SUBSYSTEM
				left join TR_TASKHISTORY TTHI with (nolock) on (TTHI.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK and TTH.UUID_TASK_H = TTHI.UUID_TASK_H)
				left join TR_TASKORDERDATA TTOD with (nolock) on TTH.UUID_TASK_H = TTOD.UUID_TASK_Id
				left join MS_DEALER MD with (nolock) on MD.UUID_DEALER = TTOD.DEALER_ID
			where TTH.UUID_BRANCH = :id
				and AMS.SUBSYSTEM_NAME = 'MO'
				and (TTH.APPROVAL_DATE IS NULL OR
					 TTH.APPROVAL_DATE BETWEEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00' AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997')
				and TTH.DTM_UPD BETWEEN (
						CASE
						  WHEN MST.STATUS_CODE IN ('R', :statusApprove, 'D') 
							THEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00'
						  ELSE '1990-01-01 00:00:00'
						END) AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997'
		)
		SELECT	UUID_TASK_H, UUID_MS_USER, LOGIN_ID + ' - ' + FULL_NAME as name,
				UUID_STATUS_TASK, STATUS_TASK_DESC,
				FORMAT(ORDER_DATE, 'dd/MM/yyyy HH:mm:ss') as orderDate,
				ISNULL(CAST((SLA_MINUTES / 60) AS VARCHAR), '-') as jam,
				ISNULL(CAST((SLA_MINUTES % 60) AS VARCHAR), '-') as menit,
				ISNULL(FORMAT(STATUS_DATE, 'dd/MM/yyyy HH:mm:ss'), '-') as statusDate,
				(CASE
					WHEN STATUS_DATE IS NULL OR STATUS_CODE = 'D' THEN 'D'
					WHEN APPROVAL_DATE IS NOT NULL THEN 'F'
					WHEN SLA_MINUTES - :batasHijau <= 0 THEN 'G'
					WHEN SLA_MINUTES - :batasKuning <= 0 THEN 'Y'
					ELSE 'R'
				END) as color,
				DEALER_ID, DEALER_NAME,
				ORDER_NO, TASK_ID, CUSTOMER_NAME
		  FROM N
		 ORDER BY LOGIN_ID, ORDER_DATE
		 ]]>
	</sql-query>
	

	<sql-query name="inquiry.ordermonitoring.orderMonitoringListBranchDob">
		<query-param name="id" type="string" />
		<query-param name="statusApprove" type="string"/>
		<query-param name="batasHijau" type="int"/>
		<query-param name="batasKuning" type="int"/>
		<![CDATA[
		WITH N AS (
		  select	TTH.UUID_TASK_H, TTH.TASK_ID, TTH.CUSTOMER_NAME, TTH.APPROVAL_DATE,
					TTOD.DTM_CRT AS ORDER_DATE, TTOD.ORDER_NO, TTOD.DEALER_ID, MD.DEALER_NAME, 
					AMU.UUID_MS_USER, AMU.LOGIN_ID, AMU.FULL_NAME,
					TTH.UUID_STATUS_TASK, MST.STATUS_CODE, MST.STATUS_TASK_DESC,			
					dbo.GET_DATEDIFFWITHHOLIDAY(TTH.UUID_BRANCH, TTHI.DTM_CRT) as SLA_MINUTES,
					TTHI.DTM_CRT AS STATUS_DATE
			from tr_task_h TTH with (nolock)
				join AM_MSUSER AMU with (nolock) on TTH.UUID_MS_USER = AMU.UUID_MS_USER
				join AM_MSSUBSYSTEM AMS with (nolock) on AMS.UUID_MS_SUBSYSTEM = AMU.UUID_MS_SUBSYSTEM
				join MS_STATUSTASK MST with (nolock) on TTH.UUID_STATUS_TASK = MST.UUID_STATUS_TASK
				left join TR_TASKHISTORY TTHI with (nolock) on (TTHI.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK and TTH.UUID_TASK_H = TTHI.UUID_TASK_H)
				left join TR_TASKORDERDATA TTOD with (nolock) on TTH.UUID_TASK_H = TTOD.UUID_TASK_Id
				left join MS_DEALER MD with (nolock) on MD.UUID_DEALER = TTOD.DEALER_ID
			where TTOD.DEALER_ID = :id
				and AMS.SUBSYSTEM_NAME = 'MO'
				and (TTH.APPROVAL_DATE IS NULL OR
					 TTH.APPROVAL_DATE BETWEEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00' AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997')
				and TTH.DTM_UPD BETWEEN (
						CASE
						  WHEN MST.STATUS_CODE IN ('R', :statusApprove, 'D') 
							THEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00'
						  ELSE '1990-01-01 00:00:00'
						END) AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997'
		)
		SELECT	UUID_TASK_H, UUID_MS_USER, LOGIN_ID + ' - ' + FULL_NAME as name,
				UUID_STATUS_TASK, STATUS_TASK_DESC,
				FORMAT(ORDER_DATE, 'dd/MM/yyyy HH:mm:ss') as orderDate,
				ISNULL(CAST((SLA_MINUTES / 60) AS VARCHAR), '-') as jam,
				ISNULL(CAST((SLA_MINUTES % 60) AS VARCHAR), '-') as menit,
				ISNULL(FORMAT(STATUS_DATE, 'dd/MM/yyyy HH:mm:ss'), '-') as statusDate,
				(CASE
					WHEN STATUS_DATE IS NULL OR STATUS_CODE = 'D' THEN 'D'
					WHEN APPROVAL_DATE IS NOT NULL THEN 'F'
					WHEN SLA_MINUTES - :batasHijau <= 0 THEN 'G'
					WHEN SLA_MINUTES - :batasKuning <= 0 THEN 'Y'
					ELSE 'R'
				END) as color,
				DEALER_ID, DEALER_NAME,
				ORDER_NO, TASK_ID, CUSTOMER_NAME
		  FROM N
		 ORDER BY LOGIN_ID, ORDER_DATE
		 ]]>
	</sql-query>
	
	<sql-query name="inquiry.ordermonitoring.orderMonitoringListBranchExc">
		<query-param name="id" type="string" />
		<query-param name="statusApprove" type="string"/>
		<query-param name="batasHijau" type="int"/>
		<query-param name="batasKuning" type="int"/>
		<![CDATA[
		WITH N AS (
		  select	TTH.UUID_TASK_H, TTH.TASK_ID, TTH.CUSTOMER_NAME, TTH.APPROVAL_DATE,
					TTOD.DTM_CRT AS ORDER_DATE, TTOD.ORDER_NO, TTOD.DEALER_ID, MD.DEALER_NAME, 
					AMU.UUID_MS_USER, AMU.LOGIN_ID, AMU.FULL_NAME,
					TTH.UUID_STATUS_TASK, MST.STATUS_CODE, MST.STATUS_TASK_DESC,			
					dbo.GET_DATEDIFFWITHHOLIDAY(TTH.UUID_BRANCH, TTHI.DTM_CRT) as SLA_MINUTES,
					TTHI.DTM_CRT AS STATUS_DATE
			from tr_task_h TTH with (nolock)
				join AM_MSUSER AMU with (nolock) on TTH.UUID_MS_USER = AMU.UUID_MS_USER
				join AM_MSSUBSYSTEM AMS with (nolock) on AMS.UUID_MS_SUBSYSTEM = AMU.UUID_MS_SUBSYSTEM
				join MS_STATUSTASK MST with (nolock) on TTH.UUID_STATUS_TASK = MST.UUID_STATUS_TASK
				left join TR_TASKHISTORY TTHI with (nolock) on (TTHI.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK and TTH.UUID_TASK_H = TTHI.UUID_TASK_H)
				left join TR_TASKORDERDATA TTOD with (nolock) on TTH.UUID_TASK_H = TTOD.UUID_TASK_Id
				left join MS_DEALER MD with (nolock) on MD.UUID_DEALER = TTOD.DEALER_ID
			where TTH.UUID_BRANCH = :id
				and AMS.SUBSYSTEM_NAME = 'MO'
				and (TTH.APPROVAL_DATE IS NULL OR
					 TTH.APPROVAL_DATE BETWEEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00' AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997')
				and TTH.DTM_UPD BETWEEN (
						CASE
						  WHEN MST.STATUS_CODE IN ('R', :statusApprove, 'D') 
							THEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00'
						  ELSE '1990-01-01 00:00:00'
						END) AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997'
		)
		SELECT	UUID_TASK_H, UUID_MS_USER, LOGIN_ID + ' - ' + FULL_NAME as name,
				UUID_STATUS_TASK, STATUS_TASK_DESC,
				FORMAT(ORDER_DATE, 'dd/MM/yyyy HH:mm:ss') as orderDate,
				ISNULL(CAST((SLA_MINUTES / 60) AS VARCHAR), '-') as jam,
				ISNULL(CAST((SLA_MINUTES % 60) AS VARCHAR), '-') as menit,
				ISNULL(FORMAT(STATUS_DATE, 'dd/MM/yyyy HH:mm:ss'), '-') as statusDate,
				(CASE
					WHEN STATUS_DATE IS NULL OR STATUS_CODE = 'D' THEN 'D'
					WHEN APPROVAL_DATE IS NOT NULL THEN 'F'
					WHEN SLA_MINUTES - :batasHijau <= 0 THEN 'G'
					WHEN SLA_MINUTES - :batasKuning <= 0 THEN 'Y'
					ELSE 'R'
				END) as color,
				DEALER_ID, DEALER_NAME,
				ORDER_NO, TASK_ID, CUSTOMER_NAME
		  FROM N
		 ORDER BY LOGIN_ID, ORDER_DATE
		 ]]>
	</sql-query>
	
	<sql-query name="inquiry.ordermonitoring.orderMonitoringListByDealer">
		<query-param name="id" type="string" />
		<query-param name="statusApprove" type="string"/>
		<query-param name="batasHijau" type="int"/>
		<query-param name="batasKuning" type="int"/>
		<![CDATA[
		WITH N AS (
		  select	TTH.UUID_TASK_H, TTH.TASK_ID, TTH.CUSTOMER_NAME, TTH.APPROVAL_DATE,
					TTOD.DTM_CRT AS ORDER_DATE, TTOD.ORDER_NO, TTOD.DEALER_ID, MD.DEALER_NAME, 
					AMU.UUID_MS_USER, AMU.LOGIN_ID, AMU.FULL_NAME,
					TTH.UUID_STATUS_TASK, MST.STATUS_CODE, MST.STATUS_TASK_DESC,			
					dbo.GET_DATEDIFFWITHHOLIDAY(TTH.UUID_BRANCH, TTHI.DTM_CRT) as SLA_MINUTES,
					TTHI.DTM_CRT AS STATUS_DATE
			from tr_task_h TTH with (nolock)
				join AM_MSUSER AMU with (nolock) on TTH.UUID_MS_USER = AMU.UUID_MS_USER
				join AM_MSSUBSYSTEM AMS with (nolock) on AMS.UUID_MS_SUBSYSTEM = AMU.UUID_MS_SUBSYSTEM
				join MS_STATUSTASK MST with (nolock) on TTH.UUID_STATUS_TASK = MST.UUID_STATUS_TASK
				left join TR_TASKHISTORY TTHI with (nolock) on (TTHI.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK and TTH.UUID_TASK_H = TTHI.UUID_TASK_H)
				left join TR_TASKORDERDATA TTOD with (nolock) on TTH.UUID_TASK_H = TTOD.UUID_TASK_Id
				left join MS_DEALER MD with (nolock) on MD.UUID_DEALER = TTOD.DEALER_ID
			where TTOD.DEALER_ID = :id
				and AMS.SUBSYSTEM_NAME = 'MO'
				and (TTH.APPROVAL_DATE IS NULL OR
					 TTH.APPROVAL_DATE BETWEEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00' AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997')
				and TTH.DTM_UPD BETWEEN (
						CASE
						  WHEN MST.STATUS_CODE IN ('R', :statusApprove, 'D') 
							THEN FORMAT(GETDATE(), 'yyyy-MM-dd') +' 00:00:00'
						  ELSE '1990-01-01 00:00:00'
						END) AND FORMAT(GETDATE(), 'yyyy-MM-dd') +' 23:59:59.997'
		)
		SELECT	UUID_TASK_H, UUID_MS_USER, LOGIN_ID + ' - ' + FULL_NAME as name,
				UUID_STATUS_TASK, STATUS_TASK_DESC,
				FORMAT(ORDER_DATE, 'dd/MM/yyyy HH:mm:ss') as orderDate,
				ISNULL(CAST((SLA_MINUTES / 60) AS VARCHAR), '-') as jam,
				ISNULL(CAST((SLA_MINUTES % 60) AS VARCHAR), '-') as menit,
				ISNULL(FORMAT(STATUS_DATE, 'dd/MM/yyyy HH:mm:ss'), '-') as statusDate,
				(CASE
					WHEN STATUS_DATE IS NULL OR STATUS_CODE = 'D' THEN 'D'
					WHEN APPROVAL_DATE IS NOT NULL THEN 'F'
					WHEN SLA_MINUTES - :batasHijau <= 0 THEN 'G'
					WHEN SLA_MINUTES - :batasKuning <= 0 THEN 'Y'
					ELSE 'R'
				END) as color,
				DEALER_ID, DEALER_NAME,
				ORDER_NO, TASK_ID, CUSTOMER_NAME
		  FROM N
		 ORDER BY LOGIN_ID, ORDER_DATE
		 ]]>
	</sql-query>
</hibernate-mapping>