<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
    
<hibernate-mapping>
	<sql-query name="report.dashboardanalyticsorder.getPerformanceMTD">
		<query-param name="uuidBranch" type="long"/>
		<query-param name="uuidSubsystem" type="long"/>
		SELECT DISTINCT N.STATUS, N.JUMLAH, N.DAYDATE 
		FROM (
			SELECT 'N' STATUS, COUNT(1) JUMLAH, DAY(H.SUBMIT_DATE) DAYDATE
			FROM TR_TASK_H H WITH (NOLOCK)
				JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
			WHERE H.UUID_BRANCH = :uuidBranch
				AND <PERSON><PERSON><PERSON>BMIT_DATE BETWEEN DATEADD(M, DATEDIFF(M, 0, GETDATE()), 0) 
				AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112))
				AND MST.UUID_MS_SUBSYSTEM = :uuidSubsystem
			GROUP BY DAY(H.SUBMIT_DATE)
			UNION ALL
			SELECT 'GLV' STATUS, COUNT(1) JUMLAH, DAY(H.DTM_UPD) DAYDATE
			FROM TR_TASK_H H WITH (NOLOCK)
				JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
			WHERE H.UUID_BRANCH = :uuidBranch
				AND H.DTM_UPD BETWEEN DATEADD(M, DATEDIFF(M, 0, GETDATE()), 0) 
				AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112))
				AND MST.STATUS_CODE IN ('GLV')
			GROUP BY DAY(H.DTM_UPD)
			UNION ALL
			SELECT 'GLV' STATUS, COUNT(1) JUMLAH, DAY(H.DTM_UPD) DAYDATE
			FROM FINAL_TR_TASK_H H WITH (NOLOCK)
				JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
			WHERE H.UUID_BRANCH = :uuidBranch
				AND H.DTM_UPD BETWEEN DATEADD(M, DATEDIFF(M, 0, GETDATE()), 0) 
				AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112))
				AND MST.STATUS_CODE IN ('GLV')
			GROUP BY DAY(H.DTM_UPD)
		) N
	</sql-query>
	<sql-query name="report.dashboardanalyticsorder.getPerformanceToday">
		<query-param name="uuidBranch" type="long"/>
		<query-param name="uuidSubsystem" type="long"/>
		SELECT DISTINCT N.STATUS, N.JUMLAH, N.DAYDATE 
		FROM (
			SELECT 'N' STATUS, COUNT(1) JUMLAH, DAY(H.SUBMIT_DATE) DAYDATE
			FROM TR_TASK_H H WITH (NOLOCK)
				JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
			WHERE H.UUID_BRANCH = :uuidBranch
				AND H.SUBMIT_DATE BETWEEN CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112)) 
				AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE()+1,112))
				AND MST.UUID_MS_SUBSYSTEM = :uuidSubsystem
			GROUP BY DAY(H.SUBMIT_DATE)
			UNION ALL
			SELECT 'GLV' STATUS, COUNT(1) JUMLAH, DAY(H.DTM_UPD) DAYDATE
			FROM TR_TASK_H H WITH (NOLOCK)
				JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
			WHERE H.UUID_BRANCH = :uuidBranch
				AND H.DTM_UPD BETWEEN CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112)) 
				AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE()+1,112))
				AND MST.STATUS_CODE IN ('GLV')
			GROUP BY DAY(H.DTM_UPD)
			UNION ALL
			SELECT 'GLV' STATUS, COUNT(1) JUMLAH, DAY(H.DTM_UPD) DAYDATE
			FROM FINAL_TR_TASK_H H WITH (NOLOCK)
				JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
			WHERE H.UUID_BRANCH = :uuidBranch
				AND H.DTM_UPD BETWEEN CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112)) 
				AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE()+1,112))
				AND MST.STATUS_CODE IN ('GLV')
			GROUP BY DAY(H.DTM_UPD)
		) N
	</sql-query>
	<sql-query name="report.dashboardanalyticsorder.getDealerStatusMTD">
		<query-param name="uuidBranch" type="long"/>
		<query-param name="uuidSubsystem" type="long"/>
		SELECT MD.DEALER_NAME, (
			SELECT SUM(N.JUMLAH) TOTAL 
			FROM (
		  		SELECT COUNT(UUID_TASK_H) JUMLAH
		  		FROM TR_TASK_H H WITH (NOLOCK)
		  			JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
		  			JOIN TR_TASKORDERDATA TTOD ON H.UUID_TASK_H = TTOD.UUID_TASK_ID
		  		WHERE H.UUID_BRANCH = :uuidBranch
		  			AND H.SUBMIT_DATE BETWEEN DATEADD(M, DATEDIFF(M, 0, GETDATE()), 0) 
		  			AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112))
		  			AND MST.UUID_MS_SUBSYSTEM = :uuidSubsystem
		  			AND TTOD.DEALER_ID = MD.UUID_DEALER
		  		UNION ALL
		  		SELECT COUNT(UUID_TASK_H) JUMLAH
		  		FROM FINAL_TR_TASK_H H WITH (NOLOCK)
		  			JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
		  			JOIN FINAL_TR_TASKORDERDATA TTOD ON H.UUID_TASK_H = TTOD.UUID_TASK_ID
		  		WHERE H.UUID_BRANCH = :uuidBranch
		  			AND H.SUBMIT_DATE BETWEEN DATEADD(M, DATEDIFF(M, 0, GETDATE()), 0) 
		  			AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112))
		  			AND MST.UUID_MS_SUBSYSTEM = :uuidSubsystem
		  			AND TTOD.DEALER_ID = MD.UUID_DEALER
		  	) N
		) ORDERIN, (
		  	SELECT SUM(N.JUMLAH) TOTAL 
		  	FROM (
		  		SELECT COUNT(UUID_TASK_H) JUMLAH
		  		FROM TR_TASK_H H WITH (NOLOCK)
		  			JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
		  			JOIN TR_TASKORDERDATA TTOD ON H.UUID_TASK_H = TTOD.UUID_TASK_ID
		  		WHERE H.UUID_BRANCH = :uuidBranch
		  			AND H.DTM_UPD BETWEEN DATEADD(M, DATEDIFF(M, 0, GETDATE()), 0) 
		  			AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112))
		  			AND MST.STATUS_CODE IN ('GLV')
		  			AND TTOD.DEALER_ID = MD.UUID_DEALER
		  		UNION ALL
		  		SELECT COUNT(UUID_TASK_H) JUMLAH
		  		FROM FINAL_TR_TASK_H H WITH (NOLOCK)
		  			JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
		  			JOIN FINAL_TR_TASKORDERDATA TTOD ON H.UUID_TASK_H = TTOD.UUID_TASK_ID
		  		WHERE H.UUID_BRANCH = :uuidBranch
		  			AND H.DTM_UPD BETWEEN DATEADD(M, DATEDIFF(M, 0, GETDATE()), 0) 
		  			AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112))
		  			AND MST.STATUS_CODE IN ('GLV')
		  			AND TTOD.DEALER_ID = MD.UUID_DEALER
		  	) N
		) GOLIVE
		FROM MS_DEALEROFBRANCH DOB
			JOIN MS_DEALER MD ON DOB.UUID_DEALER = MD.UUID_DEALER
		WHERE DOB.UUID_BRANCH = :uuidBranch
			AND MD.DEALER_CODE != 'HO'
	</sql-query>
	<sql-query name="report.dashboardanalyticsorder.getDealerStatusToday">
		<query-param name="uuidBranch" type="long"/>
		<query-param name="uuidSubsystem" type="long"/>
		SELECT MD.DEALER_NAME, (
			SELECT SUM(N.JUMLAH) TOTAL 
			FROM (
				SELECT COUNT(UUID_TASK_H) JUMLAH
				FROM TR_TASK_H H WITH (NOLOCK)
					JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
					JOIN TR_TASKORDERDATA TTOD ON H.UUID_TASK_H = TTOD.UUID_TASK_ID
				WHERE H.UUID_BRANCH = :uuidBranch
					AND H.SUBMIT_DATE BETWEEN CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112)) 
					AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE()+1,112))
					AND MST.UUID_MS_SUBSYSTEM = :uuidSubsystem
					AND TTOD.DEALER_ID = MD.UUID_DEALER
				UNION ALL
				SELECT COUNT(UUID_TASK_H) JUMLAH
				FROM FINAL_TR_TASK_H H WITH (NOLOCK)
					JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
					JOIN FINAL_TR_TASKORDERDATA TTOD ON H.UUID_TASK_H = TTOD.UUID_TASK_ID
				WHERE H.UUID_BRANCH = :uuidBranch
					AND H.SUBMIT_DATE BETWEEN CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112)) 
					AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE()+1,112))
					AND MST.UUID_MS_SUBSYSTEM = :uuidSubsystem
					AND TTOD.DEALER_ID = MD.UUID_DEALER
			) N
		) ORDERIN, (
			SELECT SUM(N.JUMLAH) TOTAL 
			FROM (
				SELECT COUNT(UUID_TASK_H) JUMLAH
				FROM TR_TASK_H H WITH (NOLOCK)
					JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
					JOIN TR_TASKORDERDATA TTOD ON H.UUID_TASK_H = TTOD.UUID_TASK_ID
				WHERE H.UUID_BRANCH = :uuidBranch
					AND H.DTM_UPD BETWEEN CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112)) 
					AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE()+1,112))
					AND MST.STATUS_CODE IN ('GLV')
					AND TTOD.DEALER_ID = MD.UUID_DEALER
				UNION ALL
				SELECT COUNT(UUID_TASK_H) JUMLAH
				FROM FINAL_TR_TASK_H H WITH (NOLOCK)
					JOIN MS_STATUSTASK MST ON MST.UUID_STATUS_TASK = H.UUID_STATUS_TASK
					JOIN FINAL_TR_TASKORDERDATA TTOD ON H.UUID_TASK_H = TTOD.UUID_TASK_ID
				WHERE H.UUID_BRANCH = :uuidBranch
					AND H.DTM_UPD BETWEEN CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112)) 
					AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE()+1,112))
					AND MST.STATUS_CODE IN ('GLV')
					AND TTOD.DEALER_ID = MD.UUID_DEALER
			) N
		) GOLIVE
		FROM MS_DEALEROFBRANCH DOB
			JOIN MS_DEALER MD ON DOB.UUID_DEALER = MD.UUID_DEALER
		WHERE DOB.UUID_BRANCH = :uuidBranch
		AND MD.DEALER_CODE != 'HO'
	</sql-query>
	<sql-query name="report.dashboardanalyticsorder.getTodayStatus">
		<query-param name="uuidBranch" type="long"/>
		<query-param name="uuidSubsystem" type="long"/>
		SELECT STATUS_LABEL, STATUS_TASK_DESC, COLOR, (
			SELECT SUM(JUMLAH) 
			FROM (
				SELECT COUNT(UUID_TASK_H) JUMLAH
				FROM TR_TASK_H H WITH (NOLOCK)
				WHERE H.UUID_BRANCH = :uuidBranch
					AND H.DTM_UPD BETWEEN CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112)) 
					AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE()+1,112))
					AND H.UUID_STATUS_TASK = MST.UUID_STATUS_TASK
				UNION ALL
				SELECT COUNT(UUID_TASK_H) JUMLAH
				FROM FINAL_TR_TASK_H H WITH (NOLOCK)
				WHERE H.UUID_BRANCH = :uuidBranch
					AND H.DTM_UPD BETWEEN CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE(),112)) 
					AND CONVERT(DATETIME,CONVERT(VARCHAR(8),GETDATE()+1,112))
					AND H.UUID_STATUS_TASK = MST.UUID_STATUS_TASK
			) N
		) JUMLAH
		FROM [DBO].[MS_STATUSTASK] MST
		WHERE UUID_MS_SUBSYSTEM = :uuidSubsystem
			AND IS_VISIBLE = '1'
		ORDER BY SEQ_ORDER
	</sql-query>
	
</hibernate-mapping>    