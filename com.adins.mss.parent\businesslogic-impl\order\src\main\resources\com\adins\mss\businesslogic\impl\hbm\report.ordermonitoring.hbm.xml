<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
    
<hibernate-mapping>
	<sql-query name="report.ordermonitoring.getListBranch">
		<query-param name="branchId" type="string"/>
		select keyValue uuid, branch_code + ' - ' + branch_name bcode 
		from dbo.getCabangByLogin(:branchId)
	</sql-query>
	
	<sql-query name="report.ordermonitoring.getListDealer">
		<query-param name="dealerId" type="string"/>
		select keyValue uuid, dealerName dcode 
		from dbo.getDealerByLogin(:dealerId)
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReportAll">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select branchName, sum(totalTaskOrder) as totalTaskOrder,
			sum(taskOrderApproved) as taskOrderApproved,
			sum(taskOrderCanceled) as taskOrderCanceled,
			sum(taskOrderRejected) as taskOrderRejected,
			sum(taskOrderProgress) as taskOrderProgress, uuid
		from (
			select msb.BRANCH_NAME as branchName, count(1) as totalTaskOrder,
  				sum(case when mst.STATUS_CODE=:statusApprove then 1 else 0 end) as taskOrderApproved,
 				sum(case when mst.STATUS_CODE='D' then 1 else 0 end) as taskOrderCanceled,
				sum(case when mst.STATUS_CODE='R' then 1 else 0 end) as taskOrderRejected,
				sum(case when mst.STATUS_CODE not in (:statusApprove,'R','D') 
					then 1 else 0 end) as taskOrderProgress, msb.UUID_BRANCH uuid
			from TR_TASKORDERDATA ttod with (nolock) 
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H 
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join (
					SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
					FROM dbo.getCabangByLogin(:branchId)
				) msb on tth.UUID_BRANCH = msb.UUID_BRANCH
				join MS_DEALEROFBRANCH mdb with(nolock) on ttod.DEALER_ID = mdb.UUID_DEALER 
					and mdb.uuid_branch = tth.UUID_BRANCH
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msb.BRANCH_NAME,msb.uuid_branch		
			UNION ALL
			select msb.BRANCH_NAME as branchName, count(1) as totalTaskOrder,
	  			sum(case when mst.STATUS_CODE=:statusApprove then 1 else 0 end) as taskOrderApproved,
	 			sum(case when mst.STATUS_CODE='D' then 1 else 0 end) as taskOrderCanceled,
				sum(case when mst.STATUS_CODE='R' then 1 else 0 end) as taskOrderRejected,
				sum(case when mst.STATUS_CODE not in (:statusApprove,'R','D') 
					then 1 else 0 end) as taskOrderProgress, msb.UUID_BRANCH uuid			
			from FINAL_TR_TASKORDERDATA ttod with (nolock) 
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H 
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join (
					SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
					FROM dbo.getCabangByLogin(:branchId)
				) msb on tth.UUID_BRANCH = msb.UUID_BRANCH
				join MS_DEALEROFBRANCH mdb with(nolock) on ttod.DEALER_ID = mdb.UUID_DEALER 
					and mdb.uuid_branch = tth.UUID_BRANCH
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msb.BRANCH_NAME,msb.uuid_branch
		) b
		group by branchName, uuid
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReport">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select branchName, sum(totalTaskOrder) as totalTaskOrder,
			sum(taskOrderApproved) as taskOrderApproved,
			sum(taskOrderCanceled) as taskOrderCanceled,
			sum(taskOrderRejected) as taskOrderRejected,
			sum(taskOrderProgress) as taskOrderProgress, uuid
		from (
			select msb.BRANCH_NAME as branchName, count(1) as totalTaskOrder,
	  			sum(case when mst.STATUS_CODE=:statusApprove then 1 else 0 end) as taskOrderApproved,
	 			sum(case when mst.STATUS_CODE='D' then 1 else 0 end) as taskOrderCanceled,
				sum(case when mst.STATUS_CODE='R' then 1 else 0 end) as taskOrderRejected,
				sum(case when mst.STATUS_CODE not in (:statusApprove,'R','D') 
					then 1 else 0 end) as taskOrderProgress, msb.UUID_BRANCH uuid
			from TR_TASKORDERDATA ttod with (nolock) 
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H 
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join MS_DEALEROFBRANCH mdb with(nolock) on ttod.DEALER_ID = mdb.UUID_DEALER 
					and mdb.uuid_branch = tth.UUID_BRANCH
			where 
				tth.FLAG_SOURCE in ('MO','SMO')
				and msb.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msb.BRANCH_NAME,msb.uuid_branch
			UNION ALL
			select msb.BRANCH_NAME as branchName, count(1) as totalTaskOrder,
	  			sum(case when mst.STATUS_CODE=:statusApprove then 1 else 0 end) as taskOrderApproved,
	 			sum(case when mst.STATUS_CODE='D' then 1 else 0 end) as taskOrderCanceled,
				sum(case when mst.STATUS_CODE='R' then 1 else 0 end) as taskOrderRejected,
				sum(case when mst.STATUS_CODE not in (:statusApprove,'R','D') 
					then 1 else 0 end) as taskOrderProgress, msb.UUID_BRANCH uuid
			from FINAL_TR_TASKORDERDATA ttod with (nolock) 
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H 
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join MS_DEALEROFBRANCH mdb with(nolock) on ttod.DEALER_ID = mdb.UUID_DEALER 
					and mdb.uuid_branch = tth.UUID_BRANCH
			where tth.FLAG_SOURCE in ('MO','SMO')
				and msb.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msb.BRANCH_NAME,msb.uuid_branch
		) b
		group by branchName, uuid
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReportDetailAll">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		SELECT * 
		FROM (
			select msb.BRANCH_NAME as branchName, msd.DEALER_NAME as dealerName,
				ttod.ORDER_NO as orderNo, mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId,
				'1' as flag 
			from TR_TASKORDERDATA ttod with(nolock)
				join TR_TASK_H tth with(nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with(nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with(nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join MS_BRANCH msb with(nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
			where 
				tth.FLAG_SOURCE in ('MO','SMO')
				and msb.UUID_BRANCH = :branchId
				and msd.UUID_DEALER in (
					select uuid_dealer 
					from MS_DEALEROFBRANCH 
					where uuid_branch =:branchId
				)
				and ttod.DTM_CRT BETWEEN :startDate and :endDate		
			UNION ALL
			select msb.BRANCH_NAME as branchName, msd.DEALER_NAME as dealerName,
				ttod.ORDER_NO as orderNo, mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId,
				'2' as flag 
			from FINAL_TR_TASKORDERDATA ttod with(nolock)
				join FINAL_TR_TASK_H tth with(nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with(nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with(nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join MS_BRANCH msb with(nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
			where tth.FLAG_SOURCE in ('MO','SMO')
				and msb.UUID_BRANCH = :branchId
				and msd.UUID_DEALER in (
					select uuid_dealer 
					from MS_DEALEROFBRANCH 
					where uuid_branch =:branchId
				)
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.orderNo 
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReportDetail">
		<query-param name="branchId" type="string"/>
		<query-param name="dealerId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		SELECT * 
		FROM (
			select msb.BRANCH_NAME as branchName, msd.DEALER_NAME as dealerName,
				ttod.ORDER_NO as orderNo, mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId,
				'1' as flag 
			from TR_TASKORDERDATA ttod with (nolock) 
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
			where tth.FLAG_SOURCE in ('MO','SMO')
				and msb.UUID_BRANCH = :branchId
				and msd.UUID_DEALER = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate		
			UNION ALL
			select msb.BRANCH_NAME as branchName, msd.DEALER_NAME as dealerName,
				ttod.ORDER_NO as orderNo, mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId,
				'2' as flag 
			from FINAL_TR_TASKORDERDATA ttod with (nolock) 
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
			where tth.FLAG_SOURCE in ('MO','SMO')
				and msb.UUID_BRANCH = :branchId
				and msd.UUID_DEALER = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.orderNo
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReportDealerAll">
		<query-param name="dealerId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT name, sum(totalTaskOrder), sum(taskOrderApproved) as taskOrderApproved,
			sum(taskOrderCanceled) as taskOrderCanceled, sum(taskOrderRejected) as taskOrderRejected,
			sum(taskOrderProgress) as taskOrderProgress
		FROM (
			select msd.DEALER_NAME name, count(1) as totalTaskOrder,
		  		sum(case when mst.STATUS_CODE=:statusApprove then 1 else 0 end) as taskOrderApproved,
		  		sum(case when mst.STATUS_CODE='D' then 1 else 0 end) as taskOrderCanceled,
				sum(case when mst.STATUS_CODE='R' then 1 else 0 end) as taskOrderRejected,
				sum(case when mst.STATUS_CODE not in (:statusApprove,'R','D') 
					then 1 else 0 end) taskOrderProgress
			from TR_TASKORDERDATA ttod with (nolock) 
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H 
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dbl on dbl.UUID_DEALER = msd.UUID_DEALER
			where tth.FLAG_SOURCE in ('MO','SMO') 
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msd.DEALER_NAME		
			UNION ALL
			select msd.DEALER_NAME name, count(1) as totalTaskOrder,
			  	sum(case when mst.STATUS_CODE=:statusApprove then 1 else 0 end) as taskOrderApproved,
			  	sum(case when mst.STATUS_CODE='D' then 1 else 0 end) as taskOrderCanceled,
				sum(case when mst.STATUS_CODE='R' then 1 else 0 end) as taskOrderRejected,
				sum(case when mst.STATUS_CODE not in (:statusApprove,'R','D') 
					then 1 else 0 end) taskOrderProgress
			from FINAL_TR_TASKORDERDATA ttod with (nolock) 
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H 
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dbl on dbl.UUID_DEALER = msd.UUID_DEALER
			where tth.FLAG_SOURCE in ('MO','SMO') 
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msd.DEALER_NAME
		) a
		group by a.name
		order by a.name asc
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReportDealer">
		<query-param name="dealerId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT name, sum(totalTaskOrder), sum(taskOrderApproved) as taskOrderApproved,
			sum(taskOrderCanceled) as taskOrderCanceled, sum(taskOrderRejected) as taskOrderRejected,
			sum(taskOrderProgress) as taskOrderProgress 
		FROM (
			select msd.DEALER_NAME name, count(1) as totalTaskOrder,
			  	sum(case when mst.STATUS_CODE=:statusApprove then 1 else 0 end) as taskOrderApproved,
			  	sum(case when mst.STATUS_CODE='D' then 1 else 0 end) as taskOrderCanceled,
				sum(case when mst.STATUS_CODE='R' then 1 else 0 end) as taskOrderRejected,
				sum(case when mst.STATUS_CODE not in (:statusApprove,'R','D') 
					then 1 else 0 end) taskOrderProgress
			from TR_TASKORDERDATA ttod with (nolock) 
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H 
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where tth.FLAG_SOURCE in ('MO','SMO') 
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
				and msd.UUID_DEALER = :dealerId
			group by msd.DEALER_NAME
			UNION ALL
			select msd.DEALER_NAME name, count(1) as totalTaskOrder,
			  	sum(case when mst.STATUS_CODE=:statusApprove then 1 else 0 end) as taskOrderApproved,
			  	sum(case when mst.STATUS_CODE='D' then 1 else 0 end) as taskOrderCanceled,
				sum(case when mst.STATUS_CODE='R' then 1 else 0 end) as taskOrderRejected,
				sum(case when mst.STATUS_CODE not in (:statusApprove,'R','D') 
					then 1 else 0 end) taskOrderProgress
			from FINAL_TR_TASKORDERDATA ttod with (nolock) 
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H 
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where tth.FLAG_SOURCE in ('MO','SMO') 
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
				and msd.UUID_DEALER = :dealerId
			group by msd.DEALER_NAME
		) a
		group by a.name
		order by a.name asc
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReportDealerDetailAll">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		SELECT * 
		FROM (
			select msd.DEALER_NAME as dealerName, amu.FULL_NAME salesName,
				ttod.ORDER_NO as orderNo, mst.STATUS_TASK_DESC as statusCode,
				tth.UUID_TASK_H as taskId, '1' as flag 
			from TR_TASKORDERDATA ttod with (nolock) 
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dbl on dbl.UUID_DEALER = ttod.DEALER_ID
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msd.DEALER_NAME as dealerName, amu.FULL_NAME salesName,
				ttod.ORDER_NO as orderNo, mst.STATUS_TASK_DESC as statusCode,
				tth.UUID_TASK_H as taskId, '2' as flag 
			from FINAL_TR_TASKORDERDATA ttod with (nolock) 
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dbl on dbl.UUID_DEALER = ttod.DEALER_ID
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.orderNo
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReportDealerDetail">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		SELECT * 
		FROM (
			select msd.DEALER_NAME as dealerName, amu.FULL_NAME salesName, ttod.ORDER_NO as orderNo,
				mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId, '1' as flag 
			from TR_TASKORDERDATA ttod with (nolock) 
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dbl on dbl.UUID_DEALER = ttod.DEALER_ID
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
				and amu.UUID_MS_USER = :userId
			UNION ALL
			select msd.DEALER_NAME as dealerName, amu.FULL_NAME salesName, ttod.ORDER_NO as orderNo, 
				mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId, '2' as flag 
			from FINAL_TR_TASKORDERDATA ttod with (nolock) 
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dbl on dbl.UUID_DEALER = ttod.DEALER_ID
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
				and amu.UUID_MS_USER = :userId
		) a
		order by a.orderNo
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReportDealerDetailAll2">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		SELECT * 
		FROM (
			select msd.DEALER_NAME as dealerName, amu.FULL_NAME salesName, ttod.ORDER_NO as orderNo,
				mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId, '1' as flag 
			from TR_TASKORDERDATA ttod with (nolock) 
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DEALER_ID = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msd.DEALER_NAME as dealerName, amu.FULL_NAME salesName, ttod.ORDER_NO as orderNo,
				mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId, '2' as flag 
			from FINAL_TR_TASKORDERDATA ttod with (nolock) 
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DEALER_ID = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.orderNo
	</sql-query>
	<sql-query name="report.ordermonitoring.getListReportDealerDetail2">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		SELECT * 
		FROM (
			select msd.DEALER_NAME as dealerName, amu.FULL_NAME salesName, ttod.ORDER_NO as orderNo,
				mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId, '1' as flag 
			from TR_TASKORDERDATA ttod with (nolock) 
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DEALER_ID = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
				and amu.UUID_MS_USER = :userId
			
			UNION ALL
			select msd.DEALER_NAME as dealerName, amu.FULL_NAME salesName, ttod.ORDER_NO as orderNo,
				mst.STATUS_TASK_DESC as statusCode, tth.UUID_TASK_H as taskId, '2' as flag 
			from FINAL_TR_TASKORDERDATA ttod with (nolock) 
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER
			where tth.FLAG_SOURCE in ('MO','SMO')
				and ttod.DEALER_ID = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
				and amu.UUID_MS_USER = :userId
		) a
		order by a.orderNo
	</sql-query>
</hibernate-mapping>    