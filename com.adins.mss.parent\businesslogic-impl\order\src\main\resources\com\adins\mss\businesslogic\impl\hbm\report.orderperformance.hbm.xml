<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
    
<hibernate-mapping>
	<sql-query name="report.orderperformance.getListBranch">
		<query-param name="branchId" type="string"/>
		select keyValue uuid, branch_code + ' - ' + branch_name bcode 
		from dbo.getCabangByLogin(:branchId)
	</sql-query>
	
	<sql-query name="report.orderperformance.getListDealerByBranch">
		<query-param name="branchId" type="string"/>
		select msd.UUID_DEALER keyValue, msd.DEALER_CODE +' - '+ msd.DEALER_NAME name
		from MS_DEALEROFBRANCH msdob with (nolock)
			join MS_DEALER msd with (nolock) on msdob.UUID_DEALER=msd.UUID_DEALER
		where msdob.UUID_BRANCH=:branchId
	</sql-query>
	<sql-query name="report.orderperformance.getListDealer">
		<query-param name="dealerId" type="string"/>
		select keyValue uuid, dealerName dcode 
		from dbo.getDealerByLogin(:dealerId)
	</sql-query>
	<sql-query name="report.orderperformance.getListReportAll">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			select msb.BRANCH_NAME bName, msd.DEALER_NAME dName, ttod.ORDER_NO oNumber,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT rDate,
				CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60),2) sla,
				tth.uuid_branch as branchId
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join TR_TASKHISTORY trth with (nolock) on ttod.UUID_TASK_ID = trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALEROFBRANCH mdob with (nolock) on mdob.UUID_DEALER = ttod.DEALER_ID
				join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)) msb2 on msb2.UUID_BRANCH = mdob.UUID_BRANCH
			WHERE mst.STATUS_CODE in (:statusApprove, 'R', 'D')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msb.BRANCH_NAME bName, msd.DEALER_NAME dName, ttod.ORDER_NO oNumber,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT rDate,
				CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60),2) sla,
				tth.uuid_branch as branchId
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join FINAL_TR_TASKHISTORY trth with (nolock) on ttod.UUID_TASK_ID = trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALEROFBRANCH mdob with (nolock) on mdob.UUID_DEALER = ttod.DEALER_ID
				join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)) msb2 on msb2.UUID_BRANCH = mdob.UUID_BRANCH
			WHERE mst.STATUS_CODE in (:statusApprove, 'R', 'D')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.oNumber			
	</sql-query>
	<sql-query name="report.orderperformance.getListReport">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			select msb.BRANCH_NAME bName, msd.DEALER_NAME dName, ttod.ORDER_NO oNumber,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT rDate,
				CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60),2) sla,
				tth.uuid_branch as branchId
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join TR_TASKHISTORY trth with (nolock) on ttod.UUID_TASK_ID = trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALEROFBRANCH mdob with (nolock) on mdob.UUID_DEALER = ttod.DEALER_ID
				join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)) msb2 on msb2.UUID_BRANCH = mdob.UUID_BRANCH
			WHERE mst.STATUS_CODE in (:statusApprove, 'R', 'D')
				and msb.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msb.BRANCH_NAME bName, msd.DEALER_NAME dName, ttod.ORDER_NO oNumber,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT rDate,
				CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60),2) sla,
				tth.uuid_branch as branchId
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID = tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join FINAL_TR_TASKHISTORY trth with (nolock) on ttod.UUID_TASK_ID = trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALEROFBRANCH mdob with (nolock) on mdob.UUID_DEALER = ttod.DEALER_ID
				join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)) msb2 on msb2.UUID_BRANCH = mdob.UUID_BRANCH
			WHERE mst.STATUS_CODE in (:statusApprove, 'R', 'D')
				and msb.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.oNumber
	</sql-query>
	<sql-query name="report.orderperformance.getListReportDetailAll">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="orderNo" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			SELECT branch, DNAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime,
				full_name, this
			FROM (
				select tth.UUID_TASK_H, msb.BRANCH_NAME as branch, msd.DEALER_NAME dName,
					ttod.ORDER_NO orderNo, AMU.FULL_NAME, ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from TR_TASKORDERDATA ttod with (nolock)
					join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
					join MS_DEALEROFBRANCH mdob with (nolock) on mdob.UUID_DEALER = ttod.DEALER_ID
					join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)) msb2 on msb2.UUID_BRANCH = mdob.UUID_BRANCH
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and tth.uuid_branch = :branchId
					and ttod.DTM_CRT BETWEEN :startDate and :endDate				
			) N 
				JOIN ( 
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC 
						) AS PREV, HIST1.DTM_CRT THIS, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT 
						) AS NEXT1
					FROM TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
			UNION ALL
			SELECT branch, DNAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime,
				full_name, this
			FROM (
				select tth.UUID_TASK_H, msb.BRANCH_NAME as branch, msd.DEALER_NAME dName,
					ttod.ORDER_NO orderNo, AMU.FULL_NAME, ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from FINAL_TR_TASKORDERDATA ttod with (nolock)
					join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join FINAL_TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
					join MS_DEALEROFBRANCH mdob with (nolock) on mdob.UUID_DEALER = ttod.DEALER_ID
					join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)) msb2 on msb2.UUID_BRANCH = mdob.UUID_BRANCH
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and tth.uuid_branch = :branchId
					and ttod.DTM_CRT BETWEEN :startDate and :endDate				
			) N 
				JOIN ( 
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC 
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT 
						) AS NEXT1
					FROM FINAL_TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
		) A
		WHERE ORDERNO like '%'+:orderNo+'%'
		ORDER BY ORDERNO, A.THIS
	</sql-query>
	<sql-query name="report.orderperformance.getListReportDetail">
		<query-param name="branchId" type="string"/>
		<query-param name="dealerId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="orderNo" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			SELECT branch, DNAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime,
				full_name, this
			FROM (
				select tth.UUID_TASK_H, msb.BRANCH_NAME as branch, msd.DEALER_NAME dName,
					ttod.ORDER_NO orderNo, AMU.FULL_NAME, ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from TR_TASKORDERDATA ttod with (nolock)
					join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DEALER_ID  = :dealerId
					and tth.uuid_branch = :branchId
					and ttod.DTM_CRT BETWEEN :startDate and :endDate
			) N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC 
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT 
						) AS NEXT1
					FROM TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
			UNION ALL
			SELECT branch, DNAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime,
				full_name, this
			FROM (
				select tth.UUID_TASK_H, msb.BRANCH_NAME as branch, msd.DEALER_NAME dName,
					ttod.ORDER_NO orderNo, AMU.FULL_NAME, ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from FINAL_TR_TASKORDERDATA ttod with (nolock)
					join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join FINAL_TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DEALER_ID  = :dealerId
					and tth.uuid_branch = :branchId
					and ttod.DTM_CRT BETWEEN :startDate and :endDate
			) N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT ORDER BY TH.DTM_CRT DESC 
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT 
						) AS NEXT1
					FROM FINAL_TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
		) A
		WHERE ORDERNO like '%'+:orderNo+'%'
		ORDER BY ORDERNO, A.THIS
	</sql-query>
	<sql-query name="report.orderperformance.getListReportDealerAll">
		<query-param name="dealerId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			select msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, tth.SUBMIT_DATE subDate, trth.DTM_CRT fDate,
				CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND,tth.SUBMIT_DATE,trth.DTM_CRT )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND,tth.SUBMIT_DATE,trth.DTM_CRT )%60),2)	slaTime
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
			WHERE mst.STATUS_CODE in (:statusApprove, 'R', 'D')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, tth.SUBMIT_DATE subDate, trth.DTM_CRT fDate,
				CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND,tth.SUBMIT_DATE,trth.DTM_CRT )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND,tth.SUBMIT_DATE,trth.DTM_CRT )%60),2)	slaTime
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join FINAL_TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
			WHERE mst.STATUS_CODE in (:statusApprove, 'R', 'D')
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a	
		order by a.orderNo
	</sql-query>
	<sql-query name="report.orderperformance.getListReportDealer">
		<query-param name="dealerId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			select msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, tth.SUBMIT_DATE subDate, trth.DTM_CRT fDate,
				CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND,tth.SUBMIT_DATE,trth.DTM_CRT )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND,tth.SUBMIT_DATE,trth.DTM_CRT )%60),2)	slaTime
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			WHERE mst.STATUS_CODE in (:statusApprove, 'R', 'D')
				and ttod.DEALER_ID = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate	
			UNION ALL
			select msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, tth.SUBMIT_DATE subDate, trth.DTM_CRT fDate,
				CONVERT(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND,tth.SUBMIT_DATE,trth.DTM_CRT )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND,tth.SUBMIT_DATE,trth.DTM_CRT )%60),2)	slaTime
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join FINAL_TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			WHERE mst.STATUS_CODE in (:statusApprove, 'R', 'D')
				and ttod.DEALER_ID = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.orderNo
	</sql-query>
	<sql-query name="report.orderperformance.getListReportDealerDetailAll">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="orderNo" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			SELECT DNAME, FULL_NAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime, this
			FROM (
				select tth.UUID_TASK_H, msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, AMU.FULL_NAME,
					ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from TR_TASKORDERDATA ttod with (nolock)
					join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join (
						select keyValue 
						from dbo.getUserByLogin(:userId)
					) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DTM_CRT BETWEEN :startDate and :endDate
			) N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT 
						) AS NEXT1
					FROM TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
			UNION ALL
			SELECT DNAME, FULL_NAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime, this
			FROM (
				select tth.UUID_TASK_H, msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, AMU.FULL_NAME,
					ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from FINAL_TR_TASKORDERDATA ttod with (nolock)
					join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join FINAL_TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join (
						select keyValue 
						from dbo.getUserByLogin(:userId)
					) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DTM_CRT BETWEEN :startDate and :endDate
			) N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						RDER BY TH.DTM_CRT DESC 
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT 
						) AS NEXT1
					FROM FINAL_TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
		) A
		WHERE ORDERNO like '%'+:orderNo+'%'
		ORDER BY ORDERNO, A.THIS
	</sql-query>
	<sql-query name="report.orderperformance.getListReportDealerDetail">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="orderNo" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			SELECT DNAME, FULL_NAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime, THIS
			FROM (
				select tth.UUID_TASK_H, msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, AMU.FULL_NAME,
					ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from TR_TASKORDERDATA ttod with (nolock)
					join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DTM_CRT BETWEEN :startDate and :endDate
					and amu.UUID_MS_USER  = :userId
			)N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT
						) AS NEXT1
					FROM TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst with (nolock) on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
			UNION ALL
			SELECT DNAME, FULL_NAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime, THIS
			FROM (
				select tth.UUID_TASK_H, msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, AMU.FULL_NAME,
					ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from FINAL_TR_TASKORDERDATA ttod with (nolock)
					join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join FINAL_TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DTM_CRT BETWEEN :startDate and :endDate
					and amu.UUID_MS_USER  = :userId
			) N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT
						) AS NEXT1
					FROM FINAL_TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst with (nolock) on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
		) A
		WHERE ORDERNO like '%'+:orderNo+'%'
		ORDER BY ORDERNO, A.THIS
	</sql-query>
	<sql-query name="report.orderperformance.getListReportDealerDetailAll2">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="orderNo" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			SELECT DNAME, FULL_NAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime, THIS
			FROM (
				select tth.UUID_TASK_H, msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, AMU.FULL_NAME,
					ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from TR_TASKORDERDATA ttod with (nolock)
					join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join (
						select keyValue 
						from dbo.getUserByLogin(:userId)
					) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DEALER_ID = :dealerId
					and ttod.DTM_CRT BETWEEN :startDate and :endDate				
			) N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock)
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT
						) AS NEXT1
					FROM TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
			UNION ALL
			SELECT DNAME, FULL_NAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime, THIS
			FROM (
				select tth.UUID_TASK_H, msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, AMU.FULL_NAME,
					ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from FINAL_TR_TASKORDERDATA ttod with (nolock)
					join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join FINAL_TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
					join (
						select keyValue 
						from dbo.getUserByLogin(:userId)
					) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DEALER_ID = :dealerId
					and ttod.DTM_CRT BETWEEN :startDate and :endDate
			) N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock)
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT
						) AS NEXT1
					FROM FINAL_TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
		) A
		WHERE ORDERNO like '%'+:orderNo+'%'
		ORDER BY ORDERNO, A.THIS
	</sql-query>
	<sql-query name="report.orderperformance.getListReportDealerDetail2">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="orderNo" type="string"/>
		<query-param name="statusApprove" type="string"/>
		SELECT * 
		FROM (
			SELECT DNAME, FULL_NAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime, THIS
			FROM (
				select tth.UUID_TASK_H, msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, AMU.FULL_NAME,
					ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from TR_TASKORDERDATA ttod with (nolock)
					join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				where mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DEALER_ID = :dealerId
					and ttod.DTM_CRT BETWEEN :startDate and :endDate
					and amu.UUID_MS_USER = :userId
			) N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT
						) AS NEXT1
					FROM TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst with (nolock) on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
			UNION ALL
			SELECT DNAME, FULL_NAME, ORDERNO, STATUS_TASK_DESC, 
				CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END AS START, 
				CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END AS ENDDATE,
				CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )/3600)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%3600/60),2)+':'+
				RIGHT('0'+CONVERT(VARCHAR,DATEDIFF(SECOND, 
					(CASE WHEN PREV IS NULL THEN SUBDATE ELSE PREV END), 
					(CASE WHEN NEXT1 IS NULL THEN FDATE ELSE THIS END) )%60),2) AS slaTime, THIS
			FROM (
				select tth.UUID_TASK_H, msd.DEALER_NAME dName, ttod.ORDER_NO orderNo, AMU.FULL_NAME,
					ttod.DTM_CRT subDate, trth.DTM_CRT fDate
				from FINAL_TR_TASKORDERDATA ttod with (nolock)
					join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
					join FINAL_TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
					join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
					join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				where  mst.STATUS_CODE in (:statusApprove, 'R', 'D')
					and ttod.DEALER_ID = :dealerId
					and ttod.DTM_CRT BETWEEN :startDate and :endDate
					and amu.UUID_MS_USER = :userId
			) N 
				JOIN (
					SELECT HIST1.UUID_TASK_H, MST.STATUS_TASK_DESC, 
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND HIST1.DTM_CRT>TH.DTM_CRT 
						ORDER BY TH.DTM_CRT DESC
						) AS PREV, HIST1.DTM_CRT THIS,
						(SELECT TOP 1 TH.DTM_CRT 
						FROM FINAL_TR_TASKHISTORY TH with (nolock) 
						WHERE HIST1.UUID_TASK_H = TH.UUID_TASK_H 
							AND TH.DTM_CRT>HIST1.DTM_CRT 
						ORDER BY TH.DTM_CRT
						) AS NEXT1
					FROM FINAL_TR_TASKHISTORY HIST1 with (nolock)
						JOIN MS_STATUSTASK mst with (nolock) on HIST1.UUID_STATUS_TASK = mst.UUID_STATUS_TASK 
				) AS M ON N.UUID_TASK_H=M.UUID_TASK_H
		) A
		WHERE ORDERNO like '%'+:orderNo+'%'
		ORDER BY ORDERNO, A.THIS
	</sql-query>
</hibernate-mapping>    