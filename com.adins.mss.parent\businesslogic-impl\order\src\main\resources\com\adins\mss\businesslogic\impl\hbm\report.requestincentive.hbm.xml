<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

	<sql-query name="report.requestincentive.report.requestincentiveListUser">
		<query-param name="fullName" type="string"/>
		<query-param name="loginId" type="string"/>
		<query-param name="spvId" type="string"/>
		<query-param name="subsystem" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * 
		from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum 
			FROM (
				SELECT msu.LOGIN_ID, msu.FULL_NAME, ROW_NUMBER() OVER (ORDER BY msu.LOGIN_ID) AS rownum
				FROM AM_MSUSER msu with (nolock)
					LEFT JOIN AM_MSSUBSYSTEM ms with (nolock) ON ms.UUID_MS_SUBSYSTEM = msu.UUID_MS_SUBSYSTEM
				where ms.UUID_MS_SUBSYSTEM = :subsystem
					and msu.SPV_ID = :spvId
					and UPPER(msu.LOGIN_ID) like UPPER('%'+ :loginId +'%')
					and UPPER(msu.FULL_NAME) like UPPER('%'+ :fullName +'%')
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>

	<sql-query name="report.requestincentive.report.requestincentiveCountListUser">
		<query-param name="fullName" type="string"/>
		<query-param name="loginId" type="string"/>
		<query-param name="spvId" type="string"/>
		<query-param name="subsystem" type="string"/>
			SELECT COUNT(1)
			FROM AM_MSUSER msu with (nolock)
				LEFT JOIN AM_MSSUBSYSTEM ms with (nolock) ON ms.UUID_MS_SUBSYSTEM = msu.UUID_MS_SUBSYSTEM
			where ms.UUID_MS_SUBSYSTEM = :subsystem
				and msu.SPV_ID = :spvId
				and UPPER(msu.LOGIN_ID) like UPPER('%'+ :loginId +'%')
				and UPPER(msu.FULL_NAME) like UPPER('%'+ :fullName +'%')
	</sql-query>
</hibernate-mapping>