<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
    
<hibernate-mapping>
	<sql-query name="report.slaorder.getListBranch">
		<query-param name="branchId" type="string"/>
		select keyValue uuid, branch_code + ' - ' + branch_name bcode 
		from dbo.getCabangByLogin(:branchId)
	</sql-query>
	
	<sql-query name="report.slaorder.getListDealerByBranch">
		<query-param name="branchId" type="string"/>
		select msd.UUID_DEALER keyValue, msd.DEALER_CODE +' - '+ msd.DEALER_NAME name
		from MS_DEALEROFBRANCH msdob with (nolock)
			join MS_DEALER msd with (nolock) on msdob.UUID_DEALER=msd.UUID_DEALER
		where msdob.UUID_BRANCH=:branchId
	</sql-query>
	<sql-query name="report.slaorder.getListDealer">
		<query-param name="dealerId" type="string"/>
		select keyValue uuid, dealerName dcode 
		from dbo.getDealerByLogin(:dealerId)
	</sql-query>
	<sql-query name="report.slaorder.getListReportAll">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="sla" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select * 
		from (
			select msb.BRANCH_NAME as name, count(1) as totalTask,
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))/3600) +':'+
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%3600/60)  +':'+
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%60) as times,
				isnull(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then 1 end),0) as befSla,
				isnull(convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgbefSla,
				isnull(sum(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then 1 end),0) as aftSla,
				isnull(convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgaftSla,
				msb.UUID_BRANCH uuidBranch
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALEROFBRANCH mdob with (nolock) on mdob.UUID_DEALER = ttod.DEALER_ID
				join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)) msb2 on msb2.UUID_BRANCH = mdob.UUID_BRANCH
			where st.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msb.BRANCH_NAME,msb.UUID_BRANCH
			UNION ALL
			select msb.BRANCH_NAME as name, count(1) as totalTask,
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))/3600) +':'+
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%3600/60)  +':'+
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%60) as times,
				isnull(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then 1 end),0) as befSla,
				isnull(convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgbefSla,
				isnull(sum(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then 1 end),0) as aftSla,
				isnull(convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgaftSla,
				msb.UUID_BRANCH uuidBranch
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALEROFBRANCH mdob with (nolock) on mdob.UUID_DEALER = ttod.DEALER_ID
				join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchId)) msb2 on msb2.UUID_BRANCH = mdob.UUID_BRANCH
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msb.BRANCH_NAME,msb.UUID_BRANCH
		) a
		order by a.name
	</sql-query>
	<sql-query name="report.slaorder.getListReport">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="sla" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select * 
		from (
			select msb.BRANCH_NAME as name, count(1) as totalTask,
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))/3600) +':'+
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%3600/60)  +':'+
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%60) as times,
				isnull(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla then 1 end),0) as befSla,
				isnull(convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgbefSla,
				isnull(sum(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then 1 end),0) as aftSla,
				isnull(convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgaftSla,
				msb.UUID_BRANCH uuidBranch
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID in (
					select uuid_dealer 
					from MS_DEALEROFBRANCH with (nolock) 
					where UUID_BRANCH = :branchId
				)
				and tth.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msb.BRANCH_NAME,msb.UUID_BRANCH
			UNION ALL
			select msb.BRANCH_NAME as name, count(1) as totalTask,
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))/3600) +':'+
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%3600/60)  +':'+
				convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%60) as times,
				isnull(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then 1 end),0) as befSla,
				isnull(convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgbefSla,
				isnull(sum(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla then 1 end),0) as aftSla,
				isnull(convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
				convert(VARCHAR,avg(case 
					when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
						then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgaftSla,
				msb.UUID_BRANCH uuidBranch
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID in (
					select uuid_dealer 
					from MS_DEALEROFBRANCH with (nolock) 
					where UUID_BRANCH = :branchId
				)
				and tth.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			group by msb.BRANCH_NAME,msb.UUID_BRANCH
		) a
		order by a.name
	</sql-query>
	<sql-query name="report.slaorder.getListReportDetailAll">
		<query-param name="branchId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select * 
		from (
			select msb.BRANCH_NAME bname, msd.DEALER_NAME dname, ttod.ORDER_NO orderno,
				tth.SUBMIT_DATE submitDate, trth.DTM_CRT dateGlv,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID in (
					select uuid_dealer 
					from MS_DEALEROFBRANCH with (nolock) 
					where UUID_BRANCH = :branchId
				)
				and tth.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msb.BRANCH_NAME bname, msd.DEALER_NAME dname, ttod.ORDER_NO orderno,
				tth.SUBMIT_DATE submitDate, trth.DTM_CRT dateGlv,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID in (
					select uuid_dealer 
					from MS_DEALEROFBRANCH with (nolock) 
					where UUID_BRANCH = :branchId
				)
				and tth.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		)a
		order by a.orderno
	</sql-query>
	<sql-query name="report.slaorder.getListReportDetail">
		<query-param name="branchId" type="string"/>
		<query-param name="dealerId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select * 
		from (
			select msb.BRANCH_NAME bname, msd.DEALER_NAME dname, ttod.ORDER_NO orderno,
				tth.SUBMIT_DATE submitDate, trth.DTM_CRT dateGlv,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID = :dealerId
				and tth.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msb.BRANCH_NAME bname, msd.DEALER_NAME dname, ttod.ORDER_NO orderno,
				tth.SUBMIT_DATE submitDate, trth.DTM_CRT dateGlv,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join MS_BRANCH msb with (nolock) on tth.UUID_BRANCH = msb.UUID_BRANCH
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID = :dealerId
				and tth.UUID_BRANCH = :branchId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
	order by a.orderno
	</sql-query>
	<sql-query name="report.slaorder.getListReportDealerAll">
		<query-param name="dealerId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="sla" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select msd.DEALER_NAME, count(1) total,
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))/3600) +':'+
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%3600/60)  +':'+
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%60) as times,
			isnull(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
				then 1 end),0) as befSla,
			isnull(convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgbefSla,
			ISNULL(sum(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then 1 end),0) as aftSla,
			ISNULL(convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60), 0) as avgaftSla,
			msd.UUID_DEALER uuidDealer
 		from TR_TASKORDERDATA ttod with (nolock)
			join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
			join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
			join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
			join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
			join (
				select keyValue 
				from dbo.getUserByLogin(:userId)
			) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
		where mst.STATUS_CODE IN ('GLV', :statusApprove)
			and ttod.DEALER_ID in (
				select keyValue 
				from dbo.getDealerByLogin(:dealerId)
			)
			and ttod.DTM_CRT BETWEEN :startDate and :endDate
		group by msd.DEALER_NAME,msd.UUID_DEALER
		
		UNION ALL
		select msd.DEALER_NAME, count(1) total,
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))/3600) +':'+
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%3600/60)  +':'+
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%60) as times,
			isnull(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
				then 1 end),0) as befSla,
			isnull(convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgbefSla,
			ISNULL(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
				then 1 end),0) as aftSla,
			ISNULL(convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60), 0) as avgaftSla,
			msd.UUID_DEALER uuidDealer
 		from FINAL_TR_TASKORDERDATA ttod with (nolock)
			join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
			join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
			join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
			join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
			join (
				select keyValue 
				from dbo.getUserByLogin(:userId)
			) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
		join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
		where mst.STATUS_CODE IN ('GLV', :statusApprove)
			and ttod.DTM_CRT BETWEEN :startDate and :endDate
		group by msd.DEALER_NAME,msd.UUID_DEALER
	</sql-query>
	<sql-query name="report.slaorder.getListReportDealer">
		<query-param name="dealerId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select msd.DEALER_NAME, count(1) total,
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))/3600) +':'+
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%3600/60)  +':'+
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%60) as times,
			isnull(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
				then 1 end),0) as befSla,
			isnull(convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgbefSla,
			ISNULL(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
				then 1 end),0) as aftSla,
			ISNULL(convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60), 0) as avgaftSla,
			msd.UUID_DEALER uuidDealer
 		from TR_TASKORDERDATA ttod with (nolock)
			join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
			join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
			join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
			join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
			join (
				select keyValue 
				from dbo.getUserByLogin(:userId)
			) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
		where mst.STATUS_CODE IN ('GLV', :statusApprove)
			and ttod.DEALER_ID = :dealerId
			and ttod.DTM_CRT BETWEEN :startDate and :endDate
		group by msd.DEALER_NAME,msd.UUID_DEALER
		UNION ALL
		select msd.DEALER_NAME, count(1) total,
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))/3600) +':'+
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%3600/60)  +':'+
			convert(VARCHAR,sum(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT))%60) as times,
			isnull(sum(case when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
				then 1 end),0) as befSla,
			isnull(convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) between 0 and :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60),0) as avgbefSla,
			ISNULL(sum(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then 1 end),0) as aftSla,
			ISNULL(convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)/3600)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%3600/60)+':'+
			convert(VARCHAR,avg(case 
				when DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) > :sla 
					then DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT) end)%60), 0) as avgaftSla,
			msd.UUID_DEALER uuidDealer
 		from FINAL_TR_TASKORDERDATA ttod with (nolock)
			join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
			join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
			join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
			join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
			join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
			join (
				select keyValue 
				from dbo.getUserByLogin(:userId)
			) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
		where mst.STATUS_CODE IN ('GLV', :statusApprove)
			and ttod.DEALER_ID = :dealerId
			and ttod.DTM_CRT BETWEEN :startDate and :endDate
		group by msd.DEALER_NAME,msd.UUID_DEALER
	</sql-query>
	<sql-query name="report.slaorder.getListReportDealerDetailAll">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select * 
		from (
			select msd.DEALER_NAME dname, amu.FULL_NAME fname, ttod.ORDER_NO orderNo,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT finalDate,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msd.DEALER_NAME dname, amu.FULL_NAME fname, ttod.ORDER_NO orderNo,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT finalDate,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.orderno
	</sql-query>
	<sql-query name="report.slaorder.getListReportDealerDetail">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select * 
		from (
			select msd.DEALER_NAME dname, amu.FULL_NAME fname, ttod.ORDER_NO orderNo,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT finalDate,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and amu.UUID_MS_USER = :userId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msd.DEALER_NAME dname, amu.FULL_NAME fname, ttod.ORDER_NO orderNo,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT finalDate,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (select keyValue as UUID_DEALER from dbo.getDealerByLogin(:dealerId)) dlb on dlb.UUID_DEALER = ttod.DEALER_ID
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and amu.UUID_MS_USER = :userId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.orderno
	</sql-query>
	<sql-query name="report.slaorder.getListReportDealerDetailAll2">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select * 
		from (
			select msd.DEALER_NAME dname, amu.FULL_NAME fname, ttod.ORDER_NO orderNo,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT finalDate,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msd.DEALER_NAME dname, amu.FULL_NAME fname, ttod.ORDER_NO orderNo,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT finalDate,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
				join (
					select keyValue 
					from dbo.getUserByLogin(:userId)
				) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID = :dealerId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.orderno
	</sql-query>
	<sql-query name="report.slaorder.getListReportDealerDetail2">
		<query-param name="dealerId" type="string"/>
		<query-param name="userId" type="string"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
		<query-param name="statusApprove" type="string"/>
		select * 
		from (
			select msd.DEALER_NAME dname, amu.FULL_NAME fname, ttod.ORDER_NO orderNo,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT finalDate,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from TR_TASKORDERDATA ttod with (nolock)
				join TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID = :dealerId
				and amu.UUID_MS_USER = :userId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
			UNION ALL
			select msd.DEALER_NAME dname, amu.FULL_NAME fname, ttod.ORDER_NO orderNo,
				tth.SUBMIT_DATE sDate, trth.DTM_CRT finalDate,
				convert(VARCHAR,DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)/3600) +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%3600/60))  +':'+
				convert(VARCHAR,(DATEDIFF(SECOND, tth.SUBMIT_DATE, trth.DTM_CRT)%60)) as times
			from FINAL_TR_TASKORDERDATA ttod with (nolock)
				join FINAL_TR_TASK_H tth with (nolock) on ttod.UUID_TASK_ID=tth.UUID_TASK_H
				join TR_TASKHISTORY trth with (nolock) on tth.UUID_TASK_H=trth.UUID_TASK_H
				join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
				join MS_DEALER msd with (nolock) on ttod.DEALER_ID = msd.UUID_DEALER
				join AM_MSUSER amu with (nolock) on tth.UUID_MS_USER = amu.UUID_MS_USER
			where mst.STATUS_CODE IN ('GLV', :statusApprove)
				and ttod.DEALER_ID = :dealerId
				and amu.UUID_MS_USER = :userId
				and ttod.DTM_CRT BETWEEN :startDate and :endDate
		) a
		order by a.orderno
	</sql-query>
</hibernate-mapping>    