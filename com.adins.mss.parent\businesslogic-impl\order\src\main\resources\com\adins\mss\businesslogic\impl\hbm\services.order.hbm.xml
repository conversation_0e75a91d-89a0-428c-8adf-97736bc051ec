<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
<!-- CHECK ORDER -->
	
	<!-- Get Order Number and Customer Name by Individuals -->
	<sql-query name="services.order.checkOrder.checkOrder1A">
		<query-param name="uuidUser" type="String"/>
		<query-param name="orderNumber" type="String"/>
			select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			 where tskod.ORDER_NO = :orderNumber
			   and tskh.UUID_MS_USER = :uuidUser
			  union all
			  select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from FINAL_TR_TASK_H tskh with (nolock) inner join FINAL_TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			 where tskod.ORDER_NO = :orderNumber
			   and tskh.UUID_MS_USER = :uuidUser
	</sql-query>
	<sql-query name="services.order.checkOrder.checkOrder2A">
		<query-param name="uuidUser" type="String"/>
		<query-param name="startDate" type="String"/>
		<query-param name="endDate" type="String"/>
			select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			 where tskh.UUID_MS_USER = :uuidUser
			   and tskh.SUBMIT_DATE between :startDate and :endDate
			   union all
			 select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from FINAL_TR_TASK_H tskh with (nolock) inner join FINAL_TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			 where tskh.UUID_MS_USER = :uuidUser
			   and tskh.SUBMIT_DATE between :startDate and :endDate
	</sql-query>
	
	<sql-query name="services.order.checkOrder.checkOrder3A">
		<query-param name="uuidUser" type="String"/>
		<query-param name="custName" type="String"/>
			select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			 where tskh.CUSTOMER_NAME like '%' + :custName + '%'
			   and tskh.UUID_MS_USER = :uuidUser
			  UNION ALL
			 select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from FINAL_TR_TASK_H tskh with (nolock) inner join FINAL_TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			 where tskh.CUSTOMER_NAME like '%' + :custName + '%'
			   and tskh.UUID_MS_USER = :uuidUser
	</sql-query>
	
	<!-- Get Order Number and Customer Name by Supervisor -->
	<sql-query name="services.order.checkOrder.checkOrder1B">
		<query-param name="uuidUser" type="String"/>
		<query-param name="orderNumber" type="String"/>
			select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			  	   inner join AM_MSUSER msu with (nolock) on tskh.UUID_MS_USER = msu.UUID_MS_USER
			 where tskod.ORDER_NO = :orderNumber
			   and msu.SPV_ID = :uuidUser
			 UNION ALL
			select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from FINAL_TR_TASK_H tskh with (nolock) inner join FINAL_TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			  	   inner join AM_MSUSER msu with (nolock) on tskh.UUID_MS_USER = msu.UUID_MS_USER
			 where tskod.ORDER_NO = :orderNumber
			   and msu.SPV_ID = :uuidUser
	</sql-query>
	<sql-query name="services.order.checkOrder.checkOrder2B">
		<query-param name="uuidUser" type="String"/>
		<query-param name="startDate" type="String"/>
		<query-param name="endDate" type="String"/>
			select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			  	   inner join AM_MSUSER msu with (nolock) on tskh.UUID_MS_USER = msu.UUID_MS_USER
			 where msu.SPV_ID = :uuidUser
			   and tskh.SUBMIT_DATE between :startDate and :endDate
			 UNION ALL
			select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from FINAL_TR_TASK_H tskh with (nolock) inner join FINAL_TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			  	   inner join AM_MSUSER msu with (nolock) on tskh.UUID_MS_USER = msu.UUID_MS_USER
			 where msu.SPV_ID = :uuidUser
			   and tskh.SUBMIT_DATE between :startDate and :endDate
	</sql-query>
	
	<sql-query name="services.order.checkOrder.checkOrder3B">
		<query-param name="uuidUser" type="String"/>
		<query-param name="custName" type="String"/>
			select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			  	   inner join AM_MSUSER msu with (nolock) on tskh.UUID_MS_USER = msu.UUID_MS_USER
			 where tskh.CUSTOMER_NAME like '%' + :custName + '%'
			   and msu.SPV_ID = :uuidUser
			   UNION ALL
			 select isnull(tskod.ORDER_NO, '') as "key", isnull(tskh.CUSTOMER_NAME, '') as "value", tskod.UUID_TASK_ID uuid
			  from FINAL_TR_TASK_H tskh with (nolock) inner join FINAL_TR_TASKORDERDATA tskod with (nolock) on tskh.UUID_TASK_H = tskod.UUID_TASK_ID
			  	   inner join AM_MSUSER msu with (nolock) on tskh.UUID_MS_USER = msu.UUID_MS_USER
			 where tskh.CUSTOMER_NAME like '%' + :custName + '%'
			   and msu.SPV_ID = :uuidUser
	</sql-query>

	<!-- CONTENT NEWS -->	
	<sql-query name="services.order.contentNews.getHeader">
		<query-param name="uuid" type="String"/>
			select DISTINCT(ISNULL(msbp.UUID_STK_PROMO, '')) as uuid_mobile_content_h,
				   ISNULL(msp.PROMO_NAME, '') as content_name,
				   ISNULL(msp.PROMO_SHORT_DESC, '') as content_description,
				   ISNULL(replace(convert(varchar(10),msp.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msp.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.ENDEFF_DATE,14),':',''), '') as end_date,
				   ISNULL(msp.PARENT_PROMO_ID, '') as uuid_parent_content
			  from MS_PROMOOFBRANCH msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_BRANCH IN 
			 	   (select dob.UUID_BRANCH 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob with (nolock) on msu.UUID_BRANCH = dob.UUID_BRANCH
					 where UUID_MS_USER = :uuid)
	</sql-query>
	
	
	
	<sql-query name="services.order.contentNews.getHeaderDealer">
		<query-param name="uuid" type="String"/>
			select DISTINCT(ISNULL(msbp.UUID_STK_PROMO, '')) as uuid_mobile_content_h,
				   ISNULL(msp.PROMO_NAME, '') as content_name,
				   ISNULL(msp.PROMO_SHORT_DESC, '') as content_description,
				   ISNULL(replace(convert(varchar(10),msp.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msp.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.ENDEFF_DATE,14),':',''), '') as end_date,
				   ISNULL(msp.PARENT_PROMO_ID, '') as uuid_parent_content
			  from MS_PROMOOFBRANCH msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_BRANCH IN 
			 	   (select dob.UUID_BRANCH 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob with (nolock) on msu.UUID_DEALER = dob.UUID_DEALER
					 where UUID_MS_USER = :uuid)
	</sql-query>
	
	<sql-query name="services.order.contentNews.getHeaderUp">
		<query-param name="uuid" type="String"/>
		<query-param name="uuid_mobile_content_h" type="String"/>
		<query-param name="last_update" type="string"/>
			select msbp.UUID_STK_PROMO as uuid_mobile_content_h,
				   msp.PROMO_NAME as content_name,
				   msp.PROMO_SHORT_DESC as content_description,
				   ISNULL(replace(convert(varchar(10),msp.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msp.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.ENDEFF_DATE,14),':',''), '') as end_date,
				   msp.PARENT_PROMO_ID as uuid_parent_content
			  from MS_PROMOOFBRANCH msbp with (nolock) left join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_BRANCH in 
			 	   (select dob.UUID_BRANCH 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob with (nolock) on msu.UUID_BRANCH = dob.UUID_BRANCH
					 where UUID_MS_USER = :uuid)
			   and msp.UUID_STK_PROMO = :uuid_mobile_content_h
			   and msp.DTM_UPD > :last_update
	</sql-query>
	
	
	<sql-query name="services.order.contentNews.getHeaderUpDealer">
		<query-param name="uuid" type="String"/>
		<query-param name="uuid_mobile_content_h" type="String"/>
		<query-param name="last_update" type="string"/>
			select msbp.UUID_STK_PROMO as uuid_mobile_content_h,
				   msp.PROMO_NAME as content_name,
				   msp.PROMO_SHORT_DESC as content_description,
				   ISNULL(replace(convert(varchar(10),msp.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msp.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.ENDEFF_DATE,14),':',''), '') as end_date,
				   msp.PARENT_PROMO_ID as uuid_parent_content
			  from MS_PROMOOFBRANCH msbp with (nolock) left join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_BRANCH in 
			 	   (select dob.UUID_BRANCH 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob with (nolock) on msu.UUID_DEALER = dob.UUID_DEALER
					 where UUID_MS_USER = :uuid)
			   and msp.UUID_STK_PROMO = :uuid_mobile_content_h
			   and msp.DTM_UPD > :last_update
	</sql-query>
	
	<sql-query name="services.order.contentNews.getHeaderByDealer">
		<query-param name="uuid" type="String"/>
			select DISTINCT(ISNULL(msbp.UUID_STK_PROMO, '')) as uuid_mobile_content_h,
				   ISNULL(msp.PROMO_NAME, '') as content_name,
				   ISNULL(msp.PROMO_SHORT_DESC, '') as content_description,
				   ISNULL(replace(convert(varchar(10),msp.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msp.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.ENDEFF_DATE,14),':',''), '') as end_date,
				   ISNULL(msp.PARENT_PROMO_ID, '') as uuid_parent_content
			    from MS_PROMOOFDEALER msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_DEALER IN 
			 	   (select dob.UUID_DEALER 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob with (nolock) on msu.UUID_DEALER = dob.UUID_DEALER
					 where UUID_MS_USER = :uuid)
	</sql-query>	
	
	
	<sql-query name="services.order.contentNews.getHeaderDealerBranch">
		<query-param name="uuid" type="String"/>
			select DISTINCT(ISNULL(msbp.UUID_STK_PROMO, '')) as uuid_mobile_content_h,
				   ISNULL(msp.PROMO_NAME, '') as content_name,
				   ISNULL(msp.PROMO_SHORT_DESC, '') as content_description,
				   ISNULL(replace(convert(varchar(10),msp.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msp.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.ENDEFF_DATE,14),':',''), '') as end_date,
				   ISNULL(msp.PARENT_PROMO_ID, '') as uuid_parent_content
			    from MS_PROMOOFDEALER msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_DEALER IN 
			 	   (select dob.UUID_DEALER 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob with (nolock) on msu.UUID_BRANCH = dob.UUID_BRANCH
					 where UUID_MS_USER = :uuid)
	</sql-query>
	
	<sql-query name="services.order.contentNews.getHeaderUpByDealer">
		<query-param name="uuid" type="String"/>
		<query-param name="uuid_mobile_content_h" type="String"/>
		<query-param name="last_update" type="string"/>
			select msbp.UUID_STK_PROMO as uuid_mobile_content_h,
				   msp.PROMO_NAME as content_name,
				   msp.PROMO_SHORT_DESC as content_description,
				   ISNULL(replace(convert(varchar(10),msp.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msp.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.ENDEFF_DATE,14),':',''), '') as end_date,
				   msp.PARENT_PROMO_ID as uuid_parent_content
			   from MS_PROMOOFDEALER msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_DEALER IN 
			 	   (select dob.UUID_DEALER 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob with (nolock) on msu.UUID_DEALER = dob.UUID_DEALER
					 where UUID_MS_USER = :uuid)
			   and msp.UUID_STK_PROMO = :uuid_mobile_content_h
			   and msp.DTM_UPD > :last_update
	</sql-query>
	<sql-query name="services.order.contentNews.getHeaderUpByDealerBranch">
		<query-param name="uuid" type="String"/>
		<query-param name="uuid_mobile_content_h" type="String"/>
		<query-param name="last_update" type="string"/>
			select msbp.UUID_STK_PROMO as uuid_mobile_content_h,
				   msp.PROMO_NAME as content_name,
				   msp.PROMO_SHORT_DESC as content_description,
				   ISNULL(replace(convert(varchar(10),msp.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msp.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msp.ENDEFF_DATE,14),':',''), '') as end_date,
				   msp.PARENT_PROMO_ID as uuid_parent_content
			   from MS_PROMOOFDEALER msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_DEALER IN 
			 	   (select dob.UUID_DEALER 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob with (nolock) on msu.UUID_BRANCH = dob.UUID_BRANCH
					 where UUID_MS_USER = :uuid)
			   and msp.UUID_STK_PROMO = :uuid_mobile_content_h
			   and msp.DTM_UPD > :last_update
	</sql-query>
	
	<sql-query name="services.order.contentNews.filter">
		<query-param name="uuid" type="String"/>
		<query-param name="uuid_mobile_content_h" type="String"/>
			select DISTINCT msp.PARENT_PROMO_ID as uuid_parent_content
			  from MS_PROMOOFBRANCH msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_BRANCH IN 
			 	   (select dob.UUID_BRANCH 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob on msu.UUID_BRANCH = dob.UUID_BRANCH
					 where UUID_MS_USER = :uuid)
				   and msbp.UUID_STK_PROMO = :uuid_mobile_content_h
	</sql-query>
	
	<sql-query name="services.order.contentNews.filterDealer">
		<query-param name="uuid" type="String"/>
		<query-param name="uuid_mobile_content_h" type="String"/>
			select DISTINCT msp.PARENT_PROMO_ID as uuid_parent_content
			  from MS_PROMOOFBRANCH msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_BRANCH IN 
			 	   (select dob.UUID_BRANCH 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob on msu.UUID_DEALER = dob.UUID_DEALER
					 where UUID_MS_USER = :uuid)
				   and msbp.UUID_STK_PROMO = :uuid_mobile_content_h
	</sql-query>
	
	<sql-query name="services.order.contentNews.filterByDealer">
		<query-param name="uuid" type="String"/>
		<query-param name="uuid_mobile_content_h" type="String"/>
			select DISTINCT msp.PARENT_PROMO_ID as uuid_parent_content
			  from MS_PROMOOFDEALER msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_DEALER IN 
			 	   (select dob.UUID_DEALER 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob on msu.UUID_DEALER = dob.UUID_DEALER
					 where UUID_MS_USER = :uuid)
				   and msbp.UUID_STK_PROMO = :uuid_mobile_content_h
	</sql-query>
	
	<sql-query name="services.order.contentNews.filterByDealerBranch">
		<query-param name="uuid" type="String"/>
		<query-param name="uuid_mobile_content_h" type="String"/>
			select DISTINCT msp.PARENT_PROMO_ID as uuid_parent_content
			  from MS_PROMOOFDEALER msbp with (nolock) left outer join MS_STKPROMO msp with (nolock)
				on msbp.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msbp.UUID_DEALER IN 
			 	   (select dob.UUID_DEALER 
			 	   	  from AM_MSUSER  msu with (nolock) 
			  	   		   inner join MS_DEALEROFBRANCH dob on msu.UUID_BRANCH = dob.UUID_BRANCH
					 where UUID_MS_USER = :uuid)
				   and msbp.UUID_STK_PROMO = :uuid_mobile_content_h
	</sql-query>
	<sql-query name="services.order.contentNews.getDetailContentP">
		<query-param name="parent" type="String"/>
		<query-param name="uuid_mobile_content_h" type="String"/>
			select msc.UUID_STK_PROMO_CONTENT as uuid_mobile_content_d,
				   msc.IS_ACTIVE as is_sctive,
				   msc.CONTENT as content,
				   msc.CONTENT_TYPE as content_type,
				   msc.SEQUENCE as sequence,
				   msc.USR_CRT as usr_crt,
				   msc.DTM_CRT as dtm_crt,
				   msc.USR_UPD as usr_upd,
				   ISNULL(replace(convert(varchar(10),msc.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msc.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msc.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msc.ENDEFF_DATE,14),':',''), '') as end_date,
				   msc.DTM_UPD as dtm_upd
			  FROM MS_STKPROMOCONTENT msc with (nolock) inner join MS_STKPROMO msp with (nolock) 
			    on msc.UUID_STK_PROMO = msp.UUID_STK_PROMO
			 where msc.UUID_STK_PROMO = :uuid_mobile_content_h and msp.PARENT_PROMO_ID = :parent
	</sql-query>
	
	<sql-query name="services.order.contentNews.getDetailContentNP">
		<query-param name="uuid_mobile_content_h" type="String"/>
			select msc.UUID_STK_PROMO_CONTENT as uuid_mobile_content_d,
				   msc.IS_ACTIVE as is_sctive,
				   msc.CONTENT as content,
				   msc.CONTENT_TYPE as content_type,
				   msc.SEQUENCE as sequence,
				   msc.USR_CRT as usr_crt,
				   msc.DTM_CRT as dtm_crt,
				   msc.USR_UPD as usr_upd,
				   ISNULL(replace(convert(varchar(10),msc.STARTEFF_DATE,103),'/','')+replace(convert(varchar(8),msc.STARTEFF_DATE,14),':',''), '') as start_date,
				   ISNULL(replace(convert(varchar(10),msc.ENDEFF_DATE,103),'/','')+replace(convert(varchar(8),msc.ENDEFF_DATE,14),':',''), '') as end_date,
				   msc.DTM_UPD as dtm_upd
			  FROM MS_STKPROMOCONTENT msc with (nolock)
			 where msc.UUID_STK_PROMO = :uuid_mobile_content_h
	</sql-query>
</hibernate-mapping>