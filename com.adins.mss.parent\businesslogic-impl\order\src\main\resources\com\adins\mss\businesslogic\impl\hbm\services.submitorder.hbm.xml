<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
<!-- SUBMIT ORDER -->
	<sql-query name="services.submitorder.getNextUser">
		<query-param name="uuidManager" type="String"/>	
		SELECT mr.UUID_MS_USER, flag_task
	      FROM MS_ROUNDROBINTASK mr with (nolock)
	      join (select keyValue from dbo.getUserByLogin(:uuidManager)) hrkUser on hrkUser.keyValue = mr.UUID_MS_USER
		  where FLAG_TASK = '1'
	    order by UUID_MS_USER
	</sql-query>
	
	<sql-query name="services.submitorder.getNextUserTop">
		<query-param name="uuidManager" type="String"/>	
		SELECT top 1 mr.UUID_MS_USER, flag_task
	      FROM MS_ROUNDROBINTASK mr with (nolock)
	      join (select keyValue from dbo.getUserByLogin(:uuidManager)) hrkUser on hrkUser.keyValue = mr.UUID_MS_USER
		order by UUID_MS_USER
	</sql-query>
	
	<sql-query name="services.submitorder.getLeadUser">
		<query-param name="uuidManager" type="String"/>	
		SELECT mr.UUID_MS_USER,
	   	LEAD (mr.UUID_MS_USER, 1) OVER (ORDER BY mr.UUID_MS_USER) AS nextUser
	    FROM MS_ROUNDROBINTASK mr with (nolock) 
	    join (select keyValue from dbo.getUserByLogin(:uuidManager)) hrkUser on hrkUser.keyValue = mr.UUID_MS_USER
	    ORDER BY UUID_MS_USER
	</sql-query>
	
	<sql-query name="services.submitorder.getUserAssignLowTask">
		<query-param name="uuidBranch" type="String"/>
		<query-param name="uuidManager" type="String"/>	
	select top 1 amu2.UUID_MS_USER, a.countTask from (
			select amu.UUID_MS_USER,count(b.UUID_TASK_H) as countTask
			from AM_MSUSER amu with (nolock) left join 
			(select UUID_MS_USER, UUID_TASK_H from TR_TASK_H ttah with (nolock)
				left join MS_STATUSTASK mst with (nolock)
				on mst.UUID_STATUS_TASK = ttah.UUID_STATUS_TASK
				where ttah.UUID_BRANCH = :uuidBranch
				and STATUS_CODE in ('N','U') 
				and mst.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager)
			) b	
			on amu.UUID_MS_USER =  b.UUID_MS_USER
			left join MS_JOB mj with (nolock) 
			on amu.UUID_JOB = mj.UUID_JOB
			left join AM_MSSUBSYSTEM ams with (nolock)
			on amu.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM
			join (select keyValue from dbo.getUserByLogin(:uuidManager)) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where amu.UUID_BRANCH = :uuidBranch
			and ams.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager)
			and mj.IS_FIELD_PERSON = '1'
			and amu.IS_ACTIVE = '1'
			and amu.IS_DELETED = '0'
			and amu.UUID_MS_USER != :uuidManager
			group by amu.UUID_MS_USER
		) a left join AM_MSUSER amu2
		on a.UUID_MS_USER = amu2.UUID_MS_USER
		where amu2.MAX_TASK_LOAD >= a.countTask
		order by a.countTask
	</sql-query>
	
	<sql-query name="services.submitorder.getAllUserAssignLowTask">
		<query-param name="uuidBranch" type="String"/>
		<query-param name="uuidManager" type="String"/>
		select amu2.UUID_MS_USER, a.countTask, a.UUID_AREA, a.AREA_TYPE_CODE, a.LATITUDE, a.LONGITUDE, a.RADIUS from (
			select amu.UUID_MS_USER,count(b.UUID_TASK_H) as countTask, ma.AREA_TYPE_CODE, ma.LATITUDE, ma.LONGITUDE, ma.RADIUS, ma.UUID_AREA
			from AM_MSUSER amu with (nolock) left join 
			(select UUID_MS_USER, UUID_TASK_H from TR_TASK_H ttah with (nolock)
				left join MS_STATUSTASK mst with (nolock)
				on mst.UUID_STATUS_TASK = ttah.UUID_STATUS_TASK
				where ttah.UUID_BRANCH = :uuidBranch
				and STATUS_CODE in ('N','U') 
				and mst.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager)
			) b	
			on amu.UUID_MS_USER =  b.UUID_MS_USER
			left join MS_JOB mj with (nolock) 
			on amu.UUID_JOB = mj.UUID_JOB
			left join AM_MSSUBSYSTEM ams with (nolock)
			on amu.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM
			join (select keyValue from dbo.getUserByLogin(:uuidManager)) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			join MS_AREAOFUSER maou on amu.UUID_MS_USER = maou.UUID_MS_USER
			join MS_AREA ma on maou.UUID_AREA = ma.UUID_AREA
			where amu.UUID_BRANCH = :uuidBranch
			and ams.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager)
			and mj.IS_FIELD_PERSON = '1'
			and amu.IS_ACTIVE = '1'
			and amu.IS_DELETED = '0'
			and amu.UUID_MS_USER != :uuidManager
			group by amu.UUID_MS_USER, ma.AREA_TYPE_CODE, ma.LONGITUDE, ma.LATITUDE, ma.RADIUS, ma.UUID_AREA
		) a left join AM_MSUSER amu2
		on a.UUID_MS_USER = amu2.UUID_MS_USER
		where amu2.MAX_TASK_LOAD >= a.countTask
		order by a.countTask
	</sql-query>
	
	<sql-query name="services.submitorder.getUserAssignZipCode">
		<query-param name="uuidBranch" type="String"/>
		<query-param name="zipCode" type="String"/>
		<query-param name="uuidManager" type="String"/>
		
		select top 1 amu2.UUID_MS_USER, a.countTask from (
			select amu.UUID_MS_USER,count(b.UUID_TASK_H) as countTask
			from AM_MSUSER amu with (nolock) left join 
			(select UUID_MS_USER, UUID_TASK_H from TR_TASK_H ttah with (nolock)
				left join MS_STATUSTASK mst with (nolock)
				on mst.UUID_STATUS_TASK = ttah.UUID_STATUS_TASK
				where ttah.UUID_BRANCH = :uuidBranch
				and STATUS_CODE in ('N','U')
				and mst.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager)
			) b	
			on amu.UUID_MS_USER =  b.UUID_MS_USER
			left join MS_JOB mj with (nolock) 
			on amu.UUID_JOB = mj.UUID_JOB
			left join AM_MSSUBSYSTEM ams with (nolock)
			on amu.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM
			left join MS_ZIPCODEOFUSER mzc with (nolock)
			on mzc.UUID_MS_USER = amu.UUID_MS_USER
			join (select keyValue from dbo.getUserByLogin(:uuidManager)) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where amu.UUID_BRANCH = :uuidBranch
			and ams.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager) 
			and mj.IS_FIELD_PERSON = '1'
			and amu.IS_ACTIVE = '1'
			and amu.IS_DELETED = '0'
			and mzc.ZIP_CODE = :zipCode
			and amu.UUID_MS_USER != :uuidManager
			group by amu.UUID_MS_USER
		) a left join AM_MSUSER amu2 with (nolock)
		on a.UUID_MS_USER = amu2.UUID_MS_USER
		where amu2.MAX_TASK_LOAD >= a.countTask
		order by a.countTask
	</sql-query>
	
	<sql-query name="services.submitorder.getMsLov">
		<query-param name="code" type="String"/>
		<query-param name="lovGroup" type="String"/>
		SELECT UUID_LOV, IS_ACTIVE, IS_DELETED, LOV_GROUP, CODE,
		DESCRIPTION, SEQUENCE, CONSTRAINT_1, CONSTRAINT_2, CONSTRAINT_3, 
		CONSTRAINT_4, CONSTRAINT_5 FROM MS_LOV with (nolock) WHERE CODE = :code AND LOV_GROUP = :lovGroup
	</sql-query>
	
	<sql-query name="services.submitorder.getNextUserByTaskLoad">
		<query-param name="uuidUser" type="String"/>
		<query-param name="uuidManager" type="String"/>
		<query-param name="statusCode" type="String"/>
		<query-param name="uuidSubsystem" type="String"/>
		WITH N AS (
			select 
				a.UUID_MS_USER, 
				LOGIN_ID, 
				MAX_TASK_LOAD, 
				ISNULL(c.task_load, 0) TASK_LOAD, 
				LEAD (a.UUID_MS_USER, 1) OVER (ORDER BY a.UUID_MS_USER) AS nextUser,
				rownum = ROW_NUMBER() OVER (ORDER BY a.UUID_MS_USER)
			from MS_ROUNDROBINTASK a with (nolock) left join AM_MSUSER b with (nolock) on a.UUID_MS_USER = b.UUID_MS_USER
				left join (select count(1) task_load, UUID_MS_USER
					from tr_task_h trth with (nolock) left join ms_statustask mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
					where STATUS_CODE = :statusCode and UUID_MS_SUBSYSTEM = :uuidSubsystem
					group by UUID_MS_USER) c on a.UUID_MS_USER = c.UUID_MS_USER
				join (select keyValue from dbo.getUserByLogin(:uuidManager)) hrkUser on hrkUser.keyValue = a.UUID_MS_USER
			where ISNULL(c.task_load, 0) between 0 and MAX_TASK_LOAD - 1
		)
		select TOP 1
			N.UUID_MS_USER,
			CASE 
				WHEN N.rownum between 0 and (select rownum from N where UUID_MS_USER = :uuidUser) 
					THEN (N.rownum + (SELECT MAX(rownum) from N)) 
				ELSE
					N.rownum
			end as row_num, 
			N.LOGIN_ID, N.MAX_TASK_LOAD, N.TASK_LOAD, nex.UUID_MS_USER nextUser
		from N LEFT JOIN N prev ON prev.rownum = N.rownum - 1
		LEFT JOIN N nex ON nex.rownum = N.rownum + 1
		where N.rownum != ISNULL((select rownum from N where UUID_MS_USER = :uuidUser),0)
		order by row_num
	</sql-query>
	
	<sql-query name="services.submitorder.getTotalTask">
		<query-param name="uuidUser" type="String"/>
		<query-param name="statusCode" type="String"/>
		<query-param name="uuidSubsystem" type="String"/>
		select count(1) total_task
		from tr_task_h trth with (nolock) left join ms_statustask mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
		where UUID_MS_USER = :uuidUser 
			and STATUS_CODE = :statusCode
			and UUID_MS_SUBSYSTEM = :uuidSubsystem
	</sql-query>
	
<!-- 	SUBMIT ORDER BY BRANCH	-->
	<sql-query name="services.submitorder.getNextUserByBranch">
		<query-param name="uuidBranch" type="string"/>
		<query-param name="jobCode" type="string"/>		
		SELECT mr.UUID_MS_USER, flag_task
			FROM MS_ROUNDROBINTASK mr with (nolock)
			join (select keyValue from dbo.getUserByBranch(:uuidBranch, :jobCode)) hrkUser on hrkUser.keyValue = mr.UUID_MS_USER
		where FLAG_TASK = '1'
		order by UUID_MS_USER
	</sql-query>
	<sql-query name="services.submitorder.getNextUserTopByBranch">
		<query-param name="uuidBranch" type="string"/>
		<query-param name="jobCode" type="string"/>	
		SELECT top 1 mr.UUID_MS_USER, flag_task
	      FROM MS_ROUNDROBINTASK mr with (nolock)
	      join (select keyValue from dbo.getUserByBranch(:uuidBranch, :jobCode)) hrkUser on hrkUser.keyValue = mr.UUID_MS_USER
		order by UUID_MS_USER
	</sql-query>
	
	<sql-query name="services.submitorder.getLeadUserByBranch">
		<query-param name="uuidBranch" type="string"/>
		<query-param name="jobCode" type="string"/>	
		SELECT mr.UUID_MS_USER,
	   	LEAD (mr.UUID_MS_USER, 1) OVER (ORDER BY mr.UUID_MS_USER) AS nextUser
	    FROM MS_ROUNDROBINTASK mr with (nolock)
	    join (select keyValue from dbo.getUserByBranch(:uuidBranch, :jobCode)) hrkUser on hrkUser.keyValue = mr.UUID_MS_USER
	    ORDER BY UUID_MS_USER
	</sql-query>
	<sql-query name="services.submitorder.getUserAssignLowTaskByBranch">
		<query-param name="uuidBranch" type="String"/>
		<query-param name="uuidManager" type="String"/>	
		<query-param name="jobCode" type="string"/>	
		select top 1 amu2.UUID_MS_USER, a.countTask from (
			select amu.UUID_MS_USER,count(b.UUID_TASK_H) as countTask
			from AM_MSUSER amu with (nolock) left join 
			(select UUID_MS_USER, UUID_TASK_H from TR_TASK_H ttah with (nolock)
				left join MS_STATUSTASK mst with (nolock)
				on mst.UUID_STATUS_TASK = ttah.UUID_STATUS_TASK
				where ttah.UUID_BRANCH = :uuidBranch
				and STATUS_CODE in ('N','U') 
				and mst.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager)
			) b	
			on amu.UUID_MS_USER =  b.UUID_MS_USER
			left join MS_JOB mj with (nolock) 
			on amu.UUID_JOB = mj.UUID_JOB
			left join AM_MSSUBSYSTEM ams with (nolock)
			on amu.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM
			join (select keyValue from dbo.getUserByBranch(:uuidBranch, :jobCode)) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where amu.UUID_BRANCH = :uuidBranch
			and ams.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager)
			and mj.IS_FIELD_PERSON = '1'
			and amu.IS_ACTIVE = '1'
			and amu.IS_DELETED = '0'
			group by amu.UUID_MS_USER
		) a left join AM_MSUSER amu2
		on a.UUID_MS_USER = amu2.UUID_MS_USER
		where amu2.MAX_TASK_LOAD >= a.countTask
		order by a.countTask
	</sql-query>
	
	<sql-query name="services.submitorder.getUserAssignZipCodeByBranch">
		<query-param name="uuidBranch" type="String"/>
		<query-param name="zipCode" type="String"/>
		<query-param name="uuidManager" type="String"/>
		<query-param name="jobCode" type="string"/>	
		select top 1 amu2.UUID_MS_USER, a.countTask from (
			select amu.UUID_MS_USER,count(b.UUID_TASK_H) as countTask
			from AM_MSUSER amu with (nolock) left join 
			(select UUID_MS_USER, UUID_TASK_H from TR_TASK_H ttah with (nolock)
				left join MS_STATUSTASK mst with (nolock)
				on mst.UUID_STATUS_TASK = ttah.UUID_STATUS_TASK
				where ttah.UUID_BRANCH = :uuidBranch
				and STATUS_CODE in ('N','U')
				and mst.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager)
			) b	
			on amu.UUID_MS_USER =  b.UUID_MS_USER
			left join MS_JOB mj with (nolock) 
			on amu.UUID_JOB = mj.UUID_JOB
			left join AM_MSSUBSYSTEM ams with (nolock)
			on amu.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM
			left join MS_ZIPCODEOFUSER mzc with (nolock)
			on mzc.UUID_MS_USER = amu.UUID_MS_USER
			join (select keyValue from dbo.getUserByBranch(:uuidBranch, :jobCode)) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
			where amu.UUID_BRANCH = :uuidBranch
			and ams.uuid_ms_subsystem = (select UUID_MS_SUBSYSTEM from am_msuser with (nolock) where UUID_MS_USER = :uuidManager) 
			and mj.IS_FIELD_PERSON = '1'
			and amu.IS_ACTIVE = '1'
			and amu.IS_DELETED = '0'
			and mzc.ZIP_CODE = :zipCode
			group by amu.UUID_MS_USER
		) a left join AM_MSUSER amu2
		on a.UUID_MS_USER = amu2.UUID_MS_USER
		where amu2.MAX_TASK_LOAD >= a.countTask
		order by a.countTask
	</sql-query>
	
	<sql-query name="services.submitorder.getNextUserByTaskLoadByBranch">
		<query-param name="uuidUser" type="String"/>
		<query-param name="uuidBranch" type="String"/>
		<query-param name="statusCode" type="String"/>
		<query-param name="uuidSubsystem" type="String"/>
		<query-param name="jobCode" type="String"/>
		WITH N AS (
			select 
				a.UUID_MS_USER, 
				LOGIN_ID, 
				MAX_TASK_LOAD, 
				ISNULL(c.task_load, 0) TASK_LOAD, 
				LEAD (a.UUID_MS_USER, 1) OVER (ORDER BY a.UUID_MS_USER) AS nextUser,
				rownum = ROW_NUMBER() OVER (ORDER BY a.UUID_MS_USER)
			from MS_ROUNDROBINTASK a with (nolock) left join AM_MSUSER b with (nolock) on a.UUID_MS_USER = b.UUID_MS_USER
				left join (select count(1) task_load, UUID_MS_USER
					from tr_task_h trth with (nolock) left join ms_statustask mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
					where STATUS_CODE = :statusCode and UUID_MS_SUBSYSTEM = :uuidSubsystem
					group by UUID_MS_USER) c on a.UUID_MS_USER = c.UUID_MS_USER
				join (select keyValue from dbo.getUserByBranch(:uuidBranch, :jobCode)) hrkUser on hrkUser.keyValue = a.UUID_MS_USER
			where ISNULL(c.task_load, 0) between 0 and MAX_TASK_LOAD - 1
		)
		select TOP 1
			N.UUID_MS_USER,
			CASE 
				WHEN N.rownum between 0 and (select rownum from N where UUID_MS_USER = :uuidUser) 
					THEN (N.rownum + (SELECT MAX(rownum) from N)) 
				ELSE
					N.rownum
			end as row_num, 
			N.LOGIN_ID, N.MAX_TASK_LOAD, N.TASK_LOAD, nex.UUID_MS_USER nextUser
		from N LEFT JOIN N prev ON prev.rownum = N.rownum - 1
		LEFT JOIN N nex ON nex.rownum = N.rownum + 1
		where N.rownum != ISNULL((select rownum from N where UUID_MS_USER = :uuidUser),0)
		order by row_num
	</sql-query>
	
</hibernate-mapping>