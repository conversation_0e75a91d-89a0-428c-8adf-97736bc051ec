<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

<!-- CANCEL ORDER MOBILE INTERFACE -->
	
	<!-- GET HEADER by Individuals-->
	<sql-query name="task.order.cancelOrder.getHeader1A">
		<query-param name="uuidUser" type="long"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
			select tod.ORDER_NO, tskh.CUSTOMER_NAME, tskh.UUID_TASK_H
			from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tod with (nolock) on tskh.UUID_TASK_H = tod.UUID_TASK_ID
				   inner join MS_STATUSTASK sttsk with (nolock) on tskh.UUID_STATUS_TASK = sttsk.UUID_STATUS_TASK
			where sttsk.STATUS_CODE IN ('N','A','V','P')
			   and tskh.UUID_MS_USER = :uuidUser
			   and tod.DTM_CRT between :startDate and :endDate
	</sql-query>		
	<sql-query name="task.order.cancelOrder.getHeader2A">
		<query-param name="uuidUser" type="long"/>
		<query-param name="orderNumber" type="String"/>
			select tod.ORDER_NO, tskh.CUSTOMER_NAME, tskh.UUID_TASK_H
			from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tod with (nolock) on tskh.UUID_TASK_H = tod.UUID_TASK_ID
				   inner join MS_STATUSTASK sttsk with (nolock) on tskh.UUID_STATUS_TASK = sttsk.UUID_STATUS_TASK
			where sttsk.STATUS_CODE IN ('N','A','V','P')
			   and tskh.UUID_MS_USER = :uuidUser
			   and tod.ORDER_NO = :orderNumber
	</sql-query>
	<sql-query name="task.order.cancelOrder.getHeader3A">
		<query-param name="uuidUser" type="long"/>
		<query-param name="custName" type="String"/>
			select tod.ORDER_NO, tskh.CUSTOMER_NAME, tskh.UUID_TASK_H
			from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tod with (nolock) on tskh.UUID_TASK_H = tod.UUID_TASK_ID
				   inner join MS_STATUSTASK sttsk with (nolock) on tskh.UUID_STATUS_TASK = sttsk.UUID_STATUS_TASK
			where sttsk.STATUS_CODE IN ('N','A','V','P')
			   and tskh.UUID_MS_USER = :uuidUser
			   and tskh.CUSTOMER_NAME like '%' + :custName + '%'
	</sql-query>
	
	<!-- GET HEADER by Supervisor-->
	<sql-query name="task.order.cancelOrder.getHeader1B">
		<query-param name="uuidUser" type="long"/>
		<query-param name="startDate" type="string"/>
		<query-param name="endDate" type="string"/>
			select tod.ORDER_NO, tskh.CUSTOMER_NAME, tskh.UUID_TASK_H
			from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tod with (nolock) on tskh.UUID_TASK_H = tod.UUID_TASK_ID
				   inner join MS_STATUSTASK sttsk with (nolock) on tskh.UUID_STATUS_TASK = sttsk.UUID_STATUS_TASK
				   inner join AM_MSUSER msu with (nolock) on tskh.UUID_MS_USER = msu.UUID_MS_USER
			where sttsk.STATUS_CODE IN ('N','A','V','P')
			   and msu.SPV_ID = :uuidUser
			   and tod.DTM_CRT between :startDate and :endDate
	</sql-query>		
	<sql-query name="task.order.cancelOrder.getHeader2B">
		<query-param name="uuidUser" type="long"/>
		<query-param name="orderNumber" type="String"/>
			select tod.ORDER_NO, tskh.CUSTOMER_NAME, tskh.UUID_TASK_H
			from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tod with (nolock) on tskh.UUID_TASK_H = tod.UUID_TASK_ID
				   inner join MS_STATUSTASK sttsk with (nolock) on tskh.UUID_STATUS_TASK = sttsk.UUID_STATUS_TASK
				   inner join AM_MSUSER msu with (nolock) on tskh.UUID_MS_USER = msu.UUID_MS_USER
			where sttsk.STATUS_CODE IN ('N','A','V','P')
			   and msu.SPV_ID = :uuidUser
			   and tod.ORDER_NO = :orderNumber
	</sql-query>
	<sql-query name="task.order.cancelOrder.getHeader3B">
		<query-param name="uuidUser" type="long"/>
		<query-param name="custName" type="String"/>
			select tod.ORDER_NO, tskh.CUSTOMER_NAME, tskh.UUID_TASK_H
			from TR_TASK_H tskh with (nolock) inner join TR_TASKORDERDATA tod with (nolock) on tskh.UUID_TASK_H = tod.UUID_TASK_ID
				   inner join MS_STATUSTASK sttsk with (nolock) on tskh.UUID_STATUS_TASK = sttsk.UUID_STATUS_TASK
				   inner join AM_MSUSER msu with (nolock) on tskh.UUID_MS_USER = msu.UUID_MS_USER
			where sttsk.STATUS_CODE IN ('N','A','V','P')
			   and msu.SPV_ID = :uuidUser
			   and tskh.CUSTOMER_NAME like '%' + :custName + '%'
	</sql-query>
	
	<!-- GET DETAIL -->
	<sql-query name="task.order.cancelOrder.getDetailNon">
		<query-param name="uuidTaskH" type="long"/>
			select ISNULL(trtd.QUESTION_TEXT, '') QUESTION_TEXT,
				   ISNULL(trtd.TEXT_ANSWER, '') TEXT_ANSWER,
				   ISNULL(trtd.OPTION_TEXT, '') OPTION_TEXT
			 from tr_task_h trth with (nolock) inner join tr_task_d trtd with (nolock) 
			 	on trth.uuid_task_h = trtd.uuid_task_h
			 where trth.UUID_TASK_H = :uuidTaskH
	</sql-query>	
	<sql-query name="task.order.cancelOrder.getDetailI">
		<query-param name="uuidTaskH" type="long"/>
			select ISNULL(trtdl.QUESTION_TEXT, '') QUESTION_TEXT,
				ISNULL(CAST(trtdl.QUESTION_ID AS VARCHAR), '') UUID_QUESTION,
				case 
					when trtdl.lob_file is not null then '1'
					when trtdl.image_path is not null then '1' 
				end Images
			from tr_task_h trth with (nolock) inner join tr_taskdetaillob trtdl with (nolock) 
				on trth.uuid_task_h = trtdl.uuid_task_h
			where trtdl.lob_file is not null and trth.UUID_TASK_H = :uuidTaskH
	</sql-query>
	
	<!-- CANCEL ORDER -->	
	<sql-query name="task.order.cancelOrder.getSurveyId">
		<query-param name="uuidTaskId" type="long"/>
			select tskl.UUID_TASK_H_SURVEY
			from TR_TASKLINK tskl with (nolock) inner join TR_TASKORDERDATA tod with (nolock) 
				on tskl.UUID_TASK_H_ORDER = tod.UUID_TASK_ID
			where tod.UUID_TASK_ID = :uuidTaskId
	</sql-query>

<!-- END OF CALCEL ORDER MOBILE INTERFACE -->

	<sql-query name="task.order.cancelOrderList">
		<query-param name="customerName" type="string"/>
		<query-param name="customerAddress" type="string"/>
		<query-param name="applicationNo" type="string"/>
		<query-param name="statusTask" type="string"/>
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="orderDateStart" type="string"/>
		<query-param name="orderDateEnd" type="string"/>
		<query-param name="subsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="dealer" type="long"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT trth.UUID_TASK_H,msb.BRANCH_NAME , trth.CUSTOMER_NAME,
					trtod.ORDER_NO, msu.FULL_NAME , LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as submitDate,
					ROW_NUMBER() OVER (ORDER BY trtod.ORDER_NO) AS rownum, trth.CUSTOMER_ADDRESS, ms.STATUS_TASK_DESC
				FROM TR_TASK_H trth with (nolock) 
					LEFT JOIN MS_BRANCH msb with (nolock)
					ON trth.UUID_BRANCH = msb.UUID_BRANCH
					LEFT JOIN AM_MSUSER msu with (nolock)
					ON trth.UUID_MS_USER = msu.UUID_MS_USER
					LEFT JOIN TR_TASKORDERDATA trtod with (nolock)
					ON trtod.UUID_TASK_ID = trth.UUID_TASK_H
					LEFT JOIN MS_STATUSTASK ms with (nolock)
					ON ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK
				where ms.UUID_MS_SUBSYSTEM = :subsystem
					and ms.STATUS_CODE IN (:statusTask)
					and UPPER(trth.CUSTOMER_NAME) like UPPER('%'+ :customerName +'%') AND UPPER(trtod.ORDER_NO) like UPPER('%'+ :applicationNo +'%')
					and UPPER(trth.CUSTOMER_ADDRESS) like UPPER('%'+ :customerAddress +'%')
					AND COALESCE(trtod.dtm_crt, GETDATE()) BETWEEN (CASE WHEN :orderDateStart = '%' then '1990-01-01 00:00:00' else :orderDateStart END) 
					AND (CASE WHEN :orderDateEnd = '%' then :currentDate else :orderDateEnd END)
					AND COALESCE(trth.SUBMIT_DATE, GETDATE()) BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01 00:00:00' else :assignmentDateStart END) 
					AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END)
					AND trtod.DEALER_ID = :dealer
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.order.countCancelOrderList">
		<query-param name="customerName" type="string"/>
		<query-param name="customerAddress" type="string"/>
		<query-param name="applicationNo" type="string"/>
		<query-param name="statusTask" type="string"/>
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="orderDateStart" type="string"/>
		<query-param name="orderDateEnd" type="string"/>
		<query-param name="subsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="dealer" type="long"/>
			SELECT COUNT(1)
			FROM TR_TASK_H trth with (nolock) 
				LEFT JOIN MS_BRANCH msb with (nolock)
				ON trth.UUID_BRANCH = msb.UUID_BRANCH
				LEFT JOIN AM_MSUSER msu with (nolock)
				ON trth.UUID_MS_USER = msu.UUID_MS_USER
				LEFT JOIN TR_TASKORDERDATA trtod with (nolock)
				ON trtod.UUID_TASK_ID = trth.UUID_TASK_H
				LEFT JOIN MS_STATUSTASK ms with (nolock)
				ON ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK
			where ms.UUID_MS_SUBSYSTEM = :subsystem
				and ms.STATUS_CODE IN (:statusTask)
				and UPPER(trth.CUSTOMER_NAME) like UPPER('%'+ :customerName +'%') AND UPPER(trtod.ORDER_NO) like UPPER('%'+ :applicationNo +'%')
				and UPPER(trth.CUSTOMER_ADDRESS) like UPPER('%'+ :customerAddress +'%')
				AND COALESCE(trtod.dtm_crt, GETDATE()) BETWEEN (CASE WHEN :orderDateStart = '%' then '1990-01-01 00:00:00' else :orderDateStart END) 
				AND (CASE WHEN :orderDateEnd = '%' then :currentDate else :orderDateEnd END)
				AND COALESCE(trth.SUBMIT_DATE, GETDATE()) BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01 00:00:00' else :assignmentDateStart END) 
				AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END)
				AND trtod.DEALER_ID = :dealer
	</sql-query>
	
	<sql-query name="task.order.getDetailOrderFromQSet">
		<query-param name="uuidOrder" type="long" />
		WITH N AS (
		  	SELECT trtd.UUID_TASK_H, trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, 
				trtd.OPTION_TEXT, trtd.IMAGE_PATH, trtd.LATITUDE, trtd.LONGITUDE, '0' as HAS_IMAGE, '' as UUID_TASK_DETAIL_LOB,
				trth.UUID_FORM, '0' IS_IMAGE, trth.FORM_VERSION, trtd.accuracy 
			FROM TR_TASK_D trtd with (nolock) 
				INNER JOIN TR_TASK_H trth with (nolock) ON trtd.UUID_TASK_H = trth.UUID_TASK_H
			WHERE trtd.UUID_TASK_H = :uuidOrder

		  UNION ALL

		  	SELECT trtdl.UUID_TASK_H, trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.TEXT_ANSWER, 
				trtdl.OPTION_TEXT, trtdl.IMAGE_PATH, trtdl.LATITUDE, trtdl.LONGITUDE,
				CASE
				  WHEN trtdl.LOB_FILE IS NOT NULL THEN '1'
				  WHEN trtdl.IMAGE_PATH IS NOT NULL THEN '1' ELSE '0'
				END AS HAS_IMAGE,
				CAST(trtdl.UUID_TASK_DETAIL_LOB AS VARCHAR),
			    trth.UUID_FORM, '1' IS_IMAGE, trth.FORM_VERSION, trtdl.accuracy
			FROM TR_TASKDETAILLOB trtdl with (nolock) 
				INNER JOIN TR_TASK_H trth with (nolock) ON trtdl.UUID_TASK_H = trth.UUID_TASK_H
			WHERE trth.UUID_TASK_H = :uuidOrder
		)
		SELECT qset.QUESTION_LABEL as questionText,
			msan.ANSWER_TYPE_NAME as "msQuestion.msAnswertype.answerTypeName",
			CASE
			  WHEN msan.code_answer_type in ('001','002','003','004','005','013','014','015','024','025','026') THEN ISNULL(n.TEXT_ANSWER,'')
			  WHEN msan.code_answer_type in ('006','009','011') THEN ISNULL(n.OPTION_TEXT,'')
			  WHEN msan.code_answer_type in ('007','008','010','012') THEN ISNULL(n.OPTION_TEXT,'') + ' - ' + ISNULL(n.TEXT_ANSWER,'')
			  ELSE N.UUID_TASK_DETAIL_LOB
			END AS textAnswer,
			msan.code_answer_type as "msQuestion.msAnswertype.codeAnswerType",
			N.HAS_IMAGE AS hasImage,
			NULL as lobFile,
			ISNULL(n.TEXT_ANSWER,'') as textAnswerOri,
			N.LATITUDE as latitude,
			N.LONGITUDE as longitude,
			N.IS_IMAGE as isImage,
			N.UUID_TASK_H as uuidTaskH,
			N.UUID_QUESTION as "msQuestion.uuidQuestion",
			N.ACCURACY as "accuracy"
		FROM N 
			INNER JOIN MS_FORMHISTORY hist
			ON hist.UUID_FORM = N.UUID_FORM AND N.FORM_VERSION = hist.FORM_VERSION
			INNER JOIN MS_FORMQUESTIONSET qset
			ON qset.UUID_FORM_HISTORY = hist.UUID_FORM_HISTORY AND N.UUID_QUESTION = qset.UUID_QUESTION
			INNER JOIN MS_ANSWERTYPE msan with (nolock) 
			ON qset.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
		WHERE N.UUID_TASK_H = :uuidOrder
		order by qset.QUESTION_GROUP_OF_FORM_SEQ, qset.QUESTION_OF_GROUP_SEQ
	</sql-query> 
	
		<sql-query name="task.cancelorder.viewMapPhoto">
		<query-param name="uuidTaskH" type="long" />
			select trtdl.UUID_TASK_H,
				trtdl.QUESTION_TEXT,
				trtdl.TEXT_ANSWER,
				trtdl.OPTION_TEXT,
				CASE
					WHEN (trtdl.LOB_FILE IS NOT NULL or trtdl.IMAGE_PATH IS NOT NULL) THEN '1'
					ELSE '0'
				END as HAS_IMAGE,
				trtdl.LATITUDE,
				trtdl.LONGITUDE,
				NULL AS LOB_FILE,
				trtdl.UUID_TASK_DETAIL_LOB
			from TR_TASKDETAILLOB trtdl with (nolock)
			where UUID_TASK_H = :uuidTaskH
	</sql-query>
	
	<sql-query name="task.order.cancelOrderListByHierarkiUser">
		<query-param name="customerName" type="string"/>
		<query-param name="customerAddress" type="string"/>
		<query-param name="applicationNo" type="string"/>
		<query-param name="statusTask" type="string"/>
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="orderDateStart" type="string"/>
		<query-param name="orderDateEnd" type="string"/>
		<query-param name="subsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="dealer" type="long"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="uuidUser" type="long"/>
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT trth.UUID_TASK_H,msb.BRANCH_NAME , trth.CUSTOMER_NAME,
					trtod.ORDER_NO, msu.FULL_NAME , LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as submitDate,
					ROW_NUMBER() OVER (ORDER BY trtod.ORDER_NO) AS rownum, trth.CUSTOMER_ADDRESS, ms.STATUS_TASK_DESC
				FROM TR_TASK_H trth with (nolock) 
					LEFT JOIN MS_BRANCH msb with (nolock)
					ON trth.UUID_BRANCH = msb.UUID_BRANCH
					LEFT JOIN AM_MSUSER msu with (nolock)
					ON trth.UUID_MS_USER = msu.UUID_MS_USER
					LEFT JOIN TR_TASKORDERDATA trtod with (nolock)
					ON trtod.UUID_TASK_ID = trth.UUID_TASK_H
					LEFT JOIN MS_STATUSTASK ms with (nolock)
					ON ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK
					JOIN (select keyValue as UUID_MS_USER from getUserByLogin(:uuidUser)) ubl on ubl.UUID_MS_USER = trth.UUID_MS_USER
				where ms.UUID_MS_SUBSYSTEM = :subsystem
					and ms.STATUS_CODE IN (:statusTask)
					and UPPER(trth.CUSTOMER_NAME) like UPPER('%'+ :customerName +'%') AND UPPER(trtod.ORDER_NO) like UPPER('%'+ :applicationNo +'%')
					and UPPER(trth.CUSTOMER_ADDRESS) like UPPER('%'+ :customerAddress +'%')
					AND COALESCE(trtod.dtm_crt, GETDATE()) BETWEEN (CASE WHEN :orderDateStart = '%' then '1990-01-01 00:00:00' else :orderDateStart END) 
					AND (CASE WHEN :orderDateEnd = '%' then :currentDate else :orderDateEnd END)
					AND COALESCE(trth.SUBMIT_DATE, GETDATE()) BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01 00:00:00' else :assignmentDateStart END) 
					AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END)
					AND trtod.DEALER_ID = :dealer
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.order.countCancelOrderListByHierarkiUser">
		<query-param name="customerName" type="string"/>
		<query-param name="customerAddress" type="string"/>
		<query-param name="applicationNo" type="string"/>
		<query-param name="statusTask" type="string"/>
		<query-param name="assignmentDateStart" type="string"/>
		<query-param name="assignmentDateEnd" type="string"/>
		<query-param name="orderDateStart" type="string"/>
		<query-param name="orderDateEnd" type="string"/>
		<query-param name="subsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		<query-param name="dealer" type="long"/>
		<query-param name="uuidUser" type="long"/>
			SELECT COUNT(1)
			FROM TR_TASK_H trth with (nolock) 
				LEFT JOIN MS_BRANCH msb with (nolock)
				ON trth.UUID_BRANCH = msb.UUID_BRANCH
				LEFT JOIN AM_MSUSER msu with (nolock)
				ON trth.UUID_MS_USER = msu.UUID_MS_USER
				LEFT JOIN TR_TASKORDERDATA trtod with (nolock)
				ON trtod.UUID_TASK_ID = trth.UUID_TASK_H
				LEFT JOIN MS_STATUSTASK ms with (nolock)
				ON ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK
				JOIN (select keyValue as UUID_MS_USER from getUserByLogin(:uuidUser)) ubl on ubl.UUID_MS_USER = trth.UUID_MS_USER
			where ms.UUID_MS_SUBSYSTEM = :subsystem
				and ms.STATUS_CODE IN (:statusTask)
				and UPPER(trth.CUSTOMER_NAME) like UPPER('%'+ :customerName +'%') AND UPPER(trtod.ORDER_NO) like UPPER('%'+ :applicationNo +'%')
				and UPPER(trth.CUSTOMER_ADDRESS) like UPPER('%'+ :customerAddress +'%')
				AND COALESCE(trtod.dtm_crt, GETDATE()) BETWEEN (CASE WHEN :orderDateStart = '%' then '1990-01-01 00:00:00' else :orderDateStart END) 
				AND (CASE WHEN :orderDateEnd = '%' then :currentDate else :orderDateEnd END)
				AND COALESCE(trth.SUBMIT_DATE, GETDATE()) BETWEEN (CASE WHEN :assignmentDateStart = '%' then '1990-01-01 00:00:00' else :assignmentDateStart END) 
				AND (CASE WHEN :assignmentDateEnd = '%' then :currentDate else :assignmentDateEnd END)
				AND trtod.DEALER_ID = :dealer
	</sql-query>
	
	<sql-query name="task.order.getCancelOrderHeader">
		<query-param name="uuidTask" type="long"/>
			SELECT trth.UUID_TASK_H, 
			trth.TASK_ID, 
			msf.FORM_NAME, 
			msb.BRANCH_NAME,
			msp.PRIORITY_DESC, 
			ISNULL(trth.NOTES, '-') as NOTES, 
			ISNULL(trth.RESULT, '-') as RESULT, 
			trth.CUSTOMER_NAME, 
			trth.CUSTOMER_ADDRESS, 
			trth.CUSTOMER_PHONE, 
			trtod.ORDER_NO, 
			ammsu.FULL_NAME SALES
		FROM TR_TASK_H trth with (nolock) left join MS_BRANCH msb with (nolock) on trth.UUID_BRANCH = msb.UUID_BRANCH
			left join MS_FORM msf with (nolock) 
			on trth.UUID_FORM = msf.UUID_FORM
			LEFT JOIN MS_PRIORITY msp with (nolock) 
			on trth.UUID_PRIORITY = msp.UUID_PRIORITY
			left join TR_TASKORDERDATA trtod with (nolock) 
				on trth.UUID_TASK_H = trtod.UUID_TASK_ID
			left join AM_MSUSER ammsu with (nolock) 
				on ammsu.UUID_MS_USER = trth.UUID_MS_USER
		WHERE trth.UUID_TASK_H = :uuidTask
	</sql-query>
</hibernate-mapping>