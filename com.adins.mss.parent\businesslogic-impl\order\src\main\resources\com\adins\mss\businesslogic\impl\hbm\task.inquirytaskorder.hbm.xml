<?xml version="1.0"?>
<!DOCTY<PERSON>E hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>			
	<sql-query name="task.inquirytaskorder.getHeader">
    <query-param name="uuidTask" type="long" />
    <query-param name="uuidSubsystem" type="long" />
		SELECT trth.UUID_TASK_H, 
			trth.TASK_ID, 
			trth.APPL_NO, 
			msf.FORM_NAME, 
			msb.BRANCH_NAME,
			msp.PRIORITY_DESC, 
			trth.NOTES, 
			trth.RESULT, 
			trth.CUSTOMER_NAME, 
			trth.CUSTOMER_ADDRESS, 
			trth.CUSTOMER_PHONE, 
			trth.ZIP_CODE,
			trtod.ORDER_NO, 
			msd.DEALER_NAME, 
			ammsu.FULL_NAME SALES,
			LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as SUBMIT_DATE,
			LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as ASSIGN_DATE,
			ammsu2.FULL_NAME SURVEYOR_NAME, 
			msst.STATUS_TASK_DESC,
			'' as VOICE_NOTE
		FROM TR_TASK_H trth with (nolock) left join MS_BRANCH msb with (nolock) 
		    	on trth.UUID_BRANCH = msb.UUID_BRANCH
			left join MS_FORM msf with (nolock) 
			 	on trth.UUID_FORM = msf.UUID_FORM
			LEFT JOIN MS_PRIORITY msp with (nolock) 
				on trth.UUID_PRIORITY = msp.UUID_PRIORITY
			left join TR_TASKORDERDATA trtod with (nolock) 
				on trth.UUID_TASK_H = trtod.UUID_TASK_ID
			left join MS_DEALER msd with (nolock) 
				on trtod.DEALER_ID = msd.UUID_DEALER
			left join AM_MSUSER ammsu with (nolock) 
				on ammsu.UUID_MS_USER = trth.UUID_MS_USER
				and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem
			left join AM_MSUSER ammsu2 with (nolock) 
				on ammsu2.UUID_MS_USER = trtod.CA_ID
			left join MS_STATUSTASK msst with (nolock) 
				on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
		WHERE trth.UUID_TASK_H = :uuidTask
	</sql-query>
	
	<sql-query name="task.inquirytaskorder.getHeaderFinal">
    <query-param name="uuidTask" type="long" />
    <query-param name="uuidSubsystem" type="long" />
		SELECT trth.UUID_TASK_H, 
			trth.TASK_ID, 
			trth.APPL_NO, 
			msf.FORM_NAME, 
			msb.BRANCH_NAME,
			msp.PRIORITY_DESC, 
			trth.NOTES, 
			trth.RESULT, 
			trth.CUSTOMER_NAME, 
			trth.CUSTOMER_ADDRESS, 
			trth.CUSTOMER_PHONE, 
			trth.ZIP_CODE,
			trtod.ORDER_NO, 
			msd.DEALER_NAME, 
			ammsu.FULL_NAME SALES,
			LEFT(CONVERT(VARCHAR, trth.SUBMIT_DATE, 113), 17) as SUBMIT_DATE,
			LEFT(CONVERT(VARCHAR, trtod.DTM_CRT, 113), 17) as ASSIGN_DATE,
			ammsu2.FULL_NAME SURVEYOR_NAME, 
			msst.STATUS_TASK_DESC,
			'' as VOICE_NOTE
		FROM FINAL_TR_TASK_H trth with (nolock) left join MS_BRANCH msb with (nolock) 
				on trth.UUID_BRANCH = msb.UUID_BRANCH
			left join MS_FORM msf with (nolock) 
				on trth.UUID_FORM = msf.UUID_FORM
			LEFT JOIN MS_PRIORITY msp with (nolock) 
				on trth.UUID_PRIORITY = msp.UUID_PRIORITY
			left join FINAL_TR_TASKORDERDATA trtod with (nolock) 
				on trth.UUID_TASK_H = trtod.UUID_TASK_ID
			left join MS_DEALER msd with (nolock) 
				on trtod.DEALER_ID = msd.UUID_DEALER
			left join AM_MSUSER ammsu with (nolock) 
				on ammsu.UUID_MS_USER = trth.UUID_MS_USER
				and ammsu.UUID_MS_SUBSYSTEM = :uuidSubsystem
			left join AM_MSUSER ammsu2 with (nolock) 
				on ammsu2.UUID_MS_USER = trtod.CA_ID
			left join MS_STATUSTASK msst with (nolock) 
				on trth.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
		WHERE trth.UUID_TASK_H = :uuidTask
	</sql-query>
	
	<sql-query name="task.inquirytaskorder.getHistory">
    <query-param name="uuidTask" type="long" />
		SELECT CASE 
				WHEN trths.CODE_PROCESS = '001' THEN 'Assignment' 
				WHEN trths.CODE_PROCESS = '002' THEN 'Re-assignment'
				WHEN trths.CODE_PROCESS = '003' THEN 'Submitted' 
				WHEN trths.CODE_PROCESS = '004' THEN 'Verified' 
				WHEN trths.CODE_PROCESS = '005' THEN 'Approval' 
				WHEN trths.CODE_PROCESS = '006' THEN 'Released'
				WHEN trths.code_process = '007' THEN 'Deleted'
				WHEN trths.CODE_PROCESS = '008' THEN 'Rejected'
				WHEN trths.CODE_PROCESS = '009' THEN 'Rejected on Verification'
				WHEN trths.CODE_PROCESS = '010' THEN 'Rejected on Approval'
				WHEN trths.CODE_PROCESS = '011' THEN 'On Core'
				WHEN trths.CODE_PROCESS = '015' THEN 'Unassigned'
				WHEN trths.CODE_PROCESS = '013' THEN 'Rejected with Re-Survey'
				WHEN trths.CODE_PROCESS = '014' THEN 'Promise to Survey'
				WHEN trths.CODE_PROCESS = '012' THEN 'Canceled'
			END TASK_PROCESS,
			trths.ACTOR, trths.FIELD_PERSON,
			trths.DTM_CRT,
			trths.NOTES, 
			msst.STATUS_TASK_DESC, 
			trths.uuid_task_h,
			trths.CODE_PROCESS,
			trths.UUID_TASK_REJECTED_HISTORY
		FROM TR_TASKHISTORY trths with (nolock) 
			LEFT JOIN MS_STATUSTASK msst with (nolock) 
			ON trths.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
		WHERE trths.UUID_TASK_H = :uuidTask
		ORDER BY DTM_CRT desc
	</sql-query>
	
		<sql-query name="task.inquirytaskorder.getHistoryFinal">
    <query-param name="uuidTask" type="long" />
		SELECT CASE 
				WHEN trths.CODE_PROCESS = '001' THEN 'Assignment' 
				WHEN trths.CODE_PROCESS = '002' THEN 'Re-assignment'
				WHEN trths.CODE_PROCESS = '003' THEN 'Submitted' 
				WHEN trths.CODE_PROCESS = '004' THEN 'Verified' 
				WHEN trths.CODE_PROCESS = '005' THEN 'Approval' 
				WHEN trths.CODE_PROCESS = '006' THEN 'Released'
				WHEN trths.code_process = '007' THEN 'Deleted'
				WHEN trths.CODE_PROCESS = '008' THEN 'Rejected'
				WHEN trths.CODE_PROCESS = '009' THEN 'Rejected on Verification'
				WHEN trths.CODE_PROCESS = '010' THEN 'Rejected on Approval'
				WHEN trths.CODE_PROCESS = '011' THEN 'On Core'
				WHEN trths.CODE_PROCESS = '015' THEN 'Unassigned'
				WHEN trths.CODE_PROCESS = '013' THEN 'Rejected with Re-Survey'
				WHEN trths.CODE_PROCESS = '014' THEN 'Promise to Survey'
				WHEN trths.CODE_PROCESS = '012' THEN 'Canceled'
			END TASK_PROCESS,
			trths.ACTOR, trths.FIELD_PERSON,
			trths.DTM_CRT,
			trths.NOTES, 
			msst.STATUS_TASK_DESC, 
			trths.uuid_task_h,
			trths.CODE_PROCESS,
			trths.UUID_TASK_REJECTED_HISTORY
		FROM FINAL_TR_TASKHISTORY trths with (nolock)
			LEFT JOIN MS_STATUSTASK msst with (nolock) 
			ON trths.UUID_STATUS_TASK = msst.UUID_STATUS_TASK
		WHERE trths.UUID_TASK_H = :uuidTask
		ORDER BY DTM_CRT desc
	</sql-query>
	
	<sql-query name="task.inquirytaskorder.getAnswerQuestionSet">
    <query-param name="uuidTask" type="long" />
    <query-param name="uuidForm" type="long" />
		WITH N AS (
		  SELECT trtd.UUID_TASK_H, trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, msfq.UUID_ANSWER_TYPE, msfq.UUID_FORM,
				msfq.QUESTION_OF_GROUP_SEQ, msfq.QUESTION_GROUP_OF_FORM_SEQ,
				trtd.OPTION_TEXT, trtd.IMAGE_PATH, trtd.LATITUDE, trtd.LONGITUDE, null as LOB_FILE, '' as UUID_TASK_DETAIL_LOB
			FROM TR_TASK_D trtd with (nolock) 
				INNER JOIN TR_TASK_H trth with (nolock) ON trtd.UUID_TASK_H = trth.UUID_TASK_H
				INNER JOIN MS_FORMHISTORY msfh with (nolock) on trth.UUID_FORM = msfh.UUID_FORM and trth.FORM_VERSION = msfh.form_version
				INNER JOIN MS_FORMQUESTIONSET msfq WITH (nolock) on msfh.UUID_FORM_HISTORY = msfq.UUID_FORM_HISTORY AND trtd.UUID_QUESTION = msfq.UUID_QUESTION
				WHERE trth.UUID_TASK_H = :uuidTask AND trth.UUID_FORM = :uuidForm
		  UNION ALL

		  SELECT trtdl.UUID_TASK_H, trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.TEXT_ANSWER, msfq.UUID_ANSWER_TYPE, msfq.UUID_FORM,
			msfq.QUESTION_OF_GROUP_SEQ, msfq.QUESTION_GROUP_OF_FORM_SEQ,
				trtdl.OPTION_TEXT, trtdl.IMAGE_PATH, trtdl.LATITUDE, trtdl.LONGITUDE, '1' LOB_FILE, trtdl.UUID_TASK_DETAIL_LOB
			FROM TR_TASKDETAILLOB trtdl with (nolock) 
				INNER JOIN TR_TASK_H trth with (nolock) ON trtdl.UUID_TASK_H = trth.UUID_TASK_H
				INNER JOIN MS_FORMHISTORY msfh with (nolock) on trth.UUID_FORM = msfh.UUID_FORM and trth.FORM_VERSION = msfh.form_version
				INNER JOIN MS_FORMQUESTIONSET msfq WITH (nolock) on msfh.UUID_FORM_HISTORY = msfq.UUID_FORM_HISTORY AND trtdl.QUESTION_ID = msfq.UUID_QUESTION
				WHERE trth.UUID_TASK_H = :uuidTask AND trth.UUID_FORM = :uuidForm
		)
		SELECT N.UUID_TASK_H, ISNULL(N.QUESTION_TEXT, '') QUESTION_TEXT, 
			ISNULL(N.TEXT_ANSWER, '') TEXT_ANSWER, 
			ISNULL(N.OPTION_TEXT, '') OPTION_TEXT,
			CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.LOB_FILE IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.IMAGE_PATH IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.LOB_FILE IS NULL) THEN '0'
				WHEN (msan.code_answer_type = '024') THEN '01'
				ELSE '10'
			END AS HAS_IMAGE,
			N.LATITUDE, 
			N.LONGITUDE, 
			N.LOB_FILE, 
			N.UUID_TASK_DETAIL_LOB, msan.code_answer_type,
			'1' AS FLAG
		FROM N INNER JOIN MS_ANSWERTYPE msan with (nolock) 
			ON N.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
		ORDER BY N.QUESTION_GROUP_OF_FORM_SEQ, N.QUESTION_OF_GROUP_SEQ
	</sql-query>
	
	<sql-query name="task.inquirytaskorder.getAnswerFinalQuestionSet">
    <query-param name="uuidTask" type="long" />
    <query-param name="uuidForm" type="long" />
		WITH N AS (
		  SELECT trtd.UUID_TASK_H, trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.TEXT_ANSWER, msfq.UUID_ANSWER_TYPE, msfq.UUID_FORM,
				msfq.QUESTION_OF_GROUP_SEQ, msfq.QUESTION_GROUP_OF_FORM_SEQ,
				trtd.OPTION_TEXT, trtd.IMAGE_PATH, trtd.LATITUDE, trtd.LONGITUDE, null as LOB_FILE, '' as UUID_TASK_DETAIL_LOB
			FROM FINAL_TR_TASK_D trtd with (nolock) 
				INNER JOIN FINAL_TR_TASK_H trth with (nolock) ON trtd.UUID_TASK_H = trth.UUID_TASK_H
				INNER JOIN MS_FORMHISTORY msfh with (nolock) on trth.UUID_FORM = msfh.UUID_FORM and trth.FORM_VERSION = msfh.form_version
				INNER JOIN MS_FORMQUESTIONSET msfq WITH (nolock) on msfh.UUID_FORM_HISTORY = msfq.UUID_FORM_HISTORY AND trtd.UUID_QUESTION = msfq.UUID_QUESTION
				WHERE trth.UUID_TASK_H = :uuidTask AND trth.UUID_FORM = :uuidForm
		  UNION ALL

		  SELECT trtdl.UUID_TASK_H, trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.TEXT_ANSWER, msfq.UUID_ANSWER_TYPE, msfq.UUID_FORM,
			msfq.QUESTION_OF_GROUP_SEQ, msfq.QUESTION_GROUP_OF_FORM_SEQ,
				trtdl.OPTION_TEXT, trtdl.IMAGE_PATH, trtdl.LATITUDE, trtdl.LONGITUDE, '1' LOB_FILE, trtdl.UUID_TASK_DETAIL_LOB
			FROM FINAL_TR_TASKDETAILLOB trtdl with (nolock) 
				INNER JOIN FINAL_TR_TASK_H trth with (nolock) ON trtdl.UUID_TASK_H = trth.UUID_TASK_H
				INNER JOIN MS_FORMHISTORY msfh with (nolock) on trth.UUID_FORM = msfh.UUID_FORM and trth.FORM_VERSION = msfh.form_version
				INNER JOIN MS_FORMQUESTIONSET msfq WITH (nolock) on msfh.UUID_FORM_HISTORY = msfq.UUID_FORM_HISTORY AND trtdl.QUESTION_ID = msfq.UUID_QUESTION
				WHERE trth.UUID_TASK_H = :uuidTask AND trth.UUID_FORM = :uuidForm
		)
		SELECT N.UUID_TASK_H, ISNULL(N.QUESTION_TEXT, '') QUESTION_TEXT, 
			ISNULL(N.TEXT_ANSWER, '') TEXT_ANSWER, 
			ISNULL(N.OPTION_TEXT, '') OPTION_TEXT,
			CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.LOB_FILE IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.IMAGE_PATH IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021') AND N.LOB_FILE IS NULL) THEN '0'
				WHEN (msan.code_answer_type = '024') THEN '01'
				ELSE '10'
			END AS HAS_IMAGE,
			N.LATITUDE, 
			N.LONGITUDE, 
			N.LOB_FILE, 
			N.UUID_TASK_DETAIL_LOB, msan.code_answer_type,
			'1' AS FLAG
		FROM N INNER JOIN MS_ANSWERTYPE msan with (nolock) 
			ON N.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
		ORDER BY N.QUESTION_GROUP_OF_FORM_SEQ, N.QUESTION_OF_GROUP_SEQ
	</sql-query>	

	<sql-query name="task.inquirytaskorder.getListTaskSurvey">
	<query-param name="uuidTask" type="long" />
		select b.TASK_ID, b.STATUS_TASK_DESC, b.UUID_TASK_H, b.FORM_NAME, b.FLAG
		from(
			select task_id as TASK_ID, STATUS_TASK_DESC as STATUS_TASK_DESC, tt.uuid_task_h as UUID_TASK_H, FORM_NAME as FORM_NAME, '1' as FLAG 
			from MS_GROUPTASK mgr with (nolock)
			inner join tr_task_h tt with (nolock) on tt.UUID_TASK_H = mgr.UUID_TASK_H
			inner join MS_STATUSTASK mst with (nolock) on mst.UUID_STATUS_TASK = tt.UUID_STATUS_TASK
			inner join ms_form mf with (nolock) on mf.uuid_form = tt.uuid_form
			where GROUP_TASK_ID = (
				select group_task_id 
				from MS_GROUPTASK mg with (nolock)
					inner join TR_TASK_H tth with (nolock) on tth.UUID_TASK_H = mg.UUID_TASK_H
				where tth.UUID_TASK_H = (
					select top 1 UUID_TASK_H_SURVEY 
					from TR_TASKLINK with (nolock) 
					where UUID_TASK_H_ORDER = :uuidTask
				)
			)
			UNION ALL
			select task_id as TASK_ID, STATUS_TASK_DESC as STATUS_TASK_DESC, 
				tt.uuid_task_h as UUID_TASK_H, FORM_NAME as FORM_NAME, '2' as FLAG 
			from MS_GROUPTASK mgr with (nolock)
				inner join FINAL_tr_task_h tt with (nolock) on tt.UUID_TASK_H = mgr.UUID_TASK_H
				inner join MS_STATUSTASK mst with (nolock) on mst.UUID_STATUS_TASK = tt.UUID_STATUS_TASK
				inner join ms_form mf with (nolock) on mf.uuid_form = tt.uuid_form
			where GROUP_TASK_ID = (
				select group_task_id 
				from MS_GROUPTASK mg with (nolock)
					inner join FINAL_TR_TASK_H tth with (nolock) on tth.UUID_TASK_H = mg.UUID_TASK_H
				where tth.UUID_TASK_H = (
					select top 1 UUID_TASK_H_SURVEY 
					from TR_TASKLINK with (nolock) 
					where UUID_TASK_H_ORDER = :uuidTask
				)
			)
		)b order by b.task_id desc
	</sql-query>
	
	<sql-query name="task.inquirytaskorder.getListTaskSurveyFinal">
    <query-param name="uuidTask" type="long" />
		select task_id, STATUS_TASK_DESC, tt.uuid_task_h, form_name, 
				'2' as FLAG 
		from MS_GROUPTASK mgr with (nolock)
			inner join final_tr_task_h tt with (nolock) on tt.UUID_TASK_H = mgr.UUID_TASK_H
			inner join MS_STATUSTASK mst with (nolock) on mst.UUID_STATUS_TASK = tt.UUID_STATUS_TASK
			inner join ms_form mf with (nolock) on mf.uuid_form = tt.uuid_form
		where GROUP_TASK_ID = 
			(select group_task_id from MS_GROUPTASK mg with (nolock)
				inner join FINAL_TR_TASK_H tth with (nolock) on tth.UUID_TASK_H = mg.UUID_TASK_H
			where tth.UUID_TASK_H = (select top 1 UUID_TASK_H_SURVEY from FINAL_TR_TASKLINK with (nolock) 
									where UUID_TASK_H_ORDER = :uuidTask)
		) order by task_id desc
	</sql-query>
</hibernate-mapping>