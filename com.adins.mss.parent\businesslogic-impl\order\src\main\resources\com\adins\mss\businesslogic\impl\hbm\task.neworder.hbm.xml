<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="task.neworder.getPublishedForm">
		<query-param name="uuidUser" type="long" />
			with n as (
			select DISTINCT msf.UUID_FORM uuidForm, msf.FORM_NAME fromName, mfh.UUID_FORM_HISTORY uuidFormHistory, mfh.FORM_VERSION version
			from MS_FORMOFGROUP mffg with (nolock)
			    join AM_MEMBEROFGROUP amog with (nolock) 
			     on mffg.UUID_MS_GROUP = amog.UUID_MS_GROUP
			    join AM_MSUSER amu with (nolock) 
			     on amog.UUID_MS_USER = amu.UUID_MS_USER
			    join MS_FORM msf with (nolock) 
			     on mffg.UUID_FORM = msf.UUID_FORM
			    join MS_FORMHISTORY mfh with (nolock) 
			     on msf.UUID_FORM =  mfh.uuid_form
			where msf.IS_ACTIVE='1' and amu.UUID_MS_USER = :uuidUser
			) 
			select cast(n.uuidForm as varchar(10)) +';' + cast(n.uuidFormHistory as varchar(10)) as 'key', 
				n.fromName as 'value'
			from n
				INNER JOIN 
			 	( select hist.UUID_FORM, MAX (hist.form_version) as "version"
			   	  from MS_FORMHISTORY hist
			   	  GROUP BY hist.UUID_FORM
			  	) grup
			on grup.UUID_FORM = n.uuidForm
			and grup.version = n.version
	</sql-query>
	
	<sql-query name="task.neworder.getQuestionsSetList">
		<query-param name="uuidForm" type="long" />
		SELECT 
			FQS.UUID_QUESTION,
			FQS.QUESTION_OF_GROUP_SEQ, 
			FQS.QUESTION_LABEL, 
			FQS.REF_ID,  
			NT.CODE_ANSWER_TYPE, 
			FQS.LOV_GROUP, 
			REPLACE(REPLACE(FQS.RELEVANT,'{' ,''),'}','') RELEVANT, 
			CASE WHEN CHARINDEX('table',FQS.CHOICE_FILTER) > 0 THEN FQS.CHOICE_FILTER ELSE REPLACE(REPLACE(FQS.CHOICE_FILTER,'{' ,''),'}','') END CHOICEFILTER,
			FQS.IS_MANDATORY, 
			FQS.IS_READONLY, 
			FQS.MAX_LENGTH,
			'' AS UUID_QUESTION_GROUP,
			REPLACE(REPLACE(FQS.CALCULATE,'{' ,''),'}','') CALCULATE,
			FQS.IS_VISIBLE
		FROM MS_FORMHISTORY FH WITH (NOLOCK) 
			JOIN MS_FORMQUESTIONSET FQS WITH (NOLOCK) ON FH.UUID_FORM_HISTORY = FQS.UUID_FORM_HISTORY
			JOIN MS_ANSWERTYPE NT WITH (NOLOCK) ON NT.UUID_ANSWER_TYPE=FQS.UUID_ANSWER_TYPE
		WHERE FH.UUID_FORM = :uuidForm
			AND FH.FORM_VERSION = (SELECT TOP 1 FORM_VERSION FROM MS_FORMHISTORY WHERE UUID_FORM = :uuidForm ORDER BY FORM_VERSION DESC)
			AND FQS.QUESTION_IS_ACTIVE = '1' 
			AND FQS.QUESTION_GROUP_IS_ACTIVE = '1'
		ORDER BY FQS.QUESTION_GROUP_OF_FORM_SEQ, FQS.QUESTION_OF_GROUP_SEQ
	</sql-query>
	<sql-query name="task.neworder.getLov">
		<query-param name="lovGroup" type="string" />
		select UUID_LOV,CODE,DESCRIPTION from MS_LOV with (nolock) 
		where LOV_GROUP = :lovGroup and is_deleted = '0' and IS_ACTIVE='1' 
		ORDER BY SEQUENCE asc
	</sql-query>
	<sql-query name="task.neworder.getLov1">
		<query-param name="lovGroup" type="string" />
		<query-param name="constraint1" type="string" />
		select 
			UUID_LOV,CODE,DESCRIPTION 
		from MS_LOV with (nolock) 
		where 
			LOV_GROUP = :lovGroup and 
			is_deleted = '0' and
			IS_ACTIVE='1' and
			constraint_1 in (:constraint1, '%')
		ORDER BY SEQUENCE asc
	</sql-query>
	<sql-query name="task.neworder.getLov2">
		<query-param name="lovGroup" type="string" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		select 
			UUID_LOV,CODE,DESCRIPTION 
		from MS_LOV with (nolock) 
		where 
			LOV_GROUP = :lovGroup and 
			is_deleted = '0' and
			IS_ACTIVE='1' and
			constraint_1 in (:constraint1, '%') and
			constraint_2 in (:constraint2, '%')
		ORDER BY SEQUENCE asc
	</sql-query>
	<sql-query name="task.neworder.getLov3">
		<query-param name="lovGroup" type="string" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		select 
			UUID_LOV,CODE,DESCRIPTION 
		from MS_LOV with (nolock) 
		where 
			LOV_GROUP = :lovGroup and 
			is_deleted = '0' and
			IS_ACTIVE='1' and
			constraint_1 in (:constraint1, '%') and
			constraint_2 in (:constraint2, '%') and
			constraint_3 in (:constraint3, '%')
		ORDER BY SEQUENCE asc
	</sql-query>
	<sql-query name="task.neworder.getLov4">
		<query-param name="lovGroup" type="string" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
		select 
			UUID_LOV,CODE,DESCRIPTION 
		from MS_LOV with (nolock) 
		where 
			LOV_GROUP = :lovGroup and 
			is_deleted = '0' and
			IS_ACTIVE='1' and
			constraint_1 in (:constraint1, '%') and
			constraint_2 in (:constraint2, '%') and
			constraint_3 in (:constraint3, '%') and
			constraint_4 in (:constraint4, '%')
		ORDER BY SEQUENCE asc
	</sql-query>
	<sql-query name="task.neworder.getLov5">
		<query-param name="lovGroup" type="string" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
		<query-param name="constraint5" type="string" />
		select 
			UUID_LOV,CODE,DESCRIPTION 
		from MS_LOV with (nolock) 
		where 
			LOV_GROUP = :lovGroup and 
			is_deleted = '0' and
			IS_ACTIVE='1' and
			constraint_1 in (:constraint1, '%') and
			constraint_2 in (:constraint2, '%') and
			constraint_3 in (:constraint3, '%') and
			constraint_4 in (:constraint4, '%') and
			constraint_5 in (:constraint5, '%')
		ORDER BY SEQUENCE asc
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch00">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
			 union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH = :uuidBranch 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch01">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
			 union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.constraint_1 in (:constraint1, '%')
				 
	</sql-query>
	<sql-query name="task.neworder.getLovBranch02">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_BRANCH = :uuidBranch
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch03">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				and t1.constraint_3 in (:constraint3, '%')
			union 			 
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
				 and t1.constraint_3 in (:constraint3, '%')
				 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch04">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				and t1.constraint_3 in (:constraint3, '%')
				and t1.constraint_4 in (:constraint4, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
				 and t1.constraint_3 in (:constraint3, '%')
				 and t1.constraint_4 in (:constraint4, '%')
				 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch05">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
		<query-param name="constraint5" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				and t1.constraint_3 in (:constraint3, '%')
				and t1.constraint_4 in (:constraint4, '%')
				and t1.constraint_5 in (:constraint5, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
				 and t1.constraint_3 in (:constraint3, '%')
				 and t1.constraint_4 in (:constraint4, '%')
				 and t1.constraint_5 in (:constraint5, '%')
				 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch00A">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
			 union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in :uuidBranch
				 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch01A">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
			 union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in :uuidBranch
				 and t1.constraint_1 in (:constraint1, '%')
				 
	</sql-query>
	<sql-query name="task.neworder.getLovBranch02A">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
				FROM MS_LOV t1 with (nolock)
				join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_BRANCH in :uuidBranch
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch03A">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				and t1.constraint_3 in (:constraint3, '%')
			union 			 
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in :uuidBranch
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
				 and t1.constraint_3 in (:constraint3, '%')
				 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch04A">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				and t1.constraint_3 in (:constraint3, '%')
				and t1.constraint_4 in (:constraint4, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in :uuidBranch
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
				 and t1.constraint_3 in (:constraint3, '%')
				 and t1.constraint_4 in (:constraint4, '%')
				 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch05A">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
		<query-param name="constraint5" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				and t1.constraint_3 in (:constraint3, '%')
				and t1.constraint_4 in (:constraint4, '%')
				and t1.constraint_5 in (:constraint5, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in :uuidBranch
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
				 and t1.constraint_3 in (:constraint3, '%')
				 and t1.constraint_4 in (:constraint4, '%')
				 and t1.constraint_5 in (:constraint5, '%')
				 
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch00D">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidDealer" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
		<query-param name="constraint5" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in (SELECT DOB.UUID_BRANCH FROM MS_DEALEROFBRANCH DOB JOIN MS_BRANCH MB ON DOB.UUID_BRANCH = MB.UUID_BRANCH
						WHERE DOB.UUID_DEALER = :uuidDealer
						AND MB.BRANCH_CODE !='HO')
	</sql-query>
	
	
	<sql-query name="task.neworder.getLovBranch01D">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidDealer" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
		<query-param name="constraint5" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in (SELECT DOB.UUID_BRANCH FROM MS_DEALEROFBRANCH DOB JOIN MS_BRANCH MB ON DOB.UUID_BRANCH = MB.UUID_BRANCH
						WHERE DOB.UUID_DEALER = :uuidDealer
						AND MB.BRANCH_CODE !='HO')
				 and t1.constraint_1 in (:constraint1, '%')
	</sql-query>
	
	
	<sql-query name="task.neworder.getLovBranch02D">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidDealer" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in (SELECT DOB.UUID_BRANCH FROM MS_DEALEROFBRANCH DOB JOIN MS_BRANCH MB ON DOB.UUID_BRANCH = MB.UUID_BRANCH
						WHERE DOB.UUID_DEALER = :uuidDealer
						AND MB.BRANCH_CODE !='HO')
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
	</sql-query>
	
	
	<sql-query name="task.neworder.getLovBranch03D">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidDealer" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				and t1.constraint_3 in (:constraint3, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in (SELECT DOB.UUID_BRANCH FROM MS_DEALEROFBRANCH DOB JOIN MS_BRANCH MB ON DOB.UUID_BRANCH = MB.UUID_BRANCH
										 WHERE DOB.UUID_DEALER = :uuidDealer
										 AND MB.BRANCH_CODE !='HO')
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
				 and t1.constraint_3 in (:constraint3, '%')
	</sql-query>
	
	
	<sql-query name="task.neworder.getLovBranch04D">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidDealer" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				and t1.constraint_3 in (:constraint3, '%')
				and t1.constraint_4 in (:constraint4, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in (SELECT DOB.UUID_BRANCH FROM MS_DEALEROFBRANCH DOB JOIN MS_BRANCH MB ON DOB.UUID_BRANCH = MB.UUID_BRANCH
										 WHERE DOB.UUID_DEALER = :uuidDealer
										 AND MB.BRANCH_CODE !='HO')
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
				 and t1.constraint_3 in (:constraint3, '%')
				 and t1.constraint_4 in (:constraint4, '%')
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch05D">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidDealer" type="long" />
		<query-param name="constraint1" type="string" />
		<query-param name="constraint2" type="string" />
		<query-param name="constraint3" type="string" />
		<query-param name="constraint4" type="string" />
		<query-param name="constraint5" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.constraint_1 in (:constraint1, '%')
				and t1.constraint_2 in (:constraint2, '%')
				and t1.constraint_3 in (:constraint3, '%')
				and t1.constraint_4 in (:constraint4, '%')
				and t1.constraint_5 in (:constraint5, '%')
			union
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH in (SELECT DOB.UUID_BRANCH FROM MS_DEALEROFBRANCH DOB JOIN MS_BRANCH MB ON DOB.UUID_BRANCH = MB.UUID_BRANCH
										 WHERE DOB.UUID_DEALER = :uuidDealer
										 AND MB.BRANCH_CODE !='HO')
				 and t1.constraint_1 in (:constraint1, '%')
				 and t1.constraint_2 in (:constraint2, '%')
				 and t1.constraint_3 in (:constraint3, '%')
				 and t1.constraint_4 in (:constraint4, '%')
				 and t1.constraint_5 in (:constraint5, '%')
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch">
		<query-param name="lovGroup" type="string" />
		<query-param name="constraint1" type="string" />
		select UUID_LOV,CODE,DESCRIPTION 
		from MS_LOV with (nolock) where 
			LOV_GROUP = :lovGroup AND 
			is_deleted = '0' and
			IS_ACTIVE='1' and
			CONSTRAINT_1=:constraint1 
		ORDER BY DESCRIPTION asc
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch2">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidBranch" type="long" />
		<query-param name="constraint1" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.CONSTRAINT_1 = :constraint1
			union
			SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			FROM MS_LOV t1 with (nolock)
				 join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH = :uuidBranch
				 and t1.CONSTRAINT_1 = :constraint1
	</sql-query>
	
	<sql-query name="task.neworder.getLovBranch2D">
		<query-param name="lovGroup" type="string" />
		<query-param name="uuidDealer" type="long" />
		<query-param name="constraint1" type="string" />
			 SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			 FROM MS_LOV t1 with (nolock)
				left join MS_BRANCHOFLOV mbl with (nolock)
				on t1.UUID_LOV = mbl.UUID_LOV
			 WHERE LOV_GROUP = :lovGroup
				and t1.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				and mbl.UUID_LOV is null
				and t1.CONSTRAINT_1 = :constraint1
			union
			SELECT t1.UUID_LOV, CODE, [DESCRIPTION]
			FROM MS_LOV t1 with (nolock)
			join MS_BRANCHOFLOV mbl with (nolock)
				 on t1.UUID_LOV = mbl.UUID_LOV
			WHERE LOV_GROUP = :lovGroup
				 and mbl.IS_DELETED = '0' and t1.IS_ACTIVE = '1'
				 and mbl.UUID_BRANCH  in (SELECT DOB.UUID_BRANCH FROM MS_DEALEROFBRANCH DOB JOIN MS_BRANCH MB ON DOB.UUID_BRANCH = MB.UUID_BRANCH
										  WHERE DOB.UUID_DEALER = :uuidDealer
										  AND MB.BRANCH_CODE !='HO')
				 and t1.CONSTRAINT_1 = :constraint1
	</sql-query>
	
	<sql-query name="task.neworder.getMsLov">
		<query-param name="uuidLov" type="long" />
		select CODE from MS_LOV with (nolock) where UUID_LOV = :uuidLov
	</sql-query>
	
	<sql-query name="task.neworder.getRelevantScript">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		select RELEVANT from MS_FORMQUESTIONSET
			where 
				UUID_QUESTION = ( 
									select UUID_QUESTION from MS_QUESTION where ref_id = :refId 
								)
				and UUID_FORM_HISTORY = (
									select top 1 UUID_FORM_HISTORY from MS_FORMHISTORY where UUID_FORM = :uuidForm order by dtm_crt desc
								)
	</sql-query>
	
	<sql-query name="task.neworder.getQuestRelevantCopy">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		select UUID_QUESTION, REF_ID
		from MS_FORMQUESTIONSET with(nolock)
		where 
			QUESTION_VALUE like '%{' + :refId + '}%'
			and UUID_FORM_HISTORY = (
										select top 1 UUID_FORM_HISTORY from MS_FORMHISTORY where UUID_FORM = :uuidForm order by dtm_crt desc
									)
	</sql-query>
	
	<sql-query name="task.neworder.getScriptCopyValue">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		select QUESTION_VALUE from MS_FORMQUESTIONSET
		WHERE
				UUID_QUESTION = ( 
									select UUID_QUESTION from MS_QUESTION where ref_id = :refId 
								)
				and UUID_FORM_HISTORY = (
											select top 1 UUID_FORM_HISTORY from MS_FORMHISTORY where UUID_FORM = :uuidForm order by dtm_crt desc
										)
	</sql-query>
	
	<sql-query name="task.neworder.getScriptCopyValueOcr">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		select REF_ID, QUESTION_VALUE, UUID_FORM_HISTORY, UUID_MS_SUBSYSTEM from MS_FORMQUESTIONSET
			WHERE QUESTION_VALUE like 'copyDukcapil('+ :refId +'%' +')'
			and UUID_FORM_HISTORY = ( select top 1 UUID_FORM_HISTORY from MS_FORMHISTORY where UUID_FORM = :uuidForm order by dtm_crt desc )
	</sql-query>
	
	<sql-query name="task.neworder.getScriptValidation">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		select QUESTION_VALIDATION, QUESTION_ERROR_MESSAGE, UUID_QUESTION_GROUP from MS_FORMQUESTIONSET
		WHERE
				UUID_QUESTION = ( 
									select UUID_QUESTION from MS_QUESTION where ref_id = :refId 
								)
				and UUID_FORM_HISTORY = (
											select top 1 UUID_FORM_HISTORY from MS_FORMHISTORY where UUID_FORM = :uuidForm order by dtm_crt desc
										)	
	</sql-query>
	<sql-query name="task.neworder.isReleventedQuestion">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		SELECT SUM(TOT) FROM(
			SELECT COUNT(1) AS TOT
			FROM MS_FORMHISTORY FH WITH (NOLOCK) 
			JOIN MS_FORMQUESTIONSET FQS WITH (NOLOCK) ON FH.UUID_FORM_HISTORY = FQS.UUID_FORM_HISTORY
			WHERE FH.UUID_FORM = :uuidForm
			AND FH.FORM_VERSION = (SELECT TOP 1 FORM_VERSION FROM MS_FORMHISTORY WITH (NOLOCK) WHERE UUID_FORM = :uuidForm ORDER BY FORM_VERSION DESC)
			AND (CHOICE_FILTER LIKE '%{'+ :refId +'}%' OR CHOICE_FILTER LIKE '%\['+ :refId +']%' ESCAPE '\')
		
			UNION ALL
		
			SELECT COUNT(1) AS TOT
			FROM MS_FORMHISTORY FH WITH (NOLOCK) 
			JOIN MS_FORMQUESTIONSET FQS WITH (NOLOCK) ON FH.UUID_FORM_HISTORY = FQS.UUID_FORM_HISTORY
			WHERE FH.UUID_FORM = :uuidForm
			AND FH.FORM_VERSION = (SELECT TOP 1 FORM_VERSION FROM MS_FORMHISTORY WITH (NOLOCK) WHERE UUID_FORM = :uuidForm ORDER BY FORM_VERSION DESC)
			AND RELEVANT LIKE '%{'+ :refId +'}%'
			
			UNION ALL
			
			SELECT COUNT(1) AS TOT
			FROM MS_FORMHISTORY FH WITH (NOLOCK) 
			JOIN MS_FORMQUESTIONSET FQS WITH (NOLOCK) ON FH.UUID_FORM_HISTORY = FQS.UUID_FORM_HISTORY
			WHERE FH.UUID_FORM = :uuidForm
			AND FH.FORM_VERSION = (SELECT TOP 1 FORM_VERSION FROM MS_FORMHISTORY WITH (NOLOCK) WHERE UUID_FORM = :uuidForm ORDER BY FORM_VERSION DESC)
			AND QUESTION_VALUE LIKE '%{'+ :refId +'}%'
		
			UNION ALL
		
			SELECT COUNT(1) AS TOT
			FROM MS_FORMHISTORY FH WITH (NOLOCK) 
			JOIN MS_FORMQUESTIONSET FQS WITH (NOLOCK) ON FH.UUID_FORM_HISTORY = FQS.UUID_FORM_HISTORY
			WHERE FH.UUID_FORM = :uuidForm
			AND FH.FORM_VERSION = (SELECT TOP 1 FORM_VERSION FROM MS_FORMHISTORY WITH (NOLOCK) WHERE UUID_FORM = :uuidForm ORDER BY FORM_VERSION DESC)
			AND QUESTION_VALIDATION IS NOT NULL AND QUESTION_VALIDATION != ''
			AND REF_ID = :refId
		
			UNION ALL
		
			SELECT COUNT(1) AS TOT
			FROM MS_FORMHISTORY FH WITH (NOLOCK) 
			JOIN MS_FORMQUESTIONSET FQS WITH (NOLOCK) ON FH.UUID_FORM_HISTORY = FQS.UUID_FORM_HISTORY
			WHERE FH.UUID_FORM = :uuidForm
			AND FH.FORM_VERSION = (SELECT TOP 1 FORM_VERSION FROM MS_FORMHISTORY WITH (NOLOCK) WHERE UUID_FORM = :uuidForm ORDER BY FORM_VERSION DESC)
			AND QUESTION_VALIDATION LIKE '%{'+ :refId +'}%'
		) RELEVANT
	</sql-query>
	
	<sql-query name="task.neworder.getQuestionsSetList2">
		<query-param name="uuidFormHistory" type="long" />
		<query-param name="uuidQuestionGroup" type="long" />
		SELECT 
			FQS.UUID_QUESTION,
			FQS.QUESTION_OF_GROUP_SEQ, 
			FQS.QUESTION_LABEL, 
			FQS.REF_ID,  
			NT.CODE_ANSWER_TYPE, 
			FQS.LOV_GROUP, 
			REPLACE(REPLACE(FQS.RELEVANT,'{' ,''),'}','') RELEVANT, 
			CASE WHEN CHARINDEX('table',FQS.CHOICE_FILTER) > 0 THEN FQS.CHOICE_FILTER ELSE REPLACE(REPLACE(FQS.CHOICE_FILTER,'{' ,''),'}','') END CHOICEFILTER,
			FQS.IS_MANDATORY, 
			FQS.IS_READONLY, 
			FQS.MAX_LENGTH,
			'' AS UUID_QUESTION_GROUP,
			REPLACE(REPLACE(FQS.CALCULATE,'{' ,''),'}','') CALCULATE,
			FQS.IS_VISIBLE,
			FQS.QUESTION_GROUP_LABEL,
			FQS.QUESTION_VALUE,
			MSA.ASSET_TAG_NAME
		FROM MS_FORMQUESTIONSET FQS WITH (NOLOCK) 
			JOIN MS_ANSWERTYPE NT WITH (NOLOCK) ON NT.UUID_ANSWER_TYPE=FQS.UUID_ANSWER_TYPE
			LEFT JOIN MS_ASSETTAG MSA WITH(NOLOCK) ON MSA.UUID_ASSET_TAG = FQS.UUID_ASSET_TAG
		WHERE FQS.UUID_FORM_HISTORY = :uuidFormHistory
			AND FQS.UUID_QUESTION_GROUP = :uuidQuestionGroup
			AND FQS.QUESTION_IS_ACTIVE = '1'
			AND FQS.QUESTION_GROUP_IS_ACTIVE = '1'
		ORDER BY FQS.QUESTION_GROUP_OF_FORM_SEQ, FQS.QUESTION_OF_GROUP_SEQ
	</sql-query>
	<sql-query name="task.neworder.isReleventedQuestion2">
		<query-param name="refId" type="string" />
		<query-param name="uuidFormHistory" type="long" />
		SELECT SUM(TOT) FROM(
			SELECT COUNT(1) AS TOT
			FROM MS_FORMQUESTIONSET FQS WITH (NOLOCK) 
			WHERE FQS.UUID_FORM_HISTORY = :uuidFormHistory
			AND (CHOICE_FILTER LIKE '%{'+ :refId +'}%' OR CHOICE_FILTER LIKE '%\['+ :refId +']%' ESCAPE '\')
		
			UNION ALL
		
			SELECT COUNT(1) AS TOT
			FROM MS_FORMQUESTIONSET FQS WITH (NOLOCK) 
			WHERE FQS.UUID_FORM_HISTORY = :uuidFormHistory
			AND RELEVANT LIKE '%{'+ :refId +'}%'
			
			UNION ALL
			
			SELECT COUNT(1) AS TOT
			FROM MS_FORMQUESTIONSET FQS WITH (NOLOCK) 
			WHERE FQS.UUID_FORM_HISTORY = :uuidFormHistory
			AND QUESTION_VALUE LIKE '%{'+ :refId +'}%'
		
			UNION ALL
		
			SELECT COUNT(1) AS TOT
			FROM MS_FORMQUESTIONSET FQS WITH (NOLOCK) 
			WHERE FQS.UUID_FORM_HISTORY = :uuidFormHistory
			AND QUESTION_VALIDATION IS NOT NULL AND QUESTION_VALIDATION != ''
			AND REF_ID = :refId
		
			UNION ALL
		
			SELECT COUNT(1) AS TOT
			FROM MS_FORMQUESTIONSET FQS WITH (NOLOCK) 
			WHERE FQS.UUID_FORM_HISTORY = :uuidFormHistory
			AND QUESTION_VALIDATION LIKE '%{'+ :refId +'}%'
			
			UNION ALL
			
			SELECT COUNT(1) AS TOT
			FROM MS_FORMQUESTIONSET FQS WITH (NOLOCK) 
			WHERE FQS.UUID_FORM_HISTORY = :uuidFormHistory
			AND CALCULATE LIKE '%$%'+ :refId +'%$%'
		) RELEVANT
	</sql-query>
</hibernate-mapping>