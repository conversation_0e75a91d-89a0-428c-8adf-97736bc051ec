package com.adins.mss.businesslogic.impl.survey;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.AssetDstHeatMapLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsAreaofbranch;
import com.adins.mss.model.MsAreapath;
import com.adins.mss.model.MsAssettag;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericAssetDstHeatMapLogic extends BaseLogic implements AssetDstHeatMapLogic {

	@Override
	public Map<String, Object> getCombo(AmMsuser amMsuser, AuditContext callerId) {
		Map<String, Object> resultMap = new HashMap();
		List listBranchCombo = new ArrayList<>();
		if (GlobalVal.JOB_ADMIN.equals(amMsuser.getMsJob().getJobCode())) {
			listBranchCombo = this.getManagerDAO().selectAllNative(
					"monitoring.assetdistribution.getBranchCombo", null, null);
		} 
		else {
			String[][] params = { { "branchId", String.valueOf(amMsuser.getMsBranch().getUuidBranch()) } };
			listBranchCombo = this.getManagerDAO().selectAllNative(
					"monitoring.assetdistribution.getBranchComboByJob", params, null);
		}
		
		Map<String, String> branchCombo = new HashMap<String, String>();
		if (!listBranchCombo.isEmpty()) {
			for (int i = 0; i < listBranchCombo.size(); i++) {
				Map temp = (Map) listBranchCombo.get(i);
				branchCombo.put(temp.get("d0").toString(),
						(String) temp.get("d2")+" - "+(String) temp.get("d1"));
			}
		}
		
		Object[][] paramsAst = { { Restrictions.eq("isActive", "1") }, { Restrictions.isNotNull("assetTagImage") } };
		Map<String, Object> mapAssetTag = this.getManagerDAO().list(MsAssettag.class, paramsAst, null);
		List<MsAssettag> listAssetCombo = (List<MsAssettag>) mapAssetTag.get(GlobalKey.MAP_RESULT_LIST);
		Map<String, String> assetCombo = new HashMap<String, String>();
		if (!listAssetCombo.isEmpty()) {
			for (int i = 0; i < listAssetCombo.size(); i++) {
				assetCombo.put(String.valueOf(listAssetCombo.get(i).getUuidAssetTag()),
						(String) listAssetCombo.get(i).getAssetTagName());
			}
		}
		
		resultMap.put("branchCombo", branchCombo);
		resultMap.put("assetCombo", assetCombo);
		return resultMap;
	}

	@Override
	public List<TrTaskdetaillob> listTaskDetailLobs(String uuidAssetTag, String uuidBranch, String month, 
			String year, String uuidSubsystem, AuditContext callerId) {
		List<TrTaskdetaillob> listTrTaskDetailLobs = new ArrayList<TrTaskdetaillob>();		
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		SimpleDateFormat formatterS = new SimpleDateFormat("yyyy-MM-01 00:00:00.000");
		SimpleDateFormat formatterE = new SimpleDateFormat("yyyy-MM-dd 23:59:59.997");
		
		Date dateE = new Date();
	    Calendar calE = Calendar.getInstance();
	    calE.setTime(dateE);
	    calE.set(Calendar.MONTH, Integer.parseInt(month));
	    calE.set(Calendar.YEAR, Integer.parseInt(year));
	    calE.set(Calendar.DAY_OF_MONTH, 1);  
	    calE.add(Calendar.DATE, -1);  

        Date lastDayOfMonth = calE.getTime();
        
        Date dateS = new Date();
	    Calendar calS = Calendar.getInstance();
	    calS.setTime(dateS);
	    calS.set(Calendar.MONTH, Integer.parseInt(month)-1);
	    calS.set(Calendar.YEAR, Integer.parseInt(year));
	    
	    Date start = new Date();
	    Date end = new Date();
		try {
			start = formatterS.parse(formatterS.format(calS.getTime()));
			end = formatter.parse(formatterE.format(lastDayOfMonth));
		} 
		catch (ParseException e) {
			e.printStackTrace();
		}
		
		Map<String, Object> paramMap = new HashMap<>(); 
		StringBuilder whereStatement = new StringBuilder();
		if (!"0".equals(uuidBranch)) {
			whereStatement.append(" and msBranch.uuidBranch = :uuidBranch");
			paramMap.put("uuidBranch", Long.valueOf(uuidBranch));
		}			
		whereStatement.append(" and dtmUpd between :start and :end");
		paramMap.put("start", start);
		paramMap.put("end", end);
		
		Map<String, Object> mapTrTaskH = this.getManagerDAO().list(
				"from TrTaskH where 1=1" + whereStatement.toString(), paramMap);

		List<TrTaskH> listTrTaskHs = (List<TrTaskH>) mapTrTaskH.get(GlobalKey.MAP_RESULT_LIST);
		String[] uuidTaskH = new String[listTrTaskHs.size()];
		for (int i = 0; i < listTrTaskHs.size(); i++) {
			uuidTaskH[i] = String.valueOf(listTrTaskHs.get(i).getUuidTaskH());
		}
		
		Object[][] paramsD = { 
				{ "uuidSubsystem", uuidSubsystem }, 
				{ "uuidTaskH", Arrays.asList(uuidTaskH) }, 
				{ "uuidAssetTag", uuidAssetTag.equals("0") ? '%' : uuidAssetTag } };
//		List<Map<String, Object>> listDetailLobs = this.getManagerDAO().selectAllNative(
//				"monitoring.assetdistribution.getListTaskDetailLob", paramsD, null);
		List<Map<String, Object>> listDetailLobs = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) paramsD, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
		.append("SELECT tdl.UUID_TASK_DETAIL_LOB, tdl.LATITUDE, tdl.LONGITUDE, msat.ASSET_TAG_IMAGE ")
		.append("FROM TR_TASKDETAILLOB tdl with (nolock) ")
		.append("INNER JOIN MS_QUESTION msq with (nolock) ON tdl.QUESTION_ID = msq.UUID_QUESTION ")
		.append("INNER JOIN AM_MSSUBSYSTEM mssub with (nolock) ON msq.UUID_MS_SUBSYSTEM = mssub.UUID_MS_SUBSYSTEM ")
		.append("INNER JOIN MS_ASSETTAG msat with (nolock) ON msq.UUID_ASSET_TAG = msat.UUID_ASSET_TAG ")
		.append("INNER JOIN TR_TASK_H taskh with (nolock) ON tdl.UUID_TASK_H = taskh.UUID_TASK_H ")
		.append("LEFT OUTER JOIN MS_STATUSTASK mst with (nolock) on taskh.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
		.append("WHERE tdl.LATITUDE IS NOT NULL AND ")
		.append("tdl.LONGITUDE IS NOT NULL AND ")
		.append("msq.UUID_ASSET_TAG IS NOT NULL ")
		.append("and mst.STATUS_CODE not in ('RS') ")
		.append(paramsQueryString);
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    listDetailLobs = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		for (int i = 0; i < listDetailLobs.size(); i++) {
			TrTaskdetaillob trTaskdetaillob = new TrTaskdetaillob();
			trTaskdetaillob.setUuidTaskDetailLob(Long.valueOf(listDetailLobs.get(i).get("d0").toString()));
			trTaskdetaillob.setLatitude(new BigDecimal(listDetailLobs.get(i).get("d1").toString()));
			trTaskdetaillob.setLongitude(new BigDecimal(listDetailLobs.get(i).get("d2").toString()));
			listTrTaskDetailLobs.add(trTaskdetaillob);
		}
		return listTrTaskDetailLobs;
	}
	
	/*
	0 { "uuidSubsystem", uuidSubsystem }, 
	1 { "uuidTaskH", Arrays.asList(uuidTaskH) }, 
	2 { "uuidAssetTag", uuidAssetTag.equals("0") ? '%' : uuidAssetTag} 
	*/
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null){
			return new StringBuilder();
		}
		
		StringBuilder sb = new StringBuilder();
		//UUID_TASK_H
		sb.append(" AND tdl.UUID_TASK_H IN ( :uuidTaskH ) ");
		paramStack.push(new Object[]{"uuidTaskH", params[1][1]});
		
		//UUID_SUBSYSTEM
		sb.append(" AND msq.UUID_MS_SUBSYSTEM = :uuidSubsystem ");
		paramStack.push(new Object[]{"uuidSubsystem", Long.valueOf((String) params[0][1])});
		
		//---UUID_ASSET_TAG
		if (!StringUtils.equals("%", String.valueOf(params[2][1]))) {
			sb.append(" AND msq.UUID_ASSET_TAG = :uuidAssetTag ");
			paramStack.push(new Object[]{"uuidAssetTag", Long.valueOf((String) params[2][1])});
		}
		return sb;
	}

	@Override
	public Map<String, Object> branchMap(long uuidBranch,
			AuditContext callerId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if(0 != uuidBranch) {
			MsBranch branch = this.getManagerDAO().selectOne(MsBranch.class, uuidBranch);
			resultMap.put("branch", branch);
			MsAreaofbranch msAreaofbranch = (MsAreaofbranch) this.getManagerDAO().selectOne(
					"from MsAreaofbranch m join fetch m.msArea a join fetch m.msBranch b "
					+ "where b.uuidBranch = :uuidBranch", new Object[][] {{"uuidBranch", uuidBranch}});
			if (msAreaofbranch != null) {
				if (GlobalVal.AREA_POLY.equals(msAreaofbranch.getMsArea().getAreaTypeCode())) {
					Object[][] paramsArea = {
							{Restrictions.eq("msArea.uuidArea", msAreaofbranch.getMsArea().getUuidArea())}};
					String[][] ordersArea = { { "sequence", "ASC" } };
					Map<String, Object> mapAreaPath = this.getManagerDAO().list(
							MsAreapath.class, paramsArea, ordersArea);
					List<MsAreapath> listAreapaths = (List<MsAreapath>) mapAreaPath.get(GlobalKey.MAP_RESULT_LIST);
					String[][] point = new String[listAreapaths.size()][2];
					for (int i = 0; i < listAreapaths.size(); i++) {
						point[i][0] = listAreapaths.get(i).getLatitude().toString();
						point[i][1] = listAreapaths.get(i).getLongitude().toString();
					}
					resultMap.put("point", point);
				}
				resultMap.put("msArea", msAreaofbranch.getMsArea());
			}
		} 
		else {				
			Object[][] paramsBranch = {
				{ "uuidUser", callerId.getCallerId() }
			};
			
			List<Map<String,Object>> listBranch = this.getManagerDAO().selectAllNative(
					"monitoring.assetdistribution.getAllBranchByLogin", paramsBranch, null);
			List<Map<String,Object>> areaBranchList = new ArrayList<Map<String,Object>>();
			String[] branches = new String[listBranch.size()];
			
			for (int i = 0; i < listBranch.size(); i++) {
				Map<String,Object> branchMap = new HashMap<String,Object>();
				Map<String,Object> temp = (Map<String,Object>) listBranch.get(i);
				branches[i] = temp.get("d0").toString();
				
				MsBranch branch = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(branches[i]));
				branchMap.put("branch", branch);
				
				MsAreaofbranch msAreaofbranch = (MsAreaofbranch) this.getManagerDAO().selectOne(
						"from MsAreaofbranch m join fetch m.msArea a join fetch m.msBranch b "
						+ "where b.uuidBranch = :uuidBranch", new Object[][] {{"uuidBranch", Long.valueOf(branches[i])}});
				if (msAreaofbranch != null) {
					if (GlobalVal.AREA_POLY.equals(msAreaofbranch.getMsArea().getAreaTypeCode())) {
						Object[][] paramsArea = {
								{Restrictions.eq("msArea.uuidArea", msAreaofbranch.getMsArea().getUuidArea())}};
						String[][] ordersArea = { { "sequence", "ASC" } };
						Map<String, Object> mapAreaPath = this.getManagerDAO().list(
								MsAreapath.class, paramsArea, ordersArea);
						List<MsAreapath> listAreapaths = (List<MsAreapath>) mapAreaPath.get(GlobalKey.MAP_RESULT_LIST);
						String[][] point = new String[listAreapaths.size()][2];
						for (int j = 0; j < listAreapaths.size(); j++) {
							point[j][0] = listAreapaths.get(j).getLatitude().toString();
							point[j][1] = listAreapaths.get(j).getLongitude().toString();
						}
						branchMap.put("point", point);
					}
					branchMap.put("msArea", msAreaofbranch.getMsArea());
				}
				areaBranchList.add(branchMap);
			}
			
			resultMap.put("areaBranchList", areaBranchList);
		}
		return resultMap;
	}	
}
