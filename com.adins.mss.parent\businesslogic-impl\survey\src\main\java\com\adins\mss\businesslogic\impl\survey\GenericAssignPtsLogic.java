package com.adins.mss.businesslogic.impl.survey;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.survey.AssignPtsLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.MsTskdistributionofmodule;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericAssignPtsLogic extends BaseLogic implements AssignPtsLogic, MessageSourceAware{
	
	private GlobalLogic globalLogic;	
	@Autowired
	private GeolocationLogic geolocationLogic;
	@Autowired
	private TaskDistributionLogic taskDistributionLogic;
	@Autowired
	private MessageSource messageSource;
    
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}

	public void setGeolocationLogic(GeolocationLogic geolocationLogic) {
		this.geolocationLogic = geolocationLogic;
	}

	public void setTaskDistributionLogic(TaskDistributionLogic taskDistributionLogic) {
		this.taskDistributionLogic = taskDistributionLogic;
	}
	
	@Override
	public Map<String, Object> listTaskH(AmMsuser amMsuser, Object params,
			Object paramsCnt, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<String, Object>();
		String uuidStatusTask = this.getUuidStatusTask(
				GlobalVal.SURVEY_STATUS_TASK_UNASSIGN, amMsuser, callerId);
		String[][] prm = (String[][]) params;
		prm[2][1] = uuidStatusTask;
		String[][] prmCnt = (String[][]) paramsCnt;
		prmCnt[2][1] = uuidStatusTask;

		List resultPtsTask = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT * from ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("select DISTINCT *, ROW_NUMBER() OVER (ORDER BY PTS_DATE, CUSTOMER_NAME ASC) AS rownum from ")
			.append("( select pts.UUID_TASK_H, pts.CUSTOMER_NAME, pts.CUSTOMER_ADDRESS, pts.CUSTOMER_PHONE, ")
			.append("ISNULL (br.BRANCH_NAME, '-' ) as BRANCH_NAME,  ")
			.append("ISNULL (LEFT(CONVERT(VARCHAR, prev.ASSIGN_DATE, 113), 17), '-' ) as ASSIGN_DATE, ") 
			.append("ISNULL (prevusr.FULL_NAME, '-' ) as FULL_NAME, ")
			.append("ISNULL (LEFT(CONVERT(VARCHAR, prev.SUBMIT_DATE, 113), 17), '-' ) as RESCHEDULE_DATE, ")
			.append("LEFT(CONVERT(VARCHAR, pts.PTS_DATE, 113), 17) as PTS_DATE, fr.FORM_NAME, ISNULL(pts.APPL_NO, '-') as APPL_NO ")
			.append("from tr_task_h pts with (nolock) ")
			.append("left join ms_form fr with (nolock) on pts.UUID_FORM = fr.UUID_FORM ")
			.append("left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) br on pts.UUID_BRANCH = br.UUID_BRANCH ")
			.append("left join tr_task_h prev with (nolock) on pts.RESURVEY_ID = prev.UUID_TASK_H ")
			.append("left join am_msuser prevusr with (nolock) on prev.UUID_MS_USER = prevusr. UUID_MS_USER ")
			.append("where pts.uuid_status_task = :uuidStatusTask ")
			.append("and ( pts.customer_name like '%' + :customerName + '%' or pts.customer_name is null) ")
			.append("and ( ISNULL(pts.APPL_NO, '%') like '%' + :applNo + '%') ")
			.append("and ( prevusr.FULL_NAME like '%' + :rescheduleBy + '%' or prevusr.FULL_NAME is null) ")
			.append("AND COALESCE(pts.PTS_DATE, '1990-01-01 00:00:00') ")
			.append("BETWEEN (CASE WHEN :ptsDateStart = '%' THEN '1990-01-01 00:00:00' ELSE :ptsDateStart END) ")
			.append("AND (CASE WHEN :ptsDateEnd = '%' THEN '9999-12-31 23:59:59' ELSE :ptsDateEnd END) ")
			.append("and pts.pts_date is not null ")
			.append("and ( ( pts.RESURVEY_ID is null and pts.FLAG_SOURCE = 'MO' )  or (pts.RESURVEY_ID is not null ) ) ")
			.append("UNION ")
			.append("select pts.UUID_TASK_H, pts.CUSTOMER_NAME, pts.CUSTOMER_ADDRESS, pts.CUSTOMER_PHONE, ")
			.append("ISNULL (br.BRANCH_NAME, '-' ) as BRANCH_NAME,  ")
			.append("ISNULL (LEFT(CONVERT(VARCHAR, prev.ASSIGN_DATE, 113), 17), '-' ) as ASSIGN_DATE, ") 
			.append("ISNULL (prevusr.FULL_NAME, '-' ) as FULL_NAME, ")
			.append("ISNULL (LEFT(CONVERT(VARCHAR, prev.SUBMIT_DATE, 113), 17), '-' ) as RESCHEDULE_DATE, ")
			.append("LEFT(CONVERT(VARCHAR, pts.PTS_DATE, 113), 17) as PTS_DATE, fr.FORM_NAME, ISNULL(pts.APPL_NO, '-') as APPL_NO ")
			.append("from tr_task_h pts with (nolock) ")
			.append("left join ms_form fr with (nolock) on pts.UUID_FORM = fr.UUID_FORM ")
			.append("left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) br on pts.UUID_BRANCH = br.UUID_BRANCH ")
			.append("left join tr_task_h prev with (nolock) on pts.RESURVEY_ID = prev.UUID_TASK_H ")
			.append("left join am_msuser prevusr with (nolock) on prev.UUID_MS_USER = prevusr. UUID_MS_USER ")
			.append("where pts.uuid_status_task = :uuidStatusTask ")
			.append("and ( pts.customer_name like '%' + :customerName + '%' or pts.customer_name is null) ")
			.append("and ( ISNULL(pts.APPL_NO, '%') like '%' + :applNo + '%') ")
			.append("and ( prevusr.FULL_NAME like '%' + :rescheduleBy + '%' or prevusr.FULL_NAME is null) ")
			.append("AND COALESCE(pts.PTS_DATE, '1990-01-01 00:00:00') ")
			.append("BETWEEN (CASE WHEN :ptsDateStart = '%' THEN '1990-01-01 00:00:00' ELSE :ptsDateStart END) ")
			.append("AND (CASE WHEN :ptsDateEnd = '%' THEN '9999-12-31 23:59:59' ELSE :ptsDateEnd END) ")
			.append("and pts.pts_date is not null ")
			.append("and ( ( pts.RESURVEY_ID is null and pts.FLAG_SOURCE = 'MO' )  or (pts.RESURVEY_ID is not null ) ) ") 
			.append(paramsQueryString)
			.append(") as UT ")
			.append(") a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[]{"uuidBranch", ((Object[][]) params)[1][1]});
		paramsStack.push(new Object[]{"uuidStatusTask", ((Object[][]) params)[2][1]});
		paramsStack.push(new Object[]{"customerName", ((Object[][]) params)[3][1]});
		paramsStack.push(new Object[]{"applNo", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"rescheduleBy", ((Object[][]) params)[5][1]});
		paramsStack.push(new Object[]{"ptsDateStart", ((Object[][]) params)[6][1]});
		paramsStack.push(new Object[]{"ptsDateEnd", ((Object[][]) params)[7][1]});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[8][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[9][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
		
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    resultPtsTask = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		Integer resultPtsTaskCnt = NumberUtils.INTEGER_ZERO;
		Stack<Object[]> paramsStackCount = new Stack<>();		
		StringBuilder paramsQueryStringCount = this.sqlPagingBuilder((Object[][]) params, paramsStackCount, callerId);
		
		StringBuilder queryBuilderCount = new StringBuilder()
			.append("select count(1) from ")
			.append("(select DISTINCT *, ROW_NUMBER() OVER (ORDER BY PTS_DATE, CUSTOMER_NAME ASC) AS rownum from ")
			.append("( select pts.UUID_TASK_H, pts.CUSTOMER_NAME, pts.CUSTOMER_ADDRESS, pts.CUSTOMER_PHONE, ")
			.append("ISNULL (br.BRANCH_NAME, '-' ) as BRANCH_NAME,  ")
			.append("ISNULL (LEFT(CONVERT(VARCHAR, prev.ASSIGN_DATE, 113), 17), '-' ) as ASSIGN_DATE, ") 
			.append("ISNULL (prevusr.FULL_NAME, '-' ) as FULL_NAME, ")
			.append("ISNULL (LEFT(CONVERT(VARCHAR, prev.SUBMIT_DATE, 113), 17), '-' ) as RESCHEDULE_DATE, ")
			.append("LEFT(CONVERT(VARCHAR, pts.PTS_DATE, 113), 17) as PTS_DATE, fr.FORM_NAME, ISNULL(pts.APPL_NO, '-') as APPL_NO ")
			.append("from tr_task_h pts with (nolock) ")
			.append("left join ms_form fr with (nolock) on pts.UUID_FORM = fr.UUID_FORM ")
			.append("left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) br on pts.UUID_BRANCH = br.UUID_BRANCH ")
			.append("left join tr_task_h prev with (nolock) on pts.RESURVEY_ID = prev.UUID_TASK_H ")
			.append("left join am_msuser prevusr with (nolock) on prev.UUID_MS_USER = prevusr. UUID_MS_USER ")
			.append("where pts.uuid_status_task = :uuidStatusTask ")
			.append("and ( pts.customer_name like '%' + :customerName + '%' or pts.customer_name is null) ")
			.append("and ( ISNULL(pts.APPL_NO, '%') like '%' + :applNo + '%') ")
			.append("and ( prevusr.FULL_NAME like '%' + :rescheduleBy + '%' or prevusr.FULL_NAME is null) ")
			.append("AND COALESCE(pts.PTS_DATE, '1990-01-01 00:00:00') ")
			.append("BETWEEN (CASE WHEN :ptsDateStart = '%' THEN '1990-01-01 00:00:00' ELSE :ptsDateStart END) ")
			.append("AND (CASE WHEN :ptsDateEnd = '%' THEN '9999-12-31 23:59:59' ELSE :ptsDateEnd END) ")
			.append("and pts.pts_date is not null ")
			.append("and ( ( pts.RESURVEY_ID is null and pts.FLAG_SOURCE = 'MO' )  or (pts.RESURVEY_ID is not null ) ) ")		
			.append("UNION	 ")
			.append("select pts.UUID_TASK_H, pts.CUSTOMER_NAME, pts.CUSTOMER_ADDRESS, pts.CUSTOMER_PHONE, ")
			.append("ISNULL (br.BRANCH_NAME, '-' ) as BRANCH_NAME,  ")
			.append("ISNULL (LEFT(CONVERT(VARCHAR, prev.ASSIGN_DATE, 113), 17), '-' ) as ASSIGN_DATE, ") 
			.append("ISNULL (prevusr.FULL_NAME, '-' ) as FULL_NAME, ")
			.append("ISNULL (LEFT(CONVERT(VARCHAR, prev.SUBMIT_DATE, 113), 17), '-' ) as RESCHEDULE_DATE, ")
			.append("LEFT(CONVERT(VARCHAR, pts.PTS_DATE, 113), 17) as PTS_DATE, fr.FORM_NAME, ISNULL(pts.APPL_NO, '-') as APPL_NO ")
			.append("from tr_task_h pts with (nolock) ")
			.append("left join ms_form fr with (nolock) on pts.UUID_FORM = fr.UUID_FORM ")
			.append("left join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) br on pts.UUID_BRANCH = br.UUID_BRANCH ")
			.append("left join tr_task_h prev with (nolock) on pts.RESURVEY_ID = prev.UUID_TASK_H ")
			.append("left join am_msuser prevusr with (nolock) on prev.UUID_MS_USER = prevusr. UUID_MS_USER ")
			.append("where pts.uuid_status_task = :uuidStatusTask ")
			.append("and ( pts.customer_name like '%' + :customerName + '%' or pts.customer_name is null) ")
			.append("and ( ISNULL(pts.APPL_NO, '%') like '%' + :applNo + '%') ")
			.append("and ( prevusr.FULL_NAME like '%' + :rescheduleBy + '%' or prevusr.FULL_NAME is null) ")
			.append("AND COALESCE(pts.PTS_DATE, '1990-01-01 00:00:00') ")
			.append("BETWEEN (CASE WHEN :ptsDateStart = '%' THEN '1990-01-01 00:00:00' ELSE :ptsDateStart END) ")
			.append("AND (CASE WHEN :ptsDateEnd = '%' THEN '9999-12-31 23:59:59' ELSE :ptsDateEnd END) ")
			.append("and pts.pts_date is not null ")
			.append("and ( ( pts.RESURVEY_ID is null and pts.FLAG_SOURCE = 'MO' )  or (pts.RESURVEY_ID is not null ) ) ")
			.append(paramsQueryStringCount)
			.append(") as UT2  ")
			.append(") as UT ");
		
		paramsStackCount.push(new Object[]{"uuidBranch", ((Object[][]) params)[1][1]});
		paramsStackCount.push(new Object[]{"uuidStatusTask", ((Object[][]) params)[2][1]});
		paramsStackCount.push(new Object[]{"customerName", ((Object[][]) params)[3][1]});
		paramsStackCount.push(new Object[]{"applNo", ((Object[][]) params)[4][1]});
		paramsStackCount.push(new Object[]{"rescheduleBy", ((Object[][]) params)[5][1]});
		paramsStackCount.push(new Object[]{"ptsDateStart", ((Object[][]) params)[6][1]});
		paramsStackCount.push(new Object[]{"ptsDateEnd", ((Object[][]) params)[7][1]});		
		Object[][] sqlParamsCount = new Object[paramsStackCount.size()][2];
	    
		for (int i = 0; i < paramsStackCount.size(); i++) {
			Object[] objects = paramsStackCount.get(i);
			sqlParamsCount[i] = objects;
		}
		
	    resultPtsTaskCnt = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderCount.toString(), sqlParamsCount);

		result.put(GlobalKey.MAP_RESULT_LIST, resultPtsTask);
		result.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(resultPtsTaskCnt));
		return result;
	}
	
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack, AuditContext callerId) {
		
		if (params == null){
			return new StringBuilder();
		}
		
		StringBuilder sb = new StringBuilder();
		//---UUID USER
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and pts.uuid_ms_user = :uuidMsUser ");
			paramStack.push(new Object[]{"uuidMsUser", Long.valueOf((String) params[0][1])});
		}
		return sb;
	}

	@Override
	public Map<String, Object> listTaskToAssign(String[] selectedTask, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<String, Object>();
		String[] tasks = selectedTask[0].split(",");
		Object[][] params = { {"uuidTaskH", Arrays.asList(tasks)} };
		List listTaskHs = this.getManagerDAO().selectAllNative("task.assignpts.getPtsTaskAssign", params, null);
		result.put(GlobalKey.MAP_RESULT_LIST, listTaskHs);
		result.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(listTaskHs.size()));
		return result;
	}

	@Override
	public Map<String, Object> listUser(AmMsuser amMsuser, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<String, Object>();
		Map<String, Object> resultUser = new HashMap<String, Object>();
		if (GlobalVal.JOB_ADMIN.equals(amMsuser.getMsJob().getJobCode())) {
			String svyJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, callerId);
			MsJob job = globalLogic.getMsJob(svyJobCode, callerId);
			
			Object[][] params = { { Restrictions.eq("msJob.uuidJob", job.getUuidJob()) } };
			String[][] orders = { { "fullName", "ASC" } };
			resultUser = this.getManagerDAO().selectAll(AmMsuser.class, params, orders);
		} 
		else {
			String[][] params = { { "uuidMsUser", String.valueOf(amMsuser.getUuidMsUser()) } };
			String[][] users = getUserByLogin(params, callerId);
			List<AmMsuser> listUser = new ArrayList<AmMsuser>();

			for (int i = 0; i < users.length; i++) {
				if (!"1".equals(users[i][3])) {
					AmMsuser bean = new AmMsuser();
					bean.setUuidMsUser(Long.valueOf(users[i][0]));
					bean.setLoginId(users[i][1]);
					bean.setFullName(users[i][2]);
					listUser.add(bean);
				}
			}
			resultUser.put(GlobalKey.MAP_RESULT_LIST, listUser);
			resultUser.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(listUser.size()));
		}
		Integer[][] assignment = this
				.getAssignment((List<AmMsuser>) resultUser
						.get(GlobalKey.MAP_RESULT_LIST), callerId);
		String[][] lastLoc = this.getLastLoc((List<AmMsuser>) resultUser
				.get(GlobalKey.MAP_RESULT_LIST), callerId);
		result.put("result", resultUser);
		result.put("assignment", assignment);
		result.put("location", lastLoc);
		return result;
	}

	@Override
	public AmMsuser getUser(long uuidMsUser, AuditContext callerId) {
		
		AmMsuser amMsuser = new AmMsuser();
		amMsuser = (AmMsuser) this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser"
				, new Object[][] {{"uuidMsUser", uuidMsUser}});
		return amMsuser;
	}

	public Integer[][] getAssignment(List<AmMsuser> listResult, AuditContext callerId) {
		
		Integer[][] result = new Integer[listResult.size()][3];
		String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
		for (int i = 0; i < listResult.size(); i++) {
			AmMsuser usr = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.amMssubsystem "
					+ "where u.uuidMsUser = :uuidMsUser"
					, new Object[][] {{"uuidMsUser", listResult.get(i).getUuidMsUser()}});
			String[][] params = { { "uuidMsUser", String.valueOf(usr.getUuidMsUser()) },
					{ "start", currentDate + " 00:00:00.000" },
					{ "end", currentDate + " 23:59:59.997" } };
			Integer taskAssignment = (Integer) this.getManagerDAO().selectOneNative(
					"task.assignpts.getTotalTaskAssignment", params);
			Integer submittedTask = (Integer) this.getManagerDAO().selectOneNative(
					"task.assignpts.getTotalSubmittedTask",params);
			Object[][] paramsStatus = {
					{ Restrictions.eq("statusCode",
							GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
							usr.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(
					MsStatustask.class, paramsStatus);
			String[][] paramsPending = { { "uuidMsUser", String.valueOf(usr.getUuidMsUser()) },
					{ "uuidStatusTask", String.valueOf(msStatustask.getUuidStatusTask()) } };
			Integer pendingTask = (Integer) this.getManagerDAO()
					.selectOneNative("task.assignpts.getTotalPendingTask",
							paramsPending);
			result[i][0] = taskAssignment;
			result[i][1] = submittedTask;
			result[i][2] = pendingTask;
		}
		return result;
	}

	private String[][] getLastLoc(List<AmMsuser> listResult, AuditContext callerId) {
		String[][] result = new String[listResult.size()][2];
		for (int i = 0; i < listResult.size(); i++) {
			String[][] params = { { "uuidMsUser",
				String.valueOf(listResult.get(i).getUuidMsUser()) } };
			Object[] lastLoc = (Object[]) this.getManagerDAO().selectOneNative(
					"task.assignpts.getLastLoc", params);
			if (null != lastLoc) {

				BigDecimal lat = new BigDecimal(lastLoc[4].toString());
				BigDecimal lng = new BigDecimal(lastLoc[5].toString());
				Integer mcc = Integer.parseInt(lastLoc[7].toString());
				Integer mnc = Integer.parseInt(lastLoc[8].toString());
				Integer lac = Integer.parseInt(lastLoc[9].toString());
				Integer cellId = Integer.parseInt(lastLoc[10].toString());

				if (null != lat && null != lng) {
					if (0 == lat.compareTo((BigDecimal.ZERO))
							&& 0 == lng.compareTo(BigDecimal.ZERO)) {
						lat = null;
						lng = null;
						if (null != mcc && null != mnc && null != lac
								&& null != cellId) {
							List<LocationBean> listLocation = new ArrayList<LocationBean>();
							LocationBean locationBean = new LocationBean();
							locationBean.setCellid(cellId);
							locationBean.setLac(lac);
							locationBean.setMcc(mcc);
							locationBean.setMnc(mnc);
							listLocation.add(locationBean);
							this.geolocationLogic.geocodeCellId(listLocation, callerId);
							if (null != listLocation.get(0).getCoordinate()) {
								result[i][0] = new BigDecimal(locationBean
										.getCoordinate().getLatitude())
										.toString();
								result[i][1] = new BigDecimal(locationBean
										.getCoordinate().getLongitude())
										.toString();
							}
						}
					} 
					else {
						result[i][0] = lat.toString();
						result[i][1] = lng.toString();
					}
				}
			}			
			else {
			    result[i][0] = null;
			    result[i][1] = null;
			}
		}
		return result;
	}

	public String[][] getUserByLogin(Object params, AuditContext callerId) {

		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getUserByLogin", params, null);
		if (!list.isEmpty()) {
			String[][] stringResult = new String[list.size()][4];
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				stringResult[i][0] = map.get("d0").toString();
				stringResult[i][1] = (String) map.get("d1");
				stringResult[i][2] = (String) map.get("d2");
				stringResult[i][3] = ((Integer) map.get("d3")).toString();
			}
			return stringResult;
		} 
		else {
			return null;
		}

	}

	public String getUuidStatusTask(String statusCode, AmMsuser loginBean, AuditContext callerId) {
		
		String uuidStatusTask = StringUtils.EMPTY;
		Object[][] params = {
				{ Restrictions.eq("statusCode", statusCode) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
						loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
		MsStatustask msStatustask = (MsStatustask) this.getManagerDAO()
				.selectOne(MsStatustask.class, params);
		uuidStatusTask = String.valueOf(msStatustask.getUuidStatusTask());
		return uuidStatusTask;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void assignTask(String[] selectedTask, long uuidMsUser,
		AmMsuser loginBean, long uuidPriority, AuditContext callerId) {
		
		MsPriority msPriority = this.getManagerDAO().selectOne(
				MsPriority.class, uuidPriority);
		Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_NEW) } };
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
		String[] tasks = selectedTask[0].split(",");
		
		for (int i = 0; i < tasks.length; i++) {
			AmMsuser amMsuser = this.getManagerDAO().selectOne(
					AmMsuser.class, uuidMsUser);
			TrTaskH trTaskH = this.getManagerDAO().selectOne(
					"from TrTaskH t join fetch t.msStatustask m join fetch m.amMssubsystem "
					+ "where t.uuidTaskH = :uuidTaskH"
					, new Object[][] {{"uuidTaskH", Long.valueOf(tasks[i])}});
			MsStatustask sts = this.getManagerDAO().selectOne(
					MsStatustask.class,
					trTaskH.getMsStatustask().getUuidStatusTask());
			if (!GlobalVal.SURVEY_STATUS_TASK_UNASSIGN.equals(sts.getStatusCode())){
				throw new ChangeException(
						this.messageSource.getMessage("businesslogic.error.changeexception", 
								null, this.retrieveLocaleAudit(callerId)),sts.getStatusCode());
			}
			
			trTaskH.setAmMsuser(amMsuser);
			trTaskH.setAssignDate(new Date());
			trTaskH.setMsPriority(msPriority);
			trTaskH.setDtmUpd(new Date());
			trTaskH.setUsrCrt(String.valueOf(loginBean.getUuidMsUser()));
			trTaskH.setMsStatusmobile(msm);
			this.getManagerDAO().update(trTaskH);

			String[][] param = {{"param", GlobalVal.PROCESS_CODE_MSCORE}};
			BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
					selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
			String statusCode = wfEngineLogic.commitCurrentTask(
					uuidProcess.longValue(), trTaskH.getUuidTaskH());
			Object[][] params2 = {{ Restrictions.eq("statusCode", statusCode) },
					{Restrictions.eq("amMssubsystem.uuidMsSubsystem",loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(
					MsStatustask.class, params2);
			trTaskH.setMsStatustask(msStatustask);

			TrTaskhistory trTaskhistory = new TrTaskhistory();
			trTaskhistory.setCodeProcess(GlobalVal.CODE_PROCESS_ASSIGNMENT);
			trTaskhistory.setActor(loginBean.getFullName());
			trTaskhistory.setFieldPerson(amMsuser.getFullName());
			trTaskhistory.setMsStatustask(msStatustask);
			trTaskhistory.setTrTaskH(trTaskH);
			trTaskhistory.setNotes(trTaskH.getNotes());
			trTaskhistory.setUsrCrt(String.valueOf(loginBean.getUuidMsUser()));
			trTaskhistory.setDtmCrt(new Date());

			this.getManagerDAO().update(trTaskH);
			this.getManagerDAO().insert(trTaskhistory);

			if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
				commitOrder(loginBean, trTaskH.getNotes(), trTaskH, trTaskH
						.getAmMsuser().getAmMssubsystem(), 0,
						GlobalVal.CODE_PROCESS_ASSIGNMENT, callerId);
			}
		}
	}

	@Override
	public List getPriorityList(Object params, Object order, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.priorityList", params, order);
		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void assignPtsTask(AuditContext callerId) throws ParseException {
		
		Object[][] paramsStatus = { {"statusCode", GlobalVal.SURVEY_STATUS_TASK_UNASSIGN},
									{"subsysMo", GlobalVal.SUBSYSTEM_MO} };
		List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNative("task.assignpts.getUuidTaskPtsScheduler", paramsStatus, null);
		
		if (!resultList.isEmpty()) {
			for (int i=0; i<resultList.size(); i++) {
				Map<String, Object> map = resultList.get(i);
				BigInteger uuidTaskH = BigInteger.valueOf(Long.valueOf(map.get("d0").toString()));
				Object[][] params = { {Restrictions.eq("uuidTaskH", uuidTaskH.longValue())} };
				TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, params);
				
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
				Date now = new Date();
				String stringNow = sdf.format(now);
				now = sdf.parse(stringNow);
				
				String stringPts = sdf.format(taskH.getPtsDate());
				Date ptsDate = sdf.parse(stringPts);
				
				if (now.after(ptsDate) || now.equals(ptsDate)) {
					TrTaskH prevTask = taskH.getTrTaskH();
					AmMsuser prevUser = new AmMsuser();
					String uuidMh = StringUtils.EMPTY;
					String userUpdateUuid = StringUtils.EMPTY;
					if (null == prevTask) {
						prevUser = taskH.getAmMsuser();
						uuidMh = String.valueOf(taskH.getAmMsuser().getUuidMsUser());
					} 
					else {
						prevUser = prevTask.getAmMsuser();
						uuidMh = String.valueOf(prevUser.getAmMsuser().getUuidMsUser());
					}
					
					AmMssubsystem amMsSubsystem = prevUser.getAmMssubsystem();
					
					//Check Task Distribution
					Map<String, Object> result = null;
						MsTskdistributionofmodule msTaskDistModul =  (MsTskdistributionofmodule) this.getManagerDAO().selectOne(
							"from MsTskdistributionofmodule m join fetch m.msTaskdistribution t "
							+ "where m.amMssubsystem.uuidMsSubsystem = :uuidMsSubsystem"
							, new Object[][] {{"uuidMsSubsystem", amMsSubsystem.getUuidMsSubsystem()}});
					
					if (msTaskDistModul!=null && !GlobalVal.MODULE_TASK_DISTRIBUTION_OFF.equals(
							msTaskDistModul.getMsTaskdistribution().getCode())) {
						//Has Task Distribution
						List<TrTaskH> listTaskDist = new ArrayList<TrTaskH>();
						listTaskDist.add(taskH);
						
						if(GlobalVal.MODULE_TASK_DISTRIBUTION_ROUND_ROBIN.equals(msTaskDistModul.getMsTaskdistribution().getCode())){
							result = taskDistributionLogic.distributeRoundRobin(listTaskDist, amMsSubsystem, uuidMh, null, null, callerId);
						}
						else if(GlobalVal.MODULE_TASK_DISTRIBUTION_LOW_TASK.equals(msTaskDistModul.getMsTaskdistribution().getCode())){
							result = taskDistributionLogic.distributeLowTask(listTaskDist,amMsSubsystem, uuidMh, null, null, callerId);
						}
						else if(GlobalVal.MODULE_TASK_DISTRIBUTION_ZIP_CODE.equals(msTaskDistModul.getMsTaskdistribution().getCode())){
							result = taskDistributionLogic.distributeZipCode(listTaskDist,amMsSubsystem, taskH.getZipCode(), uuidMh, null, null, callerId);
						}
						else if(GlobalVal.MODULE_TASK_DISTRIBUTION_GEOFENCING.equals(msTaskDistModul.getMsTaskdistribution().getCode())){
							if(taskH.getLatitude() != null && taskH.getLongitude()!= null){
								result = taskDistributionLogic.distributeGeofencing(listTaskDist,amMsSubsystem,
										taskH.getLatitude()+";"+taskH.getLongitude(), uuidMh, null, null, callerId);
							}
						}
						if(result!= null && result.size()!= 0){
							AmMsuser userAssign = (AmMsuser) result.get("userAssign");
							userUpdateUuid = String.valueOf(userAssign.getUuidMsUser());
							callerId.setCallerId(userUpdateUuid);
						}
					}
					else {
						if (null != prevTask) {
							userUpdateUuid = String.valueOf(prevTask.getAmMsuser().getUuidMsUser());
							callerId.setCallerId(userUpdateUuid);
							String[][] param = {{"param", GlobalVal.PROCESS_CODE_MSCORE}};
							BigInteger uuidProcess = (BigInteger) this.getManagerDAO().
									selectOneNativeString("select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :param", param);
							MsStatustask msStatustaskSurvey = commitWfAndUpdateTaskH(taskH, uuidProcess.longValue(), amMsSubsystem, 0);
							taskH.setAmMsuser(prevTask.getAmMsuser());
							taskH.setUsrUpd(String.valueOf(prevTask.getAmMsuser().getUuidMsUser()));
							taskH.setDtmUpd(new Date());
							taskH.setAssignDate(new Date());
							taskH.setMsStatustask(msStatustaskSurvey);
							
							insertTaskHistory(callerId, msStatustaskSurvey, taskH, "Assign Task With Round Robin Module",
							        GlobalVal.CODE_PROCESS_ASSIGNMENT, prevTask.getAmMsuser().getFullName());
						}
					}
					
					if (null != result && result.size()!= 0 && GlobalVal.SUBSYSTEM_MO.equals(taskH.getFlagSource())) {
						TrTaskH taskbean = (TrTaskH) result.get("taskH");
						commitOrder(prevUser, taskbean.getNotes(), taskbean, taskbean
								.getAmMsuser().getAmMssubsystem(), 0,
								GlobalVal.CODE_PROCESS_ASSIGNMENT, callerId);
					}
				}
			}
		}
	}
	
	@Override
	public Map<String, Object> listTaskHByHierarkiUser(AmMsuser amMsuser, Object params,
			Object paramsCnt, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<String, Object>();
		String uuidStatusTask = this.getUuidStatusTask(
				GlobalVal.SURVEY_STATUS_TASK_UNASSIGN, amMsuser, callerId);
		String[][] prm = (String[][]) params;
		prm[2][1] = uuidStatusTask;
		String[][] prmCnt = (String[][]) paramsCnt;
		prmCnt[2][1] = uuidStatusTask;

		List resultPtsTask = this.getManagerDAO()
				.selectAllNative("task.assignpts.getPtsTaskListByHierarkiUser",
						prm, null);
		
		Integer resultPtsTaskCnt = (Integer) this.getManagerDAO()
				.selectOneNative("task.assignpts.getPtsTaskListCountByHierarkiUser",
						prmCnt);

		result.put(GlobalKey.MAP_RESULT_LIST, resultPtsTask);
		result.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(resultPtsTaskCnt));
		return result;
	}
}
