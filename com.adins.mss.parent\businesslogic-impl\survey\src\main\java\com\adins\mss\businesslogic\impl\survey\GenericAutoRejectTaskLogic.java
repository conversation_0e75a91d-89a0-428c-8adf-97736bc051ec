package com.adins.mss.businesslogic.impl.survey;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.AutoRejectTaskLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;

public class GenericAutoRejectTaskLogic extends BaseLogic implements AutoRejectTaskLogic{

	@Transactional
	@SuppressWarnings({ "unchecked", "unused" })
	@Override
	public void autoRejectTask(AuditContext callerId) {
		//sementara di disable
//		List<TrTaskH> taskList = getListTask();
//		String duration = (String) this.getManagerDAO().selectOneNativeString("SELECT GS_VALUE FROM AM_GENERALSETTING WHERE GS_CODE = :gsCode", new Object[][] {{"gsCode", "MS_DURATION_AUTOREJECT"}});
//		for (TrTaskH taskH : taskList) {
//				Calendar cal = Calendar.getInstance();
//				cal.setTime(taskH.getStartDtm());
//				cal.add(Calendar.HOUR_OF_DAY, Integer.parseInt(duration));
//				Date CurrentTime = new Date();
//				Date ExpiredTime =  cal.getTime();
//				if (ExpiredTime.before(CurrentTime)) {
//					autoReject(taskH, callerId);
//				}
//			}
		}

	private void autoReject(TrTaskH taskH, AuditContext callerId) {
		Object[][] params = {{Restrictions.eq("statusCode", "D")}, {Restrictions.eq("amMssubsystem.uuidMsSubsystem", taskH.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem())}};
		MsStatustask delStatus = this.getManagerDAO().selectOne(MsStatustask.class, params);
		String fieldPerson = (String) this.getManagerDAO().selectOneNativeString("select FULL_NAME from AM_MSUSER where UUID_MS_USER = :uuidMsUser", new Object[][] {{"uuidMsUser", Long.valueOf(taskH.getAmMsuser().getUuidMsUser())}});
		taskH.setMsStatustask(delStatus);
		taskH.setNotes(appendNotes(taskH.getNotes(), "Auto Reject"));
		TrTaskhistory obj = new TrTaskhistory();
		obj.setUsrCrt(callerId.getCallerId());
		obj.setDtmCrt(new Date());
		obj.setTrTaskH(taskH);
		obj.setMsStatustask(delStatus);
		obj.setNotes(taskH.getNotes());
		obj.setFieldPerson(fieldPerson);
		obj.setActor(callerId.getCallerId());
		obj.setCodeProcess(GlobalVal.CODE_PROCESS_DELETED);
		this.getManagerDAO().insert(obj);
	}

	String appendNotes(String notes, String appended) {
		try {
			String newNotes = "";

			if (null != notes && !"".equals(notes)) { // have old notes
				newNotes = notes + "|" + appended;
			} else { // new notes
				newNotes = appended;
			}

			return newNotes;
		} catch (Exception e) {
			e.printStackTrace();
			return appended;
		}
	}
	
	@SuppressWarnings("rawtypes")
	private List getListTask(){
		Map taskMap = this.getManagerDAO().list("from TrTaskH t join fetch t.msStatustask m join fetch m.amMssubsystem a where t.startDtm is not null and m.statusCode = :statusCode and a.uuidMsSubsystem = '4'", new Object[][] {{"statusCode", "N"}});
		List taskList = (List)taskMap.get(GlobalKey.MAP_RESULT_LIST);
		return taskList;
	}
	
}
