package com.adins.mss.businesslogic.impl.survey;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.ReportCMORecommendationLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericCMORecommendationLogic extends BaseLogic 
implements ReportCMORecommendationLogic, MessageSourceAware{
	private static final String[] HEADER_DETAIL = { "NIK User Mobile Survey","Nama User Mobile Survey", "NIK SPV",
			"Nama SPV", "App/ODR Date", "No. App/ODR", "Nama Customer", "SOA", "Product Offering Type", "Region", "Office",
			"Total Duplicate", "Cust Rating", "New/RO", "SLIK Aktif", "SLIK Non Aktif", "Reason CMO Recommendation", 
			"Notes/Keterangan"};
		private static final int[] DETAIL_COLUMN_WIDTH = { 10 * 256, 40 * 256, 30 * 256,
			40 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256,
			30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256};
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericReportBranchLogic.class);
	
	@Autowired
    private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    
	private String keyHeader = "header";
    
    /*
     * 1. NIK User Mobile Survey
     * 2. Nama User Mobile Survey
     * 3. NIK Supervisor
     * 4. Nama Supervisor
     * 5. Order Date
     * 6. Appl No
     * 7. Nama Customer
     * 8. Region
     * 9. Office
     * 10. UUID_TASK_H
     * 11. SOA
     * 12. Product offering
     * 13. Total duplicate
     * 14. Cust rating
     * 15. New/Ro
     * 16. SLIK Aktif
     * 17. SLIK Non Aktif
     * 18. Reason Rekomendasi CMO
     * 19. Notes Rekomendasi CMO
     * */
	@Override
	public List<Map<String, Object>> getCmoRecommendationList(String[][] params, AuditContext callerId) {
			
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT ams.LOGIN_ID as nikUser, ams.FULL_NAME as namaUser, spv.LOGIN_ID as nikSpv, spv.FULL_NAME as namaSpv, tth.ASSIGN_DATE, tth.APPL_NO, tth.CUSTOMER_NAME, ")
				.append("tabel1.SVY_SOA, tabel1.SVY_PRODUCT_OFFERING, mr.REGION_NAME, msb.BRANCH_NAME, tabel1.SVY_TOTAL_DUPLICATE, tabel1.SVY_CUST_RATING, tabel1.SVY_NEW_RO, "
						+ "tabel1.MAX_DPD_BDE_PLA_PMHN, tabel1.MAX_DPD_PLA_PMHN, tabel1.SVY_R_CMO_RCMD, tabel1.SVY_NOTES_CMO_RCMD, tabel1.UUID_TASK_H from ( ")
				.append("SELECT * from getDataCmoRecommendation( :startDate , :endDate )")
				.append(") tabel1 ")
				.append("JOIN TR_TASK_H tth on tth.UUID_TASK_H = tabel1.UUID_TASK_H ")
				.append("JOIN AM_MSUSER ams on ams.UUID_MS_USER = tth.UUID_MS_USER ")
				.append("JOIN AM_MSUSER spv on spv.UUID_MS_USER = ams.SPV_ID ")
				.append("JOIN MS_BRANCH msb on msb.UUID_BRANCH = ams.UUID_BRANCH ")
				.append("JOIN MS_REGION mr on mr.UUID_REGION = msb.UUID_REGION ")
				.append("WHERE 1=1 ")
				.append(paramsQueryString);
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
		List<Map<String,Object>> result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString() , sqlParams);
			
	    return result;
	}
	
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}

		StringBuilder sb = new StringBuilder();
		//---REGION_NAME
		if (!StringUtils.equals("0", (String) params[0][1])) {
			sb.append(" AND msb.UUID_REGION = :uuidRegion ");
			paramStack.push(new Object[]{"uuidRegion", (String) params[0][1]});
		}
		
		if (!StringUtils.equals("0", (String) params[5][1])) {
			sb.append(" AND tabel1.SVY_PRODUCT_OFFERING = :prodOff ");
			paramStack.push(new Object[]{"prodOff", (String) params[5][1]});
		}
		
		if (!StringUtils.equals("0", (String) params[6][1])) {
			sb.append(" AND tabel1.SVY_CUST_RATING = :custRating ");
			paramStack.push(new Object[]{"custRating", Long.valueOf((String) params[6][1])});
		}
		
		if(!"0".equals(params[1][1])) {
			sb.append(" AND msb.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[] {"uuidBranch", Long.valueOf((String) params[1][1])});
		}
		
		if(!StringUtils.isBlank(params[2][1].toString())) {
			sb.append(" AND tth.APPL_NO like '%'+ :applNo +'%' ");
			paramStack.push(new Object[] {"applNo", (String) params[2][1]});
		}
		
		paramStack.push(new Object[] {"startDate", (String) params[3][1]});
		paramStack.push(new Object[] {"endDate", (String) params[4][1]});

		return sb;
	}
	
	@Override
	public List<Map<String, Object>> getRegionListCombo(String uuidBranch, AuditContext callerId) {
		List<Map<String,Object>> result;
		String[][] params = { { "isActive", "1" }, { "uuidBranch", uuidBranch }};
		result = this.getManagerDAO().selectAllNativeString(
				"SELECT DISTINCT region.REGION_NAME, region.UUID_REGION FROM MS_REGION region WITH (NOLOCK) "
				+ "join MS_BRANCH msb WITH (NOLOCK) on msb.UUID_REGION = region.UUID_REGION "
				+ "WHERE region.IS_ACTIVE = :isActive AND msb.UUID_BRANCH in (select keyValue from dbo.getCabangByLogin(:uuidBranch))"
				+ "ORDER BY region.REGION_NAME asc", params);
		return result;
	}
	
	@Override
	public List<Map<String, Object>> getProdOffTypeListCombo(AuditContext callerId) {
		List<Map<String,Object>> result;
		String[][] params = { { "isDeleted", "0" }};
		result = this.getManagerDAO().selectAllNativeString(
				"SELECT DISTINCT (prod.PRODUCT_OFFERING_NAME) "
				+ "FROM STAGING_PRODUCT_OFFERING prod WITH (NOLOCK) "
				+ "WHERE prod.IS_DELETED = :isDeleted "
				+ "ORDER BY prod.PRODUCT_OFFERING_NAME asc", params);
		return result;
	}

	@Override
	public byte[] exportExcel(String[][] params, AuditContext callerId) {
		XSSFWorkbook workbook = this.createXlsTemplate(params, callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(String[][] params, AuditContext callerId) {
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setUuidLoginId(callerId.getCallerId());
		reportBean.setUuidBranch(params[1][1]);
		reportBean.setStartDate(params[3][1]);
		reportBean.setEndDate(params[4][1]);
		reportBean.setParamsAction(params);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);
		
		AmMsuser amMsUser = new AmMsuser();
		amMsUser.setUuidMsUser(Long.valueOf(callerId.getCallerId()));
		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("CMO Recommendation Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_CMO_RECOMMENDATION.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}
	
	public XSSFWorkbook createXlsTemplate(String[][] params, AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Report CMO Recommendation");
				List result= this.getCmoRecommendationList(params, callerId);
				this.createData(workbook, sheet, result, params[3][1], params[4][1]);
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private static Map<String, CellStyle> createStyles(Workbook wb) {
		Map<String, CellStyle> styles = new HashMap<>();
		CellStyle style;

		style = wb.createCellStyle();
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		styles.put("cell", style);

		style = wb.createCellStyle();
		Font font = wb.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setFillPattern(CellStyle.SOLID_FOREGROUND);
		styles.put("header", style);
		return styles;
	}
	
	private void createData(XSSFWorkbook workbook, XSSFSheet sheet,
			List result, String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell = 0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		XSSFRow rowHeader = sheet.createRow(rowcell++);
		for (int i = 0; i < HEADER_DETAIL.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT CMO RECOMMENDATION PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get(keyHeader));
			
			XSSFCell cellNo = rowHeader.createCell(0);
			cellNo.setCellValue("NO");
			cellNo.setCellStyle(styles.get(keyHeader));
			
			XSSFCell cell = rowHeader.createCell(i+1);
			cell.setCellValue(HEADER_DETAIL[i]);
			cell.setCellStyle(styles.get(keyHeader));
			sheet.setColumnWidth(i, DETAIL_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_DETAIL.length));

		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i + (double) 1);
			cellNo.setCellStyle(styles.get("cell"));
			//data cell			
			for (int j = 1; j < temp.size(); j++) {
				XSSFCell cell = rowData.createCell(j);
				if(temp.get("d"+(j-1)) != null) {
					cell.setCellValue(temp.get("d" + (j-1)).toString());
				} else {
					cell.setCellValue("");
				}
				
				cell.setCellStyle(styles.get("cell"));
			}
			
		} 
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(reportBean.getParamsAction(), callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("ReportCMORecommendation_");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getEndDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
					sb.append("ALL");
				} 
				else {
					MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
							Long.valueOf(reportBean.getUuidBranch()));
					sb.append(msBranch.getBranchCode());
				}
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(trReportResultLog
				.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime().getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}
}
