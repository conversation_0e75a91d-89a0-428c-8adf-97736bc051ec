package com.adins.mss.businesslogic.impl.survey;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.net.UnknownHostException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Types;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Stack;
import java.util.TimeZone;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.joda.time.Period;
import org.joda.time.PeriodType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.common.TaskServiceLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.CreateTaskLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.exceptions.SubmitTaskException;
import com.adins.mss.exceptions.UploadTaskException;
import com.adins.mss.exceptions.UploadTaskException.Reason;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.CheckCustomerHistory;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsMappingformH;
import com.adins.mss.model.MsOrdertag;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TblApiUsage;
import com.adins.mss.model.TblProductCategory;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskcolldata;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.TrTaskrejectedhistory;
import com.adins.mss.model.TrTasksurveydata;
import com.adins.mss.model.custom.Query;
import com.adins.mss.model.custom.Query.Constraint;
import com.adins.mss.model.custom.Query.Join;
import com.adins.mss.model.custom.TaskDukcapilBean;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.model.custom.UploadImageBean;
import com.adins.mss.model.custom.UploadImageRequestBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.ImageBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.QuestionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.InstantApprovalResponse;
import com.adins.mss.services.model.common.SubmitTaskDBean;
import com.adins.mss.services.model.newconfins.CIFBean;
import com.adins.mss.services.model.newconfins.CheckDukcapilResponse;
import com.adins.mss.services.model.newconfins.OCRForNcBean;
import com.adins.mss.services.model.newconfins.OCRForNcResponse;
import com.adins.mss.services.model.newconfins.SubmitPage1Request;
import com.adins.mss.services.model.newconfins.SubmitPage2Request;
import com.adins.mss.util.CipherTool;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericCreateTaskLogic extends BaseLogic implements CreateTaskLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericCreateTaskLogic.class);
	private Gson gson = new GsonBuilder().serializeNulls().create();
	
	@Autowired
	private MessageSource messageSource;

	private CommonLogic commonLogic;
	private IntFormLogic intFormLogic;
	private ImageStorageLogic imageStorageLogic;
	private GeolocationLogic geocoder;
	private TaskDistributionLogic taskDistributionLogic; 	
	private TaskServiceLogic taskServiceLogic;	
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }
	
	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}
	
	public void setTaskDistributionLogic(TaskDistributionLogic taskDistributionLogic) {
		this.taskDistributionLogic = taskDistributionLogic;
	}
	
	public void setTaskServiceLogic(TaskServiceLogic taskServiceLogic) {
		this.taskServiceLogic = taskServiceLogic;
	}

	@Override
	public List<Map<String, Object>> listAnswer(long uuidForm, AuditContext callerId){		
		Object[][] params = new Object[][]{{"uuidForm", uuidForm}};
		List<Map<String,Object>> listQuestions = this.getManagerDAO().selectAllNative("task.neworder.getQuestionsSetList", params, null);
		List<Map<String,Object>> result = new ArrayList<>(listQuestions.size());
		for (int i = 0; i < listQuestions.size(); i++) {
            Map<String, Object> temp = listQuestions.get(i);
            temp.put("d12", temp.get("d12") != null ? temp.get("d12") : StringUtils.EMPTY);
            Object paramsIsRelevented[][] = {{"refId", (String)temp.get("d3")},{"uuidForm", uuidForm}};
            Integer qRelevented = (Integer)this.getManagerDAO().selectOneNative("task.neworder.isReleventedQuestion", paramsIsRelevented);
            if(qRelevented > 0) {
            	 temp.put("d17", "1");
            }else {
            	temp.put("d17", "0");
            }
            result.add(temp);
		}
		
		List<Map<String,Object>> resultFinal = new ArrayList<>();
		if (!result.isEmpty()) {
			for (int i=0; i<result.size(); i++) {
				Map<String,Object> mapResult = result.get(i);
				String calculateString = StringUtils.EMPTY;
				if (mapResult.get("d12") != null) {
					calculateString = (String) mapResult.get("d12");
				}
				String calculateTemp = StringUtils.EMPTY;
				String calculate = StringUtils.EMPTY;
				String results = StringUtils.EMPTY;
				if (StringUtils.isNotBlank(calculateString)) {
					String[] calculateArr = calculateString.split("start");
					calculateArr = calculateArr[calculateArr.length-1].split("end");
					calculateTemp = calculateArr[0].replace("_var", StringUtils.EMPTY).replace("/*", StringUtils.EMPTY).replace("*/", StringUtils.EMPTY).replace(" ", StringUtils.EMPTY);
					calculateTemp = calculateTemp.replace("$", StringUtils.EMPTY).replace("result=", StringUtils.EMPTY);

					mapResult.put("d15", calculateTemp);
					calculate = calculateTemp.trim().replace("+", ",").replace("-", ",")
							.replace("/", ",").replace("%", ",").replace("*", ",");
					if (calculate.contains(",")) {
						String[] calculateArrTemp = calculate.split(",");
						
						int check=0;
						for(int j=0;j<calculateArrTemp.length;j++){
							if((!StringUtils.isNumeric(calculateArrTemp[j]) && (calculateArrTemp[j] != null))){
								check ++;
							}
						}
						String[] tempArr= new String[check];
						int tempTtl = 0;
						for (int k=0;k<calculateArrTemp.length;k++) {
							if ((!StringUtils.isNumeric(calculateArrTemp[k]) && (calculateArrTemp[k] != null))) {
								tempArr[k-tempTtl]=calculateArrTemp[k];
							} 
							else {
								tempTtl += 1;
							}
						}
						results = StringUtils.join(tempArr,",");
					}
					else {
						results = calculate;
					}
					mapResult.put("d16", results);
				}
				else {
					mapResult.put("d15", StringUtils.EMPTY);
					mapResult.put("d16", StringUtils.EMPTY);
				}
				resultFinal.add(mapResult);
			}
		}

		return result;
	}
	
	@Override
	public List<Map<String, Object>> getForm(long userId, String flagFormIA,
			AuditContext callerId) {
		List<Map<String,Object>> result = new ArrayList<>();		
		if ("1".equals(flagFormIA)) {
			result = this.getManagerDAO().selectAllNative("task.createtask.getPublishedFormIA", null, null);
		} else {
			result = this.getManagerDAO().selectAllNative("task.createtask.getPublishedFormNewLead", null, null);
		}
		return result;
	}

	@Override
	public List<Map<String, String>> getInitLov(String lovGroup,long uuidDealer,String[] constraints, AuditContext callerId){
		List<Map<String,String>> result = new ArrayList<>();
			
			List<Map<String,Object>> listLov = new ArrayList<>();
			if( GlobalVal.DEALEROFBRANCH.equalsIgnoreCase(lovGroup) ){
				Object[][] params = {{"lovGroup",lovGroup},{"constraint1",uuidDealer}};
				listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch", params, null);
			}
			else{
				if(constraints.length == 0){
					String[][] params = {{"lovGroup",lovGroup}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov", params, null);
				}
				else if(constraints.length == 1){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov1", params, null);
				}
				else if(constraints.length == 2){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]},{"constraint2",constraints[1]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov2", params, null);
				}
				else if(constraints.length == 3){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]},
										{"constraint2",constraints[1]},{"constraint3",constraints[2]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov3", params, null);
				}
				else if(constraints.length == 4){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]},{"constraint2",constraints[1]},
										{"constraint3",constraints[2]},{"constraint4",constraints[3]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov4", params, null);
				}
				else if(constraints.length == 5){
					String[][] params = {{"lovGroup",lovGroup},{"constraint1",constraints[0]},{"constraint2",constraints[1]},
										{"constraint3",constraints[2]},{"constraint4",constraints[3]},{"constraint5",constraints[4]}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLov5", params, null);
				}
			}
			for(int i=0;i<listLov.size();i++){
				Map map = (HashMap) listLov.get(i);
				Map<String,String> tmp = new HashMap<>();
				tmp.put("uuid", map.get("d0").toString());
				tmp.put("id",map.get("d1")!=null ? map.get("d1").toString():StringUtils.EMPTY);
				tmp.put("name", map.get("d2").toString());
				result.add(tmp);
			}
		
		return result;
	}
	
	@Override
	public List<Map<String, String>> getInitLovBranch(String lovGroup,long uuidDealer,String[] constraints, AuditContext callerId){
		List<Map<String,String>> result = new ArrayList<>();
		
		Object[][] paramsUsr = {
				{ Restrictions.eq("uuidMsUser", Long.valueOf(callerId.getCallerId()))}};
		
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, paramsUsr);
		long uuidBranch = user.getMsBranch().getUuidBranch();
		
			List<Map<String,Object>> listLov = new ArrayList<>();
			if( GlobalVal.DEALEROFBRANCH.equalsIgnoreCase(lovGroup) ){
				Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}, {"constraint1",uuidDealer}};
				listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch2", params, null);
			}
			else{
				if("1".equals(user.getMsJob().getIsBranch())){
					if(constraints.length == 0){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch00", params, null);
					}
					else if(constraints.length == 1){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}, {"constraint1",constraints[0]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch01", params, null);
					}
					else if(constraints.length == 2){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch02", params, null);
					}
					else if(constraints.length == 3){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch03", params, null);
					}
					else if(constraints.length == 4){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]},{"constraint4",constraints[3]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch04", params, null);
					}
					else if(constraints.length == 5){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]},{"constraint4",constraints[3]},
											{"constraint5",constraints[4]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch05", params, null);
					}
				} 
				else {
					Object branchList [] = getBranchFromDealer(user.getMsDealer().getUuidDealer());
					if(constraints.length == 0){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch00A", params, null);
					}
					else if(constraints.length == 1){
						Object [][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList}, {"constraint1",constraints[0]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch01A", params, null);
					}
					else if(constraints.length == 2){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch02A", params, null);
					}
					else if(constraints.length == 3){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList},
											{"constraint1",constraints[0]},{"constraint2",constraints[1]},{"constraint3",constraints[2]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch03A", params, null);
					}
					else if(constraints.length == 4){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]},{"constraint4",constraints[3]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch04A", params, null);
					}
					else if(constraints.length == 5){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",branchList},{"constraint1",constraints[0]},
											{"constraint2",constraints[1]},{"constraint3",constraints[2]},{"constraint4",constraints[3]},
											{"constraint5",constraints[4]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch05A", params, null);
					}
				}
				
			}
			for(int i=0;i<listLov.size();i++){
				Map map = (HashMap) listLov.get(i);
				Map<String,String> tmp = new HashMap<>();
				tmp.put("uuid", map.get("d0").toString());
				tmp.put("id",map.get("d1")!=null ? map.get("d1").toString():StringUtils.EMPTY);
				tmp.put("name", map.get("d2").toString());
				result.add(tmp);
			}
		return result;
	}
	
	public String[] getBranchFromDealer(long uuidDealer) {

		Object[][] params = { { "uuidDealer", uuidDealer } };

		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"services.common.task.getBranchFromDealer", params, null);
		if (!list.isEmpty()) {
			String[] stringResult = new String[list.size()];
			for (int i = 0; i < list.size(); i++) {
				Map map = list.get(i);
				stringResult[i] = map.get("d0").toString();
			}
			return stringResult;
		} 
		else {
			return null;
		}
	}

	@Override
	public List<Map<String, String>> getLov(String[] answerCFilter,String lovGroup, long uuidDealer,String jobCode, AuditContext callerId){
		List<Map<String,String>> result = new ArrayList<>();
			
			Stack<Object[]> paramStack = new Stack<>();
			paramStack.push(new Object[]{ Restrictions.eq("lovGroup", lovGroup)});
			paramStack.push(new Object[]{ Restrictions.eq("isActive", "1")});
			paramStack.push(new Object[]{ Restrictions.eq("isDeleted", "0")});
			for (int i = 0; i < answerCFilter.length; i++) {
				Object[] value = {answerCFilter[i],"%"};
				paramStack.push(new Object[]{ Restrictions.in("constraint" + (i+1), value)});
			}
			if ( GlobalVal.LOV_TAG_BRANCH_OF_DEALER.equalsIgnoreCase(lovGroup) && GlobalVal.JOB_SD.equalsIgnoreCase(jobCode)) {
				paramStack.push(new Object[]{ Restrictions.eq("code", uuidDealer)});
			}
			
			Object[][] sqlParams = new Object[paramStack.size()][2];
		    for (int k = 0; k < paramStack.size(); k++) {
				Object[] objects = paramStack.get(k);
				sqlParams[k] = objects;
			}
		    
		    String[][] orders = {{"description", "ASC"}};
			Map<String, Object> msLov = this.getManagerDAO().list(MsLov.class, sqlParams, orders);
			List<MsLov> listLov = (List<MsLov>) msLov.get(GlobalKey.MAP_RESULT_LIST);
			for(int i=0;i<listLov.size();i++){
				MsLov lov = (MsLov) listLov.get(i);
				Map<String,String> tmp = new HashMap<>();
				tmp.put("uuid", String.valueOf(lov.getUuidLov()));
				tmp.put("id", lov.getCode());
				tmp.put("name", lov.getDescription());
				result.add(tmp);
			}
		
		return result;
	}
	
	@Override
	public List<Map<String, String>> getLovBranch(String[] answerCFilter,String lovGroup, long uuidDealer, String jobCode, AuditContext callerId){
		List<Map<String,String>> result = new ArrayList<>();
		AmMsuser user = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msJob where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
		long uuidBranch = user.getMsBranch().getUuidBranch();
			
		List<Map<String,Object>> listLov = new ArrayList<>();
			if( GlobalVal.DEALEROFBRANCH.equalsIgnoreCase(lovGroup) ){
				
				if("1".equals(user.getMsJob().getIsBranch())){
					Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}, {"constraint1",String.valueOf(uuidDealer)}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch2", params, null);
				}
				else {
					Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer}, {"constraint1",String.valueOf(uuidDealer)}};
					listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch2D", params, null);
				}
			}
			else{
				if(answerCFilter.length == 0){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch00", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch00D", params, null);
					}
				}
				else if(answerCFilter.length == 1){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch}, {"constraint1",answerCFilter[0]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch01", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer}, {"constraint1",answerCFilter[0]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch01D", params, null);
					}
				}
				else if(answerCFilter.length == 2){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch02", params, null);
					}
					else{
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch02D", params, null);
					}
				}
				else if(answerCFilter.length == 3){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch03", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch03D", params, null);
					}
				}
				else if(answerCFilter.length == 4){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]},{"constraint4",answerCFilter[3]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch04", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer},{"constraint1",answerCFilter[0]},
											{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]},{"constraint4",answerCFilter[3]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch04D", params, null);
					}
				}
				else if(answerCFilter.length == 5){
					if("1".equals(user.getMsJob().getIsBranch())){
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidBranch",uuidBranch},{"constraint1",answerCFilter[0]},
								{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]},{"constraint4",answerCFilter[3]},
								{"constraint5",answerCFilter[4]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch05", params, null);
					}
					else {
						Object[][] params = {{"lovGroup",lovGroup}, {"uuidDealer",uuidDealer},{"constraint1",answerCFilter[0]},
								{"constraint2",answerCFilter[1]},{"constraint3",answerCFilter[2]},{"constraint4",answerCFilter[3]},
								{"constraint5",answerCFilter[4]}};
						listLov = this.getManagerDAO().selectAllNative("task.neworder.getLovBranch05D", params, null);
					}
				}
			}
			for(int i=0;i<listLov.size();i++){
				Map map = (HashMap) listLov.get(i);
				Map<String,String> tmp = new HashMap<>();
				tmp.put("uuid", map.get("d0").toString());
				tmp.put("id",map.get("d1")!=null ? map.get("d1").toString():StringUtils.EMPTY);
				tmp.put("name", map.get("d2").toString());
				result.add(tmp);
			}
		
		return result;
	}

	@Transactional
	@Override
	public Map insert(String data, long uuidUser, String uuidForm, AmMsuser loginBean, String isSelfAssignment, String idHistCust, AuditContext callerId) {
		// 0 = no match, 1 = match, 2 = not found, 3 = DSR not passed
		int validateDukcapil = 0;
		
		TaskDukcapilBean taskDukcapilBean = this.validasiFormLead(data, uuidUser, true, callerId);		
		Map mapValidateDukcapil = this.validateDukcapil(taskDukcapilBean, "0", false, idHistCust, callerId);
		validateDukcapil = (int) mapValidateDukcapil.get("d0");
		taskDukcapilBean.setIsMatchDukcapil(String.valueOf(validateDukcapil));
		taskDukcapilBean.setIdHistCust(idHistCust);
		Map mapTask = createTaskLead(taskDukcapilBean, data, uuidForm, loginBean, isSelfAssignment, true, callerId);
		
		TrTaskH taskhOrder = (TrTaskH) mapTask.get("d0");
		
		if(StringUtils.isNotBlank(taskhOrder.getApplNo())) {
			taskDukcapilBean.setOrderNo(taskhOrder.getApplNo());
		}
		String dbName = StringUtils.EMPTY;
		if(validateDukcapil != 0) {
			
			this.getManagerDAO().fetch(taskhOrder.getMsForm());
			if (StringUtils.contains(taskhOrder.getMsForm().getFormName().toUpperCase(), GlobalVal.FORM_INSTANT_APPROVAL)) {
				InstantApprovalResponse iar = intFormLogic.submitInstantApproval(callerId, String.valueOf(taskhOrder.getUuidTaskH()), "1");
				if(1!=iar.getStatus().getCode()) {
					AmMssubsystem subsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
					Object[][] param = { 
										 {Restrictions.eq("amMssubsystem.uuidMsSubsystem", subsystem.getUuidMsSubsystem())},
										 {Restrictions.eq("statusCode", "F")}
											};
					MsStatustask msStatus  = getManagerDAO().selectOne(MsStatustask.class, param);					
					taskhOrder.setMsStatustask(msStatus);
					taskhOrder.setDtmUpd(new Date());
					taskhOrder.setUsrUpd("SYSTEM");
					this.getManagerDAO().update(taskhOrder);					
					
					//INSERT INTO CHECK HISTORY
					String codeProcess = GlobalVal.CODE_PROCESS_FAILED;
					insertTaskHistory(callerId, msStatus, taskhOrder, taskhOrder.getNotes(), 
							codeProcess, loginBean.getFullName(), loginBean, null);
					
					String prodCode = taskDukcapilBean.getProdCode();
					Object[][] params = { {Restrictions.eq("productCategoryCode", prodCode)} };
					TblProductCategory tpc =  this.getManagerDAO().selectOne(TblProductCategory.class, params);
					List<TrTaskH> listTaskSvy = createTaskSurvey(taskhOrder, tpc.getProductCategoryCode(), callerId);
					for (int i = 0; i < listTaskSvy.size(); i++) {
						TrTaskH taskH = listTaskSvy.get(i);
						taskServiceLogic.insertDefaultAnswerQuestionPilotingCae(taskH, false, callerId);
					}
				}
			}
			if(validateDukcapil == 2) {
				dbName =  SpringPropertiesUtils.getProperty(GlobalKey.DATABASE_CONFINS_MATCH) + ".dbo.STG_POOLING_CUST_X";
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "Result: Not Found Data Dukcapil") );
				taskhOrder.setIsMatchDukcapil("0");
				this.getManagerDAO().update(taskhOrder);
				this.insertTaskDukcapil(taskDukcapilBean, "2", dbName, callerId);
			} else {
				dbName =  SpringPropertiesUtils.getProperty(GlobalKey.DATABASE_CONFINS_MATCH) + ".dbo.STG_POOLING_CUST_X";
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "Result: Match Data Dukcapil") );
				taskhOrder.setIsMatchDukcapil("1");
				this.getManagerDAO().update(taskhOrder);
				this.insertTaskDukcapil(taskDukcapilBean, "1", dbName, callerId);
			}
						
		}else {
			dbName =  SpringPropertiesUtils.getProperty(GlobalKey.DATABASE_CONFINS_NOMATCH) + ".dbo.TBL_ORDER_NOMATCH_DUKCAPIL_X";
			taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "Result: No Match Data Dukcapil") );
			taskhOrder.setIsMatchDukcapil("0");
			AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
			Object[][] params2 = {
					{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_FAILED_ASSIGNMENT) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidMsSub.getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
			taskhOrder.setMsStatustask(msStatustask);
			taskhOrder.setDtmUpd(new Date());
			taskhOrder.setUsrUpd("SYSTEM");
	
			//INSERT INTO CHECK HISTORY
			String codeProcess = GlobalVal.CODE_PROCESS_FAILED;
			insertTaskHistory(callerId, msStatustask, taskhOrder, taskhOrder.getNotes(), 
					codeProcess, loginBean.getFullName(), loginBean, null);
			this.getManagerDAO().update(taskhOrder);
			this.insertTaskDukcapil(taskDukcapilBean, "0", dbName, callerId);
		}
		return mapValidateDukcapil;
	}
	
	@Transactional
	private Map createTaskLead(TaskDukcapilBean dukcapilBean, String data, String uuidForm, AmMsuser loginBean, String isSelfAssignment, boolean lastQuestionGroup, AuditContext callerId) {
		Map mapResult = new HashMap<>();
		MsForm form = commonLogic.retrieveMsFormByUuid(new Long(uuidForm), callerId);
		Object params[][] = {{"uuidForm", new Long(uuidForm)}};
		Integer formVersion = (Integer)this.getManagerDAO().selectOneNativeString("select top 1 form_version from MS_FORMHISTORY WITH(NOLOCK) WHERE UUID_FORM = :uuidForm ORDER BY DTM_CRT DESC", params);
		String orderNo = this.commonLogic.retrieveNewTaskId(GlobalVal.SEQUENCE_ORDER_NO_CODE);
		MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(
				Long.parseLong(uuidForm), formVersion, callerId);
		
		//create task header
		Date dateNow = new Date();
		TrTaskH taskHOrder = new TrTaskH();
		taskHOrder.setAmMsuser(loginBean);
		taskHOrder.setCustomerName(dukcapilBean.getCustName());
		taskHOrder.setCustomerAddress(dukcapilBean.getLegalAddr());
		taskHOrder.setCustomerPhone(dukcapilBean.getMobilePhnNo());
		taskHOrder.setUsrCrt(callerId.getCallerId());
		taskHOrder.setDtmCrt(dateNow);
		taskHOrder.setMsForm(form);
		taskHOrder.setFormVersion(Integer.valueOf(formVersion));
		taskHOrder.setMsBranch(loginBean.getMsBranch());
		taskHOrder.setApplNo(orderNo);
		taskHOrder.setFlagSource(GlobalVal.SUBSYSTEM_MS);
		taskHOrder.setSubmitDate(dateNow);
		if(StringUtils.isNotBlank(dukcapilBean.getDealerCode())) {
			dukcapilBean.setDealerCode((dukcapilBean.getDealerCode().split("\\^"))[0]);
		}
		taskHOrder.setDealerCode(dukcapilBean.getDealerCode());
		taskHOrder.setSubzipcode(dukcapilBean.getSubzipcode());
		
		if(StringUtils.isNotBlank(isSelfAssignment)){
			taskHOrder.setIsSelfassignment(isSelfAssignment);
		}
		
		//Priority
		MsPriority normalPriority = this.commonLogic.retrieveMsPriority(GlobalVal.TAKS_PRIORTY_NORMAL, true, callerId);
		taskHOrder.setMsPriority(normalPriority);
		
		taskHOrder.setIsDraft("0");
		taskHOrder.setReadDate(null);
		taskHOrder.setIsAppNotified("0");
		
		//Update CIF DUKCAPIL taskH, update orderNo history
		if(StringUtils.isNotBlank(dukcapilBean.getIdHistCust())){
		CheckCustomerHistory history = this.getManagerDAO().selectOne(CheckCustomerHistory.class, Long.valueOf(dukcapilBean.getIdHistCust()));
		if (null != history) {
			taskHOrder.setCifDukcapil("1".equals(history.getIsWise()) ? history.getCustomerNo() : history.getOrderNoTemp());
			history.setOrderNo(orderNo);
			this.getManagerDAO().update(history);
		}
		}
		
		this.getManagerDAO().insert(taskHOrder);
		
		taskHOrder.setTaskId(String.valueOf(taskHOrder.getUuidTaskH()));
		this.saveGroupTaskSurvey(taskHOrder, callerId); //insert ms_grouptask
		this.getManagerDAO().update(taskHOrder);
		
		//set default answer
		String [] answers = data.split("#");
		SubmitTaskDBean[] taskDBeanList = generateSubmitTaskDBean(data, callerId, true);
		if(taskDBeanList != null) {
			Map<Integer, MsQuestion> msQuestions = this.getAllMsQuestion(taskDBeanList, formHist);
			
			ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(callerId);
	        Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
	                ? this.imageStorageLogic.retrieveGsImageFileSystemPath(callerId) : null;
			
            SaveTaskDResult saveResult = null;
            boolean saveAsJson = PropertiesHelper.isTaskDJson();
    		if (saveAsJson) {
    			saveResult = this.saveTaskDIntoJson(taskDBeanList, taskHOrder, loginBean, false, null, true, msQuestions, isl, imagePath, callerId);
    		}
    		else {			
    			//2016-09-16 SUM - select all taskD first instead of select one for all answers
    			Map<Long, TrTaskD> listTaskD = this.listTaskDAsMap(String.valueOf(taskHOrder.getUuidTaskH()));
    		    saveResult = this.saveTaskDIntoRow(taskDBeanList, taskHOrder, loginBean, false, null, true, msQuestions, listTaskD, isl, imagePath, callerId);
    		}
		}
		//commit wf 
		AmMssubsystem subsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		long uuidProcess = this.getUuidProcess(taskHOrder, subsystem);
		MsStatustask msStatustask = commitWfAndUpdateTaskH(taskHOrder, uuidProcess, subsystem, 0);
		//get status from workflow
		Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_UPLOADING) } };
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
		taskHOrder.setMsStatusmobile(msm);
		
		if(lastQuestionGroup) {
			if (GlobalVal.COLLECTION_STATUS_TASK_UPLOADING.equals(msStatustask.getStatusCode())) {
				taskHOrder.setMsStatusmobile(msm);
				msStatustask = commitWfAndUpdateTaskH(taskHOrder, uuidProcess, subsystem, 0);
			}	
		}

		String notes = StringUtils.EMPTY;
		String codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
		if(lastQuestionGroup) {
			notes = "Task New Lead has been submitted.";
		}else {
			notes = "Task New Lead Page 1 has been submitted.";
		}
		
		if(dukcapilBean.getIsMatchDukcapil().equals("0")) {
			taskHOrder.setNotes( appendNotes(taskHOrder.getNotes(), "IA failed - Dukcapil Not Match, Order stop / drop") );
			taskHOrder.setIsMatchDukcapil("0");
			AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
			Object[][] paramsReject = {
					{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_REJECTED) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidMsSub.getUuidMsSubsystem()) } };
			
			MsStatustask msStatustaskReject = this.getManagerDAO().selectOne(MsStatustask.class, paramsReject);
			taskHOrder.setMsStatustask(msStatustaskReject);
			taskHOrder.setDtmUpd(new Date());
			taskHOrder.setUsrUpd("SYSTEM");
			this.getManagerDAO().update(taskHOrder);

			notes = "IA failed - Dukcapil Not Match, Order stop / drop";
			codeProcess = GlobalVal.CODE_PROCESS_REJECTED;
		}
		
		//INSERT INTO CHECK HISTORY
		insertTaskHistory(callerId, msStatustask, taskHOrder, notes, 
				codeProcess, loginBean.getFullName(), loginBean, null);
		mapResult.put("d0", taskHOrder);
		mapResult.put("d1", taskDBeanList);
		return mapResult;
	}
	
	private SubmitTaskDBean[] generateSubmitTaskDBean (String data, AuditContext callerId, boolean flagLastQuestionGroup) {
		String[] answers = data.split("#");
		SubmitTaskDBean[] taskDBean = new SubmitTaskDBean[answers.length];
		for(int i=0;i<answers.length;i++){
			taskDBean[i] = new SubmitTaskDBean();
			String[] tempData = answers[i].split("~");
			String answer = tempData[4].equalsIgnoreCase("-1")?StringUtils.EMPTY:tempData[4];
			taskDBean[i].setUuid_task_d(this.getManagerDAO().getUUID());
			taskDBean[i].setQuestion_id(tempData[0]);
			taskDBean[i].setQuestion_group_id(tempData[3]);
			if (tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO)||
				tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION)||
				tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN)||
				tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION)||
				tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION)
				){
				if(!answer.equalsIgnoreCase(StringUtils.EMPTY)){
					Object[][] msLovParams = {{"uuidLov",Long.valueOf(answer)}};
					String code = (String)this.getManagerDAO().selectOneNative("task.neworder.getMsLov", msLovParams); // so slowly
					taskDBean[i].setOption_answer_id(answer);
					taskDBean[i].setLov(code);
					
					MsQuestion msQuestion = this.commonLogic.retrieveQuestionByUuid(Long.parseLong(tempData[0]), callerId);
					MsOrdertag orderTag = msQuestion.getMsOrdertag();
					if (orderTag != null && GlobalVal.ORDER_TAG_JOB_MH.equals(orderTag.getTagName())) {
						taskDBean[i].setOption_answer_id(code);
					}
				}
			}
			else if(tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DATE)){
				SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
				SimpleDateFormat df2 = new SimpleDateFormat("ddMMyyyyHHmmss");
				try {
					Date date = df.parse(answer);
					taskDBean[i].setText_answer(df2.format(date));
				} 
				catch (ParseException e) {
					LOG.error(e.getMessage(), e);
				}
			}
			else if(tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_IMAGE)
					|| tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_IMAGE_WITH_GEODATA)
					|| tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_IMAGE_WITH_GPS)
					|| tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DRAWING)
					|| tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_IMAGE_CAPTURE)
					|| tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_OCR)
					|| tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_IMAGE_BIOMETRIK)){
				if(!answer.equalsIgnoreCase(StringUtils.EMPTY)){
					String image = StringUtils.EMPTY;
					String[] imageTemp = answer.split("\\|");
					if (imageTemp.length!=1){
						image = imageTemp[3].split(",")[1];
						taskDBean[i].setLatitude(imageTemp[0]);
						taskDBean[i].setLongitude(imageTemp[1]);
						taskDBean[i].setAccuracy(imageTemp[2]);
					}
					else {
						image = imageTemp[0].split(",")[1];
					}
					taskDBean[i].setImage(image);
				}
			}
			else if (tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_LOCATION)) {
				if (StringUtils.isEmpty(answer)) {
					continue;
				}
				
				final String defaultAccuracy = "10"; 
				String[] points = StringUtils.split(answer, ",", 2);
				taskDBean[i].setLatitude(points[0]);
				taskDBean[i].setLongitude(points[1]);
				taskDBean[i].setAccuracy(defaultAccuracy);
				taskDBean[i].setMcc("0");
				taskDBean[i].setMnc("0");
				taskDBean[i].setLac("0");
				taskDBean[i].setCid("0");
				taskDBean[i].setText_answer(String.format("Coord : %1$s, %2$s Accuracy : %3$s m",
						new Object[]{points[0], points[1], defaultAccuracy}));
			}else if (GlobalVal.ANSWER_TYPE_LU_OFFLINE.equals(tempData[1]) || 
					GlobalVal.ANSWER_TYPE_LU_OFFLINE_WITH_SUGGESTION.equals(tempData[1])) {
				if (StringUtils.isEmpty(answer)) {
					continue;
				}
				String answerLookup[] = answer.split("\\^");
				taskDBean[i].setLov(answerLookup[0]);
				taskDBean[i].setUuid_lookup(answerLookup[1]);
				taskDBean[i].setText_answer(answerLookup[2]);
			}
			else {
				taskDBean[i].setText_answer(answer);
			}
			taskDBean[i].setQuestion_label(tempData[2]);
			if(flagLastQuestionGroup) {
				taskDBean[i].setIs_final("1");	
			}
				
		}
		return taskDBean;
	}
	
	@Override
	public List<Map<String, Object>> copyValue(List<Map<String, String>> list, long uuidForm, long uuidQuestion, 
			long uuidUser, int seqQuest, AuditContext callerId, String isOpenLookup) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		MsQuestion msQuestion = this.getManagerDAO().selectOne( MsQuestion.class, uuidQuestion);
		Object [][] param = {{"refId", msQuestion.getRefId() },
							 { "uuidForm", uuidForm}};
		List <Map<String, Object>> listQuest = this.getManagerDAO().selectAllNative("task.neworder.getQuestRelevantCopy", param, null);
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, uuidUser);
		String convertedExpression = "";
		SimpleDateFormat format = new SimpleDateFormat();
			for (int i=0; i<list.size(); i++){
					Map map = list.get(i);
				if(Integer.parseInt(map.get("id").toString()) >= seqQuest){

					Object [][] prm = {{"refId", (String) map.get("refid")},
										{"uuidForm", uuidForm }};
					String script = (String) this.getManagerDAO().selectOneNative ("task.neworder.getScriptCopyValue", prm);
					String refIdSumberValue = null;
					
					convertedExpression = script;
					if (convertedExpression == null || convertedExpression.length() == 0) {
						continue;
					} 
					else {
						if(GlobalVal.ANSWER_TYPE_ADDRESS_MULTILINE.equalsIgnoreCase(map.get("answerType").toString())) {
							String oneConvertedExpression[] = convertedExpression.split(";");
							if(0!=oneConvertedExpression.length) {
								int indexOpenBrace = 0;
								int indexCloseBrace = 0;
								String tempIndetifier = "";
								Map<String, Object> tempCodeAnswerType = new HashMap<>();
								tempCodeAnswerType.put("codeAnswerType", map.get("answerType").toString());
								tempCodeAnswerType.put("ansType", map.get("answerType"));
								tempCodeAnswerType.put("idGroup", map.get("idGroup"));
								tempCodeAnswerType.put("id", map.get("id"));
								listResult.add(tempCodeAnswerType);
								for(int j = 0; j<oneConvertedExpression.length; j++) {
									if(oneConvertedExpression[j].toUpperCase().contains("COPYQUESTION".toUpperCase())) {
										oneConvertedExpression[j] = oneConvertedExpression[j].toUpperCase().replaceAll("(?i)COPYQUESTION", "");
										oneConvertedExpression[j] = oneConvertedExpression[j].replace("(", "");
										oneConvertedExpression[j] = oneConvertedExpression[j].replace(")", "");
										String temp[] = oneConvertedExpression[j].split(","); 
										for(int idx = 0; idx<temp.length; idx++) {
											indexOpenBrace = temp[idx].indexOf('{');
									        indexCloseBrace = temp[idx].indexOf('}');
									        tempIndetifier = temp[idx].substring(indexOpenBrace+1, indexCloseBrace);
									        Map<String, Object> tempMap = new HashMap<>();
									        tempMap.put("ansType", map.get("answerType"));
									        tempMap.put("result", tempIndetifier);
									        tempMap.put("idGroup", map.get("idGroup"));
									        tempMap.put("id", map.get("id"));
									        listResult.add(tempMap);	
										}
									}else if(oneConvertedExpression[j].toUpperCase().contains("COPY".toUpperCase())) {
										oneConvertedExpression[j] = oneConvertedExpression[j].toUpperCase().replaceAll("(?i)COPY", "");
										oneConvertedExpression[j] = oneConvertedExpression[j].replace("(", "");
										oneConvertedExpression[j] = oneConvertedExpression[j].replace(")", "");
										boolean needReplacing = true;
										while (needReplacing) {
											int idxOfOpenBrace = oneConvertedExpression[j].indexOf('{');
											if (idxOfOpenBrace != -1) {
												int idxOfCloseBrace = oneConvertedExpression[j].indexOf('}');
												String identifier = oneConvertedExpression[j].substring(idxOfOpenBrace + 1, idxOfCloseBrace);
												int idxOfOpenAbs = identifier.indexOf("$");
												String flatAnswer = "";
												if (idxOfOpenAbs != -1) { // value yang bukan reff_id
													String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
													if (finalIdentifier.equals("LOGIN_ID")) {
														if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
															flatAnswer = user.getLoginId();
														}
													} 
													else if (finalIdentifier.equals("UUID_USER")) {
														if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
															flatAnswer = String.valueOf(uuidUser);
														}
													}
													else if (finalIdentifier.equals("UUID_BRANCH")) {
														if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
															flatAnswer = String.valueOf(user.getMsBranch().getUuidBranch());
														}
													} 
													else if (finalIdentifier.equals("BRANCH_ID")) {
														if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
															flatAnswer = user.getMsBranch().getBranchCode();
														}
													} 
													else if (finalIdentifier.equals("BRANCH_Name")) {
														if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
															flatAnswer = user.getMsBranch().getBranchName();
														}
													}
													else if (finalIdentifier.equals("UUID_DEALER")) {
														if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
															flatAnswer = String.valueOf(user.getMsDealer().getUuidDealer());
														}
													} 
													else if (finalIdentifier.equals("DEALER_NAME")) {
														if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
															flatAnswer = user.getMsDealer().getDealerName();
														}
													} 
													else if (finalIdentifier.equals("FLAG_JOB")) {
														if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
															flatAnswer = user.getMsJob().getJobCode();
														}
													} 
													else if (finalIdentifier.equals("THISYEAR")) {
														if(map.get("answerType").toString().equals("003")|| map.get("answerType").toString().equals("004")||
																map.get("answerType").toString().equals("005")){
															Calendar cal = Calendar.getInstance(TimeZone.getDefault());
															flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
														}
													} 
													else if (finalIdentifier.equals("NOWADAYS")) {
														Calendar cal = Calendar.getInstance(TimeZone.getDefault());
														if(map.get("answerType").toString().equals("013")){
															format = new SimpleDateFormat("yyyy-MM-dd");
															flatAnswer = format.format(cal.getTime());
														} 
														else if(map.get("answerType").toString().equals("015")){
															format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
															flatAnswer = format.format(cal.getTime());
														}
													}
												} 
												else {
													if (!listQuest.isEmpty()){
														for (int idxJ=0; idxJ<i; idxJ++){
															Map map2 = list.get(idxJ);
															if (map2.get("refid").equals(identifier)){
																flatAnswer = map2.get("value").toString();
																flatAnswer = flatAnswer.replaceAll(",", "@@@");
																refIdSumberValue = (String) map2.get("refid"); //
															}
														}
													}
												}
												if (flatAnswer != null && flatAnswer.length() > 0) {
													oneConvertedExpression[j] = oneConvertedExpression[j].replace("{" + identifier + "}", flatAnswer);
												} 
												else {
													needReplacing = false;
												}
											} 
											else {
												needReplacing = false;
											}
										}
										if(oneConvertedExpression[j].indexOf('{')!=-1 && listQuest.isEmpty() ) {
											continue;
										}
										String arg [] = oneConvertedExpression[j].split(",");
										for(int y = 0; y<arg.length; y++) {
											arg[y] = arg[y].replaceAll("@@@", ",");
										}
										boolean condition=false;
										String kondisi = convert(list, arg[0]);
										ScriptEngineManager mgr = new ScriptEngineManager();
										ScriptEngine engine = mgr.getEngineByName("JavaScript");
										try {
											condition = (boolean) engine.eval(kondisi);
										} catch (ScriptException e) {
											LOG.error("Exception on copyvalue evaluation", e);
										}
										if(condition) {
											Map<String, Object> mapResult = new HashMap<>();
											mapResult.put("id", map.get("id"));
											mapResult.put("result", arg[1]);
											mapResult.put("ansType", map.get("answerType"));
											mapResult.put("srcRefId", refIdSumberValue);
											mapResult.put("idGroup", map.get("idGroup"));
											mapResult.put("isCopyAddress", "1");
											listResult.add(mapResult);
										}
										else{
											if(arg.length>2){
												Map<String, Object> mapResult = new HashMap<>();
												mapResult.put("id", map.get("id"));
												mapResult.put("result", arg[2]);
												mapResult.put("ansType", map.get("answerType"));
												mapResult.put("srcRefId", refIdSumberValue);
												mapResult.put("idGroup", map.get("idGroup"));
												mapResult.put("isCopyAddress", "0");
												listResult.add(mapResult);
											}else {
												Map<String, Object> mapResult = new HashMap<>();
												mapResult.put("id", map.get("id"));
												mapResult.put("ansType", map.get("answerType"));
												mapResult.put("idGroup", map.get("idGroup"));
												mapResult.put("isCopyAddress", "0");
												listResult.add(mapResult);
											}
										}
									}
								}   
								if("1".equals(isOpenLookup)) {
									 return listResult;
								}
						      }
						} else {
						boolean needReplacing = true;
						while (needReplacing) {
							int idxOfOpenBrace = convertedExpression.indexOf('{');
							if (idxOfOpenBrace != -1) {
								int idxOfCloseBrace = convertedExpression.indexOf('}');
								String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
								int idxOfOpenAbs = identifier.indexOf("$");
								String flatAnswer = "";
								if (idxOfOpenAbs != -1) { // value yang bukan reff_id
									String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
									if (finalIdentifier.equals("LOGIN_ID")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getLoginId();
										}
									} 
									else if (finalIdentifier.equals("UUID_USER")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = String.valueOf(uuidUser);
										}
									}
									else if (finalIdentifier.equals("UUID_BRANCH")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = String.valueOf(user.getMsBranch().getUuidBranch());
										}
									} 
									else if (finalIdentifier.equals("BRANCH_ID")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getMsBranch().getBranchCode();
										}
									} 
									else if (finalIdentifier.equals("BRANCH_Name")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getMsBranch().getBranchName();
										}
									}
									else if (finalIdentifier.equals("UUID_DEALER")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = String.valueOf(user.getMsDealer().getUuidDealer());
										}
									} 
									else if (finalIdentifier.equals("DEALER_NAME")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getMsDealer().getDealerName();
										}
									} 
									else if (finalIdentifier.equals("FLAG_JOB")) {
										if(map.get("answerType").toString().equals("001")|| map.get("answerType").toString().equals("002")){
											flatAnswer = user.getMsJob().getJobCode();
										}
									} 
									else if (finalIdentifier.equals("THISYEAR")) {
										if(map.get("answerType").toString().equals("003")|| map.get("answerType").toString().equals("004")||
												map.get("answerType").toString().equals("005")){
											Calendar cal = Calendar.getInstance(TimeZone.getDefault());
											flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
										}
									} 
									else if (finalIdentifier.equals("NOWADAYS")) {
										Calendar cal = Calendar.getInstance(TimeZone.getDefault());
										if(map.get("answerType").toString().equals("013")){
											format = new SimpleDateFormat("yyyy-MM-dd");
											flatAnswer = format.format(cal.getTime());
										} 
										else if(map.get("answerType").toString().equals("015")){
											format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
											flatAnswer = format.format(cal.getTime());
										}
									}
								} 
								else {
									if (!listQuest.isEmpty()){
										for (int j=0; j<i; j++){
											Map map2 = list.get(j);
											if (map2.get("refid").equals(identifier)){
												flatAnswer = map2.get("value").toString();
												refIdSumberValue = (String) map2.get("refid"); //
											}
										}
									}
								}
								if (flatAnswer != null && flatAnswer.length() > 0) {
									convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
								} 
								else {
									needReplacing = false;
								}
							} 
							else {
								needReplacing = false;
							}
						}
					}
					if (!convertedExpression.contains("{")){
						if(!convertedExpression.toUpperCase().contains("COPY".toUpperCase())){
							Map<String, Object> mapResult = new HashMap<>();
							mapResult.put("id", map.get("id"));
							mapResult.put("result", convertedExpression);
							mapResult.put("ansType", map.get("answerType"));
							mapResult.put("srcRefId", refIdSumberValue);
							mapResult.put("idGroup", map.get("idGroup"));
							listResult.add(mapResult);
						}
						else{
							convertedExpression = convertedExpression.replaceAll("(?i)COPY", "");
							convertedExpression = convertedExpression.replace("(", "");
							convertedExpression = convertedExpression.replace(")", "");
							String arg [] = convertedExpression.split(",");
							boolean condition=false;
							String kondisi = convert(list, arg[0]);
							ScriptEngineManager mgr = new ScriptEngineManager();
							ScriptEngine engine = mgr.getEngineByName("JavaScript");
							try {
								condition = (boolean) engine.eval(kondisi);
							} catch (ScriptException e) {
								LOG.error("Exception on copyvalue evaluation", e);
							}
							if(condition) {
								Map<String, Object> mapResult = new HashMap<>();
								mapResult.put("id", map.get("id"));
								mapResult.put("result", arg[1]);
								mapResult.put("ansType", map.get("answerType"));
								mapResult.put("srcRefId", refIdSumberValue);
								mapResult.put("idGroup", map.get("idGroup"));
								listResult.add(mapResult);
							}
							else{
								if(arg.length>2){
									Map<String, Object> mapResult = new HashMap<>();
									mapResult.put("id", map.get("id"));
									mapResult.put("result", arg[2]);
									mapResult.put("ansType", map.get("answerType"));
									mapResult.put("srcRefId", refIdSumberValue);
									mapResult.put("idGroup", map.get("idGroup"));
									listResult.add(mapResult);
								}
							}
						}
					}
				}
			}
		}
		return listResult;
	}
	
	public boolean cekArgumen (String convertedExpression, String answerType){
		boolean result = false;
		SimpleDateFormat format = new SimpleDateFormat();
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		
		if(convertedExpression.contains("dateDifference".toUpperCase())){
			convertedExpression = convertedExpression.replace("dateDifference".toUpperCase(), "");
			convertedExpression = convertedExpression.replace(")", "");
			convertedExpression = convertedExpression.replace("(", "");
			String arg [] = convertedExpression.split(",");
			long time1 = 0, time2 = 0;
			if(answerType.equals("013")){
				format = new SimpleDateFormat("yyyy-MM-dd");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("014")){
				format = new SimpleDateFormat("yyyy-MM-dd");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("015")){
				format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			Period difference = new Period(time1, time2, PeriodType.yearMonthDayTime());
			if(arg[2].contains("DAY")){
				int day = difference.getDays();
				arg[2] = arg[2].replace("|DAY|", day+"");
			}
			else if(arg[2].contains("MONTH")){
				int month = difference.getMonths();
				arg[2] = arg[2].replace("|MONTH|", month+"");
			} 
			else if(arg[2].contains("YEAR")){
				int year = difference.getYears();
				arg[2] = arg[2].replace("|YEAR|", year+"");
			}
			else if(arg[2].contains("HOUR")){
				int hour = difference.getHours();
				arg[2] = arg[2].replace("|HOUR|", hour+"");
			}
			else if(arg[2].contains("MINUTE")){
				int minute = difference.getMinutes();
				arg[2] = arg[2].replace("|MINUTE|", minute+"");
			}
			else if(arg[2].contains("SECOND")){
				int second = difference.getYears();
				arg[2] = arg[2].replace("|SECOND|", second+"");
			}
			arg[2] = arg[2].contains("==")?arg[2].replace("==", "<="):arg[2];
			try {
				result = (boolean) engine.eval(arg[2]);
			} 
			catch (ScriptException e) {
				LOG.error("Exception on date evaluation", e);
			}
		}
		else if (convertedExpression.contains("<=") || convertedExpression.contains("<") || convertedExpression.contains(">=") || convertedExpression.contains(">") || convertedExpression.contains("!=") || convertedExpression.contains("==")){
			convertedExpression = convertedExpression.replace("(", "");
			convertedExpression = convertedExpression.replace(")", "");
			String [] arg = convertedExpression.split("<=|<|>=|>|!=|==");
			String cek = "";
			String delimeter="";
			if (convertedExpression.contains("<=")){
				delimeter = "<=";
			}
			else if (convertedExpression.contains("<")){
				delimeter = "<";
			}
			else if (convertedExpression.contains(">=")){
				delimeter = ">=";
			}
			else if (convertedExpression.contains(">")){
				delimeter = ">";
			}
			else if (convertedExpression.contains("!=")){
				delimeter = "!=";
			}
			else if (convertedExpression.contains("==")){
				delimeter = "==";
			}
			if(answerType.equals("013")){
				long time1, time2;
				format = new SimpleDateFormat("yyyy-MM-dd");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 +delimeter + time2;
				} catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("014")){
				long time1, time2;
				format = new SimpleDateFormat("yyyy-MM-dd");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 + delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("015")){
				long time1, time2;
				format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 + delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if(answerType.equals("001") || answerType.equals("002")){
				arg[0] = arg[0].replace(" ", "");
				arg[1] = arg[1].replace(" ", "");
				if (delimeter.equals("==")){
					boolean a = (arg[0].equalsIgnoreCase(arg[1])?true : false);
					cek = a +"" ;
				}
				else if (delimeter.equals("!=")){
					boolean a = (!arg[0].equalsIgnoreCase(arg[1])?true : false);
					cek = a + "";
				}
				else{
					cek = convertedExpression;
				}
			}
			else{
				cek = convertedExpression;
			}
			try {
				result = (boolean) engine.eval(cek);
			} 
			catch (ScriptException e) {
				LOG.error("Exception on date evaluation", e);
			}
		}
		return result;
	}
	
	@Override
	public List<Map<String, Object>> validation (List<Map<String, String>> list, long uuidForm, long uuidQuestion, AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		SimpleDateFormat format = new SimpleDateFormat();
		SimpleDateFormat formatDate = new SimpleDateFormat();
		String convertedExpression="";
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		boolean finalResult = false;

		for (int k = 0; k<list.size(); k++){
			Map map = list.get(k) ;

			Object[][] paramsRel = {{ "refId", (String) map.get("refid") },
					{ "uuidForm", uuidForm} };
			Object [] obj = (Object []) this.getManagerDAO().selectOneNative("task.neworder.getScriptValidation", paramsRel);
			
			String script = "";
			String msg = "";
			
			if(obj != null) {
			script = obj[0].toString();
			msg = obj[1].toString();
			if (StringUtils.isNotBlank(script)){
				convertedExpression = script;
			}
			else{
				continue;
			}}
			
			String answerType = (String) map.get("answerType");
			String answerString = (String) map.get("value");
			String id = (String) map.get("id");
			String idGroup = (String) map.get("idGroup");
			
			if (convertedExpression == null || convertedExpression.length() == 0 
					|| answerString.length() == 0) {
				continue;
			} 
			else {
				boolean needReplacing = true;
				while (needReplacing) {
					int idxOfOpenBrace = convertedExpression.indexOf('{');
					if (idxOfOpenBrace != -1) {
						int idxOfCloseBrace = convertedExpression.indexOf('}');
						String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
						int idxOfOpenAbs = identifier.indexOf("$");
						String flatAnswer = "";
						if (idxOfOpenAbs != -1) {
							String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
							if (finalIdentifier.equals("ANSWER")) {
								try {
									if (answerType.equals("014")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										formatDate = new SimpleDateFormat("yyyyMMdd");
										Date date2 = null;
										try {
											date2 = format.parse(answerString);
										} 
										catch (Exception e) {
											date2 = formatDate.parse(answerString);
										}
										Calendar now = Calendar.getInstance(TimeZone.getDefault());
										Calendar date = Calendar.getInstance(TimeZone.getDefault());
										date.setTime(date2);
										date.set(Calendar.YEAR, now.get(Calendar.YEAR));
										date.set(Calendar.MONTH, now.get(Calendar.MONTH));
										date.set(Calendar.DAY_OF_MONTH,now.get(Calendar.DAY_OF_MONTH));
										flatAnswer = format.format(date.getTime());
									} 
									else if (answerType.equals("015")) {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										formatDate = new SimpleDateFormat("ddMMyyyyHHmmss");
										Date date2 = null;
										try {
											date2 = format.parse(answerString);
										} 
										catch (Exception e) {
											date2 = formatDate.parse(answerString);
										}
										Calendar date = Calendar.getInstance(TimeZone.getDefault());
										date.setTime(date2);
										flatAnswer = format.format(date.getTime());
									} 
									else if (answerType.equals("013")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										formatDate = new SimpleDateFormat("ddMMyyyy");
										Date date2 = null;
										try {
											date2 = format.parse(answerString);
										} 
										catch (Exception e) {
											date2 = formatDate.parse(answerString);
										}
										Calendar date = Calendar.getInstance(TimeZone.getDefault());
										date.setTime(date2);
										flatAnswer = format.format(date.getTime());
									} 
									else {
										flatAnswer = answerString;
									}
								} catch (Exception e) {
								}
							}
							else if (finalIdentifier.equals("THISYEAR")) {
								Calendar cal = Calendar.getInstance(TimeZone.getDefault());
								flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
							} 
							else if (finalIdentifier.equals("NOWADAYS")) {
								Calendar cal = Calendar.getInstance(TimeZone.getDefault());

									if (answerType.equals("014")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										flatAnswer = format.format(cal.getTime());
									} 
									else if (answerType.equals("015")) {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										flatAnswer = format.format(cal.getTime());
									} 
									else if (answerType.equals("013")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										flatAnswer = format.format(cal.getTime());
									}
									else {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										flatAnswer = format.format(cal.getTime());
									}
							} 
							else if (finalIdentifier.equals("YESTERDAY")) {
								Calendar cal = Calendar.getInstance(TimeZone.getDefault());
								cal.add(Calendar.DATE, -1);
									if (answerType.equals("014")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										flatAnswer = format.format(cal.getTime());
									} 
									else if (answerType.equals("015")) {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										flatAnswer = format.format(cal.getTime());
									} 
									else if (answerType.equals("013")) {
										format = new SimpleDateFormat("yyyy-MM-dd");
										flatAnswer = format.format(cal.getTime());
									} 
									else {
										format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
										flatAnswer = format.format(cal.getTime());
									}
							}
						}
						else{
							for (int j=0; j<list.size()-1; j++){
								Map map2 = list.get(j);
								if (map2.get("refid").equals(identifier)){
									flatAnswer = map2.get("value").toString();
								}
							}
						}
						if (flatAnswer != null && flatAnswer.length() > 0) {
							convertedExpression = convertedExpression.replace(
									"{" + identifier + "}", flatAnswer);
						}
					} 
					else {
						needReplacing = false;
					}
				}
				String validasi = valid(convertedExpression, answerType);
				try {
					finalResult = (boolean) engine.eval(validasi);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on validation evaluation", e);
				}	
			}
			Map<String, Object> result = new HashMap<>();
			result.put("id", id);
			result.put("validasi", finalResult);
			result.put("msg", msg);
			result.put("idGroup", idGroup);
			listResult.add(result);	
		}
		return listResult;
	}

	@Override
	public List<Map<String, Object>> relevant(List<Map<String, String>> list, long uuidForm,  AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		String convertedExpression = "";
		for (int k = 0; k<list.size(); k++){
			
			Map map = list.get(k) ;
			Object[][] params = {{ "refId", (String) map.get("refid") },
								 {"uuidForm", uuidForm}};
			String script = (String) this.getManagerDAO().selectOneNative("task.neworder.getRelevantScript", params);
			
			if (script == null || script.length() == 0) {
				map.put("isRel", true);
				listResult.add(map);
				continue;
			}
			else {
				boolean res = false;
				ScriptEngineManager mgr = new ScriptEngineManager();
				ScriptEngine engine = mgr.getEngineByName("JavaScript");
				convertedExpression = this.convert(list, script);
				try {
					res = (boolean) engine.eval(convertedExpression);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on relevant evaluation", e);
				}
				map.put("isRel", res);
				listResult.add(map);
			}
		}
		return listResult;
	}
	
	public String valid (String script, String type){
		String convertedExpression = script;
		String [] arg = convertedExpression.toUpperCase().split("(&&)|( AND )|( OR )|(\\|\\|)");
		boolean hasil = false;
		for	(int i =0; i<arg.length; i++){
			String finalscript = arg[i].replace("(", "");
			finalscript = finalscript.replace(")","");
			hasil = cekArgumen(finalscript, type);
			convertedExpression = convertedExpression.toUpperCase().replace(arg[i], hasil+"");
		}
		return convertedExpression;
	}
	
	public String convert (List<Map<String, String>> list, String script){
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		boolean result = false;
		String convertedExpression = script;
		boolean needReplacing = true;
		String flatAnswer = "";
		while (needReplacing) {
			int idxOfOpenBrace = convertedExpression.indexOf('{');
			if (idxOfOpenBrace != -1) {
				int idxOfCloseBrace = convertedExpression.indexOf('}');
				String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
				for (int j=0; j<list.size(); j++){
					Map map2 = list.get(j);
					if (map2.get("refid").equals(identifier)){
						if(map2.get("value")!=null) {
							String [] split = map2.get("value").toString().split("\\^");
							flatAnswer = split[0];
						}
					}
				}
				if (flatAnswer != null && flatAnswer.length() > 0) {
					convertedExpression = convertedExpression.replace(
							"{" + identifier + "}", flatAnswer);
				}
				else if(flatAnswer != null && flatAnswer.length() == 0){
					convertedExpression = convertedExpression.replace(
							"{" + identifier + "}", "-");
				}
			}
			else {
				needReplacing = false;
			}
		}
		String [] arg = convertedExpression.toUpperCase().split("(&&)|( AND )|( OR )|(\\|\\|)");
		for	(int i =0; i<arg.length; i++){
			if (arg[i].contains("<=") || arg[i].contains("<") || arg[i].contains(">=") || arg[i].contains(">") || arg[i].contains("!=") || arg[i].contains("==")){
				arg[i] = arg[i].replace("(", "");
				arg[i] = arg[i].replace(")", "");
				arg[i] = arg[i].replace(" ", "");
				String [] arg2 = arg[i].split("<=|<|>=|>|!=|==");
				String cek = "";
				String delimeter="";
				if (arg[i].contains("<=")){
					delimeter = "<=";
				}
				else if (arg[i].contains("<")){
					delimeter = "<";
				}
				else if (arg[i].contains(">=")){
					delimeter = ">=";
				}
				else if (arg[i].contains(">")){
					delimeter = ">";
				}
				else if (arg[i].contains("!=")){
					delimeter = "!=";
				}
				else if (arg[i].contains("==")){
					delimeter = "==";
				}
				if (delimeter.equals("==")){
					cek = (String) (arg2[0].equalsIgnoreCase(arg2[1])?true + "" : false + "");
				}
				else if (delimeter.equals("!=")){
					cek = (String) (!arg2[0].equalsIgnoreCase(arg2[1])?true + "" : false + "");
				}
				else{
					cek = arg[i];
				}
				
				try {
					result = (boolean) engine.eval(cek);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on evaluation", e);
				}
			}
			convertedExpression = convertedExpression.toUpperCase().replace(arg[i], result+"");
		}
		convertedExpression = convertedExpression.toUpperCase().replace("OR", "||");
		convertedExpression = convertedExpression.toUpperCase().replace("AND", "&&");
		convertedExpression = convertedExpression.toLowerCase();
		return convertedExpression;
	}	

	@Override
	public CheckDukcapilResponse dukcapil(AuditContext auditContext, String isPilotingCAE, String nik, String name, String birthPlace, String birthDate, String userId, String appSource, String taskIdPolo, Integer isPreApproval) {
		CIFBean result = intFormLogic.revampDukcapil(auditContext, nik, name, birthPlace, birthDate, userId, appSource, null);
		CheckDukcapilResponse checkdukcapilResponse = intFormLogic.checkDukcapil(auditContext, isPilotingCAE, nik, name, birthPlace, birthDate, userId, appSource, null, result, taskIdPolo,isPreApproval);
		checkdukcapilResponse.setCif(result.getCustNo());
		return checkdukcapilResponse;
	}
	
	@Override
	public Map validateDukcapil(TaskDukcapilBean taskDukcapilBean, String isPilotingCae, boolean isReview, String idHistCust, AuditContext auditContext) {
		Map mapResult = new HashMap<>();
		CheckDukcapilResponse dukcapil = new CheckDukcapilResponse();
		Object[][] params = { { Restrictions.eq("uuidMsUser", new Long(auditContext.getCallerId())) } };
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, params);
		 
		AmGeneralsetting amGeneralSetting = this.commonLogic.retrieveGs(GlobalKey.GENERALSETTING_CHECK_DUKCAPIL, auditContext);
		if (!"1".equals(amGeneralSetting.getGsValue())) {
			mapResult.put("d0", 2);
			return mapResult;
		} else {
			String userId = amMsuser.getLoginId();
			String appSource = SpringPropertiesUtils.getProperty(GlobalKey.NC_CHECK_DUKCAPIL_APP_SOURCE);
			
			if(isReview) {
				dukcapil = this.dukcapil(auditContext, isPilotingCae, taskDukcapilBean.getNik(), taskDukcapilBean.getCustName(), taskDukcapilBean.getBirthPlace(), taskDukcapilBean.getBirthDt(), userId, appSource, null, null);
			} else {
				CheckCustomerHistory hist = this.getManagerDAO().selectOne(CheckCustomerHistory.class, Long.valueOf(idHistCust));
				dukcapil = gson.fromJson(hist.getResponseDukcapil(), CheckDukcapilResponse.class);
			}
			
			if ("Match".equalsIgnoreCase(dukcapil.getFinalResult()) && "00".equals(dukcapil.getResponseCode())) {
				mapResult.put("d0", 1);
				mapResult.put("cif", dukcapil.getCif());
				mapResult.put("idHistCust", dukcapil.getIdCheckCustHistory());
				return mapResult;
			} else {
				// not match 0 not found 2 null not found
				if ("Not Match".equalsIgnoreCase(dukcapil.getFinalResult())) {
					mapResult.put("d0", 0);
				} else if ("Not Found".equalsIgnoreCase(dukcapil.getFinalResult())) {
					mapResult.put("d0", 2);
				} else if (dukcapil.getFinalResult() == null) {
					mapResult.put("d0", 2);
				}
				
				mapResult.put("d1", dukcapil.getResponseMessage() + ":" + taskDukcapilBean.getNik());
				mapResult.put("cif", dukcapil.getCif());
				mapResult.put("idHistCust", dukcapil.getIdCheckCustHistory());
				return mapResult;
			}
		}
	}
	
	@Transactional
	@Override
	public void insertTaskDukcapil(TaskDukcapilBean taskDukcapilBean, String isMatchDukcapil, String dbName, AuditContext auditContext) {
		LOG.info("Start Insert to Staging Confins with json {}", gson.toJson(taskDukcapilBean));
		Date date = null;
		SimpleDateFormat  formatter = new SimpleDateFormat("dd MMM yyyy"); 
		try {
			date = formatter.parse(taskDukcapilBean.getBirthDt());
		} catch (ParseException e1) {
			e1.printStackTrace();
		}
		
		Object[][] params = {{ 1, taskDukcapilBean.getNik() },
		{ 2, taskDukcapilBean.getCustName()},
		{ 3, taskDukcapilBean.getBirthPlace()},
		{ 4, DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss.sss")},
		{ 5, taskDukcapilBean.getLegalAddr()},
		{ 6, taskDukcapilBean.getLegalRt()},
		{ 7, taskDukcapilBean.getLegalRw()},
		{ 8, taskDukcapilBean.getLegalKelurahan()},
		{ 9, taskDukcapilBean.getLegalKecamatan()},
		{ 10, taskDukcapilBean.getLegalCity()},
		{ 11, taskDukcapilBean.getLegalZipcode()},
		{ 12, taskDukcapilBean.getResidenceAddr()},
		{ 13, taskDukcapilBean.getResidenceRt()},
		{ 14, taskDukcapilBean.getResidenceRw()},
		{ 15, taskDukcapilBean.getResidenceKelurahan()},
		{ 16, taskDukcapilBean.getResidenceKecamatan()},
		{ 17, taskDukcapilBean.getResidenceCity()},
		{ 18, taskDukcapilBean.getResidenceZipcode()},
		{ 19, taskDukcapilBean.getMobilePhnNo()},
		{ 20, taskDukcapilBean.getProdCode()},
		{ 21, taskDukcapilBean.getSubzipcode()},
		{ 22, taskDukcapilBean.getDealerCode()},
		{ 23, taskDukcapilBean.getSoa()},
		{ 24, taskDukcapilBean.getLegalProvinsi()},
		{ 25, taskDukcapilBean.getResidenceProvinsi()},
		{ 26, taskDukcapilBean.getStatus()},
		{ 27, taskDukcapilBean.getUsrCrt()},
		{ 28, isMatchDukcapil},
		{ 29, dbName},
		{ 30, taskDukcapilBean.getExistingAgrmntNo()==null?"":taskDukcapilBean.getExistingAgrmntNo()},
		{ 31, taskDukcapilBean.getOrderNo()==null?"":taskDukcapilBean.getOrderNo()},
		{ 32, taskDukcapilBean.getCatatanCust()==null?"":taskDukcapilBean.getCatatanCust()},
		{ 33, taskDukcapilBean.getMotherName()==null?"":taskDukcapilBean.getMotherName()}
		};
		
		Object[][] paramOutput = {{ 34,  Types.VARCHAR}, { 35,  Types.VARCHAR}};
		Map mapInsert = null;
		try {
			 mapInsert = (Map)this.getManagerDAO().callProcedureNativeString("{ CALL ADD_NEW_LEAD(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}", params, paramOutput);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RemoteException(messageSource.getMessage("businesslogic.createtask.failedsavetoconfins",null, this.retrieveLocaleAudit(auditContext)));
		}
		
		String map = mapInsert.get("o1").toString();
		String map2 = mapInsert.get("o2").toString();
	    if(!"OK".equals(map)) {
	    	LOG.error("Error Insert To Staging Confins: "+map);
	    	LOG.error("Error Insert stattement: "+map2);
	    	throw new RemoteException(messageSource.getMessage("businesslogic.createtask.failedsavetoconfins",null, this.retrieveLocaleAudit(auditContext)));
	    }
	   
	}
	
	@Override
	public String getKelurahanFromSubmitPage(String data, AuditContext callerId) {
		String result = null;
		String [] answers = data.split("#");
		for (int i = 0; i < answers.length; i++) {
			String[] tempData = answers[i].split("~");
			String answer = tempData[4].equalsIgnoreCase("-1") ? StringUtils.EMPTY : tempData[4];
			MsQuestion msQuestion = this.commonLogic.retrieveQuestionByUuid(Long.parseLong(tempData[0]), callerId);
			if (GlobalVal.STG_RES_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_KELURAHN.equalsIgnoreCase(msQuestion.getRefId())) {
				result = answer;
				return result;
			}
		}
		return result;
	}

	@Override
	public TaskDukcapilBean validasiFormLead(String data, long uuidUser, boolean flagDukcapil, AuditContext callerId) {
		AmMsuser userLogin = this.getManagerDAO().selectOne(AmMsuser.class, uuidUser);
		String [] answers = data.split("#");
		TaskDukcapilBean taskDukcapilBean = new TaskDukcapilBean();
		taskDukcapilBean.setOrderDt(new Date());
		taskDukcapilBean.setStatus("REQ");
		taskDukcapilBean.setUsrCrt(userLogin!=null?userLogin.getUniqueId():String.valueOf(uuidUser));
		taskDukcapilBean.setDtmCrt(new Date());
		for (int i = 0; i < answers.length; i++) {
			String[] tempData = answers[i].split("~");
			String answer = tempData[4].equalsIgnoreCase("-1") ? StringUtils.EMPTY : tempData[4];
			MsQuestion msQuestion = this.commonLogic.retrieveQuestionByUuid(Long.parseLong(tempData[0]), callerId);
			
			if (tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO) ||
					tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION) ||
					tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN) ||
					tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION) ||
					tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION)) {
				Object param [][] = {{Restrictions.eq("uuidLov", Long.valueOf(answer))}};
				MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, param);
				if (GlobalVal.STG_SOA.equalsIgnoreCase(msQuestion.getRefId())) {
					answer = msLov.getDescription();
				} else {
					answer = msLov.getCode();
				}
			}
			if (GlobalVal.STG_NIK.equals(msQuestion.getRefId())) {
				taskDukcapilBean.setNik(answer);
			} else if (GlobalVal.STG_CUST_NAME.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setCustName(answer);
			} else if (GlobalVal.STG_BIRTH_PLACE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setBirthPlace(answer);
			} else if (GlobalVal.STG_BIRTH_DT.equalsIgnoreCase(msQuestion.getRefId())) {
				try {
					SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
					SimpleDateFormat format2 = new SimpleDateFormat("dd MMMM yyyy");
					Date date = format1.parse(answer);
					taskDukcapilBean.setBirthDt(format2.format(date));
				} catch (ParseException e) {
					LOG.error(e.getMessage(), e);
				}
			} else if (GlobalVal.STG_LEGAL_ADDR.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.STG_LEGAL_ADDR_PMHON.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalAddr(answer);
			} else if (GlobalVal.STG_LEGAL_RT.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_RT.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalRt(answer);
			} else if (GlobalVal.STG_LEGAL_RW.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_RW.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalRw(answer);
			} else if (GlobalVal.STG_LEGAL_PROVINSI.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_PROVINSI.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalProvinsi(answer);
			} else if (GlobalVal.STG_LEGAL_CITY.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_KOTA.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalCity(answer);
			} else if (GlobalVal.STG_LEGAL_KECAMATAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_KECAMATAN.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalKecamatan(answer);
			} else if (GlobalVal.STG_LEGAL_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalKelurahan(answer);
			} else if (GlobalVal.STG_LEGAL_ZIPCODE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setLegalZipcode(answer);
			} else if (GlobalVal.STG_NAMA_IBU.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setMotherName(answer);
			} else if (GlobalVal.STG_RES_ADDR.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.STG_ADDR_PMHON.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceAddr(answer);
			} else if (GlobalVal.STG_RES_RT.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_RT.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceRt(answer);
			} else if (GlobalVal.STG_RES_RW.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_RW.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceRw(answer);
			} else if (GlobalVal.STG_RES_PROVINSI.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_PROVINSI.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceProvinsi(answer);
			} else if (GlobalVal.STG_RES_CITY.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_CITY.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceCity(answer);
			} else if (GlobalVal.STG_RES_KECAMATAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_KECAMATN.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceKecamatan(answer);
			} else if (GlobalVal.STG_RES_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_STG_RES_KELURAHN.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceKelurahan(answer);
			} else if (GlobalVal.STG_RES_ZIPCODE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setResidenceZipcode(answer);
			} else if (GlobalVal.STG_MOBILE_PHN_NO.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.STG_NO_HANDPHONE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setMobilePhnNo(answer);
			} else if (GlobalVal.STG_PROD_CODE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setProdCode(answer);
			} else if (GlobalVal.STG_SUBZIPCODE.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setSubzipcode(answer);
			} else if (GlobalVal.STG_DEALER_CODE.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.STG_DEALERCODE_LEAD.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setDealerCode(answer);
			} else if (GlobalVal.STG_SOA.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setSoa(answer);
			} else if (GlobalVal.STG_EXT_AGR_NO.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setExistingAgrmntNo(answer);
			} else if (GlobalVal.NOTES.equalsIgnoreCase(msQuestion.getRefId())) {
				taskDukcapilBean.setCatatanCust(answer);
			}
		}
		
		//Set SubZipcode
		MsLov msLovSubZipcode = null;
		
		if (StringUtils.isNotBlank(taskDukcapilBean.getResidenceKelurahan())
				&& StringUtils.isNotBlank(taskDukcapilBean.getResidenceKecamatan())
				&& StringUtils.isNotBlank(taskDukcapilBean.getResidenceCity())
				&& StringUtils.isNotBlank(taskDukcapilBean.getResidenceProvinsi())) {
			Object paramSubZipcode[][] = { { Restrictions.eq("constraint1", taskDukcapilBean.getResidenceKelurahan()) },
					{ Restrictions.eq("constraint2", taskDukcapilBean.getResidenceKecamatan()) },
					{ Restrictions.eq("constraint3", taskDukcapilBean.getResidenceCity()) },
					{ Restrictions.eq("constraint4", taskDukcapilBean.getResidenceProvinsi()) },
					{ Restrictions.eq("lovGroup", GlobalVal.CODE_LOV_GROUP_SUB_ZIPCODE) } };
			msLovSubZipcode = this.getManagerDAO().selectOne(MsLov.class, paramSubZipcode);
		} else {
			Object paramSubZipcode[][] = { { Restrictions.eq("constraint1", taskDukcapilBean.getLegalKelurahan()) },
					{ Restrictions.eq("constraint2", taskDukcapilBean.getLegalKecamatan()) },
					{ Restrictions.eq("constraint3", taskDukcapilBean.getLegalCity()) },
					{ Restrictions.eq("constraint4", taskDukcapilBean.getLegalProvinsi()) },
					{ Restrictions.eq("lovGroup", GlobalVal.CODE_LOV_GROUP_SUB_ZIPCODE) } };
			msLovSubZipcode = this.getManagerDAO().selectOne(MsLov.class, paramSubZipcode);
		}
		
		if(msLovSubZipcode != null) {
			taskDukcapilBean.setSubzipcode(msLovSubZipcode.getCode());
		}
			
			

		if(flagDukcapil) {
			//validasi subzipcode & dealer code
			String[][] params = { { "productCategory", taskDukcapilBean.getProdCode() } };
			List listValidate = (List) this.getManagerDAO().selectAllNativeString("SELECT ttam.ASSIGN_CODE FROM TBL_PRODUCT_CATEGORY tpc WITH (nolock) " + 
					"JOIN TBL_MAP_PROD_ASSIGN tmpa WITH (nolock) on tpc.TBL_PRODUCT_CATEGORY_ID = tmpa.TBL_PRODUCT_CATEGORY_ID " + 
					"JOIN TBL_TASK_ASSIGN_MODE ttam WITH (nolock) on tmpa.TBL_TASK_ASSIGN_MODE_ID = ttam.TBL_TASK_ASSIGN_MODE_ID " + 
					"WHERE tpc.PRODUCT_CATEGORY_CODE = :productCategory", params);
			
			for (int i = 0; i < listValidate.size(); i++) {
				Map mapValidate = (Map) listValidate.get(i);
				String valueValidate = (String) mapValidate.get("d0");
				if(GlobalVal.MAPPING_KAT.equalsIgnoreCase(valueValidate)){
					if(taskDukcapilBean.getSubzipcode() == null) {
						MsQuestion msQuestion = this.commonLogic.retrieveQuestionByRefId(GlobalVal.STG_SUBZIPCODE, callerId);
						throw new RemoteException(messageSource.getMessage("businesslogic.global.mandatory",
		                        new Object[] {msQuestion.getQuestionLabel()}, this.retrieveLocaleAudit(callerId)));   
					}
				}
				if(GlobalVal.MAPPING_DEALER_TYPE_FOCUS.equalsIgnoreCase(valueValidate)
					|| GlobalVal.MAPPING_DEALER_TYPE_NON_FOCUS.equalsIgnoreCase(valueValidate)) {
					if(taskDukcapilBean.getDealerCode() == null) {
						MsQuestion msQuestion = this.commonLogic.retrieveQuestionByRefId(GlobalVal.STG_DEALER_CODE, callerId);
						throw new RemoteException(messageSource.getMessage("businesslogic.global.mandatory",
		                        new Object[] {msQuestion.getQuestionLabel()}, this.retrieveLocaleAudit(callerId)));   
					}
				}
			}
			//end validasi
		}
		
		return taskDukcapilBean;
	}
	
	private SaveTaskDResult saveTaskDIntoJson(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, AmMsuser userSubmit, boolean flagRevisit,
			TrTaskrejectedhistory rejectHistory, boolean flag,
			Map<Integer, MsQuestion> msQuestions, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		String isFinal = "0";
		String totalBayar = "0";
		
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		TaskDocumentBean document = null;
		boolean newDoc = false;
		if (docDb == null) {
			docDb = new TrTaskdocument(trTaskH, new Date(), auditContext.getCallerId());
			document = new TaskDocumentBean();
			newDoc = true;
		}
		else {
			document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
			docDb.setDtmUpd(new Date());
			docDb.setUsrUpd(auditContext.getCallerId());
			newDoc = false;
		}
		
		List<AnswerBean> answers = (newDoc) ? new ArrayList<AnswerBean>(taskDBean.length) : document.getAnswers();		
		if (newDoc) {
			document.setAnswers(answers);
		}
		
		TrTaskcolldata taskCollData = this.getManagerDAO().selectOne(TrTaskcolldata.class, trTaskH.getUuidTaskH());
		if (taskCollData == null){
			taskCollData = new TrTaskcolldata();
			taskCollData.setTrTaskH(trTaskH);
		}
		for (int i=0; i < taskDBean.length; i++) {
			isFinal = taskDBean[i].getIs_final();
			
			MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
			String answerTypeCode = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String optionAnswerId = StringUtils.stripToEmpty(taskDBean[i].getOption_answer_id());
			String questionText = StringUtils.stripToEmpty(taskDBean[i].getQuestion_label());
			String textAnswer = StringUtils.stripToEmpty(taskDBean[i].getText_answer());
			String latitude = StringUtils.stripToNull(taskDBean[i].getLatitude());						
			String longitude = StringUtils.stripToNull(taskDBean[i].getLongitude());
			String mcc = StringUtils.stripToNull(taskDBean[i].getMcc());
			String mnc = StringUtils.stripToNull(taskDBean[i].getMnc());
			String lac = StringUtils.stripToNull(taskDBean[i].getLac());				
			String cellId = StringUtils.stripToNull(taskDBean[i].getCid());				
			String accuracy = StringUtils.stripToNull(taskDBean[i].getAccuracy());	
			String image = StringUtils.stripToNull(taskDBean[i].getImage());
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerTypeCode) &&
					StringUtils.isNotEmpty(textAnswer) && StringUtils.containsAny(textAnswer, ',', '.')) {
				textAnswer = textAnswer.substring(0, textAnswer.length()-2).replace(",", "").replace(".", "");				
			}
			
			int idxAnswer = document.findAnswerIndex(msQuestion.getUuidQuestion());
			
			AnswerBean answer = this.constructAnswer(
					document, idxAnswer, msQuestion, answerTypeCode, questionText, textAnswer, optionAnswerId,
					latitude, longitude, accuracy, mcc, mnc, lac, cellId, trTaskH, image, isl, imagePath, auditContext);
			
			if (idxAnswer == -1) {
				answers.add(answer);
			}
		}
		docDb.setDocument(gson.toJson(document, TaskDocumentBean.class));
		
		if (newDoc) {
			this.getManagerDAO().insert(docDb);
		}
		else {
			this.getManagerDAO().update(docDb);
		}
		
		return new SaveTaskDResult(isFinal, totalBayar);
	}
	
	private AnswerBean constructAnswer(TaskDocumentBean document, int idxAnswer, MsQuestion msQuestion,
			String answerTypeCode, String questionText, String textAnswer, String optionAnswerId,
			String latitude, String longitude, String accuracy, String mcc, String mnc, String lac, String cellId,
			TrTaskH trTaskH, String image, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {
		AnswerBean answer = (idxAnswer == -1) ? new AnswerBean() : document.getAnswers().get(idxAnswer);
		QuestionBean questionBean = new QuestionBean(
				msQuestion.getUuidQuestion(), questionText, msQuestion.getRefId(), answerTypeCode,
				(msQuestion.getMsOrdertag() == null ? null : msQuestion.getMsOrdertag().getTagName()),
				(msQuestion.getMsAssettag() == null ? null : msQuestion.getMsAssettag().getAssetTagName()),
				(msQuestion.getMsCollectiontag() == null ? null : msQuestion.getMsCollectiontag().getTagName()));
		answer.setQuestion(questionBean);
		
		if (MssTool.isChoiceQuestion(answerTypeCode)) {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			List<OptionBean> options = (answer.getOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getOptAnswers();
			
			MsLov msLovByLovId = null; 
			if (StringUtils.isNotBlank(optionAnswerId)) {
				msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
			}
			if (msLovByLovId != null) {
				OptionBean option = new OptionBean();
				option.setUuid(msLovByLovId.getUuidLov());
				option.setCode(msLovByLovId.getCode());
				option.setDesc(msLovByLovId.getDescription());
				option.setFreeText(StringUtils.trimToNull(textAnswer));
				if (!options.contains(option)) {
					options.add(option);
				}
				
				answer.setOptAnswers(options);
			}				
		}
		else if (MssTool.isImageQuestion(answerTypeCode) || GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
			answer.setOptAnswers(null);
			Boolean isGps = null;
			Boolean isConverted = null;
			
			if (mcc != null) {
				com.adins.mss.model.taskdjson.LocationBean locationBean = (answer.getLocation() == null) ?
						new com.adins.mss.model.taskdjson.LocationBean() : answer.getLocation();
				locationBean.setMcc(checkEmptyInteger(mcc).intValue());
				locationBean.setMnc(checkEmptyInteger(mnc).intValue());
				locationBean.setLac(checkEmptyInteger(lac).intValue());
				locationBean.setCid(checkEmptyInteger(cellId).intValue());
				locationBean.setAccuracy(checkEmptyInteger(accuracy));
				
				if (latitude != null && longitude != null
						&& new BigDecimal(latitude).intValue() != 0 && new BigDecimal(longitude).intValue() != 0) {
					locationBean.setLat(checkEmptyBigdecimal(latitude).doubleValue());
					locationBean.setLng(checkEmptyBigdecimal(longitude).doubleValue());
					locationBean.setIsGps(1);
				}
				else {
					LocationBean convertedLocation = this.getLocationByCellId(locationBean.getMcc(), locationBean.getMnc(),
							locationBean.getLac(), locationBean.getCid(), auditContext);
					if (convertedLocation.getCoordinate() != null) {
						locationBean.setAccuracy(convertedLocation.getAccuracy());
						locationBean.setLat(convertedLocation.getCoordinate().getLatitude());
						locationBean.setLng(convertedLocation.getCoordinate().getLongitude());
						locationBean.setIsGps(0);
						locationBean.setIsConverted(1);
						
						latitude = Double.toString(convertedLocation.getCoordinate().getLatitude());
						longitude = Double.toString(convertedLocation.getCoordinate().getLongitude());
						accuracy = Integer.toString(convertedLocation.getAccuracy());
					}
					isGps = Boolean.FALSE;
					isConverted = Boolean.TRUE;
				}
				
				answer.setLocation(locationBean);
			}

			if (MssTool.isImageQuestion(answerTypeCode)) {
				ImageBean imageBean = (answer.getLobAnswer() == null) ? new ImageBean() : answer.getLobAnswer();
				if (image != null && !image.isEmpty()) {
					imageBean.setHasImage(true);
					answer.setLobAnswer(imageBean);
				}
				
				//insert into Table TR_TASKDETAILLOB
				String uuidTaskD = this.getManagerDAO().getUUID();
				long idLob = this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, isGps, isConverted, isl, imagePath);
				if (idLob > 0L){
					imageBean.setId(idLob);
				}
			}
			else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerTypeCode)) {
				answer.setTxtAnswer(textAnswer);
			}
		}
		else {
			answer.setLocation(null);
			answer.setLobAnswer(null);
			
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)
					|| GlobalVal.ANSWER_TYPE_DATETIME.equals(answerTypeCode)) {
				textAnswer = this.dateAnswerToText(textAnswer, answerTypeCode);
			}
			answer.setOptAnswers(null);
			answer.setTxtAnswer(textAnswer);
		}
		
		return answer;
	}
	
	private BigDecimal checkEmptyBigdecimal(String in){
		if (StringUtils.isBlank(in)) {
			return null;
		}
		else{
			return new BigDecimal(in);		
		}
	}
	
	private Integer checkEmptyInteger(String in){		
		if (StringUtils.isBlank(in)){
			return null;
		}
		else {
			return Integer.valueOf((int) Math.round(new Double(in)));		
		}
	}
	
	private String dateAnswerToText(String dateAnswer, String answerTypeCode) {
		DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
		try {
			Date result = df.parse(dateAnswer);
			if (GlobalVal.ANSWER_TYPE_DATE.equals(answerTypeCode)) {
				return (null != result) ? new SimpleDateFormat("dd/MM/yyyy").format(result) : null;
			}
			else {
				return (null != result) ? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result) : null;
			}
		}
		catch (ParseException e) {
			LOG.warn("Cannot parse '{}' to answer type {}", dateAnswer, answerTypeCode, e);
			return null;
		}
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	private long insertDetailLob(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD,
			String questionText, String base64Image, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Boolean isGps, Boolean isConverted, ImageStorageLocation saveImgLoc, Path imgLoc){
		
		Object[][] params = { 
				{Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())},
				{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())} 
		};
		TrTaskdetaillob trTaskdetaillob = this.getManagerDAO().selectOne(TrTaskdetaillob.class, params);

		if (trTaskdetaillob == null) {
			trTaskdetaillob = new TrTaskdetaillob();
			trTaskdetaillob.setUsrCrt(auditContext.getCallerId());
			trTaskdetaillob.setDtmCrt(new Date());
		} 
		else {
			trTaskdetaillob.setUsrUpd(auditContext.getCallerId());
			trTaskdetaillob.setDtmUpd(new Date());
		}
		
		trTaskdetaillob.setTrTaskH(trTaskH);
		trTaskdetaillob.setMsQuestion(msQuestion);
		
		if(GlobalVal.ANSWER_TYPE_OCR.equalsIgnoreCase(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			String[] ocr = base64Image.split("@@@");
			base64Image = ocr[0];
			String result = URLDecoder.decode(ocr[1]);
			
			trTaskdetaillob.setTextAnswer(result);
		}
		
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				trTaskdetaillob.setLobFile(BaseEncoding.base64().decode(base64Image));
			}
		}
		else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) {
			if (base64Image != null && !base64Image.isEmpty()) {
				DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
				Date date = new Date();
				Path outputPath = Paths.get(imgLoc.toString(), dateFormat.format(date));

				String absoluteFilename = trTaskH.getTaskId() + "_" + msQuestion.getRefId() + "_"
				        + uuidTaskD + ".jpg";
				
				String outputFile = this.imageStorageLogic.storeImageFileSystem(
				        BaseEncoding.base64().decode(base64Image), outputPath, absoluteFilename);
				trTaskdetaillob.setImagePath(outputFile);
			}
		}
		trTaskdetaillob.setQuestionText(questionText);
		trTaskdetaillob.setLatitude(checkEmptyBigdecimal(latitude));
		trTaskdetaillob.setLongitude(checkEmptyBigdecimal(longitude));
		
		if (Boolean.TRUE.equals(isGps) || (trTaskdetaillob.getLatitude()!=null && trTaskdetaillob.getLongitude()!=null &&
				trTaskdetaillob.getLatitude().intValue()!=0 && trTaskdetaillob.getLongitude().intValue()!=0) &&
				!Boolean.TRUE.equals(isConverted))
			trTaskdetaillob.setIsGps("1");
		else if (Boolean.FALSE.equals(isGps))
			trTaskdetaillob.setIsGps("0");
		
		trTaskdetaillob.setMcc(checkEmptyInteger(mcc));
		trTaskdetaillob.setMnc(checkEmptyInteger(mnc));
		trTaskdetaillob.setLac(checkEmptyInteger(lac));
		trTaskdetaillob.setCellId(checkEmptyInteger(cellId));
		trTaskdetaillob.setAccuracy(checkEmptyInteger(accuracy));
		if (!Boolean.TRUE.equals(isConverted) && !"1".equals(trTaskdetaillob.getIsGps()) && trTaskdetaillob.getMcc() != null && trTaskdetaillob.getMnc() != null
				&& trTaskdetaillob.getLac() != null && trTaskdetaillob.getCellId() != null) {
			this.getLocationByCellId(trTaskdetaillob, auditContext);
		}
		else if (Boolean.TRUE.equals(isConverted)) {
			trTaskdetaillob.setIsConverted("1");
		}
		
		//penambahan save ke table TR_TASKSURVEYDATA
		if (msQuestion.getMsAssettag() != null) {
			Object paramsSurveyData[][] = { 
					{Restrictions.eq("uuidTaskId", trTaskH.getUuidTaskH())} 
			};
			TrTasksurveydata trTasksurveydata = this.getManagerDAO().selectOne(TrTasksurveydata.class, paramsSurveyData);
			TrTasksurveydata taskSurveyDataInsert = new TrTasksurveydata();
			if (trTasksurveydata == null) {
				taskSurveyDataInsert.setUsrCrt(auditContext.getCallerId());
				taskSurveyDataInsert.setDtmCrt(new Date());
				taskSurveyDataInsert.setTrTaskH(trTaskH);
				taskSurveyDataInsert.setUuidTaskId(trTaskH.getUuidTaskH());
			} else {
				taskSurveyDataInsert = trTasksurveydata;
				taskSurveyDataInsert.setUsrUpd(auditContext.getCallerId());
				taskSurveyDataInsert.setDtmUpd(new Date());
			}
			
			if (GlobalVal.ASSET_TAG_HOME.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setHomeLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setHomeLongitude(trTaskdetaillob.getLongitude());
			}
			else if (GlobalVal.ASSET_TAG_IDENTITY.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setLegalAddrLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setLegalAddrLongitude(trTaskdetaillob.getLongitude());
			}
			else if (GlobalVal.ASSET_TAG_OFFICE.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setOfficeLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setOfficeLongitude(trTaskdetaillob.getLongitude());
			}
			else if (GlobalVal.ASSET_TAG_STREET.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setDrivewayLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setDrivewayLongitude(trTaskdetaillob.getLongitude());
			}
			else if (GlobalVal.ASSET_TAG_VEHICLE.equals(msQuestion.getMsAssettag().getAssetTagName())) {
				taskSurveyDataInsert.setVehicleLatitude(trTaskdetaillob.getLatitude());
				taskSurveyDataInsert.setVehicleLongitude(trTaskdetaillob.getLongitude());
			}
			
			if (trTasksurveydata == null) {
				this.getManagerDAO().insert(taskSurveyDataInsert);
			}
			else {
				this.getManagerDAO().update(taskSurveyDataInsert); //DAO will do saveOrUpdate
			}
		}	

		//end penambahan save ke table TR_TASKSURVEYDATA
		this.getManagerDAO().update(trTaskdetaillob); //DAO will do saveOrUpdate
		
		if (ImageStorageLocation.DMS == saveImgLoc) {
			UploadImageRequestBean request = new UploadImageRequestBean();
			UploadImageBean uploadBean  = new UploadImageBean();
			
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Object [][] param = {{"uuidTaskH", trTaskH.getTaskId()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT	GROUP_TASK_ID " + 
					"FROM	MS_GROUPTASK with(nolock) " + 
					"WHERE	UUID_TASK_H = :uuidTaskH ", param);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(trTaskH.getMsForm().getUuidForm()));
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != trTaskH.getSubmitDate()) {
	        	taskDate = trTaskH.getSubmitDate();
	        } else {
	        	taskDate = new Date();
	        }
			try {
				cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
			} catch (ParseException e1) {
				LOG.error(e1.getMessage(), e1);
				e1.printStackTrace();
			}
			if(taskDate.before(cutoffDate)) {
				uploadBean.setId(trTaskH.getTaskId());
				uploadBean.setTaskId(trTaskH.getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				uploadBean.setId(groupTaskIdForm);
				uploadBean.setTaskId(groupTaskIdForm);
			}
	
	
			uploadBean.setType("survey");
			uploadBean.setJenisDoc(msQuestion.getQuestionLabel());
			uploadBean.setRefId(msQuestion.getRefId());
			uploadBean.setUsername(trTaskH.getAmMsuser().getUniqueId());
			if(StringUtils.isNotEmpty(base64Image)){
				trTaskdetaillob.setImagePath(String.valueOf(trTaskdetaillob.getUuidTaskDetailLob()));
				this.getManagerDAO().update(trTaskdetaillob);
				request.setByteImage(BaseEncoding.base64().decode(base64Image));
			}
			request.setFileName(trTaskdetaillob.getUuidTaskDetailLob()+".jpg");
			try {
				request.setIpAddress(InetAddress.getLocalHost().getHostAddress());
			} catch (UnknownHostException e) {
				LOG.error("Logging error DMS Host : {}",e.getMessage());
			}
			request.setUploadImageBean(uploadBean); 
			if(StringUtils.isNotEmpty(base64Image)){
				
				//PANGGIL KE INTNCFORMLOGIC
				intFormLogic.uploadImageResponse(auditContext,request);
			}
		}
		
		return trTaskdetaillob.getUuidTaskDetailLob();
	}
	
	private void getLocationByCellId(TrTaskD taskD, AuditContext auditContext){
		List<LocationBean> listLocations = new ArrayList<LocationBean>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(taskD.getCellId());
		locationBean.setLac(taskD.getLac().intValue());
		locationBean.setMcc(taskD.getMcc().intValue());
		locationBean.setMnc(taskD.getMnc().intValue());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			taskD.setLatitude(new BigDecimal(locationBean.getCoordinate()
					.getLatitude()));
			taskD.setLongitude(new BigDecimal(locationBean.getCoordinate()
					.getLongitude()));
			taskD.setAccuracy(new Integer(locationBean.getAccuracy()));
			
			taskD.setTextAnswer("Coord : "+taskD.getLatitude().setScale(6, RoundingMode.HALF_UP)+", "+taskD.getLongitude().setScale(6, RoundingMode.HALF_UP)+" Accuracy : "+taskD.getAccuracy()+" m");
			taskD.setIsConverted("1");
		}
	}
	
	private LocationBean getLocationByCellId(int mcc, int mnc, int lac, int cellId,
			AuditContext auditContext) {
		List<LocationBean> listLocations = new ArrayList<LocationBean>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(cellId);
		locationBean.setLac(lac);
		locationBean.setMnc(mnc);
		locationBean.setMcc(mcc);
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);
		return locationBean;
	}
	
	private void getLocationByCellId(TrTaskdetaillob detailLob, AuditContext auditContext){
		List<LocationBean> listLocations = new ArrayList<LocationBean>();
		LocationBean locationBean = new LocationBean();
		locationBean.setCellid(detailLob.getCellId());
		locationBean.setLac(detailLob.getLac().intValue());
		locationBean.setMcc(detailLob.getMcc().intValue());
		locationBean.setMnc(detailLob.getMnc().intValue());
		listLocations.add(locationBean);
		this.geocoder.geocodeCellId(listLocations, auditContext);

		if (locationBean.getCoordinate() != null) {
			detailLob.setLatitude(new BigDecimal(locationBean.getCoordinate()
					.getLatitude()));
			detailLob.setLongitude(new BigDecimal(locationBean.getCoordinate()
					.getLongitude()));
			detailLob.setAccuracy(new Integer(locationBean.getAccuracy()));
			
			detailLob.setTextAnswer("Coord : "+detailLob.getLatitude().setScale(6, RoundingMode.HALF_UP)+", "+detailLob.getLongitude().setScale(6, RoundingMode.HALF_UP)+" Accuracy : "+detailLob.getAccuracy()+" m");
			detailLob.setIsConverted("1");
		}
	}
	
	private Map<Long, TrTaskD> listTaskDAsMap(String uuidTaskH) {
		List<TrTaskD> result = this.listTaskD(uuidTaskH);
		
		if (result == null || result.isEmpty()){
			return Collections.emptyMap();
		}
		
		Map<Long, TrTaskD> resultMap = new HashMap<>(result.size());
		for (Iterator iterator = result.iterator(); iterator.hasNext();) {
			TrTaskD trTaskD = (TrTaskD) iterator.next();
			resultMap.put(trTaskD.getMsQuestion().getUuidQuestion(), trTaskD);
		}
		
		return resultMap;
	}
	
	private List<TrTaskD> listTaskD(String uuidTaskH) {
		if (StringUtils.isBlank(uuidTaskH)) {
			return Collections.emptyList();
		}
		
		Object[][] params = { {Restrictions.eq("trTaskH.uuidTaskH", Long.valueOf(uuidTaskH))} };
		Map<String, Object> result = this.getManagerDAO().list(TrTaskD.class, params, null);
		
		return (List<TrTaskD>) result.get(GlobalKey.MAP_RESULT_LIST);
	}
	
	private class SaveTaskDResult {
		private String isFinal;
		private String totalBayar;
				
		public SaveTaskDResult(String isFinal, String totalBayar) {
			super();
			this.isFinal = isFinal;
			this.totalBayar = totalBayar;
		}
		
		public String getIsFinal() {
			return isFinal;
		}
		public String getTotalBayar() {
			return totalBayar;
		}
	}
	
	private SaveTaskDResult saveTaskDIntoRow(SubmitTaskDBean[] taskDBean, TrTaskH trTaskH, AmMsuser userSubmit, boolean flagRevisit,
			TrTaskrejectedhistory rejectHistory, boolean flag,
			Map<Integer, MsQuestion> msQuestions, Map<Long, TrTaskD> listTaskD, ImageStorageLocation isl, Path imagePath, AuditContext auditContext) {				
		
		boolean hasBrand = false;
		boolean hasModel = false;
		boolean hasGroup = false;
		boolean hasType = false;
		String idAsset = StringUtils.EMPTY;
		
		Map<String, Object> mapAssetProduct = new HashMap<String, Object>();
		
		if (taskDBean.length > 1) {
			//delete multiple detail
			String[] answerTypeArr = {GlobalVal.ANSWER_TYPE_MULTIPLE, GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION,
					GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION};
			Object[][] paramsMulti = {
					{"uuidTaskH", trTaskH.getUuidTaskH()},
					{"answerType", Arrays.asList(answerTypeArr)} };
			this.getManagerDAO().deleteNativeString("delete d from tr_task_d d "
					+"left join ms_question q on d.uuid_question = q.uuid_question "
					+"left join ms_answertype at on q.uuid_answer_type = at.uuid_answer_type "
					+"where d.uuid_task_h = :uuidTaskH and "
					+"at.code_answer_type in (:answerType)", paramsMulti);
		}
		
		String isFinal = "0";
		String totalBayar = "0";
		
		TrTaskcolldata taskCollData = this.getManagerDAO().selectOne(TrTaskcolldata.class, trTaskH.getUuidTaskH());
		if (taskCollData == null){
			taskCollData = new TrTaskcolldata();
		}
		
		for (int i=0; i < taskDBean.length; i++) {
			MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
			
			String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
			
			String uuidTaskD = taskDBean[i].getUuid_task_d();
			String optionAnswerId = taskDBean[i].getOption_answer_id();
			String lov = taskDBean[i].getLov();
			String textAnswer = taskDBean[i].getText_answer();
			String questionText = taskDBean[i].getQuestion_label();
			String latitude = taskDBean[i].getLatitude();
			String longitude = taskDBean[i].getLongitude();
			String mcc = taskDBean[i].getMcc();
			String mnc = taskDBean[i].getMnc();
			String lac = taskDBean[i].getLac();
			String cellId = taskDBean[i].getCid();
			String accuracy = taskDBean[i].getAccuracy();
			String image = taskDBean[i].getImage();
			String uuidLookup = taskDBean[i].getUuid_lookup();
			isFinal = taskDBean[i].getIs_final();
			
			String assetType = StringUtils.EMPTY;
			if (null != msQuestion.getMsAssettag()) {
				assetType = msQuestion.getMsAssettag().getAssetTagName();
			}
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType)) {
				if (textAnswer.contains(",")||textAnswer.contains(".")) {
					textAnswer = textAnswer.substring(0,textAnswer.length()-2).replace(",", "").replace(".", "");
				}
			}
			
			if (GlobalVal.ANSWER_TYPE_LOCATION_WITH_ADDRESS.equals(answerType)) {				
				if (textAnswer.contains("|")) {
					String[] temp = textAnswer.split("[|]");
					textAnswer = temp[temp.length-1];
				}
			}
			
			if (MssTool.isImageQuestion(answerType)){										
					//insert into Table TR_TASKDETAILLOB
				this.insertDetailLob(auditContext, trTaskH, msQuestion, uuidTaskD, questionText, image, 
							latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, isl, imagePath);						
			} else if (GlobalVal.ANSWER_TYPE_LU_OFFLINE.equals(answerType) || 
					GlobalVal.ANSWER_TYPE_LU_OFFLINE_WITH_SUGGESTION.equals(answerType)) {
				// Check product answer
				if (GlobalVal.ASSET_TAG_BRAND.equals(assetType) ||
						GlobalVal.ASSET_TAG_MODEL.equals(assetType) ||
						GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType) ||
						GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
					if (GlobalVal.ASSET_TAG_BRAND.equals(assetType)) {
						hasBrand = true;
					} else if (GlobalVal.ASSET_TAG_MODEL.equals(assetType)) {
						hasModel = true;
					} else if (GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType)) {
						hasGroup = true;
					} else if (GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
						hasType = true;
						idAsset = taskDBean[i].getUuid_lookup();
					}
					mapAssetProduct.put(assetType, i);
				} else {
					Map<String, Object> mapAns = new HashMap<String, Object>();
					if (null != uuidLookup) {
						Long idStagingAsset = Long.valueOf(uuidLookup);
						String optionText = null;
						if (null != idStagingAsset) {
							if (GlobalVal.ASSET_TAG_PRODUCT.equals(assetType)) {
								Object[][] paramLu = { {"idLu", idStagingAsset} };
								List<Map<String, Object>> luAnswer = this.getManagerDAO().selectAllNativeString("select PRODUCT_OFFERING_ID, PRODUCT_OFFERING_NAME, ID from STAGING_PRODUCT_OFFERING where PRODUCT_OFFERING_ID = :idLu", paramLu);
								if (!luAnswer.isEmpty()) {
									mapAns = luAnswer.get(0);
									BigInteger prodId = (BigInteger) mapAns.get("d0");
									mapAns.put("d0", String.valueOf(prodId));
								}
								optionText = (String) mapAns.get("d0");
								textAnswer = (String) mapAns.get("d1");
								idStagingAsset = ((BigInteger) mapAns.get("d2")).longValue();
								optionAnswerId = null;
							} else {
								optionText = lov;
								optionAnswerId = null;
							}
							
							insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
									latitude, longitude, mcc, mnc, lac, cellId, accuracy, idStagingAsset, optionText, listTaskD);
						}
					}
				}
			} else if (GlobalVal.ANSWER_TYPE_SCORING.equals(answerType) ) {
				String optionText = lov;
				optionAnswerId = null;
				insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, optionText, listTaskD);
			}
			else {
				//insert into Table TR_TASK_D
				this.insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, null, null, listTaskD);
			}

		}
		
		if (GlobalVal.SUBSYSTEM_MC.equals(userSubmit.getAmMssubsystem().getSubsystemName())) {
			if (flagRevisit) {
				taskCollData.setTrTaskH(trTaskH);
				taskCollData.setDtmCrt(new Date());
				taskCollData.setUsrCrt(auditContext.getCallerId());
				this.getManagerDAO().insert(taskCollData);
			}
			//update ke trtaskcollData
			else if (!flagRevisit && taskCollData != null) {
				taskCollData.setDtmUpd(new Date());
				taskCollData.setUsrUpd(auditContext.getCallerId());
				this.getManagerDAO().update(taskCollData);
			}
		}
		
		//Insert into Task D for Product 
		if (hasBrand && hasModel && hasGroup && hasType) {
			Long longIdAsset = new Long(idAsset);
			
			Object[][] paramPo = { {"idPo", longIdAsset} };
			List<Map<String, Object>> listAssetAns = this.getManagerDAO().selectAllNativeString(
					"select HIERARCHY_L1_CODE, ASSET_HIERARCHY_L1_NAME, "
					+ " HIERARCHY_L2_CODE, ASSET_HIERARCHY_L2_NAME, "
					+ " GROUP_TYPE, MASTER_CODE, MASTER_NAME "
					+ " from STAGING_PO_ASSET where id = :idPo", paramPo);
			
			Map<String, Object> mapAsset = new HashMap<String, Object>();
			if (!listAssetAns.isEmpty()) {
				mapAsset = listAssetAns.get(0);
			}
			
			for (Map.Entry<String, Object> mp : mapAssetProduct.entrySet()) {
				int i = (int) mp.getValue();
				MsQuestion msQuestion = (MsQuestion) msQuestions.get(i);
				String assetType = msQuestion.getMsAssettag().getAssetTagName();
				String answerType = msQuestion.getMsAnswertype().getCodeAnswerType();
				String uuidTaskD = taskDBean[i].getUuid_task_d();
				String optionAnswerId = null;
				String textAnswer = taskDBean[i].getText_answer();
				String questionText = taskDBean[i].getQuestion_label();
				String latitude = taskDBean[i].getLatitude();
				String longitude = taskDBean[i].getLongitude();
				String mcc = taskDBean[i].getMcc();
				String mnc = taskDBean[i].getMnc();
				String lac = taskDBean[i].getLac();
				String cellId = taskDBean[i].getCid();
				String gpsTime = taskDBean[i].getGps_time();
				String accuracy = taskDBean[i].getAccuracy();
				String image = taskDBean[i].getImage();
				String uuidLookup = taskDBean[i].getUuid_lookup();
				String optionText = null;
				
				if (GlobalVal.ASSET_TAG_BRAND.equals(assetType)) {
					optionText = (String) mapAsset.get("d0");
					textAnswer = (String) mapAsset.get("d1");
				} else if (GlobalVal.ASSET_TAG_MODEL.equals(assetType)) {
					optionText = (String) mapAsset.get("d2");
					textAnswer = (String) mapAsset.get("d3");
				} else if (GlobalVal.ASSET_TAG_GROUP_TYPE.equals(assetType)) {
					optionText = (String) mapAsset.get("d4");
					textAnswer = (String) mapAsset.get("d4");
				} else if (GlobalVal.ASSET_TAG_TYPE.equals(assetType)) {
					optionText = (String) mapAsset.get("d5");
					textAnswer = (String) mapAsset.get("d6");
				}
				insertTaskD(auditContext, trTaskH, msQuestion, uuidTaskD, optionAnswerId, questionText, textAnswer, 
						latitude, longitude, mcc, mnc, lac, cellId, accuracy, longIdAsset, optionText, listTaskD);
			}
		}
		
		return new SaveTaskDResult(isFinal, totalBayar);
	}
	
	private void insertTaskD(AuditContext auditContext, TrTaskH trTaskH, MsQuestion msQuestion, String uuidTaskD, String optionAnswerId,
			String questionText, String textAnswer, String latitude, String longitude, String mcc, String mnc, String lac,
			String cellId, String accuracy, Long idStagingAsset, String optionText, Map<Long, TrTaskD> listTaskD){
		DateTime startDateTime = new DateTime();
		LOG.info("Inserting task detail...");
		MsLov msLovByLovId =  null;
		TrTaskD taskD = null;
		if (!GlobalVal.ANSWER_TYPE_MULTIPLE.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType()) &&
				!GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
			taskD = listTaskD.get(msQuestion.getUuidQuestion());
		}
		if (StringUtils.isNotBlank(optionAnswerId)){
			msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(optionAnswerId));
		}
		if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())
				|| GlobalVal.ANSWER_TYPE_DATETIME.equals(msQuestion.getMsAnswertype().getCodeAnswerType())){
			if (textAnswer != null){
				DateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
				try {
					Date result = df.parse(textAnswer);
					if (GlobalVal.ANSWER_TYPE_DATE.equals(msQuestion.getMsAnswertype().getCodeAnswerType())) {
						textAnswer = (null != result) 
								? new SimpleDateFormat("dd/MM/yyyy").format(result)
								: null;
					} 
					else {
						textAnswer = (null != result) 
								? new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(result)
								: null;
					}
				} 
				catch (ParseException e) {
					LOG.error(e.getMessage(), e);
					e.printStackTrace();
				} 
			}
		}
		if (null != taskD) {
			
			taskD.setUsrUpd(auditContext.getCallerId());
			taskD.setDtmUpd(new Date());
			if (msLovByLovId != null) {
				taskD.setMsLovByLovId(msLovByLovId);
				taskD.setOptionText(msLovByLovId.getDescription());
			} else {
				taskD.setOptionText(optionText);
			}
			taskD.setQuestionText(questionText);
			taskD.setTextAnswer(textAnswer);
			taskD.setLatitude(checkEmptyBigdecimal(latitude));
			taskD.setLongitude(checkEmptyBigdecimal(longitude));
			if (taskD.getLatitude()!=null && taskD.getLongitude()!=null &&
					taskD.getLatitude().intValue() != 0 && taskD.getLongitude().intValue() != 0) {
				taskD.setIsGps("1");
			}
			taskD.setMcc(checkEmptyInteger(mcc));
			taskD.setMnc(checkEmptyInteger(mnc));
			taskD.setLac(checkEmptyInteger(lac));
			taskD.setCellId(checkEmptyInteger(cellId));
			taskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(taskD.getIsGps()) && taskD.getMcc() != null && taskD.getMnc() != null
					&& taskD.getLac() != null && taskD.getCellId() != null) {
				this.getLocationByCellId(taskD, auditContext);
			}
			taskD.setIdStagingAsset(idStagingAsset);
			this.getManagerDAO().update(taskD);
		} 
		else {				
			TrTaskD trTaskD = new TrTaskD();
			trTaskD.setUsrCrt(auditContext.getCallerId());
			trTaskD.setDtmCrt(new Date());
			trTaskD.setTrTaskH(trTaskH);
			trTaskD.setMsQuestion(msQuestion);
			if (msLovByLovId != null) {
				trTaskD.setMsLovByLovId(msLovByLovId);
				trTaskD.setOptionText(msLovByLovId.getDescription());
			} else {
				trTaskD.setOptionText(optionText);
			}
			trTaskD.setQuestionText(questionText);
			trTaskD.setTextAnswer(textAnswer);
			trTaskD.setLatitude(checkEmptyBigdecimal(latitude));
			trTaskD.setLongitude(checkEmptyBigdecimal(longitude));
			if (trTaskD.getLatitude()!=null && trTaskD.getLongitude()!=null &&
					trTaskD.getLatitude().intValue() != 0 && trTaskD.getLongitude().intValue() != 0) {
				trTaskD.setIsGps("1");
			}
			trTaskD.setMcc(checkEmptyInteger(mcc));
			trTaskD.setMnc(checkEmptyInteger(mnc));
			trTaskD.setLac(checkEmptyInteger(lac));
			trTaskD.setCellId(checkEmptyInteger(cellId));
			trTaskD.setAccuracy(checkEmptyInteger(accuracy));
			if (!"1".equals(trTaskD.getIsGps()) && trTaskD.getMcc() != null && trTaskD.getMnc() != null
					&& trTaskD.getLac() != null && trTaskD.getCellId() != null) {
				this.getLocationByCellId(trTaskD, auditContext);
			}
			trTaskD.setIdStagingAsset(idStagingAsset);
			this.getManagerDAO().insert(trTaskD);
			
		}
		LOG.info("End of insert task detail : {}", new DateTime().minusMillis(startDateTime.getMillisOfDay()).getMillisOfDay());
	}
	
	private Map<Integer, MsQuestion> getAllMsQuestion(SubmitTaskDBean[] taskDBean, MsFormhistory formHist){
		Map<Integer, MsQuestion> result = new HashMap<>();
		 
		for (int i=0; i<taskDBean.length; i++) {
			Object[][] prm = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHist.getUuidFormHistory())},
								{Restrictions.eq("msQuestion.uuidQuestion", Long.valueOf(taskDBean[i].getQuestion_id()))}};
			MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prm);
			
			MsQuestion msQuestion = new MsQuestion();
			msQuestion.setUuidQuestion(Long.parseLong(taskDBean[i].getQuestion_id()));
			msQuestion.setRefId(qset.getRefId());
			msQuestion.setQuestionLabel(qset.getQuestionLabel());
			msQuestion.setMsAnswertype(qset.getMsAnswertype());
			msQuestion.setMsAssettag(qset.getMsAssettag());
			msQuestion.setMsCollectiontag(qset.getMsCollectiontag());
			msQuestion.setMsOrdertag(qset.getMsOrdertag());
			msQuestion.setLovGroup(qset.getLovGroup());
			msQuestion.setIsVisible(qset.getIsVisible());
			msQuestion.setIsMandatory(qset.getIsMandatory());
			msQuestion.setIsReadonly(qset.getIsReadonly());
			msQuestion.setIsHolidayAllowed(qset.getIsHolidayAllowed());
			msQuestion.setMaxLength(qset.getMaxLength());
			msQuestion.setRegexPattern(qset.getRegexPattern());
			msQuestion.setAmMssubsystem(qset.getAmMssubsystem());
			msQuestion.setImgQlt(qset.getImgQlt());
			result.put(i, msQuestion);
		}
		return result;
	}
	
	private void saveGroupTaskSurvey(TrTaskH taskHSurvey, AuditContext auditContext) {
		MsGrouptask msGrouptask = new MsGrouptask();
		msGrouptask.setGroupTaskId(taskHSurvey.getUuidTaskH());
		msGrouptask.setTrTaskH(taskHSurvey);
		msGrouptask.setMsBranch(taskHSurvey.getMsBranch());
		msGrouptask.setUsrCrt(auditContext.getCallerId());
		msGrouptask.setDtmCrt(new Date());
		msGrouptask.setCustomerName(taskHSurvey.getCustomerName());
		msGrouptask.setApplNo(taskHSurvey.getApplNo());
		this.getManagerDAO().insert(msGrouptask);
	}

	@Override
	@Transactional(readOnly=true)
	public List<Map<String, String>> getLookupOffline(String choiceFilter, String product, AuditContext callerId) {
		List result = new ArrayList<>();
		boolean isJasa = "MGJMTR".equals(product) || "MGJMBL".equals(product)
				|| "FASDANMBL".equals(product) || "FASDANMTR".equals(product) || "MDLMBL".equals(product)
				|| "MDLMTR".equals(product) || "SLBINV".equals(product) || "SLBMBL".equals(product)
				|| "SLBMTR".equals(product) ? true : false;
				
		if(StringUtils.isNotBlank(choiceFilter)) {
			if(choiceFilter.contains("table")) {
				Query query = gson.fromJson(choiceFilter, Query.class);
				if(query!=null) {
					if(query.getFields()!=null && query.getFields().length>0) {
						StringBuilder scriptQuery = new StringBuilder("SELECT ");
						
						for(int i=0; i<query.getFields().length;i++) {
							scriptQuery.append(query.getFields()[i]);
							if(i != query.getFields().length-1) {
								scriptQuery.append(", ");
							}else {
								scriptQuery.append(" ");
							}
						}
						
						scriptQuery.append("FROM ");
						scriptQuery.append(query.getTable() +" WITH(NOLOCK) ");
						
						if(query.getJoin()!=null && query.getJoin().size()>0) {
							List<Join> joins = query.getJoin();
							for(int i=0; i<joins.size();i++) {
								if(isJasa) {
									if(joins.get(i).getTable().toUpperCase().contains("DEALER")) {
										continue;
									}
								}
								scriptQuery.append("JOIN"+" "+joins.get(i).getTable()+" WITH(NOLOCK) ");
								scriptQuery.append("ON "+joins.get(i).getOn()+" ");
							}
						}
						
						if(query.getConstraint()!=null && query.getConstraint().size()>0) {
							List<Constraint> constraints = query.getConstraint();
							scriptQuery.append("WHERE ");
							int idx = 0;
							for(int i=0; i<constraints.size();i++) {
								Constraint constraint = constraints.get(i);
								if(isJasa) {
									if(constraint.getColumn().toUpperCase().contains("DEALER")) {
										continue;
									}
								}
								if(idx!=0) {
									scriptQuery.append(" AND ");
								}
								idx++;
								scriptQuery.append(constraint.getColumn()+" ");
								if(StringUtils.isBlank(constraint.getOperator())) {
									scriptQuery.append("= ");
								}else {
									scriptQuery.append(constraint.getOperator()+" ");
								}
								if(StringUtils.isNotBlank(constraint.getValue())) {
									String values[] = constraint.getValue().split(",");
									if(values.length>1) {
										scriptQuery.append("(");
									}
									for(int j=0; j<values.length;j++) {
										scriptQuery.append("'"+values[j]+"'");
										if(j!=values.length-1) {
											scriptQuery.append(",");
										}
									}
									if(values.length>1) {
										scriptQuery.append(") ");
									}
								}else {
									scriptQuery.append(" '' ");
								}
								
								
							}
						}
						
						if(StringUtils.isNotBlank(query.getGroup())) {
							scriptQuery.append(" GROUP BY "+query.getGroup());
						}
						
						if(query.getOrder()!=null && query.getOrder().length>0) {
							scriptQuery.append(" ORDER BY ");
							String orders[] = query.getOrder();
							for(int i=0; i<orders.length;i++) {
								scriptQuery.append(orders[i]);
								if(i!=orders.length-1) {
									scriptQuery.append(",");
								}else {
									scriptQuery.append(" ");
								}
							}
						}
						
						List<Map<String, Object>> resultQuery = this.getManagerDAO().selectAllNativeString(scriptQuery.toString(), null);
						if(resultQuery!=null && !resultQuery.isEmpty()) {
							for(Map map:resultQuery) {
								Map<String,String> tmp = new HashMap<>();
								tmp.put("uuid",((BigInteger)map.get("d0")).toString());
								tmp.put("id", map.get("d1").toString());
								tmp.put("name", map.get("d2").toString());
								result.add(tmp);
							}
						}
					}
					
				}
			}
		}
		return result;
	}
	
	private List<TrTaskH> createTaskSurvey(TrTaskH trTaskH, String prodCat, AuditContext callerId) {
		AmMsuser amMsuser = null;
		this.getManagerDAO().fetch(trTaskH.getMsForm());
		AmMssubsystem amMssubsystem = this.commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		
		Object [][] paramMapForm = { {Restrictions.eq("productCategoryCode", prodCat)}, {Restrictions.eq("jenisAsset", trTaskH.getJenisAsset())} };
		TblProductCategory tblProductCategory = this.getManagerDAO().selectOne(TblProductCategory.class, paramMapForm);
		if (null == tblProductCategory) {
			throw new UploadTaskException("Product Category Code doesn't exist", Reason.ERROR_GENERATE);
		} else {
			if (0 == tblProductCategory.getIsMapped()) {
				throw new UploadTaskException("Product Category is not mapped", Reason.ERROR_GENERATE);
			}
			
			if (null == tblProductCategory.getMsForm()) {
				throw new UploadTaskException("Product Category have not been mapped with form", Reason.ERROR_GENERATE);
			} else {
				this.getManagerDAO().fetch(tblProductCategory.getMsForm());
				if ("0".equalsIgnoreCase(tblProductCategory.getMsForm().getIsActive())) {
					throw new UploadTaskException("Form is not active", Reason.ERROR_GENERATE);
				}
			}
		}
			
		Map distributionResult = taskDistributionLogic.poolingTaskDistribution(tblProductCategory.getTblProductCategoryId(),
				this.getNikByTask(trTaskH.getUuidTaskH()), trTaskH.getSubzipcode(), trTaskH.getDealerCode(), tblProductCategory.getJenisPembiayaan(), null, "1", null, null, "0", callerId);
		if(distributionResult.get("errMsg") != null) {
			if(StringUtils.isNotBlank(distributionResult.get("errMsg").toString())) {
				throw new UploadTaskException(distributionResult.get("errMsg").toString(), Reason.ERROR_GENERATE);
			}
		}
			
		List <BigInteger> listUser = (List<BigInteger>) distributionResult.get("listUser");
			
		if (listUser.isEmpty()) {
			this.getManagerDAO().fetch(trTaskH.getAmMsuser());
			amMsuser = trTaskH.getAmMsuser();
		} else {
			if (1 != listUser.size()) {
				listUser = taskDistributionLogic.assignByLoad(listUser, tblProductCategory.getJenisPembiayaan(), null, null, null, null, "0", callerId);
			}
			amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
					new Object[][] { {Restrictions.eq("uuidMsUser", listUser.get(0).longValue())} });
		}
		
		Object paramMap[][] = { {Restrictions.eq("msForm.uuidForm", Long.valueOf(trTaskH.getMsForm().getUuidForm()))},
				{Restrictions.eq("isActive", "1")}, {Restrictions.eq("productCategoryCode", tblProductCategory.getProduct())}, {Restrictions.eq("isPiloting", "1")} };
		MsMappingformH msMapFormH = this.getManagerDAO().selectOne(MsMappingformH.class, paramMap);
		
		//check group task
		Object paramsGroupTask[][] = {{"uuidTaskH", trTaskH.getUuidTaskH()}};
		BigInteger groupTaskIdLead = (BigInteger)this.getManagerDAO().selectOneNativeString("SELECT GROUP_TASK_ID FROM MS_GROUPTASK WITH(NOLOCK) WHERE UUID_TASK_H = :uuidTaskH ", paramsGroupTask);
		long groupTaskId = trTaskH.getUuidTaskH();
		if(groupTaskIdLead != null) {
			groupTaskId = groupTaskIdLead.longValue();
		}
		
		return taskServiceLogic.createTaskSurveyPiloting(callerId, amMsuser, trTaskH, amMssubsystem, msMapFormH, groupTaskId, null);
	}
	
	private List<TrTaskH> createTaskComplete(TrTaskH trTaskH, String prodCat, AuditContext callerId) {
		AmMsuser amMsuser = null;
		this.getManagerDAO().fetch(trTaskH.getMsForm());
		AmMssubsystem amMssubsystem = this.commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		
		Object [][] paramMapForm = { {Restrictions.eq("productCategoryCode", prodCat)}, {Restrictions.eq("jenisAsset", trTaskH.getJenisAsset())}};
		TblProductCategory tblProductCategory = this.getManagerDAO().selectOne(TblProductCategory.class, paramMapForm);
		if (null == tblProductCategory) {
			throw new UploadTaskException("Product Category Code doesn't exist", Reason.ERROR_GENERATE);
		} else {
			if (0 == tblProductCategory.getIsMapped()) {
				throw new UploadTaskException("Product Category is not mapped", Reason.ERROR_GENERATE);
			}
			
			if (null == tblProductCategory.getMsForm()) {
				throw new UploadTaskException("Product Category have not been mapped with form", Reason.ERROR_GENERATE);
			} else {
				this.getManagerDAO().fetch(tblProductCategory.getMsForm());
				if ("0".equalsIgnoreCase(tblProductCategory.getMsForm().getIsActive())) {
					throw new UploadTaskException("Form is not active", Reason.ERROR_GENERATE);
				}
			}
		}
		
		//JIKA MENGGUNAKAN TASK DISTRIBUSI
		/*Map distributionResult = taskDistributionLogic.poolingTaskDistribution(tblProductCategory.getTblProductCategoryId(),
				trTaskH.getSubzipcode(), trTaskH.getDealerCode(), tblProductCategory.getJenisPembiayaan(), null, "1", callerId);
		if(distributionResult.get("errMsg") != null) {
			if(StringUtils.isNotBlank(distributionResult.get("errMsg").toString())) {
				throw new UploadTaskException(distributionResult.get("errMsg").toString(), Reason.ERROR_GENERATE);
			}
		}
			
		List <BigInteger> listUser = (List<BigInteger>) distributionResult.get("listUser");
			
		if (0 == listUser.size()) {
			this.getManagerDAO().fetch(trTaskH.getAmMsuser());
			amMsuser = trTaskH.getAmMsuser();
		} else {
			if (1 != listUser.size()) {
				listUser = taskDistributionLogic.assignByLoad(listUser, tblProductCategory.getJenisPembiayaan(), null, null, callerId);
			}
			amMsuser = this.getManagerDAO().selectOne(AmMsuser.class,
					new Object[][] { {Restrictions.eq("uuidMsUser", listUser.get(0).longValue())} });
		}*/
		//END TASK DISTRIBUSI//
		
		//JIKA MENGGUNAKAN SELF ASSIGNMENT
		this.getManagerDAO().fetch(trTaskH.getAmMsuser());
		amMsuser = trTaskH.getAmMsuser();
		//END SELF ASSIGNMENT//
		
		MsQuestion question = commonLogic.retrieveQuestionByRefId(GlobalVal.STG_PRODUCT, callerId);
		Object params[][] = { {Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())}, {Restrictions.eq("msQuestion.uuidQuestion", question.getUuidQuestion())} };
		TrTaskD taskD = this.getManagerDAO().selectOne(TrTaskD.class, params);
		String product = "";
		if(taskD != null) {
			product = taskD.getMsLovByLovId().getCode();
		}
	
		Object paramMap[][] = { {Restrictions.eq("msForm.uuidForm", Long.valueOf(trTaskH.getMsForm().getUuidForm()))},
				{Restrictions.eq("isActive", "1")}, {Restrictions.eq("productCategoryCode", product)}, {Restrictions.eq("isPiloting", "3")} };
		MsMappingformH msMapFormH = this.getManagerDAO().selectOne(MsMappingformH.class, paramMap);
		
		//check group task
		Object paramsGroupTask[][] = {{"uuidTaskH", trTaskH.getUuidTaskH()}};
		BigInteger groupTaskIdLead = (BigInteger)this.getManagerDAO().selectOneNativeString("SELECT GROUP_TASK_ID FROM MS_GROUPTASK WITH(NOLOCK) WHERE UUID_TASK_H = :uuidTaskH ", paramsGroupTask);
		long groupTaskId = trTaskH.getUuidTaskH();
		if(groupTaskIdLead != null) {
			groupTaskId = groupTaskIdLead.longValue();
		}
		
		return taskServiceLogic.createTaskSurveyPiloting(callerId, amMsuser, trTaskH, amMssubsystem, msMapFormH, groupTaskId, null);
	}
	
	private void autoAssign(TrTaskH taskhOrder, AmMsuser loginBean, String procCategoryCode, AuditContext callerId, String kelurahan) {
		AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		Object[][] params = {
				{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_FAILED_INSTANT_APPROVAL) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidMsSub.getUuidMsSubsystem()) } };
		
		MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
		taskhOrder.setMsStatustask(msStatustask);
		taskhOrder.setDtmUpd(new Date());
		taskhOrder.setUsrUpd("SYSTEM");
		this.getManagerDAO().update(taskhOrder);
		//INSERT INTO CHECK HISTORY
		String codeProcess = GlobalVal.CODE_PROCESS_FAILED_INSANT_APPROVAL;
		insertTaskHistory(callerId, msStatustask, taskhOrder, taskhOrder.getNotes(), 
				codeProcess, loginBean.getFullName(), loginBean, null);
		this.getManagerDAO().update(taskhOrder);
		
		//CREATE 2 TASK SURVEY
		List<TrTaskH> listTaskSvy = new ArrayList<>();
		listTaskSvy = createTaskSurvey(taskhOrder, procCategoryCode, callerId);
		// UPDATE TASK FOTO & TEXT UNTUK DATA KELURAHANNYA
		for(int i=0; i<listTaskSvy.size(); i++) {
			TrTaskH taskBean = listTaskSvy.get(i);
			taskBean.setKelurahan(kelurahan);
			this.getManagerDAO().update(taskBean);
			
			taskServiceLogic.insertDefaultAnswerQuestionPilotingCae(taskBean, false, callerId);
		}
	}

	@Override
	public List<Map<String, Object>> listAnswer2(String uuidFormHistory, String uuidQuestionGroup,
			AuditContext callerId) {
		Object[][] params = new Object[][]{{"uuidFormHistory", uuidFormHistory}, {"uuidQuestionGroup", uuidQuestionGroup}};
		List<Map<String,Object>> listQuestions = this.getManagerDAO().selectAllNative("task.neworder.getQuestionsSetList2", params, null);
		List<Map<String,Object>> result = new ArrayList<>(listQuestions.size());
		for (int i = 0; i < listQuestions.size(); i++) {
            Map<String, Object> temp = listQuestions.get(i);
            temp.put("d12", temp.get("d12") != null ? temp.get("d12") : StringUtils.EMPTY);
            Object paramsIsRelevented[][] = {{"refId", (String)temp.get("d3")},{"uuidFormHistory", uuidFormHistory}};
            Integer qRelevented = (Integer)this.getManagerDAO().selectOneNative("task.neworder.isReleventedQuestion2", paramsIsRelevented);
            if(qRelevented > 0) {
            	 temp.put("d22", "1");
            }else {
            	temp.put("d22", "0");
            }
            result.add(temp);
		}
		
		List<Map<String,Object>> resultFinal = new ArrayList<>();
		if (!result.isEmpty()) {
			for (int i=0; i<result.size(); i++) {
				Map<String,Object> mapResult = result.get(i);
				String calculateString = StringUtils.EMPTY;
				if (mapResult.get("d12") != null) {
					calculateString = (String) mapResult.get("d12");
				}
				String calculateTemp = StringUtils.EMPTY;
				String calculate = StringUtils.EMPTY;
				String results = StringUtils.EMPTY;
				if (StringUtils.isNotBlank(calculateString)) {
					String[] calculateArr = calculateString.split("start");
					calculateArr = calculateArr[calculateArr.length-1].split("end");
					calculateTemp = calculateArr[0].replace("_var", StringUtils.EMPTY).replace("/*", StringUtils.EMPTY).replace("*/", StringUtils.EMPTY).replace(" ", StringUtils.EMPTY);
					calculateTemp = calculateTemp.replace("$", StringUtils.EMPTY).replace("result=", StringUtils.EMPTY);

					mapResult.put("d20", calculateTemp);
					calculate = calculateTemp.trim().replace("+", ",").replace("-", ",")
							.replace("/", ",").replace("%", ",").replace("*", ",").replace("(", "").replace(")", "");
					if (calculate.contains(",")) {
						String[] calculateArrTemp = calculate.split(",");
						
						int check=0;
						for(int j=0;j<calculateArrTemp.length;j++){
							if((!NumberUtils.isNumber(calculateArrTemp[j]) && (calculateArrTemp[j] != null))){
								check ++;
							}
						}
						String[] tempArr= new String[check];
						int tempTtl = 0;
						for (int k=0;k<calculateArrTemp.length;k++) {
							if ((!NumberUtils.isNumber(calculateArrTemp[k]) && (calculateArrTemp[k] != null))) {
								tempArr[k-tempTtl]=calculateArrTemp[k];
							} 
							else {
								tempTtl += 1;
							}
						}
						results = StringUtils.join(tempArr,",");
					}
					else {
						results = calculate;
					}
					mapResult.put("d21", results);
				}
				else {
					mapResult.put("d20", StringUtils.EMPTY);
					mapResult.put("d21", StringUtils.EMPTY);
				}
				resultFinal.add(mapResult);
			}
		}

		return result;
	}

	@Override
	public Map getCurrentUuidQuestionGroup(String uuidFormHistory, String uuidQuestionGroup,
			AuditContext callerId) {
		Map questionGroup = new HashMap<>();
		String nextUuidQuestionGroup = StringUtils.EMPTY;
		String nextQuestionGroupName = StringUtils.EMPTY;
		if(StringUtils.isBlank(uuidQuestionGroup)) {
			Object paramsQuestionGroup[][] = {{"uuidFormHistory", uuidFormHistory}};
			Object[] result = (Object[]) this.getManagerDAO().selectOneNativeString(" select top 1 msfqs.UUID_QUESTION_GROUP, msfqs.QUESTION_GROUP_OF_FORM_SEQ, msfqs.QUESTION_GROUP_LABEL "
					+ "from MS_FORMQUESTIONSET msfqs with(nolock) "
					+ "join MS_FORMHISTORY msfh with(nolock) on msfh.UUID_FORM_HISTORY = msfqs.UUID_FORM_HISTORY "
					+ "where msfh.UUID_FORM_HISTORY = :uuidFormHistory "
					+ "and QUESTION_GROUP_IS_ACTIVE='1' "
					+ "order by QUESTION_GROUP_OF_FORM_SEQ asc", paramsQuestionGroup);
			if(result!=null) {
				nextUuidQuestionGroup = ((BigInteger) result[0]).toString();
				nextQuestionGroupName = (String)result[2];
			}
		}else {
			Object paramSeqGroup[][] = {{"uuidFormHistory", uuidFormHistory}, {"uuidQuestionGroup", uuidQuestionGroup}};
			Integer lastSeqQuestionGroup = (Integer) this.getManagerDAO().selectOneNativeString("select top 1 msfqs.QUESTION_GROUP_OF_FORM_SEQ "
					+ "from MS_FORMQUESTIONSET msfqs with(nolock) "
					+ "join MS_FORMHISTORY msfh with(nolock) on msfh.UUID_FORM_HISTORY = msfqs.UUID_FORM_HISTORY "
					+ "where msfh.UUID_FORM_HISTORY = :uuidFormHistory and msfqs.UUID_QUESTION_GROUP = :uuidQuestionGroup ", paramSeqGroup);
			
			Object paramsQuestionGroup[][] = {{"uuidFormHistory", uuidFormHistory}, {"lastSeqQuestionGroup", lastSeqQuestionGroup}};
			Object[] result = (Object[]) this.getManagerDAO().selectOneNativeString(" select top 1 msfqs.UUID_QUESTION_GROUP, msfqs.QUESTION_GROUP_OF_FORM_SEQ, msfqs.QUESTION_GROUP_LABEL "
					+ "from MS_FORMQUESTIONSET msfqs with(nolock) "
					+ "join MS_FORMHISTORY msfh with(nolock) on msfh.UUID_FORM_HISTORY = msfqs.UUID_FORM_HISTORY "
					+ "where msfh.UUID_FORM_HISTORY = :uuidFormHistory "
					+ "and QUESTION_GROUP_IS_ACTIVE='1' "
					+ "and QUESTION_GROUP_OF_FORM_SEQ > :lastSeqQuestionGroup "
					+ "order by QUESTION_GROUP_OF_FORM_SEQ asc", paramsQuestionGroup);
			if(result!=null) {
				nextUuidQuestionGroup = ((BigInteger) result[0]).toString();
				nextQuestionGroupName = (String)result[2];
			}
		}
		
		questionGroup.put("uuid", nextUuidQuestionGroup);
		questionGroup.put("name", nextQuestionGroupName);
		return questionGroup;
	}

	@Override
	@Transactional
	public Map submitPage(String submitPage, String data, long uuidUser, String uuidForm, String isSelfAssignment, String uuidTaskH, 
			boolean lastQuestionGroup, String uuidFormHistory, String isValidateDukcapil, String isValidTelecheck, String customerRating, String isRO, String idHistCust, AuditContext callerId) {
		TrTaskH taskhOrder = null;
		String procCategoryCode = null;
		AmMsuser loginBean = this.getManagerDAO().selectOne(AmMsuser.class, uuidUser);
		Map result = new HashMap<>();
		data = fixCopyVal(data, callerId);
		String dataKelurahan = getKelurahanFromSubmitPage(data, callerId);
		if("1".equals(submitPage)) {
			//Start Validasi Biometric, umur, cust. rating, dan tele owner
			//Start create task lead
			TaskDukcapilBean taskDukcapilBean = this.validasiFormLead(data, uuidUser, false, callerId);
			Map mapValidateDukcapil = this.validateDukcapil(taskDukcapilBean, "0", false, idHistCust, callerId);
			taskDukcapilBean.setIsMatchDukcapil(String.valueOf(isValidateDukcapil));
			taskDukcapilBean.setIdHistCust(idHistCust);
			Map mapTask = createTaskLead(taskDukcapilBean, data, uuidForm, loginBean, isSelfAssignment, lastQuestionGroup, callerId);
			
			taskhOrder = (TrTaskH) mapTask.get("d0");
			SubmitTaskDBean[] taskDBean = (SubmitTaskDBean[]) mapTask.get("d1");
			
			Object[][] params2 = {
					{ "uuidTaskH", taskhOrder.getUuidTaskH()},
					{ "assetTag", GlobalVal.ASSET_TAG_PRODUCT },{"konvenSyariah", taskhOrder.getAmMsuser().getMsBranch().getKonvenSyariah()} };
			procCategoryCode = (String) this.getManagerDAO().selectOneNativeString("select DISTINCT PRODUCT_CATEGORY_CODE from TBL_PRODUCT_CATEGORY tbc with (nolock) " + 
					"join MS_LOV ml with (nolock) on tbc.PRODUCT = ml.CODE " + 
					"join TR_TASK_D ttd with (nolock) on ttd.LOV_ID = ml.UUID_LOV " + 
					"join MS_FORMQUESTIONSET mq with (nolock) on mq.UUID_QUESTION = ttd.UUID_QUESTION " + 
					"join MS_ASSETTAG ma with (nolock) on ma.UUID_ASSET_TAG = mq.UUID_ASSET_TAG " +
					"where ma.ASSET_TAG_NAME = :assetTag and ttd.UUID_TASK_H = :uuidTaskH " + 
					"and tbc.KONVEN_SYARIAH = :konvenSyariah ", params2);
			
			boolean validateConfins = false;
			boolean isTelecheckId = false;
			Map resPage1 = new HashMap();
			
			if("1".equalsIgnoreCase(isValidateDukcapil)) {
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "Dukcapil Match, Lanjut proses Instant Approval") );
				taskhOrder.setIsMatchDukcapil("1");
				this.getManagerDAO().update(taskhOrder);
				result.put("msg", "Dukcapil Match, Lanjut proses Instant Approval");
				result.put("code", 0);
				//return result;
			}
			
			if("0".equalsIgnoreCase(isValidateDukcapil)) {
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - Dukcapil Not Match, Order stop / drop") );
				taskhOrder.setIsMatchDukcapil("0");
				AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
				Object[][] params = {
						{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_REJECTED) },
						{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidMsSub.getUuidMsSubsystem()) } };
				
				MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
				taskhOrder.setMsStatustask(msStatustask);
				taskhOrder.setDtmUpd(new Date());
				taskhOrder.setUsrUpd("SYSTEM");
				this.getManagerDAO().update(taskhOrder);
				//INSERT INTO CHECK HISTORY
				String codeProcess = GlobalVal.CODE_PROCESS_REJECTED;
				insertTaskHistory(callerId, msStatustask, taskhOrder, taskhOrder.getNotes(), 
						codeProcess, loginBean.getFullName(), loginBean, null);
				
				result.put("msg", "IA failed - Dukcapil Not Match, Order stop / drop");
				result.put("code", 4);
				return result;
			}
			if( "0".equalsIgnoreCase(isValidTelecheck)) {
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - Phone number not active, Order stop / drop") );
				AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
				Object[][] params = {
						{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_REJECTED) },
						{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidMsSub.getUuidMsSubsystem()) } };
				
				MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
				taskhOrder.setMsStatustask(msStatustask);
				taskhOrder.setDtmUpd(new Date());
				taskhOrder.setUsrUpd("SYSTEM");
				this.getManagerDAO().update(taskhOrder);
				//INSERT INTO CHECK HISTORY
				String codeProcess = GlobalVal.CODE_PROCESS_REJECTED;
				insertTaskHistory(callerId, msStatustask, taskhOrder, taskhOrder.getNotes(), 
						codeProcess, loginBean.getFullName(), loginBean, null);
				
				result.put("msg", "IA failed - Phone number not active, Order stop / drop");
				result.put("code", 4);
				return result;
			}
			if("4".equalsIgnoreCase(isValidTelecheck)) {
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - Cannot connect tele check,Task Survey sudah diturunkan ke mobile survey") );
				this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
				result.put("msg", "IA failed - Cannot connect tele check,Task Survey sudah diturunkan ke mobile survey");
				result.put("code", 4);
				return result;
			}
			if(!"0".equalsIgnoreCase(isValidTelecheck) && "2".equalsIgnoreCase(isValidateDukcapil)) {
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - Dukcapil Not Found, Task Survey sudah diturunkan ke mobile survey") );
				taskhOrder.setIsMatchDukcapil("0");
				this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
				result.put("msg", "IA failed - Dukcapil Not Found, Task Survey sudah diturunkan ke mobile survey");
				result.put("code", 4);
				return result;
			}
			SubmitPage1Request submitData = this.validateData(taskhOrder, taskDBean, callerId);
			try {
				isTelecheckId = intFormLogic.requestTeleowner(submitData.getNik(), submitData.getMobilePhone(), false, callerId);
			}catch (Exception e) {
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - Cannot connect tele check,Task Survey sudah diturunkan ke mobile survey") );
				this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
				result.put("msg", "IA failed - Cannot connect tele check,Task Survey sudah diturunkan ke mobile survey");
				result.put("code", 4);
				return result;
			}
			 
			if(isTelecheckId  && !"2".equalsIgnoreCase(isValidateDukcapil)) {
				resPage1 = this.intFormLogic.validatePage1(submitData, callerId);
				validateConfins = (boolean) resPage1.get("validateConfins");
				//End  Validasi Biometric, umur, cust. rating, dan tele ownerr
			}else {
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - ID not match with phone owner, Task survey sudah diturunkan ke mobile survey") );
				this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
				result.put("msg", "IA failed - ID not match with phone owner, Task survey sudah diturunkan ke mobile survey");
				result.put("code", 4);
				return result;
			}
			
			if(validateConfins) {
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA Layer 1 passed - Lanjut IA Layer 2") );
				this.getManagerDAO().update(taskhOrder);
				
				int newCust = Integer.parseInt((String) resPage1.get("isRO")) ^ 1;
				
				Object[][] prmIsPassCek = { {"prodCategoryCode", procCategoryCode},{"newCust", newCust} };
				int isPassCek = (int) this.getManagerDAO().selectOneNativeString("DECLARE @PASSCEK int\r\n" + 
						"select TOP(1) @PASSCEK = PASSCEK from TBL_MAP_PASSCEK where PRODUCT_CATEGORY_CODE = :prodCategoryCode and NEW_CUST = :newCust\r\n" + 
						"\r\n" + 
						"if @PASSCEK is null\r\n" + 
						"select 1\r\n" + 
						"else\r\n" + 
						"select @PASSCEK", prmIsPassCek);
				
				//Check Mandatory NPWP
				AmGeneralsetting gensetNPWP = this.commonLogic.retrieveGs(GlobalKey.GENERALSETTING_IS_MANDATORY_NPWP_IA, callerId);

				result.put("isPassCek", isPassCek);
				result.put("isMandatoryNPWP", gensetNPWP.getGsValue());
				result.put("isRO", resPage1.get("isRO"));
				result.put("customerRating", resPage1.get("customerRating"));
				result.put("uuidTaskH", taskhOrder.getUuidTaskH());
				result.put("msg", "IA Layer 1 passed - Lanjut IA Layer 2");
				result.put("code", 0);
				return result;
			}else {
				String isPassedBiometric = (String) resPage1.get("isPassedBiometric"); 
				String isFailedToConnect = (String) resPage1.get("isFailedToConnect");
				String isPass = (String) resPage1.get("isPass");
				String msg = (String) resPage1.get("message");
				if("2".equals(isPass)) {
					taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "Not Passed Validation Criteria") );
					AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
					Object[][] params = {
							{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_REJECTED) },
							{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidMsSub.getUuidMsSubsystem()) } };
					
					MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
					taskhOrder.setMsStatustask(msStatustask);
					taskhOrder.setDtmUpd(new Date());
					taskhOrder.setUsrUpd("SYSTEM");
					this.getManagerDAO().update(taskhOrder);
					//INSERT INTO CHECK HISTORY
					String codeProcess = GlobalVal.CODE_PROCESS_REJECTED;
					insertTaskHistory(callerId, msStatustask, taskhOrder, taskhOrder.getNotes(), 
							codeProcess, loginBean.getFullName(), loginBean, null);
					
					result.put("msg", "Not Passed Validation Criteria");
					result.put("code", 4);
					return result;
				}
				if("1".equals(isFailedToConnect)) {
					taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), msg) );
					this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
					result.put("msg", msg);
					result.put("code", 4);
					return result;
				}
				if(!"1".equals(isPassedBiometric)) {
					taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - Matching  NIK  dan  Foto tidak  sesuai, order stop / drop") );
					AmMssubsystem uuidMsSub = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
					Object[][] params = {
							{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_REJECTED) },
							{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidMsSub.getUuidMsSubsystem()) } };
					
					MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
					taskhOrder.setMsStatustask(msStatustask);
					taskhOrder.setDtmUpd(new Date());
					taskhOrder.setUsrUpd("SYSTEM");
					this.getManagerDAO().update(taskhOrder);
					//INSERT INTO CHECK HISTORY
					String codeProcess = GlobalVal.CODE_PROCESS_REJECTED;
					insertTaskHistory(callerId, msStatustask, taskhOrder, taskhOrder.getNotes(), 
							codeProcess, loginBean.getFullName(), loginBean, null);
					
					result.put("msg", "IA failed - Matching  NIK  dan  Foto tidak  sesuai, order stop / drop");
					result.put("code", 4);
					return result;
				}
				
				taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - Not pass risk criteria,Task Survey sudah diturunkan ke mobile survey") );
				this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
				result.put("msg", "IA failed - Not pass risk criteria,Task Survey sudah diturunkan ke mobile survey");
				result.put("code", 4);
				return result;
			}
		}else if("2".equals(submitPage)||"3".equals(submitPage)){
			if(StringUtils.isNotBlank(uuidTaskH)) {
				taskhOrder = this.getManagerDAO().selectOne(TrTaskH.class, Long.parseLong(uuidTaskH));
				
				Object[][] params2 = {
						{ "uuidTaskH", taskhOrder.getUuidTaskH()},
						{ "assetTag", GlobalVal.ASSET_TAG_PRODUCT },{"konvenSyariah", taskhOrder.getAmMsuser().getMsBranch().getKonvenSyariah()} };
				procCategoryCode = (String) this.getManagerDAO().selectOneNativeString("select DISTINCT PRODUCT_CATEGORY_CODE from TBL_PRODUCT_CATEGORY tbc with (nolock) " + 
						"join MS_LOV ml with (nolock) on tbc.PRODUCT = ml.CODE " + 
						"join TR_TASK_D ttd with (nolock) on ttd.LOV_ID = ml.UUID_LOV " + 
						"join MS_FORMQUESTIONSET mq with (nolock) on mq.UUID_QUESTION = ttd.UUID_QUESTION " + 
						"join MS_ASSETTAG ma with (nolock) on ma.UUID_ASSET_TAG = mq.UUID_ASSET_TAG " +
						"where ma.ASSET_TAG_NAME = :assetTag and ttd.UUID_TASK_H = :uuidTaskH " + 
						"and tbc.KONVEN_SYARIAH = :konvenSyariah ", params2);
			}
			if(taskhOrder != null) {
				String isTaskSurvey = "0";
				String isInstant = "0";
				String is30Menit = "0";
				String isFailedToConnect = "0";
				if(StringUtils.isNotBlank(data)) {
					//set default answer
					String [] answers = data.split("#");
					SubmitTaskDBean[] taskDBeanList = generateSubmitTaskDBean(data, callerId, lastQuestionGroup);
					if("2".equals(submitPage)) {
						SubmitPage2Request submitData = this.validateDataPage2(taskhOrder, taskDBeanList, callerId);
						submitData.setIsRO(isRO);
						submitData.setCustomerRating(customerRating);
						
						Map resPage2 = this.intFormLogic.validatePage2(submitData, callerId);
						isTaskSurvey = (String) resPage2.get("isTaskSurvey");
						isInstant = (String) resPage2.get("isInstant");
						is30Menit = (String) resPage2.get("is30Menit");
						isFailedToConnect = (String) resPage2.get("isFailedToConnect");
					}

					if(taskDBeanList != null) {
						MsFormhistory formHist = this.getManagerDAO().selectOne(MsFormhistory.class, Long.parseLong(uuidFormHistory));
						Map<Integer, MsQuestion> msQuestions = this.getAllMsQuestion(taskDBeanList, formHist);
						
						ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(callerId);
				        Path imagePath = (ImageStorageLocation.FILE_SYSTEM == isl)
				                ? this.imageStorageLogic.retrieveGsImageFileSystemPath(callerId) : null;
						
			            SaveTaskDResult saveResult = null;
			            boolean saveAsJson = PropertiesHelper.isTaskDJson();
			    		if (saveAsJson) {
			    			saveResult = this.saveTaskDIntoJson(taskDBeanList, taskhOrder, loginBean, false, null, true, msQuestions, isl, imagePath, callerId);
			    		}
			    		else {			
			    			//2016-09-16 SUM - select all taskD first instead of select one for all answers
			    			Map<Long, TrTaskD> listTaskD = this.listTaskDAsMap(String.valueOf(taskhOrder.getUuidTaskH()));
			    		    saveResult = this.saveTaskDIntoRow(taskDBeanList, taskhOrder, loginBean, false, null, true, msQuestions, listTaskD, isl, imagePath, callerId);
			    		}
					}
					
					if("3".equals(submitPage)) {
						if(StringUtils.isNotBlank(uuidTaskH)) {
							taskhOrder = this.getManagerDAO().selectOne(TrTaskH.class, Long.parseLong(uuidTaskH));
						}
						
						//Check DSR
						AmGeneralsetting gensetDSR = this.commonLogic.retrieveGs(GlobalKey.GENERALSETTING_MAX_DSR_PASSED, callerId);
						if (null != gensetDSR) {
							Object[][] params2 = {
									{ "uuidTaskH", taskhOrder.getUuidTaskH()},
									{ "assetTag", GlobalVal.ASSET_TAG_PRODUCT },{"konvenSyariah", taskhOrder.getAmMsuser().getMsBranch().getKonvenSyariah()} };
							procCategoryCode = (String) this.getManagerDAO().selectOneNativeString("select DISTINCT PRODUCT_CATEGORY_CODE from TBL_PRODUCT_CATEGORY tbc with (nolock) " + 
									"join MS_LOV ml with (nolock) on tbc.PRODUCT = ml.CODE " + 
									"join TR_TASK_D ttd with (nolock) on ttd.LOV_ID = ml.UUID_LOV " + 
									"join MS_FORMQUESTIONSET mq with (nolock) on mq.UUID_QUESTION = ttd.UUID_QUESTION " + 
									"join MS_ASSETTAG ma with (nolock) on ma.UUID_ASSET_TAG = mq.UUID_ASSET_TAG " +
									"where ma.ASSET_TAG_NAME = :assetTag and ttd.UUID_TASK_H = :uuidTaskH " + 
									"and tbc.KONVEN_SYARIAH = :konvenSyariah ", params2);
							
							Object[][] paramQuestionDSR = { { Restrictions.eq("refId", GlobalVal.STG_DSR) }};
							MsQuestion questionDSR = this.getManagerDAO().selectOne(MsQuestion.class, paramQuestionDSR);
							
							Object[][] paramTaskDDSR = { { Restrictions.eq("trTaskH.uuidTaskH", taskhOrder.getUuidTaskH()) },
									{Restrictions.eq("msQuestion.uuidQuestion", questionDSR.getUuidQuestion()) }};
							TrTaskD taskDDSR = this.getManagerDAO().selectOne(TrTaskD.class, paramTaskDDSR);
							
							if(Double.parseDouble(taskDDSR.getTextAnswer()) > Double.parseDouble(gensetDSR.getGsValue()))
							{
								taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - Not pass risk criteria,Task Survey sudah diturunkan ke mobile survey") );
								this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
								result.put("msg", "IA failed - Not pass risk criteria,Task Survey sudah diturunkan ke mobile survey");
								result.put("code", "999");
								return result;
							}
						}
					}
				}
				
				//commit wf 
				this.getManagerDAO().fetch(taskhOrder.getMsStatustask());
				MsStatustask msStatustask = taskhOrder.getMsStatustask();
				
				if(!GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(taskhOrder.getMsStatustask().getStatusCode())){
					AmMssubsystem subsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
					long uuidProcess = this.getUuidProcess(taskhOrder, subsystem);
					msStatustask = commitWfAndUpdateTaskH(taskhOrder, uuidProcess, subsystem, 0);
					//get status from workflow
					Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_SUBMITTED) } };
					MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
					taskhOrder.setMsStatusmobile(msm);
				} else {
					//generate PO
					Map<String, Object> resPrintPo = taskServiceLogic.printPO(String.valueOf(taskhOrder.getUuidTaskH()),callerId);
					String idPo = "";
					try {
						TrTaskH trthPo = (TrTaskH) resPrintPo.get("idPo");
						idPo = getIdPo(trthPo.getUuidTaskH());
					} catch (Exception e) {
						e.printStackTrace();
					}
					
					//start create task foto completed
					List<TrTaskH> listTaskSvy = new ArrayList<>();
					listTaskSvy = createTaskComplete(taskhOrder, procCategoryCode, callerId);
					// UPDATE TASK COMPLETED UNTUK DATA KELURAHANNYA
					for(int i=0; i<listTaskSvy.size(); i++) {
						TrTaskH taskBean = listTaskSvy.get(i);
						taskBean.setKelurahan(dataKelurahan);
						this.getManagerDAO().update(taskBean);
					}
					//end create task foto completed
					
					LOG.info("Instant Approval Appl, idPo : " + idPo);
					result.put("idPo", idPo);
					result.put("msg", "Instant Approval Success, Task Completed Telah Terbentuk di Mobile Survey");
					result.put("code", "0");
					return result;
				}
				
//				boolean resultSuccess = false;
				//Start Submit ke confins
				
				//End Submit ke confins
//				if(resultSuccess) {
				try {
					if("1".equals(isFailedToConnect)) {
						this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
						result.put("msg", "Failed to connect to Wise. Generate Task Survey.");
						result.put("code", "4");
						return result;
					}
					else if("1".equalsIgnoreCase(is30Menit)) {
//					else if("1".equalsIgnoreCase("1")) {
						//start submit ke confins ke menu preparation
						String preparation = StringUtils.EMPTY;
						try {
							preparation = this.intFormLogic.submitNap(callerId,taskhOrder.getTaskId(), "2");
						}catch (Exception e) {
							e.printStackTrace();
							preparation = "Failed";
						}
						//end submit ke confins ke menu preparation
						String message = StringUtils.EMPTY;
						taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "IA failed - Not pass risk criteria,Lanjut data preparation product 30 menit") );
						this.getManagerDAO().update(taskhOrder);
						if("Failed".equalsIgnoreCase(preparation)) {
							message = "Your Application Will Be Send To Preparation Later";
							//INSERT INTO CHECK HISTORY
							Object[][] param = { { Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_PENDING_PREPARATION) } };
							
							MsStatustask statustask = this.getManagerDAO().selectOne(MsStatustask.class, param);
							taskhOrder.setMsStatustask(statustask);
							this.getManagerDAO().update(taskhOrder);
							
							String codeProcess = GlobalVal.CODE_PROCESS_PENDING_PREPARATION;
							insertTaskHistory(callerId, statustask, taskhOrder, "Pending Preparation", 
									codeProcess, loginBean.getFullName(), loginBean, null);
						}else {
							String messageErr = StringUtils.EMPTY;
							messageErr = "IA failed - Not pass risk criteria,Lanjut data preparation product 30 menit";
							//INSERT INTO CHECK HISTORY
							String codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
							insertTaskHistory(callerId, msStatustask, taskhOrder, "Your 30 Minutes Application Already Sent To Preparation", 
									codeProcess, loginBean.getFullName(), loginBean, null);

							result.put("msg", messageErr);
							result.put("code", "111");
							result.put("flag", "30");
							//return result;
						}
						
						result.put("msg", message);
						result.put("flag", "30");
						
					}else if("1".equalsIgnoreCase(isInstant)){
						taskhOrder.setNotes( appendNotes(taskhOrder.getNotes(), "Approved - Principal Instant Approval") );
						this.getManagerDAO().update(taskhOrder);
						 
						//INSERT INTO CHECK HISTORY
						String codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;
						insertTaskHistory(callerId, msStatustask, taskhOrder, "Aplikasi Instant Approval", 
								codeProcess, loginBean.getFullName(), loginBean, null);
						
						result.put("msg", "Aplikasi Instant Approval");
						result.put("flag", "IA");
						result.put("uuidTaskH", taskhOrder.getUuidTaskH());
						result.put("code", 0);
						return result;
					}else if("1".equalsIgnoreCase(isTaskSurvey)) {
						this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
						result.put("msg", "IA failed - Not pass risk criteria,Task Survey sudah diturunkan ke mobile survey");
						result.put("flag", "AA");
						result.put("code", "111");
						//return result;
					}else {
						this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
						//result.put("msg", "IA failed - Cannot connect WISe,Task Survey sudah diturunkan ke mobile survey");
						result.put("msg", "IA failed - Error is30Menit, isInstant, & isTaskSurvey is 0");
						
						result.put("code", "4");
						return result;
					}
				}catch (Exception e) {
					e.printStackTrace(); 
					//result.put("msg", "IA failed - Cannot connect WISe,Task Survey sudah diturunkan ke mobile survey");
					result.put("msg", "IA failed - Error When Submit Page, Error Message=" + e.getMessage());
					result.put("code", "4");
					return result;
				}
			}else{
				throw new SubmitTaskException("Task Not Found!", com.adins.mss.exceptions.SubmitTaskException.Reason.ERROR_SUBMIT);
			}
		}else {
			this.autoAssign(taskhOrder, loginBean, procCategoryCode, callerId, dataKelurahan);
			//result.put("msg", "IA failed - Cannot connect WISe,Task Survey sudah diturunkan ke mobile survey");
			result.put("msg", "IA failed - Invalid Submit Page, SubmitPage=" + submitPage);
			result.put("code", "4");
			return result;
		}
		result.put("uuidTaskH", taskhOrder.getUuidTaskH());
		result.put("code", 0);
		return result;
	}
	
	public String getIdPo(Long uuidTaskH) {
		String idPo = "";
		try {
			Object[][] prmTpo = { {"uuidTaskH", uuidTaskH} };
			BigInteger tpo = (BigInteger) this.getManagerDAO().selectOneNativeString("select ID_PO from TBL_PO (nolock) where UUID_TASK_H = :uuidTaskH ", prmTpo);
			idPo = String.valueOf(tpo);
		} catch (Exception e) {
			e.printStackTrace();
			idPo = "";
		}
		return idPo;
	}
	
	private SubmitPage1Request validateData(TrTaskH taskhOrder, SubmitTaskDBean[] taskDBean, AuditContext callerId) {
		
		SubmitPage1Request submitData = new SubmitPage1Request();
		for (int i=0; i < taskDBean.length; i++) {
			String idQuest = taskDBean[i].getQuestion_id();
			Object[][] params = { { Restrictions.eq("uuidQuestion", Long.valueOf(idQuest)) }};
			MsQuestion msQuestion = this.getManagerDAO().selectOne(MsQuestion.class, params);

			if(GlobalVal.STG_CUST_NAME.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setNamaKtp(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_NIK.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setNik(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_BIRTH_DT.equalsIgnoreCase(msQuestion.getRefId())) {
				SimpleDateFormat df = new SimpleDateFormat("ddMMyyyyHHmmss");
				SimpleDateFormat df2 = new SimpleDateFormat("dd/MM/yyyy");
				try {
					Date date = df.parse(taskDBean[i].getText_answer());
					taskDBean[i].setText_answer(df2.format(date));
				} 
				catch (ParseException e) {
					LOG.error(e.getMessage(), e);
				}
				submitData.setTglLahirPemohon(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_BIRTH_PLACE.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setTmpLahirPemohon(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_NO_HANDPHONE.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setMobilePhone(taskDBean[i].getText_answer());
			} else if(GlobalVal.OCR_PROVINSI.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setProvinsi(taskDBean[i].getLov());
			} else if(GlobalVal.OCR_KOTA.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setKota(taskDBean[i].getLov());
			} else if(GlobalVal.OCR_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setKelurahan(taskDBean[i].getLov());
			} else if(GlobalVal.OCR_KECAMATAN.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setKecamatan(taskDBean[i].getLov());
			} else if(GlobalVal.OCR_ALAMAT_LEGAL.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setAddress(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_PRODUCT.equalsIgnoreCase(msQuestion.getRefId())) {
				Object[][] params2 = {
						{ "product", taskDBean[i].getLov()}, {"konvenSyariah", taskhOrder.getAmMsuser().getMsBranch().getKonvenSyariah()} };
				String procCategoryCode = (String) this.getManagerDAO().selectOneNativeString("select DISTINCT PRODUCT_CATEGORY_CODE from TBL_PRODUCT_CATEGORY with (nolock) " + 
						"where PRODUCT = :product and KONVEN_SYARIAH = :konvenSyariah", params2);
				submitData.setProduct(procCategoryCode);
			} else if(GlobalVal.OCR_RT.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setRt(taskDBean[i].getText_answer());
			} else if(GlobalVal.OCR_RW.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setRw(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_LEGAL_ZIPCODE.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setZipcode(taskDBean[i].getLov());
			} else if(GlobalVal.STG_DEALERCODE_LEAD.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.STG_DEALER_CODE.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setDealer(taskDBean[i].getLov());
			} else if (GlobalVal.STG_POS_DEALER.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setPosDealer(taskDBean[i].getText_answer());
			} else if ( GlobalVal.STG_SALES_DEALER.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setSalesPerson(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_FOTO_PEMOHON.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setImageSelfie(taskDBean[i].getImage());
				
				Object[][] paramLob = {{ "uuidTaskH", taskhOrder.getUuidTaskH()}, { "refId", "STG_FOTO_PEMOHON" } };
				
				BigInteger uuidLob = (BigInteger) this.getManagerDAO().selectOneNativeString("SELECT ttb.UUID_TASK_DETAIL_LOB from TR_TASKDETAILLOB ttb WITH (nolock) " + 
						"join MS_QUESTION mq WITH (nolock) on ttb.QUESTION_ID = mq.UUID_QUESTION " + 
						"where UUID_TASK_H = :uuidTaskH and mq.REF_ID = :refId", paramLob);
				submitData.setFileName(uuidLob.toString() + ".jpg");
			}
			
		}
		
		submitData.setMobileTaskCode(taskhOrder.getApplNo());
		submitData.setOfficeCode(taskhOrder.getMsBranch().getBranchCode());
		
		return submitData;
	}

	private SubmitPage2Request validateDataPage2(TrTaskH taskhOrder, SubmitTaskDBean[] taskDBean, AuditContext callerId) {
		
		SubmitPage2Request submitData = new SubmitPage2Request();
		submitData.setNpwp("");
		
		double ntf = 0;
		for (int i=0; i < taskDBean.length; i++) {
			String idQuest = taskDBean[i].getQuestion_id();
			Object[][] params = { { Restrictions.eq("uuidQuestion", Long.valueOf(idQuest)) }};
			MsQuestion msQuestion = this.getManagerDAO().selectOne(MsQuestion.class, params);
			
			if (GlobalVal.STG_BRAND.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setBrand(taskDBean[i].getText_answer());
			} else if (GlobalVal.STG_ASSET.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setAsset(taskDBean[i].getText_answer());
			} else if (GlobalVal.STG_TYPE.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setType(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_OTR.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setOtr(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_DP_AMOUNT.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setDpAmount(taskDBean[i].getText_answer());
				submitData.setLtvPercentage("0");
				submitData.setLtvAmount("0");
				submitData.setNtfAmt("0");
			} else if(GlobalVal.STG_DP_PERCENTAGE.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setDpPercentage(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_NPWP.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setNpwp(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_PENGHASILAN.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setPenghasilan(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_ANGSURAN.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setAngsuran(taskDBean[i].getText_answer());
			}else if(GlobalVal.STG_STATUS_RUMAH.equalsIgnoreCase(msQuestion.getRefId())) {
				String[][] paramHO = {{"id", taskDBean[i].getOption_answer_id()}};
				String desc = (String)this.getManagerDAO().selectOneNativeString("SELECT TOP 1 DESCRIPTION FROM MS_LOV WITH(NOLOCK) WHERE UUID_LOV =  :id", paramHO);
				submitData.setStatusKepemilikanRumah(desc);
			} else if(GlobalVal.STG_TAHUN_KENDARAAN.equalsIgnoreCase(msQuestion.getRefId())) {
				submitData.setTahunKendaraan(taskDBean[i].getText_answer());
			} else if(GlobalVal.STG_PRODUCT_OFFERING.equalsIgnoreCase(msQuestion.getRefId())) {
				String[][] paramPO = {{"id", taskDBean[i].getLov()}};
				String code = (String)this.getManagerDAO().selectOneNativeString("SELECT TOP 1 PRODUCT_OFFERING_CODE FROM STAGING_PRODUCT_OFFERING WITH(NOLOCK) WHERE PRODUCT_OFFERING_ID =  :id", paramPO);
				submitData.setProductOffering(code);  
			} 
			
			if(GlobalVal.STG_LTV.equalsIgnoreCase(msQuestion.getRefId())) {
				if(StringUtils.isNotBlank(taskDBean[i].getText_answer())) {
					submitData.setLtvPercentage(taskDBean[i].getText_answer());
					double ltv = Double.parseDouble(taskDBean[i].getText_answer());
					double ltvAmount = (ltv*ntf)/100;
					submitData.setLtvAmount(String.format("%.0f",ltvAmount));
				}else {
					submitData.setLtvPercentage("0");
				}
			} 
			if(GlobalVal.STG_NTF.equalsIgnoreCase(msQuestion.getRefId())) {
				if(StringUtils.isNotBlank(taskDBean[i].getText_answer())) {
					submitData.setNtfAmt(taskDBean[i].getText_answer());
					ntf = Double.parseDouble(taskDBean[i].getText_answer());
				}else {
					submitData.setNtfAmt("0");
				}
				submitData.setDpAmount("0");
				submitData.setDpPercentage("0");
			}
			
		}
		
		Map<String, Object> resultTaskD = this.getManagerDAO().list(
				"from TrTaskD ttd join fetch ttd.trTaskH tth "
				+ "where tth.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", taskhOrder.getUuidTaskH()}});
		
		List<TrTaskD> listTask = (List) resultTaskD.get(GlobalKey.MAP_RESULT_LIST);
		
		for (TrTaskD taskD : listTask) {
			if(GlobalVal.STG_CUST_NAME.equalsIgnoreCase(taskD.getMsQuestion().getRefId())) {
				submitData.setNamaKtp(taskD.getTextAnswer());
			} else if(GlobalVal.STG_NIK.equalsIgnoreCase(taskD.getMsQuestion().getRefId())) {
				submitData.setNik(taskD.getTextAnswer());
			} else if(GlobalVal.STG_BIRTH_DT.equalsIgnoreCase(taskD.getMsQuestion().getRefId())) {
				submitData.setTanggalLahir(taskD.getTextAnswer());
			} else if(GlobalVal.STG_BIRTH_PLACE.equalsIgnoreCase(taskD.getMsQuestion().getRefId())) {
				submitData.setTempatLahir(taskD.getTextAnswer());
			} else if(GlobalVal.STG_PRODUCT.equalsIgnoreCase(taskD.getMsQuestion().getRefId())) {
				Object[][] params2 = {
						{ "product", taskD.getMsLovByLovId().getCode()}, {"konvenSyariah", taskhOrder.getAmMsuser().getMsBranch().getKonvenSyariah()} };
				String procCategoryCode = (String) this.getManagerDAO().selectOneNativeString("select DISTINCT PRODUCT_CATEGORY_CODE from TBL_PRODUCT_CATEGORY with (nolock) " + 
						"where PRODUCT = :product and KONVEN_SYARIAH = :konvenSyariah", params2);
				submitData.setProduct(procCategoryCode);
			}
		}
		
		Double totalDsr = Double.parseDouble(submitData.getAngsuran()) / Double.parseDouble(submitData.getPenghasilan()) * 100;
		submitData.setDsr(String.valueOf(new DecimalFormat("##.##").format(totalDsr)));
		submitData.setMobileTaskCode(taskhOrder.getApplNo());
		submitData.setTaksasi("0");
		
		return submitData;
	}
	
	@Override
	public List<Map<String, Object>> copyValueOcr(List<Map<String, String>> list, long uuidForm, long uuidQuestion, 
			long uuidUser, int seqQuest, AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
			for (int i=0; i<list.size(); i++){
					Map map = list.get(i);
				if(Integer.parseInt(map.get("id").toString()) >= seqQuest){

					Object [][] prm = {{"refId", (String) map.get("refid")},
										{"uuidForm", uuidForm }};
					List <Map<String, Object>> listValue =  this.getManagerDAO().selectAllNative ("task.neworder.getScriptCopyValueOcr", prm, null);
					
					if (!listValue.isEmpty()){
						for (int j=0; j<listValue.size(); j++){
							Map map2 = listValue.get(j);
							String convertedExpression = new String(map2.get("d1").toString());
					        String[] arg = convertedExpression.split(",");
					        String mainIdf = arg[0].trim();
					        mainIdf = mainIdf.replace("copyDukcapil(", "").trim();
					        mainIdf = mainIdf.replace("{", "").trim();
					        mainIdf = mainIdf.replace("}", "").trim();
					        String valueIdf = arg[1].trim();
					        valueIdf = valueIdf.replace(")", "").trim();
					        valueIdf = valueIdf.replace("{", "").trim();
					        valueIdf = valueIdf.replace("}", "").trim();
					        
							MsQuestion msQuestion = this.commonLogic.retrieveQuestionByRefIdQset(map2.get("d0").toString(), Long.parseLong(map2.get("d3").toString()), Long.parseLong(map2.get("d2").toString()), callerId);

							Map<String, Object> mapResult = new HashMap<>();
							mapResult.put("valueIdf", valueIdf);
							mapResult.put("ansType", msQuestion.getMsAnswertype().getCodeAnswerType());
							mapResult.put("srcRefId", map2.get("d0").toString());
							listResult.add(mapResult);
						}
					}
				}
			}
			return listResult;
	}
	
	@Transactional
	public Map validateOCR(String base64, String type, AuditContext auditContext) {
		Map result = intFormLogic.requestOCR(base64, type, false, auditContext);
		return result;
	}
	
	@Override
	@Transactional
	public Map checkDataOCR(String data, String dataDukcapil, String idApiUsage, boolean isCopyQuestion, String uuidForm, AuditContext auditContext) {
		Map mapResult = new HashMap<>();
		OCRForNcResponse ocrResp = null;
		OCRForNcResponse ocrBean = null;
		
		String ocrJson = "";
		String msg = "";
		//check bypass or not
		if(StringUtils.isNotBlank(idApiUsage) && !idApiUsage.equals("-")){
			Object[][] param = {{Restrictions.eq("idApiUsage", Long.valueOf(idApiUsage)) }};
			TblApiUsage tblApiUsage = this.getManagerDAO().selectOne(TblApiUsage.class, param);
			
			ocrResp = gson.fromJson(tblApiUsage.getJsonResponse(), OCRForNcResponse.class);
			
			ocrBean = generateSubmitDetailDataOCR(data, ocrResp, auditContext);
		
			ocrBean.setMessage(ocrResp.getMessage());
			ocrBean.setPricingStrategy(ocrResp.getPricingStrategy());
			ocrBean.setTransactionId(ocrResp.getTransactionId());
			ocrBean.setCode(ocrResp.getCode());
			ocrBean.setExtra(ocrResp.getExtra());
			ocrJson = gson.toJson(ocrBean, OCRForNcResponse.class);

			tblApiUsage.setUpdateResponse(ocrJson);
			tblApiUsage.setDtmUpd(new Date());
			tblApiUsage.setUsrUpd(auditContext.getCallerId().toString());
			this.getManagerDAO().update(tblApiUsage);
		} else {
			ocrBean = generateSubmitDetailDataOCR(data, ocrResp, auditContext);
		}
		
		if(isCopyQuestion) {
			String[] birth = ocrBean.getData().getBirthPlaceBirthday().split(", ");
			TaskDukcapilBean taskDukcapilBean = new TaskDukcapilBean();
			taskDukcapilBean.setNik(ocrBean.getData().getIdNumber());
			try {
				SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM-dd");
				SimpleDateFormat format2 = new SimpleDateFormat("dd MMMM yyyy");
				Date date = format1.parse(birth[1]);
				taskDukcapilBean.setBirthDt(format2.format(date));
			} catch (ParseException e) {
				LOG.error(e.getMessage(), e);
			}
			taskDukcapilBean.setBirthPlace(birth[0]);
			taskDukcapilBean.setCustName(ocrBean.getData().getName());
			
			Map mapValidateDukcapil = this.validateDukcapil(taskDukcapilBean, "0", true, "", auditContext);
			int validateDukcapil = (int) mapValidateDukcapil.get("d0");
			taskDukcapilBean.setIsMatchDukcapil(String.valueOf(validateDukcapil));
			taskDukcapilBean.setIdHistCust((String) mapValidateDukcapil.get("idHistCust"));
			mapResult.put("idHistCust", (String) mapValidateDukcapil.get("idHistCust"));
			msg = (String) mapValidateDukcapil.get("d1");
			//code 0 : no match, 1 : match, 2 : not found
			if(validateDukcapil == 0) {
				ocrJson = "0";
				String isSelfAssignment = StringUtils.EMPTY;
				Object[][] params = { { Restrictions.eq("uuidMsUser", new Long(auditContext.getCallerId())) } };
				AmMsuser loginBean = this.getManagerDAO().selectOne(AmMsuser.class, params);
				
				createTaskLead(taskDukcapilBean, dataDukcapil, uuidForm, loginBean, isSelfAssignment, true, auditContext);				
			}else if(validateDukcapil == 2){
				ocrJson = "2";
			}else {
				ocrJson = "1";
			}
		}
		if(msg == null) {
			msg = "OK";
		}
		
		mapResult.put("code", ocrJson);
		mapResult.put("msg", msg);
		
		return mapResult;
	}
	
	private OCRForNcResponse generateSubmitDetailDataOCR (String data,  OCRForNcResponse ocrResp, AuditContext callerId) {
		String[] answers = data.split("#");
		OCRForNcBean ocrbean = new OCRForNcBean();
		String rtrw = "";
		String tmptLahir = "";
		String tglLahir = "";
		for(int i=0;i<answers.length;i++){			
			String[] tempData= answers[i].split("~");
			String idOcr = tempData[4];
			String value = "";
			if(tempData.length<6) {
				value = "";
			}else {				
				value = tempData[5];
			}
			
			if(idOcr.equalsIgnoreCase("nama")) {
				ocrbean.setName(value);
			}else if(idOcr.equalsIgnoreCase("nik")) {
				ocrbean.setIdNumber(value);
			}else if(idOcr.equalsIgnoreCase("tempatLahir")) {
				tmptLahir = value;
			}else if(idOcr.equalsIgnoreCase("tanggalLahir")) {
				tglLahir = value;
			}else if(idOcr.equalsIgnoreCase("alamat")) {
				ocrbean.setAddress(value);
			}else if(idOcr.equalsIgnoreCase("rtRw")) {
				ocrbean.setRtrw(value);
			}else if(idOcr.equalsIgnoreCase("provinsi")) {
				ocrbean.setProvince(value);
			}else if(idOcr.equalsIgnoreCase("kotaKabupaten")) {
				ocrbean.setCity(value);
			}else if(idOcr.equalsIgnoreCase("kecamatan")) {
				ocrbean.setDistrict(value);
			}else if(idOcr.equalsIgnoreCase("kelurahanDesa")) {
				ocrbean.setVillage(value);
			}									
		}
		OCRForNcResponse ocrRes = new OCRForNcResponse();
		ocrbean.setBirthPlaceBirthday(tmptLahir + ", " + tglLahir);
		ocrRes .setData(ocrbean);
		return ocrRes;
	}
	@Override
	public Map imageQuality(AuditContext auditContext) {
		int picWidth = 480;
		int picHeight = 640;
		int picQuality = 75;
		Map comboMap = new HashMap();
		Object[][] params = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_IMAGE_QUALITY)}};
		
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		 if (amGeneralsetting != null) {
	            String[] values = amGeneralsetting.getGsValue().split("&");
	            for (String value : values) {
	                String[] newParam = value.split("=");
	                String nCode = newParam[0];
	                String nValue = newParam[1];
	                if (nCode.equals("width"))
	                	picWidth = Integer.valueOf(nValue);
	                else if (nCode.equals("height"))
	                	picHeight = Integer.valueOf(nValue);
	                else if (nCode.equals("jpegquality"))
	                	picQuality = Integer.valueOf(nValue);
	            }
				comboMap.put("picWidth", picWidth);
	    		comboMap.put("picHeight", picHeight);
	    		comboMap.put("picQuality", picQuality);
	            
	        }
		 return comboMap;
	}
	
	@Override
	@Transactional(readOnly = true)
	public Map refreshTelecheck(String idApiUsage, AuditContext auditContext) {
		Map result = new HashMap<>();
		//check bypass or not
		if(StringUtils.isNotBlank(idApiUsage) && !idApiUsage.equals("-")){
		TblApiUsage apiUsage = this.getManagerDAO().selectOne(TblApiUsage.class, new Long(idApiUsage));
		if(apiUsage!=null) {
			if("0".equals(apiUsage.getStatus())) {
				result.put("code", 0);
				result.put("result", "Request On Progress. Press button refresh to update the current status.");
			}else if("1".equals(apiUsage.getStatus())) {
				result.put("code", 1);
				result.put("result", "The phone number is valid");
			}else if("999".equals(apiUsage.getStatus()) || "500".equals(apiUsage.getStatus())) {
				result.put("code", 999);
				result.put("result", apiUsage.getKeterangan());
			}
		}else {
			result.put("code", 500);
			result.put("result", "Request Not Found");
		}
		} else {
			result.put("code", 1);
			result.put("result", "The phone number is valid");
		}
		return result;
	}

	@Override
	@Transactional
	public Map checkTeleStatus(AuditContext audit, String phoneNumber, int phoneOwner) {
		Map result = intFormLogic.checkTeleStatus(audit, phoneNumber, phoneOwner, false, null,null);
		return result;
	}
	
	@Transactional
	@Override
	public AmGeneralsetting retrieveMaxRetry(String gsCode, AuditContext callerId) {
		Object[][] paramGeneralSetting = { { Restrictions.eq("gsCode", gsCode) } };
		return this.getManagerDAO().selectOne(AmGeneralsetting.class, paramGeneralSetting);
	}
		
	@Override
	@Transactional
	public void regeneratePrintPO(AuditContext callerId) {
		List tblpo = this.getManagerDAO().selectAllNativeString("SELECT * FROM TBL_PO TP WHERE TP.STATUS = 0", null);
		for(int i = 0; i<tblpo.size(); i++) {
			Map temp = (Map) tblpo.get(i);
			String uuidTask  = temp.get("d1").toString();
			String usrcrt  = temp.get("d3").toString();
			callerId.setCallerId(usrcrt);
			taskServiceLogic.printPO(uuidTask, callerId);
		}		
	}
	
	public String appendNotes(String notes, String appended) {
		LOG.info("Appending notes " + notes + " " + appended);
		try {
			String newNotes = "";
			
			if (null != notes && !"".equals(notes)){ // have old notes
				newNotes = notes + "|" + appended;
			} else { // new notes
				newNotes = appended;
			}
			
			return newNotes;
		} catch(Exception e) {
			e.printStackTrace();
			return appended;
		}
	}
	
	@Override
	public Map getApiUsage(AuditContext audit, String idApiUsage) {
		Map result = new HashMap<>();
		TblApiUsage apiUsage = this.getManagerDAO().selectOne(TblApiUsage.class, Long.valueOf(idApiUsage));
		
		result.put("code", apiUsage.getStatus());
		result.put("msg", apiUsage.getJsonResponse());
		result.put("idApiUsage", apiUsage.getIdApiUsage());
		
		return result;
	}
	
	@Override
	@Transactional
	public void setStatusApiUsage(AuditContext audit, String idApiUsage) {
		TblApiUsage apiUsage = this.getManagerDAO().selectOne(TblApiUsage.class, Long.valueOf(idApiUsage));
		if(apiUsage!=null) {
			if(StringUtils.isBlank(apiUsage.getUpdateResponse())) {
				apiUsage.setStatus("2");
				this.getManagerDAO().update(apiUsage);
			}
		}
	}
	@Override
	@Transactional
	public String fixCopyVal(String data, AuditContext callerId) {
		String result = null;
		String [] list = data.split("#");
		List<String[]> listTaskD = new ArrayList<>();
	
		boolean isAddressSame = false;
		String kelurahan = null;
		String kecamatan = null;
		String city = null;
		String provinsi = null;
			
		for (int i = 0; i < list.length; i++) {
			String[] tempData = list[i].split("~");
			String answer = tempData[4].equalsIgnoreCase("-1") ? StringUtils.EMPTY : tempData[4];
			MsQuestion msQuestion = this.commonLogic.retrieveQuestionByUuid(Long.parseLong(tempData[0]), callerId);
			
			if (tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO) ||
					tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION) ||
					tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN) ||
					tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION) ||
					tempData[1].equalsIgnoreCase(GlobalVal.ANSWER_TYPE_TEXT_WITH_SUGGESTION)) {
				Object param [][] = {{Restrictions.eq("uuidLov", Long.valueOf(answer))}};
				MsLov msLov = this.getManagerDAO().selectOne(MsLov.class, param);
				answer = msLov.getCode();
			}
			if(GlobalVal.STG_IS_EQ_ALMT_TGL.equalsIgnoreCase(msQuestion.getRefId())) {
				if("1".equalsIgnoreCase(answer)) {//1 = Ya
					isAddressSame = true;
				}
			} else if (GlobalVal.STG_LEGAL_PROVINSI.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_PROVINSI.equalsIgnoreCase(msQuestion.getRefId())) {
				provinsi = tempData[4].equalsIgnoreCase("-1") ? StringUtils.EMPTY : tempData[4];
			} else if (GlobalVal.STG_LEGAL_CITY.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_KOTA.equalsIgnoreCase(msQuestion.getRefId())) {
				city = tempData[4].equalsIgnoreCase("-1") ? StringUtils.EMPTY : tempData[4];
			} else if (GlobalVal.STG_LEGAL_KECAMATAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_KECAMATAN.equalsIgnoreCase(msQuestion.getRefId())) {
				kecamatan = tempData[4].equalsIgnoreCase("-1") ? StringUtils.EMPTY : tempData[4];
			} else if (GlobalVal.STG_LEGAL_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId()) || GlobalVal.OCR_KELURAHAN.equalsIgnoreCase(msQuestion.getRefId())) {
				kelurahan = tempData[4].equalsIgnoreCase("-1") ? StringUtils.EMPTY : tempData[4];
			}
			
			String [] taskD = {
				/*0 */tempData[0],
				/*1 */tempData[1],
				/*2 */tempData[2],
				/*3 */tempData[3],
				/*4 */tempData[4],
				/*5 */msQuestion.getRefId()
			};
			
			listTaskD.add(taskD);
		}
		
		if(isAddressSame) {
			for(String[] taskD : listTaskD) {
				String refId = taskD[5];
				if (GlobalVal.STG_RES_PROVINSI.equalsIgnoreCase(refId) || GlobalVal.OCR_STG_RES_PROVINSI.equalsIgnoreCase(refId)) {
					taskD[4] = provinsi;
				} else if (GlobalVal.STG_RES_CITY.equalsIgnoreCase(refId) || GlobalVal.OCR_STG_RES_CITY.equalsIgnoreCase(refId)) {
					taskD[4] = city;
				} else if (GlobalVal.STG_RES_KECAMATAN.equalsIgnoreCase(refId) || GlobalVal.OCR_STG_RES_KECAMATN.equalsIgnoreCase(refId)) {
					taskD[4] = kecamatan;
				} else if (GlobalVal.STG_RES_KELURAHAN.equalsIgnoreCase(refId) || GlobalVal.OCR_STG_RES_KELURAHN.equalsIgnoreCase(refId)) {
					taskD[4] = kelurahan;
				}
				
				if(null == result) {
					result =
							taskD[0] + "~" +
							taskD[1] + "~" +
							taskD[2] + "~" +
							taskD[3] + "~" +
							taskD[4] + "#";
				} else {
					result = result  +
							taskD[0] + "~" +
							taskD[1] + "~" +
							taskD[2] + "~" +
							taskD[3] + "~" +
							taskD[4] + "#";
				}
			}
			LOG.info("fixCopyVal.before: {}", data);
			LOG.info("fixCopyVal.after: {}", result);
			return result;
		} else {
			return data; 
		}
	}
	
	@Override
	public List listPreIA(Object params, AuditContext callerId) {
		List result = new ArrayList<>();
		
		result = this.getManagerDAO().selectAllNative(
					"task.createtask.listPreIA", params, null);

		return result;
	}
		
	@Override
	public Integer countlistPreIA(Object params, AuditContext callerId) {
		Integer result;
		
		result = (Integer) this.getManagerDAO().selectOneNative(
					"task.createtask.countListPreIA", params);

		return result;
	}
	
	@Override
	public Map<String, String> getBranchListCombo(long branchId, AuditContext callerId) {
		Map<String, String> result = MapUtils.EMPTY_MAP;
		
		Object[][] params = { { "branchId", branchId } };
		List list = this.getManagerDAO().selectAllNative(
				"task.verification.getBranchListCombo", params, null);
		
		Map<String, String> comboList = new HashMap<String, String>();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				Map temp2 = (Map) list.get(i);
				comboList.put((String) temp2.get("d1"),
						(String) temp2.get("d2")+" - "+(String) temp2.get("d1"));
			}
			result = sortByValues(comboList);
		} 
		else {
			result.put("%", "ALL");
		}
		
		return result;
	}
	
	@Override
	public List getFormListCombo(AuditContext callerId) {
		List result = null;
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
		String[][] paramsForm = { { "isActive", "1" }, {"subsystemName", subsysName}};
		result = this.getManagerDAO()
				.selectAllNativeString(
						"SELECT form.UUID_FORM, form.FORM_NAME FROM MS_FORM "
						+ "form WITH (NOLOCK) JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
						+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
						+ "WHERE form.IS_ACTIVE = :isActive "
						+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName",
						paramsForm);
		return result;
	}
	
	Map sortByValues(Map<String, String> comboList) {
		List list = new LinkedList(comboList.entrySet());

		Collections.sort(list, new Comparator() {
			public int compare(Object o1, Object o2) {
				return ((Comparable) ((Map.Entry) (o1)).getValue())
						.compareTo(((Map.Entry) (o2)).getValue());
			}
		});

		Map sortedHashMap = new LinkedHashMap();
		sortedHashMap.put("%", "ALL");
		for (Iterator it = list.iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			sortedHashMap.put(entry.getKey(), entry.getValue());
		}
		return sortedHashMap;
	}
	
	@Override
	public Map<String, Object> detailPreIA(long uuidTaskH, AuditContext callerId) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Boolean hasImage = Boolean.FALSE;
		
		Object[][] params = {{"uuidTaskH", uuidTaskH}};
		List<Map<String, Object>> result = this.getManagerDAO().selectForListOfMap("task.createtask.preIADetailQSet", params, null);
		
		for (int i = 0; i < result.size(); i++) {
			Map map = (HashMap) result.get(i);
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("msQuestion.msAnswertype.codeAnswerType").toString())){
				NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);				
				if (StringUtils.isNotBlank((String) map.get("textAnswer"))) {
					map.put("textAnswer", "Rp "+ formatKurs.format(NumberUtils.toDouble((String) map.get("textAnswer"))));
				}
				else {
					map.put("textAnswer", StringUtils.EMPTY);
				}
				map.put("accuracy", StringUtils.EMPTY);
			}
			
			if (MssTool.isImageQuestion(map.get("msQuestion.msAnswertype.codeAnswerType").toString()) && "1".equals(map.get("hasImage").toString())
					&& null != map.get("latitude") && null != map.get("longitude")) { //Lat Lng
				String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
				if("1".equals(link_encrypt)){
					String uuidTaskLob = (String) map.get("textAnswer");
					String[] toBeEncrypt = {uuidTaskLob};
					uuidTaskLob = CipherTool.encryptData(toBeEncrypt).get(0).toString();	
					map.put("textAnswer", uuidTaskLob);
				}
				hasImage = Boolean.TRUE;
			}
			else if (MssTool.isImageQuestion(map.get("msQuestion.msAnswertype.codeAnswerType").toString())
					&& !"1".equals(map.get("hasImage").toString())) {
				map.put("textAnswer", StringUtils.EMPTY);
			}else if (MssTool.isImageQuestion(map.get("msQuestion.msAnswertype.codeAnswerType").toString())
					&& "1".equals(map.get("hasImage").toString())) {
				String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
				if("1".equals(link_encrypt)){
					String uuidTaskLob = (String) map.get("textAnswer");
					String[] toBeEncrypt = {uuidTaskLob};
					uuidTaskLob = CipherTool.encryptData(toBeEncrypt).get(0).toString();	
					map.put("textAnswer", uuidTaskLob);
				}
				hasImage = Boolean.TRUE;
			}
		}
		
		resultMap.put("resultList", result);
		resultMap.put("hasImage", hasImage);
		
		return resultMap;
	}
	
	@Override
	public List getWfEngineNextStep(TrTaskH trTaskH, AmMssubsystem msSubsystem, AuditContext callerId){
		List<String> result = null;
		List wfDesc= new ArrayList<>() ;
		long uuid = trTaskH.getUuidTaskH();
		long uuidProcess = this.getUuidProcess(trTaskH, msSubsystem);
		
		List uuidNextTask = wfEngineLogic.getNextTaskUuid(uuidProcess, uuid);
		result  = wfEngineLogic.getNextTask(uuidProcess, uuid);
	
		for (int i = 0; i < result.size(); i++) {
			String statusCode = result.get(i).toString();
			String[][] params = { { "statusCode", statusCode },
					{ "msSubsystem", String.valueOf(msSubsystem.getUuidMsSubsystem()) }};
			String descNexttask = (String) this.getManagerDAO()
					.selectOneNativeString("select STATUS_TASK_DESC "
							+ "from MS_STATUSTASK with (nolock) "
							+ "where STATUS_CODE = :statusCode "
							+ "AND UUID_MS_SUBSYSTEM = :msSubsystem ", params);
			
			Map a= new HashMap<>();
			a.put("uuidNextTask", uuidNextTask.get(i)+"&"+statusCode);
			a.put("descNextTask", descNexttask);
			wfDesc.add(a);
		}
		
	    return wfDesc ;
	}
	
	@Override
	public List viewMapPhoto(long uuid, AuditContext callerId) throws UnsupportedEncodingException {
		List <TrTaskBean> result = new ArrayList<TrTaskBean>();
		Object[][] params = {{ "uuidTaskH", uuid } };
		List list = this.getManagerDAO().selectAllNative("task.createtask.viewMapPhoto", params, null);

		for (int i = 0; i < list.size(); i++) {
			TrTaskBean approval = new TrTaskBean();
			Map map = (HashMap) list.get(i);
			approval.setHasImage((String) map.get("d4"));
			if ("1".equals((String) map.get("d4"))) {
				approval.setLob(map.get("d8").toString());
			}
			
			if (map.get("d5")!=null && map.get("d6") != null) {
				approval.setLatitude((BigDecimal) map.get("d5"));
				approval.setLongitude((BigDecimal) map.get("d6"));
				result.add(approval);
			}	
		}
		return result;
	}
	
	@Override
	public TrTaskH getTaskH(long uuid, AuditContext callerId) {
		TrTaskH result = this.getManagerDAO().selectOne(
				"from TrTaskH t "
				+ "join fetch t.msPriority p "
				+ "join fetch t.msForm f "
				+ "where t.uuidTaskH = :uuidTaskH", 
				new Object[][] {{"uuidTaskH", uuid}});
		if (result.getResult() == null || result.getResult().isEmpty()) {
			result.setResult("-");
		}
		if (result.getNotes() == null || result.getNotes().isEmpty()) {
			result.setNotes("-");
		}
		return result;
	}
}
