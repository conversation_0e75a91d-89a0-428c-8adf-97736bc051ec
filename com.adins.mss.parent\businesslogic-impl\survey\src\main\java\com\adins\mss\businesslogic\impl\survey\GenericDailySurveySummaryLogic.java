package com.adins.mss.businesslogic.impl.survey;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.DailySurveySummaryLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.TrSurveydailysummary;

public class GenericDailySurveySummaryLogic extends BaseLogic implements DailySurveySummaryLogic {

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@SuppressWarnings("unchecked")
	@Override
	public void dailySurveySummary(AuditContext callerId) {
		List<AmMsuser> userList = getListUser();
		DateTime dt = new DateTime();
		DateTime start = dt.minusMillis(dt.getMillisOfDay());
		DateTime end = start.plusDays(1).minusMillis(3);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		String startDate = formatter.format(start.toDate());
		String endDate = formatter.format(end.toDate());
		for(AmMsuser user: userList){
			Object[] cntResult = countTot(user, startDate, endDate);
			TrSurveydailysummary summary = getTodaySummary(user.getUuidMsUser());
			if(summary == null){
				insertSurveyDailySummer(user, cntResult, callerId);
			}
			else {
				updateDailySummary(summary, user, cntResult, callerId);
			}
		}
	}
	
	@SuppressWarnings("rawtypes")
	private List getListUser(){
		Map userMap = this.getManagerDAO().list(
			"from AmMsuser u join fetch u.msJob j join fetch u.msBranch b "
			+ "join fetch u.amMssubsystem s "
			+ "where s.subsystemName = :subsystemName and j.isFieldPerson = :isFieldPerson", 
			new Object[][] {{"subsystemName", GlobalVal.SUBSYSTEM_MS}, {"isFieldPerson", "1"}});
		List userList = (List)userMap.get(GlobalKey.MAP_RESULT_LIST);
		return userList;
	}
	
	private Object[] countTot(AmMsuser user, String start, String end){
		String[][] params = { {"uuidSpv", String.valueOf(user.getUuidMsUser())}, {"start", start}, {"end", end}, {"subsystem", String.valueOf(user.getAmMssubsystem().getUuidMsSubsystem())} };
		Object[] cnt = (Object[]) this.getManagerDAO().selectOneNative("survey.dailysummary", params);
		return cnt;
	}
	
	private void insertSurveyDailySummer(AmMsuser user, Object[] cnt, AuditContext callerId){
		
		TrSurveydailysummary obj = new TrSurveydailysummary();
		obj.setUsrCrt(callerId.getCallerId());
		obj.setDtmCrt(new Date());
		obj.setAmMsuser(user);
		obj.setDailyDate(new Date());
		obj.setMsBranch(user.getMsBranch());
		obj.setTotalAssignedTask((Integer)cnt[0]);
		obj.setTotalSubmittedTask((Integer)cnt[1]);
		obj.setTotalNewTask((Integer)cnt[2]);
		this.getManagerDAO().insert(obj);
		
	}
	
	public TrSurveydailysummary getTodaySummary(long idUser){
		TrSurveydailysummary summary = null;
		DateTime currentTime = new DateTime();
		currentTime = currentTime.minusMillis(currentTime.getMillisOfDay());
		Date minDate = currentTime.toDate();
		currentTime = currentTime.plusDays(1).minusMillis(3);
		Date maxDate = currentTime.toDate();
		Object[][] params = { { Restrictions.eq("amMsuser.uuidMsUser", idUser) },
				{ Restrictions.between("dailyDate", minDate, maxDate) } };
		summary = this.getManagerDAO().selectOne(TrSurveydailysummary.class, params);
		return summary;
	}
	
	public void updateDailySummary(TrSurveydailysummary obj, AmMsuser user, Object[] cnt, AuditContext callerId){
		obj.setTotalAssignedTask((Integer)cnt[0]);
		obj.setTotalSubmittedTask((Integer)cnt[1]);
		obj.setTotalNewTask((Integer)cnt[2]);
		this.getManagerDAO().update(obj);
	}
}
