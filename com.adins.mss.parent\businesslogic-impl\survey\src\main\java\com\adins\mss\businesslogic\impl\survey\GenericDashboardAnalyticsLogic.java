package com.adins.mss.businesslogic.impl.survey;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.DashboardAnalyticsLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.custom.DashboardAnalyticsBean;
import com.adins.mss.services.model.common.DataAnalyticResponse;

@SuppressWarnings({ "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericDashboardAnalyticsLogic extends BaseLogic implements
		DashboardAnalyticsLogic {

	@Override
	public List getPerformanceMTD(long uuidBranch, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch } };
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getPerformanceMTD", params, null);
		return result;
	}
	
	@Override
	public List getPerformanceToday(long uuidBranch, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch } };
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getPerformanceToday", params, null);
		return result;
	}
	
	@Override
	public List<DashboardAnalyticsBean> getDrillMonth(long uuidBranch, AuditContext callerId) {
		String[][] paramsQuestion = { };
		List question = this.getManagerDAO().selectAllNative(
				"survey.dm.getQuestion", paramsQuestion, null);
		
		List<DashboardAnalyticsBean> result = new ArrayList<DashboardAnalyticsBean>();
		DashboardAnalyticsBean bean = new DashboardAnalyticsBean();
		
		if(!question.isEmpty()){
			for(int i=0; i<question.size(); i++){
				Map tempLov = (Map) question.get(i);
				bean = new DashboardAnalyticsBean();
				Object[][] params = { { "uuidBranch", uuidBranch }, {"uuidQuestion", tempLov.get("d0")}, 
						{"uuidLov", tempLov.get("d1")}};
				List tempResult = this.getManagerDAO().selectAllNative(
					"survey.dm.getDrillMonth", params, null);
				bean.setDrillMonth(tempResult);
				bean.setLovCode(tempLov.get("d2").toString());
				result.add(bean);
			}
			
			Map temp = (Map) question.get(0);
			bean = new DashboardAnalyticsBean();
			Object[][] params = { { "uuidBranch", uuidBranch }, {"uuidQuestion", temp.get("d0")}};
			List tempResult = this.getManagerDAO().selectAllNative(
					"survey.dm.getOtherDrillMonth", params, null);
			bean.setDrillMonth(tempResult);
			bean.setLovCode("Others");
			result.add(bean);
		}
		return result;
	}
	
	@Override
	public List getSubmitMTD(long uuidBranch, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }};
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getSubmitMTD", params, null);
		return result;
	}
	
	@Override
	public List getSubmitToday(long uuidBranch, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch } };
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getSubmitToday", params, null);
		return result;
	}
	
	@Override
	public List getVerificationMTD(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }, { "uuidSubsystem", uuidSubsystem } };
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getVerificationMTD", params, null);
		return result;
	}
	
	@Override
	public List getVerificationToday(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }, { "uuidSubsystem", uuidSubsystem } };
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getVerificationToday", params, null);
		return result;
	}
	
	@Override
	public List getSurveyorStatusToday(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", uuidBranch }, { "uuidSubsystem", uuidSubsystem }};
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getSurveyorStatusToday", params, null);
		return result;
	}
	
	@Override
	public List userList(long uuidBranch, long uuidSubsystem, AuditContext callerId) {
		List userList = Collections.EMPTY_LIST;
		Object[][] param = { { Restrictions.eq("gsCode", "MS_JOBSVY") } };
		AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(
				AmGeneralsetting.class, param);
		if (amGeneralSetting!=null) {
			Object[][] paramJob = { { Restrictions.eq("jobCode", amGeneralSetting.getGsValue()) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidSubsystem) } };
			MsJob msJob =  this.getManagerDAO().selectOne(MsJob.class, paramJob);
			if (msJob!=null) {
				Object params[][] = {{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidSubsystem) },
						{Restrictions.eq("isActive", "1")},
						{Restrictions.eq("msBranch.uuidBranch", uuidBranch)},
						{Restrictions.eq("msJob.uuidJob", msJob.getUuidJob())}
				};
				Map result = this.getManagerDAO().list(AmMsuser.class, params, null);
				userList = (List)result.get(GlobalKey.MAP_RESULT_LIST);
			}
		}
				
		return userList;
	}

	@Override
	public String getAutoupdateInterval(AuditContext callerId){
		Object params[][] = {{Restrictions.eq("gsCode", "MS_PRM22_DAN")}};
		AmGeneralsetting result = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		return result.getGsValue();
	}
	
	@Override
	public List getDataAnalyticMobile(String task, String diagram, AuditContext callerId) {
		final String SUBSYSTEM = "MS";
		final String TASK_ALLMTD = "All MTD", TASK_ALLTODAY = "All Today", TASK_TODAY = "Today", TASK_MTD = "MTD";
		final String DIAG_PERFORMANCE = "performance", DIAG_SUBMIT = "submit", DIAG_VERIF = "verif", 
				DIAG_SVYSTATUS = "surveyorStatus", DIAG_DRILLMONTH = "drillMonth";
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, NumberUtils.toLong(callerId.getCallerId()));
		long uuidBranch = user.getMsBranch().getUuidBranch();
		long uuidSubsystem = user.getAmMssubsystem().getUuidMsSubsystem();
		List<DataAnalyticResponse> listResult = new ArrayList<DataAnalyticResponse>();
		if(TASK_ALLMTD.equalsIgnoreCase(task) || TASK_ALLTODAY.equalsIgnoreCase(task) 
				|| TASK_TODAY.equalsIgnoreCase(task)){
			if((TASK_TODAY.equalsIgnoreCase(task) && DIAG_PERFORMANCE.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List performanceToday = this.getPerformanceToday(uuidBranch, callerId);
				for (int i = 0; i < performanceToday.size(); i++) {
					Map mapResult = (Map) performanceToday.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_PERFORMANCE);
					bean.setSubsystem(SUBSYSTEM);
					bean.setStatusTask(String.valueOf(mapResult.get("d0")));
					bean.setJumlah(String.valueOf(mapResult.get("d1")));
					bean.setDayDate(String.valueOf(mapResult.get("d2")));
					listResult.add(bean);
				}
			}
			if((TASK_TODAY.equalsIgnoreCase(task) && DIAG_SUBMIT.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List submitToday = this.getSubmitToday(uuidBranch, callerId);
				for (int i = 0; i < submitToday.size(); i++) {
					Map mapResult = (Map) submitToday.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_SUBMIT);
					bean.setSubsystem(SUBSYSTEM);
					bean.setUuidUser(String.valueOf(mapResult.get("d0")));
					bean.setFullName(String.valueOf(mapResult.get("d1")));
					bean.setAssignIn(String.valueOf(mapResult.get("d2")));
					bean.setSubmit(String.valueOf(mapResult.get("d3")));
					listResult.add(bean);
				}
			}
			if((TASK_TODAY.equalsIgnoreCase(task) && DIAG_VERIF.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List verificationToday = this.getVerificationToday(uuidBranch, uuidSubsystem, callerId);
				for (int i = 0; i < verificationToday.size(); i++) {
					Map mapResult = (Map) verificationToday.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_VERIF);
					bean.setSubsystem(SUBSYSTEM);
					bean.setUuidUser(String.valueOf(mapResult.get("d0")));
					bean.setFullName(String.valueOf(mapResult.get("d1")));
					bean.setJumlah(String.valueOf(mapResult.get("d2")));
					listResult.add(bean);
				}
			}
			if((TASK_TODAY.equalsIgnoreCase(task) && DIAG_SVYSTATUS.equalsIgnoreCase(diagram)) || 
					(StringUtils.isEmpty(diagram) && !TASK_TODAY.equalsIgnoreCase(task))){
				List surveyorStatus = this.getSurveyorStatusToday(uuidBranch, uuidSubsystem, callerId);
				for (int i = 0; i < surveyorStatus.size(); i++) {
					Map mapResult = (Map) surveyorStatus.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_TODAY);
					bean.setDiagram(DIAG_SVYSTATUS);
					bean.setSubsystem(SUBSYSTEM);
					bean.setUuidUser(String.valueOf(mapResult.get("d0")));
					bean.setFullName(String.valueOf(mapResult.get("d1")));
					bean.setSpvName(String.valueOf(mapResult.get("d2")));
					bean.setNewTask(String.valueOf(mapResult.get("d3")));
					bean.setDownload(String.valueOf(mapResult.get("d4")));
					bean.setRead(String.valueOf(mapResult.get("d5")));
					bean.setSurvey(String.valueOf(mapResult.get("d6")));
					bean.setUpload(String.valueOf(mapResult.get("d7")));
					bean.setSubmit(String.valueOf(mapResult.get("d8")));
					bean.setVerif(String.valueOf(mapResult.get("d9")));
					bean.setRelease(String.valueOf(mapResult.get("d10")));
					listResult.add(bean);
				}
			}
			if(TASK_ALLMTD.equalsIgnoreCase(task)){
				List performanceMTD = this.getPerformanceMTD(uuidBranch, callerId);
				for (int i = 0; i < performanceMTD.size(); i++) {
					Map mapResult = (Map) performanceMTD.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_MTD);
					bean.setDiagram(DIAG_PERFORMANCE);
					bean.setSubsystem(SUBSYSTEM);
					bean.setStatusTask(String.valueOf(mapResult.get("d0")));
					bean.setJumlah(String.valueOf(mapResult.get("d1")));
					bean.setDayDate(String.valueOf(mapResult.get("d2")));
					listResult.add(bean);
				}
				List submitMTD = this.getSubmitMTD(uuidBranch, callerId);
				for (int i = 0; i < submitMTD.size(); i++) {
					Map mapResult = (Map) submitMTD.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_MTD);
					bean.setDiagram(DIAG_SUBMIT);
					bean.setSubsystem(SUBSYSTEM);
					bean.setUuidUser(String.valueOf(mapResult.get("d0")));
					bean.setFullName(String.valueOf(mapResult.get("d1")));
					bean.setAssignIn(String.valueOf(mapResult.get("d2")));
					bean.setSubmit(String.valueOf(mapResult.get("d3")));
					listResult.add(bean);
				}
				List verificationMTD = this.getVerificationMTD(uuidBranch, uuidSubsystem, callerId);
				for (int i = 0; i < verificationMTD.size(); i++) {
					Map mapResult = (Map) verificationMTD.get(i);
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_MTD);
					bean.setDiagram(DIAG_VERIF);
					bean.setSubsystem(SUBSYSTEM);
					bean.setUuidUser(String.valueOf(mapResult.get("d0")));
					bean.setFullName(String.valueOf(mapResult.get("d1")));
					bean.setJumlah(String.valueOf(mapResult.get("d2")));
					listResult.add(bean);
				}
				List<DashboardAnalyticsBean> drillMonthList = this.getDrillMonth(uuidBranch, callerId);
				for (int i = 0; i < drillMonthList.size(); i++) {
					DashboardAnalyticsBean daBean = drillMonthList.get(i);
					List drillMonth = daBean.getDrillMonth();
					List<DataAnalyticResponse> drillMonthResponse = new ArrayList<DataAnalyticResponse>();
					for(int j = 0; j < drillMonth.size(); j++){
						Map mapResult = (Map) drillMonth.get(j);
						DataAnalyticResponse beanDm = new DataAnalyticResponse();
						beanDm.setStatusTask(String.valueOf(mapResult.get("d0")));
						beanDm.setJumlah(String.valueOf(mapResult.get("d1")));
						beanDm.setDayDate(String.valueOf(mapResult.get("d2")));
						drillMonthResponse.add(beanDm);
					}
					DataAnalyticResponse bean = new DataAnalyticResponse();
					bean.setTask(TASK_MTD);
					bean.setDiagram(DIAG_DRILLMONTH);
					bean.setSubsystem(SUBSYSTEM);
					bean.setLovCode(String.valueOf(daBean.getLovCode()));
					bean.setDrillMonth(drillMonthResponse);
					listResult.add(bean);
				}
			}
		}
		return listResult;
	}
}
