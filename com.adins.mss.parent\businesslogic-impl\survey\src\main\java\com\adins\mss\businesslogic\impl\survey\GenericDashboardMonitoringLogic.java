package com.adins.mss.businesslogic.impl.survey;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.survey.DashboardMonitoringLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.MssWorkflowProcess;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsAssettag;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrLocationhistory;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.custom.DashboardBranchBean;
import com.adins.mss.model.custom.DashboardMonitoringBean;
import com.adins.mss.model.custom.DashboardSvyBean;
import com.adins.mss.util.CipherTool;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericDashboardMonitoringLogic extends BaseLogic implements
		DashboardMonitoringLogic {
	private CommonLogic commonLogic; 
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	
	public void setCommonLogic(CommonLogic commonLogic) {
        this.commonLogic = commonLogic;
    }

	@Override
	public DashboardBranchBean getBranch(long uuidBranch, long uuidSpv, AuditContext callerId) {
		DashboardBranchBean result = new DashboardBranchBean();
		MsBranch branch = null;
		branch = this.getManagerDAO().selectOne(MsBranch.class, uuidBranch);
		
		if(null == branch.getLatitude() && null == branch.getLongitude() ||
				branch.getLongitude().toString().isEmpty() && branch.getLatitude().toString().isEmpty())
		{	// set user's LatLng to HO's LatLng
			boolean flag = true;
			Map<String, Object> latlong = new HashMap();
			
			String idBranch = String.valueOf(branch.getUuidBranch());
			StringBuilder sb = new StringBuilder(); 
			sb.append("select top 1 PARENT_ID, LATITUDE, LONGITUDE from MS_BRANCH ");
			sb.append("where UUID_BRANCH = isnull((select PARENT_ID from MS_BRANCH ");
			sb.append("where UUID_BRANCH = :UUID_MS_BRANCH),");
			sb.append(":UUID_MS_BRANCH)");
			
			while(flag){
				Object[][] params = { { "UUID_MS_BRANCH" , idBranch } };
				List temp = this.getManagerDAO().selectAllNativeString(sb.toString(), params);
				latlong = (Map) temp.get(0);
				if(null != latlong.get("d1") && null != latlong.get("d2"))
				{
					flag = false;
					branch.setLatitude((BigDecimal) latlong.get("d1"));
					branch.setLongitude((BigDecimal) latlong.get("d2"));
				} else {
					idBranch = latlong.get("d0").toString();
				}
			}
		}
		
		Object[][] paramGs = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_JOBSVY)} };
		AmGeneralsetting amGS = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramGs);
		Object[][] params = { { "uuidBranch", uuidBranch } , { "jobCode", amGS.getGsValue()} };
		Integer totalSvy = (Integer) this.getManagerDAO().selectOneNative("survey.dm.countUsersAllByBranch", params);
		result.setCurrentDate(DateFormatUtils.format(new Date(), "dd MMMM yyyy"));
		result.setBranch(branch);
		result.setTotalSvy(totalSvy);
		result = this.getInfoTask(result, uuidBranch, callerId);
		return result;
	}

	@Override
	public List getUsers(Object params, long idSubsystem,
			AuditContext callerId) throws ParseException{
		List<AmMsuser> listUser = null;
		List<DashboardMonitoringBean> result = new ArrayList<>();
			listUser = this.getUserAllList(params, callerId);
			if (listUser != null) {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
			DateTime currentTime = new DateTime();
			currentTime = currentTime.minusMillis(currentTime.getMillisOfDay());
			Date minDate = currentTime.toDate();
			currentTime = currentTime.plusDays(1).minusMillis(3);
			Date maxDate = currentTime.toDate();
			for (int i = 0; i < listUser.size(); i++) {
				Map temp = (Map) listUser.get(i);
				DashboardMonitoringBean bean = new DashboardMonitoringBean();
				bean.setUuidMsUser(Long.valueOf(temp.get("d0").toString()));
				bean.setFullName(temp.get("d1").toString());
				bean.setIsLoggedIn(temp.get("d2").toString());
				bean.setLoginId(temp.get("d3").toString());
				bean.setCurrentDate(DateFormatUtils.format(new Date(),
						"dd MMMM yyyy"));

				String[][] paramsGetDate = { { "uuidUser",
					String.valueOf(bean.getUuidMsUser())}, {"start", formatter.format(minDate)}, 
					{"end", formatter.format(maxDate)}};
				bean.setAttendanceIn((String) this.getManagerDAO()
						.selectOneNative("survey.dm.getAttendanceIn",
								paramsGetDate));
				bean.setLastTimeDetected((String) this.getManagerDAO()
						.selectOneNative("survey.dm.getLastTimeDetected",
								paramsGetDate));
				bean = this.getTaskRecapitulation(bean.getUuidMsUser(),
						idSubsystem, bean, formatter.format(minDate), formatter.format(maxDate), callerId);
				bean = this.getSurveyorArea(bean, bean.getUuidMsUser());
				bean = this.getTracking(bean, bean.getUuidMsUser(), minDate, maxDate);
				bean = this.getDetailSurveyor(bean, bean.getUuidMsUser(), formatter.format(minDate), 
						formatter.format(maxDate));
				result.add(bean);
			}
		}
		return result;
	}

	private List getUserAllList(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getUsersAll", params, null);
		return result;
	}
	private List getUserExcList(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getUsersExc", params, null);
		return result;
	}
	private List getUserExcListAssignInOut(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getUsersExcAssignInOut", params, null);
		return result;
	}

	private DashboardMonitoringBean getDetailSurveyor(
			DashboardMonitoringBean bean, long uuidUser, String startDate, String endDate) {
		DashboardSvyBean svyBean = new DashboardSvyBean();
		this.getAssetHome(svyBean, uuidUser, startDate, endDate);
		this.getAssetOffice(svyBean, uuidUser, startDate, endDate);
		this.getAssetStreet(svyBean, uuidUser, startDate, endDate);
		this.getAssetVehicle(svyBean, uuidUser, startDate, endDate);
		this.getAssetIdentity(svyBean, uuidUser, startDate, endDate);
		this.getTotTask(svyBean, uuidUser);
		bean.setSurveyor(svyBean);
		return bean;
	}

	private DashboardMonitoringBean getSurveyorArea(
			DashboardMonitoringBean bean, long uuidUser) {
		Object[][] paramsArea = { { "uuidUser", uuidUser } };
		List areaList = this.getManagerDAO().selectAllNative(
				"survey.dm.getArea", paramsArea, null);
		BigDecimal[][] areaLoc = new BigDecimal[areaList.size()][2];
		for (int i = 0; i < areaList.size(); i++) {
			Map tempArea = (Map) areaList.get(i);
			if (i == 0) {
				bean.setAreaName((String) tempArea.get("d0"));
				bean.setAreaType((String) tempArea.get("d1"));
				bean.setRadius((int) tempArea.get("d2"));
				bean.setLatitude(new BigDecimal(tempArea.get("d3").toString()));
				bean.setLongitude(new BigDecimal(tempArea.get("d4").toString()));
			}
			if (tempArea.get("d5") != null && tempArea.get("d6") != null) {
				areaLoc[i][0] = new BigDecimal(tempArea.get("d5").toString());
				areaLoc[i][1] = new BigDecimal(tempArea.get("d6").toString());
			}
		}
		bean.setArea(areaLoc);
		return bean;
	}

	private DashboardMonitoringBean getTracking(DashboardMonitoringBean bean,
			long uuidUser, Date startDate, Date endDate) throws ParseException{
		Object[][] paramsTrack = { { Restrictions.eq("amMsuser.uuidMsUser", uuidUser) },
				{ Restrictions.between("datetime", startDate, endDate) } };

		String[][] orderTrack = { { "datetime", GlobalVal.ROW_ORDER_DESC } };
		Map track = this.getManagerDAO().list(TrLocationhistory.class, paramsTrack, orderTrack);
		List<TrLocationhistory> trackingList = (List) track
				.get(GlobalKey.MAP_RESULT_LIST);
		if (!trackingList.isEmpty()) {
			bean.setLatitude(trackingList.get(0).getLatitude());
			bean.setLongitude(trackingList.get(0).getLongitude());
		}
		bean.setTracking(trackingList);
		return bean;
	}

	private DashboardSvyBean getAssetHome(DashboardSvyBean bean, 
			long uuidUser, String startDate, String endDate) {
		MsAssettag assetTag = commonLogic.retrieveAssetTag(GlobalVal.ASSET_TAG_HOME);
		Object[][] params = { { "uuidUser", uuidUser },
				{ "asset", GlobalVal.ASSET_TAG_HOME }, {"start", startDate}, {"end", endDate}, 
				{"assetImage", assetTag.getAssetTagImage() }};
		List asset = this.getManagerDAO().selectAllNative("survey.dm.getAssetHome",
				params, null);
		bean.setAssetHome(asset);
		return bean;
	}

	private DashboardSvyBean getAssetVehicle(DashboardSvyBean bean,
			long uuidUser, String startDate, String endDate) {
		MsAssettag assetTag = commonLogic.retrieveAssetTag(GlobalVal.ASSET_TAG_VEHICLE);
		Object[][] params = { { "uuidUser", uuidUser },
				{ "asset", GlobalVal.ASSET_TAG_VEHICLE }, {"start", startDate}, {"end", endDate}, 
				{"assetImage", assetTag.getAssetTagImage() }};
		List asset = this.getManagerDAO().selectAllNative("survey.dm.getAssetVehicle",
				params, null);
		bean.setAssetVehicle(asset);
		return bean;
	}

	private DashboardSvyBean getAssetStreet(DashboardSvyBean bean,
			long uuidUser, String startDate, String endDate) {
		MsAssettag assetTag = commonLogic.retrieveAssetTag(GlobalVal.ASSET_TAG_STREET);
		Object[][] params = { { "uuidUser", uuidUser },
				{ "asset", GlobalVal.ASSET_TAG_STREET }, {"start", startDate}, {"end", endDate}, 
				{"assetImage", assetTag.getAssetTagImage() }};
		List asset = this.getManagerDAO().selectAllNative("survey.dm.getAssetDriveway",
				params, null);
		bean.setAssetStreet(asset);
		return bean;
	}

	private DashboardSvyBean getAssetOffice(DashboardSvyBean bean,
			long uuidUser, String startDate, String endDate) {
		MsAssettag assetTag = commonLogic.retrieveAssetTag(GlobalVal.ASSET_TAG_OFFICE);
		Object[][] params = { { "uuidUser", uuidUser },
				{ "asset", GlobalVal.ASSET_TAG_OFFICE }, {"start", startDate}, {"end", endDate}, 
				{"assetImage", assetTag.getAssetTagImage() }};
		List asset = this.getManagerDAO().selectAllNative("survey.dm.getAssetOffice",
				params, null);
		bean.setAssetOffice(asset);
		return bean;
	}

	private DashboardSvyBean getAssetIdentity(DashboardSvyBean bean,
			long uuidUser, String startDate, String endDate) {
		MsAssettag assetTag = commonLogic.retrieveAssetTag(GlobalVal.ASSET_TAG_IDENTITY);
		Object[][] params = { { "uuidUser", uuidUser },
				{ "asset", GlobalVal.ASSET_TAG_IDENTITY }, {"start", startDate}, {"end", endDate}, 
				{"assetImage", assetTag.getAssetTagImage() }};
		List asset = this.getManagerDAO().selectAllNative("survey.dm.getAssetIdentity",
				params, null);
		bean.setAssetIdentity(asset);
		return bean;
	}

	private DashboardMonitoringBean getTaskRecapitulation(long uuidSvy,
			long idSubsystem, DashboardMonitoringBean bean, String startDate, String endDate,
			AuditContext callerId) {
		Object[][] params = { { "uuidUser", uuidSvy },
				{ "idSubsystem", idSubsystem }, {"start", startDate}, {"end", endDate}};
		Object[] taskRecapitulation = (Object[]) this.getManagerDAO()
				.selectOneNative("survey.dm.getTaskRecapitulation", params);
		bean.setCountNewTask((int) taskRecapitulation[0]);
		bean.setCountRead((int) taskRecapitulation[1]);
		bean.setCountDownloaded((int) taskRecapitulation[2]);
		bean.setCountTaskSubmitted((int) taskRecapitulation[3]);
		bean.setCountUploadingImg((int) taskRecapitulation[4]);
		bean.setCountOutVer((int) taskRecapitulation[5]);
		bean.setCountStart((int) taskRecapitulation[6]);
		return bean;
	}

	private DashboardSvyBean getTotTask(DashboardSvyBean bean, long uuidUser) {
		DateTime dt = new DateTime();
		int days = dt.getDayOfWeek();
		DateTime endThisWeek = new DateTime();
		DateTime startThisWeek;
		if (days > 1) {
			startThisWeek = dt.minusDays(endThisWeek.getDayOfWeek() - 1);
		}
		else {
			startThisWeek = dt;
		}
		
		bean.setCntThisWeek(endThisWeek.getDayOfWeek());

		DateTime endLastWeek = startThisWeek.minusDays(1);
		bean.setCntLastWeek(endLastWeek.getDayOfWeek());
		DateTime startLastWeek = startThisWeek.minusWeeks(1);

		DateTime endThisMonth = dt;
		bean.setCntThisMonth(endThisMonth.getDayOfMonth());
		DateTime startThisMonth = dt.minusDays(dt.getDayOfMonth() - 1);

		DateTime endLastMonth = startThisMonth.minusDays(1);
		bean.setCntLastMonth(endLastMonth.getDayOfMonth());
		DateTime startLastMonth = startThisMonth.minusMonths(1);

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		
		Object[][] params = { { "uuidUser", uuidUser },
				{ "startTW",  formatter.format(startThisWeek.toDate())},
				{ "endTW", formatter.format(endThisWeek.toDate())},
				{ "startLW", formatter.format(startLastWeek.toDate())},
				{ "endLW", formatter.format(endLastWeek.toDate())},
				{ "startTM", formatter.format(startThisMonth.toDate())},
				{ "endTM", formatter.format(endThisMonth.toDate())},
				{ "startLM", formatter.format(startLastMonth.toDate())},
				{ "endLM", formatter.format(endLastMonth.toDate())},
				{ "currentDate", formatter.format(new DateTime().toDate())} };
		Object[] resultTask = (Object[]) this.getManagerDAO().selectOneNative(
				"survey.dm.getTotalTaskSurveyor", params);

		bean.setAssignThisWeek((int) (resultTask[0] == null ? 0 : resultTask[0]));
		bean.setAssignLastWeek((int) (resultTask[1] == null ? 0 : resultTask[1]));
		bean.setSubmitThisWeek((int) (resultTask[2] == null ? 0 : resultTask[2]));
		bean.setSubmitLastWeek((int) (resultTask[3] == null ? 0 : resultTask[3]));
		bean.setAssignThisMonth((int) (resultTask[4] == null ? 0
				: resultTask[4]));
		bean.setAssignLastMonth((int) (resultTask[5] == null ? 0
				: resultTask[5]));
		bean.setSubmitThisMonth((int) (resultTask[6] == null ? 0
				: resultTask[6]));
		bean.setSubmitLastMonth((int) (resultTask[7] == null ? 0
				: resultTask[7]));
		bean.setNewSurveyTask((int) (resultTask[8] == null ? 0 : resultTask[8]));
		return bean;
	}

	private DashboardBranchBean getInfoTask(DashboardBranchBean bean,
			long uuidBranch, AuditContext callerId) {
		DateTime dt = new DateTime();
		int days = dt.getDayOfWeek();
		DateTime endThisWeek = new DateTime();
		DateTime startThisWeek;
		if (days > 1){
			startThisWeek = dt.minusDays(endThisWeek.getDayOfWeek() - 1);
		}
		else {
			startThisWeek = dt;
		}
		
		bean.setCntThisWeek(endThisWeek.getDayOfWeek());

		DateTime endLastWeek = startThisWeek.minusDays(1);
		bean.setCntLastWeek(endLastWeek.getDayOfWeek());
		DateTime startLastWeek = startThisWeek.minusWeeks(1);

		DateTime endThisMonth = dt;
		bean.setCntThisMonth(endThisMonth.getDayOfMonth());
		DateTime startThisMonth = dt.minusDays(dt.getDayOfMonth() - 1);

		DateTime endLastMonth = startThisMonth.minusDays(1);
		bean.setCntLastMonth(endLastMonth.getDayOfMonth());
		DateTime startLastMonth = startThisMonth.minusMonths(1);

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		Object[][] params = { { "uuidBranch", uuidBranch },
				{ "startTW", formatter.format(startThisWeek.toDate())},
				{ "endTW", formatter.format(endThisWeek.toDate()) },
				{ "startLW", formatter.format(startLastWeek.toDate()) },
				{ "endLW", formatter.format(endLastWeek.toDate()) },
				{ "startTM", formatter.format(startThisMonth.toDate()) },
				{ "endTM", formatter.format(endThisMonth.toDate()) },
				{ "startLM", formatter.format(startLastMonth.toDate() )},
				{ "endLM", formatter.format(endLastMonth.toDate() )},
				{ "currentDate", formatter.format(new DateTime().toDate())} };
		Object[] resultTask = (Object[]) this.getManagerDAO().selectOneNative(
				"survey.dm.getTotalTaskBranchByBranch", params);
		bean.setAssignToday((int) (resultTask[0] == null ? 0 : resultTask[0]));
		bean.setAssignThisWeek((int) (resultTask[1] == null ? 0 : resultTask[1]));
		bean.setAssignLastWeek((int) (resultTask[2] == null ? 0 : resultTask[2]));
		bean.setAssignThisMonth((int) (resultTask[3] == null ? 0
				: resultTask[3]));
		bean.setAssignLastMonth((int) (resultTask[4] == null ? 0
				: resultTask[4]));
		bean.setSubmitToday((int) (resultTask[5] == null ? 0 : resultTask[5]));
		bean.setSubmitThisWeek((int) (resultTask[6] == null ? 0 : resultTask[6]));
		bean.setSubmitLastWeek((int) (resultTask[7] == null ? 0 : resultTask[7]));
		bean.setSubmitThisMonth((int) (resultTask[8] == null ? 0
				: resultTask[8]));
		bean.setSubmitLastMonth((int) (resultTask[9] == null ? 0
				: resultTask[9]));
		bean.setNewTaskToday((int) (resultTask[10] == null ? 0 : resultTask[10]));
		bean.setNewTaskThisWeek((int) (resultTask[11] == null ? 0
				: resultTask[11]));
		bean.setNewTaskThisMonth((int) (resultTask[12] == null ? 0
				: resultTask[12]));

		return bean;
	}

	@Override
	public List getOtherSvy(Object params,String flag, AuditContext callerId) {
		List result = null;
		if (flag.equals("1")) {
			result = getUserExcList(params, callerId);
		}
		if (flag.equals("0")) {
			result = getUserExcListAssignInOut(params, callerId);
		}
		return result;
	}
	
	@Override
	public int getOtherSvyCount(Object params,AuditContext callerId){
		String[][] tmp = (String[][])params;
		String[][] paramsCnt = {{tmp[0][0],tmp[0][1]},{tmp[1][0],tmp[1][1]}};
		int result = (int)this.getManagerDAO().selectOneNative("survey.dm.getUsersExcCnt", paramsCnt);
		return result;
	}

	@Override
	public Map getTaskLists(String assignType, String modeInOut, long idSvy,
			String idSvySelected, AmMsuser spv, List otherSvy, int pageNumber,
			int pageSize,
			AuditContext callerId) {
		Map result = Collections.EMPTY_MAP;
		long idSubsystem = spv.getAmMssubsystem().getUuidMsSubsystem();
		if ("1".equals(modeInOut)) {
			long idStatus = getIdStatusTask(
					GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION, idSubsystem);
			Map<String, Object> paramMap = new HashMap<>();
			StringBuilder condition = new StringBuilder();
			condition.append(" and tth.msStatustask.uuidStatusTask=:uuidStatusTask");
			condition.append(" and tth.amMsuser.uuidMsUser=:uuidMsUser");
			paramMap.put("uuidMsUser", idSvy);
			paramMap.put("uuidStatusTask", idStatus);
			
			StringBuilder orderQuery = new StringBuilder();
			orderQuery.append(" order by tth.taskId ASC");
			
			result = this.getManagerDAO().selectAll(
					"from TrTaskH tth join fetch tth.msStatustask join fetch tth.amMsuser join fetch tth.msPriority "
					+ "where 1=1"
						+ condition.toString() + orderQuery.toString(),
				"select count(*) from TrTaskH tth join tth.msStatustask join tth.amMsuser where 1=1"
						+ condition.toString(),
				paramMap, pageNumber, pageSize);
		} 
		else {
			if (assignType == null || assignType.isEmpty()){
				assignType = "0";
			}
			
			if ("0".equals(assignType)) {
				long idStatus = getIdStatusTask(
						GlobalVal.SURVEY_STATUS_TASK_UNASSIGN, idSubsystem);
				
				Map<String, Object> paramMap = new HashMap<>();
				StringBuilder condition = new StringBuilder();
				condition.append(" and tth.msStatustask.uuidStatusTask=:uuidStatusTask");
				condition.append(" and tth.msBranch.uuidBranch=:uuidBranch");
				paramMap.put("uuidBranch", spv.getMsBranch().getUuidBranch());
				paramMap.put("uuidStatusTask", idStatus);
				
				StringBuilder orderQuery = new StringBuilder();
				orderQuery.append(" order by tth.taskId ASC");
				
				result = this.getManagerDAO().selectAll(
						"from TrTaskH tth join fetch tth.msStatustask join fetch tth.msBranch "
						+ "join fetch tth.msPriority where 1=1"
							+ condition.toString() + orderQuery.toString(),
					"select count(*) from TrTaskH tth join tth.msStatustask join tth.msBranch where 1=1"
							+ condition.toString(),
					paramMap, pageNumber, pageSize);
			} 
			else {
				if (idSvySelected == null || idSvySelected.isEmpty()) {
					Map temp = (Map) otherSvy.get(0);
					idSvySelected = temp.get("d0").toString();
				}
				long idStatus = getIdStatusTask(
						GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION,
						idSubsystem);
				
				Map<String, Object> paramMap = new HashMap<>();
				StringBuilder condition = new StringBuilder();
				condition.append(" and tth.msStatustask.uuidStatusTask=:uuidStatusTask");
				condition.append(" and tth.amMsuser.uuidMsUser=:uuidMsUser");
				paramMap.put("uuidMsUser", Long.valueOf(idSvySelected));
				paramMap.put("uuidStatusTask", idStatus);
				
				StringBuilder orderQuery = new StringBuilder();
				orderQuery.append(" order by tth.taskId ASC");
				
				result = this.getManagerDAO().selectAll(
						"from TrTaskH tth join fetch tth.msStatustask join fetch tth.amMsuser "
						+ "join fetch tth.msPriority where 1=1"
							+ condition.toString() + orderQuery.toString(),
					"select count(*) from TrTaskH tth join tth.msStatustask join tth.amMsuser where 1=1"
							+ condition.toString(),
					paramMap, pageNumber, pageSize);
			}
		}
		return result;
	}

	private long getIdStatusTask(String statusTask, long idSubsystem) {
		Object[][] params = { { Restrictions.eq("statusCode", statusTask) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", idSubsystem) } };
		MsStatustask result = this.getManagerDAO().selectOne(
				MsStatustask.class, params);
		return result.getUuidStatusTask();
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveAssignIn(String idTasks, String assignType, long idSvy,
			AmMsuser actor, AuditContext callerId) {
		String idTask[] = idTasks.split(",");
		if (ArrayUtils.isNotEmpty(idTask)) {
		    AmMsuser surveyor = this.getManagerDAO().selectOne(AmMsuser.class, idSvy);
		    
			for (int i = 0; i < idTask.length; i++) {
				if (StringUtils.isEmpty(idTask[i])){
					continue;
				}
				
				String[] id = idTask[i].split(";");
				TrTaskH obj = this.getManagerDAO().selectOne(
						"from TrTaskH tth join fetch tth.msStatustask mst join fetch mst.amMssubsystem "
						+ "where tth.uuidTaskH = :uuidTaskH", 
						new Object[][] {{"uuidTaskH", Long.valueOf(id[0])}});
				if (id[1] != "?") {
					MsPriority pr = this.getManagerDAO().selectOne(MsPriority.class, Long.valueOf(id[1]));
					obj.setMsPriority(pr);
				}
				this.updateAssignIn(obj, assignType, idSvy, actor, callerId);
				
				if ("0".equals(assignType)) {
					this.insertTaskHistory(obj, GlobalVal.NOTES_ASSIGN_IN,
							actor.getFullName(), surveyor.getFullName(), GlobalVal.CODE_PROCESS_ASSIGNMENT, callerId);
					
					if (GlobalVal.SUBSYSTEM_MO.equals(obj.getFlagSource())) {
						this.commitOrder(actor, GlobalVal.NOTES_ASSIGN_IN, obj, 
								obj.getMsStatustask().getAmMssubsystem(), 0, GlobalVal.CODE_PROCESS_ASSIGNMENT, 
								callerId);
					}
				}
				else {
					this.insertTaskHistory(obj, GlobalVal.NOTES_ASSIGN_IN,
							actor.getFullName(), surveyor.getFullName(), 
							GlobalVal.CODE_PROCESS_REASSIGNMENT, callerId);
				}
			}
		}

	}

	private TrTaskH updateAssignIn(TrTaskH obj, String assignType,
			long idSvy, AmMsuser actor, AuditContext callerId) {
		if ("0".equals(assignType)) {
			String processCode = MssWorkflowProcess.MS_CORE.toString();
			String[][] params = { { "processCode", processCode } };
			Long uuidProcess = Long.valueOf(this.getManagerDAO().selectOneNativeString(
					"select UUID_PROCESS from WF_PROCESS where PROCESS_CODE = :processCode", params).toString());
			String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess, obj.getUuidTaskH());
			Object[][] params2 = {
					{ Restrictions.eq("statusCode", statusCode) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
							actor.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(
					MsStatustask.class, params2);
			obj.setMsStatustask(msStatustask);
			Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_NEW) } };
			MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
			obj.setMsStatusmobile(msm);
		}
		else {
			Object[][] params = {
					{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
							actor.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(
					MsStatustask.class, params);
			obj.setMsStatustask(msStatustask);
		}
		obj.setApprovalDate(null);
		obj.setDownloadDate(null);
		obj.setSubmitDate(null);
		obj.setReadDate(null);
		obj.setStartDtm(null);
		obj.setRfaDate(null);
		obj.setSubmitDate(null);
		obj.setAssignDate(new Date());
		obj.setAmMsuser(this.getManagerDAO().selectOne(AmMsuser.class, idSvy));
		obj.setUsrUpd(callerId.getCallerId());
		obj.setDtmUpd(new Date());
		this.getManagerDAO().update(obj);
		return obj;
	}

	private void insertTaskHistory(TrTaskH task, String note, String actor,
			String idSvy, String processCode, AuditContext callerId) {
		TrTaskhistory obj = new TrTaskhistory();
		obj.setDtmCrt(new Date());
		obj.setUsrCrt(callerId.getCallerId());
		obj.setActor(actor);
		obj.setFieldPerson(idSvy);
		obj.setNotes(note);
		obj.setTrTaskH(task);
		obj.setMsStatustask(task.getMsStatustask());
		obj.setCodeProcess(processCode);
		this.getManagerDAO().insert(obj);
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveAssignOut(String idTasks, String idSvy,
			String idSvySelected, AmMsuser actor, AuditContext callerId) {

		String idTask[] = idTasks.split(",");
		if (ArrayUtils.isNotEmpty(idTask)) {
	        Object[][] params = {
	                { Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION) },
	                { Restrictions.eq("amMssubsystem.uuidMsSubsystem", actor.getAmMssubsystem().getUuidMsSubsystem()) } };
	        MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
	        AmMsuser selectedSurveyor = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(idSvySelected));
		    
			for (int i = 0; i < idTask.length; i++) {
				if (idTask[i].isEmpty()){
					continue;
				}
					
				TrTaskH obj = this.getManagerDAO().selectOne(TrTaskH.class, Long.valueOf(idTask[i]));
				this.updateAssignOut(obj, selectedSurveyor, actor, msStatustask, callerId);
				this.insertTaskHistory(obj, GlobalVal.NOTES_ASSIGN_OUT, actor.getFullName(),
				        selectedSurveyor.getFullName(),
				        GlobalVal.CODE_PROCESS_REASSIGNMENT, callerId);
			}
		}
	}

	private void updateAssignOut(TrTaskH obj, AmMsuser selectedSurveyor,
			AmMsuser actor, MsStatustask msStatustask, AuditContext callerId) {
		obj.setAmMsuser(selectedSurveyor);
		obj.setMsStatustask(msStatustask);
		obj.setApprovalDate(null);
		obj.setDownloadDate(null);
		obj.setSubmitDate(null);
		obj.setReadDate(null);
		obj.setStartDtm(null);
		obj.setRfaDate(null);
		obj.setSubmitDate(null);
		obj.setAssignDate(new Date());
		obj.setUsrUpd(callerId.getCallerId());
		obj.setDtmUpd(new Date());
		this.getManagerDAO().update(obj);
	}

	@Override
	public List getTaskMonitoring(String uuidSvy, String uuidSubsystem,
			AuditContext callerId) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		DateTime currentTime = new DateTime();
		currentTime = currentTime.minusMillis(currentTime.getMillisOfDay());
		Date minDate = currentTime.toDate();
		currentTime = currentTime.plusDays(1).minusMillis(3);
		Date maxDate = currentTime.toDate();
		String[][] params = { { "uuidSvy", uuidSvy },
				{"start", formatter.format(minDate)}, {"end", formatter.format(maxDate)} };
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.taskmonitoring.load", params, null);
		if("1".equals(link_encrypt)){
			List resultNew = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				String[] toBeEncrypt = {map.get("d0").toString()};
				map.put("d0", CipherTool.encryptData(toBeEncrypt).get(0).toString());
				resultNew.add(map);
			}
			return resultNew;
		}else{
			return result;
		}
	}

	@Override
	public AmMsuser getUser(String uuidSvy, AuditContext callerId) {
		if (uuidSvy.contains("@")){
		    uuidSvy = uuidSvy.substring(0,uuidSvy.indexOf("@"));
		}
		
		AmMsuser result = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(uuidSvy));
		return result;
	}

	@Override
	public List getSubmittedTaskList(long uuidSvy, long uuidSubsystem, AuditContext callerId) {
		DateTime currentTime = new DateTime();
		currentTime = currentTime.minusMillis(currentTime.getMillisOfDay());
		Date minDate = currentTime.toDate();
		currentTime = currentTime.plusDays(1).minusMillis(3);
		Date maxDate = currentTime.toDate();
		
		String[] status = { GlobalVal.SURVEY_STATUS_TASK_APPROVAL, GlobalVal.SURVEY_STATUS_TASK_UPLOADING,
				GlobalVal.SURVEY_STATUS_TASK_VERIFICATION, GlobalVal.SURVEY_STATUS_TASK_RELEASED };
		Object[][] prm = {{Restrictions.in("statusCode", status)}, 
				{Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidSubsystem)}};
		Map statusResult = this.getManagerDAO().list(MsStatustask.class, prm, null);
		List<MsStatustask> uuidResult = (List) statusResult.get(GlobalKey.MAP_RESULT_LIST);
		Long[] uuidStatus = new Long[uuidResult.size()];
		for (int i = 0; i < uuidResult.size(); i++) {
			uuidStatus[i] = uuidResult.get(i).getUuidStatusTask();
		}
		
		Object[][] params = {{Restrictions.eq("amMsuser.uuidMsUser", uuidSvy)}, 
				{Restrictions.between("dtmUpd", minDate, maxDate) },
			{Restrictions.in("msStatustask.uuidStatusTask", uuidStatus)} };
		String[][] orders = { { "submitDate", GlobalVal.ROW_ORDER_ASC } };
		
		Map resultMap = this.getManagerDAO().selectAll(TrTaskH.class, params, orders);
		List result = (List) resultMap.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}

	@Override
	public List getOutstandingTaskList(long uuidSvy, long uuidSubsystem, AuditContext callerId) {
		Object[][] prm = {{Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION)},
		        {Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidSubsystem)}};
		MsStatustask statusResult = this.getManagerDAO().selectOne(MsStatustask.class, prm);
		
		Map resultMap = this.getManagerDAO().list(
				"from TrTaskH tth join fetch tth.amMsuser am join fetch tth.msStatustask mst "
				+ "where tth.amMsuser.uuidMsUser = :uuidMsUser and "
				+ "tth.submitDate is null and tth.msStatustask.uuidStatusTask = :uuidStatusTask order by tth.assignDate asc", 
				new Object[][] {{"uuidMsUser", uuidSvy}, {"uuidStatusTask", statusResult.getUuidStatusTask()}});
		List<TrTaskH> result = (List) resultMap.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}

	@Override
	public DashboardMonitoringBean surveyorTracking(long uuidSvy,
			String startDate, String endDate, AuditContext callerId) throws ParseException{
		DashboardMonitoringBean result = new DashboardMonitoringBean();
		AmMsuser svy = this.getManagerDAO().selectOne(AmMsuser.class, uuidSvy);
		result.setUuidMsUser(svy.getUuidMsUser());
		result.setFullName(svy.getFullName());
		result.setIsLoggedIn(svy.getIsLoggedIn());
		result = getSurveyorArea(result, uuidSvy);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		result = getTracking(result, uuidSvy, formatter.parse(startDate), formatter.parse(endDate));	
		return result;
	}

	@Override
	public List getForms(long uuidSubsystem, AuditContext callerId) {
		Object[][] params = {{Restrictions.eq("amMssubsystem.uuidMsSubsystem", uuidSubsystem)},
		        {Restrictions.eq("isActive","1")}};
		String[][] order = {{"formName", GlobalVal.ROW_ORDER_ASC}};
		Map resultMap = this.getManagerDAO().list(MsForm.class, params, order);
		List result = (List) resultMap.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}

	@Override
	public List getPriority(AuditContext callerId) {
		Object[][] param = {{Restrictions.eq("isActive","1")}};
		String[][] order = {{"uuidPriority", GlobalVal.ROW_ORDER_ASC}};
		Map resultMap = this.getManagerDAO().list(MsPriority.class, param, order);
		List result = (List) resultMap.get(GlobalKey.MAP_RESULT_LIST);
		return result;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveNewTask(TrTaskH newTask, long idSvy, AmMsuser actor, long groupTaskId, AuditContext callerId) {
		newTask.setUsrCrt(callerId.getCallerId());
		newTask.setDtmCrt(new Date());
		AmMsuser svy = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", 
			new Object[][] {{"uuidMsUser", idSvy}});
		newTask.setAmMsuser(svy);
		newTask.setMsBranch(svy.getMsBranch());
		newTask.setMsForm(this.getManagerDAO().selectOne(MsForm.class, newTask.getMsForm().getUuidForm()));
		newTask.setMsPriority(this.getManagerDAO().selectOne(
				MsPriority.class, newTask.getMsPriority().getUuidPriority()));
		newTask.setAssignDate(new Date());
		newTask.setFlagSource(GlobalVal.SPVADHOC);		
		newTask.setTaskSlaHour(null);
		newTask.setMsStatustask(null);
		newTask.setUsrUpd(callerId.getCallerId());
		newTask.setDtmUpd(new Date());
		
		this.getManagerDAO().insert(newTask);
		
		MsGrouptask groupTask = this.getManagerDAO().selectOne(MsGrouptask.class, groupTaskId);
		MsGrouptask newGroupTask = new MsGrouptask();
		
		newGroupTask.setDtmCrt(new Date());
		newGroupTask.setUsrCrt(callerId.getCallerId());
		newGroupTask.setTrTaskH(newTask);
		if (groupTask == null) {								
			newGroupTask.setCustomerName(newTask.getCustomerName());
			newGroupTask.setApplNo(newTask.getApplNo());
			newGroupTask.setMsBranch(newTask.getMsBranch());
			newGroupTask.setGroupTaskId(newTask.getUuidTaskH());
		}
		else {
			newGroupTask.setCustomerName(groupTask.getCustomerName());
			newGroupTask.setApplNo(groupTask.getApplNo());
			newGroupTask.setMsBranch(groupTask.getMsBranch());
			newGroupTask.setGroupTaskId(groupTask.getGroupTaskId());
		}
		
		this.getManagerDAO().insert(newGroupTask);
		String processCode = MssWorkflowProcess.MS_ADHOC_SPV.toString();
		String[][] params = { { "processCode", processCode } };
		BigInteger uuidProcess = (BigInteger) this.getManagerDAO().selectOneNativeString(
				"select UUID_PROCESS from WF_PROCESS with (nolock) where PROCESS_CODE = :processCode", params);
		String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess.longValue(), newTask.getUuidTaskH());
	
		Object[][] params2 = {
				{ Restrictions.eq("statusCode", statusCode) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
						actor.getAmMssubsystem().getUuidMsSubsystem()) } };
		MsStatustask msStatustask = this.getManagerDAO().selectOne(
				MsStatustask.class, params2);
		newTask.setMsStatustask(msStatustask);
		
		newTask.setTaskId(String.valueOf(newTask.getUuidTaskH()));
		this.getManagerDAO().update(newTask);
		
		insertTaskHistory(newTask, newTask.getNotes(),
			actor.getFullName(), this.getManagerDAO().selectOne(AmMsuser.class, idSvy)
			.getFullName(), GlobalVal.CODE_PROCESS_ASSIGNMENT, callerId);
			
	}
	
	@Override
    public DashboardMonitoringBean retrieveInfoWindowData(long uuidSurveyor,
    		long uuidSubsystem, AuditContext callerId) {
        if (uuidSurveyor == 0l){
            return null;
        }
        
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        DateTime currentTime = new DateTime();
        currentTime = currentTime.minusMillis(currentTime.getMillisOfDay());
        Date minDate = currentTime.toDate();
        currentTime = currentTime.plusDays(1).minusMillis(3);
        Date maxDate = currentTime.toDate();
        
        String minDateStr = formatter.format(minDate);
        String maxDateStr = formatter.format(maxDate);
                
        DashboardMonitoringBean bean = new DashboardMonitoringBean();
        bean.setUuidMsUser(Long.valueOf(uuidSurveyor));
        
        String[][] paramsGetDate = { {"uuidUser", String.valueOf(bean.getUuidMsUser())},
                {"start", minDateStr},
                {"end", maxDateStr} };
        
        bean.setAttendanceIn((String) this.getManagerDAO().selectOneNative(
                "survey.dm.getAttendanceIn", paramsGetDate));   
        
        this.getTaskRecapitulation(uuidSurveyor, uuidSubsystem, bean, minDateStr, maxDateStr, callerId);        
        this.fillPerformanceData(bean, uuidSurveyor);
        String atb_code= GlobalKey.MSATTR_LAST_PERCENTAGE_BATTERY;
        this.getPercentageBattery(uuidSurveyor,uuidSubsystem, atb_code ,bean);
        return bean;
    }

    private void fillPerformanceData(DashboardMonitoringBean bean, long uuidUser) {
        if (bean == null){
            throw new IllegalArgumentException("bean cannot be null");
        }
        
        DashboardSvyBean svyBean = new DashboardSvyBean();
        this.getTotTask(svyBean, uuidUser);
        bean.setSurveyor(svyBean);        
    }

    @Override
    public List<DashboardSvyBean> retrieveTagLegal(long[] uuidUsers, Date startDate, Date endDate,
            AuditContext callerId) {
        if (ArrayUtils.isEmpty(uuidUsers)){
            return Collections.emptyList();
        }
        
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        
        List<DashboardSvyBean> dsbList = new ArrayList<>();
        
        for (long uuidUser : uuidUsers) {
            DashboardSvyBean bean = new DashboardSvyBean();
            this.getAssetIdentity(bean, uuidUser, formatter.format(startDate), formatter.format(endDate));
            dsbList.add(bean);
        }
        
        return dsbList;
    }

    @Override
    public List<DashboardSvyBean> retrieveTagDriveway(long[] uuidUsers, Date startDate, Date endDate,
            AuditContext callerId) {
        if (ArrayUtils.isEmpty(uuidUsers)){
            return Collections.emptyList();
        }
        
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        
        List<DashboardSvyBean> dsbList = new ArrayList<>();        
        for (long uuidUser : uuidUsers) {
            DashboardSvyBean bean = new DashboardSvyBean();                
            this.getAssetStreet(bean, uuidUser, formatter.format(startDate), formatter.format(endDate));
            dsbList.add(bean);
        }
        
        return dsbList;
    }

    @Override
    public List<DashboardSvyBean> retrieveTagOffice(long[] uuidUsers, Date startDate, Date endDate,
            AuditContext callerId) {
        if (ArrayUtils.isEmpty(uuidUsers)){
            return Collections.emptyList();
        }
        
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        
        List<DashboardSvyBean> dsbList = new ArrayList<>();        
        for (long uuidUser : uuidUsers) {
            DashboardSvyBean bean = new DashboardSvyBean();                
            this.getAssetOffice(bean, uuidUser, formatter.format(startDate), formatter.format(endDate));
            dsbList.add(bean);
        }
        
        return dsbList;
    }

    @Override
    public List<DashboardSvyBean> retrieveTagVehicle(long[] uuidUsers, Date startDate, Date endDate,
            AuditContext callerId) {
        if (ArrayUtils.isEmpty(uuidUsers)){
            return Collections.emptyList();
        }
        
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        
        List<DashboardSvyBean> dsbList = new ArrayList<>();        
        for (long uuidUser : uuidUsers) {
            DashboardSvyBean bean = new DashboardSvyBean();
            this.getAssetVehicle(bean, uuidUser, formatter.format(startDate), formatter.format(endDate));
            dsbList.add(bean);
        }
        
        return dsbList;
    }

    @Override
    public List<DashboardSvyBean> retrieveTagHome(long[] uuidUsers, Date startDate, Date endDate,
            AuditContext callerId) {
        if (ArrayUtils.isEmpty(uuidUsers)){
            return Collections.emptyList();
        }
        
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        
        List<DashboardSvyBean> dsbList = new ArrayList<>();        
        for (long uuidUser : uuidUsers) {
            DashboardSvyBean bean = new DashboardSvyBean();                
            this.getAssetHome(bean, uuidUser,
                    formatter.format(startDate), formatter.format(endDate));
            dsbList.add(bean);
        }
        
        return dsbList;
    }

	@Override
	public List getDropDownList(Object params, Object order, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"task.inquirygrouptasksurvey.branchList", params, order);
		return result;
	}
	
	@Override
	public Map getGroupTaskList(Object[][] params, Object order, int pageNo, String load, AuditContext callerId) {
		Map<String, Object> result = new HashMap();
		List listCriteria = new ArrayList();
		Integer countCriteria = 0;
		Object[][] queryParamList = {{ params[0][0], params[0][1]},
				{ params[1][0], params[1][1]}, { params[2][0], params[2][1]},
				{ "start",  String.valueOf((pageNo-1)*GlobalVal.ROW_PER_PAGE_LOOKUP+1)},
				{ "end",   String.valueOf((pageNo-1)*GlobalVal.ROW_PER_PAGE_LOOKUP+GlobalVal.ROW_PER_PAGE_LOOKUP)}};
		Object[][] queryParamCount = {{ params[0][0], params[0][1]},
				{ params[1][0], params[1][1]}, { params[2][0], params[2][1]}};
		if("specific".equals(load)){
			listCriteria = this.getManagerDAO().selectAllNative(
					"survey.dm.getGroupTaskList", queryParamList, order);
			countCriteria = (Integer)this.getManagerDAO().selectOneNative(
					"survey.dm.getGroupTaskListCount", queryParamCount);
		}
		else{
			listCriteria = this.getManagerDAO().selectAllNative(
					"survey.dm.getGroupTaskListAll", queryParamList, order);
			countCriteria = (Integer)this.getManagerDAO().selectOneNative(
					"survey.dm.getGroupTaskListCountAll", queryParamCount);
		}
		result.put(GlobalKey.MAP_RESULT_LIST, listCriteria);
		result.put(GlobalKey.MAP_RESULT_SIZE, countCriteria);
		return result;
	}
	
	@Override
	public List getFormCombo(Object params, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.taskmonitoring.getFormByGroupTask", params, null);
		Collections.sort(result, new Comparator<Map<String, String>>() {

			@Override
			public int compare(Map<String, String> m1, Map<String, String> m2) {
		        return m1.get("d1").compareTo(m2.get("d1"));
		    }
		});
		
		return result;
	}
	
	@Override
	public boolean checkIsSpv(AmMsuser amMsUser, AuditContext auditContext) {
		Object[][] params = { 
				{Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MS+GlobalKey.GENERALSETTING_JOBSPV)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if (ArrayUtils.contains(StringUtils.split(amGeneralsetting.getGsValue(), ";"), amMsUser.getMsJob().getJobCode())) {
			return true;
		}
		return false;
	}
	
	@Override
	public AmMsuser getAmSpv(long uuidSpv, AuditContext auditContext) {
		AmMsuser amMsUser = this.getManagerDAO().selectOne(
			"from AmMsuser u join fetch u.amMssubsystem join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", 
			new Object[][] {{"uuidMsUser", uuidSpv}});
		return amMsUser;
	}
	
	@Override
	public Map getSpv(int pageNo, int pageSize, String uuidMsUser, AuditContext auditContext) {
		Map mapResult = new HashMap();
		Object[][] paramsJob = { 
				{Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MS+GlobalKey.GENERALSETTING_JOBSPV)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsJob);
		
		List listJobs = Arrays.asList(StringUtils.split(amGeneralsetting.getGsValue(), ";"));
		Object[][] params = { {"start", ( pageNo-1 ) * pageSize + 1 }, 
				{ "end", ( pageNo-1 ) * pageSize + pageSize }, {"jobCode", listJobs},
				{"uuidMsUser", uuidMsUser}};
		List result = this.getManagerDAO().selectAllNative("survey.dm.getSpvList", params, null);
		
		Object[][] paramsCount = { {"jobCode", listJobs}, {"uuidMsUser", uuidMsUser} };
		Integer total = (Integer)this.getManagerDAO().selectOneNative("survey.dm.getSpvListCount", paramsCount);	
		
		mapResult.put(GlobalKey.MAP_RESULT_LIST, result);
		mapResult.put(GlobalKey.MAP_RESULT_SIZE, total);
		return mapResult;
	}
	
	@Override
	public Map<String, Object> getHierarkiBranchLogin(Object params, Object orders,
			int pageNumber, int pageSize, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String,Object>();
		String[][] tempParam = (String[][])params;
		List listCriteria = new ArrayList();
		Integer countCriteria = 0;

		if(StringUtils.isBlank(tempParam[0][1])){
			tempParam[0][1] = "%";
		}
		if(StringUtils.isBlank(tempParam[1][1])){
			tempParam[1][1] = "%";
		}	
		
		String[][] queryParamList = {{ tempParam[0][0], tempParam[0][1]},
				{ tempParam[1][0], tempParam[1][1]}, { tempParam[2][0], tempParam[2][1]},
				{ "start",  String.valueOf((pageNumber-1)*pageSize+1)},
				{ "end",   String.valueOf((pageNumber-1)*pageSize+pageSize)}};
		String[][] queryParamCount = {{ tempParam[0][0], tempParam[0][1]},
				{ tempParam[1][0], tempParam[1][1]}, { tempParam[2][0], tempParam[2][1]}};
		
		String queryListName = "survey.dm.getListHierarkiCabang";
		String queryCountName = "survey.dm.getListHierarkiCabangCount";
		
		listCriteria = this.getManagerDAO().selectAllNative(queryListName, queryParamList, null);
		countCriteria = (Integer)this.getManagerDAO().selectOneNative(queryCountName, queryParamCount);
	
		result.put(GlobalKey.MAP_RESULT_LIST, listCriteria);
		result.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(countCriteria));
			
		return result;
	}
	
	@Override
	public Map getSpvByBranch(String uuidBranch, int pageNo, int pageSize, AuditContext auditContext) {
		Map mapResult = new HashMap();
		Object[][] paramsJob = { 
				{Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MS+GlobalKey.GENERALSETTING_JOBSPV)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramsJob);
		
		List listJobs = Arrays.asList(StringUtils.split(amGeneralsetting.getGsValue(), ";"));
		Object[][] params = { {"start", ( pageNo-1 ) * pageSize + 1 }, 
				{ "end", ( pageNo-1 ) * pageSize + pageSize }, 
				{"jobCode", listJobs}, {"uuidBranch", uuidBranch} };
		List result = this.getManagerDAO().selectAllNative("survey.dm.getSpvListByBranch", params, null);
		
		Object[][] paramsCount = { {"jobCode", listJobs}, {"uuidBranch", uuidBranch} };
		Integer total = (Integer)this.getManagerDAO().selectOneNative(
				"survey.dm.getSpvListByBranchCount", paramsCount);	
		
		if("1".equals(this.link_encrypt)){
			List newRes = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				String[] temp = {map.get("d0").toString()};
				List res = CipherTool.encryptData(temp);
				map.put("d0", res.get(0).toString());
				newRes.add(map);
			}
			mapResult.put(GlobalKey.MAP_RESULT_LIST, newRes);
		}else{
			mapResult.put(GlobalKey.MAP_RESULT_LIST, result);
		}
		
		mapResult.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(total));
		return mapResult;
	}
	
	@Override
	public Map getGroupTaskListByHierarkiUser(Object[][] params, Object order, int pageNo, 
			String load, AuditContext callerId) {
		Map<String, Object> result = new HashMap();
		List listCriteria = new ArrayList();
		Integer countCriteria = 0;
		Object[][] queryParamList = {{ params[0][0], params[0][1]},
				{ params[1][0], params[1][1]}, { params[2][0], params[2][1]}, { params[3][0], params[3][1]},
				{ "start",  String.valueOf((pageNo-1)*GlobalVal.ROW_PER_PAGE_LOOKUP+1)},
				{ "end",   String.valueOf((pageNo-1)*GlobalVal.ROW_PER_PAGE_LOOKUP+GlobalVal.ROW_PER_PAGE_LOOKUP)}};
		Object[][] queryParamCount = {{ params[0][0], params[0][1]},
				{ params[1][0], params[1][1]}, { params[2][0], params[2][1]}, { params[3][0], params[3][1]}};
		
		listCriteria = this.getManagerDAO().selectAllNative(
				"survey.dm.getGroupTaskListByHierarkiUser", queryParamList, order);
		countCriteria = (Integer)this.getManagerDAO().selectOneNative(
				"survey.dm.getGroupTaskListByHierarkiUserCount", queryParamCount);

		result.put(GlobalKey.MAP_RESULT_LIST, listCriteria);
		result.put(GlobalKey.MAP_RESULT_SIZE, countCriteria);
		return result;
	}
	
	@Override
	public List userList(long uuidSpv, AuditContext callerId) {
		Object params[][] = {
				{Restrictions.eq("amMsuser.uuidMsUser", uuidSpv)}, {Restrictions.eq("isActive", "1")}};
		Map result = this.getManagerDAO().selectAll(AmMsuser.class, params, null);
		List userList = (List)result.get(GlobalKey.MAP_RESULT_LIST);
		return userList;
	}

	@Override
	public List<?> getAllBranch(AuditContext callerId) {
	  List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getBranchAll", null, null);
		return result;
	}

	@Override
	public int getCountAllBranch(AuditContext callerId) {
		
		return 0;
	}
	
	@Override
	public List<?> getSurveyorBranchWithColour(String uuidbranch, String idxBranch,AuditContext callerId) {
		String[][] param = {{"uuidBranch",uuidbranch }, {"idxBranch", idxBranch}};
		List result = this.getManagerDAO().selectAllNative(
				"survey.dm.getSurveyorBranchWithColour", param, null);
		return result;
	}
	
	@Override
	public DashboardMonitoringBean getPercentageBattery(long uuidSvy, long idSubsystem, 
			String atb_code, DashboardMonitoringBean bean) {
		Object[][] params = { { "uuidUser", uuidSvy },
				{ "idSubsystem", idSubsystem }, {"atb_code", atb_code}};
		String precentageBattery = (String) this.getManagerDAO().selectOneNative(
				"survey.dm.getPrecentagebattery", params);
		if(precentageBattery == null){
			precentageBattery = "-";
		}
		bean.setPrecentageBattery(precentageBattery);
		return bean;
	}
	
	@Override
	public String getAutoupdateInterval(String gsCode, AuditContext callerId) {
		Object params[][] = {{Restrictions.eq("gsCode", gsCode)}};
		String result = StringUtils.EMPTY;
		AmGeneralsetting bean = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if(bean!=null) {
			result = bean.getGsValue(); 
		}
		else {
			result = 	"0";
		}
		return result ;
	}

}
