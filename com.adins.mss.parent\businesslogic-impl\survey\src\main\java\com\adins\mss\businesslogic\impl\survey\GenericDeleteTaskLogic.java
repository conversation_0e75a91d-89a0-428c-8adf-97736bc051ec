package com.adins.mss.businesslogic.impl.survey;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.jfree.util.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.DeleteTaskLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.model.MsSettingapprovalots;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TblCaeData;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.TrTasklink;
import com.adins.mss.services.model.common.AddTaskCAERequest;
import com.adins.mss.services.model.newconfins.UpdateStatusMainDealerResponse;
import com.adins.mss.util.CipherTool;
import com.google.gson.Gson;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericDeleteTaskLogic extends BaseLogic implements DeleteTaskLogic, MessageSourceAware {
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	private IntFormLogic intFormLogic;
	private static final Logger LOG = LoggerFactory.getLogger(GenericDeleteTaskLogic.class);
	private Gson gson = new Gson();

	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	@Override
	public List listDeleteTask(Object params, AuditContext callerId) {
		List result = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT * from ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("SELECT  ")
			.append("trth.UUID_TASK_H, ") 
			.append("trth.TASK_ID, ")
			.append("trth.CUSTOMER_NAME, ")
			.append("trth.CUSTOMER_ADDRESS,  ")
			.append("LEFT(CONVERT(VARCHAR, trth.ASSIGN_DATE, 113), 17) as ASSIGN_DATE, ")
			.append("ms.STATUS_TASK_DESC, ")
			.append("LEFT(CONVERT(VARCHAR, trth.PROMISE_DATE, 113), 17) as PROMISE_DATE, ")
			.append("ROW_NUMBER() OVER (ORDER BY trth.TASK_ID asc) AS rownum ")			
			.append("FROM TR_TASK_H trth with (nolock)  ")
			.append("LEFT JOIN MS_STATUSTASK ms with (nolock) ON ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK ")	
			.append("JOIN (SELECT keyValue as UUID_MS_USER FROM dbo.getUserByLogin(:uuidUser)) ubl on ubl.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("where ms.UUID_MS_SUBSYSTEM = :subsystem and ") 
			.append("ms.STATUS_CODE IN ('N','A','V','P') ")
			.append(paramsQueryString)
			.append(") a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[]{"subsystem", ((Object[][]) params)[6][1]});
		paramsStack.push(new Object[]{"uuidUser", ((Object[][]) params)[8][1]});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[12][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[13][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
	    
	    if("1".equals(this.link_encrypt)){
			//d0 = seqNo
			List newList = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				String[] toBeEncrypt = {map.get("d0").toString()};
				map.put("d0", CipherTool.encryptData(toBeEncrypt).get(0).toString());
				newList.add(map);
			}
			return newList;
		}
		return result;
	}
	
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null)
			return new StringBuilder();
		StringBuilder sb = new StringBuilder();
		//---CUSTOMER_NAME
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("and UPPER(trth.CUSTOMER_NAME) like UPPER('%' + :customerName + '%') ");
			paramStack.push(new Object[]{"customerName", (String) params[1][1]});
		}
		
		//---TASK_ID
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and UPPER(trth.TASK_ID) like UPPER('%' + :taskId + '%')  ");
			paramStack.push(new Object[]{"taskId", (String) params[0][1]});
		}
		
		//---CUSTOMER_ADDRESS
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("AND UPPER(trth.CUSTOMER_ADDRESS) like UPPER('%' + :customerAddress + '%') ");
			paramStack.push(new Object[]{"customerAddress", (String) params[2][1]});
		}	
		
		//---UUID_STATUS_TASK
		if (!StringUtils.equals("%", (String) params[5][1])) {
			sb.append("AND ms.UUID_STATUS_TASK = :statusTask ");
			paramStack.push(new Object[]{"statusTask", Long.valueOf((String) params[5][1])});
		}
		
		//---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[9][1])) {
			sb.append("and trth.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{"uuidBranch", Long.valueOf((String) params[9][1])});
		}
		
		if(!StringUtils.equals("%", (String) params[3][1]) && !StringUtils.equals("%", (String) params[4][1])){
			sb.append("and trth.ASSIGN_DATE BETWEEN :assignmentDateStart ");
			sb.append("AND :assignmentDateEnd ");
			paramStack.push(new Object[]{"assignmentDateStart", (String) params[3][1]});
			paramStack.push(new Object[]{"assignmentDateEnd", (String) params[4][1]});
		} 
		if(!StringUtils.equals("%", (String) params[10][1]) && !StringUtils.equals("%", (String) params[11][1])){
			sb.append("and trth.PROMISE_DATE BETWEEN :promiseDateStart ");
			sb.append("AND :promiseDateEnd ");
			paramStack.push(new Object[]{"promiseDateStart", (String) params[10][1]});
			paramStack.push(new Object[]{"promiseDateEnd", (String) params[11][1]});
		}

		
		return sb;
	}

	@Override
	public Integer countDeleteTask(Object params, AuditContext callerId) {
		Integer result = NumberUtils.INTEGER_ZERO;
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT COUNT(1) ")
			.append("FROM TR_TASK_H trth with (nolock)  ")
			.append("LEFT JOIN MS_STATUSTASK ms with (nolock) ON ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK ")	
			.append("JOIN (SELECT keyValue as UUID_MS_USER FROM dbo.getUserByLogin(:uuidUser)) ubl on ubl.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("where ms.UUID_MS_SUBSYSTEM = :subsystem and ") 
			.append("ms.STATUS_CODE IN ('N','A','V','P')  ")
			.append(paramsQueryString);
		paramsStack.push(new Object[]{"uuidUser", ((Object[][]) params)[8][1]});
		paramsStack.push(new Object[]{"subsystem", ((Object[][]) params)[6][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}

	@Override
	public List getStatusList(Object params, Object orders, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.survey.statusList", params, orders);		
		return result;
	}

	@Override
	public TrTaskH getDeleteTask(long uuidTaskH, AuditContext callerId) {
		TrTaskH result = this.getManagerDAO().selectOne(
				"from TrTaskH tth join fetch tth.msBranch join fetch tth.msPriority "
				+ "join fetch tth.msForm join fetch tth.amMsuser join fetch tth.msStatustask "
				+ "where tth.uuidTaskH = :uuidTaskH", 
				new Object[][] {{"uuidTaskH", uuidTaskH}});
		if (StringUtils.isEmpty(result.getNotes())) {
			result.setNotes("-");
		}
		if (StringUtils.isEmpty(result.getResult())) {
			result.setResult("-");
		}
		return result;
	}

	@Override
	public Map detailDeleteTask(Object[][] params, AuditContext callerId) {
		Map resultMap = new HashMap();
		Boolean hasImage = Boolean.FALSE;
		
		List<Map<String, Object>> result = this.getManagerDAO().selectForListOfMap("task.order.getDetailOrderFromQSet", params, null);
		
		for (int i = 0; i < result.size(); i++) {
			Map map = (HashMap) result.get(i);
		
			if ("1".equals(map.get("isImage").toString())) { //d9=isImage
				if (!"1".equals((String) map.get("hasImage"))) { //d4=hasImage
					map.put("textAnswer", StringUtils.EMPTY);
				}
				
				if (null != map.get("latitude")  && null != map.get("longitude")) {
					hasImage = Boolean.TRUE;
				}
			}
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("msQuestion.msAnswertype.codeAnswerType").toString())) {								
				if (StringUtils.isNotBlank((String) map.get("textAnswer"))) {
					String currency = (String) map.get("textAnswer");
					if (StringUtils.contains(currency, ",") || StringUtils.contains(currency, ".")) {
						currency = StringUtils.replace(currency, ",", "");
						currency = StringUtils.replace(currency, ".00", "");
					}
					NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
					map.put("textAnswer", formatKurs.format(NumberUtils.toDouble(currency)));					
					
				}				
			}
		}
		resultMap.put("resultList", result);
		resultMap.put("hasImage", hasImage);
		
		return resultMap;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void deleteTask(String[] selectedTask, TrTaskH trTaskH, AmMsuser loginBean, AuditContext callerId) {
		String notes = trTaskH.getNotes();
		String[] tasks = selectedTask[0].split(",");
		Map<String, String> result = new HashMap<>();
		TrTaskH taskHIDE = new TrTaskH();
		
		for (int i = 0; i < tasks.length; i++) {
			long uuidTaskH = 0;
			if("1".equals(this.link_encrypt)){
				uuidTaskH = Long.valueOf(CipherTool.decryptData( new String[]{tasks[i]} ).get(0).toString());
			} else {
				uuidTaskH = Long.valueOf(tasks[i]);
			}
			
			trTaskH = this.getManagerDAO().selectOne(
					"from TrTaskH tth where tth.uuidTaskH = :uuidTaskH",
					new Object[][] {{"uuidTaskH", uuidTaskH}});
			Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_DELETED) } };
			MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
			
			Object[][] paramsStatusSurvey = {
				{Restrictions.eq("statusCode", "D")},
				{Restrictions.eq("amMssubsystem.uuidMsSubsystem", trTaskH.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem())}
			};
			
			MsStatustask deleteSurvey = this.getManagerDAO().selectOne(MsStatustask.class, paramsStatusSurvey);
			
			result = this.validateUpdateStatusIDE(trTaskH, loginBean, callerId); 
			
			if(result==null) {
				result = new HashMap<>();
			}
			
			if(!result.isEmpty()) {
				LOG.info("TASK H IDE : {} ", result.get("uuidTaskH"));
				taskHIDE = this.getManagerDAO().selectOne(TrTaskH.class, 
						new Object[][] {{Restrictions.eq("uuidTaskH", Long.valueOf(result.get("uuidTaskH")))}});
			}
			if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
				
				Object[][] paramsSurvey = {
					{Restrictions.eq("uuidTaskHSurvey", uuidTaskH)}
				};
				
				TrTasklink tasklink = this.getManagerDAO().selectOne(TrTasklink.class, paramsSurvey);
				
				if (null != tasklink) {
					Object[][] paramsOrder = {
						{Restrictions.eq("uuidTaskHOrder", tasklink.getUuidTaskHOrder())}
					};
					
					Map<String, Object> mapSurvey = this.getManagerDAO().list(TrTasklink.class, paramsOrder, null);
					List<TrTasklink> listSurvey = (List<TrTasklink>) mapSurvey.get(GlobalKey.MAP_RESULT_LIST);
					
					List<TrTaskH> listTask = new ArrayList<TrTaskH>();
					boolean flagReleased = false;
					if (!listSurvey.isEmpty()) {
						for(TrTasklink bean : listSurvey) {
							TrTaskH taskSurvey = this.getManagerDAO().selectOne(
								"from TrTaskH tth join fetch tth.msStatustask where tth.uuidTaskH = :uuidTaskH"
									, new Object[][] {{"uuidTaskH", bean.getUuidTaskHSurvey()}});
							
							if (!GlobalVal.SURVEY_STATUS_TASK_RELEASED.equals(taskSurvey.getMsStatustask().getStatusCode())) {
								listTask.add(taskSurvey);
							} 
							else {
								flagReleased = true;
							}
						}
						if (flagReleased) {
							trTaskH.setNotes(notes);
							trTaskH.setMsStatustask(deleteSurvey);
							trTaskH.setUsrUpd(loginBean.getFullName());
							trTaskH.setDtmUpd(new Date());
							trTaskH.setApprovalDate(new Date());
							if(trTaskH.getSubmitDate() == null){
								trTaskH.setMsStatusmobile(msm);
							}
							this.getManagerDAO().update(trTaskH);
							
							this.insertTaskHistory(callerId, deleteSurvey, trTaskH, 
									this.messageSource.getMessage("businesslogic.deletetask.deleted", 
											new Object[]{trTaskH.getFlagSource()}, this.retrieveLocaleAudit(callerId))
									, GlobalVal.CODE_PROCESS_DELETED, loginBean.getFullName());
						} 
						else {
							if (!listTask.isEmpty()) {
								for(TrTaskH bean : listTask) {
									bean.setNotes(notes);
									bean.setMsStatustask(deleteSurvey);
									bean.setUsrUpd(loginBean.getFullName());
									bean.setDtmUpd(new Date());
									bean.setApprovalDate(new Date());
									if(trTaskH.getSubmitDate() == null){
										bean.setMsStatusmobile(msm);
									}
									this.getManagerDAO().update(bean);
									
									this.insertTaskHistory(callerId, deleteSurvey, bean,
											this.messageSource.getMessage("businesslogic.deletetask.deleted", 
													new Object[]{trTaskH.getFlagSource()}, this.retrieveLocaleAudit(callerId))
											, GlobalVal.CODE_PROCESS_DELETED, loginBean.getFullName());
								}
								
								TrTaskH taskOrder = this.getManagerDAO().selectOne(
										"from TrTaskH tth join fetch tth.amMsuser.amMssubsystem where tth.uuidTaskH = :uuidTaskH"
										, new Object[][] {{"uuidTaskH", tasklink.getUuidTaskHOrder()}});
								
								Object[][] paramsStatusOrder = {
										{Restrictions.eq("statusCode", "D")},
										{Restrictions.eq("amMssubsystem.uuidMsSubsystem", taskOrder.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem())}
									};
									
								MsStatustask deleteOrder = this.getManagerDAO().selectOne(MsStatustask.class, paramsStatusOrder);
								
								taskOrder.setNotes(notes);
								taskOrder.setMsStatustask(deleteOrder);
								taskOrder.setUsrUpd(loginBean.getFullName());
								taskOrder.setDtmUpd(new Date());
								taskOrder.setApprovalDate(new Date());
								this.getManagerDAO().update(taskOrder);
								
								this.insertTaskHistory(callerId, deleteOrder, taskOrder,
										this.messageSource.getMessage("businesslogic.deletetask.deleted", 
												new Object[]{trTaskH.getFlagSource()}, this.retrieveLocaleAudit(callerId))
										, GlobalVal.CODE_PROCESS_DELETED, loginBean.getFullName());
							}
						}
					}
				}
			} 
			else {
				trTaskH.setNotes(notes);
				trTaskH.setMsStatustask(deleteSurvey);
				trTaskH.setUsrUpd(loginBean.getFullName());
				trTaskH.setDtmUpd(new Date());
				trTaskH.setApprovalDate(new Date());
				if(trTaskH.getSubmitDate() == null){
					trTaskH.setMsStatusmobile(msm);
				}
				this.getManagerDAO().update(trTaskH);
				
				this.insertTaskHistory(callerId, deleteSurvey, trTaskH, 
						this.messageSource.getMessage("businesslogic.deletetask.deleted", 
								new Object[]{trTaskH.getFlagSource()}, this.retrieveLocaleAudit(callerId))
						, GlobalVal.CODE_PROCESS_DELETED, loginBean.getFullName());
				
				this.getManagerDAO().fetch(trTaskH.getMsForm());
				boolean isTaskOts = false;
				if (GlobalVal.FORM_OTS.equals(trTaskH.getMsForm().getFormName())) {
					isTaskOts = true;
					String flagSourceOts = getOtsFlagSource(String.valueOf(trTaskH.getUuidTaskH()), null != trTaskH.getTrTaskH());
					if (null != trTaskH.getAmMsuser()) {
						this.getManagerDAO().fetch(trTaskH.getAmMsuser());
						this.getManagerDAO().fetch(trTaskH.getAmMsuser().getMsJob());
					}
					intFormLogic.submitTaskOTS(callerId, flagSourceOts, trTaskH);
					if(!result.isEmpty()) {
						this.intFormLogic.updateStatusIDE(taskHIDE, result.get("mssStat"), result.get("mssStat"), loginBean, null, callerId);
					}
				} else {					
					intFormLogic.updateDataPolo(null, trTaskH, null, "Deleted", "F", null, null, null, null, callerId);
					if(!result.isEmpty()) {
						this.intFormLogic.updateStatusIDE(taskHIDE, result.get("mssStat"), result.get("mssStat"), loginBean, null, callerId);
					}
				}

				if (StringUtils.isNotBlank(trTaskH.getOrderId())) {
					UpdateStatusMainDealerResponse updStatusTaskMainDealer = this.intFormLogic.updateStatusTaskMainDealer(trTaskH, callerId);
					if (!"00".equals(updStatusTaskMainDealer.getResponseCode())) {
						throw new RemoteException(updStatusTaskMainDealer.getResponseMessage());
					}
				}
				if (!isTaskOts) {
					deleteGroupTask(trTaskH, deleteSurvey, loginBean, callerId);
				}
			}

//			intFormLogic.cancelTask(trTaskH.getTaskId(), trTaskH.getFlagSource(), callerId.getCallerId());
		}
		
	}
	
	@Override
	public List listDeleteTaskByHierarkiBranch(Object params, AuditContext callerId) {

		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilderHierarkiBranch((Object[][]) params, paramsStack, callerId);
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT * from ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("SELECT  ")
			.append("trth.UUID_TASK_H, ") 
			.append("trth.TASK_ID, ")
			.append("trth.CUSTOMER_NAME, ")
			.append("trth.CUSTOMER_ADDRESS,  ")
			.append("LEFT(CONVERT(VARCHAR, trth.ASSIGN_DATE, 113), 17) as ASSIGN_DATE, ")
			.append("ms.STATUS_TASK_DESC, ")
			.append("LEFT(CONVERT(VARCHAR, trth.PROMISE_DATE, 113), 17) as PROMISE_DATE, ")
			.append("ROW_NUMBER() OVER (ORDER BY trth.TASK_ID asc) AS rownum ")
			.append("FROM TR_TASK_H trth with (nolock)  ")
			.append("RIGHT JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) msb on trth.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("LEFT JOIN MS_STATUSTASK ms with (nolock) ON ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK ")
			.append("where ms.UUID_MS_SUBSYSTEM = :subsystem and ") 
			.append("ms.STATUS_CODE IN ('N','A','V','P') ")
			.append(paramsQueryString)
			.append(") a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[]{"subsystem", ((Object[][]) params)[6][1]});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[12][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[13][1]});
		paramsStack.push(new Object[]{"uuidBranch", ((Object[][]) params)[9][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    List result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);

	    if("1".equals(this.link_encrypt)){
			//d0 = seqNo
			List newList = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				String[] toBeEncrypt = {map.get("d0").toString()};
				map.put("d0", CipherTool.encryptData(toBeEncrypt).get(0).toString());
				newList.add(map);
			}
			return newList;
		}
	    
		return result;
	}
	
	private StringBuilder sqlPagingBuilderHierarkiBranch(Object[][] params, Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null)
			return new StringBuilder();
		StringBuilder sb = new StringBuilder();
		//---CUSTOMER_NAME
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("and UPPER(trth.CUSTOMER_NAME) like UPPER('%' + :customerName + '%') ");
			paramStack.push(new Object[]{"customerName", (String) params[1][1]});
		}
		
		//---TASK_ID
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and UPPER(trth.TASK_ID) like UPPER('%' + :taskId + '%')  ");
			paramStack.push(new Object[]{"taskId", (String) params[0][1]});
		}
		
		//---CUSTOMER_ADDRESS
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("AND UPPER(trth.CUSTOMER_ADDRESS) like UPPER('%' + :customerAddress + '%') ");
			paramStack.push(new Object[]{"customerAddress", (String) params[2][1]});
		}	
		
		//---UUID_STATUS_TASK
		if (!StringUtils.equals("%", (String) params[5][1])) {
			sb.append("AND ms.UUID_STATUS_TASK = :statusTask ");
			paramStack.push(new Object[]{"statusTask", Long.valueOf((String) params[5][1])});
		}
		
		if(!StringUtils.equals("%", (String) params[3][1]) && !StringUtils.equals("%", (String) params[4][1])){
			sb.append("and trth.ASSIGN_DATE BETWEEN :assignmentDateStart ");
			sb.append("AND :assignmentDateEnd ");
			paramStack.push(new Object[]{"assignmentDateStart", (String) params[3][1]});
			paramStack.push(new Object[]{"assignmentDateEnd", (String) params[4][1]});
		}
		if(!StringUtils.equals("%", (String) params[10][1]) && !StringUtils.equals("%", (String) params[11][1])){
			sb.append("and trth.PROMISE_DATE BETWEEN :promiseDateStart ");
			sb.append("AND :promiseDateEnd ");
			paramStack.push(new Object[]{"promiseDateStart", (String) params[10][1]});
			paramStack.push(new Object[]{"promiseDateEnd", (String) params[11][1]});
		}
		return sb;
	}

	@Override
	public Integer countDeleteTaskByHierarkiBranch(Object params, AuditContext callerId) {

		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilderHierarkiBranch((Object[][]) params, paramsStack, callerId);
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT COUNT(1) ")
			.append("FROM TR_TASK_H trth with (nolock)  ")
			.append("RIGHT JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) msb on trth.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("LEFT JOIN MS_STATUSTASK ms with (nolock) ON ms.UUID_STATUS_TASK = trth.UUID_STATUS_TASK ")
			.append("where ms.UUID_MS_SUBSYSTEM = :subsystem	and ") 
			.append("ms.STATUS_CODE IN ('N','A','V','P') ")
			.append(paramsQueryString);
		
		paramsStack.push(new Object[]{"subsystem", ((Object[][]) params)[6][1]});
		paramsStack.push(new Object[]{"uuidBranch", ((Object[][]) params)[9][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    Integer result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);

		return result;
	}
	
	@Override
	public Map<String, Object> listTaskToDelete(String[] selectedTask, AuditContext callerId) {
		Map<String, Object> result = new HashMap<String, Object>();
		List<Map> listTaskHs = new ArrayList<Map>();
		String[] tasks = selectedTask[0].split(",");
		for (int i = 0; i < tasks.length; i++) {
			String id = tasks[i];
			if("1".equals(this.link_encrypt)){
				id = CipherTool.decryptData( new String[]{tasks[i]} ).get(0).toString();
			}
			
			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class,	Long.valueOf(id));
			Map mapTask = new HashMap<>();
			mapTask.put("d0", tasks[i]);
			mapTask.put("d1", trTaskH.getTaskId());
			mapTask.put("d2", trTaskH.getCustomerName());
			mapTask.put("d3", trTaskH.getCustomerAddress());
			mapTask.put("d4", (null == trTaskH.getAssignDate()) ? "" : DateFormatUtils.format(trTaskH.getAssignDate(), "dd MMM yyyy HH:mm") );
			mapTask.put("d5", trTaskH.getMsStatustask().getStatusTaskDesc());
			mapTask.put("d6", (null == trTaskH.getPromiseDate()) ? "" : DateFormatUtils.format(trTaskH.getPromiseDate(), "dd MMM yyyy HH:mm") );
			listTaskHs.add(mapTask);
		}
		
		result.put(GlobalKey.MAP_RESULT_LIST, listTaskHs);
		result.put(GlobalKey.MAP_RESULT_SIZE, listTaskHs.size());

		return result;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void autoDeleteTask(long uuidTaskH, Map applNoDelete, AuditContext callerId) {
		String duration = (String) this.getManagerDAO().selectOneNativeString(
				"SELECT GS_VALUE " +
				"FROM AM_GENERALSETTING WITH (NOLOCK) " +
				"WHERE GS_CODE = 'MS_DURATION_AUTODELETE' ", null);
		
		TrTaskH trTaskH = this.getManagerDAO().selectOne(
				"FROM TrTaskH trth "
				+ "JOIN FETCH trth.msStatustask ms "
				+ "JOIN FETCH ms.amMssubsystem sub "
				+ "JOIN FETCH trth.amMsuser amu "
				+ "JOIN FETCH trth.msForm mf "
				+ "WHERE trth.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", uuidTaskH}});
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(trTaskH.getAssignDate());
		calendar.add(Calendar.HOUR_OF_DAY, Integer.parseInt(duration));
		Date durationTotal = calendar.getTime();
		if (durationTotal.before(new Date())) {
			autoDelete(applNoDelete, trTaskH, callerId);
		}
	}

	@Override
	@Transactional
	public List getListTask(AuditContext callerId) {
		StringBuilder queryGetTaskList = new StringBuilder();
		queryGetTaskList.append("SELECT UUID_TASK_H as uuidTaskH FROM TR_TASK_H tth WITH(NOLOCK) ");
		queryGetTaskList.append("JOIN MS_STATUSTASK mst WITH(NOLOCK) on mst.UUID_STATUS_TASK = tth.UUID_STATUS_TASK ");
		queryGetTaskList.append("WHERE STATUS_CODE = 'N' ");
		queryGetTaskList.append("AND IS_PILOTING_CAE = '1' ");
		queryGetTaskList.append("AND PROMISE_DATE IS NULL ");
		queryGetTaskList.append("AND ASSIGN_DATE IS NOT NULL ");
		
		List taskList = this.getManagerDAO().selectForListOfMapString(queryGetTaskList.toString(), (Object[][]) null, null);
		return taskList;
	}
	
	private void autoDelete(Map applNoDelete, TrTaskH trTaskHDelete, AuditContext callerId) {
		Object[][] params = {{Restrictions.eq("statusCode", "D")},
				{Restrictions.eq("amMssubsystem.uuidMsSubsystem", trTaskHDelete.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem())}};
		MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
		
		List<TrTaskH> taskNeedDelete = new ArrayList<TrTaskH>();
		taskNeedDelete.add(trTaskHDelete);
		
		if ("1".equalsIgnoreCase(trTaskHDelete.getIsPilotingCae()) 
				&& !GlobalVal.FORM_GUARANTOR.equalsIgnoreCase(trTaskHDelete.getMsForm().getFormName())
				&& !GlobalVal.FORM_OTS.equalsIgnoreCase(trTaskHDelete.getMsForm().getFormName())
				&& !GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskHDelete.getMsForm().getFormName())) {
			Map taskMap = this.getManagerDAO().list(
					"FROM TrTaskH trth "
					+ "JOIN FETCH trth.msStatustask ms "
					+ "JOIN FETCH trth.amMsuser amu "
					+ "JOIN FETCH ms.amMssubsystem sub "
					+ "WHERE trth.uuidTaskH != :uuidTaskH "
					+ "AND trth.applNo = :applNo "
					+ "AND ms.statusCode = :statusCode "
					+ "AND trth.promiseDate is null "
					+ "AND sub.uuidMsSubsystem = '4' ", new Object[][] {{"applNo", trTaskHDelete.getApplNo()}, {"statusCode", GlobalVal.SURVEY_STATUS_TASK_WAITING_ON_PENDING}, {"uuidTaskH", trTaskHDelete.getUuidTaskH()}});
			List<TrTaskH> taskList = (List) taskMap.get(GlobalKey.MAP_RESULT_LIST);
			if (taskList!=null && taskList.size()>0) {
				for (TrTaskH trTaskH: taskList) {
					taskNeedDelete.add(trTaskH);
				}
			}
		}
		
		for (int i=0; i<taskNeedDelete.size(); i++) {
			TrTaskH trTaskH = taskNeedDelete.get(i);
			String fieldPerson = "";
			if (trTaskH.getAmMsuser() != null) {
				fieldPerson = (String) this.getManagerDAO().selectOneNativeString(
					"SELECT FULL_NAME "
					+ "FROM AM_MSUSER WITH (NOLOCK)"
					+ "WHERE UUID_MS_USER = :uuidMsUser ", 
					new Object[][] {{"uuidMsUser", Long.valueOf(trTaskH.getAmMsuser().getUuidMsUser())}});
			}
			trTaskH.setMsStatustask(msStatustask);
			trTaskH.setDtmUpd(new Date());
			trTaskH.setUsrUpd(callerId.getCallerId());
			trTaskH.setNotes(appendNotes(trTaskH.getNotes(), "Task Auto Delete"));
			
			TrTaskhistory taskhistory = new TrTaskhistory();
			taskhistory.setActor(callerId.getCallerId());
			taskhistory.setCodeProcess(GlobalVal.CODE_PROCESS_DELETED);
			taskhistory.setDtmCrt(new Date());
			taskhistory.setFieldPerson(fieldPerson);
			taskhistory.setMsStatustask(msStatustask);
			taskhistory.setNotes(trTaskH.getNotes());
			taskhistory.setTrTaskH(trTaskH);
			taskhistory.setUsrCrt(callerId.getCallerId());
			
			this.getManagerDAO().insert(taskhistory);
			
			if (applNoDelete.get(trTaskH.getApplNo()) == null) {
				intFormLogic.updateDataPolo(null, trTaskH, null, "Deleted", "F", null, null, "1", null, callerId);
				applNoDelete.put(trTaskH.getApplNo(), trTaskH);
			}
		}
		
	}
	
	String appendNotes(String notes, String appended) {
		try {
			String newNotes = "";
			if (null != notes && !"".equals(notes)) { // have old notes
				newNotes = notes + "|" + appended;
			} else { // new notes
				newNotes = appended;
			}
			return newNotes;
		} catch (Exception e) {
			e.printStackTrace();
			return appended;
		}
	}
	
	
	private void deleteGroupTask(TrTaskH trTaskH, MsStatustask deleteStat, AmMsuser loginBean, AuditContext callerId) {
		LOG.info("TR TASK H PILOTING CAE : {}", trTaskH.getIsPilotingCae());
		Log.info("deleteGroupTask.checkPilotingCAE or not");
		if("1".equals(trTaskH.getIsPilotingCae())) {
			Object[][] paramGroupTask = { {Restrictions.eq("trTaskH.uuidTaskH", trTaskH.getUuidTaskH())} };
			
			MsGrouptask groupTask = this.getManagerDAO().selectOne(MsGrouptask.class, paramGroupTask);
			
			Object[][] paramListTaskH = { {Restrictions.eq("groupTaskId", groupTask.getGroupTaskId())} };
			List<MsGrouptask> listGroupTask = (List<MsGrouptask>) this.getManagerDAO().selectAll(MsGrouptask.class, paramListTaskH, null).get(GlobalKey.MAP_RESULT_LIST);
					
			for(MsGrouptask groupTaskH : listGroupTask) {
				Object[][] paramTask = { {Restrictions.eq("uuidTaskH", groupTaskH.getTrTaskH().getUuidTaskH())} };
				TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, paramTask);
				
				if(!GlobalVal.SURVEY_STATUS_TASK_DELETED.equals(taskH.getMsStatustask().getStatusCode())) {
					taskH.setNotes(trTaskH.getNotes());
					taskH.setMsStatustask(deleteStat);
					taskH.setUsrUpd(trTaskH.getUsrUpd());
					taskH.setDtmUpd(new Date());
					taskH.setApprovalDate(new Date());
					this.getManagerDAO().update(taskH);
					
					this.insertTaskHistory(callerId, deleteStat, taskH, 
							this.messageSource.getMessage("businesslogic.deletetask.deleted", 
									new Object[]{trTaskH.getFlagSource()}, this.retrieveLocaleAudit(callerId))
							, GlobalVal.CODE_PROCESS_DELETED, taskH.getUsrUpd());
				}
			}
		}
		else if(StringUtils.isBlank(trTaskH.getIsPilotingCae())) {
			throw new RemoteException(this.messageSource.getMessage("businesslogic.deletetask.notcae", null, retrieveLocaleAudit(callerId)));
		}
	}
	
	
	private Map validateUpdateStatusIDE(TrTaskH trTaskH, AmMsuser loginBean, AuditContext callerId) {
		
		boolean isReject = false;
		String uuidTaskH = StringUtils.EMPTY;
		String mssStat = StringUtils.EMPTY;
		Map<String,String> result = new HashMap<>();
		
		StringBuilder queryTask1 = new StringBuilder()
				.append(" SELECT DISTINCT UUID_TASK_H, MST.STATUS_CODE ")
				.append(" FROM TR_TASK_H TTH WITH(NOLOCK) ")
				.append(" JOIN MS_STATUSTASK MST WITH(NOLOCK) ON TTH.UUID_STATUS_TASK = MST.UUID_STATUS_TASK " )
				.append(" JOIN MS_SETTINGAPPROVALOTS SETAP WITH(NOLOCK) ON TTH.UUID_FORM = SETAP.UUID_FORM " )
				.append(" WHERE 1=1")
				.append(" AND MST.STATUS_CODE != :statusCode ")
				.append(" AND TTH.APPL_NO = :applNo");
				
		List<Map<String,Object>> listTask  = this.getManagerDAO().selectAllNativeString(queryTask1.toString(), 
												new Object [][] {{"applNo", trTaskH.getApplNo()},{"statusCode", GlobalVal.SURVEY_STATUS_TASK_DELETED}});
		if(!listTask.isEmpty()) {
			for(int i = 0 ; i < listTask.size() ; i++) {
				if( GlobalVal.SURVEY_STATUS_TASK_REJECTED_WITH_RESURVEY.equalsIgnoreCase(String.valueOf(listTask.get(i).get("d1")))) {
					isReject = true;
					break;
				}
			}
		
			if(isReject) {
				StringBuilder queryTask2 = new StringBuilder()
						.append(" SELECT DISTINCT UUID_TASK_H ")
						.append(" FROM TR_TASK_H TTH WITH(NOLOCK) ")
						.append(" JOIN MS_STATUSTASK MST WITH(NOLOCK) ON TTH.UUID_STATUS_TASK = MST.UUID_STATUS_TASK " )
						.append(" JOIN MS_SETTINGAPPROVALOTS SETAP WITH(NOLOCK) ON TTH.UUID_FORM = SETAP.UUID_FORM " )
						.append(" WHERE 1=1 ")
						.append(" AND MST.STATUS_CODE NOT IN ( :statusCodeDel, :statusCodeReject) ")
						.append(" AND TTH.APPL_NO = :applNo ");
				uuidTaskH  = String.valueOf(this.getManagerDAO().selectOneNativeString(queryTask2.toString(), 
						new Object [][] {
											{"applNo", trTaskH.getApplNo()},
											{"statusCodeDel", GlobalVal.SURVEY_STATUS_TASK_DELETED},
											{"statusCodeReject", GlobalVal.SURVEY_STATUS_TASK_REJECTED_WITH_RESURVEY}
										}));
				mssStat = GlobalVal.UPDATE_STATUS_IDE_CANCEL_RESURVEY;
			} else {
				uuidTaskH = String.valueOf(listTask.get(0).get("d0"));
				TrTaskH taskHIDE = this.getManagerDAO().selectOne(TrTaskH.class, new Object[][] {{Restrictions.eq("uuidTaskH", Long.valueOf(uuidTaskH))}});
				this.getManagerDAO().fetch(trTaskH.getMsGrouptasks());
				MsGrouptask groupTask = trTaskH.getMsGrouptasks().iterator().next();
				 
				TblCaeData tblBean = this.getManagerDAO().selectOne(TblCaeData.class, new Object[][] {
									{Restrictions.eq("orderNoCae", trTaskH.getOrderNoCae())}, 
									{Restrictions.eq("groupTaskId", groupTask.getGroupTaskId())},
									{Restrictions.eq("isSuccess", "1")}
								});
				AddTaskCAERequest tblRequestBean = gson.fromJson(tblBean.getJsonRequest(), AddTaskCAERequest.class);
				
				String isIA = null == tblBean.getIsIa() ? null : tblBean.getIsIa();
				Integer isPreApproval = null == tblRequestBean.getIsPreApproval() ? null : tblRequestBean.getIsPreApproval();
				
				LOG.info("isIA : {} | isPreApproval : {}", isIA, isPreApproval);
				
				if("1".equals(isIA) || 1 == isPreApproval) {
					if(GlobalVal.USR_CRT_ADD_TASK_SCORING_CAE.equalsIgnoreCase(taskHIDE.getUsrCrt()) 
							|| GlobalVal.USR_CRT_AUTO_CANCEL_TASK.equalsIgnoreCase(taskHIDE.getUsrCrt())) {
						mssStat = GlobalVal.UPDATE_STATUS_IDE_CANCEL_RESURVEY;
					} else {
						mssStat = GlobalVal.UPDATE_STATUS_IDE_DELETE;
					}
				}else {
					mssStat = GlobalVal.UPDATE_STATUS_IDE_DELETE;
				}
			}
			result.put("mssStat", mssStat);
			result.put("uuidTaskH", uuidTaskH);
			
			return result;
		}
		return null;
	}
}