package com.adins.mss.businesslogic.impl.survey;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.DocuproHistLogic;
import com.adins.mss.model.AmMsuser;

public class GenericDocuproHistLogic extends BaseLogic implements DocuproHistLogic, MessageSourceAware {

	private IntFormLogic intFormLogic;
	
	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

    @Transactional
	@Override
	public List listDocuproHist(String customerName, AuditContext callerId) {
    	List result = Collections.emptyList();
		
		Object[][] params = { { "customerName", customerName } };
		String query = 
				  " SELECT "
				+ " 	DTM_CRT, TASK_ID, BRANCH_NAME, CUSTOMER_NAME, ID_NUMBER, PRODUCT_TYPE, FIELD_NOTE, "
				+ " 	DTM_START_SEND, DTM_END_SEND, DTM_APPROVED, SURVEYOR_NAME, WOM_APPID, IS_CMO_RECOMMEND "
				+ " FROM TR_DOCUPRO_HIST with(nolock) "
				+ " WHERE CUSTOMER_NAME LIKE :customerName "
				+ " ORDER BY DTM_CRT DESC ";
				
		List resultList =  this.getManagerDAO().selectAllNativeString(query, params);
		
		return resultList;
	}
	
	

}
