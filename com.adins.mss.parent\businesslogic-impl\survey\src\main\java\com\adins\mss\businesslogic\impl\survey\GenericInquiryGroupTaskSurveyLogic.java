package com.adins.mss.businesslogic.impl.survey;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.InquiryGroupTaskSurveyLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.model.MsGrouptask;
import com.adins.mss.util.CipherTool;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericInquiryGroupTaskSurveyLogic extends BaseLogic implements InquiryGroupTaskSurveyLogic {
	private final String linkEncrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	
	@Override
	public Map<String, Object> listInquiryGroupTaskSurvey(Object params, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<>();
		List resultAll = null;
		Integer resultCount = 0;
		Stack<Object[]> paramsStack = new Stack<Object[]>();
		
		StringBuilder ordersQueryString = this.sqlPagingBuilderOrder((String) ((Object[][]) params)[8][1], callerId);
		Map<String, String> paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		String joinInside = paramsQueryString.get("joinInside");
		String where = paramsQueryString.get("sb");
		
		StringBuilder queryBuilderCount = new StringBuilder()
				.append("SELECT COUNT(1) ")
				.append("	FROM (SELECT DISTINCT GROUP_TASK_ID, BRANCH_NAME, msgt.CUSTOMER_NAME ")
				.append("			FROM MS_GROUPTASK msgt WITH (NOLOCK)  ")
				.append(joinInside)
				.append("			WHERE 1=1 ")
				.append(where)
				.append("			UNION ALL ")
				.append("			SELECT DISTINCT GROUP_TASK_ID, BRANCH_NAME, msgt.CUSTOMER_NAME ")
				.append("			FROM FINAL_MS_GROUPTASK msgt WITH (NOLOCK)  ")
				.append(joinInside)
				.append("			WHERE 1=1 ")
				.append(where)
				.append("	) countGroupTask ");
		
		Object[][] sqlParamsCount = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParamsCount[i] = objects;
		}
		
		resultCount = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderCount.toString(), sqlParamsCount);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("WITH groupTask AS ( ") 
				.append("	SELECT msgt.GROUP_TASK_ID, mfc.UUID_FORM_CATEGORY, ")
				.append("		MAX(trth.DTM_CRT) AS DTM_CRT, ")
				.append("		MAX(mst.STATUS_TASK_DESC) AS STATUS_TASK_DESC ") 
				.append("	  FROM MS_GROUPTASK msgt WITH (NOLOCK) ")
				.append("	  JOIN TR_TASK_H trth WITH (NOLOCK) ") 
				.append("		ON msgt.UUID_TASK_H = trth.UUID_TASK_H ") 
				.append("	  JOIN ms_form msf WITH (NOLOCK) ")
				.append("		ON trth.UUID_FORM = msf.UUID_FORM ") 
				.append("	  JOIN MS_FORMCATEGORY mfc WITH (NOLOCK) ")
				.append("		ON msf.UUID_FORM_CATEGORY = mfc.UUID_FORM_CATEGORY ")
				.append("	  JOIN MS_STATUSTASK mst WITH (NOLOCK) ") 
				.append("		ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ") 
				.append("	GROUP BY msgt.GROUP_TASK_ID, mfc.UUID_FORM_CATEGORY ")
				.append("), ")
				.append("groupTaskFinal AS ( ") 
				.append("	SELECT fmsgt.GROUP_TASK_ID, mfc.UUID_FORM_CATEGORY, ") 
				.append("		MAX(ftrth.DTM_CRT) AS DTM_CRT, ")
				.append("		MAX(mst.STATUS_TASK_DESC) AS STATUS_TASK_DESC ") 
				.append("	  FROM FINAL_MS_GROUPTASK fmsgt WITH (NOLOCK) ") 
				.append("	  JOIN FINAL_TR_TASK_H ftrth WITH (NOLOCK) ") 
				.append("		ON fmsgt.UUID_TASK_H = ftrth.UUID_TASK_H ") 
				.append("	  JOIN ms_form msf WITH (NOLOCK) ") 
				.append("		ON ftrth.UUID_FORM = msf.UUID_FORM ") 
				.append("	  JOIN MS_FORMCATEGORY mfc WITH (NOLOCK) ") 
				.append("		ON msf.UUID_FORM_CATEGORY = mfc.UUID_FORM_CATEGORY ") 
				.append("	  JOIN MS_STATUSTASK mst WITH (NOLOCK) ") 
				.append("		ON ftrth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("	GROUP BY fmsgt.GROUP_TASK_ID, mfc.UUID_FORM_CATEGORY ")
				.append(") ")
				.append("SELECT b.* FROM ( ")
				.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
				.append("SELECT c.*, ROW_NUMBER() OVER ( ")
				.append(ordersQueryString)
				.append(") AS rownum FROM ( ")
				.append("SELECT GROUP_TASK_ID, b.CUSTOMER_NAME, b.APPL_NO, b.BRANCH_NAME, STATUS_TEXT, STATUS_GAMBAR, flagOrder ")
				.append("	FROM (SELECT GROUP_TASK_ID, CUSTOMER_NAME, APPL_NO, BRANCH_NAME, STATUS_TEXT, STATUS_GAMBAR, flagOrder ")
				.append("			FROM (SELECT GROUP_TASK_ID, CONVERT(VARCHAR, msgt.CUSTOMER_NAME, 80) AS CUSTOMER_NAME, ") 
				.append("					MAX(msgt.APPL_NO) AS APPL_NO, BRANCH_NAME, ")
				.append("					(SELECT STATUS_TASK_DESC ") 
				.append("						FROM groupTask gt WITH (NOLOCK) ") 
				.append("  						WHERE gt.GROUP_TASK_ID = msgt.GROUP_TASK_ID ") 
				.append("						AND UUID_FORM_CATEGORY = 2 ") 
				.append("					) AS STATUS_TEXT, ")
				.append("					(SELECT STATUS_TASK_DESC ") 
				.append("						FROM groupTask gt WITH (NOLOCK) ") 
				.append("						WHERE gt.GROUP_TASK_ID = msgt.GROUP_TASK_ID ") 
				.append("						AND UUID_FORM_CATEGORY = 8 ")
				.append("					) AS STATUS_GAMBAR, ") 
				.append("					'0' AS flagOrder ") 
				.append("					FROM MS_GROUPTASK msgt WITH (NOLOCK) ") 
				.append(joinInside)
				.append("					WHERE 1 = 1 ")
				.append(where)
				.append("					GROUP BY msgt.GROUP_TASK_ID, BRANCH_NAME, msgt.CUSTOMER_NAME ")
				.append("				) msGroupTask")
				.append("		UNION ALL ")
				.append("		SELECT GROUP_TASK_ID, CUSTOMER_NAME, APPL_NO, BRANCH_NAME, STATUS_TEXT, STATUS_GAMBAR, flagOrder ")
				.append("			FROM (SELECT GROUP_TASK_ID, CONVERT(VARCHAR, msgt.CUSTOMER_NAME, 80) as CUSTOMER_NAME, ")
				.append("					MAX(msgt.APPL_NO) AS APPL_NO, BRANCH_NAME, ")
				.append("					(SELECT STATUS_TASK_DESC ") 
				.append("						FROM groupTaskFinal gtf WITH (NOLOCK) ") 
				.append("						WHERE gtf.GROUP_TASK_ID = msgt.GROUP_TASK_ID ") 
				.append("						AND UUID_FORM_CATEGORY = 2 ")
				.append("					) AS STATUS_TEXT, ")
				.append("					(SELECT STATUS_TASK_DESC ")
				.append("  						FROM groupTaskFinal gtf WITH (NOLOCK) ")
				.append("						WHERE gtf.GROUP_TASK_ID = msgt.GROUP_TASK_ID ")
				.append("						AND UUID_FORM_CATEGORY = 8 ")
				.append("					) AS STATUS_GAMBAR, ")
			    .append("					'0' as flagOrder ")
				.append("					FROM FINAL_MS_GROUPTASK msgt WITH (NOLOCK) ")
				.append(joinInside)
				.append("					WHERE 1=1 ")
				.append(where)
				.append("					GROUP BY msgt.GROUP_TASK_ID, BRANCH_NAME, msgt.CUSTOMER_NAME ")
				.append("				) finalMsGroupTask")
				.append(") b ")
				.append(") c ")
				.append(") a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start ");
	    
	    paramsStack.push(new Object[]{"start", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[5][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		
	    resultAll = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
	    if("1".equals(this.linkEncrypt)){
	    	List newRes = new ArrayList();
			for(int i=0; i<resultAll.size(); i++){
				Map map = (Map) resultAll.get(i);
				String[] temp = {map.get("d0").toString()};
				List res = CipherTool.encryptData(temp);
				map.put("d6", res.get(0).toString());
				newRes.add(map);
			}
			result.put(GlobalKey.MAP_RESULT_LIST, newRes);
	    }else{
	    	result.put(GlobalKey.MAP_RESULT_LIST, resultAll);
	    }
	    
		result.put(GlobalKey.MAP_RESULT_SIZE, resultCount);

		return result;
	}
	
	private Map<String, String> sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null) {
			return new HashMap<>();
		}
		
		StringBuilder sb = new StringBuilder();
		StringBuilder sbJoinInside = new StringBuilder();
		Map<String, String> mapResult = new HashMap<String, String>();
		
		//---UUID_BRANCH
		if (!StringUtils.equals("%", String.valueOf(params[1][1]))){
			sbJoinInside.append("JOIN MS_BRANCH msb WITH (NOLOCK) ON (msb.uuid_branch = msgt.uuid_branch) ");
			sb.append("AND msgt.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{"uuidBranch", Long.valueOf(String.valueOf(params[1][1]))});
		} else if ("%".equals(String.valueOf(params[1][1]))){
			sbJoinInside.append("JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranchLogin)) msb ON msgt.UUID_BRANCH = msb.UUID_BRANCH ");
			paramStack.push(new Object[]{"uuidBranchLogin", Long.valueOf(String.valueOf(params[7][1]))});
		}
		
		//---CUSTOMER NAME
		if (!StringUtils.equals("%", (String) params[0][1]) && StringUtils.isNotBlank((String) params[0][1])) {
			sb.append("AND msgt.CUSTOMER_NAME LIKE '%' + :customerName + '%' ");
			paramStack.push(new Object[]{"customerName", (String) params[0][1]});
		}
		
		//---APPL_NO
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append("AND (ISNULL(msgt.APPL_NO, '%') LIKE '%' + :applNo + '%') ");
			paramStack.push(new Object[]{"applNo", (String) params[3][1]});
		}
		
		mapResult.put("sb", sb.toString());
		mapResult.put("joinInside", sbJoinInside.toString());
		return mapResult;
	}
	
	private Map<String, String> sqlPagingBuilderByUser(Object[][] params, Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null) {
			return new HashMap<>();
		}
		
		StringBuilder sb = new StringBuilder();
		StringBuilder sbJoinInside = new StringBuilder();
		Map<String, String> mapResult = new HashMap<String, String>();
		
		//---UUID_MS_USER
		paramStack.push(new Object[]{"uuidUser", Long.valueOf(String.valueOf(params[6][1]))});
		
		if (!StringUtils.equals("%", String.valueOf(params[1][1]))) {
			sb.append("and msgt.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{"uuidBranch", Long.valueOf(String.valueOf(params[1][1]))});
		}
		
		//---CUSTOMER NAME
		if (!StringUtils.equals("%", (String) params[0][1]) && StringUtils.isNotBlank((String) params[0][1])) {
			sb.append("AND msgt.CUSTOMER_NAME LIKE '%' + :customerName + '%' ");
			paramStack.push(new Object[]{"customerName", (String) params[0][1]});
		}
		
		//---APPL_NO
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append("AND (ISNULL(msgt.APPL_NO, '%') LIKE '%' + :applNo + '%') ");
			paramStack.push(new Object[]{"applNo", (String) params[3][1]});
		}
		
		mapResult.put("sb", sb.toString());
		mapResult.put("joinInside", sbJoinInside.toString());
		return mapResult;
	}

	private StringBuilder sqlPagingBuilderOrder(String orders, AuditContext callerId) {
		if (StringUtils.isEmpty(orders) || "0".equals(orders)) {
			orders = "1A"; // set default order by flagOrder ASC
		}

		String[] orderCols = { "flagOrder", "c.CUSTOMER_NAME", "c.APPL_NO",
				"c.STATUS_TEXT", "c.STATUS_GAMBAR",
				"c.BRANCH_NAME" };
		StringBuilder queryOrder = new StringBuilder(" ORDER BY ");

		int colIdx = NumberUtils.toInt(StringUtils.substring(orders, 0, StringUtils.length(orders) - 1));
		queryOrder.append(orderCols[colIdx]);
		if (StringUtils.endsWith(orders, "D")) {
			queryOrder.append(" DESC");
		}
		else {
			queryOrder.append(" ASC");
		}

		return queryOrder;
	}
	
	@Override
	public List getDropDownList(Object params, Object order, AuditContext callerId) {
		
		List result = this.getManagerDAO().selectAllNative("task.inquirygrouptasksurvey.branchList", params, order);
			
		return result;
	}

	@Override
	public List getDetailGroupTask(String uuidSubsystem, String groupTaskId, String modeAction, AuditContext callerId) {

		String[][] params = {{"uuidSubsystem", uuidSubsystem}, {"groupTaskId", groupTaskId}};
		List result = this.getManagerDAO().selectAllNative("task.inquirygrouptasksurvey.getDetailGroupTask", params, null);
		if("1".equals(this.linkEncrypt)){
			//encrypt uuid
			List newRes = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				String[] temp = {map.get("d0").toString()};
				List res = CipherTool.encryptData(temp);
				map.put("d0", res.get(0).toString());
				newRes.add(map);
			}
			return newRes;
		}else{
			return result;
		}
	}

	@Override
	public Map<String, Object> listInquiryGroupTaskSurveyByUser(Object params, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<>();
		List resultAll = null;
		Integer resultCount = 0;
		Stack<Object[]> paramsStack = new Stack<Object[]>();
		
		StringBuilder ordersQueryString = this.sqlPagingBuilderOrder((String) ((Object[][]) params)[8][1], callerId);
		Map<String, String> paramsQueryString = this.sqlPagingBuilderByUser((Object[][]) params, paramsStack, callerId);
		String where = paramsQueryString.get("sb");
		
		StringBuilder queryBuilderCount = new StringBuilder()
				.append("SELECT COUNT(1) ")
				.append("	FROM (SELECT DISTINCT GROUP_TASK_ID, BRANCH_NAME, msgt.CUSTOMER_NAME ")
				.append("			FROM MS_GROUPTASK msgt WITH (NOLOCK) ")
				.append("			JOIN MS_BRANCH msb WITH (NOLOCK) ON msgt.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("			JOIN TR_TASK_H tth WITH (NOLOCK) ON tth.uuid_task_h = msgt.uuid_task_h ")
				.append("			JOIN (SELECT keyValue FROM dbo.getUserByLogin(:uuidUser)) hrkUser ON hrkUser.keyValue = tth.UUID_MS_USER ")
				.append("			WHERE 1=1 ")
				.append(where)
				.append("			UNION ALL ")
				.append("			SELECT DISTINCT GROUP_TASK_ID, BRANCH_NAME, msgt.CUSTOMER_NAME ")
				.append("			FROM FINAL_MS_GROUPTASK msgt WITH (NOLOCK) ")
				.append("			JOIN MS_BRANCH msb WITH (NOLOCK) ON msgt.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("			JOIN FINAL_TR_TASK_H ftth WITH (NOLOCK) ON ftth.uuid_task_h = msgt.uuid_task_h ")
				.append("			JOIN (SELECT keyValue FROM dbo.getUserByLogin(:uuidUser)) hrkUser ON hrkUser.keyValue = ftth.UUID_MS_USER ")
				.append("			WHERE 1=1 ")
				.append(where)
				.append("	) countGroupTask ");
		
		Object[][] sqlParamsCount = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParamsCount[i] = objects;
		}
		
	    resultCount = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderCount.toString(), sqlParamsCount);
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("WITH groupTask AS ( ") 
			.append("	SELECT msgt.GROUP_TASK_ID, mfc.UUID_FORM_CATEGORY, ")
			.append("		MAX(trth.DTM_CRT) AS DTM_CRT, ")
			.append("		MAX(mst.STATUS_TASK_DESC) AS STATUS_TASK_DESC ") 
			.append("	  FROM MS_GROUPTASK msgt WITH (NOLOCK) ")
			.append("	  JOIN TR_TASK_H trth WITH (NOLOCK) ") 
			.append("		ON msgt.UUID_TASK_H = trth.UUID_TASK_H ") 
			.append("	  JOIN ms_form msf WITH (NOLOCK) ")
			.append("		ON trth.UUID_FORM = msf.UUID_FORM ") 
			.append("	  JOIN MS_FORMCATEGORY mfc WITH (NOLOCK) ")
			.append("		ON msf.UUID_FORM_CATEGORY = mfc.UUID_FORM_CATEGORY ")
			.append("	  JOIN MS_STATUSTASK mst WITH (NOLOCK) ") 
			.append("		ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ") 
			.append("	GROUP BY msgt.GROUP_TASK_ID, mfc.UUID_FORM_CATEGORY ")
			.append("), ")
			.append("groupTaskFinal AS ( ") 
			.append("	SELECT fmsgt.GROUP_TASK_ID, mfc.UUID_FORM_CATEGORY, ") 
			.append("		MAX(ftrth.DTM_CRT) AS DTM_CRT, ")
			.append("		MAX(mst.STATUS_TASK_DESC) AS STATUS_TASK_DESC ") 
			.append("	  FROM FINAL_MS_GROUPTASK fmsgt WITH (NOLOCK) ") 
			.append("	  JOIN FINAL_TR_TASK_H ftrth WITH (NOLOCK) ") 
			.append("		ON fmsgt.UUID_TASK_H = ftrth.UUID_TASK_H ") 
			.append("	  JOIN ms_form msf WITH (NOLOCK) ") 
			.append("		ON ftrth.UUID_FORM = msf.UUID_FORM ") 
			.append("	  JOIN MS_FORMCATEGORY mfc WITH (NOLOCK) ") 
			.append("		ON msf.UUID_FORM_CATEGORY = mfc.UUID_FORM_CATEGORY ") 
			.append("	  JOIN MS_STATUSTASK mst WITH (NOLOCK) ") 
			.append("		ON ftrth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
			.append("	GROUP BY fmsgt.GROUP_TASK_ID, mfc.UUID_FORM_CATEGORY ")
			.append("), ")
			.append("joinGroupTask AS ( ")
			.append("	SELECT GROUP_TASK_ID, msgt.CUSTOMER_NAME, msgt.DTM_CRT, msgt.APPL_NO, msgt.UUID_BRANCH, UUID_MS_USER, BRANCH_NAME ")
			.append("		FROM MS_GROUPTASK msgt  WITH(NOLOCK) ") 
			.append("		JOIN TR_TASK_H tth  WITH(NOLOCK) ON tth.UUID_TASK_H = msgt.UUID_TASK_H ")
			.append("		JOIN (SELECT keyValue FROM dbo.getUserByLogin(:uuidUser)) hrkUser ON hrkUser.keyValue = tth.UUID_MS_USER ") 
			.append("		JOIN MS_BRANCH msb WITH (NOLOCK) ON msgt.UUID_BRANCH = msb.UUID_BRANCH ")
			.append("), ")
			.append("joinGroupTaskFinal AS ( ") 
			.append("	SELECT GROUP_TASK_ID, fmsgt.CUSTOMER_NAME, fmsgt.DTM_CRT, fmsgt.APPL_NO, fmsgt.UUID_BRANCH, UUID_MS_USER, BRANCH_NAME ")
			.append("		FROM FINAL_MS_GROUPTASK fmsgt  WITH(NOLOCK) ") 
			.append("		JOIN FINAL_TR_TASK_H ftth WITH(NOLOCK) ON ftth.UUID_TASK_H = fmsgt.UUID_TASK_H ") 
			.append("		JOIN (SELECT keyValue FROM dbo.getUserByLogin(:uuidUser)) hrkUser ON hrkUser.keyValue = ftth.UUID_MS_USER ")
			.append("		JOIN MS_BRANCH msb WITH (NOLOCK) ON fmsgt.UUID_BRANCH = msb.UUID_BRANCH ")
			.append(") ")
			.append("SELECT b.* FROM ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("SELECT c.*, ROW_NUMBER() OVER ( ")
			.append(ordersQueryString)
			.append(") AS rownum FROM ( ")
			.append("SELECT GROUP_TASK_ID, b.CUSTOMER_NAME, b.APPL_NO, b.BRANCH_NAME, STATUS_TEXT, STATUS_GAMBAR, flagOrder ")
			.append("	FROM (SELECT GROUP_TASK_ID, CUSTOMER_NAME, APPL_NO, UUID_BRANCH, BRANCH_NAME, STATUS_TEXT, STATUS_GAMBAR, '0' AS flagOrder ")
			.append("		FROM (SELECT GROUP_TASK_ID, CUSTOMER_NAME, APPL_NO, UUID_BRANCH, BRANCH_NAME, STATUS_TEXT, STATUS_GAMBAR")
			.append("			FROM (SELECT GROUP_TASK_ID, CONVERT(VARCHAR, msgt.CUSTOMER_NAME, 80) AS CUSTOMER_NAME, ") 
			.append("					MAX(msgt.APPL_NO) AS APPL_NO, msgt.UUID_BRANCH, msgt.BRANCH_NAME,")
			.append("					(SELECT STATUS_TASK_DESC ") 
			.append("						FROM groupTask gt WITH (NOLOCK) ") 
			.append("  						WHERE gt.GROUP_TASK_ID = msgt.GROUP_TASK_ID ") 
			.append("						AND UUID_FORM_CATEGORY = 2 ") 
			.append("					) AS STATUS_TEXT, ")
			.append("					(SELECT STATUS_TASK_DESC ") 
			.append("						FROM groupTask gt WITH (NOLOCK) ") 
			.append("						WHERE gt.GROUP_TASK_ID = msgt.GROUP_TASK_ID ") 
			.append("						AND UUID_FORM_CATEGORY = 8 ")
			.append("					) AS STATUS_GAMBAR ")
			.append("					FROM joinGroupTask msgt WITH (NOLOCK) ")
			.append("					GROUP BY msgt.GROUP_TASK_ID, msgt.UUID_BRANCH, msgt.BRANCH_NAME, msgt.CUSTOMER_NAME ")
			.append("				) msGroupTask")
			.append("			UNION ALL ")
			.append("			SELECT GROUP_TASK_ID, CUSTOMER_NAME, APPL_NO, UUID_BRANCH, BRANCH_NAME, STATUS_TEXT, STATUS_GAMBAR ")
			.append("				FROM (SELECT GROUP_TASK_ID, CONVERT(VARCHAR, fmsgt.CUSTOMER_NAME, 80) as CUSTOMER_NAME, ")
			.append("					MAX(fmsgt.APPL_NO) AS APPL_NO, fmsgt.UUID_BRANCH, fmsgt.BRANCH_NAME, ")
			.append("					(SELECT STATUS_TASK_DESC ") 
			.append("						FROM groupTaskFinal gtf WITH (NOLOCK) ") 
			.append("						WHERE gtf.GROUP_TASK_ID = fmsgt.GROUP_TASK_ID ") 
			.append("						AND UUID_FORM_CATEGORY = 2 ")
			.append("					) AS STATUS_TEXT, ")
			.append("					(SELECT STATUS_TASK_DESC ")
			.append("  						FROM groupTaskFinal gtf WITH (NOLOCK) ")
			.append("						WHERE gtf.GROUP_TASK_ID = fmsgt.GROUP_TASK_ID ")
			.append("						AND UUID_FORM_CATEGORY = 8 ")
			.append("					) AS STATUS_GAMBAR ")
			.append("					FROM joinGroupTaskFinal fmsgt WITH (NOLOCK) ")
			.append("					GROUP BY fmsgt.GROUP_TASK_ID, fmsgt.UUID_BRANCH, fmsgt.BRANCH_NAME, fmsgt.CUSTOMER_NAME ")
			.append("				) finalMsGroupTask")
			.append("			) msgt")
			.append("			WHERE 1=1 ")
			.append(where)
			.append(")b ")
			.append(") c ")
			.append(") a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[5][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    resultAll = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
	    
	    if("1".equals(this.linkEncrypt)){
		    List newRes = new ArrayList();
			for(int i=0; i<resultAll.size(); i++){
				Map map = (Map) resultAll.get(i);
				String[] temp = {map.get("d0").toString()};
				List res = CipherTool.encryptData(temp);
				map.put("d6", res.get(0).toString());
				newRes.add(map);
			}
			result.put(GlobalKey.MAP_RESULT_LIST, newRes);
	    }else{
	    	result.put(GlobalKey.MAP_RESULT_LIST, resultAll);
	    }
	    
		result.put(GlobalKey.MAP_RESULT_SIZE, resultCount);

		return result;
	}
	
    @Override
    public List getDetailGroupTaskWithEncript(String groupTaskId, AuditContext callerId) {
          List<Map<String, Object>> result = new ArrayList<>();
          String[][] params = {{"groupTaskId", groupTaskId}};
          List<Map<String, Object>> resultList = this.getManagerDAO().selectAllNative(
        		  "task.inquirygrouptasksurvey.getDetailGroupTask", params, null);
          
          if (!resultList.isEmpty()) {
                for(Map map : resultList) {
                      String taskId = (String) map.get("d1");
                      try {
                            map.put("d7", taskId);
                      } 
                      catch (Exception e) {
                            throw new RuntimeException(e.getMessage());
                      }
                      
                      result.add(map);
                }
          }
          return result;
    }
	
	@Override
	public MsGrouptask getMsGroupTask(String groupTaskId, AuditContext callerId) {
		MsGrouptask msGroupTask = new MsGrouptask();
		Map map = this.getManagerDAO().selectAll(
			"from MsGrouptask mgt join fetch mgt.msBranch where mgt.groupTaskId = :groupTaskId order by mgt.dtmCrt desc", 
			new Object[][] {{"groupTaskId", groupTaskId}});
		List<MsGrouptask> listResult = (List) map.get(GlobalKey.MAP_RESULT_LIST);
		if (!listResult.isEmpty()) {
			for (MsGrouptask bean : listResult) {
				msGroupTask = bean;
				if (StringUtils.isNotBlank(bean.getApplNo())) {
					break;
				}
			}
		}
		return msGroupTask;
	}

	@Override
	public List splitParams(String params) {
		List result =  new ArrayList<String>();
		String[] temp = params.split("#");
		for(int i=0; i<temp.length; i++){
			result.add(temp[i].toString());
		}
		return result;
	}
}
