package com.adins.mss.businesslogic.impl.survey;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.Clob;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.DistanceUtils;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.survey.InquiryTaskSurveyLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.model.FinalTrTaskhistory;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.TblCaeData;
import com.adins.mss.model.TblPoloData;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.model.custom.InquiryTaskCollectionFinalBean;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.services.model.common.AddTaskCAERequest;
import com.adins.mss.services.model.common.AddTaskPoloRequest;
import com.adins.mss.util.CipherTool;
import com.google.gson.Gson;
import com.itextpdf.text.Chunk;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.ColumnText;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.pdf.PdfWriter;

@SuppressWarnings("rawtypes" )
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericInquiryTaskSurveyLogic extends BaseLogic implements InquiryTaskSurveyLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericInquiryTaskSurveyLogic.class);
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	private GeolocationLogic geocoder;

	public void setGeocoder(GeolocationLogic geocoder) {
		this.geocoder = geocoder;
	}
	
	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
    private final List<String> LIST_REF_ID_ALLOWMAPS = Arrays.asList("SVY_TMPTGL_LUARRMH","SVY_FOTO_VALIDASI_DO","SVY_LGL_LOC_ADDR","SVY_RES_LOC_ADDR","STV_IMG_VISIT", "PRE_RSLT_GEO_TAG");
    private String paramUuidTaskH = "uuidTaskH";
    
    private String clobStringConversion(Clob clb) throws IOException, java.sql.SQLException {
		if (clb == null)
			return "";
		StringBuilder str = new StringBuilder();
		String strng;
		BufferedReader bufferRead = new BufferedReader(clb.getCharacterStream());
		while ((strng = bufferRead.readLine()) != null)
			str.append(strng);
		return str.toString();
	}
    
	@SuppressWarnings("unchecked")
	@Override
	public List listInquiryTaskSurveyNativeString(Object params, AuditContext callerId) {

		List result = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		
		Map<String, String> paramsQuery = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		String selectInside = paramsQuery.get("selectInside");
		String selectOutside = paramsQuery.get("selectOutside");
		String joinInside = paramsQuery.get("joinInside");
		String joinOutside = paramsQuery.get("joinOutside");
		String where = paramsQuery.get("sb");
		String caseWhen = paramsQuery.get("caseWhen");
		
		StringBuilder ordersQueryString = this.sqlPagingOrderBuilder((String) ((Object[][]) params)[19][1], callerId);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("WITH cpx AS (SELECT  MOBILE_TASK_CODE, APP_NO, STATUS, STATS_START_DT, PREP_START_DT, PREP_END_DT, SEQUENCE_PROCESS ")
				.append("FROM STAGING_CREDIT_PROCESS_X_RANK with (nolock)) ")
				.append("SELECT * from ( ")
				.append("SELECT a.*, ROW_NUMBER() OVER ( ")
				.append("ORDER BY rownum) AS recnum FROM ( ")
				.append("select c.uuid_task_h, ")
				.append("isnull(c.CUSTOMER_NAME,'-') CUSTOMER_NAME, ")
				.append("isnull(c.appl_no,'-') appl_no, ")
				.append("isnull(LEFT(CONVERT(VARCHAR, c.assign_date, 113), 17),'-') as assign_date, ")
				.append("isnull(LEFT(CONVERT(VARCHAR, c.download_date, 113),17),'-') as download_date, ")
				.append("isnull(LEFT(CONVERT(VARCHAR, c.submit_date, 113),17),'-') as submit_date, ")
				.append(selectOutside)
				.append("isnull(mssta.status_task_desc,'-') status_task_desc, ")
				.append("isnull(LEFT(CONVERT(VARCHAR, c.send_date, 113),17),'-') as send_date, c.flag, ")
				.append("isnull(LEFT(CONVERT(VARCHAR, c.promise_date, 113),17),'-') as promise_date, ")
				.append("c.task_id, isnull(LEFT(CONVERT(VARCHAR, c.start_dt, 113),17),'-') as start_dt, ")
				.append("flagOdr, msf.FORM_NAME,  ")
				.append("isnull(STATUS, '-') as statusWise, ")
				.append("isnull(LEFT(CONVERT(VARCHAR, STATS_START_DT, 113),17),'-') as status_date, ")
				.append("isnull(LEFT(CONVERT(VARCHAR, PREP_START_DT, 113),17),'-') as prep_start_date, ")
				.append("isnull(LEFT(CONVERT(VARCHAR, PREP_END_DT, 113),17),'-') as prep_send_date, ")
				.append("ROW_NUMBER() OVER ( ").append(ordersQueryString).append(") AS rownum, ")
				.append("c.DOWNLOADABLE ")
				.append("from ( ")
				.append("SELECT trth.uuid_task_h, ")
				.append("trth.customer_name as CUSTOMER_NAME, ")
				.append("trth.appl_no as appl_no, ")
				.append("trth.assign_date as assign_date, ")
				.append("trth.download_date as download_date, ")
				.append("trth.submit_date as submit_date, ")
				.append(selectInside)
				.append("trth.uuid_status_task as uuid_status_task, ")
				.append("trth.send_date as send_date, ")
				.append("'0' as flagOdr, '1' as flag,")
				.append("trth.assign_date as assign_dt, ")
				.append("trth.download_date as download_dt, ")
				.append("trth.submit_date as submit_dt, ")
				.append("trth.send_date as send_dt, ")
				.append("trth.PROMISE_DATE as promise_date, ")
				.append("trth.task_id, trth.start_dtm as start_dt, ")
				.append("trth.uuid_form AS uuid_form, ")
				.append(caseWhen)
				.append("FROM tr_task_h trth with (nolock) ")
				.append(joinInside)
				.append("WHERE trth.uuid_branch = :branchIdLogin ")
				.append(where)
				.append("UNION ALL ")
				.append("SELECT trth.uuid_task_h, ")
				.append("trth.customer_name as CUSTOMER_NAME, ")
				.append("trth.appl_no as appl_no, ")
				.append("trth.assign_date as assign_date, ")
				.append("trth.download_date as download_date, ")
				.append("trth.submit_date as submit_date, ")
				.append(selectInside)
				.append("trth.uuid_status_task as uuid_status_task, ")
				.append("trth.send_date as send_date, ")
				.append("'0' as flagOdr, '1' as flag,")
				.append("trth.assign_date as assign_dt, ")
				.append("trth.download_date as download_dt, ")
				.append("trth.submit_date as submit_dt, ")
				.append("trth.send_date as send_dt, ")
				.append("trth.PROMISE_DATE as promise_date, ")
				.append("trth.task_id, trth.start_dtm as start_dt, ")
				.append("trth.uuid_form AS uuid_form, ")
				.append(caseWhen)
				.append("FROM final_tr_task_h trth with (nolock) ")
				.append(joinInside)
				.append("WHERE trth.uuid_branch = :branchIdLogin ")
				.append(where)
				.append(") c ")
				.append(joinOutside)
				.append("JOIN ms_statustask mssta with (nolock) ON (c.uuid_status_task = mssta.uuid_status_task) ")
				.append("JOIN MS_FORM msf with (nolock) on (msf.uuid_form = c.uuid_form) ")
				.append("left outer JOIN cpx on (c.APPL_NO=cpx.MOBILE_TASK_CODE ")
				.append("or c.APPL_NO = cpx.APP_NO) ")
				.append(") a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start ");
		
//		paramsStack.push(new Object[]{"odr", (String) ((Object[][]) params)[19][1]});	
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[17][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[18][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}

		result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		if("1".equals(this.link_encrypt)){
			List newRes = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				String[] temp = {map.get("d0").toString()};
				List res = CipherTool.encryptData(temp);
				map.put("d0", res.get(0).toString());
				newRes.add(map);
			}
			return newRes;	
		}else{
			return result;
		}
	}
	
	@Override
	public Integer countListInquiryTaskSurveyNativeString(Object params, AuditContext callerId){
		Integer result = 0;
		
		Stack<Object[]> paramsStack = new Stack<>();
		Map<String, String> paramsQuery = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		String where = paramsQuery.get("sb");
		String joinInside = paramsQuery.get("joinInside");
		String joinOutside = paramsQuery.get("joinOutside");
		String selectInside = paramsQuery.get("selectInside");
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT COUNT(1) ")
			.append("from (select  ")
			.append(selectInside)
			.append("trth.uuid_task_h ")
			.append("FROM tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE trth.uuid_branch = :branchIdLogin ")
			.append(where)
			.append("union all select ")
			.append(selectInside)
			.append("trth.uuid_task_h ")
			.append("FROM final_tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE trth.uuid_branch = :branchIdLogin ")
			.append(where)
			.append(") c ")
			.append(joinOutside);
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}

		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		
		return result;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List listInquiryGeneralTask(Object params, AuditContext callerId) {
		List result = Collections.emptyList();
		Object[][] temp = (Object[][]) params;
		String form = (String) temp[1][1];
		String trthFormId = "";
		String formId = "";
		String paramDate = "";
		String paramDateKawan = "";
		String assignDateTrtu = "";
		String statusTask = "";
		String priority = "";
		String region = "";
		String show = "1=1";
		String show2 = "";
		Stack<Object[]> paramStack = new Stack<>();
		
		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
		
		DateTime assignDateStart = null;
		DateTime assignDateEnd = null;
		DateTime downloadDateStart = null;
		DateTime downloadDateEnd = null;
		DateTime sendDateStart = null;
		DateTime sendDateEnd = null;
		DateTime promiseDateStart = null;
		DateTime promiseDateEnd = null;
		DateTime submitDateStart = null;
		DateTime submitDateEnd = null;
		StringBuilder queryBuilder = new StringBuilder();
		//customerName
		if (!"kawan".equalsIgnoreCase(form) && !"update".equalsIgnoreCase(form)) {
			paramStack.push(new Object[] {"customerName", (String)temp[0][1]});
			//form
			if (!form.equals("%")) {
				trthFormId = "trth.UUID_FORM = :uuidForm AND";
				formId = "c.UUID_FORM = :uuidForm AND";
				paramStack.push(new Object[] { "uuidForm", form});
				show += " AND UUID_FORM != '-'";
			}
			//appl No
			paramStack.push(new Object[] {"applNo", (String)temp[2][1]});
			// ---ASSIGN_DATE
			if (!StringUtils.equals("%", (String) temp[3][1])) {
				assignDateStart = formatter.parseDateTime((String) temp[3][1]);
				assignDateStart = assignDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[4][1])) {
				assignDateEnd = formatter.parseDateTime((String) temp[4][1]);
				assignDateEnd = assignDateEnd.minusMillis(3);
			}
			
			// ---DOWNLOAD_DATE
			if (!StringUtils.equals("%", (String) temp[11][1])) {
				downloadDateStart = formatter.parseDateTime((String) temp[11][1]);
				downloadDateStart = downloadDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[12][1])) {
				downloadDateEnd = formatter.parseDateTime((String) temp[12][1]);
				downloadDateEnd = downloadDateEnd.minusMillis(3);
			}
			
			// ---SEND_DATE
			if (!StringUtils.equals("%", (String) temp[13][1])) {
				sendDateStart = formatter.parseDateTime((String) temp[13][1]);
				sendDateStart = sendDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[14][1])) {
				sendDateEnd = formatter.parseDateTime((String) temp[14][1]);
				sendDateEnd = sendDateEnd.minusMillis(3);
			}
			
			// ---PROMISE_DATE
			if (!StringUtils.equals("%", (String) temp[15][1])) {
				promiseDateStart = formatter.parseDateTime((String) temp[15][1]);
				promiseDateStart = promiseDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[16][1])) {
				promiseDateEnd = formatter.parseDateTime((String) temp[16][1]);
				promiseDateEnd = promiseDateEnd.minusMillis(3);
			}
			
			// ---SUBMIT_DATE
			if (!StringUtils.equals("%", (String) temp[17][1])) {
				submitDateStart = formatter.parseDateTime((String) temp[17][1]);
				submitDateStart = submitDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[18][1])) {
				submitDateEnd = formatter.parseDateTime((String) temp[18][1]);
				submitDateEnd = submitDateEnd.minusMillis(3);
			}
			
			if (assignDateStart != null && assignDateEnd != null) {
				paramDate = "AND ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ";
				assignDateTrtu = "AND trtu.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ";
				paramDateKawan = "AND reqDate BETWEEN :assignDateStart AND :assignDateEnd ";
				paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
				paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
				show += " AND ASSIGN_DATE != '-'";
			} 
			
			
			if (downloadDateStart != null && downloadDateEnd != null) {
				paramDate += "AND DOWNLOAD_DATE BETWEEN :downloadDateStart AND :downloadDateEnd ";
				paramStack.push(new Object[] { "downloadDateStart",downloadDateStart.toDate() });
				paramStack.push(new Object[] { "downloadDateEnd",downloadDateEnd.toDate() });
				show += " AND DOWNLOAD_DATE != '-' ";
			} 
			
			if (sendDateStart != null && sendDateEnd != null) {
				paramDate += "AND SEND_DATE BETWEEN :sendDateStart AND :sendDateEnd ";
				paramStack.push(new Object[] { "sendDateStart",sendDateStart.toDate() });
				paramStack.push(new Object[] { "sendDateEnd",sendDateEnd.toDate() });
				show += " AND SEND_DATE != '-' ";
			} 
			
			if (promiseDateStart != null && promiseDateEnd != null) {
				paramDate += "AND PROMISE_DATE BETWEEN :promiseDateStart AND :promiseDateEnd ";
				paramStack.push(new Object[] { "promiseDateStart",promiseDateStart.toDate() });
				paramStack.push(new Object[] { "promiseDateEnd",promiseDateEnd.toDate() });
				show += " AND PROMISE_DATE != '-' ";
			} 
			
			if (submitDateStart != null && submitDateEnd != null) {
				paramDate += "AND SUBMIT_DATE BETWEEN :submitDateStart AND :submitDateEnd ";
				paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
				paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
				show += " AND SUBMIT_DATE != '-' ";
			} 
			
			if (!StringUtils.equals("%", (String) temp[8][1])) {
				paramStack.push(new Object[] { "uuidStatusTask", (String) temp[8][1]});
				statusTask = "AND c.UUID_STATUS_TASK = :uuidStatusTask ";
				show2 += " AND UUID_STATUS_TASK != 0 ";
			}
			
			if (!StringUtils.equals("%", (String) temp[9][1])) {
				paramStack.push(new Object[] { "uuidPriority", (String) temp[9][1]});
				priority = "AND UUID_PRIORITY = :uuidPriority ";
				show2 += " AND UUID_PRIORITY != 0 ";
			}
			
			if (!StringUtils.equals("%", (String) temp[7][1])) {
				paramStack.push(new Object[] { "uuidRegion", (String) temp[7][1]});
				region = "AND msr.UUID_REGION = :uuidRegion ";
				show2 += " AND UUID_REGION != 0 ";
			}
			
			paramStack.push(new Object[] { "fullName", temp[10][1].toString()});
			if (!StringUtils.equals("%", temp[10][1].toString())) {
				show2 += " AND FULL_NAME != '-' ";
			}
			
			paramStack.push(new Object[] {"start", ((Object[][]) params)[5][1]});
			paramStack.push(new Object[] {"end", ((Object[][]) params)[6][1]});
			
			Object[][] sqlParams = new Object[paramStack.size()][2];
		    for (int i = 0; i < paramStack.size(); i++) {
				Object[] objects = paramStack.get(i);
				sqlParams[i] = objects;
			}
		    
			queryBuilder
					.append("SELECT * from (")
					.append("SELECT a.*, ROW_NUMBER() ")
					.append("OVER (ORDER BY rownum) AS recnum ")
					/* INQUIRY TASK SURVEY BY BRANCH */
					.append("FROM (SELECT UUID_TASK_H as UUID_TASK_GENERAL, CUSTOMER_NAME, '-' as UUID_TASK_UPDATE, '-' as DATE_REQUEST, APPL_NO, c.UUID_FORM, FORM_NAME, c.UUID_BRANCH, isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME, "
							+ "isnull(msu.full_name,'-') FULL_NAME, isnull(LEFT(CONVERT(VARCHAR, ASSIGN_DATE, 113), 17),'-') as ASSIGN_DATE,  isnull(LEFT(CONVERT(VARCHAR, c.download_date, 113),17),'-') as DOWNLOAD_DATE, "
							+ "isnull(LEFT(CONVERT(VARCHAR, c.submit_date, 113),17),'-') as SUBMIT_DATE, isnull(mssta.status_task_desc,'-') STATUS_TASK_DESC, isnull(LEFT(CONVERT(VARCHAR, c.send_date, 113),17),'-') as SEND_DATE, "
							+ "c.flag, isnull(LEFT(CONVERT(VARCHAR, c.promise_date, 113),17),'-') as PROMISE_DATE, isnull(LEFT(CONVERT(VARCHAR, c.start_dt, 113),17),'-') as START_DT, isnull(STATUS, '-') as STATUS_WISE, "
							+ "isnull(LEFT(CONVERT(VARCHAR, STATS_START_DT, 113),17),'-') as STATUS_DATE, isnull(LEFT(CONVERT(VARCHAR, PREP_START_DT, 113),17),'-') as PREP_START_DATE, isnull(LEFT(CONVERT(VARCHAR, PREP_END_DT, 113),17),'-') "
							+ "as PREP_SEND_DATE, '-' as [json], ROW_NUMBER() OVER (ORDER BY CUSTOMER_NAME ASC) AS rownum, c.DOWNLOADABLE AS DOWNLOADABLE, [TYPE] = 1, '-' as source, c.UUID_STATUS_TASK as UUID_STATUS_TASK, "
							+ "uuid_priority as UUID_PRIORITY, msr.UUID_REGION as UUID_REGION ")
					.append("from (SELECT trth.uuid_task_h, trth.customer_name as CUSTOMER_NAME, trth.appl_no as appl_no, trth.assign_date as assign_date, trth.download_date as download_date, trth.submit_date as submit_date, "
							+ "trth.UUID_BRANCH as uuid_branch, trth.UUID_MS_USER as uuid_ms_user, trth.uuid_status_task as uuid_status_task, trth.send_date as send_date, '1' as flag, '0' as flagOrder, trth.assign_date as assign_dt, "
							+ "trth.download_date as download_dt, trth.submit_date as submit_dt, trth.send_date as send_dt, trth.PROMISE_DATE as promise_date, trth.task_id, trth.start_dtm as start_dt, trth.uuid_form AS uuid_form, ") 
					.append("  trth.uuid_priority as uuid_priority, ")
					.append("0 AS DOWNLOADABLE ")
					.append("FROM tr_task_h trth with (nolock) WHERE ")
					.append(trthFormId + " 1 = 1 union ALL ")
					.append("SELECT trth.uuid_task_h, trth.customer_name as CUSTOMER_NAME, trth.appl_no as appl_no, trth.assign_date as assign_date, trth.download_date as download_date, trth.submit_date as submit_date, "
							+ "trth.UUID_BRANCH as uuid_branch, trth.UUID_MS_USER as uuid_ms_user, trth.uuid_status_task as uuid_status_task, trth.send_date as send_date, '2' as flag, '0' as flagOrder, "
							+ "trth.assign_date as assign_dt, trth.download_date as download_dt, trth.submit_date as submit_dt, trth.send_date as send_dt, trth.PROMISE_DATE as promise_date, trth.task_id, "
							+ "trth.start_dtm as start_dt, trth.uuid_form AS uuid_form, trth.uuid_priority as uuid_priority, ")
					.append("0 AS DOWNLOADABLE FROM final_tr_task_h trth with (nolock) WHERE ")
					.append(trthFormId + " 1 = 1)c JOIN MS_FORM msf with (nolock) on (msf.uuid_form = c.uuid_form) JOIN ms_statustask mssta with (nolock) ON (c.uuid_status_task = mssta.uuid_status_task) "
							+ "JOIN MS_BRANCH msb with (nolock) on (msb.uuid_branch = c.uuid_branch) LEFT JOIN MS_REGION msr with (nolock) on (msr.uuid_region = msb.uuid_region) "
							+ "left outer JOIN am_msuser msu with (nolock) ON (c.uuid_ms_user = msu.uuid_ms_user) left outer JOIN STAGING_CREDIT_PROCESS_X_RANK scpr WITH(NOLOCK) ON c.APPL_NO = scpr.APP_NO ")
					.append("WHERE " + formId + " APPL_NO LIKE '%' + :applNo + '%' AND CUSTOMER_NAME LIKE '%' + :customerName + '%' AND (FULL_NAME LIKE '%' + :fullName + '%' OR :fullName = '%') ")
					.append(statusTask)
					.append(priority)
					.append(region)
					.append(paramDate)
					.append("UNION ALL ")
					/* INQUIRY TASK UPDATE */
					.append("SELECT trtu.UUID_TASK_H as UUID_TASK_GENERAL, CUSTOMER_NAME, UUID_TASK_UPDATE, '-' as DATE_REQUEST, APPL_NO, '-' AS UUID_FORM, '-' AS FORM_NAME, trth.UUID_BRANCH, BRANCH_NAME, FULL_NAME, isnull(LEFT(CONVERT(VARCHAR, trtu.ASSIGN_DATE, 113), 17),'-') as ASSIGN_DATE, '-' AS DOWNLOAD_DATE, "
							+ "'-' AS SUBMIT_DATE, '-' AS STATUS_TASK_DESC, '-' AS SEND_DATE, '-' AS FLAG, '-' as PROMISE_DATE, '-' AS START_DT, '-' AS STATUS_WISE, '-' AS STATUS_DATE, '-' AS PREP_START_DATE, '-' AS PREP_SEND_DATE, '-' as [json], ROW_NUMBER() OVER (ORDER BY CUSTOMER_NAME ASC) AS rownum, "
							+ "0 AS DOWNLOADABLE, [TYPE] = 2, '-' as source, '-' as UUID_STATUS_TASK, UUID_PRIORITY as UUID_PRIORITY, '-' as UUID_REGION ")
					.append("FROM TR_TASKUPDATE trtu WITH(NOLOCK) JOIN TR_TASK_H trth WITH(NOLOCK) ON trtu.UUID_TASK_H = trth.UUID_TASK_H JOIN MS_BRANCH msb with(NOLOCK) ON trth.UUID_BRANCH = msb.UUID_BRANCH JOIN "
							+ "AM_MSUSER amu with(nolock) on amu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("WHERE APPL_NO LIKE '%' + :applNo + '%' AND CUSTOMER_NAME LIKE '%' + :customerName + '%' AND (FULL_NAME LIKE '%' + :fullName + '%' OR :fullName = '%') ")
					.append(assignDateTrtu)
					.append(priority)
					.append("UNION ALL ")
					/* INQUIRY TASK KAWAN INTERNAL */
					.append("SELECT id AS UUID_TASK_GENERAL, '-' AS CUSTOMER_NAME, '-' as UUID_TASK_UPDATE,  isnull(LEFT(CONVERT(VARCHAR, reqDate, 113), 17),'-') as DATE_REQUEST, taskId as APPL_NO, '-' AS UUID_FORM, '-' AS FORM_NAME, '-' AS UUID_BRANCH, '-' AS BRANCH_NAME, '-' AS FULL_NAME, isnull(LEFT(CONVERT(VARCHAR, reqDate, 113), 17),'-') as ASSIGN_DATE, "
							+ "'-' AS DOWNLOAD_DATE, '-' AS SUBMIT_DATE, '-' AS STATUS_TASK_DESC, '-' AS SEND_DATE, '-' AS FLAG, '-' AS PROMISE_DATE, '-' AS START_DT, '-' AS STATUS_WISE, '-' AS STATUS_DATE, '-' AS PREP_START_DATE, "
							+ "'-' AS PREP_SEND_DATE, [json], ROW_NUMBER() OVER (ORDER BY reqDate) AS rownum, 0 AS DOWNLOADABLE, [TYPE] = 3, source, '-' as UUID_STATUS_TASK, '-' as UUID_PRIORITY, '-' as UUID_REGION ")
					.append("FROM(")
					.append("SELECT DISTINCT UUID_CAE_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(ORDER_NO_CAE as VARCHAR) + ' / ' + msgt.APPL_NO else ORDER_NO_CAE end as taskId, JSON_REQUEST as json, "
							+ "'CAE' as source, cd.DTM_CRT as reqDate ")
					.append("FROM TBL_CAE_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0 ")
					.append("WHERE IS_FLAG_KAWAN_INTERNAL = '1' AND msgt.APPL_NO LIKE '%' + :applNo + '%' AND JSON_VALUE(JSON_REQUEST, '$.customerName') LIKE '%' + :customerName + '%' ")
					.append("UNION ALL ")
					.append("SELECT DISTINCT UUID_POLO_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(TASK_ID_POLO as VARCHAR) + ' / ' + msgt.APPL_NO else TASK_ID_POLO end as taskId, JSON_REQUEST as json, "
							+ "'POLO' as source, cd.DTM_CRT as reqDate ")
					.append("FROM TBL_POLO_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0 ")
					.append("WHERE IS_FLAG_KAWAN_INTERNAL = '1' AND msgt.APPL_NO LIKE '%' + :applNo + '%' AND JSON_VALUE(JSON_REQUEST, '$.customer_name') LIKE '%' + :customerName + '%')kwn ")
					.append("WHERE 1=1 " + paramDateKawan)
					.append(")a WHERE ")
					.append(show)
					.append(show2)
					.append(")b ")
					.append("where b.recnum >= :start and b.recnum <= :end");
			
			String query = queryBuilder.toString();
			String param = "";
			LOG.info("listInquiryGeneralTask.1.query:{}", query);
			for(int i = 0;  i < sqlParams.length; i++) {
				String key = (String) sqlParams[i][0];
				String value = (String) sqlParams[i][1];
				if(i == 0) {
					param = key + "=" + value;
				} else {
					param = param + ", " + key + "=" + value;
				}
				
			}
			LOG.info("listInquiryGeneralTask.1.param:{}", param.toString());
			
			result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		}
		
		else if("kawan".equalsIgnoreCase(form)) {
			String paramUuidStatusTask = StringUtils.EMPTY;
			String paramUuidRegion = StringUtils.EMPTY;
			String paramFullName = StringUtils.EMPTY;
			String paramUuidPriority = StringUtils.EMPTY;
			
			if (!StringUtils.equals("%", (String) temp[8][1])) {
				paramUuidStatusTask = (String) temp[8][1];
			}
			
			if (!StringUtils.equals("%", (String) temp[7][1])) {
				paramUuidRegion = (String) temp[7][1];
			}
			
			if (!StringUtils.equals("%", temp[10][1].toString())) {
				paramFullName = temp[10][1].toString();
			}
			
			if (!StringUtils.equals("%", (String) temp[9][1])) {
				paramUuidPriority = (String) temp[9][1];
			}
			
			if (StringUtils.isNotBlank(paramUuidStatusTask) || StringUtils.isNotBlank(paramUuidRegion) || StringUtils.isNotBlank(paramFullName) || StringUtils.isNotBlank(paramUuidPriority)) {
				return new ArrayList<>();
			}
			
			//customerName
			paramStack.push(new Object[] {"customerName", (String)temp[0][1]});
			//appl No
			paramStack.push(new Object[] {"applNo", (String)temp[2][1]});
			if (!StringUtils.equals("%", (String) temp[3][1])) {
				assignDateStart = formatter.parseDateTime((String) temp[3][1]);
				assignDateStart = assignDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[4][1])) {
				assignDateEnd = formatter.parseDateTime((String) temp[4][1]);
				assignDateEnd = assignDateEnd.minusMillis(3);
			}
			
			if (assignDateStart != null && assignDateEnd != null) {
				paramDateKawan = "AND reqDate BETWEEN :assignDateStart AND :assignDateEnd ";
				paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
				paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
			}
			
			paramStack.push(new Object[] {"start", ((Object[][]) params)[5][1]});
			paramStack.push(new Object[] {"end", ((Object[][]) params)[6][1]});
			Object[][] sqlParams = new Object[paramStack.size()][2];
		    for (int i = 0; i < paramStack.size(); i++) {
				Object[] objects = paramStack.get(i);
				sqlParams[i] = objects;
			}
		    
			queryBuilder
					.append("SELECT * from (")
					.append("SELECT a.*, ROW_NUMBER() ")
					.append("OVER (ORDER BY rownum) AS recnum FROM (")
					.append("SELECT id AS UUID_TASK_GENERAL, '-' AS CUSTOMER_NAME, '-' as UUID_TASK_UPDATE,  isnull(LEFT(CONVERT(VARCHAR, reqDate, 113), 17),'-') as DATE_REQUEST, taskId as APPL_NO, '-' AS UUID_FORM, '-' AS FORM_NAME, '-' AS UUID_BRANCH, '-' AS BRANCH_NAME, '-' AS FULL_NAME, isnull(LEFT(CONVERT(VARCHAR, reqDate, 113), 17),'-') as ASSIGN_DATE, "
							+ "'-' AS DOWNLOAD_DATE, '-' AS SUBMIT_DATE, '-' AS STATUS_TASK_DESC, '-' AS SEND_DATE, '-' AS FLAG, '-' AS PROMISE_DATE, '-' AS START_DT, '-' AS STATUS_WISE, '-' AS STATUS_DATE, '-' AS PREP_START_DATE, "
							+ "'-' AS PREP_SEND_DATE, [json], ROW_NUMBER() OVER (ORDER BY reqDate) AS rownum, 0 AS DOWNLOADABLE, [TYPE] = 3, source ")
					.append("FROM(")
					.append("SELECT DISTINCT UUID_CAE_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(ORDER_NO_CAE as VARCHAR) + ' / ' + msgt.APPL_NO else ORDER_NO_CAE end as taskId, JSON_REQUEST as json, "
							+ "'CAE' as source, cd.DTM_CRT as reqDate ")
					.append("FROM TBL_CAE_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0 ")
					.append("WHERE IS_FLAG_KAWAN_INTERNAL = '1' AND msgt.APPL_NO LIKE '%' + :applNo + '%' AND JSON_VALUE(JSON_REQUEST, '$.customerName') LIKE '%' + :customerName + '%' ")
					.append("UNION ALL ")
					.append("SELECT DISTINCT UUID_POLO_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(TASK_ID_POLO as VARCHAR) + ' / ' + msgt.APPL_NO else TASK_ID_POLO end as taskId, JSON_REQUEST as json, "
							+ "'POLO' as source, cd.DTM_CRT as reqDate ")
					.append("FROM TBL_POLO_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0 ")
					.append("WHERE IS_FLAG_KAWAN_INTERNAL = '1' AND msgt.APPL_NO LIKE '%' + :applNo + '%' AND JSON_VALUE(JSON_REQUEST, '$.customer_name') LIKE '%' + :customerName + '%')kwn ")
					.append("WHERE 1=1 " + paramDateKawan)
					.append(")a where a.rownum <= :end ")
					.append(")b where b.recnum >= :start ");
			
			
			result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);	
		}
		
		else if("update".equalsIgnoreCase(form)) {
			String paramUuidStatusTask = StringUtils.EMPTY;
			String paramUuidRegion = StringUtils.EMPTY;
			String paramFullName = StringUtils.EMPTY;
			
			if (!StringUtils.equals("%", (String) temp[8][1])) {
				paramUuidStatusTask = (String) temp[8][1];
			}
			
			if (!StringUtils.equals("%", (String) temp[7][1])) {
				paramUuidRegion = (String) temp[7][1];
			}
			
			if (!StringUtils.equals("%", temp[10][1].toString())) {
				paramFullName = temp[10][1].toString();
			}
			
			if (StringUtils.isNotBlank(paramUuidStatusTask)) {
				return new ArrayList<>();
			}
			//customerName
			paramStack.push(new Object[] {"customerName", (String)temp[0][1]});
			//appl No
			paramStack.push(new Object[] {"applNo", (String)temp[2][1]});
			// ---ASSIGN_DATE
			if (!StringUtils.equals("%", (String) temp[3][1])) {
				assignDateStart = formatter.parseDateTime((String) temp[3][1]);
				assignDateStart = assignDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[4][1])) {
				assignDateEnd = formatter.parseDateTime((String) temp[4][1]);
				assignDateEnd = assignDateEnd.minusMillis(3);
			}
			
			if (!StringUtils.equals("%", (String) temp[9][1])) {
				paramStack.push(new Object[] { "uuidPriority", (String) temp[9][1]});
				priority = "AND UUID_PRIORITY = :uuidPriority ";
			}
			
			if (assignDateStart != null && assignDateEnd != null) {
				assignDateTrtu = "AND trtu.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ";
				paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
				paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
			} 
			
			paramStack.push(new Object[] {"start", ((Object[][]) params)[5][1]});
			paramStack.push(new Object[] {"end", ((Object[][]) params)[6][1]});
			Object[][] sqlParams = new Object[paramStack.size()][2];
			for (int i = 0; i < paramStack.size(); i++) {
				Object[] objects = paramStack.get(i);
				sqlParams[i] = objects;
			}
			
			queryBuilder
			.append("SELECT * from (")
			.append("SELECT a.*, ROW_NUMBER() ")
			.append("OVER (ORDER BY rownum) AS recnum FROM (")
			.append("SELECT trtu.UUID_TASK_H as UUID_TASK_GENERAL, CUSTOMER_NAME, UUID_TASK_UPDATE, '-' as DATE_REQUEST, APPL_NO, '-' AS UUID_FORM, '-' AS FORM_NAME, trth.UUID_BRANCH, BRANCH_NAME, FULL_NAME, isnull(LEFT(CONVERT(VARCHAR, trtu.ASSIGN_DATE, 113), 17),'-') as ASSIGN_DATE, '-' AS DOWNLOAD_DATE, "
					+ "'-' AS SUBMIT_DATE, '-' AS STATUS_TASK_DESC, '-' AS SEND_DATE, '-' AS FLAG, '-' as PROMISE_DATE, '-' AS START_DT, '-' AS STATUS_WISE, '-' AS STATUS_DATE, '-' AS PREP_START_DATE, '-' AS PREP_SEND_DATE, '-' as [json], ROW_NUMBER() OVER (ORDER BY CUSTOMER_NAME ASC) AS rownum, "
					+ "0 AS DOWNLOADABLE, [TYPE] = 2, '-' as source ")
			.append("FROM TR_TASKUPDATE trtu WITH(NOLOCK) JOIN TR_TASK_H trth WITH(NOLOCK) ON trtu.UUID_TASK_H = trth.UUID_TASK_H JOIN MS_BRANCH msb with(NOLOCK) ON trth.UUID_BRANCH = msb.UUID_BRANCH JOIN "
					+ "AM_MSUSER amu with(nolock) on amu.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("WHERE APPL_NO LIKE '%' + :applNo + '%' AND CUSTOMER_NAME LIKE '%' + :customerName + '%' ");
			
			if (StringUtils.isNotBlank(paramFullName)) {
				queryBuilder.append(" AND amu.FULL_NAME like '%"+paramFullName+"%' ");	
			}
			
			if (StringUtils.isNotBlank(paramUuidRegion)) {
				queryBuilder.append(" AND msb.uuid_region = "+paramUuidRegion+" ");
			}
			
			queryBuilder.append(assignDateTrtu)
			.append(priority)
			.append(")a where a.rownum <= :end ")
			.append(")b where b.recnum >= :start ");
			
			result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);	
		}
		Iterator itr = result.iterator();
		while (itr.hasNext()) {
			Map<String, Object> row = (Map<String, Object>) itr.next();
			try {
				String json = clobStringConversion((Clob) row.get("d22"));
				row.put("json", json);
			} catch (Exception e) {
				row.put("json", "-");
				LOG.warn("Error CLOB Conversion For Json with Message ( "+e.getMessage()+" )");
			}
			row.remove("d22");
		}
		if("1".equals(this.link_encrypt)){
			List newRes = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				String[] temp2 = {map.get("d0").toString()};
				String[] temp3 = {map.get("d2").toString()};
				List res = CipherTool.encryptData(temp2);
				List res2 = CipherTool.encryptData(temp3);
				map.put("encryptedUuid", res.get(0).toString());
				map.put("taskUpdateUuid", res2.get(0).toString());
				newRes.add(map);
			}
			return newRes;	
		}else{
			List newRes = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				map.put("encryptedUuid", map.get("d0").toString());
				map.put("taskUpdateUuid", map.get("d2").toString());
				newRes.add(map);
			}
			return newRes;
		}
	}
	
	@Override
	public Integer countListGeneralTask(Object params, AuditContext callerId){
		Integer result = 0;
		
		Object[][] temp = (Object[][]) params;
		String form = (String) temp[1][1];
		String trthFormId = "";
		String formId = "";
		String paramDate = "";
		String paramDateKawan = "";
		String assignDateTrtu = "";
		String statusTask = "";
		String priority = "";
		String region = "";
		String show = "1=1";
		String show2 = "";
		Stack<Object[]> paramStack = new Stack<>();
		
		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
		
		DateTime assignDateStart = null;
		DateTime assignDateEnd = null;
		DateTime downloadDateStart = null;
		DateTime downloadDateEnd = null;
		DateTime sendDateStart = null;
		DateTime sendDateEnd = null;
		DateTime promiseDateStart = null;
		DateTime promiseDateEnd = null;
		DateTime submitDateStart = null;
		DateTime submitDateEnd = null;
		StringBuilder queryBuilder = new StringBuilder();
		//customerName
		if(!"kawan".equalsIgnoreCase(form) && !"update".equalsIgnoreCase(form)) {
			paramStack.push(new Object[] {"customerName", (String)temp[0][1]});
			//form
			if(!form.equals("%")) {
				trthFormId = "trth.UUID_FORM = :uuidForm AND";
				formId = "c.UUID_FORM = :uuidForm AND";
				paramStack.push(new Object[] { "uuidForm", form});
				show += " AND UUID_FORM != '-'";
			}
			//appl No
			paramStack.push(new Object[] {"applNo", (String)temp[2][1]});
			// ---ASSIGN_DATE
			if (!StringUtils.equals("%", (String) temp[3][1])) {
				assignDateStart = formatter.parseDateTime((String) temp[3][1]);
				assignDateStart = assignDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[4][1])) {
				assignDateEnd = formatter.parseDateTime((String) temp[4][1]);
				assignDateEnd = assignDateEnd.minusMillis(3);
			}
			
			// ---DOWNLOAD_DATE
			if (!StringUtils.equals("%", (String) temp[11][1])) {
				downloadDateStart = formatter.parseDateTime((String) temp[11][1]);
				downloadDateStart = downloadDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[12][1])) {
				downloadDateEnd = formatter.parseDateTime((String) temp[12][1]);
				downloadDateEnd = downloadDateEnd.minusMillis(3);
			}
			
			// ---SEND_DATE
			if (!StringUtils.equals("%", (String) temp[13][1])) {
				sendDateStart = formatter.parseDateTime((String) temp[13][1]);
				sendDateStart = sendDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[14][1])) {
				sendDateEnd = formatter.parseDateTime((String) temp[14][1]);
				sendDateEnd = sendDateEnd.minusMillis(3);
			}
			
			// ---PROMISE_DATE
			if (!StringUtils.equals("%", (String) temp[15][1])) {
				promiseDateStart = formatter.parseDateTime((String) temp[15][1]);
				promiseDateStart = promiseDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[16][1])) {
				promiseDateEnd = formatter.parseDateTime((String) temp[16][1]);
				promiseDateEnd = promiseDateEnd.minusMillis(3);
			}
			
			// ---SUBMIT_DATE
			if (!StringUtils.equals("%", (String) temp[17][1])) {
				submitDateStart = formatter.parseDateTime((String) temp[17][1]);
				submitDateStart = submitDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[18][1])) {
				submitDateEnd = formatter.parseDateTime((String) temp[18][1]);
				submitDateEnd = submitDateEnd.minusMillis(3);
			}
			
			if (assignDateStart != null && assignDateEnd != null) {
				paramDate = "AND ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ";
				assignDateTrtu = "AND trtu.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ";
				paramDateKawan = "AND reqDate BETWEEN :assignDateStart AND :assignDateEnd ";
				paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
				paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
				show += " AND ASSIGN_DATE != '-'";
			} 
			
			if (downloadDateStart != null && downloadDateEnd != null) {
				paramDate += "AND DOWNLOAD_DATE BETWEEN :downloadDateStart AND :downloadDateEnd ";
				paramStack.push(new Object[] { "downloadDateStart",downloadDateStart.toDate() });
				paramStack.push(new Object[] { "downloadDateEnd",downloadDateEnd.toDate() });
				show += " AND DOWNLOAD_DATE != '-' ";
			} 
			
			if (sendDateStart != null && sendDateEnd != null) {
				paramDate += "AND SEND_DATE BETWEEN :sendDateStart AND :sendDateEnd ";
				paramStack.push(new Object[] { "sendDateStart",sendDateStart.toDate() });
				paramStack.push(new Object[] { "sendDateEnd",sendDateEnd.toDate() });
				show += " AND SEND_DATE != '-' ";
			} 
			
			if (promiseDateStart != null && promiseDateEnd != null) {
				paramDate += "AND PROMISE_DATE BETWEEN :promiseDateStart AND :promiseDateEnd ";
				paramStack.push(new Object[] { "promiseDateStart",promiseDateStart.toDate() });
				paramStack.push(new Object[] { "promiseDateEnd",promiseDateEnd.toDate() });
				show += " AND PROMISE_DATE != '-' ";
			} 
			
			if (submitDateStart != null && submitDateEnd != null) {
				paramDate += "AND SUBMIT_DATE BETWEEN :submitDateStart AND :submitDateEnd ";
				paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
				paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
				show += " AND SUBMIT_DATE != '-' ";
			} 
			
			if (!StringUtils.equals("%", (String) temp[8][1])) {
				paramStack.push(new Object[] { "uuidStatusTask", (String) temp[8][1]});
				statusTask = "AND c.UUID_STATUS_TASK = :uuidStatusTask ";
				show2 += " AND UUID_STATUS_TASK != 0 ";
			}
			
			if (!StringUtils.equals("%", (String) temp[9][1])) {
				paramStack.push(new Object[] { "uuidPriority", (String) temp[9][1]});
				priority = "AND UUID_PRIORITY = :uuidPriority ";
				show2 += " AND UUID_PRIORITY != 0 ";
			}
			
			if (!StringUtils.equals("%", (String) temp[7][1])) {
				paramStack.push(new Object[] { "uuidRegion", (String) temp[7][1]});
				region = "AND msr.UUID_REGION = :uuidRegion ";
				show2 += " AND UUID_REGION != 0 ";
			}
			
			paramStack.push(new Object[] { "fullName", (String) temp[10][1]});
			if (!StringUtils.equals("%", (String) temp[10][1])) {
				show2 += " AND FULL_NAME != '-' ";
			}
			
			queryBuilder
					.append("SELECT COUNT(a.UUID_TASK_GENERAL) ")
					/* INQUIRY TASK SURVEY BY BRANCH */
					.append("FROM (SELECT UUID_TASK_H as UUID_TASK_GENERAL, CUSTOMER_NAME, '-' as UUID_TASK_UPDATE, '-' as DATE_REQUEST, APPL_NO, c.UUID_FORM, FORM_NAME, c.UUID_BRANCH, isnull(msb.BRANCH_NAME,'-') as BRANCH_NAME, "
							+ "isnull(msu.full_name,'-') FULL_NAME, isnull(LEFT(CONVERT(VARCHAR, ASSIGN_DATE, 113), 17),'-') as ASSIGN_DATE,  isnull(LEFT(CONVERT(VARCHAR, c.download_date, 113),17),'-') as DOWNLOAD_DATE, "
							+ "isnull(LEFT(CONVERT(VARCHAR, c.submit_date, 113),17),'-') as SUBMIT_DATE, isnull(mssta.status_task_desc,'-') STATUS_TASK_DESC, isnull(LEFT(CONVERT(VARCHAR, c.send_date, 113),17),'-') as SEND_DATE, "
							+ "c.flag, isnull(LEFT(CONVERT(VARCHAR, c.promise_date, 113),17),'-') as PROMISE_DATE, isnull(LEFT(CONVERT(VARCHAR, c.start_dt, 113),17),'-') as START_DT, isnull(STATUS, '-') as STATUS_WISE, "
							+ "isnull(LEFT(CONVERT(VARCHAR, STATS_START_DT, 113),17),'-') as STATUS_DATE, isnull(LEFT(CONVERT(VARCHAR, PREP_START_DT, 113),17),'-') as PREP_START_DATE, isnull(LEFT(CONVERT(VARCHAR, PREP_END_DT, 113),17),'-') "
							+ "as PREP_SEND_DATE, '-' as [json], ROW_NUMBER() OVER (ORDER BY CUSTOMER_NAME ASC) AS rownum, c.DOWNLOADABLE AS DOWNLOADABLE, [TYPE] = 1, '-' as source, c.UUID_STATUS_TASK as UUID_STATUS_TASK, " 
							+ "uuid_priority as UUID_PRIORITY, msr.UUID_REGION as UUID_REGION ")
					.append("from (SELECT trth.uuid_task_h, trth.customer_name as CUSTOMER_NAME, trth.appl_no as appl_no, trth.assign_date as assign_date, trth.download_date as download_date, trth.submit_date as submit_date, "
							+ "trth.UUID_BRANCH as uuid_branch, trth.UUID_MS_USER as uuid_ms_user, trth.uuid_status_task as uuid_status_task, trth.send_date as send_date, '1' as flag, '0' as flagOrder, trth.assign_date as assign_dt, "
							+ "trth.download_date as download_dt, trth.submit_date as submit_dt, trth.send_date as send_dt, trth.PROMISE_DATE as promise_date, trth.task_id, trth.start_dtm as start_dt, trth.uuid_form AS uuid_form, ") 
					.append("  trth.uuid_priority as uuid_priority, ")
					.append("0 AS DOWNLOADABLE ")
					.append("FROM tr_task_h trth with (nolock) WHERE ")
					.append(trthFormId + " 1 = 1 union ALL ")
					.append("SELECT trth.uuid_task_h, trth.customer_name as CUSTOMER_NAME, trth.appl_no as appl_no, trth.assign_date as assign_date, trth.download_date as download_date, trth.submit_date as submit_date, "
							+ "trth.UUID_BRANCH as uuid_branch, trth.UUID_MS_USER as uuid_ms_user, trth.uuid_status_task as uuid_status_task, trth.send_date as send_date, '2' as flag, '0' as flagOrder, "
							+ "trth.assign_date as assign_dt, trth.download_date as download_dt, trth.submit_date as submit_dt, trth.send_date as send_dt, trth.PROMISE_DATE as promise_date, trth.task_id, "
							+ "trth.start_dtm as start_dt, trth.uuid_form AS uuid_form, trth.uuid_priority as uuid_priority, ")
					.append("0 AS DOWNLOADABLE FROM final_tr_task_h trth with (nolock) WHERE ")
					.append(trthFormId + " 1 = 1)c JOIN MS_FORM msf with (nolock) on (msf.uuid_form = c.uuid_form) JOIN ms_statustask mssta with (nolock) ON (c.uuid_status_task = mssta.uuid_status_task) "
							+ "JOIN MS_BRANCH msb with (nolock) on (msb.uuid_branch = c.uuid_branch) LEFT JOIN MS_REGION msr with (nolock) on (msr.uuid_region = msb.uuid_region) "
							+ "left outer JOIN am_msuser msu with (nolock) ON (c.uuid_ms_user = msu.uuid_ms_user) left outer JOIN STAGING_CREDIT_PROCESS_X_RANK scpr WITH(NOLOCK) ON c.APPL_NO = scpr.APP_NO ")
					.append("WHERE " + formId + " APPL_NO LIKE '%' + :applNo + '%' AND CUSTOMER_NAME LIKE '%' + :customerName + '%' AND FULL_NAME LIKE '%' + :fullName + '%' ")
					.append(statusTask)
					.append(priority)
					.append(region)
					.append(paramDate)
					.append("UNION ALL ")
					/* INQUIRY TASK UPDATE */
					.append("SELECT trtu.UUID_TASK_H as UUID_TASK_GENERAL, CUSTOMER_NAME, UUID_TASK_UPDATE, '-' as DATE_REQUEST, APPL_NO, '-' AS UUID_FORM, '-' AS FORM_NAME, trth.UUID_BRANCH, BRANCH_NAME, FULL_NAME, isnull(LEFT(CONVERT(VARCHAR, trtu.ASSIGN_DATE, 113), 17),'-') as ASSIGN_DATE, '-' AS DOWNLOAD_DATE, "
							+ "'-' AS SUBMIT_DATE, '-' AS STATUS_TASK_DESC, '-' AS SEND_DATE, '-' AS FLAG, '-' as PROMISE_DATE, '-' AS START_DT, '-' AS STATUS_WISE, '-' AS STATUS_DATE, '-' AS PREP_START_DATE, '-' AS PREP_SEND_DATE, '-' as [json], ROW_NUMBER() OVER (ORDER BY CUSTOMER_NAME ASC) AS rownum, "
							+ "0 AS DOWNLOADABLE, [TYPE] = 2, '-' as source, '-' as UUID_STATUS_TASK, UUID_PRIORITY as UUID_PRIORITY, '-' as UUID_REGION ")
					.append("FROM TR_TASKUPDATE trtu WITH(NOLOCK) JOIN TR_TASK_H trth WITH(NOLOCK) ON trtu.UUID_TASK_H = trth.UUID_TASK_H JOIN MS_BRANCH msb with(NOLOCK) ON trth.UUID_BRANCH = msb.UUID_BRANCH JOIN "
							+ "AM_MSUSER amu with(nolock) on amu.UUID_MS_USER = trth.UUID_MS_USER ")
					.append("WHERE APPL_NO LIKE '%' + :applNo + '%' AND CUSTOMER_NAME LIKE '%' + :customerName + '%' AND FULL_NAME LIKE '%' + :fullName + '%' ")
					.append(assignDateTrtu)
					.append(priority)
					.append("UNION ALL ")
					/* INQUIRY TASK KAWAN INTERNAL */
					.append("SELECT id AS UUID_TASK_GENERAL, '-' AS CUSTOMER_NAME, '-' as UUID_TASK_UPDATE,  isnull(LEFT(CONVERT(VARCHAR, reqDate, 113), 17),'-') as DATE_REQUEST, taskId as APPL_NO, '-' AS UUID_FORM, '-' AS FORM_NAME, '-' AS UUID_BRANCH, '-' AS BRANCH_NAME, '-' AS FULL_NAME, '-' as ASSIGN_DATE, "
							+ "'-' AS DOWNLOAD_DATE, '-' AS SUBMIT_DATE, '-' AS STATUS_TASK_DESC, '-' AS SEND_DATE, '-' AS FLAG, '-' AS PROMISE_DATE, '-' AS START_DT, '-' AS STATUS_WISE, '-' AS STATUS_DATE, '-' AS PREP_START_DATE, "
							+ "'-' AS PREP_SEND_DATE, [json], ROW_NUMBER() OVER (ORDER BY reqDate) AS rownum, 0 AS DOWNLOADABLE, [TYPE] = 3, source, '-' as UUID_STATUS_TASK, '-' as UUID_PRIORITY, '-' as UUID_REGION ")
					.append("FROM(")
					.append("SELECT DISTINCT UUID_CAE_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(ORDER_NO_CAE as VARCHAR) + ' / ' + msgt.APPL_NO else ORDER_NO_CAE end as taskId, JSON_REQUEST as json, "
							+ "'CAE' as source, cd.DTM_CRT as reqDate ")
					.append("FROM TBL_CAE_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0 ")
					.append("WHERE IS_FLAG_KAWAN_INTERNAL = '1' AND msgt.APPL_NO LIKE '%' + :applNo + '%' AND JSON_VALUE(JSON_REQUEST, '$.customerName') LIKE '%' + :customerName + '%' ")
					.append("UNION ALL ")
					.append("SELECT DISTINCT UUID_POLO_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(TASK_ID_POLO as VARCHAR) + ' / ' + msgt.APPL_NO else TASK_ID_POLO end as taskId, JSON_REQUEST as json, "
							+ "'POLO' as source, cd.DTM_CRT as reqDate ")
					.append("FROM TBL_POLO_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0 ")
					.append("WHERE IS_FLAG_KAWAN_INTERNAL = '1' AND msgt.APPL_NO LIKE '%' + :applNo + '%' AND JSON_VALUE(JSON_REQUEST, '$.customer_name') LIKE '%' + :customerName + '%')kwn ")
					.append("WHERE 1=1 " + paramDateKawan)
					.append(")a WHERE ")
					.append(show)
					.append(show2);
			Object[][] sqlParams = new Object[paramStack.size()][2];
		    for (int i = 0; i < paramStack.size(); i++) {
				Object[] objects = paramStack.get(i);
				sqlParams[i] = objects;
			}

			result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		}
		else if("kawan".equalsIgnoreCase(form)) {
			String paramUuidStatusTask = StringUtils.EMPTY;
			String paramUuidRegion = StringUtils.EMPTY;
			String paramFullName = StringUtils.EMPTY;
			String paramUuidPriority = StringUtils.EMPTY;
			
			if (!StringUtils.equals("%", (String) temp[8][1])) {
				paramUuidStatusTask = (String) temp[8][1];
			}
			
			if (!StringUtils.equals("%", (String) temp[7][1])) {
				paramUuidRegion = (String) temp[7][1];
			}
			
			if (!StringUtils.equals("%", temp[10][1].toString())) {
				paramFullName = temp[10][1].toString();
			}
			
			if (!StringUtils.equals("%", (String) temp[9][1])) {
				paramUuidPriority = (String) temp[9][1];
			}
			
			if (StringUtils.isNotBlank(paramUuidStatusTask) || StringUtils.isNotBlank(paramUuidRegion) || StringUtils.isNotBlank(paramFullName) || StringUtils.isNotBlank(paramUuidPriority)) {
				return 0;
			}
			
			paramStack.push(new Object[] {"customerName", (String)temp[0][1]});
			//form
			//appl No
			paramStack.push(new Object[] {"applNo", (String)temp[2][1]});
			if (!StringUtils.equals("%", (String) temp[3][1])) {
				assignDateStart = formatter.parseDateTime((String) temp[3][1]);
				assignDateStart = assignDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[4][1])) {
				assignDateEnd = formatter.parseDateTime((String) temp[4][1]);
				assignDateEnd = assignDateEnd.minusMillis(3);
			}
			
			if (assignDateStart != null && assignDateEnd != null) {
				paramDateKawan = "AND reqDate BETWEEN :assignDateStart AND :assignDateEnd ";
				paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
				paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
			}
			
			Object[][] sqlParams = new Object[paramStack.size()][2];
		    for (int i = 0; i < paramStack.size(); i++) {
				Object[] objects = paramStack.get(i);
				sqlParams[i] = objects;
			}
		    
			queryBuilder
					.append("SELECT COUNT(a.UUID_TASK_GENERAL) FROM (")
					.append("SELECT id AS UUID_TASK_GENERAL, '-' AS CUSTOMER_NAME, '-' as UUID_TASK_UPDATE,  isnull(LEFT(CONVERT(VARCHAR, reqDate, 113), 17),'-') as DATE_REQUEST, taskId as APPL_NO, '-' AS UUID_FORM, '-' AS FORM_NAME, '-' AS UUID_BRANCH, '-' AS BRANCH_NAME, '-' AS FULL_NAME, '-' as ASSIGN_DATE, "
							+ "'-' AS DOWNLOAD_DATE, '-' AS SUBMIT_DATE, '-' AS STATUS_TASK_DESC, '-' AS SEND_DATE, '-' AS FLAG, '-' AS PROMISE_DATE, '-' AS START_DT, '-' AS STATUS_WISE, '-' AS STATUS_DATE, '-' AS PREP_START_DATE, "
							+ "'-' AS PREP_SEND_DATE, [json], ROW_NUMBER() OVER (ORDER BY reqDate) AS rownum, 0 AS DOWNLOADABLE, [TYPE] = 3, source ")
					.append("FROM(")
					.append("SELECT DISTINCT UUID_CAE_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(ORDER_NO_CAE as VARCHAR) + ' / ' + msgt.APPL_NO else ORDER_NO_CAE end as taskId, JSON_REQUEST as json, "
							+ "'CAE' as source, cd.DTM_CRT as reqDate ")
					.append("FROM TBL_CAE_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0 ")
					.append("WHERE IS_FLAG_KAWAN_INTERNAL = '1' AND msgt.APPL_NO LIKE '%' + :applNo + '%' AND JSON_VALUE(JSON_REQUEST, '$.customerName') LIKE '%' + :customerName + '%' ")
					.append("UNION ALL ")
					.append("SELECT DISTINCT UUID_POLO_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(TASK_ID_POLO as VARCHAR) + ' / ' + msgt.APPL_NO else TASK_ID_POLO end as taskId, JSON_REQUEST as json, "
							+ "'POLO' as source, cd.DTM_CRT as reqDate ")
					.append("FROM TBL_POLO_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0 ")
					.append("WHERE IS_FLAG_KAWAN_INTERNAL = '1' AND msgt.APPL_NO LIKE '%' + :applNo + '%' AND JSON_VALUE(JSON_REQUEST, '$.customer_name') LIKE '%' + :customerName + '%')kwn ")
					.append("WHERE 1=1 " + paramDateKawan)
					.append(")a ");
			result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		}
		else if("update".equalsIgnoreCase(form)) {
			String paramUuidStatusTask = StringUtils.EMPTY;
			String paramUuidRegion = StringUtils.EMPTY;
			String paramFullName = StringUtils.EMPTY;
			
			if (!StringUtils.equals("%", (String) temp[8][1])) {
				paramUuidStatusTask = (String) temp[8][1];
			}
			
			if (!StringUtils.equals("%", (String) temp[7][1])) {
				paramUuidRegion = (String) temp[7][1];
			}
			
			if (!StringUtils.equals("%", temp[10][1].toString())) {
				paramFullName = temp[10][1].toString();
			}
			
			if (StringUtils.isNotBlank(paramUuidStatusTask)) {
				return 0;
			}
			
			paramStack.push(new Object[] {"customerName", (String)temp[0][1]});
			//appl No
			paramStack.push(new Object[] {"applNo", (String)temp[2][1]});
			// ---ASSIGN_DATE
			if (!StringUtils.equals("%", (String) temp[3][1])) {
				assignDateStart = formatter.parseDateTime((String) temp[3][1]);
				assignDateStart = assignDateStart.withMillisOfDay(0);
			}
			if (!StringUtils.equals("%", (String) temp[4][1])) {
				assignDateEnd = formatter.parseDateTime((String) temp[4][1]);
				assignDateEnd = assignDateEnd.minusMillis(3);
			}
			
			if (assignDateStart != null && assignDateEnd != null) {
				assignDateTrtu = "AND trtu.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ";
				paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
				paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
			} 
			
			if (!StringUtils.equals("%", (String) temp[9][1])) {
				paramStack.push(new Object[] { "uuidPriority", (String) temp[9][1]});
				priority = "AND UUID_PRIORITY = :uuidPriority ";
			}
			
			Object[][] sqlParams = new Object[paramStack.size()][2];
			for (int i = 0; i < paramStack.size(); i++) {
				Object[] objects = paramStack.get(i);
				sqlParams[i] = objects;
			}
			
			queryBuilder
			.append("SELECT COUNT(a.UUID_TASK_GENERAL) FROM (")
			.append("SELECT trtu.UUID_TASK_H as UUID_TASK_GENERAL, CUSTOMER_NAME, UUID_TASK_UPDATE, '-' as DATE_REQUEST, APPL_NO, '-' AS UUID_FORM, '-' AS FORM_NAME, trth.UUID_BRANCH, BRANCH_NAME, FULL_NAME, isnull(LEFT(CONVERT(VARCHAR, trtu.ASSIGN_DATE, 113), 17),'-') as ASSIGN_DATE, '-' AS DOWNLOAD_DATE, "
					+ "'-' AS SUBMIT_DATE, '-' AS STATUS_TASK_DESC, '-' AS SEND_DATE, '-' AS FLAG, '-' as PROMISE_DATE, '-' AS START_DT, '-' AS STATUS_WISE, '-' AS STATUS_DATE, '-' AS PREP_START_DATE, '-' AS PREP_SEND_DATE, '-' as [json], ROW_NUMBER() OVER (ORDER BY CUSTOMER_NAME ASC) AS rownum, "
					+ "0 AS DOWNLOADABLE, [TYPE] = 2, '-' as source ")
			.append("FROM TR_TASKUPDATE trtu WITH(NOLOCK) JOIN TR_TASK_H trth WITH(NOLOCK) ON trtu.UUID_TASK_H = trth.UUID_TASK_H JOIN MS_BRANCH msb with(NOLOCK) ON trth.UUID_BRANCH = msb.UUID_BRANCH JOIN "
					+ "AM_MSUSER amu with(nolock) on amu.UUID_MS_USER = trth.UUID_MS_USER ")
			.append("WHERE APPL_NO LIKE '%' + :applNo + '%' AND CUSTOMER_NAME LIKE '%' + :customerName + '%' ");
			
			if (StringUtils.isNotBlank(paramFullName)) {
				queryBuilder.append(" AND amu.FULL_NAME like '%"+paramFullName+"%' ");	
			}
			
			if (StringUtils.isNotBlank(paramUuidRegion)) {
				queryBuilder.append(" AND msb.uuid_region = "+paramUuidRegion+" ");
			}
			
			queryBuilder.append(assignDateTrtu)
			.append(priority)
			.append(")a ");
			
			result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		}
		
		return result;
	}
	
	/*0	customerName, 1	branchId, 2 customerAddress,
	3 applNo, 4 fullName, 5 assignDateStart, 6 assignDateEnd, 
	7 submitDateStart, 8 submitDateEnd, 9 retrieveDateStart,
	10 retrieveDateEnd, 11 branchIdLogin, 12 statusId,
	13 subsystemId, 14 priorityId, 15 formId, 16 currentDate,
	17 start, 18 end, 19 odr, 20 sendDateStart, 21 sendDateEnd,
	22 uuidUser */
	private Map<String, String> sqlPagingBuilder(Object[][] params,Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null){
			return new HashMap<String, String>();
		}

		Map<String, String> mapResult = new HashMap<String, String>();
		StringBuilder sb = new StringBuilder();
		StringBuilder sbJoinInside = new StringBuilder();
		StringBuilder sbJoinOutside = new StringBuilder();
		StringBuilder sbSelectInside = new StringBuilder();
		StringBuilder sbSelectOutside = new StringBuilder();
		StringBuilder sbCaseWhen = new StringBuilder();

		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
		DateTime currentDate = formatter.parseDateTime((String) params[16][1]);
		currentDate = currentDate.minusMillis(3);
		// ---ASSIGN_DATE
		DateTime assignDateStart = null, assignDateEnd = null;
		if (params.length > 5&& !StringUtils.equals("%", (String) params[5][1])) {
			assignDateStart = formatter.parseDateTime((String) params[5][1]);
			assignDateStart = assignDateStart.withMillisOfDay(0);
		}
		if (params.length > 5&& !StringUtils.equals("%", (String) params[6][1])) {
			assignDateEnd = formatter.parseDateTime((String) params[6][1]);
			assignDateEnd = assignDateEnd.minusMillis(3);
		}
		
		if (assignDateStart != null && assignDateEnd != null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart == null && assignDateEnd != null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN '1990-01-01 00:00:00' AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart != null && assignDateEnd == null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN :assignDateStart AND :currentDate ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
//		else if(assignDateStart == null && assignDateEnd == null){
//			sb.append("AND trth.ASSIGN_DATE BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}

		// ---SUBMIT_DATE
		DateTime submitDateStart = null, submitDateEnd = null;
		if (params.length > 7&& !StringUtils.equals("%", (String) params[7][1])) {
			submitDateStart = formatter.parseDateTime((String) params[7][1]);
			submitDateStart = submitDateStart.withMillisOfDay(0);
		}
		if (params.length > 7&& !StringUtils.equals("%", (String) params[8][1])) {
			submitDateEnd = formatter.parseDateTime((String) params[8][1]);
			submitDateEnd = submitDateEnd.minusMillis(3);
		}
		if (submitDateStart != null && submitDateEnd != null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN :submitDateStart AND :submitDateEnd ");
			paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
			paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
		} 
		else if (submitDateStart == null && submitDateEnd != null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN '1990-01-01 00:00:00' AND :submitDateEnd ");
			paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
		} 
		else if (submitDateStart != null && submitDateEnd == null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN :submitDateStart AND :currentDate ");
			paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
//		else if(submitDateStart == null && submitDateEnd == null){
//			sb.append("AND trth.SUBMIT_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}
		
		// ---PROMISE DATE
		DateTime promiseDateStart = null, promiseDateEnd = null;
		if (params.length > 23
				&& !StringUtils.equals("%", (String) params[23][1])) {
			promiseDateStart = formatter.parseDateTime((String) params[23][1]);
			promiseDateStart = promiseDateStart.withMillisOfDay(0);
		}
		if (params.length > 23
				&& !StringUtils.equals("%", (String) params[24][1])) {
			promiseDateEnd = formatter.parseDateTime((String) params[24][1]);
			promiseDateEnd = promiseDateEnd.minusMillis(3);
		}
		if (promiseDateStart != null && promiseDateEnd != null) {
			sb.append("AND trth.PROMISE_DATE BETWEEN :promiseDateStart AND :promiseDateEnd "
					+ "AND trth.PROMISE_DATE is not null ");
			paramStack.push(new Object[] { "promiseDateStart",
					promiseDateStart.toDate() });
			paramStack.push(new Object[] { "promiseDateEnd",
					promiseDateEnd.toDate() });
		}
//		else if (promiseDateStart == null && promiseDateEnd != null) {
//			sb.append("AND trth.PROMISE_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :promiseDateEnd ");
//			paramStack.push(new Object[] { "promiseDateEnd",
//					promiseDateEnd.toDate() });
//		} else if (promiseDateStart != null && promiseDateEnd == null) {
//			sb.append("AND trth.PROMISE_DATE, '1990-01-01 00:00:00') BETWEEN :promiseDateStart AND :currentDate ");
//			paramStack.push(new Object[] { "promiseDateStart",
//					promiseDateStart.toDate() });
//			paramStack
//					.push(new Object[] { "currentDate", currentDate.toDate() });
//		} else if (promiseDateStart == null && promiseDateEnd == null) {
//			sb.append("AND trth.PROMISE_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack
//					.push(new Object[] { "currentDate", currentDate.toDate() });
//		}
		
		// ---DOWNLOAD_DATE
		DateTime downloadDateStart = null, downloadDateEnd = null;
		if (params.length > 9&& !StringUtils.equals("%", (String) params[9][1])) {
			downloadDateStart = formatter.parseDateTime((String) params[9][1]);
			downloadDateStart = downloadDateStart.withMillisOfDay(0);
		}
		if (params.length > 9&& !StringUtils.equals("%", (String) params[10][1])) {
			downloadDateEnd = formatter.parseDateTime((String) params[10][1]);
			downloadDateEnd = downloadDateEnd.minusMillis(3);
		}
		if (downloadDateStart != null && downloadDateEnd != null) {
			sb.append("AND trth.DOWNLOAD_DATE BETWEEN :retrieveDateStart AND :retrieveDateEnd ");
			paramStack.push(new Object[] { "retrieveDateStart",downloadDateStart.toDate() });
			paramStack.push(new Object[] { "retrieveDateEnd",downloadDateEnd.toDate() });
		} 
		else if (downloadDateStart == null && downloadDateEnd != null) {
			sb.append("AND trth.DOWNLOAD_DATE BETWEEN '1990-01-01 00:00:00' AND :retrieveDateEnd ");
			paramStack.push(new Object[] { "retrieveDateEnd",downloadDateEnd.toDate() });
		} 
		else if (downloadDateStart != null && downloadDateEnd == null) {
			sb.append("AND trth.DOWNLOAD_DATE BETWEEN :retrieveDateStart AND :currentDate ");
			paramStack.push(new Object[] { "retrieveDateStart",downloadDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		}
//		else if(downloadDateStart == null && downloadDateEnd == null){
//			sb.append("AND trth.DOWNLOAD_DATE BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}
		
		// ---SEND_DATE
		DateTime sendDateStart = null, sendDateEnd = null;
		if (params.length > 20&& !StringUtils.equals("%", (String) params[20][1])) {
			sendDateStart = formatter.parseDateTime((String) params[20][1]);
			sendDateStart = sendDateStart.withMillisOfDay(0);
		} 
		else if (params.length == 20 && !StringUtils.equals("%", (String) params[18][1])) {
			sendDateStart = formatter.parseDateTime((String) params[18][1]);
			sendDateStart = sendDateStart.withMillisOfDay(0);
		}
		if (params.length > 20&& !StringUtils.equals("%", (String) params[21][1])) {
			sendDateEnd = formatter.parseDateTime((String) params[21][1]);
			sendDateEnd = sendDateEnd.minusMillis(3);
		}  
		else if (params.length == 20 && !StringUtils.equals("%", (String) params[19][1])) {
			sendDateEnd = formatter.parseDateTime((String) params[19][1]);
			sendDateEnd = sendDateEnd.minusMillis(3);
		}
		if (sendDateStart != null && sendDateEnd != null) {
			sb.append("AND trth.SEND_DATE BETWEEN :sendDateStart AND :sendDateEnd ");
			paramStack.push(new Object[] { "sendDateStart",sendDateStart.toDate() });
			paramStack.push(new Object[] { "sendDateEnd",sendDateEnd.toDate() });
		} 
		else if (sendDateStart == null && sendDateEnd != null) {
			sb.append("AND trth.SEND_DATE BETWEEN '1990-01-01 00:00:00' AND :sendDateEnd ");
			paramStack.push(new Object[] { "sendDateEnd",sendDateEnd.toDate() });
		} 
		else if (sendDateStart != null && sendDateEnd == null) {
			sb.append("AND trth.SEND_DATE BETWEEN :sendDateStart AND :currentDate ");
			paramStack.push(new Object[] { "sendDateStart",sendDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
//		else if(sendDateStart == null && sendDateEnd == null){
//			sb.append("AND trth.SEND_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}
		
		// ---OTHERS
		if(!StringUtils.equals("%", (String) params[0][1])){
			sb.append("AND UPPER(trth.customer_name) LIKE UPPER('%' + :customerName + '%') ");
			paramStack.push(new Object[] { "customerName",(String) params[0][1] });
		}
		
		if (params[1][1] != null && !StringUtils.equals("%", (String) params[1][1])) {
			sbSelectInside.append("msb.branch_name as branch_name, ");
			sbSelectOutside.append("isnull(c.branch_name,'-') as branch_name, ");
			sbJoinInside.append("JOIN ms_branch msb with (nolock) ON (trth.uuid_branch = msb.uuid_branch) ");
			sb.append("AND msb.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[] { "uuidBranch", Long.valueOf((String) params[1][1]) });
		} else {
			sbSelectInside.append("trth.UUID_BRANCH as uuid_branch, ");
			sbSelectOutside.append("isnull(msb.BRANCH_NAME,'-') as branch_name, ");
			sbJoinOutside.append("JOIN ms_branch msb with (nolock) ON (c.uuid_branch = msb.uuid_branch) ");
		}
		
		if(params[2][1] != null &&  !StringUtils.equals("%", (String) params[2][1])){
			sb.append("AND UPPER(trth.customer_address) LIKE UPPER('%' + :customerAddress + '%') ");
			paramStack.push(new Object[] { "customerAddress", (String) params[2][1] });
		}
		
		if(params[3][1] != null && !StringUtils.equals("%", (String) params[3][1])){
			sb.append("AND UPPER(trth.appl_no) LIKE UPPER('%' + :applNo + '%') ");
			paramStack.push(new Object[] { "applNo", (String) params[3][1] });
		}
		
		if (params[3][1] != null && !StringUtils.equals("%", (String) params[4][1])){
			sbSelectInside.append("msu.full_name as full_name, ");
			sbSelectOutside.append("isnull(c.full_name,'-') full_name, ");
			sbJoinInside.append("JOIN (select keyValue, fullName as full_name from dbo.getUserByLogin(:uuidUser)) msu on msu.keyValue = trth.UUID_MS_USER ");
			sb.append("AND UPPER(msu.full_name) LIKE UPPER('%' + :fullName + '%') ");
			paramStack.push(new Object[] { "fullName", (String) params[4][1] });
		} else {
			sbSelectInside.append("trth.UUID_MS_USER as uuid_ms_user, ");
			sbSelectOutside.append("isnull(msu.full_name,'-') full_name, ");
			sbJoinOutside.append("JOIN (select keyValue, fullName as full_name from dbo.getUserByLogin(:uuidUser)) msu on msu.keyValue = c.UUID_MS_USER ");
		}
		
		if(params[12][1] != null && !StringUtils.equals("%", (String) params[12][1])){
			sb.append("AND trth.uuid_status_task = :statusId ");
			paramStack.push(new Object[] { "statusId", Long.valueOf((String) params[12][1]) });
		}
		
//		paramStack.push(new Object[] { "subsystemId", (long) params[13][1] });
		
		if(params[14][1] != null && !StringUtils.equals("%", (String) params[14][1])){
			sb.append("AND trth.uuid_priority = :priorityId ");
			paramStack.push(new Object[] { "priorityId", Long.valueOf((String) params[14][1]) });
		}
		
		if(params[15][1] != null && !StringUtils.equals("%", (String) params[15][1])){
			sb.append("AND trth.uuid_form = :formId ");
			paramStack.push(new Object[] { "formId", Long.valueOf((String) params[15][1]) });
		}
		
		if(params.length > 22){
			paramStack.push(new Object[] { "uuidUser", (long) params[22][1] });
		}
		else {
			paramStack.push(new Object[] { "uuidUser", (long) params[17][1] });
		}
		
		paramStack.push(new Object[] { "branchIdLogin", (long) params[11][1] });
		
		sbCaseWhen.append("CASE WHEN EXISTS ( ")
		.append("SELECT 1 FROM TR_TASK_D ttd with(nolock) WHERE ttd.UUID_TASK_H = trth.UUID_TASK_H ")
		.append("AND ttd.QUESTION_TEXT = 'Persetujuan Pemohon') ")
		.append("THEN 1 ELSE 0 END AS DOWNLOADABLE ");
		
		mapResult.put("selectInside", sbSelectInside.toString());
		mapResult.put("selectOutside", sbSelectOutside.toString());
		mapResult.put("sb", sb.toString());
		mapResult.put("joinOutside", sbJoinOutside.toString());
		mapResult.put("joinInside", sbJoinInside.toString());
		mapResult.put("caseWhen", sbCaseWhen.toString());
		return mapResult;
	}
	
	/*
	 * 1 CUSTOMER_NAME 2 appl_no 3 assign_dt 4
	 * download_dt 5 submit_dt 6 full_name 7
	 * status_task_desc 8 send_dt
	 */
	private StringBuilder sqlPagingOrderBuilder(String orders, AuditContext callerId) {
		if (StringUtils.isEmpty(orders) || "0".equals(orders)) {
			orders = "3A"; // set default order by assignDate ASC
		}

		String[] orderCols = { "c.CUSTOMER_NAME", "c.appl_no",
				"c.assign_dt", "c.download_dt",
				"c.submit_dt", "c.full_name", "c.status_task_desc",
				"c.send_dt"};
		StringBuilder queryOrder = new StringBuilder(" ORDER BY ");

		int colIdx = NumberUtils.toInt(StringUtils.substring(orders, 0,
				StringUtils.length(orders) - 1));
		colIdx = (colIdx > 0) ? colIdx - 1 : colIdx;
		queryOrder.append(orderCols[colIdx]);
		if (StringUtils.endsWith(orders, "D")) {
			queryOrder.append(" DESC");
		}
		else {
			queryOrder.append(" ASC");
		}

		return queryOrder;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List listInquiryTaskSurveybyBranchNativeString(Object params, AuditContext callerId) {

		List result = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		Map<String, String> paramsQuery = this.sqlPagingBuilderByBranch((Object[][]) params, paramsStack, callerId);
		String selectInside = paramsQuery.get("selectInside");
		String selectOutside = paramsQuery.get("selectOutside");
		String joinInside = paramsQuery.get("joinInside");
		String joinOutside = paramsQuery.get("joinOutside");
		String where = paramsQuery.get("sb");
		String caseWhen = paramsQuery.get("caseWhen");
		StringBuilder ordersQueryString = this.sqlPagingOrderRegionBuilder((String) ((Object[][]) params)[19][1], callerId);

		StringBuilder queryBuilder = new StringBuilder()
			.append("WITH cpx AS (SELECT  MOBILE_TASK_CODE, APP_NO, STATUS, STATS_START_DT, PREP_START_DT, PREP_END_DT, SEQUENCE_PROCESS ")
			.append("FROM STAGING_CREDIT_PROCESS_X_RANK with (nolock)) ")
			.append("SELECT * from ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER ( ")
			.append("ORDER BY rownum) AS recnum FROM ( ")
			.append("select c.uuid_task_h, ")
			.append("isnull(c.CUSTOMER_NAME,'-') CUSTOMER_NAME, ")
			.append("isnull(c.appl_no,'-') appl_no, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.assign_date, 113), 17),'-') as assign_date, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.download_date, 113),17),'-') as download_date, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.submit_date, 113),17),'-') as submit_date, ")
			.append(selectOutside)
			.append("isnull(mssta.status_task_desc,'-') status_task_desc, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.send_date, 113),17),'-') as send_date, c.flag, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.promise_date, 113),17),'-') as promise_date, ")
			.append("c.task_id, isnull(LEFT(CONVERT(VARCHAR, c.start_dt, 113),17),'-') as start_dt, ")
			.append("flagOrder, msf.FORM_NAME,  ")
			.append("isnull(STATUS, '-') as statusWise, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, STATS_START_DT, 113),17),'-') as status_date, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, PREP_START_DT, 113),17),'-') as prep_start_date, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, PREP_END_DT, 113),17),'-') as prep_send_date, ")
			.append("ROW_NUMBER() OVER ( ").append(ordersQueryString).append(") AS rownum, ")
			.append("c.DOWNLOADABLE ")
			.append("from ( ")
			.append("SELECT trth.uuid_task_h, ")
			.append("trth.customer_name as CUSTOMER_NAME, ")
			.append("trth.appl_no as appl_no, ")
			.append("trth.assign_date as assign_date, ")
			.append("trth.download_date as download_date, ")
			.append("trth.submit_date as submit_date, ")
			.append(selectInside)
			.append("trth.uuid_status_task as uuid_status_task, ")
			.append("trth.send_date as send_date, ")
			.append("'1' as flag, '0' as flagOrder, ")
			.append("trth.assign_date as assign_dt, ")
			.append("trth.download_date as download_dt, ")
			.append("trth.submit_date as submit_dt, ")
			.append("trth.send_date as send_dt, ")
			.append("trth.PROMISE_DATE as promise_date, ")
			.append("trth.task_id, trth.start_dtm as start_dt, ")
			.append("trth.uuid_form AS uuid_form, ")
			.append(caseWhen)
			.append("FROM tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE 1=1 ")
			.append(where)
			.append("union ALL ")
			.append("SELECT trth.uuid_task_h, ")
			.append("trth.customer_name as CUSTOMER_NAME, ")
			.append("trth.appl_no as appl_no, ")
			.append("trth.assign_date as assign_date, ")
			.append("trth.download_date as download_date, ")
			.append("trth.submit_date as submit_date, ")
			.append(selectInside)
			.append("trth.uuid_status_task as uuid_status_task, ")
			.append("trth.send_date as send_date, ")
			.append("'2' as flag, '0' as flagOrder, ")
			.append("trth.assign_date as assign_dt, ")
			.append("trth.download_date as download_dt, ")
			.append("trth.submit_date as submit_dt, ")
			.append("trth.send_date as send_dt, ")
			.append("trth.PROMISE_DATE as promise_date, ")
			.append("trth.task_id, trth.start_dtm as start_dt, ")
			.append("trth.uuid_form AS uuid_form, ")
			.append(caseWhen)
			.append("FROM final_tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE 1=1 ")
			.append(where)
			.append(") c ")
			.append(joinOutside)
			.append("JOIN ms_statustask mssta with (nolock) ON (c.uuid_status_task = mssta.uuid_status_task) ")
			.append("JOIN MS_FORM msf with (nolock) ON msf.uuid_form = c.uuid_form ")
			.append("left outer JOIN cpx on (c.APPL_NO=cpx.MOBILE_TASK_CODE ")
			.append("or c.APPL_NO = cpx.APP_NO) ")
			.append(") a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		
//		paramsStack.push(new Object[]{"odr", (String) ((Object[][]) params)[19][1]});	
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[17][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[18][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		if("1".equals(this.link_encrypt)){
			List newRes = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				String[] temp = {map.get("d0").toString()};
				List res = CipherTool.encryptData(temp);
				map.put("d0", res.get(0).toString());
				newRes.add(map);
			}
			return newRes;	
		}else{
			return result;
		}
	}
	
	/*0	customerName, 1	branchId, 2 customerAddress,
	3 applNo, 4 fullName, 5 assignDateStart, 6 assignDateEnd, 
	7 submitDateStart, 8 submitDateEnd, 9 retrieveDateStart,
	10 retrieveDateEnd, 11 branchIdLogin, 12 statusId,
	13 subsystemId, 14 priorityId, 15 formId, 16 currentDate,
	17 start, 18 end, 19 odr, 20 sendDateStart, 21 sendDateEnd,
	22 sendDateStart, 23 sendDateEnd
	*/
	private Map<String, String> sqlPagingBuilderByBranch(Object[][] params,Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null){
			return new HashMap<String, String>();
		}

		Map<String, String> mapResult = new HashMap<String, String>();
		StringBuilder sb = new StringBuilder();
		StringBuilder sbJoinInside = new StringBuilder();
		StringBuilder sbJoinOutside = new StringBuilder();
		StringBuilder sbSelectInside = new StringBuilder();
		StringBuilder sbSelectOutside = new StringBuilder();
		StringBuilder sbCaseWhen = new StringBuilder();
		
		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
		DateTime currentDate = formatter.parseDateTime((String) params[16][1]);
		currentDate = currentDate.minusMillis(3);
		// ---ASSIGN_DATE
		DateTime assignDateStart = null, assignDateEnd = null;
		if (params.length > 5&& !StringUtils.equals("%", (String) params[5][1])) {
			assignDateStart = formatter.parseDateTime((String) params[5][1]);
			assignDateStart = assignDateStart.withMillisOfDay(0);
		}
		if (params.length > 5&& !StringUtils.equals("%", (String) params[6][1])) {
			assignDateEnd = formatter.parseDateTime((String) params[6][1]);
			assignDateEnd = assignDateEnd.minusMillis(3);
		}
		
		if (assignDateStart != null && assignDateEnd != null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart == null && assignDateEnd != null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN '1990-01-01 00:00:00' AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart != null && assignDateEnd == null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN :assignDateStart AND :currentDate ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
//		else if(assignDateStart == null && assignDateEnd == null){
//			sb.append("AND trth.ASSIGN_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}

		// ---SUBMIT_DATE
		DateTime submitDateStart = null, submitDateEnd = null;
		if (params.length > 7&& !StringUtils.equals("%", (String) params[7][1])) {
			submitDateStart = formatter.parseDateTime((String) params[7][1]);
			submitDateStart = submitDateStart.withMillisOfDay(0);
		}
		if (params.length > 7&& !StringUtils.equals("%", (String) params[8][1])) {
			submitDateEnd = formatter.parseDateTime((String) params[8][1]);
			submitDateEnd = submitDateEnd.plusDays(1).minusMillis(3);
		}
		if (submitDateStart != null && submitDateEnd != null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN :submitDateStart AND :submitDateEnd ");
			paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
			paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
		} 
		else if (submitDateStart == null && submitDateEnd != null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN '1990-01-01 00:00:00' AND :submitDateEnd ");
			paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
		} 
		else if (submitDateStart != null && submitDateEnd == null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN :submitDateStart AND :currentDate ");
			paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		}
//		else if(submitDateStart == null && submitDateEnd == null){
//			sb.append("AND trth.SUBMIT_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}
		
		// ---PROMISE DATE
		DateTime promiseDateStart = null, promiseDateEnd = null;
		if (params.length > 22
				&& !StringUtils.equals("%", (String) params[23][1])) {
			promiseDateStart = formatter.parseDateTime((String) params[23][1]);
			promiseDateStart = promiseDateStart.withMillisOfDay(0);
		}
		if (params.length > 23
				&& !StringUtils.equals("%", (String) params[24][1])) {
			promiseDateEnd = formatter.parseDateTime((String) params[24][1]);
			promiseDateEnd = promiseDateEnd.minusMillis(3);
		}
		if (promiseDateStart != null && promiseDateEnd != null) {
			sb.append("AND trth.PROMISE_DATE BETWEEN :promiseDateStart AND :promiseDateEnd "
					+ "AND trth.PROMISE_DATE is not null ");
			paramStack.push(new Object[] { "promiseDateStart",
					promiseDateStart.toDate() });
			paramStack.push(new Object[] { "promiseDateEnd",
					promiseDateEnd.toDate() });
		} 
//		else if (promiseDateStart == null && promiseDateEnd != null) {
//			sb.append("AND trth.PROMISE_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :promiseDateEnd ");
//			paramStack.push(new Object[] { "promiseDateEnd",
//					promiseDateEnd.toDate() });
//		} else if (promiseDateStart != null && promiseDateEnd == null) {
//			sb.append("AND trth.PROMISE_DATE, '1990-01-01 00:00:00') BETWEEN :promiseDateStart AND :currentDate ");
//			paramStack.push(new Object[] { "promiseDateStart",
//					promiseDateStart.toDate() });
//			paramStack
//					.push(new Object[] { "currentDate", currentDate.toDate() });
//		} else if (promiseDateStart == null && promiseDateEnd == null) {
//			sb.append("AND trth.PROMISE_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack
//					.push(new Object[] { "currentDate", currentDate.toDate() });
//		}
		
		// ---DOWNLOAD_DATE
		DateTime downloadDateStart = null, downloadDateEnd = null;
		if (params.length > 9&& !StringUtils.equals("%", (String) params[9][1])) {
			downloadDateStart = formatter.parseDateTime((String) params[9][1]);
			downloadDateStart = downloadDateStart.withMillisOfDay(0);
		}
		if (params.length > 9&& !StringUtils.equals("%", (String) params[10][1])) {
			downloadDateEnd = formatter.parseDateTime((String) params[10][1]);
			downloadDateEnd = downloadDateEnd.plusDays(1).minusMillis(3);
		}
		if (downloadDateStart != null && downloadDateEnd != null) {
			sb.append("AND trth.DOWNLOAD_DATE BETWEEN :retrieveDateStart AND :retrieveDateEnd ");
			paramStack.push(new Object[] { "retrieveDateStart",downloadDateStart.toDate() });
			paramStack.push(new Object[] { "retrieveDateEnd",downloadDateEnd.toDate() });
		} 
		else if (downloadDateStart == null && downloadDateEnd != null) {
			sb.append("AND trth.DOWNLOAD_DATE BETWEEN '1990-01-01 00:00:00' AND :retrieveDateEnd ");
			paramStack.push(new Object[] { "retrieveDateEnd",downloadDateEnd.toDate() });
		} 
		else if (downloadDateStart != null && downloadDateEnd == null) {
			sb.append("AND trth.DOWNLOAD_DATE BETWEEN :retrieveDateStart AND :currentDate ");
			paramStack.push(new Object[] { "retrieveDateStart",downloadDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
//		else if(downloadDateStart == null && downloadDateEnd == null){
//			sb.append("AND trth.DOWNLOAD_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}
		
		// ---SEND_DATE
		DateTime sendDateStart = null, sendDateEnd = null;
		if (params.length > 20&& !StringUtils.equals("%", (String) params[20][1])) {
			sendDateStart = formatter.parseDateTime((String) params[20][1]);
			sendDateStart = sendDateStart.withMillisOfDay(0);
		} 
		else if (params.length <= 20 && !StringUtils.equals("%", (String) params[17][1])) {
			sendDateStart = formatter.parseDateTime((String) params[17][1]);
			sendDateStart = sendDateStart.withMillisOfDay(0);
		}
		if (params.length > 20&& !StringUtils.equals("%", (String) params[21][1])) {
			sendDateEnd = formatter.parseDateTime((String) params[21][1]);
			sendDateEnd = sendDateEnd.plusDays(1).minusMillis(3);
		}  
		else if (params.length <= 20 && !StringUtils.equals("%", (String) params[18][1])) {
			sendDateEnd = formatter.parseDateTime((String) params[18][1]);
			sendDateEnd = sendDateEnd.withMillisOfDay(0);
		}
		if (sendDateStart != null && sendDateEnd != null) {
			sb.append("AND trth.SEND_DATE BETWEEN :sendDateStart AND :sendDateEnd ");
			paramStack.push(new Object[] { "sendDateStart",sendDateStart.toDate() });
			paramStack.push(new Object[] { "sendDateEnd",sendDateEnd.toDate() });
		} 
		else if (sendDateStart == null && sendDateEnd != null) {
			sb.append("AND trth.SEND_DATE BETWEEN '1990-01-01 00:00:00' AND :sendDateEnd ");
			paramStack.push(new Object[] { "sendDateEnd",sendDateEnd.toDate() });
		} 
		else if (sendDateStart != null && sendDateEnd == null) {
			sb.append("AND trth.SEND_DATE BETWEEN :sendDateStart AND :currentDate ");
			paramStack.push(new Object[] { "sendDateStart",sendDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
//		else if(sendDateStart == null && sendDateEnd == null){
//			sb.append("AND trth.SEND_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}

		// ---OTHERS
		if(!StringUtils.equals("%", (String) params[0][1])){
			sb.append(" AND UPPER(trth.customer_name) LIKE UPPER('%' + :customerName + '%') ");
			paramStack.push(new Object[] { "customerName",(String) params[0][1] });
		}
		if(!StringUtils.equals("%", (String) params[1][1])){
			sbSelectInside.append("msb.branch_name as branch_name, ");
			sbSelectOutside.append("isnull(c.branch_name,'-') as branch_name, ");
			sbJoinInside.append("JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchIdLogin)) msb on trth.UUID_BRANCH = msb.UUID_BRANCH ");
			sb.append("AND msb.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[] { "uuidBranch", Long.valueOf((String) params[1][1]) });
		} else {
			sbSelectInside.append("trth.UUID_BRANCH as uuid_branch, ");
			sbSelectOutside.append("isnull(msb.BRANCH_NAME,'-') as branch_name, ");
			sbJoinOutside.append("JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchIdLogin)) msb on c.UUID_BRANCH = msb.UUID_BRANCH ");
		}
		
		if(!StringUtils.equals("%", (String) params[2][1])){
			sb.append("AND UPPER(trth.customer_address) LIKE UPPER('%' + :customerAddress + '%') ");
			paramStack.push(new Object[] { "customerAddress", (String) params[2][1] });
		}
		
		if(!StringUtils.equals("%", (String) params[3][1])){
			sb.append("AND UPPER(trth.appl_no) LIKE UPPER('%' + :applNo + '%') ");
			paramStack.push(new Object[] { "applNo", (String) params[3][1] });
		}
		
		if (!StringUtils.equals("%", (String) params[4][1])){
			sbSelectInside.append("msu.full_name as full_name, ");
			sbSelectOutside.append("isnull(c.full_name,'-') full_name, ");
			sbJoinInside.append("left outer JOIN am_msuser msu with (nolock) ON (trth.uuid_ms_user = msu.uuid_ms_user) ");
			sb.append("AND UPPER(msu.full_name) LIKE UPPER('%' + :fullName + '%') ");
			paramStack.push(new Object[] { "fullName", (String) params[4][1] });
		} else {
			sbSelectInside.append("trth.UUID_MS_USER as uuid_ms_user, ");
			sbSelectOutside.append("isnull(msu.full_name,'-') full_name, ");
			sbJoinOutside.append("left outer JOIN am_msuser msu with (nolock) ON (c.uuid_ms_user = msu.uuid_ms_user) ");
		}
		
		if(!StringUtils.equals("%", (String) params[12][1])){
			sb.append("AND trth.uuid_status_task = :statusId ");
			paramStack.push(new Object[] { "statusId", Long.valueOf((String) params[12][1]) });
		}
		
//		sb.append("AND mssta.uuid_ms_subsystem = :subsystemId ");
//		paramStack.push(new Object[] { "subsystemId", (long) params[13][1] });
		
		if(!StringUtils.equals("%", (String) params[14][1])){
			sb.append("AND trth.uuid_priority = :priorityId ");
			paramStack.push(new Object[] { "priorityId", Long.valueOf((String) params[14][1]) });
		}
		
		if(!StringUtils.equals("%", (String) params[15][1])){
			sb.append("AND trth.uuid_form = :formId ");
			paramStack.push(new Object[] { "formId", Long.valueOf((String) params[15][1]) });
		}		
		paramStack.push(new Object[] { "branchIdLogin", (long) params[11][1] });
		
		sbCaseWhen.append("CASE WHEN EXISTS ( ")
		.append("SELECT 1 FROM TR_TASK_D ttd WITH(NOLOCK) WHERE ttd.UUID_TASK_H = trth.UUID_TASK_H ")
		.append("AND ttd.QUESTION_TEXT = 'Persetujuan Pemohon') ")
		.append("THEN 1 ELSE 0 END AS DOWNLOADABLE ");
		
		mapResult.put("selectInside", sbSelectInside.toString());
		mapResult.put("selectOutside", sbSelectOutside.toString());
		mapResult.put("sb", sb.toString());
		mapResult.put("joinOutside", sbJoinOutside.toString());
		mapResult.put("joinInside", sbJoinInside.toString());
		mapResult.put("caseWhen", sbCaseWhen.toString());
		return mapResult;
	}
	
	@Override
	public Integer countListInquiryTaskSurveyBranchNativeString(Object params, AuditContext callerId){
		Integer result = 0;
		Stack<Object[]> paramsStack = new Stack<>();
		Map<String, String> paramsQuery = this.sqlPagingBuilderByBranch((Object[][]) params, paramsStack, callerId);
		String joinInside = paramsQuery.get("joinInside");
		String joinOutside = paramsQuery.get("joinOutside");
		String where = paramsQuery.get("sb");
		String selectInside = paramsQuery.get("selectInside");
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT COUNT(1) from (SELECT ")
			.append(selectInside)
			.append(" trth.uuid_task_h ")
			.append("FROM tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE 1=1 ")
			.append(where)
			.append("union ALL SELECT ")
			.append(selectInside)
			.append(" trth.uuid_task_h ")
			.append("FROM final_tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE 1=1 ")
			.append(where)
			.append(") c ")
			.append(joinOutside);
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}

		result = (Integer)this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}

	@Override
	public List getDropDownList(Object params, Object order, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.branchList", params, order);		
		return result;
	}

	@Override
	public List getInquiryTaskSurveyDetailAnswerHistory(Object params,
			Object order, String codeProcess, String uuidTaskRejectedHistory, AuditContext callerId) throws UnsupportedEncodingException {
		List <TrTaskBean> result = new ArrayList<>();
		
		Object[] bean = (Object[])this.getManagerDAO().selectOneNative("task.inquirytasksurvey.getTaskH", params);
		String[][] params1 = { { "uuidTask", bean[0].toString() }};
		List list = null;
		if (GlobalVal.CODE_PROCESS_REJECTED.equals(codeProcess)) {
			String[][] param =  { { "uuidTask", bean[0].toString() },{"uuidTaskRejectedHistory",uuidTaskRejectedHistory} };
			list = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.answerHistoryRejectedQset", param, order);
		}
		else if (GlobalVal.CODE_PROCESS_VERIFIED.equals(codeProcess)) {
			list = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.answerHistoryVerifQSet", params1, order);
		}
		else {
			list = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.answerHistoryQSet", params1, order);
		}
		
		String oldLabel = "";
		String questionText = "";
		String refId = "";
		
		for (int i = 0; i < list.size(); i++) {
			TrTaskBean taskCollection = new TrTaskBean();
			Map map = (Map) list.get(i);
			
			if (GlobalVal.CODE_PROCESS_REJECTED.equals(codeProcess)) {
				refId = (null!=map.get("d18") ? map.get("d18").toString() : "");
			} else {
				refId = (null!=map.get("d27") ? map.get("d27").toString() : "");
			}
			
			boolean flagverifmultiple = false;
			if (null != map.get("d5") && LIST_REF_ID_ALLOWMAPS.contains(refId)) {
			    taskCollection.setLat(map.get("d5").toString());
			}
			if (null != map.get("d6") && LIST_REF_ID_ALLOWMAPS.contains(refId)){ 
			    taskCollection.setLng(map.get("d6").toString()); 
			}
			if (null != map.get("d21")){
			    taskCollection.setAccuracy(Integer.parseInt(map.get("d21").toString()));
			}
			
			if (GlobalVal.CODE_PROCESS_REJECTED.equals(codeProcess)) {				
				if(null != map.get("d16") && LIST_REF_ID_ALLOWMAPS.contains(refId)){
					taskCollection.setAccuracy(Integer.parseInt(map.get("d16").toString()));
				}
				
				if (null != map.get("d4") && StringUtils.isNotBlank(map.get("d7").toString())) {
					taskCollection.setLob(map.get("d7").toString());
				}
				taskCollection.setQuestionText(map.get("d0").toString());
				taskCollection.setTextAnswer(map.get("d1").toString());
				taskCollection.setOptionText(map.get("d2").toString());
				taskCollection.setHasImage(map.get("d3").toString());
				if ("1".equals(taskCollection.getHasImage())) {
					taskCollection.setUuidTaskD(Long.valueOf(map.get("d7").toString()));
				}
				taskCollection.setIsAsset(map.get("d9").toString());
				taskCollection.setLatBranch(map.get("d10") == null ? null : map.get("d10").toString());
				taskCollection.setLngBranch(map.get("d11") == null ? null : map.get("d11").toString());
//				if ("1".equals(taskCollection.getIsAsset())){
					double lat = Double.parseDouble(taskCollection.getLat()==null?"0":taskCollection.getLat());
					double lng = Double.parseDouble(taskCollection.getLng()==null?"0":taskCollection.getLng());
					if(null == taskCollection.getLat()
							&& null == taskCollection.getLng()
							&& null != map.get("d15")
							&& null != map.get("d14")
							&& null != map.get("d13")
							&& null != map.get("d12")) {
						List<LocationBean> listLocations = new ArrayList<LocationBean>();
						LocationBean locationBean = new LocationBean();
						locationBean.setCellid(Integer.parseInt(map.get("d15").toString()));
						locationBean.setLac(Integer.parseInt(map.get("d14").toString()));
						locationBean.setMcc(Integer.parseInt(map.get("d12").toString()));
						locationBean.setMnc(Integer.parseInt(map.get("d13").toString()));
						listLocations.add(locationBean);
						this.geocoder.geocodeCellId(listLocations, callerId);
						
						lat = locationBean.getCoordinate()==null?0:locationBean.getCoordinate().getLatitude();
						lng = locationBean.getCoordinate()==null?0:locationBean.getCoordinate().getLongitude();
					}
						
					if (lat != 0 && lng != 0) {
						double distance = DistanceUtils.getDistanceInKm(lat, lng, 
								Double.parseDouble(taskCollection.getLatBranch()==null?"0":taskCollection.getLatBranch()), 
								Double.parseDouble(taskCollection.getLngBranch()==null?"0":taskCollection.getLngBranch()));
						NumberFormat nf = new DecimalFormat("#.##");
						String dtc = nf.format(distance);
						
						taskCollection.setDistance(dtc);
					}
//				}
				
				if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("d8").toString())){
					NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
					if (StringUtils.isNotBlank((String) map.get("d1"))) {
						taskCollection.setTextAnswer("Rp "+formatKurs.format(NumberUtils.toDouble(map.get("d1").toString())));
					}
				}
				
				if (GlobalVal.ANSWER_TYPE_DATE.equals(map.get("d8").toString())){
					if (StringUtils.isNotBlank(map.get("d1").toString())) {
						try {
							Date date = DateUtils.parseDate(map.get("d1").toString(), "dd/MM/yyyy");
							taskCollection.setTextAnswer(DateFormatUtils.format(date, "dd/MM/yyyy"));
						} 
						catch (ParseException e) {
							try {
								Date date = DateUtils.parseDate(map.get("d1").toString(), "ddMMyyyyHHmmss");
								taskCollection.setTextAnswer(DateFormatUtils.format(date, "dd/MM/yyyy"));
							} 
							catch (ParseException ex) {
								throw new ChangeException(
										this.messageSource.getMessage("businesslogic.inquirytasksurvey.errorparsedate", 
												null, this.retrieveLocaleAudit(callerId))
										,map.get("d1").toString());
							}
						}
					}
				}
				if (GlobalVal.ANSWER_TYPE_TEXT_MULTILINE_SEPERATE.equals(map.get("d8").toString())){
					if (StringUtils.isNotBlank((String) map.get("d1"))) {
						taskCollection.setTextAnswer(map.get("d1").toString().replaceAll("\n", "<br>"));
					}
				}
				
				if ((GlobalVal.CODE_SVY_REASON_PMHN.equalsIgnoreCase(refId) || GlobalVal.CODE_SVY_REASON_PSGN.equalsIgnoreCase(refId))
						|| GlobalVal.CODE_SVY_REASON_GRTR.equalsIgnoreCase(refId)) {
					if (StringUtils.isNotBlank((String) map.get("d1"))) {
						taskCollection.setTextAnswer(map.get("d1").toString().replaceAll("\n", "<br><br>"));
					}
				}
				
				MsQuestion msQuestion = new MsQuestion();
				MsAnswertype msAnswertype = new MsAnswertype();
				msAnswertype.setCodeAnswerType(map.get("d8").toString());
				msQuestion.setMsAnswertype(msAnswertype);
				
				taskCollection.setMsQuestion(msQuestion);
				
				taskCollection.setFlagSource(map.get("d17").toString());
				taskCollection.setCodeProcess(codeProcess);
				
				if (null != map.get("d19")) {
					taskCollection.setAssetTagName(map.get("d19").toString());
				}
			} 
			else {
                if (StringUtils.isNotBlank(map.get("d8").toString()) && !"0".equalsIgnoreCase(map.get("d8").toString())) {
                    Object[][] accDetailLobParams = { {"uuidTaskD", Long.valueOf(map.get("d8").toString())} };
                    Integer accuracy = (Integer) this.getManagerDAO().selectOneNative("common.retrieveAccuraryDetailLob", accDetailLobParams);
                    taskCollection.setAccuracy(accuracy);
                    taskCollection.setLob(map.get("d8").toString());
                    // while accuracy is queried from DB, image itself is loaded using lazyload mechanism 
                }

				taskCollection.setQuestionText(map.get("d1").toString());
				questionText = taskCollection.getQuestionText();

				taskCollection.setTextAnswer(map.get("d2").toString());
				taskCollection.setOptionText(map.get("d3").toString());
				
				taskCollection.setFinOptionText(map.get("d12").toString());
				taskCollection.setFinOptionText(map.get("d13").toString());
				
				if ( GlobalVal.CODE_PROCESS_VERIFIED.equals(codeProcess)) {
					if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(map.get("d9").toString())
							|| GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(map.get("d9").toString())
							|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(map.get("d9").toString())){
						if ((!oldLabel.equals(questionText) && StringUtils.isBlank(taskCollection.getFinOptionText()))
								|| StringUtils.isNotBlank(taskCollection.getFinOptionText())
								|| (StringUtils.isBlank(taskCollection.getOptionText())
										&& StringUtils.isBlank(taskCollection.getFinOptionText())))  {
							flagverifmultiple = true;
						} 
					}
					taskCollection.setTextAnswer(map.get("d12").toString());
					taskCollection.setOptionText(map.get("d13").toString());
				}
				taskCollection.setHasImage(map.get("d4").toString());
				if ("1".equals(taskCollection.getHasImage())) {
					BigInteger uuidTaskD = (BigInteger) map.get("d26");
					taskCollection.setUuidTaskD(Long.valueOf(uuidTaskD.toString()));
				}
				taskCollection.setIntTextAnswer(map.get("d10").toString());
				taskCollection.setIntOptionText(map.get("d11").toString());
				taskCollection.setIsAsset(map.get("d14").toString());
				taskCollection.setLatBranch(map.get("d15") == null ? null : map.get("d15").toString());
				taskCollection.setLngBranch(map.get("d16") == null ? null : map.get("d16").toString()); 
//				if ("1".equals(taskCollection.getIsAsset())){
					double lat = Double.parseDouble(taskCollection.getLat()==null?"0":taskCollection.getLat());
					double lng = Double.parseDouble(taskCollection.getLng()==null?"0":taskCollection.getLng());
					if(null == taskCollection.getLat()
							&& null == taskCollection.getLng()
							&& null != map.get("d20")
							&& null != map.get("d19")
							&& null != map.get("d18")
							&& null != map.get("d17")) {
						List<LocationBean> listLocations = new ArrayList<LocationBean>();
						LocationBean locationBean = new LocationBean();
						locationBean.setCellid(Integer.parseInt(map.get("d20")==null?"0":map.get("d20").toString()));
						locationBean.setLac(Integer.parseInt(map.get("d19")==null?"0":map.get("d19").toString()));
						locationBean.setMcc(Integer.parseInt(map.get("d17")==null?"0":map.get("d17").toString()));
						locationBean.setMnc(Integer.parseInt(map.get("d18")==null?"0":map.get("d18").toString()));
						listLocations.add(locationBean);
						this.geocoder.geocodeCellId(listLocations, callerId);
						
						lat = locationBean.getCoordinate()==null?0:locationBean.getCoordinate().getLatitude();
						lng = locationBean.getCoordinate()==null?0:locationBean.getCoordinate().getLongitude();
					}
					
					if (lat != 0 && lng != 0) {
						double distance = DistanceUtils.getDistanceInKm(lat, lng, 
								Double.parseDouble(taskCollection.getLatBranch()==null?"0":taskCollection.getLatBranch()), 
								Double.parseDouble(taskCollection.getLngBranch()==null?"0":taskCollection.getLngBranch()));
						NumberFormat nf = new DecimalFormat("#.##");
						String dtc = nf.format(distance);
						
						taskCollection.setDistance(dtc);
					}
					
//				}
				
				if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("d9").toString())){
					NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
					if (StringUtils.isNotBlank((String) map.get("d2"))) { 
						taskCollection.setTextAnswer("Rp "+ formatKurs.format(NumberUtils.toDouble(map.get("d2").toString())));
					}
					if (StringUtils.isNotBlank((String) map.get("d10"))) {
						taskCollection.setIntTextAnswer("Rp "+ formatKurs.format(NumberUtils.toDouble(map.get("d10").toString())));
					}
				}
				
				if (GlobalVal.ANSWER_TYPE_TEXT_MULTILINE_SEPERATE.equals(map.get("d9").toString())){
					if (StringUtils.isNotBlank((String) map.get("d2"))) {
						taskCollection.setTextAnswer(map.get("d2").toString().replaceAll("\n", "<br>"));
					}
					if (StringUtils.isNotBlank((String) map.get("d10"))) {
						taskCollection.setIntTextAnswer(map.get("d10").toString().replaceAll("\n", "<br>"));
					}
				}
				
				if ((GlobalVal.CODE_SVY_REASON_PMHN.equalsIgnoreCase(refId) || GlobalVal.CODE_SVY_REASON_PSGN.equalsIgnoreCase(refId))
						|| GlobalVal.CODE_SVY_REASON_GRTR.equalsIgnoreCase(refId)) {
					if (StringUtils.isNotBlank((String) map.get("d2"))) {
						taskCollection.setTextAnswer(map.get("d2").toString().replaceAll("\n", "<br><br>"));
					}
					if (StringUtils.isNotBlank((String) map.get("d10"))) {
						taskCollection.setIntTextAnswer(map.get("d10").toString().replaceAll("\n", "<br><br>"));
					}
				}
				
				MsQuestion msQuestion = new MsQuestion();
				MsAnswertype msAnswertype = new MsAnswertype();
				msAnswertype.setCodeAnswerType(map.get("d9").toString());
				msQuestion.setMsAnswertype(msAnswertype);
				
				taskCollection.setMsQuestion(msQuestion);
				
				taskCollection.setFlagSource(map.get("d25").toString());
				taskCollection.setCodeProcess(codeProcess);
				
				if (null != map.get("d28")) {
					taskCollection.setAssetTagName(map.get("d28").toString());
				}
			}
			
			if (result.contains(taskCollection)){
			    continue;
			}
			
			if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(map.get("d9").toString())
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(map.get("d9").toString())
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(map.get("d9").toString())){
				if (GlobalVal.CODE_PROCESS_VERIFIED.equals(codeProcess)) {
						if (flagverifmultiple)  {
							result.add(taskCollection);
						}	
				}
				else if (GlobalVal.CODE_PROCESS_SUBMITTED.equals(codeProcess) || null == codeProcess) {
					if ((!oldLabel.equals(questionText) 
							&& (StringUtils.isBlank(taskCollection.getOptionText()) || StringUtils.isNotBlank(taskCollection.getIntOptionText())))
							|| (StringUtils.isNotBlank(taskCollection.getOptionText()) || StringUtils.isNotBlank(taskCollection.getIntOptionText()))
							|| (StringUtils.isBlank(taskCollection.getOptionText())
							&& StringUtils.isBlank(taskCollection.getFinOptionText()))) {
						result.add(taskCollection);
					}
				}
				else {
					result.add(taskCollection);
				}
			}
			else {
				result.add(taskCollection);
			}
			oldLabel = taskCollection.getQuestionText();
		}
		
		TrTaskBean taskBean = this.getDetailApproveReason(((BigInteger) bean[0]).longValue());
		if (null != taskBean) {
			result.add(taskBean);
		}
		
		if("1".equals(this.link_encrypt)){
			for(int i=0; i<result.size(); i++){
				if("1".equals(result.get(i).getHasImage())){
					String[] temp = {result.get(i).getLob().toString()};
					result.get(i).setLob(CipherTool.encryptData(temp).get(0).toString());
				}
			}
		}
		return result;
	}
	
	private TrTaskBean getDetailApproveReason(long uuidTaskH) {
		TrTaskBean taskCollection = null;
		
		MsQuestion msq = this.getManagerDAO().selectOne(MsQuestion.class,
				new Object [][] { {Restrictions.eq("refId", GlobalVal.CODE_SVY_APPROVAL_REASON)} });
		this.getManagerDAO().fetch(msq.getMsAnswertype());
		TrTaskD trTaskD = this.getManagerDAO().selectOne(TrTaskD.class, new Object [][] {
				{Restrictions.eq("msQuestion.uuidQuestion", msq.getUuidQuestion())}, {Restrictions.eq("trTaskH.uuidTaskH", uuidTaskH)} });
		if (null != trTaskD) {
			taskCollection = new TrTaskBean();
			
			taskCollection.setIntOptionText(trTaskD.getIntOptionText());
			taskCollection.setOptionText(trTaskD.getOptionText());
			taskCollection.setQuestionText(trTaskD.getQuestionText());
			taskCollection.setMsQuestion(msq);
		}
		
		return taskCollection;
	}

	@Override
	public List getInquiryTaskSurveyDetailAnswerHistoryFinal(Object params,
			Object order, String codeProcess, String uuidTaskRejectedHistory, AuditContext callerId) throws UnsupportedEncodingException {
		List <InquiryTaskCollectionFinalBean> result = new ArrayList<InquiryTaskCollectionFinalBean>();
		
		Object[] bean = (Object[])this.getManagerDAO().selectOneNative("task.inquirytasksurvey.getTaskHFinal", params);
		String[][] params1 = { { "uuidTask", bean[0].toString() }};
		List list = null;
		if (GlobalVal.CODE_PROCESS_REJECTED.equals(codeProcess)) {
			String[][] param =  { { "uuidTask", bean[0].toString() },{"uuidTaskRejectHistory", uuidTaskRejectedHistory}};
			list = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.answerHistoryRejectedFinalQSet", param, order);
		} 
		else {
			if (GlobalVal.CODE_PROCESS_VERIFIED.equals(codeProcess)){
				list = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.answerHistoryVerifQSet", params1, order);
			}
			else{
				list = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.answerHistoryQSet", params1, order);
			} 
		}
		String oldLabel = "";
		String questionText = "";
		String refId = "";
		
		for (int i = 0; i < list.size(); i++) {
			InquiryTaskCollectionFinalBean taskCollection = new InquiryTaskCollectionFinalBean();
			Map map = (HashMap) list.get(i);
			boolean flagverifmultiple = false;
			
			if (GlobalVal.CODE_PROCESS_REJECTED.equals(codeProcess)) {
				refId = null!=map.get("d19") ? map.get("d19").toString() : ""; 
			} else {
				refId = null!=map.get("d27") ? map.get("d27").toString() : "";
			}
						
			if (null != map.get("d5") && LIST_REF_ID_ALLOWMAPS.contains(refId)) {
			    taskCollection.setLat(map.get("d5").toString());
			}
			if (null != map.get("d6") && LIST_REF_ID_ALLOWMAPS.contains(refId)){ 
			    taskCollection.setLng(map.get("d6").toString()); 
			}
			if (null != map.get("d21")){
			    taskCollection.setAccuracy(Integer.parseInt(map.get("d21").toString()));
			}
			if (GlobalVal.CODE_PROCESS_REJECTED.equals(codeProcess)) {
				if(null != map.get("d16")){
					taskCollection.setAccuracy(Integer.parseInt(map.get("d16").toString()));
				}
				
				if (null != map.get("d4") && StringUtils.isNotBlank((String) map.get("d7"))) {
					taskCollection.setLob(map.get("d7").toString());
				}
				
				taskCollection.setQuestionText(map.get("d0").toString());
				taskCollection.setTextAnswer(map.get("d1").toString());
				taskCollection.setOptionText(map.get("d2").toString());
				taskCollection.setHasImage(map.get("d3").toString());
				if ("1".equals(taskCollection.getHasImage())) {
					taskCollection.setUuidTaskD((long) map.get("d18"));
				}
				taskCollection.setIsAsset(map.get("d9").toString());
				taskCollection.setLatBranch(map.get("d10") == null ? null : map.get("d10").toString());
				taskCollection.setLngBranch(map.get("d11") == null ? null : map.get("d11").toString());
				if ("1".equals(taskCollection.getIsAsset())){
					double lat = Double.parseDouble(taskCollection.getLat()==null?"0":taskCollection.getLat());
					double lng = Double.parseDouble(taskCollection.getLng()==null?"0":taskCollection.getLng());
					if(null == taskCollection.getLat()
							&& null == taskCollection.getLng()
							&& null != map.get("d15")
							&& null != map.get("d14")
							&& null != map.get("d13")
							&& null != map.get("d12")) {
						List<LocationBean> listLocations = new ArrayList<LocationBean>();
						LocationBean locationBean = new LocationBean();
						locationBean.setCellid(Integer.parseInt(map.get("d15").toString()));
						locationBean.setLac(Integer.parseInt(map.get("d14").toString()));
						locationBean.setMcc(Integer.parseInt(map.get("d12").toString()));
						locationBean.setMnc(Integer.parseInt(map.get("d13").toString()));
						listLocations.add(locationBean);
						this.geocoder.geocodeCellId(listLocations, callerId);
						
						lat = locationBean.getCoordinate()==null?0:locationBean.getCoordinate().getLatitude();
						lng = locationBean.getCoordinate()==null?0:locationBean.getCoordinate().getLongitude();
					}
						
					double distance = DistanceUtils.getDistanceInKm(lat, lng, 
							Double.parseDouble(taskCollection.getLatBranch()==null?"0":taskCollection.getLatBranch()), 
							Double.parseDouble(taskCollection.getLngBranch()==null?"0":taskCollection.getLngBranch()));
					NumberFormat nf = new DecimalFormat("#.##");
					String dtc = nf.format(distance);
					
					taskCollection.setDistance(dtc);
				}
				
				if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("d8").toString())){
					NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
					if (StringUtils.isNotBlank((String) map.get("d1"))) {
						taskCollection.setTextAnswer("Rp "+formatKurs.format(NumberUtils.toDouble(map.get("d1").toString())));
					}
				}
				
				if (GlobalVal.ANSWER_TYPE_DATE.equals(map.get("d8").toString())){
					if (StringUtils.isNotBlank(map.get("d1").toString())) {
						try {
							Date date = DateUtils.parseDate(map.get("d1").toString(), "ddMMyyyyHHmmss");
							taskCollection.setTextAnswer(DateFormatUtils.format(date, "dd/MM/yyyy"));
						} 
						catch (ParseException e) {
							throw new ChangeException(
									this.messageSource.getMessage("businesslogic.inquirytasksurvey.errorparsedate", 
											null, this.retrieveLocaleAudit(callerId))
									,map.get("d1").toString());
						}
					}
				}
				
				MsQuestion msQuestion = new MsQuestion();
				MsAnswertype msAnswertype = new MsAnswertype();
				msAnswertype.setCodeAnswerType(map.get("d8").toString());
				msQuestion.setMsAnswertype(msAnswertype);
				
				taskCollection.setMsQuestion(msQuestion);
				taskCollection.setFlagSource(map.get("d17").toString());
				taskCollection.setCodeProcess(codeProcess);
				
				if (null != map.get("d19")) {
					taskCollection.setAssetTagName(map.get("d19").toString());
				}
			} 
			else {	                
                if (StringUtils.isNotBlank(map.get("d8").toString())) {
                    Object[][] accDetailLobParams = { {"uuidTaskD", Long.valueOf(map.get("d8").toString())} };
                    Integer accuracy = (Integer) this.getManagerDAO().selectOneNative("common.retrieveAccuraryDetailLobFinal", accDetailLobParams);
                    taskCollection.setAccuracy(accuracy);
                    taskCollection.setLob(map.get("d8").toString());
                    //while accuracy is queried from DB, image itself is loaded using lazyload mechanism 
                }

				taskCollection.setQuestionText(map.get("d1").toString());
				questionText = taskCollection.getQuestionText();
				
				taskCollection.setTextAnswer(map.get("d2").toString());
				taskCollection.setOptionText(map.get("d3").toString());	
				taskCollection.setFinOptionText(map.get("d12").toString());
				taskCollection.setFinOptionText(map.get("d13").toString());
				
				if ( GlobalVal.CODE_PROCESS_VERIFIED.equals(codeProcess)) {
					if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(map.get("d9").toString())
							|| GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(map.get("d9").toString())
							|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(map.get("d9").toString())){
						if ((!oldLabel.equals(questionText) && StringUtils.isBlank(taskCollection.getFinOptionText()))
								|| StringUtils.isNotBlank(taskCollection.getFinOptionText())
								|| (StringUtils.isBlank(taskCollection.getOptionText())
										&& StringUtils.isBlank(taskCollection.getFinOptionText())))  {
							flagverifmultiple = true;
						} 
					}
					taskCollection.setTextAnswer(map.get("d12").toString());
					taskCollection.setOptionText(map.get("d13").toString());
				}
				
				taskCollection.setHasImage(map.get("d4").toString());
				if ("1".equals(taskCollection.getHasImage())) {
					taskCollection.setUuidTaskD((long) map.get("d26"));
				}
				taskCollection.setIntTextAnswer(map.get("d10").toString());
				taskCollection.setIntOptionText(map.get("d11").toString());
				taskCollection.setIsAsset(map.get("d14").toString());
				taskCollection.setLatBranch(map.get("d15") == null ? null : map.get("d15").toString());
				taskCollection.setLngBranch(map.get("d16") == null ? null : map.get("d16").toString());
				if ("1".equals(taskCollection.getIsAsset())){
					double lat = Double.parseDouble(taskCollection.getLat()==null?"0":taskCollection.getLat());
					double lng = Double.parseDouble(taskCollection.getLng()==null?"0":taskCollection.getLng());
					if(null == taskCollection.getLat()
							&& null == taskCollection.getLng()
							&& null != map.get("d20")
							&& null != map.get("d19")
							&& null != map.get("d18")
							&& null != map.get("d17")) {
						List<LocationBean> listLocations = new ArrayList<LocationBean>();
						LocationBean locationBean = new LocationBean();
						locationBean.setCellid(Integer.parseInt(map.get("d20")==null?"0":map.get("d20").toString()));
						locationBean.setLac(Integer.parseInt(map.get("d19")==null?"0":map.get("d19").toString()));
						locationBean.setMcc(Integer.parseInt(map.get("d17")==null?"0":map.get("d17").toString()));
						locationBean.setMnc(Integer.parseInt(map.get("d18")==null?"0":map.get("d18").toString()));
						listLocations.add(locationBean);
						this.geocoder.geocodeCellId(listLocations, callerId);
						
						lat = locationBean.getCoordinate()==null?0:locationBean.getCoordinate().getLatitude();
						lng = locationBean.getCoordinate()==null?0:locationBean.getCoordinate().getLongitude();
					}
					double distance = DistanceUtils.getDistanceInKm(lat, lng, 
							Double.parseDouble(taskCollection.getLatBranch()==null?"0":taskCollection.getLatBranch()), 
							Double.parseDouble(taskCollection.getLngBranch()==null?"0":taskCollection.getLngBranch()));
					NumberFormat nf = new DecimalFormat("#.##");
					String dtc = nf.format(distance);
					
					taskCollection.setDistance(dtc);
				}
				
				if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(map.get("d9").toString())){
					NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
					if (StringUtils.isNotBlank((String) map.get("d2"))) { 
						taskCollection.setTextAnswer("Rp "+ formatKurs.format(NumberUtils.toDouble(map.get("d2").toString())));
					}
					if (StringUtils.isNotBlank((String) map.get("d10"))) {
						taskCollection.setIntTextAnswer("Rp "+ formatKurs.format(NumberUtils.toDouble(map.get("d10").toString())));
					}
				}
				
				MsQuestion msQuestion = new MsQuestion();
				MsAnswertype msAnswertype = new MsAnswertype();
				msAnswertype.setCodeAnswerType(map.get("d9").toString());
				msQuestion.setMsAnswertype(msAnswertype);
				
				taskCollection.setMsQuestion(msQuestion);
				taskCollection.setFlagSource(map.get("d25").toString());
				taskCollection.setCodeProcess(codeProcess);
				
				if (null != map.get("d28")) {
					taskCollection.setAssetTagName(map.get("d28").toString());
				}
			}
			
			if (result.contains(taskCollection))
			    continue;
			
			if (GlobalVal.ANSWER_TYPE_MULTIPLE.equals(map.get("d9").toString())
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(map.get("d9").toString())
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(map.get("d9").toString())){
				if ( GlobalVal.CODE_PROCESS_VERIFIED.equals(codeProcess)) {
						if (flagverifmultiple)  {
							result.add(taskCollection);
						}	
				}
				else if(GlobalVal.CODE_PROCESS_SUBMITTED.equals(codeProcess) || null == codeProcess ){
					if ((!oldLabel.equals(questionText) 
							&& (StringUtils.isBlank(taskCollection.getOptionText()) || StringUtils.isNotBlank(taskCollection.getIntOptionText())))
							|| (StringUtils.isNotBlank(taskCollection.getOptionText()) || StringUtils.isNotBlank(taskCollection.getIntOptionText()))
							|| (StringUtils.isBlank(taskCollection.getOptionText())
							&& StringUtils.isBlank(taskCollection.getFinOptionText()))) {
						result.add(taskCollection);
					}
				} 
				else {
					result.add(taskCollection);
				}
			} 
			else {
				result.add(taskCollection);
			}
			oldLabel = map.get("d0").toString();
		}
		
		if("1".equals(this.link_encrypt)){
			for(int i=0; i<result.size(); i++){
				if("1".equals(result.get(i).getHasImage())){
					String[] temp = {result.get(i).getLob().toString()};
					result.get(i).setLob(CipherTool.encryptData(temp).get(0).toString());
				}
			}
		}
		return result;
	}

	@Override
	public List getInquiryTaskSurveyDetailTaskHistory(Object params,
			Object order, AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.taskHistory", params, order);	
		for(int i=0; i<list.size(); i++){
			Map<String, Object> data =  (Map) list.get(i);
			if(GlobalVal.CODE_PROCESS_LINKED_TASK.equals((String) data.get("d7"))) {
				String message = (String) data.get("d5");
				if (StringUtils.isNotBlank(message)) {
					String uuidLinkedTask = message.substring(message.indexOf("(")+1, message.indexOf(")"));
					if (StringUtils.isNotBlank(uuidLinkedTask)) {
						data.put("linkedTask", uuidLinkedTask);
					}
				}
			}
		}
		return list;
	}
	
	@Override
	public List getInquiryTaskSurveyDetailTaskHistoryFinal(Object params,
			Object order, AuditContext callerId) {
		List list = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.taskHistoryFinal", params, order);		
		for(int i=0; i<list.size(); i++){
			Map<String, Object> data =  (Map) list.get(i);
			if(GlobalVal.CODE_PROCESS_LINKED_TASK.equals((String) data.get("d7"))) {
				String message = (String) data.get("d5");
				if (StringUtils.isNotBlank(message)) {
					String uuidLinkedTask = message.substring(message.indexOf("(")+1, message.indexOf("("));
					if (StringUtils.isNotBlank(uuidLinkedTask)) {
						data.put("linkedTask", uuidLinkedTask);
					}
				}
			}
		}
		return list;
	}

	@Override
	public List getStatusList(Object params, Object order, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.statusList", params, order);		
		return result;
	}

	@Override
	public List getPriorityList(Object params, Object order, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.priorityList", params, order);		
		return result;
	}

	@Override
	public List getFormList(Object params, Object order, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.formList", params, order);	
		return result;
	}

	@Override
	public Object[] getDetailTaskSurvey(Object params, String modeAction, AuditContext callerId) throws UnsupportedEncodingException {
		Object[] result = (Object[]) this.getManagerDAO().selectOneNative("task.inquirytasksurvey.detailtasksurvey", params);
		return result;
	}
	
	@Override
	public Object[] getDetailTaskSurveyFinal(Object params, AuditContext callerId) throws UnsupportedEncodingException {
		Object[] result = (Object[]) this.getManagerDAO().selectOneNative("task.inquirytasksurvey.detailtasksurveyFinal", params);
		return result;
	}
	
	@Override
	public String getUuidSvyId(String svyId, String sqCode, AuditContext callerId) {
		Object[][] param = { {Restrictions.eq("taskId", svyId)} };
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, param);
		String result = String.valueOf(trTaskH.getUuidTaskH());
		
		return result;
	}

	@Override
	public String getTaskByHistroy(String uuidTaskHist, AuditContext callerId) {
		TrTaskhistory taskHist = this.getManagerDAO().selectOne(TrTaskhistory.class, Long.valueOf(uuidTaskHist));
		if (null == taskHist) {
			return null;
		}
		if (StringUtils.contains(taskHist.getNotes(), GlobalVal.NOTES_TASK_SUBMITTED_HIST)) {
			uuidTaskHist = StringUtils.replace(taskHist.getNotes(), GlobalVal.NOTES_TASK_SUBMITTED_HIST, ""); 
		} else {
			uuidTaskHist = null;
		}
		return uuidTaskHist;
	}
	
	@Override
	public String getTaskByHistroyFinal(String uuidTaskHist, AuditContext callerId) {
		FinalTrTaskhistory taskHist = this.getManagerDAO().selectOne(FinalTrTaskhistory.class, Long.valueOf(uuidTaskHist));
		if (null == taskHist) {
			return null;
		}
		if (StringUtils.containsAny(taskHist.getNotes(), GlobalVal.NOTES_TASK_SUBMITTED_HIST)) {
			uuidTaskHist = StringUtils.replace(taskHist.getNotes(), GlobalVal.NOTES_TASK_SUBMITTED_HIST, ""); 
		} else {
			uuidTaskHist = null;
		}
		return uuidTaskHist;
	}

	@Override
	public String checkMenu(AuditContext callerId) {
		String[][] params ={{"menuCode",GlobalVal.MENU_INQUIRY_REGION},
							{"uuid",callerId.getCallerId()}};
		String check = "0";
		Integer count =
				(Integer) this.getManagerDAO().selectOneNative("task.inquirytasksurvey.checkMenu", params);
		if(count>0){
			check = "1";
		}
		
		return check;
	}

	@Override
	public List getDropDownRegionList(AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.regionList", null, null);		
		return result;
	}
	
	@Override
	public List getDropDownRegionListExcludeRegionUserLogin(Long uuidBranch) {
		Object[][] params ={{"uuidBranch",uuidBranch}};
		List result = this.getManagerDAO().selectAllNative("task.inquirytasksurvey.regionListExcludeRegionUserLogin", params, null);		
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List listInquiryTaskSurveybyBranchRegionNativeString(Object params,
			AuditContext callerId) {
		List result = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();
		Map<String, String> paramsQuery = this.sqlPagingBuilderByRegion((Object[][]) params, paramsStack, callerId);
		String selectInside = paramsQuery.get("selectInside");
		String selectOutside = paramsQuery.get("selectOutside");
		String joinInside = paramsQuery.get("joinInside");
		String joinOutside = paramsQuery.get("joinOutside");
		String where = paramsQuery.get("sb");
		String caseWhen = paramsQuery.get("caseWhen");
		StringBuilder ordersQueryString = this.sqlPagingOrderRegionBuilder((String) ((Object[][]) params)[19][1], callerId);

		StringBuilder queryBuilder = new StringBuilder()
			.append("WITH cpx AS (SELECT  MOBILE_TASK_CODE, APP_NO, STATUS, STATS_START_DT, PREP_START_DT, PREP_END_DT, SEQUENCE_PROCESS ")
			.append("FROM STAGING_CREDIT_PROCESS_X_RANK with (nolock)) ")
			.append("SELECT * from ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER ( ")
			.append("ORDER BY rownum) AS recnum FROM ( ")
			.append("select c.uuid_task_h, ")
			.append("isnull(c.CUSTOMER_NAME,'-') CUSTOMER_NAME, ")
			.append("isnull(c.appl_no,'-') appl_no, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.assign_date, 113), 17),'-') as assign_date, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.download_date, 113),17),'-') as download_date, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.submit_date, 113),17),'-') as submit_date, ")
			.append(selectOutside)
			.append("isnull(mssta.status_task_desc,'-') status_task_desc, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.send_date, 113),17),'-') as send_date, c.flag, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, c.promise_date, 113),17),'-') as promise_date, ")
			.append("c.task_id, isnull(LEFT(CONVERT(VARCHAR, c.start_dt, 113),17),'-') as start_dt, ")
			.append("flagOrder, msf.FORM_NAME,  ")
			.append("isnull(STATUS, '-') as statusWise, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, STATS_START_DT, 113),17),'-') as status_date, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, PREP_START_DT, 113),17),'-') as prep_start_date, ")
			.append("isnull(LEFT(CONVERT(VARCHAR, PREP_END_DT, 113),17),'-') as prep_send_date, ")
			.append("ROW_NUMBER() OVER ( ").append(ordersQueryString).append(") AS rownum, ")
			.append("c.DOWNLOADABLE ")
			.append("from ( ")
			.append("SELECT trth.uuid_task_h, ")
			.append("trth.customer_name as CUSTOMER_NAME, ")
			.append("trth.appl_no as appl_no, ")
			.append("trth.assign_date as assign_date, ")
			.append("trth.download_date as download_date, ")
			.append("trth.submit_date as submit_date, ")
			.append(selectInside)
			.append("trth.uuid_status_task as uuid_status_task, ")
			.append("trth.send_date as send_date, ")
			.append("'1' as flag, '0' as flagOrder, ")
			.append("trth.assign_date as assign_dt, ")
			.append("trth.download_date as download_dt, ")
			.append("trth.submit_date as submit_dt, ")
			.append("trth.send_date as send_dt, ")
			.append("trth.PROMISE_DATE as promise_date, ")
			.append("trth.task_id, trth.start_dtm as start_dt, ")
			.append("trth.uuid_form AS uuid_form, ")
			.append(caseWhen)
			.append("FROM tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE 1=1 ")
			.append(where)
			.append("union ALL ")
			.append("SELECT trth.uuid_task_h, ")
			.append("trth.customer_name as CUSTOMER_NAME, ")
			.append("trth.appl_no as appl_no, ")
			.append("trth.assign_date as assign_date, ")
			.append("trth.download_date as download_date, ")
			.append("trth.submit_date as submit_date, ")
			.append(selectInside)
			.append("trth.uuid_status_task as uuid_status_task, ")
			.append("trth.send_date as send_date, ")
			.append("'2' as flag, '0' as flagOrder, ")
			.append("trth.assign_date as assign_dt, ")
			.append("trth.download_date as download_dt, ")
			.append("trth.submit_date as submit_dt, ")
			.append("trth.send_date as send_dt, ")
			.append("trth.PROMISE_DATE as promise_date, ")
			.append("trth.task_id, trth.start_dtm as start_dt, ")
			.append("trth.uuid_form AS uuid_form, ")
			.append(caseWhen)
			.append("FROM final_tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE 1=1 ")
			.append(where)
			.append(") c ")
			.append("JOIN MS_FORM msf with (nolock) on (msf.uuid_form = c.uuid_form) ")
			.append("JOIN ms_statustask mssta with (nolock) ON (c.uuid_status_task = mssta.uuid_status_task) ")
			.append(joinOutside)
			.append("left outer JOIN cpx on (c.APPL_NO=cpx.MOBILE_TASK_CODE ")
			.append("or c.APPL_NO = cpx.APP_NO) ")
			.append(") a WHERE a.rownum <= :end ")	
			.append(") b WHERE b.recnum >= :start ");
		
//		paramsStack.push(new Object[]{"odr", (String) ((Object[][]) params)[19][1]});	
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[17][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[18][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
	    LOG.info(queryBuilder.toString());
	    
		result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		if("1".equals(this.link_encrypt)){
			List newRes = new ArrayList();
			for(int i=0; i<result.size(); i++){
				Map map = (Map) result.get(i);
				String[] temp = {map.get("d0").toString()};
				List res = CipherTool.encryptData(temp);
				map.put("d0", res.get(0).toString());
				newRes.add(map);
			}
			return newRes;
		}else{
			return result;
		}
		
		
	}

	@Override
	public Integer countListInquiryTaskSurveyBranchRegionNativeString(
			Object params, AuditContext callerId) {
		Integer result = 0;
		Stack<Object[]> paramsStack = new Stack<>();
		Map<String, String> paramsQuery = this.sqlPagingBuilderByRegion((Object[][]) params, paramsStack, callerId);
		String joinInside = paramsQuery.get("joinInside");
		String where = paramsQuery.get("sb");
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT COUNT(1) from ( ")
			.append("SELECT trth.uuid_task_h ")
			.append("FROM tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE 1=1 ")
			.append(where)
			.append("union ALL ")
			.append("SELECT trth.uuid_task_h ")
			.append("FROM final_tr_task_h trth with (nolock) ")
			.append(joinInside)
			.append("WHERE 1=1 ")
			.append(where)
			.append(") c ");
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}

		result = (Integer)this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}
	
	/*0	customerName, 1	regionId, 2 customerAddress,
	3 applNo, 4 fullName, 5 assignDateStart, 6 assignDateEnd, 
	7 submitDateStart, 8 submitDateEnd, 9 retrieveDateStart,
	10 retrieveDateEnd, 11 branchIdLogin, 12 statusId,
	13 subsystemId, 14 priorityId, 15 formId, 16 currentDate,
	17 start, 18 end, 19 odr, 20 sendDateStart, 21 sendDateEnd,
	*/
	private Map<String, String> sqlPagingBuilderByRegion(Object[][] params,Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null){
			return new HashMap<String, String>();
		}

		StringBuilder sb = new StringBuilder();
		StringBuilder sbJoinInside = new StringBuilder();
		StringBuilder sbJoinOutside = new StringBuilder();
		StringBuilder sbSelectInside = new StringBuilder();
		StringBuilder sbSelectOutside = new StringBuilder();
		StringBuilder sbCaseWhen = new StringBuilder();
		Map<String, String> mapResult = new HashMap<String, String>();
		
		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
		DateTime currentDate = formatter.parseDateTime((String) params[16][1]);
		currentDate = currentDate.minusMillis(3);
		// ---ASSIGN_DATE
		DateTime assignDateStart = null, assignDateEnd = null;
		if (params.length > 5&& !StringUtils.equals("%", (String) params[5][1])) {
			assignDateStart = formatter.parseDateTime((String) params[5][1]);
			assignDateStart = assignDateStart.withMillisOfDay(0);
		}
		if (params.length > 5&& !StringUtils.equals("%", (String) params[6][1])) {
			assignDateEnd = formatter.parseDateTime((String) params[6][1]);
			assignDateEnd = assignDateEnd.minusMillis(3);
		}
		
		if (assignDateStart != null && assignDateEnd != null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart == null && assignDateEnd != null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN '1990-01-01 00:00:00' AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart != null && assignDateEnd == null) {
			sb.append("AND trth.ASSIGN_DATE BETWEEN :assignDateStart AND :currentDate ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
//		else if(assignDateStart == null && assignDateEnd == null){
//			sb.append("AND trth.ASSIGN_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}

		// ---SUBMIT_DATE
		DateTime submitDateStart = null, submitDateEnd = null;
		if (params.length > 7&& !StringUtils.equals("%", (String) params[7][1])) {
			submitDateStart = formatter.parseDateTime((String) params[7][1]);
			submitDateStart = submitDateStart.withMillisOfDay(0);
		}
		if (params.length > 7&& !StringUtils.equals("%", (String) params[8][1])) {
			submitDateEnd = formatter.parseDateTime((String) params[8][1]);
			submitDateEnd = submitDateEnd.minusMillis(3);
		}
		if (submitDateStart != null && submitDateEnd != null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN :submitDateStart AND :submitDateEnd ");
			paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
			paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
		} 
		else if (submitDateStart == null && submitDateEnd != null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN '1990-01-01 00:00:00' AND :submitDateEnd ");
			paramStack.push(new Object[] { "submitDateEnd",submitDateEnd.toDate() });
		} 
		else if (submitDateStart != null && submitDateEnd == null) {
			sb.append("AND trth.SUBMIT_DATE BETWEEN :submitDateStart AND :currentDate ");
			paramStack.push(new Object[] { "submitDateStart",submitDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		}
//		else if(submitDateStart == null && submitDateEnd == null){
//			sb.append("AND trth.SUBMIT_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}
		// ---PROMISE DATE
				DateTime promiseDateStart = null, promiseDateEnd = null;
				if (params.length > 22
						&& !StringUtils.equals("%", (String) params[23][1])) {
					promiseDateStart = formatter.parseDateTime((String) params[23][1]);
					promiseDateStart = promiseDateStart.withMillisOfDay(0);
				}
				if (params.length > 23
						&& !StringUtils.equals("%", (String) params[24][1])) {
					promiseDateEnd = formatter.parseDateTime((String) params[24][1]);
					promiseDateEnd = promiseDateEnd.minusMillis(3);
				}
				if (promiseDateStart != null && promiseDateEnd != null) {
					sb.append("AND trth.PROMISE_DATE BETWEEN :promiseDateStart AND :promiseDateEnd "
							+ "AND trth.PROMISE_DATE is not null ");
					paramStack.push(new Object[] { "promiseDateStart",
							promiseDateStart.toDate() });
					paramStack.push(new Object[] { "promiseDateEnd",
							promiseDateEnd.toDate() });
				}
//				else if (promiseDateStart == null && promiseDateEnd != null) {
//					sb.append("AND trth.PROMISE_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :promiseDateEnd ");
//					paramStack.push(new Object[] { "promiseDateEnd",
//							promiseDateEnd.toDate() });
//				} else if (promiseDateStart != null && promiseDateEnd == null) {
//					sb.append("AND trth.PROMISE_DATE, '1990-01-01 00:00:00') BETWEEN :promiseDateStart AND :currentDate ");
//					paramStack.push(new Object[] { "promiseDateStart",
//							promiseDateStart.toDate() });
//					paramStack
//							.push(new Object[] { "currentDate", currentDate.toDate() });
//				} 
//				else if (promiseDateStart == null && promiseDateEnd == null) {
//					sb.append("AND trth.PROMISE_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//					paramStack
//							.push(new Object[] { "currentDate", currentDate.toDate() });
//				}
				
		// ---DOWNLOAD_DATE
		DateTime downloadDateStart = null, downloadDateEnd = null;
		if (params.length > 9&& !StringUtils.equals("%", (String) params[9][1])) {
			downloadDateStart = formatter.parseDateTime((String) params[9][1]);
			downloadDateStart = downloadDateStart.withMillisOfDay(0);
		}
		if (params.length > 9&& !StringUtils.equals("%", (String) params[10][1])) {
			downloadDateEnd = formatter.parseDateTime((String) params[10][1]);
			downloadDateEnd = downloadDateEnd.minusMillis(3);
		}
		if (downloadDateStart != null && downloadDateEnd != null) {
			sb.append("AND trth.DOWNLOAD_DATE BETWEEN :retrieveDateStart AND :retrieveDateEnd ");
			paramStack.push(new Object[] { "retrieveDateStart",downloadDateStart.toDate() });
			paramStack.push(new Object[] { "retrieveDateEnd",downloadDateEnd.toDate() });
		} 
		else if (downloadDateStart == null && downloadDateEnd != null) {
			sb.append("AND trth.DOWNLOAD_DATE BETWEEN '1990-01-01 00:00:00' AND :retrieveDateEnd ");
			paramStack.push(new Object[] { "retrieveDateEnd",downloadDateEnd.toDate() });
		} 
		else if (downloadDateStart != null && downloadDateEnd == null) {
			sb.append("AND trth.DOWNLOAD_DATE BETWEEN :retrieveDateStart AND :currentDate ");
			paramStack.push(new Object[] { "retrieveDateStart",downloadDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
//		else if(downloadDateStart == null && downloadDateEnd == null){
//			sb.append("AND trth.DOWNLOAD_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}
		
		// ---SEND_DATE
		DateTime sendDateStart = null, sendDateEnd = null;
		if (params.length > 20&& !StringUtils.equals("%", (String) params[20][1])) {
			sendDateStart = formatter.parseDateTime((String) params[20][1]);
			sendDateStart = sendDateStart.withMillisOfDay(0);
		} 
		else if (params.length <= 20 && !StringUtils.equals("%", (String) params[17][1])) {
			sendDateStart = formatter.parseDateTime((String) params[17][1]);
			sendDateStart = sendDateStart.withMillisOfDay(0);
		}
		if (params.length > 20&& !StringUtils.equals("%", (String) params[21][1])) {
			sendDateEnd = formatter.parseDateTime((String) params[21][1]);
			sendDateEnd = sendDateEnd.minusMillis(3);
		}  
		else if (params.length <= 20 && !StringUtils.equals("%", (String) params[18][1])) {
			sendDateEnd = formatter.parseDateTime((String) params[18][1]);
			sendDateEnd = sendDateEnd.withMillisOfDay(0);
		}
		if (sendDateStart != null && sendDateEnd != null) {
			sb.append("AND trth.SEND_DATE BETWEEN :sendDateStart AND :sendDateEnd ");
			paramStack.push(new Object[] { "sendDateStart",sendDateStart.toDate() });
			paramStack.push(new Object[] { "sendDateEnd",sendDateEnd.toDate() });
		} 
		else if (sendDateStart == null && sendDateEnd != null) {
			sb.append("AND trth.SEND_DATE BETWEEN '1990-01-01 00:00:00' AND :sendDateEnd ");
			paramStack.push(new Object[] { "sendDateEnd",sendDateEnd.toDate() });
		} 
		else if (sendDateStart != null && sendDateEnd == null) {
			sb.append("AND trth.SEND_DATE BETWEEN :sendDateStart AND :currentDate ");
			paramStack.push(new Object[] { "sendDateStart",sendDateStart.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
		} 
//		else if(sendDateStart == null && sendDateEnd == null){
//			sb.append("AND trth.SEND_DATE, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
//			paramStack.push(new Object[] { "currentDate",currentDate.toDate() });
//		}

		// ---OTHERS
		if(!StringUtils.equals("%", (String) params[0][1])){
			sb.append(" AND UPPER(trth.customer_name) LIKE UPPER('%' + :customerName + '%') ");
			paramStack.push(new Object[] { "customerName",(String) params[0][1] });
		}
		if (!StringUtils.equals("%", (String) params[1][1])){
			sbSelectInside.append("msb.branch_name as branch_name, ");
			sbSelectOutside.append("isnull(c.branch_name,'-') as branch_name, ");
			sbJoinInside.append("JOIN MS_BRANCH msb with (nolock) on (msb.uuid_branch = trth.uuid_branch) ")
					.append("LEFT JOIN MS_REGION msr with (nolock) on (msr.uuid_region = msb.uuid_region) ");
			sb.append("AND msr.UUID_REGION = :uuidRegion ");
			paramStack.push(new Object[] { "uuidRegion", Long.valueOf((String) params[1][1]) });
		} else {
			sbSelectInside.append("trth.UUID_BRANCH as uuid_branch, ");
			sbSelectOutside.append("isnull(msb.BRANCH_NAME,'-') as branch_name, ");
			sbJoinOutside.append("JOIN MS_BRANCH msb with (nolock) on (msb.uuid_branch = c.uuid_branch) ");
		}
		
		if(!StringUtils.equals("%", (String) params[2][1])){
			sb.append("AND UPPER(trth.customer_address) LIKE UPPER('%' + :customerAddress + '%') ");
			paramStack.push(new Object[] { "customerAddress", (String) params[2][1] });
		}
		
		if(!StringUtils.equals("%", (String) params[3][1])){
			sb.append("AND UPPER(trth.appl_no) LIKE UPPER('%' + :applNo + '%') ");
			paramStack.push(new Object[] { "applNo", (String) params[3][1] });
		}
		
		if (!StringUtils.equals("%", (String) params[4][1])){
			sbSelectInside.append("msu.full_name as full_name, ");
			sbSelectOutside.append("isnull(c.full_name,'-') full_name, ");
			sbJoinInside.append("left outer JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user) ");
			sb.append("AND UPPER(msu.full_name) LIKE UPPER('%' + :fullName + '%') ");
			paramStack.push(new Object[] { "fullName", (String) params[4][1] });
		} else {
			sbSelectInside.append("trth.UUID_MS_USER as uuid_ms_user, ");
			sbSelectOutside.append("isnull(msu.full_name,'-') full_name, ");
			sbJoinOutside.append("left outer JOIN am_msuser msu with (nolock) ON (c.uuid_ms_user = msu.uuid_ms_user) ");
		}
		
		if(!StringUtils.equals("%", (String) params[12][1])){
			sb.append("AND trth.uuid_status_task = :statusId ");
			paramStack.push(new Object[] { "statusId", Long.valueOf((String) params[12][1]) });
		}
		
//		sb.append("AND mssta.uuid_ms_subsystem = :subsystemId ");
//		paramStack.push(new Object[] { "subsystemId", (long) params[13][1] });
		
		if(!StringUtils.equals("%", (String) params[14][1])){
			sb.append("AND trth.uuid_priority = :priorityId ");
			paramStack.push(new Object[] { "priorityId", Long.valueOf((String) params[14][1]) });
		}
		
		if(!StringUtils.equals("%", (String) params[15][1])){
			sb.append("AND trth.uuid_form = :formId ");
			paramStack.push(new Object[] { "formId", Long.valueOf((String) params[15][1]) });
		}
		
		sbCaseWhen.append("CASE WHEN EXISTS ( ")
		.append("SELECT 1 FROM TR_TASK_D ttd WITH(NOLOCK) WHERE ttd.UUID_TASK_H = trth.UUID_TASK_H ")
		.append("AND ttd.QUESTION_TEXT = 'Persetujuan Pemohon') ")
		.append("THEN 1 ELSE 0 END AS DOWNLOADABLE ");
		
		mapResult.put("selectInside", sbSelectInside.toString());
		mapResult.put("selectOutside", sbSelectOutside.toString());
		mapResult.put("sb", sb.toString());
		mapResult.put("joinOutside", sbJoinOutside.toString());
		mapResult.put("joinInside", sbJoinInside.toString());
		mapResult.put("caseWhen", sbCaseWhen.toString());
		return mapResult;
	}
	
	/*
	 * 1 CUSTOMER_NAME 2 appl_no 3 assign_dt 4
	 * download_dt 5 submit_dt 6 full_name 7
	 * status_task_desc 8 send_dt
	 */
	private StringBuilder sqlPagingOrderRegionBuilder(String orders, AuditContext callerId) {
		if (StringUtils.isEmpty(orders) || "0".equals(orders)) {
			orders = "1A"; // set default order by flagOrder ASC
		}

		String[] orderCols = { "flagOrder", "c.CUSTOMER_NAME", "c.appl_no",
				"c.assign_dt", "c.download_dt",
				"c.submit_dt", "c.full_name", "c.status_task_desc",
				"c.send_dt", "branch_name" };
		StringBuilder queryOrder = new StringBuilder(" ORDER BY ");

		int colIdx = NumberUtils.toInt(StringUtils.substring(orders, 0, StringUtils.length(orders) - 1));
//		colIdx = (colIdx > 0) ? colIdx - 1 : colIdx;
		queryOrder.append(orderCols[colIdx]);
		if (StringUtils.endsWith(orders, "D")) {
			queryOrder.append(" DESC");
		}
		else {
			queryOrder.append(" ASC");
		}

		return queryOrder;
	}
	
	@Transactional(readOnly=true)
	@Override
	public Map<String, Object> exportPDF(String uuidTask, String codeProcess, 
			String flagSource, String uuidTaskRejectedHistory, AuditContext callerId) {
		Map<String, Object> res = new HashMap<>();
		Document document = new Document();
		PdfWriter writer = null;
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			writer = PdfWriter.getInstance(document, stream);
			
			document.setPageSize(PageSize.A4);
	        document.setMargins(36, 36, 80, 45);
	        
			writer.setPageEvent(new MyHeaderFooter());

			document.open();
		
			boolean isFinal = false;
			
			document.add(new Phrase("\n"));
			
			List<String> answer = getDataAnswer(uuidTask, callerId);
			
			boolean isWrapText = setAnswerLayout(document, writer, answer);
			document.add(new Phrase("\n\n"));
			declarationParagraph(document, writer, answer, isWrapText);
			
			document.close();
 			String fileName = this.retriveFileNamePDF(uuidTask, "MS", isFinal) +".pdf";
			res.put("exp", stream.toByteArray());
			res.put("fileName", fileName);
			
		} catch (DocumentException | IOException e) {
			LOG.error("Error on exporting PDF", e);
		}
		
		return res;
	}
	
	public class MyHeaderFooter extends PdfPageEventHelper {

		private void addHeader(PdfWriter writer, Document document) {
			PdfContentByte cb = writer.getDirectContent();
			Font ffont = new Font(Font.FontFamily.UNDEFINED, 12, Font.BOLD);
			Font font = new Font(Font.FontFamily.UNDEFINED, 11, Font.NORMAL);
			
			Phrase header1 = new Phrase("Formulir Permohonan Analisa Fasilitas Pembiayaan ", ffont);
			ColumnText.showTextAligned(
					cb,
					Element.ALIGN_LEFT,
					header1,
					document.left(), 
					document.top() + 25, 0);
			Phrase header2 = new Phrase("Saya yang mengisi formulir di bawah ini :", font);
			ColumnText.showTextAligned(
					cb,
					Element.ALIGN_LEFT,
					header2,
					document.left(), 
					document.top() + 5, 0);
		}
		
		private void addFooter(PdfWriter writer, Document document) throws MalformedURLException, IOException, DocumentException {
			PdfContentByte cb = writer.getDirectContent();
			
			URL tem = getClass().getClassLoader().getResource("META-INF/resources/include/images/login/Logo-AdIns.png");
			Image img = Image.getInstance(tem);
			img.scaleToFit(180, 180);
			img.setAbsolutePosition(30, 50);
			document.add(img);
			
			Font ffont = new Font(Font.FontFamily.UNDEFINED, 9, Font.BOLD);
			Font font = new Font(Font.FontFamily.UNDEFINED, 8, Font.BOLD);

			Phrase footerText = new Phrase("PT Wahana Ottomitra Multiartha Tbk ('WOM Finance')", ffont);
			ColumnText.showTextAligned(
					cb,
					Element.ALIGN_LEFT,
					footerText,
					document.left(),
					document.bottom()+10, 0);
			
			Phrase footerText2 = new Phrase("Terdaftar dan diawasi oleh Otoritas Jasa Keuangan", font);
			ColumnText.showTextAligned(
					cb,
					Element.ALIGN_LEFT,
					footerText2,
					document.left(),
					document.bottom(), 0);
		}


		@Override
		public void onEndPage(PdfWriter writer, Document document) {
			try {
				addHeader(writer, document);
				addFooter(writer, document);
			} catch (Exception e) {
				LOG.error("Error on writing header/footer for exporting PDF", e);
			}

		}
	}
	
	@Override
	public String retriveFileNamePDF(String uuidTask, String msSubSystem, boolean isFinal) {
		StringBuilder absFileName = new StringBuilder(); 		
		absFileName.append(uuidTask);
		return absFileName.toString();	
	}
	
	public List<Map<String, Object>> getAnswerList(String uuidTask){
		Object[][] params ={{"ttd.trTaskH.uuidTaskH",uuidTask}};
		List<Map<String, Object>> result = this.getManagerDAO().selectAllNativeString("SELECT ", params);
		return result;
	}
	
	private boolean setAnswerLayout(Document document, PdfWriter writer, List<String> answer) throws DocumentException, IOException {
		Font font = new Font(Font.FontFamily.UNDEFINED, 10, Font.NORMAL);
		
		URL tem = getClass().getClassLoader().getResource("META-INF/resources/include/images/icon/ic_checked_box_sm.png");
		Image img = Image.getInstance(tem);
		img.scaleToFit(15, 15);
		
		URL tem2 = getClass().getClassLoader().getResource("META-INF/resources/include/images/icon/ic_unchecked_box_sm.png");
		Image img2 = Image.getInstance(tem2);
		img2.scaleToFit(15, 15);
		
		List<String> labelList = new ArrayList<>();
		labelList.add("  1. Nama lengkap (sesuai KTP)				");
		labelList.add("\n   2. NIK (sesuai KTP)					");
		labelList.add("\n   3. Alamat lengkap (sesuai KTP)		");
		labelList.add("\n   4. Tempat lahir (sesuai KTP)			");
		labelList.add("\n   5. Tanggal lahir (sesuai KTP)		");
		labelList.add("\n   6. Kewarganegaraan					");
		labelList.add("\n   7. Pekerjaan							");
		labelList.add("\n   8. Jenis kelamin						");
		labelList.add("\n   9. Status perkawinan					");
		labelList.add("\n10. Penghasilan Rata-rata per tahun (Pilih satu)	");
		
		//check new lines from wrap text
		int labelLines = 0;
		boolean isWrapText = false;
		List<Integer> lines = addCheckLines(answer);
		for (int i=0; i<labelList.size(); i++) {
			int itemLine = lines.get(i);
			labelLines = labelLines+(15*itemLine);
			if (itemLine > 0) {
				StringBuilder label = new StringBuilder(labelList.get(i));
				for (int j = 0; j<=itemLine; j++) {
					label.append("\n");
					isWrapText = true;
				}
				labelList.set(i, label.toString());
			}
		}
		
		//kordinat x,y untuk checkbox
		int xCheckbox = 55;
		int yCheckbox = 175 + labelLines;
		if (isWrapText) {
			yCheckbox = yCheckbox + 15;
		}
		for (int i=1; i<=5; i++) {
			if (String.valueOf(i).equals(answer.get(9))) {
				img.setAbsolutePosition(xCheckbox, document.top()-yCheckbox );
				document.add(img);
			} 
			else {
				img2.setAbsolutePosition(xCheckbox, document.top()-yCheckbox);
				document.add(img2);
			}
			yCheckbox+=15;
		}
		
		PdfContentByte cb = writer.getDirectContent();
		
		List<Phrase> phrases = new ArrayList<>();
		
		phrases.add(new Phrase("0-60 juta /tahun ", font));
		phrases.add(new Phrase("61-120 juta /tahun ", font));
		phrases.add(new Phrase("121-180 juta /tahun ", font));
		phrases.add(new Phrase("181-240 juta /tahun ", font));
		phrases.add(new Phrase(">240 juta /tahun ", font));
		
		//kordinat x,y untuk text checkbox
		float xText = 55;
		int yText=171+labelLines;
		
		if (isWrapText) {
			yText = yText + 15;
		}
		
		for (int i = 1; i <= 5; i++) {
			ColumnText.showTextAligned(cb, Element.ALIGN_LEFT, phrases.get(i - 1),
					xText + 20, document.top() - yText, 0);
			yText += 15;
		}
	
		for(int i=0; i<labelList.size();i++) {
			document.add(new Phrase(labelList.get(i), font));
		}
		
		document.add(new Phrase("\n\n\n\n\n\n\n11. Pilihan Produk Pembayaran	", font));
		
		//kordinat x,y untuk text answer
		float xAns=document.right()-280;
		float yAns=18;
		
		for (int i=0; i<answer.size()-1; i++) {
			if(i!=9) {
				//untuk alamat
				//Jika terlalu panjang
				int itemLine = lines.get(i);
				if (itemLine>0) {
					ColumnText ct = new ColumnText(cb);
					ct.setSimpleColumn(550, 0, xAns+20, (document.top()-yAns) + 15);
					ct.addElement(new Paragraph(answer.get(i), font));
					ct.go();
					yAns+=15;
				}
				else {
					ColumnText.showTextAligned(
						cb,
						Element.ALIGN_LEFT,
						new Phrase(answer.get(i), font),
						xAns+20, 
						document.top()-yAns, 0);
				}
				yAns+=(15*(1+itemLine));
			}
		}
		ColumnText.showTextAligned(
				cb,
				Element.ALIGN_LEFT,
				new Phrase(answer.get(answer.size()-1), font),
				xAns+20, 
				document.top()-(yAns+105), 0);
		
		return isWrapText;
	}
	
	private void declarationParagraph(Document document, PdfWriter writer, List<String> answer, boolean isWrapText) throws DocumentException, MalformedURLException, IOException{
		URL tem = getClass().getClassLoader().getResource("META-INF/resources/include/images/icon/ic_checked_box_sm.png");
		Image img = Image.getInstance(tem);
		img.scaleToFit(15, 15);
		
		URL tem2 = getClass().getClassLoader().getResource("META-INF/resources/include/images/icon/ic_unchecked_box_sm.png");
		Image img2 = Image.getInstance(tem2);
		img2.scaleToFit(15, 15);
		
		Font ffont = new Font(Font.FontFamily.UNDEFINED, 12, Font.BOLD);
		Font font = new Font(Font.FontFamily.UNDEFINED, 10, Font.NORMAL);
		
		Phrase subheader = new Phrase("Menyatakan sebagai berikut : ", ffont);
		
		document.add(subheader);
		
		String text1 = "1. Bahwa Saya mengajukan fasilitas pembiayaan kepada WOM Finance dan dengan mengisi Formulir"
				+ "\n     Permohonan Analisa Fasilitas Pembiayaan ini Pemohon bersama-sama dengan pasangan/penjamin"
				+ "\n     menyatakan bahwa seluruh informasi/data/dokumen yang diberikan Pemohon adalah benar, valid, masih"
				+ "\n     berlaku, dan sesuai dengan dokumen aslinya, yang saya lampirkan dalam formulir ini serta Pemohon"
				+ "\n     menyatakan tujuan penggunaan fasilitas pembiayaan sesuai pilihan produk pembiayaan sebagaimana"
				+ "\n     disebutkan diatas adalah BENAR.";
		
		Paragraph p1 = new Paragraph(text1, font);

		document.add(p1);
		document.add(Chunk.NEWLINE);
		
		String text2 = "2. Data-data Pemohon dapat digunakan untuk pemasaran produk WOM Finance maupun untuk dihubungi untuk"
				+ "\n     memberikan informasi mengenai program-program produk WOM Finance (jika tidak diberikan pilihan, maka"
				+ "\n     dianggap bersedia).";
		
		Paragraph p2 = new Paragraph(text2, font);

		document.add(p2);
		document.add(Chunk.NEWLINE);
		document.add(Chunk.NEWLINE);
		document.add(Chunk.NEWLINE);
		
		String text3 = "3. Pemohon bersama-sama dengan pasangan/penjamin menyetujui informasi/data/dokumen yang telah"
				+ "\n     disampaikan kepada WOM Finance digunakan untuk analisa pembiayaan termasuk namun tidak terbatas"
				+ "\n     melakukan verifikasi data melalui sistem layanan informasi keuangan yang diatur berdasarkan peraturan yang"
				+ "\n     berlaku dalam rangka pemberian fasilitas pembiayaan.";
		
		Paragraph p3 = new Paragraph(text3, font);

		document.add(p3);
		document.add(Chunk.NEWLINE);
		
		String text4 = "4. Menyetujui bahwa Formulir Permohonan Analisa Fasilitas Pembiayaan ini adalah formulir pengajuan awal"
				+ "\n     dalam proses persetujuan fasilitas pembiayaan. sehingga WOM Finance dapat meminta informasi susulan"
				+ "\n     dan/atau data lain yang diperlukan dalam proses persetujuan fasilitas pembiayaan.";
		
		Paragraph p4 = new Paragraph(text4, font);

		document.add(p4);
		document.add(Chunk.NEWLINE);
		
		List<Integer> checkList=addCheckLines(answer);
		int lines = 0;
		for (int i=0; i<checkList.size(); i++) {
			int itemLine = checkList.get(i);
			lines = lines+(15*itemLine);
		}
		
		float x = 55;
		int y=250-lines;
		
		if (isWrapText) {
			y = y - 15;
		}
		
		img.setAbsolutePosition(55, document.bottom()+y );
		document.add(img);
	
		img2.setAbsolutePosition(55, document.bottom()+(y-15));
		document.add(img2);

		
		PdfContentByte cb = writer.getDirectContent();
		
		List<Phrase> phrases = new ArrayList<>();
		
		phrases.add(new Phrase("setuju", font));
		phrases.add(new Phrase("tidak setuju", font));
		
		y=255-lines;
		if (isWrapText) {
			y = y - 15;
		}
		for (int i = 1; i <= 2; i++) {
			ColumnText.showTextAligned(cb, Element.ALIGN_LEFT, phrases.get(i - 1),
					x + 20, document.bottom() + y, 0);
			y -= 15;
		}
	}
	
	public List<String> getDataAnswer(String uuidTask, AuditContext auditContext) {
		String nama = StringUtils.EMPTY;
		String nik = StringUtils.EMPTY;
		String alamat = StringUtils.EMPTY;
		String tempatLahir = StringUtils.EMPTY;
		String tanggalLahir = StringUtils.EMPTY;
		String kewarganegaraan = "WNI";
		String pekerjaan = StringUtils.EMPTY;
		String jenisKelamin = StringUtils.EMPTY;
		String statusPerkawinan = StringUtils.EMPTY;
		String penghasilan = StringUtils.EMPTY;
		String pilihanProduk = StringUtils.EMPTY;
		
		List<Map<String, Object>> taskD = new ArrayList<>();
		
		/*Get Task Survey*/
		Object[][] paramTaskSurvey = { {Restrictions.eq(paramUuidTaskH, Long.parseLong(uuidTask))} };
		TrTaskH taskHSurvey = this.getManagerDAO().selectOne(TrTaskH.class, paramTaskSurvey);
		
		/*Get Data from Task Lead*/
		nama = taskHSurvey.getCustomerName();
		
		//check task source polo/cae/others
		Gson gson = new Gson();
		if (StringUtils.isNotBlank(taskHSurvey.getTaskIdPolo())) {
			Object params[][] = { {Restrictions.eq("taskIdPolo", taskHSurvey.getTaskIdPolo())}, {Restrictions.eq("isSuccess", "1")},
					{Restrictions.eq("groupTaskId", this.getGrouptTaskId(uuidTask))} };
			TblPoloData dataPolo = this.getManagerDAO().selectOne(TblPoloData.class, params);
			if (dataPolo != null) {
				StringBuilder queryGetProduct = new StringBuilder();
				queryGetProduct.append(" SELECT PRODUCT_CATEGORY_NAME ");
				queryGetProduct.append(" FROM   TBL_PRODUCT_CATEGORY TPC WITH(NOLOCK) ");
				queryGetProduct.append(" WHERE  PRODUCT_CATEGORY_CODE = :productCategoryCode");
				
				String value = (String)this.getManagerDAO().selectOneNativeString(queryGetProduct.toString(), new Object[][] {{"productCategoryCode", dataPolo.getProductCategoryCode()}});
				if (StringUtils.isNotBlank(value)) {
					pilihanProduk = value;
				}
				
				String requestJson = dataPolo.getJsonRequest();
				AddTaskPoloRequest dataRequest = gson.fromJson(requestJson, AddTaskPoloRequest.class);
				nama = StringUtils.isNotBlank(dataRequest.getCustomer_name())? dataRequest.getCustomer_name():StringUtils.EMPTY;
				nik = StringUtils.isNotBlank(dataRequest.getNik_ktp())? dataRequest.getNik_ktp():StringUtils.EMPTY;
				alamat = StringUtils.isNotBlank(dataRequest.getAlamat_legal())? dataRequest.getAlamat_legal():StringUtils.EMPTY;
				tempatLahir = StringUtils.isNotBlank(dataRequest.getTempat_lahir())? dataRequest.getTempat_lahir():StringUtils.EMPTY;
				tanggalLahir = StringUtils.isNotBlank(dataRequest.getTanggal_lahir())? dataRequest.getTanggal_lahir():StringUtils.EMPTY;
				
//				kewarganegaraan = StringUtils.isNotBlank(dataRequest.get("customer_name"))? dataRequest.get("customer_name"):StringUtils.EMPTY;
//				pekerjaan = StringUtils.isNotBlank(dataRequest.get("customer_name"))? dataRequest.get("customer_name"):StringUtils.EMPTY;
//				jenisKelamin = StringUtils.isNotBlank(dataRequest.get("customer_name"))? dataRequest.get("customer_name"):StringUtils.EMPTY;
				String codeStatusPerkawinan = dataRequest.getMarital_status();
				statusPerkawinan = getDescriptionFromLov(codeStatusPerkawinan, "MARITAL_STAT");
				
				penghasilan = StringUtils.isNotBlank(dataRequest.getPenghasilan())? dataRequest.getPenghasilan():StringUtils.EMPTY;
			} 
		} else if (StringUtils.isNotBlank(taskHSurvey.getOrderNoCae())) {
			Object params[][] = {{Restrictions.eq("orderNoCae", taskHSurvey.getOrderNoCae())}, {Restrictions.eq("isSuccess", "1")},
					{Restrictions.eq("groupTaskId", this.getGrouptTaskId(uuidTask))} };
			TblCaeData dataCae = this.getManagerDAO().selectOne(TblCaeData.class, params);
			if (dataCae != null) {
				StringBuilder queryGetProduct = new StringBuilder();
				queryGetProduct.append(" SELECT PRODUCT_CATEGORY_NAME ");
				queryGetProduct.append(" FROM   TBL_PRODUCT_CATEGORY TPC WITH(NOLOCK) ");
				queryGetProduct.append(" WHERE  PRODUCT_CATEGORY_CODE = :productCategoryCode");
				
				String value = (String)this.getManagerDAO().selectOneNativeString(queryGetProduct.toString(), new Object[][] {{"productCategoryCode", dataCae.getProductCategoryCode()}});
				if (StringUtils.isNotBlank(value)) {
					pilihanProduk = value;
				}
				
				String requestJson = dataCae.getJsonRequest();
				AddTaskCAERequest dataRequest = gson.fromJson(requestJson, AddTaskCAERequest.class);
				nama = StringUtils.isNotBlank(dataRequest.getCustomerName())? dataRequest.getCustomerName():StringUtils.EMPTY;
				nik = StringUtils.isNotBlank(dataRequest.getNikKtp())? dataRequest.getNikKtp():StringUtils.EMPTY;
				alamat = StringUtils.isNotBlank(dataRequest.getAlamatLegal())? dataRequest.getAlamatLegal():StringUtils.EMPTY;
				tempatLahir = StringUtils.isNotBlank(dataRequest.getTempatLahir())? dataRequest.getTempatLahir():StringUtils.EMPTY;
				tanggalLahir = StringUtils.isNotBlank(dataRequest.getTanggalLahir())? dataRequest.getTanggalLahir():StringUtils.EMPTY;
//				kewarganegaraan = StringUtils.isNotBlank(dataRequest.get)? dataRequest.get("customer_name"):StringUtils.EMPTY;
				
				String codePekerjaan = dataRequest.getProfessionName();
				String codeJenisKelamin = dataRequest.getJenisKelamin();
				String codeStatusPerkawinan = dataRequest.getStatusPernikahan();
				pekerjaan = getDescriptionFromLov(codePekerjaan, "REF_PROFESSION");
				jenisKelamin = getDescriptionFromLov(codeJenisKelamin, "GENDER");
				statusPerkawinan = getDescriptionFromLov(codeStatusPerkawinan, "MARITAL_STAT");
				
				penghasilan = StringUtils.isNotBlank(dataRequest.getPenghasilan())? dataRequest.getPenghasilan():StringUtils.EMPTY;
			} 
		} else {
			//cari task lead nya dulu
			StringBuilder queryTaskLead = new StringBuilder();
			queryTaskLead.append(" SELECT lead.UUID_TASK_H ");
			queryTaskLead.append(" FROM   MS_GROUPTASK msgt with(nolock) ");
			queryTaskLead.append(" JOIN   TR_TASK_H lead with(nolock) on msgt.GROUP_TASK_ID = lead.UUID_TASK_H ");
			queryTaskLead.append(" WHERE  msgt.uuid_task_h = :uuidTaskSurvey ");
			
			BigInteger uuidTaskLead = (BigInteger) this.getManagerDAO().selectOneNativeString(queryTaskLead.toString(), new Object[][] {{"uuidTaskSurvey", uuidTask}});
			if(uuidTaskLead != null) {
				Object[][] param2 ={{paramUuidTaskH, uuidTaskLead}};
				StringBuilder query = new StringBuilder();
				query.append(" SELECT ttd.UUID_QUESTION, msq.REF_ID, ISNULL(ttd.TEXT_ANSWER, ttd.INT_TEXT_ANSWER) as textAnswer, ISNULL(ttd.OPTION_TEXT, ttd.INT_OPTION_TEXT) as optionAnswer ");
				query.append(" FROM TR_TASK_D ttd with (nolock) ");
				query.append(" JOIN MS_QUESTION msq with (nolock) ON ttd.UUID_QUESTION = msq.UUID_QUESTION ");
				query.append(" WHERE ttd.UUID_TASK_H = :uuidTaskH ");
				List<Map<String, Object>> taskDLead = this.getManagerDAO().selectAllNativeString(query.toString(), param2);
				if (taskDLead != null && !taskDLead.isEmpty()) {
					taskD.addAll(taskDLead);
				}
			}
		}
				
		/*Get Data From Task Survey*/
		Object[][] param2 ={{paramUuidTaskH, uuidTask}};
		StringBuilder query = new StringBuilder();
		query.append(" SELECT ttd.UUID_QUESTION, msq.REF_ID, ISNULL(ttd.TEXT_ANSWER, ttd.INT_TEXT_ANSWER) as textAnswer, ISNULL(ttd.OPTION_TEXT, ttd.INT_OPTION_TEXT) as optionAnswer ");
		query.append(" FROM TR_TASK_D ttd with (nolock) ");
		query.append(" JOIN MS_QUESTION msq with (nolock) ON ttd.UUID_QUESTION = msq.UUID_QUESTION ");
		query.append(" WHERE ttd.UUID_TASK_H = :uuidTaskH ");
		
		List<Map<String, Object>> taskDSurvey = this.getManagerDAO().selectAllNativeString(query.toString(), param2);
		if (taskDSurvey != null && !taskDSurvey.isEmpty()) {
			taskD.addAll(taskDSurvey);
		}
		
		for (Map<String, Object> map : taskD) {
			String refId = map.get("d1").toString();
			String value = StringUtils.EMPTY;
			if (map.get("d2") != null && StringUtils.isNotBlank(map.get("d2").toString())) {
				value = map.get("d2").toString();
			}
			
			if (StringUtils.isBlank(value) && map.get("d3") != null && StringUtils.isNotBlank(map.get("d3").toString())) {
				value = map.get("d3").toString();
			} 
			
			if (StringUtils.isBlank(value)) {
				continue;
			}
			
			if (GlobalVal.NO_IDENTITAS.equals(refId)
					|| GlobalVal.STG_NIK.equals(refId)) {
				nik = value;
			} else if (GlobalVal.TMPT_LAHIR.equals(refId)
					|| GlobalVal.STG_BIRTH_PLACE.equals(refId)
					|| GlobalVal.REF_PRESURVEY_TEMPAT_LAHIR.equals(refId)) {
				tempatLahir = value;
			} else if (GlobalVal.TGL_LAHIR.equals(refId)
					|| GlobalVal.STG_BIRTH_DT.equals(refId)
					|| GlobalVal.REF_PRESURVEY_TGL_LAHIR.equals(refId)) {
				tanggalLahir = value;
			} else if (GlobalVal.STG_LEGAL_ADDR.equals(refId)
					|| GlobalVal.REF_PRESURVEY_LEGAL_ALAMAT.equals(refId)
					|| GlobalVal.PMHN_NAMA_JALAN.equals(refId)
					|| GlobalVal.STG_LEGAL_ADDR_PMHON.equals(refId)) {
				alamat = value;
			} else if (GlobalVal.TTL_BRUTO.equalsIgnoreCase(refId)
					|| GlobalVal.STG_PENGHASILAN.equals(refId)) {
				penghasilan = value;
			} else if(GlobalVal.STATUS_PERNIKAHAN.equalsIgnoreCase(refId)
					|| GlobalVal.STG_MARITAL_STAT.equals(refId)) {
				statusPerkawinan = value;
			} else if (GlobalVal.KRJ_PMHN_PROFESI.equalsIgnoreCase(refId)) {
				pekerjaan = value;
			} else if (GlobalVal.GENDER.equalsIgnoreCase(refId)) {
				jenisKelamin = value;
			} else if (GlobalVal.PRODUCT_CATEGORY.equals(refId)
					|| GlobalVal.STG_PROD_CODE.equals(refId)
					|| GlobalVal.STG_PRODUCT.equals(refId)) {
				pilihanProduk = value;
			}  else if (GlobalVal.SVY_NATIONALITY.equals(refId)) {
				kewarganegaraan = value;
			}
			
		}
		
		List<String> answer = new ArrayList<>();
		
		answer.add(nama);
		answer.add(nik);
		answer.add(alamat);
		answer.add(tempatLahir);
		answer.add(tanggalLahir);
		answer.add(kewarganegaraan);
		answer.add(pekerjaan);
		answer.add(jenisKelamin);
		answer.add(statusPerkawinan);
		
		int flagPenghasilan = 0;
		double dataPenghasilan = Double.parseDouble(penghasilan);
		if(dataPenghasilan*12 >=0 && dataPenghasilan*12 <61000000) {
			flagPenghasilan=1;
		} else if (dataPenghasilan*12 >= 61000000 && dataPenghasilan*12 < 121000000) {
			flagPenghasilan=2;
		} else if (dataPenghasilan*12 >= 121000000 && dataPenghasilan*12 < 181000000 ) {
			flagPenghasilan=3;
		} else if (dataPenghasilan*12 >= 181000000 && dataPenghasilan*12 <= 240000000) {
			flagPenghasilan=4;
		} else if (dataPenghasilan*12 > 240000000) {
			flagPenghasilan=5;
		}
		answer.add(String.valueOf(flagPenghasilan));
		answer.add(pilihanProduk);
		
		return answer;
	}
	
	private String getDescriptionFromLov(String code, String lovGroup) {
		if (StringUtils.isBlank(code)) {
			return StringUtils.EMPTY;
		}
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT top 1 DESCRIPTION FROM MS_LOV WITH(NOLOCK) ");
		query.append(" WHERE CODE = :code AND LOV_GROUP = :lovGroup AND IS_ACTIVE='1' ");
		
		String result = (String) this.getManagerDAO().selectOneNativeString(query.toString(), new Object[][] {{"code", code}, {"lovGroup", lovGroup}});
		if (StringUtils.isBlank(result)) {
			return StringUtils.EMPTY;
		} else {
			return result;
		}
	}
	
	public int countWord(String answer) {
		
		int count = 0;
		//jumlah max huruf
		int width = 50;
		int countWord = 0;
		
		String trim = answer;
		String[] word = trim.split("[ .,]+");
		count = word.length;
		int newLines = 0;
		for(int t=0; t<=count-1; t++) {
			countWord = countWord + word[t].length() + 2;
			if (countWord > width) {
				newLines++;
				countWord = 0;
				t--;
			}
		}
		
		return newLines;
	}
	
	public List<Integer> addCheckLines(List<String> answer) {
		List<Integer> newLines = new ArrayList<>();
		for (int i=0; i<answer.size()-1; i++) {
			newLines.add(countWord(answer.get(i)));
		}
		return newLines;
	}

	@Override
	public String retrieveResultType(String uuidTaskH, String formName, String flagFinal, AuditContext auditContext) {
		return this.retrieveResultTypeGlobal(uuidTaskH, formName, null, null, flagFinal);
	}
	
	@Override
	public Object[] getRegion(Long uuidBranch) {
		Object[] region = (Object[]) this.getManagerDAO().selectOneNativeString("SELECT TOP(1) mb.UUID_REGION, mr.REGION_NAME FROM MS_BRANCH mb WITH (NOLOCK) JOIN MS_REGION mr WITH (NOLOCK) ON mb.UUID_REGION = mr.UUID_REGION WHERE mb.UUID_BRANCH = :uuidBranch", new Object[][] { {"uuidBranch", uuidBranch} });
		return region;
	}
}
