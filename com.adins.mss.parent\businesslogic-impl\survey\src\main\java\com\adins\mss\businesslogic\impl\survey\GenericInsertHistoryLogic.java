package com.adins.mss.businesslogic.impl.survey;

import java.sql.Types;
import java.util.Date;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.jfree.util.Log;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.InsertHistoryLogic;
import com.adins.mss.model.CheckCustomerHistory;
import com.adins.mss.model.TblCaeData;
import com.adins.mss.model.TblOtsData;
import com.adins.mss.model.TblPoloData;
import com.adins.mss.services.model.common.AddTaskCAERequest;
import com.adins.mss.services.model.common.AddTaskCAEResponse;
import com.adins.mss.services.model.common.AddTaskOTSRequest;
import com.adins.mss.services.model.common.AddTaskOTSResponse;
import com.adins.mss.services.model.common.AddTaskPoloResponse;
import com.adins.mss.services.model.newconfins.CheckCustomerRequest;
import com.google.gson.Gson;

public class GenericInsertHistoryLogic extends BaseLogic implements InsertHistoryLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericInsertHistoryLogic.class);
	private Gson gson = new Gson();

	private String usrCrtSystem = "SYSTEM";
	
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public CheckCustomerHistory insertCheckCustomerHistory(String userId, CheckCustomerRequest checkCustomerReq, String customerNo,
			boolean isWise, String notes, String retry, String jsonRequest, String jsonResponse) {
		CheckCustomerHistory newHistory = new CheckCustomerHistory();
		newHistory.setNik(checkCustomerReq.getNik());
		newHistory.setName(checkCustomerReq.getName());
		newHistory.setBirthPlace(checkCustomerReq.getBirthPlace());
		newHistory.setBirthDays(checkCustomerReq.getBirthDate());
		if (isWise) {
			newHistory.setCustomerNo(customerNo);
			newHistory.setIsWise("1");
			newHistory.setIsMobile("0");
			newHistory.setNotesWise(notes);
			newHistory.setRetry(retry);
		} else {
			newHistory.setOrderNoTemp(customerNo);
			newHistory.setIsWise("0");
			newHistory.setIsMobile("1");
		}
		newHistory.setRequestDukcapil(jsonRequest);
		newHistory.setResponseDukcapil(jsonResponse);
		newHistory.setUsrCrt(userId);
		newHistory.setDtmCrt(new Date());
		this.getManagerDAO().insert(newHistory);
		return newHistory;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public TblPoloData insertPoloHistory(String request, String response, int flag, String taskIdPolo, long groupTaskId, String productCategoryCode, String isFlagKawanInternal) {
		TblPoloData polo = new TblPoloData();
		polo.setDtmCrt(new Date());
		polo.setUsrCrt(usrCrtSystem);
		polo.setFlag(String.valueOf(flag));
		polo.setTaskIdPolo(taskIdPolo);
		polo.setJsonRequest(request);
		polo.setJsonResponse(response);
		polo.setGroupTaskId(groupTaskId);
		polo.setProductCategoryCode(productCategoryCode);
		polo.setIsFlagKawanInternal(isFlagKawanInternal);
		
		AddTaskPoloResponse res = gson.fromJson(response, AddTaskPoloResponse.class);
		polo.setIsSuccess(String.valueOf(res.getCode()));
		
		this.getManagerDAO().insert(polo);
		
		if(polo.getIsSuccess().equalsIgnoreCase("1")
			|| polo.getIsFlagKawanInternal().equalsIgnoreCase("1")) {
			createReportKawanInternal("TBL_POLO_DATA", polo.getUuidPoloData());
		}
		return polo;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public TblCaeData insertCAEHistory(String request, String response, String isIA, String orderNoCae, long groupTaskId,
			String productCategoryCode, String isFlagKawanInternal) {
		AddTaskCAERequest task = gson.fromJson(request, AddTaskCAERequest.class);
		TblCaeData data = new TblCaeData();
		data.setDtmCrt(new Date());
		data.setUsrCrt(usrCrtSystem);
		data.setIsIa(task.getIsInstantApproval());
		data.setOrderNoCae(orderNoCae);
		data.setJsonRequest(request);
		data.setJsonResponse(response);
		data.setGroupTaskId(groupTaskId);
		data.setProductCategoryCode(productCategoryCode);
		data.setIsFlagKawanInternal(isFlagKawanInternal);
		
		AddTaskCAEResponse res = gson.fromJson(response, AddTaskCAEResponse.class);
		data.setIsSuccess(String.valueOf(res.getCode()));
		
		this.getManagerDAO().insert(data);
		
		if(data.getIsSuccess().equalsIgnoreCase("1")
			|| data.getIsFlagKawanInternal().equalsIgnoreCase("1")) {
			createReportKawanInternal("TBL_CAE_DATA", data.getUuidCaeData());
		}
		return data;
	}

	@Transactional(propagation = Propagation.REQUIRES_NEW)
	@Override
	public TblOtsData insertOTSHistory(String request, String response, String groupTaskId) {
		AddTaskOTSRequest task = gson.fromJson(request, AddTaskOTSRequest.class);
		AddTaskOTSResponse res = gson.fromJson(response, AddTaskOTSResponse.class);
		
		TblOtsData data = new TblOtsData();
		data.setDtmCrt(new Date());
		data.setUsrCrt(usrCrtSystem);
		data.setFlagSourceOts(task.getFlagSource());
		data.setOrderNoOts(task.getMobileAssignmentId());
		data.setJsonRequest(request);
		data.setJsonResponse(response);
		long groupTaskIdVal = 0;
		if (StringUtils.isNotBlank(groupTaskId)) {
			groupTaskIdVal = Long.valueOf(groupTaskId);
		}
		data.setGroupTaskId(groupTaskIdVal);
		data.setIsSuccess(String.valueOf(res.getCode()));
		
		this.getManagerDAO().insert(data);
		
		return data;
	}
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	private void createReportKawanInternal(String refTable, long refUUID) {
		LOG.info("createReportKawanInternal({},{})", refTable, refUUID);
		try {
			Object[][] param = { {1, refTable}, {2, refUUID} };
			String query = "{ call [dbo].[REPORT_KAWAN_INTERNAL_INSERT] (?, ?) }";
			this.getManagerDAO().callProcedureNativeString(query, param, null);
			LOG.info("Success Report Kawan Internal Generated");
		} catch (Exception e) {
			LOG.info("Error When createReportKawanInternal, skip process...");
		}
	}
}
