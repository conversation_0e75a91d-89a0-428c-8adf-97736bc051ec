package com.adins.mss.businesslogic.impl.survey;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import javax.validation.constraints.Null;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.common.TaskServiceLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.PrintPOLogic;
import com.adins.mss.model.TblPo;
import com.adins.mss.model.TrTaskH;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericPrintPOLogic extends BaseLogic implements PrintPOLogic {
	private static final Logger LOG = LoggerFactory.getLogger(GenericPrintPOLogic.class);	
	
	@Autowired
	private MessageSource messageSource;

	private CommonLogic commonLogic;
	private IntFormLogic intFormLogic;
	
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
		
	
	@Override
	public List listPrintPO(Object[][] params,AuditContext callerId) {				
		List result = null;				
		
		for (int i = 0;  i < params.length; i++) {
			params[i][1] = "".equalsIgnoreCase(params[i][1].toString()) ? "%" : params[i][1];
		}
		
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder(params, paramsStack);	
		StringBuilder ordersQueryString = this.sqlPagingOrderBuilder((String) ((Object[][]) params)[6][1], callerId);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT * from ( ")				
				.append("SELECT a.*, ROW_NUMBER() OVER ( ")
				.append("ORDER BY rownum) AS recnum FROM ( ")
				.append("select c.*, ")
				.append("ROW_NUMBER() OVER ( ")
				.append(ordersQueryString)
				.append(") AS rownum ")
				.append("from ( ")
				.append("SELECT TP.ID_PO, TTH.CUSTOMER_NAME, TTH.APPL_NO, CONVERT(varchar, TTH.DTM_CRT, 120) AS DATE, MB.BRANCH_NAME, MS.STATUS_TASK_DESC, TTH.DTM_CRT, TP.STATUS FROM TBL_PO TP WITH (NOLOCK)")
				.append("JOIN TR_TASK_H TTH WITH (NOLOCK) ")
				.append("ON TP.UUID_TASK_H = TTH.UUID_TASK_H ")
				.append("JOIN MS_STATUSTASK MS ")
				.append("ON TTH.UUID_STATUS_TASK = MS.UUID_STATUS_TASK ")
				.append("JOIN MS_BRANCH MB ")
				.append("ON TTH.UUID_BRANCH = MB.UUID_BRANCH ")
				.append("WHERE 1=1 ")				
				.append(paramsQueryString)
				.append(") c ")
				.append(") a WHERE a.rownum <= :end ")	
				.append(") b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[7][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[8][1]});
				
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
	    
		return result;
	}	

	@Override
	public Map<String, Object> getPrintPO(String idPO) {
		Map<String, Object> result = new HashMap<String, Object>();
		Object [][] param = { {Restrictions.eq("idPo", Long.valueOf(idPO))} };
		TblPo tblPO = this.getManagerDAO().selectOne(TblPo.class, param);
		Object [][] params = { {"id", idPO} };
		Object odrno = this.getManagerDAO().selectOneNativeString("select tth.APPL_NO from TBL_PO tp with (nolock) " + 
				"join TR_TASK_H tth with (nolock) " + 
				"on tp.UUID_TASK_H = tth.UUID_TASK_H " + 
				"where tp.ID_PO = :id ", params);
		
		String fileName = "E-PURCHASEORDER-"+odrno.toString()+".pdf";
		result.put("exp", tblPO.getPo());
		result.put("fileName", fileName);
		return result;
	}

	@Override
	public Integer countListPrint(Object[][] params, AuditContext callerId) {
		Integer result = 0;
		
		for (int i = 0;  i < params.length; i++) {
			params[i][1] = params[i][1].toString().isEmpty() ? "%" : params[i][1];
		}
		
		Stack<Object[]> paramsStack = new Stack<>();
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);			
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("select count(1) FROM TBL_PO TP WITH (NOLOCK)")
			.append(" JOIN TR_TASK_H TTH WITH (NOLOCK)")
			.append(" ON TP.UUID_TASK_H = TTH.UUID_TASK_H")
			.append(" JOIN MS_STATUSTASK MS")
			.append(" ON TTH.UUID_STATUS_TASK = MS.UUID_STATUS_TASK")
			.append(" JOIN MS_BRANCH MB")
			.append(" ON TTH.UUID_BRANCH = MB.UUID_BRANCH")
			.append(" WHERE 1=1")
			.append(paramsQueryString);			
				
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		
		result = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);		
		return result;
	}
	
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null){
			return new StringBuilder();
		}
		
		StringBuilder sb = new StringBuilder();	
		
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append(" and TTH.CUSTOMER_NAME LIKE '%'+ :customerName +'%' ");
			paramStack.push(new Object[]{"customerName", (String) params[0][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append(" and TTH.APPL_NO LIKE '%'+ :applNo +'%' ");
			paramStack.push(new Object[]{"applNo", (String) params[1][1]});
		}			
		
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append(" and MB.UUID_BRANCH = :uuidBranch ");
			paramStack.push(new Object[]{"uuidBranch", (String) params[2][1]});
		}
		
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append(" and MS.UUID_STATUS_TASK = :statusTask ");
			paramStack.push(new Object[]{"statusTask", (String) params[3][1]});
		}				
		
		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd");
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = new Date();		
		String currentDate = dateFormat.format(date);		
		// ---ASSIGN_DATE
		DateTime startDate = null, endDate = null;
		if (!StringUtils.equals("%", (String) params[4][1])) {
			startDate = formatter.parseDateTime((String) params[4][1]);
			startDate = startDate.withMillisOfDay(0);			
		}
		if (!StringUtils.equals("%", (String) params[5][1])) {
			endDate = formatter.parseDateTime((String) params[5][1]);
			endDate = endDate.plusDays(1).minusMillis(3);
		}
		
		if (startDate != null && endDate != null) {
			sb.append("AND COALESCE(TTH.DTM_CRT, '1990-01-01 00:00:00') BETWEEN :assignDateStart AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateStart",startDate.toDate() });
			paramStack.push(new Object[] { "assignDateEnd",endDate.toDate() });
		} 
		else if (startDate == null && endDate != null) {
			sb.append("AND COALESCE(TTH.DTM_CRT, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateEnd",endDate.toDate() });
		} 
		else if (startDate != null && endDate == null) {
			sb.append("AND COALESCE(TTH.DTM_CRT, '1990-01-01 00:00:00') BETWEEN :assignDateStart AND :currentDate ");
			paramStack.push(new Object[] { "assignDateStart",startDate.toDate() });
			paramStack.push(new Object[] { "currentDate",currentDate });
		} 
		else if(startDate == null && endDate == null){
			sb.append("AND COALESCE(TTH.DTM_CRT, '1990-01-01 00:00:00') BETWEEN '1990-01-01 00:00:00' AND :currentDate ");
			paramStack.push(new Object[] { "currentDate",currentDate });
		}
				
		return sb;
	}
	
	/*
	 * 1 CUSTOMER_NAME 2 APPL_NO 3 DTM_CRT 4 BRANCH_NAME 5 STATUS_TASK_DESC
	 */
	private StringBuilder sqlPagingOrderBuilder(String orders, AuditContext callerId) {
		if (StringUtils.isEmpty(orders) || "0".equals(orders)) {
			orders = "1A"; // set default order by customer name ASC
		}

		String[] orderCols = { "CUSTOMER_NAME", "APPL_NO",
				"DTM_CRT", "BRANCH_NAME", "STATUS_TASK_DESC"};
		StringBuilder queryOrder = new StringBuilder(" ORDER BY ");

		int colIdx = NumberUtils.toInt(StringUtils.substring(orders, 0,
				StringUtils.length(orders) - 1));
		colIdx = (colIdx > 0) ? colIdx - 1 : colIdx;
		queryOrder.append(orderCols[colIdx]);
		if (StringUtils.endsWith(orders, "D")) {
			queryOrder.append(" DESC");
		}
		else {
			queryOrder.append(" ASC");
		}

		return queryOrder;
	}

	@Transactional
	@Override
	public void updatePODownload(String uuidTaskH) {
		TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, Long.valueOf(uuidTaskH));
		
		if(StringUtils.isBlank(taskH.getEpoDownloaded())) {
			taskH.setEpoDownloadDate(new Date());
			taskH.setEpoDownloaded("1");
			taskH.setDtmUpd(new Date());
			taskH.setUsrUpd("EPODOWNLOADED");

			this.getManagerDAO().update(taskH);
		}
	}

	@Transactional
	@Override
	public void updatePODownloadIdPo(String idPo) {
		Object [][] param = { {Restrictions.eq("idPo", Long.valueOf(idPo))} };
		TblPo tblPO = this.getManagerDAO().selectOne(TblPo.class, param);

		TrTaskH taskH = tblPO.getTrTaskH();
		
		if(StringUtils.isBlank(taskH.getEpoDownloaded())) {
			taskH.setEpoDownloadDate(new Date());
			taskH.setEpoDownloaded("1");
			taskH.setDtmUpd(new Date());
			taskH.setUsrUpd("EPODOWNLOADED");

			this.getManagerDAO().update(taskH);
		}
	}
	
}
