package com.adins.mss.businesslogic.impl.survey;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.ReAssignLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericReAssignLogic extends BaseLogic implements ReAssignLogic{
	
	@Override
	public Map getUserList(String[][] params, String[][] paramsCount, AuditContext auditContext) {
		Map mapResult = new HashMap();
		Integer totalList = 0;
        List queryResultList = this.getManagerDAO().selectAllNative("task.reassign.getUsers", params, null);
        
        if (paramsCount != null) {
        	totalList = (Integer) this.getManagerDAO().selectOneNative("task.reassign.getUsersCnt", paramsCount);
        }
        
        mapResult.put(GlobalKey.MAP_RESULT_LIST, queryResultList);
        mapResult.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(totalList));
        return mapResult;
	}

	@Override
	public Map getTaskList(long idSvy, int pageNumber, int pageSize, String[][] params, AmMsuser spv, boolean isNeedComboList, AuditContext auditContext){
		Map mapResult = new HashMap();
		List listOtherSvy = new ArrayList();
		List listPrior = new ArrayList();
		List listFormCombo = new ArrayList();
		
		if (isNeedComboList) {
			Object paramsSvy[][] = {{"uuidSpv", spv.getUuidMsUser()}, {"uuidSvy", idSvy}};
			listOtherSvy = this.getManagerDAO().selectAllNative("task.reassign.getExcUserList", paramsSvy, null);
	
			Object[][] paramPrior = {{Restrictions.eq("isActive","1")}};
			String[][] orderPrior = {{"uuidPriority", GlobalVal.ROW_ORDER_ASC}};
			Map resultMapPrior = this.getManagerDAO().list(MsPriority.class, paramPrior, orderPrior);
			listPrior = (List) resultMapPrior.get(GlobalKey.MAP_RESULT_LIST);
			
			//Combo List Form
			Object[][] paramsForm = { { Restrictions.eq("isActive", "1") }, 
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",spv.getAmMssubsystem().getUuidMsSubsystem())}};
			Map<String, Object> mapForm = this.getManagerDAO().list(MsForm.class, paramsForm, null);
			listFormCombo = (List<MsForm>) mapForm.get(GlobalKey.MAP_RESULT_LIST);
		}
		
		MsStatustask stat = this.getManagerDAO().selectOne(MsStatustask.class,
				new Object[][] {{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", spv.getAmMssubsystem().getUuidMsSubsystem()) },
								{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION) } });

		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		
		condition.append(" and h.amMsuser.uuidMsUser=:uuidMsUser");
		condition.append(" and h.msStatustask.uuidStatusTask=:uuidStatusTask");
		paramMap.put("uuidMsUser", idSvy);
		paramMap.put("uuidStatusTask", stat.getUuidStatusTask());
		
		if (StringUtils.isNotBlank(params[0][1]) && !"%".equals(params[0][1])) {
			condition.append(" and h.customerName LIKE :customerName");
			paramMap.put("customerName", "%" + params[0][1] + "%");
		}
		if (StringUtils.isNotBlank(params[1][1]) && !"%".equals(params[1][1])) {
			condition.append(" and h.taskId LIKE :taskId");
			paramMap.put("taskId", "%" + params[1][1] + "%");
		}
		if (StringUtils.isNotBlank(params[2][1]) && !"%".equals(params[2][1])) {
			condition.append(" and h.msForm.uuidForm = :uuidForm");
			paramMap.put("uuidForm", Long.valueOf(params[2][1]));
		}

		StringBuilder orderQuery = new StringBuilder();
		orderQuery.append(" order by h.taskId ASC");
		
		Map<String, Object> resultMapTask = this.getManagerDAO().selectAll(
				"from TrTaskH h join fetch h.msForm mf join fetch h.msPriority mp join fetch h.msStatustask ms join fetch h.amMsuser where 1=1"
						+ condition.toString() + orderQuery.toString(),
				"select count(*) from TrTaskH h join h.msForm mf join h.msPriority mp join h.msStatustask ms join h.amMsuser where 1=1"
						+ condition.toString(),
				paramMap, pageNumber, pageSize);
		List<TrTaskH> listTask = (List<TrTaskH>) resultMapTask.get(GlobalKey.MAP_RESULT_LIST);

		resultMapTask.put(GlobalKey.MAP_RESULT_LIST, listTask);
		mapResult.put("listOtherSvy", listOtherSvy);
		mapResult.put("listPrior", listPrior);
		mapResult.put("resultMapTask", resultMapTask);
		mapResult.put("listFormCombo", listFormCombo);
		return mapResult;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void saveReAssign(String idTasks, long idSelectedSvy, AmMsuser userLogin, AuditContext auditContext) {
		String idTask[] = idTasks.split(",");
		if (ArrayUtils.isNotEmpty(idTask)) {
	        Object[][] params = {
	                { Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION) },
	                { Restrictions.eq("amMssubsystem.uuidMsSubsystem", userLogin.getAmMssubsystem().getUuidMsSubsystem()) } };
	        MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params);
	        AmMsuser selectedSurveyor = this.getManagerDAO().selectOne(AmMsuser.class, idSelectedSvy);
		    
			for (int i = 0; i < idTask.length; i++) {
				if (idTask[i].isEmpty()){
					continue;
				}
				String[] id = idTask[i].split(";");
				TrTaskH obj = this.getManagerDAO().selectOne(TrTaskH.class, Long.valueOf(id[0]));
				if ("?" != id[1] ) {
					MsPriority pr = this.getManagerDAO().selectOne(MsPriority.class, Long.valueOf(id[1]));
					obj.setMsPriority(pr);
				}
				this.updateAssignOut(obj, selectedSurveyor, msStatustask, auditContext);
				this.insertTaskHistory(obj, GlobalVal.NOTES_ASSIGN_OUT, userLogin.getFullName(),
				        selectedSurveyor.getFullName(),
				        GlobalVal.CODE_PROCESS_REASSIGNMENT, auditContext);
			}
		}
	}
	
	private void updateAssignOut(TrTaskH obj, AmMsuser selectedSurveyor, MsStatustask msStatustask, AuditContext callerId) {
		obj.setAmMsuser(selectedSurveyor);
		obj.setMsStatustask(msStatustask);
		obj.setApprovalDate(null);
		obj.setDownloadDate(null);
		obj.setSubmitDate(null);
		obj.setReadDate(null);
		obj.setStartDtm(null);
		obj.setRfaDate(null);
		obj.setSubmitDate(null);
		obj.setAssignDate(new Date());
		obj.setUsrUpd(callerId.getCallerId());
		obj.setDtmUpd(new Date());
		this.getManagerDAO().update(obj);
	}
	
	private void insertTaskHistory(TrTaskH task, String note, String actor,
			String idSvy, String processCode, AuditContext callerId) {
		TrTaskhistory obj = new TrTaskhistory();
		obj.setDtmCrt(new Date());
		obj.setUsrCrt(callerId.getCallerId());
		obj.setActor(actor);
		obj.setFieldPerson(idSvy);
		obj.setNotes(note);
		obj.setTrTaskH(task);
		obj.setMsStatustask(task.getMsStatustask());
		obj.setCodeProcess(processCode);
		this.getManagerDAO().insert(obj);
	}
}
