package com.adins.mss.businesslogic.impl.survey;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.survey.ReAssignTaskOTSLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericReAssignTaskOTSLogic extends BaseLogic implements ReAssignTaskOTSLogic, MessageSourceAware {

	private String paramStatusCode = "statusCode";
	private String paramUuidMsUser = "uuidMsUser";
	private String paramUuidStatusTask = "uuidStatusTask";
	@Autowired
	private MessageSource messageSource;
	private GeolocationLogic geolocationLogic;
	
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setGeolocationLogic(GeolocationLogic geolocationLogic) {
		this.geolocationLogic = geolocationLogic;
	}

	@Override
	public Map<String, Object> listTaskH(String mode, AmMsuser amMsuser, Object params,
			Object paramsCnt, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<>();
		List<TrTaskH> listTaskHs = new ArrayList<>();

		List<Map<String, Object>> resultUnassigned = Collections.EMPTY_LIST;
		Stack<Object[]> paramsStack = new Stack<>();		
		Stack<Object[]> paramsStackCount = new Stack<>();		
		StringBuilder queryJoin = new StringBuilder();
		String parameterCondition = getQueryParameterCondition(params, paramsStack);	
		String parameterConditionCount = getQueryParameterCondition(params, paramsStackCount);	
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT * from ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("select DISTINCT *, ROW_NUMBER() OVER (ORDER BY task_id) AS rownum from ")
			.append("(select tth.UUID_TASK_H, tth.TASK_ID, tth.APPL_NO, tth.CUSTOMER_NAME, tth.CUSTOMER_ADDRESS, ")
			.append("tth.CUSTOMER_PHONE, tth.ZIP_CODE, tth.UUID_PRIORITY, msbr.KONVEN_SYARIAH, CONVERT(VARCHAR,tth.assign_date,120)assign_date, CONVERT(VARCHAR,ISNULL(tth.DTM_UPD,tth.assign_date),120)DTM_UPD, ")
			.append("FORM_NAME, MS.STATUS_TASK_DESC, AU.FULL_NAME, MSBR.BRANCH_NAME, MSBR.UUID_BRANCH, ots.FLAG_SOURCE_OTS ")
			.append("from tr_task_h tth with (nolock) ")
			.append("JOIN MS_BRANCH MSBR WITH (NOLOCK) ON MSBR.UUID_BRANCH = TTH.UUID_BRANCH ")
			.append("JOIN MS_FORM MF WITH (NOLOCK) ON MF.UUID_FORM = TTH.UUID_FORM ")
			.append("JOIN MS_STATUSTASK MS WITH (NOLOCK) ON MS.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK ")
			.append("LEFT JOIN TBL_OTS_DATA ots with(nolock) ON ots.group_task_id = tth.TASK_ID ")
			.append("LEFT JOIN AM_MSUSER AU WITH (NOLOCK) ON AU.UUID_MS_USER = TTH.UUID_MS_USER ")
			.append(queryJoin)
			.append("where (ms.status_code = 'N' or ms.status_code = 'A') ")
			.append("and mf.form_name = :formName ")
			.append(parameterCondition);
		queryBuilder.append(" ) as UT ")
			.append(") a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[] {"formName", GlobalVal.FORM_OTS});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[6][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[7][1]});
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    resultUnassigned = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		StringBuilder queryBuilderCount = new StringBuilder()
			.append("select count(1) from ")
			.append("(select tth.uuid_task_h from tr_task_h tth with (nolock) ")
			.append("JOIN MS_BRANCH MSBR WITH (NOLOCK) ON MSBR.UUID_BRANCH = TTH.UUID_BRANCH ")
			.append("JOIN MS_FORM MF WITH (NOLOCK) ON MF.UUID_FORM = TTH.UUID_FORM ")
			.append("JOIN MS_STATUSTASK MS WITH (NOLOCK) ON MS.UUID_STATUS_TASK = TTH.UUID_STATUS_TASK ")
			.append(queryJoin)
			.append("where (ms.status_code = 'N' or ms.status_code = 'A') ")
			.append("and mf.form_name = :formName ")
			.append(parameterConditionCount)
			.append(" ) as UT ");
		
		paramsStackCount.push(new Object[] {"formName", GlobalVal.FORM_OTS});
		Object[][] sqlParamsCount = new Object[paramsStackCount.size()][2];
	    for (int i = 0; i < paramsStackCount.size(); i++) {
			Object[] objects = paramsStackCount.get(i);
			sqlParamsCount[i] = objects;
		}
		
	    Integer resultUnassignedCnt = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderCount.toString(), sqlParamsCount);
		
		Object[][] paramPH = {{Restrictions.eq("priorityDesc", "High")}};
		Object[][] paramPN = {{Restrictions.eq("priorityDesc", "Normal")}};
		
		MsPriority msPriorityH = this.getManagerDAO().selectOne(MsPriority.class, paramPH);
		MsPriority msPriorityN = this.getManagerDAO().selectOne(MsPriority.class, paramPN);
		
		for (int i = 0; i < resultUnassigned.size(); i++) {
			Map temp = resultUnassigned.get(i);
			TrTaskH trTaskH = new TrTaskH();
			trTaskH.setUuidTaskH(Long.valueOf(temp.get("d0").toString()));
			trTaskH.setTaskId((String)temp.get("d1"));
			trTaskH.setApplNo(temp.get("d2") == null ? StringUtils.EMPTY : temp.get("d2").toString());
			trTaskH.setCustomerName(temp.get("d3").toString());
			trTaskH.setCustomerAddress(temp.get("d4").toString());			
			trTaskH.setCustomerPhone(temp.get("d5").toString());
			trTaskH.setZipCode(temp.get("d6") == null ? StringUtils.EMPTY : temp.get("d6").toString());
			if (temp.get("d7").toString().equals(msPriorityH.getUuidPriority())) {
				trTaskH.setMsPriority(msPriorityH);
			} 
			else {
				trTaskH.setMsPriority(msPriorityN);
			}
			MsBranch msBranch = new MsBranch();
			msBranch.setKonvenSyariah(String.valueOf(temp.get("d8")));
			trTaskH.setMsBranch(msBranch);
			listTaskHs.add(trTaskH);
		}		
		
		result.put(GlobalKey.MAP_RESULT_LIST, resultUnassigned);
		result.put(GlobalKey.MAP_RESULT_SIZE, resultUnassignedCnt);
		
		return result;
	}
	
	@Override
	public List getStatusTaskList(Object params, Object order, AuditContext callerId) {
		List result = this.getManagerDAO().selectAllNative("task.reassigntaskots.statusList", params, order);	
		return result;
	}

	public String getUuidStatusTask(String statusCode, AmMsuser loginBean) {
		String uuidStatusTask = StringUtils.EMPTY;
		Object[][] params = {
				{ Restrictions.eq(paramStatusCode, statusCode) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
						loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
		MsStatustask msStatustask = (MsStatustask) this.getManagerDAO()
				.selectOne(MsStatustask.class, params);
		uuidStatusTask = String.valueOf(msStatustask.getUuidStatusTask());
		return uuidStatusTask;
	}
	
	@Override
	public Map<String, Object> listTaskToReAssign(String[] selectedTask, AuditContext auditContext) {
		Map<String, Object> result = new HashMap<>();
		List<TrTaskH> listTaskHs = new ArrayList<>();
		String[] tasks = selectedTask[0].split(",");
		for (int i = 0; i < tasks.length; i++) {
			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class,
					Long.valueOf(tasks[i]));
			this.getManagerDAO().fetch(trTaskH.getMsBranch());
			listTaskHs.add(trTaskH);
		}
		result.put(GlobalKey.MAP_RESULT_LIST, listTaskHs);
		result.put(GlobalKey.MAP_RESULT_SIZE, listTaskHs.size());

		return result;
	}

	@Override
	public List getPriorityList(Object params, Object order, AuditContext auditContext) {
		List result = this.getManagerDAO().selectAllNative(
				"task.inquirytasksurvey.priorityList", params, order);
		return result;
	}

	@Override
	public List getSpvList(String mode, Object params, Object order, AuditContext callerId) {
		List result = null;
		result = this.getManagerDAO().selectAllNative(
					"task.reassigntaskots.getSpvList", params, order);
		return result;
	}

	@Transactional
	@Override
	public void reAssignTask(String[] selectedTask, Long uuidMsUser, AmMsuser loginBean, long uuidPriority,
			AuditContext callerId) {
		MsPriority msPriority = this.getManagerDAO().selectOne(MsPriority.class, new Object [][] { { Restrictions.eq("uuidPriority", uuidPriority) } });
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, new Object [][] { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_NEW) } });
		
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, new Object [][] { { Restrictions.eq(paramUuidMsUser, uuidMsUser) } });
		this.getManagerDAO().fetch(amMsuser.getMsBranch());
		MsBranch msBranch = amMsuser.getMsBranch();
		
		String[] tasks = selectedTask[0].split(",");
		for (int i = 0; i < tasks.length; i++) {
			TrTaskH trTaskH = this.getManagerDAO().selectOne("from TrTaskH tth "
					+ "join fetch tth.msBranch msb "
					+ "join fetch tth.msStatustask mst "
					+ "join fetch mst.amMssubsystem ams "
					+ "where tth.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", Long.valueOf(tasks[i])}});
			
			MsStatustask sts = this.getManagerDAO().selectOne(MsStatustask.class,
					new Object [][] { { Restrictions.eq(paramUuidStatusTask, trTaskH.getMsStatustask().getUuidStatusTask()) } });
			
			if (GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION.equals(sts.getStatusCode())) {
				MsStatustask stsPending = this.getManagerDAO().selectOne(MsStatustask.class, new Object [][] { {Restrictions.eq(paramStatusCode, "N") }, {Restrictions.eq("amMssubsystem.uuidMsSubsystem", trTaskH.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem())} });
				trTaskH.setMsStatustask(stsPending);
			} else if (GlobalVal.SURVEY_STATUS_TASK_UNASSIGN.equals(sts.getStatusCode())) {
				long uuidProcess = this.getUuidProcess(trTaskH, trTaskH.getMsStatustask().getAmMssubsystem());
				MsStatustask msStatustask = commitWf(trTaskH, uuidProcess, trTaskH.getMsStatustask().getAmMssubsystem(), 0);
				trTaskH.setMsStatustask(msStatustask);
			}
			trTaskH.setAmMsuser(amMsuser);
			trTaskH.setMsBranch(msBranch);
			trTaskH.setAssignDate(new Date());
			trTaskH.setMsPriority(msPriority);
			trTaskH.setDtmUpd(new Date());
			trTaskH.setUsrCrt(String.valueOf(loginBean.getUuidMsUser()));
			trTaskH.setMsStatusmobile(msm);
			this.getManagerDAO().update(trTaskH);

			TrTaskhistory trTaskhistory = new TrTaskhistory();
			trTaskhistory.setCodeProcess(GlobalVal.CODE_PROCESS_ASSIGNMENT);
			trTaskhistory.setActor(loginBean.getFullName());
			trTaskhistory.setFieldPerson(amMsuser.getFullName());
			trTaskhistory.setMsStatustask(trTaskH.getMsStatustask());
			trTaskhistory.setTrTaskH(trTaskH);
			trTaskhistory.setNotes(trTaskH.getNotes());
			trTaskhistory.setUsrCrt(String.valueOf(loginBean.getUuidMsUser()));
			trTaskhistory.setDtmCrt(new Date());

			this.getManagerDAO().update(trTaskH);
			this.getManagerDAO().insert(trTaskhistory);

			if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
				commitOrder(loginBean, trTaskH.getNotes(), trTaskH, trTaskH
						.getAmMsuser().getAmMssubsystem(), 0,
						GlobalVal.CODE_PROCESS_ASSIGNMENT, callerId);
			}
		}
		
	}

	@Override
	public Map<String, Object> listUserAssign(String mode, String uuidBranchTask, String flagSource, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<>();
		Map<String, Object> resultUser = new HashMap<>();

		List<AmMsuser> listUser = new ArrayList<>();
		
		StringBuilder queryGetUser = new StringBuilder(" SELECT distinct UUID_MS_USER, LOGIN_ID, FULL_NAME, MJ.JOB_CODE ")
				.append(" FROM AM_MSUSER AMU WITH(NOLOCK) ")
				.append(" JOIN MS_JOB MJ WITH(NOLOCK) ON MJ.UUID_JOB = AMU.UUID_JOB ")
				.append(" JOIN MS_SETTINGAPPROVALOTS MSOTS WITH(NOLOCK) ON MSOTS.UUID_JOB_ASSIGN = AMU.UUID_JOB ")
				.append(" WHERE MSOTS.FLAG_SOURCE = :flagSource ")
				.append(" AND AMU.UUID_BRANCH = :uuidBranch ")
				.append(" AND AMU.IS_ACTIVE='1'");
		
		List userList = this.getManagerDAO().selectAllNativeString(queryGetUser.toString(), new Object[][] {{"uuidBranch", uuidBranchTask}, {"flagSource", flagSource}});
		
		if (userList!= null && !userList.isEmpty()) {
			for (int i=0; i<userList.size(); i++) {
				Map beanMap = (Map) userList.get(i);
				AmMsuser bean = new AmMsuser();
				bean.setUuidMsUser(Long.valueOf(beanMap.get("d0").toString()));
				bean.setLoginId(beanMap.get("d1").toString());
				bean.setFullName(beanMap.get("d2").toString() + " - " + beanMap.get("d3").toString());
				listUser.add(bean);
			}
		}
		
		resultUser.put(GlobalKey.MAP_RESULT_LIST, listUser);
		resultUser.put(GlobalKey.MAP_RESULT_SIZE, listUser.size());
		
		Integer[][] assignment = this
				.getAssignment((List<AmMsuser>) resultUser
						.get(GlobalKey.MAP_RESULT_LIST));
		String[][] lastLoc = this.getLastLoc((List<AmMsuser>) resultUser
				.get(GlobalKey.MAP_RESULT_LIST));
		
		List<AmMsuser> listUserSort = (List<AmMsuser>) resultUser.get(GlobalKey.MAP_RESULT_LIST);
		listUserSort.sort(Comparator.comparing(AmMsuser::getFullName));
		resultUser.put(GlobalKey.MAP_RESULT_LIST, listUserSort);
		
		result.put("result", resultUser);
		result.put("assignment", assignment);
		result.put("location", lastLoc);
		
		return result;
	}
	
	@Override
	public AmMsuser getUser(long uuidMsUser, AuditContext callerId) {
		AmMsuser amMsuser = (AmMsuser) this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", new Object[][] {{paramUuidMsUser, uuidMsUser}});
		return amMsuser;
	}

	@Override
	public Map<String, Object> listTaskToAssign(String[] selectedTask, AuditContext callerId) {
		Map<String, Object> result = new HashMap<>();
		List<TrTaskH> listTaskHs = new ArrayList<>();
		String[] tasks = selectedTask[0].split(",");
		for (int i = 0; i < tasks.length; i++) {
			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class,
					Long.valueOf(tasks[i]));
			this.getManagerDAO().fetch(trTaskH.getMsBranch());
			listTaskHs.add(trTaskH);
		}
		result.put(GlobalKey.MAP_RESULT_LIST, listTaskHs);
		result.put(GlobalKey.MAP_RESULT_SIZE, listTaskHs.size());

		return result;
	}

	private String getQueryParameterCondition(Object params, Stack<Object[]> paramsStack) {
		StringBuilder queryBuilder = new StringBuilder();
		//filter uuidStatusTask
		if (StringUtils.isNotBlank(((String[][]) params)[8][1]) && !"%".equalsIgnoreCase(((String[][]) params)[8][1])) {
			queryBuilder.append("and tth.uuid_status_task = :uuidStatusTask ");
			paramsStack.push(new Object[]{paramUuidStatusTask, ((Object[][]) params)[8][1]});
		}
				
		//filter customer name
		if (StringUtils.isNotBlank(((String[][]) params)[3][1]) && !"%".equalsIgnoreCase(((String[][]) params)[3][1])) {
			queryBuilder.append("and customer_name like '%' + :customerName + '%' ");
			paramsStack.push(new Object[]{"customerName", ((Object[][]) params)[3][1]});
		}
		
		//filter customer_address
		if (StringUtils.isNotBlank(((String[][]) params)[4][1]) && !"%".equalsIgnoreCase(((String[][]) params)[4][1])) {
			queryBuilder.append("and customer_address like '%' + :customerAddress + '%' ");
			paramsStack.push(new Object[]{"customerAddress", ((Object[][]) params)[4][1]});
		}

		//filter appl_no
		if (StringUtils.isNotBlank(((String[][]) params)[5][1]) && !"%".equalsIgnoreCase(((String[][]) params)[5][1])) {
			queryBuilder.append("and (ISNULL(appl_no, '%') like '%' + :applNo + '%')");
			paramsStack.push(new Object[]{"applNo", ((Object[][]) params)[5][1]});
		}
				
		return queryBuilder.toString();
	}
	
	public Integer[][] getAssignment(List<AmMsuser> listResult) {
		Integer[][] result = new Integer[listResult.size()][3];
		String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
		for (int i = 0; i < listResult.size(); i++) {
			AmMsuser usr = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", new Object[][] {{paramUuidMsUser, listResult.get(i).getUuidMsUser()}});
			String[][] params = { { paramUuidMsUser, String.valueOf(usr.getUuidMsUser()) },
					{ "start", currentDate + " 00:00:00.000" },
					{ "end", currentDate + " 23:59:59.997" } };
			Integer taskAssignment = (Integer) this.getManagerDAO()
					.selectOneNative(
							"task.unassigntask.getTotalTaskAssignment", params);
			Integer submittedTask = (Integer) this.getManagerDAO()
					.selectOneNative("task.unassigntask.getTotalSubmittedTask",
							params);
			Object[][] paramsStatus = {
					{ Restrictions.eq(paramStatusCode,
							GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
							usr.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(
					MsStatustask.class, paramsStatus);
			String[][] paramsPending = { { paramUuidMsUser, String.valueOf(usr.getUuidMsUser()) },
					{ paramUuidStatusTask, String.valueOf(msStatustask.getUuidStatusTask()) } };
			Integer pendingTask = (Integer) this.getManagerDAO()
					.selectOneNative("task.unassigntask.getTotalPendingTask",
							paramsPending);
			result[i][0] = taskAssignment;
			result[i][1] = submittedTask;
			result[i][2] = pendingTask;
		}
		return result;
	}

	private String[][] getLastLoc(List<AmMsuser> listResult) {
		String[][] result = new String[listResult.size()][2];
		for (int i = 0; i < listResult.size(); i++) {
			String[][] params = { { paramUuidMsUser,
				String.valueOf(listResult.get(i).getUuidMsUser()) } };
			Object[] lastLoc = (Object[]) this.getManagerDAO().selectOneNative(
					"task.unassigntask.getLastLoc", params);
			if (null != lastLoc) {

				BigDecimal lat = new BigDecimal(lastLoc[4].toString());
				BigDecimal lng = new BigDecimal(lastLoc[5].toString());
				Integer mcc = Integer.parseInt(lastLoc[7].toString());
				Integer mnc = Integer.parseInt(lastLoc[8].toString());
				Integer lac = Integer.parseInt(lastLoc[9].toString());
				Integer cellId = Integer.parseInt(lastLoc[10].toString());

				if (null != lat && null != lng) {
					if (0 == lat.compareTo((BigDecimal.ZERO))
							&& 0 == lng.compareTo(BigDecimal.ZERO)) {
						lat = null;
						lng = null;
						if (null != mcc && null != mnc && null != lac
								&& null != cellId) {
							List<LocationBean> listLocation = new ArrayList<>();
							LocationBean locationBean = new LocationBean();
							locationBean.setCellid(cellId);
							locationBean.setLac(lac);
							locationBean.setMcc(mcc);
							locationBean.setMnc(mnc);
							listLocation.add(locationBean);
							this.geolocationLogic.geocodeCellId(listLocation, null);
							if (null != listLocation.get(0).getCoordinate()) {
								result[i][0] = BigDecimal.valueOf(locationBean.getCoordinate().getLatitude()).toString();
								result[i][1] = BigDecimal.valueOf(locationBean.getCoordinate().getLongitude()).toString();
							}
						}
					} 
					else {
						result[i][0] = lat.toString();
						result[i][1] = lng.toString();
					}
				}
			}			
			//2015-11-26 SUM will show no location on maps instead of latLong of area, misleading information
			else {
			    result[i][0] = null;
			    result[i][1] = null;
			}
		}
		return result;
	}
}
