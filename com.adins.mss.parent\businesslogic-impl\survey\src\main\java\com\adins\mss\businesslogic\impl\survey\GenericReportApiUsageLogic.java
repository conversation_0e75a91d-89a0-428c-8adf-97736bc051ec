package com.adins.mss.businesslogic.impl.survey;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.ReportApiUsageLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "rawtypes", "unchecked"})
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportApiUsageLogic extends BaseLogic 
		implements ReportApiUsageLogic, MessageSourceAware{
	private static final String[] HEADER_SUMMARY = { "No", "Branch Name", "User Name", 
		"New Order", "Assignment Task", "Pending Task", "Submitted Task", "Total" };
	private static final String[] HEADER_SUMMARY_API_USAGE = { "No", "Type", "Cabang", "Total Success", 
		"Total Failed" };
	private static final int[] SUMMARY_COLUMN_WIDTH = { 10 * 256, 30 * 256, 30 * 256, 20 * 256, 20 * 256, 
		20 * 256, 20 * 256, 20 * 256 };
	private static final int[] SUMMARY_API_USAGE_COLUMN_WIDTH = { 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256 };
	private static final String[] HEADER_DETAIL = { "No", "Branch Name", "User Id", "Full Name", "Application ID", "Assignment Date", 
		"Submitted Date", "Status Task", "Form Name" };
	private static final String[] HEADER_DETAIL_API_USAGE = { "No", "Cabang", "User", "Type", "Time", "Status", "Keterangan" };
	private static final int[] DETAIL_COLUMN_WIDTH = { 10 * 256, 30 * 256, 30 * 256, 30 * 256, 20 * 256, 30 * 256, 30 * 256, 
		30 * 256, 30 * 256 };
	private static final String[] HEADER_DETAIL_0 = { "No", "User Id", "Full Name", "Application ID", 
		"Assignment Date", "Submitted Date", "Status Task", "Form Name"  };
	private static final String[] HEADER_DETAIL_API_USAGE_0 = { "No", "Cabang", "User", "Type", "Time", "Status", "Keterangan" };
	private static final int[] DETAIL_COLUMN_WIDTH_0 = { 10 * 256, 30 * 256, 30 * 256, 20 * 256, 30 * 256, 
			30 * 256, 30 * 256, 30 * 256 };
	private static final int[] DETAIL_API_USAGE_COLUMN_WIDTH_0 = { 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256, 30 * 256 };
	private static final Logger LOG = LoggerFactory.getLogger(GenericReportApiUsageLogic.class);
	
	@Autowired
    private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	@Override
	public List getComboBranch(String branchId, AuditContext callerId) {
		List result = null;
		String[][] params = { { "branchId", branchId } };
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}
	
	@Override
	public List getSummary(String branchId, String userId, String startDate,
			String endDate, AuditContext callerId, String usageType) {
		AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, 
				Long.valueOf(callerId.getCallerId()));
		
		String[][] params = { 
				{ "branchLogin", String.valueOf(amMsUser.getMsBranch().getUuidBranch())}, 
				{ "branchId", branchId }, { "userId", userId.isEmpty()?"%":userId },
				{ "usageType", usageType }, { "startDate", startDate }, { "endDate", endDate }, };

		List list = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilderSummary(
				(Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
			.append("WITH N as ( ")
			.append("select * from TBL_API_USAGE tap ")
			.append(") ")
			.append("SELECT * FROM( ")
			.append("select tap.TYPE code, msr.BRANCH_NAME branch, ")
			.append("sum(case when tap.TYPE = :usageType and tap.STATUS = 1 ")
			.append("and tap.DTM_CRT between :startDate and :endDate then 1 else 0 end) taskSuccess, ")
			.append("sum(case when tap.TYPE = :usageType and tap.STATUS = 999 or tap.STATUS = 500  ")
			.append("and tap.DTM_CRT between :startDate and :endDate then 1 else 0 end) taskFailed, ")
			.append("case when tap.type = '001' then 'Biometrik' when tap.type = '002' then 'OCR' ")
			.append("when tap.type = '004' then 'Telecheck' when tap.type = '005' then 'Telecheck ID' ")
			.append("else null end as type ")
			.append("from TBL_API_USAGE tap with (nolock) ")
			.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
			.append("FROM dbo.getCabangByLogin(:branchLogin)) msr on tap.BRANCH = msr.UUID_BRANCH ")
			.append("where tap.TYPE = :usageType ")
			.append(paramsQueryString)
			.append("GROUP BY tap.TYPE, msr.BRANCH_NAME ")
			.append(")a ")
			.append("ORDER BY a.code ");
		paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[5][1]});
		paramsStack.push(new Object[]{"branchLogin", ((Object[][]) params)[0][1]});
		paramsStack.push(new Object[]{"usageType", ((Object[][]) params)[3][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
		for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		list = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		return list;
	}

	private StringBuilder sqlPagingBuilderSummary(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append(" and tap.BRANCH = :branchId ");
			paramStack.push(new Object[]{"branchId", Long.valueOf((String) params[1][1])});
		}
		
		return sb;
	}
	
	@Override
	public List getDetail(String branchId, String userId, String startDate,
			String endDate, AuditContext callerId, long userBranch, String usageType) {
		List result = null;
		String user = StringUtils.isBlank(userId)?"%":userId;
		String branch = StringUtils.isBlank(branchId) || "0".equals(branchId) ?"%":branchId;
		String type = StringUtils.isBlank(usageType) || "0".equals(usageType) ?"%":usageType;
		
		StringBuilder queryBuilder = new StringBuilder();
		Stack<Object[]> paramsStack = new Stack<>();
		
		Object[][] params = { { "branchId", branch }, { "userId", user }, 
				{ "startDate", startDate }, { "endDate", endDate },
				{ "userBranch", userBranch }, { "usageType", type } };
		paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[2][1]});
		paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[3][1]});
		paramsStack.push(new Object[]{"userBranch", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"usageType", ((Object[][]) params)[5][1]});
				
		StringBuilder paramsQueryString = this.sqlPagingBuilderDetail((Object[][]) params, paramsStack);
		queryBuilder.append("SELECT * FROM ( ")
			.append("select msr.BRANCH_NAME as Cabang, amu.FULL_NAME as fullname, ")
			.append("case when tap.TYPE = '001' then 'Biometrik'  ")
			.append("when tap.TYPE = '002' then 'OCR'  ")
			.append("when tap.TYPE = '004' then 'Telecheck Status'  ")
			.append("when tap.TYPE = '005' then 'Telecheck ID'  ") 
			.append("else null end as Type, tap.DTM_CRT, ")
			.append("case when tap.STATUS = '1' then 'Success'  ")
			.append("when tap.STATUS = '999' or tap.STATUS = '500' then 'Failed' else null end as Status, tap.KETERANGAN as Keterangan ")
			.append("from TBL_API_USAGE tap with (nolock) ")
			.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ") 
			.append("FROM dbo.getCabangByLogin(:userBranch)) msr on tap.BRANCH = msr.UUID_BRANCH ")
			.append("JOIN AM_MSUSER AMU with (nolock) ON tap.USR_CRT = AMU.UUID_MS_USER ")
			.append("where tap.TYPE = :usageType and tap.DTM_CRT BETWEEN :startDate and :endDate ")
			.append(paramsQueryString)
			.append(") a ")
			.append("ORDER BY a.fullname, a.DTM_CRT ");
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}
	
	@Override
	public int getDetailCount(String branchId, String userId, String startDate,
			String endDate, AuditContext callerId, long userBranch, String usageType) {
		int resultCount = 0;
		String user = StringUtils.isBlank(userId)?"%":userId;
		String branch = StringUtils.isBlank(branchId) || "0".equals(branchId) ?"%":branchId;
		String type = StringUtils.isBlank(usageType) || "0".equals(usageType) ?"%":usageType;
		Object[][] params = { { "branchId", branch }, { "userId", user }, 
				{ "startDate", startDate }, { "endDate", endDate },
				{ "userBranch", userBranch }, { "usageType", type } };
					
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilderDetail((Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT count(1) FROM ( ")
			.append("select msr.BRANCH_NAME as Cabang, amu.FULL_NAME as fullname, ")
			.append("case when tap.TYPE = '001' then 'Biometrik'  ")
			.append("when tap.TYPE = '002' then 'OCR'  ")
			.append("when tap.TYPE = '003' then 'Telecheck Status'  ")
			.append("when tap.TYPE = '004' then 'Telecheck ID'  ") 
			.append("when tap.TYPE = '005' then 'Personal Tax' else null end as Type, tap.DTM_CRT, ")
			.append("case when tap.STATUS = '1' then 'Success'  ")
			.append("when tap.STATUS = '999' or tap.STATUS = '500' then 'Failed' else null end as Status, tap.KETERANGAN as Keterangan ")
			.append("from TBL_API_USAGE tap with (nolock) ")
			.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ") 
			.append("FROM dbo.getCabangByLogin(:userBranch)) msr on tap.BRANCH = msr.UUID_BRANCH ")
			.append("JOIN AM_MSUSER AMU with (nolock) ON tap.USR_CRT = AMU.UUID_MS_USER ")
			.append("where tap.TYPE = :usageType and tap.DTM_CRT BETWEEN :startDate and :endDate ")
			.append(paramsQueryString)
			.append(") c ");
		paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[2][1]});
		paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[3][1]});
		paramsStack.push(new Object[]{"userBranch", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"usageType", ((Object[][]) params)[5][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    resultCount = (int) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return resultCount;
	}
	
	private StringBuilder sqlPagingBuilderDetail(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and tap.BRANCH = :branchId ");
			paramStack.push(new Object[]{"branchId", Long.valueOf((String) params[0][1])});
		}

		//---UUID_USER
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("and tap.USR_CRT = :userId ");
			paramStack.push(new Object[]{"userId", Long.valueOf((String) params[1][1])});
		}
		return sb;
	}

	private Map<String,String> getUserBranch(String branchId, String userId, AuditContext callerId) {
		Map<String,String> result = new HashMap<>();
			
		AmMsuser user = null;
		if (StringUtils.isNotBlank(userId)) {
			user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(userId));
		}
		
		MsBranch branch = null;
		if (StringUtils.isNotBlank(branchId) && !"%".equals(branchId)) {
			branch = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(branchId));
		}
		
		result.put("user", user!=null?user.getFullName():"All User");
		result.put("branch", branch!=null?branch.getBranchCode()+" - "+branch.getBranchName():"All Branch");
		return result;
	}
	
	@Override
	public byte[] exportExcel(String branchId, String userId, String startDate,
			String endDate, String type, AuditContext callerId, String usageType) {	
		XSSFWorkbook workbook = this.createXlsTemplate(branchId, userId, startDate, 
				endDate, type, callerId, usageType);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(String branchId, String userId,
			String startDate, String endDate, String type, AuditContext callerId, String usageType) {
		AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, 
				Long.valueOf(callerId.getCallerId()));
		
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setSubsystemCode(amMsUser.getAmMssubsystem().getSubsystemName());
		reportBean.setUuidLoginId(String.valueOf(amMsUser.getUuidMsUser()));
		reportBean.setUuidBranch(branchId);
		reportBean.setUuidUser(userId);
		reportBean.setStartDate(startDate);
		reportBean.setEndDate(endDate);
		reportBean.setType(type);
		reportBean.setUsageType(usageType);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("Report API Usage");
		trReportResultLog.setRptType(FilterType.FILTER_BY_API_USAGE.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}
	
	private XSSFWorkbook createXlsTemplate(String branchId, String userId, String startDate,
			String endDate, String type, AuditContext callerId, String usageType) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		String typeName = "";
		
		if (("001".equals(usageType))) {
			typeName = "Biometrik";
		} else if (("002".equals(usageType))) {
			typeName = "OCR";
		} else if (("004".equals(usageType))) {
			typeName = "Telecheck Status";
		} else if (("005".equals(usageType))) {
			typeName = "Telecheck ID";
		}
		
		try {
			XSSFSheet sheet = workbook.createSheet("Report API Usage");
			if (type.equalsIgnoreCase("0")) {
				List result= this.getSummary(branchId, userId, startDate, endDate, callerId, usageType);
				this.createDataSummary(workbook, sheet, result, startDate, endDate, typeName);
			}
			else {
				AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
				List result= this.getDetail(branchId, userId, startDate, endDate, callerId, usr.getMsBranch().getUuidBranch(), usageType);
				if (type.equals("1")) {
					this.createDataDetail(workbook, sheet, result, this.getUserBranch(branchId, 
							userId, callerId), startDate, endDate, typeName);
				}
				else {
					this.createDataDetailFromSummary(workbook, sheet, result, this.getUserBranch(
							branchId, userId, callerId), startDate, endDate, typeName);
				}
			}
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private void createDataSummary(XSSFWorkbook workbook, XSSFSheet sheet, List result, String startDate, 
			String endDate, String usagetype) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		XSSFCell cellHead = rowHead.createCell(0);
		cellHead.setCellValue("REPORT API USAGE " + usagetype.toUpperCase() + " SUMMARY PERIOD "+ startDate.substring(0, 10) + " - " 
				+ endDate.substring(0, 10));
		cellHead.setCellStyle(styles.get("header"));
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0,HEADER_SUMMARY.length-4));
		XSSFRow rowHeader = sheet.createRow(rowcell++);
		
		for (int i = 0; i < HEADER_SUMMARY_API_USAGE.length; i++) {
			XSSFCell cell4 = rowHeader.createCell(i);
			cell4.setCellValue(HEADER_SUMMARY_API_USAGE[i]);
			cell4.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, SUMMARY_API_USAGE_COLUMN_WIDTH[i]);
		}

		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			String cellData[] = { "d4", "d1", "d2", "d3" };
			//data cell
			for (int j = 0; j < cellData.length; j++) {
				XSSFCell cell4 = rowData.createCell(j+1);
				if (temp.get(cellData[j]) != null) {
					cell4.setCellValue(temp.get(cellData[j]).toString());
				}
				cell4.setCellStyle(styles.get("cell"));	
			}
		}       	
	}
	
	private void createDataDetail(XSSFWorkbook workbook, XSSFSheet sheet, List result, Map userBranch, 
			String startDate, String endDate, String usageType) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader1 = sheet.createRow(rowcell++);
		XSSFCell cell = rowHeader1.createCell(0);
		cell.setCellValue("User");
		XSSFCell cell1 = rowHeader1.createCell(1);
		cell1.setCellValue(userBranch.get("user").toString());
		
		XSSFRow rowHeader2 = sheet.createRow(rowcell++);
		XSSFCell cell2 = rowHeader2.createCell(0);
		cell2.setCellValue("Branch");
		XSSFCell cell3 = rowHeader2.createCell(1);
		cell3.setCellValue(userBranch.get("branch").toString());
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_DETAIL_API_USAGE.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT API USAGE " + usageType.toUpperCase() + " DETAIL PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell4 = rowHeader.createCell(i);
			cell4.setCellValue(HEADER_DETAIL_API_USAGE[i]);
			cell4.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, DETAIL_API_USAGE_COLUMN_WIDTH_0[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_DETAIL.length-3));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			String cellData[] = { "d0", "d1", "d2", "d3", "d4", "d5" };
			//data cell
			for (int j = 0; j < cellData.length; j++) {
				XSSFCell cell4 = rowData.createCell(j+1);
				if (temp.get(cellData[j]) != null) {
					cell4.setCellValue(temp.get(cellData[j]).toString());
				}
				cell4.setCellStyle(styles.get("cell"));	
			}
		}       	
	}	
	
	private void createDataDetailFromSummary(XSSFWorkbook workbook, XSSFSheet sheet, List result, 
			Map userBranch, String startDate, String endDate, String usageType) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader1 = sheet.createRow(rowcell++);
		XSSFCell cell = rowHeader1.createCell(0);
		cell.setCellValue("User");
		XSSFCell cell1 = rowHeader1.createCell(1);
		cell1.setCellValue(userBranch.get("user").toString());
		
		XSSFRow rowHeader2 = sheet.createRow(rowcell++);
		XSSFCell cell2 = rowHeader2.createCell(0);
		cell2.setCellValue("Branch");
		XSSFCell cell3 = rowHeader2.createCell(1);
		cell3.setCellValue(userBranch.get("branch").toString());
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_DETAIL_API_USAGE_0.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT API USAGE " + usageType.toUpperCase() + " DETAIL PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell4 = rowHeader.createCell(i);
			cell4.setCellValue(HEADER_DETAIL_API_USAGE_0[i]);
			cell4.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, DETAIL_API_USAGE_COLUMN_WIDTH_0[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_DETAIL_API_USAGE_0.length-3));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			String cellData[] = { "d0", "d1", "d2", "d3", "d4", "d5" };
			//data cell
			for (int j = 0; j < cellData.length; j++) {
				XSSFCell cell4 = rowData.createCell(j+1);
				if (temp.get(cellData[j]) != null) {
					cell4.setCellValue(temp.get(cellData[j]).toString());
				}
				cell4.setCellStyle(styles.get("cell"));	
			}
		}       	
	}	
	
	private static Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style;

        style = wb.createCellStyle();
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styles.put("cell", style);

        style = wb.createCellStyle();
        Font font = wb.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(CellStyle.SOLID_FOREGROUND);
        styles.put("header",style );
        return styles;
    }

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(reportBean.getUuidBranch(), 
				reportBean.getUuidUser(), reportBean.getStartDate(), reportBean.getEndDate(), 
				reportBean.getType(), 
				new AuditContext(String.valueOf(trReportResultLog.getAmMsuser().getUuidMsUser())), 
				reportBean.getUsageType());
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("ReportApiUsage_");
			
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
				reportBean.setUuidBranch(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
						Long.valueOf(reportBean.getUuidBranch()));
				sb.append(msBranch.getBranchCode());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if ("0".equals(reportBean.getUuidUser()) || "%".equals(reportBean.getUuidUser())) {
				reportBean.setUuidUser(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
				AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
						Long.valueOf(reportBean.getUuidUser()));
				sb.append(user.getLoginId());
			} 
			else {
				sb.append("ALL");
			}
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(trReportResultLog
				.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime().getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}
	
	@Override
	public List getFormListCombo(AuditContext callerId) {
		List result = null;
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
		String[][] paramsForm = { { "isActive", "1" }, {"subsystemName", subsysName}};
		result = this.getManagerDAO().selectAllNativeString(
				"SELECT form.UUID_FORM, form.FORM_NAME "
				+ "FROM MS_FORM form WITH (NOLOCK) "
					+ "JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
					+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
				+ "WHERE form.IS_ACTIVE = :isActive "
					+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName "
					+ "ORDER BY form.FORM_NAME ASC", paramsForm);
		
		return result;
	}
}
