package com.adins.mss.businesslogic.impl.survey;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFPicture;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtilities;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.labels.PieSectionLabelGenerator;
import org.jfree.chart.labels.StandardPieSectionLabelGenerator;
import org.jfree.chart.plot.PiePlot;
import org.jfree.data.general.DefaultPieDataset;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.ReportBranchLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericReportBranchLogic extends BaseLogic 
		implements ReportBranchLogic, MessageSourceAware {
	private static final String[] HEADER_SUMMARY = { "NO","Branch", "New Order",
		"Pending Task", "Submitted Task", "Total" };
	private static final int[] SUMMARY_COLUMN_WIDTH = { 10 * 256, 20 * 256, 20 * 256,
		20 * 256, 20 * 256, 20 * 256};

	private static final Logger LOG = LoggerFactory.getLogger(GenericReportBranchLogic.class);
	
	@Autowired
    private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	@Override
	public List<Map<String,Object>> getBranchListCombo(String branchId, AuditContext callerId){
		List<Map<String,Object>> result;
		String[][] params={{"branchId",branchId}};
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}
	
	@Override
	public List getReportBranch(String[][] params, String type, AuditContext callerId){
		List result = null;
		if (type.equalsIgnoreCase("0")) {
			Stack<Object[]> paramsStack = new Stack<>();		
			StringBuilder paramsQueryStringAll = this.sqlPagingBuilderAll((Object[][]) params, paramsStack);
			StringBuilder queryBuilder = new StringBuilder()
				.append("select msr.BRANCH_NAME branch, ")
				.append("sum(case when tth.FLAG_SOURCE='MS' and tth.ASSIGN_DATE is null and tth.dtm_crt ")
				.append("BETWEEN :startDate and :endDate then 1 else 0 end) newSurvey, ")
				.append("sum(case when mst.STATUS_CODE = 'N' and tth.ASSIGN_DATE ")
				.append("BETWEEN :startDate and :endDate then 1 else 0 end) pendingSurvey, ")
				.append("sum(case when tth.SUBMIT_DATE BETWEEN :startDate and :endDate ")
				.append("then 1 else 0 end) submittedSurvey, ")
				.append("msr.uuid_branch ")
				.append("from TR_TASK_H tth with (nolock) ")
				.append("left join MS_STATUSTASK mst with (nolock) ")
				.append("on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("left join AM_MSSUBSYSTEM ams with (nolock) ")
				.append("on mst.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM  ")
				.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
				.append("FROM dbo.getCabangByLogin(:branchId)) msr on tth.UUID_BRANCH = msr.UUID_BRANCH ")
				.append("left JOIN MS_FORM form with (nolock) ON tth.UUID_FORM = form.UUID_FORM ")
				.append("where ams.SUBSYSTEM_NAME = 'MS'  ")
				.append(paramsQueryStringAll)
				.append("GROUP BY msr.BRANCH_CODE, msr.BRANCH_NAME, msr.UUID_BRANCH ")
				.append("ORDER BY msr.BRANCH_CODE, msr.BRANCH_NAME ");
			paramsStack.push(new Object[]{"branchId", ((Object[][]) params)[0][1]});
			paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[1][1]});
			paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[2][1]});
			Object[][] sqlParams = new Object[paramsStack.size()][2];
		    for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
		    result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		}
		else {
			Stack<Object[]> paramsStack = new Stack<>();		
			StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
			StringBuilder queryBuilder = new StringBuilder()
				.append("select msr.BRANCH_NAME branch, ")
				.append("sum(case when tth.FLAG_SOURCE='MS' and tth.ASSIGN_DATE is null and tth.dtm_crt ")
				.append("BETWEEN :startDate and :endDate then 1 else 0 end) newSurvey, ")
				.append("sum(case when mst.STATUS_CODE = 'N' and tth.ASSIGN_DATE ")
				.append("BETWEEN :startDate and :endDate then 1 else 0 end) pendingSurvey, ")
				.append("sum(case when tth.SUBMIT_DATE BETWEEN :startDate and :endDate ")
				.append("then 1 else 0 end) submittedSurvey, ")
				.append("msr.uuid_branch ")
				.append("from TR_TASK_H tth with (nolock)  ")
				.append("left join MS_STATUSTASK mst with (nolock) ")
				.append("on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("left join AM_MSSUBSYSTEM ams with (nolock) ")
				.append("on mst.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM ") 
				.append("left join MS_BRANCH msr with (nolock) on tth.UUID_BRANCH = msr.UUID_BRANCH ") 
				.append("left JOIN MS_FORM form with (nolock) ON tth.UUID_FORM = form.UUID_FORM ")
				.append("where ams.SUBSYSTEM_NAME = 'MS' ")
				.append(paramsQueryString)
				.append("GROUP BY msr.BRANCH_CODE, msr.BRANCH_NAME, msr.UUID_BRANCH ")
				.append("ORDER BY msr.BRANCH_CODE, msr.BRANCH_NAME ");
			paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[1][1]});
			paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[2][1]});
			Object[][] sqlParams = new Object[paramsStack.size()][2];
			for (int i = 0; i < paramsStack.size(); i++) {
				Object[] objects = paramsStack.get(i);
				sqlParams[i] = objects;
			}
			result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		}
		return result;
	}
	
	/*
	 * 0 {{"branchId",branchId},
	 * 1 {"startDate",start+" 00:00:00.000"},
	 * 2 {"endDate",end+" 23:59:59.997"},
	 * 3 {"uuidForm",uuidForm}}
	 */
	private StringBuilder sqlPagingBuilderAll(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID FORM
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append("and form.UUID_FORM = :uuidForm ");
			paramStack.push(new Object[]{"uuidForm", Long.valueOf((String) params[3][1])});
		}
		return sb;
	}
	
	/*
	 * 0 {{"branchId",branchId},
	 * 1 {"startDate",start+" 00:00:00.000"},
	 * 2 {"endDate",end+" 23:59:59.997"},
	 * 3 {"uuidForm",uuidForm}}
	 */
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID BRANCH
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and tth.UUID_BRANCH = :branchId ");
			paramStack.push(new Object[]{"branchId", Long.valueOf((String) params[0][1])});
		}
				
		//---UUID FORM
		if (!StringUtils.equals("%", (String) params[3][1])) {
			sb.append("and form.UUID_FORM = :uuidForm ");
			paramStack.push(new Object[]{"uuidForm", Long.valueOf((String) params[3][1])});
		}
		return sb;
	}
	
	@Override
	public byte[] exportExcel(String[][] params, String type, AuditContext callerId) {
		//type apabila ada selain summary
		XSSFWorkbook workbook = this.createXlsTemplate(params,type,callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(String[][] params, String type, AuditContext callerId) {
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setUuidLoginId(callerId.getCallerId());
		reportBean.setUuidBranch(params[0][1]);
		reportBean.setStartDate(params[1][1]);
		reportBean.setEndDate(params[2][1]);
		reportBean.setParamsAction(params);
		reportBean.setType(type);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);
		
		AmMsuser amMsUser = new AmMsuser();
		amMsUser.setUuidMsUser(Long.valueOf(callerId.getCallerId()));
		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("Branch Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_BRANCH.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}
	
	public XSSFWorkbook createXlsTemplate(String[][] params, String type, AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Report Branch");
				List result= this.getReportBranch(params,type, callerId);
				boolean isBranchAll = false;
				if (params[0][1].equalsIgnoreCase("%")) {
					isBranchAll = true;
				}
				DefaultPieDataset pieChart = this.createDataSummary(workbook, sheet, result,
						isBranchAll, params[1][1], params[2][1]);
				//creating chart
				JFreeChart myPieChart = ChartFactory.createPieChart("Report Branch",pieChart,true,true,false);
				PiePlot plot = (PiePlot) myPieChart.getPlot();
				plot.setSimpleLabels(true);
				PieSectionLabelGenerator gen = new StandardPieSectionLabelGenerator(
			            "{0} = {1} ({2})", new DecimalFormat("0"), new DecimalFormat("0%"));
			    plot.setLabelGenerator(gen);
				int width = 640; /* Width of the chart */
	            int height = 480; /* Height of the chart */
	            float quality = 1;
	            ByteArrayOutputStream chart_out = new ByteArrayOutputStream();          
	            ChartUtilities.writeChartAsJPEG(chart_out,quality,myPieChart,width,height);
	            int my_picture_id = workbook.addPicture(chart_out.toByteArray(), Workbook.PICTURE_TYPE_JPEG);
	            chart_out.close();
	            XSSFDrawing drawing = sheet.createDrawingPatriarch();
	            ClientAnchor my_anchor = new XSSFClientAnchor();
	            /* Define top left corner, and we can resize picture suitable from there */
	            my_anchor.setCol1(1);
	            my_anchor.setRow1(result.size()+4);
	            /* Invoke createPicture and pass the anchor point and ID */
	            XSSFPicture  my_picture = drawing.createPicture(my_anchor, my_picture_id);
	            /* Call resize method, which resizes the image */
	            my_picture.resize();
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private DefaultPieDataset createDataSummary(XSSFWorkbook workbook, XSSFSheet sheet,
			List result, boolean isBranchAll, String startDate, String endDate) {
		DefaultPieDataset chart = new DefaultPieDataset();
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_SUMMARY.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT SUMMARY PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(HEADER_SUMMARY[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, SUMMARY_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_SUMMARY.length-1));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			int total = 0;
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			//data cell
			for (int j = 1; j < HEADER_SUMMARY.length; j++) {
				XSSFCell cell = rowData.createCell(j);
				if (j == 1) { //Set data to string
					cell.setCellValue(temp.get("d"+(j-1)).toString());
					cell.setCellStyle(styles.get("cell"));
				}
				else if (HEADER_SUMMARY[j].equalsIgnoreCase("total")) {
					cell.setCellValue(total);
					cell.setCellStyle(styles.get("cell"));
				}
				else {
					cell.setCellValue(Integer.parseInt(temp.get("d"+(j-1)).toString()));
					cell.setCellStyle(styles.get("cell"));
					if (!isBranchAll) { // set chart if branch not all
						chart.setValue(HEADER_SUMMARY[j], Integer.parseInt(temp.get("d"+(j-1)).toString()));
					}
					total = total + Integer.parseInt(temp.get("d"+(j-1)).toString());
				}
			}
			
		} 
		//Total Data
		if (!result.isEmpty()) {
			XSSFRow rowData = sheet.createRow(rowcell++);
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue("Total");
			cellNo.setCellStyle(styles.get("header"));
			XSSFCell cell = rowData.createCell(1);
			String ref = (char)('B') + "2:" + (char)('B') + Integer.toString(result.size()+1);
			cell.setCellFormula("COUNTA(" + ref + ")");	
			cell.setCellStyle(styles.get("header"));
        
			for (int i = 0; i < HEADER_SUMMARY.length - 2; i++) {    // -2  karena sudah cell A dan B sudah diisi
				cell = rowData.createCell(i+2);
				ref = (char)('C'+i) + "3:" + (char)('C'+i) + Integer.toString(result.size()+2);
				cell.setCellFormula("SUM(" + ref + ")");
				cell.setCellStyle(styles.get("header"));
	        }
        //End Total Data
			int[] tmpTot = new int[3];
			if (isBranchAll) {
				for (int i = 1; i <= 3; i++) {
					for (int j = 0; j < result.size(); j++) {
						Map tmp = (Map)result.get(j);
						tmpTot[i-1] += Integer.parseInt(tmp.get("d"+i).toString());
					}
					chart.setValue(HEADER_SUMMARY[i+1],tmpTot[i-1]);
				}
			}
        }
        return chart;
	}	
	
	private static Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style;

        style = wb.createCellStyle();
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styles.put("cell", style);

        style = wb.createCellStyle();
        Font font = wb.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(CellStyle.SOLID_FOREGROUND);
        styles.put("header",style );
        return styles;
    }

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(reportBean.getParamsAction(), 
				reportBean.getType(), callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("ReportBranch_");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getEndDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
					sb.append("ALL");
				} 
				else {
					MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
							Long.valueOf(reportBean.getUuidBranch()));
					sb.append(msBranch.getBranchCode());
				}
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(trReportResultLog
				.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime().getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}
	
	@Override
	public List<Map<String, Object>> getFormListCombo(AuditContext callerId) {
		List<Map<String,Object>> result;
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
		String[][] paramsForm = { { "isActive", "1" }, {"subsystemName", subsysName}};
		result = this.getManagerDAO().selectAllNativeString(
				"SELECT form.UUID_FORM, form.FORM_NAME "
				+ "FROM MS_FORM form WITH (NOLOCK) "
					+ "JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
					+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
				+ "WHERE form.IS_ACTIVE = :isActive "
					+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName "
				+ "ORDER BY form.FORM_NAME", paramsForm);
		return result;
	}
}