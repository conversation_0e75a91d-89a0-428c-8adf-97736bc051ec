package com.adins.mss.businesslogic.impl.survey;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.survey.SurveyorPerformanceLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "rawtypes", "unchecked"})
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericSurveyPerformanceLogic extends BaseLogic 
		implements SurveyorPerformanceLogic, MessageSourceAware{
	private static final String[] HEADER_SUMMARY = { "No", "Branch Name", "User Name", 
		"New Order", "Assignment Task", "Pending Task", "Submitted Task", "Total" };
	private static final int[] SUMMARY_COLUMN_WIDTH = { 10 * 256, 30 * 256, 30 * 256, 20 * 256, 20 * 256, 
		20 * 256, 20 * 256, 20 * 256 };
	private static final String[] HEADER_DETAIL = { "No", "Branch Name", "User Id", "Full Name", "Application ID", "Assignment Date", 
		"Submitted Date", "Status Task", "Form Name" };
	private static final int[] DETAIL_COLUMN_WIDTH = { 10 * 256, 30 * 256, 30 * 256, 30 * 256, 20 * 256, 30 * 256, 30 * 256, 
		30 * 256, 30 * 256 };
	private static final String[] HEADER_DETAIL_0 = { "No", "User Id", "Full Name", "Application ID", 
		"Assignment Date", "Submitted Date", "Status Task", "Form Name"  };
	private static final int[] DETAIL_COLUMN_WIDTH_0 = { 10 * 256, 30 * 256, 30 * 256, 20 * 256, 30 * 256, 
		30 * 256, 30 * 256, 30 * 256 };
	private static final Logger LOG = LoggerFactory.getLogger(GenericSurveyPerformanceLogic.class);
	
	@Autowired
    private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    
    @Autowired
	private GlobalLogic globalLogic;
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}
	
	@Override
	public List getComboBranch(String branchId, AuditContext callerId) {
		List result = null;
		String[][] params = { { "branchId", branchId } };
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}
	
	@Override
	public List getSummary( String branchId, String userId, String startDate,
			String endDate, AuditContext callerId, String uuidForm) {
		List result = new ArrayList<>();
		String jobCodeSvy = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, callerId);
		AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, 
				Long.valueOf(callerId.getCallerId()));
		String[][] params = { 
				{"branchLogin", String.valueOf(amMsUser.getMsBranch().getUuidBranch())}, 
				{ "branchId", branchId }, { "userId", userId.isEmpty()?"%":userId }, 
				{ "startDate", startDate }, { "endDate", endDate }, { "uuidForm", uuidForm }, 
				{ "jobCodeSvy", jobCodeSvy } };
		String branch = "";
		List tempList = new ArrayList<>();
		Map tempMap = new HashMap<>();

		List list = Collections.emptyList();
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilderSummary(
				(Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
			.append("WITH N as ( ")
			.append("select * from TR_TASK_H tth ")
			.append("UNION ALL ")
			.append("select * from FINAL_TR_TASK_H fttrh ")
			.append(") ")
			.append("SELECT * FROM( ")
			.append("select msr.BRANCH_CODE code, msr.BRANCH_NAME branch, AMU.FULL_NAME name, ")
			.append("sum(case when tth.FLAG_SOURCE = 'MS' and tth.dtm_crt BETWEEN :startDate ")
			.append("and :endDate and resurvey_id is null then 1 else 0 end) newSurvey, ")
			.append("sum(case when mst.STATUS_CODE = 'N' and tth.ASSIGN_DATE BETWEEN :startDate ")
			.append("and :endDate then 1 else 0 end ) pendingSurvey, ")
			.append("sum(case when (tth.FLAG_SOURCE != 'MS' and tth.SUBMIT_DATE BETWEEN :startDate ")
			.append("and :endDate) or ")
			.append("(tth.FLAG_SOURCE = 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate ")
			.append("and resurvey_id is not null) then 1 else 0 end) submittedSurvey, ")
			.append("msr.uuid_branch ")
			.append("from N tth with (nolock)  ")
			.append("join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
			.append("join AM_MSSUBSYSTEM ams with (nolock) on mst.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM ")
			.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
			.append("FROM dbo.getCabangByLogin(:branchLogin)) msr on tth.UUID_BRANCH = msr.UUID_BRANCH ")
			.append("JOIN AM_MSUSER AMU with (nolock) ON tth.UUID_MS_USER = AMU.UUID_MS_USER ")
			.append("JOIN MS_FORM form with (nolock) ON tth.UUID_FORM = form.UUID_FORM ")
			.append("JOIN MS_JOB mj with (nolock) on mj.uuid_job = amu.UUID_JOB ")
			.append("where ams.SUBSYSTEM_NAME = 'MS' and form.is_active = '1' ")
			.append(paramsQueryString)
			.append("GROUP BY msr.BRANCH_CODE, msr.BRANCH_NAME, msr.UUID_BRANCH, AMU.FULL_NAME ")
			.append(")a ")
			.append("ORDER BY a.code, a.name ");
		paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[3][1]});
		paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"branchLogin", ((Object[][]) params)[0][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
		for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
		list = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
			
		for (int i = 0, j = 1, jml = 1; i < list.size(); i++, jml++) {
			Map map = (Map) list.get(i);
			Map temp = new HashMap();
				
			temp.put("d0", map.get("d2").toString());
			temp.put("d1", map.get("d3").toString());
			temp.put("d2", map.get("d4").toString());
			temp.put("d3", map.get("d5").toString());
			tempList.add(temp);

			branch = map.get("d0").toString();
			if ((i + 1) == list.size() || !branch.equals(((Map)list.get(i+1)).get("d0").toString())) {
				tempMap.put("branch", branch + " - " + map.get("d1").toString());
				tempMap.put("list", tempList);
				tempMap.put("seq", j);
				tempMap.put("branchId", map.get("d6").toString());		
				j++;
				if (!branch.isEmpty()) {
					tempMap.put("total", jml+1);
					result.add(tempMap);
					tempList = new ArrayList<>();
					tempMap = new HashMap<>();
					jml = 0;
				}
			}
		}
		return result;
	}
	
	/*
	 * 0 {"branchLogin", String.valueOf(amMsUser.getMsBranch().getUuidBranch())},
	 * 1 { "branchId", branchId }, 
	 * 2 { "userId", userId.isEmpty()?"%":userId },
	 * 3 { "startDate", startDate }, 
	 * 4 { "endDate", endDate }, 
	 * 5 { "uuidForm", uuidForm }, 
	 * 6 { "jobCodeSvy", jobCodeSvy } 
	 */
	private StringBuilder sqlPagingBuilderSummary(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append(" and tth.UUID_BRANCH = :branchId ");
			paramStack.push(new Object[]{"branchId", Long.valueOf((String) params[1][1])});
		}
		
		//---UUID_USER
		if (!StringUtils.equals("%", (String) params[2][1])) {
			sb.append("and tth.UUID_MS_USER = :userId ");
			paramStack.push(new Object[]{"userId", Long.valueOf((String) params[2][1])});
		}
		
		//---UUID_FORM
		if (!StringUtils.equals("%", (String) params[5][1])) {
			sb.append("and form.UUID_FORM = :uuidForm ");
			paramStack.push(new Object[]{"uuidForm", Long.valueOf((String) params[5][1])});
		}
		sb.append("and mj.job_code = :jobCodeSvy ");
		paramStack.push(new Object[]{"jobCodeSvy", (String) params[6][1]});
		return sb;
	}
	
	@Override
	public List getDetail(String branchId, String userId, String startDate,
			String endDate, AuditContext callerId, String uuidForm, long userBranch, int start, int end) {
		List result = null;
		String user = StringUtils.isBlank(userId)?"%":userId;
		String branch = StringUtils.isBlank(branchId) || "0".equals(branchId) ?"%":branchId;
		
		StringBuilder queryBuilder = new StringBuilder();
		Stack<Object[]> paramsStack = new Stack<>();
		
		//FOR GENERATE REPORT
		if (start == 999 && end == 999) {
			Object[][] params = { { "branchId", branch }, { "userId", user }, 
					{ "startDate", startDate }, { "endDate", endDate }, { "uuidForm", uuidForm },
					{ "userBranch", userBranch } };
			paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[2][1]});
			paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[3][1]});
			paramsStack.push(new Object[]{"userBranch", ((Object[][]) params)[5][1]});
					
			StringBuilder paramsQueryString = this.sqlPagingBuilderDetail((Object[][]) params, paramsStack);
			queryBuilder.append("SELECT * FROM ( ")
				.append("select tth.UUID_TASK_H, ISNULL(APPL_NO, '-') as applNo, ASSIGN_DATE, ")
				.append("SUBMIT_DATE, mst.STATUS_TASK_DESC, form.FORM_NAME, AMU.UNIQUE_ID, amu.FULL_NAME as fullname, msr.BRANCH_NAME ")
				.append("from TR_TASK_H tth with (nolock)  ")
				.append("join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("join AM_MSSUBSYSTEM ams with (nolock) on mst.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM ") 
				.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
				.append("FROM dbo.getCabangByLogin(:userBranch)) msr on tth.UUID_BRANCH = msr.UUID_BRANCH ")
				.append("JOIN AM_MSUSER AMU with (nolock) ON tth.UUID_MS_USER = AMU.UUID_MS_USER ")
				.append("JOIN MS_FORM form with (nolock) ON tth.UUID_FORM = form.UUID_FORM ")
				.append("where ams.SUBSYSTEM_NAME = 'MS' ") 
				.append("and ((mst.STATUS_CODE = 'N' and tth.ASSIGN_DATE BETWEEN :startDate and :endDate)  ")
				.append("or (tth.FLAG_SOURCE = 'MS' and tth.dtm_crt BETWEEN :startDate and :endDate ")
				.append("and resurvey_id is null) ")
				.append("or (tth.FLAG_SOURCE != 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate) ")
				.append("or (tth.FLAG_SOURCE = 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate ")
				.append("and resurvey_id is not null)) ")
				.append("and form.is_active = '1' ")
				.append(paramsQueryString)
				.append("UNION ALL ")
				.append("select tth.UUID_TASK_H, ISNULL(APPL_NO, '-') as applNo, ASSIGN_DATE, SUBMIT_DATE, ")
				.append("mst.STATUS_TASK_DESC, form.FORM_NAME, AMU.UNIQUE_ID, amu.FULL_NAME as fullname, msr.BRANCH_NAME ")
				.append("from FINAL_TR_TASK_H tth with (nolock)  ")
				.append("join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("join AM_MSSUBSYSTEM ams with (nolock) on mst.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM ") 
				.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
				.append("FROM dbo.getCabangByLogin(:userBranch)) msr on tth.UUID_BRANCH = msr.UUID_BRANCH ")
				.append("JOIN AM_MSUSER AMU with (nolock) ON tth.UUID_MS_USER = AMU.UUID_MS_USER ")
				.append("JOIN MS_FORM form with (nolock) ON tth.UUID_FORM = form.UUID_FORM ")
				.append("where ams.SUBSYSTEM_NAME = 'MS'  ")
				.append("and form.is_active = '1' ")
				.append("and ((mst.STATUS_CODE = 'N' and tth.ASSIGN_DATE BETWEEN :startDate and :endDate)  ")
				.append("or (tth.FLAG_SOURCE = 'MS' and tth.dtm_crt BETWEEN :startDate and :endDate ")
				.append("and resurvey_id is null) ")
				.append("or (tth.FLAG_SOURCE != 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate) ")
				.append("or (tth.FLAG_SOURCE = 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate ")
				.append("and resurvey_id is not null)) ")
				.append(paramsQueryString)
				.append(") a ")
				.append("ORDER BY a.fullname, a.ASSIGN_DATE ");
		} else {
			Object[][] params = { { "branchId", branch }, { "userId", user }, 
					{ "startDate", startDate }, { "endDate", endDate }, { "uuidForm", uuidForm },
					{ "userBranch", userBranch }, { "start", start }, { "end", end } };
			paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[2][1]});
			paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[3][1]});
			paramsStack.push(new Object[]{"userBranch", ((Object[][]) params)[5][1]});
			paramsStack.push(new Object[]{"start", ((Object[][]) params)[6][1]});
			paramsStack.push(new Object[]{"end", ((Object[][]) params)[7][1]});
					
			StringBuilder paramsQueryString = this.sqlPagingBuilderDetail((Object[][]) params, paramsStack);
			queryBuilder.append("SELECT * FROM ( ")
				.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
				.append("SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.fullname, c.ASSIGN_DATE ) AS rownum FROM ( ")
				.append("select tth.UUID_TASK_H, ISNULL(APPL_NO, '-') as applNo, ASSIGN_DATE, ")
				.append("SUBMIT_DATE, mst.STATUS_TASK_DESC, form.FORM_NAME, AMU.UNIQUE_ID, amu.FULL_NAME as fullname, msr.BRANCH_NAME ")
				.append("from TR_TASK_H tth with (nolock)  ")
				.append("join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("join AM_MSSUBSYSTEM ams with (nolock) on mst.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM ") 
				.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
				.append("FROM dbo.getCabangByLogin(:userBranch)) msr on tth.UUID_BRANCH = msr.UUID_BRANCH ")
				.append("JOIN AM_MSUSER AMU with (nolock) ON tth.UUID_MS_USER = AMU.UUID_MS_USER ")
				.append("JOIN MS_FORM form with (nolock) ON tth.UUID_FORM = form.UUID_FORM ")
				.append("where ams.SUBSYSTEM_NAME = 'MS' ") 
				.append("and ((mst.STATUS_CODE = 'N' and tth.ASSIGN_DATE BETWEEN :startDate and :endDate)  ")
				.append("or (tth.FLAG_SOURCE = 'MS' and tth.dtm_crt BETWEEN :startDate and :endDate ")
				.append("and resurvey_id is null) ")
				.append("or (tth.FLAG_SOURCE != 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate) ")
				.append("or (tth.FLAG_SOURCE = 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate ")
				.append("and resurvey_id is not null)) ")
				.append("and form.is_active = '1' ")
				.append(paramsQueryString)
				.append("UNION ALL ")
				.append("select tth.UUID_TASK_H, ISNULL(APPL_NO, '-') as applNo, ASSIGN_DATE, SUBMIT_DATE, ")
				.append("mst.STATUS_TASK_DESC, form.FORM_NAME, AMU.UNIQUE_ID, amu.FULL_NAME as fullname, msr.BRANCH_NAME ")
				.append("from FINAL_TR_TASK_H tth with (nolock)  ")
				.append("join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
				.append("join AM_MSSUBSYSTEM ams with (nolock) on mst.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM ") 
				.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
				.append("FROM dbo.getCabangByLogin(:userBranch)) msr on tth.UUID_BRANCH = msr.UUID_BRANCH ")
				.append("JOIN AM_MSUSER AMU with (nolock) ON tth.UUID_MS_USER = AMU.UUID_MS_USER ")
				.append("JOIN MS_FORM form with (nolock) ON tth.UUID_FORM = form.UUID_FORM ")
				.append("where ams.SUBSYSTEM_NAME = 'MS'  ")
				.append("and form.is_active = '1' ")
				.append("and ((mst.STATUS_CODE = 'N' and tth.ASSIGN_DATE BETWEEN :startDate and :endDate)  ")
				.append("or (tth.FLAG_SOURCE = 'MS' and tth.dtm_crt BETWEEN :startDate and :endDate ")
				.append("and resurvey_id is null) ")
				.append("or (tth.FLAG_SOURCE != 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate) ")
				.append("or (tth.FLAG_SOURCE = 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate ")
				.append("and resurvey_id is not null)) ")
				.append(paramsQueryString)
				.append(") c ")
				.append(") a WHERE a.rownum <= :end ")
				.append(") b WHERE b.recnum >= :start ");
		}
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		return result;
	}
	
	@Override
	public int getDetailCount(String branchId, String userId, String startDate,
			String endDate, AuditContext callerId, String uuidForm, long userBranch) {
		int resultCount = 0;
		String user = StringUtils.isBlank(userId)?"%":userId;
		String branch = StringUtils.isBlank(branchId) || "0".equals(branchId) ?"%":branchId;
		Object[][] params = { { "branchId", branch }, { "userId", user }, 
				{ "startDate", startDate }, { "endDate", endDate }, { "uuidForm", uuidForm },
				{ "userBranch", userBranch } };
					
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilderDetail((Object[][]) params, paramsStack);
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT count(1) FROM ( ")
			.append("select tth.UUID_TASK_H, ISNULL(APPL_NO, '-') as applNo, ASSIGN_DATE, ")
			.append("SUBMIT_DATE, mst.STATUS_TASK_DESC, form.FORM_NAME, AMU.UNIQUE_ID, amu.FULL_NAME as fullname, msr.BRANCH_NAME ")
			.append("from TR_TASK_H tth with (nolock)  ")
			.append("join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
			.append("join AM_MSSUBSYSTEM ams with (nolock) on mst.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM ") 
			.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
			.append("FROM dbo.getCabangByLogin(:userBranch)) msr on tth.UUID_BRANCH = msr.UUID_BRANCH ")
			.append("JOIN AM_MSUSER AMU with (nolock) ON tth.UUID_MS_USER = AMU.UUID_MS_USER ")
			.append("JOIN MS_FORM form with (nolock) ON tth.UUID_FORM = form.UUID_FORM ")
			.append("where ams.SUBSYSTEM_NAME = 'MS' ") 
			.append("and ((mst.STATUS_CODE = 'N' and tth.ASSIGN_DATE BETWEEN :startDate and :endDate)  ")
			.append("or (tth.FLAG_SOURCE = 'MS' and tth.dtm_crt BETWEEN :startDate and :endDate ")
			.append("and resurvey_id is null) ")
			.append("or (tth.FLAG_SOURCE != 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate) ")
			.append("or (tth.FLAG_SOURCE = 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate ")
			.append("and resurvey_id is not null)) ")
			.append("and form.is_active = '1' ")
			.append(paramsQueryString)
			.append("UNION ALL ")
			.append("select tth.UUID_TASK_H, ISNULL(APPL_NO, '-') as applNo, ASSIGN_DATE, SUBMIT_DATE, ")
			.append("mst.STATUS_TASK_DESC, form.FORM_NAME, AMU.UNIQUE_ID, amu.FULL_NAME as fullname, msr.BRANCH_NAME ")
			.append("from FINAL_TR_TASK_H tth with (nolock)  ")
			.append("join MS_STATUSTASK mst with (nolock) on tth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK ")
			.append("join AM_MSSUBSYSTEM ams with (nolock) on mst.UUID_MS_SUBSYSTEM = ams.UUID_MS_SUBSYSTEM ") 
			.append("join (SELECT keyValue as UUID_BRANCH, BRANCH_CODE, BRANCH_NAME ")
			.append("FROM dbo.getCabangByLogin(:userBranch)) msr on tth.UUID_BRANCH = msr.UUID_BRANCH ")
			.append("JOIN AM_MSUSER AMU with (nolock) ON tth.UUID_MS_USER = AMU.UUID_MS_USER ")
			.append("JOIN MS_FORM form with (nolock) ON tth.UUID_FORM = form.UUID_FORM ")
			.append("where ams.SUBSYSTEM_NAME = 'MS'  ")
			.append("and form.is_active = '1' ")
			.append("and ((mst.STATUS_CODE = 'N' and tth.ASSIGN_DATE BETWEEN :startDate and :endDate)  ")
			.append("or (tth.FLAG_SOURCE = 'MS' and tth.dtm_crt BETWEEN :startDate and :endDate ")
			.append("and resurvey_id is null) ")
			.append("or (tth.FLAG_SOURCE != 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate) ")
			.append("or (tth.FLAG_SOURCE = 'MS' and tth.SUBMIT_DATE BETWEEN :startDate and :endDate ")
			.append("and resurvey_id is not null)) ")
			.append(paramsQueryString)
			.append(") c ");
		paramsStack.push(new Object[]{"startDate", ((Object[][]) params)[2][1]});
		paramsStack.push(new Object[]{"endDate", ((Object[][]) params)[3][1]});
		paramsStack.push(new Object[]{"userBranch", ((Object[][]) params)[5][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    resultCount = (int) this.getManagerDAO().selectOneNativeString(queryBuilder.toString(), sqlParams);
		return resultCount;
	}
	
	/*
	 * 0 { { "branchId", branchId }, 
	 * 1 { "userId", user }, 
	 * 2 { "startDate", startDate }, 
	 * 3 { "endDate", endDate }, 
	 * 4 { "uuidForm", uuidForm } }
	 */
	private StringBuilder sqlPagingBuilderDetail(Object[][] params, Stack<Object[]> paramStack) {
		if (params == null) {
			return new StringBuilder();
		}
		StringBuilder sb = new StringBuilder();
		//---UUID_BRANCH
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and tth.UUID_BRANCH = :branchId ");
			paramStack.push(new Object[]{"branchId", Long.valueOf((String) params[0][1])});
		}

		//---UUID_USER
		if (!StringUtils.equals("%", (String) params[1][1])) {
			sb.append("and tth.UUID_MS_USER = :userId ");
			paramStack.push(new Object[]{"userId", Long.valueOf((String) params[1][1])});
		}
		
		//---UUID_FORM
		if (!StringUtils.equals("%", (String) params[4][1])) {
			sb.append("and form.UUID_FORM = :uuidForm ");
			paramStack.push(new Object[]{"uuidForm", Long.valueOf((String) params[4][1])});
		}
		return sb;
	}

	private Map<String,String> getUserBranch(String branchId, String userId, AuditContext callerId) {
		Map<String,String> result = new HashMap<>();
			
		AmMsuser user = null;
		if (StringUtils.isNotBlank(userId)) {
			user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(userId));
		}
		
		MsBranch branch = null;
		if (StringUtils.isNotBlank(branchId) && !"%".equals(branchId)) {
			branch = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(branchId));
		}
		
		result.put("user", user!=null?user.getFullName():"All User");
		result.put("branch", branch!=null?branch.getBranchCode()+" - "+branch.getBranchName():"All Branch");
		return result;
	}
	
	@Override
	public byte[] exportExcel(String branchId, String userId, String startDate,
			String endDate, String type, AuditContext callerId, String uuidForm) {	
		XSSFWorkbook workbook = this.createXlsTemplate(branchId, userId, startDate, 
				endDate, type, callerId, uuidForm);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}
	
	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(String branchId, String userId,
			String startDate, String endDate, String type, AuditContext callerId, String uuidForm) {
		AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, 
				Long.valueOf(callerId.getCallerId()));
		
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setSubsystemCode(amMsUser.getAmMssubsystem().getSubsystemName());
		reportBean.setUuidLoginId(String.valueOf(amMsUser.getUuidMsUser()));
		reportBean.setUuidForm(uuidForm);
		reportBean.setUuidBranch(branchId);
		reportBean.setUuidUser(userId);
		reportBean.setStartDate(startDate);
		reportBean.setEndDate(endDate);
		reportBean.setType(type);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("Survey Performance Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_SVY_PERFORMANCE.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}
	
	private XSSFWorkbook createXlsTemplate(String branchId, String userId, String startDate,
			String endDate, String type, AuditContext callerId, String uuidForm) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Report Svy Performance");
			if (type.equalsIgnoreCase("0")) {
				List result= this.getSummary(branchId, userId, startDate, endDate, callerId, uuidForm);
				this.createDataSummary(workbook, sheet, result, startDate, endDate);
			}
			else {
				AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));
				List result= this.getDetail(branchId, userId, startDate, endDate, callerId, uuidForm, usr.getMsBranch().getUuidBranch(), 999, 999);
				if (type.equals("1")) {
					this.createDataDetail(workbook, sheet, result, this.getUserBranch(branchId, 
							userId, callerId), startDate, endDate);
				}
				else {
					this.createDataDetailFromSummary(workbook, sheet, result, this.getUserBranch(
							branchId, userId, callerId), startDate, endDate);
				}
			}
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}
	
	private void createDataSummary(XSSFWorkbook workbook, XSSFSheet sheet, List result, String startDate, 
			String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		XSSFCell cellHead = rowHead.createCell(0);
		cellHead.setCellValue("REPORT SUMMARY PERIOD "+ startDate.substring(0, 10) + " - " 
				+ endDate.substring(0, 10));
		cellHead.setCellStyle(styles.get("header"));
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0,HEADER_SUMMARY.length-2));
		XSSFRow rowHeader = sheet.createRow(rowcell++);
		XSSFRow rowHeader1 = sheet.createRow(rowcell++);
		for (int i = 0; i < HEADER_SUMMARY.length; i++) {		
			if (i >= 4) {
				if (i == 4){
					XSSFCell cell = rowHeader.createCell(i);
					cell.setCellValue(HEADER_SUMMARY[i]);
					cell.setCellStyle(styles.get("header"));
					
					XSSFCell cell1 = rowHeader.createCell(i+1);
					cell1.setCellStyle(styles.get("header"));
					sheet.setColumnWidth(i+1, SUMMARY_COLUMN_WIDTH[i+1]);
					sheet.addMergedRegion(new CellRangeAddress(1, 1, 4, 5));
				}
				else {
					XSSFCell cell = i != 7? rowHeader1.createCell(i-1): rowHeader.createCell(i-1);
					cell.setCellValue(HEADER_SUMMARY[i]);
					cell.setCellStyle(styles.get("header"));
					sheet.setColumnWidth(i-1, SUMMARY_COLUMN_WIDTH[i-1]);
					
					if (i == 7) {
						XSSFCell cell1 = rowHeader1.createCell(i-1);
						cell1.setCellStyle(styles.get("header"));
						sheet.setColumnWidth(i-1, SUMMARY_COLUMN_WIDTH[i-1]);
						sheet.addMergedRegion(new CellRangeAddress(1, 2, i-1, i-1));
					}
				}
			}
			else {
				XSSFCell cell = rowHeader.createCell(i);
				cell.setCellValue(HEADER_SUMMARY[i]);
				cell.setCellStyle(styles.get("header"));
				sheet.setColumnWidth(i, SUMMARY_COLUMN_WIDTH[i]);
				
				XSSFCell cell1 = rowHeader1.createCell(i);
				cell1.setCellStyle(styles.get("header"));
				sheet.setColumnWidth(i, SUMMARY_COLUMN_WIDTH[i]);
				sheet.addMergedRegion(new CellRangeAddress(1, 2, i, i));
			}
		}
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));			
			
			//Branch cell
			XSSFCell cellBranch = rowData.createCell(1);
			cellBranch.setCellValue(temp.get("branch").toString());
			cellBranch.setCellStyle(styles.get("cell"));
			
			List list = (List) temp.get("list");
			for (int k = 0; k < list.size(); k++) {
				Map map = (Map) list.get(k);
				//data cell
				if (k != 0) {
					XSSFCell cell = rowData.createCell(0);
					cell.setCellStyle(styles.get("cell"));
					
					XSSFCell cell1 = rowData.createCell(0);
					cell1.setCellStyle(styles.get("cell"));
				}
				int tot = 0;
				for (int j = 2; j < HEADER_SUMMARY.length-1; j++) {
					XSSFCell cell = rowData.createCell(j);
					if (j == 2) {
						cell.setCellValue(map.get("d"+(j-2)).toString());
						cell.setCellStyle(styles.get("cell"));
					}
					else {
						if (j == HEADER_SUMMARY.length-2) {
							cell.setCellValue(tot);
							cell.setCellStyle(styles.get("cell"));
						}
						else {
							cell.setCellValue(Integer.parseInt(map.get("d"+(j-2)).toString()));
							cell.setCellStyle(styles.get("cell"));
							tot += Integer.parseInt(map.get("d"+(j-2)).toString());
						}						
					}
				}
				if(list.size()-1 != k) {
				     rowData = sheet.createRow(rowcell++);
				}
			}
			//rowcell += Integer.parseInt(temp.get("total").toString());
			if(rowcell+1-Integer.parseInt(temp.get("total").toString()) < rowcell-1) {
				sheet.addMergedRegion(new CellRangeAddress(rowcell+1-Integer.parseInt(temp.get("total").toString()), rowcell-1, 0, 0));
				sheet.addMergedRegion(new CellRangeAddress(rowcell+1-Integer.parseInt(temp.get("total").toString()), rowcell-1, 1, 1));
			}
		} 
		
		//Total Data
		XSSFRow rowData = sheet.createRow(rowcell);
		XSSFCell cellNo = rowData.createCell(0);
		cellNo.setCellValue("Total");
		cellNo.setCellStyle(styles.get("header"));
		
		XSSFCell cellNo1 = rowData.createCell(1);
		cellNo1.setCellStyle(styles.get("header"));
		
		XSSFCell cell = rowData.createCell(2);
        String ref = (char)('C') + "4:" + (char)('C') + (rowcell);
        cell.setCellFormula("COUNTA(" + ref + ")");	
        cell.setCellStyle(styles.get("header"));
        
        for (int i = 0; i < HEADER_SUMMARY.length-4; i++) {
        	cell = rowData.createCell(i+3);
        	ref = (char)('D'+i) + "4:" + (char)('D'+i) + (rowcell);
            cell.setCellFormula("SUM(" + ref + ")");
            cell.setCellStyle(styles.get("header"));
        }
        //End Total Data       	
	}
	
	private void createDataDetail(XSSFWorkbook workbook, XSSFSheet sheet, List result, Map userBranch, 
			String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader1 = sheet.createRow(rowcell++);
		XSSFCell cell = rowHeader1.createCell(0);
		cell.setCellValue("User");
		XSSFCell cell1 = rowHeader1.createCell(1);
		cell1.setCellValue(userBranch.get("user").toString());
		
		XSSFRow rowHeader2 = sheet.createRow(rowcell++);
		XSSFCell cell2 = rowHeader2.createCell(0);
		cell2.setCellValue("Branch");
		XSSFCell cell3 = rowHeader2.createCell(1);
		cell3.setCellValue(userBranch.get("branch").toString());
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_DETAIL.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT DETAIL PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell4 = rowHeader.createCell(i);
			cell4.setCellValue(HEADER_DETAIL[i]);
			cell4.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, DETAIL_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_DETAIL.length-1));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			String cellData[] = { "d8", "d6", "d7", "d1", "d2", "d3", "d4", "d5" };
			//data cell
			for (int j = 0; j < cellData.length; j++) {
				XSSFCell cell4 = rowData.createCell(j+1);
				if (temp.get(cellData[j]) != null) {
					cell4.setCellValue(temp.get(cellData[j]).toString());
				}
				cell4.setCellStyle(styles.get("cell"));	
			}
		}       	
	}	
	
	private void createDataDetailFromSummary(XSSFWorkbook workbook, XSSFSheet sheet, List result, 
			Map userBranch, String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell=0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		
		XSSFRow rowHeader1 = sheet.createRow(rowcell++);
		XSSFCell cell = rowHeader1.createCell(0);
		cell.setCellValue("User");
		XSSFCell cell1 = rowHeader1.createCell(1);
		cell1.setCellValue(userBranch.get("user").toString());
		
		XSSFRow rowHeader2 = sheet.createRow(rowcell++);
		XSSFCell cell2 = rowHeader2.createCell(0);
		cell2.setCellValue("Branch");
		XSSFCell cell3 = rowHeader2.createCell(1);
		cell3.setCellValue(userBranch.get("branch").toString());
		
		XSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < HEADER_DETAIL_0.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT DETAIL PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get("header"));
			XSSFCell cell4 = rowHeader.createCell(i);
			cell4.setCellValue(HEADER_DETAIL_0[i]);
			cell4.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, DETAIL_COLUMN_WIDTH_0[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_DETAIL_0.length-1));
		//Data row
		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i+1);
			cellNo.setCellStyle(styles.get("cell"));
			String cellData[] = { "d6", "d7", "d1", "d2", "d3", "d4", "d5" };
			//data cell
			for (int j = 0; j < cellData.length; j++) {
				XSSFCell cell4 = rowData.createCell(j+1);
				if (temp.get(cellData[j]) != null) {
					cell4.setCellValue(temp.get(cellData[j]).toString());
				}
				cell4.setCellStyle(styles.get("cell"));	
			}
		}       	
	}	
	
	private static Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
        CellStyle style;

        style = wb.createCellStyle();
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styles.put("cell", style);

        style = wb.createCellStyle();
        Font font = wb.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(CellStyle.SOLID_FOREGROUND);
        styles.put("header",style );
        return styles;
    }

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(reportBean.getUuidBranch(), 
				reportBean.getUuidUser(), reportBean.getStartDate(), reportBean.getEndDate(), 
				reportBean.getType(), 
				new AuditContext(String.valueOf(trReportResultLog.getAmMsuser().getUuidMsUser())), 
				reportBean.getUuidForm());
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("SurveyPerformance_");
			
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
				reportBean.setUuidBranch(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
						Long.valueOf(reportBean.getUuidBranch()));
				sb.append(msBranch.getBranchCode());
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if ("0".equals(reportBean.getUuidUser()) || "%".equals(reportBean.getUuidUser())) {
				reportBean.setUuidUser(null);
			}
			if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
				AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, 
						Long.valueOf(reportBean.getUuidUser()));
				sb.append(user.getLoginId());
			} 
			else {
				sb.append("ALL");
			}
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(trReportResultLog
				.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime().getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}
	
	@Override
	public List getFormListCombo(AuditContext callerId) {
		List result = null;
		Map callerIdMap = callerId.getParameters();
		String subsysName = (String) callerIdMap.get(AuditContext.KEY_PARAMS_SUBSYSTEMNAME);
		String[][] paramsForm = { { "isActive", "1" }, {"subsystemName", subsysName}};
		result = this.getManagerDAO().selectAllNativeString(
				"SELECT form.UUID_FORM, form.FORM_NAME "
				+ "FROM MS_FORM form WITH (NOLOCK) "
					+ "JOIN AM_MSSUBSYSTEM subsystem WITH (NOLOCK) "
					+ "ON form.UUID_MS_SUBSYSTEM = subsystem.UUID_MS_SUBSYSTEM "
				+ "WHERE form.IS_ACTIVE = :isActive "
					+ "AND subsystem.SUBSYSTEM_NAME = :subsystemName "
					+ "ORDER BY form.FORM_NAME ASC", paramsForm);
		
		return result;
	}
}
