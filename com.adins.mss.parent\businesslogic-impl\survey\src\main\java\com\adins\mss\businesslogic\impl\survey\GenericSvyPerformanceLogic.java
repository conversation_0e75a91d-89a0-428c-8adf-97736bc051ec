package com.adins.mss.businesslogic.impl.survey;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.service.base.model.KeyValue;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.SurveyorPerformanceLogic;
import com.adins.mss.businesslogic.api.survey.SvyPerformanceLogic;
import com.adins.mss.model.AmMsuser;

public class GenericSvyPerformanceLogic extends BaseLogic implements SvyPerformanceLogic  {
//	private static final Logger LOG = LoggerFactory.getLogger(GenericSvyPerformanceLogic.class);
	
	private SurveyorPerformanceLogic svyPerformanceLogic;
	
	public void setSvyPerformanceLogic(SurveyorPerformanceLogic svyPerformanceLogic) {
		this.svyPerformanceLogic = svyPerformanceLogic;
	}
	
	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public List<KeyValue> resultServer(Date date1, Date date2, Integer month, AuditContext callerId){
		//create object 
		KeyValue newT = new KeyValue();
		KeyValue pendT = new KeyValue();
		KeyValue subT = new KeyValue();
		
		List<KeyValue> result = new ArrayList<KeyValue>();
		String uuidMsUser = callerId.getCallerId();
		
		AmMsuser usr = this.getManagerDAO().selectOne(AmMsuser.class, Long.parseLong(uuidMsUser));
		
		String startDate = "";
		String endDate = "";
		/* Daily */
		if(date1 != null && (date2 == null && month == null)){
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
//			String tmpStartDate = df.format(date1)+" 00:00:00.000";
//			String tmpEndDate = df.format(date1)+" 23:59:59.997";
//			Object[][] param = {{"uuidMsUser", uuidMsUser}, {"startDate", tmpStartDate},{"endDate",tmpEndDate}};
//			//call the hbm
//			Object[] taskN = (Object[]) this.getManagerDAO().selectOneNative("services.survey.svyPerformanceNew1", param);
//			Object[] taskP = (Object[]) this.getManagerDAO().selectOneNative("services.survey.svyPerformancePend1", param);
//			Object[] taskS = (Object[]) this.getManagerDAO().selectOneNative("services.survey.svyPerformanceSub1", param);
//			
//			//set the key and the value
//			newT.setKey((String)taskN[0]);
//			newT.setValue((String)taskN[1].toString());
//			pendT.setKey((String)taskP[0]);
//			pendT.setValue((String)taskP[1].toString());
//			subT.setKey((String)taskS[0]);
//			subT.setValue((String)taskS[1].toString());
//			
//			if(result != null){
//				result.add(newT);
//				result.add(pendT);
//				result.add(subT);	
//			}	
			startDate = df.format(date1)+" 00:00:00.000";
			endDate = df.format(date1)+" 23:59:59.997";
		}
		else if((date1 != null && date2 != null) && month == null){/* Range */
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			startDate = df.format(date1)+" 00:00:00.000";
			endDate = df.format(date2)+" 23:59:59.997";
//			String tmpStartDate = df.format(date1)+" 00:00:00.000";
//			String tmpEndDate = df.format(date2)+" 23:59:59.997";
//			Object[][] param = {{"uuidMsUser", uuidMsUser}, {"date1", tmpStartDate}, {"date2", tmpEndDate}};
//			//call the hbm
//			Object[] taskN = (Object[]) this.getManagerDAO().selectOneNative("services.survey.svyPerformanceNew2", param);
//			Object[] taskP = (Object[]) this.getManagerDAO().selectOneNative("services.survey.svyPerformancePend2", param);
//			Object[] taskS = (Object[]) this.getManagerDAO().selectOneNative("services.survey.svyPerformanceSub2", param);
//			
//			//set the key and value
//			newT.setKey((String)taskN[0]);
//			newT.setValue((String)taskN[1].toString());
//			pendT.setKey((String)taskP[0]);
//			pendT.setValue((String)taskP[1].toString());
//			subT.setKey((String)taskS[0]);
//			subT.setValue((String)taskS[1].toString());
//			
//			if(result != null){
//				result.add(newT);
//				result.add(pendT);
//				result.add(subT);	
//			}		
		}
		else if((date1 == null && date2 == null) && month != null){/* monthly */	
			int year = Calendar.getInstance().get(Calendar.YEAR);
			Calendar cal = new GregorianCalendar(year, month-1, 1);
			int daysInMonth= cal.getActualMaximum(Calendar.DAY_OF_MONTH);
			startDate = year+"-"+month+"-1 "+"00:00:00.000";
			endDate = year+"-"+month+"-"+daysInMonth+" 23:59:59.997";
//			Object[][] param = {{"uuidMsUser", uuidMsUser}, {"startDate", startDate},{"endDate",endDate}};
//			//call the hbm
//			Object[] taskN = (Object[]) this.getManagerDAO().selectOneNative("services.survey.svyPerformanceNew3", param);
//			Object[] taskP = (Object[]) this.getManagerDAO().selectOneNative("services.survey.svyPerformancePend3", param);
//			Object[] taskS = (Object[]) this.getManagerDAO().selectOneNative("services.survey.svyPerformanceSub3", param);
//			
//			//set the key and value
//			newT.setKey((String)taskN[0]);
//			newT.setValue((String)taskN[1].toString());
//			pendT.setKey((String)taskP[0]);
//			pendT.setValue((String)taskP[1].toString());
//			subT.setKey((String)taskS[0]);
//			subT.setValue((String)taskS[1].toString());
//			
//			if(result != null){
//				result.add(newT);
//				result.add(pendT);
//				result.add(subT);	
//			}			
		}
		
		List list = (List) svyPerformanceLogic.getSummary(String.valueOf(usr.getMsBranch().getUuidBranch()), String.valueOf(usr.getUuidMsUser()), 
					startDate, endDate, callerId, "%");
		String totalNew = "0";
		String totalPending = "0";
		String totalSubmit = "0";
		if (!list.isEmpty()) {
			Map mapList = (Map) list.get(0);
			List listMapTotal = (List) mapList.get("list");
			if (!listMapTotal.isEmpty()) {
				Map mapTotal = (Map) listMapTotal.get(0);
				totalNew = mapTotal.get("d1")+"";
				totalPending = mapTotal.get("d2")+"";
				totalSubmit = mapTotal.get("d3")+"";
			}
		}
		
		newT.setKey("New Order");
		newT.setValue(totalNew);
		pendT.setKey("Pending Task");
		pendT.setValue(totalPending);
		subT.setKey("Submitted Task");
		subT.setValue(totalSubmit);
		result.add(newT);
		result.add(pendT);
		result.add(subT);
		return result;
	}
}
