package com.adins.mss.businesslogic.impl.survey;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.TaskClaimbyCustomerServiceLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TblPoloData;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.services.model.common.AddTaskPoloRequest;
import com.adins.mss.services.model.common.GetReferantorResponse;
import com.adins.mss.services.model.newconfins.UpdateStatusMainDealerResponse;
import com.google.gson.Gson;

@SuppressWarnings({"rawtypes", "unused"})
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericTaskClaimbyCustomerServiceLogic extends BaseLogic implements TaskClaimbyCustomerServiceLogic, MessageSourceAware {
	private static final Logger LOG = LoggerFactory.getLogger(GenericTaskClaimbyCustomerServiceLogic.class);
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	
	private CommonLogic commonLogic;
	private IntFormLogic intFormLogic;
	private TaskDistributionLogic taskDistributionLogic;
	
	private Gson gson = new Gson();
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}	
	
	public CommonLogic getCommonLogic() {
		return commonLogic;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}
	
	public void setTaskDistributionLogic(TaskDistributionLogic taskDistributionLogic) {
		this.taskDistributionLogic = taskDistributionLogic;
	}
	
	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, Object> listTaskH(Object params, AmMsuser amMsuser, Object paramsCnt, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<String, Object>();
		String uuidStatusTask = this.getUuidStatusTask(
				GlobalVal.SURVEY_STATUS_TASK_UNASSIGN, amMsuser, callerId);
		String[][] prm = (String[][]) params;
		prm[1][1] = uuidStatusTask;
		String[][] prmCnt = (String[][]) paramsCnt;
		prmCnt[1][1] = uuidStatusTask;

		List<Map<String, Object>> resultUnassigned = Collections.EMPTY_LIST;
		Stack<Object[]> paramsStack = new Stack<>();
		Map<String, String> paramsQuery = this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
		String where = paramsQuery.get("sb");
		Stack<Object[]> paramsStackCount = new Stack<>();
		Map<String, String> paramsQueryCount = this.sqlPagingBuilder((Object[][]) params, paramsStackCount, callerId);
		String whereCout = paramsQueryCount.get("sb");
		StringBuilder queryJoin = new StringBuilder();
		
		queryJoin.append(" LEFT JOIN MS_GROUPTASK msg WITH(NOLOCK) on msg.UUID_TASK_H = tth.UUID_TASK_H ");
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT * from ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("select DISTINCT *, ROW_NUMBER() OVER (ORDER BY task_id) AS rownum from ")
			.append("(select tth.UUID_TASK_H, tth.TASK_ID, tth.APPL_NO, FORM_NAME, tth.CUSTOMER_NAME, tth.CUSTOMER_ADDRESS, ")
			.append("CASE WHEN EXISTS ( SELECT 1 FROM TR_TASK_D ttd with(nolock)) ") 
			.append("THEN 'CLAIM' ELSE NULL END AS TASK_CLAIM, tth.CUSTOMER_PHONE, msbr.KONVEN_SYARIAH, CONVERT(VARCHAR,tth.assign_date,120)assign_date, CONVERT(VARCHAR,ISNULL(tth.DTM_UPD,tth.assign_date),120)DTM_UPD, ")
			.append("isnull(tth.IS_PILOTING_CAE, '') as ispilotingcae ")
			.append("from tr_task_h tth with (nolock) ")
			.append("JOIN MS_BRANCH MSBR WITH (NOLOCK) ON MSBR.UUID_BRANCH = TTH.UUID_BRANCH ")
			.append("JOIN MS_FORM MF WITH (NOLOCK) ON MF.UUID_FORM = TTH.UUID_FORM ")
			.append(queryJoin)
			.append("WHERE UUID_STATUS_TASK = :uuidStatusTask ")
			.append(where)
			.append("AND tth.IS_PRE_APPROVAL = 1 AND tth.IS_UNASSIGN = 1 ")
			.append("AND tth.OPSI_PENANGANAN = 'Di Cabang' ")
			.append("AND MF.FORM_NAME = 'Form Task Promise To Survey' ")
			.append("AND (ISNULL(tth.appl_no, '%') like '%' + :applNo + '%')) as UT ")
			.append(") a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[]{"uuidStatusTask", ((Object[][]) params)[1][1]});
		paramsStack.push(new Object[]{"applNo", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[9][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[10][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    resultUnassigned = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		StringBuilder queryBuilderCount = new StringBuilder()
			.append("select count(1) from ")
			.append("(select DISTINCT *, ROW_NUMBER() OVER (ORDER BY task_id) AS rownum from ")
			.append("(select tth.* from tr_task_h tth with (nolock) ")
			.append("JOIN MS_BRANCH MSBR WITH (NOLOCK) ON MSBR.UUID_BRANCH = TTH.UUID_BRANCH ")
			.append("JOIN MS_FORM MF WITH (NOLOCK) ON MF.UUID_FORM = TTH.UUID_FORM ")
			.append("where uuid_status_task = :uuidStatusTask ")
			.append(whereCout)
			.append("AND tth.IS_PRE_APPROVAL = 1 AND tth.IS_UNASSIGN = 1 ")
			.append("AND tth.OPSI_PENANGANAN = 'Di Cabang' ")
			.append("AND MF.FORM_NAME = 'Form Task Promise To Survey' ")
			.append("and (ISNULL(tth.appl_no, '%') like '%' + :applNo + '%')) as UT2 ) as UT ");
		
		paramsStackCount.push(new Object[]{"uuidStatusTask", ((Object[][]) params)[1][1]});
		paramsStackCount.push(new Object[]{"applNo", ((Object[][]) params)[4][1]});
		
		Object[][] sqlParamsCount = new Object[paramsStackCount.size()][2];
	    for (int i = 0; i < paramsStackCount.size(); i++) {
			Object[] objects = paramsStackCount.get(i);
			sqlParamsCount[i] = objects;
		}
		
	    Integer resultUnassignedCnt = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderCount.toString(), sqlParamsCount);
		
		result.put(GlobalKey.MAP_RESULT_LIST, resultUnassigned);
		result.put(GlobalKey.MAP_RESULT_SIZE, resultUnassignedCnt);
		
		return result;
	}
	
	public String getUuidStatusTask(String statusCode, AmMsuser loginBean, AuditContext callerId) {
		String uuidStatusTask = StringUtils.EMPTY;
		Object[][] params = {
				{ Restrictions.eq("statusCode", statusCode) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
						loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
		MsStatustask msStatustask = (MsStatustask) this.getManagerDAO()
				.selectOne(MsStatustask.class, params);
		uuidStatusTask = String.valueOf(msStatustask.getUuidStatusTask());
		return uuidStatusTask;
	}
	
	private Map<String, String> sqlPagingBuilder(Object[][] params,Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null){
			return new HashMap<String, String>();
		}

		Map<String, String> mapResult = new HashMap<String, String>();
		StringBuilder sb = new StringBuilder();

		DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
		// ---ASSIGN_DATE
		DateTime assignDateStart = null, assignDateEnd = null;
		if (params.length > 5&& !StringUtils.equals("%", (String) params[5][1])) {
			assignDateStart = formatter.parseDateTime((String) params[5][1]);
			assignDateStart = assignDateStart.withMillisOfDay(0);
		}
		if (params.length > 5&& !StringUtils.equals("%", (String) params[6][1])) {
			assignDateEnd = formatter.parseDateTime((String) params[6][1]);
			assignDateEnd = assignDateEnd.minusMillis(3);
		}
		
		if (assignDateStart != null && assignDateEnd != null) {
			sb.append("AND tth.ASSIGN_DATE BETWEEN :assignDateStart AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart == null && assignDateEnd != null) {
			sb.append("AND tth.ASSIGN_DATE BETWEEN '1990-01-01 00:00:00' AND :assignDateEnd ");
			paramStack.push(new Object[] { "assignDateEnd",assignDateEnd.toDate() });
		} 
		else if (assignDateStart != null && assignDateEnd == null) {
			sb.append("AND tth.ASSIGN_DATE BETWEEN :assignDateStart AND :currentDate ");
			paramStack.push(new Object[] { "assignDateStart",assignDateStart.toDate() });
		}
		//customer name
		if(!StringUtils.equals("%", (String) params[0][1])){
			sb.append("AND tth.CUSTOMER_NAME LIKE '%' + :customerName + '%' ");
			paramStack.push(new Object[] { "customerName",(String) params[0][1] });
		}
		//poloId
		if(!StringUtils.equals("%", (String) params[3][1])){
			sb.append("AND tth.ORDER_NO_CAE LIKE '%' + :poloId + '%' ");
			paramStack.push(new Object[] { "poloId",(String) params[3][1] });
		}
		//NIK
		if(!StringUtils.equals("%", (String) params[3][1])){
			sb.append("AND tth.NIK LIKE '%' + :NIK + '%' ");
			paramStack.push(new Object[] { "NIK",(String) params[2][1] });
		}
		
		mapResult.put("sb", sb.toString());
		return mapResult;
	}
	
	@Transactional
	@Override
	public String doClaimTaskpiloting(String uuidTaskH, String loginId, AuditContext callerId) {
		LOG.info("Start Claim Task by Customer Service");
		
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, new Object [][] { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_NEW) } });
		
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, new Object [][] { { Restrictions.eq("loginId", loginId) } });
		this.getManagerDAO().fetch(amMsuser.getMsBranch());
		MsBranch msBranch = amMsuser.getMsBranch();
		
		TrTaskH taskH = this.getManagerDAO().selectOne("from TrTaskH tth "
				+ "join fetch tth.msBranch msb "
				+ "join fetch tth.msStatustask mst "
				+ "join fetch mst.amMssubsystem ams "
				+ "where tth.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", Long.valueOf(uuidTaskH)}});
		if (!msBranch.getKonvenSyariah().equalsIgnoreCase(taskH.getMsBranch().getKonvenSyariah())) {
			throw new RemoteException("Task branch type is not same as user branch type " + msBranch.getKonvenSyariah());
		}
		
		
		Map listTask = this.getManagerDAO().selectAll(TrTaskH.class, new Object[][] { { Restrictions.eq("applNo", taskH.getApplNo()) },
			{Restrictions.eq("msStatustask.uuidStatusTask", taskH.getMsStatustask().getUuidStatusTask())} }, null);
		List listTrTaskH = (List) listTask.get("resultList");
		
		TrTaskH taskPts = null;
		TrTaskH taskPreSurvey = null;
		List<TrTaskH> otherTaskPts = new ArrayList<>();
		boolean isDropByPilotingCae = false;
		for(int i = 0; i < listTrTaskH.size(); i++) {
			TrTaskH trTaskH = (TrTaskH) listTrTaskH.get(i);
			this.getManagerDAO().fetch(trTaskH.getMsForm());
			if (GlobalVal.FORM_PROMISE_TO_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
				taskPts = trTaskH;
			} else {
				if (GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
					taskPreSurvey = trTaskH;
				}
				// Penjagaan untuk exclude task OTS
				if (GlobalVal.FORM_OTS.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) { 
					continue;
				}
				else if (!GlobalVal.FORM_OTS.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) { 
					otherTaskPts.add(trTaskH);						
				}
			}

			trTaskH.setAmMsuser(amMsuser);
			trTaskH.setMsBranch(msBranch);
			trTaskH.setAssignDate(new Date());
			trTaskH.setDtmUpd(new Date());
			trTaskH.setUsrUpd(String.valueOf(amMsuser.getUuidMsUser()));
			trTaskH.setMsStatusmobile(msm);
			trTaskH.setIsUnassign("0");
			this.getManagerDAO().update(trTaskH);
			
			
			//hit updatePolo
			String kodeRef = "";
			String namaRef = "";
		  	if (StringUtils.isNotBlank(trTaskH.getTaskIdPolo())) {
		  		StringBuilder queryGroupTask = new StringBuilder(" SELECT GROUP_TASK_ID ")
						.append(" FROM MS_GROUPTASK WITH(NOLOCK) ")
						.append(" WHERE UUID_TASK_H = :uuidTaskH ");
			  	
			  	Object[][] paramGroupTask = {{"uuidTaskH", trTaskH.getUuidTaskH()}};
				BigInteger groupTaskId = (BigInteger) this.getManagerDAO().selectOneNativeString(queryGroupTask.toString(), paramGroupTask);
				
				Object[][] paramPoloData = {{Restrictions.eq("taskIdPolo",trTaskH.getTaskIdPolo())}, {Restrictions.eq("isSuccess","1")},
						{Restrictions.eq("groupTaskId", Long.valueOf(String.valueOf(groupTaskId.toString())))}};
				
				TblPoloData poloData = this.getManagerDAO().selectOne(TblPoloData.class, paramPoloData);
				AddTaskPoloRequest requestTaskPolo = gson.fromJson(poloData.getJsonRequest(), AddTaskPoloRequest.class);
				
				if (requestTaskPolo.getNamaTask().equals("Penawaran")) {
					kodeRef = requestTaskPolo.getKodeReferantor();
					namaRef = requestTaskPolo.getNamaReferantor();
				}
		  	}
	  		
		  	intFormLogic.updateDataPolo(null, trTaskH, null, "Pending", "F", null, null, null, null, callerId);
			//start set default answer untuk referantor task visit
		    //2022-12-23 - referantor 1 diset dengan user assign
			if(GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
				GetReferantorResponse referantorDetail = intFormLogic.getReferantorCodeFromNc(amMsuser.getUniqueId(), callerId);
				
				if(referantorDetail != null) {
					taskDistributionLogic.updateReferantor(referantorDetail.getReferantorNo(), GlobalVal.REF_STV_REFCODE, trTaskH.getUuidTaskH(), callerId);
					taskDistributionLogic.updateReferantor(referantorDetail.getReferantorName(), GlobalVal.REF_STV_REFNAME, trTaskH.getUuidTaskH(), callerId);			
				}
				
				if (null != amMsuser.getAmMsuser()) {
					taskDistributionLogic.updateReferantor(retrieveSpvCro(String.valueOf(amMsuser.getAmMsuser().getUuidMsUser())),
							GlobalVal.REF_STV_SPV, trTaskH.getUuidTaskH(), callerId);
				}
			}
			//end set default answer untuk referantor task visit

			long uuidProcess = this.getUuidProcess(trTaskH, trTaskH.getMsStatustask().getAmMssubsystem());
			String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess, trTaskH.getUuidTaskH());
			Object[][] params2 = {
					{ Restrictions.eq("statusCode", statusCode) },
					{Restrictions.eq("amMssubsystem.uuidMsSubsystem", amMsuser.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
			
			if ((GlobalVal.FORM_PRE_SURVEY.equals(trTaskH.getMsForm().getFormName()) ||
				     GlobalVal.FORM_PROMISE_TO_SURVEY.equals(trTaskH.getMsForm().getFormName())) &&
				    (taskPts != null || taskPreSurvey != null)) {

				    trTaskH.setMsStatustask(msStatustask);
				    this.getManagerDAO().update(trTaskH);

				} else if (taskPts == null && taskPreSurvey == null) {
					
				    trTaskH.setMsStatustask(msStatustask);
				    this.getManagerDAO().update(trTaskH);

				}
			LOG.info("status task {}:{}",trTaskH.getUuidTaskH(),trTaskH.getMsStatustask().getStatusTaskDesc());

			TrTaskhistory trTaskhistory = new TrTaskhistory();
			trTaskhistory.setCodeProcess(GlobalVal.CODE_PROCESS_ASSIGNMENT);
			trTaskhistory.setActor(amMsuser.getFullName());
			trTaskhistory.setFieldPerson(amMsuser.getFullName());
			trTaskhistory.setMsStatustask(msStatustask);
			trTaskhistory.setTrTaskH(trTaskH);
			trTaskhistory.setNotes(trTaskH.getNotes());
			trTaskhistory.setUsrCrt(String.valueOf(amMsuser.getUuidMsUser()));
			trTaskhistory.setDtmCrt(new Date());

			this.getManagerDAO().update(trTaskH);
			
			if (StringUtils.isNotBlank(trTaskH.getOrderId())) {
				UpdateStatusMainDealerResponse updStatusTaskMainDealer = this.intFormLogic.updateStatusTaskMainDealer(trTaskH, callerId);
				if (!"00".equals(updStatusTaskMainDealer.getResponseCode())) {
					throw new RemoteException(updStatusTaskMainDealer.getResponseMessage());
				}
			}
				
			this.getManagerDAO().insert(trTaskhistory);
		}
		//codingan dibawah ini tidak termasuk looping
		/*update task to waiting on pending*/
		if (taskPts != null || taskPreSurvey != null) {
			if (otherTaskPts != null && !otherTaskPts.isEmpty()) {
				if (taskPts == null) {
					otherTaskPts.remove(taskPreSurvey);
				}
				for(TrTaskH task : otherTaskPts) {
					Object[][] params2 = {
							{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_WAITING_ON_PENDING) },
							{Restrictions.eq("amMssubsystem.uuidMsSubsystem", amMsuser.getAmMssubsystem().getUuidMsSubsystem()) } };
					MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
					task.setMsStatustask(msStatustask);
					this.getManagerDAO().update(task);
				}
			}
		}
		if (isDropByPilotingCae) {
			return "Flagging Piloting CAE Doesn't Match between Task ( "+taskH.getApplNo()+" ) and Branch User. Task have been drop. )";
		}
		
		LOG.info("End Claim Task by Customer Service");
		
		return GlobalVal.SERVICES_RESULT_SUCCESS;
	}
	
}
