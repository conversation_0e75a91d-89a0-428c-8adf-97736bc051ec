package com.adins.mss.businesslogic.impl.survey;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.ReportTaskGuarantorLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "rawtypes" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericTaskGuarantorLogic extends BaseLogic 
implements ReportTaskGuarantorLogic {
	private static final String[] HEADER_SUMMARY = {"Reason Penginputan Guarantor",
			"Notes Penginputan Guarantor", "No Identitas Guarantor", "Nama KTP Guarantor",
			"Tempat Lahir Guarantor", "Tanggal Lahir Guarantor", "Jenis Kelamin Guarantor",
			"Alamat Legal (KTP) Guarantor", "RT Legal (KTP) Guarantor", "RW Legal (KTP) Guarantor",
			"Nama Provinsi Legal (KTP) Guarantor", "Nama Kabupaten/Kota Legal (KTP) Guarantor",
			"Nama kecamatan Legal (KTP) Guarantor", "Nama kelurahan Legal (KTP) Guarantor",
			"Kode Pos Legal (KTP) Guarantor", "Nama Ibu Kandung Guarantor", "Mobile Phone 1 / No Handphone Guarantor",
			"Hubungan Dengan Pemohon", "Penghasilan", "Customer Model Name", "Job Data", "Profession Name",
			"Job Position", "Job Title Name", "Nama Perusahaan/Usaha", "Nama Tipe Perusahaan/Usaha",
			"RT", "RW", "Nama Provinsi", "Kecamatan", "Kelurahan", "Kode Pos", "Email", 
			"Guarantor : Lama Bekerja/Usaha (Bulan)", "Guarantor : Gaji pokok", 
			"Guarantor : Total Tunjangan Tetap", "Guarantor : Total Tunjangan Tidak Tetap", "Notes Guarantor", 
			"Reason Task Guarantor", "Detail Pending Guarantor", "Detail Reason Guarantor", "Validasi Guarantor"};
	private static final int[] SUMMARY_COLUMN_WIDTH = { 30 * 256, 30 * 256, 30 * 256, 
			20 * 256, 20 * 256, 15 * 256, 30 * 256, 10 * 256, 10 * 256, 20 * 256, 20 * 256,
			20 * 256, 20 * 256, 15 * 256, 30 * 256, 20 * 256, 20 * 256, 10 * 256, 30 * 256,
			30 * 256, 20 * 256, 20 * 256, 20 * 256, 30 * 256, 30 * 256, 10 * 256, 10 * 256, 
			10 * 256, 10 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 30 * 256, 20 * 256,
			20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256, 20 * 256};
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericTaskGuarantorLogic.class);
	
	private String keyHeader = "header";
	
	@Autowired
    private MessageSource messageSource;
	
	@Override
	public List<Map<String, Object>> getBranchListCombo(String branchId, AuditContext callerId) {
		List<Map<String,Object>> result;
		String[][] params={{"branchId",branchId}};
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}

	@Override
	public List<Map<String, Object>> getUserList(String userId, AuditContext callerId) {
		List<Map<String,Object>> result;
		String[][] params= {{"userId", userId}};
		result = this.getManagerDAO().selectAllNativeString(
				"SELECT UUID_MS_USER "
				+ "FROM AM_MSUSER amu WITH(NOLOCK) "
				+ "WHERE UUID_MS_USER = :userId", params);
		return result;
	}
	
	@Override
	public List<Map<String, Object>> getTaskGuarantorList(String[][] params, AuditContext callerId) {
		Stack<Object[]> paramsStack = new Stack<>();		
		StringBuilder paramsQueryString = this.sqlPagingBuilder((Object[][]) params, paramsStack);
		
		StringBuilder queryBuilder = new StringBuilder()
				.append("SELECT	a.GUAR1_INPT_REASON, a.GUAR1_INPT_NOTE, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_KTP_NO)), '*') GUAR1_KTP_NO, a.GUAR1_KTP_NAME, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_POB)), '*') GUAR1_POB, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_DOB)), '*') GUAR1_DOB, ")
				.append("		a.GUAR1_GENDER, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_KTP_ADDRESS)), '*') GUAR1_KTP_ADDRESS, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_KTP_RT)), '*') GUAR1_KTP_RT,")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_KTP_RW)), '*') GUAR1_KTP_RW,  ")
				.append("		a.GUAR1_KTP_PROVINCE, a.GUAR1_KTP_KABUPATEN, a.GUAR1_KTP_KECAMATAN, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_KTP_KELURAHAN)), '*') GUAR1_KTP_KELURAHAN, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_KTP_POSTCODE)), '*') GUAR1_KTP_POSTCODE, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_MOTHER_NAME)), '*') GUAR1_MOTHER_NAME, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR1_PHON_NUM)), '*') GUAR1_PHON_NUM, ")
				.append("		a.GUAR1_HUB_PEMOHON, a.GUAR2_INCOME, a.GUAR2_CUSTMODEL_NAME, a.GUAR2_JOB_DATA, ")
				.append("		a.GUAR2_PROF_NAME, a.GUAR2_JOB_POS, a.GUAR2_JOB_TITLE, a.GUAR2_COMP_NAME, a.GUAR2_COMP_TYPE, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR2_RT)), '*') GUAR2_RT, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR2_RW)), '*') GUAR2_RW,  ")
				.append("		a.GUAR2_PROVINCE, a.GUAR2_KECAMATAN, a.GUAR2_KELURAHAN, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR2_POST_CODE)), '*') GUAR2_POST_CODE,")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR2_EMAIL)), '*') GUAR2_EMAIL, ")
				.append("		ISNULL(REPLICATE('*', LEN(a.GUAR2_WORK_MONTHS)), '*') GUAR2_WORK_MONTHS, ")
				.append("		a.GUAR2_SALARY, a.GUAR2_FIXED_ALLOW, GUAR2_NONFIXED_ALLOW, ")
				.append("		a.GUAR1_NOTES, a.GUAR1_CAT, a.GUAR1_SUB_CAT, a.GUAR1_REASON_DETAIL, a.GUAR1_VALIDASI ")
				.append("FROM (SELECT * FROM getDataReportTaskGuarantor()) a ")
				.append("JOIN TR_TASK_H trth WITH(NOLOCK) ON trth.UUID_TASK_H = a.UUID_TASK_H ")
				.append("JOIN MS_BRANCH msb WITH(NOLOCK)  ON trth.UUID_BRANCH = msb.UUID_BRANCH ")
				.append("WHERE 1=1 ")
				.append(paramsQueryString);
		
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    
		List<Map<String,Object>> result = this.getManagerDAO().selectAllNativeString(queryBuilder.toString() , sqlParams);
			
	    return result;
	}

	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramsStack) {
		if (params == null) {
			return new StringBuilder();
		}
		
		StringBuilder sb = new StringBuilder();
		if(!StringUtils.equals("%", (String) params[0][1])) {
			sb.append(" AND msb.UUID_BRANCH = :uuidBranch ");
			paramsStack.push(new Object[] {"uuidBranch", Long.valueOf((String) params[0][1])});
		}
		
		if(!StringUtils.isBlank(params[1][1].toString()) || !StringUtils.isBlank(params[2][1].toString()) ) {
			sb.append(" AND trth.DTM_CRT BETWEEN :startDate AND :endDate ");
			paramsStack.push(new Object[] {"startDate", (String) params[1][1]});
			paramsStack.push(new Object[] {"endDate", (String) params[2][1]});
		}
		
		if(!StringUtils.equals("%", (String) params[3][1])) {
			sb.append(" AND trth.UUID_MS_USER = :uuidMsUser ");
			paramsStack.push(new Object[] {"uuidMsUser", Long.valueOf((String) params[3][1])});
		}
		
		return sb;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), 
				ReportDownloadBean.class);
		XSSFWorkbook workbook = this.createXlsTemplate(reportBean.getParamsAction(), callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);
		
			byte[] exp = stream.toByteArray();
			
			Object[][] param = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH)} };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage(
						"businesslogic.report.excelresult", null, this.retrieveLocaleAudit(callerId)),
						GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();
			
			File doneFolder = new File(pathFolder + DateFormatUtils.format(
					trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}
			
			StringBuilder sb = new StringBuilder();
			
			sb.append("ReportTaskGuarantor_");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getStartDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse start date : {}",reportBean.getStartDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getEndDate())) {
				try {
					sb.append(DateFormatUtils.format(DateUtils.parseDate(reportBean.getEndDate(), 
							"yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} 
				catch (ParseException e) {
					LOG.error("Error when parse end date : {}",reportBean.getEndDate(), e);
					sb.append("-");
				}
			} 
			else {
				sb.append("-");
			}
			
			sb.append("_BRANCH-");
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
					sb.append("ALL");
				} 
				else {
					MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class, 
							Long.valueOf(reportBean.getUuidBranch()));
					sb.append(msBranch.getBranchCode());
				}
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_USER-");
			if (StringUtils.isNotBlank(reportBean.getUuidUser())) {
				if ("0".equals(reportBean.getUuidUser()) || "%".equals(reportBean.getUuidUser())) {
					sb.append("ALL");
				} 
				else {
					AmMsuser amMuser = this.getManagerDAO().selectOne(AmMsuser.class, 
							Long.valueOf(reportBean.getUuidUser()));
					sb.append(amMuser.getFullName());
				}
			} 
			else {
				sb.append("ALL");
			}
			
			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");
			
			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} 
			finally {
				fileOut.close();
			}
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		
		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int)Math.abs(trReportResultLog
				.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime().getTime())/1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}


	@Override
	public byte[] exportExcel(String[][] params, AuditContext callerId) {
		XSSFWorkbook workbook = this.createXlsTemplate(params, callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private XSSFWorkbook createXlsTemplate(String[][] params, AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Report Task Guarantor");
				List result= this.getTaskGuarantorList(params, callerId);
				this.createData(workbook, sheet, result, params[1][1], params[2][1]);
		} 
		catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createData(XSSFWorkbook workbook, XSSFSheet sheet, 
			List result, String startDate, String endDate) {
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell = 0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		XSSFRow rowHeader = sheet.createRow(rowcell++);
		for (int i = 0; i < HEADER_SUMMARY.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT TASK GUARANTOR PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get(keyHeader));
			
			XSSFCell cellNo = rowHeader.createCell(0);
			cellNo.setCellValue("NO");
			cellNo.setCellStyle(styles.get(keyHeader));
			
			XSSFCell cell = rowHeader.createCell(i+1);
			cell.setCellValue(HEADER_SUMMARY[i]);
			cell.setCellStyle(styles.get(keyHeader));
			sheet.setColumnWidth(i, SUMMARY_COLUMN_WIDTH[i]);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER_SUMMARY.length));

		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i + (double) 1);
			cellNo.setCellStyle(styles.get("cell"));
			//data cell			
			for (int j = 1; j <= temp.size(); j++) {
				XSSFCell cell = rowData.createCell(j);
				if(temp.get("d"+(j-1)) != null) {
					cell.setCellValue(temp.get("d" + (j-1)).toString());
				} else {
					cell.setCellValue("");
				}
				
				cell.setCellStyle(styles.get("cell"));
			}
			
		} 
			
	}

	private Map<String, CellStyle> createStyles(XSSFWorkbook wb) {
		Map<String, CellStyle> styles = new HashMap<>();
		CellStyle style;

		style = wb.createCellStyle();
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		styles.put("cell", style);

		style = wb.createCellStyle();
		Font font = wb.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setFillPattern(CellStyle.SOLID_FOREGROUND);
		styles.put(keyHeader, style);
		return styles;
	}

	@Override
	public String saveExportScheduler(String[][] params, AuditContext callerId) {
		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setUuidLoginId(callerId.getCallerId());
		reportBean.setUuidBranch(params[0][1]);
		reportBean.setStartDate(params[1][1]);
		reportBean.setEndDate(params[2][1]);
		reportBean.setUuidUser(params[3][1]);
		reportBean.setParamsAction(params);
		
		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);
		
		AmMsuser amMsUser = new AmMsuser();
		amMsUser.setUuidMsUser(Long.valueOf(callerId.getCallerId()));
		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("Task Guarantor Report");
		trReportResultLog.setRptType(FilterType.FILTER_BY_TASK_GUARANTOR.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null, this.retrieveLocaleAudit(callerId));
	}

}
