package com.adins.mss.businesslogic.impl.survey;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Clob;
import java.sql.SQLException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.codehaus.jackson.map.ObjectMapper;
import org.codehaus.jackson.type.TypeReference;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.survey.TaskKawanInternalLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.SerializedName;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericTaskKawanInternalLogic extends BaseLogic implements TaskKawanInternalLogic, MessageSourceAware {

    private static final String[] HEADER = { "No Identitas Pemohon", "Nama KTP", "Tempat Lahir Pemohon",
            "Tanggal Lahir Pemohon", "Alamat Legal (KTP)", "RT Legal (KTP)", "RW Legal (KTP)",
            "Nama Provinsi Legal (KTP)", "Nama Kabupaten/Kota Legal (KTP)", "Nama kecamatan Legal (KTP)",
            "Nama kelurahan Legal (KTP)", "Kode Pos Legal (KTP)", "Status Pernikahan", "Nama Ibu Kandung",
            "Jenis Kelamin", "Agama", "Email", "No Handphone",
            "Surat Pernyataan Pasangan tidak ikut tanda tangan Kredit", "No Identitas Pasangan", "Nama KTP Pasangan",
            "Tempat Lahir Pasangan", "Tanggal Lahir Pasangan", "Alamat Legal (KTP) Pasangan", "RT Legal (KTP) Pasangan",
            "RW Legal (KTP) Pasangan", "Nama Provinsi Legal (KTP) Pasangan", "Nama Kabupaten/Kota Legal (KTP) Pasangan",
            "Nama kecamatan Legal (KTP) Pasangan", "Nama kelurahan Legal (KTP) Pasangan",
            "Kode Pos Legal (KTP) Pasangan", "Nama Ibu Kandung Pasangan", "Email Pasangan", "No Handphone Pasangan",
            "No Identitas Guarantor", "Nama KTP Guarantor", "Tempat Lahir Guarantor", "Tanggal Lahir Guarantor",
            "Alamat Legal (KTP) Guarantor", "RT Legal (KTP) Guarantor", "RW Legal (KTP) Guarantor",
            "Nama Provinsi Legal (KTP) Guarantor", "Nama Kabupaten/Kota Legal (KTP) Guarantor",
            "Nama kecamatan Legal (KTP) Guarantor", "Nama kelurahan Legal (KTP) Guarantor",
            "Kode Pos Legal (KTP) Guarantor", "Nama Ibu Kandung Guarantor", "Email Guarantor", "No Handphone Guarantor",
            "Status Kepemilikan Rumah", "NPWP", "Alamat Tinggal (Survey)", "RT Tinggal (Survey)", "RW Tinggal (survey)",
            "Nama Provinsi Tinggal (Survey)", "Nama Kota/Kabupaten Tinggal (Survey)", "Nama Kecamatan Tinggal (Survey)",
            "Nama Kelurahan Tinggal (Survey)", "Kode Pos Tinggal (Survey)", "Sub Zip Code", "Product Type",
            "Product Offering Type (POT)", "Brand", "Assets", "Group Type", "Type", "Kategori Assets",
            "Tahun Kendaraan", "SOA", "Kode-Nama Dealer (Mandatory untuk NB)", "Pos Dealer",
            "Kode-Nama Sales Dealer (Mandatory untuk NB)", "Notes", "Prospect/Interest?", "OTR", "DP/NTF (Amount)",
            "DP Murni", "DP/LTV (%)", "Tenor", "Angsuran", "Penghasilan", "Education", "Profession Name",
            "Length Of Work (Months)", "DSR (%)", "Stay length (Months)", "BPKB Ownership", "Owner Relationship" };
    
    private static final String[] MASKED_HEADER = {
             "No Identitas Pemohon"
            ,"Tempat Lahir Pemohon"
            ,"Tanggal Lahir Pemohon"
            ,"Alamat Legal (KTP)"
            ,"RT Legal (KTP)"
            ,"RW Legal (KTP)"
            ,"Nama Ibu Kandung"
            ,"Email"
            ,"No Handphone"
            ,"Surat Pernyataan Pasangan tidak ikut tanda tangan Kredit"
            ,"No Identitas Pasangan"
            ,"Tempat Lahir Pasangan"
            ,"Tanggal Lahir Pasangan"
            ,"Alamat Legal (KTP) Pasangan"
            ,"RT Legal (KTP) Pasangan"
            ,"RW Legal (KTP) Pasangan"
            ,"Nama Ibu Kandung Pasangan"
            ,"Email Pasangan"
            ,"No Handphone Pasangan"
            ,"No Identitas Guarantor"
            ,"Tempat Lahir Guarantor"
            ,"Tanggal Lahir Guarantor"
            ,"Alamat Legal (KTP) Guarantor"
            ,"RT Legal (KTP) Guarantor"
            ,"RW Legal (KTP) Guarantor"
            ,"Nama Ibu Kandung Guarantor"
            ,"Email Guarantor"
            ,"No Handphone Guarantor"
            ,"NPWP"
            ,"Alamat Tinggal (Survey)"
            ,"RT Tinggal (Survey"
            ,"RW Tinggal (survey)"
            ,"Kode Pos Legal (KTP)"
            ,"Kode Pos Legal (KTP) Pasangan"
            ,"Kode Pos Legal (KTP) Guarantor"
            ,"Kode Pos Tinggal (Survey)"
            
//          /*1*/"nikKtp", 
//          /*2*/"tempatLahir", 
//          /*3*/"tanggalLahir", 
//          /*4*/"alamatLegal",
//          /*5*/"rtLegal", 
//          /*6*/"rwLegal", 
//          /*7*/"namaIbuKandung", 
//          /*8*/"email", 
//          /*9*/"mobilePhone",
//          /*10*/"psgnTdkTtd",
//          /*11*/"nikKtpPsgn", 
//          /*12*/"tempatLahirPsgn", 
//          /*13*/"tanggalLahirPsgn", 
//          /*14*/"alamatLegalPsgn", 
//          /*15*/"rtLegalPsgn",
//          /*16*/"rwLegalPsgn", 
//          /*17*/"namaIbuKandungPsgn", 
//          /*18*/"emailPsgn", 
//          /*19*/"mobilePhonePsgn", 
//          /*20*/"nikKtpGrntr", 
//          /*21*/"tempatLahirGrntr",
//          /*22*/"tanggalLahirGrntr", 
//          /*23*/"alamatLegalGrntr", 
//          /*24*/"rtLegalGrntr",
//          /*25*/"rwLegalGrntr",
//          /*26*/"namaIbuKandungGrntr",
//          /*27*/"emailGrntr", 
//          /*28*/"mobilePhoneGrntr",
//          /*29*/"npwp",
//          /*30*/"alamatSurvey",
//          /*31*/"rtSurvey", 
//          /*32*/"rwSurvey",
//          /*33*/"kodeposLegal",
//          /*34*/"kodeposLegalPsgn",
//          /*35*/"kodeposLegalGrntr",
//          /*35*/"kodeposSurvey"
    };

    private static final String[] COL_KEY = { "nikKtp", "customerName", "tempatLahir", "tanggalLahir", "alamatLegal",
            "rtLegal", "rwLegal", "provinsiLegal", "kabupatenLegal", "kecamatanLegal", "kelurahanLegal", "kodeposLegal",
            "statusPernikahan", "namaIbuKandung", "jenisKelamin", "agama", "email", "mobilePhone", "psgnTdkTtd",
            "nikKtpPsgn", "namaKtpPsgn", "tempatLahirPsgn", "tanggalLahirPsgn", "alamatLegalPsgn", "rtLegalPsgn",
            "rwLegalPsgn", "provinsiLegalPsgn", "kabupatenLegalPsgn", "kecamatanLegalPsgn", "KelurahanLegalPsgn",
            "kodeposLegalPsgn", "namaIbuKandungPsgn", "emailPsgn", "mobilePhonePsgn", "nikKtpGrntr", "namaKtpGrntr",
            "tempatLahirGrntr", "tanggalLahirGrntr", "alamatLegalGrntr", "rtLegalGrntr", "rwLegalGrntr",
            "provinsiLegalGrntr", "kabupatenLegalGrntr", "kecamatanLegalGrntr", "kelurahanLegalGrntr",
            "kodeposLegalGrntr", "namaIbuKandungGrntr", "emailGrntr", "mobilePhoneGrntr", "statusPemilikRumah", "npwp",
            "alamatSurvey", "rtSurvey", "rwSurvey", "provinsiSurvey", "kabupatenSurvey", "kecamatanSurvey",
            "kelurahanSurvey", "kodeposSurvey", "subzipcodeSurvey", "productType", "productOfferingType", "brand",
            "asset", "groupType", "type", "kategoriAsset", "tahunKendaraan", "soa", "kodeNamaDealer", "posDealer",
            "kodeNamaSales", "notes", "prospectInterest", "otr", "dpNtfAmount", "dpMurni", "dpPercentage", "tenor",
            "angsuran", "penghasilan", "education", "professionName", "lengthWork", "dsrPercentage", "stayLength",
            "bpkbOwner", "bpkbOwnerRelation" };

    private static final int COLUMN_WIDTH = 20 * 256;

    private static final Logger LOG = LoggerFactory.getLogger(GenericTaskKawanInternalLogic.class);

    private Gson gson = new Gson();

    private String paramUuidBranch = "uuidBranch";
    private String keyHeader = "header";
    private String paramLovGroup = "lovGroup";
    
    @Autowired
    private MessageSource messageSource;

    @Override
    public List getComboBranch(String branchId, AuditContext callerId) {
        List result = null;
        String[][] params = { { "branchId", branchId } };
        result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
        return result;
    }

    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    @Override
    public String saveExportScheduler(String branchId, String userId, String startDate, String endDate,
            AuditContext callerId) {
        AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));

        String[][] paramsAction = { { "branchId", branchId }, { "userId", userId }, { "startDate", startDate },
                { "endDate", endDate } };

        ReportDownloadBean reportBean = new ReportDownloadBean();
        reportBean.setSubsystemCode(amMsUser.getAmMssubsystem().getSubsystemName());
        reportBean.setUuidLoginId(String.valueOf(amMsUser.getUuidMsUser()));
        reportBean.setUuidBranch(branchId);
        reportBean.setUuidUser(userId);
        reportBean.setStartDate(startDate + " 00:00:00.000");
        reportBean.setEndDate(endDate + " 23:59:59.997");
        reportBean.setParamsAction(paramsAction);

        Gson gson = new GsonBuilder().serializeNulls().create();
        String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

        TrReportresultlog trReportResultLog = new TrReportresultlog();
        trReportResultLog.setAmMsuser(amMsUser);
        trReportResultLog.setDtmRequest(new Date());
        trReportResultLog.setRptName("Report Task Kawan Internal");
        trReportResultLog.setRptType(FilterType.FILTER_BY_TASK_KAWAN_INTERNAL.toString());
        trReportResultLog.setRptParams(jsonParams);
        trReportResultLog.setProcessStatus("0");
        this.getManagerDAO().insert(trReportResultLog);

        return messageSource.getMessage("businesslogic.global.successrequestdownload", null,
                this.retrieveLocaleAudit(callerId));
    }

    @Override
    public byte[] exportExcel(String branchId, String userId, String startDate, String endDate, AuditContext callerId) {
        XSSFWorkbook workbook = this.createXlsTemplate(branchId, userId, startDate, endDate, callerId);
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        try {
            workbook.write(stream);
        } catch (IOException e) {
            LOG.error(e.getMessage(), e);
        }
        return stream.toByteArray();
    }

    @Override
    public List getReport(String branchId, String userId, String startDate, String endDate, AuditContext callerId) {
        List result = new ArrayList<Map>();
        StringBuilder query = new StringBuilder();
        query.append(
                "SELECT JSON_REQUEST FROM TBL_CAE_DATA as cd WITH(NOLOCK) JOIN TR_TASK_H as tth WITH(NOLOCK) ON cd.ORDER_NO_CAE = tth.ORDER_NO_CAE ");
        query.append("WHERE cd.DTM_CRT BETWEEN '" + startDate + " 00:00:00.000' AND '" + endDate + " 23:59:59.997'");
        query.append(" AND cd.IS_SUCCESS = '1' ");

        if (!"0".equals(branchId)) {
            Object[][] paramBranch = { { Restrictions.eq(paramUuidBranch, Long.valueOf(branchId)) } };
            MsBranch branch = this.getManagerDAO().selectOne(MsBranch.class, paramBranch);
            String officeCodeParam = "'%" + "\"officeCode\":\"" + branch.getBranchCode() + "\"%'";
            query.append(" AND JSON_REQUEST LIKE " + officeCodeParam);
        }

        if (!StringUtils.isEmpty(userId)) {
            query.append(" AND tth.UUID_MS_USER = " + userId);
        }

        List jsonList = this.getManagerDAO().selectAllNativeString(query.toString(), null);
        Iterator itr = jsonList.iterator();
        while (itr.hasNext()) {
            Map<String, Object> row = (Map<String, Object>) itr.next();
            try {
                Map json = this.jsonStringToMap((Clob) row.get("d0"));
                result.add(json);
            } catch (Exception e) {
                e.printStackTrace();
                return result;
            }

        }
        return result;
    }

    @Override
    public int getReportCount(String branchId, String userId, String startDate, String endDate, AuditContext callerId) {
        StringBuilder query = new StringBuilder();
        query.append(
                "SELECT COUNT(1) FROM TBL_CAE_DATA as cd WITH(NOLOCK) JOIN TR_TASK_H as tth WITH(NOLOCK) ON cd.ORDER_NO_CAE = tth.ORDER_NO_CAE ");
        query.append("WHERE cd.DTM_CRT BETWEEN '" + startDate + " 00:00:00.000' AND '" + endDate + " 23:59:59.997'");
        query.append(" AND cd.IS_SUCCESS = '1' ");

        if (!"0".equals(branchId)) {
            Object[][] paramBranch = { { Restrictions.eq(paramUuidBranch, Long.valueOf(branchId)) } };
            MsBranch branch = this.getManagerDAO().selectOne(MsBranch.class, paramBranch);
            String officeCodeParam = "'%" + "\"officeCode\":\"" + branch.getBranchCode() + "\"%'";
            query.append(" AND JSON_REQUEST LIKE " + officeCodeParam);
        }

        if (!StringUtils.isEmpty(userId)) {
            query.append(" AND tth.UUID_MS_USER = " + userId);
        }

        return (int) this.getManagerDAO().selectOneNativeString(query.toString(), null);
    }

    private XSSFWorkbook createXlsTemplate(String branchId, String userId, String startDate, String endDate,
            AuditContext callerId) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        try {
            XSSFSheet sheet = workbook.createSheet("Report Task Kawan Internal");
            List result = this.getReport_Masked(branchId, userId, startDate, endDate, callerId);

            this.createData2(workbook, sheet, result, startDate, endDate, callerId);

        } catch (Exception ex) {
            LOG.error(ex.getMessage(), ex);
        }
        return workbook;
    }

    private void createData(XSSFWorkbook workbook, XSSFSheet sheet, List result, String startDate, String endDate) {
        Map<String, CellStyle> styles = createStyles(workbook);
        int rowcell = 0;
        XSSFRow rowHead = sheet.createRow(rowcell++);
        XSSFRow rowHeader = sheet.createRow(rowcell++);

        for (int i = 0; i < HEADER.length; i++) {
            XSSFCell cellHead = rowHead.createCell(i);
            cellHead.setCellValue(
                    "REPORT DETAIL PERIOD " + startDate.substring(0, 10) + " - " + endDate.substring(0, 10));
            cellHead.setCellStyle(styles.get(keyHeader));
            XSSFCell cell4 = rowHeader.createCell(i);
            cell4.setCellValue(HEADER[i]);
            cell4.setCellStyle(styles.get(keyHeader));
            sheet.setColumnWidth(i, COLUMN_WIDTH);
        }
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER.length - 1));
        // Data row
        for (int i = 0; i < result.size(); i++) {
            Map temp = (Map) result.get(i);
            XSSFRow rowData = sheet.createRow(rowcell++);

            // data cell
            for (int j = 0; j < HEADER.length; j++) {
                XSSFCell cell4 = rowData.createCell(j);
                if (temp.get(COL_KEY[j]) != null) {
                    cell4.setCellValue(temp.get(COL_KEY[j]).toString());
                }
                cell4.setCellStyle(styles.get("cell"));
            }
        }
    }
    
    private void createData2(XSSFWorkbook workbook, XSSFSheet sheet, List result, String startDate, String endDate, AuditContext auditContext) {
        Map<String, CellStyle> styles = createStyles(workbook);
        int rowcell = 0;
        XSSFRow rowHead = sheet.createRow(rowcell++);
        XSSFRow rowHeader = sheet.createRow(rowcell++);

        List template = getTemplateKawanInternal(auditContext);
        for (int i = 0; i < template.size(); i++) {
            Map tempHeader = (Map) template.get(i);
            
            XSSFCell cellHead = rowHead.createCell(i);
            cellHead.setCellValue(
                    "REPORT KAWAN INTERNAL PERIOD " + startDate.substring(0, 10) + " - " + endDate.substring(0, 10));
            cellHead.setCellStyle(styles.get(keyHeader));
            XSSFCell cell4 = rowHeader.createCell(i);
            cell4.setCellValue((String)tempHeader.get(keyHeader));
            cell4.setCellStyle(styles.get(keyHeader));
            sheet.setColumnWidth(i, COLUMN_WIDTH);
        }
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, template.size() - 1));
        
        for (int i = 0; i < result.size(); i++) {
            Map temp = (Map) result.get(i);
            XSSFRow rowData = sheet.createRow(rowcell++);
            BigInteger idH =  (BigInteger) temp.get("d0");
            Map<String, String> listReportD = getReportKawanInternalD(idH);
            
            for (int j = 0; j < template.size(); j++) {
                Map tempHeader = (Map) template.get(j);
                String header = (String) tempHeader.get("header");
                String value = String.valueOf(listReportD.get(header));
                if(value == null || value.equals("null") || value.isEmpty()) {
                    value = "";
                }
                XSSFCell cell4 = rowData.createCell(j);
                cell4.setCellValue(value);
                cell4.setCellStyle(styles.get("cell"));
            }   
        }
        
        /*
        // Data row
        for (int i = 0; i < result.size(); i++) {
            Map temp = (Map) result.get(i);
            XSSFRow rowData = sheet.createRow(rowcell++);
            String json = (String)temp.get("json");
            
            Map jsonMap = gson.fromJson(json, Map.class);
            // data cell
            for (int j = 0; j < template.size(); j++) {
                Map tempHeader = (Map) template.get(j);
                String key = StringUtils.EMPTY;
                String source = temp.get("source").toString();
                String lovGroup = tempHeader.get(paramLovGroup) == null ? StringUtils.EMPTY: tempHeader.get(paramLovGroup).toString();
                
                if ("CAE".equalsIgnoreCase(source)) {
                    key = tempHeader.get("keyCae").toString();
                } else {
                    key = tempHeader.get("keyPolo").toString();
                }
                
                XSSFCell cell4 = rowData.createCell(j);
                if ("POLO".equalsIgnoreCase(source) && "dpNtfAmount".equalsIgnoreCase(key)) {
                    Object dpAmount = jsonMap.get("dpAmount");
                    Object ntfAmt = jsonMap.get("ntfAmt");
                    cell4.setCellValue( (dpAmount==null?"":dpAmount.toString()) +"/"+ (ntfAmt==null?"":ntfAmt.toString()) );
                } else if (jsonMap.get(key) != null) {
                    String value = jsonMap.get(key).toString();
                    if (StringUtils.isNotBlank(value) && StringUtils.isNotBlank(lovGroup)) {
                        String desc = getValueDescription(value, lovGroup, auditContext);
                        if (StringUtils.isNotBlank(desc)) {
                            value = desc;
                        }
                    }
                    cell4.setCellValue(value);
                }
                cell4.setCellStyle(styles.get("cell"));
            }
        }
        */
    }


    private Map<String, String> getUserBranch(String branchId, String userId) {
        Map<String, String> result = new HashMap<>();

        AmMsuser user = null;
        if (StringUtils.isNotBlank(userId)) {
            user = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(userId));
        }

        MsBranch branch = null;
        if (StringUtils.isNotBlank(branchId) && !"%".equals(branchId)) {
            branch = this.getManagerDAO().selectOne(MsBranch.class, Long.valueOf(branchId));
        }

        result.put("user", user != null ? user.getFullName() : "All User");
        result.put("branch", branch != null ? branch.getBranchCode() + " - " + branch.getBranchName() : "All Branch");
        return result;
    }

    private static Map<String, CellStyle> createStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();
        CellStyle style;

        style = wb.createCellStyle();
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        styles.put("cell", style);

        style = wb.createCellStyle();
        Font font = wb.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(CellStyle.ALIGN_CENTER);
        style.setWrapText(true);
        style.setBorderRight(CellStyle.BORDER_THIN);
        style.setRightBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderLeft(CellStyle.BORDER_THIN);
        style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderTop(CellStyle.BORDER_THIN);
        style.setTopBorderColor(IndexedColors.BLACK.getIndex());
        style.setBorderBottom(CellStyle.BORDER_THIN);
        style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
        style.setFillPattern(CellStyle.SOLID_FOREGROUND);
        styles.put("header", style);
        return styles;
    }

    @Transactional(isolation = Isolation.READ_UNCOMMITTED)
    @Override
    public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
    	ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), ReportDownloadBean.class);
        String[][] params = reportBean.getParamsAction();
        String branchId = params[0][1];
        String userId = params[1][1];
        String startDate = params[2][1];
        String endDate = params[3][1];

        XSSFWorkbook workbook = this.createXlsTemplate(branchId, userId, startDate, endDate, callerId);
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        String filePath = StringUtils.EMPTY;
        try {
            workbook.write(stream);

            byte[] exp = stream.toByteArray();

            Object[][] param = { { Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH) } };
            AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
            if (amGeneralSetting == null) {
                throw new EntityNotFoundException(this.messageSource.getMessage("businesslogic.report.excelresult",
                        null, this.retrieveLocaleAudit(callerId)), GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
            }
            String pathFolder = amGeneralSetting.getGsValue();
            pathFolder = "/Users/<USER>/Download/Document/";
            trReportResultLog.setProcessStartTime(new Date());
            
            File doneFolder = new File(
                    pathFolder + DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyy"));
            if (!doneFolder.exists()) {
                doneFolder.mkdirs();
            }

            StringBuilder sb = new StringBuilder();

            sb.append("ReportTaskKawanInternal_");
            if (StringUtils.isNotBlank(reportBean.getStartDate())) {
                try {
                    sb.append(DateFormatUtils.format(
                            DateUtils.parseDate(reportBean.getStartDate(), "yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
                } catch (ParseException e) {
                    LOG.error("Error when parse start date : {}", reportBean.getStartDate(), e);
                    sb.append("-");
                }
            } else {
                sb.append("-");
            }
            sb.append("-");
            if (StringUtils.isNotBlank(reportBean.getEndDate())) {
                try {
                    sb.append(DateFormatUtils
                            .format(DateUtils.parseDate(reportBean.getEndDate(), "yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
                } catch (ParseException e) {
                    LOG.error("Error when parse end date : {}", reportBean.getEndDate(), e);
                    sb.append("-");
                }
            } else {
                sb.append("-");
            }

            sb.append("_BRANCH-");
            if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
                if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
                    sb.append("ALL");
                } else {
                    MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class,
                            Long.valueOf(reportBean.getUuidBranch()));
                    sb.append(msBranch.getBranchCode());
                }
            } else {
                sb.append("ALL");
            }

            sb.append("_");
            sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
            sb.append(".xlsx");

            filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
            LOG.info("File path generated report result : {}", filePath);
            FileOutputStream fileOut = new FileOutputStream(filePath);
            try {
                fileOut.write(exp);
                fileOut.flush();
            } finally {
                fileOut.close();
            }
        } catch (IOException e) {
            LOG.error(e.getMessage(), e);
        }

        trReportResultLog.setProcessFinishTime(new Date());
        trReportResultLog.setProcessDurationSeconds((int) Math.abs(
                trReportResultLog.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime().getTime())
                / 1000);
        trReportResultLog.setReportFileLocation(filePath);
        trReportResultLog.setProcessStatus("1");
        this.getManagerDAO().update(trReportResultLog);
    }

    @Override
    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    private String clobStringConversion(Clob clb) throws IOException, java.sql.SQLException {
        if (clb == null)
            return "";
        StringBuilder str = new StringBuilder();
        String strng;
        BufferedReader bufferRead = new BufferedReader(clb.getCharacterStream());
        while ((strng = bufferRead.readLine()) != null)
            str.append(strng);
        return str.toString();
    }

    private Map<String, Object> jsonStringToMap(Clob json) throws IOException, SQLException {
        String jsonString = this.clobStringConversion(json);
        Map result = new HashMap<String, String>();
        TaskKawanInternal task = gson.fromJson(jsonString, TaskKawanInternal.class);

        result.put("officeCode", task.getOfficeCode());
        result.put("odrNoCAE", task.getOdrNoCAE());
        result.put("dukcapilStatus", task.getDukcapilStatus());
        result.put("negativeStatus", task.getNegativeStatus());
        result.put("teleStatus", task.getTeleStatus());
        result.put("customerName", task.getCustomerName());
        result.put("mobilePhone", task.getMobilePhone());
        result.put("promiseSurveyDatetime", task.getPromiseSurveyDatetime());
        result.put("totalKontrakBakiAllSlikAktf", task.getTotalKontrakBakiAllSlikAktf());
        result.put("totaKontrakTunggakSlikAktf", task.getTotaKontrakTunggakSlikAktf());
        result.put("totalAngsuranSlikAktf", task.getTotalAngsuranSlikAktf());
        result.put("maxDPDSlikAktf", task.getMaxDPDSlikAktf());
        result.put("lastUpdateSlikAktf", task.getLastUpdateSlikAktf());
        result.put("totalKontrakSlikLns", task.getTotalKontrakSlikLns());
        result.put("maxDPDSlikLns", task.getMaxDPDSlikLns());
        result.put("totalKontrakMaxDPDSlikLns", task.getTotalKontrakMaxDPDSlikLns());
        result.put("lastUpdateSlikLns", task.getLastUpdateSlikLns());
        result.put("fotoKtp", task.getFotoKtp());
        result.put("nikKtp", task.getNikKtp());
        result.put("tempatLahir", task.getTempatLahir());
        result.put("tanggalLahir", task.getTanggalLahir());
        result.put("alamatLegal", task.getAlamatLegal());
        result.put("rtLegal", task.getRtLegal());
        result.put("rwLegal", task.getRwLegal());
        result.put("provinsiLegal", task.getProvinsiLegal());
        result.put("kabupatenLegal", task.getKabupatenLegal());
        result.put("kecamatanLegal", task.getKecamatanLegal());
        result.put("kelurahanLegal", task.getKelurahanLegal());
        result.put("kodeposLegal", task.getKodeposLegal());
        result.put("subzipcodeLegal", task.getSubzipcodeLegal());
        result.put("statusPernikahan", task.getStatusPernikahan());
        result.put("namaIbuKandung", task.getNamaIbuKandung());
        result.put("jenisKelamin", task.getJenisKelamin());
        
        if (task.getJenisKelamin() != null && !StringUtils.isEmpty(task.getJenisKelamin())) {
            try {
                Object[][] params = { { paramLovGroup, "GENDER" }, { "code", task.getJenisKelamin() } };
                MsLov lov = this.getManagerDAO().selectOne(MsLov.class, params);
                result.put("jenisKelamin", lov.getDescription());
            } catch (Exception e) {
                result.put("jenisKelamin", task.getJenisKelamin());
            }
        } else {
            result.put("jenisKelamin", task.getJenisKelamin());
        }
        
        if (task.getAgama() != null && !StringUtils.isEmpty(task.getAgama())) {
            try {
                Object[][] params = { { paramLovGroup, "AGAMA" }, { "code", task.getAgama() } };
                MsLov lov = this.getManagerDAO().selectOne(MsLov.class, params);
                result.put("agama", lov.getDescription());
            } catch (Exception e) {
                result.put("agama", task.getAgama());
            }
        } else {
            result.put("agama", task.getAgama());
        }
        
        result.put("email", task.getEmail());
        result.put("dokTdkLgkp", task.getDokTdkLgkp());
        result.put("mlbtknPsgn", task.getMlbtknPsgn());
        result.put("psgnTdkTtd", task.getPsgnTdkTtd());
        result.put("fotoKtpPsgn", task.getFotoKtpPsgn());
        result.put("nikKtpPsgn", task.getNikKtpPsgn());
        result.put("namaKtpPsgn", task.getNamaKtpPsgn());
        result.put("tempatLahirPsgn", task.getTempatLahirPsgn());
        result.put("tanggalLahirPsgn", task.getTanggalLahirPsgn());
        result.put("alamatLegalPsgn", task.getAlamatLegalPsgn());
        result.put("rtLegalPsgn", task.getRtLegalPsgn());
        result.put("rwLegalPsgn", task.getRwLegalPsgn());
        result.put("provinsiLegalPsgn", task.getProvinsiLegalPsgn());
        result.put("kabupatenLegalPsgn", task.getKabupatenLegalPsgn());
        result.put("kecamatanLegalPsgn", task.getKecamatanLegalPsgn());
        result.put("kodeposLegalPsgn", task.getKodeposLegalPsgn());
        result.put("namaIbuKandungPsgn", task.getNamaIbuKandungPsgn());
        result.put("emailPsgn", task.getEmailPsgn());
        result.put("mobilePhonePsgn", task.getMobilePhonePsgn());
        result.put("fotoKtpGrntr", task.getFotoKtpGrntr());
        result.put("nikKtpGrntr", task.getNikKtpGrntr());
        result.put("namaKtpGrntr", task.getNamaKtpGrntr());
        result.put("tempatLahirGrntr", task.getTempatLahirGrntr());
        result.put("tanggalLahirGrntr", task.getTanggalLahirGrntr());
        result.put("alamatLegalGrntr", task.getAlamatLegalGrntr());
        result.put("rtLegalGrntr", task.getRtLegalGrntr());
        result.put("rwLegalGrntr", task.getRwLegalGrntr());
        result.put("provinsiLegalGrntr", task.getProvinsiLegalGrntr());
        result.put("kabupatenLegalGrntr", task.getKabupatenLegalGrntr());
        result.put("kecamatanLegalGrntr", task.getKecamatanLegalGrntr());
        result.put("kelurahanLegalGrntr", task.getKelurahanLegalGrntr());
        result.put("kodeposLegalGrntr", task.getKodeposLegalGrntr());
        result.put("namaIbuKandungGrntr", task.getNamaIbuKandungGrntr());
        result.put("emailGrntr", task.getEmailGrntr());
        result.put("mobilePhoneGrntr", task.getMobilePhoneGrntr());
        result.put("statusPemilikRumah", task.getStatusPemilikRumah());
        result.put("npwp", task.getNpwp());
        result.put("fotoKK", task.getFotoKK());
        result.put("alamatSurvey", task.getAlamatSurvey());
        result.put("rtSurvey", task.getRtSurvey());
        result.put("rwSurvey", task.getRwSurvey());
        result.put("provinsiSurvey", task.getProvinsiSurvey());
        result.put("kabupatenSurvey", task.getKabupatenSurvey());
        result.put("kecamatanSurvey", task.getKecamatanSurvey());
        result.put("kelurahanSurvey", task.getKelurahanSurvey());
        result.put("kodeposSurvey", task.getKodeposSurvey());
        result.put("subzipcodeSurvey", task.getSubzipcodeSurvey());
        result.put("productType", task.getProductType());
        result.put("productOfferingType", task.getProductOfferingType());
        result.put("brand", task.getBrand());
        result.put("asset", task.getAsset());
        result.put("groupType", task.getGroupType());
        result.put("type", task.getType());

        if (task.getKategoriAsset() != null && !StringUtils.isEmpty(task.getKategoriAsset())) {
            try {
                Object[][] params = { {Restrictions.eq(paramLovGroup, "ASSET_CATEGORY")}, { Restrictions.eq("code", task.getKategoriAsset()) } };
                MsLov lov = this.getManagerDAO().selectOne(MsLov.class, params);
                result.put("kategoriAsset", lov.getDescription());
            } catch (Exception e) {
                result.put("kategoriAsset", task.getKategoriAsset());
            }
        } else {
            result.put("kategoriAsset", task.getKategoriAsset());
        }

        result.put("tahunKendaraan", task.getTahunKendaraan());

        if (task.getSoa() != null && !StringUtils.isEmpty(task.getSoa())) {
            try {
                Object[][] params = { {Restrictions.eq(paramLovGroup, "SOA")} ,  {Restrictions.eq("code", task.getSoa())} };
                MsLov lov = this.getManagerDAO().selectOne(MsLov.class, params);
                result.put("soa", lov.getDescription());
            } catch (Exception e) {
                result.put("soa", task.getSoa());
            }
        } else {
            result.put("soa", task.getSoa());
        }

        result.put("kodeNamaDealer", task.getKodeNamaDealer());
        result.put("posDealer", task.getPosDealer());
        result.put("kodeNamaSales", task.getKodeNamaSales());
        result.put("notes", task.getNotes());
        result.put("prospectInterest", task.getProspectInterest());
        result.put("persetujuanSlik", task.getPersetujuanSlik());
        result.put("fotoPemohon", task.getFotoPemohon());
        result.put("fotoPsgn", task.getFotoPsgn());
        result.put("fotoGrntr", task.getFotoGrntr());
        result.put("otr", task.getOtr());
        result.put("dpNtfAmount", task.getDpNtfAmount());
        result.put("dpMurni", task.getDpMurni());
        result.put("dpPercentage", task.getDpPercentage());
        result.put("tenor", task.getTenor());
        result.put("angsuran", task.getAngsuran());
        result.put("penghasilan", task.getPenghasilan());
        result.put("education", task.getEducation());
        if (task.getProfessionName() != null && !StringUtils.isEmpty(task.getProfessionName())) {
            try {
                Object[][] params = { { paramLovGroup, "REF_PROFESSION" }, { "code", task.getProfessionName()} };
                MsLov lov = this.getManagerDAO().selectOne(MsLov.class, params);
                result.put("professionName", lov.getDescription());
            } catch (Exception e) {
                result.put("professionName", task.getProfessionName());
            }
        } else {
            result.put("professionName", task.getAgama());
        }
        result.put("lengthWork", task.getLengthWork());
        result.put("dsrPercentage", task.getDsrPercentage());
        result.put("stayLength", task.getStayLength());
        result.put("bpkbOwner", task.getBpkbOwner());
        result.put("bpkbOwnerRelation", task.getBpkbOwnerRelation());
        result.put("biometricPemohonResult", task.getBiometricPemohonResult());
        result.put("biometricPsgnResult", task.getBiometricPsgnResult());
        result.put("biometricGrntResult", task.getBiometricGrntResult());
        result.put("isInstantApproval", task.getIsInstantApproval());
        result.put("productCategoryCode", task.getProductCategoryCode());

        return result;
    }

    @Override
    @Transactional(readOnly=true)
    public  List<Map<String, Object>> getReport_Masked(String branchId, String userId, String startDate, String endDate, AuditContext callerId) {
        String filterOfficeCode = "";
        if (StringUtils.isNotBlank(branchId) && !"0".equalsIgnoreCase(branchId)) {
            Object[][] paramBranch = { { Restrictions.eq(paramUuidBranch, Long.valueOf(branchId)) } };
            MsBranch branch = this.getManagerDAO().selectOne(MsBranch.class, paramBranch);
            filterOfficeCode = "AND OFFICE_CODE = '" + branch.getBranchCode() + "'";
        }
        startDate = startDate + " 00:00:00.000";
        endDate = endDate + " 23:59:59.997";
        
        Object[][] param = {{"startDate", startDate}, {"endDate", endDate}};
        
        String query = 
                "SELECT ID_HEADER\r\n"
                + "FROM REPORT_KAWAN_INTERNAL_H A with(nolock)\r\n"
                + "WHERE DTM_CRT BETWEEN :startDate AND :endDate\r\n"
                + filterOfficeCode;
        
        List<Map<String, Object>> listReportH = this.getManagerDAO().selectAllNativeString(query, param);
        return listReportH;
    }
    @Override
    @Transactional(readOnly=true)
    public  List<Map<String, Object>> getReport_MaskedJson(String branchId, String userId, String startDate, String endDate, AuditContext callerId) {
    	List<Map<String, Object>>  result = new ArrayList<>();
        List<Map<String, Object>> listReportH = getReport_Masked(branchId, userId, startDate, endDate, callerId);
        
        for(Map<String, Object> reportH : listReportH) {
        	BigInteger idH = (BigInteger) reportH.get("d0");
        	Object[][] param = { {"idH", idH} };
        	String query =
        			"SELECT *\n"
        			+ "FROM (\n"
        			+ "	SELECT B.UUID_CAE_DATA [id], B.ORDER_NO_CAE [taskId], 'CAE' as [source]\n"
        			+ "	FROM REPORT_KAWAN_INTERNAL_H A with(nolock)\n"
        			+ "	JOIN TBL_CAE_DATA B with(nolock) ON A.REF_TABLE = 'TBL_CAE_DATA' AND A.REF_UUID = B.UUID_CAE_DATA\n"
        			+ "	WHERE A.ID_HEADER = :idH\n"
        			+ "	UNION ALL\n"
        			+ "	SELECT B.UUID_POLO_DATA [id], B.TASK_ID_POLO [taskId], 'POLO' as [source]\n"
        			+ "	FROM REPORT_KAWAN_INTERNAL_H A with(nolock)\n"
        			+ "	JOIN TBL_POLO_DATA B with(nolock) ON A.REF_TABLE = 'TBL_POLO_DATA' AND A.REF_UUID = B.UUID_POLO_DATA\n"
        			+ "	WHERE A.ID_HEADER = :idH\n"
        			+ ") A\n";
        	List<Map<String, Object>> tempReportH = this.getManagerDAO().selectAllNativeString(query, param);
        	if(tempReportH.size() > 0) {
        		BigInteger id = (BigInteger) tempReportH.get(0).get("d0");
        		String taskId = (String) tempReportH.get(0).get("d1");
        		String source = (String) tempReportH.get(0).get("d2");
        		String json = getJsonFormat(idH, source);
        		Map<String, Object> tempMap = new HashMap<>();
        		tempMap.put("id", id);
        		tempMap.put("taskId", taskId);
        		tempMap.put("json", json);
        		tempMap.put("source", source);
        		result.add(tempMap);
        	}
        }
        return result;
    }
    
    @Override
    @Transactional(readOnly=true)
    public List getTemplateKawanInternal(AuditContext auditContext) {
        List templates = new ArrayList<>();
        
        StringBuilder queryGetTemplate = new StringBuilder();
        queryGetTemplate.append(" SELECT HEADER_COLUMN as header, ATTR_JSON_CAE as keyCae,  ATTR_JSON_POLO as keyPolo, ISNULL(LOV_GROUP, '') as lovGroup ");
        queryGetTemplate.append(" FROM MAPPING_REPORT_KAWAN_INTERNAL WITH(NOLOCK) ");
        queryGetTemplate.append(" WHERE IS_ACTIVE = :isActive ");
        queryGetTemplate.append(" ORDER BY SEQ asc ");
        
        Object[][] params = new Object[][] {{"isActive", "1"}};
        templates = this.getManagerDAO().selectForListOfMapString(queryGetTemplate.toString(), params, null);
        return templates;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    @Transactional(readOnly=true)
    public List listInquiryTaskKawanInternalNativeString(Object[][] params, AuditContext callerId) {
        String taskId = (String) params[0][1];
        String status = (String) params[1][1];
        String startDate = (String) params[2][1];
        String endDate = (String) params[3][1];
        String start = (String) params[4][1];
        String end = (String) params[5][1];
        
        StringBuilder paramsQuery = new StringBuilder();
        StringBuilder paramsQuery1 = new StringBuilder();
        if (StringUtils.isNotBlank(taskId) && !"".equalsIgnoreCase(taskId)) {
            String taskIdParam = "'%" + taskId + "%'";
            paramsQuery.append(" AND (ORDER_NO_CAE LIKE " + taskIdParam + " OR msgt.APPL_NO LIKE " + taskIdParam + ") ");
            paramsQuery1.append(" AND (TASK_ID_POLO LIKE " + taskIdParam + " OR msgt.APPL_NO LIKE " + taskIdParam + ") ");
        }
        if (!"%".equals(startDate) && !"%".equals(endDate)) {
            paramsQuery.append(" AND cd.DTM_CRT BETWEEN '" + startDate + "' AND '" + endDate + "'");
            paramsQuery1.append(" AND cd.DTM_CRT BETWEEN '" + startDate + "' AND '" + endDate + "'");
        }
        
        StringBuilder query = new StringBuilder();
        query.append(" SELECT * FROM ( SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum ");
        query.append(" FROM (SELECT tbl.id as id, tbl.taskId as taskId, tbl.json as json, tbl.source as source, LEFT(CONVERT(VARCHAR, tbl.reqDate, 113), 11) as reqDate, ROW_NUMBER() OVER (ORDER BY tbl.reqDate) AS rownum ");
        query.append(" FROM (SELECT DISTINCT UUID_CAE_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(ORDER_NO_CAE as VARCHAR) + ' / ' + msgt.APPL_NO else ORDER_NO_CAE end as taskId, JSON_REQUEST as json, 'CAE' as source, cd.DTM_CRT as reqDate ");
        query.append(" FROM TBL_CAE_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0");
        query.append(" WHERE cd.IS_SUCCESS like '" + status + "'");
        query.append(" AND cd.IS_FLAG_KAWAN_INTERNAL = '1' ");
        query.append(paramsQuery.toString());
        query.append(" UNION ALL ");
        query.append(" SELECT DISTINCT UUID_POLO_DATA as id, CASE WHEN msgt.APPL_NO is not null then CAST(TASK_ID_POLO as VARCHAR) + ' / ' + msgt.APPL_NO else TASK_ID_POLO end as taskId, JSON_REQUEST as json, 'POLO' as source, cd.DTM_CRT as reqDate ");
        query.append(" FROM TBL_POLO_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0");
        query.append(" WHERE cd.IS_SUCCESS like '" + status + "'");
        query.append(" AND cd.IS_FLAG_KAWAN_INTERNAL = '1' ");
        query.append(paramsQuery1.toString());
        query.append(" ) tbl ");
        query.append(" ) a WHERE a.ROWNUM <= "  + end);
        query.append(" ) b WHERE b.recnum >= " + start);
        
        Object[][] param = null;
        List jsonList = this.getManagerDAO().selectForListOfMapString(query.toString(), param, null);
        Iterator itr = jsonList.iterator();
        while (itr.hasNext()) {
            Map<String, Object> row = (Map<String, Object>) itr.next();
            try {
                String json = clobStringConversion((Clob) row.get("json"));
                row.put("json", json);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return jsonList;
    }
    
    @SuppressWarnings("unchecked")
    @Override
    @Transactional(readOnly=true)
    public Integer countInquiryTaskKawanInternalNativeString(Object[][] params, AuditContext callerId) {
        Integer result = 0;
        String taskId = (String) params[0][1];
        String status = (String) params[1][1];
        String startDate = (String) params[2][1];
        String endDate = (String) params[3][1];
        
        StringBuilder paramsQuery = new StringBuilder();
        StringBuilder paramsQuery1 = new StringBuilder();
        if (StringUtils.isNotBlank(taskId) && !"".equalsIgnoreCase(taskId)) {
            String taskIdParam = "'%" + taskId + "%'";
            paramsQuery.append(" AND (ORDER_NO_CAE LIKE " + taskIdParam + " OR msgt.APPL_NO LIKE " + taskIdParam + ") ");
            paramsQuery1.append(" AND (TASK_ID_POLO LIKE " + taskIdParam + " OR msgt.APPL_NO LIKE " + taskIdParam + ") ");
        }
        if (!"%".equals(startDate) && !"%".equals(endDate)) {
            paramsQuery.append(" AND cd.DTM_CRT BETWEEN '" + startDate + "' AND '" + endDate + "'");
            paramsQuery1.append(" AND cd.DTM_CRT BETWEEN '" + startDate + "' AND '" + endDate + "'");
        }
        
        StringBuilder query = new StringBuilder();
        query.append(" SELECT count(tbl.id) ");
        query.append(" FROM (SELECT DISTINCT UUID_CAE_DATA as id ");
        query.append(" FROM TBL_CAE_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0");
        query.append(" WHERE cd.IS_SUCCESS like '" + status + "'");
        query.append(" AND cd.IS_FLAG_KAWAN_INTERNAL = '1' ");
        query.append(paramsQuery.toString());
        query.append(" UNION ALL ");
        query.append(" SELECT DISTINCT UUID_POLO_DATA as id ");
        query.append(" FROM TBL_POLO_DATA as cd WITH(NOLOCK) left join MS_GROUPTASK msgt WITH(NOLOCK) on msgt.GROUP_TASK_ID = cd.GROUP_TASK_ID and cd.GROUP_TASK_ID != 0");
        query.append(" WHERE cd.IS_SUCCESS like '" + status + "'");
        query.append(" AND cd.IS_FLAG_KAWAN_INTERNAL = '1' ");
        query.append(paramsQuery1.toString());
        query.append(" ) tbl ");
        
        Object[][] param = null;
        result = (Integer) this.getManagerDAO().selectOneNativeString(query.toString(), param);
        return result;
    }

    @Transactional(readOnly=true)
    @Override
    public String getValueDescription(String value, String lovGroup, AuditContext callerId) {
        StringBuilder queryGetDescription = new StringBuilder();
        queryGetDescription.append(" SELECT TOP 1 DESCRIPTION FROM MS_LOV WITH(NOLOCK) ");
        queryGetDescription.append(" WHERE LOV_GROUP = :lovGroup ");
        queryGetDescription.append(" AND CODE = :codeValue ");

        String result = (String) this.getManagerDAO().selectOneNativeString(queryGetDescription.toString(), new Object[][] {{paramLovGroup, lovGroup}, {"codeValue",value}});
        return result;
    }
    //CR-WOMFMSS MASKING SENSITIVE DATA 2023-10-05
    public static String maskSensitiveData(String data, int visibleDigits) {
        if (data == null || data.length() < visibleDigits) {
            return data; // No masking required or invalid input
        }
        StringBuilder maskedData = new StringBuilder();
        // Append the first 'visibleDigits' characters as-is
        maskedData.append(data.substring(0, visibleDigits));
        // Append asterisks for the remaining characters
        for (int i = visibleDigits; i < data.length(); i++) {
            maskedData.append('*');
            }
        return maskedData.toString();
    }
    private Map<String, String> getReportKawanInternalD(BigInteger idH) {
        Map<String, String> result = new HashMap<String, String>();
        Object[][] param = { {"idH", idH}, {"listMask", MASKED_HEADER} };
        String query =
                "SELECT\r\n"
                + " A.CODE,\r\n"
                + " CASE\r\n"
                + "     WHEN A.CODE IN (:listMask)\r\n"
                + "     THEN REPLICATE('*', CASE WHEN LEN(A.VALUE) < 200 THEN LEN(A.VALUE) ELSE 200 END)\r\n"
                + "     ELSE A.VALUE\r\n"
                + " END\r\n"
                + " VALUE\r\n"
                + "FROM REPORT_KAWAN_INTERNAL_D A with(nolock)\r\n"
                + "WHERE ID_HEADER = :idH\r\n";
        
        List<Map<String, Object>> listReportD = this.getManagerDAO().selectAllNativeString(query, param);
        
        if(listReportD.size() > 0) {
            for(Map<String, Object> reportD : listReportD) {
                String code = String.valueOf(reportD.get("d0"));
                String value = String.valueOf(reportD.get("d1"));
                result.put(code, value);
            }
        }
        return result;
    }
    private String getJsonFormat(BigInteger idH, String source) {
    	String result = null;
    	Map<String, String> listReportD_paramNamed = new HashMap<>();
    	Map<String, String> listReportD = getReportKawanInternalD(idH);
    	
    	List<Map<String, String>> listTemplate = getTemplateKawanInternal(null);
    	
    	if(listTemplate.size() > 0) {
    		for(Map<String, String> template : listTemplate) {
    			String header = template.get("header");
    			String key = "";
    			switch(source) {
    			case "CAE":
    				key = template.get("keyCae"); 
    				break;
    			case "POLO":
    				template.get("keyPolo");
    				break;
    			}
    			if(!key.isEmpty()) {
    				String value = listReportD.get(header);
    				listReportD_paramNamed.put(key, value);
    			}
    		}
    	}
    	result = gson.toJson(listReportD_paramNamed);
        return result;
    }
}

@SuppressWarnings("serial")
class TaskKawanInternal implements Serializable {
    @SerializedName("officeCode")
    private String officeCode;
    @SerializedName("odrNoCAE")
    private String odrNoCAE;
    @SerializedName("dukcapilStatus")
    private String dukcapilStatus;
    @SerializedName("negativeStatus")
    private String negativeStatus;
    @SerializedName("teleStatus")
    private String teleStatus;
    @SerializedName("customerName")
    private String customerName;
    @SerializedName("mobilePhone")
    private String mobilePhone;
    @SerializedName("promiseSurveyDatetime")
    private String promiseSurveyDatetime;
    @SerializedName("totalKontrakBakiAllSlikAktf")
    private String totalKontrakBakiAllSlikAktf;
    @SerializedName("totaKontrakTunggakSlikAktf")
    private String totaKontrakTunggakSlikAktf;
    @SerializedName("totalAngsuranSlikAktf")
    private String totalAngsuranSlikAktf;
    @SerializedName("maxDPDSlikAktf")
    private String maxDPDSlikAktf;
    @SerializedName("lastUpdateSlikAktf")
    private String lastUpdateSlikAktf;
    @SerializedName("totalKontrakSlikLns")
    private String totalKontrakSlikLns;
    @SerializedName("maxDPDSlikLns")
    private String maxDPDSlikLns;
    @SerializedName("totalKontrakMaxDPDSlikLns")
    private String totalKontrakMaxDPDSlikLns;
    @SerializedName("lastUpdateSlikLns")
    private String lastUpdateSlikLns;
    @SerializedName("fotoKtp")
    private String fotoKtp;
    @SerializedName("nikKtp")
    private String nikKtp;
    @SerializedName("tempatLahir")
    private String tempatLahir;
    @SerializedName("tanggalLahir")
    private String tanggalLahir;
    @SerializedName("alamatLegal")
    private String alamatLegal;
    @SerializedName("rtLegal")
    private String rtLegal;
    @SerializedName("rwLegal")
    private String rwLegal;
    @SerializedName("provinsiLegal")
    private String provinsiLegal;
    @SerializedName("kabupatenLegal")
    private String kabupatenLegal;
    @SerializedName("kecamatanLegal")
    private String kecamatanLegal;
    @SerializedName("kelurahanLegal")
    private String kelurahanLegal;
    @SerializedName("kodeposLegal")
    private String kodeposLegal;
    @SerializedName("subzipcodeLegal")
    private String subzipcodeLegal;
    @SerializedName("statusPernikahan")
    private String statusPernikahan;
    @SerializedName("namaIbuKandung")
    private String namaIbuKandung;
    @SerializedName("jenisKelamin")
    private String jenisKelamin;
    @SerializedName("agama")
    private String agama;
    @SerializedName("email")
    private String email;
    @SerializedName("dokTdkLgkp")
    private String dokTdkLgkp;
    @SerializedName("mlbtknPsgn")
    private String mlbtknPsgn;
    @SerializedName("psgnTdkTtd")
    private String psgnTdkTtd;
    @SerializedName("fotoKtpPsgn")
    private String fotoKtpPsgn;
    @SerializedName("nikKtpPsgn")
    private String nikKtpPsgn;
    @SerializedName("namaKtpPsgn")
    private String namaKtpPsgn;
    @SerializedName("tempatLahirPsgn")
    private String tempatLahirPsgn;
    @SerializedName("tanggalLahirPsgn")
    private String tanggalLahirPsgn;
    @SerializedName("alamatLegalPsgn")
    private String alamatLegalPsgn;
    @SerializedName("rtLegalPsgn")
    private String rtLegalPsgn;
    @SerializedName("rwLegalPsgn")
    private String rwLegalPsgn;
    @SerializedName("provinsiLegalPsgn")
    private String provinsiLegalPsgn;
    @SerializedName("kabupatenLegalPsgn")
    private String kabupatenLegalPsgn;
    @SerializedName("kecamatanLegalPsgn")
    private String kecamatanLegalPsgn;
    @SerializedName("kodeposLegalPsgn")
    private String kodeposLegalPsgn;
    @SerializedName("namaIbuKandungPsgn")
    private String namaIbuKandungPsgn;
    @SerializedName("emailPsgn")
    private String emailPsgn;
    @SerializedName("mobilePhonePsgn")
    private String mobilePhonePsgn;
    @SerializedName("fotoKtpGrntr")
    private String fotoKtpGrntr;
    @SerializedName("nikKtpGrntr")
    private String nikKtpGrntr;
    @SerializedName("namaKtpGrntr")
    private String namaKtpGrntr;
    @SerializedName("tempatLahirGrntr")
    private String tempatLahirGrntr;
    @SerializedName("tanggalLahirGrntr")
    private String tanggalLahirGrntr;
    @SerializedName("alamatLegalGrntr")
    private String alamatLegalGrntr;
    @SerializedName("rtLegalGrntr")
    private String rtLegalGrntr;
    @SerializedName("rwLegalGrntr")
    private String rwLegalGrntr;
    @SerializedName("provinsiLegalGrntr")
    private String provinsiLegalGrntr;
    @SerializedName("kabupatenLegalGrntr")
    private String kabupatenLegalGrntr;
    @SerializedName("kecamatanLegalGrntr")
    private String kecamatanLegalGrntr;
    @SerializedName("kelurahanLegalGrntr")
    private String kelurahanLegalGrntr;
    @SerializedName("kodeposLegalGrntr")
    private String kodeposLegalGrntr;
    @SerializedName("namaIbuKandungGrntr")
    private String namaIbuKandungGrntr;
    @SerializedName("emailGrntr")
    private String emailGrntr;
    @SerializedName("mobilePhoneGrntr")
    private String mobilePhoneGrntr;
    @SerializedName("statusPemilikRumah")
    private String statusPemilikRumah;
    @SerializedName("npwp")
    private String npwp;
    @SerializedName("fotoKK")
    private String fotoKK;
    @SerializedName("alamatSurvey")
    private String alamatSurvey;
    @SerializedName("rtSurvey")
    private String rtSurvey;
    @SerializedName("rwSurvey")
    private String rwSurvey;
    @SerializedName("provinsiSurvey")
    private String provinsiSurvey;
    @SerializedName("kabupatenSurvey")
    private String kabupatenSurvey;
    @SerializedName("kecamatanSurvey")
    private String kecamatanSurvey;
    @SerializedName("kelurahanSurvey")
    private String kelurahanSurvey;
    @SerializedName("kodeposSurvey")
    private String kodeposSurvey;
    @SerializedName("subzipcodeSurvey")
    private String subzipcodeSurvey;
    @SerializedName("productType")
    private String productType;
    @SerializedName("productOfferingType")
    private String productOfferingType;
    @SerializedName("brand")
    private String brand;
    @SerializedName("asset")
    private String asset;
    @SerializedName("groupType")
    private String groupType;
    @SerializedName("type")
    private String type;
    @SerializedName("kategoriAsset")
    private String kategoriAsset;
    @SerializedName("tahunKendaraan")
    private String tahunKendaraan;
    @SerializedName("soa")
    private String soa;
    @SerializedName("kodeNamaDealer")
    private String kodeNamaDealer;
    @SerializedName("posDealer")
    private String posDealer;
    @SerializedName("kodeNamaSales")
    private String kodeNamaSales;
    @SerializedName("notes")
    private String notes;
    @SerializedName("prospectInterest")
    private String prospectInterest;
    @SerializedName("persetujuanSlik")
    private String persetujuanSlik;
    @SerializedName("fotoPemohon")
    private String fotoPemohon;
    @SerializedName("fotoPsgn")
    private String fotoPsgn;
    @SerializedName("fotoGrntr")
    private String fotoGrntr;
    @SerializedName("otr")
    private String otr;
    @SerializedName("dpNtfAmount")
    private String dpNtfAmount;
    @SerializedName("dpMurni")
    private String dpMurni;
    @SerializedName("dpPercentage")
    private String dpPercentage;
    @SerializedName("tenor")
    private String tenor;
    @SerializedName("angsuran")
    private String angsuran;
    @SerializedName("penghasilan")
    private String penghasilan;
    @SerializedName("education")
    private String education;
    @SerializedName("professionName")
    private String professionName;
    @SerializedName("lengthWork")
    private String lengthWork;
    @SerializedName("dsrPercentage")
    private String dsrPercentage;
    @SerializedName("stayLength")
    private String stayLength;
    @SerializedName("bpkbOwner")
    private String bpkbOwner;
    @SerializedName("bpkbOwnerRelation")
    private String bpkbOwnerRelation;
    @SerializedName("biometricPemohonResult")
    private String biometricPemohonResult;
    @SerializedName("biometricPsgnResult")
    private String biometricPsgnResult;
    @SerializedName("biometricGrntResult")
    private String biometricGrntResult;
    @SerializedName("isInstantApproval")
    private String isInstantApproval;
    @SerializedName("productCategoryCode")
    private String productCategoryCode;

    public String getOfficeCode() {
        return officeCode;
    }

    public void setOfficeCode(String officeCode) {
        this.officeCode = officeCode;
    }

    public String getOdrNoCAE() {
        return odrNoCAE;
    }

    public void setOdrNoCAE(String odrNoCAE) {
        this.odrNoCAE = odrNoCAE;
    }

    public String getDukcapilStatus() {
        return dukcapilStatus;
    }

    public void setDukcapilStatus(String dukcapilStatus) {
        this.dukcapilStatus = dukcapilStatus;
    }

    public String getNegativeStatus() {
        return negativeStatus;
    }

    public void setNegativeStatus(String negativeStatus) {
        this.negativeStatus = negativeStatus;
    }

    public String getTeleStatus() {
        return teleStatus;
    }

    public void setTeleStatus(String teleStatus) {
        this.teleStatus = teleStatus;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getPromiseSurveyDatetime() {
        return promiseSurveyDatetime;
    }

    public void setPromiseSurveyDatetime(String promiseSurveyDatetime) {
        this.promiseSurveyDatetime = promiseSurveyDatetime;
    }

    public String getTotalKontrakBakiAllSlikAktf() {
        return totalKontrakBakiAllSlikAktf;
    }

    public void setTotalKontrakBakiAllSlikAktf(String totalKontrakBakiAllSlikAktf) {
        this.totalKontrakBakiAllSlikAktf = totalKontrakBakiAllSlikAktf;
    }

    public String getTotaKontrakTunggakSlikAktf() {
        return totaKontrakTunggakSlikAktf;
    }

    public void setTotaKontrakTunggakSlikAktf(String totaKontrakTunggakSlikAktf) {
        this.totaKontrakTunggakSlikAktf = totaKontrakTunggakSlikAktf;
    }

    public String getTotalAngsuranSlikAktf() {
        return totalAngsuranSlikAktf;
    }

    public void setTotalAngsuranSlikAktf(String totalAngsuranSlikAktf) {
        this.totalAngsuranSlikAktf = totalAngsuranSlikAktf;
    }

    public String getMaxDPDSlikAktf() {
        return maxDPDSlikAktf;
    }

    public void setMaxDPDSlikAktf(String maxDPDSlikAktf) {
        this.maxDPDSlikAktf = maxDPDSlikAktf;
    }

    public String getLastUpdateSlikAktf() {
        return lastUpdateSlikAktf;
    }

    public void setLastUpdateSlikAktf(String lastUpdateSlikAktf) {
        this.lastUpdateSlikAktf = lastUpdateSlikAktf;
    }

    public String getTotalKontrakSlikLns() {
        return totalKontrakSlikLns;
    }

    public void setTotalKontrakSlikLns(String totalKontrakSlikLns) {
        this.totalKontrakSlikLns = totalKontrakSlikLns;
    }

    public String getMaxDPDSlikLns() {
        return maxDPDSlikLns;
    }

    public void setMaxDPDSlikLns(String maxDPDSlikLns) {
        this.maxDPDSlikLns = maxDPDSlikLns;
    }

    public String getTotalKontrakMaxDPDSlikLns() {
        return totalKontrakMaxDPDSlikLns;
    }

    public void setTotalKontrakMaxDPDSlikLns(String totalKontrakMaxDPDSlikLns) {
        this.totalKontrakMaxDPDSlikLns = totalKontrakMaxDPDSlikLns;
    }

    public String getLastUpdateSlikLns() {
        return lastUpdateSlikLns;
    }

    public void setLastUpdateSlikLns(String lastUpdateSlikLns) {
        this.lastUpdateSlikLns = lastUpdateSlikLns;
    }

    public String getFotoKtp() {
        return fotoKtp;
    }

    public void setFotoKtp(String fotoKtp) {
        this.fotoKtp = fotoKtp;
    }

    public String getNikKtp() {
        return nikKtp;
    }

    public void setNikKtp(String nikKtp) {
        this.nikKtp = nikKtp;
    }

    public String getTempatLahir() {
        return tempatLahir;
    }

    public void setTempatLahir(String tempatLahir) {
        this.tempatLahir = tempatLahir;
    }

    public String getTanggalLahir() {
        return tanggalLahir;
    }

    public void setTanggalLahir(String tanggalLahir) {
        this.tanggalLahir = tanggalLahir;
    }

    public String getAlamatLegal() {
        return alamatLegal;
    }

    public void setAlamatLegal(String alamatLegal) {
        this.alamatLegal = alamatLegal;
    }

    public String getRtLegal() {
        return rtLegal;
    }

    public void setRtLegal(String rtLegal) {
        this.rtLegal = rtLegal;
    }

    public String getRwLegal() {
        return rwLegal;
    }

    public void setRwLegal(String rwLegal) {
        this.rwLegal = rwLegal;
    }

    public String getProvinsiLegal() {
        return provinsiLegal;
    }

    public void setProvinsiLegal(String provinsiLegal) {
        this.provinsiLegal = provinsiLegal;
    }

    public String getKabupatenLegal() {
        return kabupatenLegal;
    }

    public void setKabupatenLegal(String kabupatenLegal) {
        this.kabupatenLegal = kabupatenLegal;
    }

    public String getKecamatanLegal() {
        return kecamatanLegal;
    }

    public void setKecamatanLegal(String kecamatanLegal) {
        this.kecamatanLegal = kecamatanLegal;
    }

    public String getKelurahanLegal() {
        return kelurahanLegal;
    }

    public void setKelurahanLegal(String kelurahanLegal) {
        this.kelurahanLegal = kelurahanLegal;
    }

    public String getKodeposLegal() {
        return kodeposLegal;
    }

    public void setKodeposLegal(String kodeposLegal) {
        this.kodeposLegal = kodeposLegal;
    }

    public String getSubzipcodeLegal() {
        return subzipcodeLegal;
    }

    public void setSubzipcodeLegal(String subzipcodeLegal) {
        this.subzipcodeLegal = subzipcodeLegal;
    }

    public String getStatusPernikahan() {
        return statusPernikahan;
    }

    public void setStatusPernikahan(String statusPernikahan) {
        this.statusPernikahan = statusPernikahan;
    }

    public String getNamaIbuKandung() {
        return namaIbuKandung;
    }

    public void setNamaIbuKandung(String namaIbuKandung) {
        this.namaIbuKandung = namaIbuKandung;
    }

    public String getJenisKelamin() {
        return jenisKelamin;
    }

    public void setJenisKelamin(String jenisKelamin) {
        this.jenisKelamin = jenisKelamin;
    }

    public String getAgama() {
        return agama;
    }

    public void setAgama(String agama) {
        this.agama = agama;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDokTdkLgkp() {
        return dokTdkLgkp;
    }

    public void setDokTdkLgkp(String dokTdkLgkp) {
        this.dokTdkLgkp = dokTdkLgkp;
    }

    public String getMlbtknPsgn() {
        return mlbtknPsgn;
    }

    public void setMlbtknPsgn(String mlbtknPsgn) {
        this.mlbtknPsgn = mlbtknPsgn;
    }

    public String getPsgnTdkTtd() {
        return psgnTdkTtd;
    }

    public void setPsgnTdkTtd(String psgnTdkTtd) {
        this.psgnTdkTtd = psgnTdkTtd;
    }

    public String getFotoKtpPsgn() {
        return fotoKtpPsgn;
    }

    public void setFotoKtpPsgn(String fotoKtpPsgn) {
        this.fotoKtpPsgn = fotoKtpPsgn;
    }

    public String getNikKtpPsgn() {
        return nikKtpPsgn;
    }

    public void setNikKtpPsgn(String nikKtpPsgn) {
        this.nikKtpPsgn = nikKtpPsgn;
    }

    public String getNamaKtpPsgn() {
        return namaKtpPsgn;
    }

    public void setNamaKtpPsgn(String namaKtpPsgn) {
        this.namaKtpPsgn = namaKtpPsgn;
    }

    public String getTempatLahirPsgn() {
        return tempatLahirPsgn;
    }

    public void setTempatLahirPsgn(String tempatLahirPsgn) {
        this.tempatLahirPsgn = tempatLahirPsgn;
    }

    public String getTanggalLahirPsgn() {
        return tanggalLahirPsgn;
    }

    public void setTanggalLahirPsgn(String tanggalLahirPsgn) {
        this.tanggalLahirPsgn = tanggalLahirPsgn;
    }

    public String getAlamatLegalPsgn() {
        return alamatLegalPsgn;
    }

    public void setAlamatLegalPsgn(String alamatLegalPsgn) {
        this.alamatLegalPsgn = alamatLegalPsgn;
    }

    public String getRtLegalPsgn() {
        return rtLegalPsgn;
    }

    public void setRtLegalPsgn(String rtLegalPsgn) {
        this.rtLegalPsgn = rtLegalPsgn;
    }

    public String getRwLegalPsgn() {
        return rwLegalPsgn;
    }

    public void setRwLegalPsgn(String rwLegalPsgn) {
        this.rwLegalPsgn = rwLegalPsgn;
    }

    public String getProvinsiLegalPsgn() {
        return provinsiLegalPsgn;
    }

    public void setProvinsiLegalPsgn(String provinsiLegalPsgn) {
        this.provinsiLegalPsgn = provinsiLegalPsgn;
    }

    public String getKabupatenLegalPsgn() {
        return kabupatenLegalPsgn;
    }

    public void setKabupatenLegalPsgn(String kabupatenLegalPsgn) {
        this.kabupatenLegalPsgn = kabupatenLegalPsgn;
    }

    public String getKecamatanLegalPsgn() {
        return kecamatanLegalPsgn;
    }

    public void setKecamatanLegalPsgn(String kecamatanLegalPsgn) {
        this.kecamatanLegalPsgn = kecamatanLegalPsgn;
    }

    public String getKodeposLegalPsgn() {
        return kodeposLegalPsgn;
    }

    public void setKodeposLegalPsgn(String kodeposLegalPsgn) {
        this.kodeposLegalPsgn = kodeposLegalPsgn;
    }

    public String getNamaIbuKandungPsgn() {
        return namaIbuKandungPsgn;
    }

    public void setNamaIbuKandungPsgn(String namaIbuKandungPsgn) {
        this.namaIbuKandungPsgn = namaIbuKandungPsgn;
    }

    public String getEmailPsgn() {
        return emailPsgn;
    }

    public void setEmailPsgn(String emailPsgn) {
        this.emailPsgn = emailPsgn;
    }

    public String getMobilePhonePsgn() {
        return mobilePhonePsgn;
    }

    public void setMobilePhonePsgn(String mobilePhonePsgn) {
        this.mobilePhonePsgn = mobilePhonePsgn;
    }

    public String getFotoKtpGrntr() {
        return fotoKtpGrntr;
    }

    public void setFotoKtpGrntr(String fotoKtpGrntr) {
        this.fotoKtpGrntr = fotoKtpGrntr;
    }

    public String getNikKtpGrntr() {
        return nikKtpGrntr;
    }

    public void setNikKtpGrntr(String nikKtpGrntr) {
        this.nikKtpGrntr = nikKtpGrntr;
    }

    public String getNamaKtpGrntr() {
        return namaKtpGrntr;
    }

    public void setNamaKtpGrntr(String namaKtpGrntr) {
        this.namaKtpGrntr = namaKtpGrntr;
    }

    public String getTempatLahirGrntr() {
        return tempatLahirGrntr;
    }

    public void setTempatLahirGrntr(String tempatLahirGrntr) {
        this.tempatLahirGrntr = tempatLahirGrntr;
    }

    public String getTanggalLahirGrntr() {
        return tanggalLahirGrntr;
    }

    public void setTanggalLahirGrntr(String tanggalLahirGrntr) {
        this.tanggalLahirGrntr = tanggalLahirGrntr;
    }

    public String getAlamatLegalGrntr() {
        return alamatLegalGrntr;
    }

    public void setAlamatLegalGrntr(String alamatLegalGrntr) {
        this.alamatLegalGrntr = alamatLegalGrntr;
    }

    public String getRtLegalGrntr() {
        return rtLegalGrntr;
    }

    public void setRtLegalGrntr(String rtLegalGrntr) {
        this.rtLegalGrntr = rtLegalGrntr;
    }

    public String getRwLegalGrntr() {
        return rwLegalGrntr;
    }

    public void setRwLegalGrntr(String rwLegalGrntr) {
        this.rwLegalGrntr = rwLegalGrntr;
    }

    public String getProvinsiLegalGrntr() {
        return provinsiLegalGrntr;
    }

    public void setProvinsiLegalGrntr(String provinsiLegalGrntr) {
        this.provinsiLegalGrntr = provinsiLegalGrntr;
    }

    public String getKabupatenLegalGrntr() {
        return kabupatenLegalGrntr;
    }

    public void setKabupatenLegalGrntr(String kabupatenLegalGrntr) {
        this.kabupatenLegalGrntr = kabupatenLegalGrntr;
    }

    public String getKecamatanLegalGrntr() {
        return kecamatanLegalGrntr;
    }

    public void setKecamatanLegalGrntr(String kecamatanLegalGrntr) {
        this.kecamatanLegalGrntr = kecamatanLegalGrntr;
    }

    public String getKelurahanLegalGrntr() {
        return kelurahanLegalGrntr;
    }

    public void setKelurahanLegalGrntr(String kelurahanLegalGrntr) {
        this.kelurahanLegalGrntr = kelurahanLegalGrntr;
    }

    public String getKodeposLegalGrntr() {
        return kodeposLegalGrntr;
    }

    public void setKodeposLegalGrntr(String kodeposLegalGrntr) {
        this.kodeposLegalGrntr = kodeposLegalGrntr;
    }

    public String getNamaIbuKandungGrntr() {
        return namaIbuKandungGrntr;
    }

    public void setNamaIbuKandungGrntr(String namaIbuKandungGrntr) {
        this.namaIbuKandungGrntr = namaIbuKandungGrntr;
    }

    public String getEmailGrntr() {
        return emailGrntr;
    }

    public void setEmailGrntr(String emailGrntr) {
        this.emailGrntr = emailGrntr;
    }

    public String getMobilePhoneGrntr() {
        return mobilePhoneGrntr;
    }

    public void setMobilePhoneGrntr(String mobilePhoneGrntr) {
        this.mobilePhoneGrntr = mobilePhoneGrntr;
    }

    public String getStatusPemilikRumah() {
        return statusPemilikRumah;
    }

    public void setStatusPemilikRumah(String statusPemilikRumah) {
        this.statusPemilikRumah = statusPemilikRumah;
    }

    public String getNpwp() {
        return npwp;
    }

    public void setNpwp(String npwp) {
        this.npwp = npwp;
    }

    public String getFotoKK() {
        return fotoKK;
    }

    public void setFotoKK(String fotoKK) {
        this.fotoKK = fotoKK;
    }

    public String getAlamatSurvey() {
        return alamatSurvey;
    }

    public void setAlamatSurvey(String alamatSurvey) {
        this.alamatSurvey = alamatSurvey;
    }

    public String getRtSurvey() {
        return rtSurvey;
    }

    public void setRtSurvey(String rtSurvey) {
        this.rtSurvey = rtSurvey;
    }

    public String getRwSurvey() {
        return rwSurvey;
    }

    public void setRwSurvey(String rwSurvey) {
        this.rwSurvey = rwSurvey;
    }

    public String getProvinsiSurvey() {
        return provinsiSurvey;
    }

    public void setProvinsiSurvey(String provinsiSurvey) {
        this.provinsiSurvey = provinsiSurvey;
    }

    public String getKabupatenSurvey() {
        return kabupatenSurvey;
    }

    public void setKabupatenSurvey(String kabupatenSurvey) {
        this.kabupatenSurvey = kabupatenSurvey;
    }

    public String getKecamatanSurvey() {
        return kecamatanSurvey;
    }

    public void setKecamatanSurvey(String kecamatanSurvey) {
        this.kecamatanSurvey = kecamatanSurvey;
    }

    public String getKelurahanSurvey() {
        return kelurahanSurvey;
    }

    public void setKelurahanSurvey(String kelurahanSurvey) {
        this.kelurahanSurvey = kelurahanSurvey;
    }

    public String getKodeposSurvey() {
        return kodeposSurvey;
    }

    public void setKodeposSurvey(String kodeposSurvey) {
        this.kodeposSurvey = kodeposSurvey;
    }

    public String getSubzipcodeSurvey() {
        return subzipcodeSurvey;
    }

    public void setSubzipcodeSurvey(String subzipcodeSurvey) {
        this.subzipcodeSurvey = subzipcodeSurvey;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductOfferingType() {
        return productOfferingType;
    }

    public void setProductOfferingType(String productOfferingType) {
        this.productOfferingType = productOfferingType;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getAsset() {
        return asset;
    }

    public void setAsset(String asset) {
        this.asset = asset;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getKategoriAsset() {
        return kategoriAsset;
    }

    public void setKategoriAsset(String kategoriAsset) {
        this.kategoriAsset = kategoriAsset;
    }

    public String getTahunKendaraan() {
        return tahunKendaraan;
    }

    public void setTahunKendaraan(String tahunKendaraan) {
        this.tahunKendaraan = tahunKendaraan;
    }

    public String getSoa() {
        return soa;
    }

    public void setSoa(String soa) {
        this.soa = soa;
    }

    public String getKodeNamaDealer() {
        return kodeNamaDealer;
    }

    public void setKodeNamaDealer(String kodeNamaDealer) {
        this.kodeNamaDealer = kodeNamaDealer;
    }

    public String getPosDealer() {
        return posDealer;
    }

    public void setPosDealer(String posDealer) {
        this.posDealer = posDealer;
    }

    public String getKodeNamaSales() {
        return kodeNamaSales;
    }

    public void setKodeNamaSales(String kodeNamaSales) {
        this.kodeNamaSales = kodeNamaSales;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getProspectInterest() {
        return prospectInterest;
    }

    public void setProspectInterest(String prospectInterest) {
        this.prospectInterest = prospectInterest;
    }

    public String getPersetujuanSlik() {
        return persetujuanSlik;
    }

    public void setPersetujuanSlik(String persetujuanSlik) {
        this.persetujuanSlik = persetujuanSlik;
    }

    public String getFotoPemohon() {
        return fotoPemohon;
    }

    public void setFotoPemohon(String fotoPemohon) {
        this.fotoPemohon = fotoPemohon;
    }

    public String getFotoPsgn() {
        return fotoPsgn;
    }

    public void setFotoPsgn(String fotoPsgn) {
        this.fotoPsgn = fotoPsgn;
    }

    public String getFotoGrntr() {
        return fotoGrntr;
    }

    public void setFotoGrntr(String fotoGrntr) {
        this.fotoGrntr = fotoGrntr;
    }

    public String getOtr() {
        return otr;
    }

    public void setOtr(String otr) {
        this.otr = otr;
    }

    public String getDpNtfAmount() {
        return dpNtfAmount;
    }

    public void setDpNtfAmount(String dpNtfAmount) {
        this.dpNtfAmount = dpNtfAmount;
    }

    public String getDpMurni() {
        return dpMurni;
    }

    public void setDpMurni(String dpMurni) {
        this.dpMurni = dpMurni;
    }

    public String getDpPercentage() {
        return dpPercentage;
    }

    public void setDpPercentage(String dpPercentage) {
        this.dpPercentage = dpPercentage;
    }

    public String getTenor() {
        return tenor;
    }

    public void setTenor(String tenor) {
        this.tenor = tenor;
    }

    public String getAngsuran() {
        return angsuran;
    }

    public void setAngsuran(String angsuran) {
        this.angsuran = angsuran;
    }

    public String getPenghasilan() {
        return penghasilan;
    }

    public void setPenghasilan(String penghasilan) {
        this.penghasilan = penghasilan;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getProfessionName() {
        return professionName;
    }

    public void setProfessionName(String professionName) {
        this.professionName = professionName;
    }

    public String getLengthWork() {
        return lengthWork;
    }

    public void setLengthWork(String lengthWork) {
        this.lengthWork = lengthWork;
    }

    public String getDsrPercentage() {
        return dsrPercentage;
    }

    public void setDsrPercentage(String dsrPercentage) {
        this.dsrPercentage = dsrPercentage;
    }

    public String getStayLength() {
        return stayLength;
    }

    public void setStayLength(String stayLength) {
        this.stayLength = stayLength;
    }

    public String getBpkbOwner() {
        return bpkbOwner;
    }

    public void setBpkbOwner(String bpkbOwner) {
        this.bpkbOwner = bpkbOwner;
    }

    public String getBpkbOwnerRelation() {
        return bpkbOwnerRelation;
    }

    public void setBpkbOwnerRelation(String bpkbOwnerRelation) {
        this.bpkbOwnerRelation = bpkbOwnerRelation;
    }

    public String getBiometricPemohonResult() {
        return biometricPemohonResult;
    }

    public void setBiometricPemohonResult(String biometricPemohonResult) {
        this.biometricPemohonResult = biometricPemohonResult;
    }

    public String getBiometricPsgnResult() {
        return biometricPsgnResult;
    }

    public void setBiometricPsgnResult(String biometricPsgnResult) {
        this.biometricPsgnResult = biometricPsgnResult;
    }

    public String getBiometricGrntResult() {
        return biometricGrntResult;
    }

    public void setBiometricGrntResult(String biometricGrntResult) {
        this.biometricGrntResult = biometricGrntResult;
    }

    public String getIsInstantApproval() {
        return isInstantApproval;
    }

    public void setIsInstantApproval(String isInstantApproval) {
        this.isInstantApproval = isInstantApproval;
    }

    public String getProductCategoryCode() {
        return productCategoryCode;
    }

    public void setProductCategoryCode(String productCategoryCode) {
        this.productCategoryCode = productCategoryCode;
    }

}
