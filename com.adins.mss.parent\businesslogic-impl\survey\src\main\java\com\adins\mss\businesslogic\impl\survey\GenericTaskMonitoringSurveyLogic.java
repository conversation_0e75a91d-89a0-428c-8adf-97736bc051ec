package com.adins.mss.businesslogic.impl.survey;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.survey.TaskMonitoringSurveyLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.util.CipherTool;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericTaskMonitoringSurveyLogic extends BaseLogic implements
		TaskMonitoringSurveyLogic {
	@Autowired
	private GlobalLogic globalLogic;
	
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);
	
	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}

	@Override
	public Map<String, String> getSpvCombo(String uuidBranch, AuditContext callerId) {
		Map<String, String> result = MapUtils.EMPTY_MAP;
		String tagJob = globalLogic.getGsValue(
				GlobalVal.SUBSYSTEM_MS + GlobalKey.GENERALSETTING_JOBSPV, callerId);
		List listJobs = Arrays.asList(StringUtils.split(tagJob, ";"));
		Object[][] params = { { "uuidBranch", uuidBranch }, { "jobCode", listJobs } };
		List list = this.getManagerDAO().selectAllNative("survey.taskmonitoring.spvCombo", params, null);
		Map<String, String> comboList = new HashMap<String, String>();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				Map temp2 = (Map) list.get(i);
				comboList.put((String) temp2.get("d0"), (String) temp2.get("d1"));
			}
			result = sortByValues(comboList);
		} 	
		return result;
	}
	
	private Map sortByValues(Map<String, String> comboList) {
		List list = new LinkedList(comboList.entrySet());

		Collections.sort(list, new Comparator() {
			public int compare(Object o1, Object o2) {
				return ((Comparable) ((Map.Entry) (o1)).getValue())
						.compareTo(((Map.Entry) (o2)).getValue());
			}
		});

		Map sortedHashMap = new LinkedHashMap();
		for (Iterator it = list.iterator(); it.hasNext();) {
			Map.Entry entry = (Map.Entry) it.next();
			sortedHashMap.put(entry.getKey(), entry.getValue());
		}
		return sortedHashMap;
	}
	
	@Override
	public Map getTaskMonitoring(String id, String uuidSubsystem, AuditContext callerId) {
		Map result = new HashMap<>();
		List monitoringList = new ArrayList<>() ;
		String[][] params = {{"uuidSpv", id}};
		List userList = this.getManagerDAO().selectAllNative(
				"survey.dm.getUsersAll", params, null);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		DateTime currentTime = new DateTime();
		currentTime = currentTime.minusMillis(currentTime.getMillisOfDay());
		Date minDate = currentTime.toDate();
		currentTime = currentTime.plusDays(1).minusMillis(3);
		Date maxDate = currentTime.toDate();
		for (int i = 0; i < userList.size(); i++) {
			Map map = (HashMap) userList.get(i);
		    String uuidSvy= map.get("d0").toString();
		    String[][] params2 = { { "uuidSvy", uuidSvy },
					{"start", formatter.format(minDate)}, {"end", formatter.format(maxDate)} };
			List<Map<String,Object>> task = this.getManagerDAO().selectAllNative(
					"survey.taskmonitoring", params2, null);
			
			List<Map<String,Object>> result2 = new ArrayList<>();
			for (int j = 0; j < task.size(); j++) {
				Map<String, Object> temp = (Map<String, Object>) task.get(j);
				temp.put("d10",  temp.get("d3").toString());
				result2.add(temp);
			}
			Map<String,Object> mapResult = null;
			List<Map<String,Object>> b = new ArrayList<>();
			if(!result2.isEmpty()){
				for(int j=0;j<result2.size(); j++){
					mapResult = result2.get(j);
					String address = "";
					if(mapResult.get("d3") != null){
						address = (String) mapResult.get("d3");
					}
					if (StringUtils.isNotBlank(address)) {
						String[] addressArr = address.split(",");
						if(addressArr.length>=3){
//							mapResult.put("d10", addressArr[2]);
							mapResult.put("d10", "");
						}
						else{
							mapResult.put("d10", "");
						}
					}
					else{
						mapResult.put("d10", "");
					}
					
					if("1".equals(link_encrypt)){
						String[] idToEncrypt = {mapResult.get("d0").toString()};
						mapResult.put("d0",CipherTool.encryptData(idToEncrypt).get(0).toString());	
					}
					b.add(mapResult);
				}
			}
			
			Map a= new HashMap<>();
			a.put("loginId", map.get("d3").toString());
			a.put("fullName", map.get("d1").toString());
			a.put("taskList", b);
			monitoringList.add(a);
		}
		
		String[][] params3 = { { "uuidSpv", id },
				{"start", formatter.format(minDate)}, {"end", formatter.format(maxDate)} };
		
		Object[] summary1 = (Object[]) this.getManagerDAO().selectOneNative(
				"survey.taskmonitoring.sumnewtaskandsubmittedtask", params3);
		
		SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-01 00:00:00.000");
		SimpleDateFormat formatter2 = new SimpleDateFormat("yyyy-MM-dd 23:59:59.997");
		
		Calendar calendar = Calendar.getInstance();  
        calendar.setTime(new Date());  

        calendar.add(Calendar.MONTH, 1);  
        calendar.set(Calendar.DAY_OF_MONTH, 1);  
        calendar.add(Calendar.DATE, -1);  

        Date lastDayOfMonth = calendar.getTime();  
		
		String[][] params4 = { { "uuidSpv", id },
				{"start", formatter1.format(minDate)}, {"end", formatter2.format(lastDayOfMonth)} };
		
		Object[] summary2 = (Object[]) this.getManagerDAO().selectOneNative(
				"survey.taskmonitoring.sumnewtaskandsubmittedtask", params4);
		
		String[][] params5 = { { "uuidSpv", id }, { "subsystem", uuidSubsystem } };
		
		Object summary3 = (Object) this.getManagerDAO().selectOneNative(
				"survey.taskmonitoring.totalpending", params5);
		
		result.put("monitoringList", monitoringList);
		result.put("newSurveyTask", summary1[0].toString());
		result.put("totalTask", summary2[1].toString());
		result.put("totalPending", summary3.toString());
			
		return result ;
	}
		
	@Override
	public Map<String, String> getSpvComboByHierarkiUser(String uuidUser, AuditContext callerId) {
		Map<String, String> result = MapUtils.EMPTY_MAP;
		
		String tagJob = globalLogic.getGsValue(
				GlobalVal.SUBSYSTEM_MS + GlobalKey.GENERALSETTING_JOBSPV, callerId);
		List listJobs = Arrays.asList(StringUtils.split(tagJob, ";"));
		Object[][] params = { { "uuidUser", uuidUser }, { "jobCode", listJobs } };
		List list = this.getManagerDAO().selectAllNative(
				"survey.taskmonitoring.spvComboByHierarkiUser", params, null);
		Map<String, String> comboList = new HashMap<String, String>();
		if (!list.isEmpty()) {
			for (int i = 0; i < list.size(); i++) {
				Map temp2 = (Map) list.get(i);
				comboList.put((String) temp2.get("d0"), (String) temp2.get("d1"));
			}
			result = sortByValues(comboList);
		} 	
		return result;
	}
	
	public boolean checkIsSpv(AmMsuser amMsUser, AuditContext auditContext) {
		Object[][] params = { {Restrictions.eq("gsCode", GlobalVal.SUBSYSTEM_MS+GlobalKey.GENERALSETTING_JOBSPV)} };
		AmGeneralsetting amGeneralsetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if (ArrayUtils.contains(StringUtils.split(amGeneralsetting.getGsValue(), ";"), amMsUser.getMsJob().getJobCode())) {
			return true;
		}
		return false;
	}
	
	@Override
	public String getIntervalDuration(AuditContext callerId){
		Object params[][] = {{Restrictions.eq("gsCode", "INTERVAL")}};
		String result = StringUtils.EMPTY;
		AmGeneralsetting bean = this.getManagerDAO().selectOne(AmGeneralsetting.class, params);
		if(bean!=null){
			result = bean.getGsValue(); 
		}
		else {
			result = "0";
		}
		return result ;
	}
}

