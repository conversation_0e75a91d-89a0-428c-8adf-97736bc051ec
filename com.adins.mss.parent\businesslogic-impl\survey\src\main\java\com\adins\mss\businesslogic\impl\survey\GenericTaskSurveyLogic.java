package com.adins.mss.businesslogic.impl.survey;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.survey.TaskSurveyLogic;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.TrTaskH;

public class GenericTaskSurveyLogic extends BaseLogic implements TaskSurveyLogic{

    @SuppressWarnings("unused")
	private CommonLogic commonLogic;
    
	public void setCommonLogic(CommonLogic commonLogic) {
        this.commonLogic = commonLogic;
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
	@Transactional(readOnly=true)
	@Override
	@Deprecated
	public Map addTask(AuditContext auditContext, String branchCode,
			String applNo, String formId, String priority, String custName,
			String custPhone, String custAddress, String notes, String zipCode) {
		Map  map = new HashMap ();
		String paramB[][] = { {"uuidBranch",branchCode} };
		String paramF[][] = { {"uuidForm", formId} };
		String paramP[][] = { {"uuidPriority", priority} };
		BigInteger uuidBranch = (BigInteger) this.getManagerDAO().selectOneNativeString("select uuid_branch from ms_branch where branch_code = :uuidBranch", paramB);
		BigInteger uuidForm = (BigInteger) this.getManagerDAO().selectOneNativeString("select uuid_form from ms_form where form_name = :uuidForm", paramF);
		BigInteger uuidPriority = (BigInteger) this.getManagerDAO().selectOneNativeString("select uuid_priority from ms_priority where priority_desc = :uuidPriority", paramP);
	
		MsBranch branch = this.getManagerDAO().selectOne(MsBranch.class, uuidBranch);
		MsForm form = this.getManagerDAO().selectOne(MsForm.class, uuidForm);
		MsPriority prior = this.getManagerDAO().selectOne(MsPriority.class, uuidPriority);
		

		TrTaskH tth = new TrTaskH();
		tth.setUsrCrt(auditContext.getCallerId());
		tth.setDtmCrt(new Date());
		tth.setMsBranch(branch);
		tth.setMsForm(form);
		tth.setMsPriority(prior);
		tth.setApplNo(applNo);
		tth.setCustomerName(custName);
		tth.setCustomerPhone(custPhone);
		tth.setCustomerAddress(custAddress);
		tth.setZipCode(zipCode);
		tth.setNotes(notes);

		map.put("taskId", tth.getTaskId());
		map.put("statusTask", "s");
			

		return map;
	}

    /**
     * @deprecated method is deprecated as GET_NEXT_SEQ_NO procedure must be executed in different tx
     */
    @SuppressWarnings("unused")
	@Deprecated
	private String getNewTaskId(String seqCode){
		String result = StringUtils.EMPTY;
		Object[][] params = { { 1, seqCode },
                { 2, "" }};
		Object[][] outputs = { { 3, java.sql.Types.VARCHAR } };
		
		Map<String, ?> map =  this.getManagerDAO().callProcedureNativeString("{ call GET_NEXT_SEQ_NO(?,?,?) } ", params, outputs);
		result = (String) map.get("o1");
		
		return result;
	}
}
