package com.adins.mss.businesslogic.impl.survey;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.properties.SpringPropertiesUtils;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.TaskUpdateLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.enums.FilterType;
import com.adins.mss.exceptions.EntityNotFoundException;
import com.adins.mss.model.AmGeneralsetting;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.TrReportresultlog;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskupdate;
import com.adins.mss.model.custom.ReportDownloadBean;
import com.adins.mss.model.custom.UploadImageBean;
import com.adins.mss.model.custom.ViewImageRequestBean;
import com.adins.mss.model.custom.ViewImageResponseBean;
import com.adins.mss.util.CipherTool;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

@SuppressWarnings({ "rawtypes", "unchecked" })
@Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
public class GenericTaskUpdateLogic extends BaseLogic implements TaskUpdateLogic, MessageSourceAware {
	private final String link_encrypt = SpringPropertiesUtils.getProperty(GlobalKey.LINK_ENCRYPTION);

	private static final String[] HEADER = { "Task Update Id", "Task ID", "Order Number", "Customer Name", "Branch Name", "Full Name", "Assignment Date", "Pending Notes", "Docupro Feedback", "Feedback Notes", "Category", "Sub Category", "Reason Detail", "Validasi"};

	private static final int COLUMN_WIDTH = 20 * 256;

	private static final Logger LOG = LoggerFactory.getLogger(GenericTaskUpdateLogic.class);
	
	private String keyHeader = "header";
	private String keyValue = "value";
	private String keyImage = "isImage";
	@Autowired
	private MessageSource messageSource;
	
	private IntFormLogic intFormLogic;
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	@Override
	public List getComboBranch(String branchId, AuditContext callerId) {
		List result = null;
		String[][] params = { { "branchId", branchId } };
		result = this.getManagerDAO().selectAllNative("report.usermember.getListBranch", params, null);
		return result;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public String saveExportScheduler(String branchId, String userId, String startDate, String endDate,
			AuditContext callerId) {
		AmMsuser amMsUser = this.getManagerDAO().selectOne(AmMsuser.class, Long.valueOf(callerId.getCallerId()));

		String[][] paramsAction = { { "branchId", branchId }, { "userId", userId }, { "startDate", startDate },
				{ "endDate", endDate } };

		ReportDownloadBean reportBean = new ReportDownloadBean();
		reportBean.setSubsystemCode(amMsUser.getAmMssubsystem().getSubsystemName());
		reportBean.setUuidLoginId(String.valueOf(amMsUser.getUuidMsUser()));
		reportBean.setUuidBranch(branchId);
		reportBean.setUuidUser(userId);
		reportBean.setStartDate(startDate);
		reportBean.setEndDate(endDate);
		reportBean.setParamsAction(paramsAction);

		Gson gson = new GsonBuilder().serializeNulls().create();
		String jsonParams = gson.toJson(reportBean, ReportDownloadBean.class);

		TrReportresultlog trReportResultLog = new TrReportresultlog();
		trReportResultLog.setAmMsuser(amMsUser);
		trReportResultLog.setDtmRequest(new Date());
		trReportResultLog.setRptName("Report Task Update");
		trReportResultLog.setRptType(FilterType.FILTER_BY_TASK_UPDATE.toString());
		trReportResultLog.setRptParams(jsonParams);
		trReportResultLog.setProcessStatus("0");
		this.getManagerDAO().insert(trReportResultLog);

		return messageSource.getMessage("businesslogic.global.successrequestdownload", null,
				this.retrieveLocaleAudit(callerId));
	}

	@Override
	public byte[] exportExcel(String branchId, String userId, String startDate, String endDate, AuditContext callerId) {
		XSSFWorkbook workbook = this.createXlsTemplate(branchId, userId, startDate, endDate, callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	@Override
	public List getReport(String branchId, String userId, String startDate, String endDate, AuditContext callerId) {
		List result = new ArrayList<Map>();
		
		StringBuilder paramsQuery = new StringBuilder();
		if (StringUtils.isNotBlank(branchId) && !"0".equals(branchId) && !"%".equals(branchId)) {
			paramsQuery.append(" AND tth.UUID_BRANCH = " + branchId);
		}
		if (!StringUtils.isEmpty(userId) && !"%".equals(userId) ) {
			paramsQuery.append(" AND tth.UUID_MS_USER = " + userId);
		}
		if (!"%".equals(startDate) && !"%".equals(endDate)) {
			paramsQuery.append(" AND ttu.ASSIGN_DATE BETWEEN '" + startDate + "' AND '" + endDate + "'");
		}
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT UUID_TASK_UPDATE as taskupdateid, TASK_ID as taskid, APPL_NO as applno, CUSTOMER_NAME as customername, BRANCH_NAME as branchname, FULL_NAME as fullname, ASSIGN_DATE as assigndate, PENDING_NOTES as pendingnotes, DOCUPRO_FEEDBACK as docuprofeedback, FEEDBACK_NOTES as feedbacknotes, ");
		query.append(" CATEGORY as category, SUB_CATEGORY as subCategory, REASON_DETAIL as reasonDetail, VALIDASI as validasi ");
		query.append(" FROM (select distinct ttu.UUID_TASK_UPDATE, tth.TASK_ID, tth.APPL_NO, tth.CUSTOMER_NAME, msb.BRANCH_NAME, amu.FULL_NAME, ttu.ASSIGN_DATE, PENDING_NOTES, DOCUPRO_FEEDBACK, FEEDBACK_NOTES, ");
		query.append(" CATEGORY, SUB_CATEGORY, REASON_DETAIL, VALIDASI ");
		query.append(" from TR_TASKUPDATE TTU with(nolock) ");
		query.append(" join TR_TASK_H tth with(nolock) on tth.UUID_TASK_H = ttu.UUID_TASK_H ");
		query.append(" join AM_MSUSER amu with(nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER ");
		query.append(" join MS_BRANCH msb with(nolock) on msb.UUID_BRANCH = tth.UUID_BRANCH ");
		query.append(" WHERE 1=1 ");
		query.append(paramsQuery.toString());
		query.append(" ) tbl ");

		result = this.getManagerDAO().selectAllNativeString(query.toString(), null);

		return result;
	}

	private XSSFWorkbook createXlsTemplate(String branchId, String userId, String startDate, String endDate,
			AuditContext callerId) {
		XSSFWorkbook workbook = new XSSFWorkbook();
		try {
			XSSFSheet sheet = workbook.createSheet("Report Task Update Internal");
			List result = this.getReport(branchId, userId, startDate, endDate, callerId);

			this.createData(workbook, sheet, result, startDate, endDate);

		} catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createData(XSSFWorkbook workbook, XSSFSheet sheet, List result, String startDate, String endDate) {
		LOG.info("REPORT TASK UPDATE result : {}", result);
		Map<String, CellStyle> styles = createStyles(workbook);
		int rowcell = 0;
		XSSFRow rowHead = sheet.createRow(rowcell++);
		XSSFRow rowHeader = sheet.createRow(rowcell++);
		for (int i = 0; i < HEADER.length; i++) {
			XSSFCell cellHead = rowHead.createCell(i);
			cellHead.setCellValue("REPORT TASK UPDATE PERIOD "+ startDate.substring(0, 10) + " - " 
					+ endDate.substring(0, 10));
			cellHead.setCellStyle(styles.get(keyHeader));
			
			XSSFCell cellNo = rowHeader.createCell(0);
			cellNo.setCellValue("NO");
			cellNo.setCellStyle(styles.get(keyHeader));
			
			XSSFCell cell = rowHeader.createCell(i+1);
			cell.setCellValue(HEADER[i]);
			cell.setCellStyle(styles.get(keyHeader));
			sheet.setColumnWidth(i, COLUMN_WIDTH);
		}
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, HEADER.length));

		for (int i = 0; i < result.size(); i++) {
			Map temp = (Map)result.get(i);
			XSSFRow rowData = sheet.createRow(rowcell++);
			//Number cell
			XSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i + (double) 1);
			cellNo.setCellStyle(styles.get("cell"));
			//data cell			
			for (int j = 1; j <= temp.size(); j++) {
				XSSFCell cell = rowData.createCell(j);
				if(temp.get("d"+(j-1)) != null) {
					cell.setCellValue(temp.get("d" + (j-1)).toString());
				} else {
					cell.setCellValue("");
				}
				
				cell.setCellStyle(styles.get("cell"));
			}
			
		} 
	}

	private static Map<String, CellStyle> createStyles(Workbook wb) {
		Map<String, CellStyle> styles = new HashMap<>();
		CellStyle style;

		style = wb.createCellStyle();
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setVerticalAlignment(CellStyle.VERTICAL_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		styles.put("cell", style);

		style = wb.createCellStyle();
		Font font = wb.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setFillPattern(CellStyle.SOLID_FOREGROUND);
		styles.put("header", style);
		return styles;
	}

	@Transactional(isolation = Isolation.READ_UNCOMMITTED)
	@Override
	public void doExportScheduler(TrReportresultlog trReportResultLog, AuditContext callerId) {
		ReportDownloadBean reportBean = new Gson().fromJson(trReportResultLog.getRptParams(), ReportDownloadBean.class);
		String[][] params = reportBean.getParamsAction();
		String branchId = params[0][1];
		String userId = params[1][1];
		String startDate = params[2][1];
		String endDate = params[3][1];

		XSSFWorkbook workbook = this.createXlsTemplate(branchId, userId, startDate, endDate, callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		String filePath = StringUtils.EMPTY;
		try {
			workbook.write(stream);

			byte[] exp = stream.toByteArray();

			Object[][] param = { { Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_XLS_RESULT_PATH) } };
			AmGeneralsetting amGeneralSetting = this.getManagerDAO().selectOne(AmGeneralsetting.class, param);
			if (amGeneralSetting == null) {
				throw new EntityNotFoundException(this.messageSource.getMessage("businesslogic.report.excelresult",
						null, this.retrieveLocaleAudit(callerId)), GlobalKey.GENERALSETTING_XLS_RESULT_PATH);
			}
			String pathFolder = amGeneralSetting.getGsValue();

			File doneFolder = new File(
					pathFolder + DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyy"));
			if (!doneFolder.exists()) {
				doneFolder.mkdirs();
			}

			StringBuilder sb = new StringBuilder();

			sb.append("ReportTaskUpdate_");
			if (StringUtils.isNotBlank(reportBean.getStartDate())) {
				try {
					sb.append(DateFormatUtils.format(
							DateUtils.parseDate(reportBean.getStartDate(), "yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} catch (ParseException e) {
					LOG.error("Error when parse start date : {}", reportBean.getStartDate(), e);
					sb.append("-");
				}
			} else {
				sb.append("-");
			}
			sb.append("-");
			if (StringUtils.isNotBlank(reportBean.getEndDate())) {
				try {
					sb.append(DateFormatUtils
							.format(DateUtils.parseDate(reportBean.getEndDate(), "yyyy-MM-dd HH:mm:ss.S"), "yyyyMMdd"));
				} catch (ParseException e) {
					LOG.error("Error when parse end date : {}", reportBean.getEndDate(), e);
					sb.append("-");
				}
			} else {
				sb.append("-");
			}

			sb.append("_BRANCH-");
			if (StringUtils.isNotBlank(reportBean.getUuidBranch())) {
				if ("0".equals(reportBean.getUuidBranch()) || "%".equals(reportBean.getUuidBranch())) {
					sb.append("ALL");
				} else {
					MsBranch msBranch = this.getManagerDAO().selectOne(MsBranch.class,
							Long.valueOf(reportBean.getUuidBranch()));
					sb.append(msBranch.getBranchCode());
				}
			} else {
				sb.append("ALL");
			}

			sb.append("_");
			sb.append(DateFormatUtils.format(trReportResultLog.getProcessStartTime(), "yyyyMMddHHmmss"));
			sb.append(".xlsx");

			filePath = doneFolder.getPath() + SystemUtils.FILE_SEPARATOR + sb.toString();
			LOG.info("File path generated report result : {}", filePath);
			FileOutputStream fileOut = new FileOutputStream(filePath);
			try {
				fileOut.write(exp);
				fileOut.flush();
			} finally {
				fileOut.close();
			}
		} catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}

		trReportResultLog.setProcessFinishTime(new Date());
		trReportResultLog.setProcessDurationSeconds((int) Math.abs(
				trReportResultLog.getProcessStartTime().getTime() - trReportResultLog.getProcessFinishTime().getTime())
				/ 1000);
		trReportResultLog.setReportFileLocation(filePath);
		trReportResultLog.setProcessStatus("1");
		this.getManagerDAO().update(trReportResultLog);
	}

	@Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}

	@Override
	@Transactional(readOnly=true)
	public List getDetailTaskUpdate(AuditContext auditContext, String taskUpdateId) {
		List result = new ArrayList<>();
		
		TrTaskupdate taskUpdate = this.getManagerDAO().selectOne(TrTaskupdate.class, Long.valueOf(taskUpdateId));
		
		this.getManagerDAO().fetch(taskUpdate.getTrTaskH());
		TrTaskH taskH = taskUpdate.getTrTaskH();

		Map<String, Object> detailMap1 = new HashMap<>();
		detailMap1.put(keyHeader, "Pending Notes");
		detailMap1.put(keyValue, taskUpdate.getPendingNotes());
		detailMap1.put(keyImage, "0");
		result.add(detailMap1);
		
		Map<String, Object> detailMap2 = new HashMap<>();
		detailMap2.put(keyHeader, "Docupro Feedback");
		detailMap2.put(keyValue, taskUpdate.getDocuproFeedback());
		detailMap2.put(keyImage, "0");
		result.add(detailMap2);
		
		Map<String, Object> detailMap3 = new HashMap<>();
		detailMap3.put(keyHeader, "Feedback Notes");
		detailMap3.put(keyValue, taskUpdate.getFeedbackNotes());
		detailMap3.put(keyImage, "0");
		result.add(detailMap3);

		Map<String, Object> detailMap4 = new HashMap<>();
		detailMap4.put(keyHeader, "Document");
		if(taskUpdate.getLobDocument() != null) {
			detailMap4.put(keyValue, BaseEncoding.base64().encode(taskUpdate.getLobDocument()));
			detailMap4.put(keyImage, "1");
		} else if(taskUpdate.getDocumentPath() != null) {
			String uuid = taskUpdate.getDocumentPath();
			
			if ("1".equals(this.link_encrypt)) {
				String[] temp = { uuid };
				uuid = CipherTool.encryptData(temp).get(0).toString();
			}
	
			ViewImageRequestBean request = new ViewImageRequestBean();
			UploadImageBean imageBean = new UploadImageBean();
			
			//Pengecekan cutoff perubahan taskId DMS menggunakan taskGroup
			Object[][] paramImgLoc = { {Restrictions.eq("gsCode", GlobalKey.GENERALSETTING_DMS_TASK_ID_CUTOFF_DATE)} };
	        AmGeneralsetting gs = this.getManagerDAO().selectOne(AmGeneralsetting.class, paramImgLoc);
	        Date cutoffDate = null;
	        Date taskDate = null;
	        if(null != taskUpdate.getDtmUpd()) {
	        	taskDate = taskUpdate.getDtmUpd();
	        } else {
	        	taskDate = taskUpdate.getDtmCrt();
	        }
	        Object [][] params = {{"uuidTaskH", taskH.getTaskId()}};
			Object groupTaskId = (Object) this.getManagerDAO().selectOneNativeString(
					"SELECT	GROUP_TASK_ID " + 
					"FROM	MS_GROUPTASK with(nolock) " + 
					"WHERE	UUID_TASK_H = :uuidTaskH ", params);
			String groupTaskIdForm = String.valueOf(groupTaskId).concat("_").concat(String.valueOf(taskH.getMsForm().getUuidForm()));

			try {
				cutoffDate = new SimpleDateFormat("dd/MM/yyyy").parse(gs.getGsValue());
			} catch (ParseException e1) {
				e1.printStackTrace();
			}

			if(taskDate.before(cutoffDate)) {
				imageBean.setTaskId(taskH.getTaskId());
				imageBean.setId(taskH.getTaskId());
			} else if (taskDate.after(cutoffDate)) {
				imageBean.setTaskId(groupTaskIdForm);
				imageBean.setId(groupTaskIdForm);
			}
			String dmsType = "survey";
			
			imageBean.setJenisDoc("Document Task Update");
			if(taskH.getAmMsuser() != null){
				imageBean.setUsername(taskH.getAmMsuser().getUniqueId());
			} else {
				imageBean.setUsername("admin");
			}
			imageBean.setType(dmsType);
			imageBean.setRefId("DOC_TASK_UPDATE_" + taskUpdate.getUuidTaskUpdate());
			request.setUploadImageBean(imageBean);
			ViewImageResponseBean response = this.intFormLogic.viewImageDMS(auditContext, request);
			
			detailMap4.put(keyValue, BaseEncoding.base64().encode(response.getByteImage()));
			detailMap4.put(keyImage, "1");
		} else {
			detailMap4.put(keyValue, "");
			detailMap4.put(keyImage, "0");
		}
		result.add(detailMap4);
		
		Map<String, Object> detailMap5 = new HashMap<>();
		detailMap5.put(keyHeader, "Category");
		detailMap5.put(keyValue, taskUpdate.getCategory());
		detailMap5.put(keyImage, "0");
		result.add(detailMap5);
		
		Map<String, Object> detailMap6 = new HashMap<>();
		detailMap6.put(keyHeader, "Sub Category");
		detailMap6.put(keyValue, taskUpdate.getSubCategory());
		detailMap6.put(keyImage, "0");
		result.add(detailMap6);
		
		Map<String, Object> detailMap7 = new HashMap<>();
		detailMap7.put(keyHeader, "Reason Detail");
		detailMap7.put(keyValue, taskUpdate.getReasonDetail());
		detailMap7.put(keyImage, "0");
		result.add(detailMap7);
		
		Map<String, Object> detailMap8 = new HashMap<>();
		detailMap8.put(keyHeader, "Validasi");
		detailMap8.put(keyValue, taskUpdate.getValidasi());
		detailMap8.put(keyImage, "0");
		result.add(detailMap8);
		
		return result;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	@Transactional(readOnly=true)
	public Map<String, Object> listInquiryTaskUpdateNativeString(Object[][] params, AuditContext callerId) {
		Map<String, Object> result = new HashMap<>();
		
		String applNo = (String) params[0][1];
		String startDate = (String) params[2][1];
		String endDate = (String) params[3][1];
		String start = (String) params[4][1];
		String end = (String) params[5][1];
		
		StringBuilder paramsQuery = new StringBuilder();
		if (StringUtils.isNotBlank(applNo) && !"".equalsIgnoreCase(applNo)) {
			String applNoParam = "'%" + applNo + "%'";
			paramsQuery.append(" AND (APPL_NO LIKE " + applNoParam + " OR TASK_ID LIKE " + applNoParam + ")");
		}
		if (!"%".equals(startDate) && !"%".equals(endDate)) {
			paramsQuery.append(" AND TTU.ASSIGN_DATE BETWEEN '" + startDate + "' AND '" + endDate + "'");
		}
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT * FROM ( SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum ");
		query.append(" FROM (SELECT UUID_TASK_UPDATE as taskupdateid, TASK_ID as taskid, APPL_NO as applno, CUSTOMER_NAME as customername, BRANCH_NAME as branchname, FULL_NAME as fullname, ASSIGN_DATE as assigndate, ROW_NUMBER() OVER (ORDER BY ASSIGN_DATE) AS rownum ");
		query.append(" FROM (select distinct ttu.UUID_TASK_UPDATE, tth.TASK_ID, tth.APPL_NO, tth.CUSTOMER_NAME, msb.BRANCH_NAME, amu.FULL_NAME, ttu.ASSIGN_DATE ");
		query.append(" from TR_TASKUPDATE TTU with(nolock) ");
		query.append(" join TR_TASK_H tth with(nolock) on tth.UUID_TASK_H = ttu.UUID_TASK_H ");
		query.append(" join AM_MSUSER amu with(nolock) on amu.UUID_MS_USER = tth.UUID_MS_USER ");
		query.append(" join MS_BRANCH msb with(nolock) on msb.UUID_BRANCH = tth.UUID_BRANCH ");
		query.append(" WHERE 1=1 ");
		query.append(paramsQuery.toString());
		query.append(" ) tbl ");
		query.append(" ) a WHERE a.ROWNUM <= "  + end);
		query.append(" ) b WHERE b.recnum >= " + start);
		
		Object[][] param = null;
		List resultAll = this.getManagerDAO().selectForListOfMapString(query.toString(), param, null);

		result.put(GlobalKey.MAP_RESULT_LIST, resultAll);

		return result;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	@Transactional(readOnly=true)
	public Integer countInquiryTaskUpdateNativeString(Object[][] params, AuditContext callerId) {
		Integer result = 0;
		String applNo = (String) params[0][1];
		String status = (String) params[1][1];
		String startDate = (String) params[2][1];
		String endDate = (String) params[3][1];
		
		StringBuilder paramsQuery = new StringBuilder();
		if (StringUtils.isNotBlank(applNo) && !"".equalsIgnoreCase(applNo)) {
			String applNoParam = "'%" + applNo + "%'";
			paramsQuery.append(" AND (APPL_NO LIKE " + applNoParam + " OR TASK_ID LIKE " + applNoParam + ")");
		}
		if (!"%".equals(startDate) && !"%".equals(endDate)) {
			paramsQuery.append(" AND TTU.ASSIGN_DATE BETWEEN '" + startDate + "' AND '" + endDate + "'");
		}
		
		StringBuilder query = new StringBuilder();
		query.append(" SELECT count(UUID_TASK_UPDATE) ");
		query.append(" FROM (select ttu.UUID_TASK_UPDATE ");
		query.append(" from TR_TASKUPDATE TTU with(nolock) ");
		query.append(" join TR_TASK_H tth with(nolock) on tth.UUID_TASK_H = ttu.UUID_TASK_H ");
		query.append(" WHERE 1=1 ");
		query.append(paramsQuery.toString());
		query.append(" ) tbl ");
		
		Object[][] param = null;
		result = (Integer) this.getManagerDAO().selectOneNativeString(query.toString(), param);
		return result;
	}

}
