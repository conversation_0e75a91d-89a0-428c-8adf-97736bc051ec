package com.adins.mss.businesslogic.impl.survey;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.file.Path;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Stack;
import java.util.TimeZone;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.hibernate.criterion.Restrictions;
import org.joda.time.Period;
import org.joda.time.PeriodType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.ImageStorageLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.TeleSurveyLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.constants.enums.ImageStorageLocation;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.exceptions.UploadTaskException;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsAnswertype;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsFormhistory;
import com.adins.mss.model.MsFormquestionset;
import com.adins.mss.model.MsLov;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsQuestion;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TrTaskD;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskdetaillob;
import com.adins.mss.model.TrTaskdocument;
import com.adins.mss.model.custom.LovBean;
import com.adins.mss.model.custom.TrTaskBean;
import com.adins.mss.model.taskdjson.AnswerBean;
import com.adins.mss.model.taskdjson.ImageBean;
import com.adins.mss.model.taskdjson.LocationBean;
import com.adins.mss.model.taskdjson.OptionBean;
import com.adins.mss.model.taskdjson.QuestionBean;
import com.adins.mss.model.taskdjson.TaskDocumentBean;
import com.adins.mss.services.model.common.AnswerItemBean;
import com.adins.mss.services.model.survey.TeleSurveyBean;
import com.adins.mss.services.model.survey.TeleSurveyHeadBean;
import com.adins.mss.util.MssTool;
import com.adins.mss.util.PropertiesHelper;
import com.google.common.io.BaseEncoding;
import com.google.gson.Gson;

//TODO consider using apache commons-jexl or spring-el instead of ScriptEngine for script evaluation
@SuppressWarnings({"rawtypes", "unchecked"})
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericTeleSurveyLogic extends BaseLogic implements TeleSurveyLogic, MessageSourceAware {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericTeleSurveyLogic.class);

	private static final String DELIMETER = " # ";

	private static final String[] TEMPLATE_HEADER = { "TaskId", "Appl_no",
			"Field Person", "Branch/Office", "Form", "Customer Name",
			"Customer Phone", "Customer Address" };
	private static final int[] HEADER_COLUMN_WIDTH = { 30 * 256, 20 * 256,
			20 * 256, 30 * 256, 20 * 256, 30 * 256, 20 * 256, 20 * 256 };
	private static final String[] TEMPLATE_DETAIL = { "No", "Identifier",
			"Question", "Answer", "Choices", "Lov Group", "Mandatory",
			"Max Length", "Jenis Pertanyaan", "Code Answer Type" };
	private static final int[] DETAIL_COLUMN_WIDTH = { 20 * 256, 20 * 256,
			20 * 256, 40 * 256, 30 * 256, 20 * 256, 15 * 256, 15 * 256,
			20 * 256, 20 * 256 };
	
	private IntFormLogic intFormLogic;
	
	private ImageStorageLogic imageStorageLogic;
	
	@Autowired
	private CommonLogic commonLogic;
	
	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
	
	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	public void setImageStorageLogic(ImageStorageLogic imageStorageLogic) {
        this.imageStorageLogic = imageStorageLogic;
    }

	@Override
	public TrTaskH getTaskH(long uuid, AuditContext callerId) {
		TrTaskH result = this.getManagerDAO().selectOne(
				"from TrTaskH h join fetch h.msBranch join fetch h.msForm join fetch h.amMsuser " + 
						"join fetch h.msPriority join fetch h.msStatustask ss join fetch ss.amMssubsystem where h.uuidTaskH=:uuidTaskH",
				new Object[][] {{"uuidTaskH", uuid}});
		if (result != null) {
			if (StringUtils.isEmpty(result.getApplNo())) {
				result.setApplNo("-");
			}
			if (StringUtils.isEmpty(result.getResult())) {
				result.setResult("-");
			}
			if (StringUtils.isEmpty(result.getNotes())) {
				result.setNotes("-");
			}
		}
		return result;
	}

	@Override
	public List getComboForm(long subsystem, AuditContext callerId) {
		Object[][] params = {
				{ Restrictions.eq("isActive", "1") },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", subsystem) } };

		Map<String, Object> list = this.getManagerDAO().list(MsForm.class, params, null);
		List result = (List) list.get(GlobalKey.MAP_RESULT_LIST);

		return result;
	}

	@Override
	public List getComboPriority(AuditContext callerId) {
		Object[][] params = { { Restrictions.eq("isActive", "1") } };

		Map<String, Object> list = this.getManagerDAO().list(MsPriority.class, params, null);
		List result = (List) list.get(GlobalKey.MAP_RESULT_LIST);

		return result;
	}

	@Override
	public Map listTeleSurvey(String customerName, String startDate, String endDate,
			String form, String priority, String order, String typeOrder,
			int pageNo, int pageSize, AuditContext callerId) throws ParseException {
		AmMsuser user = this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser=:uuidMsUser",
				new Object[][] {{"uuidMsUser", Long.valueOf(callerId.getCallerId())}});
		
		MsStatustask stat = this.getManagerDAO().selectOne(MsStatustask.class,
				new Object[][] {{ Restrictions.eq("amMssubsystem.uuidMsSubsystem", user.getAmMssubsystem().getUuidMsSubsystem()) },
								{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION) } });

		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder condition = new StringBuilder();
		
		condition.append(" and h.amMsuser.uuidMsUser=:uuidMsUser");
		condition.append(" and h.msStatustask.uuidStatusTask=:uuidStatusTask");
		paramMap.put("uuidMsUser", user.getUuidMsUser());
		paramMap.put("uuidStatusTask", stat.getUuidStatusTask());
		
		if (StringUtils.isNotBlank(customerName) && !"%".equals(customerName)) {
			condition.append(" and h.customerName LIKE :customerName");
			paramMap.put("customerName", "%" + customerName + "%");
		}
		if (StringUtils.isNotBlank(priority) && !"%".equals(priority)) {
			condition.append(" and h.msPriority.uuidPriority=:uuidPriority");
			paramMap.put("uuidPriority", Long.valueOf(priority));
		}
		if (!"%".equals(startDate)) {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
			condition.append(" and h.assignDate between :startDate and :endDate");
			paramMap.put("startDate", formatter.parse(startDate + " 00:00:00.000"));
			paramMap.put("endDate", formatter.parse(endDate + " 23:59:59.997"));			
		}
		if (StringUtils.isNotBlank(form) && !"%".equals(form)) {
			condition.append(" and h.msForm.uuidForm=:uuidForm");
			paramMap.put("uuidForm", Long.valueOf(form));			
		}

		StringBuilder orderQuery = new StringBuilder();
		if (StringUtils.isNotBlank(order)) {
			orderQuery.append(" order by h.").append(order).append(" ").append(typeOrder);
		}
		
		Map<String, Object> result = this.getManagerDAO().selectAll(
				"from TrTaskH h join fetch h.msForm join fetch h.msBranch join fetch h.msStatustask where 1=1"
						+ condition.toString() + orderQuery.toString(),
				"select count(*) from TrTaskH h join h.msForm join h.msStatustask where 1=1"
						+ condition.toString(),
				paramMap, pageNo, pageSize);
		
		return result;
	}
	
	@Override
	public Map<String, Object> listAnswerByBranch(AmMsuser userLoggedIn, long uuidTrTaskH, AuditContext callerId) {
		TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTrTaskH);

		MsFormhistory msFormhistory = this.commonLogic.retrieveMsFormhistory(
				trTaskH.getMsForm().getUuidForm(), trTaskH.getFormVersion(), callerId);
		long uuidFormHistory = msFormhistory.getUuidFormHistory();
		
		List<TrTaskBean> listAnswer = null;
		Map<String, List<String>> multiByGroup = null;
		Map<String, List<String>> multiByGroupDesc = null;
		
		boolean loadFromJson = PropertiesHelper.isTaskDJson();
		ImageStorageLocation isl = this.imageStorageLogic.retrieveGsImageLocation(callerId);
		if (loadFromJson) {
			TaskDocumentBean tdb = this.retrieveTaskDJsonAsBean(trTaskH, callerId);
			if (tdb != null) {
				listAnswer = this.listTaskDJsonWithQuestionSet(tdb,
						userLoggedIn.getAmMssubsystem().getUuidMsSubsystem(), isl, callerId);
				
				MultipleTaskDResult multiByGroupResult = this.processMultipleAnswerInJson(listAnswer, tdb, callerId);
				multiByGroup = multiByGroupResult.getMultiByGroup();
				multiByGroupDesc = multiByGroupResult.getMultiByGroupDesc();
			}
		}
		else {
			listAnswer = this.listTaskDWithQuestionset(trTaskH,
					userLoggedIn.getAmMssubsystem().getUuidMsSubsystem(), uuidFormHistory, callerId);
			
			MultipleTaskDResult multiByGroupResult = this.removeMultipleTaskDRow(listAnswer, callerId);
			multiByGroup = multiByGroupResult.getMultiByGroup();
			multiByGroupDesc = multiByGroupResult.getMultiByGroupDesc();
		}
		
		long uuidBranch = userLoggedIn.getMsBranch().getUuidBranch();
		Map<String, LinkedHashMap<String, String>> mapOptionByLov = this.prepareLov(listAnswer, uuidBranch, callerId);
		
		Map<String, Object> newResultAnswer = new HashMap<>();	
		newResultAnswer.put(GlobalKey.MAP_RESULT_LIST, listAnswer);
		newResultAnswer.put(GlobalKey.MAP_RESULT_SIZE, Long.valueOf(listAnswer.size()));
		newResultAnswer.put("otherChecked", multiByGroup);
		newResultAnswer.put("descChecked", multiByGroupDesc);
		newResultAnswer.put("uuidFormHistory", String.valueOf(uuidFormHistory));
		newResultAnswer.put("mapOptionByLov", mapOptionByLov);

		return newResultAnswer;
	}	
		
	@Override
	public List<Map<String, Object>> mapOptAnsByLovByBranch(AmMsuser userLoggedIn, long uuidTaskH, long uuidForm,
			long uuidQuestion, List<Map<String, String>> refIdsWithAnswer, AuditContext auditContext) {				
		//get uuid branch
		long uuidBranch = userLoggedIn.getMsBranch().getUuidBranch();
		//end get uuid branch
					
		TrTaskH taskH = this.getManagerDAO().selectOne(TrTaskH.class, uuidTaskH);
		
		MsFormhistory formHist = this.commonLogic.retrieveMsFormhistory(uuidForm, taskH.getFormVersion(), auditContext);
		Object [][] prsQset = {{Restrictions.eq("msFormhistory.uuidFormHistory", formHist.getUuidFormHistory())},
				{Restrictions.eq("msQuestion.uuidQuestion", uuidQuestion)}};
		MsFormquestionset qset = this.getManagerDAO().selectOne(MsFormquestionset.class, prsQset);
		
		MsAnswertype ansType = this.getManagerDAO().selectOne(MsAnswertype.class, qset.getMsAnswertype().getUuidAnswerType());
		Object[][] paramsRel = {
				{ Restrictions.or(
						Restrictions.like("choiceFilter", "%{" + qset.getRefId() + "}%"),
						Restrictions.like("relevant", "%{" + qset.getRefId() + "}%")) },
				{Restrictions.eq("msFormhistory.uuidFormHistory", formHist.getUuidFormHistory())}};
		Map<String, Object> msQuestionRel = this.getManagerDAO().list(MsFormquestionset.class, paramsRel, null);
		
		List<MsFormquestionset> listQuestionRel = (List<MsFormquestionset>) msQuestionRel.get(GlobalKey.MAP_RESULT_LIST);
		
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		for (MsFormquestionset relevant : listQuestionRel) {			
			Object[][] paramsLov = {
					{ "lovGroup", relevant.getLovGroup() },
					{ "uuidBranch", uuidBranch },
					{ "isActive", "1" } };
									
			List<LovBean> listLov = this.getManagerDAO().selectForList(LovBean.class, "survey.tele.getLovByBranch", paramsLov, null);
			if (listLov != null && !listLov.isEmpty()) {
				for (Iterator<LovBean> iterator = listLov.iterator(); iterator.hasNext();) {
					LovBean msLov = iterator.next();
					msLov.setUuidLov(msLov.getBigIntUuidLov().longValue());
				}
			}
			
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("msFormQuestionSet", relevant);
			map.put("msLov", listLov);
			map.put("ansType", ansType.getCodeAnswerType());

			if (StringUtils.isNotBlank(relevant.getRelevant())) { //FIXME 2018-01-12 Tidakdigunakan, sudah dipanggil relevant
				boolean res = false;
				ScriptEngineManager mgr = new ScriptEngineManager();
				ScriptEngine engine = mgr.getEngineByName("JavaScript");
				String script = this.convert(refIdsWithAnswer, relevant.getRelevant(), auditContext);
				try {
					res = (boolean) engine.eval(script);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on relevant evaluation", e);
				}
				map.put("isRelevant", res);
			}
			else {
				map.put("isRelevant", "0");
			}

			if (StringUtils.isNotBlank(relevant.getChoiceFilter())) {
				map.put("isCF", "1");
			}
			else {
				map.put("isCF", "0");
			}

			result.add(map);
		}
		
		return result;
	}
	
	private String convert(List<Map<String, String>> list, String script, AuditContext callerId) {
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		
		String convertedExpression = script;
		boolean needReplacing = true;
		
		while (needReplacing) { //replace identifier with answered value in relevant-script
			int idxOfOpenBrace = convertedExpression.indexOf('{');
			if (idxOfOpenBrace == -1) {
				break;
			}
				
			// there's {, prepare to replace what inside the {}
			int idxOfCloseBrace = convertedExpression.indexOf('}');
			String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
			String flatAnswer = null;
			for (int j=0; j<list.size(); j++){
				Map<String, String> map = list.get(j);
				if (map.get("refid").equals(identifier)){
					flatAnswer = map.get("value").toString();
				}
			}
			if (StringUtils.isEmpty(flatAnswer)) {
				flatAnswer = "-";
			}
			
			convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);			
		}
				
		String[] arg = convertedExpression.toUpperCase().split("(&&)|(AND)|(OR)|(\\|\\|)");
		for	(int i=0; i<arg.length; i++) {
			boolean result = false;
			if (arg[i].contains("<=") || arg[i].contains("<") || arg[i].contains(">=") 
					|| arg[i].contains(">") || arg[i].contains("!=") || arg[i].contains("==")){
				arg[i] = arg[i].replace("(", "");
				arg[i] = arg[i].replace(")", "");
				arg[i] = arg[i].replace(" ", "");
				String[] arg2 = arg[i].split("<=|<|>=|>|!=|==");
				
				String delimeter = null;
				if (arg[i].contains("<=")) {
					delimeter = "<=";
				}
				else if (arg[i].contains("<")) {
					delimeter = "<";
				}
				else if (arg[i].contains(">=")) {
					delimeter = ">=";
				}
				else if (arg[i].contains(">")) {
					delimeter = ">";
				}
				else if (arg[i].contains("!=")) {
					delimeter = "!=";
				}
				else if (arg[i].contains("==")) {
					delimeter = "==";
				}
				
				String cek = null;
				if (delimeter.equals("==")) {
					cek = (String) (arg2[0].equalsIgnoreCase(arg2[1]) ? "true" : "false");
				}
				else if (delimeter.equals("!=")) {
					cek = (String) (!arg2[0].equalsIgnoreCase(arg2[1]) ? "true" : "false");
				}
				else {
					cek = arg[i];
				}
				
				try {
					result = (boolean) engine.eval(cek);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on relevant evaluation", e);
				}
			}
			convertedExpression = convertedExpression.toUpperCase().replace(" ", "");
			convertedExpression = convertedExpression.toUpperCase().replace(arg[i], Boolean.toString(result));
		}
		convertedExpression = convertedExpression.toUpperCase().replace("OR", "||");
		convertedExpression = convertedExpression.toUpperCase().replace("AND", "&&");
		convertedExpression = convertedExpression.toLowerCase();
		
		return convertedExpression;
	}

	public LinkedHashMap<String, String> mapOptAns(String[][] paramsLov, AuditContext callerId) {
		List<?> optionAnswersList = null;

		if (StringUtils.isEmpty(paramsLov[1][1])) {
			paramsLov[1][0] = null;
			optionAnswersList = this.getManagerDAO().selectAllNative(
					"survey.tele.listOptionAnswerNoConstraint", paramsLov, null);
		}
		else {
			optionAnswersList = this.getManagerDAO().selectAllNative(
					"survey.tele.listOptionAnswerWithConstraint", paramsLov, null);
		}

		LinkedHashMap<String, String> optionAnswer = new LinkedHashMap<String, String>();
		if (!optionAnswersList.isEmpty()) {
			for (int x = 0; x < optionAnswersList.size(); x++) {
				Map<?, ?> map = (Map<?, ?>) optionAnswersList.get(x);
				optionAnswer.put(map.get("d0").toString(), map.get("d1").toString());
			}
		}

		return optionAnswer;
	}
	

	//FIX 2018-01-09 known issues: lov yg ada constraint, jika tidak ada choice filter, tidak muncul pada initial load halaman detail
	//misal: brand yg choice filternya jenis asset, jika tidak ada jenis asset di questionset, lov branch kosong.
	//FIX dengan menggunakan dynamic where pada constraint
	public LinkedHashMap<String, String> mapOptAnsByBranch(long uuidBranch, TrTaskD taskAnswer, AuditContext callerId) {
		
		StringBuilder sql1 = new StringBuilder("SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE ")
				.append("FROM MS_LOV t1 with (nolock) ")
				.append("left join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV ")
				.append("WHERE LOV_GROUP = :lovGroup ")
				.append("and t1.IS_DELETED = :isDeleted ")
				.append("and t1.IS_ACTIVE = :isActive ")
				.append("and mbl.UUID_LOV is null");
		
		StringBuilder sql2 = new StringBuilder(" UNION ")
				.append("SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE ")
				.append("FROM MS_LOV t1 with (nolock) ")
				.append("join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV ")
				.append("WHERE LOV_GROUP = :lovGroup ")
				.append("and t1.IS_DELETED = :isDeleted ")
				.append("and t1.IS_ACTIVE = :isActive ")
				.append("and mbl.UUID_BRANCH = :uuidBranch");

		Map<String, Object> params = new HashMap<>();
		params.put("lovGroup", taskAnswer.getMsQuestion().getLovGroup());
		params.put("uuidBranch", uuidBranch);
		params.put("isDeleted", "0");
		params.put("isActive", "1");
		
		if (taskAnswer.getMsLovByLovId() != null) {
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint1())) {
				sql1.append(" and (t1.CONSTRAINT_1 IN (:constraint1, '%')");
				sql2.append(" and (t1.CONSTRAINT_1 IN (:constraint1, '%')");
				params.put("constraint1", taskAnswer.getMsLovByLovId().getConstraint1());
			}		
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint2())) {
				sql1.append(" and (t1.CONSTRAINT_2 IN (:constraint2, '%')");
				sql2.append(" and (t1.CONSTRAINT_2 IN (:constraint2, '%')");
				params.put("constraint2", taskAnswer.getMsLovByLovId().getConstraint2());
			}		
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint3())) {
				sql1.append(" and (t1.CONSTRAINT_3 IN (:constraint3, '%')");
				sql2.append(" and (t1.CONSTRAINT_3 IN (:constraint3, '%')");
				params.put("constraint3", taskAnswer.getMsLovByLovId().getConstraint3());
			}		
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint4())) {
				sql1.append(" and (t1.CONSTRAINT_4 IN (:constraint4, '%')");
				sql2.append(" and (t1.CONSTRAINT_4 IN (:constraint4, '%')");
				params.put("constraint4", taskAnswer.getMsLovByLovId().getConstraint4());
			}		
			if (StringUtils.isNotEmpty(taskAnswer.getMsLovByLovId().getConstraint5())) {
				sql1.append(" and (t1.CONSTRAINT_5 IN (:constraint5, '%')");
				sql2.append(" and (t1.CONSTRAINT_5 IN (:constraint5, '%')");
				params.put("constraint5", taskAnswer.getMsLovByLovId().getConstraint5());
			}
		}
		
		sql2.append(" ORDER BY t1.SEQUENCE");
		sql1.append(sql2.toString());

		List<?> optionAnswersList = this.getManagerDAO().selectAllNativeString(sql1.toString(), params);
		LinkedHashMap<String, String> optionAnswer = new LinkedHashMap<String, String>();
		if (!optionAnswersList.isEmpty()) {
			for (int x = 0; x < optionAnswersList.size(); x++) {
				Map<?, ?> map = (Map<?, ?>) optionAnswersList.get(x);
				optionAnswer.put(map.get("d0").toString(), map.get("d1").toString());
			}
		}

		return optionAnswer;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public void updateTaskHStatus(String[] uuidAnswers, String[] textAnswers,
			String[] cons, String[] latAns, String[] longAns, long uuidTaskH,
			String[] uuidQuestions, AmMsuser loginBean, AuditContext callerId) {
		TrTaskH taskH = (TrTaskH) this.getTaskH(uuidTaskH, callerId);
			
		MsStatustask sts = taskH.getMsStatustask();
		if (!GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION.equals(sts.getStatusCode())) {
			throw new ChangeException(
					this.messageSource.getMessage("businesslogic.error.changeexception", 
							null, this.retrieveLocaleAudit(callerId))
					, sts.getStatusCode());
		}

		long uuidProcess = getUuidProcess(taskH, loginBean.getAmMssubsystem());

		MsStatustask msStatustask = commitWf(taskH, uuidProcess, loginBean.getAmMssubsystem(), 0); //to uploading
		msStatustask = commitWf(taskH, uuidProcess, loginBean.getAmMssubsystem(), 0); //to submitted

		taskH.setMsStatustask(msStatustask);
		taskH.setUsrUpd(callerId.getCallerId());
		taskH.setDtmUpd(new Date());
		taskH.setSubmitDate(new Date());

		Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_SUBMITTED) } };
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
		taskH.setMsStatusmobile(msm);
		
		this.getManagerDAO().update(taskH);
	
		boolean saveAsJson = PropertiesHelper.isTaskDJson();
		if (saveAsJson) {
			this.saveAnswersJson(taskH, uuidQuestions, textAnswers, latAns, longAns, callerId);
		}
		else {
			this.saveAnswers(uuidAnswers, textAnswers, latAns, longAns, uuidQuestions, taskH, callerId);
		}		

		String notes = "Task with ID: " + taskH.getTaskId() + " has been submitted via TeleSurvey";
		String codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;

		insertTaskHistory(callerId, msStatustask, taskH, notes, codeProcess, taskH.getAmMsuser().getFullName());
		if (GlobalVal.SUBSYSTEM_MO.equals(taskH.getFlagSource())) {
			commitOrder(loginBean, taskH.getNotes(), taskH, taskH
					.getAmMsuser().getAmMssubsystem(), 0,
					codeProcess, callerId);
		}

		String taskID = taskH.getTaskId();
		String flagSource = taskH.getFlagSource();
		String subsystemCode = loginBean.getAmMssubsystem().getSubsystemName();

		intFormLogic.saveResult(callerId, taskID, flagSource, subsystemCode, callerId.getCallerId(), "1");
	}

	private TrTaskD getDetail(long uuidTaskD, AuditContext callerId) {
		TrTaskD resultDetail = this.getManagerDAO().selectOne(
				"from TrTaskD d join fetch d.msQuestion q join fetch q.msAnswertype where d.uuidTaskD = :uuidTaskD",
				new Object[][]{{"uuidTaskD", uuidTaskD}});	
		return resultDetail;
	}
	
	private void saveXlsImage(TrTaskH taskH, MsQuestion msQuestion, String refId, String questionLabel, String cellAnswer,
			ImageStorageLocation saveImgLoc, Path imgLoc, Date sysdate, String errorMsg, AuditContext auditContext) {
		Object[][] pr = {
				{Restrictions.eq("trTaskH.uuidTaskH", taskH.getUuidTaskH())},
				{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())} };
		TrTaskdetaillob tskl = this.getManagerDAO().selectOne(TrTaskdetaillob.class, pr);

		boolean existing = false;
		if (tskl != null) {
			existing = true;
			tskl.setUsrUpd(String.valueOf(auditContext.getCallerId()));
			tskl.setDtmUpd(new Date());					
		}
		else {
			tskl = new TrTaskdetaillob();
			tskl.setUsrCrt(String.valueOf(auditContext.getCallerId()));
			tskl.setDtmCrt(new Date());
			tskl.setTrTaskH(taskH);					
			tskl.setMsQuestion(msQuestion);
			tskl.setQuestionText(questionLabel);				
		}				

		if (StringUtils.isNotBlank(cellAnswer)) {
			try {
				if (saveImgLoc == ImageStorageLocation.DATABASE) {
					tskl.setLobFile(BaseEncoding.base64().decode(cellAnswer));
					tskl.setImagePath(null);
				}
				else if (saveImgLoc == ImageStorageLocation.FILE_SYSTEM) {
					DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");							
					Path imgPath = imgLoc.resolve(dateFormat.format(sysdate));
					
					String fileName = taskH.getTaskId() + "_" + refId + "_" + this.getManagerDAO().getUUID() + ".jpg";
					
					String absoluteFilename = imageStorageLogic.storeImageFileSystem(
							BaseEncoding.base64().decode(cellAnswer), imgPath, fileName);
					
					tskl.setImagePath(absoluteFilename);
					tskl.setLobFile(null);
				}
			}
			catch (IllegalArgumentException e) {
				//blabla
				errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.notvalidanswer", 
						null, this.retrieveLocaleAudit(auditContext));
				throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}
		}
		
		if (existing) {
			this.getManagerDAO().update(tskl);
		}
		else {
			this.getManagerDAO().insert(tskl);
		}		
	}
	
	private TrTaskdetaillob saveImageOnly(TrTaskH taskH, MsQuestion question, String textAnswer,
			ImageStorageLocation saveImgLoc, Date date, Path imgLoc, AuditContext auditContext) {				
		//textAnswer for Image => [0]Lat:^:[1]Lng:^:[2]Accuracy:^:[3]data:$mimeType;base64,$base64value
		String[] tmp = textAnswer.split(":\\^:");
		String base64Image = tmp[0].substring(tmp[0].indexOf(",") + 1);
		if (tmp.length > 1) {
			base64Image = tmp[3].substring(tmp[3].indexOf(",") + 1);
		}
		Object[][] paramsLobExist = {
				{Restrictions.eq("trTaskH.uuidTaskH", taskH.getUuidTaskH())},
				{Restrictions.eq("msQuestion.uuidQuestion", question.getUuidQuestion())} };
		TrTaskdetaillob checkExist = this.getManagerDAO().selectOne(TrTaskdetaillob.class, paramsLobExist);
		if (checkExist != null) {
			return checkExist;
		}
		
		TrTaskdetaillob lob = new TrTaskdetaillob();
		lob.setDtmCrt(new Date());
		lob.setUsrCrt(auditContext.getCallerId());
		if (ImageStorageLocation.DATABASE == saveImgLoc) {
			lob.setLobFile("^".equals(base64Image) ? null : BaseEncoding.base64().decode(base64Image));
		}
		else if (ImageStorageLocation.FILE_SYSTEM == saveImgLoc){
			if (!"^".equals(base64Image)) {
				DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");							
				Path imgPath = imgLoc.resolve(dateFormat.format(date));
				
				String fileName = taskH.getTaskId() + "_" + question.getRefId() + "_"
						+ this.getManagerDAO().getUUID() + ".jpg";
				
				String absoluteFilename = imageStorageLogic.storeImageFileSystem(
						BaseEncoding.base64().decode(base64Image), imgPath, fileName);
				
				lob.setImagePath(absoluteFilename);
			}
		}
		lob.setMsQuestion(question);
		lob.setTrTaskH(taskH);
		lob.setQuestionText(question.getQuestionLabel());
		if (!GlobalVal.ANSWER_TYPE_IMAGE.equals(question.getMsAnswertype().getCodeAnswerType()) && tmp.length > 1) {
			lob.setLatitude(new BigDecimal(tmp[0].toString()));
			lob.setLongitude(new BigDecimal(tmp[1].toString()));
			lob.setAccuracy(Integer.parseInt(tmp[2].toString()));
			lob.setIsGps("1");
		}
		this.getManagerDAO().insert(lob);		
		
		return lob;		
	}
	
	private void saveAnswersJson(TrTaskH trTaskH, String[] uuidQuestions, String[] textAnswers,
			String[] latAns, String[] longAns, AuditContext auditContext) {
		Gson gson = new Gson();
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		TaskDocumentBean tdb = null;
		List<AnswerBean> answers = null;
		boolean isUpdate = false;
		
		if (docDb == null) {			
			tdb = new TaskDocumentBean();
			answers = new ArrayList<>(ArrayUtils.getLength(uuidQuestions));
			tdb.setAnswers(answers);
			
			docDb = new TrTaskdocument(trTaskH, new Date(), auditContext.getCallerId());			
		}
		else {
			isUpdate = true;
			tdb = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
			answers = tdb.getAnswers();
			docDb.setDtmUpd(new Date());
			docDb.setUsrUpd(auditContext.getCallerId());
		}
		
		MsFormhistory msFormhistory = this.commonLogic.retrieveMsFormhistory(
				trTaskH.getMsForm().getUuidForm(), trTaskH.getFormVersion(), auditContext);
		long uuidFormHistory = msFormhistory.getUuidFormHistory();
		
		ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(auditContext);
		Path imgLoc = (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) ?
		        imageStorageLogic.retrieveGsImageFileSystemPath(auditContext) : null;
		        
		for (int i = 0; i < uuidQuestions.length; i++) {
			if ("~".equals(uuidQuestions[i])){
				continue;
			}
			
			long uuidQuestion = Long.parseLong(uuidQuestions[i]);
			
			MsQuestion quest = this.commonLogic.retrieveQuestionByuuidQuestionQset(
					uuidQuestion, trTaskH.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem(), uuidFormHistory, auditContext);
						
			int countLatLong = 0;
			String latitude = null, longitude = null;
			if (GlobalVal.ANSWER_TYPE_LOCATION.equals(quest.getMsAnswertype().getCodeAnswerType()) &&
					latAns.length > 0) {
				latitude = latAns[countLatLong];
				longitude = longAns[countLatLong];
				countLatLong++;
			}
			
			int idxAnswer = tdb.findAnswerIndex(quest.getUuidQuestion());

			AnswerBean answer = this.transformToJsonByAnswerType(trTaskH, uuidFormHistory, quest, textAnswers[i],
					latitude, longitude, tdb, idxAnswer, textAnswers, saveImgLoc, imgLoc, auditContext);
			
			if (idxAnswer == -1) {
				answers.add(answer);
			}
		}
		
		if (isUpdate) {
			docDb.setDocument(gson.toJson(tdb, TaskDocumentBean.class));
			this.getManagerDAO().update(docDb);
		}
		else {
			docDb.setDocument(gson.toJson(tdb, TaskDocumentBean.class));
			this.getManagerDAO().insert(docDb);
		}
	}

	private void saveAnswers(String[] uuidAnswers, String[] textAnswers,
			String[] latAns, String[] longAns,
			String[] uuidQuestions, TrTaskH taskH, AuditContext auditContext) {
		ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(auditContext);
		Path imgLoc = (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) ?
		        imageStorageLogic.retrieveGsImageFileSystemPath(auditContext) : null;
		
		List<TrTaskD> taskDs = new ArrayList<>(uuidAnswers.length);
		for (String uuidTaskD : uuidAnswers) {
			if ("0".equals(uuidTaskD) || !"~".equals(uuidTaskD)){
				TrTaskD taskD = this.getDetail(Long.valueOf(uuidTaskD), auditContext);
				taskDs.add(taskD);				
			}
			else {
				TrTaskD taskD = null;
				taskDs.add(taskD);
			}
		}
		
		MsFormhistory msFormhistory = this.commonLogic.retrieveMsFormhistory(
				taskH.getMsForm().getUuidForm(), taskH.getFormVersion(), auditContext);
		long uuidFormHistory = msFormhistory.getUuidFormHistory();
		Date date = new Date();
		int countLatLong = 0;
		for (int i = 0; i < uuidAnswers.length - 1; i++) {
			TrTaskD answerDetail = taskDs.get(i);

			MsQuestion quest = this.commonLogic.retrieveQuestionByuuidQuestionQset(Long.parseLong(uuidQuestions[i]),
					taskH.getMsStatustask().getAmMssubsystem().getUuidMsSubsystem(), uuidFormHistory, auditContext);
			if (answerDetail == null) {
				if (!MssTool.isImageQuestion(quest.getMsAnswertype().getCodeAnswerType())) {
					answerDetail = new TrTaskD();
				}
				else {
					this.saveImageOnly(taskH, quest, textAnswers[i], saveImgLoc, date, imgLoc, auditContext);
					continue;
				}
			}			
			
			//from here on, all are non image
			if ("^".equalsIgnoreCase(textAnswers[i])) {
				continue;
			}			
						
			String latitude = null, longitude = null;
			if (GlobalVal.ANSWER_TYPE_LOCATION.equals(quest.getMsAnswertype().getCodeAnswerType()) &&
					latAns.length > 0) {
				latitude = latAns[countLatLong];
				longitude = longAns[countLatLong];
				countLatLong++;
			}
			boolean flagUpdate = this.transformDataByAnswerType(
					taskH, uuidFormHistory, answerDetail, quest, uuidAnswers[i], textAnswers[i],
					latitude, longitude, taskDs, textAnswers, auditContext);

			if (flagUpdate) {
				if (answerDetail.getUuidTaskD() == 0) {
					this.insertNewDetail(answerDetail, auditContext);
				}
				else {
					this.updateTaskD(answerDetail, auditContext);
				}
			}
		}
	}

	public MsLov getLovNoCons(String[][] lovParams, AuditContext auditContext) {
		BigInteger lovId = (BigInteger) this.getManagerDAO().selectOneNative("survey.tele.getLovNoCons", lovParams);
		if (lovId == null)
			return null;
		MsLov resultLov = this.getManagerDAO().selectOne(MsLov.class, lovId.longValue());
		return resultLov;
	}

	private TrTaskD getDetailByLov(Object[][] params, AuditContext callerId) {
		BigInteger uuidTaskD = (BigInteger) this.getManagerDAO().selectOneNative("survey.tele.getDetailByLov", params);
		if (uuidTaskD == null)
			return null;
		
		TrTaskD result = this.getManagerDAO().selectOne(TrTaskD.class, uuidTaskD.longValue());
		return result;
	}

	private void updateTaskD(TrTaskD taskD, AuditContext callerId) {
		taskD.setUsrUpd(callerId.getCallerId());
		taskD.setDtmUpd(new Date());

		this.getManagerDAO().update(taskD);
	}

	private void insertNewDetail(TrTaskD newTaskD, AuditContext callerId) {
		newTaskD.setDtmCrt(new Date());
		newTaskD.setUsrCrt(callerId.getCallerId());

		this.getManagerDAO().insert(newTaskD);
	}

	@Override
	public byte[] exportExcel(AmMsuser userLoggedIn, long uuidTaskH, AuditContext callerId) { 
		HSSFWorkbook workbook = this.createXlsTemplate(userLoggedIn, uuidTaskH, callerId);
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		try {
			workbook.write(stream);
		} 
		catch (IOException e) {
			LOG.error(e.getMessage(), e);
		}
		return stream.toByteArray();
	}

	private HSSFWorkbook createXlsTemplate(AmMsuser userLoggedIn, long uuidTaskH, AuditContext callerId) {
		HSSFWorkbook workbook = new HSSFWorkbook();
		try {
			HSSFSheet sheet = workbook.createSheet("Task Detail");
			List resultHeader = this.getExportHeader(uuidTaskH, callerId);
			this.createHeader(workbook, sheet, resultHeader, callerId);
			List resultAnswer = this.getExportAnswer(uuidTaskH, callerId);
			this.createDetail(workbook, sheet, resultAnswer, userLoggedIn.getMsBranch().getUuidBranch(), callerId);
			this.setTextDataFormat(workbook, sheet, callerId);
		} 
		catch (Exception ex) {
			LOG.error(ex.getMessage(), ex);
		}
		return workbook;
	}

	private void createHeader(HSSFWorkbook workbook, HSSFSheet sheet, List resultHeader, AuditContext callerId) {
		Map<String, CellStyle> styles = createStyles(workbook, callerId);
		HSSFCellStyle styleCell = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		styleCell.setFont(font);

		Map temp = (Map) resultHeader.get(0);

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			HSSFRow row = sheet.createRow(i);
			HSSFCell cell = row.createCell(0);
			cell.setCellValue(TEMPLATE_HEADER[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(0, HEADER_COLUMN_WIDTH[i]);
			HSSFCell cellData = row.createCell(1);
			cellData.setCellValue(temp.get("d" + (i)).toString());
			cellData.setCellStyle(styleCell);
		}
	}

	private void createDetail(HSSFWorkbook workbook, HSSFSheet sheet, List resultAnswer, long uuidBranch, AuditContext auditContext) {
		Map<String, CellStyle> styles = createStyles(workbook, auditContext);
		HSSFCellStyle styleCell = workbook.createCellStyle();
		HSSFFont font = workbook.createFont();
		font.setBoldweight((short) 1000);
		styleCell.setFont(font);

		int rowcell = 10;
		HSSFRow rowHeader = sheet.createRow(rowcell++);

		for (int i = 0; i < TEMPLATE_DETAIL.length; i++) {
			HSSFCell cell = rowHeader.createCell(i);
			cell.setCellValue(TEMPLATE_DETAIL[i]);
			cell.setCellStyle(styles.get("header"));
			sheet.setColumnWidth(i, DETAIL_COLUMN_WIDTH[i]);
		}

		ImageStorageLocation isl = imageStorageLogic.retrieveGsImageLocation(auditContext);
		
		int maxCellLength = SpreadsheetVersion.EXCEL97.getMaxTextLength();
		// Data Row
		for (int i = 0; i < resultAnswer.size(); i++) {
			Map temp = (Map) resultAnswer.get(i);
			boolean isImage = MssTool.isImageQuestion(temp.get("d8").toString());
			
			HSSFRow rowData = sheet.createRow(rowcell++);
			// Number cell
			HSSFCell cellNo = rowData.createCell(0);
			cellNo.setCellValue(i + 1);
			cellNo.setCellStyle(styleCell);
			// data cell
			for (int j = 0; j < TEMPLATE_DETAIL.length; j++) {
				HSSFCell cell = rowData.createCell(j + 1);
				String mapIdx = "d" + j;
				String answer = (null != temp.get(mapIdx)) ? temp.get(mapIdx).toString() : null;
				
				if (isImage && StringUtils.isNumeric(answer) && j == 2) {
					long uuidLob = Long.parseLong(answer);
					byte[] image = null;
					if (isl == ImageStorageLocation.DATABASE) {
						image = imageStorageLogic.retrieveImageBlob(uuidLob, false);
					}
					else if (isl == ImageStorageLocation.FILE_SYSTEM) {
						image = imageStorageLogic.retrieveImageFileSystemByTaskD(uuidLob, false);
					}
					
					if (image != null) {
						String base64 = BaseEncoding.base64().encode(image);
						//The maximum length of cell contents (text) is 32,767 characters
						if (base64.length() <= maxCellLength) {
							answer = BaseEncoding.base64().encode(image);
						}
					}
					else {
						answer = null;
					}
				}
				
				if ("d3".equalsIgnoreCase(mapIdx)) { //d3=HasLov			
					if ("1".equals(answer)) { // check has LOV GROUP
						String delimitedLov = this.getListLov(temp.get("d" + (j + 1)).toString(), uuidBranch, auditContext);
						if (StringUtils.length(delimitedLov) > maxCellLength) {
							delimitedLov = StringUtils.substring(delimitedLov, 0, maxCellLength - 25) + "...(string truncated)";
						}
						cell.setCellValue(delimitedLov);
					}
					else {
						cell.setCellValue(StringUtils.EMPTY);
					}
				}
				else {
					cell.setCellValue(answer);
				}
				cell.setCellStyle(styleCell);
			}
		}
	}

	private String getListLov(String lovGroup, long uuidBranch, AuditContext callerId) {
		String listLov = StringUtils.EMPTY;
		if (StringUtils.isBlank(lovGroup))
			return listLov;

		Object[][] paramsLov = {
				{ "lovGroup", lovGroup },
				{ "uuidBranch", uuidBranch },
				{ "isActive", "1" } };
								
		List<LovBean> listResult = this.getManagerDAO().selectForList(LovBean.class, "survey.tele.getLovByBranch", paramsLov, null);
				
		if (!listResult.isEmpty()) {			
			String[] lovResult = new String[listResult.size()];
			for (int i = 0; i < listResult.size(); i++) {
				MsLov msLov = listResult.get(i);
				lovResult[i] = msLov.getDescription();
			}
			listLov = StringUtils.join(lovResult, DELIMETER);
		}
		return listLov;
	}

	private void setTextDataFormat(HSSFWorkbook workbook, HSSFSheet sheet, AuditContext callerId) {
		DataFormat format = workbook.createDataFormat();
		HSSFCellStyle style = workbook.createCellStyle();
		style.setDataFormat(format.getFormat("General"));

		for (int i = 0; i < TEMPLATE_HEADER.length; i++) {
			sheet.setDefaultColumnStyle(i, style);
		}
	}

	private List getExportHeader(long uuidTaskH, AuditContext callerId) {
		Object[][] param = { { "uuidTaskH", uuidTaskH } };
		List result = this.getManagerDAO().selectAllNative("survey.tele.getExportHeader", param, null);
		return result;
	}

	private List getExportAnswer(long uuidTaskH, AuditContext callerId) {
		Object[][] param = { { "uuidTaskH", uuidTaskH } };
		List result = this.getManagerDAO().selectAllNative("survey.tele.getExportAnswer", param, null);		
		return result;
	}

	private static Map<String, CellStyle> createStyles(Workbook wb, AuditContext callerId) {
		Map<String, CellStyle> styles = new HashMap<String, CellStyle>();
		CellStyle style;

		style = wb.createCellStyle();
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		styles.put("cell", style);

		style = wb.createCellStyle();
		Font font = wb.createFont();
		font.setBold(true);
		style.setFont(font);
		style.setAlignment(CellStyle.ALIGN_CENTER);
		style.setWrapText(true);
		style.setBorderRight(CellStyle.BORDER_THIN);
		style.setRightBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderLeft(CellStyle.BORDER_THIN);
		style.setLeftBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderTop(CellStyle.BORDER_THIN);
		style.setTopBorderColor(IndexedColors.BLACK.getIndex());
		style.setBorderBottom(CellStyle.BORDER_THIN);
		style.setBottomBorderColor(IndexedColors.BLACK.getIndex());
		style.setFillForegroundColor(IndexedColors.GREY_50_PERCENT.getIndex());
		style.setFillPattern(CellStyle.SOLID_FOREGROUND);
		styles.put("header", style);
		return styles;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public byte[] processSpreadSheetFile(File uploadedFile, AmMsuser loginBean, AuditContext callerId, long uuidTaskH) {
		Map<String, Object> parseMap = null;
		try {
			parseMap = this.parseSpreadsheetToTaskBeans(uploadedFile, callerId);
		} 
		catch (Exception e) {
			byte[] errorUploadByte = this.errorUpload(callerId);
			return errorUploadByte;
		}

		TeleSurveyHeadBean tskh = (TeleSurveyHeadBean) parseMap.get("head");
		List listOfAnswer = (List) parseMap.get("result");

		TrTaskH taskH = this.getTaskHExist(tskh, callerId);
		// check existing identical data
		if (taskH == null || !taskH.getTaskId().equalsIgnoreCase(tskh.getTaskId())) {
			throw new UploadTaskException(this.messageSource.getMessage("businesslogic.telesurvey.taskidnotmatch", 
					null, this.retrieveLocaleAudit(callerId)), UploadTaskException.Reason.TEMPLATE_MISMATCH);
		}

		MsStatustask sts = taskH.getMsStatustask();
		if (!GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION.equals(sts.getStatusCode())) {
			throw new ChangeException(
					this.messageSource.getMessage("businesslogic.error.changeexception", 
							null, this.retrieveLocaleAudit(callerId)),
					sts.getStatusCode());
		}
		
		Object[][] paramMsm = { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_SUBMITTED) } };
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, paramMsm);
		
		long uuidProcess = getUuidProcess(taskH, loginBean.getAmMssubsystem());

		MsStatustask msStatustask = commitWf(taskH, uuidProcess, loginBean.getAmMssubsystem(), 0);
		msStatustask = commitWf(taskH, uuidProcess, loginBean.getAmMssubsystem(), 0);
		
		taskH.setMsStatustask(msStatustask);
		taskH.setUsrUpd(callerId.getCallerId());
		taskH.setDtmUpd(new Date());
		taskH.setSubmitDate(new Date());
		taskH.setMsStatusmobile(msm);

		this.getManagerDAO().update(taskH);

		MsFormhistory msFormhistory = this.commonLogic.retrieveMsFormhistory(
				taskH.getMsForm().getUuidForm(), taskH.getFormVersion(), callerId);
		long uuidFormHistory = msFormhistory.getUuidFormHistory();
		
		ImageStorageLocation saveImgLoc = imageStorageLogic.retrieveGsImageLocation(callerId);
		Path imgLoc = (ImageStorageLocation.FILE_SYSTEM == saveImgLoc) ?
		        imageStorageLogic.retrieveGsImageFileSystemPath(callerId) : null;
		Date sysdate = new Date();
		
		this.saveExcelAnswersTaskD(listOfAnswer, loginBean, taskH, uuidFormHistory, saveImgLoc, imgLoc, sysdate, callerId);

		String notes = "Task with ID: " + taskH.getTaskId() + " has been submitted";
		String codeProcess = GlobalVal.CODE_PROCESS_SUBMITTED;

		insertTaskHistory(callerId, msStatustask, taskH, notes, codeProcess, taskH.getAmMsuser().getFullName());

		String taskID = String.valueOf(taskH.getTaskId());
		String subsystemCode = loginBean.getAmMssubsystem().getSubsystemName();
		String flagSource = taskH.getFlagSource();

		intFormLogic.saveResult(callerId, taskID, flagSource, subsystemCode, callerId.getCallerId(), "1");
		
		return null;
	}
	
	private void saveExcelAnswersTaskD(List listOfAnswer, AmMsuser loginBean, TrTaskH taskH, long uuidFormHistory,
			ImageStorageLocation saveImgLoc, Path imgLoc, Date sysdate, AuditContext callerId) {
		boolean saveAsJson = PropertiesHelper.isTaskDJson();
		
		Gson gson = null;	
		TrTaskdocument docDb = null;
		TaskDocumentBean document = null;
		List<AnswerBean> answers = null;
		
		if (saveAsJson) {
			gson = new Gson();
			docDb = this.getManagerDAO().selectOne(
					"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
					new Object[][]{{"uuidTaskId", taskH.getUuidTaskH()}});
			if (docDb != null && StringUtils.isNotBlank(docDb.getDocument())) {
				document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
				answers = (document.getAnswers() == null) ? new ArrayList<AnswerBean>() : document.getAnswers();
				document.setAnswers(answers);
			}
		}
		
		for (Iterator iterate = listOfAnswer.iterator(); iterate.hasNext();) {
			TeleSurveyBean tbean = (TeleSurveyBean) iterate.next();

			String codeAnswerType = StringUtils.trimToNull(tbean.getCodeAns());
			String refId = StringUtils.trimToNull(tbean.getRefId());
			String questionLabel = StringUtils.trimToNull(tbean.getQuestion());
			String lovGroup = StringUtils.trimToNull(tbean.getLovg());
			String cellAnswer = StringUtils.trimToNull(tbean.getAns());
			String mandatory = StringUtils.trimToNull(tbean.getMand());
			String errorMsg = refId + " ( " + questionLabel + " ) ";

			// get question Id
			MsQuestion msQuestion = this.commonLogic.retrieveQuestionByRefId(refId,
					loginBean.getAmMssubsystem().getUuidMsSubsystem(), callerId);
			
			int idxAnswer = -1;
            if (saveAsJson) {
            	idxAnswer = document.findAnswerIndex(msQuestion.getUuidQuestion());
            }

			if ("1".equals(mandatory) && StringUtils.isBlank(cellAnswer)) {
				errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.ismandatory", 
						null, this.retrieveLocaleAudit(callerId));
				throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}

			if (MssTool.isImageQuestion(codeAnswerType)) {
				this.saveXlsImage(taskH, msQuestion, refId, questionLabel, cellAnswer, saveImgLoc, imgLoc, sysdate, errorMsg, callerId);
				continue;
			}
			
			this.saveSheetDataByAnswerType(taskH, uuidFormHistory, listOfAnswer, msQuestion, refId, questionLabel,
					cellAnswer, codeAnswerType, lovGroup, idxAnswer, document, answers, callerId);
		}
		
		if (PropertiesHelper.isTaskDJson()) {
			docDb.setDocument(gson.toJson(document, TaskDocumentBean.class));
			docDb.setDtmUpd(new Date());
			docDb.setUsrUpd(callerId.getCallerId());

			this.getManagerDAO().update(docDb);
		}
	}

	private Map parseSpreadsheetToTaskBeans(File uploadedFile, AuditContext callerId) throws IOException {
		TeleSurveyHeadBean taskH = new TeleSurveyHeadBean();
		List<TeleSurveyBean> result = new ArrayList<>();
		List<TeleSurveyBean> resultError = new ArrayList<>();

		FileInputStream inputStream = new FileInputStream(uploadedFile);

		HSSFWorkbook wb = new HSSFWorkbook(inputStream);
		HSSFSheet sheet = wb.getSheetAt(0);
		int rows = sheet.getPhysicalNumberOfRows() + 2;

		String value = null;
		int intValue = -1;

		for (int r = 0; r < rows; r++) {
			if (r == 0 && r < 8) {
				HSSFRow row = sheet.getRow(r);
				HSSFCell cell = row.getCell(1, Row.RETURN_BLANK_AS_NULL);

				if (cell != null) {
					switch (cell.getCellType()) {
						case HSSFCell.CELL_TYPE_NUMERIC:
							value = String.valueOf((int) cell.getNumericCellValue());
							intValue = Integer.valueOf(value).intValue();
							break;
						case HSSFCell.CELL_TYPE_STRING:
							value = cell.getStringCellValue();
							break;
						default:
					}
				}

				switch (r) {
				case 0:
					taskH.setTaskId(value);
					break;
				case 1:
					taskH.setApplNo(value);
					break;
				case 2:
					taskH.setPerson(value);					
					break;
				case 3:
					taskH.setBranch(value);					
					break;
				case 4:
					taskH.setForm(value);				
					break;
				case 5:
					taskH.setCustName(value);
					break;
				case 6:
					taskH.setCustPhone(value);
					break;
				case 7:
					taskH.setCustAddress(value);
					break;
				case 8:
					break;
				}
				wb.close();
			}
			else {
				for (r = 11; r < rows; r++) {
					HSSFRow row = sheet.getRow(r);
					if (row == null) {
						continue;
					}

					boolean isEmptyRow = checkEmptyRow(row, callerId);
					if (isEmptyRow == true) {
						continue;
					}

					TeleSurveyBean dbean = new TeleSurveyBean();

					for (int c = 0; c < 10; c++) {
						HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
						value = "";
						intValue = -1;

						// if intValue -1, then sequence is posted with string
						if (cell != null) {
							switch (cell.getCellType()) {
								case HSSFCell.CELL_TYPE_NUMERIC:
									value = String.valueOf((int) cell
											.getNumericCellValue());
									intValue = Integer.valueOf(value).intValue();
									break;
								case HSSFCell.CELL_TYPE_STRING:
									value = cell.getStringCellValue();
									break;
								default:
							}
						}

						switch (c) {
							case 1:
								dbean.setRefId(value);
								break;
							case 2:
								dbean.setQuestion(value);
								break;
							case 3:
								dbean.setAns(value);
								break;
							case 4:
								dbean.setChoices(value);
								break;
							case 5:
								dbean.setLovg(value);
								break;
							case 6:
								dbean.setMand(value);
								break;
							case 7:
								dbean.setMaxl(intValue);
								break;
							case 8:
								dbean.setType(value);
								break;
							case 9:
								dbean.setCodeAns(value);
								break;
						}
						wb.close();
					}

					result.add(dbean);
				}
			}
		}

		Map<String, Object> paramParse = new HashMap<>();
		paramParse.put("head", taskH);
		paramParse.put("result", result);
		paramParse.put("resultError", resultError);
		return paramParse;
	}

	private TrTaskH getTaskHExist(TeleSurveyHeadBean tskh, AuditContext callerId) {
		String taskId = StringUtils.trimToNull(tskh.getTaskId());
		Object[][] queryParams = { {"taskId", taskId} };
		TrTaskH result = this.getManagerDAO().selectOne(
				"from TrTaskH h join fetch h.msForm join fetch h.amMsuser join fetch h.msStatustask where h.taskId = :taskId", queryParams);
		return result;
	}

	// error condition untuk di action
	private byte[] errorUpload(AuditContext callerId) {
		byte[] tmp = new byte[1];
		tmp[0] = 1;// for condition in action
		return tmp;
	}

	private boolean checkEmptyRow(HSSFRow row, AuditContext callerId) {
		String[] isEmptyCell = new String[10];
		Arrays.fill(isEmptyCell, "");

		for (int c = 0; c < 10; c++) {
			HSSFCell cell = row.getCell(c, Row.RETURN_BLANK_AS_NULL);
			if (cell == null) {
				isEmptyCell[c] = "empty";
			}
		}

		if ("empty".equals(isEmptyCell[0]) && "empty".equals(isEmptyCell[1])
				&& "empty".equals(isEmptyCell[2])
				&& "empty".equals(isEmptyCell[3])
				&& "empty".equals(isEmptyCell[4])
				&& "empty".equals(isEmptyCell[5])
				&& "empty".equals(isEmptyCell[6])
				&& "empty".equals(isEmptyCell[7])
				&& "empty".equals(isEmptyCell[8])
				&& "empty".equals(isEmptyCell[9])) {
			return true;
		} 
		else {
			return false;
		}
	}

	@Override
	public List<Map<String, Object>> autoCalculate(List<Map<String, String>> list, long uuidQuestion, 
			long uuidFormHistory, AuditContext callerId) {
		
		List<Map<String, Object>> listResult = new ArrayList<>();
		
		MsQuestion msQuestion = this.commonLogic.retrieveQuestionByUuid(uuidQuestion, callerId);

		Object[][] paramsRel = {{Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory)},
				{Restrictions.like("calculate", "%" + msQuestion.getRefId() + "%")} };
		Map<String, Object> msQuestionRel = this.getManagerDAO().list(MsFormquestionset.class, paramsRel, null);
		List<MsFormquestionset> listQuestionRel = (List<MsFormquestionset>) msQuestionRel.get(GlobalKey.MAP_RESULT_LIST);
		
		List listQuestionCur = (List) this.getManagerDAO().selectAllNativeString(
				"SELECT REF_ID, CODE_ANSWER_TYPE FROM MS_QUESTION msq with (nolock) INNER JOIN MS_ANSWERTYPE msa with (nolock)"
				+ " ON msq.UUID_ANSWER_TYPE = msa.UUID_ANSWER_TYPE WHERE msa.code_answer_type IN ('003', '004', '005')",
				null);

		for (int j = 0; j < listQuestionCur.size(); j++) { 
			boolean exist = false;
			Map<String, Object> temp = (Map<String, Object>) listQuestionCur.get(j);
			for (int i = 0; i < list.size(); i++) {
				Map<String, String> mapList = list.get(i);
				if (temp.get("d0").toString().equals(mapList.get("refid"))){
					exist = true;
				}
			}

			if (!exist) {
				Map<String, String> map = new HashMap<>();
				map.put("id", "0");
				map.put("refid", temp.get("d0").toString());
				map.put("value", "0");
				list.add(map);
			}
		}

		for (MsFormquestionset msQuestionrelevant : listQuestionRel) {
			Map<String, Object> mapResult = new HashMap<>();
			String idResult = "";
			String[] calculateArr = msQuestionrelevant.getCalculate().split("start");
			calculateArr = calculateArr[calculateArr.length - 1].split("end");
			calculateArr = calculateArr[0].split("=");
			String calculate = calculateArr[1].replace("_var", "")
					.replace("/*", "").replace("*/", "").replace(" ", "")
					.replace("$", "");

			int result = 0;

			if (!calculate.contains("dateformatter.age")) {
				for (int i = 0; i < list.size(); i++) {
					Map<String, String> mapList = list.get(i);
					calculate = calculate.replaceAll(
							"\\b" + mapList.get("refid") + "\\b",
							mapList.get("value") == null ? "0" : mapList.get("value"));

					if (msQuestionrelevant.getRefId().equals(mapList.get("refid"))) {
						idResult = mapList.get("id");
					}
				}

				ScriptEngineManager mgr = new ScriptEngineManager();
				ScriptEngine engine = mgr.getEngineByName("JavaScript");

				try {
					Object evalResult = engine.eval(calculate);					
					if (evalResult instanceof Double) {
					    result = ((Double) evalResult).intValue();
					}
					else if (evalResult instanceof Integer) {
					    result = ((Integer) evalResult).intValue();
					}
					else if (evalResult instanceof BigDecimal) {
					    result = ((BigDecimal) evalResult).intValue();
					}
				} 
				catch (ScriptException e) {
					LOG.error("Exception on autocalculate evaluation", e);
				}

			}
			else {
				calculate = calculate.replaceAll("dateformatter.age", "");
				String refIdDate = calculate.substring(1, calculate.length() - 1);
				String date = null;
				for (int i = 0; i < list.size(); i++) {
					Map<String, String> mapList2 = list.get(i);
					String refId = mapList2.get("refid").toString();
					if (msQuestionrelevant.getMsQuestion().getRefId().equals(mapList2.get("refid"))) {
						idResult = mapList2.get("id");
					}
					if (Objects.equals(refIdDate, refId)) {
						date = mapList2.get("value").toString();
					}
				}

				SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
				Date birth2 = null;
				try {
					birth2 = dateFormat.parse(date);
				}
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
				Date now = new Date();
				long timeBetween = now.getTime() - birth2.getTime();
				double yearsBetween = timeBetween / 3.15576e+10;
				int ageV = (int) Math.floor(yearsBetween);
				result = (int) ageV;
			}
			if (idResult != "" && idResult != "0") {
				mapResult.put("id", idResult);
				mapResult.put("result", result);
				listResult.add(mapResult);
			}
		}

		return listResult;
	}

	@Override
	public List<Map<String, Object>> copyValue(List<Map<String, String>> list, long uuidForm, long uuidQuestion, long uuidUser,
			int seqQuest, long uuidTaskH, AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		MsQuestion msQuestion = this.getManagerDAO().selectOne(MsQuestion.class, uuidQuestion);
		Object[][] param = {{ "refId", msQuestion.getRefId() },
	 			{"uuidForm", uuidForm}, {"uuidtaskH", uuidTaskH}};
		List <Map<String, Object>> listQuest = this.getManagerDAO().selectAllNative("survey.tele.getQuestRelevantCopy", param, null);
		AmMsuser user = this.getManagerDAO().selectOne(AmMsuser.class, uuidUser);
		
		SimpleDateFormat format = new SimpleDateFormat();
		for (int i=0; i<list.size(); i++){
			Map map = list.get(i);
			if (Integer.parseInt(map.get("id").toString()) < seqQuest) {
				continue;
			}
			Object[][] params = {{ "refId", (String) map.get("refid") },
					 			{"uuidForm", uuidForm}, {"uuidtaskH", uuidTaskH}};
			String script = (String) this.getManagerDAO().selectOneNative("survey.tele.getScriptCopyValue", params);
			if (StringUtils.isEmpty(script)) {
				continue;
			}
			
			String convertedExpression = script;

			boolean needReplacing = true;
			while (needReplacing) {
				int idxOfOpenBrace = convertedExpression.indexOf('{');
				if (idxOfOpenBrace != -1) {
					// // there's {, prepare to replace what inside the {}
					int idxOfCloseBrace = convertedExpression.indexOf('}');
					String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
					int idxOfOpenAbs = identifier.indexOf("$");
					String flatAnswer = "";
					if (idxOfOpenAbs != -1) { // value yang bukan reff_id
						String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
						if (finalIdentifier.equalsIgnoreCase("LOGIN_ID")) {
							if (map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)){
								flatAnswer = user.getLoginId();
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("UUID_USER")) {
							if(map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)){
								flatAnswer = String.valueOf(uuidUser);
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("UUID_BRANCH")) {
							if(map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)){
								flatAnswer = String.valueOf(user.getMsBranch().getUuidBranch());
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("BRANCH_ID")) {
							if(map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)){
								flatAnswer = user.getMsBranch().getBranchCode();
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("BRANCH_NAME")) {
							if(map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)){
								flatAnswer = user.getMsBranch().getBranchName();
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("UUID_DEALER")) {
							if(map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)){
								flatAnswer = String.valueOf(user.getMsDealer().getUuidDealer());
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("DEALER_NAME")) {
							if(map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)){
								flatAnswer = user.getMsDealer().getDealerName();
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("FLAG_JOB")) {
							if(map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)){
								flatAnswer = user.getMsJob().getJobCode();
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("THISYEAR")) {
							if (map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_CURRENCY)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_NUMERIC)
									|| map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_DECIMAL)){
								Calendar cal = Calendar.getInstance(TimeZone.getDefault());
								flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("NOWADAYS")) {
							Calendar cal = Calendar.getInstance(TimeZone.getDefault());
							if (map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_DATE)) {
								format = new SimpleDateFormat("dd/MM/yyyy");
								flatAnswer = format.format(cal.getTime());
							}
							else if(map.get("answerType").toString().equals(GlobalVal.ANSWER_TYPE_DATETIME)){
								format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
								flatAnswer = format.format(cal.getTime());
							}
						}
					} 
					else { //value yang reff_id
						if (!listQuest.isEmpty()) {
							for (int j=0; j<i; j++) {
								Map map2 = list.get(j);
								if (map2.get("refid").equals(identifier)) {
									flatAnswer = map2.get("value").toString();
								}
							}
						}
					}
					if (flatAnswer != null && flatAnswer.length() > 0) {
						convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
					} 
					else {
						needReplacing = false;
					}
				} 
				else {
					needReplacing = false;
				}
			}
			
			if (!convertedExpression.contains("{")){
				if (!convertedExpression.toUpperCase().contains("COPY")) {
					Map<String, Object> mapResult = new HashMap<>();
					mapResult.put("id", map.get("id"));
					mapResult.put("result", convertedExpression);
					mapResult.put("ansType", map.get("answerType"));
					listResult.add(mapResult);
				}
				else {
					convertedExpression = convertedExpression.replaceAll("(?i)COPY", "");
					convertedExpression = convertedExpression.replace("(", "");
					convertedExpression = convertedExpression.replace(")", "");
					String arg [] = convertedExpression.split(",");
					boolean condition = false;
					String kondisi = convert(list, arg[0], callerId);
					ScriptEngineManager mgr = new ScriptEngineManager();
					ScriptEngine engine = mgr.getEngineByName("JavaScript");
					try {
						condition = (boolean) engine.eval(kondisi);
					} 
					catch (ScriptException e) {
						LOG.error("Exception on copy value evaluation", e);
					}
					if (condition) {
						Map<String, Object> mapResult = new HashMap<>();
						mapResult.put("id", map.get("id"));
						mapResult.put("result", arg[1]);
						mapResult.put("ansType", map.get("answerType"));
						listResult.add(mapResult);
					}
					else{
						if (arg.length > 2) {
							Map<String, Object> mapResult = new HashMap<>();
							mapResult.put("id", map.get("id"));
							mapResult.put("result", arg[2]);
							mapResult.put("ansType", map.get("answerType"));
							listResult.add(mapResult);
						}
					}
				}
			}			
		}
		
		return listResult;
	}
	
	private MsLov getLov(long uuidQuestion, List<TeleSurveyBean> taskDs, String description, String lovGroup, 
			long uuidFormHistory, AuditContext callerId) {		
		Object[][] paramsRlv = {
				{ Restrictions.eq("msQuestion.uuidQuestion", uuidQuestion) },
				{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) }};
		//MsFormQuestionset
		MsFormquestionset msQuestionRel = this.getManagerDAO().selectOne(MsFormquestionset.class, paramsRlv);
		
		List<String> constraints = new ArrayList<String>();
		if (null != msQuestionRel && StringUtils.isNotBlank(msQuestionRel.getChoiceFilter())) {
			String[] choiceFilters = msQuestionRel.getChoiceFilter().split(",");
			for (int a = choiceFilters.length - 1; a < choiceFilters.length && a >= 0; a--) {
				String refId = choiceFilters[a].replace("{", StringUtils.EMPTY).replace("}", StringUtils.EMPTY);

				for (TeleSurveyBean tsb : taskDs) {
					if (tsb == null) {
						continue;
					}

					if (StringUtils.equals(refId, tsb.getRefId())) {
						constraints.add(tsb.getAns());
					}
				}
			}			
		}

		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[] { Restrictions.eq("lovGroup", lovGroup) });
		paramStack.push(new Object[] { Restrictions.eq("description", description) });
		paramStack.push(new Object[] { Restrictions.eq("isActive", "1") });

		int x = 1;
		for (int j = constraints.size() - 1; j < constraints.size() && j >= 0; j--) {
			paramStack.push(new Object[] { Restrictions.eq("constraint" + x, constraints.get(j)) });
			x++;
		}

		Object[][] sqlParams = new Object[paramStack.size()][2];
		for (int k = 0; k < paramStack.size(); k++) {
			Object[] objects = paramStack.get(k);
			sqlParams[k] = objects;
		}

		MsLov lov = this.getManagerDAO().selectOne(MsLov.class, sqlParams);

		return lov;
	}
	
	private MsLov getLovInJson(long uuidQuestion, String[] textAnswers, String code, String lovGroup,
			long uuidFormHistory, List<AnswerBean> answers, AuditContext callerId) {		
		
		Object[][] paramsRlv = {
				{ Restrictions.eq("msQuestion.uuidQuestion", uuidQuestion) },
				{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) }};
		//MsFormQuestionset
		MsFormquestionset msQuestionRel = this.getManagerDAO().selectOne(MsFormquestionset.class, paramsRlv);
		
		List<String> constraints = new ArrayList<String>();
		if (null != msQuestionRel && StringUtils.isNotBlank(msQuestionRel.getChoiceFilter())) {
			String[] choiceFilters = msQuestionRel.getChoiceFilter().split(",");
			for (int a = choiceFilters.length - 1; a < choiceFilters.length && a >= 0; a--) {
				String refId = choiceFilters[a].replace("{", StringUtils.EMPTY).replace("}", StringUtils.EMPTY);

				int idxTaskDs = 0;
				for (AnswerBean answer : answers) {
					if (answer == null) {
						idxTaskDs++;
						continue;
					}

					String refIdAnswer = answer.getQuestion() == null ? null : answer.getQuestion().getRefId();
					if (StringUtils.equals(refId, refIdAnswer)) {
						constraints.add(textAnswers[idxTaskDs]);
					}
					idxTaskDs++;
				}
			}			
		}

		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[] { Restrictions.eq("lovGroup", lovGroup) });
		paramStack.push(new Object[] { Restrictions.eq("code", code) });
		paramStack.push(new Object[] { Restrictions.eq("isActive", "1") });

		int x = 1;
		for (int j = constraints.size() - 1; j < constraints.size() && j >= 0; j--) {
			paramStack.push(new Object[] { Restrictions.eq("constraint" + x, constraints.get(j)) });
			x++;
		}

		Object[][] sqlParams = new Object[paramStack.size()][2];
		for (int k = 0; k < paramStack.size(); k++) {
			Object[] objects = paramStack.get(k);
			sqlParams[k] = objects;
		}

		MsLov lov = this.getManagerDAO().selectOne(MsLov.class, sqlParams);

		return lov;
	}

	private MsLov getLov(long uuidQuestion, String[] textAnswers, String code, String lovGroup,
			long uuidFormHistory, List<TrTaskD> taskDs, AuditContext callerId) {		
		
		Object[][] paramsRlv = {
				{ Restrictions.eq("msQuestion.uuidQuestion", uuidQuestion) },
				{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) }};
		//MsFormQuestionset
		MsFormquestionset msQuestionRel = this.getManagerDAO().selectOne(MsFormquestionset.class, paramsRlv);
		
		List<String> constraints = new ArrayList<String>();
		if (null != msQuestionRel && StringUtils.isNotBlank(msQuestionRel.getChoiceFilter())) {
			String[] choiceFilters = msQuestionRel.getChoiceFilter().split(",");
			for (int a = choiceFilters.length - 1; a < choiceFilters.length && a >= 0; a--) {
				String refId = choiceFilters[a].replace("{", StringUtils.EMPTY).replace("}", StringUtils.EMPTY);

				int idxTaskDs = 0;
				for (TrTaskD taskD : taskDs) {
					if (taskD == null) {
						idxTaskDs++;
						continue;
					}

					String refIdAnswer = taskD.getMsQuestion() == null ? null : taskD.getMsQuestion().getRefId();
					if (StringUtils.equals(refId, refIdAnswer)) {
						constraints.add(textAnswers[idxTaskDs]);
					}
					idxTaskDs++;
				}
			}			
		}

		Stack<Object[]> paramStack = new Stack<>();
		paramStack.push(new Object[] { Restrictions.eq("lovGroup", lovGroup) });
		paramStack.push(new Object[] { Restrictions.eq("code", code) });
		paramStack.push(new Object[] { Restrictions.eq("isActive", "1") });

		int x = 1;
		for (int j = constraints.size() - 1; j < constraints.size() && j >= 0; j--) {
			paramStack.push(new Object[] { Restrictions.eq("constraint" + x, constraints.get(j)) });
			x++;
		}

		Object[][] sqlParams = new Object[paramStack.size()][2];
		for (int k = 0; k < paramStack.size(); k++) {
			Object[] objects = paramStack.get(k);
			sqlParams[k] = objects;
		}

		MsLov lov = this.getManagerDAO().selectOne(MsLov.class, sqlParams);

		return lov;
	}
	
	@Override
	public Map<String, Object> luOnline(AmMsuser userLoggedIn, String refId, String lovGroup, 
			String searchVal, long uuidFormHistory,  String choice, AuditContext callerId) {		
		StringBuilder choiceFilterVal = new StringBuilder();
		MsQuestion quest = this.commonLogic.retrieveQuestionByRefIdQset(refId,
				userLoggedIn.getAmMssubsystem().getUuidMsSubsystem(), uuidFormHistory, callerId);		
		Object[][] paramsRel = {{ Restrictions.eq("msQuestion.uuidQuestion", quest.getUuidQuestion()) },
				{ Restrictions.eq("msFormhistory.uuidFormHistory", uuidFormHistory) } };
		MsFormquestionset msQuestionSet = this.getManagerDAO().selectOne(MsFormquestionset.class, paramsRel);
		String[] opt = choice.split(";");
		if (msQuestionSet != null) {
			if (msQuestionSet.getChoiceFilter() != null && msQuestionSet.getChoiceFilter().length() > 0) {
				String filter = msQuestionSet.getChoiceFilter();
				String[] arg = filter.split(",");
				for	(int i=0; i<arg.length; i++) {
					arg[i] = arg[i].replace("{", "").replace("}", "");
					for	(int j=0; j<opt.length; j++) {
						String arg2 [] = opt[j].split(",");
						if (arg2[0].equalsIgnoreCase(arg[i]) && arg2.length > 1) {
							choiceFilterVal.append(arg2[1]).append("@@@");
						}
					}
				}
			}
		}
		List<AnswerItemBean> list = (List<AnswerItemBean>) intFormLogic.luOnline(refId, lovGroup, searchVal, 
				choiceFilterVal.toString(), callerId);
		
		Map<String, Object> result = new HashMap<>();
		result.put(GlobalKey.MAP_RESULT_LIST, list);
		result.put(GlobalKey.MAP_RESULT_SIZE, list.size());
		return result;
	}
	
	private boolean cekArgumen(String convertedExpression, String answerType, AuditContext callerId){
		boolean result = false;
		SimpleDateFormat format = new SimpleDateFormat();
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		
		if (convertedExpression.contains("dateDifference")) {
			convertedExpression = convertedExpression.replace("dateDifference", "");
			convertedExpression = convertedExpression.replace(")", "");
			convertedExpression = convertedExpression.replace("(", "");
			String[] arg = convertedExpression.split(",");
			long time1 = 0, time2 = 0;
			if (answerType.equals(GlobalVal.ANSWER_TYPE_DATE)) {
				format = new SimpleDateFormat("dd/MM/yyyy");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_TIME)) {
				format = new SimpleDateFormat("dd/MM/yyyy");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATETIME)) {
				format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			Period difference = new Period(time1, time2, PeriodType.yearMonthDayTime());
			if (arg[2].contains("DAY")) {
				int day = difference.getDays();
				arg[2] = arg[2].replace("|DAY|", day+"");
			}
			else if(arg[2].contains("MONTH")) {
				int month = difference.getMonths();
				arg[2] = arg[2].replace("|MONTH|", month+"");
			}
			else if(arg[2].contains("YEAR")) {
				int year = difference.getYears();
				arg[2] = arg[2].replace("|YEAR|", year+"");
			}
			else if(arg[2].contains("HOUR")) {
				int hour = difference.getHours();
				arg[2] = arg[2].replace("|HOUR|", hour+"");
			}
			else if(arg[2].contains("MINUTE")) {
				int minute = difference.getMinutes();
				arg[2] = arg[2].replace("|MINUTE|", minute+"");
			}
			else if(arg[2].contains("SECOND")) {
				int second = difference.getYears();
				arg[2] = arg[2].replace("|SECOND|", second+"");
			}
			arg[2] = arg[2].contains("==")?arg[2].replace("==", "<="):arg[2];
			try {
				result = (boolean) engine.eval(arg[2]);
			} 
			catch (ScriptException e) {
				LOG.error("Exception on date evaluation", e);
			}
		}
		else if (convertedExpression.contains("<=") || convertedExpression.contains("<") ||
				convertedExpression.contains(">=") || convertedExpression.contains(">") ||
				convertedExpression.contains("!=") || convertedExpression.contains("==")) {
			convertedExpression = convertedExpression.replace("(", "");
			convertedExpression = convertedExpression.replace(")", "");
			String [] arg = convertedExpression.split("<=|<|>=|>|!=|==");
			String cek = "";
			String delimeter="";
			if (convertedExpression.contains("<=")) {
				delimeter = "<=";
			}
			else if (convertedExpression.contains("<")) {
				delimeter = "<";
			}
			else if (convertedExpression.contains(">=")) {
				delimeter = ">=";
			}
			else if (convertedExpression.contains(">")) {
				delimeter = ">";
			}
			else if (convertedExpression.contains("!=")) {
				delimeter = "!=";
			}
			else if (convertedExpression.contains("==")) {
				delimeter = "==";
			}
			if (answerType.equals(GlobalVal.ANSWER_TYPE_DATE)) {
				long time1, time2;
				format = new SimpleDateFormat("dd/MM/yyyy");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 +delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_TIME)) {
				long time1, time2;
				format = new SimpleDateFormat("dd/MM/yyyy");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 + delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATETIME)) {
				long time1, time2;
				format = new SimpleDateFormat("dd/MM/yyyyHH:mm");
				try {
					time1 = format.parse(arg[0]).getTime();
					time2 = format.parse(arg[1]).getTime();
					cek = time1 + delimeter + time2;
				} 
				catch (ParseException e) {
					LOG.error("Exception on parsing date", e);
				}
			}
			else if (answerType.equals(GlobalVal.ANSWER_TYPE_TEXT) 
					|| answerType.equals(GlobalVal.ANSWER_TYPE_TEXT_MULTILINE)) {
				arg[0] = arg[0].replace(" ", "");
				arg[1] = arg[1].replace(" ", "");
				if (delimeter.equals("==")) {
					cek = (String) (arg[0].equalsIgnoreCase(arg[1]) ? "true" : "false");
				}
				else if (delimeter.equals("!=")) {
					cek = (String) (!arg[0].equalsIgnoreCase(arg[1]) ? "true" : "false");
				}
				else {
					cek = convertedExpression;
				}
			}
			else {
				cek = convertedExpression;
			}
			
			try {
				result = (boolean) engine.eval(cek);
			}
			catch (ScriptException e) {
				LOG.error("Exception on date evaluation", e);
			}
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> validation(List<Map<String, String>> list, long uuidForm, long uuidQuestion, 
			long uuidTaskH, AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		
		ScriptEngineManager mgr = new ScriptEngineManager();
		ScriptEngine engine = mgr.getEngineByName("JavaScript");
		boolean finalResult=false;

//		boolean isValid = false;
		for (int k = 0; k<list.size(); k++){
			Map map = list.get(k) ;
			Object[][] params = {{"refId", (String) map.get("refid")},
		 			{"uuidForm", uuidForm}, {"uuidtaskH", uuidTaskH}};
			Object [] obj = (Object []) this.getManagerDAO().selectOneNative("survey.tele.getScriptValidation", params);
			String validationScript = obj[0].toString();
			String validationMessage = obj[1].toString();
			if (StringUtils.isBlank(validationScript)) {
				continue;
			}
			
			String convertedExpression = validationScript;
			String answerType = (String) map.get("answerType");
			String answerString = (String) map.get("value");
			String id = (String) map.get("id");
			
			if (StringUtils.isBlank(answerString)) {
				continue;
			}
			
			boolean needReplacing = true;
			while (needReplacing) {
				int idxOfOpenBrace = convertedExpression.indexOf('{');
				if (idxOfOpenBrace != -1) {
					// there's {, prepare to replace what inside the {}
					int idxOfCloseBrace = convertedExpression.indexOf('}');
					String identifier = convertedExpression.substring(idxOfOpenBrace + 1, idxOfCloseBrace);
					int idxOfOpenAbs = identifier.indexOf("$");
					String flatAnswer = "";
					if (idxOfOpenAbs != -1) {
						String finalIdentifier = identifier.substring(idxOfOpenAbs + 1);
						if (finalIdentifier.equalsIgnoreCase("ANSWER")) {
							try {
								if (answerType.equals(GlobalVal.ANSWER_TYPE_TIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									SimpleDateFormat formatDate = new SimpleDateFormat("yyyyMMdd");
									Date date2 = null;
									try {
										date2 = formatDate.parse(answerString);
									} 
									catch (Exception e) {
										date2 = format.parse(answerString);
									}
									Calendar now = Calendar.getInstance(TimeZone.getDefault());
									Calendar date = Calendar.getInstance(TimeZone.getDefault());
									date.setTime(date2);
									date.set(Calendar.YEAR, now.get(Calendar.YEAR));
									date.set(Calendar.MONTH, now.get(Calendar.MONTH));
									date.set(Calendar.DAY_OF_MONTH,now.get(Calendar.DAY_OF_MONTH));
									flatAnswer = format.format(date.getTime());
								}
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATETIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									SimpleDateFormat formatDate = new SimpleDateFormat("ddMMyyyyHHmmss");
									Date date2 = null;
									try {
										date2 = formatDate.parse(answerString);
									} 
									catch (Exception e) {
										date2 = format.parse(answerString);
									}
									Calendar date = Calendar.getInstance(TimeZone.getDefault());
									date.setTime(date2);
									flatAnswer = format.format(date.getTime());
								}
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATE)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									SimpleDateFormat formatDate = new SimpleDateFormat("ddMMyyyy");
									Date date2 = null;
									try {
										date2 = formatDate.parse(answerString);
									} 
									catch (Exception e) {
										date2 = format.parse(answerString);
									}
									Calendar date = Calendar.getInstance(TimeZone.getDefault());
									date.setTime(date2);
									flatAnswer = format.format(date.getTime());
								}
								else {
									flatAnswer = answerString;
								}
							} 
							catch (Exception e) {
								LOG.error("Error on validation", e);
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("THISYEAR")) {
							Calendar cal = Calendar.getInstance(TimeZone.getDefault());
							flatAnswer = String.valueOf(cal.get(Calendar.YEAR));
						}
						else if (finalIdentifier.equalsIgnoreCase("NOWADAYS")) {
							Calendar cal = Calendar.getInstance(TimeZone.getDefault());
							try {
								if (answerType.equals(GlobalVal.ANSWER_TYPE_TIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									flatAnswer = format.format(cal.getTime());
								}
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATETIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									flatAnswer = format.format(cal.getTime());
								}
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATE)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									flatAnswer = format.format(cal.getTime());
								}
								else {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									flatAnswer = format.format(cal.getTime());
								}
							} 
							catch (Exception e) {
								LOG.error("Error on validation", e);
							}
						}
						else if (finalIdentifier.equalsIgnoreCase("YESTERDAY")) {
							Calendar cal = Calendar.getInstance(TimeZone.getDefault());
							cal.add(Calendar.DATE, -1);
							try {
								if (answerType.equals(GlobalVal.ANSWER_TYPE_TIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									flatAnswer = format.format(cal.getTime());
								}
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATETIME)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									flatAnswer = format.format(cal.getTime());
								}
								else if (answerType.equals(GlobalVal.ANSWER_TYPE_DATE)) {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
									flatAnswer = format.format(cal.getTime());
								}
								else {
									SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm");
									flatAnswer = format.format(cal.getTime());
								}
							} 
							catch (Exception e) {
								LOG.error("Error on validation", e);
							}
						}
					}
					else { //if finalIdentifier = refId
						for (int j=0; j<list.size()-1; j++){
							Map map2 = list.get(j);
							if (map2.get("refid").equals(identifier)) {
								flatAnswer = map2.get("value").toString();
							}
						}
					}
					if (flatAnswer != null && flatAnswer.length() > 0) {
						convertedExpression = convertedExpression.replace("{" + identifier + "}", flatAnswer);
					}
				}
				else {
					needReplacing = false;
				}
			}
			String validasi = this.valid(convertedExpression, answerType, callerId);
			try {
				finalResult = (boolean) engine.eval(validasi);
			} 
			catch (ScriptException e) {
				LOG.error("Exception on validation evaluation", e);
			}	
			
			Map<String, Object> result = new HashMap<>();
			result.put("id", id);
			result.put("validasi", finalResult);
			result.put("message", validationMessage);
			listResult.add(result);	
		}
		return listResult;
	}
	
	@Override
	public List<Map<String, Object>> relevant(List<Map<String, String>> list, long uuidForm, long uuidTaskH,  
			AuditContext callerId) {
		List<Map<String, Object>> listResult = new ArrayList<>();
		 
		for (int k = 0; k<list.size(); k++){
			Map map = list.get(k) ;
			Object[][] params = {{"refId", (String) map.get("refid")},
								 {"uuidForm", uuidForm}, {"uuidtaskH", uuidTaskH}};
			String script = (String) this.getManagerDAO().selectOneNative("survey.tele.getRelevantScript", params);
			
			if (StringUtils.isEmpty(script)) {
				map.put("isRel", "0");
				listResult.add(map);
				continue;
			}
			else {
				boolean res = false;
				ScriptEngineManager mgr = new ScriptEngineManager();
				ScriptEngine engine = mgr.getEngineByName("JavaScript");
				String convertedExpression = this.convert(list, script, callerId);
				try {
					res = (boolean) engine.eval(convertedExpression);
				} 
				catch (ScriptException e) {
					LOG.error("Exception on relevant evaluation", e);
				}
				map.put("isRel", res);
				listResult.add(map);
			}
		}
		return listResult;
	}
	
	private String valid(String script, String type, AuditContext callerId) {
		String convertedExpression = script;
		
		String [] arg = convertedExpression.toUpperCase().split("(&&)|( AND )|( OR )|(\\|\\|)");
		boolean hasil = false;
		for	(int i =0; i<arg.length; i++){
			String finalscript = arg[i].replace("(", "");
			finalscript = finalscript.replace(")","");
			hasil = this.cekArgumen(finalscript, type, callerId);
			convertedExpression = convertedExpression.toUpperCase().replace(finalscript, Boolean.toString(hasil));
		}
		convertedExpression = convertedExpression.toUpperCase().replace("OR", "||");
		convertedExpression = convertedExpression.toUpperCase().replace("AND", "&&");
		convertedExpression = convertedExpression.toLowerCase();
		return convertedExpression;
	}
	
	private TaskDocumentBean retrieveTaskDJsonAsBean(TrTaskH trTaskH, AuditContext callerId) {
		TrTaskdocument docDb = this.getManagerDAO().selectOne(
				"from TrTaskdocument doc join fetch doc.trTaskH where doc.uuidTaskId = :uuidTaskId",
				new Object[][]{{"uuidTaskId", trTaskH.getUuidTaskH()}});
		if (docDb == null || StringUtils.isBlank(docDb.getDocument())){
			return null;
		}
		
		Gson gson = new Gson();
		
		TaskDocumentBean document = gson.fromJson(docDb.getDocument(), TaskDocumentBean.class);
		return document;
	}
	
	private List<TrTaskBean> listTaskDJsonWithQuestionSet(TaskDocumentBean document,
			long uuidFormHistory, ImageStorageLocation isl, AuditContext callerId) {		
		List<MsFormquestionset> questionset = this.commonLogic.retrieveMsFormquestionset(uuidFormHistory, callerId);
		if (questionset == null || questionset.isEmpty())
			return Collections.emptyList();	
		
		List<TrTaskBean> listAnswer = new ArrayList<>();
		MsLov emptyMsLov = new MsLov();
		for (Iterator<MsFormquestionset> iterator = questionset.iterator(); iterator.hasNext();) {
			MsFormquestionset question = iterator.next();			
			TrTaskBean bean = new TrTaskBean();
			
			bean.setUuidTaskD(0L);
			bean.setMsQuestion(question.getMsQuestion());
			bean.setQuestionText(question.getQuestionLabel());
			bean.setIsReadonly(question.getIsReadonly());
			bean.setMsLovByIntLovId(emptyMsLov); //diperlukan saat prepareLov
			listAnswer.add(bean);
			
			int idxQ = document.findAnswerIndex(question.getMsQuestion().getUuidQuestion());
			if (idxQ == -1){
				continue;
			}
			
			AnswerBean answer = document.getAnswers().get(idxQ);
			
			bean.setIntTextAnswer(answer.getIntTxtAnswer());
			bean.setTextAnswer(answer.getTxtAnswer());
			
			if (answer.getIntOptAnswers() != null && !answer.getIntOptAnswers().isEmpty()) {
				long uuidLov = answer.getIntOptAnswers().get(0).getUuid();
				bean.setMsLovByIntLovId(this.getManagerDAO().selectOne(MsLov.class, uuidLov));
			}
			
			if (answer.getLocation() != null) {
				LocationBean location = answer.getLocation();
			
				if (location.getLat() != null){
					bean.setLatitude(new BigDecimal(location.getLat().doubleValue()));
				}
				if (location.getLng() != null){
					bean.setLongitude(new BigDecimal(location.getLng().doubleValue()));
				}
				
				bean.setAccuracy(location.getAccuracy());

			}
			if (answer.getLobAnswer() != null && answer.getLobAnswer().isHasImage()) {
				byte[] image = null;
				if (isl == ImageStorageLocation.FILE_SYSTEM) {
					image = this.imageStorageLogic.retrieveImageFileSystemByTaskD(
							answer.getLobAnswer().getId(), false);				
				}
				else {
					image = imageStorageLogic.retrieveImageBlob(answer.getLobAnswer().getId(), false);					
				}
				
				if (image != null) {
					bean.setImgBase64(BaseEncoding.base64().encode(image));
				}
			}
		}
		
		return listAnswer;
	}
	
	private List<TrTaskBean> listTaskDWithQuestionset(TrTaskH trTaskH, long uuidSubsystem, long uuidFormHistory, AuditContext callerId) {
		Object[][] paramsNative = {{"uuidTaskH", trTaskH.getUuidTaskH()}};

		List<Map<String, Object>> resultAns = this.getManagerDAO().selectAllNative(
				"survey.tele.getAllTaskDVersioning", paramsNative, null);
		if (resultAns == null || resultAns.isEmpty()) {
			return Collections.emptyList();
		}
						
		List<TrTaskBean> listAnswer = new ArrayList<>(resultAns.size());
		
		for (int i = 0; i < resultAns.size(); i++) {
			Map<String, Object> taskMap = (Map<String, Object>) resultAns.get(i);
			TrTaskBean bean = new TrTaskBean();
			
			BigInteger uuidQuest = (BigInteger) taskMap.get("d6");
			MsQuestion msQuestion = commonLogic.retrieveQuestionByuuidQuestionQset(
					uuidQuest.longValue(), uuidSubsystem, uuidFormHistory, callerId);

			MsLov msLovByLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(taskMap.get("d24").toString()));
			MsLov msLovByIntLovId = new MsLov();
			MsLov msLovByFinLovId = new MsLov();

			if (StringUtils.isNotBlank(taskMap.get("d25").toString()) && !"0".equals(taskMap.get("d25").toString())) {
				msLovByIntLovId = this.getManagerDAO().selectOne(MsLov.class, Long.valueOf(taskMap.get("d25").toString()));
			}

			msLovByFinLovId.setUuidLov(Long.valueOf(taskMap.get("d26").toString()));
			
			bean.setUuidTaskD(Long.valueOf(taskMap.get("d0").toString()));
			bean.setUsrCrt(taskMap.get("d1").toString());
			bean.setDtmCrt((Date) taskMap.get("d2"));
			bean.setUsrUpd(taskMap.get("d3").toString());
			bean.setDtmUpd((Date) taskMap.get("d4"));
			bean.setTrTaskH(trTaskH);
			bean.setMsQuestion(msQuestion);
			bean.setQuestionText(taskMap.get("d7").toString());
			bean.setTextAnswer(taskMap.get("d8").toString());
			bean.setOptionText(taskMap.get("d9").toString());
			bean.setImagePath(taskMap.get("d10").toString());
			if (null != taskMap.get("d11") && null != taskMap.get("d12")) {
				bean.setLatitude(new BigDecimal(taskMap.get("d11").toString()));
				bean.setLongitude(new BigDecimal(taskMap.get("d12").toString()));
			}
			else {
				bean.setLatitude(null);
				bean.setLongitude(null);
			}
			bean.setAccuracy(Integer.parseInt(taskMap.get("d13").toString()));
			bean.setMcc(Integer.parseInt(taskMap.get("d14").toString()));
			bean.setMnc(Integer.parseInt(taskMap.get("d15").toString()));
			bean.setLac(Integer.parseInt(taskMap.get("d16").toString()));
			bean.setCellId(Integer.parseInt(taskMap.get("d17").toString()));
			bean.setTimestampTask((Date) taskMap.get("d18"));
			bean.setIsGps(taskMap.get("d19").toString());
			bean.setIsGsm(taskMap.get("d20").toString());
			bean.setGeolocationProvider(taskMap.get("d21").toString());
			bean.setIsVisibleQa(taskMap.get("d22").toString());
			bean.setIntTextAnswer(taskMap.get("d23").toString());
			bean.setMsLovByLovId(msLovByLovId);
			bean.setMsLovByIntLovId(msLovByIntLovId);
			bean.setMsLovByFinLovId(msLovByFinLovId);
			bean.setIntOptionText(taskMap.get("d27").toString());
			bean.setFinTextAnswer(taskMap.get("d28").toString());
			bean.setFinOptionText(taskMap.get("d29").toString());
			bean.setFinUseImageFlag(taskMap.get("d30").toString());
			bean.setIsReadonly(taskMap.get("d31").toString());
			bean.setIsConverted(taskMap.get("d33").toString());		
			if ("1".equals(taskMap.get("d34").toString())) {
				byte[] image = null;
				if (StringUtils.isBlank(bean.getImagePath())) {
					image = this.imageStorageLogic.retrieveImageFileSystemByFile(new File(bean.getImagePath()));					
				}
				else {
					image = imageStorageLogic.retrieveImageBlob(Long.valueOf(taskMap.get("d0").toString()), false);					
				}
				
				if (image != null) {
					bean.setImgBase64(BaseEncoding.base64().encode(image));
				}
			}
			
			listAnswer.add(bean);
		}
		
		return listAnswer;
	}
	
	private class MultipleTaskDResult {
		private Map<String, List<String>> multiByGroup;
		private Map<String, List<String>> multiByGroupDesc;
						
		public MultipleTaskDResult(Map<String, List<String>> multiByGroup2, Map<String, List<String>> multiByGroupDesc2) {
			super();
			this.multiByGroup = multiByGroup2;
			this.multiByGroupDesc = multiByGroupDesc2;
		}
		
		public Map<String, List<String>> getMultiByGroup() {
			return multiByGroup;
		}
		
		public Map<String, List<String>> getMultiByGroupDesc() {
			return multiByGroupDesc;
		}		
	}
	
	private MultipleTaskDResult processMultipleAnswerInJson(List<? extends TrTaskD> taskDs, TaskDocumentBean document, AuditContext callerId) {
		if (taskDs == null || taskDs.isEmpty() || document.getAnswers() == null || document.getAnswers().isEmpty()) { 
			return new MultipleTaskDResult(Collections.EMPTY_MAP, Collections.EMPTY_MAP);
		}
		
		Map<String, List<String>> multiByGroup = new HashMap<String, List<String>>();
		Map<String, List<String>> multiByGroupDesc = new HashMap<String, List<String>>();
		
		int idx = 0;
		for (Iterator iterator = taskDs.iterator(); iterator.hasNext();) {
			TrTaskD taskD = (TrTaskD) iterator.next();
			String answerType = taskD.getMsQuestion().getMsAnswertype().getCodeAnswerType();
			
			if (!MssTool.isMultipleQuestion(answerType)) {
				continue;
			}
			
			int idxAnswer = document.findAnswerIndex(taskD.getMsQuestion().getUuidQuestion());
			if (idxAnswer == -1) {
				continue;
			}
			AnswerBean answer = document.getAnswers().get(idxAnswer);
			if (answer == null || answer.getIntOptAnswers() == null || answer.getIntOptAnswers().isEmpty()) {
				continue;
			}
			
			List<String> optionChecked = new ArrayList<String>();
			List<String> descChecked = new ArrayList<String>();
			
			List<OptionBean> checkList = answer.getIntOptAnswers();
			for (Iterator<OptionBean> checkIter = checkList.iterator(); checkIter.hasNext();) {
				OptionBean optionBean = checkIter.next();
				optionChecked.add(optionBean.getCode());
				descChecked.add(optionBean.getFreeText());
			}
			
			multiByGroup.put(taskD.getMsQuestion().getLovGroup()
					+ "[" + taskD.getMsQuestion().getRefId() + "][" + idx + "]", optionChecked);
			multiByGroupDesc.put(taskD.getMsQuestion().getLovGroup()
					+ "[" + taskD.getMsQuestion().getRefId() + "][" + idx + "]", descChecked);
			idx++;
		}
		
		return new MultipleTaskDResult(multiByGroup, multiByGroupDesc);
	}
	
	/*
	 * Dikarenakan List QuestionSet dan answer dari TrTaskD akan digunakan untuk jsp,
	 * maka answer multiple perlu dihandle supaya: pertanyaan hanya muncul 1x di jsp 
	 */
	private MultipleTaskDResult removeMultipleTaskDRow(List<TrTaskBean> listAnswer, AuditContext callerId) {
		if (listAnswer == null || listAnswer.isEmpty()) { 
			return new MultipleTaskDResult(Collections.EMPTY_MAP, Collections.EMPTY_MAP);
		}
		
		Map<String, List<String>> multiByGroup = new HashMap<String, List<String>>();
		Map<String, List<String>> multiByGroupDesc = new HashMap<String, List<String>>();
		List<String> optionChecked = new ArrayList<String>();
		List<String> descChecked = new ArrayList<String>();
		
		long lastUuidQuestion = 0l;		
		int idx = 0;
		TrTaskBean taskAnswerPrev = null;

		for (Iterator<? extends TrTaskD> iterator = listAnswer.iterator(); iterator.hasNext();) {
			TrTaskBean taskAnswerCurr = (TrTaskBean) iterator.next();

			MsAnswertype msAnswertype = taskAnswerCurr.getMsQuestion().getMsAnswertype();

			if (taskAnswerCurr.getMsLovByIntLovId() != null && MssTool.isMultipleQuestion(msAnswertype.getCodeAnswerType())) {				
				optionChecked.add(taskAnswerCurr.getMsLovByIntLovId().getCode());
				descChecked.add(taskAnswerCurr.getIntTextAnswer());
			}
			
			if (taskAnswerPrev != null) {
				MsAnswertype msAnswertypePrev = taskAnswerPrev.getMsQuestion().getMsAnswertype();

				if ((lastUuidQuestion != taskAnswerCurr.getMsQuestion().getUuidQuestion()
						&& 0l != lastUuidQuestion && MssTool.isMultipleQuestion(msAnswertypePrev.getCodeAnswerType()))
						|| (!iterator.hasNext() && MssTool.isMultipleQuestion(msAnswertype.getCodeAnswerType()))) {
					TrTaskBean source = null;
					if (iterator.hasNext()) {
						source = taskAnswerPrev;
					}
					else {
						source = taskAnswerCurr;
					}
					
					multiByGroup.put(source.getMsLovByIntLovId().getLovGroup()
							+ "[" + source.getMsQuestion().getRefId() + "][" + idx + "]", optionChecked);
					multiByGroupDesc.put(source.getMsLovByIntLovId().getLovGroup()
							+ "[" + source.getMsQuestion().getRefId() + "][" + idx + "]", descChecked);
					optionChecked = new ArrayList<String>();
					descChecked = new ArrayList<String>();
					idx++;					
				}
			}
			
			if (lastUuidQuestion == taskAnswerCurr.getMsQuestion().getUuidQuestion()) {
				iterator.remove();
			}
			
			taskAnswerPrev = taskAnswerCurr;
			lastUuidQuestion = taskAnswerCurr.getMsQuestion().getUuidQuestion();
		}
		
		return new MultipleTaskDResult(multiByGroup, multiByGroupDesc);
	}
	
	private Map<String, LinkedHashMap<String, String>> prepareLov(List<TrTaskBean> listAnswer, long uuidBranch, AuditContext callerId) {
		if (listAnswer == null || listAnswer.isEmpty()) {
			return Collections.emptyMap();
		}
		
		NumberFormat formatKurs = DecimalFormat.getInstance(Locale.US);
		
		Map<String, LinkedHashMap<String, String>> mapOptionByLov = new HashMap<String, LinkedHashMap<String, String>>();
		for (int j = 0; j < listAnswer.size(); j++) {
			TrTaskD taskAnswer = listAnswer.get(j);

			MsAnswertype msAnswertype = taskAnswer.getMsQuestion().getMsAnswertype();
			if (taskAnswer.getMsLovByIntLovId() != null) {
				if (StringUtils.isNotBlank(taskAnswer.getMsQuestion().getLovGroup())) {
					LinkedHashMap<String, String> optionByLov = this.mapOptAnsByBranch(uuidBranch, taskAnswer, callerId);
					mapOptionByLov.put(taskAnswer.getMsQuestion().getLovGroup() +
							"[" + taskAnswer.getMsQuestion().getRefId() + "][" + j + "]", optionByLov);
				}

			}
			
			if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(msAnswertype.getCodeAnswerType())) {				
				if (StringUtils.isNotBlank(taskAnswer.getIntTextAnswer()))
					taskAnswer.setIntTextAnswer(formatKurs.format(NumberUtils.toDouble(taskAnswer.getIntTextAnswer())));
			}
		}
		
		return mapOptionByLov;
	}
	
	/*
	 * Expected cell value in xls
	 * 001 text 					: as is
	 * 002 text multiline			: as is
	 * 003 currency					: must be numeric 
	 * 004 numeric					: must be digits
	 * 005 decimal					: must be numeric
	 * 006 multiple					: code1#code2
	 * 007 multiple one description	: code1^description1#code2^description1
	 * 008 multiple with description: code1^description1#code2^description2
	 * 009 radio					: code
	 * 010 radio with description	: code^description
	 * 011 dropdown					: code
	 * 012 dropdown with description: code^description
	 * 013 date						: dd/MM/yyyy
	 * 014 time						: HH:mm
	 * 015 date time				: dd/MM/yyyy HH:mm:ss
	 * 016 image					: base64
	 * 017 image geodata			: base64 (image's location not supported)
	 * 018 image gps				: base64 (image's location not supported)
	 * 019 lookup (v1)				: as is (not supported)
	 * 020 lookup with filter (v1)	: as is (not supported)
	 * 021 drawing					: base64
	 * 022 responden (v1-poc)		: as is (not supported)
	 * 023 data list				: as is (not supported)
	 * 024 location					: lat,lng
	 * 025 text with suggestion		: as is
	 * 026 luonline					: code|description
	 */
	private void saveSheetDataByAnswerType(TrTaskH taskH, long uuidFormHistory, List<TeleSurveyBean> taskDs, MsQuestion msQuestion,
			String questionLabel, String cellAnswer, String codeAnswerType, String lovGroup,
			String errorMsg, int jsonIdxAnswer, TaskDocumentBean document,
			List<AnswerBean> jsonAnswers, AuditContext auditContext) {
		AnswerBean jsonAnswer = null;
		if (jsonIdxAnswer == -1) {
			jsonAnswer = AnswerBean.fromMsQuestion(msQuestion);
			//overwrite from MS_QUESTION because probably changed, while questionLabel argument is from published questionset
			jsonAnswer.getQuestion().setLabel(questionLabel);
		}
		else {
			jsonAnswer = jsonAnswers.get(jsonIdxAnswer);
		}
		
		if (MssTool.isMultipleQuestion(codeAnswerType)) {
			if (StringUtils.isBlank(cellAnswer)) {
				return;
			}
			
			if (GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(codeAnswerType)
					|| GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(codeAnswerType)) {
				int noOfChecked = StringUtils.countMatches(cellAnswer, "#");
				int noOfDescription = StringUtils.countMatches(cellAnswer, "^");
				if (noOfDescription <= noOfChecked) {
					errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.invalidformatchoice", 
							null, this.retrieveLocaleAudit(auditContext));
					throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
				}
			}
			
			String[] checkedAnswers = StringUtils.split(cellAnswer, '#');
			List<MsLov> listCode = new ArrayList<>();
			List<String> listDescription = new ArrayList<>();
			
			for (String checkedAnswer : checkedAnswers) {
				String[] answer = StringUtils.split(checkedAnswer, '^');
				MsLov lov = this.getLov(msQuestion.getUuidQuestion(), taskDs, answer[0], lovGroup, uuidFormHistory, auditContext);
				if (null == lov) {
					errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.wrongchoices", 
							null, this.retrieveLocaleAudit(auditContext));
					throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
				}
				
				listCode.add(lov);
				if (answer.length > 1) {
					listDescription.add(answer[1]);
				}
			}
			
			for (int i=0; i < listCode.size(); i++) {
				if (listCode.get(i) == null) {
					continue;
				}
				
				if (PropertiesHelper.isTaskDJson()) {
					if (i > 0) {
                		jsonIdxAnswer = document.findAnswerIndex(msQuestion.getUuidQuestion());
                	}
                	
					jsonAnswer.setLocation(null);
					jsonAnswer.setLobAnswer(null);
					List<OptionBean> options = (jsonAnswer.getOptAnswers() == null)
							? new ArrayList<OptionBean>() : jsonAnswer.getOptAnswers();
					
					OptionBean option = new OptionBean();
					option.setUuid(listCode.get(i).getUuidLov());
					option.setCode(listCode.get(i).getCode());
					option.setDesc(listCode.get(i).getDescription());
					if (!listDescription.isEmpty()){
						option.setFreeText(StringUtils.stripToNull(listDescription.get(i)));
					}
					if (!options.contains(option)) {
						options.add(option);
					}
					jsonAnswer.setOptAnswers(options);
            		
            		if (jsonIdxAnswer == -1) {
            			jsonAnswers.add(jsonAnswer);
            		}
				}
				else {
					Object[][] paramsTaskD = {
							{"uuidTaskH", taskH.getUuidTaskH()},
							{"uuidQuestion", msQuestion.getUuidQuestion()},
							{"msLovId", listCode.get(i).getUuidLov()}};
					TrTaskD checkboxDetail = this.getDetailByLov(paramsTaskD, auditContext);
					if (checkboxDetail != null) { // update others bcoz checkboxAns.get(k) exist					
						// jika tidak sama, update sendiri
						checkboxDetail.setMsLovByLovId(listCode.get(i));
						checkboxDetail.setOptionText(checkboxDetail.getIntOptionText());
						if (!listDescription.isEmpty())
							checkboxDetail.setTextAnswer(listDescription.get(i));
						this.updateTaskD(checkboxDetail, auditContext);
					}
					else {
						// insert new bcoz checkboxAns.get(k) not exist
						TrTaskD newCheckboxAns = new TrTaskD();
						newCheckboxAns.setTrTaskH(taskH);
						newCheckboxAns.setMsQuestion(msQuestion);
						newCheckboxAns.setQuestionText(questionLabel);
						newCheckboxAns.setMsLovByLovId(listCode.get(i));
						newCheckboxAns.setOptionText(listCode.get(i).getDescription());
						if (!listDescription.isEmpty())
							newCheckboxAns.setTextAnswer(listDescription.get(i));
						this.insertNewDetail(newCheckboxAns, auditContext);
					}
				}
			}

			return;
		}
		
		MsLov msl = null;
		BigDecimal latitude = null, longitude = null;
		
		if ((GlobalVal.ANSWER_TYPE_RADIO.equals(codeAnswerType) || GlobalVal.ANSWER_TYPE_DROPDOWN.equals(codeAnswerType))
				&& StringUtils.isNotBlank(cellAnswer)) {
			msl = this.getLov(msQuestion.getUuidQuestion(), taskDs, cellAnswer, lovGroup, uuidFormHistory, auditContext);
			
			if (null == msl) {
				errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.wrongchoices", 
						null, this.retrieveLocaleAudit(auditContext));
				throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}
			
			cellAnswer = null;
		}
		else if ((GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(codeAnswerType) 
				|| GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(codeAnswerType))
				&& StringUtils.isNotBlank(cellAnswer)) {			
			if (StringUtils.contains(cellAnswer, '^') && StringUtils.countMatches(cellAnswer, "^") != 1) {
				errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.invalidformatlov", 
						null, this.retrieveLocaleAudit(auditContext));
				throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}
			
			String[] selectedAnswer = StringUtils.split(cellAnswer, '^');
			String lovDescription = selectedAnswer[0];
			if (selectedAnswer.length > 1) {
				cellAnswer = selectedAnswer[1];
			}		
			else {
				cellAnswer = null;
			}
			
			msl = this.getLov(msQuestion.getUuidQuestion(), taskDs, lovDescription, lovGroup, uuidFormHistory, auditContext);

			if (null == msl) {
				errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.wrongchoices", 
						null, this.retrieveLocaleAudit(auditContext));
				throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}
		}
		else if ((GlobalVal.ANSWER_TYPE_DATE.equals(codeAnswerType) || GlobalVal.ANSWER_TYPE_DATETIME.equals(codeAnswerType))
				&& StringUtils.isNotBlank(cellAnswer)) {
			String dateFormat = (GlobalVal.ANSWER_TYPE_DATE.equals(codeAnswerType)) ? "dd/MM/yyyy" : "dd/MM/yyyy HH:mm:ss";
			try {
				DateUtils.parseDateStrictly(cellAnswer, dateFormat);
			}
			catch (ParseException e) {
				errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.invalidformatdate", 
						new Object[]{dateFormat}, this.retrieveLocaleAudit(auditContext));
				throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}
		}
		else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(codeAnswerType) && StringUtils.isNotBlank(cellAnswer)) {
			if (StringUtils.countMatches(cellAnswer, ",") != 1) {
				errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.invalidformatlatlong", 
						null, this.retrieveLocaleAudit(auditContext));
				throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}
			String[] points = StringUtils.split(cellAnswer, ',');
			for (String point : points) {
				if (NumberUtils.isNumber(point)) {
					continue;					
				}
				
				errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.invalidnumformatlatlong", 
						null, this.retrieveLocaleAudit(auditContext));
				throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
			}
			
			latitude = NumberUtils.createBigDecimal(points[0]);
			longitude = NumberUtils.createBigDecimal(points[0]);
		}		
		else { //bukan question image atau choice
			if (msQuestion.getMaxLength() != null && cellAnswer != null && msQuestion.getMaxLength() < cellAnswer.length()) {
				errorMsg += this.messageSource.getMessage("service.global.notvalid", 
						new Object[]{"length"}, this.retrieveLocaleAudit(auditContext));
				throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);			
			}

			if (GlobalVal.ANSWER_TYPE_NUMERIC.equals(codeAnswerType) && StringUtils.isNotBlank(cellAnswer)) {
				if (!StringUtils.isNumeric(cellAnswer)) {
					errorMsg += this.messageSource.getMessage("service.global.notvalidnumeric", 
							new Object[]{""}, this.retrieveLocaleAudit(auditContext));
					throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
				}
			}
			else if ((GlobalVal.ANSWER_TYPE_DECIMAL.equals(codeAnswerType) || GlobalVal.ANSWER_TYPE_CURRENCY.equals(codeAnswerType))
					&& StringUtils.isNotBlank(cellAnswer)) {
				if (!NumberUtils.isNumber(cellAnswer)) {
					errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.mustvalidnumber", 
							null, this.retrieveLocaleAudit(auditContext));
					throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
				}
			}
			else if (GlobalVal.ANSWER_TYPE_LU_ONLINE.equals(codeAnswerType) && StringUtils.isNotBlank(cellAnswer)) {
				if (StringUtils.countMatches(cellAnswer, "|") != 1) {
					errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.invalidformatcodedesc", 
							null, this.retrieveLocaleAudit(auditContext));
				}
				
				String code = StringUtils.split(cellAnswer, '|')[0];
				boolean isValid = this.intFormLogic.validateLuOnlineCode(code, lovGroup, auditContext);
				if (!isValid) {
					errorMsg += this.messageSource.getMessage("businesslogic.telesurvey.wrongcodevalue", 
							null, this.retrieveLocaleAudit(auditContext));
					throw new UploadTaskException(errorMsg, UploadTaskException.Reason.TEMPLATE_MISMATCH);
				}
			}
		}
		
		if (PropertiesHelper.isTaskDJson()) {
			jsonAnswer.setTxtAnswer(cellAnswer);
			
    		if (msl != null) {
    			jsonAnswer.setLocation(null);
    			jsonAnswer.setLobAnswer(null);
    			List<OptionBean> options = (jsonAnswer.getOptAnswers() == null)
    					? new ArrayList<OptionBean>() : jsonAnswer.getOptAnswers();
    			
    			OptionBean option = new OptionBean();
    			option.setUuid(msl.getUuidLov());
    			option.setCode(msl.getCode());
    			option.setDesc(msl.getDescription());
    			option.setFreeText(StringUtils.stripToNull(cellAnswer));
    			if (!options.contains(option)) {
    				options.add(option);
    			}
    			jsonAnswer.setOptAnswers(options);
    		}
    		
    		if (latitude != null) {
    			jsonAnswer.setOptAnswers(null);
    			jsonAnswer.setLobAnswer(null);
    			
    			com.adins.mss.model.taskdjson.LocationBean locationBean = (jsonAnswer.getLocation() == null) ?
						new com.adins.mss.model.taskdjson.LocationBean() : jsonAnswer.getLocation();
				if (latitude != null && longitude != null) {
					locationBean.setLat(latitude.doubleValue());
					locationBean.setLng(longitude.doubleValue());
					locationBean.setIsGps(1);
				}
				
				jsonAnswer.setLocation(locationBean);
				jsonAnswer.setTxtAnswer(cellAnswer);
    		}
    		
    		if (jsonIdxAnswer == -1) {
    			jsonAnswers.add(jsonAnswer);
    		}
		}
		else {
			Object[][] pr = {				
					{Restrictions.eq("trTaskH.uuidTaskH", taskH.getUuidTaskH())},
					{Restrictions.eq("msQuestion.uuidQuestion", msQuestion.getUuidQuestion())} };
			TrTaskD tskd = this.getManagerDAO().selectOne(TrTaskD.class, pr);
			boolean isNew = false;
			if (tskd != null) {
				tskd.setUsrUpd(String.valueOf(auditContext.getCallerId()));
				tskd.setDtmUpd(new Date());			
				
				this.getManagerDAO().update(tskd);
			}
			else {
				isNew = true;
				tskd = new TrTaskD();
				tskd.setUsrCrt(String.valueOf(auditContext.getCallerId()));
				tskd.setDtmCrt(new Date());
				tskd.setTrTaskH(taskH);
				tskd.setMsQuestion(msQuestion);
				tskd.setQuestionText(questionLabel);
			}
			
			tskd.setTextAnswer(cellAnswer);
			if (msl != null) {
				tskd.setMsLovByLovId(msl);
				tskd.setOptionText(msl.getDescription());
			}
			if (latitude != null) {
				tskd.setLatitude(latitude);
				tskd.setLongitude(longitude);
			}
			
			if (isNew) {
				this.getManagerDAO().insert(tskd);
			}
			else {
				this.getManagerDAO().update(tskd);		
			}
		}
	}
	
	private AnswerBean transformToJsonByAnswerType(TrTaskH taskH, long uuidFormHistory, MsQuestion quest,
			String textAnswer, String latitude, String longitude,
			TaskDocumentBean document, int idxAnswer, String[] textAnswers,
			ImageStorageLocation isl, Path imgPath, AuditContext auditContext) {
		Assert.notNull(document);
		
		AnswerBean answer = (idxAnswer == -1) ? new AnswerBean() : document.getAnswers().get(idxAnswer);
		QuestionBean questionBean = new QuestionBean(
				quest.getUuidQuestion(), quest.getQuestionLabel(), quest.getRefId(), quest.getMsAnswertype().getCodeAnswerType(),
				(quest.getMsOrdertag() == null ? null : quest.getMsOrdertag().getTagName()),
				(quest.getMsAssettag() == null ? null : quest.getMsAssettag().getAssetTagName()),
				(quest.getMsCollectiontag() == null ? null : quest.getMsCollectiontag().getTagName()));
		answer.setQuestion(questionBean);
		
		String answerType = quest.getMsAnswertype().getCodeAnswerType();
		String lovGroup = quest.getLovGroup();
		
		boolean flagOption = false;
		
		if (GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(answerType)
				|| GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(answerType)) {
			flagOption = true;
			String[] selectedAnswer = textAnswer.replace("#", "").split("\\^");
			if (selectedAnswer.length > 0) {
				List<OptionBean> options = (answer.getOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getOptAnswers();
				
				MsLov lovtmp = this.getLovInJson(quest.getUuidQuestion(), textAnswers,
						selectedAnswer[0], lovGroup, uuidFormHistory, document.getAnswers(), auditContext);
				if (lovtmp != null) {
					OptionBean option = new OptionBean();
					option.setUuid(lovtmp.getUuidLov());
					option.setCode(lovtmp.getCode());
					option.setDesc(lovtmp.getDescription());
					if (selectedAnswer.length > 1) {
						option.setFreeText(selectedAnswer[1]);
					}
					if (!options.contains(option)) {
						options.add(option);
					}
					answer.setOptAnswers(options);
					
				}
			}
		}
		else if (MssTool.isMultipleQuestion(answerType)) {
			flagOption = true;
			String[] multiAnswer = textAnswer.split("#");
			List<MsLov> checkboxAns = new ArrayList<MsLov>();
			List<String> checkboxDescAns = new ArrayList<String>();
			for (int j = 0; j < multiAnswer.length; j++) {
				String[] multiAns = multiAnswer[j].split("\\^");
				MsLov lov = this.getLovInJson(quest.getUuidQuestion(), textAnswers,
						multiAns[0], lovGroup, uuidFormHistory, document.getAnswers(), auditContext);
				if (lov != null)
					checkboxAns.add(lov);
				if (GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
					if (multiAns.length > 1){
						checkboxDescAns.add(multiAns[1]);
					}
					else{
						checkboxDescAns.add(StringUtils.EMPTY);
					}
				}
			}

			// update
			if (checkboxAns.size() > 0) {
				List<OptionBean> options = (answer.getOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getOptAnswers();
				for (int k = 0; k < checkboxAns.size(); k++) {
					if (checkboxAns.get(k) == null) {
						continue;
					}
					
					OptionBean option = new OptionBean();
					option.setUuid(checkboxAns.get(k).getUuidLov());
					option.setCode(checkboxAns.get(k).getCode());
					option.setDesc(checkboxAns.get(k).getDescription());
					if (!checkboxDescAns.isEmpty()) {
						option.setFreeText(checkboxDescAns.get(k));
					}
					if (!options.contains(option)) {
						options.add(option);
					}
					answer.setOptAnswers(options);					
				}
			}
		}
		else if (GlobalVal.ANSWER_TYPE_RADIO.equals(answerType)
					|| GlobalVal.ANSWER_TYPE_DROPDOWN.equals(answerType)) {
			flagOption = true;
			String code = textAnswer.replace("^", "").replace("#", "");
			
			List<OptionBean> options = (answer.getOptAnswers() == null) ? new ArrayList<OptionBean>() : answer.getOptAnswers();
			
			MsLov lov = this.getLovInJson(quest.getUuidQuestion(), textAnswers,
					code, lovGroup, taskH.getMsForm().getUuidForm(), document.getAnswers(), auditContext);
			if (lov != null) {
				OptionBean option = new OptionBean();
				option.setUuid(lov.getUuidLov());
				option.setCode(lov.getCode());
				option.setDesc(lov.getDescription());
				if (!options.contains(option)) {
					options.add(option);
				}
				answer.setOptAnswers(options);
			}
		}
		else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType) && !flagOption) {
			BigDecimal decLatitude = (NumberUtils.isNumber(latitude)) ? new BigDecimal(latitude) : null;
			BigDecimal decLongitude = (NumberUtils.isNumber(longitude)) ? new BigDecimal(longitude) : null;

			if (decLatitude != null) {
				LocationBean locationBean = (answer.getLocation() == null) ? new LocationBean() : answer.getLocation();
				locationBean.setLat(decLatitude.doubleValue());
				locationBean.setLng(decLongitude.doubleValue());
				answer.setLocation(locationBean);
				answer.setTxtAnswer(textAnswer);
			}
		}
		else if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType) && StringUtils.isNotBlank(textAnswer)) {
			answer.setTxtAnswer(textAnswer.replace(".00", "").replace(",", ""));				
		}
		else if (MssTool.isImageQuestion(answerType)) {
			ImageBean imageBean = (answer.getLobAnswer() == null) ? new ImageBean() : answer.getLobAnswer();
			if (!"^".equals(textAnswer)) {
				imageBean.setHasImage(true);				
				TrTaskdetaillob lob = this.saveImageOnly(taskH, quest, textAnswer, isl, new Date(), imgPath, auditContext);
				imageBean.setId(lob.getUuidTaskDetailLob());
				answer.setLobAnswer(imageBean);

				com.adins.mss.model.taskdjson.LocationBean locationBean = (answer.getLocation() == null) ?
						new com.adins.mss.model.taskdjson.LocationBean() : answer.getLocation();				
				
				String[] tmp = textAnswer.split(":\\^:");
				if (!GlobalVal.ANSWER_TYPE_IMAGE.equals(answerType) && tmp.length > 1) {
					locationBean.setLat(new Double(tmp[0].toString()));
					locationBean.setLng(new Double(tmp[1].toString()));
					locationBean.setAccuracy(Integer.parseInt(tmp[2].toString()));
					locationBean.setIsGps(1);
				}
				
				answer.setLocation(locationBean);
			}
		}
		else if (!flagOption) {
			//text,textmultiline,numeric,decimal,date,datetime,text with suggestion,luonline
			answer.setTxtAnswer(StringUtils.stripToNull(textAnswer));
		}
		
		return answer;
	}
	
	/*
	 * choice textAnswer => code^description#code2^description2 
	 */
	private boolean transformDataByAnswerType(TrTaskH taskH, long uuidFormHistory, TrTaskD answerDetail, MsQuestion quest,
			String uuidAnswer, String textAnswer, String latitude, String longitude,
			List<TrTaskD> taskDs, String[] textAnswers, AuditContext auditContext) {
		Assert.notNull(answerDetail);

		answerDetail.setTrTaskH(taskH);
		answerDetail.setMsQuestion(quest);
		answerDetail.setQuestionText(quest.getQuestionLabel());
		answerDetail.setUuidTaskD(uuidAnswer.isEmpty() ? null : Long.valueOf(uuidAnswer)); //Using identity			

		String answerType = answerDetail.getMsQuestion().getMsAnswertype().getCodeAnswerType();
		String lovGroup = answerDetail.getMsQuestion().getLovGroup();
		
		boolean flagOption = false;
		boolean flagNeedUpdateDb = true;
		if (GlobalVal.ANSWER_TYPE_RADIO_WITH_DESCRIPTION.equals(answerType)
				|| GlobalVal.ANSWER_TYPE_DROPDOWN_WITH_DESCRIPTION.equals(answerType)) {
			flagOption = true;
			String[] selectedAnswer = textAnswer.replace("#", "").split("\\^");
			if (selectedAnswer.length > 0) {				
				MsLov lovtmp = this.getLov(answerDetail.getMsQuestion().getUuidQuestion(), textAnswers,
						selectedAnswer[0], lovGroup, uuidFormHistory, taskDs, auditContext);
				answerDetail.setMsLovByLovId(lovtmp);
				answerDetail.setOptionText((lovtmp == null) ? null : lovtmp.getDescription());
				if (selectedAnswer.length > 1) {
					answerDetail.setTextAnswer(selectedAnswer[1]);
				}
			}
		}
		else if (MssTool.isMultipleQuestion(answerType)) {
			flagOption = true;
			String[] multiAnswer = textAnswer.split("#");
			List<MsLov> checkboxAns = new ArrayList<MsLov>();
			List<String> checkboxDescAns = new ArrayList<String>();
			for (int j = 0; j < multiAnswer.length; j++) {
				String[] multiAns = multiAnswer[j].split("\\^");
				MsLov lov = this.getLov(answerDetail.getMsQuestion().getUuidQuestion(), textAnswers,
						multiAns[0], lovGroup, uuidFormHistory, taskDs, auditContext);
				if (lov != null)
					checkboxAns.add(lov);
				if (GlobalVal.ANSWER_TYPE_MULTIPLE_ONE_DESCRIPTION.equals(answerType)
						|| GlobalVal.ANSWER_TYPE_MULTIPLE_WITH_DESCRIPTION.equals(answerType)) {
					if (multiAns.length > 1){
						checkboxDescAns.add(multiAns[1]);
					}
					else{
						checkboxDescAns.add(StringUtils.EMPTY);
					}
				}
			}

			// update
			if (!checkboxAns.isEmpty()) {
				for (int k = 0; k < checkboxAns.size(); k++) {
					if (checkboxAns.get(k) == null) {
						continue;
					}
					
					Object[][] paramsTaskD = {
							{"uuidTaskH", taskH.getUuidTaskH()},
							{"uuidQuestion", answerDetail.getMsQuestion().getUuidQuestion()},
							{"msLovId", checkboxAns.get(k).getUuidLov()} };
					TrTaskD checkboxDetail = this.getDetailByLov(paramsTaskD, auditContext);
					if (checkboxDetail != null) { // update others bcoz checkboxAns.get(k) exist							
						if (String.valueOf(checkboxDetail.getUuidTaskD()).equalsIgnoreCase(uuidAnswer)) {
							// jika sama dengan uuidAnswer, set TaskDetail
							answerDetail.setMsLovByLovId(checkboxAns.get(k));
							answerDetail.setOptionText(checkboxDetail.getIntOptionText());
							if (!checkboxDescAns.isEmpty()){
								answerDetail.setTextAnswer(checkboxDescAns.get(k));
							}
							this.updateTaskD(answerDetail, auditContext);
							flagNeedUpdateDb = false;
						}
						else {
							// jika tidak sama, update sendiri
							checkboxDetail.setMsLovByLovId(checkboxAns.get(k));
							checkboxDetail.setOptionText(checkboxDetail.getIntOptionText());
							if (!checkboxDescAns.isEmpty()){
								checkboxDetail.setTextAnswer(checkboxDescAns.get(k));
							}
							this.updateTaskD(checkboxDetail, auditContext);
							flagNeedUpdateDb = false;
						}
					} 
					else {
						// insert new bcoz checkboxAns.get(k) not exist
						TrTaskD newCheckboxAns = new TrTaskD();
						newCheckboxAns.setTrTaskH(answerDetail.getTrTaskH());
						newCheckboxAns.setMsQuestion(answerDetail.getMsQuestion());
						newCheckboxAns.setQuestionText(answerDetail.getQuestionText());
						newCheckboxAns.setMsLovByLovId(checkboxAns.get(k));
						newCheckboxAns.setOptionText(checkboxAns.get(k).getDescription());
						if (!checkboxDescAns.isEmpty()){
							newCheckboxAns.setTextAnswer(checkboxDescAns.get(k));
						}
						this.insertNewDetail(newCheckboxAns, auditContext);
						flagNeedUpdateDb = false;
					}						
				}
			}
		}
		else if (GlobalVal.ANSWER_TYPE_RADIO.equals(answerType)
				|| GlobalVal.ANSWER_TYPE_DROPDOWN.equals(answerType)) {
			flagOption = true;
			String code = textAnswer.replace("^", "").replace("#", "");
			MsLov lov = this.getLov(answerDetail.getMsQuestion().getUuidQuestion(), textAnswers,
					code, lovGroup, taskH.getMsForm().getUuidForm(), taskDs, auditContext);
			answerDetail.setMsLovByLovId(lov);
			if (lov != null) {
				answerDetail.setOptionText(lov.getDescription());
			}				
		}
		else if (GlobalVal.ANSWER_TYPE_LOCATION.equals(answerType) && !flagOption) {
			BigDecimal decLatitude = (NumberUtils.isNumber(latitude)) ? new BigDecimal(latitude) : null;
			BigDecimal decLongitude = (NumberUtils.isNumber(longitude)) ? new BigDecimal(longitude) : null;

			answerDetail.setLatitude(decLatitude);
			answerDetail.setLongitude(decLongitude);
			answerDetail.setTextAnswer(textAnswer);
		}
		else if (GlobalVal.ANSWER_TYPE_CURRENCY.equals(answerType) && StringUtils.isNotBlank(textAnswer)) {
			answerDetail.setTextAnswer(textAnswer.replace(".00", "").replace(",", ""));				
		}
		else if (answerDetail.getLatitude() == null && answerDetail.getLongitude() == null && !flagOption) {
			//text,textmultiline,numeric,decimal,date,datetime,text with suggestion,luonline
			answerDetail.setTextAnswer(StringUtils.stripToNull(textAnswer));
		}
		
		return flagNeedUpdateDb;
	}
}