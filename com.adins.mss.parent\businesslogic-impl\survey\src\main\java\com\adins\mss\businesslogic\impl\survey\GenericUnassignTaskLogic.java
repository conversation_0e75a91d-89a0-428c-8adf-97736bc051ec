package com.adins.mss.businesslogic.impl.survey;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Stack;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import com.adins.framework.persistence.dao.model.AuditContext;
import com.adins.framework.tool.geolocation.model.LocationBean;
import com.adins.mss.base.businesslogic.BaseLogic;
import com.adins.mss.businesslogic.api.am.GlobalLogic;
import com.adins.mss.businesslogic.api.common.CommonLogic;
import com.adins.mss.businesslogic.api.common.GeolocationLogic;
import com.adins.mss.businesslogic.api.common.TaskDistributionLogic;
import com.adins.mss.businesslogic.api.interfacing.IntFormLogic;
import com.adins.mss.businesslogic.api.survey.UnassignTaskLogic;
import com.adins.mss.constants.GlobalKey;
import com.adins.mss.constants.GlobalVal;
import com.adins.mss.exceptions.ChangeException;
import com.adins.mss.exceptions.RemoteException;
import com.adins.mss.model.AmMssubsystem;
import com.adins.mss.model.AmMsuser;
import com.adins.mss.model.MsBranch;
import com.adins.mss.model.MsForm;
import com.adins.mss.model.MsJob;
import com.adins.mss.model.MsPriority;
import com.adins.mss.model.MsStatusmobile;
import com.adins.mss.model.MsStatustask;
import com.adins.mss.model.TblPoloData;
import com.adins.mss.model.TrTaskH;
import com.adins.mss.model.TrTaskhistory;
import com.adins.mss.services.model.common.AddTaskPoloRequest;
import com.adins.mss.services.model.common.GetReferantorResponse;
import com.adins.mss.services.model.newconfins.UpdateStatusMainDealerResponse;
import com.google.gson.Gson;

@SuppressWarnings({ "unchecked", "rawtypes" })
@Transactional(readOnly=true, isolation=Isolation.READ_UNCOMMITTED)
public class GenericUnassignTaskLogic extends BaseLogic implements
		UnassignTaskLogic, MessageSourceAware {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericUnassignTaskLogic.class);
	
	@Autowired
	private GeolocationLogic geolocationLogic;

	private GlobalLogic globalLogic;
	private CommonLogic commonLogic;
	private TaskDistributionLogic taskDistributionLogic;
	private IntFormLogic intFormLogic;
	
	private Gson gson = new Gson();
	
	public void setIntFormLogic(IntFormLogic intFormLogic) {
		this.intFormLogic = intFormLogic;
	}

	public void setGlobalLogic(GlobalLogic globalLogic) {
		this.globalLogic = globalLogic;
	}

	public void setGeolocationLogic(GeolocationLogic geolocationLogic) {
		this.geolocationLogic = geolocationLogic;
	}
	
	public CommonLogic getCommonLogic() {
		return commonLogic;
	}

	public void setCommonLogic(CommonLogic commonLogic) {
		this.commonLogic = commonLogic;
	}

	public void setTaskDistributionLogic(TaskDistributionLogic taskDistributionLogic) {
		this.taskDistributionLogic = taskDistributionLogic;
	}



	@Autowired
	private MessageSource messageSource;
    
    @Override
	public void setMessageSource(MessageSource messageSource) {
		this.messageSource = messageSource;
	}
    
	@Override
	public Map<String, Object> listTaskH(String mode, AmMsuser amMsuser, Object params,
			Object paramsCnt, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<String, Object>();
//		List<TrTaskH> listTaskHs = new ArrayList<TrTaskH>();
		String uuidStatusTask = this.getUuidStatusTask(
				GlobalVal.SURVEY_STATUS_TASK_UNASSIGN, amMsuser, callerId);
		String[][] prm = (String[][]) params;
		prm[2][1] = uuidStatusTask;
		String[][] prmCnt = (String[][]) paramsCnt;
		prmCnt[2][1] = uuidStatusTask;

		List<Map<String, Object>> resultUnassigned = Collections.EMPTY_LIST;
		Stack<Object[]> paramsStack = new Stack<>();		
		Stack<Object[]> paramsStackCount = new Stack<>();		
		StringBuilder paramsQueryString = new StringBuilder();
		StringBuilder paramsQueryStringCount = new StringBuilder();
		StringBuilder queryJoin = new StringBuilder();
		StringBuilder queryCountJoin = new StringBuilder();
			
		if (mode.equalsIgnoreCase(GlobalVal.MODE_MENU_BY_HIERARKI_BRANCH) || mode.equalsIgnoreCase(GlobalVal.MODE_MENU_BY_BRANCH_PILOTING)) {
			queryJoin.append("LEFT JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchIdLogin)) msb on tth.UUID_BRANCH = msb.UUID_BRANCH ");
			queryCountJoin.append("LEFT JOIN (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:branchIdLogin)) msb on tth.UUID_BRANCH = msb.UUID_BRANCH ");
			paramsStack.push(new Object[]{"branchIdLogin", Long.valueOf(((String[][]) params)[0][1])});
			paramsStackCount.push(new Object[]{"branchIdLogin", Long.valueOf(((String[][]) params)[0][1])});
			if (mode.equalsIgnoreCase(GlobalVal.MODE_MENU_BY_HIERARKI_BRANCH)) {
				paramsQueryString.append("and tth.flag_source = 'MSCORE' ");
				paramsQueryStringCount.append("and tth.flag_source = 'MSCORE' ");
			} else if (mode.equalsIgnoreCase(GlobalVal.MODE_MENU_BY_BRANCH_PILOTING)) {
				paramsQueryString.append("and (FLAG_SOURCE in ('MSCOREP', 'MSCOREF', 'MSIAFOTO') OR (FLAG_SOURCE = 'MSCORE' AND MSBR.IS_PILOTING = '1')) ");
				paramsQueryStringCount.append("and (FLAG_SOURCE in ('MSCOREP', 'MSCOREF') OR (FLAG_SOURCE = 'MSCORE' AND MSBR.IS_PILOTING = '1')) ");
			}
		} else {
			paramsQueryString=this.sqlPagingBuilder((Object[][]) params, paramsStack, callerId);
			//paramsQueryString.append("and tth.uuid_branch = :uuidBranch ");
			//paramsStack.push(new Object[]{"uuidBranch", ((Object[][]) params)[1][1]});
			paramsQueryStringCount = this.sqlPagingBuilder((Object[][]) params, paramsStackCount, callerId);
			//paramsQueryStringCount.append("and tth.uuid_branch = :uuidBranch ");
			//paramsStackCount.push(new Object[]{"uuidBranch", ((Object[][]) params)[1][1]});
			if (mode.equalsIgnoreCase(GlobalVal.ACTION_NAME_DEFAULT_URI_LIST)) {
				paramsQueryString.append("and tth.flag_source = 'MSCORE' ");
				paramsQueryStringCount.append("and tth.flag_source = 'MSCORE' ");
			} else if (mode.equalsIgnoreCase(GlobalVal.MODE_MENU_BY_USER_PILOTING)) {
				paramsQueryString.append("and (FLAG_SOURCE in ('MSCOREP', 'MSCOREF') OR (FLAG_SOURCE = 'MSCORE' AND MSBR.IS_PILOTING = '1')) ");
				paramsQueryStringCount.append("and (FLAG_SOURCE in ('MSCOREP', 'MSCOREF') OR (FLAG_SOURCE = 'MSCORE' AND MSBR.IS_PILOTING = '1')) ");
			}
		}
		
		queryJoin.append(" LEFT JOIN MS_GROUPTASK msg WITH(NOLOCK) on msg.UUID_TASK_H = tth.UUID_TASK_H ");
		queryJoin.append(" LEFT JOIN TBL_POLO_DATA tblPolo WITH(NOLOCK) on msg.GROUP_TASK_ID = tblPolo.GROUP_TASK_ID ");
		
		StringBuilder queryBuilder = new StringBuilder()
			.append("SELECT * from ( ")
			.append("SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM ( ")
			.append("select DISTINCT *, ROW_NUMBER() OVER (ORDER BY task_id) AS rownum from ")
			.append("(select tth.UUID_TASK_H, tth.TASK_ID, tth.APPL_NO, tth.CUSTOMER_NAME, tth.CUSTOMER_ADDRESS, ")
			.append("tth.CUSTOMER_PHONE, tth.ZIP_CODE, tth.UUID_PRIORITY, msbr.KONVEN_SYARIAH, CONVERT(VARCHAR,tth.assign_date,120)assign_date, CONVERT(VARCHAR,ISNULL(tth.DTM_UPD,tth.assign_date),120)DTM_UPD, ")
			.append("FORM_NAME, isnull(tth.IS_PILOTING_CAE, '') as ispilotingcae ");
		
		queryBuilder.append(", isnull(JSON_VALUE(tblPolo.JSON_REQUEST, '$.jenisTask'), '') as jenisTask ");
		queryBuilder.append("from tr_task_h tth with (nolock) ")
			.append("JOIN MS_BRANCH MSBR WITH (NOLOCK) ON MSBR.UUID_BRANCH = TTH.UUID_BRANCH ")
			.append("JOIN MS_FORM MF WITH (NOLOCK) ON MF.UUID_FORM = TTH.UUID_FORM ")
			.append(queryJoin)
			.append("where uuid_status_task = :uuidStatusTask ")
			.append(paramsQueryString)
			.append("and tth.customer_name like '%' + :customerName + '%' ")
			.append("and tth.customer_address like '%' + :customerAddress + '%' ")
			.append("and MF.UUID_FORM = :uuidForm ")
			.append("and (ISNULL(tth.appl_no, '%') like '%' + :applNo + '%')) as UT ")
			.append(") a WHERE a.rownum <= :end ")
			.append(") b WHERE b.recnum >= :start ");
		
		paramsStack.push(new Object[]{"uuidStatusTask", ((Object[][]) params)[2][1]});
		paramsStack.push(new Object[]{"customerName", ((Object[][]) params)[3][1]});
		paramsStack.push(new Object[]{"customerAddress", ((Object[][]) params)[4][1]});
		paramsStack.push(new Object[]{"uuidForm", ((Object[][]) params)[8][1]});
		paramsStack.push(new Object[]{"applNo", ((Object[][]) params)[5][1]});
		paramsStack.push(new Object[]{"start", ((Object[][]) params)[6][1]});
		paramsStack.push(new Object[]{"end", ((Object[][]) params)[7][1]});
		Object[][] sqlParams = new Object[paramsStack.size()][2];
	    for (int i = 0; i < paramsStack.size(); i++) {
			Object[] objects = paramsStack.get(i);
			sqlParams[i] = objects;
		}
	    resultUnassigned = this.getManagerDAO().selectAllNativeString(queryBuilder.toString(), sqlParams);
		
		StringBuilder queryBuilderCount = new StringBuilder()
			.append("select count(1) from ")
			.append("(select DISTINCT *, ROW_NUMBER() OVER (ORDER BY task_id) AS rownum from ")
			.append("(select tth.* from tr_task_h tth with (nolock) ")
			.append("JOIN MS_BRANCH MSBR WITH (NOLOCK) ON MSBR.UUID_BRANCH = TTH.UUID_BRANCH ")
			.append("JOIN MS_FORM MF WITH (NOLOCK) ON MF.UUID_FORM = TTH.UUID_FORM ")
			.append(queryCountJoin)
			.append("where uuid_status_task = :uuidStatusTask ")
			.append(paramsQueryStringCount)
			.append("and tth.customer_name like '%' + :customerName + '%' ")
			.append("and tth.customer_address like '%' + :customerAddress + '%' ")
			.append("and MF.UUID_FORM = :uuidForm ")
			.append("and (ISNULL(tth.appl_no, '%') like '%' + :applNo + '%')) as UT2 ) as UT ");
		
		paramsStackCount.push(new Object[]{"uuidStatusTask", ((Object[][]) params)[2][1]});
		paramsStackCount.push(new Object[]{"customerName", ((Object[][]) params)[3][1]});
		paramsStackCount.push(new Object[]{"customerAddress", ((Object[][]) params)[4][1]});
		paramsStackCount.push(new Object[]{"applNo", ((Object[][]) params)[5][1]});
		paramsStackCount.push(new Object[]{"uuidForm", ((Object[][]) params)[8][1]});
		
		Object[][] sqlParamsCount = new Object[paramsStackCount.size()][2];
	    for (int i = 0; i < paramsStackCount.size(); i++) {
			Object[] objects = paramsStackCount.get(i);
			sqlParamsCount[i] = objects;
		}
		
	    Integer resultUnassignedCnt = (Integer) this.getManagerDAO().selectOneNativeString(queryBuilderCount.toString(), sqlParamsCount);
		
		result.put(GlobalKey.MAP_RESULT_LIST, resultUnassigned);
		result.put(GlobalKey.MAP_RESULT_SIZE, resultUnassignedCnt);
		
		return result;
	}
	
	private StringBuilder sqlPagingBuilder(Object[][] params, Stack<Object[]> paramStack, AuditContext callerId) {
		if (params == null)
			return new StringBuilder();
		StringBuilder sb = new StringBuilder();
		//---UUID USER
		if (!StringUtils.equals("%", (String) params[0][1])) {
			sb.append("and uuid_ms_user in (SELECT keyValue as UUID_MS_USER FROM dbo.getUserByLogin(:uuidMsUser)) ");
			paramStack.push(new Object[]{"uuidMsUser", Long.valueOf((String) params[0][1])});
		}
		return sb;
	}

	@Override
	public Map<String, Object> listTaskToAssign(String[] selectedTask,
			AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<String, Object>();
		List<TrTaskH> listTaskHs = new ArrayList<TrTaskH>();
		String[] tasks = selectedTask[0].split(",");
		for (int i = 0; i < tasks.length; i++) {
			TrTaskH trTaskH = this.getManagerDAO().selectOne(TrTaskH.class,
					Long.valueOf(tasks[i]));
			this.getManagerDAO().fetch(trTaskH.getMsBranch());
			listTaskHs.add(trTaskH);
		}
		result.put(GlobalKey.MAP_RESULT_LIST, listTaskHs);
		result.put(GlobalKey.MAP_RESULT_SIZE, listTaskHs.size());

		return result;
	}

	@Override
	public Map<String, Object> listUser(String mode, AmMsuser amMsuser, AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<String, Object>();
		Map<String, Object> resultUser = new HashMap<String, Object>();

		if (GlobalVal.JOB_ADMIN.equals(amMsuser.getMsJob().getJobCode())) {
			String svyJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, callerId);
            MsJob job = globalLogic.getMsJob(svyJobCode, callerId);
            
			Object[][] params = { { Restrictions.eq("msJob.uuidJob", job.getUuidJob()) },
									{Restrictions.eq("isActive", "1")}, {Restrictions.eq("isDeleted", "0")}};
			String[][] orders = { { "fullName", "ASC" } };
			resultUser = this.getManagerDAO().selectAll(AmMsuser.class,
					params, orders);
			
			resultUser.put(GlobalKey.MAP_RESULT_SIZE, ((List)resultUser.get(GlobalKey.MAP_RESULT_LIST)).size());
		} 
		else {		
			String[][] users = null;
			String poloJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVYPOLO, callerId);
			String listPoloJobCode[] = StringUtils.split(poloJobCode, ";");
			String svyJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, null);
			
			List listJobCode = new ArrayList();
			listJobCode.add(svyJobCode);
			for(int i=0; i<listPoloJobCode.length; i++) {
				listJobCode.add(listPoloJobCode[i]);
			}
			
			List<AmMsuser> listUser = new ArrayList<AmMsuser>();
			Iterator iter = listJobCode.iterator();
			while(iter.hasNext()) {
				String job = (String) iter.next();
				
				if (mode.equalsIgnoreCase(GlobalVal.MODE_MENU_BY_HIERARKI_BRANCH) || mode.equalsIgnoreCase(GlobalVal.MODE_MENU_BY_BRANCH_PILOTING)){
					users= getUserByBranch(String.valueOf(amMsuser.getMsBranch().getUuidBranch()), job, callerId);
				}
				else{
					users= getUserByLogin(String.valueOf(amMsuser.getUuidMsUser()), job, callerId);
				}

				if (users != null) {
					for (int i = 0; i < users.length; i++) {
						if (!"1".equals(users[i][3])) {
							if (null != users[i][0]) {
								AmMsuser bean = new AmMsuser();
								bean.setUuidMsUser(Long.valueOf(users[i][0]));
								bean.setLoginId(users[i][1]);
								bean.setFullName(users[i][2] + " - " + job);
								listUser.add(bean);
							}
						}
					}
				}
			}
			
			resultUser.put(GlobalKey.MAP_RESULT_LIST, listUser);
			resultUser.put(GlobalKey.MAP_RESULT_SIZE, listUser.size());
		}
		Integer[][] assignment = this
				.getAssignment((List<AmMsuser>) resultUser
						.get(GlobalKey.MAP_RESULT_LIST), callerId);
		String[][] lastLoc = this.getLastLoc((List<AmMsuser>) resultUser
				.get(GlobalKey.MAP_RESULT_LIST), callerId);
		
		List<AmMsuser> listUserSort = (List<AmMsuser>) resultUser.get(GlobalKey.MAP_RESULT_LIST);
		listUserSort.sort(Comparator.comparing(AmMsuser::getFullName));
		resultUser.put(GlobalKey.MAP_RESULT_LIST, listUserSort);
		
		result.put("result", resultUser);
		result.put("assignment", assignment);
		result.put("location", lastLoc);

		return result;
	}

	@Override
	public AmMsuser getUser(long uuidMsUser, AuditContext callerId) {

		AmMsuser amMsuser = (AmMsuser) this.getManagerDAO().selectOne(
				"from AmMsuser u join fetch u.msBranch where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", uuidMsUser}});

		return amMsuser;
	}

	@Override
	public Integer[][] getAssignment(List<AmMsuser> listResult, AuditContext callerId) {
		Integer[][] result = new Integer[listResult.size()][3];
		String currentDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
		for (int i = 0; i < listResult.size(); i++) {
			AmMsuser usr = this.getManagerDAO().selectOne(
					"from AmMsuser u join fetch u.amMssubsystem where u.uuidMsUser = :uuidMsUser", new Object[][] {{"uuidMsUser", listResult.get(i).getUuidMsUser()}});
			String[][] params = { { "uuidMsUser", String.valueOf(usr.getUuidMsUser()) },
					{ "start", currentDate + " 00:00:00.000" },
					{ "end", currentDate + " 23:59:59.997" } };
			Integer taskAssignment = (Integer) this.getManagerDAO()
					.selectOneNative(
							"task.unassigntask.getTotalTaskAssignment", params);
			Integer submittedTask = (Integer) this.getManagerDAO()
					.selectOneNative("task.unassigntask.getTotalSubmittedTask",
							params);
			Object[][] paramsStatus = {
					{ Restrictions.eq("statusCode",
							GlobalVal.SURVEY_STATUS_TASK_DISTRIBUTION) },
					{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
							usr.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(
					MsStatustask.class, paramsStatus);
			String[][] paramsPending = { { "uuidMsUser", String.valueOf(usr.getUuidMsUser()) },
					{ "uuidStatusTask", String.valueOf(msStatustask.getUuidStatusTask()) } };
			Integer pendingTask = (Integer) this.getManagerDAO()
					.selectOneNative("task.unassigntask.getTotalPendingTask",
							paramsPending);
			result[i][0] = taskAssignment;
			result[i][1] = submittedTask;
			result[i][2] = pendingTask;
		}
		return result;
	}

	@Override
	public String[][] getLastLoc(List<AmMsuser> listResult, AuditContext callerId) {
		String[][] result = new String[listResult.size()][2];
		for (int i = 0; i < listResult.size(); i++) {
			String[][] params = { { "uuidMsUser",
				String.valueOf(listResult.get(i).getUuidMsUser()) } };
			Object[] lastLoc = (Object[]) this.getManagerDAO().selectOneNative(
					"task.unassigntask.getLastLoc", params);
			if (null != lastLoc) {

				BigDecimal lat = new BigDecimal(lastLoc[4].toString());
				BigDecimal lng = new BigDecimal(lastLoc[5].toString());
				Integer mcc = Integer.parseInt(lastLoc[7].toString());
				Integer mnc = Integer.parseInt(lastLoc[8].toString());
				Integer lac = Integer.parseInt(lastLoc[9].toString());
				Integer cellId = Integer.parseInt(lastLoc[10].toString());

				if (null != lat && null != lng) {
					if (0 == lat.compareTo((BigDecimal.ZERO))
							&& 0 == lng.compareTo(BigDecimal.ZERO)) {
						lat = null;
						lng = null;
						if (null != mcc && null != mnc && null != lac
								&& null != cellId) {
							List<LocationBean> listLocation = new ArrayList<LocationBean>();
							LocationBean locationBean = new LocationBean();
							locationBean.setCellid(cellId);
							locationBean.setLac(lac);
							locationBean.setMcc(mcc);
							locationBean.setMnc(mnc);
							listLocation.add(locationBean);
							this.geolocationLogic.geocodeCellId(listLocation,
									null);
							if (null != listLocation.get(0).getCoordinate()) {
								result[i][0] = new BigDecimal(locationBean
										.getCoordinate().getLatitude())
										.toString();
								result[i][1] = new BigDecimal(locationBean
										.getCoordinate().getLongitude())
										.toString();
							}
						}
					} 
					else {
						result[i][0] = lat.toString();
						result[i][1] = lng.toString();
					}
				}
			}			
			//2015-11-26 SUM will show no location on maps instead of latLong of area, misleading information
			else {
			    result[i][0] = null;
			    result[i][1] = null;
			}
		}
		return result;
	}
	
	private String[][] getUserByBranch(String uuidBranch, String svyJobCode, AuditContext callerId) {
		Object[][] params = { { "uuidBranch", Long.valueOf(uuidBranch) }, 
			{ "jobCode", svyJobCode } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getUserByBranch", params, null);
		if (!list.isEmpty()) {
			String[][] stringResult = new String[list.size()][4];
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				stringResult[i][0] = map.get("d0").toString();
				stringResult[i][1] = (String) map.get("d1");
				stringResult[i][2] = (String) map.get("d2");
				stringResult[i][3] = ((Integer) map.get("d3")).toString();
			}
			return stringResult;
		} 
		else {
			return null;
		}

	}

	private String[][] getUserByLogin(String uuidUser, String svyJobCode, AuditContext callerId) {
		String[][] params = { { "uuidMsUser", uuidUser } };
		List<Map<String, Object>> list = this.getManagerDAO().selectAllNative(
				"am.usermanagement.getUserByLogin", params, null);
		if (!list.isEmpty()) {
			String[][] stringResult = new String[list.size()][4];
			for (int i = 0; i < list.size(); i++) {
				Map<String, Object> map = list.get(i);
				String jobCode = (String) map.get("d5");
				if (jobCode.equals(svyJobCode)) {
					stringResult[i][0] = map.get("d0").toString();
					stringResult[i][1] = (String) map.get("d1");
					stringResult[i][2] = (String) map.get("d2");
					stringResult[i][3] = ((Integer) map.get("d3")).toString();
				}			
			}
			return stringResult;
		} 
		else {
			return null;
		}
	}

	public String getUuidStatusTask(String statusCode, AmMsuser loginBean, AuditContext callerId) {
		String uuidStatusTask = StringUtils.EMPTY;
		Object[][] params = {
				{ Restrictions.eq("statusCode", statusCode) },
				{ Restrictions.eq("amMssubsystem.uuidMsSubsystem",
						loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
		MsStatustask msStatustask = (MsStatustask) this.getManagerDAO()
				.selectOne(MsStatustask.class, params);
		uuidStatusTask = String.valueOf(msStatustask.getUuidStatusTask());
		return uuidStatusTask;
	}

	@Transactional(isolation=Isolation.READ_UNCOMMITTED)
	@Override
	public String assignTask(String[] selectedTask, long uuidMsUser,
			AmMsuser loginBean, long uuidPriority, AuditContext callerId) {

		MsPriority msPriority = this.getManagerDAO().selectOne(MsPriority.class, new Object [][] { { Restrictions.eq("uuidPriority", uuidPriority) } });
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, new Object [][] { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_NEW) } });
		
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, new Object [][] { { Restrictions.eq("uuidMsUser", uuidMsUser) } });
		this.getManagerDAO().fetch(amMsuser.getMsBranch());
		MsBranch msBranch = amMsuser.getMsBranch();
		
		String[] tasks = selectedTask[0].split(",");
		for (int i = 0; i < tasks.length; i++) {
			TrTaskH trTaskH = this.getManagerDAO().selectOne("from TrTaskH tth "
					+ "join fetch tth.msBranch msb "
					+ "join fetch tth.msStatustask mst "
					+ "join fetch mst.amMssubsystem ams "
					+ "where tth.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", Long.valueOf(tasks[i])}});
			if (!msBranch.getKonvenSyariah().equalsIgnoreCase(trTaskH.getMsBranch().getKonvenSyariah())) {
				throw new ChangeException("Task branch type is not same as user branch type", msBranch.getKonvenSyariah());
			}
			
			MsStatustask sts = this.getManagerDAO().selectOne(MsStatustask.class,
					new Object [][] { { Restrictions.eq("uuidStatusTask", trTaskH.getMsStatustask().getUuidStatusTask()) } });
			if (!GlobalVal.SURVEY_STATUS_TASK_UNASSIGN.equals(sts
					.getStatusCode())){
				throw new ChangeException(
						this.messageSource.getMessage("businesslogic.error.changeexception", 
								null, this.retrieveLocaleAudit(callerId)),
						sts.getStatusCode());
			}
			
			/*Cek flagging piloting cae pada task dan user yang diassign match*/
			String isTaskPilotingCae = trTaskH.getIsPilotingCae();
			if (StringUtils.isBlank(isTaskPilotingCae)) {
				isTaskPilotingCae = "0";
			}
			
			String isUserPilotingCae = amMsuser.getMsBranch().getIsPilotingCAE();
			if (StringUtils.isBlank(isUserPilotingCae)) {
				isUserPilotingCae = "0";
			}
			
			if (!isTaskPilotingCae.equalsIgnoreCase(isUserPilotingCae)) {
				//droptask
				MsStatustask statustaskDelete = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_DELETED, sts.getAmMssubsystem().getUuidMsSubsystem(), callerId);
				trTaskH.setMsStatustask(statustaskDelete);
				trTaskH.setUsrUpd(callerId.getCallerId());
				trTaskH.setDtmUpd(new Date());
				this.getManagerDAO().update(trTaskH);
				
				// INSERT INTO TR TASK HISTORY
				String notesDelete = this.messageSource.getMessage("businesslogic.notes.pilotingcae.nomatch", null, 
									this.retrieveLocaleAudit(callerId));
				
				TrTaskhistory trTaskHistory = new TrTaskhistory(
						trTaskH.getMsStatustask(), trTaskH,
						loginBean.getFullName(), new Date(),
						notesDelete, null,
						amMsuser.getFullName(),
						GlobalVal.CODE_PROCESS_DELETED);
				this.getManagerDAO().insert(trTaskHistory);		
				return "Flagging Piloting CAE Doesn't Match between Task ( "+trTaskH.getApplNo()+" ) and Branch User. Task have been drop. )";
			}
			/*End Flagging cae*/

			trTaskH.setAmMsuser(amMsuser);
			trTaskH.setMsBranch(msBranch);
			trTaskH.setAssignDate(new Date());
			trTaskH.setMsPriority(msPriority);
			trTaskH.setDtmUpd(new Date());
			trTaskH.setUsrCrt(String.valueOf(loginBean.getUuidMsUser()));
			trTaskH.setMsStatusmobile(msm);
			trTaskH.setIsUnassign("0");
			this.getManagerDAO().update(trTaskH);

			long uuidProcess = this.getUuidProcess(trTaskH, trTaskH.getMsStatustask().getAmMssubsystem());
			String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess, trTaskH.getUuidTaskH());
			Object[][] params2 = {
					{ Restrictions.eq("statusCode", statusCode) },
					{Restrictions.eq("amMssubsystem.uuidMsSubsystem", loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
			MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
			trTaskH.setMsStatustask(msStatustask);

			TrTaskhistory trTaskhistory = new TrTaskhistory();
			trTaskhistory.setCodeProcess(GlobalVal.CODE_PROCESS_ASSIGNMENT);
			trTaskhistory.setActor(loginBean.getFullName());
			trTaskhistory.setFieldPerson(amMsuser.getFullName());
			trTaskhistory.setMsStatustask(msStatustask);
			trTaskhistory.setTrTaskH(trTaskH);
			trTaskhistory.setNotes(trTaskH.getNotes());
			trTaskhistory.setUsrCrt(String.valueOf(loginBean.getUuidMsUser()));
			trTaskhistory.setDtmCrt(new Date());

			this.getManagerDAO().update(trTaskH);

			if (StringUtils.isNotBlank(trTaskH.getOrderId())) {
				UpdateStatusMainDealerResponse updStatusTaskMainDealer = this.intFormLogic.updateStatusTaskMainDealer(trTaskH, callerId);
				if (!"00".equals(updStatusTaskMainDealer.getResponseCode())) {
					throw new RemoteException(updStatusTaskMainDealer.getResponseMessage());
				}
			}
			
			this.getManagerDAO().insert(trTaskhistory);

			if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
				commitOrder(loginBean, trTaskH.getNotes(), trTaskH, trTaskH
						.getAmMsuser().getAmMssubsystem(), 0,
						GlobalVal.CODE_PROCESS_ASSIGNMENT, callerId);
			}
		}
		return null;
	}

	@Override
	public List getPriorityList(Object params, Object order,
			AuditContext callerId) {
		
		 List result = this.getManagerDAO().selectAllNative(
					"task.inquirytasksurvey.priorityList", params, order);

		return result;
	}

	@Override
	public List getSpvList(String mode, Object params, Object order,
			AuditContext callerId) {
		List result = null;
		
		result = this.getManagerDAO().selectAllNative(
					"task.unassigntask.getSpvList", params, order);

		return result;
	}
	
	@Transactional
	@Override
	public String assignTaskpiloting(String[] selectedTask, long uuidMsUser,
			AmMsuser loginBean, long uuidPriority, AuditContext callerId) {
		MsPriority msPriority = this.getManagerDAO().selectOne(MsPriority.class, new Object [][] { { Restrictions.eq("uuidPriority", uuidPriority) } });
		MsStatusmobile msm = this.getManagerDAO().selectOne(MsStatusmobile.class, new Object [][] { { Restrictions.eq("statusMobileCode", GlobalVal.STATUS_MOBILE_NEW) } });
		
		AmMsuser amMsuser = this.getManagerDAO().selectOne(AmMsuser.class, new Object [][] { { Restrictions.eq("uuidMsUser", uuidMsUser) } });
		this.getManagerDAO().fetch(amMsuser.getMsBranch());
		MsBranch msBranch = amMsuser.getMsBranch();
		
		AmMssubsystem subsystem = commonLogic.retrieveSubsystemByName(GlobalVal.SUBSYSTEM_MS, callerId);
		MsStatustask unassign = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_UNASSIGN, subsystem.getUuidMsSubsystem(), callerId);
		
		String[] tasks = selectedTask[0].split(",");
		
		for (int i = 0; i < tasks.length; i++) {
			TrTaskH taskH = this.getManagerDAO().selectOne("from TrTaskH tth "
					+ "join fetch tth.msBranch msb "
					+ "join fetch tth.msStatustask mst "
					+ "join fetch mst.amMssubsystem ams "
					+ "where tth.uuidTaskH = :uuidTaskH", new Object[][] {{"uuidTaskH", Long.valueOf(tasks[i])}});
			if (!msBranch.getKonvenSyariah().equalsIgnoreCase(taskH.getMsBranch().getKonvenSyariah())) {
				throw new ChangeException("Task branch type is not same as user branch type", msBranch.getKonvenSyariah());
			}
			
			String[][] Orders = {{"uuidTaskH","ASC"}};
			Map listTask = this.getManagerDAO().selectAll(TrTaskH.class,
					new Object[][] { { Restrictions.eq("applNo", taskH.getApplNo()) }, {Restrictions.eq("msStatustask.uuidStatusTask", unassign.getUuidStatusTask())} }, Orders);
			List listTrTaskH = (List) listTask.get("resultList");
			
			TrTaskH taskPts = null;
			TrTaskH taskPreSurvey = null;
			List<TrTaskH> otherTaskPts = new ArrayList<>();
			boolean isDropByPilotingCae = false;
			for(int j = 0; j < listTrTaskH.size(); j++) {
				TrTaskH trTaskH = (TrTaskH) listTrTaskH.get(j);
				this.getManagerDAO().fetch(trTaskH.getMsForm());
				if (GlobalVal.FORM_PROMISE_TO_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName()) || 
						GlobalVal.FORM_PROMISE_TO_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
					taskPts = trTaskH;
				} else {
					if (GlobalVal.FORM_PRE_SURVEY.equalsIgnoreCase(trTaskH.getMsForm().getFormName()) || 
							GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
						taskPreSurvey = trTaskH;
					}
					// Penjagaan untuk exclude task OTS
					if (GlobalVal.FORM_OTS.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) { 
						continue;
					}
					else if (!GlobalVal.FORM_OTS.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) { 
						otherTaskPts.add(trTaskH);						
					}
				}
				
				MsStatustask sts = this.getManagerDAO().selectOne(MsStatustask.class,
						new Object [][] { { Restrictions.eq("uuidStatusTask", trTaskH.getMsStatustask().getUuidStatusTask()) } });
				if (!GlobalVal.SURVEY_STATUS_TASK_UNASSIGN.equals(sts.getStatusCode())){
					throw new ChangeException(
							this.messageSource.getMessage("businesslogic.error.changeexception", 
									null, this.retrieveLocaleAudit(callerId)),
							sts.getStatusCode());
				}
				
				if (GlobalVal.MSCOREF.equalsIgnoreCase(trTaskH.getFlagSource()) 
						|| GlobalVal.MSCOREP.equalsIgnoreCase(trTaskH.getFlagSource())
						|| GlobalVal.MSIAFOTO.equalsIgnoreCase(trTaskH.getFlagSource())) {
					/*Cek flagging piloting cae pada task dan user yang diassign match*/
					String isTaskPilotingCae = taskH.getIsPilotingCae();
					if (StringUtils.isBlank(isTaskPilotingCae)) {
						isTaskPilotingCae = "0";
					}
					
					String isUserPilotingCae = amMsuser.getMsBranch().getIsPilotingCAE();
					if (StringUtils.isBlank(isUserPilotingCae)) {
						isUserPilotingCae = "0";
					}
					/*End Flagging cae*/
					/*Cek flagging piloting cae pada task dan user yang diassign match*/
					if (!isTaskPilotingCae.equalsIgnoreCase(isUserPilotingCae)) {
						//droptask
						MsStatustask statustaskDelete = commonLogic.retrieveStatusTask(GlobalVal.SURVEY_STATUS_TASK_DELETED, subsystem.getUuidMsSubsystem(), callerId);
						trTaskH.setMsStatustask(statustaskDelete);
						trTaskH.setUsrUpd(callerId.getCallerId());
						trTaskH.setDtmUpd(new Date());
						this.getManagerDAO().update(trTaskH);
						
						// INSERT INTO TR TASK HISTORY
						String notesDelete = this.messageSource.getMessage("businesslogic.notes.pilotingcae.nomatch", null, 
											this.retrieveLocaleAudit(callerId));
						
						TrTaskhistory trTaskHistory = new TrTaskhistory(
								trTaskH.getMsStatustask(), trTaskH,
								loginBean.getFullName(), new Date(),
								notesDelete, null,
								amMsuser.getFullName(),
								GlobalVal.CODE_PROCESS_DELETED);
						this.getManagerDAO().insert(trTaskHistory);		
						isDropByPilotingCae = true;
						continue;
					}
					/*End Flagging cae*/
				}

				trTaskH.setAmMsuser(amMsuser);
				trTaskH.setMsBranch(msBranch);
				trTaskH.setAssignDate(new Date());
				trTaskH.setMsPriority(msPriority);
				trTaskH.setDtmUpd(new Date());
				trTaskH.setUsrUpd(String.valueOf(loginBean.getUuidMsUser()));
				trTaskH.setMsStatusmobile(msm);
				trTaskH.setIsUnassign("0");
				this.getManagerDAO().update(trTaskH);
				
				
				//hit updatePolo
				String kodeRef = "";
				String namaRef = "";
			  	if (StringUtils.isNotBlank(trTaskH.getTaskIdPolo())) {
			  		StringBuilder queryGroupTask = new StringBuilder(" SELECT GROUP_TASK_ID ")
							.append(" FROM MS_GROUPTASK WITH(NOLOCK) ")
							.append(" WHERE UUID_TASK_H = :uuidTaskH ");
				  	
				  	Object[][] paramGroupTask = {{"uuidTaskH", trTaskH.getUuidTaskH()}};
					BigInteger groupTaskId = (BigInteger) this.getManagerDAO().selectOneNativeString(queryGroupTask.toString(), paramGroupTask);
					
					Object[][] paramPoloData = {{Restrictions.eq("taskIdPolo",trTaskH.getTaskIdPolo())}, {Restrictions.eq("isSuccess","1")},
							{Restrictions.eq("groupTaskId", Long.valueOf(String.valueOf(groupTaskId.toString())))}};
					
					TblPoloData poloData = this.getManagerDAO().selectOne(TblPoloData.class, paramPoloData);
					AddTaskPoloRequest requestTaskPolo = gson.fromJson(poloData.getJsonRequest(), AddTaskPoloRequest.class);
					
					if (requestTaskPolo.getNamaTask().equals("Penawaran")) {
						kodeRef = requestTaskPolo.getKodeReferantor();
						namaRef = requestTaskPolo.getNamaReferantor();
					}
			  	}
			  	
			  	intFormLogic.updateDataPolo(null, trTaskH, null, "Pending", "F", null, null, null, null, callerId);
				
				//start set default answer untuk referantor task survey
			  	if ("1".equals(trTaskH.getIsPilotingCae()) && StringUtils.isNotBlank(trTaskH.getTaskIdPolo())) {		  		
			  		GetReferantorResponse referantorDetail = intFormLogic.getReferantorCodeFromNc(amMsuser.getUniqueId(), callerId);
			  		
			  		taskDistributionLogic.updateReferantor(referantorDetail.getReferantorNo(), GlobalVal.REFERANTOR1_CODE_CAE, trTaskH.getUuidTaskH(), callerId);
					taskDistributionLogic.updateReferantor(referantorDetail.getReferantorName(), GlobalVal.REFERANTOR1_NAME_CAE, trTaskH.getUuidTaskH(), callerId);
			  	} else {
			  		if(GlobalVal.MSCOREP.equalsIgnoreCase(trTaskH.getFlagSource())) {
						if (StringUtils.isBlank(trTaskH.getTaskIdPolo())) {
							taskDistributionLogic.updateReferantor(amMsuser.getUniqueId(), GlobalVal.REFERANTOR1_CODE, trTaskH.getUuidTaskH(), callerId);
							taskDistributionLogic.updateReferantor(amMsuser.getFullName(), GlobalVal.REFERANTOR1_NAME, trTaskH.getUuidTaskH(), callerId);			
						}
					}
			  	}
				//end set default answer untuk referantor task survey
				
				//start set default answer untuk referantor task visit
			    //2022-12-23 - referantor 1 diset dengan user assign
				if(GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) {
					if(!GlobalVal.SOURCE_DATA_THIRDPARTY.equalsIgnoreCase(trTaskH.getSourceData())) {
						GetReferantorResponse referantorDetail = intFormLogic.getReferantorCodeFromNc(amMsuser.getUniqueId(), callerId);
						
						if(referantorDetail != null) {
							taskDistributionLogic.updateReferantor(referantorDetail.getReferantorNo(), GlobalVal.REF_STV_REFCODE, trTaskH.getUuidTaskH(), callerId);
							taskDistributionLogic.updateReferantor(referantorDetail.getReferantorName(), GlobalVal.REF_STV_REFNAME, trTaskH.getUuidTaskH(), callerId);			
						}						
					}
					
					if (null != amMsuser.getAmMsuser()) {
						taskDistributionLogic.updateReferantor(retrieveSpvCro(String.valueOf(amMsuser.getAmMsuser().getUuidMsUser())),
								GlobalVal.REF_STV_SPV, trTaskH.getUuidTaskH(), callerId);
					}
				}
				//end set default answer untuk referantor task visit

				long uuidProcess = this.getUuidProcess(trTaskH, trTaskH.getMsStatustask().getAmMssubsystem());
				String statusCode = wfEngineLogic.commitCurrentTask(uuidProcess, trTaskH.getUuidTaskH());
				Object[][] params2 = {
						{ Restrictions.eq("statusCode", statusCode) },
						{Restrictions.eq("amMssubsystem.uuidMsSubsystem", loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
				MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
				
				if (((GlobalVal.FORM_PRE_SURVEY.equals(trTaskH.getMsForm().getFormName()) || 
						GlobalVal.FORM_PROMISE_TO_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) ||
					     (GlobalVal.FORM_PROMISE_TO_SURVEY.equals(trTaskH.getMsForm().getFormName())) ||
					     GlobalVal.FORM_VISIT_POLO.equalsIgnoreCase(trTaskH.getMsForm().getFormName())) &&
					    (taskPts != null || taskPreSurvey != null)) {

					    trTaskH.setMsStatustask(msStatustask);
					    this.getManagerDAO().update(trTaskH);

					} else if (taskPts == null && taskPreSurvey == null) {
						
					    trTaskH.setMsStatustask(msStatustask);
					    this.getManagerDAO().update(trTaskH);

					}
				LOG.info("status task {}:{}",trTaskH.getUuidTaskH(),trTaskH.getMsStatustask().getStatusTaskDesc());

				TrTaskhistory trTaskhistory = new TrTaskhistory();
				trTaskhistory.setCodeProcess(GlobalVal.CODE_PROCESS_ASSIGNMENT);
				trTaskhistory.setActor(loginBean.getFullName());
				trTaskhistory.setFieldPerson(amMsuser.getFullName());
				trTaskhistory.setMsStatustask(msStatustask);
				trTaskhistory.setTrTaskH(trTaskH);
				trTaskhistory.setNotes(trTaskH.getNotes());
				trTaskhistory.setUsrCrt(String.valueOf(loginBean.getUuidMsUser()));
				trTaskhistory.setDtmCrt(new Date());

				this.getManagerDAO().update(trTaskH);
				
				if (StringUtils.isNotBlank(trTaskH.getOrderId())) {
					UpdateStatusMainDealerResponse updStatusTaskMainDealer = this.intFormLogic.updateStatusTaskMainDealer(trTaskH, callerId);
					if (!"00".equals(updStatusTaskMainDealer.getResponseCode())) {
						throw new RemoteException(updStatusTaskMainDealer.getResponseMessage());
					}
				}
//				
				this.getManagerDAO().insert(trTaskhistory);

				if (GlobalVal.SUBSYSTEM_MO.equals(trTaskH.getFlagSource())) {
					commitOrder(loginBean, trTaskH.getNotes(), trTaskH, trTaskH
							.getAmMsuser().getAmMssubsystem(), 0,
							GlobalVal.CODE_PROCESS_ASSIGNMENT, callerId);
				}
			}
			//codingan dibawah ini tidak termasuk looping
			/*update task to waiting on pending*/
			if (taskPts != null || taskPreSurvey != null) {
				if (otherTaskPts != null && !otherTaskPts.isEmpty()) {
					if (taskPts == null) {
						otherTaskPts.remove(taskPreSurvey);
					}
					for(TrTaskH task : otherTaskPts) {
						Object[][] params2 = {
								{ Restrictions.eq("statusCode", GlobalVal.SURVEY_STATUS_TASK_WAITING_ON_PENDING) },
								{Restrictions.eq("amMssubsystem.uuidMsSubsystem", loginBean.getAmMssubsystem().getUuidMsSubsystem()) } };
						MsStatustask msStatustask = this.getManagerDAO().selectOne(MsStatustask.class, params2);
						task.setMsStatustask(msStatustask);
						task.setIsUnassign("0");
						this.getManagerDAO().update(task);
					}
				}
			}
			if (isDropByPilotingCae) {
				return "Flagging Piloting CAE Doesn't Match between Task ( "+taskH.getApplNo()+" ) and Branch User. Task have been drop. )";
			}
		}
		return null;
	}

	@Override
	public Map<String, Object> listUserSurveyor(String mode, String formName, AmMsuser amMsuser, String isPilotingCae, String taskType, 
			AuditContext callerId) {
		
		Map<String, Object> result = new HashMap<String, Object>();
		Map<String, Object> resultUser = new HashMap<String, Object>();
		MsForm form = this.getManagerDAO().selectOne(MsForm.class, Long.valueOf(formName));
		
		if (GlobalVal.JOB_ADMIN.equals(amMsuser.getMsJob().getJobCode())) {
			String svyJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, callerId);
            MsJob job = globalLogic.getMsJob(svyJobCode, callerId);
            
			Object[][] params = { { Restrictions.eq("msJob.uuidJob", job.getUuidJob()) },
									{Restrictions.eq("isActive", "1")}, {Restrictions.eq("isDeleted", "0")}};
			String[][] orders = { { "fullName", "ASC" } };
			resultUser = this.getManagerDAO().selectAll(AmMsuser.class,
					params, orders);
			
			resultUser.put(GlobalKey.MAP_RESULT_SIZE, ((List)resultUser.get(GlobalKey.MAP_RESULT_LIST)).size());
		} 
		else {		
			String[][] users = null;
			List listJobCode = new ArrayList();
			
			if(GlobalVal.FORM_VISIT_POLO.equals(form.getFormName()) || GlobalVal.FORM_PROMISE_TO_VISIT_POLO.equals(form.getFormName())) {
				if (StringUtils.isNotBlank(taskType)) {
					String poloJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_ROLE_TASK_POLO, callerId);
					if (StringUtils.isNotBlank(poloJobCode)) {
						String[] listJenisTask = poloJobCode.split(";");
						for (int i = 0; i < listJenisTask.length; i++) {
							String[] settingRole = listJenisTask[i].split("=");
							if (settingRole[0].equalsIgnoreCase(taskType)) {
								poloJobCode = settingRole[1];
								break;
							}
						}
						listJobCode.addAll(Arrays.asList(StringUtils.split(poloJobCode, ","))) ;
					}
				} else {
					String poloJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVYPOLO, callerId);
					listJobCode.addAll(Arrays.asList(StringUtils.split(poloJobCode, ";"))) ;
				}
			} else {
				if ("1".equalsIgnoreCase(isPilotingCae)) {
					if (StringUtils.isNotBlank(taskType)) {
						String poloJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_ROLE_TASK_POLO, callerId);
						if (StringUtils.isNotBlank(poloJobCode)) {
							String[] listJenisTask = poloJobCode.split(";");
							for (int i = 0; i < listJenisTask.length; i++) {
								String[] settingRole = listJenisTask[i].split("=");
								if (settingRole[0].equalsIgnoreCase(taskType)) {
									poloJobCode = settingRole[1];
									break;
								}
							}
							listJobCode.addAll(Arrays.asList(StringUtils.split(poloJobCode, ","))) ;
						}
					} else {
						String poloJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_ROLE_TASK_CAE, callerId);
						if (StringUtils.isNotBlank(poloJobCode)) {
							listJobCode.addAll(Arrays.asList(StringUtils.split(poloJobCode, ","))) ;
						}
					}
				} else {
					String svyJobCode = globalLogic.getGsValue(GlobalKey.GENERALSETTING_JOBSVY, null);
					listJobCode.add(svyJobCode);
				}
			}
			
			List<AmMsuser> listUser = new ArrayList<AmMsuser>();
			Iterator iter = listJobCode.iterator();
			while(iter.hasNext()) {
				String job = (String) iter.next();
				
				if (mode.equalsIgnoreCase(GlobalVal.MODE_MENU_BY_HIERARKI_BRANCH) || mode.equalsIgnoreCase(GlobalVal.MODE_MENU_BY_BRANCH_PILOTING)){
					users= getUserByBranch(String.valueOf(amMsuser.getMsBranch().getUuidBranch()), job, callerId);
				}
				else{
					users= getUserByLogin(String.valueOf(amMsuser.getUuidMsUser()), job, callerId);
				}

				if (users != null) {
					for (int i = 0; i < users.length; i++) {
						if (!"1".equals(users[i][3])) {
							if (null != users[i][0]) {
								AmMsuser bean = new AmMsuser();
								bean.setUuidMsUser(Long.valueOf(users[i][0]));
								bean.setLoginId(users[i][1]);
								bean.setFullName(users[i][2] + " - " + job);
								listUser.add(bean);
							}
						}
					}
				}
			}
			
			resultUser.put(GlobalKey.MAP_RESULT_LIST, listUser);
			resultUser.put(GlobalKey.MAP_RESULT_SIZE, listUser.size());
		}
		Integer[][] assignment = this
				.getAssignment((List<AmMsuser>) resultUser
						.get(GlobalKey.MAP_RESULT_LIST), callerId);
		String[][] lastLoc = this.getLastLoc((List<AmMsuser>) resultUser
				.get(GlobalKey.MAP_RESULT_LIST), callerId);
		
		List<AmMsuser> listUserSort = (List<AmMsuser>) resultUser.get(GlobalKey.MAP_RESULT_LIST);
		listUserSort.sort(Comparator.comparing(AmMsuser::getFullName));
		resultUser.put(GlobalKey.MAP_RESULT_LIST, listUserSort);
		
		result.put("result", resultUser);
		result.put("assignment", assignment);
		result.put("location", lastLoc);

		return result;
	}

}