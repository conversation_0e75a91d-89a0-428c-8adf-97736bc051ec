<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="survey.dailysummary">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	<query-param name="subsystem" type="string" />
	SELECT
	(SELECT COUNT (uuid_task_h) FROM TR_TASK_H with (nolock) where ASSIGN_DATE BETWEEN :start AND :end
		AND UUID_MS_USER = :uuidSpv ) as totAssign,
	(SELECT COUNT (uuid_task_h) FROM TR_TASK_H with (nolock) where SUBMIT_DATE BETWEEN :start AND :end
		AND UUID_MS_USER = :uuidSpv 
		AND SUBMIT_DATE IS NOT NULL
		AND FLAG_SOURCE != 'MS'
		AND UUID_STATUS_TASK in 
			(SELECT UUID_STATUS_TASK FROM MS_STATUSTASK with (nolock)
				 WHERE UUID_MS_SUBSYSTEM = :subsystem AND STATUS_CODE != 'U')) as totSubmit,
	(SELECT COUNT (uuid_task_h) FROM TR_TASK_H with (nolock) where FLAG_SOURCE = 'MS' and
		ISNULL(ASSIGN_DATE, 0) = 0
		AND DTM_CRT BETWEEN :start AND :end
		AND UUID_MS_USER = :uuidSpv ) as totNew
	</sql-query>
</hibernate-mapping>