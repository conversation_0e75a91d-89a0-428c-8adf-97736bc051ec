<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="survey.dm.getPerformanceMTD">
	   	<query-param name="uuidBranch" type="long" />
	   	SELECT A.status, SUM(A.jumlah) jumlah, A.daydate from(
	   	SELECT 'N' status, COUNT(1) jumlah, DAY(H.ASSIGN_DATE) daydate 
		  FROM TR_TASK_H H with (nolock) join am_msuser amu with (nolock)
		  on h.uuid_ms_user = amu.uuid_ms_user
		  WHERE H.UUID_BRANCH = :uuidBranch
		  AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
		  AND H.ASSIGN_DATE between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate(),112))
		  GROUP BY DAY(H.ASSIGN_DATE)
		  union all
		SELECT 'S' status, COUNT(1) jumlah, DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) daydate 
		  FROM TR_TASK_H H with (nolock) join am_msuser amu with (nolock)
		  on h.uuid_ms_user = amu.uuid_ms_user
		  JOIN MS_STATUSTASK mst with (nolock) on H.uuid_status_task = mst.uuid_status_task
		  WHERE H.UUID_BRANCH = :uuidBranch
		  AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
		  AND ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))) between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate(),112))
		  AND submit_date is not null
		  and mst.STATUS_CODE = 'S'
		  GROUP BY DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) 
	   		 union all
		SELECT 'N' status, COUNT(1) jumlah, DAY(H.ASSIGN_DATE) daydate 
		  FROM FINAL_TR_TASK_H H with (nolock) join am_msuser amu with (nolock)
		  on h.uuid_ms_user = amu.uuid_ms_user
		  WHERE H.UUID_BRANCH = :uuidBranch
		  AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
		  AND H.ASSIGN_DATE between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate(),112))
		  GROUP BY DAY(H.ASSIGN_DATE)
		  union all
		SELECT 'S' status, COUNT(1) jumlah, DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) daydate 
		  FROM FINAL_TR_TASK_H H with (nolock) join am_msuser amu with (nolock)
		  on h.uuid_ms_user = amu.uuid_ms_user
		  JOIN MS_STATUSTASK mst with (nolock) on H.uuid_status_task = mst.uuid_status_task
		  WHERE H.UUID_BRANCH = :uuidBranch
		  AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
		  AND ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))) between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate(),112))
		  AND submit_date is not null
		  and mst.STATUS_CODE = 'S'
		  GROUP BY DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) 
		  ) A
		 GROUP BY STATUS, daydate
		 ORDER BY STATUS, daydate
	</sql-query>
	<sql-query name="survey.dm.getPerformanceToday">
	   	<query-param name="uuidBranch" type="long" />
		SELECT 'N' status, COUNT(1) jumlah, DAY(H.ASSIGN_DATE) daydate
		  FROM TR_TASK_H H with (nolock) join am_msuser amu with (nolock)
		  on h.uuid_ms_user = amu.uuid_ms_user
		  WHERE H.UUID_BRANCH = :uuidBranch
		  AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
		  AND H.ASSIGN_DATE between convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
		  GROUP BY DAY(H.ASSIGN_DATE)
		  union all
		SELECT 'S' status, COUNT(1) jumlah, DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) daydate 
		  FROM TR_TASK_H H with (nolock) join am_msuser amu with (nolock)
		  on h.uuid_ms_user = amu.uuid_ms_user
		  JOIN MS_STATUSTASK mst with (nolock) on H.uuid_status_task = mst.uuid_status_task
		  WHERE H.UUID_BRANCH = :uuidBranch
		  AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
		  AND ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))  between convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
		  AND submit_date is not null
		  and mst.STATUS_CODE = 'S'
		  GROUP BY DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) 
	</sql-query>
	<sql-query name="survey.dm.getQuestion">
		 select mq.uuid_question, MS.uuid_lov, MS.CODE, MS.DESCRIPTION
		 from MS_LOV MS JOIN MS_QUESTION MQ
		 ON MS.LOV_GROUP = MQ.LOV_GROUP
		 JOIN MS_ASSETTAG MA ON MQ.UUID_ASSET_TAG = MA.UUID_ASSET_TAG
		 WHERE MA.ASSET_TAG_NAME = 'PRODUCT'
	</sql-query>
	<sql-query name="survey.dm.getDrillMonth">
	   	<query-param name="uuidBranch" type="long" />
	   	<query-param name="uuidQuestion" type="long" />
	   	<query-param name="uuidLov" type="long" />
	   	SELECT A.status, SUM(A.jumlah) jumlah, A.daydate from(
			SELECT 'N' status, COUNT(1) jumlah, DAY(H.ASSIGN_DATE) daydate
		    FROM TR_TASK_H H with (nolock)
		    JOIN TR_TASKSURVEYDATA TTSD ON H.UUID_TASK_H = TTSD.UUID_TASK_ID
			JOIN AM_MSUSER AMU ON H.UUID_MS_USER = AMU.UUID_MS_USER
		    WHERE H.UUID_BRANCH = :uuidBranch
				AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
			    AND H.ASSIGN_DATE between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate()+1,112))
			    AND TTSD.PRODUCT_LOV_ID = :uuidLov
			GROUP BY DAY(H.ASSIGN_DATE)
		    	UNION ALL
		  	SELECT 'S' status, COUNT(1) jumlah, DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) daydate
		    FROM TR_TASK_H H with (nolock)
		    JOIN TR_TASKSURVEYDATA TTSD ON H.UUID_TASK_H = TTSD.UUID_TASK_ID
			JOIN AM_MSUSER AMU ON H.UUID_MS_USER = AMU.UUID_MS_USER
			JOIN MS_STATUSTASK mst with (nolock) on H.uuid_status_task = mst.uuid_status_task
		    WHERE H.UUID_BRANCH = :uuidBranch
				AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
			    AND ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))) between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate()+1,112))
			    AND submit_date is not null
			    and mst.STATUS_CODE = 'S'
				AND TTSD.PRODUCT_LOV_ID = :uuidLov
			GROUP BY DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))))
				UNION ALL
			SELECT 'N' status, COUNT(1) jumlah, DAY(H.ASSIGN_DATE) daydate
		    FROM FINAL_TR_TASK_H H with (nolock)
		    JOIN FINAL_TR_TASKSURVEYDATA TTSD ON H.UUID_TASK_H = TTSD.UUID_TASK_ID
			JOIN AM_MSUSER AMU ON H.UUID_MS_USER = AMU.UUID_MS_USER
		    WHERE H.UUID_BRANCH = :uuidBranch
			  	AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
			    AND H.ASSIGN_DATE between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate()+1,112))
			    AND TTSD.PRODUCT_LOV_ID = :uuidLov
			GROUP BY DAY(H.ASSIGN_DATE)
		   	 	UNION ALL
		  	SELECT 'S' status, COUNT(1) jumlah, DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) daydate
		    FROM FINAL_TR_TASK_H H with (nolock)
		    JOIN FINAL_TR_TASKSURVEYDATA TTSD ON H.UUID_TASK_H = TTSD.UUID_TASK_ID
			JOIN AM_MSUSER AMU ON H.UUID_MS_USER = AMU.UUID_MS_USER
			JOIN MS_STATUSTASK mst with (nolock) on H.uuid_status_task = mst.uuid_status_task
		    WHERE H.UUID_BRANCH = :uuidBranch
				AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
			    AND ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))) between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate()+1,112))
			    AND submit_date is not null
		    	and mst.STATUS_CODE = 'S'
				AND TTSD.PRODUCT_LOV_ID = :uuidLov
		    GROUP BY DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))))
	    ) A
		 GROUP BY STATUS, daydate
		 ORDER BY STATUS, daydate
	</sql-query>
	<sql-query name="survey.dm.getOtherDrillMonth">
	   	<query-param name="uuidBranch" type="long" />
	   	<query-param name="uuidQuestion" type="long" />
		SELECT A.status, SUM(A.jumlah) jumlah, A.daydate from(
		SELECT 'N' status, COUNT(1) jumlah, DAY(H.ASSIGN_DATE) daydate
	    FROM TR_TASK_H H with (nolock)
	    LEFT JOIN TR_TASKSURVEYDATA TTSD with (nolock) ON H.UUID_TASK_H = TTSD.UUID_TASK_ID
		JOIN AM_MSUSER AMU with (nolock) ON H.UUID_MS_USER = AMU.UUID_MS_USER
	    WHERE H.UUID_BRANCH = :uuidBranch
		AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
	    AND H.ASSIGN_DATE between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate()+1,112))
		AND TTSD.PRODUCT_LOV_ID is null
		GROUP BY DAY(H.ASSIGN_DATE)
		  UNION ALL
	  	SELECT 'S' status, COUNT(1) jumlah, DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) daydate
	    FROM TR_TASK_H H with (nolock)
	    LEFT JOIN TR_TASKSURVEYDATA TTSD with (nolock) ON H.UUID_TASK_H = TTSD.UUID_TASK_ID
		JOIN AM_MSUSER AMU with (nolock) ON H.UUID_MS_USER = AMU.UUID_MS_USER
		JOIN MS_STATUSTASK mst with (nolock) on H.uuid_status_task = mst.uuid_status_task
	    WHERE H.UUID_BRANCH =  :uuidBranch
		AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
	    AND ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))) between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate()+1,112))
	    AND submit_date is not null
	    and mst.STATUS_CODE = 'S'
		AND TTSD.PRODUCT_LOV_ID is null
	    GROUP BY DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))))
			  UNION ALL
	  	SELECT 'S' status, COUNT(1) jumlah, DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0)))) daydate
	    FROM FINAL_TR_TASK_H H with (nolock)
	    LEFT JOIN FINAL_TR_TASKSURVEYDATA TTSD with (nolock) ON H.UUID_TASK_H = TTSD.UUID_TASK_ID
		JOIN AM_MSUSER AMU with (nolock) ON H.UUID_MS_USER = AMU.UUID_MS_USER
		JOIN MS_STATUSTASK mst with (nolock) on H.uuid_status_task = mst.uuid_status_task
	    WHERE H.UUID_BRANCH =  :uuidBranch
		AND amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
	    AND ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))) between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate()+1,112))
	    AND submit_date is not null
	    and mst.STATUS_CODE = 'S'
		AND TTSD.PRODUCT_LOV_ID is null
	    GROUP BY DAY(ISNULL(H.approval_date,ISNULL(H.verification_date,ISNULL(H.SUBMIT_DATE, 0))))
		) A
		GROUP BY STATUS, daydate
		ORDER BY STATUS, daydate		
	</sql-query>
	<sql-query name="survey.dm.getSubmitMTD">
	   	<query-param name="uuidBranch" type="long" />
	   	select amu.uuid_ms_user, amu.full_name,
	  (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	     where ASSIGN_DATE IS NOT NULL
	     and ASSIGN_DATE BETWEEN DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and convert(datetime,convert(varchar(8),getdate(),112))
	     and UUID_MS_USER = amu.uuid_ms_user
	    ) as assign_in,    
	    (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	     where SUBMIT_DATE IS NOT NULL and
	  trth.SUBMIT_DATE between DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and  convert(datetime,convert(varchar(8),getdate(),112))
	  and trth.UUID_MS_USER = amu.uuid_ms_user
	    ) as submit 
	   from am_msuser amu
	  left outer JOIN AM_MSUSER spv on amu.spv_id = spv.uuid_ms_user
	   where amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
	   and amu.UUID_BRANCH = :uuidBranch
	</sql-query>
	<sql-query name="survey.dm.getSubmitToday">
	   	<query-param name="uuidBranch" type="long" />
	   	select amu.uuid_ms_user, amu.full_name,
	  (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	     where ASSIGN_DATE IS NOT NULL
	     and ASSIGN_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	     and UUID_MS_USER = amu.uuid_ms_user
	    ) as assign_in,    
	    (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	     where SUBMIT_DATE IS NOT NULL and
	  trth.SUBMIT_DATE between convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	  and trth.UUID_MS_USER = amu.uuid_ms_user
	    ) as submit 
	   from am_msuser amu
	  left outer JOIN AM_MSUSER spv on amu.spv_id = spv.uuid_ms_user
	   where amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
	   and amu.UUID_BRANCH = :uuidBranch
	</sql-query>
	<sql-query name="survey.dm.getVerificationMTD">
		<query-param name="uuidSubsystem" type="long" />
	   	<query-param name="uuidBranch" type="long" />
		SELECT AMU.UUID_MS_USER, AMU.FULL_NAME, COUNT(UUID_TASK_H) JUMLAH
	   FROM (
	    SELECT DISTINCT TTH.USR_CRT, H.UUID_TASK_H FROM TR_TASK_H H WITH (NOLOCK)
	    JOIN TR_TASKHISTORY TTH WITH (NOLOCK)
	    ON H.UUID_TASK_H = TTH.UUID_TASK_H
	    JOIN MS_STATUSTASK MST WITH (NOLOCK)
	    ON H.UUID_STATUS_TASK = MST.UUID_STATUS_TASK
	    WHERE  VERIFICATION_DATE IS NOT NULL
	    AND UUID_BRANCH = :uuidBranch
	    AND MST.UUID_MS_SUBSYSTEM = :uuidSubsystem
	    AND VERIFICATION_DATE BETWEEN DATEADD(m, DATEDIFF(m, 0, GETDATE()), 0) and convert(datetime,convert(varchar(8),getdate(),112))
	    AND CODE_PROCESS IN('004','009','013')
	   ) N  JOIN AM_MSUSER AMU WITH (NOLOCK)
	   ON N.USR_CRT = AMU.UUID_MS_USER 
	   GROUP BY AMU.UUID_MS_USER, AMU.FULL_NAME
	</sql-query>
	<sql-query name="survey.dm.getVerificationToday">
		<query-param name="uuidSubsystem" type="long" />
	   	<query-param name="uuidBranch" type="long" />
		SELECT AMU.UUID_MS_USER, AMU.FULL_NAME, COUNT(UUID_TASK_H) JUMLAH
	   FROM (
	    SELECT DISTINCT TTH.USR_CRT, H.UUID_TASK_H FROM TR_TASK_H H WITH (NOLOCK)
	    JOIN TR_TASKHISTORY TTH WITH (NOLOCK)
	    ON H.UUID_TASK_H = TTH.UUID_TASK_H
	    JOIN MS_STATUSTASK MST WITH (NOLOCK)
	    ON H.UUID_STATUS_TASK = MST.UUID_STATUS_TASK
	    WHERE  VERIFICATION_DATE IS NOT NULL
	    AND UUID_BRANCH = :uuidBranch
	    AND MST.UUID_MS_SUBSYSTEM = :uuidSubsystem
	    AND VERIFICATION_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	    AND CODE_PROCESS IN('004','009','013')
	   ) N  JOIN AM_MSUSER AMU WITH (NOLOCK)
	   ON N.USR_CRT = AMU.UUID_MS_USER 
	   GROUP BY AMU.UUID_MS_USER, AMU.FULL_NAME
	</sql-query>
	<sql-query name="survey.dm.getSurveyorStatusToday">
		<query-param name="uuidSubsystem" type="long" />
	   	<query-param name="uuidBranch" type="long" />
		select amu.uuid_ms_user, amu.full_name, spv.full_name spvname,
	   (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	    JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
	    where msm.STATUS_MOBILE_CODE = 'N'
	    and ASSIGN_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	    and UUID_MS_USER = amu.uuid_ms_user
	   ) as newTask,
			(select count(tth.UUID_TASK_H) from TR_TASK_H tth with (nolock) 
	    JOIN MS_STATUSMOBILE msm with (nolock) ON tth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
	    where msm.STATUS_MOBILE_CODE = 'W'
	     and ASSIGN_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	     and UUID_MS_USER = amu.uuid_ms_user
	   ) as download, 
	   (select count(tth.UUID_TASK_H) from TR_TASK_H tth with (nolock) 
	    JOIN MS_STATUSMOBILE msm with (nolock) ON tth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
	    where msm.STATUS_MOBILE_CODE = 'R'
	    and ASSIGN_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	    and UUID_MS_USER = amu.uuid_ms_user
	   ) as reads,
		(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
	    JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
	    where msm.STATUS_MOBILE_CODE = 'O'
	    and ASSIGN_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	    and trth.uuid_ms_user = amu.uuid_ms_user
	   ) as survey,
		(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
	    JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
	    where msm.STATUS_MOBILE_CODE = 'U'
	    and SUBMIT_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	    and trth.uuid_ms_user = amu.uuid_ms_user
	   ) as upload, 
	   (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	    JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
	    where SUBMIT_DATE IS NOT NULL AND SUBMIT_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
		AND msm.STATUS_MOBILE_CODE = 'S'
		and trth.uuid_ms_user = amu.uuid_ms_user
	   ) as submit,
	   (select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
	    join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
	    where mst.STATUS_CODE IN ('V', 'A')
	    and START_DTM IS NOT NULL
	    and SUBMIT_DATE BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	    and mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
	    and trth.uuid_ms_user = amu.uuid_ms_user
	   ) as verifikasi,
	   (select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
	     join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
	     where SUBMIT_DATE IS NOT NULL
	     and COALESCE(approval_date, verification_date, SUBMIT_DATE) BETWEEN convert(datetime,convert(varchar(8),getdate(),112)) and convert(datetime,convert(varchar(8),getdate()+1,112))
	     and mst.STATUS_CODE = 'S'
	     and trth.UUID_MS_USER = amu.uuid_ms_user
	    ) as release
	  from am_msuser amu
		left outer JOIN AM_MSUSER spv on amu.spv_id = spv.uuid_ms_user
	  where amu.uuid_job = (select uuid_job from ms_job where job_code = (select gs_value from am_generalsetting where gs_code = 'MS_JOBSVY'))
	  and amu.UUID_BRANCH = :uuidBranch
	</sql-query>
</hibernate-mapping>
