<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="survey.dm.getUsersAll">
	   	<query-param name="uuidSpv" type="string" />
		WITH N AS (
		SELECT 
		msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
		UNION ALL
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME,	msu2.IS_LOGGED_IN, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID from N order by FULL_NAME
	</sql-query>
	<sql-query name="survey.dm.countUsersAllByBranch">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="jobCode" type="string" />
		SELECT count(*) FROM AM_MSUSER usr with (nolock)
			JOIN MS_JOB jb with (nolock) on usr.UUID_JOB = jb.UUID_JOB
			WHERE UUID_BRANCH = :uuidBranch 
				AND jb.JOB_CODE = :jobCode AND usr.IS_ACTIVE = '1'
	</sql-query>
	<sql-query name="survey.dm.getUsersExc">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidSvy" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		WITH N AS (
		SELECT 
		msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_BRANCH, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
		UNION ALL
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.LOGIN_ID, msu2.UUID_BRANCH, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
		select N.UUID_MS_USER, N.FULL_NAME, N.IS_LOGGED_IN, N.LOGIN_ID, msb.BRANCH_NAME,
				ROW_NUMBER() OVER (ORDER BY FULL_NAME) AS rownum
		from N JOIN MS_BRANCH msb with (nolock) ON N.UUID_BRANCH = msb.UUID_BRANCH
		where UUID_MS_USER != :uuidSvy
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	<sql-query name="survey.dm.getUsersExcCnt">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidSvy" type="string" />
	   	WITH N AS (
		SELECT 
		msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_BRANCH, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
		UNION ALL
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.LOGIN_ID, msu2.UUID_BRANCH, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select count(1)
		from N
		where UUID_MS_USER != :uuidSvy
	</sql-query>
	<sql-query name="survey.dm.getUsersExcAssignInOut">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="uuidSvy" type="string" />
		WITH N AS (
		SELECT 
		msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.LOGIN_ID, msu.UUID_BRANCH, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL
		FROM  AM_MSUSER msu with (nolock)
		WHERE msu.SPV_ID = :uuidSpv AND msu.IS_ACTIVE = '1'
		UNION ALL
		SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, msu2.LOGIN_ID, msu2.UUID_BRANCH, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1
		FROM   AM_MSUSER msu2 with (nolock),N
		WHERE N.UUID_MS_USER = msu2.SPV_ID
		)
		select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID, msb.BRANCH_NAME 
		from N JOIN MS_BRANCH msb with (nolock) ON N.UUID_BRANCH = msb.UUID_BRANCH
		where UUID_MS_USER != :uuidSvy order by FULL_NAME
	</sql-query>
	<sql-query name="survey.dm.getArea">
	   	<query-param name="uuidUser" type="string" />
		SELECT MA.AREA_NAME, MA.AREA_TYPE_CODE, MA.RADIUS, MA.LATITUDE as latLoc, MA.LONGITUDE as lngLoc,  MAP.LATITUDE as latArea , MAP.LONGITUDE lngArea 
		FROM MS_AREAOFUSER MAU with (nolock) 
		LEFT OUTER JOIN MS_AREA MA with (nolock) ON MAU.UUID_AREA = MA.UUID_AREA
		LEFT OUTER JOIN MS_AREAPATH MAP with (nolock) ON MA.UUID_AREA = MAP.UUID_AREA
		WHERE MAU.UUID_MS_USER = :uuidUser
	</sql-query>	
	<sql-query name="survey.dm.getAssetIdentity">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="asset" type="string" />
	   	<query-param name="assetImage" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		SELECT :asset as ASSET_TAG_NAME, :assetImage as ASSET_TAG_IMAGE, trtsd.LEGAL_ADDR_LATITUDE, trtsd.LEGAL_ADDR_LONGITUDE, 
			trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, ISNULL(LEFT(CONVERT(VARCHAR(30), trth.ASSIGN_DATE, 113),17), '') assignDate, 
			ISNULL(LEFT(CONVERT(VARCHAR(30), trth.SUBMIT_DATE , 113),17), '') submitDate, trth.UUID_TASK_H 
		FROM TR_TASK_H trth with (nolock) 
		LEFT OUTER JOIN TR_TASKSURVEYDATA trtsd with (nolock) on trth.UUID_TASK_H = trtsd.UUID_TASK_ID
		LEFT OUTER JOIN MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			and trth.UUID_MS_USER = :uuidUser
		  	and mst.STATUS_CODE not in ('RS')
	</sql-query>
	<sql-query name="survey.dm.getAssetOffice">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="asset" type="string" />
	   	<query-param name="assetImage" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		SELECT :asset as ASSET_TAG_NAME, :assetImage as ASSET_TAG_IMAGE, trtsd.OFFICE_LATITUDE, trtsd.OFFICE_LONGITUDE, 
			trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, ISNULL(LEFT(CONVERT(VARCHAR(30), trth.ASSIGN_DATE, 113),17), '') assignDate, 
			ISNULL(LEFT(CONVERT(VARCHAR(30), trth.SUBMIT_DATE , 113),17), '') submitDate, trth.UUID_TASK_H 
		FROM TR_TASK_H trth with (nolock) 
		LEFT OUTER JOIN TR_TASKSURVEYDATA trtsd with (nolock) on trth.UUID_TASK_H = trtsd.UUID_TASK_ID
		LEFT OUTER JOIN MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			and trth.UUID_MS_USER = :uuidUser
		  	and mst.STATUS_CODE not in ('RS')
	</sql-query>
	<sql-query name="survey.dm.getAssetDriveway">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="asset" type="string" />
	   	<query-param name="assetImage" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		SELECT :asset as ASSET_TAG_NAME, :assetImage as ASSET_TAG_IMAGE, trtsd.DRIVEWAY_LATITUDE, trtsd.DRIVEWAY_LONGITUDE, 
			trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, ISNULL(LEFT(CONVERT(VARCHAR(30), trth.ASSIGN_DATE, 113),17), '') assignDate, 
			ISNULL(LEFT(CONVERT(VARCHAR(30), trth.SUBMIT_DATE , 113),17), '') submitDate, trth.UUID_TASK_H 
		FROM TR_TASK_H trth with (nolock) 
		LEFT OUTER JOIN TR_TASKSURVEYDATA trtsd with (nolock) on trth.UUID_TASK_H = trtsd.UUID_TASK_ID
		LEFT OUTER JOIN MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			and trth.UUID_MS_USER = :uuidUser
		  	and mst.STATUS_CODE not in ('RS')
	</sql-query>
	<sql-query name="survey.dm.getAssetVehicle">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="asset" type="string" />
	   	<query-param name="assetImage" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		SELECT :asset as ASSET_TAG_NAME, :assetImage as ASSET_TAG_IMAGE, trtsd.VEHICLE_LATITUDE, trtsd.VEHICLE_LONGITUDE, 
			trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, ISNULL(LEFT(CONVERT(VARCHAR(30), trth.ASSIGN_DATE, 113),17), '') assignDate, 
			ISNULL(LEFT(CONVERT(VARCHAR(30), trth.SUBMIT_DATE , 113),17), '') submitDate, trth.UUID_TASK_H 
		FROM TR_TASK_H trth with (nolock) 
		LEFT OUTER JOIN TR_TASKSURVEYDATA trtsd with (nolock) on trth.UUID_TASK_H = trtsd.UUID_TASK_ID
		LEFT OUTER JOIN MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			and trth.UUID_MS_USER = :uuidUser
		  	and mst.STATUS_CODE not in ('RS')
	</sql-query>
	<sql-query name="survey.dm.getAssetHome">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="asset" type="string" />
	   	<query-param name="assetImage" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		SELECT :asset as ASSET_TAG_NAME, :assetImage as ASSET_TAG_IMAGE, trtsd.HOME_LATITUDE, trtsd.HOME_LONGITUDE, 
			trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, ISNULL(LEFT(CONVERT(VARCHAR(30), trth.ASSIGN_DATE, 113),17), '') assignDate, 
			ISNULL(LEFT(CONVERT(VARCHAR(30), trth.SUBMIT_DATE , 113),17), '') submitDate, trth.UUID_TASK_H 
		FROM TR_TASK_H trth with (nolock) 
		LEFT OUTER JOIN TR_TASKSURVEYDATA trtsd with (nolock) on trth.UUID_TASK_H = trtsd.UUID_TASK_ID
		LEFT OUTER JOIN MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		WHERE trth.SUBMIT_DATE BETWEEN :start AND :end
			and trth.UUID_MS_USER = :uuidUser
		  	and mst.STATUS_CODE not in ('RS')
	</sql-query>
	<sql-query name="survey.dm.getAttendanceIn">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		SELECT MIN(CONVERT(VARCHAR(5),datetime_IN,108) ) FROM TR_ATTENDANCE with (nolock)
 		WHERE datetime_IN BETWEEN :start AND :end
		AND UUID_MS_USER= :uuidUser
	</sql-query>
	<sql-query name="survey.dm.getLastTimeDetected">
	   	<query-param name="uuidUser" type="string" />
		select MAX(CONVERT(VARCHAR(5), datetime, 108) ) from TR_LOCATIONHISTORY with (nolock) 
		where datetime BETWEEN :start AND :end
		and UUID_MS_USER= :uuidUser
	</sql-query>
	
	<sql-query name="survey.dm.getTaskRecapitulation">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="idSubsystem" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select 
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'N'
				and UUID_MS_USER = :uuidUser
			) as newTask,
			(select count(tth.UUID_TASK_H) from TR_TASK_H tth with (nolock) 
				JOIN MS_STATUSMOBILE msm with (nolock) ON tth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'R'
				and UUID_MS_USER = :uuidUser
			) as reads,
			(select count(tth.UUID_TASK_H) from TR_TASK_H tth with (nolock) 
				JOIN MS_STATUSMOBILE msm with (nolock) ON tth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'W'
					and UUID_MS_USER = :uuidUser
			) as download, 
			(select count(UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where (SUBMIT_DATE IS NOT NULL
				and SUBMIT_DATE BETWEEN :start AND :end
				and msm.STATUS_MOBILE_CODE = 'S'
				and trth.UUID_MS_USER = :uuidUser) 
				or
				(msm.STATUS_MOBILE_CODE = 'S'
				and SUBMIT_DATE IS NULL
				and trth.uuid_ms_user = :uuidUser)
			) as submit,
			(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'U'
				and SUBMIT_DATE BETWEEN :start AND :end
				and trth.uuid_ms_user = :uuidUser
			) as upload, 
			(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
				join MS_STATUSTASK mst with (nolock) on trth.uuid_status_task = mst.uuid_status_task
				where mst.STATUS_CODE = 'V'
				and START_DTM IS NOT NULL
				and mst.UUID_MS_SUBSYSTEM = :idSubsystem
				and trth.uuid_ms_user = :uuidUser
			) as verifikasi,
			(select count(trth.UUID_TASK_H) from TR_TASK_H trth with (nolock)
				JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
				where msm.STATUS_MOBILE_CODE = 'O'
				and trth.uuid_ms_user = :uuidUser
			) as survey
	</sql-query>
	<sql-query name="survey.dm.getTotalTaskSurveyor">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="startTW" type="string" />
	   	<query-param name="endTW" type="string" />
	   	<query-param name="startLW" type="string" />
	   	<query-param name="endLW" type="string" />
	   	<query-param name="startTM" type="string" />
	   	<query-param name="endTM" type="string" />
	   	<query-param name="startLM" type="string" />
	   	<query-param name="endLM" type="string" />
	   	<query-param name="currentDate" type="string" />
		select 
			(select SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startTW AND :endTW
			) as assignTW, 
			(select SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startLW AND :endLW
			) as assignLW, 
			(select SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startTW AND :endTW
			) as submitTW, 
			(select SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startLW AND :endLW
			) as submitLW, 
			(select SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startTM AND :endTM
			) as assignTM, 
			(select SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startLM AND :endLM
			) as assignLM, 
			(select SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startTM AND :endTM
			) as submitTM, 
			(select SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE BETWEEN :startLM AND :endLM
			) as submitLM, 
			(select SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 where UUID_MS_USER = :uuidUser
				 and DAILY_DATE = :currentDate) as newTask
	</sql-query>	
	
	<sql-query name="survey.dm.getTotalTaskBranchByBranch">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="startTW" type="string" />
	   	<query-param name="endTW" type="string" />
	   	<query-param name="startLW" type="string" />
	   	<query-param name="endLW" type="string" />
	   	<query-param name="startTM" type="string" />
	   	<query-param name="endTM" type="string" />
	   	<query-param name="startLM" type="string" />
	   	<query-param name="endLM" type="string" />
   		<query-param name="currentDate" type="string" />
		SELECT 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE = :currentDate
				 ) as assignTd,
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTW AND :endTW
				 ) as assignTW, 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startLW AND :endLW
				 ) as assignLW, 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTM AND :endTM
				 ) as assignTM, 
			(SELECT SUM(COALESCE(TOTAL_ASSIGNED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startLM AND :endLM
				 ) as assignLM, 
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE = :currentDate
				 ) as submitTd, 
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTW AND :endTW
				 ) as submitTW, 
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startLW AND :endLW
				 ) as submitLW,
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTM AND :endTM
				 ) as submitTM,
			(SELECT SUM(COALESCE(TOTAL_SUBMITTED_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startLM AND :endLM
				 ) as submitLM,
			(SELECT SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE = :currentDate
				 ) as newTaskTd,
			(SELECT SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTW AND :endTW
				 ) as newTaskTW,
			(SELECT SUM(COALESCE(TOTAL_NEW_TASK, 0)) from TR_SURVEYDAILYSUMMARY with (nolock)
				 WHERE UUID_BRANCH = :uuidBranch
				 AND DAILY_DATE BETWEEN :startTM AND :endTM
				 ) as newTaskTM
	</sql-query>
	<sql-query name="survey.dm.taskmonitoring.load">
	   	<query-param name="uuidSvy" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	SELECT trth.UUID_TASK_H, trth.TASK_ID, trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, trth.CUSTOMER_PHONE, trth.ZIP_CODE, msf.FORM_NAME,
			CASE 
				WHEN msm.STATUS_MOBILE_CODE = 'D'
					THEN 'grey'
				WHEN msm.STATUS_MOBILE_CODE = 'N'
					THEN 'red' 
				WHEN msm.STATUS_MOBILE_CODE = 'W'
					THEN 'orange'
				WHEN msm.STATUS_MOBILE_CODE = 'R'
					THEN 'blue'
				WHEN msm.STATUS_MOBILE_CODE = 'O'   
					THEN 'purple'
				WHEN msm.STATUS_MOBILE_CODE = 'U'
					THEN 'yellow'
				WHEN (trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end)
					AND msm.STATUS_MOBILE_CODE = 'S'
					THEN 'green'
			END as status,
			CASE
		 	<![CDATA[WHEN DATEDIFF(hour, trth.ASSIGN_DATE, CURRENT_TIMESTAMP) <= (select GS_VALUE from AM_GENERALSETTING with (nolock) WHERE GS_CODE = 'SLA_TIME')]]>	
				THEN '0'
				ELSE '1'
			END as sla
			FROM TR_TASK_H trth with (nolock) JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
			WHERE trth.UUID_MS_USER = :uuidSvy 
			AND ((TRTH.APPROVAL_DATE IS NOT NULL AND TRTH.APPROVAL_DATE BETWEEN (CASE WHEN msm.STATUS_MOBILE_CODE = 'D' THEN :start ELSE '1970-01-01' END) AND :end) OR TRTH.APPROVAL_DATE IS NULL)
			AND ((trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end) OR (trth.SUBMIT_DATE IS NULL))
	</sql-query>
	<sql-query name="survey.dm.taskmonitoring.getFormByGroupTask">
	   	<query-param name="groupTaskId" type="string" />
	   	<query-param name="subsystemId" type="string" />
	   	SELECT UUID_FORM, FORM_NAME
		FROM MS_FORM with (nolock)
		WHERE IS_ACTIVE = '1'
			AND UUID_MS_SUBSYSTEM = :subsystemId
		EXCEPT
		SELECT DISTINCT MF.UUID_FORM, MF.FORM_NAME
		FROM MS_GROUPTASK MG with (nolock) 
			JOIN TR_TASK_H TRTH with (nolock) ON MG.UUID_TASK_H = TRTH.UUID_TASK_H
			JOIN MS_FORM MF with (nolock) ON TRTH.UUID_FORM = MF.UUID_FORM
		WHERE MG.GROUP_TASK_ID = 
			(SELECT GROUP_TASK_ID FROM MS_GROUPTASK with (nolock) WHERE UUID_GROUP_TASK = :groupTaskId)
	</sql-query>
	
	<sql-query name="survey.dm.getSpvList">
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="jobCode" type="string"/>
		<query-param name="uuidMsUser" type="string" />
		WITH N AS (
			SELECT 
			msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, msu.UUID_JOB
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.SPV_ID = :uuidMsUser AND msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME, msu2.IS_LOGGED_IN, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, msu2.UUID_JOB
			FROM   AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  FROM (
					select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
					from N join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
					where  J.JOB_CODE in (:jobCode)
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="survey.dm.getSpvListCount">
		<query-param name="jobCode" type="string"/>
		<query-param name="uuidMsUser" type="string" />
		WITH N AS (
			SELECT 
			msu.UUID_MS_USER, msu.FULL_NAME, msu.IS_LOGGED_IN, msu.UUID_MS_USER AS HIRARKI,CAST(msu.UUID_MS_USER AS VARCHAR(MAX)) AS HIRARKI2, 0 as LEVEL, msu.LOGIN_ID, msu.UUID_JOB
			FROM  AM_MSUSER msu with (nolock)
			WHERE msu.SPV_ID = :uuidMsUser and msu.IS_ACTIVE = '1'
			UNION ALL
			SELECT msu2.UUID_MS_USER, msu2.FULL_NAME,	msu2.IS_LOGGED_IN, N.HIRARKI, N.HIRARKI2+'/'+CAST(msu2.UUID_MS_USER AS VARCHAR(MAX)), N.LEVEL+1, msu2.LOGIN_ID, msu2.UUID_JOB
			FROM   AM_MSUSER msu2 with (nolock),N
			WHERE N.UUID_MS_USER = msu2.SPV_ID AND msu2.IS_ACTIVE = '1'
		)
		select COUNT(1) from (
			select distinct UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
			from N join MS_JOB J with (nolock) on N.UUID_JOB = J.UUID_JOB
			where  J.JOB_CODE in (:jobCode)
		) c
	</sql-query>
	
	<sql-query name="survey.dm.getListHierarkiCabang">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="branchLogin" type="string"/>
		<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select keyValue, BRANCH_CODE, BRANCH_NAME, ROW_NUMBER() OVER (ORDER BY BRANCH_CODE) AS rownum 
				from dbo.getCabangByLogin(:branchLogin)
				where BRANCH_CODE like '%' + :branchCode + '%'
				and BRANCH_NAME like '%' + :branchName + '%'
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="survey.dm.getListHierarkiCabangCount">
		<query-param name="branchCode" type="string"/>
		<query-param name="branchName" type="string"/>
		<query-param name="branchLogin" type="string"/>
		select count(BRANCH_CODE) 
		from dbo.getCabangByLogin(:branchLogin)
		where BRANCH_CODE like '%' + :branchCode + '%'
		and BRANCH_NAME like '%' + :branchName + '%'
	</sql-query>
	
	<sql-query name="survey.dm.getSpvListByBranch">
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		<query-param name="jobCode" type="string"/>
		<query-param name="uuidBranch" type="long"/>
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.FULL_NAME) AS rownum  FROM (
					select UUID_MS_USER, FULL_NAME, IS_LOGGED_IN, LOGIN_ID
					from am_msuser am with (nolock) 
					join MS_JOB J with (nolock) on am.UUID_JOB = J.UUID_JOB
					where  J.JOB_CODE in (:jobCode)
					and am.uuid_branch = :uuidBranch
					and am.is_active = '1' and am.is_deleted = '0'
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="survey.dm.getSpvListByBranchCount">
		<query-param name="jobCode" type="string"/>
		<query-param name="uuidBranch" type="long"/>
		select COUNT(1) from (
			select UUID_MS_USER
			from am_msuser am with (nolock) 
			join MS_JOB J with (nolock) on am.UUID_JOB = J.UUID_JOB
			where J.JOB_CODE in (:jobCode)
			and am.uuid_branch = :uuidBranch
			and am.is_active = '1' and am.is_deleted = '0'
		) c
	</sql-query>
	<sql-query name="survey.dm.getGroupTaskList">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.GROUP_TASK_ID) AS rownum  FROM (
					select * from (
						select GROUP_TASK_ID, CUSTOMER_NAME, APPL_NO, BRANCH_NAME, UUID_GROUP_TASK,
						row_number() over(partition by group_task_id order by mg.dtm_crt desc) as rn
						from MS_GROUPTASK mg with (nolock) inner join ms_branch mb with (nolock)
						on mb.uuid_branch = mg.uuid_branch
						where mg.customer_name like '%' + :customerName + '%'
						and mg.uuid_branch = :msBranch.uuidBranch 
						and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'
					) as T where rn = 1
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]> 
	</sql-query>
	<sql-query name="survey.dm.getGroupTaskListCount">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
		select count(distinct(group_task_id))
		from MS_GROUPTASK mg with (nolock) inner join ms_branch mb with (nolock)
			on mb.uuid_branch = mg.uuid_branch
		where mg.customer_name like '%' + :customerName + '%'
		and mg.uuid_branch = :msBranch.uuidBranch 
		and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'					
	</sql-query>
	
	<sql-query name="survey.dm.getGroupTaskListAll">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.GROUP_TASK_ID) AS rownum  FROM (
					select * from (
						select GROUP_TASK_ID, CUSTOMER_NAME, APPL_NO, msb.BRANCH_NAME, UUID_GROUP_TASK,
						row_number() over(partition by group_task_id order by mg.dtm_crt desc) as rn
						from MS_GROUPTASK mg with (nolock)
						inner join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:msBranch.uuidBranch)) msb on mg.UUID_BRANCH = msb.UUID_BRANCH
						where mg.customer_name like '%' + :customerName + '%'
						and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'
					) as T where rn = 1
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]> 
	</sql-query>
	<sql-query name="survey.dm.getGroupTaskListCountAll">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
		select count(distinct(group_task_id))
		from MS_GROUPTASK mg with (nolock) 
			inner join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:msBranch.uuidBranch)) msb on mg.UUID_BRANCH = msb.UUID_BRANCH
		where mg.customer_name like '%' + :customerName + '%'
		and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'					
	</sql-query>
	<sql-query name="survey.dm.getGroupTaskListByHierarkiUser">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="applNo" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
		select * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				SELECT c.*, ROW_NUMBER() OVER (ORDER BY c.GROUP_TASK_ID) AS rownum  FROM (
					select * from (
						select GROUP_TASK_ID, mg.CUSTOMER_NAME, mg.APPL_NO, BRANCH_NAME, UUID_GROUP_TASK,
						row_number() over(partition by group_task_id order by mg.dtm_crt desc) as rn
						from MS_GROUPTASK mg with(nolock) inner join ms_branch mb with (nolock)
						on mb.uuid_branch = mg.uuid_branch
						inner join tr_task_h tth with (nolock)
						on tth.uuid_task_h = mg.uuid_task_h
						inner join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = tth.UUID_MS_USER
						where mg.customer_name like '%' + :customerName + '%'
						and mg.uuid_branch = :msBranch.uuidBranch
						and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'
					) as T where rn = 1
				) c
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]> 
	</sql-query>
	<sql-query name="survey.dm.getGroupTaskListByHierarkiUserCount">
	   	<query-param name="msBranch.uuidBranch" type="string" />
	   	<query-param name="customerName" type="string" />
	   	<query-param name="applNo" type="string" />
	   	<query-param name="uuidUser" type="string" />
		select count(distinct(group_task_id))
		from MS_GROUPTASK mg with(nolock)
			inner join tr_task_h tth with (nolock)
			on tth.uuid_task_h = mg.uuid_task_h
			inner join (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = tth.UUID_MS_USER
		where mg.customer_name like '%' + :customerName + '%'
			and mg.uuid_branch = :msBranch.uuidBranch
			and ISNULL(mg.appl_no, '%') like '%' + :applNo + '%'					
	</sql-query>
	
	<sql-query name="survey.dm.getBranchAll">
	select branch.uuid_branch ,branch.latitude,branch.longitude,branch.color, branch.branch_name,   
	(select count(UUID_MS_USER) 
					from am_msuser amu join ms_job mj 
					on amu.uuid_job = mj.uuid_job
					join  am_mssubsystem ams on amu.uuid_ms_subsystem = ams.uuid_ms_subsystem
					 where amu.uuid_branch = branch.uuid_branch
					 and mj.IS_FIELD_PERSON = '1'  
					 and amu.IS_ACTIVE = '1' 
					 and mj.IS_ACTIVE = '1'
					 and ams.subsystem_name = 'MS') FROM MS_BRANCH branch	
	</sql-query>
	
	<sql-query name="survey.dm.getSurveyorBranchWithColour">
	<query-param name="uuidBranch" type="string" />
	<query-param name="idxBranch" type="string" />
		WITH cte AS 
		( 
		    select trlh.*,
		     ROW_NUMBER() OVER (PARTITION BY trlh.UUID_MS_USER ORDER BY trlh.datetime DESC) AS rn 
				from tr_locationhistory trlh 
				where trlh.uuid_ms_user in 
				 (
					select UUID_MS_USER 
					from am_msuser amu join ms_job mj 
					on amu.uuid_job = mj.uuid_job
					join  am_mssubsystem ams on amu.uuid_ms_subsystem = ams.uuid_ms_subsystem
					 where amu.uuid_branch = :uuidBranch
					 and mj.IS_FIELD_PERSON = '1'  
					 and amu.IS_ACTIVE = '1' 
					 and mj.IS_ACTIVE = '1'
					 and ams.subsystem_name = 'MS'
				) and latitude is not null and longitude is not null
			) 
			SELECT amur.uuid_ms_user ,amur.FULL_NAME, cte.latitude, cte.longitude, mb.COLOR, :idxBranch
			FROM cte join AM_MSUSER amur on cte.uuid_ms_user = amur.uuid_ms_user
			join MS_BRANCH mb on mb.uuid_branch = amur.uuid_branch
			WHERE rn = 1
	</sql-query>
	<sql-query name="survey.dm.getBranchCountAll">
		select count (branch.uuid_branch) FROM MS_BRANCH branch			
	</sql-query>
	<sql-query name="survey.dm.getPrecentagebattery">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="idSubsystem" type="string" />
	    <query-param name="atb_code" type="string" />
		select memberAtb.ATTRIBUTE_VALUE from AM_ATTRIBUTEOFMEMBER memberAtb
		join AM_MSUSER usr
		on usr.UUID_MS_USER = memberAtb.UUID_MS_USER
		join AM_MSATTRIBUTE atb
		on atb.UUID_MS_ATTRIBUTE = memberAtb.UUID_MS_ATTRIBUTE
		join AM_MSSUBSYSTEM subs
		on subs.UUID_MS_SUBSYSTEM = atb.UUID_MS_SUBSYSTEM
		where usr.UUID_MS_USER = :uuidUser
		and subs.UUID_MS_SUBSYSTEM = :idSubsystem
		and atb.ATB_CODE = :atb_code
	</sql-query>
	
</hibernate-mapping>
