<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="survey.taskmonitoring">
   		<query-param name="uuidSvy" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
	   	SELECT trth.UUID_TASK_H, trth.TASK_ID, trth.CUSTOMER_NAME, trth.CUSTOMER_ADDRESS, trth.CUSTOMER_PHONE, trth.ZIP_CODE, msf.FORM_NAME,
			CASE 
				WHEN msm.STATUS_MOBILE_CODE = 'D'
					THEN 'grey'
				WHEN msm.STATUS_MOBILE_CODE = 'N'
					THEN 'red' 
				WHEN (trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end)
					AND msm.STATUS_MOBILE_CODE = 'S'
					THEN 'green'
				WHEN msm.STATUS_MOBILE_CODE = 'W'
					THEN 'orange'
				WHEN msm.STATUS_MOBILE_CODE = 'R' 
					THEN 'blue'
				WHEN msm.STATUS_MOBILE_CODE = 'O'   
					THEN 'purple'
				WHEN msm.STATUS_MOBILE_CODE = 'U'
					THEN 'yellow'
			END as status,
			CASE
		 	<![CDATA[WHEN DATEDIFF(minute, trth.ASSIGN_DATE, CURRENT_TIMESTAMP) <= (select GS_VALUE from AM_GENERALSETTING with (nolock) WHERE GS_CODE = 'SLA_TIME')*60]]>	
				THEN '0'
				ELSE '1'
			END as sla, trth.ASSIGN_DATE
			FROM TR_TASK_H trth with (nolock) JOIN MS_STATUSMOBILE msm with (nolock) ON trth.STATUS_MOBILE_SEQ_NO = msm.STATUS_MOBILE_SEQ_NO
			JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
			JOIN (select keyValue from dbo.getUserByLogin(:uuidSvy)) hrkUser on hrkUser.keyValue = trth.UUID_MS_USER
			WHERE ((TRTH.APPROVAL_DATE IS NOT NULL AND TRTH.APPROVAL_DATE BETWEEN (CASE WHEN msm.STATUS_MOBILE_CODE = 'D' THEN :start ELSE '1970-01-01' END) AND :end) OR TRTH.APPROVAL_DATE IS NULL)
			AND ((trth.SUBMIT_DATE IS NOT NULL AND trth.SUBMIT_DATE BETWEEN :start AND :end) OR (trth.SUBMIT_DATE IS NULL))
	</sql-query>
	
	<sql-query name="survey.taskmonitoring.sumnewtaskandsubmittedtask">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="start" type="string" />
	   	<query-param name="end" type="string" />
 		select ISNULL(sum(TOTAL_NEW_TASK), 0) TOTAL_NEW_TASK, ISNULL(sum(TOTAL_SUBMITTED_TASK), 0) TOTAL_SUBMITTED_TASK
		from TR_SURVEYDAILYSUMMARY tsd with (nolock)
		JOIN (select keyValue from dbo.getUserByLogin(:uuidSpv)) hrkUser on hrkUser.keyValue = tsd.UUID_MS_USER
		where UUID_MS_USER != :uuidSpv and
		DTM_CRT between :start and :end
	</sql-query>
	
	<sql-query name="survey.taskmonitoring.totalpending">
	   	<query-param name="uuidSpv" type="string" />
	   	<query-param name="subsystem" type="string" />
 		select count(UUID_TASK_H)
		from TR_TASK_H trth with (nolock) 
		JOIN (select keyValue from dbo.getUserByLogin(:uuidSpv)) hrkUser on hrkUser.keyValue = trth.UUID_MS_USER
		where UUID_MS_USER != :uuidSpv
			AND UUID_STATUS_TASK = 
				(SELECT UUID_STATUS_TASK FROM MS_STATUSTASK with (nolock) WHERE UUID_MS_SUBSYSTEM = :subsystem AND STATUS_CODE = 'N')
			AND SUBMIT_DATE IS NULL
	</sql-query>
	
	<sql-query name="survey.taskmonitoring.spvCombo">
	   	<query-param name="uuidBranch" type="string" />
	   	<query-param name="jobCode" type="string" />
 		SELECT amu.UUID_MS_USER, amu.FULL_NAME + ' - ' + msb.branch_name as FULL_NAME
		FROM AM_MSUSER amu with (nolock)
			join (SELECT keyValue as UUID_BRANCH, BRANCH_NAME FROM dbo.getCabangByLogin(:uuidBranch)) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
			JOIN MS_JOB mj with (nolock) ON amu.UUID_JOB = mj.UUID_JOB
				AND amu.UUID_MS_SUBSYSTEM = mj.UUID_MS_SUBSYSTEM
		WHERE mj.JOB_CODE in (:jobCode)
			AND amu.is_active = '1'
			AND amu.is_deleted = '0'
	</sql-query>
	
	<sql-query name="survey.taskmonitoring.spvComboByHierarkiUser">
	   	<query-param name="uuidUser" type="string" />
	   	<query-param name="jobCode" type="string" />
 		SELECT amu.UUID_MS_USER, amu.FULL_NAME + ' - ' + mb.branch_name as FULL_NAME
		FROM AM_MSUSER amu with (nolock)
			JOIN MS_BRANCH mb with (nolock) on mb.uuid_branch = amu.uuid_branch
			JOIN MS_JOB mj with (nolock) ON amu.UUID_JOB = mj.UUID_JOB
				AND amu.UUID_MS_SUBSYSTEM = mj.UUID_MS_SUBSYSTEM
			JOIN (select keyValue from dbo.getUserByLogin(:uuidUser)) hrkUser on hrkUser.keyValue = amu.UUID_MS_USER
		WHERE mj.JOB_CODE in (:jobCode)
			AND amu.is_active = '1'
			AND amu.is_deleted = '0'
	</sql-query>
</hibernate-mapping>