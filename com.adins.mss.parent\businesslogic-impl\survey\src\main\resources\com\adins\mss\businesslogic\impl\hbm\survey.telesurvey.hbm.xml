<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="survey.tele.getAllTaskDVersioning">
		<query-param name="uuidTaskH" type="long" />
		WITH N(
			UUID_TASK,USR_CRT,DTM_CRT,USR_UPD,DTM_UPD,
			UUID_TASK_H,UUID_QUESTION,QUESTION_TEXT,TEXT_ANSWER,OPTION_TEXT,
			IMAGE_PATH,LATITUDE,LONGITUDE,ACCURACY,MCC,MNC,LAC,CELL_ID,
			TIMESTAMP_TASK,IS_GPS,IS_GSM,GEOLOCATION_PROVIDER,IS_VISIBLE_QA,
			INT_TEXT_ANSWER,LOV_ID,INT_LOV_ID,FIN_LOV_ID,
			INT_OPTION_TEXT,FIN_TEXT_ANSWER,FIN_OPTION_TEXT,FIN_USE_IMAGE_FLAG,
			is_readonly,regex,IS_CONVERTED,is_image)
		AS (	
		  SELECT
		  	trtd.UUID_TASK_D,trtd.USR_CRT,trtd.DTM_CRT,trtd.USR_UPD,trtd.DTM_UPD,
		  	trtd.UUID_TASK_H,trtd.UUID_QUESTION,trtd.QUESTION_TEXT,trtd.TEXT_ANSWER,trtd.OPTION_TEXT,
		  	trtd.IMAGE_PATH,trtd.LATITUDE,trtd.LONGITUDE,trtd.ACCURACY,
		  	trtd.MCC,trtd.MNC,trtd.LAC,trtd.CELL_ID,
		  	trtd.TIMESTAMP_TASK,trtd.IS_GPS,trtd.IS_GSM,trtd.GEOLOCATION_PROVIDER,trtd.IS_VISIBLE_QA,
		  	trtd.INT_TEXT_ANSWER,trtd.LOV_ID,trtd.INT_LOV_ID,trtd.FIN_LOV_ID,
		  	trtd.INT_OPTION_TEXT,trtd.FIN_TEXT_ANSWER,trtd.FIN_OPTION_TEXT,trtd.FIN_USE_IMAGE_FLAG,
		  	trtd.is_readonly,trtd.regex,trtd.IS_CONVERTED,'0' is_image
	  	  FROM TR_TASK_D trtd with (nolock)			
      	  WHERE trtd.UUID_TASK_H = :uuidTaskH
	  	  UNION
	  	  SELECT
	  	  	trtd.UUID_TASK_DETAIL_LOB,trtd.USR_CRT,trtd.DTM_CRT,trtd.USR_UPD,trtd.DTM_UPD,
	  	  	trtd.UUID_TASK_H,trtd.QUESTION_ID,trtd.QUESTION_TEXT,trtd.TEXT_ANSWER,trtd.OPTION_TEXT,
	  	  	trtd.IMAGE_PATH,trtd.LATITUDE,trtd.LONGITUDE,trtd.ACCURACY,
	  	  	trtd.MCC,trtd.MNC,trtd.LAC,trtd.CELL_ID,
	  	  	trtd.TIMESTAMP_DETAIL,trtd.IS_GPS,trtd.IS_GSM,trtd.GEOLOCATION_PROVIDER,trtd.IS_VISIBLE_QA,
	  	  	trtd.INT_TEXT_ANSWER,trtd.LOV_ID,trtd.INT_LOV_ID,trtd.FIN_LOV_ID,
	  	  	trtd.INT_OPTION_TEXT,trtd.FIN_TEXT_ANSWER,trtd.FIN_OPTION_TEXT,trtd.FIN_USE_IMAGE_FLAG,
	  	  	'' AS is_readonly,'' AS regex,trtd.IS_CONVERTED,'1' is_image		  
	  	  FROM TR_TASKDETAILLOB trtd with (nolock)			
      	  WHERE trtd.UUID_TASK_H = :uuidTaskH
	  	)
	  	SELECT
	  		ISNULL(N.UUID_TASK, '') UUID_TASK,
	  		ISNULL(N.USR_CRT, '') USR_CRT, ISNULL(N.DTM_CRT, '') DTM_CRT,
	  		ISNULL(N.USR_UPD, '') USR_UPD, ISNULL(N.DTM_UPD, '') DTM_UPD,
	  		ISNULL(TRTH.UUID_TASK_H, '') UUID_TASK_H, ISNULL(mfqs.UUID_QUESTION, '') UUID_QUESTION, ISNULL(mfqs.QUESTION_LABEL, '') QUESTION_TEXT,
	  		ISNULL(N.TEXT_ANSWER, '') TEXT_ANSWER, ISNULL(N.OPTION_TEXT, '') OPTION_TEXT, ISNULL(N.IMAGE_PATH, '') IMAGE_PATH,
	  		N.LATITUDE, N.LONGITUDE, ISNULL(N.ACCURACY, 0) ACCURACY,
	  		ISNULL(N.MCC, 0) MCC, ISNULL(N.MNC, 0) MNC, ISNULL(N.LAC, 0) LAC, ISNULL(N.CELL_ID, '') CELL_ID,
	  		ISNULL(N.TIMESTAMP_TASK, '') TIMESTAMP_TASK, ISNULL(N.IS_GPS, '') IS_GPS, ISNULL(N.IS_GSM, '') IS_GSM,
	  		ISNULL(N.GEOLOCATION_PROVIDER, '') GEOLOCATION_PROVIDER, ISNULL(N.IS_VISIBLE_QA, '') IS_VISIBLE_QA,
	  		ISNULL(N.INT_TEXT_ANSWER, '') INT_TEXT_ANSWER, ISNULL(N.LOV_ID, '') LOV_ID, ISNULL(N.INT_LOV_ID, '') INT_LOV_ID, ISNULL(N.FIN_LOV_ID, '') FIN_LOV_ID,
	  		ISNULL(N.INT_OPTION_TEXT, '') INT_OPTION_TEXT, ISNULL(N.FIN_TEXT_ANSWER, '') FIN_TEXT_ANSWER, ISNULL(N.FIN_OPTION_TEXT, '') FIN_OPTION_TEXT,
	  		ISNULL(N.FIN_USE_IMAGE_FLAG, '') FIN_USE_IMAGE_FLAG, ISNULL(mfqs.is_readonly, '') is_readonly, ISNULL(mfqs.regex_pattern, '') regex,
	  		ISNULL(N.IS_CONVERTED, '') IS_CONVERTED, ISNULL(N.is_image, '') is_image		  
		FROM TR_TASK_H TRTH with (nolock) 
			LEFT JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = trth.UUID_FORM AND mfh.FORM_VERSION = trth.FORM_VERSION
			LEFT JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
			LEFT JOIN N ON TRTH.UUID_TASK_H = N.UUID_TASK_H AND N.UUID_QUESTION = mfqs.UUID_QUESTION
		WHERE TRTH.UUID_TASK_H = :uuidTaskH
			AND mfqs.QUESTION_IS_ACTIVE = '1'
			AND mfqs.QUESTION_GROUP_IS_ACTIVE = '1'
		ORDER BY mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ
	</sql-query>
	<sql-query name="survey.tele.getAnswerVersioning">
		<query-param name="uuidTaskH" type="long" />
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		SELECT TOP 1
			ISNULL(N.UUID_TASK, '') UUID_TASK,
			ISNULL(N.USR_CRT, '') USR_CRT, ISNULL(N.DTM_CRT, '') DTM_CRT, ISNULL(N.USR_UPD, '') USR_UPD, ISNULL(N.DTM_UPD, '') DTM_UPD,
			ISNULL(TRTH.UUID_TASK_H, '') UUID_TASK_H, ISNULL(mfqs.UUID_QUESTION, '') UUID_QUESTION, ISNULL(mfqs.QUESTION_LABEL, '') QUESTION_TEXT,
			ISNULL(N.TEXT_ANSWER, '') TEXT_ANSWER, ISNULL(N.OPTION_TEXT, '') OPTION_TEXT, ISNULL(N.IMAGE_PATH, '') IMAGE_PATH,
			ISNULL(N.LATITUDE, 0) LATITUDE, ISNULL(N.LONGITUDE, 0) LONGITUDE, ISNULL(N.ACCURACY, 0) ACCURACY,
			ISNULL(N.MCC, 0) MCC, ISNULL(N.MNC, 0) MNC, ISNULL(N.LAC, 0) LAC, ISNULL(N.CELL_ID, '') CELL_ID,
			ISNULL(N.TIMESTAMP_TASK, '') TIMESTAMP_TASK, ISNULL(N.IS_GPS, '') IS_GPS, ISNULL(N.IS_GSM, '') IS_GSM,
			ISNULL(N.GEOLOCATION_PROVIDER, '') GEOLOCATION_PROVIDER, ISNULL(N.IS_VISIBLE_QA, '') IS_VISIBLE_QA,
			ISNULL(N.INT_TEXT_ANSWER, '') INT_TEXT_ANSWER, ISNULL(N.LOV_ID, '') LOV_ID, ISNULL(N.INT_LOV_ID, '') INT_LOV_ID, 
			ISNULL(N.FIN_LOV_ID, '') FIN_LOV_ID, ISNULL(N.INT_OPTION_TEXT, '') INT_OPTION_TEXT, ISNULL(N.FIN_TEXT_ANSWER, '') FIN_TEXT_ANSWER, 
			ISNULL(N.FIN_OPTION_TEXT, '') FIN_OPTION_TEXT, ISNULL(N.FIN_USE_IMAGE_FLAG, '') FIN_USE_IMAGE_FLAG, 
			ISNULL(N.is_readonly, '') is_readonly, ISNULL(N.regex, '') regex, ISNULL(N.IS_CONVERTED, '') IS_CONVERTED
		FROM TR_TASK_H TRTH with (nolock) 
			LEFT JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = trth.UUID_FORM AND mfh.FORM_VERSION = trth.FORM_VERSION
			LEFT JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
			LEFT JOIN TR_TASK_D N ON TRTH.UUID_TASK_H = N.UUID_TASK_H AND N.UUID_QUESTION = mfqs.UUID_QUESTION AND N.UUID_FORM = TRTH.UUID_FORM
		WHERE TRTH.UUID_TASK_H = :uuidTaskH 
		  and mfqs.REF_ID = :refId
		  AND TRTH.UUID_FORM = :uuidForm
		  AND mfqs.QUESTION_IS_ACTIVE = '1'
		  AND mfqs.QUESTION_GROUP_IS_ACTIVE = '1'
		ORDER BY mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ
	</sql-query>
	<sql-query name="survey.tele.listOptionAnswerWithConstraint">
	    <query-param name="lovGroup" type="string" />
	     <query-param name="constraint1" type="string" />
	     <query-param name="isActive" type="string" />
		 SELECT CODE,DESCRIPTION,SEQUENCE
		 FROM MS_LOV with (nolock)
		 WHERE LOV_GROUP = :lovGroup
		 	AND CONSTRAINT_1 = :constraint1
		 	AND IS_ACTIVE = :isActive
		 ORDER BY SEQUENCE
	</sql-query>
	
	<sql-query name="survey.tele.listOptionAnswerWithConstraintByBranch">
		<query-param name="lovGroup" type="string" />
	    <query-param name="uuidBranch" type="string" />
	    <query-param name="constraint1" type="string" />
	    <query-param name="constraint2" type="string" />
	    <query-param name="constraint3" type="string" />
	    <query-param name="constraint4" type="string" />
	    <query-param name="constraint5" type="string" />
	    <query-param name="isActive" type="string" />
		SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE
		FROM MS_LOV t1 with (nolock)
			left join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV
		WHERE LOV_GROUP = :lovGroup
			and t1.IS_DELETED = '0'
			and t1.IS_ACTIVE = :isActive
			and mbl.UUID_LOV is null
			and (t1.CONSTRAINT_1 IN (:constraint1) OR t1.CONSTRAINT_1 IS NULL)
			and (t1.CONSTRAINT_2 IN (:constraint2) OR t1.CONSTRAINT_2 IS NULL)
			and (t1.CONSTRAINT_3 IN (:constraint3) OR t1.CONSTRAINT_3 IS NULL)
			and (t1.CONSTRAINT_4 IN (:constraint4) OR t1.CONSTRAINT_4 IS NULL)
			and (t1.CONSTRAINT_5 IN (:constraint5) OR t1.CONSTRAINT_5 IS NULL)
		union 
		SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE
		FROM MS_LOV t1 with (nolock)
			join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV
		WHERE LOV_GROUP = :lovGroup
		 	and mbl.IS_DELETED = '0'
		 	and t1.IS_ACTIVE = :isActive
		 	and mbl.UUID_BRANCH = :uuidBranch
		 	and (t1.CONSTRAINT_1 IN (:constraint1) OR t1.CONSTRAINT_1 IS NULL)
		 	and (t1.CONSTRAINT_2 IN (:constraint2) OR t1.CONSTRAINT_2 IS NULL)
		 	and (t1.CONSTRAINT_3 IN (:constraint3) OR t1.CONSTRAINT_3 IS NULL)
		 	and (t1.CONSTRAINT_4 IN (:constraint4) OR t1.CONSTRAINT_4 IS NULL)
		 	and (t1.CONSTRAINT_5 IN (:constraint5) OR t1.CONSTRAINT_5 IS NULL)
		ORDER BY t1.SEQUENCE
	</sql-query>
	
	
	<sql-query name="survey.tele.listOptionAnswerNoConstraint">
	    <query-param name="lovGroup" type="string" />
	    <query-param name="isActive" type="string" />
		 SELECT CODE,DESCRIPTION,SEQUENCE
		 FROM MS_LOV with (nolock)
		 WHERE LOV_GROUP = :lovGroup
		 	AND IS_ACTIVE = :isActive
		 ORDER BY SEQUENCE
	</sql-query>
	
	<sql-query name="survey.tele.listOptionAnswerNoConstraintByBranch">
	    <query-param name="lovGroup" type="string" />
	    <query-param name="uuidBranch" type="string" />
	    <query-param name="isActive" type="string" />
		SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE
		FROM MS_LOV t1 with (nolock)
			left join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV
		WHERE LOV_GROUP = :lovGroup
			and t1.IS_DELETED = '0'
			and t1.IS_ACTIVE = :isActive
			and mbl.UUID_LOV is null
		union
		SELECT t1.CODE, t1.DESCRIPTION, t1.SEQUENCE
		FROM MS_LOV t1 with (nolock)
			join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV
		WHERE LOV_GROUP = :lovGroup
			and mbl.IS_DELETED = '0'
			and t1.IS_ACTIVE = :isActive
			and mbl.UUID_BRANCH = :uuidBranch
		ORDER BY t1.SEQUENCE
	</sql-query>
	
	<sql-query name="survey.tele.getLovByBranch">
	    <query-param name="lovGroup" type="string" />
	    <query-param name="uudiBranch" type="long" />
	    <query-param name="isActive" type="string" />
	    <return-scalar column="uuidLov" type="long"/>
		SELECT t1.UUID_LOV as bigIntUuidLov, t1.IS_ACTIVE as isActive, t1.IS_DELETED as isDeleted,
	    	t1.USR_CRT as usrCrt, t1.DTM_CRT as dtmCrt, t1.USR_UPD as usrUpd, t1.DTM_UPD as dtmUpd,
	        t1.LOV_GROUP as lovGroup, t1.CODE as code, t1.DESCRIPTION as description,
	        t1.SEQUENCE as sequence, t1.CONSTRAINT_1 as constraint1, t1.CONSTRAINT_2 as constraint2,
	        t1.CONSTRAINT_3 as constraint3, t1.CONSTRAINT_4 as constraint4, t1.CONSTRAINT_5 as constraint5
		FROM MS_LOV t1 with (nolock)
			left join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV
		WHERE LOV_GROUP = :lovGroup
			and t1.IS_DELETED = '0'
			and t1.IS_ACTIVE = :isActive
			and mbl.UUID_LOV is null
		union
		SELECT t1.UUID_LOV as bigIntUuidLov, t1.IS_ACTIVE as isActive, t1.IS_DELETED as isDeleted,
	        t1.USR_CRT as usrCrt, t1.DTM_CRT as dtmCrt, t1.USR_UPD as usrUpd, t1.DTM_UPD as dtmUpd,
	        t1.LOV_GROUP as lovGroup, t1.CODE as code, t1.DESCRIPTION as description,
	        t1.SEQUENCE as sequence, t1.CONSTRAINT_1 as constraint1, t1.CONSTRAINT_2 as constraint2,
	        t1.CONSTRAINT_3 as constraint3, t1.CONSTRAINT_4 as constraint4, t1.CONSTRAINT_5 as constraint5
		FROM MS_LOV t1 with (nolock)
			join MS_BRANCHOFLOV mbl with (nolock) on t1.UUID_LOV = mbl.UUID_LOV
		WHERE LOV_GROUP = :lovGroup
			and mbl.IS_DELETED = '0'
			and t1.IS_ACTIVE = :isActive
			and mbl.UUID_BRANCH = :uuidBranch
		order by t1.SEQUENCE
	</sql-query>
	
	<sql-query name="survey.tele.getLovNoCons">
		<query-param name="lovGroup" type="string" />
		<query-param name="code" type="string" />
		SELECT UUID_LOV
		FROM MS_LOV with (nolock)
		WHERE LOV_GROUP = :lovGroup
			AND code = :code
		 	AND (CONSTRAINT_1 IS NULL OR CONSTRAINT_1 = '')
		 	AND (CONSTRAINT_2 IS NULL OR CONSTRAINT_2 = '')
		 	AND (CONSTRAINT_3 IS NULL OR CONSTRAINT_3 = '')
		 	AND (CONSTRAINT_4 IS NULL OR CONSTRAINT_4 = '')
		 	AND (CONSTRAINT_5 IS NULL OR CONSTRAINT_5 = '')
		 	AND IS_ACTIVE = 1
	</sql-query>
	
	<sql-query name="survey.tele.getDetailByLov">
		<query-param name="msLovId" type="long" />
		<query-param name="uuidTaskH" type="long" />
		<query-param name="uuidQuestion" type="long" />
		SELECT UUID_TASK_D
		FROM TR_TASK_D with (nolock)
		WHERE INT_LOV_ID = :msLovId
			AND UUID_TASK_H = :uuidTaskH
			AND UUID_QUESTION = :uuidQuestion
	</sql-query>
	<sql-query name="survey.tele.getExportHeader">
		<query-param name="uuidTaskH" type="long" />
		SELECT
			isnull(trth.TASK_ID,'-') taskId,
			isnull(trth.APPL_NO, '-') applNo,
			isnull(amu.FULL_NAME, '-') fullName,
			isnull(msb.BRANCH_NAME, '-') branchName,
			isnull(msf.FORM_NAME, '-') formName,
			isnull(trth.CUSTOMER_NAME, '-') customerName,
			isnull(trth.CUSTOMER_PHONE, '-') customerPhone,
			isnull(trth.CUSTOMER_ADDRESS, '-') customerAddress
		FROM TR_TASK_H trth with (nolock)
			LEFT JOIN AM_MSUSER amu with (nolock) ON trth.UUID_MS_USER = amu.UUID_MS_USER
			LEFT JOIN MS_BRANCH msb with (nolock) ON trth.UUID_BRANCH = msb.UUID_BRANCH
			LEFT JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
		WHERE trth.UUID_TASK_H = :uuidTaskH
	</sql-query>	

	<sql-query name="survey.tele.getExportAnswer">
		<query-param name="uuidTaskH" type="long" />
		WITH N AS (
			SELECT
		  		trtd.UUID_TASK_H, trtd.UUID_QUESTION, trtd.QUESTION_TEXT, trtd.INT_TEXT_ANSWER,
				trtd.INT_OPTION_TEXT, trtd.IMAGE_PATH, 0 as UUID_TASK_DETAIL_LOB, '0' IS_IMAGE
			FROM TR_TASK_D trtd with (nolock) 
		   	WHERE trtd.UUID_TASK_H = :uuidTaskH
		  	UNION
		  	SELECT
		  		trtdl.UUID_TASK_H, trtdl.QUESTION_ID UUID_QUESTION, trtdl.QUESTION_TEXT, trtdl.INT_TEXT_ANSWER, 
				trtdl.INT_OPTION_TEXT, trtdl.IMAGE_PATH, trtdl.UUID_TASK_DETAIL_LOB, '1' IS_IMAGE
			FROM TR_TASKDETAILLOB trtdl with (nolock)
		   	WHERE trtdl.UUID_TASK_H = :uuidTaskH
		)
		SELECT
			mfqs.REF_ID, mfqs.QUESTION_LABEL,
			CASE
				WHEN ans.code_answer_type in ('001','002','003','004','005','013','014','015','024','025','026') THEN ISNULL(n.INT_TEXT_ANSWER,'')
				WHEN ans.code_answer_type in ('006','007','008','009','010','011','012') THEN ISNULL(n.INT_OPTION_TEXT, '')
				ELSE CAST(N.UUID_TASK_DETAIL_LOB AS VARCHAR)
			END AS ANSWER,
			Case
				WHEN (mfqs.LOV_GROUP is null or mfqs.LOV_GROUP = '') THEN '0'
				ELSE '1'
			END AS HAS_LOV,
			ISNULL(mfqs.LOV_GROUP, '') as LOV_GROUP, mfqs.IS_MANDATORY, mfqs.MAX_LENGTH, ans.ANSWER_TYPE_NAME, ans.code_answer_type
		FROM TR_TASK_H trth with (nolock)
			LEFT JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = trth.UUID_FORM AND mfh.FORM_VERSION = trth.FORM_VERSION
			LEFT JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
			LEFT JOIN N ON TRTH.UUID_TASK_H = N.UUID_TASK_H AND N.UUID_QUESTION = mfqs.UUID_QUESTION			
			INNER JOIN MS_ANSWERTYPE ans with (nolock) ON mfqs.UUID_ANSWER_TYPE = ans.UUID_ANSWER_TYPE
		where trth.UUID_TASK_H = :uuidTaskH
			AND mfqs.QUESTION_IS_ACTIVE = '1'
		  	AND mfqs.QUESTION_GROUP_IS_ACTIVE = '1'
		order by mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ
	</sql-query>
	
	<sql-query name="survey.tele.getRelevantScript">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		<query-param name="uuidtaskH" type="long" />
		select RELEVANT 
		from MS_FORMQUESTIONSET with (nolock)
		where UUID_QUESTION = 
		( 
			select UUID_QUESTION 
			from MS_QUESTION with (nolock) 
			where ref_id = :refId
		)
			and UUID_FORM_HISTORY = 
			(
				select UUID_FORM_HISTORY 
				from MS_FORMHISTORY with (nolock)
				where UUID_FORM = :uuidForm 
				and form_version = 
				(
					select form_version 
					from tr_Task_h with (nolock)
					where uuid_task_h = :uuidtaskH
				)
			)
	</sql-query>
	
	<sql-query name="survey.tele.getQuestRelevantCopy">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		<query-param name="uuidtaskH" type="long" />
		select UUID_QUESTION, REF_ID
		from MS_FORMQUESTIONSET with (nolock)
		where QUESTION_VALUE like '%{' + :refId + '}%'
			and UUID_FORM_HISTORY = 
			(
				select UUID_FORM_HISTORY 
				from MS_FORMHISTORY with (nolock)
				where UUID_FORM = :uuidForm and form_version = 
				(
					select form_version 
					from tr_Task_h with (nolock) 
					where uuid_task_h = :uuidtaskH
				)
			)
	</sql-query>
	
	<sql-query name="survey.tele.getScriptCopyValue">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		<query-param name="uuidtaskH" type="long" />
		select QUESTION_VALUE 
		from MS_FORMQUESTIONSET with (nolock)
		WHERE UUID_QUESTION = 
		( 
			select UUID_QUESTION 
			from MS_QUESTION with (nolock)
			where ref_id = :refId 
		)
			and UUID_FORM_HISTORY = 
			(
				select UUID_FORM_HISTORY 
				from MS_FORMHISTORY with (nolock)
				where UUID_FORM = :uuidForm 
				and form_version = 
				(
					select form_version 
					from tr_Task_h with (nolock) 
					where uuid_task_h = :uuidtaskH
				)
			)
	</sql-query>
	
	<sql-query name="survey.tele.getScriptValidation">
		<query-param name="refId" type="string" />
		<query-param name="uuidForm" type="long" />
		<query-param name="uuidtaskH" type="long" />
		select QUESTION_VALIDATION, QUESTION_ERROR_MESSAGE 
		from MS_FORMQUESTIONSET with (nolock)
		WHERE UUID_QUESTION = 
		( 
			select UUID_QUESTION 
			from MS_QUESTION with (nolock) 
			where ref_id = :refId 
		)
			and UUID_FORM_HISTORY = 
			(
				select UUID_FORM_HISTORY 
				from MS_FORMHISTORY with (nolock) 
				where UUID_FORM = :uuidForm and form_version = 
				(
					select form_version 
					from tr_Task_h with (nolock) 
					where uuid_task_h = :uuidtaskH
				)
			)
	</sql-query>
	
</hibernate-mapping>
