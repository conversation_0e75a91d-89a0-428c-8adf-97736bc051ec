<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>

 	<!-- ////// get total task assignment ////// -->
	<sql-query name="task.assignpts.getTotalTaskAssignment">
		<query-param name="uuidMsUser" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT COUNT(UUID_TASK_H) taskAssignment
		FROM TR_TASK_H with (nolock)
		WHERE ASSIGN_DATE IS NOT NULL 
			AND ASSIGN_DATE BETWEEN :start AND :end
			AND UUID_MS_USER = :uuidMsUser
	</sql-query>
	
	<!-- ////// get total submitted task ////// -->
	<sql-query name="task.assignpts.getTotalSubmittedTask">
		<query-param name="uuidMsUser" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT COUNT(UUID_TASK_H) submittedTask
		FROM TR_TASK_H with (nolock)
		WHERE SUBMIT_DATE IS NOT NULL 
			AND SUBMIT_DATE BETWEEN :start AND :end
			AND UUID_MS_USER = :uuidMsUser
	</sql-query>
	
	<!-- ////// get total pending task ////// -->
	<sql-query name="task.assignpts.getTotalPendingTask">
		<query-param name="uuidMsUser" type="string"/>
		<query-param name="uuidStatusTask" type="string"/>
		SELECT COUNT(1)
		FROM TR_TASK_H with (nolock)
		WHERE UUID_MS_USER = :uuidMsUser
			AND UUID_STATUS_TASK = :uuidStatusTask
	</sql-query>
	
	<!-- ////// get Last Location ////// -->
	<sql-query name="task.assignpts.getLastLoc">
		<query-param name="uuidMsUser" type="string"/>
			select TOP 1 * from TR_LOCATIONHISTORY with (nolock) where UUID_MS_USER = :uuidMsUser order by DTM_CRT DESC
	</sql-query>

	<sql-query name="task.assignpts.getPtsTaskAssign">
		<query-param name="uuidTaskH" type="string"/>
		select pts.UUID_TASK_H, pts.CUSTOMER_NAME, pts.CUSTOMER_ADDRESS, pts.CUSTOMER_PHONE,
			ISNULL (br.BRANCH_NAME, '-' ) as BRANCH_NAME, 
			ISNULL (LEFT(CONVERT(VARCHAR, prev.ASSIGN_DATE, 113), 17), '-' ) as ASSIGN_DATE, 
			ISNULL (prevusr.FULL_NAME, '-' ) as FULL_NAME,
			ISNULL (LEFT(CONVERT(VARCHAR, prev.SUBMIT_DATE, 113), 17), '-' ) as RESCHEDULE_DATE,
			LEFT(CONVERT(VARCHAR, pts.PTS_DATE, 113), 17) as PTS_DATE,
			fr.FORM_NAME, ISNULL(pts.APPL_NO, '-') as APPL_NO 
		from tr_task_h pts with (nolock)
			left join ms_form fr with (nolock) on pts.UUID_FORM = fr.UUID_FORM
			left join ms_branch br with (nolock) on pts.UUID_BRANCH = br.UUID_BRANCH
			left join tr_task_h prev with (nolock) on pts.RESURVEY_ID = prev.UUID_TASK_H
			left join am_msuser prevusr with (nolock) on prev.UUID_MS_USER = prevusr. UUID_MS_USER
		where pts.UUID_TASK_H in (:uuidTaskH)
		ORDER BY pts.PTS_DATE, pts.CUSTOMER_NAME ASC
	</sql-query>
	
	<sql-query name="task.assignpts.getUuidTaskPtsScheduler">
		<query-param name="statusCode" type="string"/>
		<query-param name="subsysMo" type="string"/>
		select pts.UUID_TASK_H
		from TR_TASK_H pts with (nolock)
			join MS_STATUSTASK st with (nolock) on pts.UUID_STATUS_TASK = st.UUID_STATUS_TASK
		where st.STATUS_CODE = :statusCode
			and pts.PTS_DATE is not null
			and ( ( pts.RESURVEY_ID is null and pts.FLAG_SOURCE = :subsysMo )  
				or (pts.RESURVEY_ID is not null ) )
		order by pts.PTS_DATE
	</sql-query>
	
	<!-- query untuk menampilkan list assign PTS by hierarki user -->
	<sql-query name="task.assignpts.getPtsTaskListByHierarkiUser">
		<query-param name="uuidBranch" type="string"/>
		<query-param name="uuidStatusTask" type="string"/>
		<query-param name="uuidMsUser" type="string"/>
		<query-param name="customerName" type="string"/>
		<query-param name="applNo" type="string"/>
		<query-param name="rescheduleBy" type="string"/>
		<query-param name="ptsDateStart" type="string"/>
		<query-param name="ptsDateEnd" type="string"/>
		<query-param name="start" type="string"/>
		<query-param name="end" type="string"/>
		SELECT * from (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select DISTINCT *, ROW_NUMBER() OVER (ORDER BY PTS_DATE, CUSTOMER_NAME ASC) AS rownum from
					( 
					select pts.UUID_TASK_H, pts.CUSTOMER_NAME, pts.CUSTOMER_ADDRESS, pts.CUSTOMER_PHONE,
						ISNULL (br.BRANCH_NAME, '-' ) as BRANCH_NAME, 
						ISNULL (LEFT(CONVERT(VARCHAR, prev.ASSIGN_DATE, 113), 17), '-' ) as ASSIGN_DATE, 
						ISNULL (prevusr.FULL_NAME, '-' ) as FULL_NAME,
						ISNULL (LEFT(CONVERT(VARCHAR, prev.SUBMIT_DATE, 113), 17), '-' ) as RESCHEDULE_DATE,
						LEFT(CONVERT(VARCHAR, pts.PTS_DATE, 113), 17) as PTS_DATE, fr.FORM_NAME, ISNULL(pts.APPL_NO, '-') as APPL_NO
					from tr_task_h pts with (nolock)
						left join ms_form fr with (nolock) on pts.UUID_FORM = fr.UUID_FORM
						left join ms_branch br with (nolock) on pts.UUID_BRANCH = br.UUID_BRANCH
						left join tr_task_h prev with (nolock) on pts.RESURVEY_ID = prev.UUID_TASK_H
						left join am_msuser prevusr with (nolock) on prev.UUID_MS_USER = prevusr. UUID_MS_USER
						inner join 
						(
							select keyValue 
							from dbo.getUserByLogin(:uuidMsUser)
						) hrkUser on hrkUser.keyValue = pts.UUID_MS_USER
					where pts.uuid_branch = :uuidBranch
					and pts.uuid_status_task = :uuidStatusTask
					and ( pts.customer_name like '%' + :customerName + '%' or pts.customer_name is null)
					and ( pts.APPL_NO like '%' + :applNo + '%' or pts.APPL_NO is null)
					and ( prevusr.FULL_NAME like '%' + :rescheduleBy + '%' or prevusr.FULL_NAME is null)
					AND COALESCE(pts.PTS_DATE, '1990-01-01 00:00:00')
						BETWEEN (CASE WHEN :ptsDateStart = '%' THEN '1990-01-01 00:00:00' ELSE :ptsDateStart END)
						AND (CASE WHEN :ptsDateEnd = '%' THEN '9999-12-31 23:59:59' ELSE :ptsDateEnd END)
					and pts.pts_date is not null
					and ( ( pts.RESURVEY_ID is null and pts.FLAG_SOURCE = 'MO' )  or (pts.RESURVEY_ID is not null ) ) 
				) as UT
			) a <![CDATA[ WHERE a.rownum <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.assignpts.getPtsTaskListCountByHierarkiUser">
		<query-param name="uuidBranch" type="string"/>
		<query-param name="uuidStatusTask" type="string"/>
		<query-param name="uuidMsUser" type="string"/>
		<query-param name="customerName" type="string"/>
		<query-param name="applNo" type="string"/>
		<query-param name="rescheduleBy" type="string"/>
		<query-param name="ptsDateStart" type="string"/>
		<query-param name="ptsDateEnd" type="string"/>
		select count(1) from (
			select DISTINCT *, ROW_NUMBER() OVER (ORDER BY PTS_DATE, CUSTOMER_NAME ASC) AS rownum from
				(
				select pts.UUID_TASK_H, pts.CUSTOMER_NAME, pts.CUSTOMER_ADDRESS, pts.CUSTOMER_PHONE,
					ISNULL (br.BRANCH_NAME, '-' ) as BRANCH_NAME, 
					ISNULL (LEFT(CONVERT(VARCHAR, prev.ASSIGN_DATE, 113), 17), '-' ) as ASSIGN_DATE, 
					ISNULL (prevusr.FULL_NAME, '-' ) as FULL_NAME,
					ISNULL (LEFT(CONVERT(VARCHAR, prev.SUBMIT_DATE, 113), 17), '-' ) as RESCHEDULE_DATE,
					LEFT(CONVERT(VARCHAR, pts.PTS_DATE, 113), 17) as PTS_DATE, fr.FORM_NAME, ISNULL(pts.APPL_NO, '-') as APPL_NO
				from tr_task_h pts with (nolock)
					left join ms_form fr with (nolock) on pts.UUID_FORM = fr.UUID_FORM
					left join ms_branch br with (nolock) on pts.UUID_BRANCH = br.UUID_BRANCH
					left join tr_task_h prev with (nolock) on pts.RESURVEY_ID = prev.UUID_TASK_H
					left join am_msuser prevusr with (nolock) on prev.UUID_MS_USER = prevusr. UUID_MS_USER
					inner join 
					(
						select keyValue 
						from dbo.getUserByLogin(:uuidMsUser)
					) hrkUser on hrkUser.keyValue = pts.UUID_MS_USER
				where pts.uuid_branch = :uuidBranch
					and pts.uuid_status_task = :uuidStatusTask 
					and ( pts.customer_name like '%' + :customerName + '%' or pts.customer_name is null)
					and ( pts.APPL_NO like '%' + :applNo + '%' or pts.APPL_NO is null)
					and ( prevusr.FULL_NAME like '%' + :rescheduleBy + '%' or prevusr.FULL_NAME is null)
					AND COALESCE(pts.PTS_DATE, '1990-01-01 00:00:00')
						BETWEEN (CASE WHEN :ptsDateStart = '%' THEN '1990-01-01 00:00:00' ELSE :ptsDateStart END)
						AND (CASE WHEN :ptsDateEnd = '%' THEN '9999-12-31 23:59:59' ELSE :ptsDateEnd END)
					and pts.pts_date is not null
					and ( ( pts.RESURVEY_ID is null and pts.FLAG_SOURCE = 'MO' )  or (pts.RESURVEY_ID is not null ) )
				) as UT2 
		) as UT
	</sql-query>
</hibernate-mapping>
