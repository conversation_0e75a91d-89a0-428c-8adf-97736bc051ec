<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="task.createtask.getPublishedFormNewLead">
		WITH N AS (
			SELECT DISTINCT MSF.UUID_FORM UUIDFORM, MSF.FORM_NAME FORMNAME, MSFH.UUID_FORM_HISTORY UUIDFORMHISTORY, MSFH.FORM_VERSION VERSION
			FROM MS_FORM MSF WITH (NOLOCK)
				JOIN MS_FORMCATEGORY MSFC WITH (NOLOCK) ON MSFC.UUID_FORM_CATEGORY = MSF.UUID_FORM_CATEGORY
				JOIN MS_FORMHISTORY MSFH WITH (NOLOCK) ON MSFH.UUID_FORM = MSF.UUID_FORM
			WHERE MSF.IS_ACTIVE = '1' AND MSFC.CATEGORY_DESC = 'Staging' AND MSF.FORM_NAME='Form Input New Lead'
		)
		SELECT CAST(N.UUIDFORM AS VARCHAR(10)) +';' + CAST(N.UUIDFORMHISTORY AS VARCHAR(10)) AS 'KEY', N.FORMNAME AS 'VALUE'
		FROM N
			INNER JOIN (
				SELECT HIST.UUID_FORM, MAX(HIST.FORM_VERSION) AS "VERSION"
				FROM MS_FORMHISTORY HIST
				GROUP BY HIST.UUID_FORM
			) GRUP ON GRUP.UUID_FORM = N.UUIDFORM AND GRUP.VERSION = N.VERSION
	</sql-query>
	
	<sql-query name="task.createtask.getPublishedFormIA">
		WITH N AS (
			SELECT DISTINCT MSF.UUID_FORM UUIDFORM, MSF.FORM_NAME FORMNAME, MSFH.UUID_FORM_HISTORY UUIDFORMHISTORY, MSFH.FORM_VERSION VERSION
			FROM MS_FORM MSF WITH (NOLOCK)
				JOIN MS_FORMCATEGORY MSFC WITH (NOLOCK) ON MSFC.UUID_FORM_CATEGORY = MSF.UUID_FORM_CATEGORY
				JOIN MS_FORMHISTORY MSFH WITH (NOLOCK) ON MSFH.UUID_FORM = MSF.UUID_FORM
			WHERE MSF.IS_ACTIVE = '1' AND MSFC.CATEGORY_DESC = 'Staging' AND MSF.FORM_NAME='Form Input New Lead Instant Approval'
		)
		SELECT CAST(N.UUIDFORM AS VARCHAR(10)) +';' + CAST(N.UUIDFORMHISTORY AS VARCHAR(10)) AS 'KEY', N.FORMNAME AS 'VALUE'
		FROM N
			INNER JOIN (
				SELECT HIST.UUID_FORM, MAX(HIST.FORM_VERSION) AS "VERSION"
				FROM MS_FORMHISTORY HIST
				GROUP BY HIST.UUID_FORM
			) GRUP ON GRUP.UUID_FORM = N.UUIDFORM AND GRUP.VERSION = N.VERSION
	</sql-query>
	
	<sql-query name="task.createtask.getTaskDByTagRevId">
		<query-param name="uuidTaskH" type="string" />		
		  SELECT ma.ASSET_TAG_NAME, mq.REF_ID, ttd.QUESTION_TEXT, 
			CASE 
				WHEN MSA.code_answer_type IN ('009', '011', '019', '020', '025') THEN TTD.OPTION_TEXT
				ELSE TTD.TEXT_ANSWER 
			END
		FROM TR_TASK_D ttd with (nolock)
		join MS_QUESTION mq with (nolock) On ttd.UUID_QUESTION = mq.UUID_QUESTION
		JOIN MS_ANSWERTYPE MSA WITH(NOLOCK) ON MSA.UUID_ANSWER_TYPE = MQ.UUID_ANSWER_TYPE
		left join MS_ASSETTAG ma with (nolock)
		on mq.UUID_ASSET_TAG = ma.UUID_ASSET_TAG
		WHERE ttd.UUID_TASK_H = :uuidTaskH			
	</sql-query>
	
	<sql-query name="task.createtask.insertOcr">
		<query-param name="nik" type="String"/>
		<query-param name="usrCrt" type="String"/>
		<query-param name="ocrJson" type="String"/>
		<query-param name="statusOcr" type="String"/>
		INSERT INTO TBL_OCR (NIK, USR_CRT, DTM_CRT, OCR_JSON, STATUS_OCR)
		VALUES (:nik, :usrCrt, getdate(), :ocrJson, :statusOcr)
	</sql-query>
	
	<sql-query name="task.createtask.listPreIA">	   
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
		<query-param name="sendDateStart" type="string"/>
		<query-param name="sendDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		SELECT * FROM (
			SELECT a.*, ROW_NUMBER() OVER (ORDER BY rownum) AS recnum FROM (
				select 
					trth.TASK_ID,
					trth.CUSTOMER_NAME,
					trth.CUSTOMER_ADDRESS,
					trth.APPL_NO,			
					trth.SEND_DATE,
					ROW_NUMBER() OVER (ORDER BY msp.PRIORITY_DESC asc,trth.submit_date asc) AS rownum
				from tr_task_h trth with (nolock)					
					JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
					JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
     				JOIN MS_FORMCATEGORY msfc with (nolock) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY   		
				where mst.STATUS_CODE = 'WS'
					AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
					AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
					AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
					AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
					AND trth.SEND_DATE IS NOT NULL
					AND trth.SEND_DATE BETWEEN (CASE WHEN :sendDateStart = '%' then '1990-01-01' else :sendDateStart END) 
						AND (CASE WHEN :sendDateEnd = '%' then :currentDate else :sendDateEnd END)				
					AND msf.form_name = 'Form Input New Lead Instant Approval'
			) a <![CDATA[ WHERE a.ROWNUM <= :end
		) b WHERE b.recnum >= :start ]]>
	</sql-query>
	
	<sql-query name="task.createtask.countListPreIA">	    
	    <query-param name="customer" type="string" />
	    <query-param name="address" type="string" />
	    <query-param name="applno" type="string" />
		<query-param name="sendDateStart" type="string"/>
		<query-param name="sendDateEnd" type="string"/>
		<query-param name="uuidSubsystem" type="long"/>
		<query-param name="currentDate" type="string"/>
		select count(1)
				from tr_task_h trth with (nolock)		
					JOIN MS_PRIORITY msp with (nolock) ON trth.UUID_PRIORITY = msp.UUID_PRIORITY
					JOIN MS_STATUSTASK mst with (nolock) ON trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
					JOIN MS_FORM msf with (nolock) ON trth.UUID_FORM = msf.UUID_FORM
     				JOIN MS_FORMCATEGORY msfc with (nolock) ON msf.UUID_FORM_CATEGORY = msfc.UUID_FORM_CATEGORY   		
				where mst.STATUS_CODE = 'WS'
					AND mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
					AND trth.CUSTOMER_ADDRESS LIKE LOWER ('%'+ :address +'%')
					AND trth.CUSTOMER_NAME LIKE LOWER ('%'+ :customer +'%')
					AND (trth.APPL_NO LIKE LOWER ('%'+ :applno +'%') OR trth.APPL_NO IS NULL)
					AND trth.SEND_DATE IS NOT NULL
					AND trth.SEND_DATE BETWEEN (CASE WHEN :sendDateStart = '%' then '1990-01-01' else :sendDateStart END) 
						AND (CASE WHEN :sendDateEnd = '%' then :currentDate else :sendDateEnd END)				
					AND msf.form_name = 'Form Input New Lead Instant Approval'
	</sql-query>
	
	<sql-query name="task.createtask.preIADetailQSet">
		<query-param name="uuidTaskH" type="long" />
			WITH N AS(		
			SELECT  trtd.UUID_QUESTION AS QUESTION,
					mfqs.QUESTION_LABEL, msat.ANSWER_TYPE_NAME,
					CASE
						WHEN msat.code_answer_type in ('001','002','003','004','005','013','014','015','025','026', '028', '029', '032', '034')
						  THEN ISNULL(trtd.FIN_TEXT_ANSWER, trtd.TEXT_ANSWER)
						WHEN msat.code_answer_type in ('006','007','008','009','010','011','012')
						  THEN ISNULL(trtd.FIN_OPTION_TEXT, trtd.OPTION_TEXT)
					END AS ANSWER, msat.code_answer_type, trtd.UUID_TASK_H, mfqs.UUID_QUESTION, '0' as hasImage,
					'' as accuracy,
					mfqs.UUID_FORM,
					mfqs.QUESTION_GROUP_OF_FORM_SEQ as LINE_SEQ_ORDER, mfqs.QUESTION_OF_GROUP_SEQ as SEQ_ORDER,
					trtd.LATITUDE,
					trtd.LONGITUDE,
					ISNULL(trtd.FIN_TEXT_ANSWER, trtd.TEXT_ANSWER) as FIN_TEXT_ANSWER
			FROM TR_TASK_D trtd with (nolock) 
				JOIN TR_TASK_H tth on trtd.UUID_TASK_H=tth.UUID_TASK_H
				JOIN MS_FORMHISTORY mfh ON mfh.UUID_FORM = tth.UUID_FORM and mfh.FORM_VERSION = tth.FORM_VERSION
				JOIN MS_FORMQUESTIONSET mfqs on mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY AND mfqs.UUID_QUESTION = trtd.UUID_QUESTION
				JOIN MS_ANSWERTYPE msat with (nolock) ON mfqs.UUID_ANSWER_TYPE = msat.UUID_ANSWER_TYPE
			WHERE trtd.UUID_TASK_H = :uuidTaskH
			UNION ALL
			SELECT  trtdl.QUESTION_ID AS QUESTION,
					mfqs.QUESTION_LABEL, msat.ANSWER_TYPE_NAME,
					CASE
						WHEN msat.code_answer_type in ('016','017','018','021', '031')
						  THEN CAST(trtdl.UUID_TASK_DETAIL_LOB AS varchar) 
					END AS ANSWER, msat.code_answer_type, trtdl.UUID_TASK_H, mfqs.UUID_QUESTION, 
					CASE
					  WHEN trtdl.LOB_FILE IS NOT NULL THEN '1'
					  WHEN trtdl.IMAGE_PATH IS NOT NULL THEN '1' ELSE '0'
					END AS hasImage,
					trtdl.accuracy,
					mfqs.UUID_FORM,
					mfqs.QUESTION_GROUP_OF_FORM_SEQ as LINE_SEQ_ORDER, mfqs.QUESTION_OF_GROUP_SEQ as SEQ_ORDER,
					trtdl.LATITUDE,
					trtdl.LONGITUDE,
					trtdl.FIN_TEXT_ANSWER
			FROM TR_TASKDETAILLOB trtdl with (nolock) 
				JOIN TR_TASK_H tth on trtdl.UUID_TASK_H=tth.UUID_TASK_H AND tth.UUID_TASK_H = :uuidTaskH
				JOIN MS_FORMHISTORY mfh ON mfh.UUID_FORM = tth.UUID_FORM and mfh.FORM_VERSION = tth.FORM_VERSION
				JOIN MS_FORMQUESTIONSET mfqs on mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY AND mfqs.UUID_QUESTION = trtdl.QUESTION_ID
				JOIN MS_ANSWERTYPE msat with (nolock) ON mfqs.UUID_ANSWER_TYPE = msat.UUID_ANSWER_TYPE
		)
		SELECT N.QUESTION as uuidQuestion,
			   N.QUESTION_LABEL as questionText,
		       N.ANSWER_TYPE_NAME as "msQuestion.msAnswertype.answerTypeName",
		       N.ANSWER AS textAnswer,
		       N.code_answer_type as "msQuestion.msAnswertype.codeAnswerType",
		       N.hasImage as hasImage,
		       N.accuracy as accuracy,
		       N.LATITUDE as latitude,
		       N.LONGITUDE as longitude,
		       N.FIN_TEXT_ANSWER as finTextAnswer
		FROM N
		WHERE N.UUID_TASK_H = :uuidTaskH
		ORDER BY N.LINE_SEQ_ORDER, N.SEQ_ORDER asc
	</sql-query>
	<sql-query name="task.createtask.viewMapPhoto">
		<query-param name="uuidTaskH" type="long" />		
		SELECT trtdl.UUID_TASK_H,
			trtdl.QUESTION_TEXT,
			'' as TEXT_ANSWER,
			'' as OPTION_TEXT,
			CASE
				WHEN (trtdl.LOB_FILE IS NOT NULL or trtdl.IMAGE_PATH IS NOT NULL) THEN '1'
				ELSE '0'
			END as HAS_IMAGE,
			trtdl.LATITUDE,
			trtdl.LONGITUDE,
			NULL AS LOB_FILE,
			trtdl.UUID_TASK_DETAIL_LOB,
			trtdl.IMAGE_PATH
		FROM
			TR_TASKDETAILLOB trtdl with (nolock)
		WHERE
			LONGITUDE is not null and LATITUDE is not null and
			UUID_TASK_H = :uuidTaskH
	</sql-query>
</hibernate-mapping>