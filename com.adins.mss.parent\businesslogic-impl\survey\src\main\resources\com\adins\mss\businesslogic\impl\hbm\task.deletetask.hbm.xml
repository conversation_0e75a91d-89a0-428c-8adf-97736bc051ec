<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>	
	<sql-query name="task.survey.statusList">
		<query-param name="subsystem" type="string" />
		SELECT UUID_STATUS_TASK, STATUS_TASK_DESC
		FROM MS_STATUSTASK with (nolock)
		WHERE UUID_MS_SUBSYSTEM = :subsystem 
			AND STATUS_CODE IN ('N','A','V','P') 
	</sql-query>
</hibernate-mapping>