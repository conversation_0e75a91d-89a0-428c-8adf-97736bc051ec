<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="task.inquirygrouptasksurvey.branchList">
		<query-param name="branchIdLogin" type="string" />
		SELECT KEYVALUE, branch_code + '-' + branch_name 
		FROM dbo.getcabangbylogin(:branchIdLogin)
	</sql-query>
	
	<sql-query name="task.inquirygrouptasksurvey.getDetailGroupTask">
		<query-param name="uuidSubsystem" type="string" />
		<query-param name="groupTaskId" type="string" />
		select trth.UUID_TASK_H, TASK_ID, CONVERT(VARCHAR, ASSIGN_DATE, 120)ASSIGN_DATE, CONVERT(VARCHAR, SEND_DATE, 120)SEND_DATE, CONVERT(VARCHAR, SUBMIT_DATE, 120)SUBMIT_DATE, FORM_NAME, STATUS_TASK_DESC, '1' AS FLAG
		from MS_GROUPTASK  msgt with (nolock) 
			join TR_TASK_H trth with (nolock) on msgt.UUID_TASK_H = trth.UUID_TASK_H
			join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM
			join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		where GROUP_TASK_ID = :groupTaskId and mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
		UNION ALL
		select trth.UUID_TASK_H, TASK_ID, CONVERT(VARCHAR, ASSIGN_DATE, 120)ASSIGN_DATE, CONVERT(VARCHAR, SEND_DATE, 120)SEND_DATE, CONVERT(VARCHAR, SUBMIT_DATE, 120)SUBMIT_DATE, FORM_NAME, STATUS_TASK_DESC, '2' AS FLAG
		from FINAL_MS_GROUPTASK  msgt with (nolock) 
			join FINAL_TR_TASK_H trth with (nolock) on msgt.UUID_TASK_H = trth.UUID_TASK_H
			join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM
			join MS_STATUSTASK mst with (nolock) on trth.UUID_STATUS_TASK = mst.UUID_STATUS_TASK
		where GROUP_TASK_ID = :groupTaskId and mst.UUID_MS_SUBSYSTEM = :uuidSubsystem
	</sql-query>
</hibernate-mapping>