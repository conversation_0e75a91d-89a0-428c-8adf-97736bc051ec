<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping>
	<sql-query name="task.inquirytasksurvey.branchList">
		<query-param name="branchIdLogin" type="long" />
		SELECT KEYVALUE, branch_code + '-' + branch_name 
		FROM dbo.getcabangbylogin(:branchIdLogin)
	</sql-query>
	
	<sql-query name="task.inquirytasksurvey.statusList">
		<query-param name="subsystemId" type="long" />
		SELECT UUID_STATUS_TASK, STATUS_TASK_DESC
		FROM MS_STATUSTASK with (nolock)
		WHERE UUID_MS_SUBSYSTEM = :subsystemId 
			AND IS_ACTIVE = '1'
	</sql-query>
	
	<sql-query name="task.inquirytasksurvey.priorityList">
		SELECT UUID_PRIORITY, PRIORITY_DESC
		FROM MS_PRIORITY with (nolock)
		WHERE IS_ACTIVE = '1'
		ORDER BY UUID_PRIORITY ASC
	</sql-query>
	
	<sql-query name="task.inquirytasksurvey.formList">
		<query-param name="subsystemId" type="long" />
		SELECT UUID_FORM, FORM_NAME
		FROM MS_FORM with (nolock)
		WHERE IS_ACTIVE = '1' 
			AND UUID_MS_SUBSYSTEM = :subsystemId
	</sql-query>
	
	<sql-query name="task.inquirytasksurvey.formListByUserGroup">
		<query-param name="uuidUser" type="long" />
		select f.UUID_FORM, f.FORM_NAME from AM_MSUSER usr
			join AM_MEMBEROFGROUP usrgrp on usr.UUID_MS_USER = usrgrp.UUID_MS_USER
			join MS_FORMOFGROUP fgrp on usrgrp.UUID_MS_GROUP = fgrp.UUID_MS_GROUP
			join MS_FORM f on fgrp.UUID_FORM = f.UUID_FORM
			where usr.UUID_MS_USER = :uuidUser
			and f.IS_ACTIVE = '1' 
	</sql-query>
	
	<sql-query name="task.inquirytasksurvey.answerHistoryQSet">
		<query-param name="uuidTask" type="string" />
		SELECT rd.UUID_TASK_H AS UUID_TASK_H, ISNULL(rd.QUESTION_TEXT, mfqs.QUESTION_LABEL) as QUESTION_TEXT,
			ISNULL(REPLACE(TEXT_ANSWER,'\n','&lt;br&gt;'), '') as TEXT_ANSWER, ISNULL(OPTION_TEXT, '') as OPTION_TEXT,
		   	CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND LOB_FILE IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND IMAGE_PATH IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND LOB_FILE IS NULL) THEN '0'
				WHEN (msan.code_answer_type IN ('024','030')) THEN '01'
				ELSE '10'
		   	END AS HAS_IMAGE,
		   	rd.LATITUDE, rd.LONGITUDE, LOB_FILE, ISNULL(UUID_TASK_DETAIL_LOB, '') as UUID_TASK_DETAIL_LOB,
		   	msan.code_answer_type,
		   	ISNULL(REPLACE(INT_TEXT_ANSWER,'\n','&lt;br&gt;'), '') as INT_TEXT_ANSWER, ISNULL(INT_OPTION_TEXT, '') as INT_OPTION_TEXT,
		   	ISNULL(FIN_TEXT_ANSWER, '') as FIN_TEXT_ANSWER, ISNULL(FIN_OPTION_TEXT, '') as FIN_OPTION_TEXT,
		   	CASE
				WHEN (lower(msat.ASSET_TAG_NAME) IN ('home','office','legal address')) THEN '1'
				ELSE '0'
		   	END AS asset,
		   	MB.LATITUDE as LATBRANCH, MB.LONGITUDE as LNGBRANCH, MCC, MNC, LAC, CELL_ID, ACCURACY, 
		   	mfqs.UUID_QUESTION_GROUP,mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ, 
		   	'1' AS FLAG, UUID_TASK_D, REF_ID, msat.ASSET_TAG_NAME
		FROM (
			SELECT th.uuid_task_h as uuid_task_h, UUID_QUESTION as uuidQuestion, QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
				IMAGE_PATH, td.LATITUDE, td.LONGITUDE, NULL AS LOB_FILE, NULL AS UUID_TASK_DETAIL_LOB,
				INT_TEXT_ANSWER, INT_OPTION_TEXT, FIN_TEXT_ANSWER, FIN_OPTION_TEXT, NULL AS LATBRANCH, NULL AS LNGBRANCH,
				MCC, MNC, LAC, CELL_ID, ACCURACY, UUID_BRANCH, td.DTM_CRT, th.UUID_FORM, th.FORM_VERSION, td.UUID_TASK_D
			FROM TR_TASK_D td with (nolock) 
				inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
			WHERE td.UUID_TASK_H = :uuidTask
			UNION
			SELECT th.uuid_task_h as uuid_task_h, QUESTION_ID as uuidQuestion, QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT, IMAGE_PATH, 
				td.LATITUDE, td.LONGITUDE, LOB_FILE, UUID_TASK_DETAIL_LOB AS UUID_TASK_DETAIL_LOB,
				INT_TEXT_ANSWER, INT_OPTION_TEXT, FIN_TEXT_ANSWER, FIN_OPTION_TEXT, NULL AS LATBRANCH, NULL AS LNGBRANCH,
				MCC, MNC, LAC, CELL_ID, ACCURACY, UUID_BRANCH, td.DTM_CRT, th.UUID_FORM, th.FORM_VERSION, td.UUID_TASK_DETAIL_LOB
			FROM TR_TASKDETAILLOB td with (nolock) 
				inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
			WHERE td.UUID_TASK_H = :uuidTask
		) rd
		  INNER JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = rd.UUID_FORM AND mfh.FORM_VERSION = rd.FORM_VERSION
		  INNER JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
		  		and mfqs.UUID_QUESTION = rd.uuidQuestion
		  INNER JOIN MS_ANSWERTYPE msan with (nolock) ON mfqs.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
		  LEFT JOIN  MS_BRANCH MB with (nolock) ON rd.UUID_BRANCH = MB.UUID_BRANCH
		  LEFT OUTER JOIN MS_ASSETTAG MSAT with (nolock) ON MSAT.UUID_ASSET_TAG = mfqs.UUID_ASSET_TAG
		WHERE mfqs.QUESTION_GROUP_IS_ACTIVE = '1' 
			AND mfqs.IS_VISIBLE ='1' 
		UNION
		SELECT
			'' as uuid_task_h, qg.QUESTION_GROUP_LABEL, '', '', '10', NULL, NULL, NULL, '0', 'QuestionGroup', '', '', '', '', '0',
			 NULL, NULL, NULL, NULL, NULL, NULL, NULL, mfqs.UUID_QUESTION_GROUP, mfqs.question_group_of_form_seq as LINE_SEQ_ORDER, (min(mfqs.question_of_group_seq)-1), '1', '', '', NULL
		FROM tr_task_d a join tr_task_h c on a.uuid_task_h=c.uuid_task_h
		JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = c.UUID_FORM AND mfh.FORM_VERSION = c.FORM_VERSION
		JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
		JOIN MS_QUESTIONOFGROUP qog with(nolock) ON mfqs.UUID_QUESTION = qog.UUID_QUESTION and a.uuid_question=qog.uuid_question
		JOIN MS_QUESTIONGROUP qg with(nolock) ON qg.UUID_QUESTION_GROUP = mfqs.UUID_QUESTION_GROUP and a.uuid_question=mfqs.uuid_question
		WHERE a.uuid_task_h = :uuidTask AND qg.QUESTION_GROUP_LABEL IN (
        		'Result Biro Kredit Fasilitas Aktif Pemohon',
        		'Result Biro Kredit Fasilitas Tidak Aktif/ Lunas Pemohon',
        		'Result Biro Kredit Fasilitas Aktif Pasangan',
        		'Result Biro Kredit Fasilitas Tidak Aktif/ Lunas Pasangan',
        		'Result Biro Kredit Fasilitas Aktif Penjamin',
        		'Result Biro Kredit Fasilitas Tidak Aktif/ Lunas Penjamin',
        		'Rekomendasi Biro Kredit Pemohon',
        		'Rekomendasi Biro Kredit Pasangan',
        		'Rekomendasi Biro Kredit Penjamin'
    		)
		group by qg.question_group_label, mfqs.UUID_QUESTION_GROUP, mfqs.question_group_of_form_seq, mfh.UUID_FORM_HISTORY
		ORDER BY QUESTION_GROUP_OF_FORM_SEQ, question_of_group_seq
	</sql-query>
	
	<sql-query name="task.inquirytasksurvey.answerHistoryVerifQSet">
		<query-param name="uuidTask" type="string" />
		SELECT rd.UUID_TASK_H AS UUID_TASK_H, ISNULL(rd.QUESTION_TEXT, mfqs.QUESTION_LABEL) as QUESTION_TEXT,
			ISNULL(REPLACE(TEXT_ANSWER,'\n','&lt;br&gt;'), '') as TEXT_ANSWER, ISNULL(OPTION_TEXT, '') as OPTION_TEXT,
		   	CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND LOB_FILE IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND IMAGE_PATH IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND LOB_FILE IS NULL) THEN '0'
				WHEN (msan.code_answer_type IN ('024','030')) THEN '01'
				ELSE '10'
		   	END AS HAS_IMAGE,
		   	rd.LATITUDE, rd.LONGITUDE, LOB_FILE, ISNULL(UUID_TASK_DETAIL_LOB, '') as UUID_TASK_DETAIL_LOB,
		   	msan.code_answer_type,
		   	ISNULL(REPLACE(INT_TEXT_ANSWER,'\n','&lt;br&gt;'), '') as INT_TEXT_ANSWER, ISNULL(INT_OPTION_TEXT, '') as INT_OPTION_TEXT,
		   	ISNULL(FIN_TEXT_ANSWER, '') as FIN_TEXT_ANSWER, ISNULL(FIN_OPTION_TEXT, '') as FIN_OPTION_TEXT,
		   	CASE
				WHEN (lower(msat.ASSET_TAG_NAME) IN ('home','office','legal address')) THEN '1'
				ELSE '0'
		   	END AS asset,
		   	MB.LATITUDE as LATBRANCH, MB.LONGITUDE as LNGBRANCH, MCC, MNC, LAC, CELL_ID, ACCURACY, 
		   	mfqs.UUID_QUESTION_GROUP,mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ, 
		   	'1' AS FLAG, UUID_TASK_D, REF_ID, msat.ASSET_TAG_NAME
		FROM (
			SELECT th.uuid_task_h as uuid_task_h, UUID_QUESTION as uuidQuestion, QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT,
				IMAGE_PATH, td.LATITUDE, td.LONGITUDE, NULL AS LOB_FILE, NULL AS UUID_TASK_DETAIL_LOB,
				INT_TEXT_ANSWER, INT_OPTION_TEXT, FIN_TEXT_ANSWER, FIN_OPTION_TEXT, NULL AS LATBRANCH, NULL AS LNGBRANCH,
				MCC, MNC, LAC, CELL_ID, ACCURACY, UUID_BRANCH, td.DTM_CRT, th.UUID_FORM, th.FORM_VERSION, td.UUID_TASK_D
			FROM TR_TASK_D td with (nolock) 
				inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
			WHERE td.UUID_TASK_H = :uuidTask
			UNION
			SELECT th.uuid_task_h as uuid_task_h, QUESTION_ID as uuidQuestion, QUESTION_TEXT, TEXT_ANSWER, OPTION_TEXT, IMAGE_PATH, 
				td.LATITUDE, td.LONGITUDE, LOB_FILE, UUID_TASK_DETAIL_LOB AS UUID_TASK_DETAIL_LOB,
				INT_TEXT_ANSWER, INT_OPTION_TEXT, FIN_TEXT_ANSWER, FIN_OPTION_TEXT, NULL AS LATBRANCH, NULL AS LNGBRANCH,
				MCC, MNC, LAC, CELL_ID, ACCURACY, UUID_BRANCH, td.DTM_CRT, th.UUID_FORM, th.FORM_VERSION, td.UUID_TASK_DETAIL_LOB
			FROM TR_TASKDETAILLOB td with (nolock) 
				inner join TR_TASK_H th with (nolock) on td.UUID_TASK_H = th.UUID_TASK_H
			WHERE td.UUID_TASK_H = :uuidTask
		) rd
		  INNER JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = rd.UUID_FORM AND mfh.FORM_VERSION = rd.FORM_VERSION
		  INNER JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
		  		and mfqs.UUID_QUESTION = rd.uuidQuestion
		  INNER JOIN MS_ANSWERTYPE msan with (nolock) ON mfqs.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
		  LEFT JOIN  MS_BRANCH MB with (nolock) ON rd.UUID_BRANCH = MB.UUID_BRANCH
		  LEFT OUTER JOIN MS_ASSETTAG MSAT with (nolock) ON MSAT.UUID_ASSET_TAG = mfqs.UUID_ASSET_TAG
		WHERE mfqs.QUESTION_GROUP_IS_ACTIVE = '1' 
			AND mfqs.IS_VISIBLE ='1' 
		UNION
		SELECT
			'' as uuid_task_h, qg.QUESTION_GROUP_LABEL, '', '', '10', NULL, NULL, NULL, '0', 'QuestionGroup', '', '', '', '', '0',
			 NULL, NULL, NULL, NULL, NULL, NULL, NULL, mfqs.UUID_QUESTION_GROUP, mfqs.question_group_of_form_seq as LINE_SEQ_ORDER, (min(mfqs.question_of_group_seq)-1), '1', '', '', NULL
		FROM tr_task_d a join tr_task_h c on a.uuid_task_h=c.uuid_task_h
		JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = c.UUID_FORM AND mfh.FORM_VERSION = c.FORM_VERSION
		JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
		JOIN MS_QUESTIONOFGROUP qog with(nolock) ON mfqs.UUID_QUESTION = qog.UUID_QUESTION and a.uuid_question=qog.uuid_question
		JOIN MS_QUESTIONGROUP qg with(nolock) ON qg.UUID_QUESTION_GROUP = mfqs.UUID_QUESTION_GROUP and a.uuid_question=mfqs.uuid_question
		WHERE a.uuid_task_h = :uuidTask AND qg.QUESTION_GROUP_LABEL IN (
        		'Result Biro Kredit Fasilitas Aktif Pemohon',
        		'Result Biro Kredit Fasilitas Tidak Aktif/ Lunas Pemohon',
        		'Result Biro Kredit Fasilitas Aktif Pasangan',
        		'Result Biro Kredit Fasilitas Tidak Aktif/ Lunas Pasangan',
        		'Result Biro Kredit Fasilitas Aktif Penjamin',
        		'Result Biro Kredit Fasilitas Tidak Aktif/ Lunas Penjamin',
        		'Rekomendasi Biro Kredit Pemohon',
        		'Rekomendasi Biro Kredit Pasangan',
        		'Rekomendasi Biro Kredit Penjamin'
    		)
		group by qg.question_group_label, mfqs.UUID_QUESTION_GROUP, mfqs.question_group_of_form_seq, mfh.UUID_FORM_HISTORY
		ORDER BY QUESTION_GROUP_OF_FORM_SEQ, question_of_group_seq
	</sql-query>

	<sql-query name="task.inquirytasksurvey.answerHistoryRejectedQset">
		<query-param name="uuidTask" type="string" />
		<query-param name="uuidTaskRejectedHistory" type="string" />
		SELECT ISNULL(rejdet.question_text, '') question_text, ISNULL(REPLACE(rejdet.text_answer,'\n','&lt;br&gt;'), '') text_answer, ISNULL(rejdet.OPTION_TEXT, '') OPTION_TEXT,
			CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND rejdet.IMAGE_BLOB IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND rejdet.IMAGE_BLOB IS NULL) THEN '0'
				WHEN (msan.code_answer_type IN ('024','030')) THEN '01'
				ELSE '10'
			END AS HAS_IMAGE,
			rejdet.IMAGE_BLOB, rejdet.LATITUDE, rejdet.LONGITUDE, rejdet.UUID_TASK_REJECTED_DETAIL, msan.code_answer_type,
			CASE
				WHEN (lower(msat.ASSET_TAG_NAME) IN ('home','office','legal address')) THEN '1'
				ELSE '0'
			END AS asset, MB.LATITUDE LATBRANCH, MB.LONGITUDE LNGBRANCH,
			rejdet.MCC, rejdet.MNC, rejdet.LAC, rejdet.CELL_ID, rejdet.ACCURACY, '1' AS FLAG, REF_ID, msat.ASSET_TAG_NAME 
		FROM TR_TASKREJECTEDDETAIL rejdet with (nolock)
			JOIN TR_TASK_H trth with (nolock) on rejdet.UUID_TASK = trth.UUID_TASK_H
			JOIN TR_TASKHISTORY trths with (nolock) on trth.UUID_TASK_H = trths.UUID_TASK_H
			INNER JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = trth.UUID_FORM AND mfh.FORM_VERSION = trth.FORM_VERSION
			INNER JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
				and mfqs.UUID_QUESTION = rejdet.QUESTION_ID
			INNER JOIN MS_ANSWERTYPE msan with (nolock) ON mfqs.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
			JOIN MS_BRANCH MB with (nolock) ON TRTH.UUID_BRANCH = MB.UUID_BRANCH
			LEFT OUTER JOIN MS_ASSETTAG MSAT with (nolock) ON MSAT.UUID_ASSET_TAG = mfqs.UUID_ASSET_TAG
		WHERE rejdet.UUID_TASK = :uuidTask and rejdet.UUID_TASK_REJECTED_HISTORY = :uuidTaskRejectedHistory and trths.UUID_TASK_REJECTED_HISTORY=:uuidTaskRejectedHistory
		ORDER BY mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ
	</sql-query>

	<sql-query name="task.inquirytasksurvey.answerHistoryRejectedFinalQSet">
		<query-param name="uuidTask" type="string" />
		<query-param name="uuidTaskRejectHistory" type="string" />
		SELECT ISNULL(rejdet.question_text, '') question_text, ISNULL(REPLACE(rejdet.text_answer,'\n','&lt;br&gt;'), '') text_answer, ISNULL(rejdet.OPTION_TEXT, '') OPTION_TEXT,
			CASE
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND rejdet.IMAGE_BLOB IS NOT NULL) THEN '1'
				WHEN (msan.code_answer_type IN ('016','017','018','021','031','033','038','039') AND rejdet.IMAGE_BLOB IS NULL) THEN '0'
				WHEN (msan.code_answer_type IN ('024','030')) THEN '01'
				ELSE '10'
			END AS HAS_IMAGE,
			rejdet.IMAGE_BLOB, rejdet.LATITUDE, rejdet.LONGITUDE, rejdet.UUID_TASK_REJECTED_DETAIL, msan.code_answer_type,
			CASE
				WHEN (lower(msat.ASSET_TAG_NAME) IN ('home','office','legal address')) THEN '1'
				ELSE '0'
			END AS asset, MB.LATITUDE LATBRANCH, MB.LONGITUDE LNGBRANCH,
			rejdet.MCC, rejdet.MNC, rejdet.LAC, rejdet.CELL_ID, rejdet.ACCURACY, '2' AS FLAG, rejdet.UUID_TASK_REJECTED_DETAIL, REF_ID, msat.ASSET_TAG_NAME
		FROM FINAL_TR_TASKREJECTEDDETAIL rejdet with (nolock)
			 JOIN FINAL_TR_TASK_H trth with (nolock) on rejdet.UUID_TASK = trth.UUID_TASK_H
			 JOIN FINAL_TR_TASKHISTORY trths with (nolock) on trth.UUID_TASK_H = trths.UUID_TASK_H
			 INNER JOIN MS_FORMHISTORY mfh with (nolock) ON mfh.UUID_FORM = trth.UUID_FORM AND mfh.FORM_VERSION = trth.FORM_VERSION
			 INNER JOIN MS_FORMQUESTIONSET mfqs with (nolock) ON mfqs.UUID_FORM_HISTORY = mfh.UUID_FORM_HISTORY
			 	and mfqs.UUID_QUESTION = rejdet.QUESTION_ID
			 INNER JOIN MS_ANSWERTYPE msan with (nolock) ON mfqs.UUID_ANSWER_TYPE = msan.UUID_ANSWER_TYPE
			 JOIN MS_BRANCH MB with (nolock) ON TRTH.UUID_BRANCH = MB.UUID_BRANCH
			 LEFT OUTER JOIN MS_ASSETTAG MSAT with (nolock) ON MSAT.UUID_ASSET_TAG = mfqs.UUID_ASSET_TAG
		WHERE rejdet.UUID_TASK = :uuidTask 
			and rejdet.UUID_TASK_REJECTED_HISTORY = :uuidTaskRejectedHistory 
			and trths.UUID_TASK_REJECTED_HISTORY=:uuidTaskRejectedHistory
		ORDER BY mfqs.QUESTION_GROUP_OF_FORM_SEQ, mfqs.QUESTION_OF_GROUP_SEQ
	</sql-query>
	<sql-query name="task.inquirytasksurvey.taskHistory">
		<query-param name="paramUuidTaskH" type="string" />
		SELECT trths.uuid_task_h, 
			CASE 
				WHEN trths.CODE_PROCESS = '001' THEN 'Assignment' 
				WHEN trths.CODE_PROCESS = '002' THEN 'Re-assignment'
				WHEN trths.CODE_PROCESS = '003' THEN 'Submitted' 
				WHEN trths.CODE_PROCESS = '004' THEN 'Verified' 
				WHEN trths.CODE_PROCESS = '005' THEN 'Approval' 
				WHEN trths.CODE_PROCESS = '006' THEN 'Released'
				WHEN trths.code_process = '007' THEN 'Deleted'
				WHEN trths.CODE_PROCESS = '008' THEN 'Rejected'
				WHEN trths.CODE_PROCESS = '009' THEN 'Rejected on Verification'
				WHEN trths.CODE_PROCESS = '010' THEN 'Rejected on Approval'
				WHEN trths.CODE_PROCESS = '011' THEN 'On Core'
				WHEN trths.CODE_PROCESS = '015' THEN 'Unassigned'
				WHEN trths.CODE_PROCESS = '013' THEN 'Rejected with Re-Survey'
				WHEN trths.CODE_PROCESS = '014' THEN 'Promise to Survey'
				WHEN trths.CODE_PROCESS = '012' THEN 'Canceled'
				WHEN trths.CODE_PROCESS = '017' THEN 'Failed Assign'
				WHEN trths.CODE_PROCESS = '018' THEN 'Released On Pending Confins'
				WHEN trths.CODE_PROCESS = '022' THEN 'Task Visit'
			END TASK_PROCESS,
			trths.ACTOR, trths.FIELD_PERSON, trths.DTM_CRT, trths.notes, mssta.status_task_desc, 
			trths.code_process, '1' as flag, trths.UUID_TASK_REJECTED_HISTORY, trths.UUID_TASK_HISTORY, mssta.status_code
		FROM tr_taskhistory trths with (nolock) 
			JOIN ms_statustask mssta with (nolock) ON (trths.uuid_status_task = mssta.uuid_status_task)
		WHERE trths.uuid_task_h = :paramUuidTaskH
		ORDER BY trths.DTM_CRT desc, uuid_task_history desc
	</sql-query>
	<sql-query name="task.inquirytasksurvey.taskHistoryFinal">
		<query-param name="paramUuidTaskH" type="string" />
		SELECT trths.uuid_task_h, 
			CASE 
				WHEN trths.CODE_PROCESS = '001' THEN 'Assignment' 
				WHEN trths.CODE_PROCESS = '002' THEN 'Re-assignment'
				WHEN trths.CODE_PROCESS = '003' THEN 'Submitted' 
				WHEN trths.CODE_PROCESS = '004' THEN 'Verified' 
				WHEN trths.CODE_PROCESS = '005' THEN 'Approval' 
				WHEN trths.CODE_PROCESS = '006' THEN 'Released'
				WHEN trths.code_process = '007' THEN 'Deleted'
				WHEN trths.CODE_PROCESS = '008' THEN 'Rejected'
				WHEN trths.CODE_PROCESS = '009' THEN 'Rejected on Verification'
				WHEN trths.CODE_PROCESS = '010' THEN 'Rejected on Approval'
				WHEN trths.CODE_PROCESS = '011' THEN 'On Core'
				WHEN trths.CODE_PROCESS = '015' THEN 'Unassigned'
				WHEN trths.CODE_PROCESS = '013' THEN 'Rejected with Re-Survey'
				WHEN trths.CODE_PROCESS = '014' THEN 'Promise to Survey'
				WHEN trths.CODE_PROCESS = '012' THEN 'Canceled'
				WHEN trths.CODE_PROCESS = '017' THEN 'Failed Assign'
				WHEN trths.CODE_PROCESS = '018' THEN 'Released On Pending Confins'
				WHEN trths.CODE_PROCESS = '022' THEN 'Task Visit'
			END TASK_PROCESS,
			trths.ACTOR, trths.FIELD_PERSON, trths.DTM_CRT, trths.notes, mssta.status_task_desc, 
			trths.code_process, '2' as flag, trths.UUID_TASK_REJECTED_HISTORY, trths.UUID_TASK_HISTORY, mssta.status_code
		FROM final_tr_taskhistory trths with (nolock) 
			JOIN ms_statustask mssta with (nolock) ON (trths.uuid_status_task = mssta.uuid_status_task)
		WHERE trths.uuid_task_h = :paramUuidTaskH
		ORDER BY trths.DTM_CRT desc, uuid_task_history desc
	</sql-query>
	<sql-query name="task.inquirytasksurvey.detailtasksurvey">
		<query-param name="paramUuidTaskH" type="string" />
		SELECT 
			trth.uuid_task_h, 
			trth.TASK_ID, 
			trth.appl_no,
			msf.FORM_NAME,
			msb.BRANCH_NAME,
			msp.PRIORITY_DESC, 
			trth.NOTES, 
			trth.CUSTOMER_NAME, 
			trth.CUSTOMER_ADDRESS, 
			trth.CUSTOMER_PHONE, 
			trth.ZIP_CODE,  
			LEFT(CONVERT(VARCHAR, trth.assign_date, 113), 17) as assign_date, 
			LEFT(CONVERT(VARCHAR, trth.download_date, 113),17) as download_date,
			LEFT(CONVERT(VARCHAR, trth.submit_date, 113),17) as submit_date, 
			msu.full_name,
			mssta.status_task_desc,
			'' as VOICE_NOTE,
			LEFT(CONVERT(VARCHAR, trth.send_date, 113),17) as send_date,
			LEFT(CONVERT(VARCHAR, trth.PROMISE_DATE, 113), 17) as promise_date,
			trth.KELURAHAN,
			trth.STATUS_PENANGANAN_TELE,
			trth.STATUS_PRIORITY_CRM
		FROM tr_task_h trth with (nolock)
			LEFT OUTER JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user)
			LEFT OUTER JOIN ms_statustask mssta with (nolock) ON(trth.uuid_status_task = mssta.uuid_status_task)
			LEFT OUTER JOIN ms_branch msb with (nolock) ON(trth.uuid_branch = msb.uuid_branch)
			LEFT OUTER join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM
			LEFT OUTER JOIN MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY
		WHERE trth.uuid_task_h = :paramUuidTaskH
	</sql-query>
	<sql-query name="task.inquirytasksurvey.detailtasksurveyFinal">
		<query-param name="paramUuidTaskH" type="string" />
		SELECT 
			trth.uuid_task_h, 
			trth.TASK_ID, 
			trth.appl_no,
			msf.FORM_NAME,
			msb.BRANCH_NAME,
			msp.PRIORITY_DESC, 
			trth.NOTES, 
			trth.CUSTOMER_NAME, 
			trth.CUSTOMER_ADDRESS, 
			trth.CUSTOMER_PHONE, 
			trth.ZIP_CODE,  
			LEFT(CONVERT(VARCHAR, trth.assign_date, 113), 17) as assign_date, 
			LEFT(CONVERT(VARCHAR, trth.download_date, 113),17) as download_date,
			LEFT(CONVERT(VARCHAR, trth.submit_date, 113),17) as submit_date, 
			msu.full_name,
			mssta.status_task_desc,
			'' as VOICE_NOTE,
			LEFT(CONVERT(VARCHAR, trth.send_date, 113),17) as send_date,
			LEFT(CONVERT(VARCHAR, trth.PROMISE_DATE, 113), 17) as promise_date
		FROM final_tr_task_h trth with (nolock)
			LEFT OUTER JOIN am_msuser msu with (nolock) ON(trth.uuid_ms_user = msu.uuid_ms_user)
			LEFT OUTER JOIN ms_statustask mssta with (nolock) ON(trth.uuid_status_task = mssta.uuid_status_task)
			LEFT OUTER JOIN ms_branch msb with (nolock) ON(trth.uuid_branch = msb.uuid_branch)
			LEFT OUTER join MS_FORM msf with (nolock) on trth.UUID_FORM = msf.UUID_FORM
			LEFT OUTER JOIN MS_PRIORITY msp with (nolock) on trth.UUID_PRIORITY = msp.UUID_PRIORITY
		WHERE trth.uuid_task_h = :paramUuidTaskH
	</sql-query>
	<sql-query name="task.inquirytasksurvey.getTaskH">
		<query-param name="paramUuidTaskH" type="string" />
		select uuid_task_h, uuid_form 
		from TR_TASK_H with (nolock) 
		WHERE uuid_task_h = :paramUuidTaskH
	</sql-query>
	<sql-query name="task.inquirytasksurvey.getTaskHFinal">
		<query-param name="paramUuidTaskH" type="string" />
		select uuid_task_h,  uuid_form 
		from FINAL_TR_TASK_H with (nolock) 
		WHERE uuid_task_h = :paramUuidTaskH
	</sql-query>
	
	<sql-query name="task.inquirytasksurvey.checkMenu">
		<query-param name="menuCode" type="string" />
		<query-param name="uuid" type="string" />
		select count(*) from AM_MENUFEATUREOFGROUP AMMF
			JOIN AM_MSGROUP MSG ON MSG.UUID_MS_GROUP = AMMF.UUID_MS_GROUP
			JOIN AM_MSMENUFEATURE AMMS ON AMMS.UUID_MSMENU_FEATURE = AMMF.UUID_MSMENU_FEATURE
			JOIN AM_MEMBEROFGROUP AMMOG ON AMMOG.UUID_MS_GROUP = MSG.UUID_MS_GROUP
			JOIN AM_MSUSER AMS ON AMS.UUID_MS_USER = AMMOG.UUID_MS_USER
		WHERE AMMS.FEATURE_CODE = :menuCode
		AND AMS.UUID_MS_USER = :uuid
	</sql-query>
	
	<sql-query name="task.inquirytasksurvey.regionList">
		<query-param name="branchIdLogin" type="long" />
			select uuid_region, REGION_NAME from ms_region where is_active = 1
	</sql-query>
	
	<sql-query name="task.inquirytasksurvey.regionListExcludeRegionUserLogin">
		<query-param name="uuidBranch" type="long" />
			select uuid_region, REGION_NAME from ms_region WITH(NOLOCK) 
			where is_active = 1 AND UUID_REGION != (SELECT UUID_REGION FROM MS_BRANCH WITH (NOLOCK) WHERE UUID_BRANCH = :uuidBranch)
	</sql-query>
</hibernate-mapping>