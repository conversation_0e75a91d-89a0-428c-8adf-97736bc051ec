<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC 
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
    
<hibernate-mapping>
	
	<sql-query name="task.reassigntaskots.statusList">
		<query-param name="subsystemId" type="long" />
		SELECT UUID_STATUS_TASK, STATUS_TASK_DESC 
		FROM MS_STATUSTASK WITH(NOLOCK)
		WHERE STATUS_CODE in ('A', 'N') 
		AND UUID_MS_SUBSYSTEM = :subsystemId
	</sql-query>
	
		<sql-query name="task.reassigntaskots.getSpvList">
		<query-param name="uuidBranch" type="string"/>
		<query-param name="jobCode" type="string"/>
		SELECT amu.UUID_MS_USER, amu.FULL_NAME + ' - ' + MSB.BRANCH_NAME, amu.IS_LOGGED_IN, amu.LOGIN_ID, amu.INITIAL_NAME
		FROM AM_MSUSER amu with (nolock)
			JOIN MS_JOB mj with (nolock) ON amu.UUID_JOB = mj.UUID_JOB 
				AND amu.UUID_MS_SUBSYSTEM = mj.UUID_MS_SUBSYSTEM
			join 
			(
				SELECT keyValue as UUID_BRANCH, BRANCH_NAME 
				FROM dbo.getCabangByLogin(:uuidBranch)
			) msb on amu.UUID_BRANCH = msb.UUID_BRANCH
		WHERE mj.JOB_CODE = :jobCode
			AND amu.IS_ACTIVE = '1'
	</sql-query>
	
</hibernate-mapping>