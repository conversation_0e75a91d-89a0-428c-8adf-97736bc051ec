package com.adins.mss.model;
// Generated Jun 18, 2025 4:03:18 PM by Hibernate Tools 5.2.8.Final

import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * TblAgreementHistory generated by hbm2java
 */
@Entity
@Table(name = "TBL_AGREEMENT_HISTORY")
public class TblAgreementHistory implements java.io.Serializable {

	private long uuidAgreementHistory;
	private String custNo;
	private String name;
	private String idNo;
	private String birthPlace;
	private Date birthDt;
	private String emailAddress;
	private String phoneNumber;
	private String type;
	private String taskIdPolo;
	private String orderNo;
	private String appNo;
	private String source;
	private Date dtmCrt;
	private Date dtmUpd;

	public TblAgreementHistory() {
	}

	public TblAgreementHistory(long uuidAgreementHistory) {
		this.uuidAgreementHistory = uuidAgreementHistory;
	}

	public TblAgreementHistory(long uuidAgreementHistory, String custNo, String name, String idNo, String birthPlace,
			Date birthDt, String emailAddress, String phoneNumber, String type, String taskIdPolo, String orderNo,
			String appNo, String source, Date dtmCrt, Date dtmUpd) {
		this.uuidAgreementHistory = uuidAgreementHistory;
		this.custNo = custNo;
		this.name = name;
		this.idNo = idNo;
		this.birthPlace = birthPlace;
		this.birthDt = birthDt;
		this.emailAddress = emailAddress;
		this.phoneNumber = phoneNumber;
		this.type = type;
		this.taskIdPolo = taskIdPolo;
		this.orderNo = orderNo;
		this.appNo = appNo;
		this.source = source;
		this.dtmCrt = dtmCrt;
		this.dtmUpd = dtmUpd;
	}

	@Id @GeneratedValue(strategy=GenerationType.IDENTITY)
	@Column(name = "UUID_AGREEMENT_HISTORY", unique = true, nullable = false)
	public long getUuidAgreementHistory() {
		return this.uuidAgreementHistory;
	}

	public void setUuidAgreementHistory(long uuidAgreementHistory) {
		this.uuidAgreementHistory = uuidAgreementHistory;
	}

	@Column(name = "CUST_NO", length = 50)
	public String getCustNo() {
		return this.custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	@Column(name = "NAME", length = 80)
	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "ID_NO", length = 20)
	public String getIdNo() {
		return this.idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}

	@Column(name = "BIRTH_PLACE", length = 80)
	public String getBirthPlace() {
		return this.birthPlace;
	}

	public void setBirthPlace(String birthPlace) {
		this.birthPlace = birthPlace;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BIRTH_DT", length = 23)
	public Date getBirthDt() {
		return this.birthDt;
	}

	public void setBirthDt(Date birthDt) {
		this.birthDt = birthDt;
	}

	@Column(name = "EMAIL_ADDRESS", length = 200)
	public String getEmailAddress() {
		return this.emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	@Column(name = "PHONE_NUMBER", length = 20)
	public String getPhoneNumber() {
		return this.phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	@Column(name = "TYPE", length = 20)
	public String getType() {
		return this.type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "TASK_ID_POLO", length = 20)
	public String getTaskIdPolo() {
		return this.taskIdPolo;
	}

	public void setTaskIdPolo(String taskIdPolo) {
		this.taskIdPolo = taskIdPolo;
	}

	@Column(name = "ORDER_NO", length = 20)
	public String getOrderNo() {
		return this.orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	@Column(name = "APP_NO", length = 20)
	public String getAppNo() {
		return this.appNo;
	}

	public void setAppNo(String appNo) {
		this.appNo = appNo;
	}

	@Column(name = "SOURCE", length = 20)
	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "DTM_CRT", length = 23)
	public Date getDtmCrt() {
		return this.dtmCrt;
	}

	public void setDtmCrt(Date dtmCrt) {
		this.dtmCrt = dtmCrt;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "DTM_UPD", length = 23)
	public Date getDtmUpd() {
		return this.dtmUpd;
	}

	public void setDtmUpd(Date dtmUpd) {
		this.dtmUpd = dtmUpd;
	}

}
