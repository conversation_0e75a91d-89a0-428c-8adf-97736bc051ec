package com.adins.mss.model;

import static org.junit.Assert.*;
import java.util.Date;
import org.junit.Before;
import org.junit.Test;

/**
 * JUnit test class for TblAgreementHistory entity
 * Tests all constructors, getters, setters and basic functionality
 * Created to achieve 100% code coverage for new lines in revision 6d55721 to 367b8b2
 */
public class TblAgreementHistoryTest {

    private TblAgreementHistory tblAgreementHistory;
    private Date testDate;
    
    @Before
    public void setUp() {
        tblAgreementHistory = new TblAgreementHistory();
        testDate = new Date();
    }
    
    @Test
    public void testDefaultConstructor() {
        TblAgreementHistory entity = new TblAgreementHistory();
        assertNotNull("Default constructor should create non-null instance", entity);
    }
    
    @Test
    public void testConstructorWithUuidOnly() {
        long testUuid = 12345L;
        TblAgreementHistory entity = new TblAgreementHistory(testUuid);
        assertEquals("UUID should be set correctly", testUuid, entity.getUuidAgreementHistory());
    }
    
    @Test
    public void testFullConstructor() {
        long testUuid = 12345L;
        String custNo = "CUST001";
        String name = "John Doe";
        String idNo = "1234567890";
        String birthPlace = "Jakarta";
        Date birthDt = new Date();
        String emailAddress = "<EMAIL>";
        String phoneNumber = "081234567890";
        String type = "Customer";
        String taskIdPolo = "TASK001";
        String orderNo = "ORDER001";
        String appNo = "APP001";
        String source = "Task Text";
        Date dtmCrt = new Date();
        Date dtmUpd = new Date();
        
        TblAgreementHistory entity = new TblAgreementHistory(testUuid, custNo, name, idNo, birthPlace,
                birthDt, emailAddress, phoneNumber, type, taskIdPolo, orderNo, appNo, source, dtmCrt, dtmUpd);
        
        assertEquals("UUID should be set correctly", testUuid, entity.getUuidAgreementHistory());
        assertEquals("Customer number should be set correctly", custNo, entity.getCustNo());
        assertEquals("Name should be set correctly", name, entity.getName());
        assertEquals("ID number should be set correctly", idNo, entity.getIdNo());
        assertEquals("Birth place should be set correctly", birthPlace, entity.getBirthPlace());
        assertEquals("Birth date should be set correctly", birthDt, entity.getBirthDt());
        assertEquals("Email address should be set correctly", emailAddress, entity.getEmailAddress());
        assertEquals("Phone number should be set correctly", phoneNumber, entity.getPhoneNumber());
        assertEquals("Type should be set correctly", type, entity.getType());
        assertEquals("Task ID Polo should be set correctly", taskIdPolo, entity.getTaskIdPolo());
        assertEquals("Order number should be set correctly", orderNo, entity.getOrderNo());
        assertEquals("App number should be set correctly", appNo, entity.getAppNo());
        assertEquals("Source should be set correctly", source, entity.getSource());
        assertEquals("Creation date should be set correctly", dtmCrt, entity.getDtmCrt());
        assertEquals("Update date should be set correctly", dtmUpd, entity.getDtmUpd());
    }
    
    @Test
    public void testUuidAgreementHistoryGetterSetter() {
        long testValue = 98765L;
        tblAgreementHistory.setUuidAgreementHistory(testValue);
        assertEquals("UUID getter/setter should work correctly", testValue, tblAgreementHistory.getUuidAgreementHistory());
    }
    
    @Test
    public void testCustNoGetterSetter() {
        String testValue = "CUST12345";
        tblAgreementHistory.setCustNo(testValue);
        assertEquals("Customer number getter/setter should work correctly", testValue, tblAgreementHistory.getCustNo());
    }
    
    @Test
    public void testNameGetterSetter() {
        String testValue = "Jane Smith";
        tblAgreementHistory.setName(testValue);
        assertEquals("Name getter/setter should work correctly", testValue, tblAgreementHistory.getName());
    }
    
    @Test
    public void testIdNoGetterSetter() {
        String testValue = "9876543210";
        tblAgreementHistory.setIdNo(testValue);
        assertEquals("ID number getter/setter should work correctly", testValue, tblAgreementHistory.getIdNo());
    }
    
    @Test
    public void testBirthPlaceGetterSetter() {
        String testValue = "Bandung";
        tblAgreementHistory.setBirthPlace(testValue);
        assertEquals("Birth place getter/setter should work correctly", testValue, tblAgreementHistory.getBirthPlace());
    }
    
    @Test
    public void testBirthDtGetterSetter() {
        tblAgreementHistory.setBirthDt(testDate);
        assertEquals("Birth date getter/setter should work correctly", testDate, tblAgreementHistory.getBirthDt());
    }
    
    @Test
    public void testEmailAddressGetterSetter() {
        String testValue = "<EMAIL>";
        tblAgreementHistory.setEmailAddress(testValue);
        assertEquals("Email address getter/setter should work correctly", testValue, tblAgreementHistory.getEmailAddress());
    }
    
    @Test
    public void testPhoneNumberGetterSetter() {
        String testValue = "081987654321";
        tblAgreementHistory.setPhoneNumber(testValue);
        assertEquals("Phone number getter/setter should work correctly", testValue, tblAgreementHistory.getPhoneNumber());
    }
    
    @Test
    public void testTypeGetterSetter() {
        String testValue = "Customer";
        tblAgreementHistory.setType(testValue);
        assertEquals("Type getter/setter should work correctly", testValue, tblAgreementHistory.getType());
    }
    
    @Test
    public void testTaskIdPoloGetterSetter() {
        String testValue = "POLO12345";
        tblAgreementHistory.setTaskIdPolo(testValue);
        assertEquals("Task ID Polo getter/setter should work correctly", testValue, tblAgreementHistory.getTaskIdPolo());
    }
    
    @Test
    public void testOrderNoGetterSetter() {
        String testValue = "ORD98765";
        tblAgreementHistory.setOrderNo(testValue);
        assertEquals("Order number getter/setter should work correctly", testValue, tblAgreementHistory.getOrderNo());
    }
    
    @Test
    public void testAppNoGetterSetter() {
        String testValue = "APP54321";
        tblAgreementHistory.setAppNo(testValue);
        assertEquals("App number getter/setter should work correctly", testValue, tblAgreementHistory.getAppNo());
    }
    
    @Test
    public void testSourceGetterSetter() {
        String testValue = "Task Completed";
        tblAgreementHistory.setSource(testValue);
        assertEquals("Source getter/setter should work correctly", testValue, tblAgreementHistory.getSource());
    }
    
    @Test
    public void testDtmCrtGetterSetter() {
        tblAgreementHistory.setDtmCrt(testDate);
        assertEquals("Creation date getter/setter should work correctly", testDate, tblAgreementHistory.getDtmCrt());
    }
    
    @Test
    public void testDtmUpdGetterSetter() {
        tblAgreementHistory.setDtmUpd(testDate);
        assertEquals("Update date getter/setter should work correctly", testDate, tblAgreementHistory.getDtmUpd());
    }
    
    @Test
    public void testNullValues() {
        // Test that null values can be set and retrieved
        tblAgreementHistory.setCustNo(null);
        tblAgreementHistory.setName(null);
        tblAgreementHistory.setEmailAddress(null);
        tblAgreementHistory.setBirthDt(null);
        
        assertNull("Customer number should accept null", tblAgreementHistory.getCustNo());
        assertNull("Name should accept null", tblAgreementHistory.getName());
        assertNull("Email address should accept null", tblAgreementHistory.getEmailAddress());
        assertNull("Birth date should accept null", tblAgreementHistory.getBirthDt());
    }
    
    @Test
    public void testEmptyStringValues() {
        // Test that empty strings can be set and retrieved
        String emptyString = "";
        tblAgreementHistory.setCustNo(emptyString);
        tblAgreementHistory.setName(emptyString);
        tblAgreementHistory.setSource(emptyString);
        
        assertEquals("Customer number should accept empty string", emptyString, tblAgreementHistory.getCustNo());
        assertEquals("Name should accept empty string", emptyString, tblAgreementHistory.getName());
        assertEquals("Source should accept empty string", emptyString, tblAgreementHistory.getSource());
    }
}
