package com.adins.mss.services.model.common;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonProperty;

@SuppressWarnings("serial")
public class AddTaskCAERequest extends KbijData implements Serializable{
	private String officeCode;
	private String odrNoCAE;
	private String dukcapilStatus;
	private String negativeStatus;
	private String teleStatus;
	private String customerName;
	private String mobilePhone;
	private String codeMobilePhone;
	private String noMobilePhone;
	private String promiseSurveyDatetime;
	private String totalKontrakBakiAllSlikAktf;
	private String totalKontrakTunggakSlikAktf;
	private String totalAngsuranSlikAktf;
	private String maxDPDSlikAktf;
	private String lastUpdateSlikAktf;
	private String totalKontrakSlikLns;
	private String maxDPDSlikLns;
	private String totalKontrakMaxDPDSlikLns;
	private String lastUpdateSlikLns;
	private String fotoKtp;
	private String nikKtp;
	private String tempatLahir;
	private String tanggalLahir;
	private String alamatLegal;
	private String rtLegal;
	private String rwLegal;
	private String provinsiLegal;
	private String kabupatenLegal;
	private String kecamatanLegal;
	private String kelurahanLegal;
	private String kodeposLegal;
	private String subzipcodeLegal;
	private String statusPernikahan;
	private String namaIbuKandung;
	private String jenisKelamin;
	private String agama;
	private String email;
	private String dokTdkLgkp;
	private String mlbtknPsgn;
	private String psgnTdkTtd;
	private String fotoKtpPsgn;
	private String nikKtpPsgn;
	private String namaKtpPsgn;
	private String tempatLahirPsgn;
	private String tanggalLahirPsgn;
	private String alamatLegalPsgn;
	private String rtLegalPsgn;
	private String rwLegalPsgn;
	private String provinsiLegalPsgn;
	private String kabupatenLegalPsgn;
	private String kecamatanLegalPsgn;
	private String kelurahanLegalPsgn;
	private String kodeposLegalPsgn;
	private String namaIbuKandungPsgn;
	private String emailPsgn;
	private String mobilePhonePsgn;
	private String codeMobilePhonePsgn;
	private String noMobilePhonePsgn;
	private String fotoKtpGrntr;
	private String nikKtpGrntr;
	private String namaKtpGrntr;
	private String tempatLahirGrntr;
	private String tanggalLahirGrntr;
	private String alamatLegalGrntr;
	private String rtLegalGrntr;
	private String rwLegalGrntr;
	private String provinsiLegalGrntr;
	private String kabupatenLegalGrntr;
	private String kecamatanLegalGrntr;
	private String kelurahanLegalGrntr;
	private String kodeposLegalGrntr;
	private String namaIbuKandungGrntr;
	private String emailGrntr;
	private String mobilePhoneGrntr;
	private String codeMobilePhoneGrntr;
	private String noMobilePhoneGrntr;
	private String statusPemilikRumah;
	private String npwp;
	private String fotoKK;
	private String alamatSurvey;
	private String rtSurvey;
	private String rwSurvey;
	private String provinsiSurvey;
	private String kabupatenSurvey;
	private String kecamatanSurvey;
	private String kelurahanSurvey;
	private String kodeposSurvey;
	private String subzipcodeSurvey;
	private String assetType;
	private String productType;
	private String productOfferingType;
	private String brand;
	private String asset;
	private String groupType;
	private String type;
	private String kategoriAsset;
	private String tahunKendaraan;
	private String soa;
	private String kodeNamaDealer;
	private String posDealer;
	private String kodeNamaSales;
	private String notes;
	private String prospectInterest;
	private String persetujuanSlik;
	private String fotoPemohon;
	private String fotoPsgn;
	private String fotoGrntr;
	private String otr;
	private String dpNtfAmount;
	private String dpMurni;
	private String dpPercentage;
	private String tenor;
	private String angsuran;
	private String penghasilan;
	private String education;
	private String professionName;
	private String lengthWork;
	private String dsrPercentage;
	private String stayLength;
	private String bpkbOwner;
	private String bpkbOwnerRelation;
	private String biometricPemohonResult;
	private String biometricPsgnResult;
	private String biometricGrntResult;
	private String isInstantApproval;
	private String persetujuanEsign;
	private String productCategoryCode;
	private String dukcapilPsgnStatus;
	private String dukcapilGrntResult;
	private String negativePsgnStatus;
	private String negativeGrntResult;
	private String telePsgnStatus;
	private String teleGrntResult;
	private String addrGrntrEqualAddrCust;
	private String addrPsgnEqualAddrCust;
	private String addrSurveyEqualAddrLegal;
	private String imgTtdCust;
	private String imgTtdPsgn;
	private String imgTtdGrntr;
	private String idStagingAsset;
	private String flagKawanInternal;
	private String location;
	private String ownerRelationship;
	private String jumlahTanggungan;
	private String statusPekerjaan;
	private String jenisIndustri;
	private String kodeReferantor;
	private String namaReferantor;
	private String totalKontrakBakiAllSlikAktfPsgn;
	private String totalKontrakTunggakSlikAktfPsgn;
	private String totalAngsuranSlikAktfPsgn;
	private String maxDPDSlikAktfPsgn;
	private String lastUpdateSlikAktfPsgn;
	private String totalKontrakSlikLnsPsgn;
	private String maxDPDSlikLnsPsgn;
	private String totalKontrakMaxDPDSlikLnsPsgn;
	private String lastUpdateSlikLnsPsgn;
	private String totalKontrakBakiAllSlikAktfGrtr;
	private String totalKontrakTunggakSlikAktfGrtr;
	private String totalAngsuranSlikAktfGrtr;
	private String maxDPDSlikAktfGrtr;
	private String lastUpdateSlikAktfGrtr;
	private String totalKontrakSlikLnsGrtr;
	private String maxDPDSlikLnsGrtr;
	private String totalKontrakMaxDPDSlikLnsGrtr;
	private String lastUpdateSlikLnsGrtr;
	private String kodeReferantor2;
	private String namaReferantor2;
	private String kodeReferantor3;
	private String namaReferantor3;
	private String fieldPersonIA;
	private String jobCodeIA;
	private String noteTelesales;
	private String funding;
	private String custNo;
	private String tglOrderIn;
	@JsonProperty("InscoBranchCode") private String InscoBranchCode;
	@JsonProperty("InscoBranchName") private String InscoBranchName;
	@JsonProperty("IsProductInformation") private String IsProductInformation;
	@JsonProperty("IsFollowUp") private String IsFollowUp;
	@JsonProperty("IsPreApproval") private Integer isPreApproval;
	private Integer mandatoryLevel;
	private String opsiPenanganan;
	@JsonProperty("InstallmentWOM")	private String installmentWOM;
	@JsonProperty("InstallmentOther") private String otherInstallment;
	private String professionNamePsgn;
	private String layerapproval;
	@JsonProperty("SourceData") private String SourceData;
	@JsonProperty("SendLinkCustomer") private String SendLinkCustomer;
	@JsonProperty("SendLinkSpouse") private String SendLinkSpouse;
	@JsonProperty("SendLinkGuarantor") private String SendLinkGuarantor;
	
	 //CR OPSI PENYELESAIAN SENGKETA
	 @JsonProperty("custProtectCode") private String custProtectCode;
	 
	
	public String getOfficeCode() {
		return officeCode;
	}
	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}
	public String getOdrNoCAE() {
		return odrNoCAE;
	}
	public void setOdrNoCAE(String odrNoCAE) {
		this.odrNoCAE = odrNoCAE;
	}
	public String getDukcapilStatus() {
		return dukcapilStatus;
	}
	public void setDukcapilStatus(String dukcapilStatus) {
		this.dukcapilStatus = dukcapilStatus;
	}
	public String getNegativeStatus() {
		return negativeStatus;
	}
	public void setNegativeStatus(String negativeStatus) {
		this.negativeStatus = negativeStatus;
	}
	public String getTeleStatus() {
		return teleStatus;
	}
	public void setTeleStatus(String teleStatus) {
		this.teleStatus = teleStatus;
	}
	public String getCustomerName() {
		return customerName;
	}
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	public String getMobilePhone() {
		return mobilePhone;
	}
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}
	public String getCodeMobilePhone() {
		return codeMobilePhone;
	}
	public void setCodeMobilePhone(String codeMobilePhone) {
		this.codeMobilePhone = codeMobilePhone;
	}
	public String getNoMobilePhone() {
		return noMobilePhone;
	}
	public void setNoMobilePhone(String noMobilePhone) {
		this.noMobilePhone = noMobilePhone;
	}
	public String getPromiseSurveyDatetime() {
		return promiseSurveyDatetime;
	}
	public void setPromiseSurveyDatetime(String promiseSurveyDatetime) {
		this.promiseSurveyDatetime = promiseSurveyDatetime;
	}
	public String getTotalKontrakBakiAllSlikAktf() {
		return totalKontrakBakiAllSlikAktf;
	}
	public void setTotalKontrakBakiAllSlikAktf(String totalKontrakBakiAllSlikAktf) {
		this.totalKontrakBakiAllSlikAktf = totalKontrakBakiAllSlikAktf;
	}
	public String getTotalKontrakTunggakSlikAktf() {
		return totalKontrakTunggakSlikAktf;
	}
	public void setTotalKontrakTunggakSlikAktf(String totalKontrakTunggakSlikAktf) {
		this.totalKontrakTunggakSlikAktf = totalKontrakTunggakSlikAktf;
	}
	public String getTotalAngsuranSlikAktf() {
		return totalAngsuranSlikAktf;
	}
	public void setTotalAngsuranSlikAktf(String totalAngsuranSlikAktf) {
		this.totalAngsuranSlikAktf = totalAngsuranSlikAktf;
	}
	public String getMaxDPDSlikAktf() {
		return maxDPDSlikAktf;
	}
	public void setMaxDPDSlikAktf(String maxDPDSlikAktf) {
		this.maxDPDSlikAktf = maxDPDSlikAktf;
	}
	public String getLastUpdateSlikAktf() {
		return lastUpdateSlikAktf;
	}
	public void setLastUpdateSlikAktf(String lastUpdateSlikAktf) {
		this.lastUpdateSlikAktf = lastUpdateSlikAktf;
	}
	public String getTotalKontrakSlikLns() {
		return totalKontrakSlikLns;
	}
	public void setTotalKontrakSlikLns(String totalKontrakSlikLns) {
		this.totalKontrakSlikLns = totalKontrakSlikLns;
	}
	public String getMaxDPDSlikLns() {
		return maxDPDSlikLns;
	}
	public void setMaxDPDSlikLns(String maxDPDSlikLns) {
		this.maxDPDSlikLns = maxDPDSlikLns;
	}
	public String getTotalKontrakMaxDPDSlikLns() {
		return totalKontrakMaxDPDSlikLns;
	}
	public void setTotalKontrakMaxDPDSlikLns(String totalKontrakMaxDPDSlikLns) {
		this.totalKontrakMaxDPDSlikLns = totalKontrakMaxDPDSlikLns;
	}
	public String getLastUpdateSlikLns() {
		return lastUpdateSlikLns;
	}
	public void setLastUpdateSlikLns(String lastUpdateSlikLns) {
		this.lastUpdateSlikLns = lastUpdateSlikLns;
	}
	public String getFotoKtp() {
		return fotoKtp;
	}
	public void setFotoKtp(String fotoKtp) {
		this.fotoKtp = fotoKtp;
	}
	public String getNikKtp() {
		return nikKtp;
	}
	public void setNikKtp(String nikKtp) {
		this.nikKtp = nikKtp;
	}
	public String getTempatLahir() {
		return tempatLahir;
	}
	public void setTempatLahir(String tempatLahir) {
		this.tempatLahir = tempatLahir;
	}
	public String getTanggalLahir() {
		return tanggalLahir;
	}
	public void setTanggalLahir(String tanggalLahir) {
		this.tanggalLahir = tanggalLahir;
	}
	public String getAlamatLegal() {
		return alamatLegal;
	}
	public void setAlamatLegal(String alamatLegal) {
		this.alamatLegal = alamatLegal;
	}
	public String getRtLegal() {
		return rtLegal;
	}
	public void setRtLegal(String rtLegal) {
		this.rtLegal = rtLegal;
	}
	public String getRwLegal() {
		return rwLegal;
	}
	public void setRwLegal(String rwLegal) {
		this.rwLegal = rwLegal;
	}
	public String getProvinsiLegal() {
		return provinsiLegal;
	}
	public void setProvinsiLegal(String provinsiLegal) {
		this.provinsiLegal = provinsiLegal;
	}
	public String getKabupatenLegal() {
		return kabupatenLegal;
	}
	public void setKabupatenLegal(String kabupatenLegal) {
		this.kabupatenLegal = kabupatenLegal;
	}
	public String getKecamatanLegal() {
		return kecamatanLegal;
	}
	public void setKecamatanLegal(String kecamatanLegal) {
		this.kecamatanLegal = kecamatanLegal;
	}
	public String getKelurahanLegal() {
		return kelurahanLegal;
	}
	public void setKelurahanLegal(String kelurahanLegal) {
		this.kelurahanLegal = kelurahanLegal;
	}
	public String getKodeposLegal() {
		return kodeposLegal;
	}
	public void setKodeposLegal(String kodeposLegal) {
		this.kodeposLegal = kodeposLegal;
	}
	public String getSubzipcodeLegal() {
		return subzipcodeLegal;
	}
	public void setSubzipcodeLegal(String subzipcodeLegal) {
		this.subzipcodeLegal = subzipcodeLegal;
	}
	public String getStatusPernikahan() {
		return statusPernikahan;
	}
	public void setStatusPernikahan(String statusPernikahan) {
		this.statusPernikahan = statusPernikahan;
	}
	public String getNamaIbuKandung() {
		return namaIbuKandung;
	}
	public void setNamaIbuKandung(String namaIbuKandung) {
		this.namaIbuKandung = namaIbuKandung;
	}
	public String getJenisKelamin() {
		return jenisKelamin;
	}
	public void setJenisKelamin(String jenisKelamin) {
		this.jenisKelamin = jenisKelamin;
	}
	public String getAgama() {
		return agama;
	}
	public void setAgama(String agama) {
		this.agama = agama;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getDokTdkLgkp() {
		return dokTdkLgkp;
	}
	public void setDokTdkLgkp(String dokTdkLgkp) {
		this.dokTdkLgkp = dokTdkLgkp;
	}
	public String getMlbtknPsgn() {
		return mlbtknPsgn;
	}
	public void setMlbtknPsgn(String mlbtknPsgn) {
		this.mlbtknPsgn = mlbtknPsgn;
	}
	public String getPsgnTdkTtd() {
		return psgnTdkTtd;
	}
	public void setPsgnTdkTtd(String psgnTdkTtd) {
		this.psgnTdkTtd = psgnTdkTtd;
	}
	public String getFotoKtpPsgn() {
		return fotoKtpPsgn;
	}
	public void setFotoKtpPsgn(String fotoKtpPsgn) {
		this.fotoKtpPsgn = fotoKtpPsgn;
	}
	public String getNikKtpPsgn() {
		return nikKtpPsgn;
	}
	public void setNikKtpPsgn(String nikKtpPsgn) {
		this.nikKtpPsgn = nikKtpPsgn;
	}
	public String getNamaKtpPsgn() {
		return namaKtpPsgn;
	}
	public void setNamaKtpPsgn(String namaKtpPsgn) {
		this.namaKtpPsgn = namaKtpPsgn;
	}
	public String getTempatLahirPsgn() {
		return tempatLahirPsgn;
	}
	public void setTempatLahirPsgn(String tempatLahirPsgn) {
		this.tempatLahirPsgn = tempatLahirPsgn;
	}
	public String getTanggalLahirPsgn() {
		return tanggalLahirPsgn;
	}
	public void setTanggalLahirPsgn(String tanggalLahirPsgn) {
		this.tanggalLahirPsgn = tanggalLahirPsgn;
	}
	public String getAlamatLegalPsgn() {
		return alamatLegalPsgn;
	}
	public void setAlamatLegalPsgn(String alamatLegalPsgn) {
		this.alamatLegalPsgn = alamatLegalPsgn;
	}
	public String getRtLegalPsgn() {
		return rtLegalPsgn;
	}
	public void setRtLegalPsgn(String rtLegalPsgn) {
		this.rtLegalPsgn = rtLegalPsgn;
	}
	public String getRwLegalPsgn() {
		return rwLegalPsgn;
	}
	public void setRwLegalPsgn(String rwLegalPsgn) {
		this.rwLegalPsgn = rwLegalPsgn;
	}
	public String getProvinsiLegalPsgn() {
		return provinsiLegalPsgn;
	}
	public void setProvinsiLegalPsgn(String provinsiLegalPsgn) {
		this.provinsiLegalPsgn = provinsiLegalPsgn;
	}
	public String getKabupatenLegalPsgn() {
		return kabupatenLegalPsgn;
	}
	public void setKabupatenLegalPsgn(String kabupatenLegalPsgn) {
		this.kabupatenLegalPsgn = kabupatenLegalPsgn;
	}
	public String getKecamatanLegalPsgn() {
		return kecamatanLegalPsgn;
	}
	public void setKecamatanLegalPsgn(String kecamatanLegalPsgn) {
		this.kecamatanLegalPsgn = kecamatanLegalPsgn;
	}
	public String getKelurahanLegalPsgn() {
		return kelurahanLegalPsgn;
	}
	public void setKelurahanLegalPsgn(String kelurahanLegalPsgn) {
		this.kelurahanLegalPsgn = kelurahanLegalPsgn;
	}
	public String getKodeposLegalPsgn() {
		return kodeposLegalPsgn;
	}
	public void setKodeposLegalPsgn(String kodeposLegalPsgn) {
		this.kodeposLegalPsgn = kodeposLegalPsgn;
	}
	public String getNamaIbuKandungPsgn() {
		return namaIbuKandungPsgn;
	}
	public void setNamaIbuKandungPsgn(String namaIbuKandungPsgn) {
		this.namaIbuKandungPsgn = namaIbuKandungPsgn;
	}
	public String getEmailPsgn() {
		return emailPsgn;
	}
	public void setEmailPsgn(String emailPsgn) {
		this.emailPsgn = emailPsgn;
	}
	public String getMobilePhonePsgn() {
		return mobilePhonePsgn;
	}
	public void setMobilePhonePsgn(String mobilePhonePsgn) {
		this.mobilePhonePsgn = mobilePhonePsgn;
	}
	public String getCodeMobilePhonePsgn() {
		return codeMobilePhonePsgn;
	}
	public void setCodeMobilePhonePsgn(String codeMobilePhonePsgn) {
		this.codeMobilePhonePsgn = codeMobilePhonePsgn;
	}
	public String getNoMobilePhonePsgn() {
		return noMobilePhonePsgn;
	}
	public void setNoMobilePhonePsgn(String noMobilePhonePsgn) {
		this.noMobilePhonePsgn = noMobilePhonePsgn;
	}
	public String getFotoKtpGrntr() {
		return fotoKtpGrntr;
	}
	public void setFotoKtpGrntr(String fotoKtpGrntr) {
		this.fotoKtpGrntr = fotoKtpGrntr;
	}
	public String getNikKtpGrntr() {
		return nikKtpGrntr;
	}
	public void setNikKtpGrntr(String nikKtpGrntr) {
		this.nikKtpGrntr = nikKtpGrntr;
	}
	public String getNamaKtpGrntr() {
		return namaKtpGrntr;
	}
	public void setNamaKtpGrntr(String namaKtpGrntr) {
		this.namaKtpGrntr = namaKtpGrntr;
	}
	public String getTempatLahirGrntr() {
		return tempatLahirGrntr;
	}
	public void setTempatLahirGrntr(String tempatLahirGrntr) {
		this.tempatLahirGrntr = tempatLahirGrntr;
	}
	public String getTanggalLahirGrntr() {
		return tanggalLahirGrntr;
	}
	public void setTanggalLahirGrntr(String tanggalLahirGrntr) {
		this.tanggalLahirGrntr = tanggalLahirGrntr;
	}
	public String getAlamatLegalGrntr() {
		return alamatLegalGrntr;
	}
	public void setAlamatLegalGrntr(String alamatLegalGrntr) {
		this.alamatLegalGrntr = alamatLegalGrntr;
	}
	public String getRtLegalGrntr() {
		return rtLegalGrntr;
	}
	public void setRtLegalGrntr(String rtLegalGrntr) {
		this.rtLegalGrntr = rtLegalGrntr;
	}
	public String getRwLegalGrntr() {
		return rwLegalGrntr;
	}
	public void setRwLegalGrntr(String rwLegalGrntr) {
		this.rwLegalGrntr = rwLegalGrntr;
	}
	public String getProvinsiLegalGrntr() {
		return provinsiLegalGrntr;
	}
	public void setProvinsiLegalGrntr(String provinsiLegalGrntr) {
		this.provinsiLegalGrntr = provinsiLegalGrntr;
	}
	public String getKabupatenLegalGrntr() {
		return kabupatenLegalGrntr;
	}
	public void setKabupatenLegalGrntr(String kabupatenLegalGrntr) {
		this.kabupatenLegalGrntr = kabupatenLegalGrntr;
	}
	public String getKecamatanLegalGrntr() {
		return kecamatanLegalGrntr;
	}
	public void setKecamatanLegalGrntr(String kecamatanLegalGrntr) {
		this.kecamatanLegalGrntr = kecamatanLegalGrntr;
	}
	public String getKelurahanLegalGrntr() {
		return kelurahanLegalGrntr;
	}
	public void setKelurahanLegalGrntr(String kelurahanLegalGrntr) {
		this.kelurahanLegalGrntr = kelurahanLegalGrntr;
	}
	public String getKodeposLegalGrntr() {
		return kodeposLegalGrntr;
	}
	public void setKodeposLegalGrntr(String kodeposLegalGrntr) {
		this.kodeposLegalGrntr = kodeposLegalGrntr;
	}
	public String getNamaIbuKandungGrntr() {
		return namaIbuKandungGrntr;
	}
	public void setNamaIbuKandungGrntr(String namaIbuKandungGrntr) {
		this.namaIbuKandungGrntr = namaIbuKandungGrntr;
	}
	public String getEmailGrntr() {
		return emailGrntr;
	}
	public void setEmailGrntr(String emailGrntr) {
		this.emailGrntr = emailGrntr;
	}
	public String getMobilePhoneGrntr() {
		return mobilePhoneGrntr;
	}
	public void setMobilePhoneGrntr(String mobilePhoneGrntr) {
		this.mobilePhoneGrntr = mobilePhoneGrntr;
	}
	public String getCodeMobilePhoneGrntr() {
		return codeMobilePhoneGrntr;
	}
	public void setCodeMobilePhoneGrntr(String codeMobilePhoneGrntr) {
		this.codeMobilePhoneGrntr = codeMobilePhoneGrntr;
	}
	public String getNoMobilePhoneGrntr() {
		return noMobilePhoneGrntr;
	}
	public void setNoMobilePhoneGrntr(String noMobilePhoneGrntr) {
		this.noMobilePhoneGrntr = noMobilePhoneGrntr;
	}
	public String getStatusPemilikRumah() {
		return statusPemilikRumah;
	}
	public void setStatusPemilikRumah(String statusPemilikRumah) {
		this.statusPemilikRumah = statusPemilikRumah;
	}
	public String getNpwp() {
		return npwp;
	}
	public void setNpwp(String npwp) {
		this.npwp = npwp;
	}
	public String getFotoKK() {
		return fotoKK;
	}
	public void setFotoKK(String fotoKK) {
		this.fotoKK = fotoKK;
	}
	public String getAlamatSurvey() {
		return alamatSurvey;
	}
	public void setAlamatSurvey(String alamatSurvey) {
		this.alamatSurvey = alamatSurvey;
	}
	public String getRtSurvey() {
		return rtSurvey;
	}
	public void setRtSurvey(String rtSurvey) {
		this.rtSurvey = rtSurvey;
	}
	public String getRwSurvey() {
		return rwSurvey;
	}
	public void setRwSurvey(String rwSurvey) {
		this.rwSurvey = rwSurvey;
	}
	public String getProvinsiSurvey() {
		return provinsiSurvey;
	}
	public void setProvinsiSurvey(String provinsiSurvey) {
		this.provinsiSurvey = provinsiSurvey;
	}
	public String getKabupatenSurvey() {
		return kabupatenSurvey;
	}
	public void setKabupatenSurvey(String kabupatenSurvey) {
		this.kabupatenSurvey = kabupatenSurvey;
	}
	public String getKecamatanSurvey() {
		return kecamatanSurvey;
	}
	public void setKecamatanSurvey(String kecamatanSurvey) {
		this.kecamatanSurvey = kecamatanSurvey;
	}
	public String getKelurahanSurvey() {
		return kelurahanSurvey;
	}
	public void setKelurahanSurvey(String kelurahanSurvey) {
		this.kelurahanSurvey = kelurahanSurvey;
	}
	public String getKodeposSurvey() {
		return kodeposSurvey;
	}
	public void setKodeposSurvey(String kodeposSurvey) {
		this.kodeposSurvey = kodeposSurvey;
	}
	public String getAssetType() {
		return assetType;
	}
	public void setAssetType(String assetType) {
		this.assetType = assetType;
	}
	public String getSubzipcodeSurvey() {
		return subzipcodeSurvey;
	}
	public void setSubzipcodeSurvey(String subzipcodeSurvey) {
		this.subzipcodeSurvey = subzipcodeSurvey;
	}
	public String getProductType() {
		return productType;
	}
	public void setProductType(String productType) {
		this.productType = productType;
	}
	public String getProductOfferingType() {
		return productOfferingType;
	}
	public void setProductOfferingType(String productOfferingType) {
		this.productOfferingType = productOfferingType;
	}
	public String getBrand() {
		return brand;
	}
	public void setBrand(String brand) {
		this.brand = brand;
	}
	public String getAsset() {
		return asset;
	}
	public void setAsset(String asset) {
		this.asset = asset;
	}
	public String getGroupType() {
		return groupType;
	}
	public void setGroupType(String groupType) {
		this.groupType = groupType;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getKategoriAsset() {
		return kategoriAsset;
	}
	public void setKategoriAsset(String kategoriAsset) {
		this.kategoriAsset = kategoriAsset;
	}
	public String getTahunKendaraan() {
		return tahunKendaraan;
	}
	public void setTahunKendaraan(String tahunKendaraan) {
		this.tahunKendaraan = tahunKendaraan;
	}
	public String getSoa() {
		return soa;
	}
	public void setSoa(String soa) {
		this.soa = soa;
	}
	public String getKodeNamaDealer() {
		return kodeNamaDealer;
	}
	public void setKodeNamaDealer(String kodeNamaDealer) {
		this.kodeNamaDealer = kodeNamaDealer;
	}
	public String getPosDealer() {
		return posDealer;
	}
	public void setPosDealer(String posDealer) {
		this.posDealer = posDealer;
	}
	public String getKodeNamaSales() {
		return kodeNamaSales;
	}
	public void setKodeNamaSales(String kodeNamaSales) {
		this.kodeNamaSales = kodeNamaSales;
	}
	public String getNotes() {
		return notes;
	}
	public void setNotes(String notes) {
		this.notes = notes;
	}
	public String getProspectInterest() {
		return prospectInterest;
	}
	public void setProspectInterest(String prospectInterest) {
		this.prospectInterest = prospectInterest;
	}
	public String getPersetujuanSlik() {
		return persetujuanSlik;
	}
	public void setPersetujuanSlik(String persetujuanSlik) {
		this.persetujuanSlik = persetujuanSlik;
	}
	public String getFotoPemohon() {
		return fotoPemohon;
	}
	public void setFotoPemohon(String fotoPemohon) {
		this.fotoPemohon = fotoPemohon;
	}
	public String getFotoPsgn() {
		return fotoPsgn;
	}
	public void setFotoPsgn(String fotoPsgn) {
		this.fotoPsgn = fotoPsgn;
	}
	public String getFotoGrntr() {
		return fotoGrntr;
	}
	public void setFotoGrntr(String fotoGrntr) {
		this.fotoGrntr = fotoGrntr;
	}
	public String getOtr() {
		return otr;
	}
	public void setOtr(String otr) {
		this.otr = otr;
	}
	public String getDpNtfAmount() {
		return dpNtfAmount;
	}
	public void setDpNtfAmount(String dpNtfAmount) {
		this.dpNtfAmount = dpNtfAmount;
	}
	public String getDpMurni() {
		return dpMurni;
	}
	public void setDpMurni(String dpMurni) {
		this.dpMurni = dpMurni;
	}
	public String getDpPercentage() {
		return dpPercentage;
	}
	public void setDpPercentage(String dpPercentage) {
		this.dpPercentage = dpPercentage;
	}
	public String getTenor() {
		return tenor;
	}
	public void setTenor(String tenor) {
		this.tenor = tenor;
	}
	public String getAngsuran() {
		return angsuran;
	}
	public void setAngsuran(String angsuran) {
		this.angsuran = angsuran;
	}
	public String getPenghasilan() {
		return penghasilan;
	}
	public void setPenghasilan(String penghasilan) {
		this.penghasilan = penghasilan;
	}
	public String getEducation() {
		return education;
	}
	public void setEducation(String education) {
		this.education = education;
	}
	public String getProfessionName() {
		return professionName;
	}
	public void setProfessionName(String professionName) {
		this.professionName = professionName;
	}
	public String getLengthWork() {
		return lengthWork;
	}
	public void setLengthWork(String lengthWork) {
		this.lengthWork = lengthWork;
	}
	public String getDsrPercentage() {
		return dsrPercentage;
	}
	public void setDsrPercentage(String dsrPercentage) {
		this.dsrPercentage = dsrPercentage;
	}
	public String getStayLength() {
		return stayLength;
	}
	public void setStayLength(String stayLength) {
		this.stayLength = stayLength;
	}
	public String getBpkbOwner() {
		return bpkbOwner;
	}
	public void setBpkbOwner(String bpkbOwner) {
		this.bpkbOwner = bpkbOwner;
	}
	public String getBpkbOwnerRelation() {
		return bpkbOwnerRelation;
	}
	public void setBpkbOwnerRelation(String bpkbOwnerRelation) {
		this.bpkbOwnerRelation = bpkbOwnerRelation;
	}
	public String getBiometricPemohonResult() {
		return biometricPemohonResult;
	}
	public void setBiometricPemohonResult(String biometricPemohonResult) {
		this.biometricPemohonResult = biometricPemohonResult;
	}
	public String getBiometricPsgnResult() {
		return biometricPsgnResult;
	}
	public void setBiometricPsgnResult(String biometricPsgnResult) {
		this.biometricPsgnResult = biometricPsgnResult;
	}
	public String getBiometricGrntResult() {
		return biometricGrntResult;
	}
	public void setBiometricGrntResult(String biometricGrntResult) {
		this.biometricGrntResult = biometricGrntResult;
	}
	public String getIsInstantApproval() {
		return isInstantApproval;
	}
	public void setIsInstantApproval(String isInstantApproval) {
		this.isInstantApproval = isInstantApproval;
	}
	public String getPersetujuanEsign() {
		return persetujuanEsign;
	}
	public void setPersetujuanEsign(String persetujuanEsign) {
		this.persetujuanEsign = persetujuanEsign;
	}
	public String getProductCategoryCode() {
		return productCategoryCode;
	}
	public void setProductCategoryCode(String productCategoryCode) {
		this.productCategoryCode = productCategoryCode;
	}
	
	public String getDukcapilPsgnStatus() {
		return dukcapilPsgnStatus;
	}
	public void setDukcapilPsgnStatus(String dukcapilPsgnStatus) {
		this.dukcapilPsgnStatus = dukcapilPsgnStatus;
	}
	public String getDukcapilGrntResult() {
		return dukcapilGrntResult;
	}
	public void setDukcapilGrntResult(String dukcapilGrntResult) {
		this.dukcapilGrntResult = dukcapilGrntResult;
	}
	public String getNegativePsgnStatus() {
		return negativePsgnStatus;
	}
	public void setNegativePsgnStatus(String negativePsgnStatus) {
		this.negativePsgnStatus = negativePsgnStatus;
	}
	public String getNegativeGrntResult() {
		return negativeGrntResult;
	}
	public void setNegativeGrntResult(String negativeGrntResult) {
		this.negativeGrntResult = negativeGrntResult;
	}
	public String getTelePsgnStatus() {
		return telePsgnStatus;
	}
	public void setTelePsgnStatus(String telePsgnStatus) {
		this.telePsgnStatus = telePsgnStatus;
	}
	public String getTeleGrntResult() {
		return teleGrntResult;
	}
	public void setTeleGrntResult(String teleGrntResult) {
		this.teleGrntResult = teleGrntResult;
	}
	public String getAddrGrntrEqualAddrCust() {
		return addrGrntrEqualAddrCust;
	}
	public void setAddrGrntrEqualAddrCust(String addrGrntrEqualAddrCust) {
		this.addrGrntrEqualAddrCust = addrGrntrEqualAddrCust;
	}
	public String getAddrPsgnEqualAddrCust() {
		return addrPsgnEqualAddrCust;
	}
	public void setAddrPsgnEqualAddrCust(String addrPsgnEqualAddrCust) {
		this.addrPsgnEqualAddrCust = addrPsgnEqualAddrCust;
	}
	public String getAddrSurveyEqualAddrLegal() {
		return addrSurveyEqualAddrLegal;
	}
	public void setAddrSurveyEqualAddrLegal(String addrSurveyEqualAddrLegal) {
		this.addrSurveyEqualAddrLegal = addrSurveyEqualAddrLegal;
	}
	public String getImgTtdCust() {
		return imgTtdCust;
	}
	public void setImgTtdCust(String imgTtdCust) {
		this.imgTtdCust = imgTtdCust;
	}
	public String getImgTtdPsgn() {
		return imgTtdPsgn;
	}
	public void setImgTtdPsgn(String imgTtdPsgn) {
		this.imgTtdPsgn = imgTtdPsgn;
	}
	public String getImgTtdGrntr() {
		return imgTtdGrntr;
	}
	public void setImgTtdGrntr(String imgTtdGrntr) {
		this.imgTtdGrntr = imgTtdGrntr;
	}
	public String getIdStagingAsset() {
		return idStagingAsset;
	}
	public void setIdStagingAsset(String idStagingAsset) {
		this.idStagingAsset = idStagingAsset;
	}
	public String getFlagKawanInternal() {
		return flagKawanInternal;
	}
	public void setFlagKawanInternal(String flagKawanInternal) {
		this.flagKawanInternal = flagKawanInternal;
	}
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	
	public String getOwnerRelationship() {
		return ownerRelationship;
	}
	public void setOwnerRelationship(String ownerRelationship) {
		this.ownerRelationship = ownerRelationship;
	}
	public String getJumlahTanggungan() {
		return jumlahTanggungan;
	}
	public void setJumlahTanggungan(String jumlahTanggungan) {
		this.jumlahTanggungan = jumlahTanggungan;
	}
	public String getStatusPekerjaan() {
		return statusPekerjaan;
	}
	public void setStatusPekerjaan(String statusPekerjaan) {
		this.statusPekerjaan = statusPekerjaan;
	}
	public String getJenisIndustri() {
		return jenisIndustri;
	}
	public void setJenisIndustri(String jenisIndustri) {
		this.jenisIndustri = jenisIndustri;
	}
	public String getKodeReferantor() {
		return kodeReferantor;
	}
	public void setKodeReferantor(String kodeReferantor) {
		this.kodeReferantor = kodeReferantor;
	}
	public String getNamaReferantor() {
		return namaReferantor;
	}
	public void setNamaReferantor(String namaReferantor) {
		this.namaReferantor = namaReferantor;
	}
	public String getTotalKontrakBakiAllSlikAktfPsgn() {
		return totalKontrakBakiAllSlikAktfPsgn;
	}
	public void setTotalKontrakBakiAllSlikAktfPsgn(String totalKontrakBakiAllSlikAktfPsgn) {
		this.totalKontrakBakiAllSlikAktfPsgn = totalKontrakBakiAllSlikAktfPsgn;
	}
	public String getTotalKontrakTunggakSlikAktfPsgn() {
		return totalKontrakTunggakSlikAktfPsgn;
	}
	public void setTotalKontrakTunggakSlikAktfPsgn(String totalKontrakTunggakSlikAktfPsgn) {
		this.totalKontrakTunggakSlikAktfPsgn = totalKontrakTunggakSlikAktfPsgn;
	}
	public String getTotalAngsuranSlikAktfPsgn() {
		return totalAngsuranSlikAktfPsgn;
	}
	public void setTotalAngsuranSlikAktfPsgn(String totalAngsuranSlikAktfPsgn) {
		this.totalAngsuranSlikAktfPsgn = totalAngsuranSlikAktfPsgn;
	}
	public String getMaxDPDSlikAktfPsgn() {
		return maxDPDSlikAktfPsgn;
	}
	public void setMaxDPDSlikAktfPsgn(String maxDPDSlikAktfPsgn) {
		this.maxDPDSlikAktfPsgn = maxDPDSlikAktfPsgn;
	}
	public String getLastUpdateSlikAktfPsgn() {
		return lastUpdateSlikAktfPsgn;
	}
	public void setLastUpdateSlikAktfPsgn(String lastUpdateSlikAktfPsgn) {
		this.lastUpdateSlikAktfPsgn = lastUpdateSlikAktfPsgn;
	}
	public String getTotalKontrakSlikLnsPsgn() {
		return totalKontrakSlikLnsPsgn;
	}
	public void setTotalKontrakSlikLnsPsgn(String totalKontrakSlikLnsPsgn) {
		this.totalKontrakSlikLnsPsgn = totalKontrakSlikLnsPsgn;
	}
	public String getMaxDPDSlikLnsPsgn() {
		return maxDPDSlikLnsPsgn;
	}
	public void setMaxDPDSlikLnsPsgn(String maxDPDSlikLnsPsgn) {
		this.maxDPDSlikLnsPsgn = maxDPDSlikLnsPsgn;
	}
	public String getTotalKontrakMaxDPDSlikLnsPsgn() {
		return totalKontrakMaxDPDSlikLnsPsgn;
	}
	public void setTotalKontrakMaxDPDSlikLnsPsgn(String totalKontrakMaxDPDSlikLnsPsgn) {
		this.totalKontrakMaxDPDSlikLnsPsgn = totalKontrakMaxDPDSlikLnsPsgn;
	}
	public String getLastUpdateSlikLnsPsgn() {
		return lastUpdateSlikLnsPsgn;
	}
	public void setLastUpdateSlikLnsPsgn(String lastUpdateSlikLnsPsgn) {
		this.lastUpdateSlikLnsPsgn = lastUpdateSlikLnsPsgn;
	}
	public String getTotalKontrakBakiAllSlikAktfGrtr() {
		return totalKontrakBakiAllSlikAktfGrtr;
	}
	public void setTotalKontrakBakiAllSlikAktfGrtr(String totalKontrakBakiAllSlikAktfGrtr) {
		this.totalKontrakBakiAllSlikAktfGrtr = totalKontrakBakiAllSlikAktfGrtr;
	}
	public String getTotalKontrakTunggakSlikAktfGrtr() {
		return totalKontrakTunggakSlikAktfGrtr;
	}
	public void setTotalKontrakTunggakSlikAktfGrtr(String totalKontrakTunggakSlikAktfGrtr) {
		this.totalKontrakTunggakSlikAktfGrtr = totalKontrakTunggakSlikAktfGrtr;
	}
	public String getTotalAngsuranSlikAktfGrtr() {
		return totalAngsuranSlikAktfGrtr;
	}
	public void setTotalAngsuranSlikAktfGrtr(String totalAngsuranSlikAktfGrtr) {
		this.totalAngsuranSlikAktfGrtr = totalAngsuranSlikAktfGrtr;
	}
	public String getMaxDPDSlikAktfGrtr() {
		return maxDPDSlikAktfGrtr;
	}
	public void setMaxDPDSlikAktfGrtr(String maxDPDSlikAktfGrtr) {
		this.maxDPDSlikAktfGrtr = maxDPDSlikAktfGrtr;
	}
	public String getLastUpdateSlikAktfGrtr() {
		return lastUpdateSlikAktfGrtr;
	}
	public void setLastUpdateSlikAktfGrtr(String lastUpdateSlikAktfGrtr) {
		this.lastUpdateSlikAktfGrtr = lastUpdateSlikAktfGrtr;
	}
	public String getTotalKontrakSlikLnsGrtr() {
		return totalKontrakSlikLnsGrtr;
	}
	public void setTotalKontrakSlikLnsGrtr(String totalKontrakSlikLnsGrtr) {
		this.totalKontrakSlikLnsGrtr = totalKontrakSlikLnsGrtr;
	}
	public String getMaxDPDSlikLnsGrtr() {
		return maxDPDSlikLnsGrtr;
	}
	public void setMaxDPDSlikLnsGrtr(String maxDPDSlikLnsGrtr) {
		this.maxDPDSlikLnsGrtr = maxDPDSlikLnsGrtr;
	}
	public String getTotalKontrakMaxDPDSlikLnsGrtr() {
		return totalKontrakMaxDPDSlikLnsGrtr;
	}
	public void setTotalKontrakMaxDPDSlikLnsGrtr(String totalKontrakMaxDPDSlikLnsGrtr) {
		this.totalKontrakMaxDPDSlikLnsGrtr = totalKontrakMaxDPDSlikLnsGrtr;
	}
	public String getLastUpdateSlikLnsGrtr() {
		return lastUpdateSlikLnsGrtr;
	}
	public void setLastUpdateSlikLnsGrtr(String lastUpdateSlikLnsGrtr) {
		this.lastUpdateSlikLnsGrtr = lastUpdateSlikLnsGrtr;
	}
	public String getKodeReferantor2() {
		return kodeReferantor2;
	}
	public void setKodeReferantor2(String kodeReferantor2) {
		this.kodeReferantor2 = kodeReferantor2;
	}
	public String getNamaReferantor2() {
		return namaReferantor2;
	}
	public void setNamaReferantor2(String namaReferantor2) {
		this.namaReferantor2 = namaReferantor2;
	}
	public String getKodeReferantor3() {
		return kodeReferantor3;
	}
	public void setKodeReferantor3(String kodeReferantor3) {
		this.kodeReferantor3 = kodeReferantor3;
	}
	public String getNamaReferantor3() {
		return namaReferantor3;
	}
	public void setNamaReferantor3(String namaReferantor3) {
		this.namaReferantor3 = namaReferantor3;
	}
	public String getFieldPersonIA() {
		return fieldPersonIA;
	}
	public void setFieldPersonIA(String fieldPersonIA) {
		this.fieldPersonIA = fieldPersonIA;
	}
	public String getJobCodeIA() {
		return jobCodeIA;
	}
	public void setJobCodeIA(String jobCodeIA) {
		this.jobCodeIA = jobCodeIA;
	}
	public String getNoteTelesales() {
		return noteTelesales;
	}
	public void setNoteTelesales(String noteTelesales) {
		this.noteTelesales = noteTelesales;
	}
	public String getFunding() {
		return funding;
	}
	public void setFunding(String funding) {
		this.funding = funding;
	}
	public Integer getIsPreApproval() {
		return isPreApproval;
	}
	public void setIsPreApproval(Integer isPreApproval) {
		this.isPreApproval = isPreApproval;
	}
	public Integer getMandatoryLevel() {
		return mandatoryLevel;
	}
	public void setMandatoryLevel(Integer mandatoryLevel) {
		this.mandatoryLevel = mandatoryLevel;
	}
	public String getOpsiPenanganan() {
		return opsiPenanganan;
	}
	public void setOpsiPenanganan(String opsiPenanganan) {
		this.opsiPenanganan = opsiPenanganan;
	}
	//TAMBAHAN
	public String getInstallmentWOM() {
		return installmentWOM;
	}
	public void setInstallmentWOM(String installmentWOM) {
		this.installmentWOM = installmentWOM;
	}
	public String getOtherInstallment() {
		return otherInstallment;
	}
	public void setOtherInstallment(String otherInstallment) {
		this.otherInstallment = otherInstallment;
	}
	public String getInscoBranchCode() {
		return InscoBranchCode;
	}
	public void setInscoBranchCode(String InscoBranchCode) {
		this.InscoBranchCode = InscoBranchCode;
	}
	public String getInscoBranchName() {
		return InscoBranchName;
	}
	public void setInscoBranchName(String InscoBranchName) {
		this.InscoBranchName = InscoBranchName;
	}
	public String getIsProductInformation() {
		return IsProductInformation;
	}
	public void setIsProductInformation(String IsProductInformation) {
		this.IsProductInformation = IsProductInformation;
	}
	public String getIsFollowUp() {
		return IsFollowUp;
	}
	public void setIsFollowUp(String IsFollowUp) {
		this.IsFollowUp = IsFollowUp;
	}
	public String getProfessionNamePsgn() {
		return professionNamePsgn;
	}
	public void setProfessionNamePsgn(String professionNamePsgn) {
		this.professionNamePsgn = professionNamePsgn;
	}
	public String getLayerapproval() {
		return layerapproval;
	}
	public void setLayerapproval(String layerapproval) {
		this.layerapproval = layerapproval;
	}
	public String getCustNo() {
		return custNo;
	}
	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}
	public String getSourceData() {
		return SourceData;
	}
	public void setSourceData(String sourceData) {
		SourceData = sourceData;
	}
	public String getSendLinkCustomer() {
		return SendLinkCustomer;
	}
	public void setSendLinkCustomer(String sendLinkCustomer) {
		SendLinkCustomer = sendLinkCustomer;
	}
	public String getSendLinkSpouse() {
		return SendLinkSpouse;
	}
	public void setSendLinkSpouse(String sendLinkSpouse) {
		SendLinkSpouse = sendLinkSpouse;
	}
	public String getSendLinkGuarantor() {
		return SendLinkGuarantor;
	}
	public void setSendLinkGuarantor(String sendLinkGuarantor) {
		SendLinkGuarantor = sendLinkGuarantor;
	}
	
	public String getTglOrderIn() {
		return tglOrderIn;
	}
	public void setTglOrderIn(String tglOrderIn) {
		this.tglOrderIn = tglOrderIn;
	}
	public String getCustProtectCode() {
		return custProtectCode;
	}
	public void setCustProtectCode(String custProtectCode) {
		this.custProtectCode = custProtectCode;
	}
	
}
