package com.adins.mss.services.model.common;

import java.io.Serializable;

import org.codehaus.jackson.annotate.JsonProperty;

@SuppressWarnings("serial")
public class AddTaskPoloRequest extends KbijData implements Serializable{

	 private int flagPreIA;
	 private String namaTask = "";
	 private String statusKepemilikanRumah = "";
	 private String angsuran = "";
	 private String penghasilan = "";
	 private String npwp = "";
	 private String tenor = "";
	 private String dpReal = "";
	 private String dpAmount = "";
	 private String ntfAmt = "";
	 private String otr = "";
	 private String tahunKendaraan = "";
	 private String assetCategory = "";
	 private String lob = "";
	 private String soa = "";
	 private String fotoPasangan = "";
	 private String marital_status = "";
	 private String mother_name = "";
	 private String status_topup = "";
	 private String old_agreement = "";
	 private String kode_promo = "";
	 private String tele_status = "";
	 private String dukcapil_status = "";
	 private String biometric_percentage = ""; 
	 private String profession_category_name = "";
	 private String profession_code = "";
	 private String officeRegionCode = "";
	 private String officeCode = "";
	 private String customer_id = "";
	 private String customer_name = "";
	 private String nik_ktp = "";
	 private String tempat_lahir = "";
	 private String tanggal_lahir = "";
	 private String alamat_legal = "";
	 private String rt_legal = "";
	 private String rw_legal = "";
	 private String provinsi_legal = "";
	 private String kabupaten_legal = "";
	 private String kecamatan_legal = "";
	 private String kelurahan_legal = "";
	 private String kodepos_legal = "";
	 private String subzipcode_legal = "";
	 private String alamat_survey = "";
	 private String rt_survey = "";
	 private String rw_survey = "";
	 private String provinsi_survey = "";
	 private String kabupaten_survey = "";
	 private String kecamatan_survey = "";
	 private String kelurahan_survey = "";
	 private String kodepos_survey = "";
	 private String subzipcode_survey = "";
	 private String mobile1 = "";
	 private String namaKodeDealer = "";
	 private String noKontrak = "";
	 private String sumberData = "";
	 private String source_data = "";
	 private String kodeReferantor = "";
	 private String namaReferantor = "";
	 private String supervisor = "";
	 private String noteTelesales = "";
	 private String taskIdPolo = "";
	 private String jenisTask = "";
	 private String negativeCustomer;
	 private String tanggalVisit = "";
	 private String tanggalInputData = "";
	 private String kodeKATKelurahan = "";
	 private String sisa_tenor = "";
	 private String product = "";
	 private String asset_type = "";
	 private String typeCode = "";
	 private String notesMss = "";
	 private String fotoPemohon = "";
	 private String fotoKtp = "";
	 private String fotoKtpPasangan = "";
	 private String fotoKK = "";
	 private String posDealer = "";
	 private String salesDealer_ID = "";
	 private String statusProspek = "";

	 private String ref1Code;
	 private String ref1Name;
	 private String ref2Code;
	 private String ref2Name;
	 
	 // polo CR CAE Fase 1
	 private String ref3Code;
	 private String ref3Name;
	 private String fotoPsgn;
	 private String fotoGuarantor;
	 private String agama;
	 private String mlbtknPsgn;
	 private String persetujuanSlik;
	 private String prospectInterest;
	 private String email;
	 private String totalKontrakBakiAllSlikAktf;
	 private String totaKontrakTunggakSlikAktf;
	 private String totalAngsuranSlikAktf;
	 private String maxDPDSlikAktf;
	 private String lastUpdateSlikAktf;
	 private String totalKontrakSlikLns;
	 private String maxDPDSlikLns;
	 private String totalKontrakMaxDPDSlikLns;
	 private String lastUpdateSlikLns;
	 private String isCancelApp;
	 private String isCancelAppReason;
	 private String isCancelAppNotes;
	 private String cmoRec;
	 private String cmoRecReason;
	 private String cmoRecNotes;
	 private String statusFollowUp;
	 private String informasiSigning;
	 private String resultGeoTagging;
	 private String spvCRO;
	 private String isInstantApproval;
	 private String statusPenangananTele;
	 private String statusPriorityCRM;
	 private String taskVisitType;
	 
	 private String flagKawanInternal;
	 private String location;
	 private String ownerRelationship;
	 private String jumlahTanggungan;
	 private String statusPekerjaan;
	 private String jenisIndustri;
	 private String totalKontrakBakiAllSlikAktfPsgn;
	 private String totalKontrakTunggakSlikAktfPsgn;
	 private String totalAngsuranSlikAktfPsgn;
	 private String maxDPDSlikAktfPsgn;
	 private String lastUpdateSlikAktfPsgn;
	 private String totalKontrakSlikLnsPsgn;
	 private String maxDPDSlikLnsPsgn;
	 private String totalKontrakMaxDPDSlikLnsPsgn;
	 private String lastUpdateSlikLnsPsgn;
	 private String totalKontrakBakiAllSlikAktfGrtr;
	 private String totalKontrakTunggakSlikAktfGrtr;
	 private String totalAngsuranSlikAktfGrtr;
	 private String maxDPDSlikAktfGrtr;
	 private String lastUpdateSlikAktfGrtr;
	 private String totalKontrakSlikLnsGrtr;
	 private String maxDPDSlikLnsGrtr;
	 private String totalKontrakMaxDPDSlikLnsGrtr;
	 private String lastUpdateSlikLnsGrtr;
	 private String addrSurveyEqualAddrLegal;
	 private String funding;
	 private Integer isPreApproval;
	 private Integer mandatoryLevel;
	 private String opsiPenanganan;
	 @JsonProperty("InscoBranchCode") private String InscoBranchCode;
	 @JsonProperty("InscoBranchName") private String InscoBranchName;
	 @JsonProperty("IsProductInformation") private String IsProductInformation;
	 @JsonProperty("IsFollowUp") private String IsFollowUp;
	 
	 private String kodeReferantor1;
	 private String namaReferantor1;
	 private String kodeReferantor2;
	 private String namaReferantor2; 
	 
	 private String negativeStatus;
	 private String dukcapilStatusPsgn;
	 private String neglistStatusPsgn;
	 private String dukcapilStatusGrt;
	 private String neglistStatusGrt;
	 
	 //cr additional third  party
	 @JsonProperty("assetType") private String assetType;
	 @JsonProperty("brand") private String brand;
	 @JsonProperty("downPayment") private String downPayment;
	 @JsonProperty("downPaymentPct") private String downPaymentPct;
	 @JsonProperty("education") private String education;
	 @JsonProperty("gender") private String gender ;
	 @JsonProperty("groupType") private String groupType;
	 @JsonProperty("guarantorAddr") private String guarantorAddr;
	 @JsonProperty("guarantorBirthDate") private String guarantorBirthDate;
	 @JsonProperty("guarantorBirthPlace") private String guarantorBirthPlace;
	 @JsonProperty("guarantorCity") private String guarantorCity;
	 @JsonProperty("guarantorSubDistrict") private String guarantorSubDistrict;
	 @JsonProperty("guarantorVillage") private String guarantorVillage;
	 @JsonProperty("guarantorMobilePhone") private String guarantorMobilePhone;
	 @JsonProperty("guarantorName") private String guarantorName;
	 @JsonProperty("guarantorIdNo") private String guarantorIdNo;
	 @JsonProperty("guarantorProvince") private String guarantorProvince;
	 @JsonProperty("guarantorRelationship") private String guarantorRelationship;
	 @JsonProperty("guarantorRt") private String guarantorRt;
	 @JsonProperty("guarantorRw") private String guarantorRw;
	 @JsonProperty("guarantorSubzipcode") private String guarantorSubzipcode;
	 @JsonProperty("guarantorZipcode") private String guarantorZipcode;
	 @JsonProperty("homeStat") private String homeStat;
	 @JsonProperty("industryTypeName") private String industryTypeName;
	 @JsonProperty("lengthOfWork") private String lengthOfWork;
	 @JsonProperty("ltv") private String ltv;
	 @JsonProperty("monthlyIncome") private String monthlyIncome;
	 @JsonProperty("monthlyInstallment") private String monthlyInstallment;
	 @JsonProperty("otrPriceAmt") private String otrPriceAmt;
	 @JsonProperty("prodType") private String prodType;
	 @JsonProperty("religion") private String religion;
	 @JsonProperty("spouseBirthDate") private String spouseBirthDate;
	 @JsonProperty("spouseBirthPlace") private String spouseBirthPlace;
	 @JsonProperty("spouseName") private String spouseName;
	 @JsonProperty("spouseIdNo") private String spouseIdNo;
	 @JsonProperty("stayLength") private String stayLength;

	 //CR OPSI PENYELESAIAN SENGKETA
	 @JsonProperty("custProtectCode") private String custProtectCode;

	 

	 // Getter Methods 
	 public int getFlagPreIA() {
	  return this.flagPreIA;
	 }

	 public String getProfession_category_name() {
	  return this.profession_category_name;
	 }

	 public String getCustomer_id() {
	  return this.customer_id;
	 }

	 public String getCustomer_name() {
	  return this.customer_name;
	 }

	 public String getNik_ktp() {
	  return this.nik_ktp;
	 }

	 public String getTempat_lahir() {
	  return this.tempat_lahir;
	 }

	 public String getTanggal_lahir() {
	  return this.tanggal_lahir;
	 }

	 public String getAlamat_legal() {
	  return this.alamat_legal;
	 }

	 public String getRt_legal() {
	  return this.rt_legal;
	 }

	 public String getRw_legal() {
	  return this.rw_legal;
	 }

	 public String getProvinsi_legal() {
	  return this.provinsi_legal;
	 }

	 public String getKabupaten_legal() {
	  return this.kabupaten_legal;
	 }

	 public String getKecamatan_legal() {
	  return this.kecamatan_legal;
	 }

	 public String getKelurahan_legal() {
	  return this.kelurahan_legal;
	 }

	 public String getKodepos_legal() {
	  return this.kodepos_legal;
	 }

	 public String getSubzipcode_legal() {
	  return this.subzipcode_legal;
	 }

	 public String getAlamat_survey() {
	  return this.alamat_survey;
	 }

	 public String getRt_survey() {
	  return this.rt_survey;
	 }

	 public String getRw_survey() {
	  return this.rw_survey;
	 }

	 public String getProvinsi_survey() {
	  return this.provinsi_survey;
	 }

	 public String getKabupaten_survey() {
	  return this.kabupaten_survey;
	 }

	 public String getKecamatan_survey() {
	  return this.kecamatan_survey;
	 }

	 public String getKelurahan_survey() {
	  return this.kelurahan_survey;
	 }

	 public String getKodepos_survey() {
	  return this.kodepos_survey;
	 }

	 public String getSubzipcode_survey() {
	  return this.subzipcode_survey;
	 }

	 public String getMobile1() {
	  return this.mobile1;
	 }

	 public String getNamaKodeDealer() {
	  return this.namaKodeDealer;
	 }

	 public String getNoKontrak() {
	  return this.noKontrak;
	 }

	 public String getSumberData() {
	  return this.sumberData;
	 }

	 public String getSource_data() {
	  return this.source_data;
	 }

	 public String getKodeReferantor() {
	  return this.kodeReferantor;
	 }

	 public String getNamaReferantor() {
	  return this.namaReferantor;
	 }

	 public String getSupervisor() {
	  return this.supervisor;
	 }

	 public String getNoteTelesales() {
	  return this.noteTelesales;
	 }

	 public String getTaskIdPolo() {
	  return this.taskIdPolo;
	 }

	 public String getJenisTask() {
	  return this.jenisTask;
	 }

	 public String getNegativeCustomer() {
	  return this.negativeCustomer;
	 }

	 public String getTanggalVisit() {
	  return this.tanggalVisit;
	 }

	 public String getTanggalInputData() {
	  return this.tanggalInputData;
	 }

	 public String getKodeKATKelurahan() {
	  return this.kodeKATKelurahan;
	 }

	 public String getSisa_tenor() {
	  return this.sisa_tenor;
	 }

	 public String getProduct() {
	  return this.product;
	 }

	 public String getAsset_type() {
	  return this.asset_type;
	 }

	 public String getTypeCode() {
	  return this.typeCode;
	 }

	 public String getNotesMss() {
	  return this.notesMss;
	 }

	 public String getFotoPemohon() {
	  return this.fotoPemohon;
	 }

	 public String getFotoKtp() {
	  return this.fotoKtp;
	 }

	 public String getFotoKtpPasangan() {
	  return this.fotoKtpPasangan;
	 }

	 public String getFotoKK() {
	  return this.fotoKK;
	 }

	 public String getPosDealer() {
	  return this.posDealer;
	 }

	 public String getSalesDealerID() {
	  return this.salesDealer_ID;
	 }

	 // Setter Methods 

	 public void setFlagPreIA(int flagPreIA) {
	  this.flagPreIA = flagPreIA;
	 }

	 public void setProfession_category_name(String profession_category_name) {
	  this.profession_category_name = profession_category_name;
	 }

	 public void setCustomer_id(String customer_id) {
	  this.customer_id = customer_id;
	 }

	 public void setCustomer_name(String customer_name) {
	  this.customer_name = customer_name;
	 }

	 public void setNik_ktp(String nik_ktp) {
	  this.nik_ktp = nik_ktp;
	 }

	 public void setTempat_lahir(String tempat_lahir) {
	  this.tempat_lahir = tempat_lahir;
	 }

	 public void setTanggal_lahir(String tanggal_lahir) {
	  this.tanggal_lahir = tanggal_lahir;
	 }

	 public void setAlamat_legal(String alamat_legal) {
	  this.alamat_legal = alamat_legal;
	 }

	 public void setRt_legal(String rt_legal) {
	  this.rt_legal = rt_legal;
	 }

	 public void setRw_legal(String rw_legal) {
	  this.rw_legal = rw_legal;
	 }

	 public void setProvinsi_legal(String provinsi_legal) {
	  this.provinsi_legal = provinsi_legal;
	 }

	 public void setKabupaten_legal(String kabupaten_legal) {
	  this.kabupaten_legal = kabupaten_legal;
	 }

	 public void setKecamatan_legal(String kecamatan_legal) {
	  this.kecamatan_legal = kecamatan_legal;
	 }

	 public void setKelurahan_legal(String kelurahan_legal) {
	  this.kelurahan_legal = kelurahan_legal;
	 }

	 public void setKodepos_legal(String kodepos_legal) {
	  this.kodepos_legal = kodepos_legal;
	 }

	 public void setSubzipcode_legal(String subzipcode_legal) {
	  this.subzipcode_legal = subzipcode_legal;
	 }

	 public void setAlamat_survey(String alamat_survey) {
	  this.alamat_survey = alamat_survey;
	 }

	 public void setRt_survey(String rt_survey) {
	  this.rt_survey = rt_survey;
	 }

	 public void setRw_survey(String rw_survey) {
	  this.rw_survey = rw_survey;
	 }

	 public void setProvinsi_survey(String provinsi_survey) {
	  this.provinsi_survey = provinsi_survey;
	 }

	 public void setKabupaten_survey(String kabupaten_survey) {
	  this.kabupaten_survey = kabupaten_survey;
	 }

	 public void setKecamatan_survey(String kecamatan_survey) {
	  this.kecamatan_survey = kecamatan_survey;
	 }

	 public void setKelurahan_survey(String kelurahan_survey) {
	  this.kelurahan_survey = kelurahan_survey;
	 }

	 public void setKodepos_survey(String kodepos_survey) {
	  this.kodepos_survey = kodepos_survey;
	 }

	 public void setSubzipcode_survey(String subzipcode_survey) {
	  this.subzipcode_survey = subzipcode_survey;
	 }

	 public void setMobile1(String mobile1) {
	  this.mobile1 = mobile1;
	 }

	 public void setNamaKodeDealer(String namaKodeDealer) {
	  this.namaKodeDealer = namaKodeDealer;
	 }

	 public void setNoKontrak(String noKontrak) {
	  this.noKontrak = noKontrak;
	 }

	 public void setSumberData(String sumberData) {
	  this.sumberData = sumberData;
	 }

	 public void setSource_data(String source_data) {
	  this.source_data = source_data;
	 }

	 public void setKodeReferantor(String kodeReferantor) {
	  this.kodeReferantor = kodeReferantor;
	 }

	 public void setNamaReferantor(String namaReferantor) {
	  this.namaReferantor = namaReferantor;
	 }

	 public void setSupervisor(String supervisor) {
	  this.supervisor = supervisor;
	 }

	 public void setNoteTelesales(String noteTelesales) {
	  this.noteTelesales = noteTelesales;
	 }

	 public void setTaskIdPolo(String taskIdPolo) {
	  this.taskIdPolo = taskIdPolo;
	 }

	 public void setJenisTask(String jenisTask) {
	  this.jenisTask = jenisTask;
	 }

	 public void setNegativeCustomer(String negativeCustomer) {
	  this.negativeCustomer = negativeCustomer;
	 }

	 public void setTanggalVisit(String tanggalVisit) {
	  this.tanggalVisit = tanggalVisit;
	 }

	 public void setTanggalInputData(String tanggalInputData) {
	  this.tanggalInputData = tanggalInputData;
	 }

	 public void setKodeKATKelurahan(String kodeKATKelurahan) {
	  this.kodeKATKelurahan = kodeKATKelurahan;
	 }

	 public void setSisa_tenor(String sisa_tenor) {
	  this.sisa_tenor = sisa_tenor;
	 }

	 public void setProduct(String product) {
	  this.product = product;
	 }

	 public void setAsset_type(String asset_type) {
	  this.asset_type = asset_type;
	 }

	 public void setTypeCode(String typeCode) {
	  this.typeCode = typeCode;
	 }

	 public void setNotesMss(String notesMss) {
	  this.notesMss = notesMss;
	 }

	 public void setFotoPemohon(String fotoPemohon) {
	  this.fotoPemohon = fotoPemohon;
	 }

	 public void setFotoKtp(String fotoKtp) {
	  this.fotoKtp = fotoKtp;
	 }

	 public void setFotoKtpPasangan(String fotoKtpPasangan) {
	  this.fotoKtpPasangan = fotoKtpPasangan;
	 }

	 public void setFotoKK(String fotoKK) {
	  this.fotoKK = fotoKK;
	 }

	 public void setPosDealer(String posDealer) {
	  this.posDealer = posDealer;
	 }

	 public void setSalesDealerID(String salesDealer_ID) {
	  this.salesDealer_ID = salesDealer_ID;
	 }

	public String getOfficeRegionCode() {
		return this.officeRegionCode;
	}

	public void setOfficeRegionCode(String officeRegionCode) {
		this.officeRegionCode = officeRegionCode;
	}

	public String getProfession_code() {
		return this.profession_code;
	}

	public void setProfession_code(String profession_code) {
		this.profession_code = profession_code;
	}

	public String getOfficeCode() {
		return this.officeCode;
	}

	public void setOfficeCode(String officeCode) {
		this.officeCode = officeCode;
	}

	public String getSalesDealer_ID() {
		return this.salesDealer_ID;
	}

	public void setSalesDealer_ID(String salesDealer_ID) {
		this.salesDealer_ID = salesDealer_ID;
	}

	public String getRef1Code() {
		return this.ref1Code;
	}

	public void setRef1Code(String ref1Code) {
		this.ref1Code = ref1Code;
	}

	public String getRef1Name() {
		return this.ref1Name;
	}

	public void setRef1Name(String ref1Name) {
		this.ref1Name = ref1Name;
	}

	public String getRef2Code() {
		return this.ref2Code;
	}

	public void setRef2Code(String ref2Code) {
		this.ref2Code = ref2Code;
	}

	public String getRef2Name() {
		return this.ref2Name;
	}

	public void setRef2Name(String ref2Name) {
		this.ref2Name = ref2Name;
	}

	public String getRef3Code() {
		return this.ref3Code;
	}

	public void setRef3Code(String ref3Code) {
		this.ref3Code = ref3Code;
	}

	public String getRef3Name() {
		return this.ref3Name;
	}

	public void setRef3Name(String ref3Name) {
		this.ref3Name = ref3Name;
	}

	public String getFotoPsgn() {
		return this.fotoPsgn;
	}

	public void setFotoPsgn(String fotoPsgn) {
		this.fotoPsgn = fotoPsgn;
	}

	public String getFotoGuarantor() {
		return this.fotoGuarantor;
	}

	public void setFotoGuarantor(String fotoGuarantor) {
		this.fotoGuarantor = fotoGuarantor;
	}

	public String getStatusProspek() {
		return this.statusProspek;
	}

	public void setStatusProspek(String statusProspek) {
		this.statusProspek = statusProspek;
	}

	public String getNamaTask() {
		return this.namaTask;
	}

	public void setNamaTask(String namaTask) {
		this.namaTask = namaTask;
	}

	public String getStatusKepemilikanRumah() {
		return this.statusKepemilikanRumah;
	}

	public void setStatusKepemilikanRumah(String statusKepemilikanRumah) {
		this.statusKepemilikanRumah = statusKepemilikanRumah;
	}

	public String getAngsuran() {
		return this.angsuran;
	}

	public void setAngsuran(String angsuran) {
		this.angsuran = angsuran;
	}

	public String getPenghasilan() {
		return this.penghasilan;
	}

	public void setPenghasilan(String penghasilan) {
		this.penghasilan = penghasilan;
	}

	public String getNpwp() {
		return this.npwp;
	}

	public void setNpwp(String npwp) {
		this.npwp = npwp;
	}

	public String getTenor() {
		return this.tenor;
	}

	public void setTenor(String tenor) {
		this.tenor = tenor;
	}

	public String getDpReal() {
		return this.dpReal;
	}

	public void setDpReal(String dpReal) {
		this.dpReal = dpReal;
	}

	public String getDpAmount() {
		return this.dpAmount;
	}

	public void setDpAmount(String dpAmount) {
		this.dpAmount = dpAmount;
	}

	public String getNtfAmt() {
		return this.ntfAmt;
	}

	public void setNtfAmt(String ntfAmt) {
		this.ntfAmt = ntfAmt;
	}

	public String getOtr() {
		return this.otr;
	}

	public void setOtr(String otr) {
		this.otr = otr;
	}

	public String getTahunKendaraan() {
		return this.tahunKendaraan;
	}

	public void setTahunKendaraan(String tahunKendaraan) {
		this.tahunKendaraan = tahunKendaraan;
	}

	public String getAssetCategory() {
		return this.assetCategory;
	}

	public void setAssetCategory(String assetCategory) {
		this.assetCategory = assetCategory;
	}

	public String getLob() {
		return this.lob;
	}

	public void setLob(String lob) {
		this.lob = lob;
	}

	public String getSoa() {
		return this.soa;
	}

	public void setSoa(String soa) {
		this.soa = soa;
	}

	public String getFotoPasangan() {
		return this.fotoPasangan;
	}

	public void setFotoPasangan(String fotoPasangan) {
		this.fotoPasangan = fotoPasangan;
	}

	public String getMarital_status() {
		return this.marital_status;
	}

	public void setMarital_status(String marital_status) {
		this.marital_status = marital_status;
	}

	public String getMother_name() {
		return this.mother_name;
	}

	public void setMother_name(String mother_name) {
		this.mother_name = mother_name;
	}

	public String getStatus_topup() {
		return this.status_topup;
	}

	public void setStatus_topup(String status_topup) {
		this.status_topup = status_topup;
	}

	public String getOld_agreement() {
		return this.old_agreement;
	}

	public void setOld_agreement(String old_agreement) {
		this.old_agreement = old_agreement;
	}

	public String getKode_promo() {
		return this.kode_promo;
	}

	public void setKode_promo(String kode_promo) {
		this.kode_promo = kode_promo;
	}

	public String getTele_status() {
		return this.tele_status;
	}

	public void setTele_status(String tele_status) {
		this.tele_status = tele_status;
	}

	public String getDukcapil_status() {
		return this.dukcapil_status;
	}

	public void setDukcapil_status(String dukcapil_status) {
		this.dukcapil_status = dukcapil_status;
	}

	public String getBiometric_percentage() {
		return this.biometric_percentage;
	}

	public void setBiometric_percentage(String biometric_percentage) {
		this.biometric_percentage = biometric_percentage;
	}

	public String getAgama() {
		return this.agama;
	}

	public void setAgama(String agama) {
		this.agama = agama;
	}

	public String getMlbtknPsgn() {
		return this.mlbtknPsgn;
	}

	public void setMlbtknPsgn(String mlbtknPsgn) {
		this.mlbtknPsgn = mlbtknPsgn;
	}

	public String getPersetujuanSlik() {
		return this.persetujuanSlik;
	}

	public void setPersetujuanSlik(String persetujuanSlik) {
		this.persetujuanSlik = persetujuanSlik;
	}

	public String getProspectInterest() {
		return this.prospectInterest;
	}

	public void setProspectInterest(String prospectInterest) {
		this.prospectInterest = prospectInterest;
	}

	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getTotalKontrakBakiAllSlikAktf() {
		return this.totalKontrakBakiAllSlikAktf;
	}

	public void setTotalKontrakBakiAllSlikAktf(String totalKontrakBakiAllSlikAktf) {
		this.totalKontrakBakiAllSlikAktf = totalKontrakBakiAllSlikAktf;
	}

	public String getTotaKontrakTunggakSlikAktf() {
		return this.totaKontrakTunggakSlikAktf;
	}

	public void setTotaKontrakTunggakSlikAktf(String totaKontrakTunggakSlikAktf) {
		this.totaKontrakTunggakSlikAktf = totaKontrakTunggakSlikAktf;
	}

	public String getTotalAngsuranSlikAktf() {
		return this.totalAngsuranSlikAktf;
	}

	public void setTotalAngsuranSlikAktf(String totalAngsuranSlikAktf) {
		this.totalAngsuranSlikAktf = totalAngsuranSlikAktf;
	}

	public String getMaxDPDSlikAktf() {
		return this.maxDPDSlikAktf;
	}

	public void setMaxDPDSlikAktf(String maxDPDSlikAktf) {
		this.maxDPDSlikAktf = maxDPDSlikAktf;
	}

	public String getLastUpdateSlikAktf() {
		return this.lastUpdateSlikAktf;
	}

	public void setLastUpdateSlikAktf(String lastUpdateSlikAktf) {
		this.lastUpdateSlikAktf = lastUpdateSlikAktf;
	}

	public String getTotalKontrakSlikLns() {
		return this.totalKontrakSlikLns;
	}

	public void setTotalKontrakSlikLns(String totalKontrakSlikLns) {
		this.totalKontrakSlikLns = totalKontrakSlikLns;
	}

	public String getMaxDPDSlikLns() {
		return this.maxDPDSlikLns;
	}

	public void setMaxDPDSlikLns(String maxDPDSlikLns) {
		this.maxDPDSlikLns = maxDPDSlikLns;
	}

	public String getTotalKontrakMaxDPDSlikLns() {
		return this.totalKontrakMaxDPDSlikLns;
	}

	public void setTotalKontrakMaxDPDSlikLns(String totalKontrakMaxDPDSlikLns) {
		this.totalKontrakMaxDPDSlikLns = totalKontrakMaxDPDSlikLns;
	}

	public String getLastUpdateSlikLns() {
		return this.lastUpdateSlikLns;
	}

	public void setLastUpdateSlikLns(String lastUpdateSlikLns) {
		this.lastUpdateSlikLns = lastUpdateSlikLns;
	}

	public String getIsCancelApp() {
		return this.isCancelApp;
	}

	public void setIsCancelApp(String isCancelApp) {
		this.isCancelApp = isCancelApp;
	}

	public String getIsCancelAppReason() {
		return this.isCancelAppReason;
	}

	public void setIsCancelAppReason(String isCancelAppReason) {
		this.isCancelAppReason = isCancelAppReason;
	}

	public String getIsCancelAppNotes() {
		return this.isCancelAppNotes;
	}

	public void setIsCancelAppNotes(String isCancelAppNotes) {
		this.isCancelAppNotes = isCancelAppNotes;
	}

	public String getCmoRec() {
		return this.cmoRec;
	}

	public void setCmoRec(String cmoRec) {
		this.cmoRec = cmoRec;
	}

	public String getCmoRecReason() {
		return this.cmoRecReason;
	}

	public void setCmoRecReason(String cmoRecReason) {
		this.cmoRecReason = cmoRecReason;
	}

	public String getCmoRecNotes() {
		return this.cmoRecNotes;
	}

	public void setCmoRecNotes(String cmoRecNotes) {
		this.cmoRecNotes = cmoRecNotes;
	}

	public String getStatusFollowUp() {
		return this.statusFollowUp;
	}

	public void setStatusFollowUp(String statusFollowUp) {
		this.statusFollowUp = statusFollowUp;
	}

	public String getInformasiSigning() {
		return this.informasiSigning;
	}

	public void setInformasiSigning(String informasiSigning) {
		this.informasiSigning = informasiSigning;
	}

	public String getResultGeoTagging() {
		return this.resultGeoTagging;
	}

	public void setResultGeoTagging(String resultGeoTagging) {
		this.resultGeoTagging = resultGeoTagging;
	}

	public String getSpvCRO() {
		return this.spvCRO;
	}

	public void setSpvCRO(String spvCRO) {
		this.spvCRO = spvCRO;
	}

	public String getIsInstantApproval() {
		return this.isInstantApproval;
	}

	public void setIsInstantApproval(String isInstantApproval) {
		this.isInstantApproval = isInstantApproval;
	}

	public String getStatusPenangananTele() {
		return this.statusPenangananTele;
	}

	public void setStatusPenangananTele(String statusPenangananTele) {
		this.statusPenangananTele = statusPenangananTele;
	}

	public String getStatusPriorityCRM() {
		return this.statusPriorityCRM;
	}

	public void setStatusPriorityCRM(String statusPriorityCRM) {
		this.statusPriorityCRM = statusPriorityCRM;
	}

	public String getTaskVisitType() {
		return this.taskVisitType;
	}

	public void setTaskVisitType(String taskVisitType) {
		this.taskVisitType = taskVisitType;
	}

	public String getFlagKawanInternal() {
		return this.flagKawanInternal;
	}

	public void setFlagKawanInternal(String flagKawanInternal) {
		this.flagKawanInternal = flagKawanInternal;
	}

	public String getLocation() {
		return this.location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getOwnerRelationship() {
		return this.ownerRelationship;
	}

	public void setOwnerRelationship(String ownerRelationship) {
		this.ownerRelationship = ownerRelationship;
	}

	public String getJumlahTanggungan() {
		return this.jumlahTanggungan;
	}

	public void setJumlahTanggungan(String jumlahTanggungan) {
		this.jumlahTanggungan = jumlahTanggungan;
	}

	public String getStatusPekerjaan() {
		return this.statusPekerjaan;
	}

	public void setStatusPekerjaan(String statusPekerjaan) {
		this.statusPekerjaan = statusPekerjaan;
	}

	public String getJenisIndustri() {
		return this.jenisIndustri;
	}

	public void setJenisIndustri(String jenisIndustri) {
		this.jenisIndustri = jenisIndustri;
	}
	
	public String getTotalKontrakBakiAllSlikAktfPsgn() {
		return this.totalKontrakBakiAllSlikAktfPsgn;
	}
	
	public void setTotalKontrakBakiAllSlikAktfPsgn(String totalKontrakBakiAllSlikAktfPsgn) {
		this.totalKontrakBakiAllSlikAktfPsgn = totalKontrakBakiAllSlikAktfPsgn;
	}
	
	public String getTotalKontrakTunggakSlikAktfPsgn() {
		return this.totalKontrakTunggakSlikAktfPsgn;
	}
	
	public void setTotalKontrakTunggakSlikAktfPsgn(String totalKontrakTunggakSlikAktfPsgn) {
		this.totalKontrakTunggakSlikAktfPsgn = totalKontrakTunggakSlikAktfPsgn;
	}
	
	public String getTotalAngsuranSlikAktfPsgn() {
		return this.totalAngsuranSlikAktfPsgn;
	}
	
	public void setTotalAngsuranSlikAktfPsgn(String totalAngsuranSlikAktfPsgn) {
		this.totalAngsuranSlikAktfPsgn = totalAngsuranSlikAktfPsgn;
	}
	
	public String getMaxDPDSlikAktfPsgn() {
		return this.maxDPDSlikAktfPsgn;
	}
	
	public void setMaxDPDSlikAktfPsgn(String maxDPDSlikAktfPsgn) {
		this.maxDPDSlikAktfPsgn = maxDPDSlikAktfPsgn;
	}
	
	public String getLastUpdateSlikAktfPsgn() {
		return this.lastUpdateSlikAktfPsgn;
	}
	
	public void setLastUpdateSlikAktfPsgn(String lastUpdateSlikAktfPsgn) {
		this.lastUpdateSlikAktfPsgn = lastUpdateSlikAktfPsgn;
	}
	
	public String getTotalKontrakSlikLnsPsgn() {
		return this.totalKontrakSlikLnsPsgn;
	}
	
	public void setTotalKontrakSlikLnsPsgn(String totalKontrakSlikLnsPsgn) {
		this.totalKontrakSlikLnsPsgn = totalKontrakSlikLnsPsgn;
	}
	
	public String getMaxDPDSlikLnsPsgn() {
		return this.maxDPDSlikLnsPsgn;
	}
	
	public void setMaxDPDSlikLnsPsgn(String maxDPDSlikLnsPsgn) {
		this.maxDPDSlikLnsPsgn = maxDPDSlikLnsPsgn;
	}
	
	public String getTotalKontrakMaxDPDSlikLnsPsgn() {
		return this.totalKontrakMaxDPDSlikLnsPsgn;
	}
	
	public void setTotalKontrakMaxDPDSlikLnsPsgn(String totalKontrakMaxDPDSlikLnsPsgn) {
		this.totalKontrakMaxDPDSlikLnsPsgn = totalKontrakMaxDPDSlikLnsPsgn;
	}
	
	public String getLastUpdateSlikLnsPsgn() {
		return this.lastUpdateSlikLnsPsgn;
	}
	
	public void setLastUpdateSlikLnsPsgn(String lastUpdateSlikLnsPsgn) {
		this.lastUpdateSlikLnsPsgn = lastUpdateSlikLnsPsgn;
	}
	
	public String getTotalKontrakBakiAllSlikAktfGrtr() {
		return this.totalKontrakBakiAllSlikAktfGrtr;
	}
	
	public void setTotalKontrakBakiAllSlikAktfGrtr(String totalKontrakBakiAllSlikAktfGrtr) {
		this.totalKontrakBakiAllSlikAktfGrtr = totalKontrakBakiAllSlikAktfGrtr;
	}
	
	public String getTotalKontrakTunggakSlikAktfGrtr() {
		return this.totalKontrakTunggakSlikAktfGrtr;
	}
	
	public void setTotalKontrakTunggakSlikAktfGrtr(String totalKontrakTunggakSlikAktfGrtr) {
		this.totalKontrakTunggakSlikAktfGrtr = totalKontrakTunggakSlikAktfGrtr;
	}
	
	public String getTotalAngsuranSlikAktfGrtr() {
		return this.totalAngsuranSlikAktfGrtr;
	}
	
	public void setTotalAngsuranSlikAktfGrtr(String totalAngsuranSlikAktfGrtr) {
		this.totalAngsuranSlikAktfGrtr = totalAngsuranSlikAktfGrtr;
	}
	
	public String getMaxDPDSlikAktfGrtr() {
		return this.maxDPDSlikAktfGrtr;
	}
	
	public void setMaxDPDSlikAktfGrtr(String maxDPDSlikAktfGrtr) {
		this.maxDPDSlikAktfGrtr = maxDPDSlikAktfGrtr;
	}
	
	public String getLastUpdateSlikAktfGrtr() {
		return this.lastUpdateSlikAktfGrtr;
	}
	
	public void setLastUpdateSlikAktfGrtr(String lastUpdateSlikAktfGrtr) {
		this.lastUpdateSlikAktfGrtr = lastUpdateSlikAktfGrtr;
	}
	
	public String getTotalKontrakSlikLnsGrtr() {
		return this.totalKontrakSlikLnsGrtr;
	}
	
	public void setTotalKontrakSlikLnsGrtr(String totalKontrakSlikLnsGrtr) {
		this.totalKontrakSlikLnsGrtr = totalKontrakSlikLnsGrtr;
	}
	
	public String getMaxDPDSlikLnsGrtr() {
		return this.maxDPDSlikLnsGrtr;
	}
	
	public void setMaxDPDSlikLnsGrtr(String maxDPDSlikLnsGrtr) {
		this.maxDPDSlikLnsGrtr = maxDPDSlikLnsGrtr;
	}
	
	public String getTotalKontrakMaxDPDSlikLnsGrtr() {
		return this.totalKontrakMaxDPDSlikLnsGrtr;
	}
	
	public void setTotalKontrakMaxDPDSlikLnsGrtr(String totalKontrakMaxDPDSlikLnsGrtr) {
		this.totalKontrakMaxDPDSlikLnsGrtr = totalKontrakMaxDPDSlikLnsGrtr;
	}
	
	public String getLastUpdateSlikLnsGrtr() {
		return this.lastUpdateSlikLnsGrtr;
	}
	
	public void setLastUpdateSlikLnsGrtr(String lastUpdateSlikLnsGrtr) {
		this.lastUpdateSlikLnsGrtr = lastUpdateSlikLnsGrtr;
	}

	public String getAddrSurveyEqualAddrLegal() {
		return this.addrSurveyEqualAddrLegal;
	}

	public void setAddrSurveyEqualAddrLegal(String addrSurveyEqualAddrLegal) {
		this.addrSurveyEqualAddrLegal = addrSurveyEqualAddrLegal;
	}

	public String getFunding() {
		return this.funding;
	}

	public void setFunding(String funding) {
		this.funding = funding;
	}

	public Integer getIsPreApproval() {
		return this.isPreApproval;
	}

	public void setIsPreApproval(Integer isPreApproval) {
		this.isPreApproval = isPreApproval;
	}

	public Integer getMandatoryLevel() {
		return this.mandatoryLevel;
	}

	public void setMandatoryLevel(Integer mandatoryLevel) {
		this.mandatoryLevel = mandatoryLevel;
	}

	public String getOpsiPenanganan() {
		return this.opsiPenanganan;
	}
	
	public void setOpsiPenanganan(String opsiPenanganan) {
		this.opsiPenanganan = opsiPenanganan;
	}

	public String getInscoBranchCode() {
		return this.InscoBranchCode;
	}

	public void setInscoBranchCode(String inscoBranchCode) {
		this.InscoBranchCode = inscoBranchCode;
	}

	public String getInscoBranchName() {
		return this.InscoBranchName;
	}

	public void setInscoBranchName(String inscoBranchName) {
		this.InscoBranchName = inscoBranchName;
	}

	public String getIsProductInformation() {
		return this.IsProductInformation;
	}

	public void setIsProductInformation(String isProductInformation) {
		this.IsProductInformation = isProductInformation;
	}

	public String getIsFollowUp() {
		return this.IsFollowUp;
	}

	public void setIsFollowUp(String isFollowUp) {
		this.IsFollowUp = isFollowUp;
	}
	
	public String getKodeReferantor1() {
		return this.kodeReferantor1;
	}

	public void setKodeReferantor1(String kodeReferantor1) {
		this.kodeReferantor1 = kodeReferantor1;
	}

	public String getNamaReferantor1() {
		return this.namaReferantor1;
	}

	public void setNamaReferantor1(String namaReferantor1) {
		this.namaReferantor1 = namaReferantor1;
	}

	public String getKodeReferantor2() {
		return this.kodeReferantor2;
	}

	public void setKodeReferantor2(String kodeReferantor2) {
		this.kodeReferantor2 = kodeReferantor2;
	}

	public String getNamaReferantor2() {
		return this.namaReferantor2;
	}

	public void setNamaReferantor2(String namaReferantor2) {
		this.namaReferantor2 = namaReferantor2;
	}

	public String getDukcapilStatusPsgn() {
		return this.dukcapilStatusPsgn;
	}

	public void setDukcapilStatusPsgn(String dukcapilStatusPsgn) {
		this.dukcapilStatusPsgn = dukcapilStatusPsgn;
	}

	public String getNeglistStatusPsgn() {
		return this.neglistStatusPsgn;
	}

	public void setNeglistStatusPsgn(String neglistStatusPsgn) {
		this.neglistStatusPsgn = neglistStatusPsgn;
	}

	public String getDukcapilStatusGrt() {
		return this.dukcapilStatusGrt;
	}

	public void setDukcapilStatusGrt(String dukcapilStatusGrt) {
		this.dukcapilStatusGrt = dukcapilStatusGrt;
	}

	public String getNeglistStatusGrt() {
		return this.neglistStatusGrt;
	}

	public void setNeglistStatusGrt(String neglistStatusGrt) {
		this.neglistStatusGrt = neglistStatusGrt;
	}

	public String getNegativeStatus() {
		return this.negativeStatus;
	}

	public void setNegativeStatus(String negativeStatus) {
		this.negativeStatus = negativeStatus;
	}

	public String getAssetType() {
		return this.assetType;
	}

	public void setAssetType(String assetType) {
		this.assetType = assetType;
	}

	public String getBrand() {
		return this.brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public String getDownPayment() {
		return this.downPayment;
	}

	public void setDownPayment(String downPayment) {
		this.downPayment = downPayment;
	}

	public String getDownPaymentPct() {
		return this.downPaymentPct;
	}

	public void setDownPaymentPct(String downPaymentPct) {
		this.downPaymentPct = downPaymentPct;
	}

	public String getEducation() {
		return this.education;
	}

	public void setEducation(String education) {
		this.education = education;
	}

	public String getGender() {
		return this.gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getGroupType() {
		return this.groupType;
	}

	public void setGroupType(String groupType) {
		this.groupType = groupType;
	}

	public String getGuarantorAddr() {
		return this.guarantorAddr;
	}

	public void setGuarantorAddr(String guarantorAddr) {
		this.guarantorAddr = guarantorAddr;
	}

	public String getGuarantorBirthDate() {
		return this.guarantorBirthDate;
	}

	public void setGuarantorBirthDate(String guarantorBirthDate) {
		this.guarantorBirthDate = guarantorBirthDate;
	}

	public String getGuarantorBirthPlace() {
		return this.guarantorBirthPlace;
	}

	public void setGuarantorBirthPlace(String guarantorBirthPlace) {
		this.guarantorBirthPlace = guarantorBirthPlace;
	}

	public String getGuarantorCity() {
		return this.guarantorCity;
	}

	public void setGuarantorCity(String guarantorCity) {
		this.guarantorCity = guarantorCity;
	}

	public String getGuarantorKecamatan() {
		return this.guarantorSubDistrict;
	}

	public void setGuarantorKecamatan(String guarantorKecamatan) {
		this.guarantorSubDistrict = guarantorKecamatan;
	}

	public String getGuarantorKelurahan() {
		return this.guarantorVillage;
	}

	public void setGuarantorKelurahan(String guarantorKelurahan) {
		this.guarantorVillage = guarantorKelurahan;
	}

	public String getGuarantorMobilePhoneNo() {
		return this.guarantorMobilePhone;
	}

	public void setGuarantorMobilePhoneNo(String guarantorMobilePhoneNo) {
		this.guarantorMobilePhone = guarantorMobilePhoneNo;
	}

	public String getGuarantorName() {
		return this.guarantorName;
	}

	public void setGuarantorName(String guarantorName) {
		this.guarantorName = guarantorName;
	}

	public String getGuarantorNIK() {
		return this.guarantorIdNo;
	}

	public void setGuarantorNIK(String guarantorNIK) {
		this.guarantorIdNo = guarantorNIK;
	}

	public String getGuarantorProvince() {
		return this.guarantorProvince;
	}

	public void setGuarantorProvince(String guarantorProvince) {
		this.guarantorProvince = guarantorProvince;
	}

	public String getGuarantorRelationship() {
		return this.guarantorRelationship;
	}

	public void setGuarantorRelationship(String guarantorRelationship) {
		this.guarantorRelationship = guarantorRelationship;
	}

	public String getGuarantorRt() {
		return this.guarantorRt;
	}

	public void setGuarantorRt(String guarantorRt) {
		this.guarantorRt = guarantorRt;
	}

	public String getGuarantorRw() {
		return this.guarantorRw;
	}

	public void setGuarantorRw(String guarantorRw) {
		this.guarantorRw = guarantorRw;
	}

	public String getGuarantorSubZipcode() {
		return this.guarantorSubzipcode;
	}

	public void setGuarantorSubZipcode(String guarantorSubZipcode) {
		this.guarantorSubzipcode = guarantorSubZipcode;
	}

	public String getGuarantorZipcode() {
		return this.guarantorZipcode;
	}

	public void setGuarantorZipcode(String guarantorZipcode) {
		this.guarantorZipcode = guarantorZipcode;
	}

	public String getHomeStat() {
		return this.homeStat;
	}

	public void setHomeStat(String homeStat) {
		this.homeStat = homeStat;
	}

	public String getIndustryTypeName() {
		return this.industryTypeName;
	}

	public void setIndustryTypeName(String industryTypeName) {
		this.industryTypeName = industryTypeName;
	}

	public String getLengthOfWork() {
		return this.lengthOfWork;
	}

	public void setLengthOfWork(String lengthOfWork) {
		this.lengthOfWork = lengthOfWork;
	}

	public String getLtv() {
		return this.ltv;
	}

	public void setLtv(String ltv) {
		this.ltv = ltv;
	}

	public String getMonthlyIncome() {
		return this.monthlyIncome;
	}

	public void setMonthlyIncome(String monthlyIncome) {
		this.monthlyIncome = monthlyIncome;
	}

	public String getMonthlyInstallment() {
		return this.monthlyInstallment;
	}

	public void setMonthlyInstallment(String monthlyInstallment) {
		this.monthlyInstallment = monthlyInstallment;
	}

	public String getOtrPriceAmt() {
		return this.otrPriceAmt;
	}

	public void setOtrPriceAmt(String otrPriceAmt) {
		this.otrPriceAmt = otrPriceAmt;
	}

	public String getProdType() {
		return this.prodType;
	}

	public void setProdType(String prodType) {
		this.prodType = prodType;
	}

	public String getSpouseBirthDate() {
		return this.spouseBirthDate;
	}

	public void setSpouseBirthDate(String spouseBirthDate) {
		this.spouseBirthDate = spouseBirthDate;
	}

	public String getSpouseBirthPlace() {
		return this.spouseBirthPlace;
	}

	public void setSpouseBirthPlace(String spouseBirthPlace) {
		this.spouseBirthPlace = spouseBirthPlace;
	}

	public String getSpouseName() {
		return this.spouseName;
	}

	public void setSpouseName(String spouseName) {
		this.spouseName = spouseName;
	}

	public String getSpouseNIK() {
		return this.spouseIdNo;
	}

	public void setSpouseNIK(String spouseNIK) {
		this.spouseIdNo = spouseNIK;
	}

	public String getStayLength() {
		return this.stayLength;
	}

	public void setStayLength(String stayLength) {
		this.stayLength = stayLength;
	}

	public String getReligion() {
		return this.religion;
	}

	public void setReligion(String religion) {
		this.religion = religion;
	}

	public String getCustProtectCode() {
		return custProtectCode;
	}

	public void setCustProtectCode(String custProtectCode) {
		this.custProtectCode = custProtectCode;
	} 
	
}
