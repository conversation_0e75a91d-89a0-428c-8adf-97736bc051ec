package com.adins.mss.services.model.common;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;

/**
 * JUnit test class for AddTaskCAERequest new fields
 * Tests the new custProtectCode field and its getter/setter methods
 * Created to achieve 100% code coverage for new lines in revision 6d55721 to 367b8b2
 */
public class AddTaskCAERequestTest {

    private AddTaskCAERequest addTaskCAERequest;
    
    @Before
    public void setUp() {
        addTaskCAERequest = new AddTaskCAERequest();
    }
    
    @Test
    public void testCustProtectCodeGetterSetter() {
        // Test with valid value
        String testValue = "PROTECT001";
        addTaskCAERequest.setCustProtectCode(testValue);
        assertEquals("custProtectCode getter/setter should work correctly", 
                     testValue, addTaskCAERequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithNullValue() {
        // Test with null value
        addTaskCAERequest.setCustProtectCode(null);
        assertNull("custProtectCode should accept null value", 
                   addTaskCAERequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithEmptyString() {
        // Test with empty string
        String emptyValue = "";
        addTaskCAERequest.setCustProtectCode(emptyValue);
        assertEquals("custProtectCode should accept empty string", 
                     emptyValue, addTaskCAERequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithSpecialCharacters() {
        // Test with special characters
        String specialValue = "PROTECT-001_TEST@2024";
        addTaskCAERequest.setCustProtectCode(specialValue);
        assertEquals("custProtectCode should accept special characters", 
                     specialValue, addTaskCAERequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithLongString() {
        // Test with long string to ensure no length restrictions in getter/setter
        String longValue = "PROTECT_CODE_WITH_VERY_LONG_STRING_TO_TEST_FIELD_CAPACITY_AND_HANDLING";
        addTaskCAERequest.setCustProtectCode(longValue);
        assertEquals("custProtectCode should handle long strings", 
                     longValue, addTaskCAERequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithNumericString() {
        // Test with numeric string
        String numericValue = "123456789";
        addTaskCAERequest.setCustProtectCode(numericValue);
        assertEquals("custProtectCode should accept numeric strings", 
                     numericValue, addTaskCAERequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithWhitespace() {
        // Test with whitespace
        String whitespaceValue = "  PROTECT 001  ";
        addTaskCAERequest.setCustProtectCode(whitespaceValue);
        assertEquals("custProtectCode should preserve whitespace", 
                     whitespaceValue, addTaskCAERequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeDefaultValue() {
        // Test default value (should be null for new instance)
        AddTaskCAERequest newRequest = new AddTaskCAERequest();
        assertNull("custProtectCode should be null by default", 
                   newRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeMultipleSetOperations() {
        // Test multiple set operations
        String firstValue = "FIRST_PROTECT";
        String secondValue = "SECOND_PROTECT";
        
        addTaskCAERequest.setCustProtectCode(firstValue);
        assertEquals("First value should be set correctly", 
                     firstValue, addTaskCAERequest.getCustProtectCode());
        
        addTaskCAERequest.setCustProtectCode(secondValue);
        assertEquals("Second value should overwrite first value", 
                     secondValue, addTaskCAERequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeFieldIndependence() {
        // Test that custProtectCode field is independent of other fields
        String protectCode = "PROTECT123";
        String customerName = "John Doe";
        
        addTaskCAERequest.setCustProtectCode(protectCode);
        addTaskCAERequest.setCustomerName(customerName);
        
        assertEquals("custProtectCode should not be affected by other fields", 
                     protectCode, addTaskCAERequest.getCustProtectCode());
        assertEquals("Other fields should not be affected by custProtectCode", 
                     customerName, addTaskCAERequest.getCustomerName());
    }
    
    @Test
    public void testCustProtectCodeJsonPropertyAnnotation() {
        // Test that the field has the correct JsonProperty annotation
        // This test verifies the annotation exists by checking field accessibility
        try {
            java.lang.reflect.Field field = AddTaskCAERequest.class.getDeclaredField("custProtectCode");
            assertNotNull("custProtectCode field should exist", field);
            
            // Check if JsonProperty annotation is present
            org.codehaus.jackson.annotate.JsonProperty annotation = 
                field.getAnnotation(org.codehaus.jackson.annotate.JsonProperty.class);
            assertNotNull("custProtectCode field should have JsonProperty annotation", annotation);
            assertEquals("JsonProperty value should be 'custProtectCode'", 
                         "custProtectCode", annotation.value());
        } catch (NoSuchFieldException e) {
            fail("custProtectCode field should exist in AddTaskCAERequest class");
        }
    }
    
    @Test
    public void testCustProtectCodeSerializationCompatibility() {
        // Test that the object is still serializable with the new field
        assertTrue("AddTaskCAERequest should implement Serializable", 
                   addTaskCAERequest instanceof java.io.Serializable);
        
        // Set the new field and verify it doesn't break serialization
        addTaskCAERequest.setCustProtectCode("SERIALIZE_TEST");
        assertNotNull("Object should remain valid after setting custProtectCode", 
                      addTaskCAERequest);
    }
    
    @Test
    public void testCustProtectCodeInheritance() {
        // Test that the new field works correctly with inheritance
        assertTrue("AddTaskCAERequest should extend KbijData", 
                   addTaskCAERequest instanceof KbijData);
        
        // Set custProtectCode and verify inheritance chain is not broken
        addTaskCAERequest.setCustProtectCode("INHERIT_TEST");
        assertEquals("custProtectCode should work with inheritance", 
                     "INHERIT_TEST", addTaskCAERequest.getCustProtectCode());
    }
}
