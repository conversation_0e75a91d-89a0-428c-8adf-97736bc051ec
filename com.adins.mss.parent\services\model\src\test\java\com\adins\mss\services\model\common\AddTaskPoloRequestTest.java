package com.adins.mss.services.model.common;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;

/**
 * JUnit test class for AddTaskPoloRequest new fields
 * Tests the new custProtectCode field and its getter/setter methods
 * Created to achieve 100% code coverage for new lines in revision 6d55721 to 367b8b2
 */
public class AddTaskPoloRequestTest {

    private AddTaskPoloRequest addTaskPoloRequest;
    
    @Before
    public void setUp() {
        addTaskPoloRequest = new AddTaskPoloRequest();
    }
    
    @Test
    public void testCustProtectCodeGetterSetter() {
        // Test with valid value
        String testValue = "POLO_PROTECT001";
        addTaskPoloRequest.setCustProtectCode(testValue);
        assertEquals("custProtectCode getter/setter should work correctly", 
                     testValue, addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithNullValue() {
        // Test with null value
        addTaskPoloRequest.setCustProtectCode(null);
        assertNull("custProtectCode should accept null value", 
                   addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithEmptyString() {
        // Test with empty string
        String emptyValue = "";
        addTaskPoloRequest.setCustProtectCode(emptyValue);
        assertEquals("custProtectCode should accept empty string", 
                     emptyValue, addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithSpecialCharacters() {
        // Test with special characters
        String specialValue = "POLO-PROTECT_001@2024#TEST";
        addTaskPoloRequest.setCustProtectCode(specialValue);
        assertEquals("custProtectCode should accept special characters", 
                     specialValue, addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithLongString() {
        // Test with long string to ensure no length restrictions in getter/setter
        String longValue = "POLO_PROTECT_CODE_WITH_VERY_LONG_STRING_TO_TEST_FIELD_CAPACITY_AND_HANDLING_CAPABILITIES";
        addTaskPoloRequest.setCustProtectCode(longValue);
        assertEquals("custProtectCode should handle long strings", 
                     longValue, addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithNumericString() {
        // Test with numeric string
        String numericValue = "987654321";
        addTaskPoloRequest.setCustProtectCode(numericValue);
        assertEquals("custProtectCode should accept numeric strings", 
                     numericValue, addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithWhitespace() {
        // Test with whitespace
        String whitespaceValue = "  POLO PROTECT 001  ";
        addTaskPoloRequest.setCustProtectCode(whitespaceValue);
        assertEquals("custProtectCode should preserve whitespace", 
                     whitespaceValue, addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeDefaultValue() {
        // Test default value (should be null for new instance)
        AddTaskPoloRequest newRequest = new AddTaskPoloRequest();
        assertNull("custProtectCode should be null by default", 
                   newRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeMultipleSetOperations() {
        // Test multiple set operations
        String firstValue = "FIRST_POLO_PROTECT";
        String secondValue = "SECOND_POLO_PROTECT";
        
        addTaskPoloRequest.setCustProtectCode(firstValue);
        assertEquals("First value should be set correctly", 
                     firstValue, addTaskPoloRequest.getCustProtectCode());
        
        addTaskPoloRequest.setCustProtectCode(secondValue);
        assertEquals("Second value should overwrite first value", 
                     secondValue, addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeFieldIndependence() {
        // Test that custProtectCode field is independent of other fields
        String protectCode = "POLO_PROTECT123";
        String namaTask = "Test Task";
        
        addTaskPoloRequest.setCustProtectCode(protectCode);
        addTaskPoloRequest.setNamaTask(namaTask);
        
        assertEquals("custProtectCode should not be affected by other fields", 
                     protectCode, addTaskPoloRequest.getCustProtectCode());
        assertEquals("Other fields should not be affected by custProtectCode", 
                     namaTask, addTaskPoloRequest.getNamaTask());
    }
    
    @Test
    public void testCustProtectCodeJsonPropertyAnnotation() {
        // Test that the field has the correct JsonProperty annotation
        // This test verifies the annotation exists by checking field accessibility
        try {
            java.lang.reflect.Field field = AddTaskPoloRequest.class.getDeclaredField("custProtectCode");
            assertNotNull("custProtectCode field should exist", field);
            
            // Check if JsonProperty annotation is present
            org.codehaus.jackson.annotate.JsonProperty annotation = 
                field.getAnnotation(org.codehaus.jackson.annotate.JsonProperty.class);
            assertNotNull("custProtectCode field should have JsonProperty annotation", annotation);
            assertEquals("JsonProperty value should be 'custProtectCode'", 
                         "custProtectCode", annotation.value());
        } catch (NoSuchFieldException e) {
            fail("custProtectCode field should exist in AddTaskPoloRequest class");
        }
    }
    
    @Test
    public void testCustProtectCodeSerializationCompatibility() {
        // Test that the object is still serializable with the new field
        assertTrue("AddTaskPoloRequest should implement Serializable", 
                   addTaskPoloRequest instanceof java.io.Serializable);
        
        // Set the new field and verify it doesn't break serialization
        addTaskPoloRequest.setCustProtectCode("POLO_SERIALIZE_TEST");
        assertNotNull("Object should remain valid after setting custProtectCode", 
                      addTaskPoloRequest);
    }
    
    @Test
    public void testCustProtectCodeInheritance() {
        // Test that the new field works correctly with inheritance
        assertTrue("AddTaskPoloRequest should extend KbijData", 
                   addTaskPoloRequest instanceof KbijData);
        
        // Set custProtectCode and verify inheritance chain is not broken
        addTaskPoloRequest.setCustProtectCode("POLO_INHERIT_TEST");
        assertEquals("custProtectCode should work with inheritance", 
                     "POLO_INHERIT_TEST", addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithUnicodeCharacters() {
        // Test with Unicode characters
        String unicodeValue = "POLO_保护代码_001";
        addTaskPoloRequest.setCustProtectCode(unicodeValue);
        assertEquals("custProtectCode should handle Unicode characters", 
                     unicodeValue, addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeCaseSensitivity() {
        // Test case sensitivity
        String lowerCase = "polo_protect_code";
        String upperCase = "POLO_PROTECT_CODE";
        
        addTaskPoloRequest.setCustProtectCode(lowerCase);
        assertEquals("custProtectCode should preserve case", 
                     lowerCase, addTaskPoloRequest.getCustProtectCode());
        
        addTaskPoloRequest.setCustProtectCode(upperCase);
        assertEquals("custProtectCode should preserve case", 
                     upperCase, addTaskPoloRequest.getCustProtectCode());
    }
    
    @Test
    public void testCustProtectCodeWithNewlineCharacters() {
        // Test with newline characters
        String newlineValue = "POLO_PROTECT\nCODE\r\n001";
        addTaskPoloRequest.setCustProtectCode(newlineValue);
        assertEquals("custProtectCode should handle newline characters", 
                     newlineValue, addTaskPoloRequest.getCustProtectCode());
    }
}
