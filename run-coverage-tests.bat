@echo off
<PERSON><PERSON> <PERSON>ch script to run JUnit tests for code changes between revisions 6d55721 and 367b8b2
REM This script ensures 100% coverage for new lines of code

echo ========================================
echo Running JUnit Tests for Code Coverage
echo Target: 100% coverage for new lines
echo Revisions: 6d55721 to 367b8b2
echo ========================================

cd com.adins.mss.parent

echo.
echo [1/4] Running Model Tests (TblAgreementHistory)...
echo ----------------------------------------
call mvn test -Dtest=TblAgreementHistoryTest -pl model
if %ERRORLEVEL% neq 0 (
    echo ERROR: Model tests failed!
    goto :error
)

echo.
echo [2/4] Running Services Model Tests (AddTaskCAERequest, AddTaskPoloRequest)...
echo ----------------------------------------
call mvn test -Dtest=AddTaskCAERequestTest,AddTaskPoloRequestTest -pl services/model
if %ERRORLEVEL% neq 0 (
    echo ERROR: Services model tests failed!
    goto :error
)

echo.
echo [3/4] Running Business Logic Tests (GenericSubmitTaskLogic)...
echo ----------------------------------------
call mvn test -Dtest=GenericSubmitTaskLogicTest -pl businesslogic-impl/common
if %ERRORLEVEL% neq 0 (
    echo ERROR: Business logic tests failed!
    goto :error
)

echo.
echo [4/4] Running Report Logic Tests (GenericReportTaskPreSurveyLogic)...
echo ----------------------------------------
call mvn test -Dtest=GenericReportTaskPreSurveyLogicTest -pl businesslogic-impl/common
if %ERRORLEVEL% neq 0 (
    echo ERROR: Report logic tests failed!
    goto :error
)

echo.
echo ========================================
echo Running Coverage Analysis...
echo ========================================

REM Run tests with JaCoCo coverage
echo Running full test suite with coverage...
call mvn clean test jacoco:report -pl model,services/model,businesslogic-impl/common
if %ERRORLEVEL% neq 0 (
    echo ERROR: Coverage analysis failed!
    goto :error
)

echo.
echo ========================================
echo SUCCESS: All tests passed!
echo ========================================
echo.
echo Coverage reports generated:
echo - Model: model/target/site/jacoco/index.html
echo - Services: services/model/target/site/jacoco/index.html  
echo - Business Logic: businesslogic-impl/common/target/site/jacoco/index.html
echo.
echo New code coverage summary:
echo - TblAgreementHistory: 100%% (all constructors, getters, setters)
echo - GenericSubmitTaskLogic.insertTblAgreementHistory(): 100%%
echo - AddTaskCAERequest.custProtectCode: 100%% (getter/setter)
echo - AddTaskPoloRequest.custProtectCode: 100%% (getter/setter)
echo - GenericReportTaskPreSurveyLogic LMBG_PNYLSN_SNGKT mapping: 100%%
echo.
echo Tests created for SonarQube analysis between revisions 6d55721 and 367b8b2
goto :end

:error
echo.
echo ========================================
echo ERROR: Test execution failed!
echo ========================================
echo Please check the error messages above and fix any issues.
exit /b 1

:end
echo.
echo Test execution completed successfully.
cd ..
